const prod_baseurl = "https://api.cannyhr.com/";
const onboarding_prod_baseurl = "https://onboardapi.cannyhr.com/";
const irukka_integration_prod_baseurl =
  "https://aju0i48d0f.execute-api.ap-south-1.amazonaws.com/uat/partner/api/v1/partnerIntegration";
const microsoft_login_url = "https://login.microsoftonline.com/common";

const production = {
  domain: "cannyhr.com",
  productLogo: 0,
  production: 1,
  TZDate: "m/d/Y",
  graphql_endpoint: {
    stepFunction: prod_baseurl + "rographql",
    ats: prod_baseurl + "ats/graphql",
    atsExternal: prod_baseurl + "ats/external",
    atsSignIn: prod_baseurl + "ats/signinGraphql",
    atswographl: prod_baseurl + "ats/wographql",
    atsrographl: prod_baseurl + "ats/rographql",
    settings: prod_baseurl + "hrappBe/settingsgraphql",
    hrappBE: prod_baseurl + "hrappBe/graphql",
    employeeMonitoring: prod_baseurl + "employeeMonitoring/graphql",
    empMonitorRead: prod_baseurl + "employeeMonitoring/roGraphql",
    payMaster: prod_baseurl + "paymaster/graphql",
    payMasterRead: prod_baseurl + "paymaster/rographql",
    billing: prod_baseurl + "billing/billinggraphql",
    coreHrRead: prod_baseurl + "coreHr/rographql",
    coreHrWrite: prod_baseurl + "coreHr/wographql ",
    coreHrNoAuth: prod_baseurl + "coreHr/noauthrographql",
    coreHrExternal: prod_baseurl + "coreHr/external",
    docusignRead: prod_baseurl + "docusign/rographql",
    docusignWrite: prod_baseurl + "docusign/wographql ",
    docusignNoAuthRead: prod_baseurl + "docusign/noauthrographql",
    docusignNoAuthWrite: prod_baseurl + "docusign/noauthwographql",
    hrappBERead: prod_baseurl + "hrappBe/roGraphql",
    hrappBEWrite: prod_baseurl + "hrappBe/woGraphql",
    empMonitorWrite: prod_baseurl + "employeeMonitoring/woGraphql",
    attendanceRead: prod_baseurl + "facialAttendance/rographql",
    attendanceWrite: prod_baseurl + "facialAttendance/wographql",
    onboardingRead: onboarding_prod_baseurl + "regraphql",
    onboardingReadNoAuth: onboarding_prod_baseurl + "rographql",
    onboardingWriteNoAuth: onboarding_prod_baseurl + "wographql",
    onboardingWrite: onboarding_prod_baseurl + "wegraphql",
    onboardingReadWrite: onboarding_prod_baseurl + "graphql",
    exitManagement: prod_baseurl + "exitManagement/exitManagement",
    payrollAdminRead: prod_baseurl + "payrollAdmin/roGraphql",
    payrollAdminWrite: prod_baseurl + "payrollAdmin/woGraphql",
    empSelfServiceRead: prod_baseurl + "employeeSelfService/rographql",
    empSelfServiceWrite: prod_baseurl + "employeeSelfService/wographql",
    settingsRead: prod_baseurl + "hrappBe/settingsroGraphql",
    settingsWrite: prod_baseurl + "hrappBe/settingswoGraphql",
    integrationRead: prod_baseurl + "integration/rographql",
    integrationWrite: prod_baseurl + "integration/wographql",
    taxAndStatutory: prod_baseurl + "employee-taxation/graphql",
    taxAndStatutoryRead: prod_baseurl + "taxAndStatutory/rographql",
    taxAndStatutoryWrite: prod_baseurl + "taxAndStatutory/wographql",
    dynamicFormBuilder:
      prod_baseurl + "dynamicFormBuilder/graphqldynamicformbuilder",
    batchProcessingRead: prod_baseurl + "batchProcessing/rographql",
    batchProcessingWrite: prod_baseurl + "batchProcessing/wographql",
    batchProcessingExternal: prod_baseurl + "batchProcessing/external",
    batchProcessingroauthgraphql:
      prod_baseurl + "batchProcessing/roauthgraphql",
    orgDataRead: prod_baseurl + "orgDataManagement/rographql",
    orgDataWrite: prod_baseurl + "orgDataManagement/wographql",
  },
  workflowUrl: prod_baseurl + "workflowEngine",
  irukkaUrl: irukka_integration_prod_baseurl,
  microsoftLogin: microsoft_login_url,
  hrappWebhookApiUrlForIndeed:
    prod_baseurl + "integration/indeedWebHookEndpoint",
  indeedScreeningQuestions: prod_baseurl + "integration/indeedApplyQuestions",
  jobPostUrlForIndeed: "cannyhr.com",
  firebase_credentials: {
    apiKey: "AIzaSyAupi9_2ATYi05M7hfgO3pZFqF1dNGK7tk",
    authDomain: "hrappidentity.firebaseapp.com",
    databaseURL: "https://hrappidentity.firebaseio.com",
    projectId: "hrappidentity",
    storageBucket: "hrappidentity.appspot.com",
    messagingSenderId: "887685568909",
  },
  // need to change prod config here
  tl_firebase_credentials: {
    apiKey: "AIzaSyCUfF3bLGc3yU_qBK91Tx72_3HaWf3cTYc",
    authDomain: "hrapptruleadidentity.firebaseapp.com",
    databaseURL:
      "https://hrapptruleadidentity-default-rtdb.asia-southeast1.firebasedatabase.app",
    projectId: "hrapptruleadidentity",
    storageBucket: "hrapptruleadidentity.appspot.com",
    messagingSenderId: "265861830197",
  },
  ipAddressApi: "https://api.ipify.org/?format=json",
  publicImageS3Path:
    "https://s3.ap-south-1.amazonaws.com/s3.logos.cannyhr.com/",
  googleMapsAPIKey: "AIzaSyA8X1UihjoJuukpRgUr4QFy-R6PrnTiXyw",
  googleFontsAPIKey: "AIzaSyBN3v3bTbNx6FDmkHBj6g_drdhSHuzR_WI",
  googleFontsAPIUrl: "https://www.googleapis.com/webfonts/v1/webfonts",
  mixPanelToken: "6df21e89c6a0f6b1bc345ae98b6ef36e",
};

const configuration = production;

export default {
  // Add common config values here
  MAX_ATTACHMENT_SIZE: 5000000,
  ...configuration,
};
