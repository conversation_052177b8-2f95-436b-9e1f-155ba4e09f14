<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <!-- <link rel="icon" href="<%= BASE_URL %>favicon.ico"> -->
	<link
		rel="preload"
		as="font"
		href="https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900&display=swap"
	/>
	<link rel="stylesheet" type="text/css" href="<%= BASE_URL %>loader.css" />
	<link
		rel="preload"
		as="font"
		href="<%= BASE_URL %>assets/fonts/gt-walsheim/gt-walsheim.css"
	/>
		<!-- CKEditor 5 content styles for view mode -->
		<link rel="stylesheet" href="https://cdn.ckeditor.com/ckeditor5/46.0.1/ckeditor5.css" />
    <title></title>
		<style type="text/css">
			body {
				position: static !important;
				top: 0px !important;
				width: 100%;
				height: 100%;
				font-size: 14px;
			}
				
			@media only screen and (min-width: 960px) and (max-width: 1126px) {
				body {
				font-size: 12px;
				}
			}

			@media only screen and (min-width: 1600px) {
				body {
				font-size: 16px;
				}
			}
		</style>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
		<!-- <script type="text/javascript">
				(function(w,d,s,r,k,h,m){
					if(w.performance && w.performance.timing && w.performance.navigation) {
						w[r] = w[r] || function(){(w[r].q = w[r].q || []).push(arguments)};
						h=d.createElement('script');h.async=true;h.setAttribute('src',s+k);
						d.getElementsByTagName('head')[0].appendChild(h);
						(m = window.onerror),(window.onerror = function (b, c, d, f, g) {
						m && m(b, c, d, f, g),g || (g = new Error(b)),(w[r].q = w[r].q || []).push(["captureException",g]);})
					}
				})(window,document,'//static.site24x7rum.in/beacon/site24x7rum-min.js?appKey=','s247r','8c102d223163526a04e9de649108ead1');
		</script> -->
		<!-- App initial loading -->
		<div id="loading-bg" class="custom-loading-cls" style="display: block">
			<div  class="loader">
				<div class="loader--dot"></div>
				<div class="loader--dot"></div>
				<div class="loader--dot"></div>
				<div class="loader--dot"></div>
				<div class="loader--dot"></div>
				<div class="loader--dot"></div>
			</div>
		</div>
		<script src="https://cdn.jsdelivr.net/npm/js-polyfills/polyfill.min.js?features=IntersectionObserver,ResizeObserver,WebAnimations,Object.fromEntries,Array.prototype.at"></script>
    <div id="app"></div>
    <!-- built files will be auto injected -->
		<script
			loading=async
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCIPzdop7dovdoQZvMy7DxdDs39iGfjgqc&libraries=places">
    </script>
  </body>
</html>
