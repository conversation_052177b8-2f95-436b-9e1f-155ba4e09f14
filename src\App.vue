<template>
  <v-app>
    <router-view></router-view>
  </v-app>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "App",
  data() {
    return {
      isErrorInList: false,
      errorContent: "",
    };
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener("resize", this.handleWindowResize);
    });
    this.$store.commit("UPDATE_WINDOW_WIDTH", window.innerWidth);
    this.$store.commit("UPDATE_WINDOW_HEIGHT", window.innerHeight);
    this.$store.dispatch("fetchUserIp");
  },

  beforeUnmount() {
    window.removeEventListener("resize", this.handleWindowResize);
  },

  methods: {
    handleWindowResize() {
      this.$store.commit("UPDATE_WINDOW_WIDTH", window.innerWidth);
      this.$store.commit("UPDATE_WINDOW_HEIGHT", window.innerHeight);
    },
    handleListError(err = "") {
      this.jobPostMemberLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "custom color picker",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
});
</script>
