import {
  Apollo<PERSON><PERSON>,
  HttpLink,
  ApolloLink,
  InMemoryCache,
} from "@apollo/client/core";
import { onError } from "@apollo/client/link/error";
import { createApolloProvider } from "@vue/apollo-option";
import store from "./store";
import config from "./config";

// Error Handling
const errorHandler = onError(({ graphQLErrors, networkError, operation }) => {
  if (graphQLErrors) {
    graphQLErrors.map(({ message, path }) =>
      console.error(`[GraphQL error]: Message: ${message}, Path: ${path}`)
    );
  } else if (networkError) {
    const context = operation.getContext();
    if (context && context.response) {
      const { status } = context.response;
      // on Unauthorization clear user session and redirect to auth
      if (status === 401) {
        store.dispatch("clearUserLock");
      }
    }
  }
});

function getAuthHeaders() {
  const token = window.$cookies.get("accessToken");
  const refreshToken = window.$cookies.get("refreshToken");
  const org_code = localStorage.getItem("orgCode");
  const user_ip = store.state.userIpAddress;
  const partnerid = window.$cookies.get("partnerid");
  const d_code = window.$cookies.get("d_code");
  const b_code = window.$cookies.get("b_code");
  const emp_uid = window.$cookies.get("empUid");
  const headers = {
    "Content-Type": "application/json",
    org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
    user_ip: user_ip,
    Authorization: token ? token : null,
    refresh_token: refreshToken ? refreshToken : null,
    partnerid: partnerid ? partnerid : "-",
    additional_headers: JSON.stringify({
      d_code: d_code,
      b_code: b_code,
      org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
      user_ip: user_ip,
      Authorization: token ? token : null,
      refresh_token: refreshToken ? refreshToken : null,
      partnerid: partnerid ? partnerid : "-",
      emp_uid: emp_uid ? emp_uid : null,
    }),
  };
  return headers;
}

function getCalendarHeaders() {
  const token = window.$cookies.get("accessToken");
  const refreshToken = window.$cookies.get("refreshToken");
  const org_code = localStorage.getItem("orgCode");
  const user_ip = store.state.userIpAddress;
  const partnerid = window.$cookies.get("partnerid");
  const d_code = window.$cookies.get("d_code");
  const b_code = window.$cookies.get("b_code");
  const emp_uid = window.$cookies.get("empUid");
  const microsoft_access_token = localStorage.getItem("outlookAccess");
  const headers = {
    "Content-Type": "application/json",
    org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
    user_ip: user_ip,
    Authorization: token ? token : null,
    refresh_token: refreshToken ? refreshToken : null,
    partnerid: partnerid ? partnerid : "-",
    microsoft_access_token: microsoft_access_token
      ? microsoft_access_token
      : null,
    additional_headers: JSON.stringify({
      d_code: d_code,
      b_code: b_code,
      org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
      user_ip: user_ip,
      Authorization: token ? token : null,
      refresh_token: refreshToken ? refreshToken : null,
      partnerid: partnerid ? partnerid : "-",
      emp_uid: emp_uid ? emp_uid : null,
    }),
  };
  return headers;
}

function getAuthSeekHeaders() {
  const token = window.$cookies.get("accessToken");
  const refreshToken = window.$cookies.get("refreshToken");
  const partnerid = window.$cookies.get("partnerid");
  const org_code = localStorage.getItem("orgCode");
  const user_ip = store.state.userIpAddress;
  const d_code = window.$cookies.get("d_code");
  const b_code = window.$cookies.get("b_code");
  const emp_uid = window.$cookies.get("empUid");
  const seekToken = window.$cookies.get("jobStreet_access_token");
  const headers = {
    "Content-Type": "application/json",
    org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
    user_ip: user_ip,
    Authorization: token ? token : null,
    partnerid: partnerid ? partnerid : "-",
    refresh_token: refreshToken ? refreshToken : null,
    additional_headers: JSON.stringify({
      d_code: d_code,
      b_code: b_code,
      org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
      user_ip: user_ip,
      Authorization: token ? token : null,
      refresh_token: refreshToken ? refreshToken : null,
      partnerid: partnerid ? partnerid : "-",
      emp_uid: emp_uid ? emp_uid : null,
    }),
  };
  headers["jobstreet_access_token"] = seekToken ? seekToken : null;
  return headers;
}

function getSeekHeaders() {
  const seekToken = window.$cookies.get("jobStreet_bowser_token");
  const headers = {
    Authorization: `Bearer ${seekToken}`,
    "Content-Type": "application/json",
  };
  return headers;
}

function getNoAuthHeaders() {
  const org_code = localStorage.getItem("orgCode");
  const d_code = window.$cookies.get("d_code");
  const b_code = window.$cookies.get("b_code");
  const headers = {
    "Content-Type": "application/json",
    org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
    additional_headers: JSON.stringify({
      d_code: d_code,
      b_code: b_code,
      org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
    }),
  };
  return headers;
}
function getNoAuthHeadersWithPartnerId() {
  const org_code = localStorage.getItem("orgCode");
  const d_code = window.$cookies.get("d_code");
  const b_code = window.$cookies.get("b_code");
  const partnerid = window.$cookies.get("partnerid");
  const headers = {
    "Content-Type": "application/json",
    org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
    partnerid: partnerid ? partnerid : "-",
    additional_headers: JSON.stringify({
      d_code: d_code,
      b_code: b_code,
      org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
    }),
  };
  return headers;
}

function getAuthIndeedHeaders() {
  const token = window.$cookies.get("accessToken");
  const refreshToken = window.$cookies.get("refreshToken");
  const org_code = localStorage.getItem("orgCode");
  const indeedAuthToken = window.$cookies.get("indeedAccessToken");
  const partnerid = window.$cookies.get("partnerid");
  const d_code = window.$cookies.get("d_code");
  const b_code = window.$cookies.get("b_code");
  const headers = {
    "Content-Type": "application/json",
    org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
    Authorization: token ? token : null,
    refresh_token: refreshToken ? refreshToken : null,
    indeed_access_token: indeedAuthToken ? "Bearer " + indeedAuthToken : null,
    partnerid: partnerid ? partnerid : "-",
    additional_headers: JSON.stringify({
      d_code: d_code,
      b_code: b_code,
      org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
      Authorization: token ? token : null,
      refresh_token: refreshToken ? refreshToken : null,
    }),
  };
  return headers;
}
function getAuthMicrosoftIndeedHeaders() {
  const token = window.$cookies.get("accessToken");
  const refreshToken = window.$cookies.get("refreshToken");
  const org_code = localStorage.getItem("orgCode");
  // const indeedAuthToken = window.$cookies.get("indeedAccessToken");
  const partnerid = window.$cookies.get("partnerid");
  const microsoft_access_token = localStorage.getItem("outlookAccess");
  const d_code = window.$cookies.get("d_code");
  const b_code = window.$cookies.get("b_code");
  const headers = {
    "Content-Type": "application/json",
    org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
    Authorization: token ? token : null,
    refresh_token: refreshToken ? refreshToken : null,
    // indeed_access_token: indeedAuthToken ? "Bearer " + indeedAuthToken : null,
    partnerid: partnerid ? partnerid : "-",
    microsoft_access_token: microsoft_access_token
      ? microsoft_access_token
      : null,
    additional_headers: JSON.stringify({
      d_code: d_code,
      b_code: b_code,
      org_code: org_code ? org_code : store.getters.orgCode, //on empty orgcode receiving from localstorage assign orgcode from the current url
      Authorization: token ? token : null,
      refresh_token: refreshToken ? refreshToken : null,
    }),
  };
  return headers;
}

//After the backend responds, we take the access token and refreshToken from headers if it exists, and save it in the cookie.
const afterwareLink = new ApolloLink((operation, forward) => {
  return forward(operation).map((response) => {
    const req_response = response;
    if (req_response.identityToken) {
      const token = req_response.identityToken.idToken;

      //set tokens as received from the backend with expire time
      if (token && window.$cookies.get("accessToken") !== token) {
        let partnerid = window.$cookies.get("partnerid");
        if (partnerid && partnerid.toLowerCase() === "entomo") {
          var date = new Date();
          date.setTime(date.getTime() + 90 * 24 * 60 * 60 * 1000); // Convert 90 days to milliseconds
          window.$cookies.set("accessToken", token, date.toUTCString());
        } else {
          // default expiry 59 mins
          window.$cookies.set("accessToken", token, "59MIN");
        }
      }
    }

    return response;
  });
});

const httpLinkA = new HttpLink({
  uri: config.graphql_endpoint.ats,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkB = new HttpLink({
  uri: config.graphql_endpoint.settings,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkC = new HttpLink({
  uri: config.graphql_endpoint.hrappBE,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkE = new HttpLink({
  uri: config.graphql_endpoint.employeeMonitoring,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkF = new HttpLink({
  uri: config.graphql_endpoint.payMaster,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkG = new HttpLink({
  uri: config.graphql_endpoint.billing,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkH = new HttpLink({
  uri: config.graphql_endpoint.atsSignIn,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkI = new HttpLink({
  uri: config.graphql_endpoint.coreHrRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkJ = new HttpLink({
  uri: config.graphql_endpoint.coreHrWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkK = new HttpLink({
  uri: config.graphql_endpoint.empMonitorRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkL = new HttpLink({
  uri: config.graphql_endpoint.hrappBERead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkM = new HttpLink({
  uri: config.graphql_endpoint.hrappBEWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkN = new HttpLink({
  uri: config.graphql_endpoint.coreHrNoAuth,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkO = new HttpLink({
  uri: config.graphql_endpoint.docusignRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkP = new HttpLink({
  uri: config.graphql_endpoint.docusignWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkQ = new HttpLink({
  uri: config.graphql_endpoint.docusignNoAuthRead,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkR = new HttpLink({
  uri: config.graphql_endpoint.empMonitorWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkS = new HttpLink({
  uri: config.graphql_endpoint.docusignNoAuthWrite,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkT = new HttpLink({
  uri: config.graphql_endpoint.attendanceRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkU = new HttpLink({
  uri: config.graphql_endpoint.attendanceWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkV = new HttpLink({
  uri: config.graphql_endpoint.onboardingRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkW = new HttpLink({
  uri: config.graphql_endpoint.onboardingWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkX = new HttpLink({
  uri: config.graphql_endpoint.onboardingReadWrite,
  fetch: (uri, options) => {
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkY = new HttpLink({
  uri: config.graphql_endpoint.stepFunction,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkZ = new HttpLink({
  uri: config.graphql_endpoint.exitManagement,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkAA = new HttpLink({
  uri: config.graphql_endpoint.payrollAdminRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkAB = new HttpLink({
  uri: config.graphql_endpoint.payrollAdminWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkAC = new HttpLink({
  uri: config.graphql_endpoint.empSelfServiceRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkAD = new HttpLink({
  uri: config.graphql_endpoint.empSelfServiceWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkAE = new HttpLink({
  uri: config.graphql_endpoint.settingsRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});

const httpLinkAF = new HttpLink({
  uri: config.graphql_endpoint.settingsWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAG = new HttpLink({
  uri: config.graphql_endpoint.integrationRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAH = new HttpLink({
  uri: config.graphql_endpoint.integrationWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAI = new HttpLink({
  uri: config.graphql_endpoint.taxAndStatutoryRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAJ = new HttpLink({
  uri: config.graphql_endpoint.taxAndStatutory,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAK = new HttpLink({
  uri: config.graphql_endpoint.taxAndStatutoryWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAL = new HttpLink({
  uri: config.graphql_endpoint.dynamicFormBuilder,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAM = new HttpLink({
  uri: config.graphql_endpoint.atswographl,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAN = new HttpLink({
  uri: config.graphql_endpoint.atsrographl,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAO = new HttpLink({
  uri: config.graphql_endpoint.atsExternal,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeadersWithPartnerId();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAP = new HttpLink({
  uri: config.graphql_endpoint.coreHrExternal,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAQ = new HttpLink({
  uri: config.graphql_endpoint.batchProcessingRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAR = new HttpLink({
  uri: config.graphql_endpoint.batchProcessingExternal,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeadersWithPartnerId();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAS = new HttpLink({
  uri: config.graphql_endpoint.onboardingReadNoAuth,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAT = new HttpLink({
  uri: config.graphql_endpoint.payMasterRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAU = new HttpLink({
  uri: config.graphql_endpoint.orgChart,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAV = new HttpLink({
  uri: config.graphql_endpoint.integrationWrite,
  fetch: (uri, options) => {
    options.headers = getAuthIndeedHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAW = new HttpLink({
  uri: config.graphql_endpoint.seekEndpoint,
  fetch: (uri, options) => {
    options.headers = getSeekHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAX = new HttpLink({
  uri: config.graphql_endpoint.integrationWrite,
  fetch: (uri, options) => {
    options.headers = getAuthSeekHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAY = new HttpLink({
  uri: config.graphql_endpoint.integrationRead,
  fetch: (uri, options) => {
    options.headers = getAuthSeekHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkAZ = new HttpLink({
  uri: config.graphql_endpoint.orgDataRead,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkBA = new HttpLink({
  uri: config.graphql_endpoint.coreHrExternal,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeadersWithPartnerId();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkBB = new HttpLink({
  uri: config.graphql_endpoint.orgDataWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    const emp_uid = window.$cookies.get("empUid");
    options.headers.emp_uid = emp_uid ? emp_uid : null;
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkBC = new HttpLink({
  uri: config.graphql_endpoint.ats,
  fetch: (uri, options) => {
    options.headers = getCalendarHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkBD = new HttpLink({
  uri: config.graphql_endpoint.atsrographl,
  fetch: (uri, options) => {
    options.headers = getCalendarHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkBE = new HttpLink({
  uri: config.graphql_endpoint.atswographl,
  fetch: (uri, options) => {
    options.headers = getCalendarHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkBF = new HttpLink({
  uri: config.graphql_endpoint.integrationWrite,
  fetch: (uri, options) => {
    options.headers = getAuthMicrosoftIndeedHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkBG = new HttpLink({
  uri: config.graphql_endpoint.onboardingWriteNoAuth,
  fetch: (uri, options) => {
    options.headers = getNoAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkBH = new HttpLink({
  uri: config.graphql_endpoint.batchProcessingWrite,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
const httpLinkBI = new HttpLink({
  uri: config.graphql_endpoint.batchProcessingroauthgraphql,
  fetch: (uri, options) => {
    options.headers = getAuthHeaders();
    if (Object.values(config.graphql_endpoint).includes(uri)) {
      return fetch(uri, options);
    } else return null;
  },
});
// const noAuthLink = noAuthHeader;
const errorLink = errorHandler;

//composing all configuration in link using apollolink
const linkA = ApolloLink.from([afterwareLink, errorLink, httpLinkA]);
const linkB = ApolloLink.from([afterwareLink, errorLink, httpLinkB]);
const linkC = ApolloLink.from([afterwareLink, errorLink, httpLinkC]);
const linkE = ApolloLink.from([afterwareLink, errorLink, httpLinkE]);
const linkF = ApolloLink.from([afterwareLink, errorLink, httpLinkF]);
const linkG = ApolloLink.from([afterwareLink, errorLink, httpLinkG]);
const linkH = ApolloLink.from([httpLinkH]);
const linkI = ApolloLink.from([afterwareLink, errorLink, httpLinkI]);
const linkJ = ApolloLink.from([afterwareLink, errorLink, httpLinkJ]);
const linkK = ApolloLink.from([afterwareLink, errorLink, httpLinkK]);
const linkL = ApolloLink.from([afterwareLink, errorLink, httpLinkL]);
const linkM = ApolloLink.from([afterwareLink, errorLink, httpLinkM]);
const linkN = ApolloLink.from([httpLinkN]);
const linkO = ApolloLink.from([afterwareLink, errorLink, httpLinkO]);
const linkP = ApolloLink.from([afterwareLink, errorLink, httpLinkP]);
const linkQ = ApolloLink.from([httpLinkQ]);
const linkR = ApolloLink.from([afterwareLink, errorLink, httpLinkR]);
const linkS = ApolloLink.from([httpLinkS]);
const linkT = ApolloLink.from([afterwareLink, errorLink, httpLinkT]);
const linkU = ApolloLink.from([afterwareLink, errorLink, httpLinkU]);
const linkV = ApolloLink.from([afterwareLink, errorLink, httpLinkV]);
const linkW = ApolloLink.from([afterwareLink, errorLink, httpLinkW]);
const linkX = ApolloLink.from([afterwareLink, errorLink, httpLinkX]);
const linkY = ApolloLink.from([afterwareLink, errorLink, httpLinkY]);
const linkZ = ApolloLink.from([afterwareLink, errorLink, httpLinkZ]);
const linkAA = ApolloLink.from([afterwareLink, errorLink, httpLinkAA]);
const linkAB = ApolloLink.from([afterwareLink, errorLink, httpLinkAB]);
const linkAC = ApolloLink.from([afterwareLink, errorLink, httpLinkAC]);
const linkAD = ApolloLink.from([afterwareLink, errorLink, httpLinkAD]);
const linkAE = ApolloLink.from([afterwareLink, errorLink, httpLinkAE]);
const linkAF = ApolloLink.from([afterwareLink, errorLink, httpLinkAF]);
const linkAG = ApolloLink.from([afterwareLink, errorLink, httpLinkAG]);
const linkAH = ApolloLink.from([afterwareLink, errorLink, httpLinkAH]);
const linkAI = ApolloLink.from([afterwareLink, errorLink, httpLinkAI]);
const linkAJ = ApolloLink.from([afterwareLink, errorLink, httpLinkAJ]);
const linkAK = ApolloLink.from([afterwareLink, errorLink, httpLinkAK]);
const linkAL = ApolloLink.from([afterwareLink, errorLink, httpLinkAL]);
const linkAM = ApolloLink.from([afterwareLink, errorLink, httpLinkAM]);
const linkAN = ApolloLink.from([afterwareLink, errorLink, httpLinkAN]);
const linkAO = ApolloLink.from([afterwareLink, errorLink, httpLinkAO]);
const linkAP = ApolloLink.from([afterwareLink, errorLink, httpLinkAP]);
const linkAQ = ApolloLink.from([afterwareLink, errorLink, httpLinkAQ]);
const linkAR = ApolloLink.from([afterwareLink, errorLink, httpLinkAR]);
const linkAS = ApolloLink.from([afterwareLink, errorLink, httpLinkAS]);
const linkAT = ApolloLink.from([afterwareLink, errorLink, httpLinkAT]);
const linkAU = ApolloLink.from([afterwareLink, errorLink, httpLinkAU]);
const linkAV = ApolloLink.from([afterwareLink, errorLink, httpLinkAV]);
const linkAW = ApolloLink.from([afterwareLink, errorLink, httpLinkAW]);
const linkAX = ApolloLink.from([afterwareLink, errorLink, httpLinkAX]);
const linkAY = ApolloLink.from([afterwareLink, errorLink, httpLinkAY]);
const linkAZ = ApolloLink.from([afterwareLink, errorLink, httpLinkAZ]);
const linkBA = ApolloLink.from([afterwareLink, errorLink, httpLinkBA]);
const linkBB = ApolloLink.from([afterwareLink, errorLink, httpLinkBB]);
const linkBC = ApolloLink.from([afterwareLink, errorLink, httpLinkBC]);
const linkBD = ApolloLink.from([afterwareLink, errorLink, httpLinkBD]);
const linkBE = ApolloLink.from([afterwareLink, errorLink, httpLinkBE]);
const linkBF = ApolloLink.from([afterwareLink, errorLink, httpLinkBF]);
const linkBG = ApolloLink.from([afterwareLink, errorLink, httpLinkBG]);
const linkBH = ApolloLink.from([afterwareLink, errorLink, httpLinkBH]);
const linkBI = ApolloLink.from([afterwareLink, errorLink, httpLinkBI]);

// Create the two apollo client instance
const apolloClientA = new ApolloClient({
  link: linkA,
  cache: new InMemoryCache({
    addTypename: false,
  }),
  connectToDevTools: process.env.NODE_ENV !== "production", //enable dev tool only on development
});
const apolloClientB = new ApolloClient({
  link: linkB,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientC = new ApolloClient({
  link: linkC,
  cache: new InMemoryCache({ addTypename: false }), //to avoid getting typename in the response
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientE = new ApolloClient({
  link: linkE,
  cache: new InMemoryCache({ addTypename: false }), //to avoid getting typename in the response
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientF = new ApolloClient({
  link: linkF,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientG = new ApolloClient({
  link: linkG,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientH = new ApolloClient({
  link: linkH,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientI = new ApolloClient({
  link: linkI,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientJ = new ApolloClient({
  link: linkJ,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientK = new ApolloClient({
  link: linkK,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientL = new ApolloClient({
  link: linkL,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientM = new ApolloClient({
  link: linkM,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientN = new ApolloClient({
  link: linkN,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientO = new ApolloClient({
  link: linkO,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientP = new ApolloClient({
  link: linkP,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientQ = new ApolloClient({
  link: linkQ,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientR = new ApolloClient({
  link: linkR,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientS = new ApolloClient({
  link: linkS,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientT = new ApolloClient({
  link: linkT,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientU = new ApolloClient({
  link: linkU,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientV = new ApolloClient({
  link: linkV,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientW = new ApolloClient({
  link: linkW,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientX = new ApolloClient({
  link: linkX,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientY = new ApolloClient({
  link: linkY,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientZ = new ApolloClient({
  link: linkZ,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAA = new ApolloClient({
  link: linkAA,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAB = new ApolloClient({
  link: linkAB,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAC = new ApolloClient({
  link: linkAC,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAD = new ApolloClient({
  link: linkAD,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAE = new ApolloClient({
  link: linkAE,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAF = new ApolloClient({
  link: linkAF,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAG = new ApolloClient({
  link: linkAG,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAH = new ApolloClient({
  link: linkAH,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAI = new ApolloClient({
  link: linkAI,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAJ = new ApolloClient({
  link: linkAJ,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAK = new ApolloClient({
  link: linkAK,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAL = new ApolloClient({
  link: linkAL,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAM = new ApolloClient({
  link: linkAM,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAN = new ApolloClient({
  link: linkAN,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAO = new ApolloClient({
  link: linkAO,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAP = new ApolloClient({
  link: linkAP,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAQ = new ApolloClient({
  link: linkAQ,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAR = new ApolloClient({
  link: linkAR,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAS = new ApolloClient({
  link: linkAS,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAT = new ApolloClient({
  link: linkAT,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAU = new ApolloClient({
  link: linkAU,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAV = new ApolloClient({
  link: linkAV,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAW = new ApolloClient({
  link: linkAW,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAX = new ApolloClient({
  link: linkAX,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAY = new ApolloClient({
  link: linkAY,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientAZ = new ApolloClient({
  link: linkAZ,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientBA = new ApolloClient({
  link: linkBA,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientBB = new ApolloClient({
  link: linkBB,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientBC = new ApolloClient({
  link: linkBC,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientBD = new ApolloClient({
  link: linkBD,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientBE = new ApolloClient({
  link: linkBE,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientBF = new ApolloClient({
  link: linkBF,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientBG = new ApolloClient({
  link: linkBG,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientBH = new ApolloClient({
  link: linkBH,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloClientBI = new ApolloClient({
  link: linkBI,
  cache: new InMemoryCache({ addTypename: false }),
  connectToDevTools: process.env.NODE_ENV !== "production",
});
const apolloProvider = createApolloProvider({
  clients: {
    apolloClientA,
    apolloClientB,
    apolloClientC,
    apolloClientE,
    apolloClientF,
    apolloClientG,
    apolloClientH,
    apolloClientI,
    apolloClientJ,
    apolloClientK,
    apolloClientL,
    apolloClientM,
    apolloClientN,
    apolloClientO,
    apolloClientP,
    apolloClientQ,
    apolloClientR,
    apolloClientS,
    apolloClientT,
    apolloClientU,
    apolloClientV,
    apolloClientW,
    apolloClientX,
    apolloClientY,
    apolloClientZ,
    apolloClientAA,
    apolloClientAB,
    apolloClientAC,
    apolloClientAD,
    apolloClientAE,
    apolloClientAF,
    apolloClientAG,
    apolloClientAH,
    apolloClientAI,
    apolloClientAJ,
    apolloClientAK,
    apolloClientAL,
    apolloClientAM,
    apolloClientAN,
    apolloClientAO,
    apolloClientAP,
    apolloClientAQ,
    apolloClientAR,
    apolloClientAS,
    apolloClientAT,
    apolloClientAU,
    apolloClientAV,
    apolloClientAW,
    apolloClientAX,
    apolloClientAY,
    apolloClientAZ,
    apolloClientBA,
    apolloClientBB,
    apolloClientBC,
    apolloClientBD,
    apolloClientBE,
    apolloClientBF,
    apolloClientBG,
    apolloClientBH,
    apolloClientBI,
  },
  defaultClient: apolloClientA,
});

// Export header functions for use in other components
export { getAuthHeaders };

// Create a provider
export default apolloProvider;
