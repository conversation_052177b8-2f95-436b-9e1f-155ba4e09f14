@font-face {
  font-family: 'hrapp';
  src:  url('fonts/hrapp.eot?bcpsop');
  src:  url('fonts/hrapp.eot?bcpsop#iefix') format('embedded-opentype'),
    url('fonts/hrapp.ttf?bcpsop') format('truetype'),
    url('fonts/hrapp.woff?bcpsop') format('woff'),
    url('fonts/hrapp.svg?bcpsop#hrapp') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="hr-"], [class*=" hr-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'hrapp' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hr-my-finance-my-declarations:before {
  content: "\e900";
}
.hr-asset-management-asset-management:before {
  content: "\e918";
}
.hr-my-finance-my-pay:before {
  content: "\e919";
}
.hr-roster-management-shift-swap:before {
  content: "\e91a";
}
.hr-data-loss-prevention-location-intelligence:before {
  content: "\e91b";
}
.hr-settings-recruitment:before {
  content: "\e91c";
}
.hr-productivity-monitoring-workforce-analytics:before {
  content: "\e91d";
}
.hr-benefits-esop:before {
  content: "\e91e";
}
.hr-benefits:before {
  content: "\e91f";
}
.hr-px:before {
  content: "\e920";
}
.hr-account-code:before {
  content: "\e921";
}
.hr-account-schema-mapping:before {
  content: "\e922";
}
.hr-account-type:before {
  content: "\e923";
}
.hr-add:before {
  content: "\e924";
}
.hr-address-book:before {
  content: "\e925";
}
.hr-address-book-o:before {
  content: "\e926";
}
.hr-address-card:before {
  content: "\e927";
}
.hr-address-card-o:before {
  content: "\e928";
}
.hr-adjust:before {
  content: "\e929";
}
.hr-admin-roles:before {
  content: "\e92a";
}
.hr-adn:before {
  content: "\e92b";
}
.hr-airport-plane:before {
  content: "\e92c";
}
.hr-alert:before {
  content: "\e92d";
}
.hr-alert-1:before {
  content: "\e92e";
}
.hr-alert-2:before {
  content: "\e92f";
}
.hr-alert-3:before {
  content: "\e930";
}
.hr-alert-circled:before {
  content: "\e931";
}
.hr-align-center:before {
  content: "\e932";
}
.hr-align-justify:before {
  content: "\e933";
}
.hr-align-left:before {
  content: "\e934";
}
.hr-align-right:before {
  content: "\e935";
}
.hr-amazon:before {
  content: "\e936";
}
.hr-ambulance:before {
  content: "\e937";
}
.hr-american-sign-language-interpreting:before {
  content: "\e938";
}
.hr-analytics-file-1:before {
  content: "\e939";
}
.hr-anchor:before {
  content: "\e93a";
}
.hr-android:before {
  content: "\e93b";
}
.hr-android-attach:before {
  content: "\e93c";
}
.hr-android-bulb:before {
  content: "\e93d";
}
.hr-android-done:before {
  content: "\e93e";
}
.hr-android-done-all:before {
  content: "\e93f";
}
.hr-angellist:before {
  content: "\e940";
}
.hr-angle-double-down:before {
  content: "\e941";
}
.hr-angle-double-left:before {
  content: "\e942";
}
.hr-angle-double-right:before {
  content: "\e943";
}
.hr-angle-double-up:before {
  content: "\e944";
}
.hr-angle-down:before {
  content: "\e945";
}
.hr-angle-left:before {
  content: "\e946";
}
.hr-angle-right:before {
  content: "\e947";
}
.hr-angle-up:before {
  content: "\e948";
}
.hr-apple:before {
  content: "\e949";
}
.hr-archive:before {
  content: "\e94a";
}
.hr-area-chart:before {
  content: "\e94b";
}
.hr-arrow-circle-down:before {
  content: "\e94c";
}
.hr-arrow-circle-left:before {
  content: "\e94d";
}
.hr-arrow-circle-o-down:before {
  content: "\e94e";
}
.hr-arrow-circle-o-left:before {
  content: "\e94f";
}
.hr-arrow-circle-o-right:before {
  content: "\e950";
}
.hr-arrow-circle-o-up:before {
  content: "\e951";
}
.hr-arrow-circle-right:before {
  content: "\e952";
}
.hr-arrow-circle-up:before {
  content: "\e953";
}
.hr-arrow-down:before {
  content: "\e954";
}
.hr-arrow-left:before {
  content: "\e955";
}
.hr-arrow-right:before {
  content: "\e956";
}
.hr-arrows:before {
  content: "\e957";
}
.hr-arrows-alt:before {
  content: "\e958";
}
.hr-arrows-h:before {
  content: "\e959";
}
.hr-arrows-v:before {
  content: "\e95a";
}
.hr-arrow-up:before {
  content: "\e95b";
}
.hr-asset-management:before {
  content: "\e95c";
}
.hr-asset-management-assets:before {
  content: "\e95d";
}
.hr-assistive-listening-systems:before {
  content: "\e95e";
}
.hr-asterisk:before {
  content: "\e95f";
}
.hr-at:before {
  content: "\e960";
}
.hr-attendance-box:before {
  content: "\e961";
}
.hr-audio-description:before {
  content: "\e962";
}
.hr-automobile:before {
  content: "\e963";
}
.hr-backward:before {
  content: "\e964";
}
.hr-balance-scale:before {
  content: "\e965";
}
.hr-ban:before {
  content: "\e966";
}
.hr-ban1:before {
  content: "\e967";
}
.hr-bandcamp:before {
  content: "\e968";
}
.hr-bank:before {
  content: "\e969";
}
.hr-bar-chart:before {
  content: "\e96a";
}
.hr-barcode:before {
  content: "\e96b";
}
.hr-bars:before {
  content: "\e96c";
}
.hr-bath:before {
  content: "\e96d";
}
.hr-battery:before {
  content: "\e96e";
}
.hr-battery-0:before {
  content: "\e96f";
}
.hr-battery-1:before {
  content: "\e970";
}
.hr-battery-2:before {
  content: "\e971";
}
.hr-battery-3:before {
  content: "\e972";
}
.hr-bed:before {
  content: "\e973";
}
.hr-beer:before {
  content: "\e974";
}
.hr-behance:before {
  content: "\e975";
}
.hr-behance-square:before {
  content: "\e976";
}
.hr-bell:before {
  content: "\e977";
}
.hr-bell1:before {
  content: "\e978";
}
.hr-bell-1:before {
  content: "\e979";
}
.hr-bell-o:before {
  content: "\e97a";
}
.hr-bell-o1:before {
  content: "\e97b";
}
.hr-bell-slash:before {
  content: "\e97c";
}
.hr-bell-slash-o:before {
  content: "\e97d";
}
.hr-bell-two:before {
  content: "\e97e";
}
.hr-bicycle:before {
  content: "\e97f";
}
.hr-billing:before {
  content: "\e980";
}
.hr-billing-billing:before {
  content: "\e981";
}
.hr-binoculars:before {
  content: "\e982";
}
.hr-birthday-cake:before {
  content: "\e983";
}
.hr-birthday-cake1:before {
  content: "\e984";
}
.hr-bitbucket:before {
  content: "\e985";
}
.hr-bitbucket-square:before {
  content: "\e986";
}
.hr-bitcoin:before {
  content: "\e987";
}
.hr-black-tie:before {
  content: "\e988";
}
.hr-blind:before {
  content: "\e989";
}
.hr-bluetooth:before {
  content: "\e98a";
}
.hr-bluetooth-b:before {
  content: "\e98b";
}
.hr-bold:before {
  content: "\e98c";
}
.hr-bolt:before {
  content: "\e98d";
}
.hr-bomb:before {
  content: "\e98e";
}
.hr-book:before {
  content: "\e98f";
}
.hr-bookmark:before {
  content: "\e990";
}
.hr-bookmark-o:before {
  content: "\e991";
}
.hr-braille:before {
  content: "\e992";
}
.hr-briefcase:before {
  content: "\e993";
}
.hr-broadcast:before {
  content: "\e994";
}
.hr-bug:before {
  content: "\e995";
}
.hr-bug1:before {
  content: "\e996";
}
.hr-building:before {
  content: "\e997";
}
.hr-building-o:before {
  content: "\e998";
}
.hr-bulk-copy:before {
  content: "\e999";
}
.hr-bullhorn:before {
  content: "\e99a";
}
.hr-bullseye:before {
  content: "\e99b";
}
.hr-bus:before {
  content: "\e99c";
}
.hr-buysellads:before {
  content: "\e99d";
}
.hr-cab:before {
  content: "\e99e";
}
.hr-calculator:before {
  content: "\e99f";
}
.hr-calculator1:before {
  content: "\e9a0";
}
.hr-calendar:before {
  content: "\e9a1";
}
.hr-calendar1:before {
  content: "\e9a2";
}
.hr-calendar-1:before {
  content: "\e9a3";
}
.hr-calendar-alt-fill:before {
  content: "\e9a4";
}
.hr-calendar-check-o:before {
  content: "\e9a5";
}
.hr-calendar-minus-o:before {
  content: "\e9a6";
}
.hr-calendar-o:before {
  content: "\e9a7";
}
.hr-calendar-plus-o:before {
  content: "\e9a8";
}
.hr-calendar-times-o:before {
  content: "\e9a9";
}
.hr-camera:before {
  content: "\e9aa";
}
.hr-camera-retro:before {
  content: "\e9ab";
}
.hr-cancel-circle:before {
  content: "\e9ac";
}
.hr-caret-down:before {
  content: "\e9ad";
}
.hr-caret-left:before {
  content: "\e9ae";
}
.hr-caret-right:before {
  content: "\e9af";
}
.hr-caret-square-o-down:before {
  content: "\e9b0";
}
.hr-caret-square-o-left:before {
  content: "\e9b1";
}
.hr-caret-square-o-right:before {
  content: "\e9b2";
}
.hr-caret-square-o-up:before {
  content: "\e9b3";
}
.hr-caret-up:before {
  content: "\e9b4";
}
.hr-cart-arrow-down:before {
  content: "\e9b5";
}
.hr-cart-plus:before {
  content: "\e9b6";
}
.hr-cc:before {
  content: "\e9b7";
}
.hr-cc-amex:before {
  content: "\e9b8";
}
.hr-cc-diners-club:before {
  content: "\e9b9";
}
.hr-cc-discover:before {
  content: "\e9ba";
}
.hr-cc-jcb:before {
  content: "\e9bb";
}
.hr-cc-mastercard:before {
  content: "\e9bc";
}
.hr-cc-paypal:before {
  content: "\e9bd";
}
.hr-cc-stripe:before {
  content: "\e9be";
}
.hr-cc-visa:before {
  content: "\e9bf";
}
.hr-certificate:before {
  content: "\e9c0";
}
.hr-chain:before {
  content: "\e9c1";
}
.hr-chain-broken:before {
  content: "\e9c2";
}
.hr-check:before {
  content: "\e9c3";
}
.hr-check1:before {
  content: "\e9c4";
}
.hr-check-all:before {
  content: "\e9c5";
}
.hr-check-circle:before {
  content: "\e9c6";
}
.hr-check-circle1:before {
  content: "\e9c7";
}
.hr-check-circle-o:before {
  content: "\e9c8";
}
.hr-check-square:before {
  content: "\e9c9";
}
.hr-check-square-o:before {
  content: "\e9ca";
}
.hr-chevron-circle-down:before {
  content: "\e9cb";
}
.hr-chevron-circle-left:before {
  content: "\e9cc";
}
.hr-chevron-circle-right:before {
  content: "\e9cd";
}
.hr-chevron-circle-up:before {
  content: "\e9ce";
}
.hr-chevron-down:before {
  content: "\e9cf";
}
.hr-chevron-left:before {
  content: "\e9d0";
}
.hr-chevron-right:before {
  content: "\e9d1";
}
.hr-chevron-up:before {
  content: "\e9d2";
}
.hr-child:before {
  content: "\e9d3";
}
.hr-chrome:before {
  content: "\e9d4";
}
.hr-circle:before {
  content: "\e9d5";
}
.hr-circle-o:before {
  content: "\e9d6";
}
.hr-circle-o-notch:before {
  content: "\e9d7";
}
.hr-circle-thin:before {
  content: "\e9d8";
}
.hr-clipboard:before {
  content: "\e9d9";
}
.hr-clock-o:before {
  content: "\e9da";
}
.hr-clone:before {
  content: "\e9db";
}
.hr-clone-roles:before {
  content: "\e9dc";
}
.hr-close:before {
  content: "\e9dd";
}
.hr-close1:before {
  content: "\e9de";
}
.hr-close-1:before {
  content: "\e9df";
}
.hr-close-round:before {
  content: "\e9e0";
}
.hr-cloud:before {
  content: "\e9e1";
}
.hr-cloud-download:before {
  content: "\e9e2";
}
.hr-cloud-download1:before {
  content: "\e9e3";
}
.hr-cloud-upload:before {
  content: "\e9e4";
}
.hr-cloud-upload1:before {
  content: "\e9e5";
}
.hr-cny:before {
  content: "\e9e6";
}
.hr-code:before {
  content: "\e9e7";
}
.hr-code-fork:before {
  content: "\e9e8";
}
.hr-codepen:before {
  content: "\e9e9";
}
.hr-codiepie:before {
  content: "\e9ea";
}
.hr-coffee:before {
  content: "\e9eb";
}
.hr-cog:before {
  content: "\e9ec";
}
.hr-cogs:before {
  content: "\e9ed";
}
.hr-cog-solid:before {
  content: "\e9ee";
}
.hr-columns:before {
  content: "\e9ef";
}
.hr-comment:before {
  content: "\e9f0";
}
.hr-commenting:before {
  content: "\e9f1";
}
.hr-commenting-o:before {
  content: "\e9f2";
}
.hr-comment-o:before {
  content: "\e9f3";
}
.hr-comments:before {
  content: "\e9f4";
}
.hr-comments-o:before {
  content: "\e9f5";
}
.hr-compass:before {
  content: "\e9f6";
}
.hr-compress:before {
  content: "\e9f7";
}
.hr-connectdevelop:before {
  content: "\e9f8";
}
.hr-contacs:before {
  content: "\e9f9";
}
.hr-contact:before {
  content: "\e9fa";
}
.hr-contact-2:before {
  content: "\e9fb";
}
.hr-contact-add:before {
  content: "\e9fc";
}
.hr-contact-add-2:before {
  content: "\e9fd";
}
.hr-contact-add-3:before {
  content: "\e9fe";
}
.hr-contact-big:before {
  content: "\e9ff";
}
.hr-contact-details:before {
  content: "\ea00";
}
.hr-contacts:before {
  content: "\ea01";
}
.hr-contao:before {
  content: "\ea02";
}
.hr-copy:before {
  content: "\ea03";
}
.hr-copy1:before {
  content: "\ea04";
}
.hr-copyright:before {
  content: "\ea05";
}
.hr-creative-commons:before {
  content: "\ea06";
}
.hr-credit-card:before {
  content: "\ea07";
}
.hr-credit-card-alt:before {
  content: "\ea08";
}
.hr-crop:before {
  content: "\ea09";
}
.hr-crosshairs:before {
  content: "\ea0a";
}
.hr-cross-mark:before {
  content: "\ea0b";
}
.hr-crown-king-streamline:before {
  content: "\ea0c";
}
.hr-css3:before {
  content: "\ea0d";
}
.hr-csv:before {
  content: "\ea0e";
}
.hr-cube:before {
  content: "\ea0f";
}
.hr-cubes:before {
  content: "\ea10";
}
.hr-cut:before {
  content: "\ea11";
}
.hr-cutlery:before {
  content: "\ea12";
}
.hr-danger:before {
  content: "\ea13";
}
.hr-dashboard:before {
  content: "\ea14";
}
.hr-dashcube:before {
  content: "\ea15";
}
.hr-database:before {
  content: "\ea16";
}
.hr-database1:before {
  content: "\ea17";
}
.hr-database-add:before {
  content: "\ea18";
}
.hr-database-edit:before {
  content: "\ea19";
}
.hr-database-information:before {
  content: "\ea1a";
}
.hr-database-remove:before {
  content: "\ea1b";
}
.hr-database-run:before {
  content: "\ea1c";
}
.hr-database-security:before {
  content: "\ea1d";
}
.hr-deaf:before {
  content: "\ea1e";
}
.hr-dedent:before {
  content: "\ea1f";
}
.hr-delete:before {
  content: "\ea20";
}
.hr-delicious:before {
  content: "\ea21";
}
.hr-department:before {
  content: "\ea22";
}
.hr-desktop:before {
  content: "\ea23";
}
.hr-deviantart:before {
  content: "\ea24";
}
.hr-diamond:before {
  content: "\ea25";
}
.hr-diff:before {
  content: "\ea26";
}
.hr-digg:before {
  content: "\ea27";
}
.hr-document-sans-accept:before {
  content: "\ea28";
}
.hr-document-sans-add:before {
  content: "\ea29";
}
.hr-document-sans-cancel:before {
  content: "\ea2a";
}
.hr-document-sans-down:before {
  content: "\ea2b";
}
.hr-document-sans-edit:before {
  content: "\ea2c";
}
.hr-document-sans-information:before {
  content: "\ea2d";
}
.hr-document-sans-remove:before {
  content: "\ea2e";
}
.hr-document-sans-run:before {
  content: "\ea2f";
}
.hr-document-sans-security:before {
  content: "\ea30";
}
.hr-document-sans-settings:before {
  content: "\ea31";
}
.hr-document-sans-up:before {
  content: "\ea32";
}
.hr-document-text:before {
  content: "\ea33";
}
.hr-document-text-add:before {
  content: "\ea34";
}
.hr-document-text-down:before {
  content: "\ea35";
}
.hr-document-text-information:before {
  content: "\ea36";
}
.hr-document-text-remove:before {
  content: "\ea37";
}
.hr-document-text-run:before {
  content: "\ea38";
}
.hr-document-text-security:before {
  content: "\ea39";
}
.hr-document-text-up:before {
  content: "\ea3a";
}
.hr-dollar:before {
  content: "\ea3b";
}
.hr-donate:before {
  content: "\ea3c";
}
.hr-dot-circle-o:before {
  content: "\ea3d";
}
.hr-download:before {
  content: "\ea3e";
}
.hr-download1:before {
  content: "\ea3f";
}
.hr-download-1:before {
  content: "\ea40";
}
.hr-download-2:before {
  content: "\ea41";
}
.hr-download-accept:before {
  content: "\ea42";
}
.hr-download-cancel:before {
  content: "\ea43";
}
.hr-download-information:before {
  content: "\ea44";
}
.hr-dribbble:before {
  content: "\ea45";
}
.hr-drivers-license:before {
  content: "\ea46";
}
.hr-drivers-license-o:before {
  content: "\ea47";
}
.hr-dropbox:before {
  content: "\ea48";
}
.hr-drupal:before {
  content: "\ea49";
}
.hr-edge:before {
  content: "\ea4a";
}
.hr-edit:before {
  content: "\ea4b";
}
.hr-edit1:before {
  content: "\ea4c";
}
.hr-eercast:before {
  content: "\ea4d";
}
.hr-eject:before {
  content: "\ea4e";
}
.hr-electric-no-off:before {
  content: "\ea4f";
}
.hr-ellipsis-h:before {
  content: "\ea50";
}
.hr-ellipsis-v:before {
  content: "\ea51";
}
.hr-empire:before {
  content: "\ea52";
}
.hr-employee-awards-1:before {
  content: "\ea53";
}
.hr-employee-clone:before {
  content: "\ea54";
}
.hr-employee-confirmation:before {
  content: "\ea55";
}
.hr-employee-designation:before {
  content: "\ea56";
}
.hr-employee-probation:before {
  content: "\ea57";
}
.hr-employees:before {
  content: "\ea58";
}
.hr-employees-assignments:before {
  content: "\ea59";
}
.hr-employees-attendance:before {
  content: "\ea5a";
}
.hr-employees-awards:before {
  content: "\ea5b";
}
.hr-employees-compensatory-off:before {
  content: "\ea5c";
}
.hr-employees-complaints:before {
  content: "\ea5d";
}
.hr-employees-custom-employee-groups:before {
  content: "\ea5e";
}
.hr-employees-designations:before {
  content: "\ea5f";
}
.hr-employees-employee-bank-account:before {
  content: "\ea60";
}
.hr-employees-employees:before {
  content: "\ea61";
}
.hr-employees-employees-document-upload:before {
  content: "\ea62";
}
.hr-employees-employee-travel:before {
  content: "\ea63";
}
.hr-employees-employee-type:before {
  content: "\ea64";
}
.hr-employees-grades:before {
  content: "\ea65";
}
.hr-employees-inbox:before {
  content: "\ea66";
}
.hr-employee-skillset:before {
  content: "\ea67";
}
.hr-employee-skillset-1:before {
  content: "\ea68";
}
.hr-employees-leaves:before {
  content: "\ea69";
}
.hr-employees-leave-types:before {
  content: "\ea6a";
}
.hr-employees-memos:before {
  content: "\ea6b";
}
.hr-employees-organization-chart:before {
  content: "\ea6c";
}
.hr-employees-organization-chart-fit-horizontal:before {
  content: "\ea6d";
}
.hr-employees-organization-chart-fit-reset:before {
  content: "\ea6e";
}
.hr-employees-organization-chart-fit-vertical:before {
  content: "\ea6f";
}
.hr-employees-organization-chart-reset-hierarchy-path:before {
  content: "\ea70";
}
.hr-employees-organization-chart-view-hierarchy-path:before {
  content: "\ea71";
}
.hr-performance-management-performance-evaluation:before {
  content: "\ea72";
}
.hr-employees-resignation:before {
  content: "\ea73";
}
.hr-employees-roles-template:before {
  content: "\ea74";
}
.hr-employees-short-time-off:before {
  content: "\ea75";
}
.hr-employees-skillset-assessment:before {
  content: "\ea76";
}
.hr-employees-timesheet-hours:before {
  content: "\ea77";
}
.hr-employee-self-service-timesheets:before {
  content: "\ea78";
}
.hr-employees-transfer:before {
  content: "\ea79";
}
.hr-employees-warnings:before {
  content: "\ea7a";
}
.hr-enterprise:before {
  content: "\ea7b";
}
.hr-entrance:before {
  content: "\ea7c";
}
.hr-envelope:before {
  content: "\ea7d";
}
.hr-envelope-o:before {
  content: "\ea7e";
}
.hr-envelope-open:before {
  content: "\ea7f";
}
.hr-envelope-open-o:before {
  content: "\ea80";
}
.hr-envelope-square:before {
  content: "\ea81";
}
.hr-envira:before {
  content: "\ea82";
}
.hr-eraser:before {
  content: "\ea83";
}
.hr-etsy:before {
  content: "\ea84";
}
.hr-eur:before {
  content: "\ea85";
}
.hr-exchange:before {
  content: "\ea86";
}
.hr-exclamation:before {
  content: "\ea87";
}
.hr-exclamation1:before {
  content: "\ea88";
}
.hr-exclamation-1:before {
  content: "\ea89";
}
.hr-exclamation-2:before {
  content: "\ea8a";
}
.hr-exclamation-3:before {
  content: "\ea8b";
}
.hr-exclamation-circle:before {
  content: "\ea8c";
}
.hr-exclamation-circle1:before {
  content: "\ea8d";
}
.hr-exclamation-circle-1:before {
  content: "\ea8e";
}
.hr-exclamation-triangle:before {
  content: "\ea8f";
}
.hr-exclamation-triangle1:before {
  content: "\ea90";
}
.hr-exclamation-triangle-1:before {
  content: "\ea91";
}
.hr-exclude-break-hours:before {
  content: "\ea92";
}
.hr-expand:before {
  content: "\ea93";
}
.hr-expand1:before {
  content: "\ea94";
}
.hr-expeditedssl:before {
  content: "\ea95";
}
.hr-external-link:before {
  content: "\ea96";
}
.hr-external-link-square:before {
  content: "\ea97";
}
.hr-eye:before {
  content: "\ea98";
}
.hr-eyedropper:before {
  content: "\ea99";
}
.hr-eye-slash:before {
  content: "\ea9a";
}
.hr-fa:before {
  content: "\ea9b";
}
.hr-facebook:before {
  content: "\ea9c";
}
.hr-facebook-official:before {
  content: "\ea9d";
}
.hr-facebook-square:before {
  content: "\ea9e";
}
.hr-fast-backward:before {
  content: "\ea9f";
}
.hr-fast-forward:before {
  content: "\eaa0";
}
.hr-fax:before {
  content: "\eaa1";
}
.hr-feed:before {
  content: "\eaa2";
}
.hr-female:before {
  content: "\eaa3";
}
.hr-female1:before {
  content: "\eaa4";
}
.hr-female-rounded-1:before {
  content: "\eaa5";
}
.hr-fighter-jet:before {
  content: "\eaa6";
}
.hr-file:before {
  content: "\eaa7";
}
.hr-file-archive-o:before {
  content: "\eaa8";
}
.hr-file-audio-o:before {
  content: "\eaa9";
}
.hr-file-code-o:before {
  content: "\eaaa";
}
.hr-file-excel-o:before {
  content: "\eaab";
}
.hr-file-image-o:before {
  content: "\eaac";
}
.hr-file-movie-o:before {
  content: "\eaad";
}
.hr-file-o:before {
  content: "\eaae";
}
.hr-file-pdf-o:before {
  content: "\eaaf";
}
.hr-file-powerpoint-o:before {
  content: "\eab0";
}
.hr-file-submodule:before {
  content: "\eab1";
}
.hr-file-symlink-directory:before {
  content: "\eab2";
}
.hr-file-text:before {
  content: "\eab3";
}
.hr-file-text-o:before {
  content: "\eab4";
}
.hr-file-word-o:before {
  content: "\eab5";
}
.hr-film:before {
  content: "\eab6";
}
.hr-filter:before {
  content: "\eab7";
}
.hr-financeclosure:before {
  content: "\eab8";
}
.hr-financial-closure:before {
  content: "\eab9";
}
.hr-fire:before {
  content: "\eaba";
}
.hr-fire-extinguisher:before {
  content: "\eabb";
}
.hr-firefox:before {
  content: "\eabc";
}
.hr-first-order:before {
  content: "\eabd";
}
.hr-flag:before {
  content: "\eabe";
}
.hr-flag-checkered:before {
  content: "\eabf";
}
.hr-flag-o:before {
  content: "\eac0";
}
.hr-flask:before {
  content: "\eac1";
}
.hr-flickr:before {
  content: "\eac2";
}
.hr-floppy-disk:before {
  content: "\eac3";
}
.hr-floppy-o:before {
  content: "\eac4";
}
.hr-folder:before {
  content: "\eac5";
}
.hr-folder-downloads:before {
  content: "\eac6";
}
.hr-folder-image:before {
  content: "\eac7";
}
.hr-folder-music:before {
  content: "\eac8";
}
.hr-folder-o:before {
  content: "\eac9";
}
.hr-folder-open:before {
  content: "\eaca";
}
.hr-folder-open-o:before {
  content: "\eacb";
}
.hr-folder-sans:before {
  content: "\eacc";
}
.hr-folder-sans-accept:before {
  content: "\eacd";
}
.hr-folder-sans-add:before {
  content: "\eace";
}
.hr-folder-sans-cancel:before {
  content: "\eacf";
}
.hr-folder-sans-down:before {
  content: "\ead0";
}
.hr-folder-sans-edit:before {
  content: "\ead1";
}
.hr-folder-sans-information:before {
  content: "\ead2";
}
.hr-folder-sans-remove:before {
  content: "\ead3";
}
.hr-folder-sans-run:before {
  content: "\ead4";
}
.hr-folder-sans-security:before {
  content: "\ead5";
}
.hr-folder-sans-settings:before {
  content: "\ead6";
}
.hr-folder-sans-up:before {
  content: "\ead7";
}
.hr-folder-text:before {
  content: "\ead8";
}
.hr-folder-video:before {
  content: "\ead9";
}
.hr-font:before {
  content: "\eada";
}
.hr-fonticons:before {
  content: "\eadb";
}
.hr-tax-and-statutory-compliance:before {
  content: "\eadc";
}
.hr-tax-and-statutory-compliance-compliance-forms:before {
  content: "\eadd";
}
.hr-compliance-management-docusign:before {
  content: "\eade";
}
.hr-forms-manager-document-template-engine:before {
  content: "\eadf";
}
.hr-workflow-dynamic-form-builder:before {
  content: "\eae0";
}
.hr-forms-manager-form16:before {
  content: "\eae1";
}
.hr-tax-and-statutory-compliance-form-downloads:before {
  content: "\eae2";
}
.hr-forms-manager-form-f:before {
  content: "\eae3";
}
.hr-forms-manager-form-g:before {
  content: "\eae4";
}
.hr-fort-awesome:before {
  content: "\eae5";
}
.hr-forumbee:before {
  content: "\eae6";
}
.hr-forward:before {
  content: "\eae7";
}
.hr-foursquare:before {
  content: "\eae8";
}
.hr-free-code-camp:before {
  content: "\eae9";
}
.hr-friends:before {
  content: "\eaea";
}
.hr-frown-o:before {
  content: "\eaeb";
}
.hr-futbol-o:before {
  content: "\eaec";
}
.hr-gamepad:before {
  content: "\eaed";
}
.hr-gavel:before {
  content: "\eaee";
}
.hr-gbp:before {
  content: "\eaef";
}
.hr-genderless:before {
  content: "\eaf0";
}
.hr-get-pocket:before {
  content: "\eaf1";
}
.hr-gg:before {
  content: "\eaf2";
}
.hr-gg-circle:before {
  content: "\eaf3";
}
.hr-gift:before {
  content: "\eaf4";
}
.hr-git:before {
  content: "\eaf5";
}
.hr-github:before {
  content: "\eaf6";
}
.hr-github-alt:before {
  content: "\eaf7";
}
.hr-github-square:before {
  content: "\eaf8";
}
.hr-gitlab:before {
  content: "\eaf9";
}
.hr-git-square:before {
  content: "\eafa";
}
.hr-gittip:before {
  content: "\eafb";
}
.hr-glass:before {
  content: "\eafc";
}
.hr-glide:before {
  content: "\eafd";
}
.hr-glide-g:before {
  content: "\eafe";
}
.hr-globe:before {
  content: "\eaff";
}
.hr-google:before {
  content: "\eb00";
}
.hr-google-plus:before {
  content: "\eb01";
}
.hr-google-plus-circle:before {
  content: "\eb02";
}
.hr-google-plus-square:before {
  content: "\eb03";
}
.hr-google-wallet:before {
  content: "\eb04";
}
.hr-graduation-cap:before {
  content: "\eb05";
}
.hr-grav:before {
  content: "\eb06";
}
.hr-group:before {
  content: "\eb07";
}
.hr-group-full:before {
  content: "\eb08";
}
.hr-group-full-edit:before {
  content: "\eb09";
}
.hr-group-full-security:before {
  content: "\eb0a";
}
.hr-group-half:before {
  content: "\eb0b";
}
.hr-group-half-edit:before {
  content: "\eb0c";
}
.hr-hacker-news:before {
  content: "\eb0d";
}
.hr-hand-grab-o:before {
  content: "\eb0e";
}
.hr-hand-lizard-o:before {
  content: "\eb0f";
}
.hr-hand-o-down:before {
  content: "\eb10";
}
.hr-hand-o-left:before {
  content: "\eb11";
}
.hr-hand-o-right:before {
  content: "\eb12";
}
.hr-hand-o-up:before {
  content: "\eb13";
}
.hr-hand-paper-o:before {
  content: "\eb14";
}
.hr-hand-peace-o:before {
  content: "\eb15";
}
.hr-hand-pointer-o:before {
  content: "\eb16";
}
.hr-hand-scissors-o:before {
  content: "\eb17";
}
.hr-handshake-o:before {
  content: "\eb18";
}
.hr-hand-spock-o:before {
  content: "\eb19";
}
.hr-hashtag:before {
  content: "\eb1a";
}
.hr-hdd-o:before {
  content: "\eb1b";
}
.hr-header:before {
  content: "\eb1c";
}
.hr-headphones:before {
  content: "\eb1d";
}
.hr-heart:before {
  content: "\eb1e";
}
.hr-heartbeat:before {
  content: "\eb1f";
}
.hr-heart-o:before {
  content: "\eb20";
}
.hr-help:before {
  content: "\eb21";
}
.hr-help-contact-us:before {
  content: "\eb22";
}
.hr-help-help-topics:before {
  content: "\eb23";
}
.hr-history:before {
  content: "\eb24";
}
.hr-history1:before {
  content: "\eb25";
}
.hr-home:before {
  content: "\eb26";
}
.hr-home1:before {
  content: "\eb27";
}
.hr-horizontal-rule:before {
  content: "\eb28";
}
.hr-hospital-o:before {
  content: "\eb29";
}
.hr-hourglass:before {
  content: "\eb2a";
}
.hr-hourglass-1:before {
  content: "\eb2b";
}
.hr-hourglass-2:before {
  content: "\eb2c";
}
.hr-hourglass-3:before {
  content: "\eb2d";
}
.hr-hourglass-o:before {
  content: "\eb2e";
}
.hr-hourly-master-report:before {
  content: "\eb2f";
}
.hr-hourly-payment:before {
  content: "\eb30";
}
.hr-houzz:before {
  content: "\eb31";
}
.hr-hr-group:before {
  content: "\eb32";
}
.hr-hr-report-employee-education:before {
  content: "\eb33";
}
.hr-hr-report-new-joinees:before {
  content: "\eb34";
}
.hr-hr-tax-section-allowance-mapping:before {
  content: "\eb35";
}
.hr-h-square:before {
  content: "\eb36";
}
.hr-html5:before {
  content: "\eb37";
}
.hr-i-cursor:before {
  content: "\eb38";
}
.hr-id-badge:before {
  content: "\eb39";
}
.hr-ils:before {
  content: "\eb3a";
}
.hr-image:before {
  content: "\eb3b";
}
.hr-imdb:before {
  content: "\eb3c";
}
.hr-inbox:before {
  content: "\eb3d";
}
.hr-include-break-hours:before {
  content: "\eb3e";
}
.hr-indent:before {
  content: "\eb3f";
}
.hr-industry:before {
  content: "\eb40";
}
.hr-info:before {
  content: "\eb41";
}
.hr-info1:before {
  content: "\eb42";
}
.hr-info-circle:before {
  content: "\eb43";
}
.hr-inr:before {
  content: "\eb44";
}
.hr-instagram:before {
  content: "\eb45";
}
.hr-insurance-payment-tracker:before {
  content: "\eb46";
}
.hr-insurance-variable:before {
  content: "\eb47";
}
.hr-integration:before {
  content: "\eb48";
}
.hr-integration-api-dashboard:before {
  content: "\eb49";
}
.hr-integration-gvp:before {
  content: "\eb4a";
}
.hr-integration-ocr:before {
  content: "\eb4b";
}
.hr-internet-explorer:before {
  content: "\eb4c";
}
.hr-intersex:before {
  content: "\eb4d";
}
.hr-ios-refresh-outline:before {
  content: "\eb4e";
}
.hr-ios-upload:before {
  content: "\eb4f";
}
.hr-ios-upload-outline:before {
  content: "\eb50";
}
.hr-ioxhost:before {
  content: "\eb51";
}
.hr-issue-closed:before {
  content: "\eb52";
}
.hr-issue-reopened:before {
  content: "\eb53";
}
.hr-italic:before {
  content: "\eb54";
}
.hr-joomla:before {
  content: "\eb55";
}
.hr-jsfiddle:before {
  content: "\eb56";
}
.hr-key:before {
  content: "\eb57";
}
.hr-key1:before {
  content: "\eb58";
}
.hr-key-1:before {
  content: "\eb59";
}
.hr-key-2:before {
  content: "\eb5a";
}
.hr-key-3:before {
  content: "\eb5b";
}
.hr-key-4:before {
  content: "\eb5c";
}
.hr-key-5:before {
  content: "\eb5d";
}
.hr-key-6:before {
  content: "\eb5e";
}
.hr-keyboard-o:before {
  content: "\eb5f";
}
.hr-key-fill:before {
  content: "\eb60";
}
.hr-key-stroke:before {
  content: "\eb61";
}
.hr-krw:before {
  content: "\eb62";
}
.hr-language:before {
  content: "\eb63";
}
.hr-laptop:before {
  content: "\eb64";
}
.hr-lastfm:before {
  content: "\eb65";
}
.hr-lastfm-square:before {
  content: "\eb66";
}
.hr-leaf:before {
  content: "\eb67";
}
.hr-leanpub:before {
  content: "\eb68";
}
.hr-lemon-o:before {
  content: "\eb69";
}
.hr-level-down:before {
  content: "\eb6a";
}
.hr-level-up:before {
  content: "\eb6b";
}
.hr-life-bouy:before {
  content: "\eb6c";
}
.hr-light-bulb:before {
  content: "\eb6d";
}
.hr-lightbulb-o:before {
  content: "\eb6e";
}
.hr-line-chart:before {
  content: "\eb6f";
}
.hr-linkedin:before {
  content: "\eb70";
}
.hr-linkedin-square:before {
  content: "\eb71";
}
.hr-linode:before {
  content: "\eb72";
}
.hr-linux:before {
  content: "\eb73";
}
.hr-list:before {
  content: "\eb74";
}
.hr-list-add:before {
  content: "\eb75";
}
.hr-list-alt:before {
  content: "\eb76";
}
.hr-list-ol:before {
  content: "\eb77";
}
.hr-list-ul:before {
  content: "\eb78";
}
.hr-location-arrow:before {
  content: "\eb79";
}
.hr-lock:before {
  content: "\eb7a";
}
.hr-lock-alt:before {
  content: "\eb7b";
}
.hr-long-arrow-down:before {
  content: "\eb7c";
}
.hr-long-arrow-left:before {
  content: "\eb7d";
}
.hr-long-arrow-right:before {
  content: "\eb7e";
}
.hr-long-arrow-up:before {
  content: "\eb7f";
}
.hr-low-vision:before {
  content: "\eb80";
}
.hr-magic:before {
  content: "\eb81";
}
.hr-magic-wand:before {
  content: "\eb82";
}
.hr-magnet:before {
  content: "\eb83";
}
.hr-mail:before {
  content: "\eb84";
}
.hr-mail-forward:before {
  content: "\eb85";
}
.hr-mail-read:before {
  content: "\eb86";
}
.hr-mail-reply:before {
  content: "\eb87";
}
.hr-mail-reply1:before {
  content: "\eb88";
}
.hr-mail-reply-all:before {
  content: "\eb89";
}
.hr-male:before {
  content: "\eb8a";
}
.hr-male1:before {
  content: "\eb8b";
}
.hr-male-rounded-1:before {
  content: "\eb8c";
}
.hr-male-user-1:before {
  content: "\eb8d";
}
.hr-map:before {
  content: "\eb8e";
}
.hr-map-marker:before {
  content: "\eb8f";
}
.hr-map-o:before {
  content: "\eb90";
}
.hr-map-pin:before {
  content: "\eb91";
}
.hr-map-signs:before {
  content: "\eb92";
}
.hr-mars:before {
  content: "\eb93";
}
.hr-mars-double:before {
  content: "\eb94";
}
.hr-mars-stroke:before {
  content: "\eb95";
}
.hr-mars-stroke-h:before {
  content: "\eb96";
}
.hr-mars-stroke-v:before {
  content: "\eb97";
}
.hr-maxcdn:before {
  content: "\eb98";
}
.hr-meanpath:before {
  content: "\eb99";
}
.hr-medium:before {
  content: "\eb9a";
}
.hr-medkit:before {
  content: "\eb9b";
}
.hr-meetup:before {
  content: "\eb9c";
}
.hr-meh-o:before {
  content: "\eb9d";
}
.hr-menu:before {
  content: "\eb9e";
}
.hr-mercury:before {
  content: "\eb9f";
}
.hr-microchip:before {
  content: "\eba0";
}
.hr-microphone:before {
  content: "\eba1";
}
.hr-microphone-slash:before {
  content: "\eba2";
}
.hr-minus:before {
  content: "\eba3";
}
.hr-minus-circle:before {
  content: "\eba4";
}
.hr-minus-square:before {
  content: "\eba5";
}
.hr-minus-square-o:before {
  content: "\eba6";
}
.hr-mixcloud:before {
  content: "\eba7";
}
.hr-mobile:before {
  content: "\eba8";
}
.hr-modx:before {
  content: "\eba9";
}
.hr-money:before {
  content: "\ebaa";
}
.hr-monthly-payment:before {
  content: "\ebab";
}
.hr-montly-master-report:before {
  content: "\ebac";
}
.hr-moon-o:before {
  content: "\ebad";
}
.hr-motorcycle:before {
  content: "\ebae";
}
.hr-mouse-pointer:before {
  content: "\ebaf";
}
.hr-music:before {
  content: "\ebb0";
}
.hr-neuter:before {
  content: "\ebb1";
}
.hr-newspaper-o:before {
  content: "\ebb2";
}
.hr-object-group:before {
  content: "\ebb3";
}
.hr-object-ungroup:before {
  content: "\ebb4";
}
.hr-odnoklassniki:before {
  content: "\ebb5";
}
.hr-odnoklassniki-square:before {
  content: "\ebb6";
}
.hr-opencart:before {
  content: "\ebb7";
}
.hr-openid:before {
  content: "\ebb8";
}
.hr-opera:before {
  content: "\ebb9";
}
.hr-optin-monster:before {
  content: "\ebba";
}
.hr-optional-choice:before {
  content: "\ebbb";
}
.hr-organization:before {
  content: "\ebbc";
}
.hr-organization-1:before {
  content: "\ebbd";
}
.hr-organization-announcements:before {
  content: "\ebbe";
}
.hr-organization-data-import:before {
  content: "\ebbf";
}
.hr-organization-data-import-1-1:before {
  content: "\ebc0";
}
.hr-organization-department-hierarchy:before {
  content: "\ebc1";
}
.hr-organization-details:before {
  content: "\ebc2";
}
.hr-organization-eft-configuration:before {
  content: "\ebc3";
}
.hr-organization-eft-configuration-deregister:before {
  content: "\ebc4";
}
.hr-organization-special-wages:before {
  content: "\ebc5";
}
.hr-organization-holiday-types:before {
  content: "\ebc6";
}
.hr-organization-locations:before {
  content: "\ebc7";
}
.hr-organization-organization-policies:before {
  content: "\ebc8";
}
.hr-organization-organization-profile:before {
  content: "\ebc9";
}
.hr-organization-organization-settings:before {
  content: "\ebca";
}
.hr-core-hr-employee-data-management:before {
  content: "\ebcb";
}
.hr-organization-projects-1:before {
  content: "\ebcc";
}
.hr-organization-system-log:before {
  content: "\ebcd";
}
.hr-organization-work-schedule:before {
  content: "\ebce";
}
.hr-pagelines:before {
  content: "\ebcf";
}
.hr-page-pdf:before {
  content: "\ebd0";
}
.hr-paint-brush:before {
  content: "\ebd1";
}
.hr-paperclip:before {
  content: "\ebd2";
}
.hr-paper-plane:before {
  content: "\ebd3";
}
.hr-paper-plane-o:before {
  content: "\ebd4";
}
.hr-paragraph:before {
  content: "\ebd5";
}
.hr-pause:before {
  content: "\ebd6";
}
.hr-pause-circle:before {
  content: "\ebd7";
}
.hr-pause-circle-o:before {
  content: "\ebd8";
}
.hr-paw:before {
  content: "\ebd9";
}
.hr-payment-tracker:before {
  content: "\ebda";
}
.hr-paypal:before {
  content: "\ebdb";
}
.hr-payroll:before {
  content: "\ebdc";
}
.hr-payroll-2:before {
  content: "\ebdd";
}
.hr-payroll-advance-salary:before {
  content: "\ebde";
}
.hr-payroll-allowances:before {
  content: "\ebdf";
}
.hr-payroll-bonus:before {
  content: "\ebe0";
}
.hr-payroll-commission:before {
  content: "\ebe1";
}
.hr-payroll-deductions:before {
  content: "\ebe2";
}
.hr-tax-and-statutory-compliance-nps:before {
  content: "\ebe3";
}
.hr-payroll-final-settlement:before {
  content: "\ebe4";
}
.hr-tax-and-statutory-compliance-fixed-health-insurance:before {
  content: "\ebe5";
}
.hr-payroll-flexi-benefit-declaration:before {
  content: "\ebe6";
}
.hr-tax-and-statutory-compliance-gratuity:before {
  content: "\ebe7";
}
.hr-payroll-gratuity-nomination:before {
  content: "\ebe8";
}
.hr-payroll-group:before {
  content: "\ebe9";
}
.hr-tax-and-statutory-compliance-insurance:before {
  content: "\ebea";
}
.hr-tax-and-statutory-compliance-labour-welfare-fund:before {
  content: "\ebeb";
}
.hr-payroll-loan:before {
  content: "\ebec";
}
.hr-payroll-additional-wage-claim:before {
  content: "\ebed";
}
.hr-payroll-payout:before {
  content: "\ebee";
}
.hr-payroll-payout-history:before {
  content: "\ebef";
}
.hr-payroll-payslip-template:before {
  content: "\ebf0";
}
.hr-tax-and-statutory-compliance-perquisite-tracker:before {
  content: "\ebf1";
}
.hr-payroll-proof-of-investment:before {
  content: "\ebf2";
}
.hr-tax-and-statutory-compliance-provident-fund:before {
  content: "\ebf3";
}
.hr-payroll-reimbursement:before {
  content: "\ebf4";
}
.hr-payroll-reimbursement-bank-statement:before {
  content: "\ebf5";
}
.hr-payroll-salary:before {
  content: "\ebf6";
}
.hr-payroll-salary-payslip:before {
  content: "\ebf7";
}
.hr-payroll-salary-template:before {
  content: "\ebf8";
}
.hr-payroll-shift-allowance:before {
  content: "\ebf9";
}
.hr-tax-and-statutory-compliance-tax-declarations:before {
  content: "\ebfa";
}
.hr-payroll-tax-declarations-1:before {
  content: "\ebfb";
}
.hr-tax-and-statutory-compliance-tax-rules:before {
  content: "\ebfc";
}
.hr-tax-and-statutory-compliance-tds-history:before {
  content: "\ebfd";
}
.hr-pencil:before {
  content: "\ebfe";
}
.hr-pencil-square:before {
  content: "\ebff";
}
.hr-percent:before {
  content: "\ec00";
}
.hr-performance-evaluation:before {
  content: "\ec01";
}
.hr-phone:before {
  content: "\ec02";
}
.hr-phone-square:before {
  content: "\ec03";
}
.hr-pie-chart:before {
  content: "\ec04";
}
.hr-pied-piper:before {
  content: "\ec05";
}
.hr-pied-piper-alt:before {
  content: "\ec06";
}
.hr-pied-piper-pp:before {
  content: "\ec07";
}
.hr-pinterest:before {
  content: "\ec08";
}
.hr-pinterest-p:before {
  content: "\ec09";
}
.hr-pinterest-square:before {
  content: "\ec0a";
}
.hr-plane:before {
  content: "\ec0b";
}
.hr-play:before {
  content: "\ec0c";
}
.hr-play-circle:before {
  content: "\ec0d";
}
.hr-play-circle-o:before {
  content: "\ec0e";
}
.hr-plug:before {
  content: "\ec0f";
}
.hr-plus:before {
  content: "\ec10";
}
.hr-plus-circle:before {
  content: "\ec11";
}
.hr-plus-square:before {
  content: "\ec12";
}
.hr-plus-square-o:before {
  content: "\ec13";
}
.hr-podcast:before {
  content: "\ec14";
}
.hr-power-off:before {
  content: "\ec15";
}
.hr-print:before {
  content: "\ec16";
}
.hr-print1:before {
  content: "\ec17";
}
.hr-product-hunt:before {
  content: "\ec18";
}
.hr-productivity-monitoring:before {
  content: "\ec19";
}
.hr-productivity-monitoring-activity-dashboard:before {
  content: "\ec1a";
}
.hr-productivity-monitoring-activity-tracker:before {
  content: "\ec1b";
}
.hr-productivity-monitoring-members:before {
  content: "\ec1c";
}
.hr-productivity-monitoring-reports:before {
  content: "\ec1d";
}
.hr-professional-tax:before {
  content: "\ec1e";
}
.hr-puzzle-piece:before {
  content: "\ec1f";
}
.hr-qq:before {
  content: "\ec20";
}
.hr-qrcode:before {
  content: "\ec21";
}
.hr-question:before {
  content: "\ec22";
}
.hr-question-circle:before {
  content: "\ec23";
}
.hr-question-circle-o:before {
  content: "\ec24";
}
.hr-quick-menu:before {
  content: "\ec25";
}
.hr-quora:before {
  content: "\ec26";
}
.hr-quote-left:before {
  content: "\ec27";
}
.hr-quote-right:before {
  content: "\ec28";
}
.hr-ra:before {
  content: "\ec29";
}
.hr-random:before {
  content: "\ec2a";
}
.hr-ravelry:before {
  content: "\ec2b";
}
.hr-recruitment:before {
  content: "\ec2c";
}
.hr-recruitment_clients_chat:before {
  content: "\ec2d";
}
.hr-recruitment_clients_import:before {
  content: "\ec2e";
}
.hr-recruitment-clients-campaign:before {
  content: "\ec2f";
}
.hr-recruitment-clients-campaign1:before {
  content: "\ec30";
}
.hr-recruitment-clients-export:before {
  content: "\ec31";
}
.hr-recruitment-clients-import:before {
  content: "\ec32";
}
.hr-recruitment-clients-master:before {
  content: "\ec33";
}
.hr-recruitment-clients-print:before {
  content: "\ec34";
}
.hr-recruitment-dashboard:before {
  content: "\ec35";
}
.hr-recruitment-dashboard-applied:before {
  content: "\ec36";
}
.hr-recruitment-dashboard-rejected:before {
  content: "\ec37";
}
.hr-recruitment-dashboard-scheduled:before {
  content: "\ec38";
}
.hr-recruitment-dashboard-shortlisted:before {
  content: "\ec39";
}
.hr-onboarding-individuals:before {
  content: "\ec3a";
}
.hr-recruitment-interview-calendar:before {
  content: "\ec3b";
}
.hr-recruitment-interview-rounds-master:before {
  content: "\ec3c";
}
.hr-recruitment-job-candidates:before {
  content: "\ec3d";
}
.hr-recruitment-job-post-requisition:before {
  content: "\ec3e";
}
.hr-recruitment-job-posts:before {
  content: "\ec3f";
}
.hr-recruitment-schedule-interviews:before {
  content: "\ec40";
}
.hr-recruitment-shortlisted-candidates:before {
  content: "\ec41";
}
.hr-recycle:before {
  content: "\ec42";
}
.hr-reddit:before {
  content: "\ec43";
}
.hr-reddit-alien:before {
  content: "\ec44";
}
.hr-reddit-square:before {
  content: "\ec45";
}
.hr-refresh:before {
  content: "\ec46";
}
.hr-refresh1:before {
  content: "\ec47";
}
.hr-registered:before {
  content: "\ec48";
}
.hr-renren:before {
  content: "\ec49";
}
.hr-repeat:before {
  content: "\ec4a";
}
.hr-repo:before {
  content: "\ec4b";
}
.hr-repo-clone:before {
  content: "\ec4c";
}
.hr-repo-force-push:before {
  content: "\ec4d";
}
.hr-report-absentees:before {
  content: "\ec4e";
}
.hr-report-assignment:before {
  content: "\ec4f";
}
.hr-report-attendance-import:before {
  content: "\ec50";
}
.hr-report-attendance-muster-info:before {
  content: "\ec51";
}
.hr-report-attendance-shortage:before {
  content: "\ec52";
}
.hr-report-attendance-summary-hourly:before {
  content: "\ec53";
}
.hr-report-attendance-summary-monthly:before {
  content: "\ec54";
}
.hr-report-attendance-without-grace-period:before {
  content: "\ec55";
}
.hr-report-attendance-with-out-grace-period:before {
  content: "\ec56";
}
.hr-report-biometric-error-log:before {
  content: "\ec57";
}
.hr-report-biometric-sync-history:before {
  content: "\ec58";
}
.hr-report-certifications:before {
  content: "\ec59";
}
.hr-report-compensatory-off:before {
  content: "\ec5a";
}
.hr-report-compensatory-off-balance:before {
  content: "\ec5b";
}
.hr-report-dependents:before {
  content: "\ec5c";
}
.hr-report-designations:before {
  content: "\ec5d";
}
.hr-report-device-management:before {
  content: "\ec5e";
}
.hr-report-educational-qualifications:before {
  content: "\ec5f";
}
.hr-report-gender:before {
  content: "\ec60";
}
.hr-report-holiday-attendance:before {
  content: "\ec61";
}
.hr-report-insurance:before {
  content: "\ec62";
}
.hr-report-insurance-fixed:before {
  content: "\ec63";
}
.hr-report-leave-history:before {
  content: "\ec64";
}
.hr-report-license-expiry:before {
  content: "\ec65";
}
.hr-report-loan:before {
  content: "\ec66";
}
.hr-report-logo:before {
  content: "\ec67";
}
.hr-report-monthly-leave-balance:before {
  content: "\ec68";
}
.hr-report-monthly-shortage:before {
  content: "\ec69";
}
.hr-report-overtime:before {
  content: "\ec6a";
}
.hr-report-project:before {
  content: "\ec6b";
}
.hr-report-report:before {
  content: "\ec6c";
}
.hr-reports:before {
  content: "\ec6d";
}
.hr-reports-adhoc-allowance:before {
  content: "\ec6e";
}
.hr-reports-custom-report:before {
  content: "\ec6f";
}
.hr-reports-deferred-loan:before {
  content: "\ec70";
}
.hr-reports-ecr-hourly:before {
  content: "\ec71";
}
.hr-reports-ecr-monthly:before {
  content: "\ec72";
}
.hr-reports-eft:before {
  content: "\ec73";
}
.hr-reports-eft-hourly:before {
  content: "\ec74";
}
.hr-reports-eft-monthly:before {
  content: "\ec75";
}
.hr-reports-employees-reports:before {
  content: "\ec76";
}
.hr-reports-employee-wise-expenses:before {
  content: "\ec77";
}
.hr-reports-esic-hourly:before {
  content: "\ec78";
}
.hr-reports-esic-monthly:before {
  content: "\ec79";
}
.hr-reports-esi-hourly:before {
  content: "\ec7a";
}
.hr-reports-esi-monthly:before {
  content: "\ec7b";
}
.hr-reports-group-deductions:before {
  content: "\ec7c";
}
.hr-report-short:before {
  content: "\ec7d";
}
.hr-reports-hourly-master-report:before {
  content: "\ec7e";
}
.hr-reports-hourly-wage-payslip:before {
  content: "\ec7f";
}
.hr-reports-hr-reports:before {
  content: "\ec80";
}
.hr-reports-insurance-fixed:before {
  content: "\ec81";
}
.hr-reports-insurance-payment-tracker:before {
  content: "\ec82";
}
.hr-report-skills:before {
  content: "\ec83";
}
.hr-reports-loan-amortization:before {
  content: "\ec84";
}
.hr-reports-loan-balance:before {
  content: "\ec85";
}
.hr-reports-monthly-master-report:before {
  content: "\ec86";
}
.hr-reports-monthly-salary:before {
  content: "\ec87";
}
.hr-reports-monthly-salary-payslip:before {
  content: "\ec88";
}
.hr-reports-payroll-reports:before {
  content: "\ec89";
}
.hr-reports-pf-hourly:before {
  content: "\ec8a";
}
.hr-reports-pf-monthly:before {
  content: "\ec8b";
}
.hr-reports-pf-payment-tracker:before {
  content: "\ec8c";
}
.hr-reports-professional-tax-hourly:before {
  content: "\ec8d";
}
.hr-reports-professional-tax-monthly:before {
  content: "\ec8e";
}
.hr-reports-recruitment-reports:before {
  content: "\ec8f";
}
.hr-reports-salary-increment:before {
  content: "\ec90";
}
.hr-report-status:before {
  content: "\ec91";
}
.hr-reports-tds:before {
  content: "\ec92";
}
.hr-reports-timeline:before {
  content: "\ec93";
}
.hr-reports-uan-based-ecr:before {
  content: "\ec94";
}
.hr-reports-uan-based-ecr-hourly:before {
  content: "\ec95";
}
.hr-reports-variable-insurance:before {
  content: "\ec96";
}
.hr-reports-work-sheet:before {
  content: "\ec97";
}
.hr-report-tax:before {
  content: "\ec98";
}
.hr-report-taxdeduct:before {
  content: "\ec99";
}
.hr-report-trainings:before {
  content: "\ec9a";
}
.hr-report-weekoff-attendance:before {
  content: "\ec9b";
}
.hr-report-year-of-join:before {
  content: "\ec9c";
}
.hr-reset:before {
  content: "\ec9d";
}
.hr-retweet:before {
  content: "\ec9e";
}
.hr-road:before {
  content: "\ec9f";
}
.hr-rocket:before {
  content: "\eca0";
}
.hr-roster-management:before {
  content: "\eca1";
}
.hr-roster-management-calendar-view:before {
  content: "\eca2";
}
.hr-roster-management-custom-group:before {
  content: "\eca3";
}
.hr-roster-management-custom-group-settings:before {
  content: "\eca4";
}
.hr-roster-management-shift-scheduling:before {
  content: "\eca5";
}
.hr-roster-management-shift-type:before {
  content: "\eca6";
}
.hr-rotate-left:before {
  content: "\eca7";
}
.hr-rouble:before {
  content: "\eca8";
}
.hr-rss-square:before {
  content: "\eca9";
}
.hr-ruler:before {
  content: "\ecaa";
}
.hr-safari:before {
  content: "\ecab";
}
.hr-scribd:before {
  content: "\ecac";
}
.hr-search:before {
  content: "\ecad";
}
.hr-search1:before {
  content: "\ecae";
}
.hr-search-minus:before {
  content: "\ecaf";
}
.hr-search-plus:before {
  content: "\ecb0";
}
.hr-section-investment:before {
  content: "\ecb1";
}
.hr-sellsy:before {
  content: "\ecb2";
}
.hr-server:before {
  content: "\ecb3";
}
.hr-settings:before {
  content: "\ecb4";
}
.hr-settings-ip-whitelisting:before {
  content: "\ecb5";
}
.hr-settings-general:before {
  content: "\ecb6";
}
.hr-settings-payroll:before {
  content: "\ecb7";
}
.hr-settings-performance-management:before {
  content: "\ecb8";
}
.hr-settings-productivity-monitoring:before {
  content: "\ecb9";
}
.hr-settings-tax-and-statutory-compliance:before {
  content: "\ecba";
}
.hr-share-alt:before {
  content: "\ecbb";
}
.hr-share-alt-square:before {
  content: "\ecbc";
}
.hr-share-square:before {
  content: "\ecbd";
}
.hr-share-square-o:before {
  content: "\ecbe";
}
.hr-shield:before {
  content: "\ecbf";
}
.hr-ship:before {
  content: "\ecc0";
}
.hr-shirtsinbulk:before {
  content: "\ecc1";
}
.hr-shopping-bag:before {
  content: "\ecc2";
}
.hr-shopping-basket:before {
  content: "\ecc3";
}
.hr-shopping-cart:before {
  content: "\ecc4";
}
.hr-shower:before {
  content: "\ecc5";
}
.hr-shrink:before {
  content: "\ecc6";
}
.hr-signal:before {
  content: "\ecc7";
}
.hr-sign-in:before {
  content: "\ecc8";
}
.hr-sign-in1:before {
  content: "\ecc9";
}
.hr-sign-in-1:before {
  content: "\ecca";
}
.hr-sign-language:before {
  content: "\eccb";
}
.hr-signout:before {
  content: "\eccc";
}
.hr-sign-out:before {
  content: "\eccd";
}
.hr-sign-out1:before {
  content: "\ecce";
}
.hr-simplybuilt:before {
  content: "\eccf";
}
.hr-sitemap:before {
  content: "\ecd0";
}
.hr-skill-definition:before {
  content: "\ecd1";
}
.hr-skill-level-association:before {
  content: "\ecd2";
}
.hr-skyatlas:before {
  content: "\ecd3";
}
.hr-skype:before {
  content: "\ecd4";
}
.hr-slack:before {
  content: "\ecd5";
}
.hr-sliders:before {
  content: "\ecd6";
}
.hr-slideshare:before {
  content: "\ecd7";
}
.hr-smile-o:before {
  content: "\ecd8";
}
.hr-snapchat:before {
  content: "\ecd9";
}
.hr-snapchat-ghost:before {
  content: "\ecda";
}
.hr-snapchat-square:before {
  content: "\ecdb";
}
.hr-snowflake-o:before {
  content: "\ecdc";
}
.hr-sort:before {
  content: "\ecdd";
}
.hr-sort-alpha-asc:before {
  content: "\ecde";
}
.hr-sort-alpha-desc:before {
  content: "\ecdf";
}
.hr-sort-amount-asc:before {
  content: "\ece0";
}
.hr-sort-amount-desc:before {
  content: "\ece1";
}
.hr-sort-asc:before {
  content: "\ece2";
}
.hr-sort-desc:before {
  content: "\ece3";
}
.hr-sort-numeric-asc:before {
  content: "\ece4";
}
.hr-sort-numeric-desc:before {
  content: "\ece5";
}
.hr-soundcloud:before {
  content: "\ece6";
}
.hr-space-shuttle:before {
  content: "\ece7";
}
.hr-spinner:before {
  content: "\ece8";
}
.hr-spoon:before {
  content: "\ece9";
}
.hr-spotify:before {
  content: "\ecea";
}
.hr-square:before {
  content: "\eceb";
}
.hr-square-o:before {
  content: "\ecec";
}
.hr-stack-exchange:before {
  content: "\eced";
}
.hr-stack-overflow:before {
  content: "\ecee";
}
.hr-star:before {
  content: "\ecef";
}
.hr-star1:before {
  content: "\ecf0";
}
.hr-star-half:before {
  content: "\ecf1";
}
.hr-star-half-empty:before {
  content: "\ecf2";
}
.hr-star-o:before {
  content: "\ecf3";
}
.hr-statistics:before {
  content: "\ecf4";
}
.hr-status-approval:before {
  content: "\ecf5";
}
.hr-status-update:before {
  content: "\ecf6";
}
.hr-steam:before {
  content: "\ecf7";
}
.hr-steam-square:before {
  content: "\ecf8";
}
.hr-step-backward:before {
  content: "\ecf9";
}
.hr-step-forward:before {
  content: "\ecfa";
}
.hr-stethoscope:before {
  content: "\ecfb";
}
.hr-sticky-note:before {
  content: "\ecfc";
}
.hr-sticky-note-o:before {
  content: "\ecfd";
}
.hr-stop:before {
  content: "\ecfe";
}
.hr-stop1:before {
  content: "\ecff";
}
.hr-stop-circle:before {
  content: "\ed00";
}
.hr-stop-circle-o:before {
  content: "\ed01";
}
.hr-street-view:before {
  content: "\ed02";
}
.hr-strikethrough:before {
  content: "\ed03";
}
.hr-stumbleupon:before {
  content: "\ed04";
}
.hr-stumbleupon-circle:before {
  content: "\ed05";
}
.hr-subscript:before {
  content: "\ed06";
}
.hr-subway:before {
  content: "\ed07";
}
.hr-suitcase:before {
  content: "\ed08";
}
.hr-sun-o:before {
  content: "\ed09";
}
.hr-superpowers:before {
  content: "\ed0a";
}
.hr-superscript:before {
  content: "\ed0b";
}
.hr-support-ticket:before {
  content: "\ed0c";
}
.hr-sync:before {
  content: "\ed0d";
}
.hr-sync-1:before {
  content: "\ed0e";
}
.hr-table:before {
  content: "\ed0f";
}
.hr-tablet:before {
  content: "\ed10";
}
.hr-tag:before {
  content: "\ed11";
}
.hr-tags:before {
  content: "\ed12";
}
.hr-tasks:before {
  content: "\ed13";
}
.hr-tax-calculation:before {
  content: "\ed14";
}
.hr-tax-entities:before {
  content: "\ed15";
}
.hr-tax-exemptions:before {
  content: "\ed16";
}
.hr-tax-rebates:before {
  content: "\ed17";
}
.hr-tax-section:before {
  content: "\ed18";
}
.hr-tax-section-1:before {
  content: "\ed19";
}
.hr-tax-slab:before {
  content: "\ed1a";
}
.hr-tax-slab-1:before {
  content: "\ed1b";
}
.hr-tds-payment-tracker:before {
  content: "\ed1c";
}
.hr-tds-submission:before {
  content: "\ed1d";
}
.hr-telegram:before {
  content: "\ed1e";
}
.hr-telescope:before {
  content: "\ed1f";
}
.hr-television:before {
  content: "\ed20";
}
.hr-tencent-weibo:before {
  content: "\ed21";
}
.hr-terminal:before {
  content: "\ed22";
}
.hr-text-height:before {
  content: "\ed23";
}
.hr-text-width:before {
  content: "\ed24";
}
.hr-th:before {
  content: "\ed25";
}
.hr-themeisle:before {
  content: "\ed26";
}
.hr-thermometer:before {
  content: "\ed27";
}
.hr-thermometer-0:before {
  content: "\ed28";
}
.hr-thermometer-1:before {
  content: "\ed29";
}
.hr-thermometer-2:before {
  content: "\ed2a";
}
.hr-thermometer-3:before {
  content: "\ed2b";
}
.hr-th-large:before {
  content: "\ed2c";
}
.hr-th-list:before {
  content: "\ed2d";
}
.hr-thumbs-down:before {
  content: "\ed2e";
}
.hr-thumbs-o-down:before {
  content: "\ed2f";
}
.hr-thumbs-o-up:before {
  content: "\ed30";
}
.hr-thumbs-up:before {
  content: "\ed31";
}
.hr-thumb-tack:before {
  content: "\ed32";
}
.hr-ticket:before {
  content: "\ed33";
}
.hr-times:before {
  content: "\ed34";
}
.hr-times-circle:before {
  content: "\ed35";
}
.hr-times-circle-o:before {
  content: "\ed36";
}
.hr-timesheet:before {
  content: "\ed37";
}
.hr-timesheet-activity:before {
  content: "\ed38";
}
.hr-timesheet-hours:before {
  content: "\ed39";
}
.hr-times-rectangle:before {
  content: "\ed3a";
}
.hr-times-rectangle-o:before {
  content: "\ed3b";
}
.hr-tint:before {
  content: "\ed3c";
}
.hr-toggle-off:before {
  content: "\ed3d";
}
.hr-toggle-on:before {
  content: "\ed3e";
}
.hr-trademark:before {
  content: "\ed3f";
}
.hr-train:before {
  content: "\ed40";
}
.hr-transgender-alt:before {
  content: "\ed41";
}
.hr-trash:before {
  content: "\ed42";
}
.hr-trash-o:before {
  content: "\ed43";
}
.hr-tree:before {
  content: "\ed44";
}
.hr-trello:before {
  content: "\ed45";
}
.hr-tripadvisor:before {
  content: "\ed46";
}
.hr-trophy:before {
  content: "\ed47";
}
.hr-truck:before {
  content: "\ed48";
}
.hr-try:before {
  content: "\ed49";
}
.hr-tty:before {
  content: "\ed4a";
}
.hr-tumblr:before {
  content: "\ed4b";
}
.hr-tumblr-square:before {
  content: "\ed4c";
}
.hr-twitch:before {
  content: "\ed4d";
}
.hr-twitter:before {
  content: "\ed4e";
}
.hr-twitter-square:before {
  content: "\ed4f";
}
.hr-umbrella:before {
  content: "\ed50";
}
.hr-underline:before {
  content: "\ed51";
}
.hr-universal-access:before {
  content: "\ed52";
}
.hr-unlock:before {
  content: "\ed53";
}
.hr-unlock-alt:before {
  content: "\ed54";
}
.hr-upload:before {
  content: "\ed55";
}
.hr-upload1:before {
  content: "\ed56";
}
.hr-usb:before {
  content: "\ed57";
}
.hr-user:before {
  content: "\ed58";
}
.hr-user1:before {
  content: "\ed59";
}
.hr-user-1:before {
  content: "\ed5a";
}
.hr-user-2:before {
  content: "\ed5b";
}
.hr-user-circle:before {
  content: "\ed5c";
}
.hr-user-circle-o:before {
  content: "\ed5d";
}
.hr-user-close:before {
  content: "\ed5e";
}
.hr-user-close-add:before {
  content: "\ed5f";
}
.hr-user-close-edit:before {
  content: "\ed60";
}
.hr-user-close-information:before {
  content: "\ed61";
}
.hr-user-close-remove:before {
  content: "\ed62";
}
.hr-user-close-settings:before {
  content: "\ed63";
}
.hr-user-full:before {
  content: "\ed64";
}
.hr-user-full-add:before {
  content: "\ed65";
}
.hr-user-full-edit:before {
  content: "\ed66";
}
.hr-user-full-remove:before {
  content: "\ed67";
}
.hr-user-full-security:before {
  content: "\ed68";
}
.hr-user-full-settings:before {
  content: "\ed69";
}
.hr-user-half:before {
  content: "\ed6a";
}
.hr-user-half-add:before {
  content: "\ed6b";
}
.hr-user-half-edit:before {
  content: "\ed6c";
}
.hr-user-half-information:before {
  content: "\ed6d";
}
.hr-user-half-remove:before {
  content: "\ed6e";
}
.hr-user-half-security:before {
  content: "\ed6f";
}
.hr-user-half-settings:before {
  content: "\ed70";
}
.hr-user-md:before {
  content: "\ed71";
}
.hr-user-o:before {
  content: "\ed72";
}
.hr-user-outline:before {
  content: "\ed73";
}
.hr-onboarding:before {
  content: "\ed74";
}
.hr-user-profile:before {
  content: "\ed75";
}
.hr-user-profile-edit:before {
  content: "\ed76";
}
.hr-user-secret:before {
  content: "\ed77";
}
.hr-user-times:before {
  content: "\ed78";
}
.hr-venus:before {
  content: "\ed79";
}
.hr-venus1:before {
  content: "\ed7a";
}
.hr-venus-double:before {
  content: "\ed7b";
}
.hr-venus-double1:before {
  content: "\ed7c";
}
.hr-venus-mars:before {
  content: "\ed7d";
}
.hr-viacoin:before {
  content: "\ed7e";
}
.hr-viadeo:before {
  content: "\ed7f";
}
.hr-viadeo-square:before {
  content: "\ed80";
}
.hr-video-camera:before {
  content: "\ed81";
}
.hr-view:before {
  content: "\ed82";
}
.hr-view-disable:before {
  content: "\ed83";
}
.hr-vimeo:before {
  content: "\ed84";
}
.hr-vimeo-square:before {
  content: "\ed85";
}
.hr-vine:before {
  content: "\ed86";
}
.hr-vk:before {
  content: "\ed87";
}
.hr-volume-control-phone:before {
  content: "\ed88";
}
.hr-volume-down:before {
  content: "\ed89";
}
.hr-volume-off:before {
  content: "\ed8a";
}
.hr-volume-up:before {
  content: "\ed8b";
}
.hr-wechat:before {
  content: "\ed8c";
}
.hr-weibo:before {
  content: "\ed8d";
}
.hr-whatsapp:before {
  content: "\ed8e";
}
.hr-wheelchair:before {
  content: "\ed8f";
}
.hr-wheelchair-alt:before {
  content: "\ed90";
}
.hr-wifi:before {
  content: "\ed91";
}
.hr-wikipedia-w:before {
  content: "\ed92";
}
.hr-window-maximize:before {
  content: "\ed93";
}
.hr-window-minimize:before {
  content: "\ed94";
}
.hr-window-restore:before {
  content: "\ed95";
}
.hr-windows:before {
  content: "\ed96";
}
.hr-wordpress:before {
  content: "\ed97";
}
.hr-workflow:before {
  content: "\ed98";
}
.hr-workflow-approval-management:before {
  content: "\ed99";
}
.hr-workflow-task-management-approve:before {
  content: "\ed9a";
}
.hr-workflow-task-management-approvedtask:before {
  content: "\ed9b";
}
.hr-workflow-task-management-claim:before {
  content: "\ed9c";
}
.hr-workflow-task-management-claim-override:before {
  content: "\ed9d";
}
.hr-workflow-task-management-form:before {
  content: "\ed9e";
}
.hr-workflow-task-management-reject:before {
  content: "\ed9f";
}
.hr-workflow-task-management-surrender:before {
  content: "\eda0";
}
.hr-workflow-task-management-view:before {
  content: "\eda1";
}
.hr-workflow-task-management-waitingforapprovaltask:before {
  content: "\eda2";
}
.hr-workflow-workflow-builder:before {
  content: "\eda3";
}
.hr-wpbeginner:before {
  content: "\eda4";
}
.hr-wpexplorer:before {
  content: "\eda5";
}
.hr-wpforms:before {
  content: "\eda6";
}
.hr-wrench:before {
  content: "\eda7";
}
.hr-x:before {
  content: "\eda8";
}
.hr-xing:before {
  content: "\eda9";
}
.hr-xing-square:before {
  content: "\edaa";
}
.hr-yahoo:before {
  content: "\edab";
}
.hr-y-combinator:before {
  content: "\edac";
}
.hr-yelp:before {
  content: "\edad";
}
.hr-yoast:before {
  content: "\edae";
}
.hr-youtube:before {
  content: "\edaf";
}
.hr-youtube-play:before {
  content: "\edb0";
}
.hr-youtube-play1:before {
  content: "\edb1";
}
.hr-youtube-square:before {
  content: "\edb2";
}
.hr-zap:before {
  content: "\edb3";
}
.hr-zoom-out:before {
  content: "\edb4";
}
.hr-performance-management:before {
  content: "\edb5";
}
.hr-performance-management-trulead:before {
  content: "\edb6";
}
.hr-core-hr-work-schedule:before {
  content: "\edb7";
}
.hr-core-hr:before {
  content: "\edb8";
}
.hr-compliance-management:before {
  content: "\edb9";
}
.hr-core-hr-register-face:before {
  content: "\edba";
}
.hr-settings-attendance-configuration:before {
  content: "\edbb";
}
.hr-data-loss-prevention-file-transfers:before {
  content: "\edbc";
}
.hr-data-loss-prevention:before {
  content: "\edbd";
}
.hr-settings-data-loss-prevention:before {
  content: "\edbe";
}
.hr-onboarding-vendors:before {
  content: "\edbf";
}
.hr-compliance-management-accreditation:before {
  content: "\edc0";
}
.hr-settings-core-hr:before {
  content: "\edc1";
}
.hr-settings-integration:before {
  content: "\edc2";
}
.hr-employee-self-service:before {
  content: "\edc3";
}
.hr-my-team:before {
  content: "\edc4";
}
.hr-employee-self-service-lop-recovery:before {
  content: "\edc5";
}
.hr-employee-self-service-pre-approval:before {
  content: "\edc6";
}
.hr-my-team-team-summary:before {
  content: "\edc7";
}
.hr-my-team-lop-recovery:before {
  content: "\edc8";
}
.hr-my-team-compensatory-off-balance:before {
  content: "\edc9";
}
.hr-employee-self-service-compensatory-off-balance:before {
  content: "\edca";
}
.hr-my-team-timesheets:before {
  content: "\edcb";
}
.hr-core-hr-time-off-management:before {
  content: "\edcc";
}
.hr-recruitment-recruitment-dashboard:before {
  content: "\edcd";
}
.hr-my-team-exit-management:before {
  content: "\edce";
}
.hr-core-hr-org-structure:before {
  content: "\edcf";
}
.hr-data-loss-prevention-location-intelligence1:before {
  content: "\edd0";
}
.hr-man-power-planning-table-of-organization:before {
  content: "\edd1";
}
.hr-man-power-planning-job-requisition:before {
  content: "\edd2";
}
.hr-man-power-planning-hiring-forecast:before {
  content: "\edd3";
}
.hr-man-power-planning:before {
  content: "\edd4";
}
.hr-settings-man-power-planning:before {
  content: "\edd5";
}
.hr-data-loss-prevention-key-logger:before {
  content: "\edd6";
}
.hr-employee-self-service-organization-chart:before {
  content: "\edd7";
}
.hr-employee-self-service-my-profile:before {
  content: "\edd8";
}
.hr-man-power-planning-settings:before {
  content: "\edd9";
}
.hr-employee-self-service-attendance:before {
  content: "\edda";
}
.hr-my-team-attendance:before {
  content: "\eddb";
}
.hr-recruitment-my-integration:before {
  content: "\eddc";
}
.hr-recruitment-careers:before {
  content: "\eddd";
}
.hr-payroll-payroll-reconciliation:before {
  content: "\edde";
}
.hr-payroll-electronic-fund-transfer:before {
  content: "\eddf";
}
.hr-core-hr-payroll-data-management:before {
  content: "\ede0";
}
.hr-employee-self-service-time-off:before {
  content: "\ede1";
}
.hr-my-team-time-off:before {
  content: "\ede2";
}
.hr-my-finance-travel-and-expenses:before {
  content: "\ede3";
}
.hr-my-team-travel-and-expenses:before {
  content: "\ede4";
}
.hr-my-finance:before {
  content: "\ede5";
}
.hr-payroll-payroll-management:before {
  content: "\ede6";
}
.hr-approvals:before {
  content: "\ede7";
}
.hr-approvals-approval-management:before {
  content: "\ede8";
}
.hr-tax-and-statutory-compliance-statutory-components:before {
  content: "\ede9";
}
