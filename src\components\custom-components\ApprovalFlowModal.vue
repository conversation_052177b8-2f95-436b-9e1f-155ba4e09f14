<template>
  <div>
    <v-dialog
      v-model="openApprovalFlowModal"
      :max-width="700"
      @click:outside="closeApprovalFlowModal()"
    >
      <v-card class="card-radius">
        <v-card-title>
          <div class="d-flex" style="width: 100%">
            Approval Flow
            <v-spacer />
            <v-icon color="secondary" @click="closeApprovalFlowModal()">
              fas fa-times
            </v-icon>
          </div>
        </v-card-title>
        <v-card-text class="px-6">
          <organization-chart :datasource="orgChartData">
            <template v-slot="{ nodeData }">
              <div class="d-flex flex-column my-2">
                <i :class="nodeData.iconName"></i>
                <div class="mt-2 text-primary">
                  {{ nodeData.title }}
                </div>
                <div class="text-blue d-flex justify-center">
                  {{ nodeData.name }}
                  <v-tooltip
                    v-if="nodeData.name === 'To be decided during runtime'"
                    text="The reporting manager who is associated with the employee in the employee module will be considered"
                    location="top"
                    max-width="400"
                  >
                    <template v-slot:activator="{ props }">
                      <div v-bind="props">
                        <v-icon color="blue" class="ml-1"
                          >fas fa-info-circle</v-icon
                        >
                      </div>
                    </template>
                  </v-tooltip>
                </div>
                <div class="d-flex align-center justify-center">
                  <v-tooltip
                    v-if="nodeData.completedOn"
                    :text="nodeData.completedOn"
                    location="top"
                    max-width="400"
                  >
                    <template v-slot:activator="{ props }">
                      <div v-bind="props">
                        <v-icon class="ml-2" color="indigo-accent-2"
                          >fas fa-clock</v-icon
                        >
                      </div>
                    </template>
                  </v-tooltip>
                  <v-tooltip
                    v-if="nodeData.remarks"
                    :text="nodeData.remarks"
                    location="top"
                    max-width="400"
                  >
                    <template v-slot:activator="{ props }">
                      <div v-bind="props">
                        <v-icon class="ml-2" color="amber"
                          >fas fa-comment-alt</v-icon
                        >
                      </div>
                    </template>
                  </v-tooltip>
                  <div
                    v-if="nodeData.formDetails && nodeData.status_id !== '1002'"
                  >
                    <i
                      class="hr-workflow-task-management-form cursor-pointer pl-2"
                      style="color: #ff6666; font-size: 18px"
                      @click="openDynamicForm(nodeData.formDetails)"
                    ></i>
                  </div>
                </div>
              </div>
            </template>
          </organization-chart>
        </v-card-text>
      </v-card>
    </v-dialog>
    <AppLoading v-if="isLoading" />
  </div>
</template>

<script>
import { GET_WORK_FLOW_VIEW } from "@/graphql/workflow/approvalManagementQueries";
import OrganizationChart from "vue3-organization-chart";
import moment from "moment";

export default {
  name: "ApprovalFlowModal",

  components: { OrganizationChart },

  props: {
    taskId: {
      type: String,
      required: false,
    },
    leaveId: {
      type: Number,
      required: false,
    },
  },

  data: () => ({
    openApprovalFlowModal: false,
    isLoading: false,
    orgchartArray: [],
    keyMap: [],
    orgChartData: {},
    empGroupData: null,
    startNode: "",
    isCalled: false,
  }),

  computed: {
    formatDate() {
      return (date, withTime = false, isEmpty = false) => {
        if (date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
          return date ? moment(date).format(orgDateFormat) : isEmpty ? "" : "-";
        }
        return "";
      };
    },
  },

  mounted() {
    this.fetchWorkflowData();
  },

  methods: {
    closeApprovalFlowModal() {
      this.openApprovalFlowModal = false;
      this.$emit("close-modal");
    },
    fetchWorkflowData() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_WORK_FLOW_VIEW,
          variables: {
            taskId: vm.taskId,
            leaveId: vm.leaveId,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getWorkFlowView &&
            response.data.getWorkFlowView.details
          ) {
            var resultData = JSON.parse(response.data.getWorkFlowView.details);
            this.orgchartArray = [];
            this.keyMap = [];
            this.orgChartData = {};
            this.isCalled = false;
            this.empGroupData = resultData.empGroupData;
            this.prepareChartJson(resultData.nodeDetails);
          } else {
            this.isLoading = false;
            let snackbarData = {
              isOpen: true,
              message:
                "Something went wrong while fetching approval workflow details. Please try after some time.",
              type: "warning",
            };
            this.showAlert(snackbarData);
            this.closeApprovalFlowModal();
          }
        })
        .catch(() => {
          this.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              "Something went wrong while fetching approval workflow details. Please try after some time.",
            type: "warning",
          };
          this.showAlert(snackbarData);
          this.closeApprovalFlowModal();
        });
    },

    prepareChartJson(workflowData) {
      this.startNode = workflowData["startNode"];
      this.prepareNode(workflowData.nodes[this.startNode], workflowData.nodes);
    },

    prepareNode(node, nodes) {
      for (let i = 0; i < node["next"].length; i++) {
        const currentNode = nodes[node["next"][i]];
        let userDetails = currentNode.data.completed_by
          ? currentNode.data.completed_by
          : currentNode.data.assignee;
        let values = this.empGroupData;
        if (!this.keyMap.includes(node["next"][i])) {
          this.keyMap.push(node["next"][i]);
          let parentNode = "";
          for (let objKey in nodes) {
            if (nodes[objKey].next.includes(node["next"][i])) {
              parentNode = nodes[objKey].nodeNo
                ? nodes[objKey].nodeNo
                : this.startNode;
              break;
            }
          }
          if (currentNode.type == "userTask") {
            if (currentNode.data.modalTaskData) {
              // Handle node with modalTaskData
              const modalData = currentNode.data.modalTaskData;
              let name = "";
              let values = this.empGroupData;

              // Determine name based on approver type
              if (
                modalData.typeOfApprove === "user" &&
                modalData.approveByUser
              ) {
                name =
                  values[`user${modalData.approveByUser.id}`]?.["Name"] ||
                  "Pending User";
              } else if (
                modalData.typeOfApprove === "group" &&
                modalData.approveByGroup
              ) {
                name = modalData.approveByGroup?.["text"] || "Pending Group";
              }

              this.orgchartArray.push({
                id: node["next"][i],
                customGroupId: modalData.approveByGroup
                  ? modalData.approveByGroup.id
                  : "",
                name: name,
                title: modalData.title || "Pending Approval",
                Parent: parentNode,
                iconName:
                  "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                remarks: currentNode.data.remarks || "",
                formDetails:
                  this.filterFormId === 34 &&
                  modalData.formId &&
                  modalData.formId.id !== "0" &&
                  parseInt(modalData.formId.id) > 0
                    ? {
                        formIdentifier: currentNode.data.form_identifier,
                        taskId: currentNode.data.task_id,
                        processInstanceId: currentNode.data.process_instance_id,
                      }
                    : "",
              });
            } else {
              //approved status
              if (userDetails && currentNode.data.status_id === "1002") {
                this.orgchartArray.push({
                  id: node["next"][i],
                  name: values[`user${userDetails}`]["Name"],
                  title: currentNode.data.description,
                  Parent: parentNode,
                  iconName:
                    "text-h3 hr-workflow-task-management-approvedtask text-green",
                  remarks: currentNode.data.remarks,
                  completedOn: currentNode?.data?.completed_date
                    ? this.formatDate(
                        new Date(currentNode.data.completed_date + "00Z"),
                        true,
                        true
                      )
                    : null,
                  formDetails:
                    currentNode.data.form_identifier &&
                    parseInt(currentNode.data.form_identifier) > 0
                      ? {
                          formIdentifier: currentNode.data.form_identifier,
                          taskId: currentNode.data.task_id,
                          processInstanceId:
                            currentNode.data.process_instance_id,
                        }
                      : "",
                });
              } else if (currentNode.data.status_id === "1007") {
                //rejected status
                this.orgchartArray.push({
                  id: node["next"][i],
                  name: values[`user${userDetails}`]["Name"],
                  title: currentNode.data.description,
                  Parent: parentNode,
                  iconName:
                    "text-h3 hr-workflow-task-management-reject text-red",
                  remarks: currentNode.data.remarks,
                  completedOn: currentNode?.data?.completed_date
                    ? this.formatDate(
                        new Date(currentNode.data.completed_date + "00Z"),
                        true,
                        true
                      )
                    : null,
                  formDetails:
                    currentNode.data.form_identifier &&
                    parseInt(currentNode.data.form_identifier) > 0
                      ? {
                          formIdentifier: currentNode.data.form_identifier,
                          taskId: currentNode.data.task_id,
                          processInstanceId:
                            currentNode.data.process_instance_id,
                        }
                      : "",
                });
              } else if (
                currentNode.data.status_id === "1001" ||
                currentNode.data.status_id === "1006"
              ) {
                // user task
                if (userDetails) {
                  if (
                    parseInt(this.loginEmployeeId) === parseInt(userDetails) ||
                    this.isAnyAdmin
                  ) {
                    this.orgchartArray.push({
                      id: node["next"][i],
                      name: values[`user${userDetails}`]["Name"],
                      title: currentNode.data.description,
                      Parent: parentNode,
                      iconName:
                        "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                      remarks: currentNode.data.remarks,
                      formDetails:
                        currentNode.data.form_identifier &&
                        parseInt(currentNode.data.form_identifier) > 0
                          ? {
                              formIdentifier: currentNode.data.form_identifier,
                              taskId: currentNode.data.task_id,
                              processInstanceId:
                                currentNode.data.process_instance_id,
                            }
                          : "",
                    });
                  } else {
                    this.orgchartArray.push({
                      id: node["next"][i],
                      name: values[`user${userDetails}`]["Name"],
                      title: currentNode.data.description,
                      Parent: parentNode,
                      iconName:
                        "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                      remarks: currentNode.data.remarks,
                    });
                  }
                }
                // group task
                else {
                  // Parse custom_group_id data, handle errors if it's not valid JSON or doesn't contain expected data
                  let groupData = {};
                  try {
                    groupData = JSON.parse(currentNode.data.custom_group_id); // Parse the custom_group_id string
                  } catch (error) {
                    groupData = null; // Set to null or handle it in some way if parsing fails
                  }

                  // Check if groupData is valid and contains the expected structure
                  const groupName =
                    groupData &&
                    groupData[`group${currentNode.data.custom_group_id}`]
                      ? groupData[`group${currentNode.data.custom_group_id}`][
                          "Name"
                        ]
                      : ""; // Fallback if Name is not found

                  this.orgchartArray.push({
                    id: node["next"][i],
                    name: groupName,
                    title: currentNode.data.description,
                    Parent: parentNode,
                    iconName:
                      "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                    remarks: currentNode.data.remarks,
                  });
                }
              } else {
                let empData = "";
                // check the type of approverUser
                if (currentNode.data.modalTaskData.typeOfApprove === "user") {
                  // check if the approverData is available
                  if (currentNode.data.modalTaskData.approveByUser) {
                    empData =
                      values[
                        `${currentNode.data.modalTaskData.typeOfApprove}${currentNode.data.modalTaskData.approveByUser.id}`
                      ];
                  } else {
                    empData = {
                      Name: "To be decided during runtime",
                    };
                  }
                } else {
                  empData =
                    values[
                      `${currentNode.data.modalTaskData.typeOfApprove}${currentNode.data.modalTaskData.approveByGroup.id}`
                    ];
                }
                this.orgchartArray.push({
                  id: node["next"][i],
                  name: empData["Name"],
                  title: currentNode.data.modalTaskData.title,
                  Parent: parentNode,
                  iconName:
                    "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                  remarks: currentNode.data.remarks,
                  formDetails:
                    currentNode.data.modalTaskData.formId.id != undefined &&
                    currentNode.data.modalTaskData.formId.id > 0
                      ? {
                          formIdentifier: currentNode.data.form_identifier,
                          taskId: currentNode.data.task_id,
                          processInstanceId:
                            currentNode.data.process_instance_id,
                        }
                      : "",
                });
              }
            }
          } else {
            this.orgchartArray.push({
              id: node["next"][i],
              name: "",
              title: "",
              Parent: parentNode,
              iconName: "text-h3 hr-workflow-approval-management text-primary",
              remarks: "",
            });
          }
          this.prepareNode(nodes[node["next"][i]], nodes);
        }
      }
      let nodeLength = Object.keys(nodes).length;
      nodeLength = nodeLength - 1;
      if (nodeLength === this.orgchartArray.length && !this.isCalled) {
        this.isCalled = true;
        let formedOrgChartData = this.formOrgChartData(this.orgchartArray);
        this.orgChartData =
          formedOrgChartData && formedOrgChartData.length > 0
            ? formedOrgChartData[0].children &&
              formedOrgChartData[0].children.length > 0
              ? formedOrgChartData[0].children[0]
              : []
            : [];
        this.isLoading = false;
        this.openApprovalFlowModal = true;
      }
    },

    formOrgChartData(items) {
      var tree = [],
        mappedArr = {};
      // Build a hash table and map items to objects
      items.forEach(function (item) {
        var id = item.id;
        // in case of duplicates
        if (!mappedArr.hasOwnProperty(id)) {
          mappedArr[id] = item; // the extracted id as key, and the item as value
          mappedArr[id].children = []; // under each item, add a key "children" with an empty array as value
        }
      });

      // If root-level nodes are not included in hash table, include them
      items.forEach(function (item) {
        var parentId = item.Parent;
        let nodeId =
          parentId === "taWorkStart" ? "taWorkStart" : "node_" + parentId;
        let nodeObj = mappedArr[nodeId];

        if (!mappedArr.hasOwnProperty(parentId)) {
          // make up an item for root-level node
          let newItem = {
            id: parentId,
            name: nodeObj && nodeObj.name ? nodeObj.name : "",
            title: nodeObj && nodeObj.title ? nodeObj.title : "",
            iconName: nodeObj && nodeObj.iconName ? nodeObj.iconName : "",
            // Parent: "",
            Parent: nodeObj && nodeObj.Parent ? nodeObj.Parent : "",
            children: [],
            remarks: nodeObj && nodeObj.remarks ? nodeObj.remarks : "",
            completedOn:
              nodeObj && nodeObj.completedOn ? nodeObj.completedOn : "",
          };
          mappedArr[parentId] = newItem; // the parent id as key, and made-up an item as value
          if (newItem.Parent) {
            delete mappedArr["node_" + parentId];
          }
        }
      });

      // Loop over hash table
      for (var id in mappedArr) {
        if (mappedArr.hasOwnProperty(id)) {
          let mappedElem = mappedArr[id];

          // If the element is not at the root level, add it to its parent array of children. Note this will continue till we have only root level elements left
          if (mappedElem.Parent) {
            var parentId = mappedElem.Parent;
            mappedArr[parentId].children.push(mappedElem);
          }

          // If the element is at the root level, directly push to the tree
          else {
            tree.push(mappedElem);
          }
        }
      }

      return tree;
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
@import "../../assets/css/orgchart.css";
.orgchart-container {
  height: fit-content !important;
}
</style>
