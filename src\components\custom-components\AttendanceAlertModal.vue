<template>
  <div>
    <v-dialog
      v-model="openNotificationModal"
      class="box-radius"
      max-width="850px"
      scrollable
      :persistent="isPersistent"
      @click:outside="!isPersistent ? $emit('close-notify-modal') : ''"
    >
      <v-card class="box-radius">
        <div v-if="showCloseModal" class="d-flex justify-end">
          <v-icon
            color="primary"
            class="pr-3 pt-3"
            @click="$emit('close-notify-modal')"
          >
            fas fa-times
          </v-icon>
        </div>
        <v-card-text class="pa-0">
          <v-container class="pb-0 mt-n4">
            <slot name="topContent" />
            <v-row id="notify-modal-content">
              <v-col
                cols="12"
                xs="12"
                class="d-flex flex-column justify-center"
              >
                <div v-if="modalTitle" class="d-flex align-center">
                  <span class="mr-2">
                    <v-icon color="primary" size="25">fas fa-circle</v-icon>
                  </span>
                  <span class="text-primary text-h6 font-weight-bold">{{
                    modalTitle
                  }}</span>
                </div>
                <div v-if="modalContent" class="my-4 pl-2">
                  <span class="text-body-2 text-primary-darken-2">{{
                    modalContent
                  }}</span>
                </div>
                <slot name="bodyContent" />
                <div v-if="buttonText" class="mt-2 d-flex justify-center">
                  <v-btn
                    rounded
                    color="primary"
                    class="d-flex"
                    @click="$emit('button-action')"
                  >
                    <v-icon v-if="buttonIcon" class="mr-1">
                      {{ buttonIcon }}
                    </v-icon>
                    <span class="font-weight-bold">{{ buttonText }}</span>
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-container>

          <v-row v-if="footerNotes" class="footer-notes mt-2">
            <span>
              <slot name="footerContent" />
            </span>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: "CameraAlertModal",
  props: {
    openNotifyModal: {
      type: [Boolean, Number],
      required: true,
    },
    footerNotes: {
      type: Boolean,
      default: false,
    },
    buttonText: {
      type: String,
      default: "",
    },
    buttonIcon: {
      type: String,
      default: "",
    },
    modalContent: {
      type: String,
      default: "",
    },
    modalTitle: {
      type: String,
      default: "",
    },
    showCloseModal: {
      type: Boolean,
      default: true,
    },
    isPersistent: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      openNotificationModal: false,
    };
  },
  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.openNotificationModal = this.openNotifyModal;
  },
};
</script>

<style scoped>
.box-radius {
  border-radius: 15px !important;
}
</style>
