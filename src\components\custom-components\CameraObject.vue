<template>
  <div class="web-camera-container">
    <v-alert v-if="displayFailure">
      <div class="font-weight-bold mb-2 text-h6 red--text">
        Something went wrong!
      </div>
      <div>
        <ol>
          <li class="red--text">
            Make sure you have a camera connected to your computer / mobile.
          </li>
          <li class="red--text">
            Make sure you have allowed access to your camera.
          </li>
          <li class="red--text">
            Make sure you are not using the camera in other application.
          </li>
        </ol>
      </div>
    </v-alert>

    <div v-show="isCameraOpen && isLoading" class="camera-loading">
      <ul class="loader-circle">
        <li />
        <li />
        <li />
      </ul>
    </div>

    <div
      v-if="!displayFailure"
      :class="
        windowWidth < 460
          ? 'text-h5 font-weight-medium grey--text text-center'
          : 'text-h3 pb-2 font-weight-medium grey--text text-center'
      "
    >
      {{ generateChallengeName }}
    </div>
    <div
      v-if="isCameraOpen"
      v-show="!isLoading"
      class="camera-box d-flex justify-center"
      :class="{ flash: isShotPhoto }"
    >
      <!-- <div class="camera-shutter" :class="{'flash' : isShotPhoto}"></div> -->
      <video
        v-show="!isPhotoTaken"
        ref="camera"
        class="camera-video"
        width="100%"
        height="100%"
        autoplay
      />

      <canvas
        v-show="isPhotoTaken"
        id="photoTaken"
        ref="canvas"
        class="photoTaken"
        :width="450"
        :height="337.5"
      />
    </div>

    <div v-if="isCameraOpen && !isLoading" class="mt-3">
      <v-btn
        rounded="lg"
        color="primary"
        variant="elevated"
        class="d-flex"
        @click="isFaceRegister ? registerPhoto() : takePhoto()"
      >
        <v-icon class="mr-1" size="15"> fas fa-camera </v-icon>
        <span class="font-weight-bold">{{ buttonText }}</span>
      </v-btn>
    </div>
  </div>
</template>

<script>
export default {
  name: "CameraObject",
  props: {
    bottomButtonText: {
      type: String,
      default() {
        return "";
      },
    },
    challenges: {
      type: Array,
      default() {
        return [];
      },
    },
    isFaceRegister: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      displayFailure: false,
      takenPhotobase64: {},
      isCameraOpen: false,
      isPhotoTaken: false,
      isShotPhoto: false,
      isLoading: false,
      link: "#",
      notVerified: false,
      faceChallenges: [],
      challengeName: "",
      challengesCount: 0,
      challengeComplete: false,
    };
  },

  computed: {
    buttonText() {
      return this.bottomButtonText || this.$t("dashboard.takePhoto");
    },
    generateChallengeName() {
      if (this.challengeName === "Smile") {
        return this.$t("dashboard.pleaseSmile");
      } else if (this.challengeName === "MouthOpen") {
        return this.$t("dashboard.openYourMouth");
      } else {
        return "";
      }
    },
    //screen size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  mounted() {
    document.getElementById("notify-modal-content").style.display = "none";
    this.isCameraOpen = true;
    this.createCameraElement();
    this.faceChallenges = JSON.parse(JSON.stringify(this.challenges));
    this.takenPhotobase64.enableLivenessDetection = this.challenges.length
      ? "Yes"
      : "No";
    this.takenPhotobase64.imageWithChallenges = [];
    this.challengeName = this.faceChallenges[this.challengesCount]
      ? this.faceChallenges[this.challengesCount].challengeName
      : "";
  },

  methods: {
    toggleCamera() {
      if (this.isCameraOpen) {
        this.isCameraOpen = false;
        this.isPhotoTaken = false;
        this.isShotPhoto = false;
        this.stopCameraStream();
      } else {
        this.isCameraOpen = true;
        this.createCameraElement();
      }
    },
    createCameraElement() {
      this.isLoading = true;

      const constraints = (window.constraints = {
        audio: false,
        video: true,
      });

      navigator.mediaDevices
        .getUserMedia(constraints)
        .then((stream) => {
          this.isLoading = false;
          if (this.$refs.camera && stream) {
            this.$refs.camera.srcObject = stream;
          } else {
            this.displayFailure = true;
            this.isCameraOpen = false;
          }
        })
        .catch(() => {
          this.isLoading = false;
          this.displayFailure = true;
          this.isCameraOpen = false;
        });
    },

    stopCameraStream() {
      let tracks = this.$refs.camera.srcObject.getTracks();

      tracks.forEach((track) => {
        track.stop();
      });
    },

    registerPhoto() {
      if (!this.isPhotoTaken) {
        this.isShotPhoto = true;
        const FLASH_TIMEOUT = 50;
        setTimeout(() => {
          this.isShotPhoto = false;
        }, FLASH_TIMEOUT);
      }

      this.isPhotoTaken = !this.isPhotoTaken;

      const context = this.$refs.canvas.getContext("2d");
      context.drawImage(this.$refs.camera, 0, 0, 450, 337.5);
      const canvas = document
        .getElementById("photoTaken")
        .toDataURL("image/jpeg");
      this.takenPhotobase64 = canvas;
      this.$emit("send-base-64", canvas);
    },

    takePhoto() {
      if (!this.isPhotoTaken) {
        this.isShotPhoto = true;

        const FLASH_TIMEOUT = 50;

        setTimeout(() => {
          this.isShotPhoto = false;
        }, FLASH_TIMEOUT);
      }

      this.isPhotoTaken = !this.isPhotoTaken;

      const context = this.$refs.canvas.getContext("2d");
      context.drawImage(this.$refs.camera, 0, 0, 450, 337.5);
      const canvas = document
        .getElementById("photoTaken")
        .toDataURL("image/jpeg");

      if (!this.faceChallenges.length) {
        this.takenPhotobase64.imageWithChallenges.push({
          challengeId: 0,
          challengeName: "",
          sourceImage: canvas,
        });
        this.isPhotoTaken = true;
        this.$emit("send-base-64", this.takenPhotobase64);
      } else if (this.faceChallenges.length > this.challengesCount) {
        this.takenPhotobase64.imageWithChallenges.push({
          challengeId: this.faceChallenges[this.challengesCount].challengeId,
          challengeName:
            this.faceChallenges[this.challengesCount].challengeName,
          sourceImage: canvas,
        });
        this.challengesCount++;
        this.isPhotoTaken = false;
        this.isCameraOpen = true;
        if (this.challengesCount === this.faceChallenges.length) {
          this.isPhotoTaken = true;
          this.$emit("send-base-64", this.takenPhotobase64);
        } else {
          this.challengeName =
            this.faceChallenges[this.challengesCount].challengeName;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
body {
  display: flex;
  justify-content: center;
}

.web-camera-container {
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #ccc;
  border-radius: 4px;

  .camera-button {
    margin-bottom: 2rem;
  }

  .camera-box {
    .camera-shutter {
      opacity: 0;
      width: 450px;
      height: 337.5px;
      background-color: #fff;
      position: absolute;

      &.flash {
        opacity: 1;
      }
    }
  }

  .camera-shoot {
    margin: 1rem 0;

    button {
      height: 60px;
      width: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 100%;

      img {
        height: 35px;
        object-fit: cover;
      }
    }
  }

  .camera-loading {
    overflow: hidden;
    height: 100%;
    position: absolute;
    width: 100%;
    min-height: 150px;
    margin: 3rem 0 0 -1.2rem;

    ul {
      height: 100%;
      position: absolute;
      width: 100%;
      z-index: 999999;
      margin: 0;
    }

    .loader-circle {
      display: block;
      height: 14px;
      margin: 0 auto;
      top: 50%;
      left: 100%;
      transform: translateY(-50%);
      transform: translateX(-50%);
      position: absolute;
      width: 100%;
      padding: 0;

      li {
        display: block;
        float: left;
        width: 10px;
        height: 10px;
        line-height: 10px;
        padding: 0;
        position: relative;
        margin: 0 0 0 4px;
        background: #999;
        animation: preload 1s infinite;
        top: -50%;
        border-radius: 100%;

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }
  }

  @keyframes preload {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0.4;
    }
    100% {
      opacity: 1;
    }
  }
}

@media (max-height: 800px) {
  .camera-video {
    width: 70%;
  }
}
@media (max-height: 676px) {
  .camera-video {
    width: 50%;
  }
}

@media (max-width: 547px) {
  .photoTaken {
    width: 100%;
    height: 100%;
  }
}
</style>
