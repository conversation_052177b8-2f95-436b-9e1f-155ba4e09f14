<template>
  <div class="relative">
    <div
      :id="keyIndex"
      class="pa-3 on-hover position-relative d-flex align-center justify-center"
      style="min-height: 150px"
      :style="
        isError && !fileName
          ? 'border: 2px solid red'
          : 'border: 2px solid rgb(var(--v-theme-primary))'
      "
      @dragover="dragover"
      @drop="drop"
    >
      <v-file-input
        id="inputFile"
        ref="file"
        style="display: none"
        :model-value="fileContent"
        accept="application/pdf, .doc, .docx"
        @update:modelValue="onChangeFiles"
      ></v-file-input>
      <div
        style=""
        class="d-flex justify-center align-center"
        :class="isMobileView ? 'w-100' : 'w-50'"
      >
        <label
          :for="'assetsFieldHandle' + keyIndex"
          class="block cursor-pointer d-flex align-center"
          :style="fileName ? 'max-width: 80%; overflow: hidden' : ''"
        >
          <!-- <div
                class="circular-add d-flex align-center justify-center mr-3"
                v-if="!fileName"
              >
                <v-icon color="primary">fas fa-plus</v-icon>
              </div> -->
          <div
            v-if="!fileName"
            style="cursor: pointer"
            class="text-h6 d-flex flex-column align-center"
            @click="focusOnInput()"
          >
            <div>
              Drag & Drop your Resume or
              <span class="text-primary font-weight-medium"
                >Upload from the system</span
              >
              <span v-if="Mandatory" style="color: red">*</span>
            </div>
            <p class="text-subtitle-2 text-red-lighten-1">
              You can upload .pdf, .docx and .doc upto 4MB
            </p>
          </div>
          <div v-else class="d-flex">
            <div class="text-h6">
              {{ fileName }}
            </div>
            <!-- <v-icon color="amber" size="30" class="ml-1">create</v-icon> -->
          </div>
        </label>
        <v-icon
          v-if="fileName"
          size="30"
          @click="onFileRemove()"
          color="red"
          class="ml-3"
          >fas fa-trash</v-icon
        >
      </div>
    </div>
    <div v-if="isError && !fileName" class="caption text-red">
      {{ isError }}
    </div>
    <!-- File size warning -->
    <div v-if="isFileSizeExceed" class="caption text-red mt-2">
      File size exceeds 4MB limit. Please select a smaller file.
    </div>
  </div>
</template>

<script>
export default {
  name: "DragAndDropFiles",

  emits: ["file-event-success"],

  props: {
    fileCategory: {
      type: String,
      required: true,
    },
    oldFileName: {
      type: String,
      required: true,
    },
    keyIndex: {
      type: String,
      default: "file-upload",
    },
    isError: {
      type: String,
      default: "",
    },
    message: {
      type: Boolean,
      default: false,
    },
    Mandatory: {
      type: Boolean,
      default: false,
    },
  },

  data: () => ({
    fileContent: null,
    fileInBase64Format: "",
    fileList: {}, // Store our uploaded files
    fileName: "",
    formattedFileName: "",
    fileSize: 0,
    isFileChanged: false,
    isFileSizeExceed: false,
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  mounted() {
    this.fileName = this.oldFileName;
  },

  watch: {
    oldFileName(val) {
      this.fileName = val;
    },
  },

  methods: {
    onChangeFiles(value) {
      this.fileContent = value;
      // Reset file size exceed flag
      this.isFileSizeExceed = false;

      if (this.fileContent && this.fileContent.name) {
        // Check if file size exceeds 4MB (4194304 bytes)
        if (this.fileContent.size > 4194304) {
          this.isFileSizeExceed = true;
          // Clear the file content to prevent upload
          this.fileContent = null;
          return;
        }
        let d = new Date();
        let dFormat =
          [d.getFullYear(), d.getMonth() + 1, d.getDate()].join("-") +
          "." +
          [d.getHours(), d.getMinutes(), d.getSeconds()].join(":");
        this.fileName = this.fileContent.name;
        this.formattedFileName =
          "resume" + "?" + dFormat + "?" + "?" + this.fileContent.name;
        this.fileSize = this.fileContent.size.toString();
        if (typeof FileReader === "function") {
          const reader = new FileReader();

          reader.onload = (event) => {
            this.fileInBase64Format = event.target.result;
            this.fileInBase64Format = this.fileInBase64Format.split("base64,");
            this.fileInBase64Format = this.fileInBase64Format[1];
            this.$emit("file-event-success", {
              fileContent: this.fileContent,
              base64: this.fileInBase64Format,
              formattedName: this.formattedFileName,
            });
          };

          reader.readAsDataURL(this.fileContent);
        }
        this.isFileChanged = true;
      }
    },
    focusOnInput() {
      if (!this.fileName) {
        let fileUpload = document.getElementById("inputFile");
        fileUpload.click();
      }
    },
    onFileRemove() {
      this.fileName = "";
      this.formattedFileName = "";
      this.fileContent = null;
      this.fileInBase64Format = "";
      this.isFileSizeExceed = false;
      this.$emit("file-event-success");
    },

    dragover(event) {
      event.preventDefault();
    },

    drop(event) {
      event.preventDefault();
      this.$refs.file.files = event?.dataTransfer?.files
        ? event.dataTransfer.files
        : "";
      this.$refs.file.dispatchEvent(new Event("change", { bubbles: true }));
    },
  },
};
</script>
<style>
.circular-add {
  background: lightgrey;
  height: 35px;
  width: 35px;
  border-radius: 50%;
}
.on-hover::after {
  position: absolute;
  width: 100%;
  top: 0;
  content: "";
  background-color: rgb(var(--v-theme-primary));
  opacity: 0.3;
  bottom: 0;
  left: 0;
  z-index: -1;
}
.on-hover {
  position: relative;
}
</style>
