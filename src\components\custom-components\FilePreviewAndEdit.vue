<template>
  <div>
    <v-dialog
      v-if="showDialog"
      v-model="openModal"
      persistent
      :width="openEdit ? 600 : 800"
    >
      <v-card class="rounded-lg">
        <v-card-title>
          <div class="d-flex" style="width: 100%">
            {{ !openEdit ? "Attachment" : "Resume Upload" }}
            <v-spacer></v-spacer>
            <v-icon
              v-if="!openEdit && currentFileIndex === 0"
              size="sm"
              color="primary"
              @click="openEdit = true"
              class="mr-5"
              >fas fa-edit</v-icon
            >
            <v-icon size="sm" color="primary" @click="closePreviewModal()"
              >fas fa-times</v-icon
            >
          </div>
        </v-card-title>
        <v-card-text
          v-if="!openEdit"
          :style="`
            max-height: calc(100vh - 200px);
            overflow: scroll;
            min-height: 400px;
          `"
        >
          <div class="mt-n4 text-center">
            <div class="text-primary text-h6 font-weight-medium">
              {{ formattedFileName }}
            </div>
            <img
              v-if="imgSrc"
              :src="imgSrc"
              alt="image source"
              style="width: 100%"
            />
            <iframe
              v-else-if="docxSrc"
              :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
                docxSrc
              )}&wdAr=1.7777777777777777`"
              allowfullscreen
              frameborder="0"
              style="width: 100%; height: 100vh"
            ></iframe>
            <vue-pdf-app
              v-else-if="pdfSrc"
              style="height: 100vh"
              :pdf="pdfSrc"
            ></vue-pdf-app>
            <div v-else-if="txtSrc">
              {{ fileContentTxt }}
            </div>
          </div>
        </v-card-text>
        <div v-else class="d-flex align-center justify-center w-100">
          <v-row>
            <v-col
              cols="12"
              sm="12"
              md="12"
              lg="12"
              xlg="12"
              class="step-content-col"
            >
              <v-row justify="center">
                <v-col :cols="isMobileView ? '12' : '12'">
                  <file-pond
                    v-if="!fileProperties || !fileProperties.length"
                    ref="vueFilePond"
                    name="test"
                    :allowFileTypeValidation="true"
                    labelFileTypeNotAllowed="Allowed file types are .pdf, .docx and .doc"
                    allowImageValidateSize="true"
                    maxFileSize="4MB"
                    @addfile="getFileContent"
                    v-bind:files="fileProperties"
                    label-idle="<div class='custom-file-upload-label-icon'>
                                                                  <i class='fas fa-cloud-upload-alt upload-icon' style='color: grey;font-size: 70px;'></i>
                                                                  </div>
                                                                  <div class='custom-file-pond-label-header'>
                                                                          <span class='custom-file-pond-label-sub-header1'>Choose a file </span> 	&nbsp; 
                                                                          <span class='custom-file-pond-label-sub-header2'> or drag it here.</span>
                                                                  </div>
                                                                  <div class='custom-label-less-than-450' style='margin-top: 40px'>
                                                                      <div style='text-align: center;'><span class='custom-file-pond-label-sub-header1'>Choose a file</span> or drag it here.</div>
                                                                      <div style='text-align: center;'>*All .pdf, .docx and .doc file types are supported.</div>
                                                                  </div>
                                                                  <div class='custom-file-pond-label-content'style='margin-top: 40px'>*All .pdf, .docx and .doc file types are supported.</div>"
                    accepted-file-types="application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/msword"
                    @removefile="removeFileContent"
                  />
                  <v-card
                    v-else
                    class="rounded-lg common-box-shadow"
                    color="cyan-lighten-5"
                  >
                    <v-card-text
                      class="d-flex justify-center align-center text-center mx-auto flex-column mb-4 mt-n4 pa-5"
                    >
                      <v-icon class="mt-n1" size="40" color="red"
                        >fas fa-file</v-icon
                      >
                      <div class="text-h6 mt-3" style="max-width: 300px">
                        {{ formattedFileName }}
                      </div>
                      <div class="mt-2">
                        {{ fileProperties[1] }}
                      </div>
                      <v-btn
                        dense
                        size="small"
                        rounded="lg"
                        class="mt-4"
                        color="grey-lighten-2"
                        @click="removeFileContent"
                        >Remove and upload again</v-btn
                      >
                      <br />
                    </v-card-text>
                  </v-card>
                  <div
                    v-if="showFileErrMsg && fileProperties.length == 0"
                    class="text-caption mt-n2 ml-2"
                    style="color: #b00020"
                  >
                    File is required
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </div>
        <div class="d-flex justify-center">
          <v-btn
            v-if="otherAttachment.length && !openEdit"
            class="font-weight-bold mb-2 primary mr-8"
            rounded="lg"
            variant="outlined"
            :disabled="currentFileIndex === 0"
            @click="fetchNextFile('previous')"
          >
            Previous
          </v-btn>

          <v-btn
            v-if="!openEdit"
            rounded="lg"
            color="primary"
            variant="elevated"
            class="font-weight-bold mb-2 primary"
            @click="downloadFile()"
          >
            Download
          </v-btn>
          <v-btn
            v-else
            rounded="lg"
            :disabled="!isFileChanged"
            variant="elevated"
            color="primary"
            class="font-weight-bold mb-2 mt-5"
            @click="updateResumeFile()"
          >
            Upload
          </v-btn>
          <v-btn
            v-if="otherAttachment.length && !openEdit"
            class="font-weight-bold mb-2 primary ml-8"
            rounded="lg"
            variant="elevated"
            :disabled="currentFileIndex === this.otherAttachment.length"
            @click="fetchNextFile('next')"
          >
            Next
          </v-btn>
        </div>
      </v-card>
      <AppLoading v-if="isFetchingFiles"></AppLoading>
    </v-dialog>

    <!-- Content without dialog wrapper -->
    <v-card v-else class="rounded-lg">
      <v-card-title>
        <div class="d-flex" style="width: 100%">
          {{ !openEdit ? "Attachment" : "Resume Upload" }}
          <v-spacer></v-spacer>
          <v-icon
            v-if="!openEdit && currentFileIndex === 0"
            size="sm"
            color="primary"
            @click="openEdit = true"
            class="mr-5"
            >fas fa-edit</v-icon
          >
          <v-icon
            v-if="openEdit"
            size="sm"
            color="primary"
            @click="closePreviewModal()"
            >fas fa-times</v-icon
          >
        </div>
      </v-card-title>
      <v-card-text
        v-if="!openEdit"
        :style="`
          max-height: calc(100vh - 200px);
          overflow: scroll;
          min-height: 400px;
        `"
      >
        <div class="text-center">
          <div class="text-primary text-h6 font-weight-medium">
            {{ formattedFileName }}
          </div>
          <img
            v-if="imgSrc"
            :src="imgSrc"
            alt="image source"
            style="width: 100%"
          />
          <iframe
            v-else-if="docxSrc"
            :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
              docxSrc
            )}&wdAr=1.7777777777777777`"
            allowfullscreen
            frameborder="0"
            style="width: 100%; height: 100vh"
          ></iframe>
          <vue-pdf-app
            v-else-if="pdfSrc"
            style="height: 100vh"
            :pdf="pdfSrc"
          ></vue-pdf-app>
          <div v-else-if="txtSrc">
            {{ fileContentTxt }}
          </div>
        </div>
      </v-card-text>
      <div v-else class="d-flex align-center justify-center w-100">
        <v-row>
          <v-col
            cols="12"
            sm="12"
            md="12"
            lg="12"
            xlg="12"
            class="step-content-col"
          >
            <v-row justify="center">
              <v-col :cols="isMobileView ? '12' : '12'">
                <file-pond
                  v-if="!fileProperties || !fileProperties.length"
                  ref="vueFilePond"
                  name="test"
                  :allowFileTypeValidation="true"
                  labelFileTypeNotAllowed="Allowed file types are .pdf, .docx, .doc and .rtf"
                  allowImageValidateSize="true"
                  maxFileSize="4MB"
                  @addfile="getFileContent"
                  v-bind:files="fileProperties"
                  label-idle="<div class='custom-file-upload-label-icon'>
                                                                <i class='fas fa-cloud-upload-alt upload-icon' style='color: grey;font-size: 70px;'></i>
                                                                </div>
                                                                <div class='custom-file-pond-label-header'>
                                                                        <span class='custom-file-pond-label-sub-header1'>Choose a file </span> 	&nbsp;
                                                                        <span class='custom-file-pond-label-sub-header2'> or drag it here.</span>
                                                                </div>
                                                                <div class='custom-label-less-than-450' style='margin-top: 40px'>
                                                                    <div style='text-align: center;'><span class='custom-file-pond-label-sub-header1'>Choose a file</span> or drag it here.</div>
                                                                    <div style='text-align: center;'>*All .pdf, .docx, .doc and .rtf file types are supported.</div>
                                                                </div>
                                                                <div class='custom-file-pond-label-content'style='margin-top: 40px'>*All .pdf, .docx, .doc and .rtf file types are supported.</div>"
                  accepted-file-types="application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document, application/msword, application/rtf"
                  @removefile="removeFileContent"
                />
                <v-card
                  v-else
                  class="rounded-lg common-box-shadow"
                  color="cyan-lighten-5"
                >
                  <v-card-text
                    class="d-flex justify-center align-center text-center mx-auto flex-column mb-4 mt-n4 pa-5"
                  >
                    <v-icon class="mt-n1" size="40" color="red"
                      >fas fa-file</v-icon
                    >
                    <div class="text-h6 mt-3" style="max-width: 300px">
                      {{ formattedFileName }}
                    </div>
                    <div class="mt-2">
                      {{ fileProperties[1] }}
                    </div>
                    <v-btn
                      dense
                      size="small"
                      rounded="lg"
                      class="mt-4"
                      color="grey-lighten-2"
                      @click="removeFileContent"
                      >Remove and upload again</v-btn
                    >
                    <br />
                  </v-card-text>
                </v-card>
                <div
                  v-if="showFileErrMsg && fileProperties.length == 0"
                  class="text-caption mt-n2 ml-2"
                  style="color: #b00020"
                >
                  File is required
                </div>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </div>
      <div class="d-flex justify-center">
        <v-btn
          v-if="otherAttachment.length && !openEdit"
          class="font-weight-bold mb-2 primary mr-8"
          rounded="lg"
          variant="outlined"
          :disabled="currentFileIndex === 0"
          @click="fetchNextFile('previous')"
        >
          Previous
        </v-btn>

        <v-btn
          v-if="!openEdit"
          rounded="lg"
          color="primary"
          variant="elevated"
          class="font-weight-bold mb-2 primary"
          @click="downloadFile()"
        >
          Download
        </v-btn>
        <v-btn
          v-else
          rounded="lg"
          :disabled="!isFileChanged"
          variant="elevated"
          color="primary"
          class="font-weight-bold mb-2 mt-5"
          @click="updateResumeFile()"
        >
          Upload
        </v-btn>
        <v-btn
          v-if="otherAttachment.length && !openEdit"
          class="font-weight-bold mb-2 primary ml-8"
          rounded="lg"
          variant="elevated"
          :disabled="currentFileIndex === this.otherAttachment.length"
          @click="fetchNextFile('next')"
        >
          Next
        </v-btn>
      </div>
    </v-card>
    <AppLoading v-if="isFetchingFiles"></AppLoading>
  </div>
</template>

<script>
import vueFilePond from "vue-filepond";
import "filepond/dist/filepond.min.css";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
// Create component
const FilePond = vueFilePond(
  FilePondPluginFileValidateType,
  FilePondPluginImagePreview
);
import VuePdfApp from "vue3-pdf-app";
import "vue3-pdf-app/dist/icons/main.css";
import moment from "moment";

export default {
  name: "FilePreviewAndEdit",

  components: { VuePdfApp, FilePond },

  props: {
    showDialog: {
      type: Boolean,
      default: true,
    },
    fileRetrieveType: {
      type: String,
      default: "documents",
    },
    fileAction: {
      type: String,
      default: "download",
    },
    otherAttachment: {
      type: Array,
      required: false,
    },
    heading: {
      type: String,
      default: "Attachments",
    },
    fileNamePosition: {
      type: Number,
      default: 3,
    },
    fileName: {
      type: String,
      required: true,
    },
    File_Name: {
      type: String,
      required: false,
    },
    folderName: {
      type: String,
      required: true,
    },
    isUpload: {
      type: Boolean,
      required: true,
    },
    fileSize: {
      type: [String, Number],
      default: "",
    },
    isUnauthorized: {
      type: Boolean,
      default: false,
    },
    getCloudfrontUrl: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["close-preview-modal", "update-resume-details"],
  data() {
    return {
      openModal: false,
      isFetchingFiles: false,
      pdfSrc: "",
      imgSrc: "",
      docxSrc: "",
      downloadLink: "",
      fileContentTxt: "",
      txtSrc: "",
      isFileChanged: false,
      openEdit: false,
      hasNextFile: false,
      fileProperties: [],
      editedFileSize: "",
      fileContent: {},
      showFileErrMsg: false,
      allAttachment: [],
      currentFileIndex: 0,
      docName: null,
      candidateDetails: {
        Other_Attachments: [],
      },
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isNextButtonDisabled() {
      return (
        this.otherAttachment.length === 0 ||
        this.currentFileIndex === this.otherAttachment.length - 1
      );
    },
    isPrevButtonDisabled() {
      return this.currentFileIndex === 0;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    formattedFileName() {
      if (
        this.currentFileIndex === 0 ||
        !this.otherAttachment ||
        !this.otherAttachment.length
      ) {
        if (this.fileProperties && this.fileProperties.length) {
          let fileNameChunks = this.fileProperties[0].split("?");
          return fileNameChunks && fileNameChunks.length > 0
            ? fileNameChunks[this.fileNamePosition]
            : "-";
        }
      } else if (
        this.currentFileIndex !== 0 &&
        this.otherAttachment &&
        this.otherAttachment.length
      ) {
        return this.otherAttachment[this.currentFileIndex - 1].File_Name.split(
          "?"
        )[0];
      }
      return "File Name";
    },
  },

  mounted() {
    if (!this.isUpload) {
      this.fileProperties = [this.fileName, this.fileSize];
      this.retrieveFileContents();
    } else {
      this.openEdit = true;
    }
    this.openModal = true;
  },

  methods: {
    downloadFile() {
      window.open(this.downloadLink, "_blank");
    },

    closePreviewModal() {
      if (this.showDialog) {
        this.openModal = false;
        this.$emit("close-preview-modal");
      } else {
        this.openEdit = false;
        this.fileProperties = [this.fileName, this.fileSize];
      }
    },
    getFileContent(error, file) {
      this.isFileChanged = true;
      // error: returned from plugin, if error, then we don't process file reade
      if (!error) {
        // define fire reader
        let fileSize = file.file.size / 1000;
        let d = new Date();
        let dFormat =
          [d.getFullYear(), d.getMonth() + 1, d.getDate()].join("-") +
          "." +
          [d.getHours(), d.getMinutes(), d.getSeconds()].join(":");
        this.fileProperties = [
          "resume" + "?" + dFormat + "?" + "?" + file.file.name,
          fileSize + "kb",
        ];
        this.fileContent = file.file;
      } else {
        this.fileProperties = [];
        this.fileContent = "";
        this.$emit("file-upload-error");
      }
    },
    removeFileContent() {
      this.isFileChanged = true;
      this.fileProperties = [];
    },
    updateResumeFile() {
      if (this.fileProperties && this.fileProperties.length) {
        this.showFileErrMsg = false;
        let emitValue = this.fileProperties;
        emitValue.push(this.fileContent);
        this.$emit("update-resume-details", emitValue);
      } else {
        this.showFileErrMsg = true;
      }
    },

    async retrieveFileContents() {
      let vm = this;
      vm.downloadLink = "";
      vm.pdfSrc = "";
      vm.imgSrc = "";
      vm.docxSrc = "";
      vm.isFetchingFiles = true;
      let fullFilePath =
        this.domainName +
        "_" +
        "/" +
        this.orgCode +
        "/" +
        this.folderName +
        "/" +
        this.fileName;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fullFilePath,
          action: vm.fileAction,
          type: vm.fileRetrieveType,
          client: vm.isUnauthorized ? "apolloClientAO" : null,
          getCloudfrontUrl: vm.getCloudfrontUrl ? 1 : 0,
        })
        .then(async (presignedUrl) => {
          vm.downloadLink = presignedUrl;
          let fName = vm.fileName.toLowerCase();
          if (fName.includes("pdf")) {
            vm.pdfSrc = presignedUrl;
            vm.imgSrc = "";
            vm.docxSrc = "";
            vm.txtSrc = "";
          } else if (
            fName.includes(".docx") ||
            fName.includes(".rtf") ||
            fName.includes(".doc")
          ) {
            vm.docxSrc = presignedUrl;
            vm.pdfSrc = "";
            vm.imgSrc = "";
            vm.txtSrc = "";
          } else if (fName.includes(".txt")) {
            vm.txtSrc = presignedUrl;
            vm.imgSrc = "";
            vm.pdfSrc = "";
            vm.docxSrc = "";
            let response = await fetch(presignedUrl);
            let content = await response.text();
            vm.fileContentTxt = content;
          } else {
            vm.txtSrc = "";
            vm.imgSrc = presignedUrl;
            vm.pdfSrc = "";
            vm.docxSrc = "";
          }
          vm.openModal = true;
          vm.isFetchingFiles = false;
        })
        .catch(() => {
          vm.isFetchingFiles = false;
        });
    },
    async fetchNextFile(direction) {
      let vm = this;
      this.openEdit = false;
      // Ensure there are attachments to navigate
      if (!vm.otherAttachment || vm.otherAttachment.length === 0) {
        return;
      }

      // Store current file index to handle previous or next logic
      let nextFileIndex = vm.currentFileIndex;

      // Handle Previous button logic
      if (direction.toLowerCase() === "previous") {
        // If current index is 0, retrieve the first document
        if (vm.currentFileIndex <= 1 && vm.currentFileIndex !== 0) {
          vm.currentFileIndex = 0;
          this.retrieveFileContents();
          return;
        }
        if (vm.currentFileIndex === 0) {
          vm.currentFileIndex = vm.otherAttachment.length;
        }

        // If not at the first file, move to the previous file
        nextFileIndex = vm.currentFileIndex - 1;
      }

      // Handle Next button logic
      if (direction.toLowerCase() === "next") {
        if (vm.currentFileIndex >= vm.otherAttachment.length) {
          vm.currentFileIndex = 0;
          this.retrieveFileContents();
          return;
        }

        // If not at the last file, move to the next file
        nextFileIndex = vm.currentFileIndex + 1;
      }
      // Update the current file index
      this.currentFileIndex = nextFileIndex;
      nextFileIndex = nextFileIndex ? nextFileIndex - 1 : nextFileIndex;

      // Fetch the file based on the updated file index
      let nextFile = vm.otherAttachment[nextFileIndex];
      let fullFilePath =
        this.domainName +
        "_" +
        "/" +
        this.orgCode +
        "/" +
        "otherAttachment" +
        "/" +
        nextFile.File_Name;

      vm.isFetchingFiles = true;

      // Retrieve the next file
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fullFilePath,
          action: vm.fileAction,
          type: vm.fileRetrieveType,
          client: vm.isUnauthorized ? "apolloClientAO" : null,
          getCloudfrontUrl: vm.getCloudfrontUrl ? 1 : 0,
        })
        .then(async (presignedUrl) => {
          vm.downloadLink = presignedUrl;

          // Determine file type and set the appropriate source (PDF or image)
          let fName = nextFile.File_Name.toLowerCase();
          if (fName.includes("pdf")) {
            vm.pdfSrc = presignedUrl;
            vm.docName = nextFile.Document_Name;
            vm.imgSrc = "";
            vm.docxSrc = "";
            vm.textSrc = "";
          } else if (
            fName.includes(".docx") ||
            fName.includes(".rtf") ||
            fName.includes(".doc")
          ) {
            // Fetch the DOCX file and render it using docx-preview
            vm.docxSrc = presignedUrl;
            vm.pdfSrc = "";
            vm.imgSrc = "";
            vm.textSrc = "";
          } else if (fName.includes(".txt")) {
            vm.txtSrc = presignedUrl;
            vm.imgSrc = "";
            vm.pdfSrc = "";
            vm.docxSrc = "";
            let response = await fetch(presignedUrl);
            let content = await response.text();
            vm.fileContentTxt = content;
          } else {
            vm.imgSrc = presignedUrl;
            vm.pdfSrc = "";
            vm.docxSrc = "";
            vm.textSrc = "";
          }

          // Open the file in the modal and set fetching state to false
          vm.openModal = true;
          vm.isFetchingFiles = false;
        })
        .catch((err) => {
          console.error("Error retrieving file:", err);
          vm.isFetchingFiles = false;
        });
    },
  },
};
</script>
<style lang="css" scoped>
@import "../../assets/css/customFilePondDocUploader.css";
</style>
