<template>
  <v-dialog v-model="openModal" persistent width="1200">
    <v-card>
      <v-card-title class="mb-4">
        <div class="d-flex" style="width: 100%">
          {{ heading }}
          <v-spacer></v-spacer>
          <v-icon color="primary" @click="closePreviewModal()"
            >fas fa-times</v-icon
          >
        </div>
      </v-card-title>
      <v-row>
        <v-col
          v-if="length > 1 ? true : false"
          cols="1"
          class="d-flex align-center justify-center ml-2"
        >
          <v-icon
            class="action-btn"
            :style="
              current === 0
                ? 'cursor: not-allowed !important; opacity: 0.5'
                : 'cursor: pointer !important'
            "
            @click="current === 0 ? {} : $emit('prev-document')"
          >
            fas fa-chevron-circle-left
          </v-icon>
        </v-col>
        <v-col :cols="length > 1 ? '10' : '12'">
          <v-row style="width: 100%; height: 100%; margin-left: 5px">
            <v-col
              :md="
                !isObjectEmpty(documentDetails) && visibleDetails == true
                  ? '9'
                  : '12'
              "
              sm="12"
            >
              <v-card-text
                :style="`
          max-height: calc(100vh - 200px);
          overflow: scroll;
          min-height: 400px;
        `"
              >
                <div class="d-flex" style="min-height: 400px">
                  <div
                    class="mt-n4 text-center"
                    :style="
                      hasSlotContent
                        ? 'min-width: 75%; max-width: 75%'
                        : 'min-width: 100%; max-width: 100%'
                    "
                  >
                    <div class="text-primary text-h6 font-weight-medium">
                      {{ formattedFileName }}
                    </div>
                    <img
                      v-if="imgSrc"
                      :src="imgSrc"
                      alt="image source"
                      style="width: 100%"
                    />
                    <iframe
                      v-else-if="docxSrc"
                      :src="`https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(
                        docxSrc
                      )}&wdAr=1.7777777777777777`"
                      allowfullscreen
                      frameborder="0"
                      style="width: 100%; height: 100vh"
                    ></iframe>
                    <vue-pdf-app
                      v-else-if="pdfSrc"
                      style="height: 100%"
                      :pdf="pdfSrc"
                    ></vue-pdf-app>
                    <pre v-else-if="txtSrc" class="text-document-display">
                    {{ fileContent }}
                  </pre
                    >
                  </div>
                  <div
                    v-if="hasSlotContent"
                    class="pa-3"
                    style="min-width: 25%; max-width: 25%"
                  >
                    <slot name="message"></slot>
                  </div>
                </div>
              </v-card-text>
              <div class="d-flex justify-center">
                <v-btn
                  rounded="lg"
                  theme="dark"
                  variant="elevated"
                  class="font-weight-bold mb-2 primary"
                  @click="downloadFile()"
                >
                  Download
                </v-btn>
              </div>
            </v-col>
            <v-col
              v-if="
                !isObjectEmpty(documentDetails) && visibleDetails == true
                  ? true
                  : false
              "
            >
              <v-row>
                <v-col
                  v-for="(field, index) in documentDetails"
                  :key="index"
                  cols="12"
                  sm="12"
                >
                  <p
                    v-if="field.Field_Visiblity == 'Yes'"
                    class="text-subtitle-1 text-grey-darken-1"
                  >
                    {{ field.Field_Alias }}
                  </p>
                  <p
                    v-if="field.Field_Visiblity == 'Yes'"
                    class="text-subtitle-1 font-weight-regular"
                  >
                    {{ field.Field_value }}
                  </p>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col
          v-if="length > 1 ? true : false"
          cols="1"
          class="d-flex align-center justify-center ml-n4"
        >
          <v-icon
            :class="current === length - 1 ? '' : 'action-btn'"
            :style="
              current === length - 1
                ? 'cursor: not-allowed !important; opacity: 0.5'
                : 'cursor: pointer !important'
            "
            @click="current === length - 1 ? {} : $emit('next-document')"
          >
            fas fa-chevron-circle-right
          </v-icon>
        </v-col>
      </v-row>
    </v-card>
    <AppLoading v-if="isFetchingFiles"></AppLoading>
  </v-dialog>
  <a ref="downloadLink" style="display: none"></a>
</template>

<script>
import VuePdfApp from "vue3-pdf-app";
import "vue3-pdf-app/dist/icons/main.css";
import { checkNullValue } from "@/helper";

export default {
  name: "FilePreviewModal",

  components: { VuePdfApp },

  props: {
    fileRetrieveType: {
      type: String,
      default: "documents",
    },
    fileAction: {
      type: String,
      default: "view",
    },
    heading: {
      type: String,
      default: "Attachments",
    },
    fileNamePosition: {
      type: Number,
      default: 3,
    },
    fileName: {
      type: String,
      required: true,
    },
    folderName: {
      type: String,
      required: true,
    },
    appendUnderScoreInDomain: {
      type: Boolean,
      default: false,
    },
    documentDetails: {
      type: Object,
      default: () => {
        return {};
      },
    },
    current: {
      type: Number,
      default: 0,
    },
    length: {
      type: Number,
      default: 0,
    },
    visibleDetails: {
      type: Boolean,
      default: false,
    },
    preSignedUrlExists: {
      type: Boolean,
      default: false,
    },
    preSignedUrl: {
      type: String,
      default: "",
    },
    getCloudfrontUrl: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["close-preview-modal", "next-document", "prev-document"],
  data() {
    return {
      openModal: false,
      isFetchingFiles: false,
      pdfSrc: "",
      imgSrc: "",
      docxSrc: "",
      txtSrc: "",
      downloadLink: "",
      fileContent: "",
    };
  },
  computed: {
    hasSlotContent() {
      // Check if the 'message' slot has content
      return !!(this.$slots.message && this.$slots.message().length > 0);
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    domainName() {
      let domain = this.$store.getters.domain;
      if (this.appendUnderScoreInDomain) {
        domain = domain + "_";
      }
      return domain;
    },
    formattedFileName() {
      if (this.fileName) {
        let fileNameChunks = this.fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[this.fileNamePosition]
          : "-";
      }
      return "File Name";
    },
  },

  mounted() {
    if (!this.preSignedUrlExists) {
      this.retrieveFileContents();
    } else {
      this.displayDocument();
    }
    this.openModal = true;
  },

  watch: {
    fileName() {
      if (!this.preSignedUrlExists) {
        this.retrieveFileContents();
      } else {
        this.displayDocument();
      }
    },
  },

  methods: {
    checkNullValue,
    downloadFile() {
      if (!this.downloadLink) {
        console.error("No download link available");
        return;
      }

      // Create a temporary anchor element to trigger download
      const link = document.createElement("a");
      link.href = this.downloadLink;
      link.download = this.formattedFileName || "download";

      // For cross-origin URLs or when download attribute might not work,
      // fetch the file and create a blob URL
      if (
        this.downloadLink.includes("amazonaws.com") ||
        this.downloadLink.includes("s3")
      ) {
        this.downloadFileAsBlob();
      } else {
        // Append to body, click, and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },

    async downloadFileAsBlob() {
      try {
        const response = await fetch(this.downloadLink);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = this.formattedFileName || "download";

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the blob URL
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Download failed:", error);
        // Fallback to window.open if blob download fails
        window.open(this.downloadLink, "_blank");
      }
    },

    isObjectEmpty(obj) {
      return Object.keys(obj).length === 0;
    },

    closePreviewModal() {
      this.openModal = false;
      this.$emit("close-preview-modal");
    },

    async displayDocument() {
      this.downloadLink = this.preSignedUrl;
      if (this.fileName?.toLowerCase().includes("pdf")) {
        this.pdfSrc = this.preSignedUrl;
        this.imgSrc = "";
        this.docxSrc = "";
        this.txtSrc = "";
      } else if (
        this.fileName?.toLowerCase().includes(".docx") ||
        this.fileName?.toLowerCase().includes(".doc") ||
        this.fileName?.toLowerCase().includes(".rtf")
      ) {
        // Fetch the DOCX file and render it using docx-preview
        this.docxSrc = this.preSignedUrl;
        this.pdfSrc = "";
        this.imgSrc = "";
        this.txtSrc = "";
      } else if (this.fileName?.toLowerCase().includes(".txt")) {
        this.txtSrc = this.preSignedUrl;
        this.imgSrc = "";
        this.pdfSrc = "";
        this.docxSrc = "";
        let response = await fetch(this.preSignedUrl);
        let content = await response.text();
        this.fileContent = content;
      } else {
        this.txtSrc = "";
        this.imgSrc = this.preSignedUrl;
        this.pdfSrc = "";
        this.docxSrc = "";
      }
      this.openModal = true;
      this.isFetchingFiles = false;
    },

    async retrieveFileContents() {
      let vm = this;
      vm.downloadLink = "";
      vm.pdfSrc = "";
      vm.imgSrc = "";
      vm.isFetchingFiles = true;
      let fullFilePath =
        vm.domainName +
        "/" +
        vm.orgCode +
        "/" +
        vm.folderName +
        "/" +
        vm.fileName;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fullFilePath,
          action: vm.fileAction,
          type: vm.fileRetrieveType,
          getCloudfrontUrl: vm.getCloudfrontUrl ? 1 : 0,
        })
        .then(async (presignedUrl) => {
          vm.downloadLink = presignedUrl;
          if (vm.fileName?.toLowerCase().includes("pdf")) {
            vm.pdfSrc = presignedUrl;
            vm.imgSrc = "";
            vm.docxSrc = "";
            vm.txtSrc = "";
          } else if (
            vm.fileName?.toLowerCase().includes(".docx") ||
            vm.fileName?.toLowerCase().includes(".doc") ||
            vm.fileName?.toLowerCase().includes(".rtf")
          ) {
            // Fetch the DOCX file and render it using docx-preview
            vm.docxSrc = presignedUrl;
            vm.pdfSrc = "";
            vm.imgSrc = "";
            vm.txtSrc = "";
          } else if (vm.fileName?.toLowerCase().includes(".txt")) {
            vm.txtSrc = presignedUrl;
            vm.imgSrc = "";
            vm.pdfSrc = "";
            vm.docxSrc = "";
            let response = await fetch(presignedUrl);
            let content = await response.text();
            vm.fileContent = content;
          } else {
            vm.txtSrc = "";
            vm.imgSrc = presignedUrl;
            vm.pdfSrc = "";
            vm.docxSrc = "";
          }
          vm.openModal = true;
          vm.isFetchingFiles = false;
        })
        .catch(() => {
          vm.isFetchingFiles = false;
        });
    },
  },
};
</script>
<style>
.action-btn:hover {
  transform: scale(2);
  cursor: pointer;
}
.text-document-display {
  max-width: 100%;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  overflow-x: auto;
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
}
</style>
