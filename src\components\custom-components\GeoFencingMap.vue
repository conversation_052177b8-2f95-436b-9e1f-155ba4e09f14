<template>
  <div>
    <v-card class="d-flex mt-3 pa-3 mx-auto" style="width: 100%" elevation="10">
      <l-map
        ref="map"
        style="height: calc(100vh - 500px)"
        class="map"
        :zoom="zoom"
        :center="
          loggedPosition && loggedPosition.length
            ? loggedPosition
            : [12.9165167, 79.1324986]
        "
        @ready="onMapReady"
      >
        <v-tilelayer-googlemutant
          :attributions="attribution"
          :apikey="apiKey"
        />
        <l-circle
          v-for="(point, index) in centerPoint"
          :key="index"
          :lat-lng="point"
          :radius="fenceRadius[index]"
          color="red"
        />
        <l-marker
          :lat-lng="
            loggedPosition && loggedPosition.length
              ? loggedPosition
              : [12.9165167, 79.1324986]
          "
        />
      </l-map>
    </v-card>
    <div class="mt-3 d-flex justify-center">
      <v-btn
        rounded="lg"
        color="primary"
        class="d-flex mt-3"
        @click="checkGeoFencing"
      >
        <v-icon class="mr-1" color="white" size="18"> fas fa-map-pin </v-icon>
        <span class="font-weight-bold">{{
          buttonText === "Check Out"
            ? "Verify and Check Out"
            : "Verify and Check In"
        }}</span>
      </v-btn>
    </div>
  </div>
</template>

<script>
import "leaflet/dist/leaflet.css";
import { LMap, LCircle, LMarker } from "@vue-leaflet/vue-leaflet";
import L from "leaflet";
import "leaflet.gridlayer.googlemutant";
import config from "@/config.js";

export default {
  name: "GeoFencingMap",
  components: {
    LMap,
    LCircle,
    LMarker,
  },
  emits: ["check-geo-fencing"],
  props: {
    centerPoint: {
      type: Array,
      default: () => [],
    },
    fenceRadius: {
      type: Array,
      default: () => [],
    },
    loggedPosition: {
      type: Array,
      default: () => [],
    },
    buttonText: {
      type: String,
      default: "Check In",
    },
  },
  data() {
    return {
      apiKey: config.googleMapsAPIKey,
      isInsideTheFence: false,
      url: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
      attribution:
        '&copy; <a target="_blank" href="https://hrapp.in/">HRAPP</a>',
      zoom: 15,
      insideCenterPoint: "",
    };
  },
  mounted() {
    setTimeout(function () {
      window.dispatchEvent(new Event("resize"));
    }, 250);
  },
  methods: {
    // Initialize Google Mutant tile layer when map is ready
    onMapReady(mapInstance) {
      // Add Google Mutant tile layer
      const googleMutant = L.gridLayer.googleMutant({
        type: "roadmap",
        attribution: this.attribution,
        apikey: this.apiKey,
      });

      // Add the tile layer to the map
      googleMutant.addTo(mapInstance);
    },
    //check geofencing
    checkGeoFencing() {
      //for every center point array, check if logged position is within the radius
      this.centerPoint.forEach((centerPoint) => {
        this.fenceRadius.forEach((fenceRadius) => {
          let ky = 40000 / 360;
          let kx = Math.cos((Math.PI * centerPoint[0]) / 180.0) * ky;
          let dx = Math.abs(centerPoint[1] - this.loggedPosition[1]) * kx;
          let dy = Math.abs(centerPoint[0] - this.loggedPosition[0]) * ky;
          this.isInside = Math.sqrt(dx * dx + dy * dy) <= fenceRadius / 1000;
          if (this.isInside) {
            this.isInsideTheFence = true;
            this.insideCenterPoint = centerPoint.toString();
          }
        });
      });
      this.$emit("check-geo-fencing", {
        isInsideTheFence: this.isInsideTheFence,
        insideCenterPoint: this.insideCenterPoint,
      });
    },
  },
};
</script>

<style>
.map {
  z-index: 1;
}
</style>
