<template>
  <div>
    <v-dialog
      v-model="openNotificationModal"
      class="box-radius"
      width="850px"
      min-height="200px"
      scrollable
      @click:outside="$emit('close-notify-modal')"
    >
      <v-card class="box-radius">
        <div class="d-flex justify-end">
          <v-icon
            color="primary"
            class="pr-3 pt-3"
            @click="$emit('close-notify-modal')"
          >
            fas fa-times
          </v-icon>
        </div>
        <v-card-text class="pa-0">
          <v-container class="pb-0 mt-n4">
            <v-row class="pa-2 pa-sm-8 mt-n5">
              <v-col
                cols="12"
                xs="12"
                :sm="windowWidth < 750 ? '12' : '4'"
                md="4"
                lg="4"
                class="d-flex justify-center align-center px-4"
              >
                <img :src="getImageUrl" :width="imageWidth" />
              </v-col>
              <v-col
                cols="12"
                xs="12"
                :sm="windowWidth < 750 ? '12' : '8'"
                md="8"
                lg="8"
                class="d-flex flex-column justify-center px-6"
              >
                <div v-if="modalTitle" class="d-flex align-center">
                  <span class="mr-2">
                    <v-progress-circular
                      model-value="100"
                      color="primary"
                      :size="22"
                    />
                  </span>
                  <span class="text-primary text-h6 font-weight-bold">{{
                    modalTitle
                  }}</span>
                </div>
                <div v-if="modalContent" class="my-4 pl-2">
                  <span class="text-body-2 text-primary">{{
                    modalContent
                  }}</span>
                </div>
                <slot name="bodyContent" />
                <div v-if="buttonText" class="mt-2">
                  <v-btn
                    rounded="lg"
                    color="primary"
                    class="d-flex"
                    variant="elevated"
                    @click="$emit('button-action')"
                  >
                    <v-icon
                      v-if="buttonIcon"
                      style="color: white !important"
                      class="mr-1"
                    >
                      {{ buttonIcon }}
                    </v-icon>
                    <span class="font-weight-bold">{{ buttonText }}</span>
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-container>

          <v-row v-if="footerNotes" class="footer-notes mt-n2">
            <img :src="getFooterImage" width="60" class="mr-3" />

            <span>
              <slot name="footerContent" />
            </span>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: "NotifyAlertModal",
  emits: ["close-notify-modal", "button-action"],
  props: {
    openNotifyModal: {
      type: [Boolean, Number],
      required: true,
    },
    imageName: {
      type: String,
      required: true,
    },
    footerImage: {
      type: String,
      default: "common/idea-bulb",
    },
    footerNotes: {
      type: Boolean,
      default: false,
    },
    buttonText: {
      type: String,
      default: "Refresh",
    },
    buttonIcon: {
      type: String,
      default: "",
    },
    modalContent: {
      type: String,
      default: "",
    },
    modalTitle: {
      type: String,
      default: "",
    },
    imageWidth: {
      type: String,
      default: "150",
    },
  },
  data() {
    return {
      openNotificationModal: false,
    };
  },
  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getImageUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/" + this.imageName + ".webp");
      else return require("@/assets/images/" + this.imageName + ".png");
    },
    getFooterImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/" + this.footerImage + ".webp");
      else return require("@/assets/images/" + this.footerImage + ".png");
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.openNotificationModal = this.openNotifyModal;
  },
};
</script>

<style scoped>
.box-radius {
  border-radius: 15px !important;
}
</style>
