<template>
  <v-overlay
    :model-value="showOverlay"
    class="d-flex justify-end"
    @click:outside="onCloseOverlay()"
  >
    <v-card :height="windowInnerHeight + 'px'" :width="componentWidth">
      <v-card-title
        class="d-flex justify-space-between align-center bg-primary"
      >
        <div class="text-h6">{{ formName }}</div>
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="onCloseOverlay()"
          color="white"
        ></v-btn>
      </v-card-title>
      <v-card-text style="height: calc(100% - 70px)">
        <div class="mt-2 d-flex justify-end">
          <v-btn
            v-if="allowEdit && formAccess.update"
            variant="text"
            color="primary"
            density="compact"
            @click="$emit('open-edit-form', selectedItem)"
          >
            <span class="mr-2">{{ "Edit" }}</span>
            <v-icon size="13">fas fa-edit</v-icon>
          </v-btn>
        </div>
        <div
          class="overflow-y-auto"
          :style="{
            height: `calc(100% - 24px)`,
          }"
        >
          <v-row>
            <v-col
              v-for="(label, index) in formLabels"
              :key="index"
              :cols="label.col || (windowWidth > 400 ? 6 : 12)"
              class="px-md-6 pb-0"
            >
              <div class="text-subtitle-1 text-grey-darken-1">
                {{ label.label }}
              </div>
              <div class="text-subtitle-1 font-weight-regular">
                <section class="text-body-2">
                  {{ checkNullValue(selectedItem[label.key]) }}
                </section>
              </div>
            </v-col>
          </v-row>
          <v-row v-if="moreDetailsList.length > 0">
            <v-col>
              <MoreDetails
                :more-details-list="moreDetailsList"
                :open-close-card="openMoreDetails"
                @on-open-close="openMoreDetails = $event"
              ></MoreDetails>
            </v-col>
          </v-row>
        </div>
      </v-card-text>
    </v-card>
  </v-overlay>
</template>
<script setup>
import {
  ref,
  defineProps,
  watch,
  defineEmits,
  computed,
  defineAsyncComponent,
  onMounted,
} from "vue";
import { useStore } from "vuex";
import { useFormatData } from "@/composables/formatingComposables";
const MoreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard")
);

// Composables
const store = useStore();
const { formatDate, checkNullValue } = useFormatData();

const props = defineProps({
  showForm: {
    type: Boolean,
    default: false,
  },
  componentWidth: {
    type: String,
    default: "50vw",
  },
  formName: {
    type: String,
    default: "",
  },
  selectedItem: {
    type: Object,
    default: () => ({}),
  },
  allowEdit: {
    type: Boolean,
    default: true,
  },
  formLabels: {
    type: Array,
    required: true, // Array of objects with label and key pair. label will be displayed as a label and key will be used to fetch the value from the selected item.
  },
  formAccess: {
    type: [Boolean, Object],
    default: false,
  },
});
onMounted(() => {
  if (props.showForm) {
    prefillMoreDetails();
  }
});
const showOverlay = ref(props.showForm);
watch(
  () => props.showForm,
  (val) => {
    showOverlay.value = val;
    if (val) {
      prefillMoreDetails();
    }
  }
);
const onCloseOverlay = () => {
  emit("close-side-overlay");
};
const emit = defineEmits(["close-side-overlay", "open-edit-form"]);
const windowInnerHeight = computed(() => {
  return store.state.windowInnerHeight;
});
const windowWidth = computed(() => {
  return store.state.windowWidth;
});

// More Details
const moreDetailsList = ref([]);
const openMoreDetails = ref(false);
const prefillMoreDetails = () => {
  moreDetailsList.value = [];
  let Added_On = "",
    Added_By = "",
    Updated_By = "",
    Updated_On = "";
  Added_On = formatDate(props.selectedItem.Added_On, true, true);
  Added_By = props.selectedItem.Added_By_Name;
  Updated_By = props.selectedItem.Updated_By_Name;
  Updated_On = formatDate(props.selectedItem.Updated_On, true, true);
  if (Added_On && Added_By) {
    moreDetailsList.value.push({
      actionDate: Added_On,
      actionBy: Added_By,
      text: "Added",
    });
  }
  if (Updated_By && Updated_On) {
    moreDetailsList.value.push({
      actionDate: Updated_On,
      actionBy: Updated_By,
      text: "Updated",
    });
  }
};
</script>
