<template>
  <div class="d-flex flex-column justify-center align-center">
    <v-sheet class="mx-xs-auto mr-sm-auto" max-width="100%">
      <v-slide-group multiple show-arrows>
        <!-- Next icon Slot -->
        <template #next>
          <v-avatar
            size="20"
            color="hover"
            class="d-flex justify-center align-center mb-1"
          >
            <v-icon color="primary" size="12"> fas fa-angle-right </v-icon>
          </v-avatar>
        </template>

        <!-- Previous icon Slot -->
        <template #prev>
          <v-avatar
            size="20"
            color="hover"
            class="d-flex justify-center align-center mb-1"
          >
            <v-icon color="primary" size="12"> fas fa-angle-left </v-icon>
          </v-avatar>
        </template>
        <!-- slider content -->
        <v-slide-group-item
          v-for="(avatar, index) in avatarArray"
          :key="index"
          v-slot="{ toggle }"
        >
          <v-list
            class="pb-0 bg-transparent"
            style="width: 120px"
            @click="toggle"
          >
            <v-list-item
              class="d-flex flex-column justify-center align-center bg-transparent pa-1"
            >
              <v-avatar
                size="30"
                density="compact"
                :color="getRandomColors()"
                class="d-flex justify-center align-center mx-auto"
              >
                <img
                  v-if="avatar.photoPath"
                  :src="avatar.photoPath"
                  alt="Profile_Pic"
                  class="d-block mx-auto"
                  style="
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                  "
                />
                <span
                  v-else
                  class="text-h6 text-white d-flex justify-center align-center"
                >
                  {{ letterAvatar(avatar.title, true) }}
                </span>
              </v-avatar>

              <!-- Desktop tooltip content -->
              <v-tooltip
                v-if="!isMobileView && avatar.tooltip"
                location="bottom"
                max-width="300"
              >
                <template #activator="{ props }">
                  <div
                    v-bind="props"
                    class="text-center pb-0 pt-1"
                    style="width: 120px"
                  >
                    <div
                      class="text-primary font-weight-medium text-truncate"
                      style="font-size: 15px"
                    >
                      {{ avatar.title }}
                    </div>
                    <div
                      class="font-weight-medium text-truncate pt-1"
                      style="font-size: 12px; white-space: break-spaces"
                    >
                      {{ avatar.subtitle }}
                    </div>
                  </div>
                </template>
                <template #default>
                  <div class="text-left">
                    <div v-if="avatar.tooltip.name" class="font-weight-bold">
                      {{ avatar.tooltip.name }}
                    </div>
                    <div v-if="avatar.tooltip.employeeId">
                      {{ avatar.tooltip.employeeId }}
                    </div>
                    <div v-if="avatar.tooltip.designation">
                      {{ avatar.tooltip.designation }}
                    </div>
                    <div v-if="avatar.tooltip.department">
                      {{ avatar.tooltip.department }}
                    </div>
                  </div>
                </template>
              </v-tooltip>

              <!-- Desktop content without tooltip -->
              <div
                v-else-if="!isMobileView"
                class="text-center pb-0 pt-1"
                style="width: 120px"
              >
                <div
                  class="text-primary font-weight-medium text-truncate"
                  style="font-size: 15px"
                >
                  {{ avatar.title }}
                </div>
                <div
                  class="font-weight-medium text-truncate pt-1"
                  style="font-size: 12px; white-space: break-spaces"
                >
                  {{ avatar.subtitle }}
                </div>
              </div>

              <!-- Mobile menu with tooltip -->
              <v-menu v-if="isMobileView" location="bottom">
                <template #activator="{ props }">
                  <div
                    v-bind="props"
                    class="text-center pb-0 pt-1"
                    style="width: 120px"
                  >
                    <div
                      class="text-primary font-weight-medium text-truncate"
                      style="font-size: 15px"
                    >
                      {{ avatar.title }}
                    </div>
                    <div
                      class="font-weight-medium text-truncate pt-1"
                      style="font-size: 12px; white-space: break-spaces"
                    >
                      {{ avatar.subtitle }}
                    </div>
                  </div>
                </template>
                <v-card v-if="avatar.tooltip" color="black" theme="dark">
                  <v-card-text>
                    <div
                      v-if="avatar.tooltip.name"
                      class="font-weight-bold text-white"
                    >
                      {{ avatar.tooltip.name }}
                    </div>
                    <div v-if="avatar.tooltip.employeeId" class="text-white">
                      {{ avatar.tooltip.employeeId }}
                    </div>
                    <div v-if="avatar.tooltip.designation" class="text-white">
                      {{ avatar.tooltip.designation }}
                    </div>
                    <div v-if="avatar.tooltip.department" class="text-white">
                      {{ avatar.tooltip.department }}
                    </div>
                  </v-card-text>
                </v-card>
              </v-menu>
            </v-list-item>
          </v-list>
        </v-slide-group-item>
      </v-slide-group>
    </v-sheet>
  </div>
</template>

<script>
export default {
  name: "AvatarSlider",
  props: {
    avatarArray: {
      type: Array,
      required: true,
    },
  },
  data: () => ({
    randomColors: [
      // Vuetify 3 compatible color names
      "red",
      "pink",
      "blue-grey",
      "purple",
      "orange",
      "deep-purple",
      "lime",
      "indigo",
      "blue",
      "cyan",
      "teal",
      "light-blue",
      "green",
    ],
  }),

  computed: {
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  methods: {
    // Function to get random colors compatible with Vuetify 3
    getRandomColors() {
      const finalColor =
        this.randomColors[Math.floor(Math.random() * this.randomColors.length)];
      // Convert to Vuetify 3 format: "color-lighten-2"
      return `${finalColor}-lighten-2`;
    },

    // Method to return first letter in capital of each word (replaces deprecated filter)
    letterAvatar(value, isSingleLetter) {
      if (!value) return "";
      var firstChar = value ? value.charAt(0).toUpperCase() : "";
      var lastChar = value
        ? value.split(" ").pop().charAt(0).toUpperCase()
        : "";
      //condition checked for single letter avatar
      if (isSingleLetter) {
        return firstChar;
      } else {
        return firstChar + lastChar;
      }
    },
  },
};
</script>
<style scoped>
.v-sheet {
  background-color: transparent;
  border-color: transparent;
}

/* Ensure proper slide group styling */
:deep(.v-slide-group__content) {
  display: flex;
  align-items: center;
}

/* Avatar container styling */
:deep(.v-slide-group-item) {
  margin-right: 6px;
}

/* Navigation arrows styling */
:deep(.v-slide-group__prev),
:deep(.v-slide-group__next) {
  min-width: 24px !important;
  margin: 0 2px;
}
</style>
