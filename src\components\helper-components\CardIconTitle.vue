<template>
  <div class="d-flex flex-column justify-center align-start">
    <v-card
      :color="cardBgColor"
      class="d-flex align-center justify-start pa-2 rounded-lg"
      min-height="50"
      max-width="200px"
      width="100%"
    >
      <div
        class="d-flex flex-column avatar-icon mr-2"
        :class="iconBackgroundClass"
      >
        <v-avatar v-if="iconInAvatar" size="22" :color="iconColorValue">
          <v-icon
            color="white"
            style="font-size: 20px"
            class="font-weight-bold"
          >
            {{ iconName }}
          </v-icon>
        </v-avatar>
        <v-icon
          v-else
          :color="iconColorValue"
          style="font-size: 20px"
          class="font-weight-bold"
        >
          {{ iconName }}
        </v-icon>
      </div>
      <span
        class="text-primary font-weight-medium d-flex align-center"
        style="font-size: 14px"
        >{{ cardTitle
        }}<span v-if="count" class="count-circle bg-indigo-lighten-5 ml-1">{{
          count
        }}</span></span
      >
    </v-card>
  </div>
</template>

<script>
export default {
  name: "CardIconTitle",
  props: {
    cardTitle: {
      type: String,
      required: true,
    },
    iconName: {
      type: String,
      required: true,
    },
    iconInAvatar: {
      type: Boolean,
      default: false,
    },
    cardBg: {
      type: String,
      default: "light-blue-lighten-4",
    },
    iconColor: {
      type: String,
      default: "error-lighten-2",
    },
    iconBackground: {
      type: String,
      default: "error-lighten-5",
    },
    count: {
      type: [Number, String],
      default: 0,
    },
  },

  computed: {
    // Convert Vuetify 2 color format to Vuetify 3 format
    cardBgColor() {
      return this.convertColorFormat(this.cardBg);
    },

    iconColorValue() {
      return this.convertColorFormat(this.iconColor);
    },

    iconBackgroundClass() {
      return this.convertColorFormat(this.iconBackground);
    },
  },

  methods: {
    // Convert Vuetify 2 color classes to Vuetify 3 format
    convertColorFormat(colorString) {
      if (!colorString) return "";

      // Handle space-separated color format (e.g., "error lighten-2")
      const parts = colorString.split(" ");
      if (parts.length === 2) {
        const [color, variant] = parts;
        return `bg-${color}-${variant}`;
      }

      // Handle hyphenated format (e.g., "error-lighten-2")
      if (colorString.includes("-")) {
        return `bg-${colorString}`;
      }

      // Return as-is for single color names
      return colorString;
    },
  },
};
</script>

<style lang="css" scoped>
.avatar-icon {
  padding: 0.6em;
  border-radius: 15px !important;
}
.count-circle {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 22px;
  height: 22px;
  border-radius: 50%;
}
</style>
