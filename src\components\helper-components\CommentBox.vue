<template>
  <div class="mr-3">
    <span
      v-if="status"
      class="text-deep-purple-lighten-1 font-weight-bold pr-3 text-body-2"
      >{{ status }}</span
    >
    <span v-if="comments">
      <v-menu :open-on-hover="!isMobileView" offset-y left>
        <template #activator="{ on }">
          <v-icon
            color="blue-lighten-3"
            v-on="on"
            @click.stop="
              {
              }
            "
            >{{ iconName }}</v-icon
          >
        </template>
        <v-card class="pa-4 text-caption text-blue-grey" width="200">
          <div class="text-primary pb-1">Comment</div>
          {{ comments }}
        </v-card>
      </v-menu>
    </span>
  </div>
</template>

<script>
export default {
  name: "CommentBox",
  props: {
    // Left side text
    status: {
      type: String,
      default: "",
    },
    // Right side icon(hover)
    iconName: {
      type: String,
      default: "fas fa-comment-alt",
    },
    // displayed in menu when icon is hovered
    comments: {
      type: String,
      required: true,
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
};
</script>
