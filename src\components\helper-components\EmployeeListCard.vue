<template>
  <v-card class="rounded-lg">
    <v-card-title class="mt-1">
      <div class="text-center text-h6 text-grey-darken-1 font-weight-bold">
        {{ modalTitle }}
      </div>
    </v-card-title>
    <v-card-text>
      <v-row v-if="showFilterSearch" justify="center">
        <v-col cols="10">
          <v-text-field
            v-model="searchInput"
            density="compact"
            :placeholder="translate ? $t('settings.search') : 'Search'"
            clearable
            variant="solo"
            prepend-inner-icon="fas fa-search"
          >
          </v-text-field>
        </v-col>
        <v-col cols="2" md="1" v-if="showFilter">
          <EmployeeDefaultFilterMenu
            menu-position="left"
            :is-search="false"
            class="pl-4"
            :list-items="empListItemsOriginal"
            :isApplyFilter="isApplyFilter"
            :departmentIdKey="departmentIdKey"
            :designationIdKey="designationIdKey"
            :locationIdKey="locationIdKey"
            :empTypeIdKey="empTypeIdKey"
            :workScheduleIdKey="workScheduleIdKey"
            @reset-emp-filter="onResetFilter()"
            @applied-filter="onApplyFilter($event)"
          ></EmployeeDefaultFilterMenu>
        </v-col>
      </v-row>
      <v-data-table
        v-if="empListItems.length > 0"
        v-model="selectedEmpRecords"
        id="gridView"
        :headers="tableHeaders"
        :show-select="!isMobileView && selectable"
        :items="empListItems"
        :items-per-page="50"
        fixed-header
        :height="
          $store.getters.getTableHeightBasedOnScreenSize(
            isMobileView ? 250 : 470,
            empListItems
          )
        "
        :items-per-page-text="
          translate ? $t('settings.itemPerPage') : 'Items per page:'
        "
        :item-value="employeeIdKey"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
        ]"
      >
        <template
          v-if="!isMobileView && selectable"
          v-slot:[`header.data-table-select`]="{ selectAll }"
        >
          <v-checkbox-btn
            v-model="selectAllBox"
            color="primary"
            false-icon="far fa-circle"
            true-icon="fas fa-check-circle"
            indeterminate-icon="fas fa-minus-circle"
            class="mt-1"
            @change="selectAll(selectAllBox)"
          ></v-checkbox-btn>
        </template>
        <template #item="{ item }">
          <tr
            class="data-table-tr bg-white"
            :class="
              isMobileView
                ? 'v-data-table__mobile-table-row'
                : selectable
                ? 'cursor-pointer'
                : ''
            "
          >
            <td
              v-if="selectable"
              :class="isMobileView ? 'v-data-table__mobile-row' : ''"
            >
              <div class="d-flex justify-center align-center">
                <v-checkbox-btn
                  v-model="item.isSelected"
                  color="primary"
                  false-icon="far fa-circle"
                  true-icon="fas fa-check-circle"
                  class="mt-n2 ml-n2"
                  @click.stop="
                    {
                    }
                  "
                  @change="checkAllSelected()"
                ></v-checkbox-btn>
              </div>
            </td>

            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1"
              >
                Employee Id
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section class="text-subtitle-1 font-weight-regular">
                  {{
                    item[userDefinedEmpIdKey] ? item[userDefinedEmpIdKey] : "-"
                  }}
                </section>
              </div>
            </td>

            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1"
              >
                Employee Name
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section
                  class="text-primary text-subtitle-1 font-weight-regular"
                >
                  {{ item[employeeNameKey] ? item[employeeNameKey] : "-" }}
                </section>
              </div>
            </td>

            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1"
              >
                Designation
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section class="text-subtitle-1 font-weight-regular">
                  {{ item[designationKey] ? item[designationKey] : "-" }}
                </section>
              </div>
            </td>

            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1"
              >
                Department
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section class="text-subtitle-1 font-weight-regular">
                  {{ item[deptNameKey] ? item[deptNameKey] : "-" }}
                </section>
              </div>
            </td>

            <td
              v-if="extraColumnKey && extraColumnText"
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1"
              >
                {{ extraColumnText }}
              </div>
              <div
                :class="isMobileView ? 'v-data-table__mobile-row__cell' : ''"
              >
                <section class="text-subtitle-1 font-weight-regular">
                  {{
                    item[extraColumnKey]
                      ? isExtraColumnAsDate
                        ? formatDate(item[extraColumnKey])
                        : item[extraColumnKey]
                      : "-"
                  }}
                </section>
              </div>
            </td>
          </tr>
        </template>
      </v-data-table>

      <AppFetchErrorScreen
        v-else
        key="no-results-screen"
        main-title="No matching search results found"
        image-name="common/no-records"
      ></AppFetchErrorScreen>
    </v-card-text>
    <div v-if="selectable" class="d-flex justify-center mb-4">
      <v-btn
        rounded="lg"
        color="primary"
        variant="elevated"
        min-width="100"
        :disabled="selectedEmployees.length === 0"
        @click="onAddEmployees()"
      >
        <span class="primary">{{ submitButtonText }}</span>
      </v-btn>
    </div>
  </v-card>
</template>

<script>
import moment from "moment";
import EmployeeDefaultFilterMenu from "../custom-components/EmployeeDefaultFilterMenu.vue";

export default {
  name: "EmployeesListCard",
  components: {
    EmployeeDefaultFilterMenu,
  },
  props: {
    selectable: {
      type: Boolean,
      required: true,
    },
    showFilter: {
      type: Boolean,
      required: true,
    },
    showModal: {
      type: Boolean,
      required: true,
    },
    modalTitle: {
      type: String,
      default: "Employee(s)",
    },
    employeesList: {
      type: Array,
      required: true,
    },
    showFilterSearch: {
      type: Boolean,
      default: false,
    },
    submitButtonText: {
      type: String,
      default: "Select",
    },
    // grid
    userDefinedEmpIdKey: {
      type: String,
      default: "user_defined_empid",
    },
    employeeNameKey: {
      type: String,
      default: "employee_name",
    },
    deptNameKey: {
      type: String,
      default: "department_name",
    },
    designationKey: {
      type: String,
      default: "designation_name",
    },
    extraColumnKey: {
      type: String,
      default: "",
    },
    extraColumnText: {
      type: String,
      default: "",
    },
    isExtraColumnAsDate: {
      type: Boolean,
      default: false,
    },
    // filter
    isApplyFilter: {
      type: Boolean,
      default: false,
    },
    departmentIdKey: {
      type: String,
      default: "Department_Id",
    },
    designationIdKey: {
      type: String,
      default: "Designation_Id",
    },
    empTypeIdKey: {
      type: String,
      default: "EmpType_Id",
    },
    locationIdKey: {
      type: String,
      default: "Location_Id",
    },
    workScheduleIdKey: {
      type: String,
      default: "Work_Schedule",
    },
    employeeIdKey: {
      type: String,
      default: "employee_id",
    },
    selectedItem: {
      type: Array,
      default: () => {
        return [];
      },
    },
    translate: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      openModal: false,
      searchInput: "",
      empListItems: [],
      empListItemsOriginal: [],
      selectAllBox: false,
      selectedEmpRecords: [],
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getNoRecordImageUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/common/no-records.webp");
      else return require("@/assets/images/common/no-records.png");
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    tableHeaders() {
      let headers = [
        {
          title: this.translate
            ? this.$t("settings.employeeId")
            : "Employee Id",
          key: this.userDefinedEmpIdKey,
        },
        {
          title: this.translate
            ? this.$t("settings.employeeName")
            : "Employee Name",
          key: this.employeeNameKey,
        },
        {
          title: this.translate
            ? this.$t("settings.designation")
            : "Designation",
          key: this.designationKey,
        },
        {
          title: this.translate ? this.$t("settings.department") : "Department",
          key: this.deptNameKey,
        },
      ];
      if (this.extraColumnKey && this.extraColumnText) {
        headers.push({
          title: this.extraColumnText,
          key: this.extraColumnKey,
        });
      }
      return headers;
    },
    selectedEmployees() {
      let selected = this.empListItems.filter((el) => el.isSelected === true);
      return selected && selected.length > 0 ? selected : [];
    },
    formatDate() {
      return (date) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return moment(date).format(orgDateFormat);
      };
    },
  },

  mounted() {
    let employees = this.employeesList;
    if (this.selectable) {
      // Compare and update isSelected property
      employees.forEach((obj2) => {
        obj2.isSelected = false; // Default value

        this.selectedItem.forEach((obj1) => {
          if (obj1.employee_id === obj2.employee_id) {
            obj2.isSelected = true;
          }
        });
      });
    }
    this.empListItems = employees;
    this.empListItemsOriginal = employees;
    this.openModal = this.showModal;
  },

  watch: {
    selectedEmpRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through empListItems
        for (const item of this.empListItems) {
          // Check if employeeIdKey is present in selRecords
          if (selRecords.includes(item[this.employeeIdKey])) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through empListItems
        for (const item of this.empListItems) {
          item.isSelected = false;
        }
      }
    },
    searchInput(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkAllSelected() {
      let selectedItems = this.empListItems.filter((el) => el.isSelected);
      this.selectAllBox = selectedItems.length === this.empListItems.length;
    },
    onApplySearch(val) {
      if (!val) {
        this.empListItems = this.empListItemsOriginal;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.employeesList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.empListItems = searchItems;
      }
    },
    onApplyFilter(filteredArray) {
      this.searchInput = "";
      if (this.isApplyFilter) {
        this.empListItems = filteredArray;
      } else {
        this.$emit("on-apply-filter");
      }
    },
    onAddEmployees() {
      this.$emit("on-select-employees", this.selectedEmployees);
    },
    onResetFilter() {
      this.searchInput = "";
      this.empListItems = this.empListItemsOriginal;
    },
    onCloseModal() {
      this.openModal = false;
      this.$emit("close-modal");
    },
  },
};
</script>

<style>
.v-selection-control__input > .v-icon {
  font-size: 18px !important;
}
</style>
