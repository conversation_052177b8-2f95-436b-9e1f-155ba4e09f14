<template>
  <v-row>
    <v-col cols="12">
      <div class="error-text font-weight-bold mb-2" style="font-size: 13px">
        {{ errorContent }}
      </div>
      <v-btn
        v-if="buttonText"
        rounded="lg"
        variant="elevated"
        color="primary"
        class="refresh-btn d-flex"
        @click="$emit('refresh-triggered')"
      >
        <v-icon class="mr-1" style="font-size: 14px"> fas fa-redo-alt </v-icon>
        <span class="font-weight-bold" style="font-size: 10px">{{
          buttonText
        }}</span>
      </v-btn>
    </v-col>
  </v-row>
</template>

<script>
export default {
  name: "ErrorContentCard",
  props: {
    // error text
    errorContent: {
      type: String,
      required: true,
    },

    buttonText: {
      type: String,
      default: "Refresh",
    },
  },
};
</script>

<style lang="css" scoped>
.error-text {
  color: #98879a;
}
@media screen and (max-width: 959px) {
  .error-text {
    text-align: center;
  }
  .main-text {
    text-align: center;
  }
  .refresh-btn {
    margin: auto;
  }
}
</style>
