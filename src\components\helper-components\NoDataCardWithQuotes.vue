<template>
  <v-row style="height: 100%; align-items: center" :style="customStyle">
    <v-col
      v-if="isShowImage"
      cols="12"
      :lg="isWrapContent ? 12 : 6"
      :md="isWrapContent ? 12 : 6"
      sm="12"
      xs="12"
      class="d-flex justify-center"
    >
      <img
        :src="getImageUrl"
        :width="isSmallCard ? smallCardImageWidth : imageWidth"
        height="auto"
      />
    </v-col>
    <v-col
      cols="12"
      :lg="isWrapContent ? 12 : 6"
      :md="isWrapContent ? 12 : 6"
      sm="12"
      xs="12"
      :class="
        windowWidth > 959 ? 'd-flex align-center justify-center pa-1' : 'pa-1'
      "
    >
      <div v-if="cardType === 'error'" class="pa-1 mt-n4">
        <ErrorContentCard
          :error-content="computedErrorContent"
          :button-text="computedButtonText"
          @refresh-triggered="$emit('refresh-triggered')"
        />
      </div>
      <div v-else class="empty-text">
        <div v-if="primaryBoldText" class="font-weight-bold text-h6">
          {{ primaryBoldText }}
        </div>
        <div class="empty-text font-weight-medium pre-formatted">
          {{ textMessage }}
        </div>
        <div v-if="secondaryBoldText" class="font-weight-bold text-h6">
          {{ secondaryBoldText }}
        </div>
        <div
          v-if="
            primaryBoldText ||
            textMessage ||
            secondaryBoldText ||
            bottomBoldText
          "
          class="empty-bar mt-2"
        />
        <div
          v-if="isAccessDenied"
          class="ml-n1 mt-2 body-2 text-error font-weight-bold d-flex align-center"
        >
          <v-icon size="18" color="error" class="mr-1"> fas fa-lock </v-icon>
          >{{ $t("authLayout.accessDeniedMessage") }}
        </div>
        <div v-if="bottomBoldText" class="font-weight-bold text-h6 mt-2">
          {{ bottomBoldText }}
        </div>
        <div
          v-if="bottomTextMessage"
          class="empty-text font-weight-medium mt-1"
        >
          {{ bottomTextMessage }}
        </div>
      </div>
    </v-col>
  </v-row>
</template>

<script>
import ErrorContentCard from "./ErrorContentCard.vue";
export default {
  name: "NoDataCardWithQuotes",
  components: { ErrorContentCard },
  props: {
    // image path in local directory
    imageName: {
      type: String,
      required: true,
    },
    imageSize: {
      type: String,
      default: "",
    },
    isShowImage: {
      type: Boolean,
      default: true,
    },
    // text content bold
    textMessage: {
      type: String,
      default: "",
    },
    // bold content present at top
    primaryBoldText: {
      type: String,
      default: "",
    },
    // bold content present at bottom
    secondaryBoldText: {
      type: String,
      default: "",
    },
    // returns error or empty
    cardType: {
      type: String,
      required: true,
    },
    // error content(used when only the cardType is error)
    errorContent: {
      type: String,
      default: "",
    },
    //bold text content below bar
    bottomBoldText: {
      type: String,
      default: "",
    },
    // text content light below bar
    bottomTextMessage: {
      type: String,
      default: "",
    },
    //to avoid image excess size in small card added new condition
    isSmallCard: {
      type: Boolean,
      default: true,
    },
    isWrapContent: {
      type: Boolean,
      default: false,
    },
    customStyle: {
      type: String,
      default: "",
    },
    buttonText: {
      type: String,
      default: "",
    },
    isAccessDenied: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    // Get error content with default translation
    computedErrorContent() {
      return this.errorContent || this.$t("dashboard.technicalDifficulties");
    },
    // Get button text with default translation
    computedButtonText() {
      return this.buttonText || this.$t("common.retry");
    },
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    // get image url
    getImageUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/" + this.imageName + ".webp");
      else return require("@/assets/images/" + this.imageName + ".png");
    },
    // width of the current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // Image size is changed based on the window size to avoid responsive issues
    imageWidth() {
      if (this.imageSize) {
        return this.imageSize;
      } else if (this.windowWidth <= 750) {
        return "100px";
      } else if (this.windowWidth > 750 && this.windowWidth < 960) {
        return "150px";
      } else if (this.windowWidth > 960 && this.windowWidth < 1264) {
        return "200px";
      } else {
        return "80%";
      }
    },

    // Small card images based on screen size
    smallCardImageWidth() {
      if (this.imageSize) {
        return this.imageSize;
      } else if (this.windowWidth <= 750) {
        return "100px";
      } else if (this.windowWidth > 750 && this.windowWidth < 960) {
        return "110px";
      } else {
        return "120px";
      }
    },
  },
};
</script>

<style lang="css" scoped>
.empty-text {
  color: #98879a;
  flex-wrap: wrap;
  flex-direction: column;
  line-height: 1.2em;
  font-size: 15px;
}
.error-text {
  color: #98879a;
}
@media screen and (max-width: 959px) {
  .error-text {
    text-align: center;
  }
  .main-text {
    text-align: center;
  }
  .refresh-btn {
    margin: auto;
  }
  .empty-text {
    text-align: center;
  }
  .empty-bar {
    margin: auto;
  }
}
.empty-bar {
  width: 50px;
  height: 10px;
  background: #98879a;
}
.pre-formatted {
  white-space: pre-line;
  line-height: 1.5rem;
}
</style>
