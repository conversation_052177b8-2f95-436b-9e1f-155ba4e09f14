<template>
  <v-card
    :color="cardProperty.background"
    class="mx-auto my-4 mr-3 ml-1 pa-3 list-action-card"
    :style="
      !isClickable
        ? 'cursor: auto;'
        : 'cursor: pointer;' + (cardProperty.style || '')
    "
    min-height="50"
    @click="isClickable ? $emit('action-triggered', listIndex) : {}"
  >
    <v-list-item class="d-flex align-center w-100">
      <template #prepend>
        <div class="d-flex align-center list-action-avatar mr-3">
          <!-- In order to customize avatar Content -->
          <slot name="avatarContent" />
          <v-avatar
            v-if="iconName"
            :size="avatarSize"
            :color="cardProperty.bg"
            style="border-radius: 15px"
          >
            <v-icon :color="cardProperty.color" style="font-size: 20px">
              {{ iconName }}
            </v-icon>
          </v-avatar>
        </div>
      </template>

      <div class="flex-grow-1">
        <v-list-item-title
          class="text-primary font-weight-bold text-body-2 mb-1"
        >
          <div
            :class="
              calledFrom === 'notification'
                ? 'notification-class text-caption font-weight-bold'
                : ''
            "
          >
            {{ title }}
          </div>
        </v-list-item-title>

        <v-list-item-subtitle>
          <div class="text-body-2">
            <span v-if="subTitleBold" class="text-primary font-weight-bold">{{
              subTitleBold
            }}</span>
            <span v-if="subTitleText">
              {{
                subTitleBold && subTitleText
                  ? " - " + subTitleText
                  : subTitleText
              }}
            </span>
            <!-- In order to customize Subtitle Content -->
            <slot name="subTitleContent" />
          </div>
        </v-list-item-subtitle>

        <div class="my-1"><slot name="buttonAction" /></div>
      </div>

      <template v-if="windowWidth > 500" #append>
        <div class="d-flex align-center justify-end ml-auto">
          <div :class="actionTextClass" class="text-body-2 mr-2">
            {{ actionText }}
          </div>
          <!-- if more customization is required we can use slot -->
          <div class="d-flex align-center justify-end">
            <slot name="actionContent" />
          </div>
        </div>
      </template>
    </v-list-item>
    <div
      v-if="windowWidth <= 500"
      class="d-flex justify-end align-center px-4 pb-2"
    >
      <div :class="actionTextClass" class="text-body-2 mr-2">
        {{ actionText }}
      </div>
      <!-- if more customization is required we can use slot -->
      <div class="d-flex align-center justify-end">
        <slot name="actionContent" />
      </div>
    </div>
  </v-card>
</template>

<script>
export default {
  name: "PresentActionCards",
  props: {
    // returns background(card), bg(avatar-background), icon(icon-color)
    cardProperty: {
      type: Object,
      default: function () {
        return {
          background: "#F9FBFC",
          bg: "green-lighten-4",
          color: "white",
        };
      },
    },
    // return icon name to show in left side of card
    iconName: {
      type: String,
      default: "",
    },
    // title of the card
    title: {
      type: String,
      default: "",
    },
    // subtitle has two portion. This is bold one
    subTitleBold: {
      type: String,
      default: "",
    },
    // subtitle has two portion. This is light one
    subTitleText: {
      type: String,
      default: "",
    },
    // right side of the card(no action is required)
    actionText: {
      type: String,
      default: "",
    },
    // color of the action text
    actionTextClass: {
      type: String,
      default: "",
    },
    // unique key and also this value is emitted when the card clicked to find which one is clicked
    listIndex: {
      type: Number,
      required: true,
    },
    // used to check the card is clickable or not
    isClickable: {
      type: Boolean,
      default: false,
    },
    // for notification, we show the text without truncating. So this is returned only from notifications
    calledFrom: {
      type: String,
      default: "",
    },
  },

  computed: {
    // width of current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // change the avatar size based on window size to avoid responsive issues
    avatarSize() {
      if (this.windowWidth <= 600) {
        return "40";
      } else if (this.windowWidth <= 400) {
        return "30";
      } else {
        return "45";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
/* Deep styles for Vuetify component customization */
:deep(.v-list-item__append) {
  margin-left: auto !important;
}

:deep(.v-list-item__content) {
  flex: 1 1 auto;
}

:deep(.v-list-item) {
  width: 100%;
  display: flex !important;
}
.list-action-card:hover {
  background: rgb(var(--v-theme-hover)) !important;
}
.list-action-card {
  border-radius: 12px !important;
  box-shadow: 0px 0px 8px rgba(141, 181, 220, 0.46) !important;
}
.list-action-avatar {
  min-width: fit-content;
}
.notification-class {
  white-space: initial;
  text-align: center;
}
</style>
