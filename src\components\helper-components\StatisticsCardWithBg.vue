<template>
  <div>
    <v-card
      class="statistics-card-bg"
      :class="isClickable ? '' : 'default-cursor'"
      :color="cardBgColor"
      @click="isClickable ? $emit('card-click-action') : {}"
    >
      <div class="d-flex align-center py-3 pl-0 pr-1" style="overflow: hidden">
        <div class="ml-n2 mr-2 d-flex align-center">
          <v-icon v-if="iconName" :size="iconSize" :color="iconColor">
            {{ iconName }}
          </v-icon>
          <img
            v-else-if="imageName"
            height="auto"
            :width="imageWidth"
            :src="getAtWorkImageUrl"
            alt="statistics_image"
          />
        </div>
        <div v-if="!isError" class="d-flex flex-column">
          <span class="font-weight-bold flex-start">
            {{ title }}
          </span>
          <span class="font-weight-regular">
            {{ subtitle }}
          </span>
        </div>
        <div v-else class="d-flex align-center">
          <div
            class="text-primary font-weight-bold mr-3"
            style="font-size: 1.2em"
          >
            Oops!
          </div>
          <v-btn
            size="small"
            variant="elevated"
            color="primary"
            rounded="lg"
            @click="$emit('refresh-action')"
          >
            <v-icon class="mr-1"> fas fa-redo-alt </v-icon>
            <span class="font-weight-bold">Refresh</span>
          </v-btn>
        </div>
      </div>
    </v-card>
  </div>
</template>

<script>
export default {
  name: "StatisticsCardWithBg",
  props: {
    isError: {
      type: Boolean,
      default: false,
    },
    title: {
      type: [Number, String],
      default: "-",
    },
    subtitle: {
      type: String,
      default: "-",
    },
    iconName: {
      type: String,
      default: "",
    },
    imageName: {
      type: String,
      default: "",
    },
    cardBgColor: {
      type: String,
      default: "light-blue-lighten-4",
    },
    imageWidth: {
      type: String,
      default: "40",
    },
    isClickable: {
      type: Boolean,
      default: false,
    },
    iconSize: {
      type: String,
      default: "",
    },
  },
  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getAtWorkImageUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/" + this.imageName + ".webp");
      else return require("@/assets/images/" + this.imageName + ".png");
    },
    iconColor() {
      if (this.cardBgColor) {
        // Extract base color from Vuetify 3 color palette syntax
        // Examples:
        // "light-green-lighten-5" -> "light-green"
        // "blue-grey-lighten-5" -> "blue-grey"
        // "green-accent-1" -> "green"
        // "red-lighten-5" -> "red"

        let colorParts = this.cardBgColor.split("-");

        // Handle compound colors like "light-green", "blue-grey"
        if (colorParts.length >= 3) {
          // Check if it's a compound color (light-green, blue-grey, etc.)
          if (
            colorParts[2] === "lighten" ||
            colorParts[2] === "darken" ||
            colorParts[2] === "accent"
          ) {
            return colorParts[0] + "-" + colorParts[1];
          }
        }

        // Handle simple colors like "red-lighten-5", "green-accent-1"
        if (colorParts.length >= 2) {
          if (
            colorParts[1] === "lighten" ||
            colorParts[1] === "darken" ||
            colorParts[1] === "accent"
          ) {
            return colorParts[0];
          }
        }

        // Fallback: return the first part
        return colorParts[0] || "";
      }
      return "";
    },
  },
};
</script>

<style lang="css" scoped>
.statistics-card-bg {
  border-radius: 8px !important;
}
.default-cursor {
  cursor: default;
}
</style>
