import { Workbook } from "exceljs";
import * as fs from "file-saver";
export function useExcelExport() {
  const singleSheetExportToExcel = async (
    fileData,
    headers,
    filename = "Excel Export",
    sheetname = "Sheet 1"
  ) => {
    try {
      // Input validation
      if (!Array.isArray(fileData)) {
        throw new Error("fileData must be an array");
      }

      if (!Array.isArray(headers) || headers.length === 0) {
        throw new Error("headers must be a non-empty array");
      }

      // Create workbook and worksheet
      const workbook = new Workbook();
      const worksheet = workbook.addWorksheet(sheetname);

      worksheet.columns = headers;
      worksheet.getRow(1).eachCell((cell) => {
        cell.fill = {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: "92CDDC" },
        };
      });

      // Add data rows if available
      if (fileData && fileData.length > 0) {
        worksheet.addRows(fileData);
      }

      // Generate and download the Excel file
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
      });

      fs.saveAs(blob, filename);
    } catch (error) {
      console.error("Excel export error:", error);
      throw new Error(`Failed to export Excel file: ${error.message}`);
    }
  };

  return {
    singleSheetExportToExcel,
  };
}
