import moment from "moment";
import { useStore } from "vuex";
export function useFormatData() {
  // Get the store instance
  const store = useStore();
  const formatDate = (date, withTime = false, isUTC = false) => {
    if (date && moment(date).isValid()) {
      let dateObj = moment(date);
      let orgDateFormat = store.state.orgDetails.orgDateFormat;
      if (isUTC) {
        dateObj = moment(
          convertUTCToLocal(dateObj.format("YYYY-MM-DD HH:mm:ss"))
        );
      }
      if (withTime) {
        orgDateFormat = orgDateFormat + " HH:mm:ss";
      }
      return dateObj.format(orgDateFormat) || "";
    }
  };
  const convertUTCToLocal = (date) => {
    if (date && moment(date).isValid()) {
      return moment.utc(date).local().format("YYYY-MM-DD HH:mm:ss");
    }
  };
  const checkNullValue = (value) => {
    const invalidValues = [
      null,
      undefined,
      "",
      "none",
      "None",
      false,
      NaN,
      [],
      {},
    ];
    if (invalidValues.includes(value)) return "-";
    return value;
  };
  return { formatDate, convertUTCToLocal, checkNullValue };
}
