import i18n from "@/translations";
const {
  validateWithRulesAndReturnMessages,
} = require("@cksiva09/validationlib/src/validator");

const t = i18n?.global?.t;
export function useValidation() {
  const required = (field, value) => {
    return !!value || t("validations.required", { fieldName: field });
  };
  const twoDecimalPrecisionValidation = (value) => {
    if (/^\d+(\.\d{1,2})?$/.test(value)) {
      return true;
    }
    return t("validations.twoDecimalValidation");
  };
  const noDecimalValidation = (value) => {
    if (/^\d+$/.test(value)) {
      return true;
    }
    return t("validations.noDecimalValidation");
  };
  const minMaxStringValidation = (field, value, minValue, maxValue) => {
    if (!value) {
      return true;
    }
    if (value.length < minValue) {
      return `The ${field} should have minimum ${minValue} characters`;
    }
    if (value.length > maxValue) {
      return `The ${field} must not exceed ${maxValue} characters`;
    }
    return true;
  };
  const numberSpaceCommaValidation = (field, value) => {
    let msg = `Only numbers, comma and spaces are allowed`;
    return /^[0-9\ \,]+$/.test(value) || msg;
  };
  const alphabetsSpaceValidation = (field, value) => {
    let msg = `Only alphabets and spaces are allowed`;
    return /^[a-zA-Z\ ]+$/.test(value) || msg;
  };
  const alphaSpaceDotValidation = (field, value) => {
    let msg = `Only alphabets, spaces and dot are allowed`;
    return /^[a-zA-Z\ \.]+$/.test(value) || msg;
  };
  const alphaNumValidation = (field, value) => {
    let msg = `Only alphabets and numbers are allowed`;
    return /^[a-zA-Z0-9]+$/.test(value) || msg;
  };
  const minMaxNumberValidation = (field, value, minValue, maxValue) => {
    if (!value) {
      return true;
    }
    if (value < minValue) {
      return `Please enter a value greater than or equal to ${minValue}.`;
    }
    if (value > maxValue) {
      return `Please enter a value less than or equal to ${maxValue}.`;
    }
    return true;
  };

  return {
    required,
    minMaxStringValidation,
    numberSpaceCommaValidation,
    alphabetsSpaceValidation,
    alphaSpaceDotValidation,
    alphaNumValidation,
    minMaxNumberValidation,
    twoDecimalPrecisionValidation,
    noDecimalValidation,
    validateWithRulesAndReturnMessages,
  };
}
