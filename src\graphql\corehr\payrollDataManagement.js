import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_AIR_TICKET_SETTINGS = gql`
  query retrieveAirTicketSettings($formId: Int!) {
    retrieveAirTicketSettings(formId: $formId) {
      errorCode
      message
      airTicketSettingData {
        Air_Ticket_Setting_Id
        Destination_City
        Destination_Country
        Air_Ticketing_Category
        Status
        Infant_Amount
        Child_Amount
        Adult_Amount
        Added_On
        Added_By
        Updated_On
        Updated_By
      }
    }
  }
`;
export const RETRIEVE_EMP_AIR_TICKET_POLICY = gql`
  query retrieveEmpAirTicketPolicy(
    $airTicketSettingId: Int
    $employeeId: Int!
    $formId: Int!
  ) {
    retrieveEmpAirTicketPolicy(
      airTicketSettingId: $airTicketSettingId
      employeeId: $employeeId
      formId: $formId
    ) {
      errorCode
      message
      data {
        Employee_Id
        Air_Ticket_Setting_Id
        Status
        Effective_Date_Enable
        Effective_Date
        Last_Availed_Date
        Eligibility_Of_Ticket_Claim_Months
        Air_Ticket_To_Dependent
        Dependent_Relationship
        Infant_Count
        Child_Count
        Adult_Count
        Infant_Amount
        Child_Amount
        Adult_Amount
        Added_On
        Updated_On
        Destination_City
        Destination_Country
        Air_Ticketing_Category
        Configuration_Status
        Added_By
        Updated_By
        No_Of_Dependents
      }
    }
  }
`;
export const RETRIEVE_AIR_TICKET_SETTLEMENT_SUMMARY = gql`
  query RetrieveAirTicketSettlementSummary(
    $formId: Int!
    $employeeId: Int
    $month: Int
    $year: Int
  ) {
    retrieveAirTicketSettlementSummary(
      formId: $formId
      employeeId: $employeeId
      month: $month
      year: $year
    ) {
      errorCode
      message
      settlementSummary {
        Employee_Id
        User_Defined_EmpId
        Employee_Name
        Air_Ticket_Settlement_Summary_Id
        Destination_City
        Destination_Country
        Infant_Dependents_Ticket
        Child_Dependents_Ticket
        Adult_Dependents_Ticket
        Infant_Accrued_Tickets
        Child_Accrued_Tickets
        Adult_Accrued_Tickets
        Total_Accrued_Tickets
        Infant_Accrued_Amount
        Child_Accrued_Amount
        Adult_Accrued_Amount
        Air_Ticketing_Category
        Effective_Date_Enable
        Effective_Date
        Availed_Date
        Eligibility_Of_Ticket_Claim_Months
        Air_Ticket_To_Dependent
        Dependent_Relationship
        No_Of_Dependents
        Infant_Policy_Amount
        Child_Policy_Amount
        Adult_Policy_Amount
        Settlement_Amount
        Settlement_Status
        Payroll_Month
      }
    }
  }
`;
export const RETRIEVE_CURRENCY_DROPDOWN = gql`
  query retrieveCurrencyDropdown($formId: Int!, $filtered: Boolean!) {
    retrieveCurrencyDropdown(formId: $formId, filtered: $filtered) {
      errorCode
      message
      currencyList {
        currencyId
        currencyName
        currencyCode
      }
    }
  }
`;
export const LIST_CURRENCY_CONVERSION = gql`
  query listCurrencyConversion($formId: Int!) {
    listCurrencyConversion(formId: $formId) {
      errorCode
      message
      data {
        conversionId
        claimCurrencyId
        claimCurrencyCode
        claimCurrencyName
        payrollCurrencyCode
        payrollCurrencyName
        conversionType
        conversionValue
        status
        addedOn
        updatedOn
        addedByName
        updatedByName
      }
    }
  }
`;
export const LIST_PER_DIEM_CONFIGURATION = gql`
  query listPerDiemConfiguration($formId: Int) {
    listPerDiemConfiguration(formId: $formId) {
      errorCode
      message
      perDiemConfigurations {
        Per_Diem_Config_Id
        Type_Of_Configuration
        Country_Code
        Country_Name
        Travel_Date
        Per_Diem_Title
        Description
        Per_Diem_Rate
        Conversion_Id
        Expense_Id
        Expense_Title
        Currency_Code
        Currency_Name
        Status
        Added_On
        Updated_On
        Added_By_Name
        Updated_By_Name
      }
    }
  }
`;
// ===============
//Mutations
// ===============
export const ADD_UPDATE_PER_DIEM_CONFIGURATION = gql`
  mutation addUpdatePerDiemConfiguration(
    $formId: Int
    $Per_Diem_Config_Id: Int
    $Type_Of_Configuration: String!
    $Country_Code: String
    $Travel_Date: Travel_Date!
    $Per_Diem_Title: String!
    $Description: String
    $Per_Diem_Rate: Float!
    $Conversion_Id: Int!
    $Expense_Id: Int!
    $Status: String!
  ) {
    addUpdatePerDiemConfiguration(
      formId: $formId
      Per_Diem_Config_Id: $Per_Diem_Config_Id
      Type_Of_Configuration: $Type_Of_Configuration
      Country_Code: $Country_Code
      Travel_Date: $Travel_Date
      Per_Diem_Title: $Per_Diem_Title
      Description: $Description
      Per_Diem_Rate: $Per_Diem_Rate
      Conversion_Id: $Conversion_Id
      Expense_Id: $Expense_Id
      Status: $Status
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_AIR_TICKET_SETTINGS = gql`
  mutation addUpdateAirTicketSetting(
    $formId: Int!
    $airTicketSettingId: Int
    $city: String!
    $country: String!
    $airTicketingCategory: String!
    $status: String!
    $infantAmount: Float
    $childAmount: Float
    $adultAmount: Float!
  ) {
    addUpdateAirTicketSetting(
      airTicketSettingId: $airTicketSettingId
      city: $city
      country: $country
      airTicketingCategory: $airTicketingCategory
      status: $status
      formId: $formId
      infantAmount: $infantAmount
      childAmount: $childAmount
      adultAmount: $adultAmount
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_EMP_AIR_TICKET_POLICY = gql`
  mutation addUpdateEmpAirTicketPolicy(
    $employeeId: Int!
    $airTicketSettingId: Int!
    $status: String!
    $effectiveDateEnable: String!
    $effectiveDate: String!
    $lastAvailedDate: String
    $eligibilityOfTicketClaimMonths: Int!
    $airTicketToDependent: String!
    $dependentRelationship: [String]
    $action: String!
    $formId: Int!
    $formStatus: Int!
  ) {
    addUpdateEmpAirTicketPolicy(
      employeeId: $employeeId
      airTicketSettingId: $airTicketSettingId
      status: $status
      effectiveDateEnable: $effectiveDateEnable
      effectiveDate: $effectiveDate
      lastAvailedDate: $lastAvailedDate
      eligibilityOfTicketClaimMonths: $eligibilityOfTicketClaimMonths
      airTicketToDependent: $airTicketToDependent
      dependentRelationship: $dependentRelationship
      action: $action
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_CURRENCY_CONVERSION = gql`
  mutation addUpdateCurrencyConversion(
    $conversionId: Int
    $claimCurrencyId: Int!
    $conversionType: String!
    $conversionValue: Float!
    $status: String
    $formId: Int!
  ) {
    addUpdateCurrencyConversion(
      conversionId: $conversionId
      claimCurrencyId: $claimCurrencyId
      conversionType: $conversionType
      conversionValue: $conversionValue
      status: $status
      formId: $formId
    ) {
      errorCode
      message
    }
  }
`;
