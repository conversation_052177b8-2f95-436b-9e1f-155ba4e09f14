import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const RETRIEVE_SALARY_LIST = gql`
  query ViewSalaryTemplate(
    $formId: Int!
    $accessFormId: Int
    $templateId: Int
    $id: Int
    $isViewMode: Boolean
    $employeeId: Int
    $isDropdown: Boolean
    $includeHistoricalRecords: Boolean
  ) {
    listSalaryTemplateDetails(
      formId: $formId
      accessFormId: $accessFormId
      templateId: $templateId
      id: $id
      isViewMode: $isViewMode
      employeeId: $employeeId
      isDropdown: $isDropdown
      includeHistoricalRecords: $includeHistoricalRecords
    ) {
      errorCode
      message
      currencySymbol
      templateDetails
    }
  }
`;

export const RETRIEVE_SYSTEM_CALCULATED_COMPONENTS = gql`
  query CalculateSalary(
    $employeeId: Int!
    $retiralDetails: String!
    $allowanceDetails: String!
    $salaryDetails: String!
    $providentFundConfigurationValue: String!
  ) {
    calculateSalary(
      employeeId: $employeeId
      retiralDetails: $retiralDetails
      allowanceDetails: $allowanceDetails
      salaryDetails: $salaryDetails
      providentFundConfigurationValue: $providentFundConfigurationValue
    ) {
      errorCode
      message
      salaryStructure
      employeeRetiralDetails
    }
  }
`;
export const RETRIEVE_SALARY_COMPONENTS = gql`
  query {
    listSalaryComponents {
      errorCode
      message
      salaryComponents
    }
  }
`;

export const RETRIEVE_LOCK_DATE = gql`
  query {
    getFBPDeclarationSettings {
      errorCode
      message
      success
      lockStatus {
        status
        lockDate
      }
    }
  }
`;

// ===============
// Mutations
// ===============

export const ADD_UPDATE_SALARY_DETAILS = gql`
  mutation AddUpdateSalaryDetails(
    # Common parameters
    $formId: Int!
    $isEditMode: Boolean
    $id: Int
    $annualCTC: String
    $allowance: [allowanceInput]
    $templateName: String
    $description: String
    $templateStatus: String
    $employeeId: Int
    $templateId: Int
    $effectiveFrom: String
    $effectiveTo: String
    $annualGrossSalary: String
    $monthlyGrossSalary: String
    $salaryEffectiveMonth: String
    $retirals: [retiralsInput]
    $payoutMonth: String
    $revisionType: String
    $revisionStatus: String
    $reviseCtcByPercentage: String
    $previousCtc: String
    $accessFormId: Int!
  ) {
    addUpdateSalaryDetails(
      formId: $formId
      isEditMode: $isEditMode
      id: $id
      annualCTC: $annualCTC
      allowance: $allowance
      templateName: $templateName
      description: $description
      templateStatus: $templateStatus
      employeeId: $employeeId
      templateId: $templateId
      effectiveFrom: $effectiveFrom
      effectiveTo: $effectiveTo
      annualGrossSalary: $annualGrossSalary
      monthlyGrossSalary: $monthlyGrossSalary
      salaryEffectiveMonth: $salaryEffectiveMonth
      retirals: $retirals
      payoutMonth: $payoutMonth
      revisionType: $revisionType
      revisionStatus: $revisionStatus
      reviseCtcByPercentage: $reviseCtcByPercentage
      previousCtc: $previousCtc
      accessFormId: $accessFormId
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_SALARY_DETAILS = gql`
  mutation DeleteSalaryRecord($id: Int, $formId: Int!, $employeeId: Int) {
    deleteSalaryRecord(id: $id, formId: $formId, employeeId: $employeeId) {
      errorCode
      message
    }
  }
`;
