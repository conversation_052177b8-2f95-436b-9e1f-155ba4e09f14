import gql from "graphql-tag";

export const LIST_EMPLOYEE_MANAGERS = gql`
  query listManagerDetails {
    listManagerDetails {
      errorCode
      message
      managerData {
        employee_name
        employee_id
        user_defined_empid
        photo_path
        emp_email
      }
    }
  }
`;

export const LIST_MY_TEAM_ACTIVITY_DETAILS = gql`
  query listMyTeamActivityDetails(
    $date: Date!
    $designationId: [Int]
    $departmentId: [Int]
    $empTypeId: [Int]
    $locationId: [Int]
    $timeZone: String!
    $subscribedPlan: String
    $workScheduleId: Int!
  ) {
    listMyTeamActivityDetails(
      date: $date
      designationId: $designationId
      departmentId: $departmentId
      empTypeId: $empTypeId
      locationId: $locationId
      timeZone: $timeZone
      subscribedPlan: $subscribedPlan
      workScheduleId: $workScheduleId
    ) {
      errorCode
      message
      considerNotActiveIdleTime
      fixedDailyWorkHours
      hasSummary
      myTeamDetails {
        employeeId
        userDefinedEmployeeId
        employeeName
        employeeEmail
        managerId
        memberRole
        memberId
        assetId
        checkInTime
        checkOutTime
        trackingStatus
        systemUpTime
        userProductivityPercentage
        userActiveTime
        notActiveAndIdleTime
        computerActivityTime
        systemProductivityPercentage
      }
    }
  }
`;

export const LIST_MY_TEAM_APP_URL_PRODUCTIVITY = gql`
  query listMyTeamAppUrlProductivity(
    $date: Date!
    $employeeId: [Int]!
    $timeZone: String!
    $workScheduleId: Int!
  ) {
    listMyTeamAppUrlProductivity(
      date: $date
      employeeId: $employeeId
      timeZone: $timeZone
      workScheduleId: $workScheduleId
    ) {
      errorCode
      message
      hasSummary
      teamAppUrlProductivityDetails {
        employeeId
        productiveTime
        unProductiveTime
        neutralTime
      }
    }
  }
`;

export const LIST_MY_ACTIVITY_APPS = gql`
  query listMyActivityApps(
    $employeeId: Int!
    $date: Date!
    $callFromMyTeamActivityForm: Int
    $timeZone: String!
    $workScheduleId: Int!
    $formId: Int!
    $tab: String
  ) {
    listMyActivityApps(
      employeeId: $employeeId
      date: $date
      callFromMyTeamActivityForm: $callFromMyTeamActivityForm
      timeZone: $timeZone
      workScheduleId: $workScheduleId
      formId: $formId
      tab: $tab
    ) {
      errorCode
      message
      appDetails
    }
  }
`;
export const LIST_EMPLOYEES = gql`
  query listEmployees(
    $isCallFromReportForm: Int!
    $designation: [Int]
    $department: [Int]
    $employeeType: [Int]
    $location: [Int]
    $subscribedPlan: String!
    $workScheduleId: Int
  ) {
    listEmployees(
      isCallFromReportForm: $isCallFromReportForm
      designation: $designation
      department: $department
      employeeType: $employeeType
      location: $location
      subscribedPlan: $subscribedPlan
      workScheduleId: $workScheduleId
    ) {
      errorCode
      message
      listEmployees
    }
  }
`;

export const GET_EMPLOYEE_WORK_SCHEDULE_DETAILS = gql`
  query getEmployeeWorkScheduleDetails($employeeId: Int!) {
    getEmployeeWorkScheduleDetails(employeeId: $employeeId) {
      errorCode
      message
      employeeWorkScheduleDetails {
        workScheduleId
        workSchedule
        shiftMarginStartDateTime
        shiftMarginEndDateTime
        timeZoneId
        timeZone
      }
    }
  }
`;
export const GET_TEAM_MEMBERS = gql`
  query getTeamMembers($formName: String, $formId: Int) {
    getTeamMembers(formName: $formName, formId: $formId) {
      errorCode
      message
      teamMembers
    }
  }
`;
