import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const RETRIEVE_DROPDOWN_DATA = gql`
  query ($status: String, $formId: Int) {
    getDropDownBoxDetails(status: $status, formId: $formId) {
      errorCode
      message
      fieldForce
      departments {
        Department_Id
        Department_Name
        Department_Code
      }
      locations {
        Location_Id
        Location_Name
        Currency_Id
      }
      workSchedules {
        WorkSchedule_Id
        Title
      }
      roles {
        Roles_Id
        Roles_Name
      }
      employeeType {
        EmpType_Id
        Employee_Type
      }
      serviceProvider {
        Service_Provider_Id
        Service_Provider_Name
      }
      state {
        State_Id
        State_Name
      }
      managers {
        Manager_Id
        Manager_Name
        Manager_User_Defined_EmpId
      }
      skills {
        SkillDefinition_Id
        Title
      }
      clients {
        Client_Id
        Company_Name
      }
      currency {
        Currency_Id
        Currency_Name
      }
      rounds {
        Round_Id
        Round_Name
      }
      workAuthorizations {
        Work_Authorization_Id
        Work_Authorization_Name
      }
      status {
        Id
        Status
      }
    }
  }
`;

export const LIST_LEAVE_TYPES = gql`
  query listLeaveTypes {
    listLeaveTypes {
      errorCode
      message
      leaveTypeDetails {
        leaveTypeId
        leaveName
        status
      }
    }
  }
`;

export const RETRIEVE_DESIGNATION_LIST = gql`
  query getDesignationDetails(
    $status: String
    $searchString: String
    $offset: Int
    $limit: Int
  ) {
    getDesignationDetails(
      status: $status
      searchString: $searchString
      offset: $offset
      limit: $limit
    ) {
      errorCode
      message
      totalRecords
      designationResult {
        Designation_Id
        Designation_Name
        Designation_Code
      }
    }
  }
`;

export const LIST_CUSTOM_GROUPS = gql`
  query listCustomEmployeeGroups($formName: String!, $preApprovalId: Int) {
    listCustomEmployeeGroups(
      formName: $formName
      preApprovalId: $preApprovalId
    ) {
      errorCode
      success
      message
      customGroups {
        Custom_Group_Id
        Custom_Group_Name
      }
    }
  }
`;

export const LIST_WORKFLOW_DETAILS = gql`
  query listWorkflowDetails($formId: Int, $formName: String!) {
    listWorkflowDetails(formId: $formId, formName: $formName) {
      errorCode
      message
      workflowDetails {
        Workflow_Id
        Workflow_Name
        Form_Id
      }
    }
  }
`;

export const LIST_LANGUAGE = gql`
  query listLanguages {
    listLanguages {
      errorCode
      message
      languages {
        Lang_Id
        Language_Name
      }
    }
  }
`;

export const LIST_COUNTRY = gql`
  query listCountries {
    listCountries {
      errorCode
      message
      countries {
        Country_Code
        Country_Name
      }
    }
  }
`;

export const LIST_EMP_PROFESSION = gql`
  query listEmpProfession {
    listEmpProfession {
      errorCode
      message
      professions {
        Profession_Id
        Profession_Name
      }
    }
  }
`;

export const LIST_COURSE = gql`
  query listCourseDetails {
    listCourseDetails {
      errorCode
      message
      courseDetails {
        Course_Id
        Course_Name
        Document_Sub_Type_Id
      }
    }
  }
`;

export const LIST_SUB_DOC_TYPE = gql`
  query listDocumentSubType {
    listDocumentSubType {
      errorCode
      message
      documentSubType {
        Document_Sub_Type_Id
        Document_Type_Id
        Document_Sub_Type
        Mandatory
        Instruction
      }
    }
  }
`;

export const LIST_DOC_CATEGORY = gql`
  query listDocumentCategory {
    listDocumentCategory {
      errorCode
      message
      documentCategory {
        Category_Id
        Category_Fields
        Vendor_Based
      }
    }
  }
`;

export const LIST_DOC_TYPE = gql`
  query listDocumentType {
    listDocumentType {
      errorCode
      message
      documentType {
        Document_Type_Id
        Document_Type
        Category_Id
      }
    }
  }
`;

export const LIST_ACCREDITATIONS_AND_TYPES = gql`
  query retrieveAccreditationCategoryAndType {
    retrieveAccreditationCategoryAndType {
      errorCode
      message
      accreditationCategoryAndType {
        Accreditation_Category_And_Type_Id
        Accreditation_Category
        Accreditation_Type
      }
    }
  }
`;

export const LIST_ACCOUNT_TYPE = gql`
  query listAccountType {
    listAccountType {
      errorCode
      message
      accountType {
        Account_Type_Id
        Account_Type
      }
    }
  }
`;

export const LIST_BANK = gql`
  query listBankDetails {
    listBankDetails {
      errorCode
      message
      bankDetails {
        Bank_Id
        Bank_Name
      }
    }
  }
`;

export const LIST_INSURANCE = gql`
  query listInsuranceType(
    $employeeId: Int!
    $type: String!
    $screenType: String
  ) {
    listInsuranceType(
      employeeId: $employeeId
      type: $type
      screenType: $screenType
    ) {
      errorCode
      message
      insuranceType
    }
  }
`;

export const LIST_BUSINESS_UNIT = gql`
  query listBusinessUnitInDropdown($action: String!) {
    listBusinessUnitInDropdown(action: $action) {
      errorCode
      message
      settings {
        businessUnitId
        businessUnit
      }
    }
  }
`;

export const RETRIEVE_ACCREDITATION_TYPE = gql`
  query retrieveAccreditationAndType($urlHash: String!) {
    retrieveAccreditationAndType(urlHash: $urlHash) {
      errorCode
      message
      accreditationAndType {
        accreditationCategoryAndTypeId
        accreditationCategory
        accreditationType
      }
    }
  }
`;

export const RETRIEVE_PROJECT_MAPPED_ACCREDITATION_DETAILS = gql`
  query retrieveProjectAccreditationCategoryDetails($formName: String) {
    retrieveProjectAccreditationCategoryDetails(formName: $formName) {
      errorCode
      message
      projectMappingDetails {
        Project_Id
        Project_Name
        Accreditation_Category_And_Type_Id
        Accreditation_Category
        Accreditation_Type
        Status
      }
    }
  }
`;

export const LIST_MARITAL_STATUS = gql`
  query retrieveMaritalStatus($retrieveRelationship: Int) {
    retrieveMaritalStatus(retrieveRelationship: $retrieveRelationship) {
      errorCode
      message
      maritalStatus
    }
  }
`;

export const LIST_RELIGIONS = gql`
  query retrieveReligionList($Org_Code: String!) {
    retrieveReligionList(Org_Code: $Org_Code) {
      religionData {
        religionId
        religionCode
        religion
      }
    }
  }
`;

export const LIST_NATIONALITIES = gql`
  query retrieveNationalityList($Org_Code: String!) {
    retrieveNationalityList(Org_Code: $Org_Code) {
      nationalityData {
        nationalityId
        nationalityCode
        nationality
      }
    }
  }
`;

export const LIST_SPECIALIZATION_INSTITUTE = gql`
  query retrieveListEduInstitutionAndSpecialization {
    retrieveListEduInstitutionAndSpecialization {
      errorCode
      message
      institution {
        Institution_Id
        Institution_Code
        Institution
      }
      specialization {
        Specialization_Id
        Specialization_Code
        Specialization
      }
    }
  }
`;

export const LIST_CITIES = gql`
  query getCityListWithState($Form_Id: Int!) {
    getCityListWithState(Form_Id: $Form_Id) {
      errorCode
      message
      cityDetails {
        City_Id
        City_Name
        Country_Code
        cityStateDetails
      }
    }
  }
`;

export const LIST_CITIES_NO_AUTH = gql`
  query getCityListWithState {
    getCityListWithState {
      errorCode
      message
      cityDetails {
        City_Id
        City_Name
        cityStateDetails
        Country_Code
        Locale
        Country_Name
        State_Name
        State_Id
      }
    }
  }
`;

export const LIST_BARANGAY = gql`
  query getBarangayListWithCity($searchString: String!, $cityId: Int) {
    getBarangayListWithCity(searchString: $searchString, cityId: $cityId) {
      errorCode
      message
      barangayDetails {
        Barangay_Id
        Barangay_Name
        Barangay_Code
        City_Id
        City_Name
        State_Id
        State_Name
        Region_Id
        Region_Name
        Region_Code
        Country_Name
        Country_Code
        barangayDetails
      }
    }
  }
`;

export const MARITAL_STATUS_LIST = gql`
  query MartialStatusQuery($Org_Code: String!, $Url_Hash: String!) {
    listMartialStatus(Org_Code: $Org_Code, Url_Hash: $Url_Hash) {
      marital_status {
        Marital_Status_Id
        Marital_Status
        __typename
      }
    }
    __typename
  }
`;

export const GENDER_LIST = gql`
  query retrieveGenderList($Org_Code: String!) {
    retrieveGenderList(Org_Code: $Org_Code) {
      genderData {
        genderId
        gender
      }
    }
  }
`;

export const TAX_CODE_LIST = gql`
  query listTimekeepingCareerDetail {
    listTimekeepingCareerDetail {
      errorCode
      message
      timeKeeping {
        Timekeeping_Id
        Timekeeping_Name
      }
      career {
        Career_Id
        Career_Name
      }
      taxDetail {
        Tax_Code
        Tax_Description
        Exemption_Amount
      }
    }
  }
`;

export const GROUP_LIST_IN_JOB_POST = gql`
  query jobTitleList($Form_Id: Int!, $conditions: [commonApplyCondition]) {
    jobTitleList(Form_Id: $Form_Id, conditions: $conditions) {
      errorCode
      message
      jobTitleResult {
        Organization_Structure_Id
        Pos_Name
        Pos_Code
        Originalpos_Id
      }
    }
  }
`;
