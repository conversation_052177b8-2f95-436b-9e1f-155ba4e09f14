import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const LIST_MY_TEAM_EMPLOYEES = gql`
  query listMyTeam($offset: Int, $limit: Int) {
    listMyTeam(offset: $offset, limit: $limit) {
      errorCode
      message
      listMyTeam
    }
  }
`;
export const RETRIEVE_TOTAL_EMP_COUNT = gql`
  query retrieveEmployeeCount {
    retrieveEmployeeCount {
      errorCode
      message
      activeCount
      inactiveCount
      totalCount
    }
  }
`;
export const RETRIEVE_EMP_PROFILE_CARD_DETAILS = gql`
  query retrieveMyProfile($employeeId: Int!) {
    retrieveMyProfile(employeeId: $employeeId) {
      errorCode
      message
      employeeProfile {
        employeeName
        empEmail
        userDefinedEmpId
        employeeId
        designationId
        designationName
        departmentId
        departmentName
        empStatus
        mobileNo
        mobileNoCountryCode
        street
        city
        apartmentName
        state
        pinCode
        country
        photoPath
        useLocationAddress
        locationStreet1
        locationStreet2
        locationCity
        locationState
        locationPinCode
        locationCountry
        invitationStatus
      }
    }
  }
`;
export const RETRIEVE_EMP_PERSONAL_INFO = gql`
  query retrievePersonalInfo($employeeId: Int!) {
    retrievePersonalInfo(employeeId: $employeeId) {
      errorCode
      message
      personalInfoDetails
      drivingLicenseDetails
      dependentDetails
      passportDetails
    }
  }
`;
export const RETRIEVE_EMP_JOB_INFO = gql`
  query retrieveJobInfo($employeeId: Int!) {
    retrieveJobInfo(employeeId: $employeeId) {
      errorCode
      message
      jobInfoDetails
      experienceDetails
      assetDetails
    }
  }
`;
export const RETRIEVE_EMP_CONTACT_INFO = gql`
  query retrieveContactInfo($employeeId: Int!) {
    retrieveContactInfo(employeeId: $employeeId) {
      errorCode
      message
      contactDetails
    }
  }
`;
export const RETRIEVE_EMP_CAREER_INFO = gql`
  query retrieveCareerInfo($employeeId: Int!) {
    retrieveCareerInfo(employeeId: $employeeId) {
      errorCode
      message
      educationalInfoDetails
      certificateInfoDetails
      trainingInfoDetails
      awardDetails
      skillDetails
    }
  }
`;
export const RETRIEVE_EMP_DOCUMENT_ACCREDITATION_INFO = gql`
  query retrieveDocumentInfo($employeeId: Int!) {
    retrieveDocumentInfo(employeeId: $employeeId) {
      errorCode
      message
      documentDetails
      accreditationDetails
    }
  }
`;
export const RETRIEVE_EMP_DOCUMENT_ASSOSIATED_FIELDS = gql`
  query getDocumentAssociatedFields($candidateId: Int!) {
    getDocumentAssociatedFields(candidateId: $candidateId) {
      errorCode
      message
      response
    }
  }
`;
export const RETRIEVE_EMP_OTHER_INFO = gql`
  query retrieveOtherInfo($employeeId: Int!) {
    retrieveOtherInfo(employeeId: $employeeId) {
      errorCode
      message
      bankDetails
      insuranceDetails
    }
  }
`;
export const RETRIEVE_MAX_EMP_ID = gql`
  query retrieveMaxEmployeeId {
    retrieveMaxEmployeeId {
      errorCode
      message
      maxEmployeeId
    }
  }
`;

export const RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION = gql`
  query retrieveProbationDate($designationId: Int!, $dateOfJoin: String!) {
    retrieveProbationDate(
      designationId: $designationId
      dateOfJoin: $dateOfJoin
    ) {
      errorCode
      message
      probationDate
    }
  }
`;

export const RETRIEVE_EMPLOYEE_CHANGES = gql`
  query retrievePendingEmployeeChanges($employeeId: Int!, $tables: [String!]) {
    retrievePendingEmployeeChanges(employeeId: $employeeId, tables: $tables) {
      errorCode
      message
      pendingChanges {
        Employee_Id
        Request_Id
        Approval_Status
        Added_On
        Added_By
        Updated_On
        Updated_By
        changes {
          Item_Id
          Table_Name
          Change_Type
          New_Data
          Reference_Id
        }
      }
    }
  }
`;

export const VALIDATE_DATE_OF_JOIN_AND_PROBATION_DATE_TO_CHANGE = gql`
  query validateDOJAndProbationDate($employeeId: Int!) {
    validateDOJAndProbationDate(employeeId: $employeeId) {
      errorCode
      dateOfJoinEdit
      dateOfJoinErrorMessage
      probationDateEdit
      probationErrorMessage
    }
  }
`;

export const VALIDATE_FIELD_AVAILABILITY = gql`
  query validateCommonAvailability(
    $columnName: String!
    $columnValue: String!
    $employeeId: Int
    $tableName: String
  ) {
    validateCommonAvailability(
      employeeId: $employeeId
      columnValue: $columnValue
      columnName: $columnName
      tableName: $tableName
    ) {
      errorCode
      message
      isAvailable
    }
  }
`;

export const VALIDATE_BENEFIT_APPLICABLE_BASED_ON_EMP_TYPE = gql`
  query validateBenefitsApplicable($employeeTypeId: Int!) {
    validateBenefitsApplicable(employeeTypeId: $employeeTypeId) {
      errorCode
      message
      benefitsApplicable
    }
  }
`;

export const VALIDATE_MOBILE_NUMBER = gql`
  query validateMobileNumber(
    $employeeId: Int!
    $mobileNo: String!
    $mobileNoCountryCode: String!
  ) {
    validateMobileNumber(
      employeeId: $employeeId
      mobileNo: $mobileNo
      mobileNoCountryCode: $mobileNoCountryCode
    ) {
      errorCode
      message
      valid
    }
  }
`;

export const RETRIEVE_SALARY_CONFIG = gql`
  query retrieveSalaryConfiguration($employeeId: Int!) {
    retrieveSalaryConfiguration(employeeId: $employeeId) {
      errorCode
      message
      validations
      retiralDetails
      bondRecoveryDetails
      contractDetails
      taxDetails
      overtimeDetails
    }
  }
`;
export const GET_AUTHENTICATION_METHODS = gql`
  query getAuthenticationMethods {
    getAuthenticationMethods {
      errorCode
      message
      authenticationMethods {
        Authentication_Method_Id
        Authentication_Method
        Description
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const ADD_UPDATE_PERSONAL_DETAILS = gql`
  mutation addUpdatePersonalInfo(
    $employeeId: Int
    $userDefinedEmpId: String
    $biometricIntegrationId: String
    $photoPath: String
    $salutation: String
    $empFirstName: String!
    $empMiddleName: String
    $empLastName: String!
    $knownAs: String
    $gender: String!
    $genderId: Int!
    $genderIdentityId: Int
    $genderExpressionId: Int
    $dob: String!
    $placeOfBirth: String
    $maritalStatus: Int!
    $bloodGroup: String
    $languages: [language]
    $nationality: String!
    $militaryService: Int
    $religion: String
    $religionId: Int
    $appellation: String
    $nationalityId: Int
    $caste: String
    $disabled: Int
    $isManager: Int
    $isRecruiter: String
    $personalEmail: String
    $smoker: Int
    $smokerAsOf: String
    $aadharNumber: String
    $uan: String
    $pan: String
    $formStatus: Int
    $allowUserSignIn: Int
    $enableMobileSignIn: Int
    $formId: Int
    $hobbies: String
    $genderOrientations: String
    $pronoun: String
    $statutoryInsuranceNumber: String
    $pranNo: String
    $taxCode: String
    $empPrefixSettingId: Int
    $personalCustomField1: String
    $personalCustomField2: String
    $personalCustomField3: String
  ) {
    addUpdatePersonalInfo(
      employeeId: $employeeId
      userDefinedEmpId: $userDefinedEmpId
      biometricIntegrationId: $biometricIntegrationId
      photoPath: $photoPath
      salutation: $salutation
      empFirstName: $empFirstName
      empMiddleName: $empMiddleName
      empLastName: $empLastName
      knownAs: $knownAs
      gender: $gender
      genderId: $genderId
      genderIdentityId: $genderIdentityId
      genderExpressionId: $genderExpressionId
      dob: $dob
      placeOfBirth: $placeOfBirth
      maritalStatus: $maritalStatus
      bloodGroup: $bloodGroup
      languages: $languages
      nationality: $nationality
      militaryService: $militaryService
      religion: $religion
      nationalityId: $nationalityId
      appellation: $appellation
      religionId: $religionId
      caste: $caste
      disabled: $disabled
      isManager: $isManager
      isRecruiter: $isRecruiter
      personalEmail: $personalEmail
      smoker: $smoker
      smokerAsOf: $smokerAsOf
      aadharNumber: $aadharNumber
      uan: $uan
      pan: $pan
      formStatus: $formStatus
      allowUserSignIn: $allowUserSignIn
      enableMobileSignIn: $enableMobileSignIn
      formId: $formId
      hobbies: $hobbies
      genderOrientations: $genderOrientations
      pronoun: $pronoun
      statutoryInsuranceNumber: $statutoryInsuranceNumber
      pranNo: $pranNo
      taxCode: $taxCode
      empPrefixSettingId: $empPrefixSettingId
      personalCustomField1: $personalCustomField1
      personalCustomField2: $personalCustomField2
      personalCustomField3: $personalCustomField3
    ) {
      employeeId
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_DEPENDENT_DETAILS = gql`
  mutation addUpdateDependentDetails(
    $employeeId: Int!
    $dependentId: Int
    $dependentFirstName: String!
    $dependentLastName: String!
    $gender: String!
    $genderId: Int!
    $relationship: String!
    $dependentDOB: String!
    $formId: Int
    $isUpdate: Int
  ) {
    addUpdateDependentDetails(
      employeeId: $employeeId
      dependentId: $dependentId
      dependentFirstName: $dependentFirstName
      dependentLastName: $dependentLastName
      gender: $gender
      genderId: $genderId
      relationship: $relationship
      dependentDOB: $dependentDOB
      formId: $formId
      isUpdate: $isUpdate
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_LICENSE_DETAILS = gql`
  mutation addUpdateDrivingLicenseDetails(
    $employeeId: Int!
    $drivingLicenseNo: String!
    $licenseIssueDate: String!
    $licenseExpiryDate: String!
    $issuingAuthority: String!
    $issuingCountry: String!
    $issuingState: String!
    $vehicleType: String
    $formId: Int
    $formStatus: Int
    $fileName: String
  ) {
    addUpdateDrivingLicenseDetails(
      employeeId: $employeeId
      drivingLicenseNo: $drivingLicenseNo
      licenseIssueDate: $licenseIssueDate
      licenseExpiryDate: $licenseExpiryDate
      issuingAuthority: $issuingAuthority
      issuingCountry: $issuingCountry
      issuingState: $issuingState
      vehicleType: $vehicleType
      formId: $formId
      formStatus: $formStatus
      fileName: $fileName
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_PASSPORT_DETAILS = gql`
  mutation addUpdatePassportDetails(
    $employeeId: Int!
    $passportNo: String!
    $passportIssueDate: String!
    $passportExpiryDate: String!
    $issuingAuthority: String!
    $issuingCountry: String!
    $formId: Int
    $formStatus: Int
    $fileName: String
  ) {
    addUpdatePassportDetails(
      employeeId: $employeeId
      passportNo: $passportNo
      passportIssueDate: $passportIssueDate
      passportExpiryDate: $passportExpiryDate
      issuingAuthority: $issuingAuthority
      issuingCountry: $issuingCountry
      formId: $formId
      formStatus: $formStatus
      fileName: $fileName
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_JOB_DETAILS = gql`
  mutation updateJobDetails(
    $Employee_Id: Int!
    $Designation_Id: Int!
    $Designation_Id_Effective_Date: String
    $Department_Id: Int!
    $Department_Id_Effective_Date: String
    $Location_Id: Int!
    $Location_Id_Effective_Date: String
    $Job_Code: String
    $Emp_Email: String
    $Manager_Id: Int
    $Manager_Id_Effective_Date: String
    $Emp_Profession: Int
    $Confirmed: Int
    $Confirmation_Date: String
    $Probation_Date: String
    $EmpType_Id: Int!
    $EmpType_Id_Effective_Date: String
    $Commission_Employee: Int
    $Work_Schedule: Int!
    $Work_Schedule_Effective_Date: String
    $TDS_Exemption: Int
    $Attendance_Enforced_Payment: Int
    $Previous_Employee_Experience: Int
    $Service_Provider_Id: Int
    $Date_Of_Join: String!
    $isUpdate: Int
    $Business_Unit_Id: Int
    $Business_Unit_Id_Effective_Date: String
    $Pf_PolicyNo: String
    $Organization_Group_Id: Int
    $Roles_Id: Int
    $isJobDetailsUpdated: Boolean
    $isProbationDateUpdated: Boolean
    $isDOJUpdated: Boolean
    $Job_Role_Ids: [Int]
    $Custom_Field_1: String
  ) {
    updateJobDetails(
      Employee_Id: $Employee_Id
      Designation_Id: $Designation_Id
      Designation_Id_Effective_Date: $Designation_Id_Effective_Date
      Department_Id: $Department_Id
      Department_Id_Effective_Date: $Department_Id_Effective_Date
      Location_Id: $Location_Id
      Location_Id_Effective_Date: $Location_Id_Effective_Date
      Job_Code: $Job_Code
      Emp_Email: $Emp_Email
      Manager_Id: $Manager_Id
      Manager_Id_Effective_Date: $Manager_Id_Effective_Date
      Emp_Profession: $Emp_Profession
      Confirmed: $Confirmed
      Confirmation_Date: $Confirmation_Date
      Probation_Date: $Probation_Date
      EmpType_Id: $EmpType_Id
      EmpType_Id_Effective_Date: $EmpType_Id_Effective_Date
      Commission_Employee: $Commission_Employee
      Work_Schedule: $Work_Schedule
      Work_Schedule_Effective_Date: $Work_Schedule_Effective_Date
      TDS_Exemption: $TDS_Exemption
      Attendance_Enforced_Payment: $Attendance_Enforced_Payment
      Previous_Employee_Experience: $Previous_Employee_Experience
      Service_Provider_Id: $Service_Provider_Id
      Date_Of_Join: $Date_Of_Join
      isUpdate: $isUpdate
      Organization_Group_Id: $Organization_Group_Id
      Business_Unit_Id: $Business_Unit_Id
      Business_Unit_Id_Effective_Date: $Business_Unit_Id_Effective_Date
      Pf_PolicyNo: $Pf_PolicyNo
      Roles_Id: $Roles_Id
      isJobDetailsUpdated: $isJobDetailsUpdated
      isProbationDateUpdated: $isProbationDateUpdated
      isDOJUpdated: $isDOJUpdated
      Job_Role_Ids: $Job_Role_Ids
      Custom_Field_1: $Custom_Field_1
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_EXPERIENCE_DETAILS = gql`
  mutation addUpdateExperienceDetails(
    $employeeId: Int!
    $experienceId: Int
    $companyName: String!
    $companyLocation: String!
    $designation: String!
    $startDate: String!
    $endDate: String!
    $duration: String!
    $years: Int!
    $months: Int!
    $fileName: String
    $fileSize: String
    $referenceDetails: [candidateExperienceReference]
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateExperienceDetails(
      employeeId: $employeeId
      companyName: $companyName
      experienceId: $experienceId
      companyLocation: $companyLocation
      designation: $designation
      startDate: $startDate
      endDate: $endDate
      duration: $duration
      years: $years
      months: $months
      fileName: $fileName
      fileSize: $fileSize
      referenceDetails: $referenceDetails
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_ASSET_DETAILS = gql`
  mutation addUpdateAssetDetails(
    $employeeId: Int!
    $assetId: Int
    $assetName: String!
    $serialNo: String!
    $receiveDate: String!
    $returnDate: String
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateAssetDetails(
      employeeId: $employeeId
      assetId: $assetId
      assetName: $assetName
      serialNo: $serialNo
      receiveDate: $receiveDate
      returnDate: $returnDate
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_EDUCATION_DETAILS = gql`
  mutation addUpdateEducationDetails(
    $educationId: Int
    $employeeId: Int!
    $educationType: Int
    $specialisation: String
    $instituteName: String
    $Specialization_Id: Int
    $Institution_Id: Int
    $university: String
    $yearOfStart: Int
    $yearOfPassing: Int
    $percentage: Float
    $grade: String
    $formId: Int
    $formStatus: Int
    $startDate: Date
    $endDate: Date
    $city: String
    $state: String
    $country: String
  ) {
    addUpdateEducationDetails(
      educationId: $educationId
      employeeId: $employeeId
      educationType: $educationType
      specialisation: $specialisation
      instituteName: $instituteName
      Specialization_Id: $Specialization_Id
      Institution_Id: $Institution_Id
      university: $university
      yearOfStart: $yearOfStart
      yearOfPassing: $yearOfPassing
      percentage: $percentage
      grade: $grade
      formId: $formId
      formStatus: $formStatus
      startDate: $startDate
      endDate: $endDate
      city: $city
      state: $state
      country: $country
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_CERTIFICATE_DETAILS = gql`
  mutation addUpdateCertificationDetails(
    $certificationId: Int
    $employeeId: Int!
    $certificationName: String!
    $receivedDate: String!
    $receivedFrom: String!
    $formId: Int
    $formStatus: Int
    $ranking: String
  ) {
    addUpdateCertificationDetails(
      certificationId: $certificationId
      certificationName: $certificationName
      receivedDate: $receivedDate
      receivedFrom: $receivedFrom
      formId: $formId
      formStatus: $formStatus
      employeeId: $employeeId
      ranking: $ranking
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_TRAINING_DETAILS = gql`
  mutation addUpdateTrainingDetails(
    $trainingId: Int
    $employeeId: Int!
    $trainingName: String!
    $trainingStartDate: String!
    $trainingEndDate: String!
    $trainingDuration: String!
    $trainer: String!
    $center: String!
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateTrainingDetails(
      trainingId: $trainingId
      employeeId: $employeeId
      trainingName: $trainingName
      trainingStartDate: $trainingStartDate
      trainingEndDate: $trainingEndDate
      trainingDuration: $trainingDuration
      trainer: $trainer
      center: $center
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_SKILL_DETAILS = gql`
  mutation addUpdateSkillDetails(
    $employeeId: Int!
    $primarySkill: String
    $secondarySkill: String
    $knownSkills: String
    $handsOn: String
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateSkillDetails(
      employeeId: $employeeId
      primarySkill: $primarySkill
      secondarySkill: $secondarySkill
      knownSkills: $knownSkills
      handsOn: $handsOn
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_AWARD_DETAILS = gql`
  mutation addUpdateAwardDetails(
    $employeeId: Int!
    $awardId: Int
    $awardName: String!
    $receivedOn: String!
    $receivedFrom: String!
    $receivedFor: String!
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateAwardDetails(
      employeeId: $employeeId
      awardId: $awardId
      awardName: $awardName
      receivedOn: $receivedOn
      receivedFrom: $receivedFrom
      receivedFor: $receivedFor
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_CONTACT_DETAILS = gql`
  mutation addUpdateContactDetails(
    $employeeId: Int!
    $permanent_appartmentName: String!
    $permanent_streetName: String
    $permanent_city: String
    $permanent_state: String
    $permanent_country: String!
    $permanent_pinCode: String
    $current_appartmentName: String!
    $current_streetName: String
    $current_city: String
    $current_state: String
    $current_country: String!
    $current_pinCode: String
    $office_appartmentName: String!
    $office_streetName: String
    $office_city: String
    $office_state: String
    $office_country: String!
    $office_pinCode: String
    $landlineNo: String
    $mobileNo: String!
    $mobileNoCountryCode: String!
    $useLocationAddress: Int!
    $faxNo: String
    $Emergency_Contact_Relation: String
    $Emergency_Contact_Name: String
    $workNo: String
    $permanent_barangay: String
    $permanent_region: String
    $current_barangay: String
    $current_region: String
    $office_barangay: String
    $office_region: String
    $permanent_barangay_id: Int
    $current_barangay_id: Int
    $office_barangay_id: Int
    $permanent_city_id: Int
    $current_city_id: Int
    $office_city_id: Int
    $formStatus: Int
    $formId: Int
  ) {
    addUpdateContactDetails(
      employeeId: $employeeId
      permanent_appartmentName: $permanent_appartmentName
      permanent_streetName: $permanent_streetName
      permanent_city: $permanent_city
      permanent_state: $permanent_state
      permanent_country: $permanent_country
      permanent_pinCode: $permanent_pinCode
      current_appartmentName: $current_appartmentName
      current_streetName: $current_streetName
      current_city: $current_city
      current_state: $current_state
      current_country: $current_country
      current_pinCode: $current_pinCode
      office_appartmentName: $office_appartmentName
      office_streetName: $office_streetName
      office_city: $office_city
      office_state: $office_state
      office_country: $office_country
      office_pinCode: $office_pinCode
      landlineNo: $landlineNo
      mobileNo: $mobileNo
      mobileNoCountryCode: $mobileNoCountryCode
      useLocationAddress: $useLocationAddress
      faxNo: $faxNo
      workNo: $workNo
      Emergency_Contact_Name: $Emergency_Contact_Name
      Emergency_Contact_Relation: $Emergency_Contact_Relation
      permanent_barangay: $permanent_barangay
      permanent_region: $permanent_region
      current_barangay: $current_barangay
      current_region: $current_region
      office_barangay: $office_barangay
      office_region: $office_region
      permanent_barangay_id: $permanent_barangay_id
      current_barangay_id: $current_barangay_id
      office_barangay_id: $office_barangay_id
      permanent_city_id: $permanent_city_id
      current_city_id: $current_city_id
      office_city_id: $office_city_id
      formStatus: $formStatus
      formId: $formId
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_ACCREDITATION_DETAILS = gql`
  mutation AddUpdateAccreditationDetails(
    $employeeId: Int!
    $accreditationDetailId: Int
    $accreditationCategoryAndType: Int!
    $receivedDate: String!
    $identifier: String
    $expiryDate: String!
    $fileName: String!
    $formId: Int
    $formStatus: Int
    $examRating: Int
    $examDateYear: Int
    $examDateMonth: String
    $dependentId: Int
  ) {
    addUpdateAccreditationDetails(
      employeeId: $employeeId
      accreditationDetailId: $accreditationDetailId
      accreditationCategoryAndType: $accreditationCategoryAndType
      receivedDate: $receivedDate
      identifier: $identifier
      expiryDate: $expiryDate
      fileName: $fileName
      formId: $formId
      formStatus: $formStatus
      examRating: $examRating
      examDateYear: $examDateYear
      examDateMonth: $examDateMonth
      dependentId: $dependentId
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_DOCUMENT_DETAILS = gql`
  mutation AddUpdateEmployeeDocuments(
    $documentId: Int
    $employeeId: Int!
    $documentName: String!
    $documentSubType: Int!
    $fileName: String!
    $fileSize: String
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateEmployeeDocuments(
      documentId: $documentId
      employeeId: $employeeId
      documentName: $documentName
      documentSubType: $documentSubType
      fileName: $fileName
      fileSize: $fileSize
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_BANK_DETAILS = gql`
  mutation AddUpdateBankDetails(
    $employeeId: Int!
    $bankId: Int
    $bankAccountNumber: String!
    $empBankId: Int!
    $branchName: String
    $ifsc: String
    $bsb: String
    $bank_streetName: String
    $bank_city: String
    $bank_state: String
    $bank_pinCode: String
    $accountType: Int!
    $creditAccount: String!
    $beneficiaryId: String
    $status: String!
    $bankAccountName: String
    $fileName: String
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateBankDetails(
      employeeId: $employeeId
      bankId: $bankId
      bankAccountNumber: $bankAccountNumber
      empBankId: $empBankId
      branchName: $branchName
      ifsc: $ifsc
      bsb: $bsb
      bank_streetName: $bank_streetName
      bank_city: $bank_city
      bank_state: $bank_state
      bank_pinCode: $bank_pinCode
      accountType: $accountType
      creditAccount: $creditAccount
      beneficiaryId: $beneficiaryId
      status: $status
      bankAccountName: $bankAccountName
      fileName: $fileName
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_INSURANCE_DETAILS = gql`
  mutation AddUpdateInsuranceDetails(
    $employeeId: Int!
    $policyId: Int
    $insuranceTypeId: Int!
    $policyNo: String!
    $insuranceType: String
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateInsuranceDetails(
      employeeId: $employeeId
      policyId: $policyId
      insuranceTypeId: $insuranceTypeId
      policyNo: $policyNo
      insuranceType: $insuranceType
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_PROFILE_PICTURE_PATH = gql`
  mutation updateProfilePhoto(
    $employeeId: Int!
    $photoPath: String!
    $formName: String
  ) {
    updateProfilePhoto(
      employeeId: $employeeId
      photoPath: $photoPath
      formName: $formName
    ) {
      errorCode
      message
    }
  }
`;

export const EMPLOYEE_FINAL_SUBMIT = gql`
  mutation employeeFinalSubmit($employeeId: Int!) {
    employeeFinalSubmit(employeeId: $employeeId) {
      errorCode
      message
    }
  }
`;

export const SEND_FIREBASE_INVITATION = gql`
  mutation triggerBulkInviteEmployees($employeeData: [employeeData]) {
    triggerBulkInviteEmployees(employeeData: $employeeData) {
      errorCode
      message
    }
  }
`;

export const CLONE_EMPLOYEE = gql`
  mutation CloneEmployee(
    $oldEmployeeId: Int!
    $Designation_Id: Int!
    $Department_Id: Int!
    $Location_Id: Int!
    $EmpType_Id: Int!
    $Work_Schedule: Int!
    $Date_Of_Join: String!
    $User_Defined_EmpId: String!
    $External_EmpId: String
    $Service_Provider_Id: Int
    $Probation_Date: String!
    $Roles_Id: Int!
    $Business_Unit_Id: Int
    $Job_Role_Ids: [Int]!
    $empPrefixSettingId: Int
  ) {
    cloneEmployee(
      oldEmployeeId: $oldEmployeeId
      Designation_Id: $Designation_Id
      Department_Id: $Department_Id
      Location_Id: $Location_Id
      EmpType_Id: $EmpType_Id
      Work_Schedule: $Work_Schedule
      Date_Of_Join: $Date_Of_Join
      User_Defined_EmpId: $User_Defined_EmpId
      External_EmpId: $External_EmpId
      Service_Provider_Id: $Service_Provider_Id
      Probation_Date: $Probation_Date
      Business_Unit_Id: $Business_Unit_Id
      Roles_Id: $Roles_Id
      Job_Role_Ids: $Job_Role_Ids
      empPrefixSettingId: $empPrefixSettingId
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_RETIRALS_CONFIG = gql`
  mutation addUpdateRetiralDetails(
    $employeeId: Int!
    $eligibleForPension: Int
    $eligibleForVpf: Int
    $vpfType: String
    $vpfEmployeeShare: Float
    $vpfEmployeeShareAmount: Float
    $cpsType: String
    $cpsEmployeeShareAmount: Float
    $cpsEmployerShareAmount: Float
    $cpsEmployeeShare: Float
    $cpsEmployerShare: Float
    $eligibleForPensionScheme: String
    $eligibleForTeacherProvidentFund: String
    $tpfNumber: String
    $tpfType: String
    $tpfEmployeeShareAmount: Float
    $tpfEmployeeShare: Float
    $cpsNumber: String
    $eligibleForSpecialProvidentFund: String
    $spfNumber: String
    $spfEmployeeShare: Float
    $spfEndMonth: String
    $formId: Int
    $formStatus: Int
    $eligibleForPf: Int
    $eligibleForNps: Int
    $eligibleForEsi: Int
    $eligibleForInsurance: Int
    $eligibleForGratuity: Int
  ) {
    addUpdateRetiralDetails(
      employeeId: $employeeId
      eligibleForPension: $eligibleForPension
      eligibleForVpf: $eligibleForVpf
      vpfType: $vpfType
      vpfEmployeeShare: $vpfEmployeeShare
      vpfEmployeeShareAmount: $vpfEmployeeShareAmount
      cpsType: $cpsType
      cpsEmployeeShareAmount: $cpsEmployeeShareAmount
      cpsEmployerShareAmount: $cpsEmployerShareAmount
      cpsEmployeeShare: $cpsEmployeeShare
      cpsEmployerShare: $cpsEmployerShare
      eligibleForPensionScheme: $eligibleForPensionScheme
      eligibleForTeacherProvidentFund: $eligibleForTeacherProvidentFund
      tpfNumber: $tpfNumber
      tpfType: $tpfType
      tpfEmployeeShareAmount: $tpfEmployeeShareAmount
      tpfEmployeeShare: $tpfEmployeeShare
      cpsNumber: $cpsNumber
      eligibleForSpecialProvidentFund: $eligibleForSpecialProvidentFund
      spfNumber: $spfNumber
      spfEmployeeShare: $spfEmployeeShare
      spfEndMonth: $spfEndMonth
      formId: $formId
      formStatus: $formStatus
      eligibleForPf: $eligibleForPf
      eligibleForNps: $eligibleForNps
      eligibleForEsi: $eligibleForEsi
      eligibleForInsurance: $eligibleForInsurance
      eligibleForGratuity: $eligibleForGratuity
    ) {
      errorCode
      message
    }
  }
`;
export const UPDATE_OVERTIME_CONFIG = gql`
  mutation AddUpdateOvertimeDetails(
    $employeeId: Int!
    $eligibleForOvertime: Int!
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateOvertimeDetails(
      employeeId: $employeeId
      eligibleForOvertime: $eligibleForOvertime
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
export const UPDATE_BOND_RECOVERY_CONFIG = gql`
  mutation AddUpdateBondRecoveryDetails(
    $employeeId: Int!
    $bondRecoveryApplicable: Int!
    $minimumMonthsToBeServed: Int
    $bondValue: String
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateBondRecoveryDetails(
      employeeId: $employeeId
      bondRecoveryApplicable: $bondRecoveryApplicable
      minimumMonthsToBeServed: $minimumMonthsToBeServed
      bondValue: $bondValue
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
export const UPDATE_TDS_CONTRACTOR_CONFIG = gql`
  mutation addUpdateTaxDetails(
    $employeeId: Int!
    $eligibleForContractorTds: Int!
    $taxSectionId: Int!
    $eligibleForPt: Int!
    $salaryCalculationScheme: String
    $minimumWage: Float
    $tdsEffectiveFrom: String
    $frroRegistration: String
    $formId: Int
    $formStatus: Int
  ) {
    addUpdateTaxDetails(
      employeeId: $employeeId
      eligibleForContractorTds: $eligibleForContractorTds
      taxSectionId: $taxSectionId
      eligibleForPt: $eligibleForPt
      salaryCalculationScheme: $salaryCalculationScheme
      minimumWage: $minimumWage
      tdsEffectiveFrom: $tdsEffectiveFrom
      frroRegistration: $frroRegistration
      formId: $formId
      formStatus: $formStatus
    ) {
      errorCode
      message
    }
  }
`;
