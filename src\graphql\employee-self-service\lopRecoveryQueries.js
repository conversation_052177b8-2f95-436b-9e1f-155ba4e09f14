import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const RETRIEVE_LOP_RECOVERY_DETAILS = gql`
  query retrieveLopRecoveryDetails($selfService: Int!) {
    retrieveLopRecoveryDetails(selfService: $selfService) {
      errorCode
      message
      lopRecoveryDetails {
        LOP_Recovery_Id
        userDefinedEmpId
        Total_Lop_Recovery_Days
        Salary_Month
        Reason
        Start_Date
        Leave_Id
        Leave_Name
        Leave_Period
        Work_Schedule
        Designation_Id
        Designation_Name
        Department_Id
        EmpType_Id
        Employee_Type
        Department_Name
        Location_Name
        Location_Id
        Duration
        Recovery_Amount
        Salary_Deduction_Month
        Approval_Status
        remark
        Employee_Id
        Employee_Name
        Added_On
        Added_By
        Updated_On
        Updated_By
        Approved_By_Name
        Approved_By
        Per_Day_Salary
        Approved_On
        All_Recovery_Amount
      }
    }
  }
`;
export const LIST_EMPLOYEE_LOP_LEAVES = gql`
  query listEmployeeLopLeaves(
    $lopRecoveryId: Int!
    $employeeId: Int!
    $salaryMonthYear: String!
  ) {
    listEmployeeLopLeaves(
      lopRecoveryId: $lopRecoveryId
      employeeId: $employeeId
      salaryMonthYear: $salaryMonthYear
    ) {
      errorCode
      message
      leaveDetails {
        Employee_Id
        Leave_Id
        Leave_Name
        Start_Date
        Duration
        Leave_Period
        Reason
        Claim_Status
      }
    }
  }
`;

export const LIST_LOP_EMPLOYEES = gql`
  query listLopEmployees($selfService: Int!, $salaryMonthYear: String!) {
    listLopEmployees(
      selfService: $selfService
      salaryMonthYear: $salaryMonthYear
    ) {
      errorCode
      message
      lopEmployeesDetails {
        Employee_Id
        userDefinedEmpId
        Employee_Name
        Work_Schedule
        Designation_Id
        Designation_Name
        EmpType_Id
        Department_Id
        Department_Name
        Location_Id
        Location_Name
      }
    }
  }
`;
export const CALCULATE_RECOVERY_AMOUNT = gql`
  query calculatePerDaySalary($employeeId: Int!, $salaryMonth: String!) {
    calculatePerDaySalary(employeeId: $employeeId, salaryMonth: $salaryMonth) {
      errorCode
      message
      perDaySalary
    }
  }
`;

export const LIST_LOP_RECOVERY_DEDUCTION_PAYROLL_MONTH = gql`
  query listLopRecoveryDeductionPayrollMonth(
    $employeeId: Int
    $selfService: Int!
  ) {
    listLopRecoveryDeductionPayrollMonth(
      employeeId: $employeeId
      selfService: $selfService
    ) {
      errorCode
      message
      payRollMonthDetails
    }
  }
`;

export const LIST_LOP_RECOVERY_PAYMENT_MONTH = gql`
  query listLopRecoveryPaymentMonth(
    $employeeId: Int
    $selfService: Int!
    $salaryDeductionMonth: String!
  ) {
    listLopRecoveryPaymentMonth(
      employeeId: $employeeId
      selfService: $selfService
      salaryDeductionMonth: $salaryDeductionMonth
    ) {
      errorCode
      message
      payConsiderationMonthDetails
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_LOP_REQUEST = gql`
  mutation addUpdateLopRequest(
    $lopRecoveryID: Int!
    $selfService: Int!
    $leaveDetails: [LeaveDetailInput]!
    $employeeId: Int!
    $salaryMonth: String!
    $LopDeductedSalaryMonth: String!
    $salaryPaymentMonth: String!
    $remark: String
  ) {
    addUpdateLopRequest(
      lopRecoveryID: $lopRecoveryID
      selfService: $selfService
      leaveDetails: $leaveDetails
      employeeId: $employeeId
      salaryMonth: $salaryMonth
      LopDeductedSalaryMonth: $LopDeductedSalaryMonth
      salaryPaymentMonth: $salaryPaymentMonth
      remark: $remark
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_LOP_REQUEST = gql`
  mutation deleteLopRequest($lopRecoveryID: Int!, $selfService: Int!) {
    deleteLopRequest(lopRecoveryID: $lopRecoveryID, selfService: $selfService) {
      errorCode
      message
    }
  }
`;
export const RETRIEVE_ORGANIZATION_LIST = gql`
  mutation GetOrganizationChart(
    $orgCode: String!
    $loggedInUserId: String!
    $managerId: String
  ) {
    getOrganizationChart(
      orgCode: $orgCode
      loggedInUserId: $loggedInUserId
      managerId: $managerId
    ) {
      error {
        code
        message
      }
      result {
        orgDetail {
          orgReportPath
          orgName
        }
        children {
          id
          name
          title
          gender
          isManager
          filePath
          relationship
          className
        }
      }
    }
  }
`;

export const GET_ORGANIZATION_CHART_LIST = gql`
  mutation getOrganizationChartList(
    $orgCode: String!
    $loginEmployeeId: Int!
    $managerId: Int
    $serviceProviderId: Int
  ) {
    getOrganizationChartList(
      orgCode: $orgCode
      loginEmployeeId: $loginEmployeeId
      managerId: $managerId
      serviceProviderId: $serviceProviderId
    ) {
      errorCode
      message
      orgDetail {
        orgReportPath
        orgName
      }
      employeeList {
        employeeId
        employeeName
        isManager
        designationName
        departmentName
        managerId
        gender
        location
      }
    }
  }
`;
