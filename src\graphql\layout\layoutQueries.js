import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const GET_DOMAIN_DETAILS_QUERY = gql`
  query getDomainDetails {
    getDomainDetails {
      errorCode
      message
      domainDetails {
        supportEmail
        copyRight
        supportLink
        termsLink
        privacyPolicyLink
        chatBot
        paymentPortalSiteName
      }
      licensingDetails
    }
  }
`;
export const GET_ORG_USER_DETAILS_QUERY = gql`
  query getOrganizationUserDetails {
    getOrganizationUserDetails {
      errorCode
      message
      organizationName
      entomoSyncType
      serviceProviderName
      serviceProviderId
      employeeSettings {
        Enable_Workflow_Profile
        Enable_Workflow_Team_Summary
      }
      organization {
        street1
        street2
        city
        state
        country
        pincode
      }
      serviceProvider {
        street1
        street2
        city
        state
        country
        pincode
      }
      orgDateFormat
      productIconPath
      assessmentYear
      paycycle
      employeeId
      monitoringType
      privacyMode
      disableLogout
      camuBaseUrl
      autoUpdateEffectiveDateForJobDetails
      Payroll_Period
      advancePayroll
      employeeEdit
      payRollIntegrationUrl
      assetManagementRedirectionUrl
      providentFundConfiguration
      userDetails {
        employeeId
        userDefinedEmployeeId
        employeeFullName
        employeeFirstName
        employeePhotoPath
        employeeEmail
        employeeLastName
        designationName
        languagePreference
      }
      fieldForce
      closureMonthJson
    }
  }
`;
export const UPDATE_EMPLOYEE_LANGUAGE_PREFERENCE = gql`
  mutation UpdateEmployeeLanguagePreference(
    $employeeId: Int!
    $languagePreference: String!
  ) {
    updateEmpLanguagePreference(
      employeeId: $employeeId
      languagePreference: $languagePreference
    ) {
      success
      errorCode
      message
    }
  }
`;

export const GET_SIDEBAR_MENU_QUERY = gql`
  query listModulesAndForms($languageCode: String) {
    listModulesAndForms(languageCode: $languageCode) {
      errorCode
      message
      modulesAndForms {
        moduleList {
          moduleId
          moduleName
          translatedModuleName
          formList {
            formId
            formName
            customFormName
            url
          }
        }
        formAccessList
        formIdAccessList
      }
    }
  }
`;
export const LIST_ANNOUNCEMENT = gql`
  query listAnnouncements {
    listAnnouncements {
      errorCode
      message
      announcementDetails {
        announcementId
        title
        embedUrl
        flashContent
        announcementType
        announcementText
      }
      announcementCount
    }
  }
`;
export const LIST_NOTIFICATIONS = gql`
  query listNotificationsInDashboard {
    listNotificationsInDashboard {
      errorCode
      message
      notificationList
      notificationCount
      leaveWorkflowEnabled
      reimbursementWorkflowEnabled
    }
  }
`;

export const GET_SUBSCRIBED_PLAN_TYPE = gql`
  query getOrganizationSubscribedPlan {
    getOrganizationSubscribedPlan {
      errorCode
      message
      subscribedDashboard
    }
  }
`;

//Modules request access query
export const REQUEST_ACCESS = gql`
  query requestRights {
    requestRights {
      errorCode
      message
    }
  }
`;
export const SEND_COUPON_EMAIL_REQUEST = gql`
  query sendEmailToClaimCoupon($organizationName: String!) {
    sendEmailToClaimCoupon(organizationName: $organizationName) {
      errorCode
      message
    }
  }
`;
export const ENCRYPT_REFRESH_TOKEN = gql`
  query encryptRefreshToken($refreshToken: String!) {
    encryptRefreshToken(refreshToken: $refreshToken) {
      errorCode
      message
      refreshToken
    }
  }
`;
export const LOGOUT_ENTOMO = gql`
  query logoutFromEntomo($xAuthToken: String!) {
    logoutFromEntomo(xAuthToken: $xAuthToken) {
      errorCode
      message
    }
  }
`;
