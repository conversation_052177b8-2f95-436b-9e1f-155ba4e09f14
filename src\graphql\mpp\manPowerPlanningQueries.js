import gql from "graphql-tag";
// ===============
// Queries
// ===============

export const RETRIEVE_HIRING_FORECAST_SETTINGS = gql`
  query retrieveForeCastSettings($formId: Int!) {
    retrieveForeCastSettings(formId: $formId) {
      errorCode
      message
      totalHiringForeCastRecords
      settings {
        Forecast_Settings_Id
        Release_Date
        End_Month
        Roles_Ids
      }
    }
  }
`;
export const RETRIEVE_LIST_ROLES = gql`
  query listRoles($formId: Int, $status: String) {
    listRoles(formId: $formId, status: $status) {
      errorCode
      message
      listRoles {
        Roles_Id
        Roles_Name
      }
    }
  }
`;
export const GET_POSITION_GLOBAL_SEARCH = gql`
  query getPositionGlobalSearch(
    $searchValue: String!
    $formId: Int!
    $screenTab: String!
    $offset: Int
    $limit: Int
    $forecastingYear: Int
  ) {
    getPositionGlobalSearch(
      searchValue: $searchValue
      formId: $formId
      screenTab: $screenTab
      offset: $offset
      limit: $limit
      forecastingYear: $forecastingYear
    ) {
      errorCode
      message
      validationError
      totalRecords
      positions {
        Organization_Structure_Id
        Recruitment_Id
        Position_Request_Id
        Group_Code
        Group_Name
        Position_Code
        Position_Title
        Division_Code
        Department_Code
        Section_Code
        Employee_Type
        Replacement_For
        Reason_For_Replacement
      }
    }
  }
`;

export const ADD_AND_UPDATE_FORECAST_SETTINGS = gql`
  mutation addUpdateForeCastSettings(
    $forecastSettingsId: Int!
    $releaseDate: Date!
    $endMonth: Int!
    $roleIds: [Int!]
  ) {
    addUpdateForeCastSettings(
      forecastSettingsId: $forecastSettingsId
      releaseDate: $releaseDate
      endMonth: $endMonth
      roleIds: $roleIds
    ) {
      errorCode
      message
    }
  }
`;

export const RETRIEVE_ROLES_EMAIL_LIST = gql`
  query retrieveEmployeeRoleEmail($roleIds: [Int], $formId: Int) {
    retrieveEmployeeRoleEmail(roleIds: $roleIds, formId: $formId) {
      errorCode
      message
      emailList {
        User_Defined_EmpId
        Employee_Id
        Emp_Email
        Employee_Name
      }
      nonRoleEmailList {
        User_Defined_EmpId
        Employee_Id
        Emp_Email
        Employee_Name
      }
    }
  }
`;

export const LIST_OF_FORECAST = gql`
  query listHiringForecast(
    $forecastingYear: Int!
    $postionParentId: String
    $offset: Int
    $limit: Int
    $alexport: Boolean
    $groupFilter: OrgFilterInput
    $divisionFilter: OrgFilterInput
    $departmentFilter: OrgFilterInput
    $sectionFilter: OrgFilterInput
    $organizationStructureId: Int
  ) {
    listHiringForecast(
      forecastingYear: $forecastingYear
      postionParentId: $postionParentId
      offset: $offset
      limit: $limit
      alexport: $alexport
      groupFilter: $groupFilter
      divisionFilter: $divisionFilter
      departmentFilter: $departmentFilter
      sectionFilter: $sectionFilter
      organizationStructureId: $organizationStructureId
    ) {
      errorCode
      message
      result
      groupId
      orgLevel
      s3Url
      s3Path
      totalCountResult
    }
  }
`;

export const LIST_OF_POSITION_LIST = gql`
  query jobTitleList($Form_Id: Int!, $conditions: [commonApplyCondition]) {
    jobTitleList(Form_Id: $Form_Id, conditions: $conditions) {
      errorCode
      message
      jobTitleResult {
        Organization_Structure_Id
        Pos_Name
        Pos_Code
        Pos_full_Name
        Originalpos_Id
      }
    }
  }
`;

export const HIRING_FORECAST_ADD_UPDATE = gql`
  mutation addUpdateHiringForecast(
    $originalPositionId: String
    $action: String!
    $divisionId: String
    $departmentId: String
    $sectionId: String
    $groupId: String
    $positionTitle: String!
    $organizationStructureId: Int
    $foreCastList: [foreCasteInput!]!
  ) {
    addUpdateHiringForecast(
      originalPositionId: $originalPositionId
      action: $action
      divisionId: $divisionId
      departmentId: $departmentId
      sectionId: $sectionId
      groupId: $groupId
      positionTitle: $positionTitle
      foreCastList: $foreCastList
      organizationStructureId: $organizationStructureId
    ) {
      errorCode
      message
    }
  }
`;

export const HIRING_POSITION_LIST = gql`
  query listForecastPosition(
    $formId: Int!
    $postionParentId: String
    $action: String
    $forecastingYear: Int
    $originalPositionId: String
    $offset: Int
    $limit: Int
    $parentPath: String
  ) {
    listForecastPosition(
      formId: $formId
      postionParentId: $postionParentId
      action: $action
      forecastingYear: $forecastingYear
      originalPositionId: $originalPositionId
      offset: $offset
      limit: $limit
      parentPath: $parentPath
    ) {
      errorCode
      message
      parentGroup
      totalCountResult
      positionList {
        Originalpos_Id
        Organization_Structure_Id
        Pos_Code
        Pos_Name
        Approved_Position
        Warm_Bodies
        To_Be_Hired
        Parent_Id
        Parent_Path
        Pos_full_Name
      }
    }
  }
`;

export const TABLE_OF_ORGANIZATION_LIST = gql`
  query listForecastPosition(
    $formId: Int!
    $postionParentId: String
    $offset: Int
    $limit: Int
    $alexport: Boolean
    $parentPath: String
    $organizationStructureId: Int
  ) {
    listForecastPosition(
      formId: $formId
      postionParentId: $postionParentId
      offset: $offset
      limit: $limit
      alexport: $alexport
      parentPath: $parentPath
      organizationStructureId: $organizationStructureId
    ) {
      errorCode
      message
      parentGroup
      orgLevel
      s3Url
      s3Path
      totalCountResult
      positionList {
        Originalpos_Id
        Pos_Code
        Pos_Name
        Job_Title_Code
        Cost_Code
        Global_Grade
        Approved_Position
        Warm_Bodies
        To_Be_Hired
        Parent_Id
        Parent_Path
        Organization_Structure_Id
        Status
      }
    }
  }
`;

export const RECRUITMENT_POSITION_LIST = gql`
  query listDetailsBasedOnOrgPosId(
    $formId: Int!
    $originalPosId: String
    $organizationStructureId: Int
  ) {
    listDetailsBasedOnOrgPosId(
      formId: $formId
      originalPosId: $originalPosId
      organizationStructureId: $organizationStructureId
    ) {
      errorCode
      message
      positionDetails {
        costCode
        groupCode
        divisionCode
        sectionCode
        deptCode
        groupFullName
        deptFullName
        sectionFullName
        divisionFullName
        positionLevel
        positionLevelId
        groupName
        divisionName
        sectionName
        deptName
        approvedPosition
        warmBodies
        totalRecruitmentCount
      }
    }
  }
`;

export const LIST_RECRUITMENT_REQUEST = gql`
  query listReqruitmentRequest(
    $formId: Int
    $postionParentCode: String
    $alexport: Boolean
    $positionParentId: String
    $groupFilter: OrgFilterInput
    $divisionFilter: OrgFilterInput
    $departmentFilter: OrgFilterInput
    $sectionFilter: OrgFilterInput
    $recruitmentId: Int
  ) {
    listReqruitmentRequest(
      formId: $formId
      postionParentCode: $postionParentCode
      alexport: $alexport
      positionParentId: $positionParentId
      groupFilter: $groupFilter
      divisionFilter: $divisionFilter
      departmentFilter: $departmentFilter
      sectionFilter: $sectionFilter
      recruitmentId: $recruitmentId
    ) {
      errorCode
      message
      groupCode
      positionParentId
      orgLevel
      s3Url
      s3Path
      reqruitmentRequestDetails {
        Recruitment_Id
        Original_Position_Id
        Group_Code
        totalRecords
        approvedByName
        Approver_Id
        Approved_Position
        Warm_Bodies
        Division_Code
        Department_Code
        Section_Code
        Position_Level
        Position_Level_Id
        Cost_Center
        Employee_Type
        Employee_Type_Name
        No_Of_Position
        Reason_For_Replacement
        Replacement_For
        Workflow_Id
        Group_Name
        Division_Name
        Department_Name
        Section_Name
        Status
        Added_By
        Added_On
        Updated_On
        Updated_By
        Pos_Code
        Pos_Name
        Custom_Group_Id
        CustomGroupName
        jobPostStatus
        candidateCount
      }
    }
  }
`;

export const ADD_EDIT_RECRUITMENT_REQUEST = gql`
  mutation addUpdateReqruitmentPosition(
    $recruitmentId: Int!
    $originalPositionId: String!
    $groupCode: String
    $divisionCode: String
    $departmentCode: String
    $sectionCode: String
    $costCenter: String
    $employeeType: String
    $noOfPosition: Int!
    $PositionLevel: Int
    $workflowId: Int
    $status: String
    $positionCode: String
    $positionTitle: String!
    $groupName: String
    $divisionName: String
    $departmentName: String
    $sectionName: String
    $employeeTypeName: String
    $eventId: String
    $reasonForRequest: String
    $reasonForReplacement: String
    $customGroupId: Int
  ) {
    addUpdateReqruitmentPosition(
      recruitmentId: $recruitmentId
      originalPositionId: $originalPositionId
      groupCode: $groupCode
      divisionCode: $divisionCode
      departmentCode: $departmentCode
      sectionCode: $sectionCode
      costCenter: $costCenter
      employeeType: $employeeType
      noOfPosition: $noOfPosition
      PositionLevel: $PositionLevel
      workflowId: $workflowId
      status: $status
      positionCode: $positionCode
      positionTitle: $positionTitle
      reasonForRequest: $reasonForRequest
      reasonForReplacement: $reasonForReplacement
      groupName: $groupName
      divisionName: $divisionName
      departmentName: $departmentName
      sectionName: $sectionName
      employeeTypeName: $employeeTypeName
      eventId: $eventId
      customGroupId: $customGroupId
    ) {
      errorCode
      message
      validationError
    }
  }
`;
export const NEW_POSITION_LIST = gql`
  query listNewPositionRequest(
    $formId: Int
    $postionParentCode: String
    $alexport: Boolean
    $positionParentId: String
    $groupFilter: OrgFilterInput
    $divisionFilter: OrgFilterInput
    $departmentFilter: OrgFilterInput
    $sectionFilter: OrgFilterInput
  ) {
    listNewPositionRequest(
      formId: $formId
      postionParentCode: $postionParentCode
      alexport: $alexport
      positionParentId: $positionParentId
      groupFilter: $groupFilter
      divisionFilter: $divisionFilter
      departmentFilter: $departmentFilter
      sectionFilter: $sectionFilter
    ) {
      errorCode
      message
      groupCode
      positionParentId
      orgLevel
      s3Url
      s3Path
      openPositiontRequestDetails {
        Position_Request_Id
        Organization_Structure_Id
        Original_Position_Id
        Position_Title
        Group_Code
        Division_Code
        Department_Code
        Section_Code
        Pos_Code
        Position_Level
        Position_Level_Id
        Reason_For_Replacement
        approvedByName
        Approver_Id
        Cost_Center
        Employee_Type
        No_Of_Position
        Comments
        Workflow_Id
        Employee_Type_Name
        Added_By_Email
        Group_Name
        Division_Name
        Department_Name
        Section_Name
        Status
        Custom_Group_Id
        Added_By
        Added_On
        Updated_On
        Updated_By
      }
    }
  }
`;

export const UPDATE_POSITION_STATUS = gql`
  mutation updateNewPositionChangeStatus(
    $status: String!
    $comments: String
    $positionRequestId: Int!
  ) {
    updateNewPositionChangeStatus(
      status: $status
      comments: $comments
      positionRequestId: $positionRequestId
    ) {
      errorCode
      message
      validationError
    }
  }
`;
export const DELETE_HIRING_FORECAST = gql`
  mutation deleteHiringForeCast(
    $originalPositionId: String!
    $forecastingYear: String!
  ) {
    deleteHiringForeCast(
      originalPositionId: $originalPositionId
      forecastingYear: $forecastingYear
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_TABLE_OF_ORGANIZATIONS = gql`
  mutation updatePositionJobSummary(
    $jobDescription: String!
    $approvedPosition: Int!
    $warmBodies: Int!
    $originalPositionId: String!
    $formId: Int!
    $positionCode: String!
  ) {
    updatePositionJobSummary(
      jobDescription: $jobDescription
      approvedPosition: $approvedPosition
      warmBodies: $warmBodies
      originalPositionId: $originalPositionId
      formId: $formId
      positionCode: $positionCode
    ) {
      errorCode
      message
      validationError
    }
  }
`;

export const DELETE_SEEK_ACCOUNT = gql`
  mutation deleteJobStreetAccount($formId: Int!, $seekHirerId: Int!) {
    deleteJobStreetAccount(formId: $formId, seekHirerId: $seekHirerId) {
      errorCode
      message
    }
  }
`;

export const UPDATE_WORKFLOW_APPROVAL = gql`
  mutation updateWorkflowApprovalSetting(
    $enableWorkflowApproval: String!
    $formId: Int!
  ) {
    updateWorkflowApprovalSetting(
      enableWorkflowApproval: $enableWorkflowApproval
      formId: $formId
    ) {
      errorCode
      message
      data
      validationError
    }
  }
`;

export const RECRUITMENT_SETTINGS = gql`
  query retrieveRecruitmentSettings {
    retrieveRecruitmentSettings {
      message
      errorCode
      warmBodiesIncludeNoticePeriod
      allowHireWithoutApprovedVacancy
    }
  }
`;

export const GET_WORKFLOW_APPROVAL_ENABLED = gql`
  query retrieveWorkflowApprovalSetting($formId: Int!) {
    retrieveWorkflowApprovalSetting(formId: $formId) {
      enableWorkflowApproval
      message
      errorCode
    }
  }
`;
