import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const LIST_ATTENDANCE = gql`
  query listAttendance(
    $employeeId: Int!
    $attendanceMonth: Int!
    $attendanceYear: Int!
    $formId: Int!
    $flag: String
  ) {
    listAttendance(
      employeeId: $employeeId
      attendanceMonth: $attendanceMonth
      attendanceYear: $attendanceYear
      formId: $formId
      flag: $flag
    ) {
      errorCode
      message
      employeeDetails {
        Employee_Id
        User_Defined_EmpId
        Presentational_Name
        Employee_Name
        Manager_Id
        Manager_User_Defined_EmpId
        Manager_Name
        __typename
      }
      attendanceDetails {
        Attendance_Date
        Early_Checkout_Hours
        Enable_Work_Place
        Comp_Off_Attendance_Balance
        Missed_Status
        Display_Punching_Date
        Gross_Hours
        Effective_Hours
        Arrival
        Late_By
        Break_Hours
        Message
        leaveCompOffObject {
          day
          totalCompOffDuration
          totalLeaveDuration
          leaveDetails {
            Reason
            Duration
            Start_Date
            End_Date
            Total_Days
            Hours
            Approval_Status
            Leave_Period
            Late_Attendance
            Attendance_Shortage
            Leave_Name
          }
          compensatoryOffDetails {
            Compensatory_Date
            Duration
            Period
            Approval_Status
            Worked_Date
          }
          shortTimeOffDetails {
            Short_Time_Off_Date
            Approval_Status
            Start_Date_Time
            End_Date_Time
            Total_Hours
            Reason
            Request_For
          }
          onDutyLeaveDetails {
            Reason
            Duration
            Start_Date
            End_Date
            Total_Days
            Hours
            Approval_Status
            Leave_Period
            Late_Attendance
            Attendance_Shortage
            Leave_Name
          }
          onDutyshortTimeOffDetails {
            Short_Time_Off_Date
            Approval_Status
            Start_Date_Time
            End_Date_Time
            Total_Hours
            Reason
            Request_For
          }
        }
        workscheduleHolidayInputs {
          Week_Off_Duration
          Check_In_Consideration_Time
          Check_Out_Consideration_Time
          Consideration_From
          Consideration_To
          Week_Off_Exist
          Title
          Holiday_Exist
          Twodays_Flag
          Regular_From
          Regular_To
          Business_Working_Days
          __typename
        }
        details {
          Attendance_Id
          Total_Hours
          Approver_Id
          Late_Attendance
          Checkin_Longitude
          Checkin_Address
          Checkout_Address
          Actual_Punch_In_Time
          Checkin_Work_Place_Id
          Checkout_Work_Place_Id
          Checkin_Work_Place
          Checkout_Work_Place
          Actual_PunchOut_Time
          Actual_Total_Hours
          Checkin_Latitude
          Checkout_Longitude
          Checkout_Latitude
          Late_Attendance_Hours
          Late_Attendance_Hours_From_Grace
          Always_Grace
          Approval_Status
          Added_By
          Checkin_Form_Source
          Checkout_Form_Source
          Checkin_Form_Source_Id
          Checkout_Form_Source_Id
          Checkin_Data_Source
          Checkout_Data_Source
          Approved_By
          Display_Late_Attendance
          PunchIn_Date
          PunchOut_Date
          AttendanceDate
          Added_On
          Updated_On
          Approved_On
          Mob_PunchIn_Date
          Mob_PunchOut_Date
          PunchIn_Time
          Display_PunchIn_Time
          Display_PunchOut_Time
          BreakHrs
          Is_Manager
          Auto_Short_Time_Off
          Added_By_Name
          Updated_By_Name
          Approver_Name
          Approver_User_Defined_EmpId
          Check_Out_Time_Buffer
          Grade_Id
          WorkSchedule_Id
          Display_Late_Attendance
          Display_Total_Hours_In_Minutes
          Late_Attendance_Time
          Attendance_PunchIn_Date
          Attendance_PunchOut_Date
          comments {
            Comment_Id
            Emp_Comment
            Approval_Status
            Type
          }
        }
      }
    }
  }
`;

export const LIST_ATTENDANCE_APPROVAL_RECORDS = gql`
  query listAttendanceApprovalRecords(
    $attendanceMonth: Int!
    $attendanceYear: Int!
    $formId: Int!
    $flag: String!
    $startDate: String
    $endDate: String
  ) {
    listAttendanceApprovalRecords(
      attendanceMonth: $attendanceMonth
      attendanceYear: $attendanceYear
      formId: $formId
      flag: $flag
      startDate: $startDate
      endDate: $endDate
    ) {
      errorCode
      message
      attendanceApprovalDetails {
        Attendance_Id
        Total_Hours
        Employee_Id
        User_Defined_EmpId
        Presentational_Name
        Approver_Id
        Late_Attendance
        Checkin_Longitude
        Actual_Punch_In_Time
        Checkin_Work_Place_Id
        Checkout_Work_Place_Id
        Checkin_Work_Place
        Checkout_Work_Place
        Actual_PunchOut_Time
        Attendance_PunchIn_Date
        Attendance_PunchOut_Date
        Actual_Total_Hours
        Display_Total_Hours_In_Minutes
        Checkin_Latitude
        Checkout_Longitude
        Enable_Work_Place
        Checkout_Latitude
        Arrival
        Late_Attendance_Hours
        Late_Attendance_Hours_From_Grace
        Always_Grace
        Approval_Status
        Added_By
        Checkin_Form_Source
        Checkout_Form_Source
        Checkin_Form_Source_Id
        Checkout_Form_Source_Id
        Checkin_Data_Source
        Checkout_Data_Source
        Approved_By
        Display_Late_Attendance
        PunchIn_Date
        PunchOut_Date
        AttendanceDate
        Grade_Id
        WorkSchedule_Id
        Added_On
        Updated_On
        Approved_On
        Mob_PunchIn_Date
        Mob_PunchOut_Date
        PunchIn_Time
        Display_PunchIn_Time
        Display_PunchOut_Time
        BreakHrs
        Emp_First_Name
        Emp_Last_Name
        Is_Manager
        Employee_Name
        Added_By_Name
        Updated_By_Name
        Approver_Name
        Grace_Time_Flag
        Check_Out_Time_Buffer
        Late_Attendance_Time
        Manager_Id
        Manager_User_Defined_EmpId
        Manager_Name
        Display_Punching_Date
        EmpType_Id
        Employee_Type
        Location_Name
        Location_Id
        Designation_Name
        Designation_Id
        Department_Name
        Department_Id
        Title
        Organization_Unit_Name
        Organization_Unit_Id
        Business_Unit_Id
        Business_Unit
        Auto_Short_Time_Off
        Checkin_Address
        Checkout_Address
        Early_Checkout_Hours
        comments {
          Comment_Id
          Emp_Comment
          Approval_Status
          Type
        }
      }
    }
  }
`;
