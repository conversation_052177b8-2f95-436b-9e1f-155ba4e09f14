import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const GET_LIST_LEAVES = gql`
  query listLeaves(
    $formId: Int!
    $leaveStartDate: Date!
    $leaveEndDate: Date!
    $offset: Int
    $limit: Int
  ) {
    listLeaves(
      formId: $formId
      leaveStartDate: $leaveStartDate
      leaveEndDate: $leaveEndDate
      offset: $offset
      limit: $limit
    ) {
      errorCode
      message
      totalCount
      leaveDetails
    }
  }
`;
export const LIST_LEAVE_TYPES = gql`
  query listLeaveTypes($formId: Int, $leaveTypeId: Int) {
    listLeaveTypes(formId: $formId, leaveTypeId: $leaveTypeId) {
      errorCode
      message
      leaveTypeDetails
    }
  }
`;
export const LIST_TIME_OFF_REPORTS = gql`
  query listTimeOffReports($formId: [Int!]!, $calledFrom: Int!) {
    listTimeOffReports(formId: $formId, calledFrom: $calledFrom) {
      errorCode
      message
      reportDetails
      commonFilterDetails
    }
  }
`;

export const GET_REPORT_FILTER_FIELDS = gql`
  query getReportFilterFields($reportId: Int!) {
    getReportFilterFields(reportId: $reportId) {
      errorCode
      message
      filterDetails
      filterFieldDefaults
    }
  }
`;
export const GET_REPORT_DETAILS = gql`
  query generateReportDetails($reportId: Int!, $filterValues: String!) {
    generateReportDetails(reportId: $reportId, filterValues: $filterValues) {
      errorCode
      message
      s3URL
    }
  }
`;
export const GET_REPORT_DEFINITIONS_LIST = gql`
  query getReportDefinitionsList($limit: Int, $offset: Int) {
    getReportDefinitionsList(limit: $limit, offset: $offset) {
      errorCode
      message
      totalRecords
      reportDefinitions {
        Report_Id
        Report_Title
        Report_Group_Name
        Form_Name
        Module_Name
        Custom_Report_Title
        Report_Visibility
        Report_Header_Id
        Report_Footer_Id
        Report_Type
        Default_Headers
        Custom_Headers
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const CLONE_AND_UPDATE_REPORT_DEFINITION = gql`
  mutation cloneAndUpdateReportDefinition(
    $action: String!
    $reportId: Int!
    $customReportTitle: String!
    $reportVisibility: String!
    $customHeaders: String!
    $reportHeaderId: Int
    $reportFooterId: Int
  ) {
    cloneAndUpdateReportDefinition(
      action: $action
      reportId: $reportId
      customReportTitle: $customReportTitle
      reportVisibility: $reportVisibility
      customHeaders: $customHeaders
      reportHeaderId: $reportHeaderId
      reportFooterId: $reportFooterId
    ) {
      errorCode
      message
    }
  }
`;
