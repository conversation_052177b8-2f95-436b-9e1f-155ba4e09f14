import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const LIST_ONBOARDED_CANDIDATES = gql`
  query listCandidateDetails {
    listCandidateDetails {
      errorCode
      message
      adminEmail
      listCandidates
    }
  }
`;
export const RETRIEVE_EMP_PROFILE_CARD_DETAILS = gql`
  query retrieveMyProfile($candidateId: Int!) {
    retrieveMyProfile(candidateId: $candidateId) {
      errorCode
      message
      employeeProfile {
        employeeName
        empEmail
        userDefinedEmpId
        candidateId
        designationId
        designationName
        departmentId
        departmentName
        empStatus
        mobileNo
        mobileNoCountryCode
        street
        city
        apartmentName
        state
        pinCode
        country
        photoPath
        useLocationAddress
        locationStreet1
        locationStreet2
        locationCity
        locationState
        locationPinCode
        locationCountry
      }
    }
  }
`;
export const RETRIEVE_EMP_PERSONAL_INFO = gql`
  query retrievePersonalInfo($candidateId: Int!) {
    retrievePersonalInfo(candidateId: $candidateId) {
      errorCode
      message
      personalInfoDetails
      dependentDetails
    }
  }
`;
export const RETRIEVE_EMP_JOB_INFO = gql`
  query retrieveJobInfo($candidateId: Int!) {
    retrieveJobInfo(candidateId: $candidateId) {
      errorCode
      message
      jobInfoDetails
      experienceDetails
      assetDetails
    }
  }
`;
export const RETRIEVE_EMP_CONTACT_INFO = gql`
  query retrieveContactInfo($candidateId: Int!) {
    retrieveContactInfo(candidateId: $candidateId) {
      errorCode
      message
      contactDetails
    }
  }
`;
export const RETRIEVE_EMP_CAREER_INFO = gql`
  query retrieveCareerInfo($candidateId: Int!) {
    retrieveCareerInfo(candidateId: $candidateId) {
      errorCode
      message
      awardDetails
      skillDetails
    }
  }
`;
export const RETRIEVE_EMP_DOCUMENT_ACCREDITATION_INFO = gql`
  query retrieveDocumentInfo($candidateId: Int!) {
    retrieveDocumentInfo(candidateId: $candidateId) {
      errorCode
      message
      educationalInfoDetails
      certificateInfoDetails
      trainingInfoDetails
      documentDetails
      accreditationDetails
      drivingLicenseDetails
      passportDetails
    }
  }
`;
export const RETRIEVE_EMP_OTHER_INFO = gql`
  query retrieveOtherInfo($candidateId: Int!) {
    retrieveOtherInfo(candidateId: $candidateId) {
      errorCode
      message
      bankDetails
      insuranceDetails
    }
  }
`;

export const RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION = gql`
  query retrieveProbationDate($designationId: Int!, $dateOfJoin: String!) {
    retrieveProbationDate(
      designationId: $designationId
      dateOfJoin: $dateOfJoin
    ) {
      errorCode
      message
      probationDate
    }
  }
`;

export const VALIDATE_FIELD_AVAILABILITY = gql`
  query validateCommonAvailability(
    $columnName: String!
    $columnValue: String!
    $employeeId: Int
    $tableName: String
  ) {
    validateCommonAvailability(
      employeeId: $employeeId
      columnValue: $columnValue
      columnName: $columnName
      tableName: $tableName
    ) {
      errorCode
      message
      isAvailable
    }
  }
`;

export const VALIDATE_BENEFIT_APPLICABLE_BASED_ON_EMP_TYPE = gql`
  query validateBenefitsApplicable($employeeTypeId: Int!) {
    validateBenefitsApplicable(employeeTypeId: $employeeTypeId) {
      errorCode
      message
      benefitsApplicable
    }
  }
`;

export const VALIDATE_MOBILE_NUMBER = gql`
  query validateMobileNumber(
    $employeeId: Int!
    $mobileNo: String!
    $mobileNoCountryCode: String!
  ) {
    validateMobileNumber(
      employeeId: $employeeId
      mobileNo: $mobileNo
      mobileNoCountryCode: $mobileNoCountryCode
    ) {
      errorCode
      message
      valid
    }
  }
`;
export const RETRIEVE_HIRING_MANAGER = gql`
  query getNewHireEmployeeDetails($candidateId: Int!) {
    getNewHireEmployeeDetails(candidateId: $candidateId) {
      errorCode
      message
      hiringManagerDetails
    }
  }
`;
// ===============
// Mutations
// ===============
export const ADD_UPDATE_PERSONAL_DETAILS = gql`
  mutation updatePersonalInfo(
    $candidateId: Int!
    $userDefinedEmpId: String
    $biometricIntegrationId: String
    $photoPath: String
    $salutation: String
    $empFirstName: String!
    $empMiddleName: String
    $empLastName: String!
    $appellation: String
    $knownAs: String
    $gender: String!
    $genderId: Int
    $genderIdentityId: Int
    $genderExpressionId: Int
    $dob: String!
    $placeOfBirth: String
    $maritalStatus: Int!
    $bloodGroup: String
    $languages: [language]
    $nationality: String!
    $nationalityId: Int!
    $militaryService: Int
    $religion: String
    $religionId: Int!
    $caste: String
    $disabled: Int
    $isManager: Int
    $personalEmail: String
    $smoker: Int
    $smokerAsOf: String
    $aadharNumber: String
    $uan: String
    $pan: String
    $formStatus: Int
    $allowUserSignIn: Int
    $enableMobileSignIn: Int
    $hobbies: String
    $genderOrientations: String
    $pronoun: String
    $statutoryInsuranceNumber: String
    $pranNo: String
    $taxCode: String
    $personalCustomField1: String
    $personalCustomField2: String
    $personalCustomField3: String
  ) {
    updatePersonalInfo(
      candidateId: $candidateId
      userDefinedEmpId: $userDefinedEmpId
      biometricIntegrationId: $biometricIntegrationId
      photoPath: $photoPath
      salutation: $salutation
      empFirstName: $empFirstName
      empMiddleName: $empMiddleName
      empLastName: $empLastName
      appellation: $appellation
      knownAs: $knownAs
      gender: $gender
      genderId: $genderId
      genderIdentityId: $genderIdentityId
      genderExpressionId: $genderExpressionId
      dob: $dob
      placeOfBirth: $placeOfBirth
      maritalStatus: $maritalStatus
      bloodGroup: $bloodGroup
      languages: $languages
      nationality: $nationality
      nationalityId: $nationalityId
      militaryService: $militaryService
      religion: $religion
      religionId: $religionId
      caste: $caste
      disabled: $disabled
      isManager: $isManager
      personalEmail: $personalEmail
      smoker: $smoker
      smokerAsOf: $smokerAsOf
      aadharNumber: $aadharNumber
      uan: $uan
      pan: $pan
      formStatus: $formStatus
      allowUserSignIn: $allowUserSignIn
      enableMobileSignIn: $enableMobileSignIn
      hobbies: $hobbies
      genderOrientations: $genderOrientations
      pronoun: $pronoun
      statutoryInsuranceNumber: $statutoryInsuranceNumber
      pranNo: $pranNo
      taxCode: $taxCode
      personalCustomField1: $personalCustomField1
      personalCustomField2: $personalCustomField2
      personalCustomField3: $personalCustomField3
    ) {
      errorCode
      message
      candidateId
    }
  }
`;

export const ADD_UPDATE_DEPENDENT_DETAILS = gql`
  mutation addUpdateDependentDetails(
    $candidateId: Int!
    $dependentId: Int
    $dependentFirstName: String!
    $dependentLastName: String!
    $gender: String!
    $genderId: Int
    $relationship: String!
    $dependentDOB: String!
  ) {
    addUpdateDependentDetails(
      candidateId: $candidateId
      dependentId: $dependentId
      dependentFirstName: $dependentFirstName
      dependentLastName: $dependentLastName
      gender: $gender
      genderId: $genderId
      relationship: $relationship
      dependentDOB: $dependentDOB
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_LICENSE_DETAILS = gql`
  mutation addUpdateDrivingLicenseDetails(
    $candidateId: Int!
    $drivingLicenseNo: String!
    $licenseIssueDate: String!
    $licenseExpiryDate: String!
    $issuingAuthority: String!
    $issuingCountry: String!
    $issuingState: String!
    $vehicleType: String
    $fileName: String
  ) {
    addUpdateDrivingLicenseDetails(
      candidateId: $candidateId
      drivingLicenseNo: $drivingLicenseNo
      licenseIssueDate: $licenseIssueDate
      licenseExpiryDate: $licenseExpiryDate
      issuingAuthority: $issuingAuthority
      issuingCountry: $issuingCountry
      issuingState: $issuingState
      vehicleType: $vehicleType
      fileName: $fileName
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_PASSPORT_DETAILS = gql`
  mutation addUpdatePassportDetails(
    $candidateId: Int!
    $passportNo: String!
    $passportIssueDate: String!
    $passportExpiryDate: String!
    $issuingAuthority: String!
    $issuingCountry: String!
    $fileName: String
  ) {
    addUpdatePassportDetails(
      candidateId: $candidateId
      passportNo: $passportNo
      passportIssueDate: $passportIssueDate
      passportExpiryDate: $passportExpiryDate
      issuingAuthority: $issuingAuthority
      issuingCountry: $issuingCountry
      fileName: $fileName
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_JOB_DETAILS = gql`
  mutation updateJobDetails(
    $Candidate_Id: Int!
    $Roles_Id: Int
    $Designation_Id: Int!
    $Department_Id: Int!
    $Location_Id: Int!
    $Job_Code: String
    $Emp_Email: String
    $Manager_Id: Int
    $Emp_Profession: Int
    $Confirmed: Int
    $Confirmation_Date: String
    $Probation_Date: String
    $EmpType_Id: Int!
    $Commission_Employee: Int
    $Work_Schedule: Int!
    $TDS_Exemption: Int
    $Attendance_Enforced_Payment: Int
    $Previous_Employee_Experience: Int
    $Service_Provider_Id: Int
    $Date_Of_Join: String!
    $Business_Unit_Id: Int
    $Pf_PolicyNo: String
    $Organization_Group_Id: Int
    $Job_Role_Ids: [Int]
  ) {
    updateJobDetails(
      Candidate_Id: $Candidate_Id
      Roles_Id: $Roles_Id
      Designation_Id: $Designation_Id
      Department_Id: $Department_Id
      Location_Id: $Location_Id
      Job_Code: $Job_Code
      Emp_Email: $Emp_Email
      Manager_Id: $Manager_Id
      Emp_Profession: $Emp_Profession
      Confirmed: $Confirmed
      Confirmation_Date: $Confirmation_Date
      Probation_Date: $Probation_Date
      EmpType_Id: $EmpType_Id
      Commission_Employee: $Commission_Employee
      Work_Schedule: $Work_Schedule
      TDS_Exemption: $TDS_Exemption
      Attendance_Enforced_Payment: $Attendance_Enforced_Payment
      Previous_Employee_Experience: $Previous_Employee_Experience
      Service_Provider_Id: $Service_Provider_Id
      Date_Of_Join: $Date_Of_Join
      Business_Unit_Id: $Business_Unit_Id
      Pf_PolicyNo: $Pf_PolicyNo
      Organization_Group_Id: $Organization_Group_Id
      Job_Role_Ids: $Job_Role_Ids
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_EXPERIENCE_DETAILS = gql`
  mutation addUpdateExperienceDetails(
    $candidateId: Int!
    $experienceId: Int
    $companyName: String!
    $companyLocation: String!
    $designation: String!
    $startDate: String!
    $endDate: String!
    $duration: String!
    $years: Int!
    $months: Int!
    $fileName: String
    $fileSize: String
    $referenceDetails: [candidateExperienceReference]
  ) {
    addUpdateExperienceDetails(
      candidateId: $candidateId
      companyName: $companyName
      experienceId: $experienceId
      companyLocation: $companyLocation
      designation: $designation
      startDate: $startDate
      endDate: $endDate
      duration: $duration
      years: $years
      months: $months
      fileName: $fileName
      fileSize: $fileSize
      referenceDetails: $referenceDetails
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_ASSET_DETAILS = gql`
  mutation addUpdateAssetDetails(
    $candidateId: Int!
    $assetId: Int
    $assetName: String!
    $serialNo: String!
    $receiveDate: String!
    $returnDate: String
  ) {
    addUpdateAssetDetails(
      candidateId: $candidateId
      assetId: $assetId
      assetName: $assetName
      serialNo: $serialNo
      receiveDate: $receiveDate
      returnDate: $returnDate
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_EDUCATION_DETAILS = gql`
  mutation addUpdateEducationDetails(
    $educationId: Int
    $candidateId: Int!
    $educationType: Int
    $specialisation: String
    $specializationId: Int
    $institutionId: Int
    $instituteName: String
    $university: String
    $yearOfPassing: Int
    $percentage: Float
    $grade: String
    $fileName: String
    $documentSubTypeId: Int
    $yearOfStart: Int
    $startDate: Date
    $endDate: Date
    $city: String
    $state: String
    $country: String
  ) {
    addUpdateEducationDetails(
      educationId: $educationId
      candidateId: $candidateId
      educationType: $educationType
      specialisation: $specialisation
      specializationId: $specializationId
      institutionId: $institutionId
      instituteName: $instituteName
      university: $university
      yearOfPassing: $yearOfPassing
      percentage: $percentage
      grade: $grade
      fileName: $fileName
      documentSubTypeId: $documentSubTypeId
      yearOfStart: $yearOfStart
      startDate: $startDate
      endDate: $endDate
      city: $city
      state: $state
      country: $country
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_CERTIFICATE_DETAILS = gql`
  mutation addUpdateCertificationDetails(
    $certificationId: Int
    $candidateId: Int!
    $certificationName: String!
    $receivedDate: String!
    $receivedFrom: String!
    $ranking: String
    $fileName: String
    $documentSubTypeId: Int
  ) {
    addUpdateCertificationDetails(
      certificationId: $certificationId
      candidateId: $candidateId
      certificationName: $certificationName
      receivedDate: $receivedDate
      receivedFrom: $receivedFrom
      fileName: $fileName
      documentSubTypeId: $documentSubTypeId
      ranking: $ranking
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_TRAINING_DETAILS = gql`
  mutation addUpdateTrainingDetails(
    $trainingId: Int
    $candidateId: Int!
    $trainingName: String!
    $trainingStartDate: String!
    $trainingEndDate: String!
    $trainingDuration: String!
    $trainer: String!
    $center: String!
    $fileName: String
    $documentSubTypeId: Int
  ) {
    addUpdateTrainingDetails(
      trainingId: $trainingId
      candidateId: $candidateId
      trainingName: $trainingName
      trainingStartDate: $trainingStartDate
      trainingEndDate: $trainingEndDate
      trainingDuration: $trainingDuration
      trainer: $trainer
      center: $center
      fileName: $fileName
      documentSubTypeId: $documentSubTypeId
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_SKILL_DETAILS = gql`
  mutation addUpdateSkillDetails(
    $candidateId: Int!
    $primarySkill: String
    $secondarySkill: String
    $knownSkills: String
  ) {
    addUpdateSkillDetails(
      candidateId: $candidateId
      primarySkill: $primarySkill
      secondarySkill: $secondarySkill
      knownSkills: $knownSkills
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_AWARD_DETAILS = gql`
  mutation addUpdateAwardDetails(
    $candidateId: Int!
    $awardId: Int
    $awardName: String!
    $receivedOn: String!
    $receivedFrom: String!
    $receivedFor: String!
  ) {
    addUpdateAwardDetails(
      candidateId: $candidateId
      awardId: $awardId
      awardName: $awardName
      receivedOn: $receivedOn
      receivedFrom: $receivedFrom
      receivedFor: $receivedFor
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_CONTACT_DETAILS = gql`
  mutation updateContactDetails(
    $candidateId: Int!
    $permanent_appartmentName: String!
    $permanent_streetName: String
    $permanent_city: String!
    $permanent_state: String!
    $permanent_country: String!
    $permanent_pinCode: String!
    $current_appartmentName: String!
    $current_streetName: String
    $current_city: String!
    $current_state: String!
    $current_country: String!
    $current_pinCode: String!
    $office_appartmentName: String!
    $office_streetName: String
    $office_city: String!
    $office_state: String!
    $office_country: String!
    $office_pinCode: String!
    $landlineNo: String
    $mobileNo: String!
    $mobileNoCountryCode: String!
    $useLocationAddress: Int!
    $faxNo: String
    $emergencyContactName: String
    $emergencyContactRelation: String
    $permanent_barangay: String
    $permanent_region: String
    $current_barangay: String
    $current_region: String
    $office_barangay: String
    $office_region: String
    $permanent_barangay_id: Int
    $current_barangay_id: Int
    $office_barangay_id: Int
    $permanent_city_id: Int
    $current_city_id: Int
    $office_city_id: Int
  ) {
    updateContactDetails(
      candidateId: $candidateId
      permanent_appartmentName: $permanent_appartmentName
      permanent_streetName: $permanent_streetName
      permanent_city: $permanent_city
      permanent_state: $permanent_state
      permanent_country: $permanent_country
      permanent_pinCode: $permanent_pinCode
      current_appartmentName: $current_appartmentName
      current_streetName: $current_streetName
      current_city: $current_city
      current_state: $current_state
      current_country: $current_country
      current_pinCode: $current_pinCode
      office_appartmentName: $office_appartmentName
      office_streetName: $office_streetName
      office_city: $office_city
      office_state: $office_state
      office_country: $office_country
      office_pinCode: $office_pinCode
      landlineNo: $landlineNo
      mobileNo: $mobileNo
      mobileNoCountryCode: $mobileNoCountryCode
      useLocationAddress: $useLocationAddress
      faxNo: $faxNo
      emergencyContactName: $emergencyContactName
      emergencyContactRelation: $emergencyContactRelation
      permanent_barangay: $permanent_barangay
      permanent_region: $permanent_region
      current_barangay: $current_barangay
      current_region: $current_region
      office_barangay: $office_barangay
      office_region: $office_region
      permanent_barangay_id: $permanent_barangay_id
      current_barangay_id: $current_barangay_id
      office_barangay_id: $office_barangay_id
      permanent_city_id: $permanent_city_id
      current_city_id: $current_city_id
      office_city_id: $office_city_id
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_ACCREDITATION_DETAILS = gql`
  mutation AddUpdateAccreditationDetails(
    $candidateId: Int!
    $accreditationDetailId: Int
    $accreditationCategoryAndType: Int!
    $receivedDate: String!
    $identifier: String
    $expiryDate: String!
    $fileName: String!
    $examRating: Int
    $examDateYear: Int
    $examDateMonth: String
    $dependentId: Int
  ) {
    addUpdateAccreditationDetails(
      candidateId: $candidateId
      accreditationDetailId: $accreditationDetailId
      accreditationCategoryAndType: $accreditationCategoryAndType
      receivedDate: $receivedDate
      identifier: $identifier
      expiryDate: $expiryDate
      fileName: $fileName
      examRating: $examRating
      examDateYear: $examDateYear
      examDateMonth: $examDateMonth
      dependentId: $dependentId
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_DOCUMENT_DETAILS = gql`
  mutation addUpdateCandidateDocuments(
    $documentId: Int
    $candidateId: Int!
    $documentName: String!
    $documentSubType: Int!
    $documentCategory: Int!
    $documentType: Int!
    $fileName: String!
    $fileSize: String
  ) {
    addUpdateCandidateDocuments(
      documentId: $documentId
      candidateId: $candidateId
      documentName: $documentName
      documentSubType: $documentSubType
      documentCategory: $documentCategory
      documentType: $documentType
      fileName: $fileName
      fileSize: $fileSize
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_BANK_DETAILS = gql`
  mutation AddUpdateBankDetails(
    $candidateId: Int!
    $bankId: Int
    $bankAccountNumber: String!
    $empBankId: Int!
    $branchName: String
    $ifsc: String
    $bsb: String
    $bank_streetName: String
    $bank_city: String
    $bank_state: String
    $bank_pinCode: String
    $accountType: Int!
    $creditAccount: String!
    $beneficiaryId: String
    $status: String!
    $bankAccountName: String
    $fileName: String
  ) {
    addUpdateBankDetails(
      candidateId: $candidateId
      bankId: $bankId
      bankAccountNumber: $bankAccountNumber
      empBankId: $empBankId
      branchName: $branchName
      ifsc: $ifsc
      bsb: $bsb
      bank_streetName: $bank_streetName
      bank_city: $bank_city
      bank_state: $bank_state
      bank_pinCode: $bank_pinCode
      accountType: $accountType
      creditAccount: $creditAccount
      beneficiaryId: $beneficiaryId
      status: $status
      bankAccountName: $bankAccountName
      fileName: $fileName
    ) {
      errorCode
      message
    }
  }
`;
export const ADD_UPDATE_INSURANCE_DETAILS = gql`
  mutation AddUpdateInsuranceDetails(
    $candidateId: Int!
    $policyId: Int
    $insuranceTypeId: Int!
    $policyNo: String!
    $insuranceType: String
  ) {
    addUpdateInsuranceDetails(
      candidateId: $candidateId
      policyId: $policyId
      insuranceTypeId: $insuranceTypeId
      policyNo: $policyNo
      insuranceType: $insuranceType
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_PROFILE_PICTURE_PATH = gql`
  mutation updateProfilePhoto($candidateId: Int!, $photoPath: String!) {
    updateProfilePhoto(candidateId: $candidateId, photoPath: $photoPath) {
      errorCode
      message
    }
  }
`;

export const UPDATE_CANDIDATE_STATUS = gql`
  mutation updateCandidateStatus(
    $candidateId: Int!
    $formId: Int!
    $status: String!
    $templateId: Int
  ) {
    updateCandidateStatus(
      candidateId: $candidateId
      formId: $formId
      status: $status
      templateId: $templateId
    ) {
      errorCode
      message
    }
  }
`;

export const CONVERT_CANDIDATE_TO_EMPLOYEE = gql`
  mutation convertCandidateToEmployee(
    $Candidate_Id: Int!
    $User_Defined_EmpId: String!
    $External_EmpId: String!
    $Timekeeping_Id: Int
    $Career_Id: Int
    $Roles_Id: Int
    $empPrefixSettingId: Int
    $Emp_Email: String
  ) {
    convertCandidateToEmployee(
      Candidate_Id: $Candidate_Id
      User_Defined_EmpId: $User_Defined_EmpId
      External_EmpId: $External_EmpId
      Timekeeping_Id: $Timekeeping_Id
      Career_Id: $Career_Id
      Roles_Id: $Roles_Id
      empPrefixSettingId: $empPrefixSettingId
      Emp_Email: $Emp_Email
    ) {
      errorCode
      message
    }
  }
`;

export const GET_SUN_FISH_DETAILS = gql`
  query retrieveOnboardingSettings($integrationType: String) {
    retrieveOnboardingSettings(integrationType: $integrationType) {
      errorCode
      message
      status
      Allow_Deployment_Notification_OnFailure
      Integration_Type
      Enable_Location_Auto_Prefill
    }
  }
`;

export const TRIGGER_SYNC_DETAILS = gql`
  mutation ($candidateId: Int!, $integrationType: String!) {
    sfaIntegrationEmployeeDetails(
      candidateId: $candidateId
      integrationType: $integrationType
    ) {
      message
      errorCode
      __typename
    }
  }
`;
