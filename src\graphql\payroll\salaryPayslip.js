import gql from "graphql-tag";

export const RETRIEVE_BIMONTHLY_PAYSLIP_LIST = gql`
  query ListSalaryPayslip(
    $formId: Int!
    $employeeId: Int
    $year: Int
    $isMonthly: Boolean
  ) {
    listSalaryPayslip(
      formId: $formId
      employeeId: $employeeId
      year: $year
      isMonthly: $isMonthly
    ) {
      errorCode
      message
      isSyntrumEnabled
      monthList
      externalApiSyncDetail {
        Integration_Type
        Sync_Direction
        Action
        Entity_Type
        Status
      }
      salaryPayslips {
        Payslip_Id
        S3_FileName
        Salary_Month
        Generated_On
        Generated_By
        Pay_Period
        Template_Id
        Basic_Salary
        Total_Earning
        Total_Deduction
        Total_Salary
        Payment_Status
        Formatted_Salary_Month
        Employee_Name
        Service_Provider_Id
        Location_Id
        Department_Id
        Manager_Id
        Designation_Id
        Designation_Name
        Department_Name
        Location_Name
        Incentive_Amount
        Eligible_For_Contractor_Tds
        Employee_Type
        EmpType_Id
        Organization_Unit_Name
        Formatted_Total_Salary
        User_Defined_EmpId
      }
    }
  }
`;
export const RETRIEVE_LIST_SALARY_PAYSLIP = gql`
  query ListSalaryPayslip(
    $formId: Int!
    $employeeId: Int
    $year: Int
    $isMonthly: Boolean
  ) {
    listSalaryPayslip(
      formId: $formId
      employeeId: $employeeId
      year: $year
      isMonthly: $isMonthly
    ) {
      errorCode
      message
      isSyntrumEnabled
    }
  }
`;
