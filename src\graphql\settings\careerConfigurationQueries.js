import gql from "graphql-tag";

// Query to get career configuration
export const RETRIEVE_GENERAL_SETTINGS = gql`
  query retrieveGeneralSettings($formId: Int) {
    retrieveGeneralSettings(formId: $formId) {
      errorCode
      message
      generalSettings {
        General_Setting_Id
        Page_Title
        Favicon_Filename
        Company_Logo
        Career_Logo_Path
        Use_Company_Logo_As_Product_Logo
        Primary_Color
        Secondary_Color
        Hover_Color
        Table_Header_Color
        Table_Header_Text_Color
        Career_Banner_Image
        Career_Headline_Text
        Career_Sub_Headline_Text
        Career_Text_Horizontal_Position
        Career_Text_Vertical_Position
        Career_Banner_Opacity
        Career_Headline_Font_Family
        Career_Heading_Font_Size
        Career_Headline_Font_Color
        Career_Sub_Headline_Font_Family
        Career_Sub_Headline_Font_Size
        Career_Sub_Headline_Font_Color
        Added_On
        Added_By
        Updated_On
        Updated_By
      }
    }
  }
`;
export const RETRIEVE_RECRUITMENT_SETTINGS = gql`
  query recruitmentSetting($formId: Int) {
    recruitmentSetting(formId: $formId) {
      errorCode
      message
      settingResult {
        Coverage
        Setting_Id
        Centralised_Recruitment
        Equal_opportunity_stmt
        auto_send_signed_document
        MPP_Integration
        Job_Title_Editable_Access
        Tenant_Unique_Key
        Candidate_Portal_Login_Access
        Candidate_Application_Form_Type
        Enable_Multiple_Application
        Blacklisted_Candidate_Portal_Access
        Archived_Candidate_Portal_Access
        Exp_portal_Expiry_Time_In_Mins
        Career_Portal_Filters
        Show_Job_Rounds_Tab
        Enable_New_Features
        Updated_By_Name
        Updated_On
      }
    }
  }
`;

// Mutation to update career configuration
export const ADD_UPDATE_GENERAL_SETTINGS = gql`
  mutation addUpdateGeneralSettings(
    $generalSettingId: Int
    $pageTitle: String
    $faviconFilename: String
    $companyLogo: String
    $useCompanyLogoAsProductLogo: String
    $careerLogoPath: String
    $primaryColor: String
    $secondaryColor: String
    $hoverColor: String
    $tableHeaderColor: String
    $tableHeaderTextColor: String
    $careerBannerImage: String
    $careerHeadlineText: String
    $careerSubHeadlineText: String
    $careerTextHorizontalPosition: String
    $careerTextVerticalPosition: String
    $careerBannerOpacity: Int
    $careerHeadlineFontFamily: String
    $careerHeadingFontSize: String
    $careerHeadlineFontColor: String
    $careerSubHeadlineFontFamily: String
    $careerSubHeadlineFontSize: String
    $careerSubHeadlineFontColor: String
    $formId: Int!
  ) {
    addUpdateGeneralSettings(
      generalSettingId: $generalSettingId
      pageTitle: $pageTitle
      faviconFilename: $faviconFilename
      companyLogo: $companyLogo
      useCompanyLogoAsProductLogo: $useCompanyLogoAsProductLogo
      careerLogoPath: $careerLogoPath
      primaryColor: $primaryColor
      secondaryColor: $secondaryColor
      hoverColor: $hoverColor
      tableHeaderColor: $tableHeaderColor
      tableHeaderTextColor: $tableHeaderTextColor
      careerBannerImage: $careerBannerImage
      careerHeadlineText: $careerHeadlineText
      careerSubHeadlineText: $careerSubHeadlineText
      careerTextHorizontalPosition: $careerTextHorizontalPosition
      careerTextVerticalPosition: $careerTextVerticalPosition
      careerBannerOpacity: $careerBannerOpacity
      careerHeadlineFontFamily: $careerHeadlineFontFamily
      careerHeadingFontSize: $careerHeadingFontSize
      careerHeadlineFontColor: $careerHeadlineFontColor
      careerSubHeadlineFontFamily: $careerSubHeadlineFontFamily
      careerSubHeadlineFontSize: $careerSubHeadlineFontSize
      careerSubHeadlineFontColor: $careerSubHeadlineFontColor
      formId: $formId
    ) {
      errorCode
      message
    }
  }
`;

export const ADD_UPDATE_RECRUITMENT_SETTINGS = gql`
  mutation addUpdateRecruitmentSetting(
    $coverage: String!
    $centralisedRecruitment: String!
    $equalOpportunityStatement: String
    $autoSendSignedDocument: String!
    $careerPageCaption: String
    $mppIntegration: String!
    $jobTitleEditableAccess: String
    $tenantUniqueKey: String
    $candidatePortalLoginAccess: String!
    $portalExpiryTimeInMins: Int!
    $candidateApplicationFormType: String!
    $enableMultipleApplication: String!
    $blacklistedCandidatePortalAccess: String!
    $archivedCandidatePortalAccess: String!
    $careerPortalFilters: String!
    $showJobRoundsTab: String!
    $enableNewFeatures: String!
  ) {
    addUpdateRecruitmentSetting(
      coverage: $coverage
      centralisedRecruitment: $centralisedRecruitment
      equalOpportunityStatement: $equalOpportunityStatement
      autoSendSignedDocument: $autoSendSignedDocument
      careerPageCaption: $careerPageCaption
      mppIntegration: $mppIntegration
      jobTitleEditableAccess: $jobTitleEditableAccess
      tenantUniqueKey: $tenantUniqueKey
      candidatePortalLoginAccess: $candidatePortalLoginAccess
      portalExpiryTimeInMins: $portalExpiryTimeInMins
      candidateApplicationFormType: $candidateApplicationFormType
      enableMultipleApplication: $enableMultipleApplication
      blacklistedCandidatePortalAccess: $blacklistedCandidatePortalAccess
      archivedCandidatePortalAccess: $archivedCandidatePortalAccess
      careerPortalFilters: $careerPortalFilters
      showJobRoundsTab: $showJobRoundsTab
      enableNewFeatures: $enableNewFeatures
    ) {
      errorCode
      message
      validationError
    }
  }
`;
