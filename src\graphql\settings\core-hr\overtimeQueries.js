import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const RETRIEVE_OVERTIME_CONFIG = gql`
  query retrieveOvertimeConfiguration($formId: Int) {
    retrieveOvertimeConfiguration(formId: $formId) {
      errorCode
      message
      OvertimeConfiguration {
        Custom_Group_Id
        Configuration_Id
        Wage_Factor
        Group_Name
        Salary_Type
        Special_Work_Days
        Status
        Overtime_Type
        Amount
        Added_On
        Added_By
        Updated_On
        Updated_By
      }
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_OVERTIME_CONFIG = gql`
  mutation addUpdateOvertimeConfiguration(
    $Configuration_Id: Int!
    $CustomGroup_Id: Int
    $Wage_Factor: Float
    $Salary_Type: String!
    $Special_Work_Days: String!
    $Status: String!
    $Overtime_Type: String!
    $overtimeFixedAmount: Float
    $formId: Int
  ) {
    addUpdateOvertimeConfiguration(
      Configuration_Id: $Configuration_Id
      CustomGroup_Id: $CustomGroup_Id
      Wage_Factor: $Wage_Factor
      Salary_Type: $Salary_Type
      Special_Work_Days: $Special_Work_Days
      Status: $Status
      Overtime_Type: $Overtime_Type
      overtimeFixedAmount: $overtimeFixedAmount
      formId: $formId
    ) {
      errorCode
      message
    }
  }
`;
