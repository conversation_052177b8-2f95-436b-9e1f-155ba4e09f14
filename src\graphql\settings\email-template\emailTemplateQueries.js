import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const LIST_CUSTOM_EMAIL_TEMPLATES = gql`
  query ListCustomEmailTemplates(
    $templateId: Int
    $categoryId: Int
    $categoryTypeId: Int
    $formId: Int
  ) {
    listCustomEmailTemplates(
      templateId: $templateId
      categoryId: $categoryId
      categoryTypeId: $categoryTypeId
      formId: $formId
    ) {
      errorCode
      message
      emailTemplates {
        Default_Template
        Template_Id
        Template_Name
        Template_Content
        Template_Fields
        Visibility
        Added_By_Name
        Updated_By_Name
        Category_Type_Name
        Category_Name
        Form_Id
        Category_Type_Id
        Category_Id
        Form_Name
        To_Emails
        CC_Emails
        Bcc_Emails
        Subject_Content
        Subject_Fields
        Additional_Emails
        Attachments
        Sender_Name
        External_Emails
        AttachmentsFileNames
        Added_On
        Added_By
        Updated_On
        Updated_By
      }
    }
  }
`;

export const LIST_EMAIL_TEMPLATE_PLACEHOLDER_VALUES = gql`
  query ListEmailTemplatePlaceHolderValues(
    $templateId: Int!
    $candidateId: Int
    $accessformId: Int!
  ) {
    listEmailTemplatePlaceHolderValues(
      templateId: $templateId
      candidateId: $candidateId
      accessformId: $accessformId
    ) {
      errorCode
      message
      jobCandidate {
        candidateFirstName
        candidateLastName
        candidateMiddleName
        initiatorFirstName
        initiatorMiddleName
        initiatorLastName
        jobTitle
        initiatorDesignation
        sourceOfApplication
        candidateStatus
        archiveReason
        interviewStartDate
        interviewEndDate
        interviewDate
        archiveReason
        companyName
        street1
        street2
        city
        state
        country
        pincode
        region
        barangay
        dateOfJoin
        initiatorEmployeeId
        initiatorDesignationCode
        organizationGroup
        areaDepartment
        sectionBranch
        sectionBranchCode
        immediateManagerFirstName
        immediateManagerMiddleName
        immediateManagerLastName
        secondLineManagerFirstName
        secondLineManagerMiddleName
        secondLineManagerLastName
        onboardingPassword
        onboardingLink
        companyLogoUrl
        organizationGroupCode
        OrganizationUnitName
        organizationUnitCode
        immediateManagerId
        secondLineManagerId
        areaDepartmentCode
        areaDepartmentName
        designationCode
        divisionCode
        divisionName
        groupCode
        groupName
        locationCode
        locationName
        initiatorMail
        initiatorMobileNumber
        employeeUserDefinedEmpId
        organizationName
        employeeFirstName
        employeeLastName
        employeeMiddleName
        designation
        regionDivision
        probationDate
      }
      emails {
        toEmails
        ccEmails
        bccEmails
        additionalEmails
      }
    }
  }
`;

export const GET_CLOUD_FRONT_URL = gql`
  query getCloudFrontUrl($fileName: String!) {
    getCloudFrontUrl(fileName: $fileName) {
      errorCode
      message
      url
    }
  }
`;

// ===============
// Mutations
// ===============

export const ADD_UPDATE_CUSTOM_EMAIL_TEMPLATE = gql`
  mutation AddUpdateCustomEmailTemplate(
    $templateId: Int!
    $templateName: String!
    $templateContent: String!
    $templateFields: [String]
    $categoryId: Int!
    $categoryTypeId: Int
    $defaultTemplate: String
    $formId: Int!
    $visibility: String!
    $toEmails: [String]
    $ccEmails: [String]
    $bccEmails: [String]
    $additionalEmails: [Int]
    $subjectContent: String!
    $subjectFields: [String]
    $attachedFiles: [String]
    $senderName: String
    $externalEmails: [String]
  ) {
    addUpdateCustomEmailTemplate(
      templateId: $templateId
      templateName: $templateName
      templateContent: $templateContent
      templateFields: $templateFields
      defaultTemplate: $defaultTemplate
      categoryId: $categoryId
      categoryTypeId: $categoryTypeId
      formId: $formId
      visibility: $visibility
      toEmails: $toEmails
      ccEmails: $ccEmails
      bccEmails: $bccEmails
      additionalEmails: $additionalEmails
      subjectContent: $subjectContent
      subjectFields: $subjectFields
      attachedFiles: $attachedFiles
      senderName: $senderName
      externalEmails: $externalEmails
    ) {
      errorCode
      message
    }
  }
`;
