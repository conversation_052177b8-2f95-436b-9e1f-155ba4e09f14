import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const RETRIEVE_ALLOWANCE_TYPE_LIST = gql`
  query ListAllowanceType($formId: Int!) {
    listAllowanceType(formId: $formId) {
      errorCode
      message
      allowanceTypes
    }
  }
`;

export const RETRIEVE_PERKS_LIST = gql`
  query getPerquisites($formId: Int!) {
    getPerquisites(formId: $formId) {
      errorCode
      message
      success
      perquisites
    }
  }
`;
export const RETRIEVE_BENEFITS_ASSOCIATION_LIST = gql`
  query GetBenefitForms($formId: Int!, $all: Boolean) {
    getBenefitForms(formId: $formId, all: $all) {
      errorCode
      message
      success
      benefitForms
    }
  }
`;

// ===============
// Mutations
// ===============
export const ADD_UPDATE_ALLOWANCE_TYPE = gql`
  mutation AddUpdateAllowanceType(
    $allowanceTypeId: Int
    $formId: Int!
    $allowanceName: String!
    $description: String
    $taxInclusion: String
    $formulaBased: String
    $allowanceMode: String
    $period: String
    $isFlexiBenefitPlan: String
    $isClaimFromReimbursement: String
    $allowanceTypeStatus: String
    $reimbursementType: String
    $unclaimedLTA: String
    $epfContribution: String
    $asIsPayment: String
    $restrictEmployeeFbpOverride: String
    $benefitAssociation: [Int]
    $isBasicPay: String
    $allowanceType: String
    $amount: Float
    $percentage: Float
    $fbpMaxDeclaration: Float
    $perquisitesId: Int
    $ltaStartYear: Int
    $endMonth: String
    $numberOfClaims: Int
    $allowanceSequence: Int
    $workflowId: Int
  ) {
    addUpdateAllowanceType(
      allowanceTypeId: $allowanceTypeId
      formId: $formId
      allowanceName: $allowanceName
      description: $description
      taxInclusion: $taxInclusion
      formulaBased: $formulaBased
      allowanceMode: $allowanceMode
      period: $period
      isFlexiBenefitPlan: $isFlexiBenefitPlan
      isClaimFromReimbursement: $isClaimFromReimbursement
      allowanceTypeStatus: $allowanceTypeStatus
      reimbursementType: $reimbursementType
      unclaimedLTA: $unclaimedLTA
      epfContribution: $epfContribution
      asIsPayment: $asIsPayment
      restrictEmployeeFbpOverride: $restrictEmployeeFbpOverride
      benefitAssociation: $benefitAssociation
      isBasicPay: $isBasicPay
      allowanceType: $allowanceType
      amount: $amount
      percentage: $percentage
      fbpMaxDeclaration: $fbpMaxDeclaration
      perquisitesId: $perquisitesId
      ltaStartYear: $ltaStartYear
      endMonth: $endMonth
      numberOfClaims: $numberOfClaims
      allowanceSequence: $allowanceSequence
      workflowId: $workflowId
    ) {
      errorCode
      message
      success
      allowanceTypeId
    }
  }
`;

export const DELETE_ALLOWANCE_TYPE = gql`
  mutation DeleteAllowanceType($allowanceTypeId: Int!, $formId: Int!) {
    deleteAllowanceType(allowanceTypeId: $allowanceTypeId, formId: $formId) {
      errorCode
      message
      success
      allowanceTypeId
    }
  }
`;
