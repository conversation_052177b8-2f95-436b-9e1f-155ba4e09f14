import gql from "graphql-tag";

// ===============
// Queries
// ===============

export const RETRIEVE_FORM24Q_LIST = gql`
  query listForm24Q(
    $formId: Int!
    $assessmentYear: Int
    $quarter: String
    $serviceProviderId: Int
  ) {
    listForm24Q(
      formId: $formId
      assessmentYear: $assessmentYear
      quarter: $quarter
      serviceProviderId: $serviceProviderId
    ) {
      errorCode
      message
      form24QList
    }
  }
`;

export const GENERATE_FORM24Q_TEXT_FILE = gql`
  mutation TriggerForm24Q(
    $assessmentYear: Int!
    $quarter: String!
    $serviceProviderId: Int
    $formId: Int!
  ) {
    triggerForm24Q(
      assessmentYear: $assessmentYear
      quarter: $quarter
      serviceProviderId: $serviceProviderId
      formId: $formId
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_FORM24Q = gql`
  mutation DeleteForm24Q($form24QId: Int!, $formId: Int!) {
    deleteForm24Q(form24QId: $form24QId, formId: $formId) {
      errorCode
      message
    }
  }
`;
