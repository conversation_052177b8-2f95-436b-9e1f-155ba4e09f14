import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_INSURANCE_RULES = gql`
  query retrieveInsuranceRules {
    retrieveInsuranceRules {
      errorCode
      message
      insuranceRulesData
      slabWiseData
    }
  }
`;

// ===============
// Mutations
// ===============

export const ADD_UPDATE_INSURANCE_RULES = gql`
  mutation addUpdateInsuranceRules(
    $insuranceTypeId: Int!
    $includeInArrearsCalculation: String
    $insuranceName: String!
    $slabWiseInsurance: String!
    $insuranceType: String
    $employerSharePercentage: Float
    $employeeSharePercentage: Float
    $employerShareAmount: Float
    $employeeShareAmount: Float
    $autoDeclaration: String
    $autoDeclarationApplicableFor: String
    $sectionInvestmentCategoryId: Int
    $overrideInsuranceContributionAtEmployeeLevel: String
    $paymentFrequency: String!
    $employeeStateInsurance: String
    $description: String
    $insuranceTypeStatus: String!
    $wageInclusion: String
    $proRateFixedInsurance: String
    $roundOffInsurance: String
  ) {
    addUpdateInsuranceRules(
      insuranceTypeId: $insuranceTypeId
      insuranceName: $insuranceName
      slabWiseInsurance: $slabWiseInsurance
      includeInArrearsCalculation: $includeInArrearsCalculation
      insuranceType: $insuranceType
      employerSharePercentage: $employerSharePercentage
      employeeSharePercentage: $employeeSharePercentage
      employerShareAmount: $employerShareAmount
      employeeShareAmount: $employeeShareAmount
      autoDeclaration: $autoDeclaration
      autoDeclarationApplicableFor: $autoDeclarationApplicableFor
      sectionInvestmentCategoryId: $sectionInvestmentCategoryId
      overrideInsuranceContributionAtEmployeeLevel: $overrideInsuranceContributionAtEmployeeLevel
      paymentFrequency: $paymentFrequency
      employeeStateInsurance: $employeeStateInsurance
      description: $description
      insuranceTypeStatus: $insuranceTypeStatus
      wageInclusion: $wageInclusion
      roundOffInsurance: $roundOffInsurance
      proRateFixedInsurance: $proRateFixedInsurance
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_INSURANCE_TYPE = gql`
  mutation DeleteInsuranceRules($insuranceTypeId: Int!) {
    deleteInsuranceRules(insuranceTypeId: $insuranceTypeId) {
      errorCode
      message
    }
  }
`;
