import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_PAYROLL_GENERAL_SETTINGS = gql`
  query retrievePayrollGeneralSettings {
    retrievePayrollGeneralSettings {
      errorCode
      message
      payrollGeneralSettingsData {
        Settings_Id
        Sync_Tax_Statutory_Compliance
        Payroll_Country_Name
        Payroll_Country
        Slab_Wise_PF
        Slab_Wise_NPS
        Include_Currency_Format_Based_On_Country
        Enable_Salary_Template
        Updated_On
        Updated_By
      }
    }
  }
`;
//mutation

export const UPDATE_PAYROLL_GENERAL_SETTINGS = gql`
  mutation updatePayrollsettings(
    $settingsId: Int!
    $payrollCountry: String
    $slabWisePF: String
    $slabWiseNPS: String
    $includeCurrencyFormatBasedOnCountry: String
    $enableSalaryTemplate: String!
  ) {
    updatePayrollsettings(
      settingsId: $settingsId
      payrollCountry: $payrollCountry
      slabWisePF: $slabWisePF
      slabWiseNPS: $slabWiseNPS
      enableSalaryTemplate: $enableSalaryTemplate
      includeCurrencyFormatBasedOnCountry: $includeCurrencyFormatBasedOnCountry
    ) {
      errorCode
      message
    }
  }
`;
