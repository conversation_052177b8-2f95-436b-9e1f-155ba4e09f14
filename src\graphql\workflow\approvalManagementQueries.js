import gql from "graphql-tag";
// ===============
// Queries
// ===============
export const LIST_USER_TASK_DETAIL = gql`
  query listUserTaskDetail(
    $formId: Int!
    $offset: Int
    $limit: Int
    $assigneeEmployeeId: Int!
    $taskFormIds: [Int!]!
    $customGroupIds: [Int]
    $startDate: Date
    $endDate: Date
    $action: String
  ) {
    listUserTaskDetail(
      formId: $formId
      offset: $offset
      limit: $limit
      assigneeEmployeeId: $assigneeEmployeeId
      taskFormIds: $taskFormIds
      customGroupIds: $customGroupIds
      startDate: $startDate
      endDate: $endDate
      action: $action
    ) {
      errorCode
      message
      data
    }
  }
`;
export const GET_WORK_FLOW_VIEW = gql`
  query ($taskId: String!, $leaveId: Int) {
    getWorkFlowView(taskId: $taskId, leaveId: $leaveId) {
      errorCode
      message
      details
    }
  }
`;
export const GET_EMPLOYEE_GROUP_IDS = gql`
  query getEmployeeGroupIds($employeeId: Int!) {
    getEmployeeGroupIds(employeeId: $employeeId) {
      errorCode
      message
      GroupIdArray
    }
  }
`;

export const GET_SERVICE_PROVIDER_EMPLOYEES = gql`
  query getServiceProviderEmployees($employeeId: Int!) {
    getServiceProviderEmployees(employeeId: $employeeId) {
      errorCode
      message
      employeeId
    }
  }
`;
export const GET_EMPLOYEE_GROUP_DATA = gql`
  query ($idArray: String!) {
    getEmployeeAndGroupData(idArray: $idArray) {
      errorCode
      message
      EmployeeAndGroupData
    }
  }
`;

export const APPROVAL_COUNT = gql`
  query ($myApprovals: Boolean, $fromApprovalManagement: Boolean) {
    listNotificationsInDashboard(
      myApprovals: $myApprovals
      fromApprovalManagement: $fromApprovalManagement
    ) {
      notificationList
      notificationCount
      leaveWorkflowEnabled
    }
  }
`;
export const GET_EMPLOYEE_LEAVE_DETAILS = gql`
  query retrieveLeaveDetails($leaveId: Int!) {
    retrieveLeaveDetails(leaveId: $leaveId) {
      errorCode
      message
      leaveDetails {
        Start_Date
        End_Date
        Total_Days
        Hours
        Contact_Details
        Employee_Id
        Approver_Id
        LeaveType_Id
        Alternate_Person
        Approval_Status
        Added_By
        Added_On
        Updated_By
        Updated_On
        Lock_Flag
        Leave_Period
        Reason_Id
        Late_Attendance
        Workflow_Status
        Workflow_Instance_Id
        Attendance_Shortage
        Approved_By
        Approved_On
        Leave_Type
        Employee_Name
        ESIC_Reason
        Duration
        Reason
      }
    }
  }
`;

export const GET_DYNAMIC_FORM_DETAILS = gql`
  query getWorkflowTaskDynamicFormDetails(
    $envelope: Envelope!
    $dynamicFormId: Int!
    $workflowTaskId: String!
  ) {
    getWorkflowTaskDynamicFormDetails(
      envelope: $envelope
      dynamicFormId: $dynamicFormId
      workflowTaskId: $workflowTaskId
    ) {
      error {
        code
        message
      }
      result {
        dynamicFormTemplates {
          templateName
          template
          conversational
        }
        dynamicFormResponse {
          formStatus
          formResponseId
          formResponse
          addedUserId
          addedUserName
          addedOn
          updatedUserId
          updatedUserName
          updatedOn
        }
      }
    }
  }
`;

export const GET_RESIGNATION_PAYSLIP_DETAILS = gql`
  query getResignationPayslipDetails($employeeId: Int!) {
    getResignationPayslipDetails(employeeId: $employeeId) {
      errorCode
      message
      payslipDetails {
        payslipGenerated
        salaryDate
        lastSalaryDate
        resignationDateOverride
      }
    }
  }
`;

export const GET_JOB_POST_DETAILS = gql`
  query CommentQuery($jobPostId: Int!, $employeeId: Int!, $action: String!) {
    retrieveJobPost(
      jobPostId: $jobPostId
      employeeId: $employeeId
      action: $action
    ) {
      errorCode
      message
      jobPostData {
        Client_Name
        Client_Id
        Currency_Code
        Job_Post_Name
        Functional_Area
        Functional_Area_Id
        Designation
        Designation_Id
        Min_Work_Experience
        Max_Work_Experience
        Job_Type
        Job_Type_Id
        Payment_Type
        Payment_Type_Id
        Currency
        Currency_Id
        Min_Payment_Frequency
        Max_Payment_Frequency
        No_Of_Vacancies
        Job_Description
        Posting_Date
        Closing_Date
        Job_Post_Status
        Status_Id
        Interview_Status
        Reason_For_Opening
        Expected_Joining_Date
        Priority
        Travel_Required
        Agency_Involved
        Job_Duration
        Workflow_Id
        Workflow_Name
        OtherWorkAuthorization
        Cooling_Period
        Required_Certification
        Required_Certification_Id
        Organization_Group_Id
        Organization_Group
        Other_Reason_For_Opening
        Pay_Type
        KeySkills {
          SkillDefinition_Id
          Title
        }
        JobLocations {
          Location_Id
          Location_Name
          City_Id
          State_Id
          Country_Code
        }
        Qualification {
          Qualification_Id
          Qualification
        }
        ReplacementFor {
          Employee_Id
          Employee_Name
        }
        WorkAuthorization {
          Work_Authorization_Id
          Work_Authorization_Name
        }
        Rounds {
          Round_Id
          Round_Name
        }
        Address
        No_Of_Male_Vacancies
        No_Of_Female_Vacancies
        Category_Id
        Category_Name
        Subcategory_Id
        Subcategory_Name
        Industry_Id
        Industry_Name
        Resource_Id_Male
        Resource_Id_Female
        Service_Provider_Id
        Service_Provider_Name
        Skill_Set
        Latitude
        Longitude
        workPlaceType
        jobLocationType
        customGroupId
        CustomGroupName
        Pincode
        Added_On
        addedByName
        Updated_On
        updatedByName
        Experience_Level
        Country_Code
        State_Id
        City_Id
        Organization_Structure_Id
        Country_Name
        State_Name
        City_Name
        Pos_Name
        Hiring_Manager_Name
        Hiring_Manager_Id
      }
    }
  }
`;

export const GET_RESIGNATION_DETAILS = gql`
  query getEmployeeResignation($envelope: Envelope!, $resignationId: Int!) {
    getEmployeeResignation(envelope: $envelope, resignationId: $resignationId) {
      error {
        code
        message
      }
      result {
        resignationId
        employeeId
        employeeName
        employeeGender
        employeePhotoPath
        employeeIsManager
        employeeDepartmentId
        employeeDepartmentName
        employeeDesignationId
        employeeDesignationName
        approverId
        approverName
        resignationStatus
        appliedDate
        resignationDate
        addedOn
        addedUserId
        addedUserName
        updatedOn
        updatedUserId
        updatedUserName
        employeeIdWithPrefix
        reasonId
        esicReasonName
        relievingReasonComment
        withdrawnCancellationComment
        isPayslipGenerated
      }
      relievingReasonDetails {
        reasonId
        esicReasonName
      }
    }
  }
`;
export const LIST_LEAVE_HISTORY = gql`
  query empLeaveHistory($employeeId: Int, $source: String) {
    empLeaveHistory(employeeId: $employeeId, source: $source) {
      errorCode
      message
      leaveHistory
    }
  }
`;
export const VALIDATE_LEAVE_REQUEST = gql`
  query validateLeaveRequest(
    $calledFrom: String!
    $formId: Int!
    $leaveDetails: [leaveRequestDetails]!
  ) {
    validateLeaveRequest(
      calledFrom: $calledFrom
      formId: $formId
      leaveDetails: $leaveDetails
    ) {
      errorCode
      message
      leaveValidationResult
    }
  }
`;
export const GET_STAFF_SCHEDULES_BY_DATE_RANGE = gql`
  query getStaffSchedulesByDateRange(
    $staffId: String!
    $fromDate: String!
    $toDate: String!
    $duration: String
    $leavePeriod: String
  ) {
    getStaffSchedulesByDateRange(
      staffId: $staffId
      fromDate: $fromDate
      toDate: $toDate
      duration: $duration
      leavePeriod: $leavePeriod
    ) {
      errorCode
      message
      allSchedules {
        eventDate
        schedules {
          id
          academicYear
          courseName
          day
          departmentName
          eventDate
          fromTime
          locationName
          programName
          sectionName
          semester
          slotDuration
          staffID
          staffName
          subject
          toTime
          type
        }
      }
      processedSchedules {
        eventDate
        isScheduled
        isScheduledPartially
        colorCode
      }
    }
  }
`;
// ===============
// Mutations
// ===============
export const UPDATE_DYNAMIC_FORM_DETAILS = gql`
  mutation updateDynamicFormDetail(
    $envelope: Envelope!
    $responseId: Int
    $taskId: String!
    $formData: String!
    $formStatus: String!
    $resignationId: Int!
    $workflowInstanceId: String!
  ) {
    updateDynamicFormDetail(
      envelope: $envelope
      responseId: $responseId
      taskId: $taskId
      formData: $formData
      formStatus: $formStatus
      resignationId: $resignationId
      workflowInstanceId: $workflowInstanceId
    ) {
      error {
        code
        message
      }
      result {
        success
        message
        data
        validation {
          code
          message
        }
      }
    }
  }
`;

export const UPDATE_RESIGNATION_DATES = gql`
  mutation updateResignationDate(
    $envelope: Envelope!
    $resignationId: Int!
    $resignationDate: Date!
    $appliedDate: Date!
  ) {
    updateResignationDate(
      envelope: $envelope
      resignationId: $resignationId
      resignationDate: $resignationDate
      appliedDate: $appliedDate
    ) {
      error {
        code
        message
      }
      result {
        success
        message
        data
        validation {
          code
          message
        }
      }
    }
  }
`;

export const UPDATE_RESIGNATION_RELIEVING_REASON_COMMENT = gql`
  mutation updateResignationReason(
    $envelope: Envelope!
    $resignationId: Int!
    $esicReason: String
    $reasonId: Int!
    $relievingReasonComment: String
  ) {
    updateResignationReason(
      envelope: $envelope
      resignationId: $resignationId
      esicReason: $esicReason
      reasonId: $reasonId
      relievingReasonComment: $relievingReasonComment
    ) {
      error {
        code
        message
      }
      result {
        success
        message
        data
        validation {
          code
          message
        }
      }
    }
  }
`;
