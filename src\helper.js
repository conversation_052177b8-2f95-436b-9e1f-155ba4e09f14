import store from "./store";
import moment from "moment";
import CryptoJ<PERSON> from "crypto-js";

export function getCookie(cname) {
  let name = cname + "=";
  let decodedCookie = decodeURIComponent(document.cookie);
  let ca = decodedCookie.split(";");
  for (let i = 0; i < ca.length; i++) {
    let c = ca[`${i}`];
    while (c.charAt(0) == " ") {
      c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
  return "";
}

//function to convert date to organization date formate
export function orgDateFormatter(date) {
  const { orgDateFormat } = store.state.orgDetails;
  if (date) {
    const day = moment(date);
    let format = orgDateFormat ? orgDateFormat : "YYYY/MM/DD";
    return day.format(format);
  } else {
    return "";
  }
}

// Parse the errorCodes that is thrown from BE using Apollo Server
export function getErrorCodes(errorList) {
  for (let err of errorList.graphQLErrors) {
    return err.extensions.code;
  }
}

//  Parse the errorCodes along with validation errorCodes that is thrown from BE using Apollo Server
export function getErrorCodesWithValidation(errors) {
  for (let err of errors.graphQLErrors) {
    return [
      err.extensions.code,
      err.extensions.validationError
        ? err.extensions.validationError // return whole validation json to use backend validation messages in ui presentation
        : null,
    ];
  }
}

//  Parse the errorCodes along with validation errorCodes that is thrown from BE using Apollo Server
export function getErrorCodesAndMessagesWithValidation(errors) {
  for (let err of errors.graphQLErrors) {
    return [
      err.extensions.code,
      err.message,
      err.extensions.validationError
        ? err.extensions.validationError // return whole validation json to use backend validation messages in ui presentation
        : null,
    ];
  }
}

// function to handle null values in presentation
export function checkNullValue(value) {
  if (!value) return "-";
  return value;
}

// remove newline and multiple spaces in a sentance
export function replaceSentanceWithoutExtraSpaceAndNewline(sentance) {
  if (sentance) {
    let modifiedSentance = sentance.replace(/(\r\n|\n|\r)/gm, " ");
    modifiedSentance.replace(/\s\s+/g, " ");
    return modifiedSentance;
  }
  return "";
}
export function generateRandomColor() {
  const array = new Uint8Array(3);
  window.crypto.getRandomValues(array); // Fill the array with secure random values
  const color =
    "#" +
    Array.from(array, (byte) => byte.toString(16).padStart(2, "0"))
      .join("")
      .toUpperCase();
  return color;
}
export function listInputValidations() {
  const inputValidations = [
    {
      Validation_Id: 1,
      Validation_Name: "Alphabet with Space",
      Regular_Expression: "^[a-zA-Z ]+$",
      Description: "Allows only alphabets and spaces.",
    },
    {
      Validation_Id: 2,
      Validation_Name: "Alphanumeric with Space",
      Regular_Expression: "^[a-zA-Z0-9 ]+$",
      Description: "Allows letters, numbers, and spaces.",
    },
    {
      Validation_Id: 3,
      Validation_Name: "Common Input Alpha Validation",
      Regular_Expression: "^[^0-9<>]+$",
      Description:
        "Allows only alphabets, spaces and special characters are allowed.",
    },
    {
      Validation_Id: 4,
      Validation_Name: "Common Input Alphanumeric Validation",
      Regular_Expression: "^[^<>]+$",
      Description:
        "Allows only alphanumeric characters, spaces and special characters are allowed.",
    },
    {
      Validation_Id: 6,
      Validation_Name: "Only Numbers",
      Regular_Expression: "^[0-9]+$",
      Description: "Allows only numeric digits.",
    },
    {
      Validation_Id: 5,
      Validation_Name: "All Inputs Allowed",
      Regular_Expression: "^.*$",
      Description: "All inputs are allowed.",
    },
  ];

  return inputValidations;
}

export function convertMonthToYearMonthsDays(monthValue) {
  if (monthValue && monthValue > 0) {
    // when month is less than one then it should definitely return days
    if (monthValue >= 1) {
      let years = 0,
        months = 0,
        days = 0,
        remainingMonth = 0;
      years = Math.floor(monthValue / 12);
      remainingMonth = monthValue % 12;
      months = Math.floor(remainingMonth);
      if (remainingMonth < 12) {
        let remainingDays = remainingMonth - months;
        days = Math.ceil(remainingDays * 30);
      }
      // forming label based on quantity to single/plural
      let yearLabel = years > 1 ? "Years" : "Year";
      let monthLabel = months > 1 ? "Months" : "Month";
      let dayLabel = days > 1 ? "Days" : "Day";
      // based on quantity forming the values with label
      let yearMsg = years > 0 ? years + " " + yearLabel + " " : "";
      let monthMsg = months > 0 ? months + " " + monthLabel + " " : "";
      let daysMsg = days > 0 ? days + " " + dayLabel : "";
      // final result
      return yearMsg + monthMsg + daysMsg;
    } else {
      let days = Math.ceil(monthValue * 30);
      let dayLabel = days > 1 ? "Days" : "Day";
      return days + " " + dayLabel;
    }
  } else return "-";
}

export function convertDaysToYearMonthsDays(days) {
  if (days && days > 0) {
    let duration = moment.duration(days, "days");
    let years = parseInt(duration.years());
    let months = parseInt(duration.months());
    let remainingDays = parseInt(duration.days());
    let msgArray = [];
    if (years) msgArray.push(years + " years");
    if (months) msgArray.push(months + " months");
    if (remainingDays) msgArray.push(remainingDays + " days");
    return msgArray.join(", ");
  } else return "-";
}

export function getMonthDifference(startDate, endDate) {
  const startMoment = moment(startDate);
  const endMoment = moment(endDate);
  const diffInMonths = endMoment.diff(startMoment, "months");
  return diffInMonths;
}

export function getDaysDifference(startDate, endDate) {
  const startMoment = moment(startDate);
  const endMoment = moment(endDate);
  const diffInDays = endMoment.diff(startMoment, "days");
  return diffInDays;
}

export function getYearDifference(startDate, endDate) {
  const startMoment = moment(startDate);
  const endMoment = moment(endDate);
  const diffInYears = endMoment.diff(startMoment, "years");
  return diffInYears;
}

// convert base64 to file object
export function base64ToFile(dataurl, filename) {
  const arr = dataurl.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n) {
    u8arr[n - 1] = bstr.charCodeAt(n - 1);
    n -= 1; // to make eslint happy
  }
  return new File([u8arr], filename, { type: mime });
}

export function extractNumberFromString(inputString) {
  const numberMatches = inputString.match(/\d+/);
  if (numberMatches && numberMatches.length > 0) {
    const extractedNumber = parseInt(numberMatches[0]);
    return extractedNumber;
  } else {
    return "";
  }
}

export function convertUTCToLocal(utcTime) {
  let format = store.state.orgDetails.orgDateFormat;
  const stillUTC = moment.utc(utcTime).toDate();
  return moment(stillUTC, "YYYY-MM-DD HH:mm:ss")
    .local()
    .format(format + " HH:mm:ss");
}
export function filterList(list, filterValue, filterColumn) {
  if (filterValue && filterValue !== "All") {
    let listData = [...list];
    return listData.filter((item) => {
      return item[`${filterColumn}`] === filterValue;
    });
  } else {
    return list;
  }
}

export function handleNetworkErrors(error) {
  let sCode = error.networkError.statusCode;
  let eCode = sCode.toString();
  let errorMessage = "";
  if (eCode === "403" || eCode === "404") {
    errorMessage = error.message;
  } else if (eCode === "502" || eCode === "504") {
    errorMessage =
      "Sorry, there was a problem processing your request due to a temporary issue. If the problem persists, feel free to contact our support team for assistance" +
      " - " +
      eCode;
  } else {
    errorMessage =
      "Sorry, there was a problem processing your request. Please contact the platform administrator" +
      " - " +
      eCode;
  }
  return errorMessage;
}

//filter a value in array of objects takes param as list,search keyword, array items on which need to search element
export function searchList(list, searchQuery, searchColumn) {
  if (searchQuery) {
    let searchTerm = searchQuery.toLowerCase();
    let listData = [...list];
    return listData.filter((item) => {
      return searchColumn.some((field) => {
        return item[`${field}`].toString().toLowerCase()?.includes(searchTerm);
      });
    });
  } else {
    return list;
  }
}
//function to compare two array of objects and return array with only modified records
export function compareAndReturnModifiedArray(
  originalList,
  modifiedList,
  findColumn = []
) {
  let columns;
  if (findColumn.length > 0) {
    columns = findColumn;
  } else {
    columns = Object.keys(originalList[0]).map((key) => key);
  }
  let modifiedArrayList = modifiedList.filter((item, index) => {
    if (originalList.length && modifiedList.length) {
      return columns.some((field) => {
        if (
          modifiedList[`${index}`][`${field}`] !==
          originalList[`${index}`][`${field}`]
        ) {
          return true;
        }
      });
    }
  });
  return modifiedArrayList;
}

export function decimalToHours(decimal, format = "hm") {
  var hours = Math.floor(decimal);
  var minutes = Math.round((decimal % 1) * 60);
  if (minutes === 60) {
    hours++;
    minutes = 0;
  }
  if (format == "hm") {
    return `${hours}h ${minutes.toString().padStart(2, "0")}m`;
  } else {
    return {
      hours: hours,
      minutes: minutes,
    };
  }
}

// this function will convert 3/3h/3h 40m these formats to decimal
export function convertToDecimal(
  input,
  regex = /^(?:([0-9]+)h\s*)?(?:([0-9]+)m\s*)?$/i
) {
  if (typeof input === "string" && regex) {
    // Sanitize input: Remove any characters that aren't digits, 'h', or 'm'
    const sanitizedInput = input.replace(/[^0-9hm\s]/gi, "");

    // Validate for numeric values directly
    if (/^\d+(\.\d+)?$/.test(sanitizedInput)) {
      return parseFloat(sanitizedInput);
    } else {
      let inputString = sanitizedInput.trim().toLowerCase();
      // Extract hours and minutes using regex
      const match = inputString.match(regex);

      if (!match) {
        return 0;
      }

      const hours = parseInt(match[1] || match[3] || 0, 10);
      const minutes = parseInt(match[2] || match[4] || 0, 10);

      const decimalHours = hours + minutes / 60;
      return decimalHours;
    }
  }
  return 0;
}

// Function to convert time to minutes
export function timeToMinutes(time) {
  const [hours, minutes] = time.split(":").map(Number);
  return hours * 60 + minutes;
}

// Function to convert minutes to decimal hours
export function minutesToDecimalHours(minutes) {
  return parseFloat(parseFloat(minutes) / 60).toFixed(2);
}

export function convertToBytes(sizeStr) {
  let bytes = 0;
  if (sizeStr) {
    // Convert the input string to lowercase to handle different case formats
    const lowerSizeStr = sizeStr.toLowerCase();
    // Extract the numeric value and the unit (KB or MB)
    const numericValue = parseFloat(lowerSizeStr);
    const unit = lowerSizeStr.slice(-2); // Extract last two characters
    // Determine the multiplier based on the unit
    if (unit === "kb") {
      bytes = numericValue * 1024; // 1 KB = 1024 bytes
    } else if (unit === "mb") {
      bytes = numericValue * 1024 * 1024; // 1 MB = 1024 KB = 1024 * 1024 bytes
    } else {
      bytes = numericValue;
    }
  }
  return bytes;
}

export function getCustomFieldName(fieldId, oldFieldName) {
  let customFormFields = store.state.customFormFields;
  if (customFormFields && Object.keys(customFormFields).length > 0) {
    if (customFormFields[fieldId]) {
      return customFormFields[fieldId].Field_Alias;
    }
  }
  return oldFieldName;
}

export function colorCode(code) {
  switch (code) {
    case 1:
      return "#fec5bb";
    case 2:
      return "#e8e8e4";
    case 3:
      return "#d8e2dc";
    case 4:
      return "#ffe5d9";
    case 5:
      return "#ffd7ba";
    case 6:
      return "#fec89a";
    case 7:
      return "#fff1e6";
    case 8:
      return "#d6e2e9";
    case 9:
      return "#e8d1c5";
    case 10:
      return "#fad2e1";
    default:
      return "#e2eafc";
  }
}
export function getOrigin() {
  if (window.location.origin) {
    return window.location.origin;
  } else {
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    const port = window.location.port;
    return port
      ? `${protocol}//${hostname}:${port}`
      : `${protocol}//${hostname}`;
  }
}

export function encryptObject(obj) {
  const iv = CryptoJS.lib.WordArray.random(16); // Generate random IV
  let orgCode = localStorage.getItem("orgCode");
  orgCode = orgCode ? orgCode : store.getters.orgCode;
  const paddedKey = padKey(orgCode); // Pad the key to the correct length
  const encrypted = CryptoJS.AES.encrypt(JSON.stringify(obj), paddedKey, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });

  return {
    iv: iv.toString(CryptoJS.enc.Hex), // Return IV as hex string
    encryptedData: encrypted.toString(), // Encrypted data in Base64
  };
}

export function padKey(key) {
  const keyUtf8 = CryptoJS.enc.Utf8.parse(key);
  // Ensure the key is 128 bits (16 bytes) long by slicing or padding
  const paddedKey = CryptoJS.lib.WordArray.create(
    keyUtf8.words.slice(0, 4),
    16
  ); // Use the first 16 bytes
  return paddedKey;
}
