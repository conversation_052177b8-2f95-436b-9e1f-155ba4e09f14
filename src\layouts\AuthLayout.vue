<template>
  <div class="fill-height">
    <v-system-bar
      v-if="systemBarOptions.length && !fetchError"
      id="system-bar"
      theme="dark"
      :color="systemBarColor"
      class="notify-alert-bar"
      :height="isMobileView && showTrialNotification ? 80 : 60"
      app
    >
      <span :style="isMobileView ? 'width:100%' : 'width: 90%'">
        <v-expand-transition>
          <div
            v-if="currentSystemBarOption === 'requestAccess'"
            class="sbar-alert-content"
          >
            <i class="fa fa-lock" aria-hidden="true"></i>
            <p
              v-if="windowWidth > 500"
              style="padding: 0.5em; margin: 0px; word-break: break-word"
            >
              {{ $t("authLayout.accessDeniedMessage") }}
            </p>
            <p
              v-else
              style="padding: 0.5em; margin: 0px; word-break: break-word"
            >
              {{ $t("authLayout.accessDeniedMessageMobile") }}
            </p>
            <v-btn
              id="requestModuleAccess"
              size="small"
              rounded="lg"
              color="white"
              :loading="isRequesting"
              @click="requestForModulesAccess('top-bar')"
            >
              {{ $t("authLayout.requestAccess") }}
            </v-btn>
          </div>
          <div
            v-if="currentSystemBarOption === 'txRegimeSelection'"
            class="sbar-alert-content"
          >
            <img
              alt="money-bag"
              :width="windowWidth > 500 ? 30 : 25"
              :height="windowWidth > 500 ? 30 : 25"
              :src="getMoneyBagImageUrl"
              :class="windowWidth > 500 ? 'ma-4' : 'ma-0'"
            />

            <p style="padding: 0.5em; margin: 0px; word-break: break-word">
              {{ $t("authLayout.taxRegimeMessage") }}
            </p>
            <v-btn
              id="compareTaxRegime"
              rounded="lg"
              size="small"
              color="white"
              @click="showTaxComparisonModal"
            >
              {{ $t("authLayout.taxRegimeButton") }}
            </v-btn>
          </div>
          <TrialNotification
            v-if="currentSystemBarOption === 'trialNotification'"
          ></TrialNotification>
        </v-expand-transition>
      </span>
      <span
        v-if="systemBarOptions.length > 1"
        class="font-weight-bold text-white text-h6"
      >
        <v-icon
          v-if="showPreviousButton"
          size="20"
          color="white"
          class="mr-2"
          @click="showPreviousSystemBarOption()"
        >
          fas fa-chevron-left
        </v-icon>
        {{ systemBarOptions.indexOf(currentSystemBarOption) + 1 }} /
        {{ systemBarOptions.length }}
        <v-icon
          v-if="showNextButton"
          size="20"
          color="white"
          class="ml-2"
          @click="showNextSystemBarOption()"
        >
          fas fa-chevron-right
        </v-icon>
      </span>
    </v-system-bar>
    <v-navigation-drawer
      v-if="!fetchError"
      v-model="appDrawerOpen"
      class="bg-primary h-100 mt-15"
      rail
      rail-width="106"
    >
      <v-list-item
        v-if="!isPaymentDueExceed && demoCompleted"
        id="org_product_icon"
        class="mb-3"
        :avatar="orgProductIcon"
      >
      </v-list-item>

      <perfect-scrollbar class="sidebar-scroll">
        <div
          v-if="!isPaymentDueExceed && demoCompleted && isRenderComponents"
          style="height: 90%"
        >
          <v-list bg-color="primary">
            <v-tooltip
              :text="$t('authLayout.accessRestricted')"
              location="right"
              max-width="400"
            >
              <template v-slot:activator="{ props }">
                <v-list-item
                  id="sidebar-menu-item"
                  v-bind="restrictOtherModulesExceptBilling ? props : ''"
                  :to="restrictOtherModulesExceptBilling ? '' : `/quick-menu`"
                  class="sidebar-menu-item my-2 text-white"
                  ripple
                  :class="activeModuleClass('Quick Menu')"
                  :style="
                    restrictOtherModulesExceptBilling
                      ? 'cursor: not-allowed; opacity: 0.5'
                      : ''
                  "
                  @mouseover="isHovering = true"
                  @mouseout="isHovering = false"
                >
                  <div class="d-flex flex-column align-center">
                    <i class="icon hr-quick-menu hrapp-icon-size"></i>
                    <span class="hrapp-menu-text">{{
                      $t("authLayout.quickMenu")
                    }}</span>
                  </div>
                </v-list-item>
              </template>
            </v-tooltip>
            <v-list-item
              v-if="dashboardType === 'HRMSDASHBOARD'"
              id="sidebar-menu-item"
              @click="dashboardRedirection()"
              class="sidebar-menu-item my-2 text-white"
              ripple
              :class="activeModuleClass('dashboard')"
              @mouseover="isHovering = true"
              @mouseout="isHovering = false"
            >
              <div class="d-flex flex-column align-center">
                <i class="icon hr-dashboard hrapp-icon-size"></i>
                <span class="hrapp-menu-text">{{
                  $t("authLayout.dashboard")
                }}</span>
              </div>
            </v-list-item>
            <v-menu
              v-for="menu in sidebarMenuList"
              :key="menu.moduleId"
              v-model="menuStates[menu.moduleId]"
              location="end"
              :open-on-hover="!isMobileView && !disableHover"
              :close-on-content-click="true"
              transition="slide-y-transition"
              min-width="230"
              max-width="230"
              max-height="400"
              z-index="10000"
            >
              <template v-slot:activator="{ props }">
                <v-list-item
                  v-if="
                    !restrictOtherModulesExceptBilling &&
                    menu.formList.length &&
                    showModule(menu.moduleName) &&
                    menu.moduleName !== 'Billing'
                  "
                  v-bind="props"
                  id="sidebar-menu-item"
                  class="sidebar-menu-item my-2 text-white"
                  ripple
                  :class="activeModuleClass(menu.moduleName)"
                  @mouseover="handleMenuHover(menu, true)"
                  @click="handleMenuClick(menu)"
                >
                  <div class="d-flex flex-column align-center">
                    <div
                      v-if="menu.moduleName === 'Benefits'"
                      class="row hrapp-module-new-label-outer"
                    >
                      <div
                        class="d-flex justify-center align-center hrapp-module-new-label"
                      >
                        New
                      </div>
                    </div>
                    <i
                      :class="
                        'icon hr-' +
                        formatModuleName(menu.moduleName) +
                        ' ' +
                        'hrapp-icon-size'
                      "
                    ></i>
                    <span class="hrapp-menu-text">{{
                      menu.translatedModuleName || menu.moduleName
                    }}</span>
                  </div>
                </v-list-item>
                <v-tooltip
                  v-if="
                    restrictOtherModulesExceptBilling &&
                    menu.formList.length &&
                    showModule(menu.moduleName) &&
                    menu.moduleName !== 'Billing'
                  "
                  :text="$t('authLayout.accessRestricted')"
                  location="right"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <v-list-item
                      id="sidebar-menu-item"
                      v-bind="props"
                      class="sidebar-menu-item my-2 text-white"
                      ripple
                      :class="activeModuleClass(menu.moduleName)"
                      :style="'cursor: not-allowed; opacity: 0.5'"
                      @mouseover="handleMenuHover(menu, true)"
                      @click="handleMenuClick(menu)"
                    >
                      <div class="d-flex flex-column align-center">
                        <div
                          v-if="menu.moduleName === 'Benefits'"
                          class="row hrapp-module-new-label-outer"
                        >
                          <div
                            class="d-flex justify-center align-center hrapp-module-new-label"
                          >
                            New
                          </div>
                        </div>
                        <i
                          :class="
                            'icon hr-' +
                            formatModuleName(menu.moduleName) +
                            ' ' +
                            'hrapp-icon-size'
                          "
                        ></i>
                        <span class="hrapp-menu-text">{{
                          menu.translatedModuleName || menu.moduleName
                        }}</span>
                      </div>
                    </v-list-item>
                  </template>
                </v-tooltip>

                <v-list-item
                  v-if="
                    menu.formList.length &&
                    menu.moduleName === 'Billing' &&
                    isAutoBilling &&
                    billingAdmin
                  "
                  id="sidebar-menu-item"
                  :href="baseUrl + 'in/billing'"
                  class="sidebar-menu-item text-white"
                  ripple
                  :class="activeModuleClass('Billing')"
                  @mouseover="isHovering = true"
                  @mouseout="isHovering = false"
                >
                  <div class="d-flex flex-column align-center">
                    <i class="icon hr-billing hrapp-icon-size"></i>
                    <span class="hrapp-menu-text">{{
                      menu.translatedModuleName || menu.moduleName
                    }}</span>
                  </div>
                </v-list-item>
              </template>

              <v-list
                v-if="
                  menu.formList &&
                  menu.formList.length > 0 &&
                  menu.moduleName !== 'Billing'
                "
                class="sidebar-submenu-scroll"
              >
                <div v-for="form in menu.formList" :key="form.formId">
                  <div v-if="isVue3LayoutForm(form.formId)">
                    <v-list-item
                      id="sub-menu"
                      class="sub-menu-list-item"
                      :class="{
                        'active-submenu-item': activeSubmodule === form.url,
                      }"
                      :to="
                        form.url === 'recruitment/careers'
                          ? '/careers'
                          : '/' + form.url
                      "
                      @click="
                        (event) => {
                          if (handleFirstMenuItemClick(form, menu)) {
                            event.preventDefault();
                            return;
                          }
                          onDisableHover();
                          closeAllMenus();
                        }
                      "
                      @mouseover="hoverSubmenu(true, menu.moduleName)"
                      @mouseout="hoverSubmenu(false, '')"
                    >
                      <template v-slot:prepend>
                        <div
                          :style="
                            activeSubmodule === form.url
                              ? 'margin: 0.3em;color:white'
                              : 'margin: 0.3em'
                          "
                        >
                          <i
                            :class="
                              'icon hr-' +
                              formIconNameFromMenu(
                                menu.moduleName,
                                form.formName
                              ) +
                              '-' +
                              formIconNameFromMenu(form.formName) +
                              ' ' +
                              'hrapp-icon-size'
                            "
                          ></i>
                        </div>
                      </template>
                      <v-list-item-title
                        :style="
                          activeSubmodule === form.url
                            ? 'font-size:14px;letter-spacing:1px;color:white'
                            : 'font-size:14px;letter-spacing:1px'
                        "
                        >{{ form.customFormName }}</v-list-item-title
                      >
                      <!-- <div
                      class="d-flex justify-center align-center text-white hrapp-module-new-label"
                    >
                      {{ formHighlightLabel(form.formName) }}
                    </div> -->
                    </v-list-item>
                  </div>
                  <div v-else-if="isPartnerLayoutForm(form)">
                    <v-list-item
                      v-if="
                        (form.formName !== 'Members' &&
                          form.formName !== 'Productivity Monitoring') ||
                        (form.formName === 'Productivity Monitoring' &&
                          pmSettingsAdmin) ||
                        (form.formName === 'Members' && membersAdmin)
                      "
                      id="partner-sub-menu"
                      class="sub-menu-list-item"
                      :class="{
                        'active-submenu-item': activeSubmodule === form.url,
                      }"
                      @click="
                        (event) => {
                          if (handleFirstMenuItemClick(form, menu)) {
                            event.preventDefault();
                            return;
                          }
                          redirectToPartnerPage(form.url);
                          closeAllMenus();
                        }
                      "
                      @mouseover="hoverSubmenu(true, menu.moduleName)"
                      @mouseout="hoverSubmenu(false, '')"
                    >
                      <template v-slot:prepend>
                        <div
                          :style="
                            activeSubmodule === form.url
                              ? 'margin: 0.3em;color:white'
                              : 'margin: 0.3em'
                          "
                        >
                          <i
                            :class="
                              'icon hr-' +
                              formIconNameFromMenu(menu.moduleName) +
                              '-' +
                              formIconNameFromMenu(form.formName) +
                              ' ' +
                              'hrapp-icon-size'
                            "
                          ></i>
                        </div>
                      </template>
                      <v-list-item-title
                        :style="
                          activeSubmodule === form.url
                            ? 'font-size:14px;letter-spacing:1px;color:white'
                            : 'font-size:14px;letter-spacing:1px'
                        "
                        >{{ form.customFormName }}</v-list-item-title
                      >
                    </v-list-item>
                  </div>
                  <v-list-item
                    v-else-if="
                      (form.formName !== 'Members' &&
                        form.formName !== 'Productivity Monitoring') ||
                      (form.formName === 'Productivity Monitoring' &&
                        pmSettingsAdmin) ||
                      (form.formName === 'Members' && membersAdmin)
                    "
                    id="sub-menu"
                    class="sub-menu-list-item"
                    :class="{
                      'active-submenu-item': activeSubmodule === form.url,
                    }"
                    :href="customHref(form)"
                    @click="
                      (event) => {
                        if (handleFirstMenuItemClick(form, menu)) {
                          event.preventDefault();
                        }
                        closeAllMenus();
                      }
                    "
                    @mouseover="hoverSubmenu(true, menu.moduleName)"
                    @mouseout="hoverSubmenu(false, '')"
                  >
                    <template v-slot:prepend>
                      <div
                        :style="
                          activeSubmodule === form.url
                            ? 'margin: 0.3em;color:white'
                            : 'margin: 0.3em'
                        "
                      >
                        <i
                          :class="
                            'icon hr-' +
                            formIconNameFromMenu(menu.moduleName) +
                            '-' +
                            formIconNameFromMenu(form.formName) +
                            ' ' +
                            'hrapp-icon-size'
                          "
                        ></i>
                      </div>
                    </template>
                    <v-list-item-title
                      :style="
                        activeSubmodule === form.url
                          ? 'font-size:14px;letter-spacing:1px;color:white'
                          : 'font-size:14px;letter-spacing:1px'
                      "
                      >{{ form.customFormName }}</v-list-item-title
                    >
                  </v-list-item>
                </div>
              </v-list>
            </v-menu>
            <div class="sidebar-extra-widgets"></div>
          </v-list>
        </div>
      </perfect-scrollbar>
    </v-navigation-drawer>
    <v-app-bar
      v-if="!fetchError"
      elevation="4"
      height="60"
      color="white"
      class="left-0 right-0 w-100 d-flex align-center"
      app
      style="z-index: 999"
    >
      <v-app-bar-nav-icon
        v-if="!appDrawerOpen"
        @click.stop="appDrawerOpen = !appDrawerOpen"
      ></v-app-bar-nav-icon>
      <div class="d-flex justify-space-between align-center ml-2">
        <!-- Image before organization name -->
        <v-img
          alt="Organization Logo"
          :src="organizationLogo"
          :style="logoStyle"
        />

        <!-- Organization name -->
        <!-- <v-toolbar-title class="hrapp-org-name-text">
          {{ organizationName }}
        </v-toolbar-title> -->
      </div>

      <v-spacer></v-spacer>
      <v-menu
        v-if="showLanguageSelector"
        location="bottom"
        transition="slide-y-transition"
        min-width="150"
        z-index="10000"
      >
        <template v-slot:activator="{ props }">
          <div class="ml-3" v-bind="props" style="cursor: pointer">
            <v-icon size="27" color="primary">fas fa-language</v-icon>
          </div>
        </template>
        <v-list class="pa-0" size="large">
          <v-list-item
            v-for="(lang, index) in availableLanguages"
            :key="index"
            :value="lang.code"
            :class="currentLanguage === lang.code ? 'bg-primary' : ''"
            class="d-flex align-center justify-space-between"
            @click="changeLanguage(lang.code, lang.name)"
          >
            <div>
              <span class="same-as-profile-name">{{
                showLanguageName(lang.name)
              }}</span>
            </div>
          </v-list-item>
        </v-list>
      </v-menu>
      <span
        v-if="
          dataSetupAccessRights === 1 && !isPaymentDueExceed && demoCompleted
        "
        class="data-setup-icon mr-2"
        style="color: rgb(var(--v-theme-primary)) !important"
        @click="redirectToDataSetup()"
      >
        <i
          :title="$t('authLayout.dataSetupDashboard')"
          class="hr-organization-organization-settings hrapp-datasetup-icon"
        ></i>
      </span>

      <v-btn
        v-if="
          partnerId &&
          partnerId !== '-' &&
          partnerId !== 'trulead' &&
          camuUrl &&
          camuUrl.length &&
          orgCode !== 'camuhr'
        "
        size="small"
        class="text-white ml-3"
        color="primary"
        variant="flat"
        :href="camuUrl"
      >
        {{ partnerId }}
      </v-btn>
      <template v-slot:append>
        <LayoutNotification
          class="ml-3"
          v-if="
            dashboardType !== 'EMPLOYEEMONITORINGDASHBOARD' &&
            !isPaymentDueExceed &&
            demoCompleted &&
            isRenderComponents
          "
        ></LayoutNotification>

        <LayoutAnnouncement
          class="ml-3"
          v-if="!isPaymentDueExceed && demoCompleted && isRenderComponents"
        ></LayoutAnnouncement>

        <v-menu
          :open-on-hover="!isMobileView"
          location="bottom"
          transition="slide-x-transition"
          min-width="150"
          z-index="10000"
        >
          <template v-slot:activator="{ props }">
            <div
              class="d-flex align-center mx-3"
              style="height: 100%"
              v-bind="props"
            >
              <v-avatar
                v-if="userDetails.employeePhotoPath"
                @click="profileRedirection()"
                size="40"
              >
                <img
                  style="width: 100%; height: auto"
                  :src="userDetails.employeePhotoPath"
                  alt="profile_pic"
                />
              </v-avatar>
              <v-avatar
                v-else
                color="primary"
                size="32"
                @click="profileRedirection()"
              >
                <v-icon theme="dark" size="16">fa fa-user</v-icon>
              </v-avatar>
              <span v-if="appDrawerOpen" class="mx-2 user-profile-name">{{
                userDetails.employeeFirstName
              }}</span>
            </div>
          </template>
          <v-list class="py-0">
            <v-list-item
              link
              class="profile-menu-highlight"
              @click="profileRedirection()"
            >
              <div class="profile-menu-item">
                <i class="fas fa-address-card"></i>
                <span class="ml-2">{{ $t("authLayout.profile") }}</span>
              </div>
            </v-list-item>
            <v-list-item
              v-if="!disableLogout"
              link
              class="profile-menu-highlight"
              @click="userLogout()"
            >
              <div class="profile-menu-item">
                <i class="fas fa-sign-out-alt"></i>
                <span class="ml-2">{{ $t("authLayout.logout") }}</span>
              </div>
            </v-list-item>
          </v-list>
        </v-menu>
      </template>
    </v-app-bar>

    <!-- App Content Design -->
    <v-main v-if="!fetchError && isRenderComponents" class="app-background">
      <AppSnackBar
        v-if="snackbarData.isOpen"
        :show-snack-bar="snackbarData.isOpen"
        :snack-bar-msg="snackbarData.message"
        :image-name="snackbarData.image"
        :snack-bar-type="snackbarData.type"
        @close-snack-bar="closeSnackbarAlert"
      >
      </AppSnackBar>
      <router-view v-slot="{ Component }">
        <transition
          v-if="!isPaymentDueExceed && demoCompleted"
          name="fade"
          mode="out-in"
        >
          <div id="layout-container">
            <component :is="Component" />
          </div>
        </transition>
      </router-view>

      <PaymentReminder
        v-if="isPaymentDueExceed && demoCompleted"
        :show-payment-exceed-card="isPaymentDueExceed"
        :payment-details="orgDemoPaymentDetails"
      ></PaymentReminder>
      <DemoBooking v-if="!demoCompleted"></DemoBooking>

      <v-btn
        v-show="fab"
        id="backToTop"
        v-scroll="onScroll"
        icon
        position="fixed"
        size="small"
        color="grey"
        style="right: 3px !important"
        @click="toTop"
      >
        <v-icon>keyboard_arrow_up</v-icon>
      </v-btn>
    </v-main>

    <!-- Footer Design -->
    <v-footer
      v-if="
        !fetchError &&
        domainDetails &&
        checkPartnerid &&
        isDashboard &&
        !isMobileView
      "
      inset
      app
      absolute
      color="grey-lighten-3"
      class="px-md-10"
      style="z-index: 999"
    >
      <v-row style="width: 100%" class="mt-1">
        <v-col cols="12">
          <v-divider></v-divider>
        </v-col>
        <v-col
          class="footer-link support-link mb-2 d-flex pa-0 justify-center text-caption font-weight-bold"
          cols="12"
          :class="
            footerDetails.copyRight && domainDetails.licensingDetails
              ? 'justify-center'
              : 'justify-end'
          "
        >
          <a
            :href="footerDetails.termsLink"
            rel="noopener noreferrer"
            target="_blank"
            class="ml-2 mr-2"
            >{{ $t("authLayout.termsOfUse") }}</a
          >
          |
          <a
            :href="footerDetails.privacyPolicyLink"
            rel="noopener noreferrer"
            target="_blank"
            class="ml-2"
            >{{ $t("authLayout.privacyPolicy") }}</a
          >
        </v-col>
      </v-row>
    </v-footer>

    <FullPageErrorScreen
      v-if="fetchError && demoCompleted && !isPaymentDueExceed"
      :button-text="$t('authLayout.retry')"
      :content="errorContent"
      image-name="layout/networkError"
      @button-click="retryFetching"
    >
    </FullPageErrorScreen>

    <ModuleAccessDeniedModel
      v-if="isNoAccessRights && demoCompleted && !isPaymentDueExceed"
      :user-name="userDetails.employeeFirstName"
      :open-alert-modal="isNoAccessRights"
      :secondary-button-text="$t('authLayout.skipForNow')"
      :modal-title="$t('authLayout.accessDeniedTitle')"
      :modal-content="$t('authLayout.accessDeniedContent')"
      image-name="layout/access-denied"
      :is-loading="isRequesting"
      @request-action="requestForModulesAccess('modal')"
      @skip-action="skipModuleRequestAccess"
    >
    </ModuleAccessDeniedModel>

    <AppLoading
      v-if="!isRenderComponents || isLoading || updateLanguageLoading"
    ></AppLoading>
    <AppWarningModal
      v-if="showCamuWarningAlert"
      :open-modal="showCamuWarningAlert"
      :confirmation-heading="$t('authLayout.camuIntegrationWarning')"
      :close-button-text="''"
      :accept-button-text="$t('authLayout.ok')"
      @accept-modal="showCamuWarningAlert = false"
    >
    </AppWarningModal>
  </div>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import Config from "@/config.js";
import supportsWebP from "supports-webp";
import {
  GET_DOMAIN_DETAILS_QUERY,
  GET_ORG_USER_DETAILS_QUERY,
  UPDATE_EMPLOYEE_LANGUAGE_PREFERENCE,
  GET_SIDEBAR_MENU_QUERY,
  REQUEST_ACCESS,
} from "@/graphql/layout/layoutQueries.js";
import {
  GET_FORM_FIELDS_NO_AUTH,
  RETRIEVE_PAYROLL_GENERAL_SETTINGS,
  RETRIEVE_LOGO_PATH,
} from "@/graphql/commonQueries";

import LayoutNotification from "./Notifications.vue";
import LayoutAnnouncement from "./Announcements.vue";
const TrialNotification = defineAsyncComponent(() =>
  import("./TrialNotification")
);
const DemoBooking = defineAsyncComponent(() => import("./DemoBooking"));
const PaymentReminder = defineAsyncComponent(() => import("./PaymentReminder"));
const FullPageErrorScreen = defineAsyncComponent(() =>
  import("@/views/pages/FullPageErrorScreen.vue")
);
const ModuleAccessDeniedModel = defineAsyncComponent(() =>
  import("@/components/custom-components/ModuleAccessDeniedModel")
);

export default defineComponent({
  name: "AuthLayout",
  components: {
    LayoutAnnouncement,
    LayoutNotification,
    TrialNotification,
    PaymentReminder,
    DemoBooking,
    FullPageErrorScreen,
    ModuleAccessDeniedModel,
  },
  data: () => ({
    currentLanguage: localStorage.getItem("language") || "en",
    availableLanguages: [
      { code: "en", name: "English" },
      // { code: "fr", name: "French" },
      // { code: "ja", name: "Japanese" },
      { code: "sp", name: "Spanish" },
    ],
    partnerId: window.$cookies.get("partnerid"),
    appDrawerOpen: null,
    fab: false,
    sidebarMenuList: [],
    domainDetails: "",
    domain: "",
    organizationName: "",
    organizationAddress: "",
    orgProductIcon: "",
    userDetails: "",
    fetchError: false,
    errorContent: "",
    isRenderComponents: false,
    activeModule: "",
    activeSubmodule: "",
    isHovering: false,
    hoveredMenu: "",
    isNoAccessRights: false, //to present no modules access rights popup
    showRequestAccess: false, // to show request access notification bar
    isPaymentDueExceed: false,
    demoCompleted: true,
    orgDemoPaymentDetails: {},
    isRequesting: false,
    currentSystemBarOption: null,
    mandatoryCallCount: 0,
    isLoading: false,
    updateLanguageLoading: false,
    disableLogout: false,
    camuUrl: "",
    showCamuWarningAlert: false,
    disableHover: false,
    payrollCountry: "",
    organizationLogo: "",
    menuStates: {}, // Track menu open/close states
  }),
  computed: {
    checkPartnerid() {
      const partnerid = String(window.$cookies.get("partnerid") || "")
        .toLowerCase()
        .trim();
      return !partnerid || partnerid === "-";
    },
    showLanguageSelector() {
      const path = this.$route.path.toLowerCase();
      return (
        path.includes("productivity-monitoring") ||
        path.includes("/data-loss-prevention/cxo-dashboard") ||
        path.includes("/data-loss-prevention/key-logger") ||
        path.includes("/data-loss-prevention/location-intelligence") ||
        path.includes("/core-hr/document-subtype") ||
        path.includes("/core-hr/accreditation-category-and-type") ||
        path.includes("/data-loss-prevention/internet-access-control") ||
        path.includes("/data-loss-prevention/location-tracking") ||
        path.includes("/data-loss-prevention/key-logging") ||
        path.includes("/data-loss-prevention/additional-screenshots") ||
        path.includes("/settings/core-hr/overtime") ||
        path.includes("/payroll/salary-payslip/bi-monthly")
      );
    },
    // showLanguageSelector() {
    //   // Get the current route path
    //   const currentPath = this.$route.path.toLowerCase();

    //   // Extract the module name from the path (first segment after the leading slash)
    //   const pathSegments = currentPath.split("/");
    //   // Skip empty first element if path starts with slash
    //   const moduleSegment = pathSegments.length > 1 ? pathSegments[1] : "";

    //   // Convert module segment to camelCase for checking in translations
    //   // e.g., 'data-loss-prevention' -> 'dataLossPrevention'
    //   const moduleName = moduleSegment.replace(/-([a-z])/g, (_, letter) =>
    //     letter.toUpperCase()
    //   );

    //   // Check if this module has translations in the i18n configuration
    //   return !!this.$i18n.messages.en[moduleName];
    // },
    logoStyle() {
      // Adjust the width and height as needed based on the image dimensions
      const width = 5;
      const height = 10;
      return {
        width: `${width}em`,
        height: `${height}em`,
      };
    },
    activeModuleClass() {
      return (moduleName) =>
        // check if any submodule is hovered
        this.hoveredMenu
          ? // if any submodule is hovered compare the module names and add bg for that
            this.hoveredMenu === moduleName
            ? "sub-menu-hover" +
              " " +
              (this.activeModule === this.formatModuleName(moduleName)
                ? "sidebar-active-menu"
                : "")
            : this.activeModule === this.formatModuleName(moduleName) // set active module class
            ? "sidebar-active-menu-not-hovered"
            : ""
          : this.isHovering // check any module is hovered
          ? this.activeModule === this.formatModuleName(moduleName) // remove the active module's bg class
            ? "sidebar-active-menu-not-hovered"
            : ""
          : this.activeModule === this.formatModuleName(moduleName) // set active module class
          ? "sidebar-active-menu"
          : "";
    },

    customHref() {
      return (form) => {
        if (form.url === "organization/special-wages") {
          return this.baseUrl + "organization/holidays";
        } else if (form.url === "employees/timesheet-hours") {
          return this.baseUrl + "employees/timesheets";
        } else if (form.url === "recruitment/careers") {
          return this.baseUrl + "v3/careers";
        } else if (form.url === "payroll/electronic-fund-transfer") {
          return (
            this.baseUrl +
            `v3/payroll/electronic-fund-transfer?payroll_country=${this.payrollCountry}`
          );
        } else if (form.url === "payroll/payroll-management") {
          if (this.$store.state.payrollUrl) {
            const userName = decodeURIComponent(
              window.$cookies.get("entomoUserName")
            );
            if (!userName) return this.$store.state.payrollUrl;
            return this.$store.state.payrollUrl + userName;
          } else {
            return this.baseUrl + "payroll/payroll-management";
          }
        } else if (
          form.formId === 370 &&
          this.$store.state.orgDetails.assetManagementRedirectionUrl
        ) {
          // Asset Management form with formId 370 - redirect to custom URL
          return this.$store.state.orgDetails.assetManagementRedirectionUrl;
        } else {
          return (
            this.baseUrl +
            (this.isVue2LayoutForm(form)
              ? "in/" + form.url
              : this.formURL(form.url))
          );
        }
      };
    },

    baseUrl() {
      return this.$store.getters.baseUrl;
    },

    footerDetails() {
      return this.domainDetails.domainDetails;
    },

    //screen size
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    //check view access rights for the employee to perform data setup
    dataSetupAccessRights() {
      let dataSetupDashboard = this.$store.getters.formAccessRights(
        "data-setup-dashboard"
      );
      if (dataSetupDashboard) {
        let checkInRights = dataSetupDashboard.accessRights;
        return checkInRights ? checkInRights.view : 0;
      } else {
        return 0;
      }
    },

    // form highlight label based on the form name
    formHighlightLabel() {
      return (formName) => (formName === "Workforce Analytics" ? "Beta" : "");
    },

    //based on request access show/hide value, to present it in top notification bar
    showRequestAccessNotification() {
      return this.$store.state.showRequestAccessNotification;
    },

    // show tax regime selection option
    showTaxRegimeSelection() {
      return this.$store.state.showTaxRegimeSelection;
    },

    //show/hide snackbar based on store data
    snackbarData() {
      return this.$store.getters.getSnackbarData;
    },

    domainName() {
      return this.$store.getters.domain;
    },

    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    // dashboard type chosen in plan
    dashboardType() {
      return this.$store.state.planDashboardType;
    },

    // current status of auto-billing plan
    planCurrentStatus() {
      return this.$store.state.autoBillingPlanCurrentStatus;
    },

    // show trial notifications based on plan current status
    showTrialNotification() {
      if (this.dashboardType === "EMPLOYEEMONITORINGDASHBOARD") {
        if (
          this.planCurrentStatus ===
            "trial-subscription-skipped-card-not-exist" ||
          this.planCurrentStatus ===
            "trial-subscription-expired-card-not-exist" ||
          this.planCurrentStatus === "active-subscription-expired" ||
          this.planCurrentStatus === "trial-subscription-not-expired-card-exist"
        ) {
          return true;
        }
        return false;
      }
      return false;
    },

    systemBarColor() {
      if (this.currentSystemBarOption === "trialNotification") {
        if (
          this.planCurrentStatus ===
            "trial-subscription-skipped-card-not-exist" ||
          this.planCurrentStatus === "trial-subscription-not-expired-card-exist"
        ) {
          return "blue";
        }
        return "red-lighten-1";
      }
      return "secondary";
    },

    // restrict other modules except billing module based on plan current status
    restrictOtherModulesExceptBilling() {
      return this.planCurrentStatus === "subscription-canceled";
    },
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    //import money bag image
    getMoneyBagImageUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/layout/money-bag.webp");
      else return require("@/assets/images/layout/money-bag.png");
    },
    //show the top alert bar notification only in dashboard path
    isDashboard() {
      if (
        // this.$route.meta.title === "Layout" ||
        this.$route.meta.title === "Dashboard"
      ) {
        return true;
      } else {
        return false;
      }
    },
    //show the top alert bar notification only in dashboard path
    isFullPageLayout() {
      if (this.$route.meta.isFullPageLayout === true) {
        return true;
      } else {
        return false;
      }
    },

    // list of items to show in system bar
    systemBarOptions() {
      let systemBarActions = [];
      // tax regime selection option only in dashboard page
      if (this.showTaxRegimeSelection && this.isDashboard) {
        systemBarActions.push("txRegimeSelection");
      }
      // request access notification in all pages
      if (this.showRequestAccessNotification) {
        systemBarActions.push("requestAccess");
      }
      // plan subscription notification in all pages
      if (this.showTrialNotification) {
        systemBarActions.push("trialNotification");
      }
      // return combined array to present it in one by one
      return systemBarActions;
    },

    // show / hide the system bar next button
    showNextButton() {
      if (this.systemBarOptions && this.systemBarOptions.length > 1) {
        if (
          this.currentSystemBarOption ===
          this.systemBarOptions[this.systemBarOptions.length - 1]
        ) {
          return false;
        } else {
          return true;
        }
      }
      return false;
    },

    // show / hide the system bar pre button
    showPreviousButton() {
      if (this.systemBarOptions && this.systemBarOptions.length > 1) {
        if (this.currentSystemBarOption === this.systemBarOptions[0]) {
          return false;
        } else {
          return true;
        }
      }
      return false;
    },

    isAutoBilling() {
      return this.$store.state.isAutoBilling;
    },

    // check admin rights for the members form
    membersAdmin() {
      let membersRights = this.$store.getters.formAccessRights("members");
      if (membersRights && membersRights.accessRights) {
        const { admin } = membersRights.accessRights;
        return admin === "admin";
      }
      return false;
    },

    pmSettingsAdmin() {
      let pmSettingsAccess = this.$store.getters.formAccessRights(
        "productivity-monitoring"
      );
      if (pmSettingsAccess && pmSettingsAccess.accessRights) {
        const { admin } = pmSettingsAccess.accessRights;
        return admin === "admin";
      }
      return false;
    },

    // check Billing form rights
    billingAdmin() {
      let billingFormRights = this.$store.getters.formAccessRights("billing");
      if (
        billingFormRights &&
        billingFormRights.accessRights &&
        billingFormRights.accessRights["admin"] === "admin"
      ) {
        return true;
      }
      return false;
    },

    // check whether the employee is an admin/employee
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isManager() {
      return this.$store.state.isManager;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },

    accessRightsBasedOnId() {
      return this.$store.getters.formIdBasedAccessRights;
    },

    myTeamAdmin() {
      let formAccess = this.accessRightsBasedOnId("243");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights["admin"] === "admin";
      } else {
        return false;
      }
    },

    showModule() {
      return (moduleName) => {
        if (moduleName === "Core HR") {
          return this.coreHrAdmin;
        } else if (moduleName === "My Team") {
          return this.isManager || this.myTeamAdmin;
        } else {
          return true;
        }
      };
    },

    coreHrAdmin() {
      let isCoreHrAdmin = this.$store.getters.checkAnyOneOfTheAdmin;
      return isCoreHrAdmin;
    },
  },
  watch: {
    //  watch router change
    $route() {
      // set the active module and submodule class
      this.setActiveModuleSubModule();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    organizationName() {
      const title = localStorage.getItem("pageTitle");
      const baseTitle = this.$route.meta.title;
      const orgName = this.organizationName;
      const suffix =
        title ||
        (this.domainName?.toLowerCase() === "cannyhr" ? "CANNYHR" : "HRAPP");

      document.title = `${baseTitle} - ${orgName} | ${suffix}`;
    },

    // watch systemBar options value, to set the current system bar option to present it in screen
    systemBarOptions: {
      immediate: true,
      handler(val) {
        if (val && val.length > 0) {
          this.currentSystemBarOption = val[0]; // if there are more than 1 to show, then by default show the first
        }
      },
    },

    // watch mandatory call count to render components on the page
    mandatoryCallCount(val) {
      this.renderComponents(val);
    },
    // watch for language changes to update the sidebar menu
    currentLanguage(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.retrieveSidebarMenu();
      }
    },
  },

  mounted() {
    if (
      this.partnerId &&
      (this.partnerId.toLowerCase() === "camu" ||
        this.partnerId.toLowerCase() === "ums")
    ) {
      this.$store.commit("UPDATE_PROJECT_LABEL", "Course");
    } else {
      this.$store.commit("UPDATE_PROJECT_LABEL", "Project");
    }
    this.getSlabWisePf();
    // set the active module and submodule class
    this.setActiveModuleSubModule();

    // call default mandatory endpoints in layout asynchronously
    if (!this.dashboardType) {
      this.getOrgPlanType();
    } else {
      this.mandatoryCallCount += 1;
    }

    this.getOrganizationBillingDetails();
    this.fetchOrganizationUserDetails();
    this.retrieveSidebarMenu();
    this.retrieveCustomLabelNames();
    // non-mandatory call
    this.retrieveDomainDetails();
    // to check whether the browser supports webp images
    this.checkSupportWebp();
    this.domain = Config.domain;
    //assigning  values in localStorage
    localStorage.setItem("domain", Config.domain);
    localStorage.setItem("productLogo", Config.productLogo);
    localStorage.setItem("clientipUrl", Config.ipAddressApi);
    localStorage.setItem("production", Config.production);
    // Fetch the organization logo dynamically
    this.retrieveOrganizationLogo();
  },

  //lifecycle hook to handle uncaught error in dashboard and its child component
  errorCaptured(err, vm, info) {
    // if (err && !err.message.includes("bottom-navigation")) {
    //   let url = window.location.href;
    console.error("layout error", err.message, vm, info);
    //   let msg =
    //     "Something went wrong while loading the layout data. Please try after some time.";
    //   if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
    //     msg = err + " " + info;
    //   }
    //   let snackbarData = {
    //     isOpen: true,
    //     message: msg,
    //     type: "warning",
    //   };
    //   this.showAlert(snackbarData);
    // }
    return false;
  },

  methods: {
    changeLanguage(langCode, langName = "") {
      if (this.currentLanguage !== langCode) {
        this.updateEmployeeLanguagePreference(langCode, langName);
      }
    },
    showLanguageName(langName) {
      if (langName?.toLowerCase() === "spanish") return "español";
      else return langName;
    },

    updateEmployeeLanguagePreference(langCode, langName) {
      this.updateLanguageLoading = true;
      this.$apollo
        .mutate({
          mutation: UPDATE_EMPLOYEE_LANGUAGE_PREFERENCE,
          client: "apolloClientC",
          variables: {
            employeeId: this.userDetails.employeeId,
            languagePreference: langName,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.updateEmpLanguagePreference &&
            response.data.updateEmpLanguagePreference.success &&
            !response.data.updateEmpLanguagePreference.errorCode
          ) {
            this.currentLanguage = langCode;
            localStorage.setItem("language", langCode);
            this.$i18n.locale = langCode;
            window.location.reload();
            this.retrieveSidebarMenu();
          } else {
            this.handleUpdateLanguagePreferenceError(
              response.data.updateEmpLanguagePreference?.errorCode || ""
            );
          }
          this.updateLanguageLoading = false;
        })
        .catch((error) => {
          this.handleUpdateLanguagePreferenceError(error);
        });
    },
    handleUpdateLanguagePreferenceError(err) {
      this.updateLanguageLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "update",
        isListError: false,
      });
    },

    async retrieveOrganizationLogo() {
      let vm = this;
      try {
        const response = await vm.$apollo.query({
          query: RETRIEVE_LOGO_PATH,
          client: "apolloClientBA",
          fetchPolicy: "no-cache",
        });

        if (
          response.data &&
          response.data.retrieveLogoPath &&
          !response.data.retrieveLogoPath.errorCode
        ) {
          const logoUrl = response.data.retrieveLogoPath.logoPath;
          if (logoUrl) {
            vm.organizationLogo = logoUrl;
          }
        }
      } catch (error) {
        vm.handleLogoError(error);
      }
    },

    handleLogoError(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        isListError: false,
      });
    },

    renderComponents(mandatoryCallCount) {
      // for (hrms + consent) or only hrms, we need default details(planType, modulesAndForms, orgDetails, billingDetails) alone. so it should be greater than or equals to 4
      if (mandatoryCallCount >= 4) {
        this.isRenderComponents = true;
        if (
          !this.camuUrl &&
          this.partnerId &&
          this.partnerId.toLowerCase() === "camu" &&
          this.isAdmin
        ) {
          this.showCamuWarningAlert = true;
        }
      }
    },
    showPreviousSystemBarOption() {
      let currentIndex = this.systemBarOptions.indexOf(
        this.currentSystemBarOption
      );
      this.currentSystemBarOption = this.systemBarOptions[currentIndex - 1];
    },

    showNextSystemBarOption() {
      let currentIndex = this.systemBarOptions.indexOf(
        this.currentSystemBarOption
      );
      this.currentSystemBarOption = this.systemBarOptions[currentIndex + 1];
    },

    // set the active module and submodule class
    setActiveModuleSubModule() {
      let pathParts = location.pathname.split("/");
      pathParts = pathParts.filter((x) => x !== "");
      let v3Index = pathParts.indexOf("v3");

      // if the url has length 3 then the 3rd param is the module name
      if (pathParts[pathParts.length - 1] === "quick-menu") {
        this.activeModule = "quick-menu";
      } else if (pathParts[pathParts.length - 1] === "billing") {
        this.activeModule = "billing";
      }
      // if the url has length 2 then its dashboard
      else if (pathParts.length <= 2) {
        this.activeModule = "dashboard";
      } else {
        this.activeModule = pathParts[v3Index + 1]; // set the active module
      }

      let activeSubmoduleUrl = "";
      if (this.activeModule === "dashboard") {
        activeSubmoduleUrl = "/in";
      } else if (this.activeModule === "quick-menu") {
        activeSubmoduleUrl = "/quick-menu";
      } else if (this.activeModule === "billing") {
        activeSubmoduleUrl = "/billing";
      } else {
        activeSubmoduleUrl =
          pathParts[v3Index + 1] + "/" + pathParts[v3Index + 2];
      }
      this.activeSubmodule = activeSubmoduleUrl;
    },

    // change the module name format
    formatModuleName(moduleName) {
      return moduleName.replace(/\s/g, "-").toLowerCase();
    },

    // return icon name from module and form name
    formIconNameFromMenu(name, formName = "") {
      let formatName = name;
      if (name === "My Team" && formName === "Pre Approval") {
        formatName = "Employee Self Service";
      }
      return formatName.trim().replace(/\s/g, "-").toLowerCase();
    },
    // check whether browser supports webp or not and save the value in store
    async checkSupportWebp() {
      if (await supportsWebP) {
        this.$store.commit("UPDATE_WEBP_SUPPORTS", true);
      } else {
        this.$store.commit("UPDATE_WEBP_SUPPORTS", false);
      }
    },
    //function to show tax regime amount comparison
    showTaxComparisonModal() {
      this.$store.commit("SHOW_TAX_COMPARISON_MODAL", true);
    },

    formURL(url) {
      let payrollModuleUnderTaxStatutoryModuleFormURLs = [
        "tax-and-statutory-compliance/fixed-health-insurance",
        "tax-and-statutory-compliance/insurance",
        "tax-and-statutory-compliance/labour-welfare-fund",
        "tax-and-statutory-compliance/perquisite-tracker",
        "tax-and-statutory-compliance/tax-declarations",
        "tax-and-statutory-compliance/tax-rules",
        "tax-and-statutory-compliance/tds-history",
      ];
      let taxFormIndex =
        payrollModuleUnderTaxStatutoryModuleFormURLs.indexOf(url);
      if (taxFormIndex > -1) {
        let taxFormUrl =
          payrollModuleUnderTaxStatutoryModuleFormURLs[taxFormIndex];
        taxFormUrl = taxFormUrl.split("/");
        taxFormUrl = taxFormUrl[1];
        return "payroll/" + taxFormUrl;
      } else if (url == "tax-and-statutory-compliance/form-downloads") {
        return "forms-manager/form-downloads";
      } else if (url == "tax-and-statutory-compliance/compliance-forms") {
        if (this.payrollCountry === "in") {
          return "forms-manager/compliance-forms";
        } else {
          return `v3/form-generator?payroll_country=${this.payrollCountry}`;
        }
      } else if (url == "tax-and-statutory-compliance/gratuity") {
        return "payroll/gratuity";
      } else if (url == "employee-self-service/organization-chart") {
        return "employees/organization-chart";
      } else {
        return url;
      }
    },

    // when any submenu is hovered
    hoverSubmenu(hoverVal, moduleName) {
      if (hoverVal) {
        this.hoveredMenu = moduleName;
      } else {
        this.hoveredMenu = "";
      }
    },

    onDisableHover() {
      this.disableHover = true;
      setTimeout(() => {
        this.disableHover = false;
      }, 1000);
    },

    // Close all menus
    closeAllMenus() {
      Object.keys(this.menuStates).forEach((moduleId) => {
        this.menuStates[moduleId] = false;
      });
    },

    // Handle menu hover with proper closing of other menus
    handleMenuHover(menu, isHovering) {
      if (isHovering) {
        // Close all other menus first
        Object.keys(this.menuStates).forEach((moduleId) => {
          if (moduleId !== menu.moduleId.toString()) {
            this.menuStates[moduleId] = false;
          }
        });

        // Open the current menu (if not disabled)
        if (!this.disableHover && !this.isMobileView) {
          this.menuStates[menu.moduleId] = true;
        }
      } else {
        // Close the current menu when mouse leaves
        this.menuStates[menu.moduleId] = false;
      }

      // Update general hover state
      this.isHovering = isHovering;
    },

    // Handle menu click to toggle menu state
    handleMenuClick(menu) {
      // Close all other menus first
      Object.keys(this.menuStates).forEach((moduleId) => {
        this.menuStates[moduleId] = false;
      });
      // Toggle the current menu
      this.menuStates[menu.moduleId] = true;
    },

    // while clicking retry in full-error screen we need to reload the page
    retryFetching() {
      window.location.reload();
    },

    //function to navigate the app on menu click
    isVue3LayoutForm(formId) {
      return this.$store.getters.checkForVue3LayoutForms(formId);
    },

    //function to navigate the app on menu click
    isVue2LayoutForm(form) {
      return this.$store.getters.checkForVue2LayoutForms(form.formName);
    },

    isPartnerLayoutForm(form) {
      return this.$store.getters.checkForPartnerLayoutForms(form.formName);
    },

    //clear user session
    userLogout() {
      this.$store.dispatch("clearUserLock");
    },

    profileRedirection() {
      this.$router.push("/employee-self-service/my-profile");
    },

    redirectToDataSetup() {
      window.location.href = this.baseUrl + "datasetup-dashboard/index";
    },

    // handle asset management form click with redirection
    handleFirstMenuItemClick(form, menu) {
      // Check if this is the Asset Management form (formId: 370) in Asset Management module (moduleId: 16)
      if (
        menu.moduleId === 16 &&
        form.formId === 370 &&
        this.$store.state.orgDetails.assetManagementRedirectionUrl
      ) {
        // Redirect to the asset management URL
        window.location.href =
          this.$store.state.orgDetails.assetManagementRedirectionUrl;
        return true; // Indicate that redirection was handled
      }
      return false; // Indicate that normal navigation should proceed
    },

    // function to scroll top
    toTop() {
      this.$vuetify.goTo(0);
    },

    onScroll(e) {
      if (typeof window === "undefined") return;
      const top = window.pageYOffset || e.target.scrollTop || 0;
      this.fab = top > 20;
    },

    // get organization subscribed plan as HRMS or EMPLOYEEMONITORINGDASHBOARD
    async getOrgPlanType() {
      await this.$store.dispatch("getPlanType").then((dashboardType) => {
        this.$store.commit("UPDATE_PLAN_DASHBOARD_TYPE", dashboardType);
        this.mandatoryCallCount += 1;
      });
    },

    // get organization billing(auto/manual) details
    getOrganizationBillingDetails() {
      this.$store
        .dispatch("getOrganizationSubscriptionDetails")
        .then((billingDetails) => {
          // auto billing details
          if (billingDetails.isAutoBilling) {
            const { currentStatus } = billingDetails;
            // when the subscription status is any of below and the user is not in billing page, then we redirect them to billing module
            if (
              currentStatus &&
              this.$route.name !== "Billing" &&
              currentStatus === "subscription-canceled"
            ) {
              window.location.href = this.baseUrl + "in/billing";
            }
          }
          // manual billing details
          else {
            const { demoCompleted, isPaymentDueExceed, orgDemoPaymentDetails } =
              billingDetails;
            this.demoCompleted = demoCompleted;
            this.isPaymentDueExceed = isPaymentDueExceed;
            this.orgDemoPaymentDetails = orgDemoPaymentDetails;
          }
          this.mandatoryCallCount += 1;
        })
        .catch(() => {
          this.mandatoryCallCount += 1;
        });
    },

    // fetch domain details
    retrieveDomainDetails() {
      let self = this;
      try {
        self.$apollo
          .query({
            query: GET_DOMAIN_DETAILS_QUERY,
            client: "apolloClientC",
          })
          .then((domainResponse) => {
            self.domainDetails = domainResponse.data.getDomainDetails;
            if (self.domainDetails) {
              self.$store.commit(
                "UPDATE_CHARGEBEE_SITE_NAME",
                self.domainDetails.domainDetails.paymentPortalSiteName
              );
            }
          })
          .catch(() => {
            self.domainDetails = "";
          });
      } catch {
        self.domainDetails = "";
      }
    },

    // fetch custom labels
    retrieveCustomLabelNames() {
      let self = this;
      try {
        self.$apollo
          .query({
            query: GET_FORM_FIELDS_NO_AUTH,
            client: "apolloClientAS",
            variables: {
              form_Id: 0,
            },
          })
          .then((fieldDetails) => {
            let { formFields } = fieldDetails.data.getFormFeildsByFormIdAndTab;
            const formedFields = formFields.reduce((acc, curr) => {
              const { Field_Id, ...rest } = curr;
              acc[Field_Id] = rest;
              return acc;
            }, {});
            if (formedFields) {
              self.$store.commit("UPDATE_CUSTOM_FIELDS", formedFields);
            }
          })
          .catch(() => {
            self.$store.commit("UPDATE_CUSTOM_FIELDS", []);
          });
      } catch {
        self.$store.commit("UPDATE_CUSTOM_FIELDS", []);
      }
    },

    // fetch organization and user details
    fetchOrganizationUserDetails() {
      let self = this;
      try {
        self.$apollo
          .query({
            query: GET_ORG_USER_DETAILS_QUERY,
            client: "apolloClientC",
          })
          .then((orgUserData) => {
            let organizationData = orgUserData.data.getOrganizationUserDetails;
            self.$store.commit("UPDATE_ORGANIZATION_DETAILS", organizationData);
            self.organizationName = organizationData.organizationName;
            self.orgProductIcon = organizationData.productIconPath;
            self.userDetails = organizationData.userDetails;
            self.$store.commit("UPDATE_USER_DETAILS", self.userDetails);
            const syncType = String(organizationData.entomoSyncType || "")
              .toLowerCase()
              .trim();
            self.$store.commit("Entomo_Sync_Type_Push", syncType === "push");
            self.$store.commit(
              "UPDATE_PAYROLL_MANAGEMENT_URL",
              organizationData.payRollIntegrationUrl
            );
            self.disableLogout = organizationData.disableLogout === "Yes";
            self.camuUrl = organizationData.camuBaseUrl;
            self.appTrackingMode = organizationData.monitoringType
              ? organizationData.monitoringType.toLowerCase()
              : null;
            self.$store.commit(
              "UPDATE_APP_TRACKING_MODE",
              self.appTrackingMode
            );
            self.mandatoryCallCount += 1;
            if (organizationData?.userDetails?.languagePreference) {
              let availableLanguages = this.availableLanguages.find(
                (lang) =>
                  lang.name ===
                  organizationData?.userDetails?.languagePreference?.trim()
              );
              if (
                availableLanguages &&
                availableLanguages.code !== this.currentLanguage
              )
                this.changeLanguage(
                  availableLanguages.code,
                  availableLanguages.name
                );
            }
          })
          .catch((fetchOrganizationError) => {
            self.resetOrganizationDetails();
            if (
              fetchOrganizationError &&
              fetchOrganizationError.graphQLErrors[0]
            ) {
              // error capture
              var errorParams = fetchOrganizationError.graphQLErrors[0];
              var error = JSON.parse(errorParams.message);
              var empId = error.employeeId;
              self.$store.commit("UPDATE_ORGANIZATION_DETAILS", {
                organizationName: "",
                orgDateFormat: "YYYY/MM/DD", //by default we have this format.Change based on org format
                assessmentYear: "", // assessment year of the organization
                paycycle: "",
                employeeId: empId,
                advancePayroll: "",
                employeeEdit: false,
                fieldForce: false,
                autoUpdateEffectiveDateForJobDetails: "No",
                organizationAddress: "",
              });
            }
          });
      } catch {
        self.resetOrganizationDetails();
      }
    },
    async getSlabWisePf() {
      this.$apollo
        .query({
          query: RETRIEVE_PAYROLL_GENERAL_SETTINGS,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listPayrollGeneralSettings &&
            response.data.listPayrollGeneralSettings
              .listPayrollGeneralSettingsData
          ) {
            this.payrollCountry =
              response.data.listPayrollGeneralSettings.listPayrollGeneralSettingsData[0]?.Country_Code?.toLowerCase();
            let payrollCurrency =
              response.data.listPayrollGeneralSettings
                .listPayrollGeneralSettingsData[0]?.Payroll_Currency || "";
            let templateEnabled =
              response.data.listPayrollGeneralSettings
                .listPayrollGeneralSettingsData[0]?.Enable_Salary_Template;
            this.$store.commit("UPDATE_PAYROLL_CURRENCY", payrollCurrency);
            this.$store.commit("UPDATE_PAYROLL_COUNTRY", this.payrollCountry);
            this.$store.commit("UPDATE_TEMPLATE_ENABLED", templateEnabled);
          }
        });
    },
    resetOrganizationDetails() {
      this.$store.commit("UPDATE_ORGANIZATION_DETAILS", {});
      this.$store.commit("UPDATE_USER_DETAILS", {});
      this.organizationName = "";
      this.orgProductIcon = "";
      this.userDetails = "";
      this.mandatoryCallCount += 1;
    },
    dashboardRedirection() {
      let redirectToV3Dashboard = [
        "fieldforce",
        "capricetest",
        "adeera",
        "hmcgroupuat",
        "capricecloud",
      ];
      if (redirectToV3Dashboard.includes(this.orgCode)) {
        this.$router.push("/");
      } else {
        window.location.href = this.baseUrl + "in/";
      }
    },

    // fetch modules and forms to list in sidebar
    retrieveSidebarMenu() {
      let self = this;
      // Map frontend language codes to backend language codes
      // Frontend uses 'sp' for Spanish while backend expects 'es'
      const backendLanguageCode =
        self.currentLanguage === "sp" ? "es" : self.currentLanguage;

      self.$apollo
        .query({
          query: GET_SIDEBAR_MENU_QUERY,
          client: "apolloClientC",
          variables: {
            languageCode: backendLanguageCode,
          },
        })
        .then((menuResponse) => {
          const { listModulesAndForms, errorCode } = menuResponse.data;
          if (listModulesAndForms && !errorCode) {
            const {
              modulesAndForms: { formAccessList, moduleList, formIdAccessList },
            } = listModulesAndForms;
            self.sidebarMenuList = moduleList;

            // Initialize menu states for all modules
            const newMenuStates = {};
            moduleList.forEach((menu) => {
              newMenuStates[menu.moduleId] = false;
            });
            self.menuStates = newMenuStates;

            // 'formList(retrieved from modules list response)' in each module returns forms which have rights. If it's empty, that means the module has no rights for any 'form'.
            let isFormsAvailable = self.sidebarMenuList.filter(
              (el) => el.formList.length > 0 // check any form has rights for the module
            );
            // show request access popup when the employee don't have access for any forms
            if (isFormsAvailable.length === 0) {
              self.isNoAccessRights = true;
            }
            self.$store.commit("UPDATE_SIDEBAR_MENU", self.sidebarMenuList);
            self.updateAdminManagerAccess(formAccessList);
            self.$store.commit("UPDATE_FORM_ACCESS_RIGHTS", formAccessList);
            let formIdList = JSON.parse(formIdAccessList);
            self.$store.commit("UPDATE_FORM_ID_ACCESS_RIGHTS", formIdList);
            self.mandatoryCallCount += 1;
          } else {
            self.handleError();
          }
        })
        .catch((menuResponseError) => {
          self.handleError(menuResponseError);
        });
    },
    // update admin/manager access globally using the passed form access list
    updateAdminManagerAccess(formAccessList) {
      let accessRights = JSON.parse(formAccessList);
      // check admin form access
      if (accessRights && accessRights["admin"]) {
        let adminAccess = accessRights["admin"].accessRights;
        // if the user is have admin or update access, then we consider the login employee as admin
        if (adminAccess.admin === "admin" || adminAccess.update === 1) {
          this.$store.commit("UPDATE_IS_ADMIN", 1);
        } else {
          this.$store.commit("UPDATE_IS_ADMIN", 0);
        }
        // update manager flag value retrieved from BE
        this.$store.commit("UPDATE_IS_MANAGER", adminAccess.isManager);
        if (adminAccess.isRecruiter == "Yes") {
          this.$store.commit("UPDATE_IS_RECRUITER", 1);
        } else {
          this.$store.commit("UPDATE_IS_RECRUITER", 0);
        }
      }
      // if we don't have record for admin for access rights, the we consider login employee is not admin
      else {
        this.$store.commit("UPDATE_IS_ADMIN", 0);
        this.$store.commit("UPDATE_IS_MANAGER", 0);
      }
    },

    // handle backend errors for modules and forms
    handleError(err = "") {
      this.mandatoryCallCount += 1;
      this.fetchError = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorParams = err.graphQLErrors[0];
        var error = JSON.parse(errorParams.message);
        var errorCode = error.errorCode;
        switch (errorCode) {
          // technical errors
          case "DB0000": //There are some technical issues.
            this.fetchError = true;
            this.errorContent =
              this.$t("authLayout.technicalIssues") + "DB0000";
            break;
          case "_DB0107": //Modules and forms does not exists.
            this.isNoAccessRights = true;
            break;
          case "_DB0106": //This employee does not have an access to any forms.
            this.isNoAccessRights = true;
            break;
          case "ERE0014": //Error while retrieving the modules and forms details.
          case "_UH0001": //Something went wrong! Please contact system admin.
          default:
            snackbarData.message = this.$t("authLayout.contactSystemAdmin");
            this.showAlert(snackbarData);
            break;
        }
      } else {
        snackbarData.message = this.$t("authLayout.requestSentSuccess");
        this.showAlert(snackbarData);
      }
    },

    async redirectToPartnerPage(formURL) {
      this.isLoading = true;
      await this.$store
        .dispatch("encryptRefreshToken", { formURL: formURL })
        .then(() => {
          this.isLoading = false;
        })
        .catch(() => {
          this.isLoading = false;
        });
    },

    //function to close the snackbar alert
    closeSnackbarAlert() {
      this.$store.commit("CLOSE_SNACKBAR");
    },

    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    //skip modal request access for now
    //on skip notify same on top notification bar
    skipModuleRequestAccess() {
      this.isNoAccessRights = false;
      this.showRequestAccess = true;
      this.$store.commit("SHOW_REQUEST_ACCESS_NOTIFICATION", true);
    },

    //sent request for module access to admin
    requestForModulesAccess(requestFrom) {
      let vm = this;
      vm.isRequesting = true;
      try {
        vm.$apollo
          .query({
            query: REQUEST_ACCESS,
            client: "apolloClientC",
          })
          .then(() => {
            if (requestFrom === "top-bar") {
              vm.showRequestAccess = false;
              vm.$store.commit("SHOW_REQUEST_ACCESS_NOTIFICATION", false);
            }
            vm.isRequesting = false;
            vm.isNoAccessRights = false;
            let snackbarData = {
              isOpen: true,
              message: this.$t("authLayout.requestSentSuccess"),
              type: "success",
            };
            vm.showAlert(snackbarData);
          })
          .catch(() => {
            vm.handleReqAccessError();
          });
      } catch {
        vm.handleReqAccessError();
      }
    },

    // handle error for access request query
    handleReqAccessError() {
      this.isRequesting = false;
      this.isNoAccessRights = false;
      this.showRequestAccess = true;
      this.$store.commit("SHOW_REQUEST_ACCESS_NOTIFICATION", true);
      let snackbarData = {
        isOpen: true,
        message: this.$t("authLayout.requestAccessError"),
        type: "warning",
      };
      this.showAlert(snackbarData);
    },
  },
});
</script>

<style>
@import "../assets/css/iconstyles.css";
@import "../assets/css/VueCommon.css";
@import "../assets/scss/dataTableStyle.scss";
@import url("https://cdn.jsdelivr.net/npm/vue-tel-input/dist/vue-tel-input.css");

.ps {
  height: 90%;
}
.app-background {
  background-color: rgb(var(--v-theme-grey), 0.15);
  height: 100%;
}
.v-list-item {
  min-height: 12px !important;
  padding: 0 0.5em !important;
}
.hrapp-icon-size {
  font-size: 16px;
}
.hrapp-menu-text {
  color: #fff;
  font-size: 0.9em;
  text-align: -webkit-center;
  text-align: center;
  text-align: -moz-center;
  line-height: 16px;
  margin-top: 10px;
  justify-content: center !important;
}
.hrapp-org-name-text {
  margin-left: 10px;
  font-size: 14px;
  color: rgb(var(--v-theme-primary));
  letter-spacing: 1px;
  font-weight: 400;
  display: flex;
  align-items: center;
}

.hrapp-module-new-label-outer {
  margin-right: -3em;
}
.hrapp-module-new-label {
  width: 50px;
  background: #ff743c;
  border-radius: 25px;
  margin-right: -4px;
  opacity: 1;
  z-index: 5;
  height: 18px;
  font-family: inherit;
  font-size: 0.9em;
}
.sidebar-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1em 0.5em 0.5em 0.5em !important;
}
.sub-menu-hover {
  background: #ffffff29;
}
.sidebar-menu-item:hover {
  background: #ffffff29 !important;
}
.sidebar-active-menu-not-hovered {
  border-left: 7px solid rgb(var(--v-theme-primary));
  background: none !important;
}
.user-profile-name {
  align-items: center;
  display: flex;
  font-size: 14px;
  color: rgb(var(--v-theme-primary));
}
.sidebar-active-menu {
  border-left: 7px solid rgb(var(--v-theme-secondary)) !important;
  background: #ffffff29;
}
.sub-menu-list-item {
  padding: 0.6em !important;
}
.sub-menu-list-item:hover {
  background-color: rgb(var(--v-theme-hover));
}
.active-submenu-item {
  background-color: rgb(var(--v-theme-primary)) !important;
  color: rgb(var(--v-theme-grey), 0.2) !important;
}
.active-submenu-item:hover {
  background-color: rgb(var(--v-theme-primary)) !important;
  color: rgb(var(--v-theme-grey), 0.2) !important;
}
.v-navigation-drawer__content {
  overflow-y: hidden;
}
.v-navigation-drawer {
  z-index: 10;
  position: fixed;
  left: 0;
  height: calc(100% - 50px);
}

/* sidebar scroll css */
.sidebar-scroll {
  max-height: 90%;
  overflow-y: auto;
}
.sidebar-scroll::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: transparent !important;
}
.sidebar-scroll::-webkit-scrollbar-thumb {
  border-radius: 25px;
  background-color: #ffffff29 !important;
  margin-top: 10px;
  margin-bottom: 10px;
}
.sidebar-scroll .ps__rail-y {
  width: 0px !important;
}
.sidebar-scroll .ps__rail-y .ps__thumb-y {
  width: 6px !important;
}
/* sidebar menu scroll css */
.sidebar-submenu-scroll {
  max-height: 300px;
  overflow-y: auto;
  padding-top: 0px;
}
.sidebar-submenu-scroll::-webkit-scrollbar-track {
  background-color: white !important;
  width: 0;
}
.sidebar-submenu-scroll::-webkit-scrollbar-thumb {
  border-radius: 25px;
  background-color: rgb(var(--v-theme-hover)) !important;
  margin-top: 10px;
  margin-bottom: 10px;
}
.sidebar-submenu-scroll::-webkit-scrollbar {
  width: 8px;
  background-color: transparent !important;
}
.widget-app-container {
  padding-right: 35px !important;
}
/* footer */
.footer-link {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.3em 1em 0.3em 1em;
}
.support-link a {
  letter-spacing: 1px;
  color: #5b5b5b !important;
  opacity: 0.8;
  text-decoration: none;
  font-size: 88%;
}
.support-link a:hover {
  letter-spacing: 1px;
  color: rgb(var(--v-theme-primary)) !important;
  text-decoration: underline;
  font-size: 85%;
}
@media screen and (max-width: 850px) {
  .footer-link {
    justify-content: center;
  }
}
/* user profile menu */
.profile-menu-item {
  padding: 0.5em 0.5em 0.5em 0.5em !important;
  display: flex;
  color: rgb(var(--v-theme-primary)) !important;
  font-size: 0.9em;
  width: 100%;
  align-items: center;
}
.same-as-profile-name {
  display: inline-block;
  padding: 0.6em 0.9em 0.6em 0.9em !important;
  display: flex;
  font-size: 0.9em;
  width: 100%;
  align-items: center;
}
.profile-menu-item:hover {
  color: rgb(var(--v-theme-primary)) !important;
}
.profile-menu-highlight:hover {
  color: rgb(var(--v-theme-primary)) !important;
  cursor: pointer;
}
.data-setup-icon {
  font-size: 18px;
  cursor: pointer;
  padding: 1em;
}
@media screen and (max-width: 500px) {
  .data-setup-icon {
    display: none;
  }
}
.sidebar-extra-widgets {
  display: block;
  margin-bottom: 100px;
  padding: 0 20px 7px;
  position: relative;
}
.v-list-item--link:before {
  background-color: transparent;
}
.sbar-alert-content {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  color: white;
  padding: 0.4em;
}
.notify-alert-bar {
  min-height: 45px;
  display: flex;
  justify-content: center;
  background-color: rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 500px) {
  .notify-alert-bar {
    min-height: 60px;
    display: flex;
    justify-content: center;
  }

  .sbar-alert-content {
    padding: 0px;
  }
}
.v-btn--variant-elevated {
  background: rgb(var(--v-theme-primary));
  color: white;
  box-shadow: 0.3px 3px 3px grey;
}
.v-btn--variant-outlined {
  color: rgb(var(--v-theme-primary));
  border: none;
  box-shadow: 0.3px 3px 3px grey;
}
</style>
