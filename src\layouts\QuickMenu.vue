<template>
  <div class="bg-white quick-menu-layout">
    <div
      id="search-menu-background"
      class="d-flex flex-column justify-center align-center pa-0"
    >
      <h2
        class="d-flex justify-center font-weight-bold text-h4"
        style="position: absolute"
      >
        {{ $t("authLayout.quickMenu") }}
      </h2>
      <img
        v-if="!isMobileView"
        width="95%"
        :height="windowWidth > 800 ? '100%' : '65%'"
        :src="getQuickMenuBgUrl"
        alt="search-background"
        class="mx-auto mt-auto"
      />
    </div>
    <div id="quickMenuSearch" class="search-box mx-auto">
      <v-text-field
        id="menuSearchInput"
        v-model="menuSearchKeyword"
        class="pa-0 common-box-shadow"
        color="blue"
        hide-details
        rounded="lg"
        variant="solo"
        :placeholder="$t('authLayout.menuSearch')"
        prepend-inner-icon="fas fa-search"
        @input="searchText"
      />
    </div>
    <v-container fluid class="menu-search-container">
      <div
        v-for="menu in filteredMenus"
        :key="menu.moduleId"
        :class="menu.formList.length > 0 ? 'my-2 mt-4' : 'ma-0'"
      >
        <div
          v-if="menu.formList.length > 0"
          class="mb-3 ml-2 ml-sm-4 ml-md-5 ml-lg-7 text-primary text-h6 font-weight-regular"
          :class="{ 'd-flex justify-center ': windowWidth < 400 }"
        >
          {{
            showModule(menu.moduleName)
              ? menu.translatedModuleName || menu.moduleName
              : ""
          }}
        </div>
        <div
          v-if="menu.formList.length > 0 && showModule(menu.moduleName)"
          class="d-flex flex-wrap"
          :class="{ 'justify-center': windowWidth < 400 }"
        >
          <div
            v-for="(form, formIndex) in menu.formList"
            :key="`${form.formId}-${formIndex}`"
            class="d-flex"
          >
            <v-hover v-slot="{ isHovering, props }">
              <div
                v-if="
                  (form.formName !== 'Members' &&
                    form.formName !== 'Productivity Monitoring') ||
                  (form.formName === 'Productivity Monitoring' &&
                    pmSettingsAdmin) ||
                  (form.formName === 'Members' && membersAdmin)
                "
                v-bind="props"
                class="ma-2 ma-sm-4 ma-md-5 ma-lg-7 d-flex flex-column"
              >
                <v-card
                  width="110px"
                  height="120px"
                  class="pa-3 d-flex flex-column justify-center align-center rounded-lg common-box-shadow cursor-pointer"
                  :class="isHovering ? 'bg-primary text-white' : 'text-primary'"
                  @click="formRedirection(form)"
                >
                  <v-chip
                    v-if="enableNewLabel"
                    class="pa-2 px-3 new-menu-label"
                    color="orange"
                    variant="flat"
                  >
                    New
                  </v-chip>
                  <i
                    :class="
                      'icon hr-' +
                      formIconNameFromMenu(menu.moduleName) +
                      '-' +
                      formIconNameFromMenu(form.formName) +
                      ' ' +
                      'forms-icon-size mb-2'
                    "
                  />
                  <span class="text-caption text-center">{{
                    form.customFormName
                  }}</span>
                </v-card>

                <div
                  v-if="showBottomIcon"
                  class="d-flex justify-space-between mt-2"
                >
                  <v-card class="pa-2 rounded-lg common-box-shadow">
                    <v-icon size="30" color="error"> fab fa-youtube </v-icon>
                  </v-card>
                  <v-card class="pa-2 rounded-lg common-box-shadow">
                    <v-icon size="30" color="info"> fas fa-info-circle </v-icon>
                  </v-card>
                </div>
              </div>
            </v-hover>
          </div>
        </div>
      </div>
      <!--search menu not found screen -->
      <AppFetchErrorScreen
        v-if="isEmptySearch"
        image-name="common/empty-search"
        :main-title="noSearchTitle"
        :content="searchNotFoundContent"
        button-text="Reset"
        icon-name="fas fa-redo-alt"
        @button-click="cleanAllFilters()"
      />
      <AppLoading v-if="isLoading" />
    </v-container>
  </div>
</template>

<script>
//import sorting function
import { searchList } from "@/helper.js";
export default {
  name: "QuickMenu",
  data: () => ({
    menuSearchKeyword: "",
    timeout: null,
    filteredMenus: [],
    searchNotFoundContent:
      "Please try again by changing the search keyword or clear it to get all the menus.",
    noSearchTitle: "No matching search results found.",

    //future purpose now set as false by default
    enableNewLabel: false,
    showBottomIcon: false,
    isLoading: false,
  }),
  computed: {
    sidebarMenus() {
      return this.$store.state.sideBarMenuList;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getQuickMenuBgUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/layout/park-green-bg.webp");
      else return require("@/assets/images/layout/park-green-bg.png");
    },
    //present empty search screen when no forms under each menus
    isEmptySearch() {
      let filteredMenus = this.filteredMenus.filter(
        (el) => el.formList.length > 0
      );
      return filteredMenus.length === 0;
    },
    //screen size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // organization billing type
    isAutoBilling() {
      return this.$store.state.isAutoBilling;
    },
    pmSettingsAdmin() {
      let pmSettingsAccess = this.$store.getters.formAccessRights(
        "productivity-monitoring"
      );
      if (pmSettingsAccess && pmSettingsAccess.accessRights) {
        const { admin } = pmSettingsAccess.accessRights;
        return admin === "admin";
      }
      return false;
    },
    // check admin rights for the members form
    membersAdmin() {
      let membersRights = this.$store.getters.formAccessRights("members");
      if (membersRights && membersRights.accessRights) {
        const { admin } = membersRights.accessRights;
        return admin === "admin";
      }
      return false;
    },
    // check Billing form rights
    billingAdmin() {
      let billingFormRights = this.$store.getters.formAccessRights("billing");
      if (
        billingFormRights &&
        billingFormRights.accessRights &&
        billingFormRights.accessRights["admin"] === "admin"
      ) {
        return true;
      }
      return false;
    },
    coreHrAdmin() {
      let isCoreHrAdmin = this.$store.getters.checkAnyOneOfTheAdmin;
      return isCoreHrAdmin;
    },

    isManager() {
      return this.$store.state.isManager;
    },

    accessRightsBasedOnId() {
      return this.$store.getters.formIdBasedAccessRights;
    },

    myTeamAdmin() {
      let formAccess = this.accessRightsBasedOnId("243");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights["admin"] === "admin";
      } else {
        return false;
      }
    },

    showModule() {
      return (moduleName) => {
        if (moduleName === "Billing") {
          return this.isAutoBilling && this.billingAdmin;
        } else if (moduleName === "Core HR") {
          return this.coreHrAdmin;
        } else if (moduleName === "My Team") {
          return this.myTeamAdmin || this.isManager;
        } else {
          return true;
        }
      };
    },
  },
  mounted() {
    //have a copy of menus in filtered Array
    this.filteredMenus = [...this.sidebarMenus];
  },
  methods: {
    // return icon name from module and form name
    formIconNameFromMenu(name) {
      return name.trim().replace(/\s/g, "-").toLowerCase();
    },

    //function to navigate the app on menu click
    isVue2LayoutForm(formName) {
      return this.$store.getters.checkForVue2LayoutForms(formName);
    },

    //function to navigate the app on menu click
    isVue3LayoutForm(formId) {
      return this.$store.getters.checkForVue3LayoutForms(formId);
    },

    isPartnerLayoutForm(formName) {
      return this.$store.getters.checkForPartnerLayoutForms(formName);
    },

    // forms redirection based on vue layout or admin layout
    formRedirection(form) {
      if (this.isPartnerLayoutForm(form.formName)) {
        this.redirectToPartnerPage(form.url);
      } else if (this.isVue2LayoutForm(form.formName)) {
        let redirectionURL = "";
        if (form.formName === "Billing") {
          redirectionURL = this.baseUrl + "in/billing";
        } else {
          redirectionURL = this.baseUrl + "in/" + form.url;
        }
        window.open(redirectionURL); // open the form in new tab
      } else if (this.isVue3LayoutForm(form.formId)) {
        let redirectionURL = null;
        if (form.url === "recruitment/careers")
          redirectionURL = this.baseUrl + "v3/careers";
        else redirectionURL = this.baseUrl + "v3/" + form.url;
        window.open(redirectionURL); // open the form in new tab
      } else {
        let redirectionURL = "";
        if (form.url === "organization/special-wages") {
          redirectionURL = this.baseUrl + "organization/holidays";
        } else if (form.url === "employee-self-service/organization-chart") {
          redirectionURL =
            this.baseUrl + "v3/employee-self-service/organization-chart";
          window.open(redirectionURL);
        } else if (form.url === "payroll/payroll-management") {
          let redirectionURL = null;
          if (this.$store.state.payrollUrl) {
            const userName = decodeURIComponent(
              window.$cookies.get("entomoUserName")
            );
            if (!userName) redirectionURL = this.$store.state.payrollUrl;
            redirectionURL = this.$store.state.payrollUrl + userName;
          } else {
            redirectionURL = this.baseUrl + "payroll/payroll-management";
          }
          window.open(redirectionURL); // open the form in new tab
        } else {
          let payrollModuleUnderTaxStatutoryModuleFormURLs = [
            "tax-and-statutory-compliance/etf",
            "tax-and-statutory-compliance/fixed-health-insurance",
            "tax-and-statutory-compliance/insurance",
            "tax-and-statutory-compliance/labour-welfare-fund",
            "tax-and-statutory-compliance/perquisite-tracker",
            "tax-and-statutory-compliance/provident-fund",
            "tax-and-statutory-compliance/tax-declarations",
            "tax-and-statutory-compliance/tax-rules",
            "tax-and-statutory-compliance/tds-history",
          ];
          let taxFormIndex =
            payrollModuleUnderTaxStatutoryModuleFormURLs.indexOf(form.url);
          if (taxFormIndex > -1) {
            let taxFormUrl =
              payrollModuleUnderTaxStatutoryModuleFormURLs[taxFormIndex];
            taxFormUrl = taxFormUrl.split("/");
            taxFormUrl = taxFormUrl[1];
            redirectionURL = this.baseUrl + "payroll/" + taxFormUrl;
          } else {
            let formsManagerFormsUnderTaxStatutoryModuleFormURLs = [
              "tax-and-statutory-compliance/form-downloads",
              "tax-and-statutory-compliance/compliance-forms",
            ];
            let taxFormIndex1 =
              formsManagerFormsUnderTaxStatutoryModuleFormURLs.indexOf(
                form.url
              );
            if (taxFormIndex1 > -1) {
              let taxFormUrl =
                formsManagerFormsUnderTaxStatutoryModuleFormURLs[taxFormIndex1];
              taxFormUrl = taxFormUrl.split("/");
              taxFormUrl = taxFormUrl[1];
              redirectionURL = this.baseUrl + "forms-manager/" + taxFormUrl;
            } else {
              if (form.url === "employees/timesheet-hours") {
                redirectionURL = this.baseUrl + "employees/timesheets";
              } else {
                redirectionURL = this.baseUrl + form.url;
              }
            }
          }
          window.open(redirectionURL); // open the form in new tab
        }
      }
    },

    async redirectToPartnerPage(formURL) {
      this.isLoading = true;
      await this.$store
        .dispatch("encryptRefreshToken", { formURL: formURL })
        .then(() => {
          this.isLoading = false;
        })
        .catch(() => {
          this.isLoading = false;
        });
    },

    // after entering text in the search field
    // the search process emit function will be called after 1 second
    searchText: function () {
      // clear timeout variable
      clearTimeout(this.timeout);

      var vm = this;
      vm.timeout = setTimeout(function () {
        // enter this block of code after 1 second
        vm.filterSearchedForms(vm.menuSearchKeyword);
      }, 500);
    },
    //function to search menus through module and forms name
    filterSearchedForms(searchKey) {
      let searchTerm = searchKey.toLowerCase();
      let listData = [...this.sidebarMenus];
      let filteredArray = listData.map((element) => {
        let forms = [];
        if (
          element["moduleName"].toString().toLowerCase().includes(searchTerm)
        ) {
          forms = element.formList;
        } else {
          forms = searchList(element.formList, searchTerm, ["formName"]);
        }
        return {
          ...element,
          formList: forms,
        };
      });
      this.filteredMenus = filteredArray;
    },
    //function to clear both search and filters
    cleanAllFilters() {
      this.menuSearchKeyword = "";
      this.filteredMenus = [...this.sidebarMenus];
    },
  },
};
</script>
<style scoped>
#search-menu-background {
  background-image: linear-gradient(to right, #bfd783, #2ee7b0);
  width: 100%;
  height: 130px;
  background-size: cover;
  color: black;
}
.search-box {
  border-radius: 10px;
  margin-top: -30px;
  width: 40%;
}
.quick-menu-layout {
  min-height: 90vh;
}
.forms-icon-size {
  font-size: 22px;
}
.menu-search-container {
  padding: 2em 2em 6em 3em;
}
::v-deep
  #quickMenuSearch
  > .v-text-field--solo
  > .v-input__control
  > .v-input__slot {
  margin-bottom: 0px !important;
  height: 55px;
  padding: 1em;
}
:deep(#quickMenuSearch .v-field--variant-solo) {
  border-radius: 8px !important;
}

.new-menu-label {
  position: absolute;
  top: 1em;
  left: 6em;
}

@media screen and (max-width: 900px) {
  .search-box {
    width: 50%;
  }
  .menu-search-container {
    padding: 1em;
  }
}

@media screen and (max-width: 500px) {
  .search-box {
    width: 70%;
  }
  .menu-search-container {
    padding: 10px;
  }
}
</style>
