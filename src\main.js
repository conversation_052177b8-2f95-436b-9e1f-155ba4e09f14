import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import i18n from "./translations/index.js";
import store from "./store";
import { loadFonts } from "./plugins/webfontloader";
import PerfectScrollbar from "vue3-perfect-scrollbar";
import createVuetifyInstance from "./plugins/vuetify";
import "vue3-perfect-scrollbar/dist/vue3-perfect-scrollbar.css";
import apolloProvider from "./apolloConfiguration";
import "@/firebase/firebaseConfig";
import VueCookies from "vue-cookies";
import tooltip from "./directives/tooltip.js";
import BaseComponents from "./components/globalComponent.js";
window.$ = window.jQuery = require("jquery");
import VueExcelEditor from "vue3-excel-editor";
import "vuetify/dist/vuetify.min.css";
import {
  CUSTOM_COLOR_PICKER,
  RETRIEVE_DROPDOWN_DETAILS,
} from "@/graphql/commonQueries.js";
import Quill from "quill";
import QuillBetterTable from "quill-better-table";
import "quill-better-table/dist/quill-better-table.css";
import Config from "@/config.js";
loadFonts();

async function retriveCustomColorPicker() {
  if (!localStorage.getItem("brand_color")) {
    try {
      const client = apolloProvider.clients.apolloClientAO;
      const response = await client.query({ query: CUSTOM_COLOR_PICKER });
      const data = response.data;
      if (
        data &&
        data.customColorPicker &&
        data.customColorPicker.colorResult &&
        data.customColorPicker.colorResult.length > 0
      ) {
        const colors = data.customColorPicker.colorResult[0];
        const colorResult = {
          Primary_Color: colors.Primary_Color || "#260029",
          Secondary_Color: colors.Secondary_Color || "#EC407A",
          Hover_Color: colors.Hover_Color || "#e5e9eb",
          Table_Header_Color: colors.Table_Header_Color || "#FFFFFF",
          Table_Header_Text_Color: colors.Table_Header_Text_Color || "#000000",
        };
        localStorage.setItem("brand_color", JSON.stringify(colorResult));
        return colorResult;
      }
    } catch (error) {
      const colors = {
        Primary_Color: "#260029",
        Secondary_Color: "#EC407A",
        Hover_Color: "#e5e9eb",
        Table_Header_Color: "#FFFFFF",
        Table_Header_Text_Color: "#000000",
      };
      localStorage.setItem("brand_color", JSON.stringify(colors));
      return colors;
    }
  } else {
    const colors = JSON.parse(localStorage.getItem("brand_color"));
    return colors;
  }
}

async function getGeneralSettings() {
  const faviconFilename = localStorage.getItem("faviconFilename");
  const pageTitle = localStorage.getItem("pageTitle");

  if (faviconFilename || pageTitle) {
    return { faviconFilename, pageTitle };
  }

  try {
    const client = apolloProvider.clients.apolloClientAO;
    const { data } = await client.query({
      query: RETRIEVE_DROPDOWN_DETAILS,
      variables: {
        formId: null,
        key: ["general_settings"],
      },
    });

    const settingsRaw = data?.retrieveDropdownDetails?.dropdownDetails;
    const parsed =
      settingsRaw?.length > 0
        ? JSON.parse(settingsRaw)?.[0]?.data?.[0] ?? {}
        : {};
    const fetchedFavicon = parsed.Favicon_Filename;
    const fetchedTitle = parsed.Page_Title;

    if (fetchedFavicon) localStorage.setItem("faviconFilename", fetchedFavicon);
    if (fetchedTitle) localStorage.setItem("pageTitle", fetchedTitle);

    return {
      faviconFilename: fetchedFavicon,
      pageTitle: fetchedTitle,
    };
  } catch (error) {
    console.error("Failed to fetch general settings", error);
    return null;
  }
}

function changeFavicon(iconPath) {
  // Find or create the favicon link element
  let link = document.querySelector("link[rel~='icon']");
  if (!link) {
    link = document.createElement("link");
    link.rel = "icon";
    document.head.appendChild(link);
  }
  // Update the href to the new icon path
  link.href = iconPath;
}

async function initializeApp() {
  // get custom color picker and general settings
  const [colors, generalSettings] = await Promise.all([
    retriveCustomColorPicker(),
    getGeneralSettings(),
  ]);

  // create vuetify instance
  const vuetify = await createVuetifyInstance(colors);

  // set favicon
  const DEFAULT_FAVICON = `${location.origin}/v3/favicon.ico`;

  if (generalSettings?.faviconFilename) {
    const orgCode = localStorage.getItem("orgCode") ?? store.getters.orgCode;
    const domainName = store.getters.domain;
    const logoPath = `${Config.publicImageS3Path}${domainName}/${orgCode}/favicon/${generalSettings.faviconFilename}`;
    try {
      const response = await fetch(logoPath);
      changeFavicon(response.ok ? logoPath : DEFAULT_FAVICON);
    } catch (err) {
      console.error("Favicon fetch failed", err);
      changeFavicon(DEFAULT_FAVICON);
    }
  } else {
    changeFavicon(DEFAULT_FAVICON);
  }

  // Register the Quill module globally
  Quill.register("modules/better-table", QuillBetterTable, true);

  const app = createApp(App)
    .use(router)
    .use(store)
    .use(vuetify)
    .use(PerfectScrollbar)
    .use(apolloProvider)
    .use(VueCookies)
    .use(VueExcelEditor)
    .use(i18n);

  BaseComponents.register(app);
  app.directive("tooltip", tooltip);
  app.mount("#app");
}
initializeApp();
