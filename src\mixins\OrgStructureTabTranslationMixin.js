/**
 * Mixin for handling organizational structure tab translations
 * This mixin provides consistent translation logic across all org structure components
 */
export default {
  methods: {
    /**
     * Get translation key for a given form name
     * @param {string} formName - The original English form name
     * @returns {string} - The translation key
     */
    getTranslationKey(formName) {
      // Map form names to translation keys
      const translationKeyMap = {
        "Employee Type": "employeeType",
        Grades: "grades",
        Locations: "locations",
        "Department Hierarchy": "departmentHierarchy",
        "Designations/Positions": "designationsPositions",
        "Job Roles": "jobRoles",
        "Work Schedule": "workSchedule",
        "Business Unit / Cost Center": "businessUnitCostCenter",
        "Organization Group": "organizationGroup",
        "Core Group": "coregroup", // Handle Core Group custom name
      };

      // Special handling for custom organization group names
      // If the form name matches the current organizationGroupFormName, use the coregroup translation
      if (
        this.organizationGroupFormName &&
        formName === this.organizationGroupFormName &&
        formName !== "Organization Group"
      ) {
        return "coregroup";
      }

      return (
        translationKeyMap[formName] ||
        formName.toLowerCase().replace(/\s+/g, "")
      );
    },

    /**
     * Get original English form name from translated name
     * @param {string} translatedName - The translated form name
     * @returns {string} - The original English form name
     */
    getOriginalFormName(translatedName) {
      // Create reverse mapping from translated names back to original English names
      const reverseTranslationMap = {
        // Spanish to English mapping
        "Tipo de Empleado": "Employee Type",
        Grados: "Grades",
        Ubicaciones: "Locations",
        "Jerarquía de Departamentos": "Department Hierarchy",
        "Designaciones/Posiciones": "Designations/Positions",
        "Roles de Trabajo": "Job Roles",
        "Horario de Trabajo": "Work Schedule",
        "Unidad de Negocio / Centro de Costos": "Business Unit / Cost Center",
        "Grupo de Organización": "Organization Group",
        "Grupo de Núcleo": "Core Group", // Spanish translation for Core Group

        // French to English mapping
        "Type d'Employé": "Employee Type",
        Emplacements: "Locations",
        "Hiérarchie des Départements": "Department Hierarchy",
        "Désignations/Postes": "Designations/Positions",
        "Rôles de Travail": "Job Roles",
        "Horaire de Travail": "Work Schedule",
        "Unité d'Affaires / Centre de Coûts": "Business Unit / Cost Center",
        "Groupe d'Organisation": "Organization Group",
        "Groupe de noyau": "Core Group", // French translation for Core Group

        // Japanese to English mapping
        従業員タイプ: "Employee Type",
        等級: "Grades",
        場所: "Locations",
        部門階層: "Department Hierarchy",
        "指定/役職": "Designations/Positions",
        職務役割: "Job Roles",
        勤務スケジュール: "Work Schedule",
        "事業単位 / コストセンター": "Business Unit / Cost Center",
        組織グループ: "Organization Group",
      };

      // Special handling for custom organization group names
      // If the name matches the current organizationGroupFormName, treat it as Organization Group
      if (
        this.organizationGroupFormName &&
        translatedName === this.organizationGroupFormName
      ) {
        return this.organizationGroupFormName;
      }

      // Return original English name or the input if no mapping found
      return reverseTranslationMap[translatedName] || translatedName;
    },

    /**
     * Translate form access array to current language
     * @param {Array} formAccessArray - Array of English form names
     * @returns {Array} - Array of translated form names
     */
    translateFormAccessArray(formAccessArray) {
      if (!formAccessArray || !Array.isArray(formAccessArray)) {
        return [];
      }

      return formAccessArray.map((formName) => {
        const translationKey = this.getTranslationKey(formName);
        return this.$t(`coreHr.${translationKey}`) || formName;
      });
    },

    /**
     * Enhanced onTabChange method that handles translated tab names
     * @param {string} tab - The clicked tab name (possibly translated)
     */
    onTabChangeWithTranslation(tab) {
      // Convert translated tab name back to English for backend processing
      const originalTabName = this.getOriginalFormName(tab);

      // Check if this is the landed form (current form)
      const landedFormOriginal = this.getOriginalFormName(this.landedFormName);

      if (originalTabName !== landedFormOriginal) {
        let processedTab = originalTabName;

        // Handle custom organization group name
        if (
          this.organizationGroupFormName &&
          (originalTabName === this.organizationGroupFormName ||
            tab === this.organizationGroupFormName)
        ) {
          processedTab = "Organization Group";
        }

        const { formAccess } = this.orgStructureFormAccess;
        let clickedForm = formAccess[processedTab];

        if (clickedForm && clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else if (clickedForm) {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        } else {
          console.error("No form found for tab:", processedTab);
        }
      }
    },
  },
};
