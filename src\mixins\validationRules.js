import moment from "moment";
import { listInputValidations } from "@/helper";
import i18n from "@/translations";
const t = i18n?.global?.t;
const {
  validateWithRulesAndReturnMessages,
} = require("@cksiva09/validationlib/src/validator");

export default {
  methods: {
    validateWithRulesAndReturnMessages,
    listInputValidations,
    alphaNumSpaceWithFiveSymbolValidation(field, value) {
      let msg = ` Only alphanumeric, spaces, and symbols(& , ( ) .) are allowed`;
      return /^[\w\ \,\.\&\(\)\ ]+$/.test(value) || msg;
    },
    alphaNumSpaceWithElevenSymbolValidation(value) {
      let msg =
        "Only alphanumeric, spaces and symbols(+ , / . # & : ( ) ' - ) are allowed.";
      return /^[\w\.\,\#\+\&\/\-\(\)\:\'\ ]*$/.test(value) || msg;
    },
    alphaNumSpaceNewLineWithElevenSymbolValidation(value) {
      let msg =
        "Only alphanumeric, spaces and symbols(& . + - , : ( ) ' / #) are allowed.";
      return /^[\w\.\,\#\+\&\/\-\(\)\n\:\'\ ]*$/.test(value) || msg;
    },
    alphaNumericValidation(field, value) {
      let msg = `Only alphanumeric characters are allowed.`;
      return /^[a-zA-Z0-9]*$/.test(value) || msg;
    },
    firstLetterSpace(field, value) {
      let msg = `First letter should not be space.`;
      return /^[^\s].*$/.test(value) ? true : msg;
    },
    alphaNumericHypSlashHash(value) {
      let msg = "Only alphanumeric and symbols(# - /)  are allowed.";
      return /^[\w\-\/\#\ ]*$/.test(value) || msg;
    },
    alphaNumSpaceWithFourSymbolValidation(field, value) {
      let msg = `Only alphanumeric characters, space and symbols (- , . () ) are allowed.`;
      return /^[a-zA-Z0-9\-\,\.\ \(\)]*$/.test(value) || msg;
    },
    //alphaNumeric with 7 symbols validation
    alphaNumericWithSevenSymbolsValidation(field, value) {
      let msg = `Only alphanumeric characters, spaces and symbols (- , . ' /() ) are allowed.`;
      return /^[a-zA-Z0-9\-\,\.\'\ / \(\)]*$/.test(value) || msg;
    },
    // alphaSpaceWith Dot and () symbol validation
    alphaSpaceDotWithFiveSymbolValidation(field, value) {
      let msg = ` Only alphabets, spaces and symbols(. , ( ) .) are allowed`;
      return /^[a-zA-Z\.\(\)\ ]+$/.test(value) || msg;
    },
    //alphaSpaceCommaWith() validation
    alphaSpaceCommaWithFiveSymbolValidation(field, value) {
      let msg = ` Only alphabets, spaces and symbols(, ( ) .) are allowed`;
      return /^[a-zA-Z\,\(\)\ ]+$/.test(value) || msg;
    },
    // alpha space  validation
    alphaSpaceValidation(field, value) {
      const msg = `Only alphabets and space are allowed`;
      return /^[a-zA-Z\ ]+$/.test(value) || msg;
    },
    // AlphaNumeric validation only
    alphaNumericCheck(field, value) {
      let msg = `Only alphanumeric characters are allowed.`;
      return /^[a-zA-Z0-9/+]*$/.test(value) || msg;
    },
    // alpha space and dot validation
    alphaNumSpCDotHySlashValidation(field, value) {
      let msg =
        "Only alphanumeric, spaces and symbols(+ , / . # & : () ' - ` | _) are allowed.";
      return (
        /^[a-zA-Z0-9\|\\_\\.\,\#\+\&\/\-\(\)\:\`\'\ ]*$/.test(value) || msg
      );
    },
    multilingualNameValidation(field, value) {
      let msg = `Only alphabets,+, comma, /, dot, #, ±, &, :, (), ', hypen and spaces allowed`;
      return (
        /^[\p{L}\p{M}\s.,#\+&/\-():'ñd’!@#$%^&*_±=|\\;?"]+$/u.test(value) || msg
      );
    },
    multilingualNameNumericValidation(field, value) {
      let msg =
        "Only alphanumeric,+, comma, /, dot, #, &, ±,  :, (), ', hypen and spaces allowed";
      return (
        value === "" ||
        /^[\p{L}\p{M}\p{Nd}\s.,#\+&/\-():'ñd’!±@#$%^&*_=|\\;?"]+$/u.test(
          value
        ) ||
        msg
      );
    },
    //alphaNumeric Space With Two Symbols
    alphaNumericSpaceWithTwoSymbols(field, value) {
      let msg = `Only alphanumeric, spaces and symbols(&, -) are allowed.`;
      return /^[\w\(\)\,\ ]*$/.test(value) || msg;
    },
    alphaNumSpaceWithThirteenSymbolValidation(value) {
      let msg =
        "Only alphanumeric, spaces, and symbols (+ , / . # & : ( ) ' - ñ d’) are allowed.";
      return /^[\w\.\,\#\+\&\/\-\(\)\:\'\ ñd’]*$/.test(value) || msg;
    },
    alphaWithSpaceAndDot(field, value) {
      let msg = `Only alphabets, spaces and symbol(.) are allowed.`;
      return /^[a-zA-Z\s.]*$/.test(value) || msg;
    },
    //validation for percentage
    percentageValidation(field, value) {
      let msg = "Only numbers and a percentage sign (%) are allowed.";
      if (/^[\d.]+%?$/.test(value)) {
        const numericValue = parseFloat(value);
        if (numericValue > 100) {
          msg = "Percentage cannot be more than 100.";
          return msg;
        }
        return true;
      }
      return msg;
    },
    maxValue(field, value, maxValue) {
      let msg = `Value cannot be more than ${maxValue}.`;
      return parseInt(value) <= parseInt(maxValue) || msg;
    },
    cidrValidation(field, value) {
      let msg = "CIDR block has to be specified (For eg.. ***********/32)";
      const ipAddressPattern =
        /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/([0-9]|[1-2][0-9]|3[0-2]))$/;
      return ipAddressPattern.test(value) || msg;
    },
    ifscValidation(field, value) {
      let msg = `${field} is not valid`;
      return /^[A-Z]{4}[0][A-Z0-9]{6}$/.test(value) || msg;
    },
    // validation rule for end date
    endDateValidation(field1, field2, startDate, endDate) {
      let msg = `${field1} cannot be less than ${field2}.`;
      const startTimestamp = new Date(startDate).getTime();
      const endTimestamp = new Date(endDate).getTime();
      return endTimestamp >= startTimestamp || msg;
    },
    minLengthValidation(field, value, minValue) {
      let msg = `${field} should be greater than or equal to ${minValue} characters`;
      let isValid = !value ? true : value.length >= minValue;
      return isValid || msg;
    },
    maxLengthValidation(field, value, maxValue) {
      let msg = `${field} should be less than or equal to ${maxValue} characters`;
      let isValid = !value ? true : value.length <= maxValue;
      return isValid || msg;
    },
    maxLengthForDigitsValidation(field, value) {
      let msg = `The value should not exceed more than 2 digits.`;
      return /^\d{1,2}$/.test(value) || msg;
    },
    maxLengthForDigitsValidationDynamic(field, value, length) {
      if (value.length >= length) {
        return `${field} should not exceed ${length} characters`;
      } else {
        return true;
      }
    },
    // only numeric are allowed
    numericValidation(field, value) {
      const msg = `Only numerics are allowed`;
      return /^\d+$/.test(value) || msg;
    },
    aadharNumberValidation(field, value) {
      const msg = "Please enter a valid Aadhar number.";
      const aadharPattern = /^\d{12}$/;
      return aadharPattern.test(value) || msg;
    },
    panCardValidation(field, value) {
      const msg = `Invalid ${field} format`;
      return /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/i.test(value) || msg;
    },
    emailValidation(field, value) {
      let msg = `Please enter a valid email address.`;
      const pattern =
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      return pattern.test(value) || msg;
    },
    positiveNumberValidation(field, value, minValue, maxValue) {
      let msg = `Please enter a valid number for ${field}.`;
      return (
        (Number.isInteger(value) && value >= minValue && value <= maxValue) ||
        msg
      );
    },
    required(field, value, translate) {
      if (translate)
        return (
          !!value || i18n.global.t("validations.required", { fieldName: field })
        );
      return !!value || `${field} is required`;
    },
    requiredArray(field, value) {
      return (value && value.length > 0) || `${field} is required`;
    },
    minNumberValidation(field, value, minValue, minFieldName = "") {
      if (value < minValue) {
        if (minFieldName) {
          return `${field} should be greater than or equal to ${minFieldName}.`;
        } else {
          return `Please enter a value greater than or equal to ${minValue}.`;
        }
      }
      return true; // Validation passes
    },
    minNumberValidationWithMsg(field, value, minValue, msg = "") {
      if (value < minValue) {
        return (
          msg || `Please enter a value greater than or equal to ${minValue}.`
        );
      }
      return true;
    },
    minMaxNumberValidationWithoutMaxField(field, value, minValue, maxValue) {
      if (value < minValue) {
        t("validations.minValidationWithoutFieldName", {
          minValue,
        });
      } else if (value > maxValue) {
        t("validations.maxValidationWithoutFieldName", {
          maxValue,
        });
      }
      return true;
    },
    minMaxNumberValidation(
      field,
      value,
      minValue,
      maxValue,
      minFieldName = "",
      maxFieldName = ""
    ) {
      if (value < minValue) {
        if (minFieldName) {
          return `${field} should be greater than or equal to ${minFieldName}.`;
        } else {
          return `Please enter a value greater than or equal to ${minValue}.`;
        }
      } else if (value > maxValue) {
        if (maxFieldName) {
          return `${field} should be less than or equal to ${maxFieldName}.`;
        } else {
          return `Please enter a value less than or equal to ${maxValue}.`;
        }
      }

      return true;
    },

    rangeValidation(field, minValue, maxValue) {
      if (minValue > maxValue) {
        return "please select a proper value";
      }
      return true;
    },
    minMaxStringValidation(field, value, minValue, maxValue) {
      if (value) {
        if (value.length < minValue) {
          return `The ${field} should have minimum ${minValue} characters`;
        } else if (value.length > maxValue) {
          return `The ${field} must not exceed ${maxValue} character`;
        } else {
          true;
        }
      }
      return true;
    },
    numericRequiredValidation(field, value, translate) {
      if (value !== undefined && value !== null && value !== "") {
        return true; // No error, value is present
      } else {
        if (translate)
          return i18n.global.t("validations.numericRequiredValidation", {
            fieldName: field,
          });
        return `${field} is required`; // Error, value is missing
      }
    },
    //  Two decimal validation
    twoDecimalPrecisionValidation(value, translate) {
      if (/^\d+(\.\d{1,2})?$/.test(value)) {
        return true;
      }
      if (translate) return i18n.global.t("validations.twoDecimalValidation");
      return `Please enter a value up to 2 decimal places`;
    },
    alphaNumSpCDotHySlash(value) {
      const msg =
        "Only alphanumeric, spaces, and symbols(+ , / . # & : () ' - _ ` ? ) are allowed.";
      return /^[\w.,#+&\/\(\):'`? -]*$/.test(value) || msg;
    },
    // This validation is for remark/reason/description
    alphaNumSpCDotHySlashNewLine(value) {
      const msg =
        "Only alphanumeric, new Line, spaces, and symbols(+ , / . # & : () ' - _ ` ? ) are allowed.";
      return /^[\w.,#+&\/\(\):'`?\n -]*$/.test(value) || msg;
    },
    domainNameValidation(value) {
      const msg = "Please enter valid domain name";
      return (
        /^([a-zA-Z0-9](?:[a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/.test(
          value
        ) || msg
      );
    },
    timeFormatValidation(value) {
      const msg = "Invalid format";
      let onlyNumericValidation = /^\d+$/.test(value);
      if (!onlyNumericValidation) {
        let inputString = String(value);
        inputString = inputString.toLowerCase();
        return /^(?:(\d+)h\s*)?(?:(\d+)m\s*)?$/i.test(inputString) || msg;
      } else return true;
    },
    roundNameValidation(value) {
      const msg =
        "Only alphanumeric, spaces and symbols(- , . ' / () )  are allowed.";
      return /^[\w\.\,\/\-\(\)\'\ ]*$/.test(value) || msg;
    },
    listLengthValidation(field, value, minRange, maxRange) {
      if (value == undefined) {
        return `Choose minimum ${minRange} ${field}`;
      }
      if (value.length < minRange) {
        return `Choose minimum ${minRange} ${field}`;
      } else if (value.length > maxRange) {
        return `Choose maximum ${maxRange} ${field}`;
      } else {
        return true;
      }
    },
    dynamicValidationRule(value, Validation_Id) {
      if (!value || !Validation_Id) return true;
      const listInputValidations = this.listInputValidations();
      // Find the selected validation object
      const selectedValidation = listInputValidations.find(
        (validation) => validation.Validation_Id === Validation_Id
      );
      if (selectedValidation && selectedValidation.Regular_Expression) {
        const regex = new RegExp(selectedValidation.Regular_Expression, "u");
        return regex.test(value) ? true : `${selectedValidation.Description}`;
      }
      return true;
    },
    urlValidation(value) {
      var urlPattern = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i;
      if (!urlPattern.test(value)) {
        return "Please enter a valid URL.";
      }
      return true;
    },
    minMaxDateValidation(field, value, minDate, maxDate) {
      if (value && (minDate || maxDate)) {
        if (moment(value).isBefore(minDate)) {
          return `The ${field} should be greater than or equal to ${minDate}`;
        } else if (moment(value).isAfter(maxDate)) {
          return `The ${field} should be less than or equal to ${maxDate}`;
        }
      }
      return true;
    },
    currentDateComparison(field, value) {
      const currentDate = moment().format("YYYY-MM-DD");
      if (value) {
        const endDate = moment(new Date(value)).format("YYYY-MM-DD");
        if (currentDate <= endDate) return true;
        else return `${field} should be greater than current date`;
      } else return true;
    },
    leaveOverrideCarry(field, value) {
      if (field === "Current year leave entitlement") {
        if (value < 0) return `Minimum value 0 required for ${field}`;
        else {
          if (value > 365) return `Should not exceed the maximum limit of 365`;
          else return true;
        }
      } else if (field === "Carry Over Balance") {
        if (value < 0) return `Minimum value 0 required for ${field}`;
        else {
          if (value > 1500)
            return `Should not exceed the maximum limit of 1500`;
          else return true;
        }
      }
    },
    noOfPositionsValidation(
      noOfPosition,
      approvedPositions,
      warmBodies,
      totalRecords,
      updateFlag,
      existingNoOfPositions
    ) {
      const approved = approvedPositions || 0;
      const warm = warmBodies || 0;
      const total = totalRecords || 0;
      const existing = existingNoOfPositions || 0;

      const availablePositions = updateFlag
        ? approved - warm - (total - existing)
        : approved - warm - total;

      return noOfPosition <= availablePositions
        ? true
        : `Number of positions exceeds the available positions for recruitment`;
    },
    fileTypeRule(file) {
      if (!file || !file.type) return true;

      const allowedTypes = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ];

      if (!allowedTypes.includes(file.type)) {
        return "Invalid file type: Supported formats: JPEG, JPG, PNG, PDF, DOC, DOCX";
      }

      if (file.size > 3000000) {
        return "Maximum file size: 3 MB";
      }

      return true;
    },
    isValidHexColor(color) {
      if (!color) return true;
      const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
      return hexColorRegex.test(color) || "Please enter a valid hex color";
    },
  },
};
