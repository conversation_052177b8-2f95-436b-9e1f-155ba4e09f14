import firebase from "firebase/app";
import "firebase/auth";
import axios from "axios";
import moment from "moment";
import config from "../config";
import apolloProvider from "../apolloConfiguration";
import {
  GET_SUBSCRIBED_PLAN_TYPE,
  LOGOUT_ENTOMO,
} from "@/graphql/layout/layoutQueries.js";
import { GET_ORGANIZATION_SUBSCRIPTION_DETAILS } from "@/graphql/billing/billingQueries";
import { RETRIEVE_SALARY_LIST } from "@/graphql/corehr/salaryQueries.js";
import { GET_EMPLOYEE_WORK_SCHEDULE_DETAILS } from "@/graphql/productivity-monitoring/activityDashboardQueries";
import {
  GET_PRESIGNED_URL,
  GET_EMPLOYEES_LIST,
  GET_ALL_EMPLOYEES,
  DELETE_PRESIGNED_URL,
  RETRIEVE_FORM_LEVEL_COVERAGE,
  UPDATE_FORM_LEVEL_COVERAGE,
  GET_EMPLOYEES_BASED_ON_CUSTOM_GROUP,
  RETRIEVE_PAYROLL_GENERAL_SETTINGS,
  TRIGGER_CONTROLLER_FUNCTION,
  RETRIEVE_DROPDOWN_DETAILS,
} from "@/graphql/commonQueries";
import {
  RETRIEVE_DROPDOWN_DATA,
  RETRIEVE_DESIGNATION_LIST,
  LIST_CUSTOM_GROUPS,
  LIST_WORKFLOW_DETAILS,
  LIST_LANGUAGE,
  LIST_COUNTRY,
} from "@/graphql/dropDownQueries";
import { GET_JOB_HEADER } from "@/graphql/recruitment/recruitmentQueries";
import {
  getErrorCodes,
  getErrorCodesAndMessagesWithValidation,
  extractNumberFromString,
  encryptObject,
} from "@/helper";

export default {
  //*Get user Ip address
  async fetchUserIp({ commit }) {
    var userIpAddress = "";
    await axios
      .get(config.ipAddressApi)
      .then((res) => {
        userIpAddress = res.data.ip;
        window.$cookies.set("userIpAddress", userIpAddress);
      })
      .catch(() => {
        userIpAddress = "";
      });
    commit("UPDATE_USER_IP_ADDRESS", userIpAddress);
  },

  //clear form lock if it set for any of the form user worked
  clearUserLock({ dispatch, getters }) {
    let baseUrl = getters.baseUrl;
    axios
      .get(baseUrl + "auth/index/clear-session-throw-lock")
      .then((clearLockResponse) => {
        if (clearLockResponse.data && clearLockResponse.data.success) {
          dispatch("userLogout");
        } else {
          dispatch("userLogout");
        }
      })
      .catch(() => {
        dispatch("userLogout");
      });
  },

  //*Logout Current User from firebase
  userLogout({ dispatch }) {
    let partnerid = window.$cookies.get("partnerid");
    if (partnerid && partnerid.toLowerCase() === "entomo") {
      const {
        clients: { apolloClientH },
      } = apolloProvider;
      return new Promise(() => {
        apolloClientH
          .mutate({
            mutation: LOGOUT_ENTOMO,
            variables: {
              xAuthToken: window.$cookies.get("accessToken"),
            },
          })
          .then((response) => {
            if (response.data.deleteS3Files) {
              dispatch("clearUserSession");
            } else {
              dispatch("clearUserSession");
            }
          })
          .catch(() => {
            dispatch("clearUserSession");
          });
      });
    } else {
      const firebaseCurrentUser = firebase.auth().currentUser;
      if (firebaseCurrentUser) {
        firebase
          .auth()
          .signOut()
          .then(() => {
            dispatch("clearUserSession");
          })
          .catch(() => {
            dispatch("clearUserSession");
          });
      } else {
        dispatch("clearUserSession");
      }
    }
  },
  // get address from google api with latitude and longitude
  getAddressFromLatLng(context, coordinates) {
    return new Promise((resolve, reject) => {
      // Validate coordinates parameter
      if (!coordinates || !coordinates.lat || !coordinates.lng) {
        reject("Invalid coordinates provided");
        return;
      }

      // Construct the Google Maps Geocoding API URL
      const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${coordinates.lat},${coordinates.lng}&key=${config.googleMapsAPIKey}`;

      // Make direct axios HTTP request to Google Maps Geocoding API
      axios
        .get(url, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          if (coordinates.addressType === "city") {
            resolve(response.data.results[6]?.formatted_address);
          } else {
            resolve(response.data.results[0]?.formatted_address);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  },

  // restore store to default value on logout
  clearUserSession({ commit, getters }) {
    commit("RESTORE_DEFAULT_STATE");
    localStorage.clear();
    window.$cookies.remove("empUid");
    window.$cookies.remove("accessToken");
    window.$cookies.remove("refreshToken");
    window.$cookies.remove("userIpAddress");
    location.replace(getters.baseUrl + "auth");
  },
  // action to retrieve tax amount based on new and old tax regime
  async retrieveTaxRegimeAmount({ dispatch, commit, getters }) {
    try {
      const apiObj = {
        // url:
        //   "https://fieldforce.hrapp.co.in/" +
        //   "payroll/tax-rules/compare-tax-regime",
        url: getters.baseUrl + "payroll/tax-rules/compare-tax-regime",
        type: "POST",
        // async: false,
        dataType: "json",
        data: {
          requestResource: "HRAPPUI",
        },
      };

      const response = await dispatch("triggerControllerFunction", apiObj);

      if (response && response.success) {
        var regimeDetails = response.regimeComparisionDetails;
        commit("UPDATE_TAX_COMPARISON_DETAILS", regimeDetails);
        //check whether want to show compare tax regime alert or not
        if (regimeDetails.Trigger_Regime_Change) {
          commit("SHOW_TAX_REGIME_SELECTION_OPTION", true);
        } else {
          commit("SHOW_TAX_REGIME_SELECTION_OPTION", false);
        }
        return regimeDetails;
      } else {
        //on error we hide the notify alert bar
        if (response && response.msg === "Session Expired") {
          dispatch("clearUserLock");
        } else {
          commit("SHOW_TAX_REGIME_SELECTION_OPTION", false);
        }
        return response;
      }
    } catch (retrieveTaxRegimeError) {
      //on error we hide the notify alert bar
      commit("SHOW_TAX_REGIME_SELECTION_OPTION", false);
      //on session expire error we clear session and redirect to auth
      if (retrieveTaxRegimeError.status === 200) {
        dispatch("clearUserLock");
      }
      throw retrieveTaxRegimeError;
    }
  },

  getPlanType({ dispatch }) {
    const {
      clients: { apolloClientC },
    } = apolloProvider;
    return new Promise((resolve) => {
      apolloClientC
        .query({
          query: GET_SUBSCRIBED_PLAN_TYPE,
        })
        .then((response) => {
          const {
            getOrganizationSubscribedPlan: { errorCode, subscribedDashboard },
          } = response.data;
          if (!errorCode && subscribedDashboard) {
            let planType = dispatch("setPlanType", subscribedDashboard);
            resolve(planType);
          } else {
            resolve(dispatch("setPlanType"));
          }
        })
        .catch(() => {
          resolve(dispatch("setPlanType"));
        });
    });
  },

  setPlanType(state, planType = "") {
    let dashboardType = "";
    // plan type is retrieved from BE, then we can use this and also set the value in cookie
    if (planType) {
      dashboardType = planType;
      // set expiry time as 17 days from current date
      let planTypeExpiryTime = moment().add(17, "days");
      window.$cookies.set(
        "orgSubscribedPlan",
        dashboardType,
        planTypeExpiryTime.toDate().toUTCString()
      );
    }
    // when plan is not retrieved from BE(any case of error in endpoint)
    else {
      // get the plan type from cookie
      let planTypeInCookie = window.$cookies.get("orgSubscribedPlan");
      /**
       * if the plan type is already exist in cookie, then we can get that value when we get error/empty response.
       * Otherwise we can set hrms plan type as default
       */
      dashboardType = planTypeInCookie ? planTypeInCookie : "HRMSDASHBOARD";
    }
    return dashboardType;
  },

  // get organization subscription details
  getOrganizationSubscriptionDetails({ commit }) {
    const {
      clients: { apolloClientG },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientG
        .query({
          query: GET_ORGANIZATION_SUBSCRIPTION_DETAILS,
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data) {
            let {
              getOrganizationBillingDetails: {
                errorCode,
                autoBilling,
                manualBillingDetails,
                autoBillingDetails,
              },
            } = response.data; //destructuring data
            if (!errorCode) {
              commit("UPDATE_AUTO_BILLING_FLAG", autoBilling);
              let orgBillingDetails = {
                isAutoBilling: autoBilling,
                currentStatus: "",
                demoCompleted: true,
                showPaymentAlert: false,
                isPaymentDueExceed: false,
                orgDemoPaymentDetails: {},
              };
              if (manualBillingDetails) {
                let manualBillingResponse = JSON.parse(manualBillingDetails);
                orgBillingDetails["orgDemoPaymentDetails"] =
                  manualBillingResponse;
                // if the status is present-demo-booking-card -> demo booking card present, with empty layout
                if (
                  manualBillingResponse.subscriptionAlertStatus ===
                  "present-demo-booking-card"
                ) {
                  orgBillingDetails["demoCompleted"] = false;
                }
                // if the status is present-red-payment-alert -> present red alert, with empty layout without any access.
                else if (
                  manualBillingResponse.subscriptionAlertStatus ===
                  "present-red-payment-alert"
                ) {
                  orgBillingDetails["isPaymentDueExceed"] = true;
                }
                // if the status is present-orange-payment-alert - > present alert, with accessing of the application
                else if (
                  manualBillingResponse.subscriptionAlertStatus ===
                  "present-orange-payment-alert"
                ) {
                  orgBillingDetails["showPaymentAlert"] = true;
                }
                // if autoBilling is enabled, then we can use autoBillingDetails
                if (
                  autoBilling &&
                  autoBillingDetails &&
                  !orgBillingDetails["isPaymentDueExceed"] &&
                  !orgBillingDetails["demoCompleted"]
                ) {
                  let autoBillingResponse = JSON.parse(autoBillingDetails);
                  const {
                    currentStatus,
                    trialRemainingDays,
                    subscriptionStatus,
                    subscriptionId,
                    subscribedPlanQuantity,
                    subscriptionCurrency,
                    subscriptionBillingPeriod,
                    portalPlanId,
                  } = autoBillingResponse;
                  commit(
                    "UPDATE_AUTO_BILLING_PLAN_CURRENT_STATUS",
                    currentStatus
                  );
                  commit("UPDATE_SUBSCRIPTION_PLAN_STATUS", subscriptionStatus);
                  commit("UPDATE_AUTO_BILLING_DETAILS", {
                    planId: portalPlanId,
                    subscriptionId: subscriptionId,
                    subscribedPlanQuantity: subscribedPlanQuantity,
                    subscriptionCurrency: subscriptionCurrency,
                    subscriptionBillingPeriod: subscriptionBillingPeriod,
                  });
                  commit("UPDATE_TRIAL_PERIOD", trialRemainingDays);
                  orgBillingDetails["currentStatus"] = currentStatus;
                  resolve(orgBillingDetails);
                } else {
                  resolve(orgBillingDetails);
                }
              } else {
                reject();
              }
            } else {
              reject();
            }
          } else {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  },

  //get employees list
  getEmployeesList(state, payload) {
    let flag = payload?.flag || "";
    let policy = payload?.fetchPolicy || "";
    const {
      clients: { apolloClientI },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientI
        .query({
          query: GET_EMPLOYEES_LIST,
          variables: {
            formName: payload.formName,
            formId: payload.formId,
            serviceProviderId: payload.serviceProviderId,
            customGroupId: payload.customGroupId,
            flag: flag,
            isSalaryEdit: payload.isSalaryEdit || false,
          },
          fetchPolicy: policy,
        })
        .then((response) => {
          const { errorCode, employeeDetails } =
            response.data.getEmployeesDetailsBasedOnRole;
          if (!errorCode) {
            resolve(employeeDetails);
          } else {
            reject("error");
          }
        })
        .catch(() => {
          reject("error");
        });
    });
  },
  getDropdownDetails(state, { apolloClient, payload }) {
    let key = payload?.key || [];
    let formId = payload?.formId || null;
    let conditionDetails = payload?.conditionDetails || [];
    return new Promise((resolve, reject) => {
      apolloClient
        .query({
          query: RETRIEVE_DROPDOWN_DETAILS,
          variables: {
            key: key,
            formId: formId,
            conditionDetails: conditionDetails,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          resolve(response);
        })
        .catch(() => {
          reject();
        });
    });
  },

  getSalaryDetails(state, payload) {
    const {
      clients: { apolloClientF },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientF
        .query({
          query: RETRIEVE_SALARY_LIST,
          variables: {
            formId: payload.formId || 207,
            accessFormId: payload.accessFormId || null,
            isViewMode: payload.isViewMode || false,
            employeeId: payload.employeeId || null,
            templateId: payload.templateId || null,
            isDropdown: payload.isDropdown || false,
            id: payload.id || null,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          resolve(response);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  getDefaultDropdownList(state, payload) {
    let status = payload !== undefined ? payload.status : "";
    let formId = payload !== undefined ? payload.formId : null;
    const {
      clients: { apolloClientA },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientA
        .query({
          query: RETRIEVE_DROPDOWN_DATA,
          variables: {
            status: status,
            formId: formId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          resolve(response);
        })
        .catch(() => {
          reject();
        });
    });
  },

  getDesignationList(state, payload) {
    let status = payload.status || "";
    let searchString = payload.searchString || "";
    let offset = payload.offset != undefined ? payload.offset : 0;
    let limit = payload.limit != undefined ? payload.limit : 25000;
    const {
      clients: { apolloClientA },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientA
        .query({
          query: RETRIEVE_DESIGNATION_LIST,
          variables: {
            status: status,
            searchString: searchString,
            offset: offset,
            limit: limit,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          resolve(response);
        })
        .catch(() => {
          reject();
        });
    });
  },

  s3FileUploadRetrieveAction({ commit }, payload) {
    const { clients } = apolloProvider;
    let aClient = payload.client
      ? clients[payload.client]
      : clients.apolloClientA;
    let errMsg = payload.action === "upload" ? "uploading" : "retrieving";
    errMsg =
      "Something went wrong while " +
      errMsg +
      " the file contents. Please try after some time.";
    return new Promise((resolve, reject) => {
      aClient
        .mutate({
          mutation: GET_PRESIGNED_URL,
          variables: payload,
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data.getPresignedUrl) {
            const { presignedUrl, s3DocumentDetails } =
              response.data.getPresignedUrl;
            if (presignedUrl) {
              if (payload.action === "upload") {
                axios
                  .put(presignedUrl, payload.fileContent)
                  .then(() => {
                    resolve();
                  })
                  .catch(() => {
                    let snackbarData = {
                      isOpen: true,
                      message: errMsg,
                      type: "warning",
                    };
                    commit("OPEN_SNACKBAR", snackbarData);
                    reject();
                  });
              } else {
                resolve(presignedUrl);
              }
            } else {
              if (
                (payload.action === "getdata" ||
                  payload.action === "getrawdata") &&
                s3DocumentDetails
              ) {
                resolve(s3DocumentDetails);
              } else {
                let snackbarData = {
                  isOpen: true,
                  message: errMsg,
                  type: "warning",
                };
                commit("OPEN_SNACKBAR", snackbarData);
                reject();
              }
            }
          } else {
            let snackbarData = {
              isOpen: true,
              message: errMsg,
              type: "warning",
            };
            commit("OPEN_SNACKBAR", snackbarData);
            reject();
          }
        })
        .catch(() => {
          let snackbarData = {
            isOpen: true,
            message: errMsg,
            type: "warning",
          };
          commit("OPEN_SNACKBAR", snackbarData);
          reject();
        });
    });
  },

  //deleting s3 file
  deletes3File(state, payload) {
    const { clients } = apolloProvider;
    let aClient = payload.client
      ? clients[payload.client]
      : clients.apolloClientA;
    return new Promise((resolve, reject) => {
      aClient
        .mutate({
          mutation: DELETE_PRESIGNED_URL,
          variables: payload,
        })
        .then((response) => {
          if (response.data.deleteS3Files) {
            resolve(response.data.deleteS3Files);
          } else {
            reject("error");
          }
        })
        .catch(() => {
          reject("error");
        });
    });
  },

  // retrieve payroll general settings
  retrievePayrollGeneralSettings({ dispatch }) {
    const {
      clients: { apolloClientAI },
    } = apolloProvider;
    const handleApiError = (err) => {
      dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "payroll settings",
        isListError: false,
      });
    };
    return new Promise((resolve, reject) => {
      apolloClientAI
        .query({
          query: RETRIEVE_PAYROLL_GENERAL_SETTINGS,
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          const { errorCode, listPayrollGeneralSettingsData } =
            response.data.listPayrollGeneralSettings;
          if (!errorCode) {
            resolve(
              listPayrollGeneralSettingsData
                ? listPayrollGeneralSettingsData
                : []
            );
          } else {
            handleApiError(null);
            reject();
          }
        })
        .catch((err) => {
          handleApiError(err);
          reject();
        });
    });
  },

  //updating form level coverage
  updateFormLevelCoverage({ dispatch }, payload) {
    const {
      clients: { apolloClientJ },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientJ
        .mutate({
          mutation: UPDATE_FORM_LEVEL_COVERAGE,
          variables: {
            formName: payload.formName,
            Coverage: payload.Coverage,
            Coverage_Id: payload.Coverage_Id,
          },
        })
        .then((response) => {
          if (response.data.updateFormLevelCoverage) {
            resolve(response.data.updateFormLevelCoverage);
          } else {
            let formName = payload.formName.toLowerCase();
            dispatch("handleApiErrors", {
              error: "",
              action: "updating",
              form: formName + " coverage",
              isListError: false,
            });
            reject();
          }
        })
        .catch((err) => {
          let formName = payload.formName.toLowerCase();
          dispatch("handleApiErrors", {
            error: err,
            action: "updating",
            form: formName + " coverage",
            isListError: false,
          });
          reject();
        });
    });
  },
  // retrieve form level coverage
  retrieveFormLevelCoverage({ dispatch }, payload) {
    const {
      clients: { apolloClientI },
    } = apolloProvider;

    const handleApiError = (err, payload) => {
      const formName = payload.formName.toLowerCase();
      dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: formName + " coverage",
        isListError: false,
      });
    };

    return new Promise((resolve, reject) => {
      apolloClientI
        .query({
          query: RETRIEVE_FORM_LEVEL_COVERAGE,
          variables: {
            formName: payload.formName,
            Form_Id: payload.Form_Id,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          const { errorCode, formLevelCoverage } =
            response.data.retrieveFormLevelCoverage;
          if (!errorCode) {
            resolve(formLevelCoverage ? formLevelCoverage : []);
          } else {
            handleApiError(null, payload);
            reject();
          }
        })
        .catch((err) => {
          handleApiError(err, payload);
          reject();
        });
    });
  },

  // retrieve employees based on custom group
  retrieveEmployeesBasedOnCustomGroup({ dispatch }, payload) {
    const {
      clients: { apolloClientL },
    } = apolloProvider;

    const handleApiError = (err) => {
      dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "employees associated with custom group",
        isListError: false,
      });
    };

    return new Promise((resolve, reject) => {
      apolloClientL
        .query({
          query: GET_EMPLOYEES_BASED_ON_CUSTOM_GROUP,
          variables: {
            customGroupId: payload.customGroupId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          const { errorCode, employeeDetails } =
            response.data.getEmployeeDetailsBasedOnGroup;
          if (!errorCode) {
            resolve(employeeDetails ? employeeDetails : []);
          } else {
            handleApiError(null, payload);
            reject();
          }
        })
        .catch((err) => {
          handleApiError(err, payload);
          reject();
        });
    });
  },

  getAllEmployeesList(state, { formName, isActiveOnly = 1 }) {
    const {
      clients: { apolloClientC },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientC
        .query({
          query: GET_ALL_EMPLOYEES,
          variables: {
            designationId: [],
            departmentId: [],
            empTypeId: [],
            workScheduleId: [],
            locationId: [],
            formName: formName,
            onlyActiveEmployees: isActiveOnly,
          },
        })
        .then((response) => {
          const { errorCode, employeeList } =
            response.data.listAllEmployeeDetails;
          if (!errorCode && employeeList) {
            resolve(employeeList);
          } else {
            reject("error");
          }
        })
        .catch((fetchError) => {
          if (fetchError && fetchError.graphQLErrors) {
            let errorCode = getErrorCodes(fetchError);
            if (errorCode) {
              switch (errorCode) {
                case "_DB0000": //- Technical error
                  reject(
                    "It’s us! There seems to be some technical difficulties while fetching the employees. Please try after some time."
                  );
                  break;
                case "DB0100": // This employee do not have view access rights
                  reject(
                    "Sorry, you don't have access rights to view the employees. Please contact the HR administrator."
                  );
                  break;
                case "EM0007": // Error while listing the employees.
                case "_DB0002": // Error while checking the employee access rights
                case "_DB0104": // While check access rights form not found.
                default:
                  reject("error");
                  break;
              }
            } else {
              reject("error");
            }
          } else {
            reject("error");
          }
        });
    });
  },

  // list custom group based on form name
  listCustomGroupBasedOnFormName(state, payload) {
    const {
      clients: { apolloClientC },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientC
        .query({
          query: LIST_CUSTOM_GROUPS,
          variables: {
            formName: payload.formName,
            preApprovalId: payload.preApprovalId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          const { errorCode, customGroups } =
            response.data.listCustomEmployeeGroups;
          if (!errorCode) {
            resolve(customGroups ? customGroups : []);
          } else {
            reject("error");
          }
        })
        .catch(() => {
          reject("error");
        });
    });
  },
  // list workflow group based on form name
  listWorkflowDetailsBasedOnFormName(state, payload) {
    const {
      clients: { apolloClientI },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientI
        .query({
          query: LIST_WORKFLOW_DETAILS,
          variables: {
            formName: payload.formName,
            formId: payload.formId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          const { errorCode, workflowDetails } =
            response.data.listWorkflowDetails;
          if (!errorCode) {
            resolve(workflowDetails ? workflowDetails : []);
          } else {
            reject("error");
          }
        })
        .catch(() => {
          reject("error");
        });
    });
  },

  listLanguages(state, payload) {
    const { clients } = apolloProvider;
    let aClient =
      payload && payload.client
        ? clients[payload.client]
        : clients.apolloClientI;
    return new Promise((resolve, reject) => {
      aClient
        .query({
          query: LIST_LANGUAGE,
        })
        .then((response) => {
          const { languages } = response.data.listLanguages;
          resolve(languages && languages.length > 0 ? languages : []);
        })
        .catch(() => {
          reject("error");
        });
    });
  },

  listCountries(state, payload) {
    const { clients } = apolloProvider;
    let aClient =
      payload && payload.client
        ? clients[payload.client]
        : clients.apolloClientI;
    return new Promise((resolve, reject) => {
      aClient
        .query({
          query: LIST_COUNTRY,
        })
        .then((response) => {
          const { countries } = response.data.listCountries;
          resolve(countries && countries.length > 0 ? countries : []);
        })
        .catch(() => {
          reject("error");
        });
    });
  },

  handleApiErrors({ dispatch, commit }, payload) {
    return new Promise((resolve) => {
      const { error, action, form, isListError } = payload;
      console.error("error", error);
      console.error("error.message", error.message);
      console.error("error.graphQLErrors", error.graphQLErrors);
      let errorMessage = `Something went wrong while ${action} the ${form}.`;
      let secondMsg = " Please try after some time.";
      if (error && error.graphQLErrors && error.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodesAndMessagesWithValidation(error);
        if (errorCode && errorCode.length > 0) {
          switch (errorCode[0]) {
            case "_DB0000": // Technical error
              errorMessage =
                "It’s us! There seems to be some technical difficulties. Please try after some time - _DB0000";
              break;
            case "_DB0100": // This employee do not have view access rights
              errorMessage =
                "Sorry, you don't have view access rights. Please contact the HR administrator - _DB0100";
              break;
            case "_DB0102": // update access denied
              errorMessage =
                "Sorry, you don't have update access rights. Please contact the HR administrator - _DB0102";
              break;
            case "_DB0103": // delete access denied
              errorMessage =
                "Sorry, you don't have delete access rights. Please contact the HR administrator - _DB0103";
              break;
            case "_DB0101": // add access denied
              errorMessage =
                "Sorry, you don't have add access rights. Please contact the HR administrator - _DB0101";
              break;
            case "_DB0111":
              var formAccess = action === "adding" ? "add" : "update";
              errorMessage =
                "Sorry, you don't have " +
                formAccess +
                " access rights. Please contact the HR administrator - DB0111";
              break;
            case "_UH0001": // unhandled error
              errorMessage =
                errorMessage +
                " If you continue to see this issue please contact the platform administrator - _UH0001";
              break;
            case "ESS0126":
            case "IO0135":
              var failedData = [];
              if (
                error &&
                error.graphQLErrors &&
                error.graphQLErrors.length > 0
              ) {
                var firstError = error.graphQLErrors[0];

                if (firstError.extensions && firstError.extensions.failedData) {
                  failedData = firstError.extensions.failedData;
                }
              }
              if (errorCode[0] !== "IO0135") {
                errorMessage = errorCode[1] + " - " + errorCode[0];
              } else {
                errorMessage = null;
              }
              resolve(failedData);
              break;
            case "BAD_USER_INPUT":
              if (errorCode[2]) {
                errorMessage = "";
                resolve(errorCode[2]);
              }
              break;
            case "INTERNAL_SERVER_ERROR":
              var eMsg = "",
                eCode = "",
                vError = "";
              try {
                var parsedObj = JSON.parse(errorCode[1]); // Parse the JSON
                eMsg = parsedObj.message || errorCode[1]; // Return the message property or the original string
                eCode = parsedObj.errorCode;
                vError = parsedObj.validationError;
              } catch {
                eMsg = errorCode[1]; // Return the original string if there's an error
                eCode = "";
                vError = "";
              }
              if (eMsg) {
                if (eMsg.toLowerCase() === "invalid input request" && vError) {
                  const errorArray = Object.values(vError);
                  errorMessage = "";
                  resolve(errorArray);
                } else {
                  errorMessage = eMsg;
                }
                if (eCode) {
                  errorMessage = errorMessage + " - " + eCode;
                }
              } else {
                errorMessage = errorMessage + secondMsg;
              }
              break;
            default:
              errorMessage = errorCode[1] + " - " + errorCode[0];
              break;
          }
        } else {
          errorMessage = errorMessage + secondMsg;
        }
      } else if (error && error.message) {
        // 500/400 series errors
        let eCode = extractNumberFromString(error.message);
        eCode = eCode.toString();
        if (eCode === "502" || eCode === "504") {
          errorMessage =
            "Sorry, there was a problem processing your request due to a temporary issue. If the problem persists, feel free to contact our support team for assistance" +
            " - " +
            eCode;
        } else if (eCode === "400") {
          errorMessage =
            `Oops! We encountered a problem while ${action} the ${form}. Please contact the platform administrator.` +
            " - " +
            eCode;
        } else if (eCode === "401") {
          dispatch("clearUserLock");
        } else {
          errorMessage = errorMessage + secondMsg + " - " + eCode;
        }
      } else {
        errorMessage = errorMessage + secondMsg;
      }
      if (errorMessage) {
        if (isListError) {
          resolve(errorMessage);
        } else {
          let snackbarData = {
            isOpen: true,
            message: errorMessage,
            type: "warning",
          };
          commit("OPEN_SNACKBAR", snackbarData);
        }
      }
    });
  },
  // action to get employee's work schedule details
  getEmpWorkScheduleDetails(state, payload) {
    const {
      clients: { apolloClientK },
    } = apolloProvider;
    return new Promise((resolve, reject) => {
      apolloClientK
        .query({
          query: GET_EMPLOYEE_WORK_SCHEDULE_DETAILS,
          fetchPolicy: "no-cache",
          variables: {
            employeeId: payload.employeeId,
          },
        })
        .then((res) => {
          try {
            const { errorCode, employeeWorkScheduleDetails } =
              res.data.getEmployeeWorkScheduleDetails;
            if (!errorCode && employeeWorkScheduleDetails) {
              resolve(employeeWorkScheduleDetails);
            } else {
              reject();
            }
          } catch {
            reject();
          }
        })
        .catch(() => {
          reject();
        });
    });
  },
  triggerControllerFunction(state, payload) {
    const {
      clients: { apolloClientBB },
    } = apolloProvider;
    let encryptedPayload = encryptObject(payload);

    return new Promise((resolve, reject) => {
      apolloClientBB
        .mutate({
          mutation: TRIGGER_CONTROLLER_FUNCTION,
          fetchPolicy: "no-cache",
          variables: encryptedPayload,
        })
        .then((res) => {
          let { triggerControllerFunction } = res.data;
          if (triggerControllerFunction && triggerControllerFunction.response) {
            let response = JSON.parse(triggerControllerFunction.response);
            resolve(response);
          } else {
            reject();
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  getRecruitmentSettings(state, payload) {
    const clientName = payload.client || "apolloClientA";

    // Get the specified client from apolloProvider
    const { clients } = apolloProvider;
    const apolloClient = clients[clientName];

    return new Promise((resolve, reject) => {
      const queryOptions = {
        query: GET_JOB_HEADER,
        variables: {
          formId: payload.formId || null,
        },
        fetchPolicy: "no-cache",
      };

      apolloClient
        .query(queryOptions)
        .then((response) => {
          if (
            response.data &&
            response.data.recruitmentSetting &&
            response.data.recruitmentSetting.settingResult[0] &&
            !response.data.recruitmentSetting.errorCode
          ) {
            resolve(response.data.recruitmentSetting.settingResult[0]);
          } else {
            const errorCode =
              response.data?.recruitmentSetting?.errorCode || "";
            reject(new Error(errorCode));
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  },
};
