import { createStore } from "vuex";
import state from "./state";
import mutations from "./mutations";
import actions from "./actions";
import getters from "./getters";
// modules
import moduleDashboard from "./dashboard/moduleDashboard";
import moduleWorkflow from "./workflow/moduleWorkflow";
import moduleEmployeeProfile from "./employee-profile/moduleEmployeeProfile";
import moduleOnboarding from "./onboarding/moduleOnboarding.js";

export default createStore({
  state: state,
  getters: getters,
  mutations: mutations,
  actions: actions,
  modules: {
    dashboard: moduleDashboard,
    workflow: moduleWorkflow,
    employeeProfile: moduleEmployeeProfile,
    onboarding: moduleOnboarding,
  },
});
