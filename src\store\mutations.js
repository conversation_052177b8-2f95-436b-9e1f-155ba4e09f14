const mutation = {
  UPDATE_WINDOW_WIDTH(state, width) {
    state.windowWidth = width;
    state.isMobileWindowSize = width > 600 ? false : true;
  },
  UPDATE_WINDOW_HEIGHT(state, height) {
    state.windowInnerHeight = height;
  },
  UPDATE_USER_INFO(state, user) {
    state.currentUser = user;
  },
  UPDATE_USER_IP_ADDRESS(state, payload) {
    state.userIpAddress = payload;
  },
  RESTORE_DEFAULT_STATE(state) {
    state.currentUser = null;
  },
  UPDATE_SIDEBAR_MENU(state, moduleList) {
    state.sideBarMenuList = moduleList;
  },
  UPDATE_FORM_ACCESS_RIGHTS(state, accessRights) {
    state.formAccessRights = accessRights;
  },
  UPDATE_FORM_ID_ACCESS_RIGHTS(state, accessRights) {
    state.formIdAccessRights = accessRights;
  },
  //mutation to update whether user is an admin or not
  UPDATE_IS_ADMIN(state, isAdmin) {
    state.isAdmin = isAdmin;
  },
  UPDATE_IS_RECRUITER(state, isRecruiter) {
    state.isRecruiter = isRecruiter;
  },
  //mutation to update whether user is a manager or not
  UPDATE_IS_MANAGER(state, isManager) {
    state.isManager = isManager;
  },
  UPDATE_EMP_SEARCH_VAL(state, value) {
    state.empSearchValue = value;
  },
  // update organization details
  UPDATE_ORGANIZATION_DETAILS(state, orgDetails) {
    if (orgDetails && orgDetails.employeeId) {
      localStorage.setItem("LoginEmpId", orgDetails.employeeId);
    }
    state.orgDetails =
      orgDetails && Object.keys(orgDetails).length > 0
        ? orgDetails
        : state.orgDetails;
  },
  // update user details
  UPDATE_USER_DETAILS(state, userDetails) {
    state.userDetails =
      userDetails && Object.keys(userDetails).length > 0
        ? userDetails
        : state.userDetails;
  },
  Entomo_Sync_Type_Push(state, isEntomoSyncTypePush) {
    state.isEntomoSyncTypePush = isEntomoSyncTypePush;
  },

  UPDATE_PAYROLL_MANAGEMENT_URL(state, url) {
    state.payrollUrl = url;
  },

  UPDATE_PAYROLL_COUNTRY(state, countryCode) {
    state.payrollCountry = countryCode;
  },

  UPDATE_TEMPLATE_ENABLED(state, templateEnabled) {
    state.templateEnabled = templateEnabled;
  },

  UPDATE_PAYROLL_CURRENCY(state, currency) {
    state.payrollCurrency = currency;
  },

  //mutation to show/hide request access in top notification bar
  SHOW_REQUEST_ACCESS_NOTIFICATION(state, value) {
    state.showRequestAccessNotification = value;
  },

  //mutation to show/hide tax regime selection in top notification bar
  SHOW_TAX_REGIME_SELECTION_OPTION(state, value) {
    state.showTaxRegimeSelection = value;
  },

  //mutation to update new and old tax amount
  UPDATE_TAX_COMPARISON_DETAILS(state, regimeDetails) {
    state.regimeComparisonDetails = regimeDetails;
  },

  //mutation to show/hide new and old tax comparison modal in dashboard
  SHOW_TAX_COMPARISON_MODAL(state, value) {
    state.showTaxComparisonModal = value;
  },

  //mutation to show snackbar to present any error or success messages
  OPEN_SNACKBAR(state, snackbarData) {
    state.showSnackbar = snackbarData.isOpen;
    state.snackBarMessage = snackbarData.message;
    state.snackBarType = snackbarData.type;
  },

  //mutation to close snackbar and set it to default values
  CLOSE_SNACKBAR(state) {
    state.showSnackbar = false;
    state.snackBarMessage = "";
    state.snackBarType = "warning";
  },

  //to know whether browser Supports Webp or not
  UPDATE_WEBP_SUPPORTS(state, value) {
    state.isWebpSupport = value;
  },

  // set the dashboard type chosen in the plan
  UPDATE_PLAN_DASHBOARD_TYPE(state, value) {
    state.planDashboardType = value;
  },

  // set the subscribed plan status
  UPDATE_SUBSCRIPTION_PLAN_STATUS(state, value) {
    state.planSubscriptionStatus = value;
  },

  // set the trial period when the plan is in trial
  UPDATE_TRIAL_PERIOD(state, value) {
    state.trialPeriod = value;
  },
  // update the app tracking mode - stealth/consent based
  UPDATE_APP_TRACKING_MODE(state, value) {
    state.appTrackingMode = value;
  },
  UPDATE_AUTO_BILLING_PLAN_CURRENT_STATUS(state, value) {
    state.autoBillingPlanCurrentStatus = value;
  },

  UPDATE_TOPBAR_CLEAR_FLAG(state, flag) {
    state.clearTopBarSearch = flag;
  },

  UPDATE_AUTO_BILLING_FLAG(state, flag) {
    state.isAutoBilling = flag;
  },

  UPDATE_AUTO_BILLING_DETAILS(state, value) {
    state.autoBillingDetails = value;
  },

  UPDATE_COORDINATES(state, value) {
    state.coordinates = value;
  },

  UPDATE_CHARGEBEE_SITE_NAME(state, value) {
    state.chargeBeeSiteName = value;
  },

  // update field details
  UPDATE_CUSTOM_FIELDS(state, customFormFields) {
    state.customFormFields =
      customFormFields && Object.keys(customFormFields).length > 0
        ? customFormFields
        : state.customFormFields;
  },
  UPDATE_APPROVAL_COUNT(state, count) {
    state.currentApprovalCount = count;
  },
  UPDATE_PROJECT_LABEL(state, label) {
    state.projectLabel = label;
  },

  SET_SHOW_JOB_ROUNDS_TAB(state, value) {
    state.showJobRoundsTab = value;
  },
};
export default mutation;
