export default {
  en: {
    /* ----- Authentication Layout ----- */
    // Authentication pages
    login: "Login",
    register: "Register",
    forgotPassword: "Forgot Password",
    resetPassword: "Reset Password",
    verifyEmail: "Verify Email",

    // Form labels
    email: "Email",
    password: "Password",
    confirmPassword: "Confirm Password",
    currentPassword: "Current Password",
    newPassword: "New Password",
    rememberMe: "Remember Me",

    // Buttons
    signIn: "Sign In",
    signUp: "Sign Up",
    sendResetLink: "Send Reset Link",
    reset: "Reset",
    verify: "Verify",
    resendVerification: "Resend Verification",
    backToLogin: "Back to Login",

    // Messages
    loginSuccess: "Successfully logged in",
    registerSuccess: "Registration successful",
    passwordResetSuccess: "Password reset successful",
    emailVerificationSent: "Verification email sent",
    emailVerified: "Email verified successfully",
    invalidCredentials: "Invalid email or password",
    passwordMismatch: "Passwords do not match",
    weakPassword: "Password is too weak",
    emailRequired: "Email is required",
    passwordRequired: "Password is required",
    sessionExpired: "Your session has expired",

    // Layout specific translations
    requestAccess: "Request Access",
    accessDeniedMessage:
      "Please contact your HR Admin/Manager to get access to this page",
    accessDeniedMessageMobile:
      "Request access to explore more product features",
    taxRegimeMessage: "You may have an opportunity to save tax. Check it out !",
    taxRegimeButton: "Compare and Choose",
    quickMenu: "Quick Menu",
    dashboard: "Dashboard",
    menuSearch: "Menu Search",
    newLabel: "New",
    termsOfUse: "Terms of use",
    privacyPolicy: "Privacy Policy",
    retry: "Retry",
    accessDeniedTitle: "Access to this page is restricted",
    accessDeniedContent:
      "To explore more! Would you mind requesting access by clicking the `Request Access` button below?",
    skipForNow: "Skip for now",
    camuIntegrationWarning:
      "CAMU Integration is incomplete. Please review the API configuration",
    ok: "Ok",
    profile: "Profile",
    logout: "Logout",

    // Additional layout messages
    accessRestricted:
      "Access restricted, please choose the plan and activate your account",
    requestSentSuccess: "Request sent successfully",
    somethingWentWrong:
      "Something went wrong. If you continue to see this issue, please contact the platform administrator.",
    technicalIssues:
      "It's us! There seems to be some technical difficulties. Please try after some time.",
    requestAccessError:
      "Something went wrong while sending access request for modules. Please try after some time.",
    noAccessToForms: "This employee does not have an access to any forms.",
    modulesFormsNotExist: "Modules and forms does not exist.",
    errorRetrievingDetails:
      "Error while retrieving the modules and forms details.",
    contactSystemAdmin: "Something went wrong! Please contact system admin.",
    dataSetupDashboard: "Data Setup Dashboard",

    // Announcements and Notifications
    technicalDifficulties:
      "There seems to be some technical difficulties while fetching data.",
    refresh: "Refresh",
    announcementCount: "You have {count} Announcement(s)",
    notificationCount: "You have {count} Notification(s)",
    caughtUpAnnouncements: "You've caught up all announcements",
    caughtUpNotifications: "You've caught up all notifications",
    newContentNotification:
      "We'll let you know when we've got something new for you.",
    invalidEmbedUrl: "Invalid Embed Url",

    // Approval Messages
    attendanceApproval:
      "{count} Attendance request(s) waiting for your approval.",
    compensatoryOffApproval:
      "{count} Compensatory off request(s) waiting for your approval.",
    leaveApproval: "{count} Leave request(s) waiting for your approval.",
    resignationApproval:
      "{count} Resignation request(s) waiting for your approval.",
    shortTimeOffApproval:
      "{count} Short time off request(s) waiting for your approval.",
    timesheetApproval:
      "{count} Timesheet request(s) waiting for your approval.",
    transferApproval: "{count} Transfer request(s) waiting for your approval.",
    travelApproval: "{count} Travel request(s) waiting for your approval.",
    advanceSalaryApproval:
      "{count} Advance salary request(s) waiting for your approval.",
    bonusApproval: "{count} Bonus request(s) waiting for your approval.",
    commissionApproval:
      "{count} Commission request(s) waiting for your approval.",
    deductionApproval:
      "{count} Deduction request(s) waiting for your approval.",
    financialClosure: "Execute the financial closure process.",
    loanApproval: "{count} Loan request(s) waiting for your approval.",
    deferredLoanApproval:
      "{count} Deferred loan request(s) waiting for your approval.",
    shiftAllowanceApproval:
      "{count} Shift allowance request(s) waiting for your approval.",
    taxDeclarationApproval:
      "{count} Tax declaration request(s) waiting for your approval.",
    taxDeclarationUpload:
      "{count} Tax declaration request(s) waiting for document upload.",
    recruitmentApproval:
      "{count} Recruitment request(s) waiting for your approval.",
    recruitmentRequestApproval:
      "{count} Recruitment request(s) waiting for your approval.",
    myprofileApproval:
      "{count} My profile request(s) waiting for your approval.",
    teamsummaryApproval:
      "{count} Team summary request(s) waiting for your approval.",
    salaryRevisionApproval:
      "{count} Salary revision request(s) waiting for your approval.",
    newPositionApproval:
      "{count} New position request(s) waiting for your approval.",
    jobPostApproval: "{count} Job post request(s) waiting for your approval.",
    lopRecoveryApproval:
      "{count} LOP recovery request(s) waiting for your approval.",
    onDutyPreApprovals:
      "{count} On-duty pre-approval request(s) waiting for your approval.",
    workFromHomeApproval:
      "{count} Work from home request(s) waiting for your approval.",
    overTimePreApproval:
      "{count} Over-time pre-approval request(s) waiting for your approval.",
    workDuringWeekOffApproval:
      "{count} Work during week-off request(s) waiting for your approval.",
    workDuringHolidayApproval:
      "{count} Work during holiday request(s) waiting for your approval.",
    housePropertyRecordApproval:
      "{count} House property record request(s) waiting for your approval.",
    housePropertyUpload:
      "{count} House property upload request(s) waiting for document upload.",
    reimbursementApproval:
      "{count} Reimbursement request(s) waiting for your approval.",
  },
  sp: {
    /* ----- Authentication Layout ----- */
    // Authentication pages
    login: "Iniciar Sesión",
    register: "Registrarse",
    forgotPassword: "Olvidé mi Contraseña",
    resetPassword: "Restablecer Contraseña",
    verifyEmail: "Verificar Email",

    // Form labels
    email: "Correo Electrónico",
    password: "Contraseña",
    confirmPassword: "Confirmar Contraseña",
    currentPassword: "Contraseña Actual",
    newPassword: "Nueva Contraseña",
    rememberMe: "Recordarme",

    // Buttons
    signIn: "Iniciar Sesión",
    signUp: "Registrarse",
    sendResetLink: "Enviar Enlace de Restablecimiento",
    reset: "Restablecer",
    verify: "Verificar",
    resendVerification: "Reenviar Verificación",
    backToLogin: "Volver al Inicio de Sesión",

    // Messages
    loginSuccess: "Sesión iniciada correctamente",
    registerSuccess: "Registro exitoso",
    passwordResetSuccess: "Contraseña restablecida correctamente",
    emailVerificationSent: "Email de verificación enviado",
    emailVerified: "Email verificado correctamente",
    invalidCredentials: "Correo electrónico o contraseña inválidos",
    passwordMismatch: "Las contraseñas no coinciden",
    weakPassword: "La contraseña es demasiado débil",
    emailRequired: "El correo electrónico es requerido",
    passwordRequired: "La contraseña es requerida",
    sessionExpired: "Tu sesión ha expirado",

    // Layout specific translations
    requestAccess: "Solicitar Acceso",
    accessDeniedMessage:
      "Por favor, contacte a su Administrador de HR/Gerente para obtener acceso a esta página",
    accessDeniedMessageMobile:
      "Solicite acceso para explorar más funciones del producto",
    taxRegimeMessage:
      "Puede tener la oportunidad de ahorrar impuestos. ¡Compruébalo!",
    taxRegimeButton: "Comparar y elegir",
    quickMenu: "Menú Rápido",
    dashboard: "Panel de Control",
    menuSearch: "Búsqueda de menú",
    newLabel: "Nuevo",
    termsOfUse: "Términos de uso",
    privacyPolicy: "Política de Privacidad",
    retry: "Reintentar",
    accessDeniedTitle: "El acceso a esta página está restringido",
    accessDeniedContent:
      "¡Para explorar más! ¿Le importaría solicitar acceso haciendo clic en el botón `Solicitar Acceso` a continuación?",
    skipForNow: "Omitir por ahora",
    camuIntegrationWarning:
      "La integración de CAMU está incompleta. Por favor, revise la configuración de la API",
    ok: "Aceptar",
    profile: "Perfil",
    logout: "Cerrar Sesión",

    // Additional layout messages
    accessRestricted:
      "Acceso restringido, por favor elija el plan y active su cuenta",
    requestSentSuccess: "Solicitud enviada exitosamente",
    somethingWentWrong:
      "Algo salió mal. Si continúa viendo este problema, por favor contacte al administrador de la plataforma.",
    technicalIssues:
      "¡Somos nosotros! Parece que hay algunas dificultades técnicas. Por favor, intente después de un tiempo.",
    requestAccessError:
      "Algo salió mal al enviar la solicitud de acceso para los módulos. Por favor, intente después de un tiempo.",
    noAccessToForms: "Este empleado no tiene acceso a ningún formulario.",
    modulesFormsNotExist: "Los módulos y formularios no existen.",
    errorRetrievingDetails:
      "Error al recuperar los detalles de módulos y formularios.",
    contactSystemAdmin:
      "¡Algo salió mal! Por favor contacte al administrador del sistema.",
    dataSetupDashboard: "Panel de Configuración de Datos",

    // Announcements and Notifications
    technicalDifficulties:
      "Parece que hay algunas dificultades técnicas al obtener los datos.",
    refresh: "Actualizar",
    announcementCount: "Tienes {count} Anuncio(s)",
    notificationCount: "Tienes {count} Notificación(es)",
    caughtUpAnnouncements: "Has visto todos los anuncios",
    caughtUpNotifications: "Has visto todas las notificaciones",
    newContentNotification: "Te avisaremos cuando tengamos algo nuevo para ti.",
    invalidEmbedUrl: "URL de incrustación no válida",

    // Approval Messages
    attendanceApproval:
      "{count} Solicitud(es) de asistencia esperando su aprobación.",
    compensatoryOffApproval:
      "{count} Solicitud(es) de día compensatorio esperando su aprobación.",
    leaveApproval: "{count} Solicitud(es) de permiso esperando su aprobación.",
    resignationApproval:
      "{count} Solicitud(es) de renuncia esperando su aprobación.",
    shortTimeOffApproval:
      "{count} Solicitud(es) de tiempo libre corto esperando su aprobación.",
    timesheetApproval:
      "{count} Solicitud(es) de hoja de tiempo esperando su aprobación.",
    transferApproval:
      "{count} Solicitud(es) de transferencia esperando su aprobación.",
    travelApproval: "{count} Solicitud(es) de viaje esperando su aprobación.",
    advanceSalaryApproval:
      "{count} Solicitud(es) de adelanto de salario esperando su aprobación.",
    bonusApproval: "{count} Solicitud(es) de bono esperando su aprobación.",
    commissionApproval:
      "{count} Solicitud(es) de comisión esperando su aprobación.",
    deductionApproval:
      "{count} Solicitud(es) de deducción esperando su aprobación.",
    financialClosure: "Ejecutar el proceso de cierre financiero.",
    loanApproval: "{count} Solicitud(es) de préstamo esperando su aprobación.",
    deferredLoanApproval:
      "{count} Solicitud(es) de préstamo diferido esperando su aprobación.",
    shiftAllowanceApproval:
      "{count} Solicitud(es) de subsidio por turno esperando su aprobación.",
    taxDeclarationApproval:
      "{count} Solicitud(es) de declaración de impuestos esperando su aprobación.",
    taxDeclarationUpload:
      "{count} Solicitud(es) de declaración de impuestos esperando la carga de documentos.",
    recruitmentApproval:
      "{count} Solicitud(es) de reclutamiento esperando su aprobación.",
    recruitmentRequestApproval:
      "{count} Solicitud(es) de reclutamiento esperando su aprobación.",
    myprofileApproval:
      "{count} Solicitud(es) de actualización de perfil esperando su aprobación.",
    teamsummaryApproval:
      "{count} Solicitud(es) de actualización de resumen de equipo esperando su aprobación.",
    salaryRevisionApproval:
      "{count} Solicitud(es) de revisión de salario esperando su aprobación.",
    newPositionApproval:
      "{count} Solicitud(es) de nueva posición esperando su aprobación.",
    jobPostApproval:
      "{count} Solicitud(es) de publicación de empleo esperando su aprobación.",
    lopRecoveryApproval:
      "{count} Solicitud(es) de recuperación de LOP esperando su aprobación.",
    onDutyPreApprovals:
      "{count} Solicitud(es) de pre-aprobación de servicio esperando su aprobación.",
    workFromHomeApproval:
      "{count} Solicitud(es) de trabajo desde casa esperando su aprobación.",
    overTimePreApproval:
      "{count} Solicitud(es) de pre-aprobación de horas extras esperando su aprobación.",
    workDuringWeekOffApproval:
      "{count} Solicitud(es) de trabajo durante fin de semana esperando su aprobación.",
    workDuringHolidayApproval:
      "{count} Solicitud(es) de trabajo durante días festivos esperando su aprobación.",
    housePropertyRecordApproval:
      "{count} Registro(s) de propiedad esperando su aprobación.",
    housePropertyUpload:
      "{count} Solicitud(es) de carga de propiedad esperando la carga de documentos.",
    reimbursementApproval:
      "{count} Solicitud(es) de reembolso esperando su aprobación.",
  },
};
