export default {
  en: {
    /* ----- Employee Data Management ----- */
    // Tabs
    customGroup: "Custom Group",
    projects: "Projects",
    holidays: "Holidays",
    userAccounts: "User Accounts",
    documentSubtype: "Document Subtype",
    employeeDataImport: "Employee Data Import",
    accreditationCategoryAndType: "Accreditation Category and Type",
    registerFace: "Register Face",

    /* ----- Organizational Structure Forms ----- */
    // Form names for navigation tabs
    employeeType: "Employee Type",
    grades: "Grades",
    locations: "Locations",
    departmentHierarchy: "Department Hierarchy",
    designationsPositions: "Designations/Positions",
    jobRoles: "Job Roles",
    workSchedule: "Work Schedule",
    businessUnitCostCenter: "Business Unit / Cost Center",
    organizationGroup: "Organization Group",
    coregroup: "Core Group",

    // ----- Accreditations Category and Type -----
    // section titles
    viewAccreditationCategoryAndType: "View Accreditation Category and Type",
    addAccreditationCategoryAndType: "Add Accreditation Category and Type",
    editAccreditationCategoryAndType: "Edit Accreditation Category and Type",
    addAccreditationEnforcementGroup: "Add Accreditation Enforcement Group",
    newAccreditationEnforcementGroup: "New Accreditation Enforcement Group",

    // Table headers
    accreditationCategory: "Accreditation Category",
    accreditationType: "Accreditation Type",
    mandatorySelfOnboarding: "Mandatory during Self Onboarding",
    enforceDependent: "Enforce Dependent",
    document: "Document",
    actions: "Actions",
    instruction: "Instruction",

    // Filter labels
    mandatoryDuringSelfOnboarding: "Mandatory during Self Onboarding",
    yes: "Yes",
    no: "No",
    accreditationEnforcementGroup: "Accreditation Enforcement Group",

    // Buttons and actions
    addNew: "Add New",
    resetFilterSearch: "Reset Filter/Search",
    export: "Export",
    edit: "Edit",
    new: "New",
    close: "Close",
    retry: "Retry",
    viewDocument: "view document",
    cancel: "Cancel",
    save: "Save",
    submit: "Submit",
    add: "Add",
    delete: "Delete",
    addDocumentEnforcementGroup: "Add Document Enforcement Group",

    // Messages
    noAccreditationsForFilters:
      "There are no accreditations for the selected filters/searches.",
    accessDeniedMessage: "You don't have access to perform this action.",
    accreditationHelp1:
      "Accreditation Category and Type helps manage different accreditations and handle them accordingly",
    accreditationHelp2:
      "Accreditation Category and Type helps manage, update, and add new accreditations as needed.",
    exitFormWarning: "Are you sure to exit this form?",
    accreditationTypeUpdated: "Accreditation type updated successfully.",
    accreditationTypeAdded: "Accreditation type added successfully.",
    accreditationEnforcementGroupAdded:
      "Accreditation Enforcement Group added successfully.",
    fileHint:
      "Max size: 3 MB. Supported formats: png, jpeg, jpg, pdf, doc, docx, txt.",
    fileSizeError: "The file size should be less than 3 MB.",
    instructionError:
      "Please create the instruction according to hints provided",
    instructionHint1:
      "The instruction must contain {here} for presenting the uploaded document",
    instructionHint2:
      "Example: Please download the document {here}. Fill it and then upload it.",
    added: "Added",
    updated: "Updated",
    editorError: "Error initializing editor",

    /* ----- Document Subtype ----- */
    // Section titles
    viewDocumentSubtype: "View Document Subtype",
    addDocumentSubtype: "Add Document Subtype",
    editDocumentSubtype: "Edit Document Subtype",

    // Table headers
    documentType: "Document Type",
    documentCategory: "Document Category",
    enforcedInSelfOnboarding: "Enforced in Self Onboarding",
    enforceDuringOnboarding: "Enforce during Onboarding",
    allowedForEmailCommunication: "Allowed for Email Communication",
    emailTemplates: "Email Templates",
    documentDownloadedDuringOnboarding:
      "Document to be download during self onboarding",
    documentEnforcementGroup: "Document Enforcement Group",
    instructionForPresentingDocument:
      "Instruction for Presenting the document in Self Onboarding",
    addedOn: "Added On",
    addedBy: "Added By",
    updatedOn: "Updated On",
    updatedBy: "Updated By",

    // Messages
    noDocumentSubtypes:
      "There are no Document subtype for the selected filters/searches.",
    documentSubtypeUpdated: "Document subtype updated successfully",
    documentSubtypeAdded: "Document subtype added successfully",
    documentSubtypeDeleted: "Document subtype deleted successfully",
    documentSubtypeHelp1:
      "Document subtype helps manage different documents and handle them according to their categories and types",

    /* ----- Per Diem Management ----- */
    // Section titles
    perDiem: "Per Diem",
    viewPerDiem: "View Per Diem",
    addPerDiem: "Add Per Diem",
    editPerDiem: "Edit Per Diem",

    // Form fields
    configurationType: "Configuration Type",
    country: "Country",
    title: "Title",
    currency: "Currency",
    expenseType: "Expense Type",
    description: "Description",
    perDiemRate: "Per Diem Rate",
    status: "Status",
    includeTravelDate: "Include Travel Dates",

    // Messages
    perDiemNotesCard1:
      "Per diem management allows organizations to set standardized daily allowances for employee travel and business expenses. This ensures consistent reimbursement policies across different countries and travel scenarios.",
    perDiemNotesCard2:
      "Configure per diem rates based on countries or create general policies for international travel. The system supports flexible rate structures with optional travel date inclusions.",
    noRecordsForFilters:
      "There are no per diem records for the selected filters/searches.",
  },
  fr: {
    /* ----- Employee Data Management ----- */
    // Tabs
    customGroup: "Groupe personnalisé",
    projects: "Projets",
    holidays: "Congés",
    userAccounts: "Comptes utilisateurs",
    documentSubtype: "Sous-type de document",
    employeeDataImport: "Importation des données employés",
    accreditationCategoryAndType: "Catégorie et type d'accréditation",
    registerFace: "Enregistrer le visage",

    /* ----- Organizational Structure Forms ----- */
    // Form names for navigation tabs
    employeeType: "Type d'Employé",
    grades: "Grades",
    locations: "Emplacements",
    departmentHierarchy: "Hiérarchie des Départements",
    designationsPositions: "Désignations/Postes",
    jobRoles: "Rôles de Travail",
    workSchedule: "Horaire de Travail",
    businessUnitCostCenter: "Unité d'Affaires / Centre de Coûts",
    organizationGroup: "Groupe d'Organisation",
    coregroup: "Groupe de noyau",

    // section titles
    viewAccreditationCategoryAndType:
      "Voir la catégorie et le type d'accréditation",
    addAccreditationCategoryAndType:
      "Ajouter une catégorie et un type d'accréditation",
    editAccreditationCategoryAndType:
      "Modifier la catégorie et le type d'accréditation",
    addAccreditationEnforcementGroup:
      "Ajouter un groupe d'application d'accréditation",
    newAccreditationEnforcementGroup:
      "Nouveau groupe d'application d'accréditation",

    // Table headers
    accreditationCategory: "Catégorie d'accréditation",
    accreditationType: "Type d'accréditation",
    mandatorySelfOnboarding: "Obligatoire pendant l'intégration autonome",
    enforceDependent: "Appliquer la dépendance",
    document: "Document",
    actions: "Actions",
    instruction: "Instruction",

    // Filter labels
    mandatoryDuringSelfOnboarding: "Obligatoire pendant l'intégration autonome",
    yes: "Oui",
    no: "Non",
    accreditationEnforcementGroup: "Groupe d'application d'accréditation",

    // Buttons and actions
    addNew: "Ajouter nouveau",
    resetFilterSearch: "Réinitialiser filtre/recherche",
    export: "Exporter",
    edit: "Modifier",
    new: "Nouveau",
    close: "Fermer",
    retry: "Réessayer",
    viewDocument: "voir le document",
    cancel: "Annuler",
    save: "Enregistrer",
    submit: "Soumettre",
    add: "Ajouter",
    delete: "Supprimer",
    addDocumentEnforcementGroup:
      "Ajouter un groupe d'application des documents",

    // Messages
    noAccreditationsForFilters:
      "Il n'y a pas d'accréditations pour les filtres/recherches sélectionnés.",
    accessDeniedMessage: "Vous n'avez pas accès pour effectuer cette action.",
    accreditationHelp1:
      "La catégorie et le type d'accréditation permettent de gérer différentes accréditations et de les traiter en conséquence",
    accreditationHelp2:
      "La catégorie et le type d'accréditation permettent de gérer, mettre à jour et ajouter de nouvelles accréditations selon les besoins.",
    exitFormWarning: "Êtes-vous sûr de vouloir quitter ce formulaire ?",
    accreditationTypeUpdated: "Type d'accréditation mis à jour avec succès.",
    accreditationTypeAdded: "Type d'accréditation ajouté avec succès.",
    accreditationEnforcementGroupAdded:
      "Groupe d'application d'accréditation ajouté avec succès.",
    fileHint:
      "Taille max : 3 Mo. Formats supportés : png, jpeg, jpg, pdf, doc, docx, txt.",
    fileSizeError: "La taille du fichier doit être inférieure à 3 Mo.",
    instructionError: "Veuillez créer l'instruction selon les conseils fournis",
    instructionHint1:
      "L'instruction doit contenir {here} pour présenter le document téléchargé",
    instructionHint2:
      "Exemple : Veuillez télécharger le document {here}. Remplissez-le puis téléchargez-le.",
    added: "Ajouté",
    updated: "Mis à jour",
    editorError: "Erreur d'initialisation de l'éditeur",

    /* ----- Document Subtype ----- */
    // Section titles
    viewDocumentSubtype: "Voir le sous-type de document",
    addDocumentSubtype: "Ajouter un sous-type de document",
    editDocumentSubtype: "Modifier le sous-type de document",

    // Table headers
    documentType: "Type de document",
    documentCategory: "Catégorie de document",
    enforcedInSelfOnboarding: "Obligatoire dans l'auto-intégration",
    enforceDuringOnboarding: "Appliquer pendant l'intégration",
    allowedForEmailCommunication: "Autorisé pour la communication par e-mail",
    emailTemplates: "Modèles d'e-mail",
    documentDownloadedDuringOnboarding:
      "Document à télécharger pendant l'intégration",
    documentEnforcementGroup: "Groupe d'application des documents",
    instructionForPresentingDocument:
      "Instruction pour présenter le document pendant l'intégration",
    addedOn: "Ajouté le",
    addedBy: "Ajouté par",
    updatedOn: "Mis à jour le",
    updatedBy: "Mis à jour par",

    // Messages
    noDocumentSubtypes:
      "Il n'y a pas de sous-type de document pour les filtres/recherches sélectionnés.",
    documentSubtypeUpdated: "Sous-type de document mis à jour avec succès",
    documentSubtypeAdded: "Sous-type de document ajouté avec succès",
    documentSubtypeDeleted: "Sous-type de document supprimé avec succès",
    documentSubtypeHelp1:
      "Le sous-type de document aide à gérer différents documents et à les traiter selon leurs catégories et types",

    /* ----- Per Diem Management ----- */
    // Section titles
    perDiem: "Indemnité Journalière",
    viewPerDiem: "Voir l'Indemnité Journalière",
    addPerDiem: "Ajouter une Indemnité Journalière",
    editPerDiem: "Modifier l'Indemnité Journalière",

    // Form fields
    configurationType: "Type de Configuration",
    country: "Pays",
    title: "Titre",
    currency: "Monnaie",
    expenseType: "Type de dépense",
    description: "Description",
    perDiemRate: "Taux d'Indemnité Journalière",
    status: "Statut",
    includeTravelDate: "Inclure les dates de voyage",

    // Messages
    perDiemNotesCard1:
      "La gestion des indemnités journalières permet aux organisations de définir des allocations quotidiennes standardisées pour les voyages d'affaires et les dépenses des employés. Cela garantit des politiques de remboursement cohérentes dans différents pays et scénarios de voyage.",
    perDiemNotesCard2:
      "Configurez les taux d'indemnités journalières basés sur les pays ou créez des politiques générales pour les voyages internationaux. Le système prend en charge des structures de taux flexibles avec inclusions optionnelles des dates de voyage.",
    noRecordsForFilters:
      "Il n'y a pas d'enregistrements d'indemnités journalières pour les filtres/recherches sélectionnés.",
  },
  ja: {
    /* ----- Employee Data Management ----- */
    // Tabs
    customGroup: "カスタムグループ",
    projects: "プロジェクト",
    holidays: "休日",
    userAccounts: "ユーザーアカウント",
    documentSubtype: "文書サブタイプ",
    employeeDataImport: "従業員データインポート",
    accreditationCategoryAndType: "認定カテゴリーと種類",
    registerFace: "顔を登録する",

    /* ----- Organizational Structure Forms ----- */
    // Form names for navigation tabs
    employeeType: "従業員タイプ",
    grades: "等級",
    locations: "場所",
    departmentHierarchy: "部門階層",
    designationsPositions: "指定/役職",
    jobRoles: "職務役割",
    workSchedule: "勤務スケジュール",
    businessUnitCostCenter: "事業単位 / コストセンター",
    organizationGroup: "組織グループ",
    coregroup: "コアグループ",

    // section titles
    viewAccreditationCategoryAndType: "認定カテゴリーと種類を表示",
    addAccreditationCategoryAndType: "認定カテゴリーと種類を追加",
    editAccreditationCategoryAndType: "認定カテゴリーと種類を編集",
    addAccreditationEnforcementGroup: "認定執行グループを追加",
    newAccreditationEnforcementGroup: "新規認定執行グループ",

    // Table headers
    accreditationCategory: "認定カテゴリー",
    accreditationType: "認定種類",
    mandatorySelfOnboarding: "セルフオンボーディング時の必須",
    enforceDependent: "依存関係を強制",
    document: "文書",
    actions: "アクション",
    instruction: "指示",

    // Filter labels
    mandatoryDuringSelfOnboarding: "セルフオンボーディング時の必須",
    yes: "はい",
    no: "いいえ",
    accreditationEnforcementGroup: "認定執行グループ",

    // Buttons and actions
    addNew: "新規追加",
    resetFilterSearch: "フィルター/検索をリセット",
    export: "エクスポート",
    edit: "編集",
    new: "新規",
    close: "閉じる",
    retry: "再試行",
    viewDocument: "文書を表示",
    cancel: "キャンセル",
    save: "保存",
    submit: "送信",
    add: "追加",
    delete: "削除",
    addDocumentEnforcementGroup: "文書執行グループを追加",

    // Messages
    noAccreditationsForFilters:
      "選択されたフィルター/検索に該当する認定はありません。",
    accessDeniedMessage: "このアクションを実行する権限がありません。",
    accreditationHelp1:
      "認定カテゴリーと種類は、様々な認定を管理し、適切に処理するのに役立ちます",
    accreditationHelp2:
      "認定カテゴリーと種類は、必要に応じて認定の管理、更新、新規追加を行うのに役立ちます。",
    exitFormWarning: "このフォームを終了してもよろしいですか？",
    accreditationTypeUpdated: "認定種類が正常に更新されました。",
    accreditationTypeAdded: "認定種類が正常に追加されました。",
    accreditationEnforcementGroupAdded:
      "認定執行グループが正常に追加されました。",
    fileHint:
      "最大サイズ：3MB。対応フォーマット：png、jpeg、jpg、pdf、doc、docx、txt。",
    fileSizeError: "ファイルサイズは3MB未満である必要があります。",
    instructionError: "提供されたヒントに従って指示を作成してください",
    instructionHint1:
      "指示には、アップロードされた文書を表示するために {here} を含める必要があります",
    instructionHint2:
      "例：文書 {here} をダウンロードしてください。記入後、アップロードしてください。",
    added: "追加済み",
    updated: "更新済み",
    editorError: "エディターの初期化エラー",

    /* ----- Document Subtype ----- */
    // Section titles
    viewDocumentSubtype: "文書サブタイプを表示",
    addDocumentSubtype: "文書サブタイプを追加",
    editDocumentSubtype: "文書サブタイプを編集",

    // Table headers
    documentType: "文書タイプ",
    documentCategory: "文書カテゴリー",
    enforcedInSelfOnboarding: "セルフオンボーディングで強制",
    enforceDuringOnboarding: "オンボーディング中に適用",
    allowedForEmailCommunication: "メール通信許可",
    emailTemplates: "メールテンプレート",
    documentDownloadedDuringOnboarding:
      "オンボーディング中にダウンロードする文書",
    documentEnforcementGroup: "文書執行グループ",
    instructionForPresentingDocument:
      "オンボーディング中に文書を提示するための指示",
    addedOn: "追加日",
    addedBy: "追加者",
    updatedOn: "更新日",
    updatedBy: "更新者",

    // Messages
    noDocumentSubtypes:
      "選択されたフィルター/検索に該当する文書サブタイプはありません。",
    documentSubtypeUpdated: "文書サブタイプが正常に更新されました",
    documentSubtypeAdded: "文書サブタイプが正常に追加されました",
    documentSubtypeDeleted: "文書サブタイプが正常に削除されました",
    documentSubtypeHelp1:
      "文書サブタイプは、異なる文書を管理し、それらのカテゴリーと種類に応じて処理するのに役立ちます",

    /* ----- Per Diem Management ----- */
    // Section titles
    perDiem: "日当",
    viewPerDiem: "日当を表示",
    addPerDiem: "日当を追加",
    editPerDiem: "日当を編集",

    // Form fields
    configurationType: "タイプの設定",
    country: "国",
    title: "タイトル",
    currency: "通貨",
    expenseType: "経費の種類",
    description: "説明",
    perDiemRate: "日当レート",
    status: "ステータス",
    includeTravelDate: "旅行日を含める",

    // Messages
    perDiemNotesCard1:
      "日当管理により、組織は従業員の出張や業務費用に対する標準化された日額手当を設定できます。これにより、異なる国や出張シナリオ間で一貫した償還ポリシーが確保されます。",
    perDiemNotesCard2:
      "国に基づいて日当レートを設定するか、国際出張用の一般的なポリシーを作成します。システムは、出張日の任意の包含を伴う柔軟なレート構造をサポートします。",
    noRecordsForFilters:
      "選択されたフィルター/検索に該当する日当記録はありません。",
  },
  sp: {
    /* ----- Employee Data Management ----- */
    // Tabs
    customGroup: "Grupo Personalizado",
    projects: "Proyectos",
    holidays: "Días Festivos",
    userAccounts: "Cuentas de Usuario",
    documentSubtype: "Subtipo de Documento",
    employeeDataImport: "Importación de Datos de Empleados",
    accreditationCategoryAndType: "Categoría y Tipo de Acreditación",
    registerFace: "Registrar rostro",

    /* ----- Organizational Structure Forms ----- */
    // Form names for navigation tabs
    // employeeType: "Tipo de Empleado",
    // grades: "Grados",
    // locations: "Ubicaciones",
    // departmentHierarchy: "Jerarquía de Departamentos",
    designationsPositions: "Designaciones/Posiciones",
    // jobRoles: "Roles de Trabajo",
    workSchedule: "Horario de Trabajo",
    // businessUnitCostCenter: "Unidad de Negocio / Centro de Costos",
    // organizationGroup: "Grupo de Organización",
    // coregroup: "Grupo de Núcleo",

    // section titles
    viewAccreditationCategoryAndType: "Ver Categoría y Tipo de Acreditación",
    addAccreditationCategoryAndType: "Agregar Categoría y Tipo de Acreditación",
    editAccreditationCategoryAndType: "Editar Categoría y Tipo de Acreditación",
    addAccreditationEnforcementGroup:
      "Agregar Grupo de Aplicación de Acreditación",
    newAccreditationEnforcementGroup:
      "Nuevo Grupo de Aplicación de Acreditación",

    // Table headers
    accreditationCategory: "Categoría de Acreditación",
    accreditationType: "Tipo de Acreditación",
    mandatorySelfOnboarding: "Obligatorio durante la Incorporación Automática",
    enforceDependent: "Aplicar Dependiente",
    document: "Documento",
    actions: "Acciones",
    instruction: "Instrucción",

    // Filter labels
    mandatoryDuringSelfOnboarding:
      "Obligatorio durante la Incorporación Automática",
    yes: "Sí",
    no: "No",
    accreditationEnforcementGroup: "Grupo de Aplicación de Acreditación",

    // Buttons and actions
    addNew: "Agregar Nuevo",
    resetFilterSearch: "Restablecer Filtro/Búsqueda",
    export: "Exportar",
    edit: "Editar",
    new: "Nuevo",
    close: "Cerrar",
    retry: "Reintentar",
    viewDocument: "ver documento",
    cancel: "Cancelar",
    save: "Guardar",
    submit: "Enviar",
    add: "Agregar",
    delete: "Eliminar",
    addDocumentEnforcementGroup: "Agregar Grupo de Aplicación de Documentos",

    // Messages
    noAccreditationsForFilters:
      "No hay acreditaciones para los filtros/búsquedas seleccionados.",
    accessDeniedMessage: "No tienes acceso para realizar esta acción.",
    accreditationHelp1:
      "La Categoría y Tipo de Acreditación ayuda a gestionar diferentes acreditaciones y manejarlas en consecuencia",
    accreditationHelp2:
      "La Categoría y Tipo de Acreditación ayuda a gestionar, actualizar y agregar nuevas acreditaciones según sea necesario.",
    exitFormWarning: "¿Estás seguro de salir de este formulario?",
    accreditationTypeUpdated: "Tipo de acreditación actualizado exitosamente.",
    accreditationTypeAdded: "Tipo de acreditación agregado exitosamente.",
    accreditationEnforcementGroupAdded:
      "Grupo de Aplicación de Acreditación agregado exitosamente.",
    fileHint:
      "Tamaño máximo: 3 MB. Formatos soportados: png, jpeg, jpg, pdf, doc, docx, txt.",
    fileSizeError: "El tamaño del archivo debe ser menor a 3 MB.",
    instructionError:
      "Por favor crea la instrucción de acuerdo a las pistas proporcionadas",
    instructionHint1:
      "La instrucción debe contener {here} para presentar el documento subido",
    instructionHint2:
      "Ejemplo: Por favor descarga el documento {here}. Llénalo y luego súbelo.",
    added: "Agregado",
    updated: "Actualizado",
    editorError: "Error al inicializar el editor",

    /* ----- Document Subtype ----- */
    // Section titles
    viewDocumentSubtype: "Ver Subtipo de Documento",
    addDocumentSubtype: "Agregar Subtipo de Documento",
    editDocumentSubtype: "Editar Subtipo de Documento",

    // Table headers
    documentType: "Tipo de Documento",
    documentCategory: "Categoría de Documento",
    enforcedInSelfOnboarding: "Aplicado en la Incorporación Automática",
    enforceDuringOnboarding: "Aplicar durante la Incorporación",
    allowedForEmailCommunication: "Permitido para Comunicación por Email",
    emailTemplates: "Plantillas de Email",
    documentDownloadedDuringOnboarding:
      "Documento a descargar durante la incorporación automática",
    documentEnforcementGroup: "Grupo de Aplicación de Documentos",
    instructionForPresentingDocument:
      "Instrucción para Presentar el documento en la Incorporación Automática",
    addedOn: "Agregado el",
    addedBy: "Agregado por",
    updatedOn: "Actualizado el",
    updatedBy: "Actualizado por",

    // Messages
    noDocumentSubtypes:
      "No hay subtipos de documento para los filtros/búsquedas seleccionados.",
    documentSubtypeUpdated: "Subtipo de documento actualizado exitosamente",
    documentSubtypeAdded: "Subtipo de documento agregado exitosamente",
    documentSubtypeDeleted: "Subtipo de documento eliminado exitosamente",
    documentSubtypeHelp1:
      "El subtipo de documento ayuda a gestionar diferentes documentos y manejarlos de acuerdo a sus categorías y tipos",

    /* ----- Per Diem Management ----- */
    // Section titles
    perDiem: "Viáticos",
    viewPerDiem: "Ver Viáticos",
    addPerDiem: "Agregar Viáticos",
    editPerDiem: "Editar Viáticos",

    // Form fields
    configurationType: "Tipo de Configuración",
    country: "País",
    title: "Título",
    currency: "Moneda",
    expenseType: "Tipo de Gasto",
    description: "Descripción",
    perDiemRate: "Tarifa de Viáticos",
    status: "Estado",
    includeTravelDate: "Incluir Fechas de Viaje",

    // Messages
    perDiemNotesCard1:
      "La gestión de viáticos permite a las organizaciones establecer asignaciones diarias estandarizadas para viajes de empleados y gastos comerciales. Esto asegura políticas de reembolso consistentes en diferentes países y escenarios de viaje.",
    perDiemNotesCard2:
      "Configure tarifas de viáticos basadas en países o cree políticas generales para viajes internacionales. El sistema admite estructuras de tarifas flexibles con inclusiones opcionales de fechas de viaje.",
    noRecordsForFilters:
      "No hay registros de viáticos para los filtros/búsquedas seleccionados.",
  },
};
