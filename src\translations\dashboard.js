export default {
  en: {
    // Dashboard Navigation
    myDashboard: "My Dashboard",
    organizationDashboard: "Organization Dashboard",
    teamDashboard: "Team Dashboard",

    // Admin Dashboard
    leaveDistribution: "Leave Distribution",
    myTeamUpdates: "My Team Updates",
    myTeamAvailability: "My Team Availability",
    myTeamAction: "My Team Action",

    // Manager Dashboard
    teamMembers: "Team Members",
    presentToday: "Present Today",
    pendingApprovals: "Pending Approvals",
    teamPerformance: "Team Performance",
    quickActions: "Quick Actions",

    // Statistics Labels
    active: "Active",
    inactive: "Inactive",
    present: "Present",
    absent: "Absent",
    onLeave: "Leave",
    leaveApprovals: "Leave Approvals",
    otherApprovals: "Other Approvals",
    recentJoiners: "Recent Joiners",
    employee: "Employee",

    // Team Update Types
    birthday: "Birthday",
    anniversary: "Anniversary",
    newJoiner: "New Joiner",
    promotion: "Promotion",
    leave: "Leave",
    compOff: "Comp Off",
    unscheduledShift: "Unscheduled Shift",
    office: "Office",
    WFH: "WFH",
    field: "Field",
    others: "Others",
    onTimeArrivals: "On Time Arrivals",
    lateArrivals: "Late Arrivals",

    // Tax Regime Comparison
    taxRegimeComparison: "Tax Regime Comparison",
    recommended: "Recommended",
    totalTax: "Total Tax",
    businessIncomeQuestion: "Do you have business income",
    noInvestmentToDeclare: "No investment to declare",
    proceedWithITDeclaration: "Proceed with IT declaration",
    taxDeclarationHelp:
      "Before choosing the tax regime for TDS calculation, please make sure you are declaring all your investments.",
    businessIncomeAcknowledgement:
      "I acknowledge that in the case of business income,  the regime change will not be allowed for future financial years.",
    regimeChangeAcknowledgement:
      "I acknowledge that I can choose my regime only once for the current financial year.",
    compareTaxOutgo:
      "Compare your tax outgo with the old and new regime before choosing the best one.",
    taxRegimeNotification:
      "As per the income tax department's notification, the regime can be selected only once in the financial year.",
    totalIncomeFinancialYear: "Your total income for this financial year",
    stopNotificationWarning:
      "Once you stop the notification, you will not be notified further and default Tax Regime at organization level will be chosen automatically for you",
    taxRegimeUpdateError:
      "Something went wrong while updating the tax regime. Please try after some time.",

    // Team Update Messages
    hasBirthdayToday: "has birthday today",
    celebratesWorkAnniversary: "celebrates work anniversary",
    joinedTheTeam: "joined the team",
    gotPromoted: "got promoted",
    isOnLeave: "is on leave",
    checkedInLate: "checked in late",

    // Availability Status
    goodAvailability: "Good Availability",
    moderateAvailability: "Moderate Availability",
    lowAvailability: "Low Availability",

    // Action Types
    leaveRequest: "Leave Request",
    attendanceRegularization: "Attendance Regularization",
    overtimeRequest: "Overtime Request",
    expenseClaim: "Expense Claim",
    probationReview: "Probation Review",
    probation: "Probation",
    salaryAdvance: "Salary Advance",

    // Action Messages
    requestedLeave: "requested leave",
    requestedAttendanceRegularization: "requested attendance regularization",
    requestedOvertime: "requested overtime",
    submittedExpenseClaim: "submitted expense claim",
    probationReviewDue: "probation review due",
    requestedSalaryAdvance: "requested salary advance",

    // Action Buttons
    approve: "Approve",
    reject: "Reject",
    approveAction: "Approve Action",
    rejectAction: "Reject Action",
    confirmApproveAction:
      "Are you sure you want to approve this {action} for {employee}?",
    confirmRejectAction:
      "Are you sure you want to reject this {action} for {employee}?",

    // Quick Actions
    attendance: "Attendance",
    reports: "Reports",
    reviews: "Reviews",
    teamDirectory: "Team Directory",
    settings: "Settings",

    // Empty States
    relaxed: "Relaxed",
    refreshedRecharged: "Refreshed, Recharged",
    teamwork: "Teamwork",
    makesTheDreamWork: "Makes the Dream Work",
    aKeyToSuccess: "A key to achieving success",
    assembleStrongStableManagementTeam:
      "is to assemble a strong and stable management team.",
    strengthInNumbers: "Strength in Numbers",
    allCaughtUp: "All Caught Up",
    noActionsRequired: "No Actions Required",
    greatThings: "Great things in business",
    areNeverDone:
      "are never done by one person they are done by a team of people.",

    // Motivational Quotes
    lifeIs10Percent: "Life is 10%",
    whatHappensToYou: "what happens to you",
    and90Percent: "And 90%",
    howYouReactToIt: "how you react to it",

    // Error Messages
    technicalDifficulties:
      "There seems to be some technical difficulties while fetching data.",
    adminDashboardError:
      "Something went wrong in the admin dashboard. Please try again.",
    managerDashboardError:
      "Something went wrong in the manager dashboard. Please try again.",
    statisticsError:
      "Something went wrong while loading statistics. Please try again.",
    actionProcessedSuccessfully: "Action processed successfully",
    actionProcessingError: "Error processing action. Please try again.",

    // Mobile Update Modal
    newVersionTitle: "New Version Available",
    newVersionSubtitle: "A new version of the app is available",
    newVersionQuestion: "Would you like to update now?",
    dismissButton: "Dismiss",
    updateButton: "Update",

    // probation pop-up
    probationDate: "Probation Date",
    confirmationDate: "Confirmation Date",
    extendProbation: "Extend Probation Period",
    confirmEmployee: "Confirm Employee",

    // Dashboard Loading Errors
    dashboardLoadError:
      "Something went wrong while loading the dashboard. Please try again.",

    // Employee Dashboard - Leave History
    myLeaves: "My Leaves",
    peopleWait: "People Wait",
    leaveWaitingMessage:
      "`all week for friday, all year for summer, all life for happiness`",
    atWork: "At work",
    late: "Late",
    leaveTakenTooltip: "Leave taken during the leave year",
    leaveTaken: "Leave Taken",
    leaveBalanceTooltip:
      "Leave balance available as of today for leave application",
    leaveBalance: "Leave Balance",
    currentLeaveEligibilityTooltip:
      "Current leave eligibility is based on leave period configuration and carry over balance",
    currentLeaveEligibility: "Current Leave Eligibility",
    totalLeaveEligibilityTooltip:
      "Total leave eligibility based on leave year and carry over balance",
    totalLeaveEligibility: "Total Leave Eligibility",
    currentEligibility: "Current Eligibility",
    totalEligibility: "Total Eligibility",
    leaveDataError:
      "Something went wrong while loading leave data. Please try after some time.",

    // Employee Dashboard - Profile
    workedToday: "Worked Today",
    refreshToRetrieveAttendance: "Refresh to retrieve attendance",
    retry: "Retry",
    checkedInAt: "Checked in at",
    viewAttendanceEntries: "View Attendance Entries",
    attendanceEntries: "Attendance Entries",
    noEntriesFound: "No entries found",
    geoNotAccurate: "Geo-Coordinates may not be accurate",
    proceedNow: "Proceed Now",
    gpsNotEnabled: "GPS Not Enabled",
    gpsNotEnabledContent: "Please enable GPS to continue",
    mobViewGPS:
      "Your organization enforced to capture GEO coordinates during check-in and check-out process. Since you have denied access to retrieve the location data you may need to manually allow the app permission to access the device location and turn on the location service before clicking on `Proceed` ",
    deskViewGPS:
      "Please check if you have given location access for this app. If it`s already given, GPS/Location service in your smartphone/browser is turned off, and please enable them to add attendance.",
    refresh: "Refresh",
    employeeProfileError:
      "Something went wrong while loading employee profile. Please try after some time.",
    faceNotRegistered: "Face has not been registered yet. Please register.",
    attendanceAddedSuccessfully: "Attendance added successfully",
    somethingWentWrong: "Something went wrong. Please try again.",
    outsideGeoFence:
      "You are not in the geo fencing area. Please check your location and try again.",
    faceVerificationFailed: "Face verification failed",
    faceVerificationError: "Face verification error",
    attendanceConfigurationError: "Attendance configuration error",
    invalidGeoFencingConfiguration:
      "Invalid Geo Fencing Configuration. Please contact your administrator.",

    // Employee Dashboard - Actions
    myActions: "My Actions",
    lifeIsPercent: "Life is 10%",
    andPercent: "And 90%",
    youMissed: "You missed",
    checkIn: "Check In",
    and: "and",
    checkOut: "Check Out",
    add: "Add",
    days: "Days",
    hours: "Hours",
    actionsDataError: "Something went wrong while loading actions data",
    attendanceCannotBeRegularized:
      "Attendance cannot be regularized as you have exceeded the regularization request limit per month.",

    // Employee Dashboard - Company Updates
    myCompanyUpdates: "My Company Updates",
    companyUpdateMessage: "When you hand good people possibilities,",
    companyUpdateSecondary: "they do great things",
    birthdays: "Birthdays",
    workAnniversary: "Work Anniversary",
    awards: "Awards",
    today: "Today",
    happyBirthday: "Happy Birthday",
    leaveClosureTitle: "Leave closure month is here",
    leaveClosureContent:
      "It's time for leave closure and conclude leave year-end activities like leave to carry forward, lapsing, encashment, and so on.",
    leaveClosureActivities:
      "The following activities have to be performed for a smooth transition to the next leave year.",
    leaveApprovalDeadline: "Leave approval till",
    initiateLeaveEncashment: "Initiate the leave encashment.",
    runLeaveClosureProcess: "Run leave closure process",
    grantLeaveCredit: "Grant/credit the leave for",
    leaveCreditPolicy: "as per your organization policy.",
    goAhead: "Go Ahead",

    // Employee Dashboard - Compliance
    myCompliance: "My Compliance",
    expiryDate: "Expiry Date",
    complianceDataError: "Something went wrong while loading compliance data",

    // Employee Dashboard - Utilization
    myUtilization: "My Utilization",
    myTotalWorkedDays: "My total worked days",
    utilizationTooltip:
      "Utilization is determined by comparing the number of days worked to the total business working days in a given payroll period. It's assessed only for finalized payroll cycles.",
    organizationWorkingDays: "Organization working days",
    myTimeOff: "My Time Off",
    apply: "Apply",
    timeManagement: "Time Management",
    viewFlow: "View Flow",
    viewStatus: "View Status",
    utilizationDataError: "Something went wrong while loading utilization data",
    workedDays: "Worked Days",
    remainingDays: "Remaining Days",
    utilization: "Utilization",

    // Employee Dashboard - Holidays and Calendar
    myUpcomingHolidays: "My Upcoming Holidays",
    autumnShowsUs: "Autumn shows us",
    howBeautifulLetGo: "how beautiful it is to let things go",
    mandatory: "Mandatory",
    holiday: "Holiday",
    personalChoice: "Personal Choice",
    businessHours: "Business Hours",

    // Camera component
    takePhoto: "Take Photo",
    pleaseSmile: "PLEASE SMILE",
    openYourMouth: "OPEN YOUR MOUTH",
    // Pop-up's
    request: "Request",
    selectWorkPlace: "Select Work Place",
    selectDurationFor: "Select the duration for",
  },

  fr: {
    // Dashboard Navigation
    myDashboard: "Mon Tableau de Bord",
    organizationDashboard: "Tableau de Bord Organisation",
    teamDashboard: "Tableau de Bord Équipe",

    // Admin Dashboard
    leaveDistribution: "Distribution des Congés",
    myTeamUpdates: "Mises à Jour de Mon Équipe",
    myTeamAvailability: "Disponibilité de Mon Équipe",
    myTeamAction: "Action de Mon Équipe",

    // Manager Dashboard
    teamMembers: "Membres de l'Équipe",
    presentToday: "Présent Aujourd'hui",
    pendingApprovals: "Approbations en Attente",
    teamPerformance: "Performance de l'Équipe",
    quickActions: "Actions Rapides",

    // Statistics Labels
    active: "Actif",
    inactive: "Inactif",
    present: "Présent",
    absent: "Absent",
    onLeave: "Congé",
    leaveApprovals: "Approbations de Congé",
    otherApprovals: "Autres Approbations",
    recentJoiners: "Nouveaux Arrivants",
    employee: "Employé",

    // Team Update Types
    birthday: "Anniversaire",
    anniversary: "Anniversaire",
    newJoiner: "Nouveau",
    promotion: "Promotion",
    leave: "Congé",
    compOff: "Congé Compensatoire",
    unscheduledShift: "Décalage Non Planifié",
    office: "Bureau",
    WFH: "Télétravail",
    field: "Champ",
    others: "Autres",
    onTimeArrivals: "Arrivées à Heure",
    lateArrivals: "Arrivées en Retard",

    // Tax Regime Comparison
    taxRegimeComparison: "Comparaison des Régimes Fiscaux",
    recommended: "Recommandé",
    totalTax: "Taxe Totale",
    businessIncomeQuestion: "Avez-vous des revenus d'entreprise ?",
    noInvestmentToDeclare: "Pas d'investissement à déclarer",
    proceedWithITDeclaration: "Procéder à la déclaration de l'IT",
    taxDeclarationHelp:
      "Avant de choisir le régime fiscal pour le calcul de la TDS, assurez-vous de déclarer tous vos investissements.",
    businessIncomeAcknowledgement:
      "J'acknowledge que dans le cas de revenus d'entreprise, le changement de régime ne sera pas autorisé pour les années financières futures.",
    regimeChangeAcknowledgement:
      "J'acknowledge que je peux choisir mon régime une seule fois pour l'année fiscale en cours.",
    compareTaxOutgo:
      "Comparez votre sortie fiscale avec l'ancien et le nouveau régime avant de choisir le meilleur.",
    taxRegimeNotification:
      "Selon la notification du ministère de l'impôt, le régime ne peut être sélectionné qu'une seule fois dans l'année fiscale.",
    totalIncomeFinancialYear: "Votre revenu total pour cette année fiscale",
    stopNotificationWarning:
      "Une fois la notification arrêtée, vous ne serez plus notifié et le régime fiscal par défaut de l'organisation sera automatiquement choisi pour vous.",

    // Team Update Messages
    hasBirthdayToday: "a son anniversaire aujourd'hui",
    celebratesWorkAnniversary: "célèbre son anniversaire de travail",
    joinedTheTeam: "a rejoint l'équipe",
    gotPromoted: "a été promu",
    isOnLeave: "est en congé",
    checkedInLate: "s'est connecté en retard",

    // Availability Status
    goodAvailability: "Bonne Disponibilité",
    moderateAvailability: "Disponibilité Modérée",
    lowAvailability: "Faible Disponibilité",

    // Action Types
    leaveRequest: "Demande de Congé",
    attendanceRegularization: "Régularisation de Présence",
    overtimeRequest: "Demande d'Heures Supplémentaires",
    expenseClaim: "Demande de Remboursement",
    probationReview: "Évaluation de Période d'Essai",
    probation: "Période d'Essai",
    salaryAdvance: "Avance sur Salaire",

    // Action Messages
    requestedLeave: "a demandé un congé",
    requestedAttendanceRegularization:
      "a demandé une régularisation de présence",
    requestedOvertime: "a demandé des heures supplémentaires",
    submittedExpenseClaim: "a soumis une demande de remboursement",
    probationReviewDue: "évaluation de période d'essai due",
    requestedSalaryAdvance: "a demandé une avance sur salaire",

    // Action Buttons
    approve: "Approuver",
    reject: "Rejeter",
    approveAction: "Approuver l'Action",
    rejectAction: "Rejeter l'Action",
    confirmApproveAction:
      "Êtes-vous sûr de vouloir approuver cette {action} pour {employee}?",
    confirmRejectAction:
      "Êtes-vous sûr de vouloir rejeter cette {action} pour {employee}?",

    // Quick Actions
    attendance: "Présence",
    reports: "Rapports",
    reviews: "Évaluations",
    teamDirectory: "Annuaire de l'Équipe",
    settings: "Paramètres",

    // Empty States
    relaxed: "Détendu",
    refreshedRecharged: "Rafraîchi, Rechargé",
    teamwork: "Travail d'Équipe",
    makesTheDreamWork: "Fait Réaliser les Rêves",
    aKeyToSuccess: "Une clé pour réussir",
    assembleStrongStableManagementTeam:
      "est d'assembler une équipe de gestion forte et stable.",
    strengthInNumbers: "La Force du Nombre",
    allCaughtUp: "Tout est à Jour",
    noActionsRequired: "Aucune Action Requise",
    greatThings: "Grandes choses dans l'entreprise",
    areNeverDone:
      "ne sont jamais faites par une seule personne, mais par une équipe de personnes.",

    // Motivational Quotes
    lifeIs10Percent: "La vie est à 10%",
    whatHappensToYou: "ce qui vous arrive",
    and90Percent: "Et à 90%",
    howYouReactToIt: "comment vous y réagissez",

    // Error Messages
    technicalDifficulties:
      "Il semble y avoir des difficultés techniques lors de la récupération des données.",
    adminDashboardError:
      "Quelque chose s'est mal passé dans le tableau de bord admin. Veuillez réessayer.",
    managerDashboardError:
      "Quelque chose s'est mal passé dans le tableau de bord manager. Veuillez réessayer.",
    statisticsError:
      "Quelque chose s'est mal passé lors du chargement des statistiques. Veuillez réessayer.",
    actionProcessedSuccessfully: "Action traitée avec succès",
    actionProcessingError:
      "Erreur lors du traitement de l'action. Veuillez réessayer.",

    // Mobile Update Modal
    newVersionTitle: "Nouvelle Version Disponible",
    newVersionSubtitle: "Une nouvelle version de l'application est disponible",
    newVersionQuestion: "Souhaitez-vous mettre à jour maintenant?",
    dismissButton: "Ignorer",
    updateButton: "Mettre à Jour",

    // probation pop-up
    probationDate: "Date de la Période d'Essai",
    confirmationDate: "Date de Confirmation",
    extendProbation: "Étendre la Période d'Essai",
    confirmEmployee: "Confirmer l'Employé",

    // Dashboard Loading Errors
    dashboardLoadError:
      "Quelque chose s'est mal passé lors du chargement du tableau de bord. Veuillez réessayer.",

    // Employee Dashboard - Leave History
    myLeaves: "Mes Congés",
    peopleWait: "Les Gens Attendent",
    leaveWaitingMessage: "le meilleur moment pour prendre un congé",
    atWork: "Au Travail",
    late: "En Retard",
    leaveTakenTooltip: "Congés pris durant l'année",
    leaveTaken: "Congé Pris",
    leaveBalanceTooltip: "Solde de congés restant disponible",
    leaveBalance: "Solde de Congés",
    currentLeaveEligibilityTooltip:
      "Éligibilité de congé actuelle est basée sur la configuration de la période de congé et le solde restant",
    currentLeaveEligibility: "Éligibilité de Congé Actuelle",
    totalLeaveEligibilityTooltip:
      "Éligibilité de congé totale est basée sur la configuration de l'année de congé et le solde restant",
    totalLeaveEligibility: "Éligibilité de Congé Totale",
    currentEligibility: "Éligibilité Actuelle",
    totalEligibility: "Éligibilité Totale",
    leaveDataError:
      "Quelque chose s'est mal passé lors du chargement des données de congé. Veuillez réessayer.",

    // Employee Dashboard - Profile
    workedToday: "Travaillé Aujourd'hui",
    refreshToRetrieveAttendance: "Actualiser pour récupérer la présence",
    retry: "Réessayer",
    checkedInAt: "Enregistré à",
    viewAttendanceEntries: "Voir les Entrées de Présence",
    attendanceEntries: "Entrées de Présence",
    noEntriesFound: "Aucune entrée trouvée",
    geoNotAccurate: "Vous n'êtes pas dans la zone de géo-encerclement",
    proceedNow: "Procéder Maintenant",
    gpsNotEnabled: "GPS Non Activé",
    gpsNotEnabledContent: "Veuillez activer le GPS pour continuer",
    mobViewGPS:
      "Votre organisation impose la capture des coordonnées géographiques lors du processus d'enregistrement et de sortie. Comme vous avez refusé l'accès aux données de localisation, vous devrez peut-être autoriser manuellement les autorisations de l'application pour accéder à la localisation de l'appareil et activer le service de localisation avant de cliquer sur `Procéder`",
    deskViewGPS:
      "Veuillez vérifier si vous avez accordé l'accès à l'emplacement pour cette application. Si c'est déjà le cas, le service GPS/Emplacement de votre smartphone/navigateur est désactivé, veuillez l'activer pour ajouter la présence.",
    refresh: "Actualiser",
    employeeProfileError:
      "Quelque chose s'est mal passé lors du chargement du profil employé. Veuillez réessayer après un certain temps.",
    faceNotRegistered:
      "Le visage n'a pas encore été enregistré. Veuillez enregistrer.",
    attendanceAddedSuccessfully: "Présence ajoutée avec succès",
    somethingWentWrong: "Quelque chose s'est mal passé. Veuillez réessayer.",
    outsideGeoFence:
      "Vous n'êtes pas dans la zone de géo-encerclement. Veuillez vérifier votre localisation et réessayer.",
    faceVerificationFailed: "Échec de la vérification du visage",
    faceVerificationError: "Erreur de vérification du visage",
    attendanceConfigurationError: "Erreur de configuration de présence",
    invalidGeoFencingConfiguration:
      "Configuration de géo-encerclement invalide. Veuillez contacter votre administrateur.",

    // Employee Dashboard - Actions
    myActions: "Mes Actions",
    lifeIsPercent: "La vie est à 10%",
    andPercent: "Et à 90%",
    youMissed: "Vous avez manqué",
    checkIn: "Enregistrement",
    and: "et",
    checkOut: "Sortie",
    add: "Ajouter",
    days: "Jours",
    hours: "Heures",
    actionsDataError:
      "Quelque chose s'est mal passé lors du chargement des données d'actions",
    attendanceCannotBeRegularized:
      "La présence ne peut pas être régularisée car vous avez dépassé la limite de demande de régularisation par mois.",

    // Employee Dashboard - Company Updates
    myCompanyUpdates: "Mes Mises à Jour d'Entreprise",
    companyUpdateMessage:
      "Quand vous donnez aux bonnes personnes des possibilités",
    companyUpdateSecondary: "ils font de grandes choses",
    birthdays: "Anniversaires",
    workAnniversary: "Anniversaire de Travail",
    awards: "Récompenses",
    today: "Aujourd'hui",
    happyBirthday: "Joyeux Anniversaire",
    leaveClosureTitle: "Le mois de la clôture des congés est arrivé",
    leaveClosureContent:
      "Il est temps de clôturer les congés et de conclure les activités de fin d'année liées aux congés, telles que le transfert des congés, l'expiration, l'encaissement, etc.",
    leaveClosureActivities:
      "Les activités suivantes doivent être effectuées pour une transition fluide vers l'année de congé suivante.",
    leaveApprovalDeadline:
      "Approuver toutes les demandes de congé en attente avant le",
    initiateLeaveEncashment: "Initier le processus d'encaissement de congé.",
    runLeaveClosureProcess: "Exécuter le processus de clôture de congé",
    grantLeaveCredit: "Accorder/accréditer le congé pour",
    leaveCreditPolicy: "conformément à votre politique d'organisation.",
    goAhead: "Continuer",

    // Employee Dashboard - Compliance
    myCompliance: "Ma Conformité",
    expiryDate: "Date d'Expiration",
    complianceDataError:
      "Quelque chose s'est mal passé lors du chargement des données de conformité",

    // Employee Dashboard - Utilization
    myUtilization: "Mon Utilisation",
    myTotalWorkedDays: "Mes Jours Travaillés Totaux",
    utilizationTooltip:
      "L'utilisation est déterminée en comparant le nombre de jours travaillés au nombre total de jours ouvrables dans une période de paie donnée. Elle est évaluée uniquement pour les cycles de paie finalisés.",
    organizationWorkingDays: "Jours Ouvrables de l'Organisation",
    myTimeOff: "Mon Temps Libre",
    apply: "Appliquer",
    timeManagement: "Gestion du Temps",
    viewFlow: "Voir le Flux",
    viewStatus: "Voir le Statut",
    utilizationDataError:
      "Quelque chose s'est mal passé lors du chargement des données d'utilisation",
    workedDays: "Jours Travaillés",
    remainingDays: "Jours Restants",
    utilization: "Utilisation",

    // Employee Dashboard - Holidays and Calendar
    myUpcomingHolidays: "Mes Prochains Congés",
    autumnShowsUs: "L'automne nous montre",
    howBeautifulLetGo: "comme il est beau de laisser partir",
    mandatory: "Obligatoire",
    holiday: "Vacances",
    personalChoice: "Choix Personnel",
    businessHours: "Heures de Travail",

    // Camera component
    takePhoto: "Prendre une Photo",
    pleaseSmile: "SOURIRE",
    openYourMouth: "OUVREZ LA BOUCHE",

    // Pop-up's
    request: "Demande",
    selectWorkPlace: "Sélectionner le Lieu de Travail",
    selectDurationFor: "Sélectionner la durée pour",
  },

  ja: {
    // Dashboard Navigation
    myDashboard: "マイダッシュボード",
    organizationDashboard: "組織ダッシュボード",
    teamDashboard: "チームダッシュボード",

    // Admin Dashboard
    leaveDistribution: "休暇配分",
    myTeamUpdates: "マイチーム更新",
    myTeamAvailability: "マイチーム可用性",
    myTeamAction: "マイチームアクション",

    // Manager Dashboard
    teamMembers: "チームメンバー",
    presentToday: "今日の出席",
    pendingApprovals: "承認待ち",
    teamPerformance: "チームパフォーマンス",
    quickActions: "クイックアクション",

    // Statistics Labels
    active: "アクティブ",
    inactive: "非アクティブ",
    present: "出席",
    absent: "欠席",
    onLeave: "休暇",
    leaveApprovals: "休暇承認",
    otherApprovals: "その他の承認",
    recentJoiners: "新入社員",
    employee: "従業員",

    // Team Update Types
    birthday: "誕生日",
    anniversary: "記念日",
    newJoiner: "新入社員",
    promotion: "昇進",
    leave: "休暇",
    compOff: "休暇代",
    unscheduledShift: "未定義シフト",
    office: "オフィス",
    WFH: "リモート",
    field: "フィールド",
    others: "その他",
    onTimeArrivals: "準時到着",
    lateArrivals: "遅刻",
    attendance: "出席",
    probation: "試用期間",

    // Tax Regime Comparison
    taxRegimeComparison: "税率比較",
    recommended: "推奨",
    totalTax: "合計税金",
    businessIncomeQuestion: "事業収入がありますか？",
    noInvestmentToDeclare: "申告する投資はありません",
    proceedWithITDeclaration: "所得税の申告を進める",
    taxDeclarationHelp:
      "所得税の申告を進める前に、すべての投資を申告してください。",
    businessIncomeAcknowledgement:
      "事業収入がある場合、将来の財政年度で税率の変更は許可されません。",
    regimeChangeAcknowledgement: "現在の財政年度で税率の変更は許可されません。",
    compareTaxOutgo:
      "税率の変更を決定する前に、古い税率と新しい税率の税金を比較してください。",
    taxRegimeNotification:
      "所得税課税当局の通知によると、税率は1回のみ選択できます。",
    totalIncomeFinancialYear: "この財政年度の総収入",
    stopNotificationWarning:
      "通知を停止すると、以降の通知はされません。組織レベルのデフォルト税率が自動的に選択されます。",
    taxRegimeUpdateError:
      "税率の更新中にエラーが発生しました。後でもう一度試してください。",

    // Team Update Messages
    hasBirthdayToday: "今日誕生日です",
    celebratesWorkAnniversary: "勤続記念日を祝います",
    joinedTheTeam: "チームに参加しました",
    gotPromoted: "昇進しました",
    isOnLeave: "休暇中です",
    checkedInLate: "遅刻しました",

    // Availability Status
    goodAvailability: "良好な可用性",
    moderateAvailability: "中程度の可用性",
    lowAvailability: "低い可用性",

    // Action Types
    leaveRequest: "休暇申請",
    attendanceRegularization: "出勤調整",
    overtimeRequest: "残業申請",
    expenseClaim: "経費請求",
    probationReview: "試用期間評価",
    salaryAdvance: "給与前払い",

    // Action Messages
    requestedLeave: "休暇を申請しました",
    requestedAttendanceRegularization: "出勤調整を申請しました",
    requestedOvertime: "残業を申請しました",
    submittedExpenseClaim: "経費請求を提出しました",
    probationReviewDue: "試用期間評価が必要です",
    requestedSalaryAdvance: "給与前払いを申請しました",

    // Action Buttons
    approve: "承認",
    reject: "拒否",
    approveAction: "アクションを承認",
    rejectAction: "アクションを拒否",
    confirmApproveAction: "{employee}の{action}を承認してもよろしいですか？",
    confirmRejectAction: "{employee}の{action}を拒否してもよろしいですか？",

    // Quick Actions
    reports: "レポート",
    reviews: "評価",
    teamDirectory: "チームディレクトリ",
    settings: "設定",

    // Empty States
    relaxed: "リラックス",
    refreshedRecharged: "リフレッシュ、充電完了",
    teamwork: "チームワーク",
    makesTheDreamWork: "夢を実現する",
    aKeyToSuccess: "成功の鍵",
    assembleStrongStableManagementTeam:
      "は、強力で安定した管理チームを構築することです。",
    strengthInNumbers: "数の力",
    allCaughtUp: "すべて完了",
    noActionsRequired: "アクションは必要ありません",
    greatThings: "ビジネスでは素晴らしいこと",
    areNeverDone:
      "は1人で達成することはできません。チームの皆さんで達成します。",

    // Motivational Quotes
    lifeIs10Percent: "人生は10%",
    whatHappensToYou: "あなたに起こること",
    and90Percent: "そして90%",
    howYouReactToIt: "それにどう反応するか",

    // Error Messages
    technicalDifficulties:
      "データの取得中に技術的な問題が発生しているようです。",
    adminDashboardError:
      "管理者ダッシュボードで問題が発生しました。もう一度お試しください。",
    managerDashboardError:
      "マネージャーダッシュボードで問題が発生しました。もう一度お試しください。",
    statisticsError:
      "統計の読み込み中に問題が発生しました。もう一度お試しください。",
    actionProcessedSuccessfully: "アクションが正常に処理されました",
    actionProcessingError:
      "アクションの処理中にエラーが発生しました。もう一度お試しください。",

    // Mobile Update Modal
    newVersionTitle: "新しいバージョンが利用可能",
    newVersionSubtitle: "アプリの新しいバージョンが利用可能です",
    newVersionQuestion: "今すぐ更新しますか？",
    dismissButton: "無視",
    updateButton: "更新",

    // probation pop-up
    probationDate: "試用期間の日付",
    confirmationDate: "確認日",
    extendProbation: "試用期間を延長する",
    confirmEmployee: "従業員を確認する",

    // Dashboard Loading Errors
    dashboardLoadError:
      "ダッシュボードの読み込み中に問題が発生しました。もう一度お試しください。",

    // Employee Dashboard - Leave History
    myLeaves: "私の休暇",
    peopleWait: "人々は待つ",
    leaveWaitingMessage: "休暇を取る最適な時です",
    atWork: "勤務中",
    late: "遅刻",
    leaveTakenTooltip: "休暇を取った",
    leaveTaken: "取得休暇",
    leaveBalanceTooltip: "利用可能な残り休暇残高",
    leaveBalance: "休暇残高",
    currentLeaveEligibilityTooltip:
      "現在の休暇資格は、休暇期間の設定と繰越残高に基づいています",
    currentLeaveEligibility: "現在の休暇資格",
    totalLeaveEligibilityTooltip:
      "休暇の年間資格は、休暇年と繰越残高に基づいています",
    totalLeaveEligibility: "総休暇資格",
    currentEligibility: "現在の資格",
    totalEligibility: "総資格",
    leaveDataError:
      "休暇データの読み込み中に問題が発生しました。しばらくしてからもう一度お試しください。",

    // Employee Dashboard - Profile
    workedToday: "今日の勤務",
    refreshToRetrieveAttendance: "出席を取得するために更新",
    retry: "再試行",
    checkedInAt: "チェックイン時刻",
    viewAttendanceEntries: "出席エントリを表示",
    attendanceEntries: "出席エントリ",
    noEntriesFound: "エントリが見つかりません",
    geoNotAccurate: "ジオフェンスエリア内にいません",
    proceedNow: "続行",
    gpsNotEnabled: "GPSが有効ではありません",
    gpsNotEnabledContent: "続行するにはGPSを有効にしてください",
    mobViewGPS:
      "組織では、チェックインとチェックアウトのプロセス中にジオ座標をキャプチャする必要があります。位置情報データへのアクセスを拒否したため、アプリの許可を手動で許可し、デバイスの位置にアクセスしてから、`Proceed`をクリックする前にロケーションサービスをオンにする必要がある場合があります。",
    deskViewGPS:
      "アプリへの位置アクセスを与えたかどうかを確認してください。既に与えられている場合は、スマホ/ブラウザのGPS/ロケーションサービスがオフになっている可能性があります。出席を追加するには、それを有効にしてください。",
    refresh: "更新",
    employeeProfileError:
      "従業員プロフィールの読み込み中に問題が発生しました。しばらくしてからもう一度お試しください。",
    faceNotRegistered: "顔はまだ登録されていません。登録してください。",
    attendanceAddedSuccessfully: "出席が正常に追加されました",
    somethingWentWrong:
      "何かがうまくいきませんでした。もう一度お試しください。",
    outsideGeoFence:
      "ジオフェンスエリア内にいません。位置を確認し、再度お試しください。",
    faceVerificationFailed: "顔認証に失敗しました",
    faceVerificationError: "顔認証エラー",
    attendanceConfigurationError: "出席設定エラー",
    invalidGeoFencingConfiguration:
      "無効なジオフェンス構成です。管理者に連絡してください。",

    // Employee Dashboard - Actions
    myActions: "私のアクション",
    lifeIsPercent: "人生は10%",
    andPercent: "そして90%",
    youMissed: "あなたは逃しました",
    checkIn: "チェックイン",
    and: "と",
    checkOut: "チェックアウト",
    add: "追加",
    days: "日",
    hours: "時間",
    actionsDataError: "アクションデータの読み込み中に問題が発生しました",
    attendanceCannotBeRegularized:
      "出席を正規化できません。月ごとの正規化要求の制限を超えています。",

    // Employee Dashboard - Company Updates
    myCompanyUpdates: "私の会社の更新",
    companyUpdateMessage: "良い人材にチャンスを与えると、",
    companyUpdateSecondary: "素晴らしいことが起こります",
    birthdays: "誕生日",
    workAnniversary: "勤続記念日",
    awards: "表彰",
    today: "今日",
    happyBirthday: "お誕生日おめでとう",
    leaveClosureTitle: "休暇締切りの月がやってきました",
    leaveClosureContent:
      "休暇の年を締め切る時期です。休暇の繰越、有効期限切れ、現金化などの休暇年終活動を終了します。",
    leaveClosureActivities:
      "次の活動を順調に実行するために、次の活動を実行する必要があります。",
    leaveApprovalDeadline: "休暇の承認は、",
    initiateLeaveEncashment: "休暇の現金化を開始する",
    runLeaveClosureProcess: "休暇締切りプロセスを実行する",
    grantLeaveCredit: "休暇のクレジットを付与する",
    leaveCreditPolicy: "組織のポリシーに従ってください。",
    goAhead: "先に進む",

    // Employee Dashboard - Compliance
    myCompliance: "私のコンプライアンス",
    expiryDate: "有効期限",
    complianceDataError:
      "コンプライアンスデータの読み込み中に問題が発生しました",

    // Employee Dashboard - Utilization
    myUtilization: "私の利用率",
    myTotalWorkedDays: "私の総勤務日数",
    utilizationTooltip:
      "利用率は、勤務日数と給与期間の合計勤務日数を比較することによって決定されます。これは、確定された給与サイクルに対してのみ評価されます。",
    organizationWorkingDays: "組織の勤務日数",
    myTimeOff: "私の休暇",
    apply: "申請",
    timeManagement: "時間管理",
    viewFlow: "フローを表示",
    viewStatus: "ステータスを表示",
    utilizationDataError: "利用率データの読み込み中に問題が発生しました",
    workedDays: "勤務日数",
    remainingDays: "残り日数",
    utilization: "利用率",

    // Employee Dashboard - Holidays and Calendar
    myUpcomingHolidays: "私の今後の休日",
    autumnShowsUs: "秋は私たちに教えてくれる",
    howBeautifulLetGo: "手放すことの美しさを",
    mandatory: "必須",
    holiday: "休日",
    personalChoice: "個人的な選択",
    businessHours: "営業時間",

    // Camera component
    takePhoto: "写真を撮る",
    pleaseSmile: "微笑んでください",
    openYourMouth: "口を開けてください",

    // Pop-up's
    request: "リクエスト",
    selectWorkPlace: "勤務場所を選択",
    selectDurationFor: "選択期間",
  },

  sp: {
    // Dashboard Navigation
    myDashboard: "Mi Panel",
    organizationDashboard: "Panel de Organización",
    teamDashboard: "Panel de Equipo",

    // Admin Dashboard
    leaveDistribution: "Distribución de Permisos",
    myTeamUpdates: "Actualizaciones de Mi Equipo",
    myTeamAvailability: "Disponibilidad de Mi Equipo",
    myTeamAction: "Acción de Mi Equipo",

    // Manager Dashboard
    teamMembers: "Miembros del Equipo",
    presentToday: "Presente Hoy",
    pendingApprovals: "Aprobaciones Pendientes",
    teamPerformance: "Rendimiento del Equipo",
    quickActions: "Acciones Rápidas",

    // Statistics Labels
    active: "Activo",
    inactive: "Inactivo",
    present: "Presente",
    absent: "Ausente",
    onLeave: "Permiso",
    leaveApprovals: "Aprobaciones de Permiso",
    otherApprovals: "Otras Aprobaciones",
    recentJoiners: "Nuevos Ingresos",
    employee: "Empleado",
    probation: "Período de Prueba",

    // Team Update Types
    birthday: "Cumpleaños",
    anniversary: "Aniversario",
    newJoiner: "Nuevo Ingreso",
    promotion: "Promoción",
    leave: "Permiso",
    compOff: "Compensatorio",
    unscheduledShift: "Turno No Programado",
    office: "Oficina",
    WFH: "WFH",
    field: "Campo",
    others: "Otros",
    onTimeArrivals: "Llegadas a tiempo",
    lateArrivals: "Llegadas tarde",

    // Tax Regime Comparison
    taxRegimeComparison: "Comparación de Régimen Fiscal",
    recommended: "Recomendado",
    totalTax: "Impuesto Total",
    businessIncomeQuestion: "¿Tiene ingresos de negocio?",
    noInvestmentToDeclare: "No hay inversiones que declarar",
    proceedWithITDeclaration: "Continuar con la Declaración de IT",
    taxDeclarationHelp:
      "Antes de elegir el régimen fiscal para el cálculo de la TDS, asegúrese de declarar todas sus inversiones.",
    businessIncomeAcknowledgement:
      "Acepto que en el caso de ingresos de negocio, el cambio de régimen no será permitido para años fiscales futuros.",
    regimeChangeAcknowledgement:
      "Acepto que solo puedo elegir mi régimen una vez para el año fiscal actual.",
    compareTaxOutgo:
      "Compare su salida fiscal con el régimen antiguo y nuevo antes de elegir el mejor.",
    taxRegimeNotification:
      "Según la notificación del Departamento de Impuestos, el régimen solo puede seleccionarse una vez en el año fiscal.",
    totalIncomeFinancialYear: "Su ingreso total para este año fiscal",
    stopNotificationWarning:
      "Una vez que detenga la notificación, ya no recibirá más notificaciones y se elegirá automáticamente el régimen fiscal predeterminado a nivel de organización para usted.",
    taxRegimeUpdateError:
      "Ocurrió un error al actualizar el régimen fiscal. Vuelva a intentarlo más tarde.",
    // Team Update Messages
    hasBirthdayToday: "cumple años hoy",
    celebratesWorkAnniversary: "celebra aniversario laboral",
    joinedTheTeam: "se unió al equipo",
    gotPromoted: "fue promovido",
    isOnLeave: "está de permiso",
    checkedInLate: "llegó tarde",

    // Availability Status
    goodAvailability: "Buena Disponibilidad",
    moderateAvailability: "Disponibilidad Moderada",
    lowAvailability: "Baja Disponibilidad",

    // Empty States
    relaxed: "Relajado",
    refreshedRecharged: "Refrescado, Recargado",
    teamwork: "Trabajo en Equipo",
    makesTheDreamWork: "Hace Realidad los Sueños",
    aKeyToSuccess: "Una clave para el éxito",
    assembleStrongStableManagementTeam:
      "es ensamblar un equipo de gestión fuerte y estable.",
    strengthInNumbers: "Fuerza en Números",
    allCaughtUp: "Todo al Día",
    noActionsRequired: "No se Requieren Acciones",
    greatThings: "Grandes cosas en el negocio",
    areNeverDone:
      "no se logran con una sola persona, sino con un equipo de personas.",

    // Motivational Quotes
    lifeIs10Percent: "La vida es 10%",
    whatHappensToYou: "lo que te sucede",
    and90Percent: "Y 90%",
    howYouReactToIt: "cómo reaccionas a ello",

    // Action Types
    leaveRequest: "Solicitud de Permiso",
    attendanceRegularization: "Regularización de Asistencia",
    overtimeRequest: "Solicitud de Horas Extra",
    expenseClaim: "Reclamo de Gastos",
    probationReview: "Revisión de Período de Prueba",
    salaryAdvance: "Adelanto de Salario",

    // Action Messages
    requestedLeave: "solicitó permiso",
    requestedAttendanceRegularization: "solicitó regularización de asistencia",
    requestedOvertime: "solicitó horas extra",
    submittedExpenseClaim: "presentó reclamo de gastos",
    probationReviewDue: "revisión de período de prueba pendiente",
    requestedSalaryAdvance: "solicitó adelanto de salario",

    // Action Buttons
    approve: "Aprobar",
    reject: "Rechazar",
    approveAction: "Aprobar Acción",
    rejectAction: "Rechazar Acción",
    confirmApproveAction:
      "¿Está seguro de que desea aprobar esta {action} para {employee}?",
    confirmRejectAction:
      "¿Está seguro de que desea rechazar esta {action} para {employee}?",

    // Quick Actions
    attendance: "Asistencia",
    reports: "Reportes",
    reviews: "Evaluaciones",
    teamDirectory: "Directorio del Equipo",
    settings: "Configuración",

    // Error Messages
    technicalDifficulties:
      "Parece haber algunas dificultades técnicas al obtener los datos.",
    adminDashboardError:
      "Algo salió mal en el panel de administración. Por favor, inténtelo de nuevo.",
    managerDashboardError:
      "Algo salió mal en el panel de gerente. Por favor, inténtelo de nuevo.",
    statisticsError:
      "Algo salió mal al cargar las estadísticas. Por favor, inténtelo de nuevo.",
    actionProcessedSuccessfully: "Acción procesada exitosamente",
    actionProcessingError:
      "Error al procesar la acción. Por favor, inténtelo de nuevo.",

    // Mobile Update Modal
    newVersionTitle: "Nueva Versión Disponible",
    newVersionSubtitle: "Una nueva versión de la aplicación está disponible",
    newVersionQuestion: "¿Desea actualizar ahora?",
    dismissButton: "Descartar",
    updateButton: "Actualizar",

    // probation pop-up
    probationDate: "Fecha del Período de Prueba",
    confirmationDate: "Fecha de Confirmación",
    extendProbation: "Extender Período de Prueba",
    confirmEmployee: "Confirmar Empleado",

    // Dashboard Loading Errors
    dashboardLoadError:
      "Algo salió mal al cargar el panel. Por favor, inténtelo de nuevo.",

    // Employee Dashboard - Leave History
    myLeaves: "Mis Permisos",
    peopleWait: "La Gente Espera",
    leaveWaitingMessage: "para el mejor momento para tomar un permiso",
    atWork: "En el Trabajo",
    late: "Tarde",
    leaveTakenTooltip: "Permiso tomado durante el año",
    leaveTaken: "Permiso Tomado",
    leaveBalanceTooltip: "Saldo de permisos disponible hasta la fecha",
    leaveBalance: "Saldo de Permisos",
    currentLeaveEligibilityTooltip:
      "Elegibilidad de permiso actual basada en la configuración del período de permiso y el saldo restante",
    currentLeaveEligibility: "Elegibilidad de Permiso Actual",
    totalLeaveEligibilityTooltip:
      "Elegibilidad de permiso total basada en la configuración del año de permiso y el saldo restante",
    totalLeaveEligibility: "Elegibilidad de Permiso Total",
    currentEligibility: "Elegibilidad Actual",
    totalEligibility: "Elegibilidad Total",
    leaveDataError:
      "Algo salió mal al cargar los datos de permisos. Por favor, inténtelo de nuevo.",

    // Employee Dashboard - Profile
    workedToday: "Trabajado Hoy",
    refreshToRetrieveAttendance: "Actualizar para recuperar asistencia",
    retry: "Reintentar",
    checkedInAt: "Registrado a las",
    viewAttendanceEntries: "Ver Entradas de Asistencia",
    attendanceEntries: "Entradas de Asistencia",
    noEntriesFound: "No se encontraron entradas",
    geoNotAccurate: "Las coordenadas geográficas no son precisas",
    proceedNow: "Continuar",
    gpsNotEnabled: "GPS No Habilitado",
    gpsNotEnabledContent: "Por favor habilite el GPS para continuar",
    mobViewGPS:
      "Su organización exige capturar coordenadas geográficas durante el proceso de registro de entrada y salida. Dado que ha denegado el acceso a los datos de ubicación, es posible que deba permitir manualmente los permisos de la aplicación para acceder a la ubicación del dispositivo y activar el servicio de ubicación antes de hacer clic en `Continuar`",
    deskViewGPS:
      "Por favor, verifique si ha otorgado acceso a la ubicación para esta aplicación. Si ya lo ha hecho, el servicio de GPS/Ubicación de su smartphone/navegador está desactivado, por favor actívelo para agregar asistencia.",
    refresh: "Actualizar",
    employeeProfileError:
      "Algo salió mal al cargar el perfil del empleado. Por favor, inténtelo de nuevo.",
    faceNotRegistered:
      "El rostro no ha sido registrado aún. Por favor, regístrese.",
    attendanceAddedSuccessfully: "Asistencia agregada exitosamente",
    somethingWentWrong: "Algo salió mal. Por favor, inténtelo de nuevo.",
    outsideGeoFence:
      "No está en el área de geovallas. Por favor, verifique su ubicación e inténtelo de nuevo.",
    faceVerificationFailed: "Falló la verificación de rostro",
    faceVerificationError: "Error de verificación de rostro",
    attendanceConfigurationError: "Error de configuración de asistencia",
    invalidGeoFencingConfiguration:
      "Configuración de geovallas inválida. Por favor, contáctese con su administrador.",

    // Employee Dashboard - Actions
    myActions: "Mis Acciones",
    lifeIsPercent: "La vida es 10%",
    andPercent: "Y 90%",
    youMissed: "Te perdiste",
    checkIn: "Registro de Entrada",
    and: "y",
    checkOut: "Registro de Salida",
    add: "Agregar",
    days: "Días",
    hours: "Horas",
    actionsDataError: "Algo salió mal al cargar los datos de acciones",
    attendanceCannotBeRegularized:
      "La asistencia no puede ser regularizada ya que ha superado el límite de solicitudes de regularización por mes.",

    // Employee Dashboard - Company Updates
    myCompanyUpdates: "Mis Actualizaciones de Empresa",
    companyUpdateMessage: "Cuando se le da a buenas personas la oportunidad",
    companyUpdateSecondary: "hacen grandes cosas",
    birthdays: "Cumpleaños",
    workAnniversary: "Aniversario Laboral",
    awards: "Premios",
    today: "Hoy",
    happyBirthday: "Feliz Cumpleaños",
    leaveClosureTitle: "El mes de cierre de permisos ha llegado",
    leaveClosureContent:
      "Es hora de cerrar el año de permisos y concluir las actividades de fin de año relacionadas con los permisos, como el traslado de permisos, el vencimiento, el cobro, etc.",
    leaveClosureActivities:
      "Las siguientes actividades deben realizarse para una transición fluida al siguiente año de permisos.",
    leaveApprovalDeadline:
      "Aprobar todas las solicitudes de permiso pendientes antes del",
    initiateLeaveEncashment: "Iniciar el proceso de cobro de permisos.",
    runLeaveClosureProcess: "Ejecutar el proceso de cierre de permisos",
    grantLeaveCredit: "Conceder/creditar el permiso para",
    leaveCreditPolicy: "de acuerdo con su política de organización.",
    goAhead: "Continuar",

    // Employee Dashboard - Compliance
    myCompliance: "Mi Cumplimiento",
    expiryDate: "Fecha de Vencimiento",
    complianceDataError: "Algo salió mal al cargar los datos de cumplimiento",

    // Employee Dashboard - Utilization
    myUtilization: "Mi Utilización",
    myTotalWorkedDays: "Mis Días Trabajados Totales",
    utilizationTooltip:
      "La utilización se determina comparando los días trabajados con el total de días laborables en un período de pago determinado. Solo se evalúa para los ciclos de pago finalizados.",
    organizationWorkingDays: "Días Laborables de la Organización",
    myTimeOff: "Mi Tiempo Libre",
    apply: "Aplicar",
    timeManagement: "Gestión del Tiempo",
    viewFlow: "Ver Flujo",
    viewStatus: "Ver Estado",
    utilizationDataError: "Algo salió mal al cargar los datos de utilización",
    workedDays: "Días Trabajados",
    remainingDays: "Días Restantes",
    utilization: "Utilización",

    // Employee Dashboard - Holidays and Calendar
    myUpcomingHolidays: "Mis Próximos Feriados",
    autumnShowsUs: "El otoño nos muestra",
    howBeautifulLetGo: "qué hermoso es dejar ir",
    mandatory: "Obligatorio",
    holiday: "Vacaciones",
    personalChoice: "Elección Personal",
    businessHours: "Horas de Negocio",

    // Camera component
    takePhoto: "Tomar Foto",
    pleaseSmile: "SONRÍE",
    openYourMouth: "ABRE LA BOCA",
    // Pop-up's
    request: "Solicitud",
    selectWorkPlace: "Seleccionar Lugar de Trabajo",
    selectDurationFor: "Seleccionar la duración para",
  },
};
