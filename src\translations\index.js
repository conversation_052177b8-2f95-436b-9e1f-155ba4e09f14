import { createI18n } from "vue-i18n";
import coreHr from "./core-hr";
import productivityMonitoring from "./productivity-monitoring";
import dataLossPrevention from "./data-loss-prevention";
import settings from "./settings";
import authLayout from "./auth-layout";
import payroll from "./payroll";
import validations from "./validations";
import common from "./common";
import taxStatutory from "./tax-statutory";
import dashboard from "./dashboard";

// Merge by language
const messages = {
  en: {
    common: common.en,
    coreHr: coreHr.en,
    productivityMonitoring: productivityMonitoring.en,
    dataLossPrevention: dataLossPrevention.en,
    settings: settings.en,
    authLayout: authLayout.en,
    payroll: payroll.en,
    validations: validations.en,
    taxStatutory: taxStatutory.en,
    dashboard: dashboard.en,
    searchPlaceholder: "Search",
  },
  fr: {
    common: common.fr,
    coreHr: coreHr.fr,
    productivityMonitoring: productivityMonitoring.fr,
    dataLossPrevention: dataLossPrevention.fr,
    settings: settings.fr,
    authLayout: authLayout.fr,
    payroll: payroll.fr,
    dashboard: dashboard.fr,
    searchPlaceholder: "Rechercher",
  },
  ja: {
    common: common.ja,
    coreHr: coreHr.ja,
    productivityMonitoring: productivityMonitoring.ja,
    dataLossPrevention: dataLossPrevention.ja,
    settings: settings.ja,
    authLayout: authLayout.ja,
    payroll: payroll.ja,
    dashboard: dashboard.ja,
    searchPlaceholder: "検索",
  },
  sp: {
    common: common.sp,
    coreHr: coreHr.sp,
    productivityMonitoring: productivityMonitoring.sp,
    dataLossPrevention: dataLossPrevention.sp,
    settings: settings.sp,
    authLayout: authLayout.sp,
    payroll: payroll.sp,
    validations: validations.sp,
    dashboard: dashboard.sp,
    searchPlaceholder: "Buscar",
  },
};

const i18n = createI18n({
  locale: localStorage.getItem("language") || "en",
  fallbackLocale: "en",
  messages,
});

export default i18n;
