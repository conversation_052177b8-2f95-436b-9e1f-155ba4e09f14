export default {
  en: {
    /* ----- Productivity Monitoring ----- */
    // Section titles
    cxoDashboard: "CXO Dashboard",
    viewCxoDashboard: "View CXO Dashboard",
    productivityMonitoring: "Productivity Monitoring",

    // Dashboard sections
    overview: "Overview",
    employeeProductivity: "Employee Productivity",
    teamActivityDashboard: "Team Activity Dashboard",
    myActivityDashboard: "My Activity Dashboard",
    departmentPerformance: "Department Performance",
    timeTracking: "Time Tracking",
    workSchedule: "Work Schedule",

    // Team Activity Dashboard
    workedDate: "Worked Date",
    customGroupLabel: "Custom Group",
    noDataAvailable: "No data available",
    avgStartEndTimeByLocation: "Avg Start & End Time by Location",
    avgStartEndTime: "Avg Start and End Time",
    locationBreakdown: "Location Breakdown",
    locationBreakdownByWeek: "Location Breakdown by Week",
    systemUpTimeLabel: "System Up Time",
    activeTimeLabel: "Active Time",
    computerActivityLabel: "Computer Activity(Mouse and Keyboard)",
    productiveTimeLabel: "Productive Time(Apps and URL)",
    avgTimeWorkedPerDay: "Avg. Time Worked / day ( in hrs )",
    timeLabel: "Time",
    weeksLabel: "Weeks",
    percentageLabel: "Percentage ( % )",
    selectDateRangeLessThan31Days:
      "Please select a date range of less than 31 days",
    selectDateRange: "Select Date Range",
    productivityInsights: "Productivity Insights",
    locationInsights: "Location Insights",
    startLabel: "Start",
    endLabel: "End",
    offline: "Offline",

    // Table headers
    employeeName: "Employee Name",
    department: "Department",
    customGroup: "Custom Group",
    activityLevel: "Activity Level",
    timeTracked: "Time Tracked",
    startTime: "Start Time",
    endTime: "End Time",
    duration: "Duration",
    status: "Status",
    actions: "Actions",

    // Dashboard metrics
    totalEmployees: "Total Employees",
    activeEmployees: "Active Employees",
    averageProductivity: "Average Productivity",
    productivityScore: "Productivity Score",
    topPerformers: "Top Performers",
    lowPerformers: "Low Performers",
    productivityTrend: "Productivity Trend",
    hoursWorked: "Hours Worked",
    systemUpTime: "System Up Time",
    lowestSystemUpTime: "Get top 10 employees with lowest system up time",
    lowest: "Lowest",
    highestSystemUpTime: "Get top 10 employees with highest system up time",
    highest: "Highest",
    activeTime: "Active Time",
    computerActivity: "Computer Activity",
    hours: "Hours",
    idleTime: "Idle Time",
    productiveTime: "Productive Time",
    productivityByLocation: "Productivity by Location",
    activeDuration: "Active Duration",
    idleDuration: "Idle Duration",
    timeInHours: "Time in hours",
    totalTimeInHours: "Total time in hours",
    productivityTrendPeriod: "Productivity trend over a given period",

    // Location types
    officeRemote: "Office/Remote",
    office: "Office",
    remote: "Remote",
    all: "All",
    locationByWeek: "Location By Week",

    // Filter labels
    dateRange: "Worked Date",
    activityType: "Activity Type",

    // Buttons and actions
    addNew: "Add New",
    resetFilterSearch: "Reset Filter/Search",
    export: "Export",
    edit: "Edit",
    view: "View",
    delete: "Delete",
    close: "Close",
    save: "Save",
    submit: "Submit",
    cancel: "Cancel",

    // Download menu translations
    downloadSVG: "Download SVG",
    downloadPNG: "Download PNG",
    downloadCSV: "Download CSV",

    // Messages
    noActivitiesFound: "No activities found for the selected filters/searches.",
    accessDeniedMessage: "You don't have access to perform this action.",
    productivityHelp:
      "Productivity monitoring helps track employee activities and measure performance.",
    exitFormWarning: "Are you sure you want to exit this form?",
    activityAdded: "Activity added successfully.",
    activityUpdated: "Activity updated successfully.",
    activityDeleted: "Activity deleted successfully.",

    // Additional translations for ProductivityCharts component
    selectDateRangePlaceholder: "Select Date Range",
    overall: "Overall",
    pleaseSelectDateRangeLessThan31Days:
      "Please select a date range of less than 31 days",
    cxoDashboardForm: "CXO Dashboard",
    retrievingAction: "retrieving",
    productivityMonitoringForm: "productivityMonitoring",
  },
  fr: {
    /* ----- Productivity Monitoring ----- */
    // Section titles
    cxoDashboard: "Tableau de bord CXO",
    viewCxoDashboard: "Voir le tableau de bord CXO",
    productivityMonitoring: "Suivi de productivité",

    // Dashboard sections
    overview: "Aperçu",
    employeeProductivity: "Productivité des employés",
    teamActivityDashboard: "Tableau de bord d'activité d'équipe",
    myActivityDashboard: "Mon tableau de bord d'activité",
    departmentPerformance: "Performance des départements",
    timeTracking: "Suivi du temps",
    workSchedule: "Horaire de travail",

    // Team Activity Dashboard
    workedDate: "Date travaillée",
    customGroupLabel: "Groupe personnalisé",
    noDataAvailable: "Aucune donnée disponible",
    productivityByLocation: "Productivité par emplacement",
    avgStartEndTimeByLocation:
      "Heure moyenne de début et de fin par emplacement",
    locationBreakdown: "Répartition par emplacement",
    locationBreakdownByWeek: "Répartition par emplacement par semaine",
    systemUpTimeLabel: "Temps de fonctionnement du système",
    activeTimeLabel: "Temps actif",
    computerActivityLabel: "Activité informatique (souris et clavier)",
    productiveTimeLabel: "Temps productif (applications et URL)",
    avgTimeWorkedPerDay: "Temps moyen travaillé / jour (en heures)",
    timeLabel: "Temps",
    weeksLabel: "Semaines",
    percentageLabel: "Pourcentage (%)",
    selectDateRangeLessThan31Days:
      "Veuillez sélectionner une plage de dates de moins de 31 jours",
    selectDateRange: "Sélectionner une plage de dates",
    productivityInsights: "Aperçus de productivité",
    locationInsights: "Aperçus de localisation",
    startLabel: "Début",
    endLabel: "Fin",
    offline: "Hors ligne",

    // Table headers
    employeeName: "Nom de l'employé",
    department: "Département",
    activityLevel: "Niveau d'activité",
    timeTracked: "Temps suivi",
    startTime: "Heure de début",
    endTime: "Heure de fin",
    duration: "Durée",
    status: "Statut",
    actions: "Actions",

    // Dashboard metrics
    totalEmployees: "Nombre total d'employés",
    activeEmployees: "Employés actifs",
    averageProductivity: "Productivité moyenne",
    productivityScore: "Score de productivité",
    topPerformers: "Meilleurs performeurs",
    lowPerformers: "Performeurs faibles",
    productivityTrend: "Tendance de productivité",
    hoursWorked: "Heures travaillées",
    activeDuration: "Durée active",
    idleDuration: "Durée d'inactivité",
    timeInHours: "Temps en heures",
    totalTimeInHours: "Temps total en heures",
    productivityTrendPeriod: "Tendance de productivité sur une période donnée",

    // Location types
    officeRemote: "Bureau/Télétravail",
    office: "Bureau",
    remote: "Télétravail",
    all: "Tous",
    locationByWeek: "Emplacement par Semaine",

    // Filter labels
    dateRange: "Plage de dates",
    activityType: "Type d'activité",

    // Buttons and actions
    addNew: "Ajouter nouveau",
    resetFilterSearch: "Réinitialiser filtre/recherche",
    export: "Exporter",
    edit: "Modifier",
    view: "Voir",
    delete: "Supprimer",
    close: "Fermer",
    save: "Enregistrer",
    submit: "Soumettre",
    cancel: "Annuler",

    // Download menu translations
    downloadSVG: "Télécharger SVG",
    downloadPNG: "Télécharger PNG",
    downloadCSV: "Télécharger CSV",

    // Messages
    noActivitiesFound:
      "Aucune activité trouvée pour les filtres/recherches sélectionnés.",
    accessDeniedMessage: "Vous n'avez pas accès pour effectuer cette action.",
    productivityHelp:
      "Le suivi de productivité aide à suivre les activités des employés et à mesurer les performances.",
    exitFormWarning: "Êtes-vous sûr de vouloir quitter ce formulaire ?",
    activityAdded: "Activité ajoutée avec succès.",
    activityUpdated: "Activité mise à jour avec succès.",
    activityDeleted: "Activité supprimée avec succès.",
  },
  ja: {
    /* ----- Productivity Monitoring ----- */
    // Section titles
    cxoDashboard: "CXOダッシュボード",
    viewCxoDashboard: "CXOダッシュボードを表示",
    productivityMonitoring: "生産性モニタリング",

    // Dashboard sections
    overview: "概要",
    employeeProductivity: "従業員の生産性",
    teamActivityDashboard: "チームアクティビティダッシュボード",
    myActivityDashboard: "マイアクティビティダッシュボード",
    departmentPerformance: "部門のパフォーマンス",
    timeTracking: "時間追跡",
    workSchedule: "勤務スケジュール",

    // Team Activity Dashboard
    workedDate: "勤務日",
    customGroupLabel: "カスタムグループ",
    noDataAvailable: "データがありません",
    productivityByLocation: "場所別の生産性",
    avgStartEndTimeByLocation: "場所別の平均開始・終了時間",
    locationBreakdown: "場所の内訳",
    locationBreakdownByWeek: "週別の場所の内訳",
    systemUpTimeLabel: "システム稼働時間",
    activeTimeLabel: "アクティブ時間",
    computerActivityLabel: "コンピュータ活動（マウスとキーボード）",
    productiveTimeLabel: "生産的な時間（アプリとURL）",
    avgTimeWorkedPerDay: "1日あたりの平均勤務時間（時間単位）",
    timeLabel: "時間",
    weeksLabel: "週",
    percentageLabel: "割合（%）",
    selectDateRangeLessThan31Days: "31日未満の日付範囲を選択してください",
    selectDateRange: "日付範囲を選択",
    productivityInsights: "生産性の洞察",
    locationInsights: "場所の洞察",
    startLabel: "開始",
    endLabel: "終了",
    offline: "オフライン",

    // Table headers
    employeeName: "従業員名",
    department: "部署",
    activityLevel: "アクティビティレベル",
    timeTracked: "追跡時間",
    startTime: "開始時間",
    endTime: "終了時間",
    duration: "期間",
    status: "ステータス",
    actions: "アクション",

    // Dashboard metrics
    totalEmployees: "従業員総数",
    activeEmployees: "アクティブな従業員",
    averageProductivity: "平均生産性",
    productivityScore: "生産性スコア",
    topPerformers: "トップパフォーマー",
    lowPerformers: "低パフォーマー",
    productivityTrend: "生産性トレンド",
    hoursWorked: "勤務時間",
    activeDuration: "アクティブ時間",
    idleDuration: "アイドル時間",
    timeInHours: "時間（時間単位）",
    totalTimeInHours: "合計時間（時間単位）",
    productivityTrendPeriod: "指定期間の生産性トレンド",

    // Location types
    officeRemote: "オフィス/リモート",
    office: "オフィス",
    remote: "リモート",
    all: "すべて",
    locationByWeek: "週別の場所",

    // Filter labels
    dateRange: "日付範囲",
    activityType: "アクティビティタイプ",

    // Buttons and actions
    addNew: "新規追加",
    resetFilterSearch: "フィルター/検索をリセット",
    export: "エクスポート",
    edit: "編集",
    view: "表示",
    delete: "削除",
    close: "閉じる",
    save: "保存",
    submit: "送信",
    cancel: "キャンセル",

    // Download menu translations
    downloadSVG: "SVGダウンロード",
    downloadPNG: "PNGダウンロード",
    downloadCSV: "CSVダウンロード",

    // Messages
    noActivitiesFound:
      "選択されたフィルター/検索に該当するアクティビティはありません。",
    accessDeniedMessage: "このアクションを実行する権限がありません。",
    productivityHelp:
      "生産性モニタリングは、従業員のアクティビティを追跡し、パフォーマンスを測定するのに役立ちます。",
    exitFormWarning: "このフォームを終了してもよろしいですか？",
    activityAdded: "アクティビティが正常に追加されました。",
    activityUpdated: "アクティビティが正常に更新されました。",
    activityDeleted: "アクティビティが正常に削除されました。",
  },
  sp: {
    /* ----- Productivity Monitoring ----- */
    // Section titles
    cxoDashboard: "Panel de CXO",
    viewCxoDashboard: "Ver CXO Dashboard",
    productivityMonitoring: "Monitoreo de Productividad",

    // Dashboard sections
    overview: "Resumen",
    employeeProductivity: "Productividad del Empleado",
    teamActivityDashboard: "Panel De Actividad Del Equipo",
    myActivityDashboard: "Mi Panel De Actividad",
    departmentPerformance: "Rendimiento del Departamento",
    timeTracking: "Seguimiento de Tiempo",
    workSchedule: "Horario de Trabajo",

    // Team Activity Dashboard
    workedDate: "Fecha Trabajada",
    customGroupLabel: "Grupo Personalizado",
    noDataAvailable: "No hay datos disponibles",
    productivityByLocation: "Productividad por Ubicación",
    avgStartEndTimeByLocation: "Hora Promedio de Inicio y Fin por Ubicación",
    avgStartEndTime: "Hora Promedio de Inicio y Fin",
    locationBreakdown: "Desglose por Ubicación",
    locationBreakdownByWeek: "Desglose por Ubicación por Semana",
    systemUpTimeLabel: "Tiempo de Sistema Encendido",
    activeTimeLabel: "Tiempo Activo",
    computerActivityLabel: "Actividad de Computadora (Ratón y Teclado)",
    productiveTimeLabel: "Tiempo Productivo (Aplicaciones y URL)",
    avgTimeWorkedPerDay: "Tiempo Promedio Trabajado / día (en horas)",
    timeLabel: "Tiempo",
    weeksLabel: "Semanas",
    percentageLabel: "Porcentaje (%)",
    selectDateRangeLessThan31Days:
      "Por favor seleccione un rango de fechas de menos de 31 días",
    selectDateRange: "Seleccionar Rango de Fechas",
    productivityInsights: "Información de Productividad",
    locationInsights: "Información de Ubicación",
    startLabel: "Inicio",
    endLabel: "Fin",
    offline: "Desconectado",

    // Table headers
    employeeName: "Nombre del Empleado",
    department: "Departamento",
    customGroup: "Grupo Personalizado",
    activityLevel: "Nivel de Actividad",
    timeTracked: "Tiempo Registrado",
    startTime: "Hora de Inicio",
    endTime: "Hora de Fin",
    duration: "Duración",
    status: "Estado",
    actions: "Acciones",

    // Dashboard metrics
    totalEmployees: "Total de Empleados",
    activeEmployees: "Empleados Activos",
    averageProductivity: "Productividad Promedio",
    productivityScore: "Puntuación de Productividad",
    topPerformers: "Mejores Rendimientos",
    lowPerformers: "Rendimientos Bajos",
    productivityTrend: "Tendencia de Productividad",
    hoursWorked: "Horas Trabajadas",
    systemUpTime: "Tiempo de Sistema Encendido",
    lowestSystemUpTime:
      "Obtener los 10 empleados con el tiempo de sistema más bajo",
    lowest: "Más bajo",
    highestSystemUpTime:
      "Obtener los 10 empleados con el tiempo de sistema más alto",
    highest: "Más alto",
    activeTime: "Tiempo Activo",
    computerActivity: "Actividad de Computadora",
    hours: "Horas",
    idleTime: "Tiempo Inactivo",
    productiveTime: "Tiempo Productivo",
    activeDuration: "Duración Activa",
    idleDuration: "Duración Inactiva",
    timeInHours: "Tiempo en horas",
    totalTimeInHours: "Tiempo total en horas",
    productivityTrendPeriod:
      "Tendencia de productividad durante un período determinado",

    // Location types
    officeRemote: "Oficina/Remoto",
    office: "Oficina",
    remote: "Remoto",
    all: "Todos",
    locationByWeek: "Ubicación por Semana",

    // Filter labels
    dateRange: "Fecha Trabajada",
    activityType: "Tipo de Actividad",

    // Buttons and actions
    addNew: "Añadir Nuevo",
    resetFilterSearch: "Restablecer Filtro/Búsqueda",
    export: "Exportar",
    edit: "Editar",
    view: "Ver",
    delete: "Eliminar",
    close: "Cerrar",
    save: "Guardar",
    submit: "Enviar",
    cancel: "Cancelar",

    // Download menu translations
    downloadSVG: "Descargar SVG",
    downloadPNG: "Descargar PNG",
    downloadCSV: "Descargar CSV",

    // Messages
    noActivitiesFound:
      "No se encontraron actividades para los filtros/búsquedas seleccionados.",
    accessDeniedMessage: "No tienes acceso para realizar esta acción.",
    productivityHelp:
      "El monitoreo de productividad ayuda a rastrear las actividades de los empleados y medir el rendimiento.",
    exitFormWarning: "¿Estás seguro de que quieres salir de este formulario?",
    activityAdded: "Actividad añadida con éxito.",
    activityUpdated: "Actividad actualizada con éxito.",
    activityDeleted: "Actividad eliminada con éxito.",

    // Additional translations for ProductivityCharts component
    selectDateRangePlaceholder: "Seleccionar Rango de Fechas",
    overall: "General",
    pleaseSelectDateRangeLessThan31Days:
      "Por favor seleccione un rango de fechas de menos de 31 días",
    cxoDashboardForm: "Panel de CXO",
    retrievingAction: "recuperando",
    productivityMonitoringForm: "productivityMonitoring",
  },
};
