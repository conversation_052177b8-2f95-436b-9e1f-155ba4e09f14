export default {
  en: {
    required: "{fieldName} is required.",
    numericRequiredValidation: "{fieldName} is required.",
    minValidationWithFieldName:
      "{fieldName} should be greater than or equal to {minFieldName}.",
    minValidationWithoutFieldName:
      "Please enter a value greater than or equal to {minValue}.",
    maxValidationWithFieldName:
      "{fieldName} should be less than or equal to {maxFieldName}.",
    maxValidationWithoutFieldName:
      "Please enter a value less than or equal to {maxValue}.",
    twoDecimalValidation: "Please enter a value up to 2 decimal places",
    noDecimalValidation: "Decimal values are not allowed.",
  },
  sp: {
    required: "{fieldName} es obligatorio.",
    numericRequiredValidation: "{fieldName} es obligatorio.",
    minValidationWithFieldName:
      "{fieldName} debe ser mayor o igual que {minFieldName}.",
    minValidationWithoutFieldName:
      "Por favor, ingrese un valor mayor o igual que {minValue}.",
    maxValidationWithFieldName:
      "{fieldName} debe ser menor o igual que {maxFieldName}.",
    maxValidationWithoutFieldName:
      "Por favor, ingrese un valor menor o igual que {maxValue}.",
    twoDecimalValidation:
      "Por favor, ingrese un valor con hasta dos decimales.",
  },
};
