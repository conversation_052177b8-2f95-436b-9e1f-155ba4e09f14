<template>
  <div class="ck-editor-wrapper">
    <div v-if="showPlaceholderMenu" class="placeholder-menu mb-3">
      <slot name="placeholder-menu"></slot>
    </div>
    <ckeditor
      :editor="editor"
      v-model="internalValue"
      :config="editorConfig"
      @ready="onReady"
      @focus="onFocus"
      @blur="onBlur"
      @input="onInput"
    />
    <div
      v-if="errorMessage"
      class="text-caption ml-4 mt-2"
      style="color: #b00020"
    >
      {{ errorMessage }}
    </div>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import {
  ClassicEditor,
  Essentials,
  Paragraph,
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Code,
  Subscript,
  Superscript,
  Heading,
  Link,
  List,
  ListProperties,
  TodoList,
  BlockQuote,
  Table,
  TableToolbar,
  TableProperties,
  TableCellProperties,
  Undo,
  Font,
  FontSize,
  FontFamily,
  FontColor,
  FontBackgroundColor,
  Highlight,
  Alignment,
  Indent,
  IndentBlock,
  Image,
  ImageCaption,
  ImageResize,
  ImageStyle,
  ImageToolbar,
  ImageUpload,
  MediaEmbed,
  HorizontalLine,
  PageBreak,
  SpecialCharacters,
  SpecialCharactersEssentials,
  FindAndReplace,
  SelectAll,
  RemoveFormat,
  SourceEditing,
  ShowBlocks,
  WordCount,
  Autoformat,
  AutoLink,
  Autosave,
  TextTransformation,
  PasteFromOffice,
} from "ckeditor5";
import { Ckeditor } from "@ckeditor/ckeditor5-vue";

// Import CKEditor5 CSS
import "ckeditor5/ckeditor5.css";

export default defineComponent({
  name: "CKEditor5",

  components: {
    ckeditor: Ckeditor,
  },

  emits: [
    "ready",
    "focus",
    "blur",
    "input",
    "update:modelValue",
    "editor-change",
    "text-change",
    "selection-change",
    "editor-initialized",
  ],

  props: {
    modelValue: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "Enter content...",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    config: {
      type: Object,
      default: () => ({}),
    },
    showPlaceholderMenu: {
      type: Boolean,
      default: false,
    },
    errorMessage: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      editor: ClassicEditor,
      editorInstance: null,
      internalValue: this.modelValue,
      editorConfig: {
        licenseKey: "GPL", // Use GPL license for open source
        plugins: [
          Essentials,
          Paragraph,
          Bold,
          Italic,
          Underline,
          Strikethrough,
          Code,
          Subscript,
          Superscript,
          Heading,
          Link,
          List,
          ListProperties,
          TodoList,
          BlockQuote,
          Table,
          TableToolbar,
          TableProperties,
          TableCellProperties,
          Undo,
          Font,
          FontSize,
          FontFamily,
          FontColor,
          FontBackgroundColor,
          Highlight,
          Alignment,
          Indent,
          IndentBlock,
          Image,
          ImageCaption,
          ImageResize,
          ImageStyle,
          ImageToolbar,
          ImageUpload,
          MediaEmbed,
          HorizontalLine,
          PageBreak,
          SpecialCharacters,
          SpecialCharactersEssentials,
          FindAndReplace,
          SelectAll,
          RemoveFormat,
          SourceEditing,
          ShowBlocks,
          WordCount,
          Autoformat,
          AutoLink,
          Autosave,
          TextTransformation,
          PasteFromOffice,
        ],
        toolbar: {
          items: [
            "undo",
            "redo",
            "|",
            "findAndReplace",
            "selectAll",
            "|",
            "heading",
            "|",
            "fontSize",
            "fontFamily",
            "fontColor",
            "fontBackgroundColor",
            "|",
            "bold",
            "italic",
            "underline",
            "strikethrough",
            "|",
            "subscript",
            "superscript",
            "code",
            "removeFormat",
            "|",
            "highlight",
            "|",
            "alignment",
            "|",
            "link",
            "uploadImage",
            "mediaEmbed",
            "insertTable",
            "blockQuote",
            "|",
            "bulletedList",
            "numberedList",
            "todoList",
            "|",
            "outdent",
            "indent",
            "|",
            "horizontalLine",
            "pageBreak",
            "specialCharacters",
            "|",
            "sourceEditing",
            "showBlocks",
          ],
          shouldNotGroupWhenFull: true,
        },
        placeholder: "Start typing your content here...",
        // Table configuration
        table: {
          contentToolbar: [
            "tableColumn",
            "tableRow",
            "mergeTableCells",
            "tableProperties",
            "tableCellProperties",
          ],
        },
        // Image configuration
        image: {
          toolbar: [
            "imageStyle:inline",
            "imageStyle:block",
            "imageStyle:side",
            "|",
            "toggleImageCaption",
            "imageTextAlternative",
          ],
        },
        // List configuration
        list: {
          properties: {
            styles: true,
            startIndex: true,
            reversed: true,
          },
        },
        // Font configuration
        fontFamily: {
          options: [
            "default",
            "Arial, Helvetica, sans-serif",
            "Courier New, Courier, monospace",
            "Georgia, serif",
            "Lucida Sans Unicode, Lucida Grande, sans-serif",
            "Tahoma, Geneva, sans-serif",
            "Times New Roman, Times, serif",
            "Trebuchet MS, Helvetica, sans-serif",
            "Verdana, Geneva, sans-serif",
          ],
        },
        fontSize: {
          options: [9, 11, 13, "default", 17, 19, 21, 27, 35],
        },
        // Heading configuration
        heading: {
          options: [
            {
              model: "paragraph",
              title: "Paragraph",
              class: "ck-heading_paragraph",
            },
            {
              model: "heading1",
              view: "h1",
              title: "Heading 1",
              class: "ck-heading_heading1",
            },
            {
              model: "heading2",
              view: "h2",
              title: "Heading 2",
              class: "ck-heading_heading2",
            },
            {
              model: "heading3",
              view: "h3",
              title: "Heading 3",
              class: "ck-heading_heading3",
            },
            {
              model: "heading4",
              view: "h4",
              title: "Heading 4",
              class: "ck-heading_heading4",
            },
            {
              model: "heading5",
              view: "h5",
              title: "Heading 5",
              class: "ck-heading_heading5",
            },
            {
              model: "heading6",
              view: "h6",
              title: "Heading 6",
              class: "ck-heading_heading6",
            },
          ],
        },
      },
    };
  },

  watch: {
    modelValue(newValue) {
      if (newValue !== this.internalValue) {
        this.internalValue = newValue;
      }
    },

    internalValue(newValue) {
      this.$emit("update:modelValue", newValue);
    },

    disabled(newValue) {
      if (this.editorInstance) {
        if (newValue) {
          this.editorInstance.enableReadOnlyMode("disabled-prop");
        } else {
          this.editorInstance.disableReadOnlyMode("disabled-prop");
        }
      }
    },
  },

  methods: {
    onReady(editor) {
      this.editorInstance = editor;
      // Use the correct API for setting read-only mode
      if (this.disabled) {
        editor.enableReadOnlyMode("disabled-prop");
      }
      this.$emit("ready", editor);
      this.$emit("editor-initialized", editor);
    },

    onFocus(event, editor) {
      this.$emit("focus", event, editor);
    },

    onBlur(event, editor) {
      this.$emit("blur", event, editor);
    },

    onInput(data, event, editor) {
      this.$emit("input", data, event, editor);
      this.$emit("text-change", data, event, editor);
      this.$emit("editor-change", data, event, editor);
    },

    // Method to insert placeholder text at cursor position
    insertPlaceholder(placeholderText) {
      if (this.editorInstance) {
        this.editorInstance.model.change((writer) => {
          const insertPosition =
            this.editorInstance.model.document.selection.getFirstPosition();
          writer.insertText(placeholderText, insertPosition);
        });
      }
    },

    // Method to get editor content
    getContent() {
      return this.editorInstance ? this.editorInstance.getData() : "";
    },

    // Method to set editor content
    setContent(content) {
      if (this.editorInstance) {
        this.editorInstance.setData(content);
      }
    },

    // Method to focus the editor
    focus() {
      if (this.editorInstance) {
        this.editorInstance.editing.view.focus();
      }
    },
  },

  mounted() {
    this.internalValue = this.modelValue;
  },

  beforeUnmount() {
    // Let the CKEditor Vue component handle its own cleanup
    // We don't need to manually destroy the editor
    this.editorInstance = null;
  },
});
</script>

<style scoped>
.ck-editor-wrapper {
  width: 100%;
}

.placeholder-menu {
  display: flex;
  justify-content: flex-end;
}

/* CKEditor 5 styling adjustments */
:deep(.ck-editor__editable) {
  min-height: 200px;
  padding: 16px;
}

:deep(.ck-editor__editable_inline) {
  border: 1px solid #ddd;
  border-radius: 4px;
}

:deep(.ck-editor__editable:focus) {
  border-color: #1976d2;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

:deep(.ck-toolbar) {
  border-radius: 4px 4px 0 0;
}

:deep(.ck-editor__editable_inline) {
  border-radius: 0 0 4px 4px;
}
</style>
