<template>
  <div class="mt-3">
    <v-overlay
      v-if="isOverlay"
      v-model="showOverlay"
      class="d-flex justify-end overlay-content-parent"
      @click:outside="this.$emit('custom-email-cancel')"
    >
      <template v-slot:default>
        <div class="overlay-card">
          <div class="overlay-body pa-3">
            <v-form ref="customEmailForm">
              <div>
                <v-text-field
                  v-model="subject"
                  label="Subject"
                  variant="solo"
                  :rules="[required('Subject', subject)]"
                  ref="subject"
                ></v-text-field>
                <v-autocomplete
                  v-if="
                    typeOfTemplate &&
                    typeOfTemplate.toLowerCase() !== 'hiringforecastsettings'
                  "
                  v-model="emailsToSend"
                  color="secondary"
                  :items="templateEmail"
                  :label="
                    emailRecievers.length > 0 || ccEmailRecievers.length > 0
                      ? 'To'
                      : ''
                  "
                  readonly
                  multiple
                  solo
                  chips
                  :closable-chips="closeableChips"
                  density="compact"
                  :single-line="
                    emailRecievers.length > 0 || ccEmailRecievers.length > 0
                      ? false
                      : true
                  "
                ></v-autocomplete>
                <div v-else>
                  <div
                    v-if="
                      !isEditable && emailsToSend && emailsToSend?.length > 4
                    "
                  >
                    <v-card class="pa-5">
                      <v-row class="d-flex align-center">
                        <div
                          v-for="Emp_Detail in emailsToSend.slice(0, 4)"
                          :key="Emp_Detail"
                          class="py-1 mx-1"
                        >
                          <v-chip size="small"> {{ Emp_Detail }} </v-chip>
                        </div>
                        <v-chip
                          size="small"
                          color="primary"
                          v-if="emailsToSend.length > 4"
                          style="cursor: pointer; font-weight: bold"
                          @click="isEditable = !isEditable"
                        >
                          {{ emailsToSend.length - 4 }} + more
                        </v-chip>
                      </v-row>
                    </v-card>
                  </div>
                  <v-autocomplete
                    v-else
                    v-model="emailsToSend"
                    color="secondary"
                    :items="tempEmailList"
                    :label="emailRecievers.length > 0 ? 'To' : ''"
                    menu-icon=""
                    readonly
                    multiple
                    solo
                    chips
                    :closable-chips="closeableChips"
                    density="compact"
                    item-title="Emp_Detail"
                    item-value="Emp_Detail"
                    :single-line="emailRecievers.length > 0 ? false : true"
                  >
                    <template v-slot:append>
                      <v-slide-x-reverse-transition
                        mode="out-in"
                        v-if="emailsToSend && emailsToSend.length > 4"
                      >
                        <v-icon
                          color="primary"
                          size="20"
                          @click="isEditable = !isEditable"
                        >
                          fas fa-times
                        </v-icon>
                      </v-slide-x-reverse-transition>
                    </template></v-autocomplete
                  >
                </div>
                <v-autocomplete
                  v-if="emailRecievers.length > 0"
                  v-model="emailToField"
                  color="secondary"
                  :items="emailRecievers"
                  label="BCC"
                  readonly
                  multiple
                  solo
                  chips
                  density="compact"
                ></v-autocomplete>
                <v-autocomplete
                  v-if="ccEmailRecievers.length > 0"
                  v-model="emailCCField"
                  color="secondary"
                  :items="ccEmailRecievers"
                  label="CC"
                  readonly
                  multiple
                  solo
                  chips
                  density="compact"
                ></v-autocomplete>
              </div>
              <div
                class="text-subtitle-1 text-grey-darken-4 pb-2"
                v-if="
                  typeOfTemplate &&
                  typeOfTemplate.toLowerCase() === 'hiringforecastsettings'
                "
              >
                Do you want to add additional email?
                <CustomSelect
                  label="Additional email address"
                  v-model="selectedAdditionalEmail"
                  :itemSelected="selectedAdditionalEmail"
                  :items="additionEmail"
                  item-value="Emp_Email"
                  item-title="Emp_Detail"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    closableChips: true,
                    clearable: true,
                  }"
                  :single-line="true"
                  placeholder="Additional email address"
                  :isAutoComplete="true"
                  variant="solo"
                ></CustomSelect>
              </div>
              <VueEditor
                v-model="htmlContent"
                :editor-options="editorOptions"
              />
            </v-form>
          </div>
          <v-card class="overlay-footer py-2" elevation="16">
            <div class="d-flex justify-space-between w-100">
              <div>
                <v-btn
                  v-if="typeOfTemplate == 'hiringForecastSettings'"
                  class="ml-5 secondary"
                  variant="text"
                  elevation="2"
                  rounded="lg"
                  @click="this.$emit('custom-email-cancel')"
                >
                  <span class="primary"> Cancel </span>
                </v-btn>
              </div>
              <div>
                <v-btn
                  class="mr-5 secondary"
                  variant="text"
                  elevation="2"
                  @click="
                    typeOfTemplate == 'hiringForecastSettings'
                      ? this.$emit('without-custom-email')
                      : this.$emit('custom-email-cancel')
                  "
                  rounded="lg"
                >
                  <span class="primary">{{
                    typeOfTemplate == "hiringForecastSettings"
                      ? "Skip & Continue"
                      : "Cancel"
                  }}</span>
                </v-btn>
                <v-btn
                  class="secondary"
                  variant="elevated"
                  rounded="lg"
                  :disabled="
                    typeOfTemplate &&
                    typeOfTemplate.toLowerCase() === 'hiringforecastsettings'
                      ? emailsToSend &&
                        emailsToSend.length === 0 &&
                        selectedAdditionalEmail &&
                        selectedAdditionalEmail.length === 0
                      : false
                  "
                  @click="validateCustomEmailForm()"
                >
                  <span class="primary"> {{ submitText }} </span>
                </v-btn>
              </div>
            </div>
          </v-card>
          <AppLoading v-if="isLoading"></AppLoading>
        </div>
      </template>
    </v-overlay>
    <v-form v-else ref="customEmailForm">
      <div>
        <v-text-field
          v-model="subject"
          label="Subject"
          variant="solo"
          :rules="[required('Subject', subject)]"
          ref="subject"
        ></v-text-field>
      </div>
      <VueEditor v-model="htmlContent" :editor-options="editorOptions" />
    </v-form>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>

<script>
import VueEditor from "./VueEditor.vue";
import moment from "moment";
import validationRules from "@/mixins/validationRules";
import { SEND_CUSTOM_EMAIL } from "@/graphql/recruitment/recruitmentQueries.js";
import {
  recruitmentEmailTemplates,
  replacementTags,
} from "./recruitmentEmailTemplates";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
export default {
  name: "CustomEmail",
  components: {
    VueEditor,
    CustomSelect,
  },
  emits: ["custom-email-sent", "custom-email-cancel", "without-custom-email"],
  mixins: [validationRules],
  props: {
    typeOfTemplate: {
      type: String,
      required: true,
    },
    typeOfSchedule: {
      type: String,
      required: true,
    },
    templateEmail: {
      type: Array,
      required: true,
    },
    templateData: {
      type: Object,
      required: true,
    },
    isOverlay: {
      type: Boolean,
      default: false,
    },
    sendDateAlone: {
      type: [Boolean, Number],
      default: false,
    },
    selectedCandidateId: {
      type: String,
      required: false,
    },
    formId: {
      type: Number,
      required: true,
    },
    submitText: {
      type: String,
      default: "Send Email",
    },
    emailFullData: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    additionalEmail: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    emailRecievers: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    closeableChips: {
      type: Boolean,
      default: false,
    },
    toCCExchange: {
      type: Boolean,
      required: false,
      default: () => false,
    },
    ccEmailRecievers: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    notificationTimeNow: {
      type: Boolean,
      default: true,
    },
  },
  data: () => ({
    isLoading: false,
    subject: "",
    htmlContent: "",
    editorOptions: {
      theme: "snow",
    },
    showOverlay: false,
    emailsToSend: [],
    emailToField: [],
    emailCCField: [],
    showValidationAlert: false,
    validationMessages: [],
    tempEmailList: [],
    additionEmail: [],
    selectedAdditionalEmail: null,
    isEditable: false,
  }),
  watch: {
    additionalEmail: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length) {
          this.additionEmail = newVal;
        }
      },
    },
  },
  computed: {
    calendarStartTime() {
      if (
        this.templateData &&
        this.templateData.Start_Time &&
        this.templateData.Start_Time.length
      ) {
        return moment(
          this.templateData.Date + " " + this.templateData.Start_Time
        ).format("YYYY-MM-DD HH:mm");
      }
      return "";
    },
    calendarEndTime() {
      if (
        this.templateData &&
        this.templateData.End_Time &&
        this.templateData.End_Time.length
      ) {
        return moment(
          this.templateData.Date + " " + this.templateData.End_Time
        ).format("YYYY-MM-DD HH:mm");
      }
      return "";
    },
    typeSchedule() {
      if (this.typeOfSchedule) {
        if (this.typeOfSchedule.toLowerCase() === "noncalendar") {
          return "candidateScheduled";
        } else if (
          this.typeOfSchedule.toLowerCase() === "sunfishdeploymentemail"
        ) {
          return "sunfishdeploymentemail";
        } else if (
          this.typeOfSchedule.toLowerCase() === "newhirenotification"
        ) {
          return "newHireNotification";
        } else {
          return "interviewerScheduled";
        }
      }
      return "interviewerScheduled";
    },
  },
  mounted() {
    if (this.isOverlay) {
      this.showOverlay = true;
    }
    if (this.emailRecievers) {
      let emailTo = this.emailRecievers
        .flatMap((email) => email.split(","))
        .filter((email) => email.trim() !== "");
      this.emailToField = [...new Set(emailTo)];
    }
    if (this.templateEmail) {
      let allEmails = this.templateEmail
        .flatMap((email) => email.split(","))
        .filter((email) => email.trim() !== "");
      this.emailsToSend = [...new Set(allEmails)];
      this.tempEmailList = this.emailsToSend;
    }
    if (this.ccEmailRecievers) {
      let emailCC = this.ccEmailRecievers
        .flatMap((email) => email.split(","))
        .filter((email) => email.trim() !== "");
      this.emailCCField = [...new Set(emailCC)];
    }
    if (
      this.emailFullData &&
      this.typeOfTemplate &&
      this.typeOfTemplate.toLowerCase() === "hiringforecastsettings"
    ) {
      this.emailsToSend = this.emailFullData.map((email) => email.Emp_Detail);
      this.tempEmailList = this.emailFullData;
    }
    this.formEmail();
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async validateCustomEmailForm() {
      const { valid } = await this.$refs.customEmailForm.validate();
      if (valid) {
        //Check if there is data in text editor
        if (this.htmlContent && this.htmlContent.length) {
          if (this.notificationTimeNow) {
            this.sendCustomEmail();
          } else {
            this.$emit("custom-email-sent");
          }
          return true;
        } else {
          let snackbarData = {
            isOpen: true,
            message:
              "Text editor is empty. Please provide some text before proceeding.",
            type: "warning",
          };
          this.showAlert(snackbarData);
          return false;
        }
      } else {
        return false;
      }
    },
    // Function to replace tags with corresponding values
    replaceTags(template, replacementTags, templateData) {
      for (const [tag, replacement] of Object.entries(replacementTags)) {
        if (
          template.includes(tag) &&
          templateData.hasOwnProperty(replacement)
        ) {
          // Check if tag exists in template and replacement data exists
          template = template.replace(
            new RegExp("\\" + tag, "g"),
            templateData[replacement]
          );
        }
      }
      return template;
    },

    formEmail() {
      if (recruitmentEmailTemplates[this.typeOfTemplate]) {
        this.subject = this.replaceTags(
          recruitmentEmailTemplates[this.typeOfTemplate].subject,
          replacementTags,
          this.templateData
        );
        this.htmlContent = this.replaceTags(
          recruitmentEmailTemplates[this.typeOfTemplate].body,
          replacementTags,
          this.templateData
        );
      }
    },
    sendCustomEmail(customData = null, url) {
      let vm = this;
      vm.isLoading = true;
      if (!customData) {
        customData = {
          formId: this.formId,
          typeOfInterview: "onlineinterview",
          typeOfSchedule: this.typeSchedule,
          bccEmails: this.toCCExchange
            ? this.emailRecievers
            : this.emailsToSend,
          toMailIds: this.toCCExchange
            ? this.emailsToSend
            : this.emailRecievers,
          ccEmails: this.ccEmailRecievers,
          subject: this.subject,
          htmlContent: this.htmlContent,
          startDateTime: this.calendarStartTime,
          endDateTime: this.calendarEndTime,
          location: this.templateData.Location,
          description: null,
        };
      } else {
        if (recruitmentEmailTemplates[customData.typeOfTemplate]) {
          customData.subject = this.replaceTags(
            recruitmentEmailTemplates[customData.typeOfTemplate].subject,
            replacementTags,
            customData.templateData
          );
          customData.htmlContent = this.replaceTags(
            recruitmentEmailTemplates[customData.typeOfTemplate].body,
            replacementTags,
            customData.templateData
          );
        }
        if (customData.templateData.Date) {
          if (this.sendDateAlone || !this.templateData.Start_Time) {
            customData.startDateTime = moment(this.templateData.Date).format(
              "YYYY-MM-DD"
            );
          } else if (this.templateData.Start_Time) {
            customData.startDateTime = moment(
              this.templateData.Date + " " + this.templateData.Start_Time
            ).format("YYYY-MM-DD HH:mm");
          }
        }
        if (customData.templateData.Date) {
          if (this.sendDateAlone || !this.templateData.End_Time) {
            customData.endDateTime = moment(this.templateData.Date).format(
              "YYYY-MM-DD"
            );
          } else if (this.templateData.End_Time) {
            customData.endDateTime = moment(
              this.templateData.Date + " " + this.templateData.End_Time
            ).format("YYYY-MM-DD HH:mm");
          }
        }
      }
      customData["templateName"] = this.templateData.emailTemplateType
        ? this.templateData.emailTemplateType
        : null;
      // if (this.selectedCandidateId) {
      customData["candidateId"] = this.selectedCandidateId
        ? this.selectedCandidateId
        : null;
      // }
      if (
        this.typeOfTemplate &&
        this.typeOfTemplate.toLowerCase() === "hiringforecastsettings"
      ) {
        customData["bccEmails"] = [];
        this.tempEmailList.forEach((email) => {
          if (this.emailsToSend.includes(email.Emp_Detail)) {
            customData["bccEmails"].push(email.Emp_Email);
          }
        });
        if (
          this.selectedAdditionalEmail &&
          this.selectedAdditionalEmail.length
        ) {
          customData["bccEmails"] = [
            ...new Set([
              ...customData["bccEmails"],
              ...this.selectedAdditionalEmail,
            ]),
          ];
        }
      }
      if (url)
        customData.htmlContent = customData.htmlContent.replace(
          "[Meeting Link]",
          url
        );
      vm.$apollo
        .mutate({
          mutation: SEND_CUSTOM_EMAIL,
          variables: customData,
          client: "apolloClientAQ",
        })
        .then(() => {
          vm.isLoading = false;
          vm.$emit("custom-email-sent");
        })
        .catch((err) => {
          vm.isLoading = false;
          this.handleError(err);
        });
    },
    handleError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.showValidationAlert = true;
          this.$emit("custom-email-cancel");
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
<style scoped>
.overlay-content-parent > .v-overlay__content {
  width: 40%;
}
@media screen and (max-width: 960px) {
  .overlay-content-parent > .v-overlay__content {
    width: 100%;
  }
}

.overlay-card {
  height: 100%;
  width: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.overlay-body {
  height: calc(100vh - 90px);
  overflow-y: scroll !important;
  overflow: hidden;
}
.overlay-footer {
  height: 7%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
</style>
