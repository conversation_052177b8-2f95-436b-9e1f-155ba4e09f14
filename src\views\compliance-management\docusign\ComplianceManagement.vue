<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row justify="center">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu class="justify-end" :isFilter="false" />
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container
      v-if="formAccess"
      fluid
      class="compliance-management-container"
    >
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <DocumentTemplateEngine
            v-if="currentTabItem === 'tab-1'"
            :form-access="formAccess"
            @access-denied="isAccessDenied = true"
            @add-opened="hideTab = $event"
            @view-not-supported="onShowWarningModal()"
          />
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
      <v-dialog
        v-model="openWarningModal"
        width="350px"
        @click:outside="openWarningModal = false"
      >
        <v-card class="card-radius" min-height="220">
          <div class="d-flex justify-end">
            <v-icon
              color="primary"
              class="pr-4 pt-4 font-weight-bold"
              @click="openWarningModal = false"
            >
              fas fa-times
            </v-icon>
          </div>
          <v-card-text
            class="mt-4 d-flex justify-center align-center flex-column"
          >
            <div
              class="text-center my-2 body-1 text-primary font-weight-medium"
            >
              This feature is only available for desktop view
            </div>
          </v-card-text>
        </v-card>
      </v-dialog>
    </v-container>
    <AppAccessDenied v-else></AppAccessDenied>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";

const DocumentTemplateEngine = defineAsyncComponent(() =>
  import("./configuration/DocumentTemplateEngine.vue")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
export default {
  name: "ComplianceManagement",

  components: {
    EmployeeDefaultFilterMenu,
    DocumentTemplateEngine,
  },

  data: () => ({
    currentTabItem: "tab-1",
    mainTabList: ["Document Generator", "Configuration"],
    configurationTab: "tab-1",
    isMounted: false,
    isAccessDenied: false,
    hideTab: false,
    openWarningModal: false,
  }),

  computed: {
    // to fetch access rights
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("134");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  mounted() {
    if (this.formAccess) {
      this.tabsList = ["Document Generator", "Configuration"]; // present custom form name as tab name
    }
    this.isMounted = true;
  },

  methods: {
    onTabChange(tabName) {
      if (tabName.toLowerCase() !== "configuration") {
        window.location.href =
          this.$store.getters.baseUrl + "in/compliance-management/docusign";
      }
    },
    onShowWarningModal() {
      this.openWarningModal = true;
    },
    closeWarningModal() {
      this.openWarningModal = false;
    },
    // show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style scoped>
.compliance-management-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
@media screen and (max-width: 805px) {
  .compliance-management-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
