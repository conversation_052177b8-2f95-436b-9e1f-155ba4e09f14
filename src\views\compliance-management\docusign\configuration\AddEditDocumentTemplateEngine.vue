<template>
  <div>
    <!-- Initial Popup Form Modal -->
    <v-dialog
      v-if="showInitialForm"
      :model-value="showInitialForm"
      :width="isMobileView ? '95vw' : '75vw'"
      :max-width="isMobileView ? '95vw' : '800px'"
      :fullscreen="isMobileView"
      persistent
    >
      <v-card class="rounded-lg">
        <v-card-title
          class="d-flex align-center justify-space-between pa-4 bg-primary"
        >
          <span class="text-h6"
            >{{ isClone ? "Clone" : isEdit ? "Edit" : "Add" }} Document
            Template</span
          >
          <v-icon @click="closeForm">fas fa-times</v-icon>
        </v-card-title>

        <v-card-text class="pa-4 overflow-y-scroll">
          <v-form ref="initialForm">
            <v-row>
              <!-- Template Name -->
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.templateName"
                  variant="solo"
                  counter="150"
                  :rules="[
                    required('Template Name', formData.templateName),
                    validateWithRulesAndReturnMessages(
                      formData.templateName,
                      'templateName',
                      'Template Name'
                    ),
                  ]"
                >
                  <template v-slot:label>
                    Template Name<span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>

              <!-- Form Dropdown -->
              <v-col cols="12" md="6">
                <CustomSelect
                  v-model="formData.formId"
                  :items="formsList"
                  label="Form"
                  :is-loading="formsLoading"
                  :is-auto-complete="true"
                  :is-required="true"
                  item-title="customFormName"
                  item-value="formId"
                  :item-selected="formData.formId"
                  :rules="[required('Form', formData.formId)]"
                  @selected-item="onFormChange"
                />
              </v-col>

              <!-- Category Dropdown -->
              <v-col v-if="formData?.formId == 16" cols="12" md="6">
                <CustomSelect
                  v-model="formData.category"
                  :items="categoryOptions"
                  label="Category"
                  item-title="title"
                  item-value="value"
                  :item-selected="formData.category"
                  @selected-item="formData.category = $event"
                />
              </v-col>

              <!-- Report Title Dropdown -->
              <v-col v-if="formData?.formId == 380" cols="12" md="6">
                <CustomSelect
                  v-model="formData.reportTitle"
                  :items="reportTitlesList"
                  label="Report Title"
                  :is-loading="reportTitlesLoading"
                  item-title="title"
                  item-value="id"
                  :item-selected="formData.reportTitle"
                  @selected-item="formData.reportTitle = $event"
                />
              </v-col>

              <!-- Document Attachment Toggle -->
              <v-col v-if="showDocumentAttachment" cols="12" md="6">
                <v-switch
                  color="primary"
                  v-model="formData.documentAttachment"
                  label="Document Attachment"
                  :true-value="1"
                  :false-value="0"
                  @update:model-value="onDocumentAttachmentChange"
                ></v-switch>
              </v-col>

              <!-- Document Sub Type -->
              <v-col v-if="showDocumentSubType" cols="12" md="6">
                <CustomSelect
                  v-model="formData.documentSubType"
                  :items="documentSubTypesList"
                  label="Document Sub Type"
                  :isRequired="true"
                  :is-loading="documentSubTypesLoading"
                  item-title="Document_Sub_Type"
                  item-value="Document_Sub_Type_Id"
                  :item-selected="formData.documentSubType"
                  :rules="[
                    required('Document Sub Type', formData.documentSubType),
                  ]"
                  @selected-item="formData.documentSubType = $event"
                />
              </v-col>

              <!-- Description -->
              <v-col cols="12">
                <v-textarea
                  v-model="formData.description"
                  label="Description"
                  counter="600"
                  variant="solo"
                  rows="3"
                  :rules="[
                    validateWithRulesAndReturnMessages(
                      formData.description,
                      'description',
                      'Description'
                    ),
                  ]"
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>

        <v-card-actions class="pa-4">
          <v-spacer />
          <v-btn
            rounded="lg"
            class="mr-3"
            variant="outlined"
            @click="closeForm"
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="primary"
            variant="elevated"
            :loading="submitLoading"
            @click="submitInitialForm"
          >
            Continue
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Main Template Editor Page -->
    <v-card v-if="showTemplateEditor" class="rounded-lg" min-height="600">
      <v-card-title class="d-flex align-center justify-space-between pa-4">
        <div class="d-flex align-center">
          <v-icon color="primary" class="mr-2">fas fa-file-alt</v-icon>
          <span class="text-h6 text-primary">{{ formData.templateName }}</span>
        </div>
        <div class="d-flex align-center">
          <v-icon color="primary" @click="closeForm">fas fa-times</v-icon>
        </div>
      </v-card-title>

      <v-card-text class="pa-0">
        <!-- Two Column Layout -->
        <v-row style="min-height: 600px">
          <!-- Left Sidebar - Template Sections (40% width) -->
          <v-col cols="12" md="4" class="template-sidebar">
            <div
              class="pa-4 h-100"
              style="
                border-right: 1px solid #e0e0e0;
                background-color: #fafafa;
                max-width: 100%;
              "
            >
              <div class="text-h6 mb-3 text-primary">Template Sections</div>

              <!-- Header Section -->
              <v-card
                class="mb-3"
                :class="{ 'section-active': activeSection === 'header' }"
              >
                <div class="pa-3 d-flex align-center justify-space-between">
                  <span class="text-h6">Header Section</span>
                  <v-btn
                    color="primary"
                    variant="plain"
                    size="small"
                    @click="openHeaderCustomization"
                  >
                    <v-icon class="mr-1" size="small">fas fa-edit</v-icon>
                    Customize Header
                  </v-btn>
                </div>
                <v-card-text class="pa-3 pt-0">
                  <div
                    v-if="templateData.headerContent"
                    class="mt-2 text-caption text-grey"
                  >
                    Header content added
                  </div>
                  <div v-else class="mt-2 text-caption text-grey">
                    No header content
                  </div>
                </v-card-text>

                <v-divider :thickness="4"></v-divider>

                <!-- Letter Content Section -->

                <div class="pa-3 d-flex align-center justify-space-between">
                  <span class="text-h6">Letter Content</span>
                  <v-btn
                    color="primary"
                    variant="plain"
                    size="small"
                    @click="openContentManagement"
                  >
                    <v-icon class="mr-1" size="small">fas fa-edit</v-icon>
                    Manage Content
                  </v-btn>
                </div>
                <v-card-text class="pa-3 pt-0">
                  <div
                    v-if="templateData.letterContent"
                    class="mt-2 text-caption text-grey"
                  >
                    Main content added
                  </div>
                  <div v-else class="mt-2 text-caption text-grey">
                    No main content
                  </div>
                </v-card-text>
                <v-divider :thickness="4"></v-divider>
                <!-- Footer Section -->

                <div class="pa-3 d-flex align-center justify-space-between">
                  <span class="text-h6">Footer Section</span>
                  <v-btn
                    color="primary"
                    variant="plain"
                    size="small"
                    @click="openFooterCustomization"
                  >
                    <v-icon class="mr-1" size="small">fas fa-edit</v-icon>
                    Customize Footer
                  </v-btn>
                </div>
                <v-card-text class="pa-3 pt-0">
                  <div
                    v-if="templateData.footerContent"
                    class="mt-2 text-caption text-grey"
                  >
                    Footer content added
                  </div>
                  <div v-else class="mt-2 text-caption text-grey">
                    No footer content
                  </div>
                </v-card-text>
              </v-card>

              <!-- Action Buttons -->
              <div class="d-flex justify-center gap-2 mt-4">
                <v-btn
                  color="primary"
                  variant="outlined"
                  @click="previewTemplate"
                >
                  <v-icon class="mr-1">fas fa-eye</v-icon>
                  Preview
                </v-btn>
                <v-btn
                  rounded="lg"
                  class="primary"
                  variant="elevated"
                  :loading="saveLoading"
                  @click="addUpdateDocument"
                >
                  <v-icon class="mr-1">fas fa-save</v-icon>
                  Save
                </v-btn>
              </div>
            </div>
          </v-col>

          <!-- Right Side - Live Preview (60% width) -->
          <v-col cols="12" md="7" class="template-preview ml-6">
            <div class="pa-4 h-100" style="max-width: 100%">
              <div
                class="preview-document"
                style="
                  background: white;
                  border: 1px solid #e0e0e0;
                  border-radius: 8px;
                  min-height: 65vh;
                  overflow-y: auto;
                  width: 100%;
                "
              >
                <div></div>
                <div class="pa-6">
                  <!-- Header Preview -->
                  <div
                    v-if="templateData.headerContent"
                    class="header-preview mb-6"
                  >
                    <div
                      class="ck-content preview-content"
                      v-html="templateData.headerContent"
                    ></div>
                    <div
                      v-if="headerData.addPageBreak"
                      class="page-break-indicator my-2"
                    >
                      <v-divider></v-divider>
                      <div class="text-center text-caption text-grey mt-1">
                        Page Break
                      </div>
                    </div>
                  </div>

                  <!-- Content Preview -->
                  <div
                    v-if="templateData.letterContent"
                    class="content-preview mb-6"
                  >
                    <div
                      class="ck-content preview-content"
                      v-html="templateData.letterContent"
                    ></div>
                  </div>

                  <!-- Footer Preview -->
                  <div v-if="templateData.footerContent" class="footer-preview">
                    <div
                      class="ck-content preview-content"
                      v-html="templateData.footerContent"
                    ></div>
                  </div>

                  <!-- Empty State -->
                  <div
                    v-if="
                      !templateData.headerContent &&
                      !templateData.letterContent &&
                      !templateData.footerContent
                    "
                    class="text-center pa-8"
                  >
                    <v-icon color="grey" size="48" class="mb-4"
                      >fas fa-file-alt</v-icon
                    >
                    <div class="text-h6 text-grey mb-2">No Content Added</div>
                    <div class="text-body-2 text-grey max-width-400 mx-auto">
                      Start by customizing the header, content, or footer
                      sections to see your template preview here.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Header Customization Modal -->
    <v-dialog
      v-if="showHeaderModal"
      :model-value="showHeaderModal"
      :width="isMobileView ? '95vw' : '75vw'"
      :max-width="isMobileView ? '95vw' : '1000px'"
      :fullscreen="isMobileView"
      persistent
    >
      <v-card class="rounded-lg">
        <v-card-title
          class="d-flex align-center justify-space-between pa-4 bg-primary"
        >
          <span class="text-h6">Customize Header</span>
          <div class="d-flex align-center">
            <v-icon @click="resetHeaderToDefault" size="20" class="mr-1"
              >fas fa-undo</v-icon
            >
            <span class="mr-2">{{
              isMobileView ? "Reset" : "  Reset To Default"
            }}</span>
            <v-icon @click="closeHeaderModal">fas fa-times</v-icon>
          </div>
        </v-card-title>

        <v-card-text class="pa-4 overflow-y-scroll">
          <v-col cols="3" class="px-md-5 pb-0 mb-0" style="margin-left: auto">
            <CustomSelect
              :items="headerPlaceholders"
              placeholder="Insert Placeholders"
              v-model="selectedHeaderPlaceholder"
              :itemSelected="selectedHeaderPlaceholder"
              item-title="title"
              item-value="value"
              variantType="underlined"
              density="compact"
              @update:modelValue="insertPlaceholder('header')"
            >
              <template #append-inner>
                <v-icon>fas fa-chevron-down</v-icon>
              </template>
            </CustomSelect>
          </v-col>

          <CKEditor5
            :key="`header-${showHeaderModal}`"
            ref="headerEditor"
            class="mt-n5"
            v-model="templateData.headerContent"
            placeholder="Enter header content..."
            @editor-initialized="onHeaderEditorReady"
          />

          <!-- <v-checkbox
            v-model="headerData.addPageBreak"
            label="Add page break"
            class="mt-4"
          /> -->
        </v-card-text>

        <v-card-actions class="pa-4">
          <v-spacer />
          <v-btn
            rounded="lg"
            class="mr-3"
            variant="outlined"
            @click="closeHeaderModal"
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="primary"
            variant="elevated"
            @click="saveHeaderContent"
          >
            <v-icon class="mr-1">fas fa-eye</v-icon>
            Preview
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Content Management Modal -->
    <v-dialog
      v-if="showContentModal"
      :model-value="showContentModal"
      :width="isMobileView ? '95vw' : '75vw'"
      :max-width="isMobileView ? '95vw' : '1000px'"
      :fullscreen="isMobileView"
      persistent
    >
      <v-card class="rounded-lg">
        <v-card-title
          class="d-flex align-center justify-space-between pa-4 bg-primary"
        >
          <span class="text-h6">Edit Letter Content</span>
          <v-icon @click="closeContentModal">fas fa-times</v-icon>
        </v-card-title>

        <v-card-text class="pa-4 overflow-y-scroll">
          <v-col cols="3" class="px-md-5 pb-0 mb-0" style="margin-left: auto">
            <CustomSelect
              :items="contentPlaceholders"
              placeholder="Insert Placeholders"
              v-model="selectedContentPlaceholder"
              :itemSelected="selectedContentPlaceholder"
              item-title="title"
              item-value="value"
              variantType="underlined"
              density="compact"
              :disabledValue="getDisabledPlaceholderTitles(contentPlaceholders)"
              @update:modelValue="insertPlaceholder('content')"
            >
              <template #append-inner>
                <v-icon>fas fa-chevron-down</v-icon>
              </template>
            </CustomSelect>
          </v-col>

          <CKEditor5
            :key="`content-${showContentModal}`"
            ref="contentEditor"
            class="mt-n5"
            v-model="templateData.letterContent"
            placeholder="Enter letter content..."
            @editor-initialized="onContentEditorReady"
          />
        </v-card-text>

        <v-card-actions class="pa-4">
          <v-spacer />
          <v-btn
            rounded="lg"
            class="mr-3"
            variant="outlined"
            @click="closeContentModal"
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="primary"
            variant="elevated"
            @click="saveContentContent"
          >
            <v-icon class="mr-1">fas fa-eye</v-icon>
            Preview
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Footer Customization Modal -->
    <v-dialog
      v-if="showFooterModal"
      :model-value="showFooterModal"
      :width="isMobileView ? '95vw' : '75vw'"
      :max-width="isMobileView ? '95vw' : '1000px'"
      :fullscreen="isMobileView"
      persistent
    >
      <v-card class="rounded-lg">
        <v-card-title
          class="d-flex align-center justify-space-between pa-4 bg-primary"
        >
          <span class="text-h6">Customize Footer</span>
          <v-icon @click="closeFooterModal">fas fa-times</v-icon>
        </v-card-title>

        <v-card-text class="pa-4 overflow-y-scroll">
          <v-col cols="3" class="px-md-5 pb-0 mb-0" style="margin-left: auto">
            <CustomSelect
              :items="footerPlaceholders"
              placeholder="Insert Placeholders"
              v-model="selectedFooterPlaceholder"
              :itemSelected="selectedFooterPlaceholder"
              item-title="title"
              item-value="value"
              variantType="underlined"
              density="compact"
              @update:modelValue="insertPlaceholder('footer')"
            >
              <template #append-inner>
                <v-icon>fas fa-chevron-down</v-icon>
              </template>
            </CustomSelect>
          </v-col>

          <CKEditor5
            :key="`footer-${showFooterModal}`"
            ref="footerEditor"
            class="mt-n5"
            v-model="templateData.footerContent"
            placeholder="Enter footer content..."
            @editor-initialized="onFooterEditorReady"
          />
        </v-card-text>

        <v-card-actions class="pa-4">
          <v-spacer />
          <v-btn
            rounded="lg"
            class="mr-3"
            variant="outlined"
            @click="closeFooterModal"
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="primary"
            variant="elevated"
            @click="saveFooterContent"
          >
            <v-icon class="mr-1">fas fa-eye</v-icon>
            Preview
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Preview Modal -->
    <!-- <v-dialog
      v-if="showPreviewModal"
      :model-value="showPreviewModal"
      :width="isMobileView ? '95vw' : '1200'"
      :max-width="isMobileView ? '95vw' : '1200px'"
      :fullscreen="isMobileView"
    >
      <v-card class="rounded-lg">
        <v-card-title class="d-flex align-center justify-space-between pa-4">
          <span class="text-h6">Template Preview</span>
          <v-icon color="primary" @click="closePreviewModal"
            >fas fa-times</v-icon
          >
        </v-card-title>

        <v-card-text class="pa-4">
          <div
            class="preview-container"
            style="background: white; padding: 20px; border: 1px solid #ddd"
          >
            <div
              v-if="templateData.headerContent"
              class="ck-content"
              v-html="templateData.headerContent"
            ></div>
            <div
              v-if="headerData.addPageBreak"
              style="page-break-after: always"
            ></div>

            <div
              v-if="templateData.letterContent"
              class="ck-content"
              v-html="templateData.letterContent"
            ></div>

            <div
              v-if="templateData.footerContent"
              class="ck-content"
              v-html="templateData.footerContent"
            ></div>
          </div>
        </v-card-text>

        <v-card-actions class="pa-4">
          <v-spacer />
          <v-btn color="secondary" variant="text" @click="closePreviewModal">
            Close
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog> -->

    <AppLoading v-if="saveLoading" />
  </div>
</template>

<script>
import { defineComponent } from "vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import CKEditor5 from "@/views/common/customEmail/CKEditor5.vue";
import { ADD_UPDATE_DOCUMENT_TEMPLATE } from "@/graphql/compliance-management/docuSignQueries";
import { LIST_SUB_DOC_TYPE } from "@/graphql/dropDownQueries";
import { LIST_DOCUMENT_TEMPLATE_FIELDS } from "@/graphql/compliance-management/docuSignQueries";
import validationRules from "@/mixins/validationRules";

export default defineComponent({
  name: "AddEditDocumentTemplateEngine",

  components: {
    CustomSelect,
    CKEditor5,
  },

  mixins: [validationRules],

  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    isClone: {
      type: Boolean,
      default: false,
    },
    docTemplateDetails: {
      type: Object,
      default: () => ({}),
    },
  },

  emits: [
    "close-add-form",
    "add-success",
    "expand-panel",
    "close-form-with-warning",
  ],

  data() {
    return {
      // Form states
      showInitialForm: true,
      showTemplateEditor: false,
      showHeaderModal: false,
      showContentModal: false,
      showFooterModal: false,
      showPreviewModal: false,

      // Loading states
      submitLoading: false,
      saveLoading: false,
      formsLoading: false,
      reportTitlesLoading: false,
      documentSubTypesLoading: false,

      // Form data
      formData: {
        templateName: "",
        formId: null,
        category: null,
        reportTitle: null,
        documentAttachment: 0,
        documentSubType: null,
        description: "",
      },

      // Template data
      templateData: {
        headerContent: "",
        letterContent: "",
        footerContent: "",
      },

      // Header data
      headerData: {
        addPageBreak: false,
      },

      // UI state
      activeSection: null, // Track which section is being edited

      // Editor instances
      headerEditor: null,
      contentEditor: null,
      footerEditor: null,

      // Dropdown data
      formsList: [],
      reportTitlesList: [],
      documentSubTypesList: [],

      // Category options
      categoryOptions: [
        { title: "Offer Letter", value: "offer_letter" },
        { title: "Others", value: "others" },
      ],

      // Placeholders
      headerPlaceholders: [
        { title: "Company Name", value: "{Company Name}" },
        { title: "Organization Logo", value: "{Organization Logo}" },
        { title: "Organization Address", value: "{Organization Address}" },
      ],

      contentPlaceholders: [],

      footerPlaceholders: [
        { title: "Company Name", value: "{Company Name}" },
        { title: "Company Email", value: "{Company Email}" },
        { title: "Organization Address", value: "{Organization Address}" },
        { title: "Organization Logo", value: "{Organization Logo}" },
      ],
      selectedHeaderPlaceholder: [],
      selectedContentPlaceholder: [],
      selectedFooterPlaceholder: [],
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    multipleAccessRights() {
      return this.$store.getters.formIdsBasedAccessRights;
    },
    showDocumentAttachment() {
      const selectedForm = this.formsList.find(
        (form) => form.formId === this.formData.formId
      );
      return (
        selectedForm &&
        (selectedForm?.formId === 243 || selectedForm?.formId === 16)
      );
    },

    showDocumentSubType() {
      return (
        this.showDocumentAttachment && this.formData.documentAttachment === 1
      );
    },
  },

  watch: {
    "formData.formId"(newVal) {
      if (newVal) {
        this.fetchReportTitles();
      }
    },
  },

  mounted() {
    this.initializeComponent();
  },

  beforeUnmount() {
    this.destroyEditors();
  },

  methods: {
    // Initialize component
    async initializeComponent() {
      if (this.isEdit && this.docTemplateDetails) {
        this.prefillEditData();
      }
      this.getDropdownDetails();
      this.fetchPlaceholderList();
    },

    // Prefill data for edit mode
    prefillEditData() {
      const details = this.docTemplateDetails;
      this.formData = {
        templateName: details.title || "",
        formId: details.formId || null,
        category: details.category || "",
        reportTitle: details.reportTitle || null,
        documentAttachment: details.documentAttachment || 0,
        documentSubType: details.documentSubType || null,
        description: details.description || "",
      };

      this.templateData = {
        headerContent: details.headerContent || "",
        letterContent: details.templateContent || "",
        footerContent: details.footerContent || "",
      };
    },

    // Fetch report titles based on selected form
    async fetchReportTitles() {
      this.reportTitlesLoading = true;
      // Mock data for now - replace with actual API call
      this.reportTitlesList = [
        { id: 1, title: "Salary Revision Letter" },
        { id: 2, title: "Offer Letter" },
        { id: 3, title: "Appointment Letter" },
        { id: 4, title: "Experience Certificate" },
      ];
      this.reportTitlesLoading = false;
    },

    // Fetch document sub types
    fetchDocumentSubTypes() {
      let vm = this;
      vm.documentSubTypesLoading = true;
      vm.$apollo
        .query({
          query: LIST_SUB_DOC_TYPE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentSubType &&
            !response.data.listDocumentSubType.errorCode
          ) {
            const { documentSubType } = response.data.listDocumentSubType;
            vm.documentSubTypesList =
              documentSubType && documentSubType.length > 0
                ? documentSubType
                : [];
          }
          vm.documentSubTypesLoading = false;
        })
        .catch((error) => {
          vm.handleListError(error);
        });
    },
    handleListError(err = "") {
      this.documentSubTypesLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "document sub types",
        isListError: true,
      });
    },
    fetchPlaceholderList() {
      let vm = this;
      vm.documentSubTypesLoading = true;
      vm.$apollo
        .query({
          query: LIST_DOCUMENT_TEMPLATE_FIELDS,
          client: "apolloClientO",
        })
        .then((response) => {
          let { documentTemplateFieldDetails, errorCode } =
            response.data.listDocumentTemplateFields;
          if (documentTemplateFieldDetails && !errorCode) {
            let docFields = JSON.parse(documentTemplateFieldDetails);

            // Transform the data for categorized placeholders (only for content)
            vm.contentPlaceholders = vm.transformPlaceholderData(docFields);
          }
          vm.documentSubTypesLoading = false;
        })
        .catch((error) => {
          vm.handleListError(error);
        });
    },
    // Handle form change
    onFormChange(formId) {
      this.formData.formId = formId;

      // Update category options based on form
      const selectedForm = this.formsList.find(
        (form) => form.formId === formId
      );
      if (selectedForm?.formName === "Job Candidates") {
        this.categoryOptions = [
          { title: "Offer Letter", value: "offer_letter" },
          { title: "Others", value: "others" },
        ];
      }

      // Reset category if not available
      if (
        !this.categoryOptions.find(
          (cat) => cat.value === this.formData.category
        )
      ) {
        this.formData.category = "";
      }
    },

    getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;
      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: 134,
            key: ["document_template_forms"],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "document_template_forms") {
                const tempFormIds = item.data?.map((item) => item.Form_Id);
                vm.formsList = vm.multipleAccessRights(tempFormIds) || [];
                if (!vm.isEdit) {
                  const defaultForm = item.data?.find(
                    (item) => item.Default_Flag?.toLowerCase() === "yes"
                  );
                  vm.formData.formId = defaultForm
                    ? defaultForm?.Form_Id
                    : vm.formsList[0]?.Form_Id;
                }
              }
            });
          } else {
            vm.handleGetDropdownDetails(
              res.data.retrieveDropdownDetails?.errorCode || ""
            );
          }
        })
        .catch((err) => {
          vm.handleGetDropdownDetails(err);
        })
        .finally(() => {
          vm.dropdownLoading = false;
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "dropdown details",
        isListError: false,
      });
    },

    // Transform placeholder data into categorized format
    transformPlaceholderData(docFields) {
      const placeholders = [];

      // Iterate through each main category (Employee, Candidate, Organization, Signing Authority)
      Object.keys(docFields).forEach((mainCategory) => {
        const categoryData = docFields[mainCategory];

        // Add main category header (disabled item for visual separation)
        placeholders.push({
          title: `--- ${mainCategory} ---`,
          value: `category_${mainCategory}`,
          disabled: true,
          isCategory: true,
        });

        // Iterate through subcategories (Personal, Job, Contact, etc.)
        Object.keys(categoryData).forEach((subCategory) => {
          const fields = categoryData[subCategory];

          // Add subcategory header if there are multiple subcategories
          if (Object.keys(categoryData).length > 1) {
            placeholders.push({
              title: `  ${subCategory}`,
              value: `subcategory_${mainCategory}_${subCategory}`,
              disabled: true,
              isSubCategory: true,
            });
          }

          // Add individual fields
          fields.forEach((field) => {
            placeholders.push({
              title: `    ${field}`,
              value: `{${field}}`, // Remove spaces for placeholder value
              disabled: false,
              isField: true,
              category: mainCategory,
              subCategory: subCategory,
            });
          });
        });
      });

      return placeholders;
    },

    // Get disabled placeholder titles for CustomSelect
    getDisabledPlaceholderTitles(placeholders) {
      return placeholders
        .filter((item) => item.disabled)
        .map((item) => item.title);
    },
    // Handle document attachment change
    onDocumentAttachmentChange(value) {
      this.formData.documentAttachment = value;
      if (value) {
        this.fetchDocumentSubTypes();
      } else {
        this.formData.documentSubType = null;
      }
    },

    // Submit initial form
    async submitInitialForm() {
      const { valid } = await this.$refs.initialForm.validate();
      if (!valid) {
        return;
      }

      this.submitLoading = true;
      try {
        // Validate required fields
        if (!this.formData.templateName || !this.formData.formId) {
          throw new Error("Please fill all required fields");
        }

        // Move to template editor
        this.showInitialForm = false;
        this.showTemplateEditor = true;
        this.$emit("expand-panel", true);
      } catch (error) {
        this.$store.dispatch("showSnackbar", {
          message: error.message || "Error submitting form",
          color: "error",
        });
      } finally {
        this.submitLoading = false;
      }
    },

    // Header customization methods
    openHeaderCustomization() {
      this.activeSection = "header";
      this.showHeaderModal = true;
    },

    closeHeaderModal() {
      this.activeSection = null;
      this.headerEditor = null;
      this.showHeaderModal = false;
    },

    onHeaderEditorReady(editor) {
      this.headerEditor = editor;
    },

    saveHeaderContent() {
      this.closeHeaderModal();
    },

    resetHeaderToDefault() {
      this.templateData.headerContent = "";
      this.headerData.addPageBreak = false;
    },

    // Content management methods
    openContentManagement() {
      this.activeSection = "content";
      this.showContentModal = true;
    },

    closeContentModal() {
      this.activeSection = null;
      this.contentEditor = null;
      this.showContentModal = false;
    },

    onContentEditorReady(editor) {
      this.contentEditor = editor;
    },

    saveContentContent() {
      this.closeContentModal();
    },

    // Footer customization methods
    openFooterCustomization() {
      this.activeSection = "footer";
      this.showFooterModal = true;
    },

    closeFooterModal() {
      this.activeSection = null;
      this.footerEditor = null;
      this.showFooterModal = false;
    },

    onFooterEditorReady(editor) {
      this.footerEditor = editor;
    },

    saveFooterContent() {
      this.closeFooterModal();
    },

    // Placeholder insertion
    insertPlaceholder(section) {
      let editorComponent;
      let placeholder;

      switch (section) {
        case "header":
          editorComponent = this.$refs.headerEditor;
          placeholder = this.selectedHeaderPlaceholder;
          break;
        case "content":
          editorComponent = this.$refs.contentEditor;
          placeholder = this.selectedContentPlaceholder;
          break;
        case "footer":
          editorComponent = this.$refs.footerEditor;
          placeholder = this.selectedFooterPlaceholder;
          break;
      }

      // For content section, check if it's a valid field (not category header)
      if (section === "content") {
        const selectedItem = this.contentPlaceholders.find(
          (item) => item.value === placeholder
        );

        // Only insert if it's not a disabled category/subcategory header
        if (selectedItem && !selectedItem.disabled && selectedItem.isField) {
          if (editorComponent && editorComponent.insertPlaceholder) {
            editorComponent.insertPlaceholder(placeholder);
          }
        }
      } else {
        // For header and footer, use the old flow (no validation needed)
        if (editorComponent && editorComponent.insertPlaceholder) {
          editorComponent.insertPlaceholder(placeholder);
        }
      }

      // Reset selections
      this.selectedHeaderPlaceholder = [];
      this.selectedContentPlaceholder = [];
      this.selectedFooterPlaceholder = [];
    },

    // Preview functionality
    previewTemplate() {
      this.showPreviewModal = true;
    },

    closePreviewModal() {
      this.showPreviewModal = false;
    },

    // Refresh preview
    refreshPreview() {
      // Force reactivity update for preview
      this.$forceUpdate();
    },
    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    addUpdateDocument() {
      this.saveLoading = true;
      let vm = this;
      let templateContent = this.combineTemplateContent();
      // add table styles when the table tag has no styles to avoid table preview issue
      templateContent = templateContent.replace(
        /<table>/g,
        "<table border='1' cellpadding='1' cellspacing='1' width='100%'>"
      );
      if (templateContent) {
        try {
          let signatoryKeys = [];
          // parse string html to html
          let doc = new DOMParser().parseFromString(
            templateContent,
            "text/html"
          );
          let imgs = doc.querySelectorAll("img"), // get all img tags from html
            imgAlts = [],
            validSignatureEntities = [
              "Candidate",
              "Employee",
              "Manager or Admin",
              "Organization Logo",
            ];
          // loop through number of img tags in html
          for (var image of imgs) {
            let imgAlt = image.alt;
            // check img alt is any one of valid signatory entity
            if (validSignatureEntities.indexOf(imgAlt) > -1)
              imgAlts.push(imgAlt);
          }
          if (imgAlts.length > 0) {
            // remove duplication from array
            signatoryKeys = [...new Set(imgAlts)];
          }
          let title = this.formData.templateName,
            isRegisteredBusinessAddress = 1;
          vm.$apollo
            .mutate({
              mutation: ADD_UPDATE_DOCUMENT_TEMPLATE,
              variables: {
                documentTemplateId: this.isClone
                  ? 0
                  : this.isEdit
                  ? this.docTemplateDetails.documentTemplateId
                  : 0,
                title: title,
                templateContent: templateContent,
                registeredBusinessAddress: parseInt(
                  isRegisteredBusinessAddress
                ),
                signatoryKeys: signatoryKeys,
                formId: this.formData.formId,
              },
              client: "apolloClientP",
            })
            .then(() => {
              let snackbarData = {
                isOpen: true,
                message: "Template configured successfully",
                type: "success",
              };
              vm.showAlert(snackbarData);
              vm.saveLoading = false;
              vm.$emit("add-success");
            })
            .catch((err) => {
              vm.handleAddEditError(err);
            });
        } catch {
          vm.handleAddEditError();
        }
      } else {
        vm.saveLoading = false;
        let snackbarData = {
          isOpen: true,
          message: "Template content should not be empty.",
          type: "warning",
        };
        vm.showAlert(snackbarData);
      }
    },
    handleAddEditError(err = "") {
      this.saveLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isClone ? "cloning" : this.isEdit ? "updating" : "adding",
        form: "document template",
        isListError: false,
      });
    },

    // Combine template content
    combineTemplateContent() {
      let content = "";

      // Add header content
      if (this.templateData.headerContent) {
        content += `<div class="template-header">${this.templateData.headerContent}</div>`;
        if (this.headerData.addPageBreak) {
          content += '<div style="page-break-after: always; height: 0;"></div>';
        }
      }

      // Add main content
      if (this.templateData.letterContent) {
        content += `<div class="template-content">${this.templateData.letterContent}</div>`;
      }

      // Add footer content
      if (this.templateData.footerContent) {
        content += `<div class="template-footer">${this.templateData.footerContent}</div>`;
      }

      return content;
    },

    // Destroy all editors
    destroyEditors() {
      // CKEditor5 components handle their own cleanup automatically
      // We just need to clear the references
      this.headerEditor = null;
      this.contentEditor = null;
      this.footerEditor = null;
    },

    // Close form
    closeForm() {
      // Clear editor references without destroying them
      // The CKEditor5 components will handle their own cleanup
      this.headerEditor = null;
      this.contentEditor = null;
      this.footerEditor = null;
      this.$emit("close-add-form");
    },
  },
});
</script>

<style scoped>
.editor-container {
  border: 1px solid #ddd;
  border-radius: 4px;
}

.ck-content {
  min-height: 200px;
  padding: 16px;
}

.preview-container {
  max-height: 600px;
  overflow-y: auto;
}

.custom-label-color {
  color: rgba(0, 0, 0, 0.6);
}

/* Two-column layout styles */
.template-sidebar {
  min-height: 600px;
  max-width: 100%;
}

.template-preview {
  min-height: 600px;
  max-width: 100%;
}

.section-active {
  border-color: #1976d2 !important;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

.preview-document {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-break-indicator {
  border-top: 2px dashed #e0e0e0;
  margin: 16px 0;
  position: relative;
}

.page-break-indicator::before {
  content: "";
  position: absolute;
  top: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: repeating-linear-gradient(
    to right,
    #e0e0e0 0px,
    #e0e0e0 8px,
    transparent 8px,
    transparent 16px
  );
}

/* Preview content styling */
.preview-content {
  /* background: #fafafa; */
  /* border: 1px solid #f0f0f0; */
  border-radius: 4px;
  padding: 16px;
  min-height: 60px;
}

.preview-section-label {
  display: flex;
  align-items: center;
}

.max-width-400 {
  max-width: 400px;
}

.gap-2 {
  gap: 8px;
}

/* Placeholder dropdown styling */
:deep(.v-list-item) {
  .v-list-item__content {
    font-weight: normal;
  }
}

/* Category headers styling */
:deep(.v-list-item[aria-disabled="true"]) {
  .v-list-item__content {
    font-weight: 600;
    color: #1976d2 !important;
    opacity: 0.8;
  }

  background-color: #f5f5f5 !important;
  pointer-events: none;
}

/* Subcategory styling */
:deep(.v-list-item[aria-disabled="true"]) {
  .v-list-item__content:has-text("  ") {
    font-weight: 500;
    color: #666 !important;
    font-style: italic;
  }
}

/* Field items styling */
:deep(.v-list-item:not([aria-disabled="true"])) {
  .v-list-item__content {
    padding-left: 8px;
  }
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .template-sidebar {
    border-right: none !important;
    border-bottom: 1px solid #e0e0e0;
  }

  .template-preview {
    border-top: 1px solid #e0e0e0;
  }
}
</style>
