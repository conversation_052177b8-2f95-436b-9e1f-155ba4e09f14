<template>
  <div class="mt-6">
    <v-row v-if="!showAddForm && !showEditForm && !showViewForm">
      <v-col cols="12" class="pa-0">
        <div v-if="isListLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="docTemplateList.length === 0 && !showAddForm"
          :button-text="formAccess && formAccess.add ? 'Add New' : ''"
          image-name="compliance-management/document-generator/document-generator-empty"
          icon-name="add"
          @button-click="addDocTemplate()"
        />
        <ListDocumentTemplateEngine
          v-else
          :table-items="docTemplateList"
          :form-access="formAccess"
          @view-doc-template="viewDocTemplate($event)"
          @on-open-add-form="addDocTemplate()"
          @on-open-edit-form="editDocTemplate($event)"
          @delete-success="retryFetching()"
          @refetch-data="retryFetching()"
        />
      </v-col>
    </v-row>
    <div v-else>
      <div v-if="showAddForm || showEditForm" class="pa-0">
        <AddEditDocumentTemplateEngine
          :is-edit="showEditForm"
          :is-clone="isClone"
          :doc-template-details="selectedDocTemplate"
          @close-add-form="closeAddEditForm()"
          @add-success="retryFetching()"
          @expand-panel="isExpandablePanel = $event"
          @close-form-with-warning="closeFormWithWarning()"
        />
      </div>
      <div v-else>
        <ViewDocumentTemplateEngine
          :form-access="formAccess"
          :doc-template-details="selectedDocTemplate"
          @close-view-form="closeViewForm()"
          @open-edit-form="editDocTemplate()"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import ListDocumentTemplateEngine from "./ListDocumentTemplateEngine";
const AddEditDocumentTemplateEngine = defineAsyncComponent(() =>
  import("./AddEditDocumentTemplateEngine")
);
const ViewDocumentTemplateEngine = defineAsyncComponent(() =>
  import("./ViewDocumentTemplateEngine")
);
import { LIST_DOCUMENT_TEMPLATE } from "@/graphql/compliance-management/docuSignQueries";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default defineComponent({
  name: "DocumentTemplateEngine",

  components: {
    ListDocumentTemplateEngine,
    AddEditDocumentTemplateEngine,
    ViewDocumentTemplateEngine,
  },

  props: {
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
  },

  data() {
    return {
      // form variables
      showAddForm: false,
      showEditForm: false,
      showViewForm: false,
      isClone: false,

      // errors
      isErrorInList: false,
      errorContent: "",
      isListLoading: true,
      isExpandablePanel: true,

      // data
      selectedDocTemplate: {},
      docTemplateList: [],
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    orgCode() {
      return this.$store.state.orgDetails.orgCode;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  watch: {
    showAddForm(val) {
      this.$emit("add-opened", val);
    },
    showEditForm(val) {
      this.$emit("add-opened", val);
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.fetchDocumentTemplateList();
  },

  methods: {
    // Fetch document template list
    async fetchDocumentTemplateList() {
      this.isListLoading = true;
      this.isErrorInList = false;

      try {
        const response = await this.$apollo.query({
          query: LIST_DOCUMENT_TEMPLATE,
          client: "apolloClientO",
          fetchPolicy: "no-cache",
        });

        if (response && response.data && response.data.listDocumentTemplate) {
          let { documentTemplateDetails, errorCode } =
            response.data.listDocumentTemplate;
          if (documentTemplateDetails && !errorCode) {
            this.docTemplateList = documentTemplateDetails;
            this.isListLoading = false;
            mixpanel.track("DocumentTemplate-list-fetch-success");
          } else {
            this.handleListError(errorCode);
          }
        } else {
          this.handleListError();
        }
      } catch (err) {
        this.handleListError(err);
      }
    },

    // retry doc template list API
    retryFetching() {
      this.closeAddEditForm();
      this.fetchDocumentTemplateList();
    },

    // show add form while clicking "Add New" button and close view and edit form
    addDocTemplate() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.showAddForm = true;
      this.showViewForm = false;
      this.showEditForm = false;
      this.isClone = false;
    },

    // open view form when any of specific record is clicked in doc-template list and close add and edit forms
    viewDocTemplate(selRecord) {
      this.selectedDocTemplate = selRecord;
      this.showAddForm = false;
      this.showViewForm = true;
      this.showEditForm = false;
      this.isClone = false;
    },

    // open edit form when clicking edit icon in individual view form and close view and add form
    editDocTemplate(item = {}) {
      if (item && item.length && Object.keys(item[0]).length) {
        this.selectedDocTemplate = item[0];
        this.isClone = item[1]?.toLowerCase() === "clone" ? true : false;
      }
      this.showAddForm = false;
      this.showViewForm = false;
      this.showEditForm = true;
    },

    // close add or edit form
    closeAddEditForm() {
      this.showAddForm = false;
      this.showEditForm = false;
      this.isClone = false;
    },

    closeFormWithWarning() {
      this.closeAddEditForm();
      this.$emit("view-not-supported");
    },

    // close view form and reset selected doc template details
    closeViewForm() {
      this.selectedDocTemplate = {};
      this.showViewForm = false;
    },

    // handle list document template error from BE using store action like TeamSummary
    handleListError(err = "") {
      this.isListLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "document template list",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
      mixpanel.track("DocumentTemplate-list-fetch-error");
    },

    // Function to show error or success message
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>
