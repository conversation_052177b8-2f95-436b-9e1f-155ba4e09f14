<template>
  <div>
    <div
      class="d-flex align-center mb-4"
      :class="isMobileView ? 'justify-center' : 'justify-end'"
    >
      <v-btn
        v-if="formAccess?.add"
        class="mr-3 ml-2"
        color="primary"
        @click="$emit('on-open-add-form')"
      >
        <v-icon class="mr-2"> fas fa-plus </v-icon>
        Add New
      </v-btn>
      <v-btn
        rounded="lg"
        color="transparent"
        variant="flat"
        class="ml-0"
        @click="$emit('refetch-data')"
      >
        <v-icon>fas fa-redo-alt</v-icon>
      </v-btn>
    </div>
    <v-data-table
      v-if="tableList.length > 0"
      :headers="tableHeaders"
      :items="tableList"
      :items-per-page="50"
      :items-per-page-options="[
        { value: 50, title: '50' },
        { value: 100, title: '100' },
        {
          value: -1,
          title: '$vuetify.dataFooter.itemsPerPageAll',
        },
      ]"
      fixed-header
      :sort-by="[{ key: 'title', order: 'asc' }]"
      :height="tableList.length > 11 ? $store.getters.getTableHeight(270) : ''"
      class="elevation-1"
      style="box-shadow: none !important"
    >
      <template v-slot:item="{ item }">
        <tr
          style="z-index: 200; cursor: pointer"
          class="data-table-tr bg-white cursor-pointer"
          @click="selectDocTemplateRecord(item)"
          :class="[
            isMobileView ? ' v-data-table__mobile-table-row ma-0 mt-2' : 'pr-6',
          ]"
        >
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2 pl-5'
            "
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Template Name
            </div>
            <section style="height: 3em" class="d-flex align-center">
              <div class="d-flex align-center">
                <div
                  v-if="windowWidth > 1264"
                  class="data-table-side-border d-flex"
                  style="height: 3em"
                ></div>
              </div>
              <span
                class="text-primary text-body-2 font-weight-regular text-truncate"
              >
                <section :class="isMobileView ? '' : 'text-truncate'">
                  <span class="text-primary font-weight-regular">
                    {{ checkNullValue(item.title) }}
                  </span>
                </section>
              </span>
            </section>
          </td>
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2 pl-5 font-weight-small'
            "
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Form Name
            </div>
            <section>
              <span class="text-body-2 font-weight-regular">
                {{ checkNullValue(item.formName) }}
              </span>
            </section>
          </td>
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2 pl-5 font-weight-small'
            "
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Registered Business Address
            </div>
            <section>
              <span class="text-body-2 font-weight-regular">
                {{ item.registeredBusinessAddress ? "Yes" : "No" }}
              </span>
            </section>
          </td>
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2'
            "
            @click.stop=""
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Actions
            </div>
            <section>
              <div class="d-flex justify-center">
                <ActionMenu
                  v-if="getActions?.length"
                  @selected-action="onActions($event, item)"
                  :actions="getActions"
                  :access-rights="checkAccess()"
                  iconColor="grey"
                ></ActionMenu>
                <section class="text-body-2 font-weight-medium" v-else>
                  -
                </section>
              </div>
            </section>
          </td>
        </tr>
      </template>
    </v-data-table>
    <AppWarningModal
      v-if="openDeleteConfirmation"
      :open-modal="openDeleteConfirmation"
      confirmation-heading="Are you sure to delete the selected record?"
      icon-name="fas fa-trash"
      icon-Size="75"
      @close-warning-modal="closeDeleteConfirmationModal()"
      @accept-modal="deleteDocumentTemplate()"
    />
    <AppLoading v-if="loadingScreen" />
  </div>
</template>

<script>
// queries
import { DELETE_DOCUMENT_TEMPLATE } from "@/graphql/compliance-management/docuSignQueries";
import { checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ListDocumentTemplateEngine",

  components: {
    ActionMenu,
  },

  props: {
    tableItems: {
      type: Array,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
  },

  data: () => ({
    tableList: [],
    havingAccess: {},
    deleteModel: false,

    // list data
    tableHeaders: [
      {
        title: "Template Name",
        key: "title",
      },
      {
        title: "Form Name",
        key: "formName",
      },
      {
        title: "Registered Business Address",
        key: "registeredBusinessAddress",
      },
      {
        title: "Actions",
        key: "action",
        align: "center",
        sortable: false,
      },
    ],
    selectedRecord: {},

    // others
    openDeleteConfirmation: false,
    loadingScreen: false,
    deleteTemplateId: null,
  }),

  computed: {
    // search value, searched in the top bar
    searchValue() {
      return this.$store.state.empSearchValue;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    windowWidth() {
      return this.$store.state.windowWidth;
    },
    getActions() {
      const optionMenu = [];
      if (this.formAccess?.update) optionMenu.push("Edit");
      if (this.formAccess?.add) optionMenu.push("Clone");
      if (this.formAccess?.delete) optionMenu.push("Delete");
      return optionMenu;
    },
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.tableList = this.tableItems || [];
  },

  methods: {
    checkNullValue,
    onApplySearch(val) {
      if (!val) {
        this.tableList = this.tableItems || [];
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.tableItems || [];
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.tableList = searchItems;
      }
    },

    checkAccess() {
      this.havingAccess["update"] = this.formAccess?.update ? 1 : 0;
      this.havingAccess["delete"] = this.formAccess?.delete ? 1 : 0;
      this.havingAccess["clone"] = this.formAccess?.add ? 1 : 0;
      return this.havingAccess;
    },
    onActions(action, item) {
      if (action.toLowerCase() === "edit" || action.toLowerCase() === "clone") {
        this.$emit("on-open-edit-form", [item, action]);
      } else if (action.toLowerCase() === "delete") {
        this.deleteModel = true;
        this.onDelete(item.documentTemplateId);
      }
    },

    // select the record to view
    selectDocTemplateRecord(selectedRecord) {
      this.selectedRecord = selectedRecord;
      this.$emit("view-doc-template", selectedRecord);
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    // clear search and filter data
    clearFilter() {
      this.$store.commit("UPDATE_TOPBAR_CLEAR_FLAG", true);
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    // on click delete icon in each record, show warning alert before triggering the event
    onDelete(documentTemplateId) {
      this.deleteTemplateId = documentTemplateId;
      this.openDeleteConfirmation = true;
    },

    // close delete confirmation modal
    closeDeleteConfirmationModal() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.openDeleteConfirmation = false;
    },

    // delete the selected document template
    deleteDocumentTemplate() {
      let vm = this;
      vm.loadingScreen = true;
      vm.closeDeleteConfirmationModal();
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_DOCUMENT_TEMPLATE,
            variables: {
              documentTemplateId: vm.deleteTemplateId,
            },
            client: "apolloClientP",
          })
          .then(() => {
            vm.loadingScreen = false;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Template deleted successfully.",
            };
            vm.clearFilter(); // clear search value
            vm.showAlert(snackbarData);
            vm.deleteTemplateId = null;
            vm.$emit("delete-success");
          })
          .catch((deleteError) => {
            vm.handleDeleteError(deleteError);
          });
      } catch {
        vm.handleDeleteError();
      }
    },

    // handle doc template delete error from backend
    handleDeleteError(err = "") {
      this.loadingScreen = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "document template",
        isListError: false,
      });
    },

    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}
</style>
