<template>
  <div>
    <v-card min-height="500" class="card-radius">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-primary font-weight-medium pl-4">
          <v-avatar
            class="mr-1"
            :class="[
              'avatarBCColor' + (docTemplateDetails.documentTemplateId % 5),
            ]"
            size="35"
          >
            <span class="body-1 font-weight-medium">
              {{ letterAvatar(docTemplateDetails.title, true) }}
            </span>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            {{ docTemplateDetails.title }}
          </div>
        </div>
        <div
          v-if="docTemplateDetails.formName"
          class="d-flex align-center text-truncate text-primary font-weight-medium pl-4"
          :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
        >
          {{ $t("form") }}: {{ docTemplateDetails.formName }}
        </div>
        <div class="pa-3 d-flex align-center">
          <v-btn
            v-if="formAccess && formAccess.update && !isMobileView"
            class="mr-1"
            color="primary"
            @click="$emit('open-edit-form')"
          >
            Edit
          </v-btn>
          <v-icon color="primary" @click="$emit('close-view-form')">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card-text>
        <v-row justify="center">
          <v-col cols="10" class="body-1 text-primary">
            <div id="docTemplateContent" class="mt-4 ck-content" />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
export default {
  name: "ViewDocumentTemplateEngine",

  props: {
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    docTemplateDetails: {
      type: Object,
      required: true,
    },
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  methods: {
    // Method to return first letter in capital of each word (replaces deprecated filter)
    letterAvatar(value, isSingleLetter) {
      if (!value) return "";
      var firstChar = value ? value.charAt(0).toUpperCase() : "";
      var lastChar = value
        ? value.split(" ").pop().charAt(0).toUpperCase()
        : "";
      //condition checked for single letter avatar
      if (isSingleLetter) {
        return firstChar;
      } else {
        return firstChar + lastChar;
      }
    },
  },

  mounted() {
    let content = this.docTemplateDetails.templateContent;
    let element = document.getElementById("docTemplateContent");
    element.innerHTML = content;
  },
};
</script>
