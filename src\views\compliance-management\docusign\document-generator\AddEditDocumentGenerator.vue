<template>
  <div>
    <v-card v-if="!tempFieldErrorObj.isError" class="card-radius">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center primary--text">
          <v-chip
            class="mr-2 rounded-tl-lg rounded-br-xl white--text pa-4"
            label
            large
            color="primary"
            style="height: 50px"
          >
            New
          </v-chip>
          <div class="body-1">Generate Document</div>
        </div>
      </div>
      <v-row justify="center">
        <v-col cols="12" lg="6" md="10" sm="12" class="pa-0">
          <v-stepper v-model="currentStep" alt-labels elevation="0">
            <v-stepper-header>
              <v-stepper-step complete step="1" color="secondary">
                <span style="white-space: nowrap"> Document Details </span>
              </v-stepper-step>
              <!-- <v-divider /> -->
              <v-stepper-step
                complete
                step="2"
                :color="currentStep >= 2 ? 'secondary' : 'grey lighten-2'"
              >
                <span style="white-space: nowrap"> Document Editor </span>
              </v-stepper-step>
              <!-- <v-divider /> -->
              <v-stepper-step
                complete
                step="3"
                :color="currentStep >= 3 ? 'secondary' : 'grey lighten-2'"
              >
                <span style="white-space: nowrap"> Document Preview </span>
              </v-stepper-step>
            </v-stepper-header>
          </v-stepper>
        </v-col>
      </v-row>
    </v-card>
    <v-card
      v-if="isFetchingTemplateList || tempFieldErrorObj.isError"
      :min-height="$store.getters.getTableHeight(230)"
      class="card-radius mt-4"
    >
      <v-overlay
        v-if="isFetchingTemplateList"
        absolute
        :opacity="'0.9'"
        color="#fff"
        z-index="1"
      >
        <v-progress-circular color="secondary" indeterminate size="54" />
      </v-overlay>
      <AppFetchErrorScreen
        v-else
        image-name="compliance-management/document-generator/document-generator-empty"
        :content="tempFieldErrorObj.errorMessage"
        :button-text="
          tempFieldErrorObj.errorType === 'empty' ? 'Configure' : 'Retry'
        "
        :icon-name="tempFieldErrorObj.errorType === 'empty' ? 'add' : 'refresh'"
        @button-click="
          tempFieldErrorObj.errorType === 'empty'
            ? $emit('change-to-config-tab')
            : retryFetching()
        "
      />
    </v-card>
    <div v-else class="my-6">
      <ValidationObserver ref="addDocGeneratorObserver">
        <v-form>
          <div v-if="currentStep === 1">
            <v-card class="card-radius mt-4">
              <v-row justify="center">
                <v-col cols="10" class="pb-0">
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <div class="body-1 font-weight-medium primary--text mb-2">
                        Document Details
                      </div>
                    </v-col>
                    <v-col cols="6">
                      <v-label>Document Name</v-label>
                      <ValidationProvider
                        ref="docNameProvider"
                        v-slot="{ errors }"
                        :name="$t('documentName')"
                        rules="required|min:3|max:150|vComment"
                      >
                        <v-text-field
                          v-model="documentName"
                          style="max-width: 300px"
                          class="mt-n2"
                          counter="150"
                          maxlength="150"
                          :error-messages="errors"
                        />
                      </ValidationProvider>
                    </v-col>
                    <v-col cols="6">
                      <v-label>Choose a Template</v-label>
                      <ValidationProvider
                        ref="templateProvider"
                        v-slot="{ errors }"
                        :name="$t('template')"
                        rules="required"
                      >
                        <v-autocomplete
                          v-model="selectedDocTemplateId"
                          :items="docTemplateList"
                          :error-messages="errors"
                          item-text="title"
                          item-value="documentTemplateId"
                          style="max-width: 300px"
                          class="mt-n2"
                          color="primary"
                          @input="onChangeTemplate()"
                        />
                      </ValidationProvider>
                    </v-col>
                    <v-col v-if="showEmpList" cols="6">
                      <v-label>Employee Name</v-label>
                      <ValidationProvider
                        ref="empNameProvider"
                        v-slot="{ errors }"
                        name="Employee name"
                        rules="required"
                      >
                        <v-autocomplete
                          ref="chosenWorkSchedule"
                          v-model="selectedEmployee"
                          :items="employeeList"
                          :loading="isFetchingEmpList"
                          item-color="secondary"
                          color="primary"
                          item-text="employeeName"
                          item-value="employeeId"
                          :error-messages="errors"
                          style="max-width: 300px"
                          class="mt-n2"
                        >
                          <template #item="{ item }">
                            <div class="d-flex align-center py-1">
                              <v-avatar
                                size="30"
                                color="grey lighten-3"
                                class="mr-1"
                              >
                                <v-icon color="primary lighten-3">
                                  person
                                </v-icon>
                              </v-avatar>
                              <div>
                                <div class="body-2">
                                  {{ item.employeeName }}
                                </div>
                                <div class="caption">
                                  {{ item.userDefinedEmployeeId }}
                                </div>
                              </div>
                            </div>
                          </template>
                        </v-autocomplete>
                      </ValidationProvider>
                    </v-col>
                    <v-col v-if="showCandidateList" cols="6">
                      <v-label>Candidate Name</v-label>
                      <ValidationProvider
                        ref="candidateNameProvider"
                        v-slot="{ errors }"
                        name="Candidate name"
                        rules="required"
                      >
                        <v-autocomplete
                          ref="chosenWorkSchedule"
                          v-model="selectedCandidate"
                          :items="candidateList"
                          :loading="isFetchingCandidateList"
                          item-color="secondary"
                          color="primary"
                          item-text="candidateName"
                          item-value="candidateId"
                          :error-messages="errors"
                          style="max-width: 300px"
                          class="mt-n2"
                        >
                          <template #item="{ item }">
                            <div class="d-flex align-center py-1">
                              <v-avatar
                                size="30"
                                color="grey lighten-3"
                                class="mr-1"
                              >
                                <v-icon color="primary lighten-3">
                                  person
                                </v-icon>
                              </v-avatar>
                              <div>
                                <div class="body-2">
                                  {{ item.candidateName }}
                                </div>
                              </div>
                            </div>
                          </template>
                        </v-autocomplete>
                      </ValidationProvider>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
            <v-card
              v-for="(customComponent, index) in keysOfCustomCompList"
              :key="customComponent + index"
              class="card-radius mt-4"
            >
              <v-row justify="center">
                <v-col cols="10" class="pb-0">
                  <v-row>
                    <v-col cols="12" class="py-0">
                      <div class="body-1 font-weight-medium primary--text mb-2">
                        {{ customComponent }}
                      </div>
                    </v-col>
                    <v-col
                      v-for="(component, compIndex) in customComponentList[
                        customComponent
                      ]"
                      :key="component + compIndex"
                      cols="6"
                    >
                      <v-label>{{ component }}</v-label>
                      <ValidationProvider
                        v-if="component === 'Candidate'"
                        ref="candidateProvider"
                        v-slot="{ errors }"
                        name="Candidate"
                        rules="required"
                      >
                        <v-autocomplete
                          v-model="customComponentInputVariables[component]"
                          :items="candidateList"
                          item-text="candidateName"
                          item-value="candidateId"
                          style="max-width: 300px"
                          class="mt-n2"
                          :error-messages="errors"
                        />
                      </ValidationProvider>
                      <ValidationProvider
                        v-if="component === 'Manager or Admin'"
                        ref="managerProvider"
                        v-slot="{ errors }"
                        name="Manager or Admin"
                        rules="required"
                      >
                        <v-autocomplete
                          v-model="customComponentInputVariables[component]"
                          :items="signatoryAdminManagerList"
                          item-text="employeeName"
                          item-value="employeeId"
                          style="max-width: 300px"
                          class="mt-n2"
                          :error-messages="errors"
                        >
                          <template #item="{ item }">
                            <div class="d-flex align-center py-1">
                              <v-avatar
                                size="30"
                                color="grey lighten-3"
                                class="mr-1"
                              >
                                <v-icon color="primary lighten-3">
                                  person
                                </v-icon>
                              </v-avatar>
                              <div>
                                <div class="body-2">
                                  {{ item.employeeName }}
                                </div>
                                <div class="caption">
                                  {{ item.userDefinedEmployeeId }}
                                </div>
                              </div>
                            </div>
                          </template>
                        </v-autocomplete>
                      </ValidationProvider>
                      <ValidationProvider
                        v-if="component === 'Employee'"
                        ref="managerAdminProvider"
                        v-slot="{ errors }"
                        name="Employee"
                        rules="required"
                      >
                        <v-autocomplete
                          v-model="customComponentInputVariables[component]"
                          :items="signatoryEmployeesList"
                          item-text="employeeName"
                          item-value="employeeId"
                          style="max-width: 300px"
                          class="mt-n2"
                          :error-messages="errors"
                        >
                          <template #item="{ item }">
                            <div class="d-flex align-center py-1">
                              <v-avatar
                                size="30"
                                color="grey lighten-3"
                                class="mr-1"
                              >
                                <v-icon color="primary lighten-3">
                                  person
                                </v-icon>
                              </v-avatar>
                              <div>
                                <div class="body-2">
                                  {{ item.employeeName }}
                                </div>
                                <div class="caption">
                                  {{ item.userDefinedEmployeeId }}
                                </div>
                              </div>
                            </div>
                          </template>
                        </v-autocomplete>
                      </ValidationProvider>
                      <ValidationProvider
                        v-if="component === 'Candidate CTC'"
                        ref="CTCProvider"
                        v-slot="{ errors }"
                        name="Candidate CTC"
                        :rules="ctcValidation"
                      >
                        <v-text-field
                          v-model="candidateCtc"
                          color="primary"
                          type="number"
                          style="max-width: 300px"
                          class="mt-n2"
                          :error-messages="errors"
                        />
                      </ValidationProvider>
                      <ValidationProvider
                        v-if="component === 'Candidate Basic Pay'"
                        ref="basicPayProvider"
                        v-slot="{ errors }"
                        name="Candidate Basic Pay"
                        rules="required|numberWithTwoDecimal|minimum:0|maximum:99999999"
                      >
                        <v-text-field
                          v-model="candidateBasicPay"
                          color="primary"
                          type="number"
                          style="max-width: 300px"
                          class="mt-n2"
                          :error-messages="errors"
                        />
                      </ValidationProvider>
                      <ValidationProvider
                        v-if="component === 'Signing Bonus'"
                        ref="signingBonusProvider"
                        v-slot="{ errors }"
                        name="Signing Bonus"
                        rules="numberWithTwoDecimal|minimum:0|maximum:99999999"
                      >
                        <v-text-field
                          v-model="signingBonus"
                          color="primary"
                          type="number"
                          style="max-width: 300px"
                          class="mt-n2"
                          :error-messages="errors"
                        />
                      </ValidationProvider>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
          </div>
        </v-form>
      </ValidationObserver>
      <v-card v-if="currentStep === 2" class="mt-4 pa-8">
        <v-overlay
          v-if="editorLoadInProgress"
          absolute
          :opacity="'0.9'"
          color="#fff"
          z-index="1"
        >
          <v-progress-circular color="secondary" indeterminate size="54" />
        </v-overlay>
        <DocumentEditor
          ref="documentEditor"
          :editor-content="employeeDocumentHtmlContent"
        />
      </v-card>
      <v-card
        v-if="currentStep === 3"
        class="card-radius mt-4 pa-8"
        min-height="300"
      >
        <div id="empDocContent" class="ck-content" />
      </v-card>
    </div>
    <v-bottom-sheet
      v-model="openBottomSheet"
      hide-overlay
      persistent
      no-click-animation
      width="100%"
      :retain-focus="false"
    >
      <v-sheet
        class="align-center text-center"
        :class="windowWidth > 450 ? 'd-flex pa-4' : 'pa-2'"
        :style="windowWidth >= 1264 ? 'margin-left: 106px' : ''"
      >
        <v-row class="pa-0">
          <v-col cols="6" class="pa-0 d-flex align-center justify-start pl-2">
            <v-btn elevation="2" @click="openCloseConfirmationModal()">
              Cancel
            </v-btn>
            <v-btn
              v-if="(currentStep > 1 && !isEdit) || currentStep > 2"
              elevation="2"
              class="ml-2"
              @click="backToPrevStep()"
            >
              Previous
            </v-btn>
          </v-col>
          <v-col
            cols="6"
            class="pa-0 d-flex justify-center align-center pr-4"
            :style="windowWidth >= 1264 ? 'margin-left: -80px' : ''"
          >
            <v-btn
              v-if="currentStep === 1"
              color="primary"
              @click="onMovingToStepTwo()"
            >
              Generate
            </v-btn>
            <v-btn
              v-if="currentStep >= 2"
              color="primary"
              class="mr-2"
              @click="saveAsDraft()"
            >
              Save As Draft
            </v-btn>
            <v-btn
              v-if="currentStep === 2"
              color="primary"
              @click="previewDocument()"
            >
              Preview
            </v-btn>
            <v-btn
              v-if="currentStep === 3 && !isSignatureRequired"
              color="primary"
              @click="submitDocument()"
            >
              Submit
            </v-btn>
            <v-btn
              v-if="currentStep === 3 && isSignatureRequired"
              color="primary"
              @click="sendDocumentToReview()"
            >
              Submit For Review
            </v-btn>
          </v-col>
        </v-row>
      </v-sheet>
    </v-bottom-sheet>
    <AppLoading v-if="isPageLoading" />
    <AppWarningModel
      v-if="openWarningModal"
      :open-delete-confirmation="openWarningModal"
      image-name="common/exit_form"
      confirmation-content="Are you sure to exit this form?"
      @close-warning-modal="closeWarningModal()"
      @accept-modal="confirmCloseAddEditForm()"
    />
  </div>
</template>

<script>
import Config from "@/config.js";
const DocumentEditor = () => import("../DocumentEditor");
import {
  GENERATE_UPDATE_DOCUMENT_FOR_EMPLOYEE,
  LIST_TEMPLATES_IN_DROPDOWN,
  LIST_CUSTOM_COMPONENTS_IN_TEMPLATE,
  RETRIEVE_DOCUMENT_INPUTS,
  LIST_EMPLOYEES_IN_GENERATOR,
  LIST_CANDIDATES,
} from "@/graphql/compliance-management/docuSignQueries";
import {
  getErrorCodesWithValidation,
  handleNetworkErrors,
  getErrorCodes,
} from "@/helper";

export default {
  name: "AddDocumentGenerator",

  components: { DocumentEditor },

  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    docGeneratorDetails: {
      type: Object,
      required: true,
    },
  },

  data: () => ({
    documentName: "",
    selectedDocTemplateId: "",
    selectedDocTemplateContent: "",
    selectedEmployee: 0,
    selectedCandidate: 0,
    employeeDocumentHtmlContent: null,
    currentStep: 1,
    openBottomSheet: true,
    docTemplateList: [],
    employeeList: [],
    signatoryEmployeesList: [],
    candidateList: [],
    signatoryAdminManagerList: [],
    templateEntityList: [],
    customComponentList: {},
    customComponentInputVariables: {},
    isSignatureRequired: false,
    isPageLoading: false,
    isFetchingTemplateList: false,
    isFetchingEmpList: false,
    isFetchingCandidateList: false,
    documentInputFields: [],
    fetchManagerAndAdmin: 0,
    businessAddressContent: "",
    openWarningModal: false,
    showEmpList: false,
    showCandidateList: false,
    editorLoadInProgress: false,
    candidateCtc: null,
    candidateBasicPay: null,
    tempFieldErrorObj: {
      isError: false,
      errorType: "",
      errorMessage: "",
    },
    signingBonus: "",
  }),

  apollo: {
    listTemplatesInDropdown: {
      query: LIST_TEMPLATES_IN_DROPDOWN,
      client: "apolloClientO",
      fetchPolicy: "no-cache",
      skip: true,
      result({ data }) {
        try {
          if (data) {
            let { documentTemplateDetails, errorCode } =
              data.listTemplatesInDropdown;
            if (documentTemplateDetails && !errorCode) {
              this.docTemplateList = documentTemplateDetails;
            }
            this.tempFieldErrorObj["isError"] = false;
            this.isFetchingTemplateList = false;
          } else {
            this.handleTempListError();
          }
        } catch {
          this.handleTempListError();
        }
      },
      error(listTempErr) {
        this.handleTempListError(listTempErr);
      },
    },
  },

  computed: {
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // object keys of custom component list which is retrieved based on selected template
    keysOfCustomCompList() {
      return this.customComponentList
        ? Object.keys(this.customComponentList)
        : [];
    },
    ctcValidation() {
      if (
        this.candidateBasicPay &&
        this.candidateBasicPay > this.candidateCtc
      ) {
        return `required|numberWithTwoDecimal|minFloat:${this.candidateBasicPay}, Basic pay must not exceed CTC`;
      } else {
        return "required|numberWithTwoDecimal|minimum:0|maximum:99999999";
      }
    },
  },

  watch: {
    windowWidth(val) {
      if (val < 1264) {
        this.$emit("close-form-with-warning");
      }
    },
  },

  mounted() {
    if (this.isEdit) {
      const { documentContent, authorizedSignatories } =
        this.docGeneratorDetails;
      this.currentStep = 2;
      this.employeeDocumentHtmlContent = documentContent;
      this.isSignatureRequired = !!authorizedSignatories;
      this.editorLoadInProgress = true;
      setTimeout(() => {
        this.editorLoadInProgress = false;
      }, 2000);
    } else {
      this.isFetchingTemplateList = true;
      this.$apollo.queries.listTemplatesInDropdown.skip = false;
      this.$apollo.queries.listTemplatesInDropdown.refetch();
    }
  },

  beforeUnmount() {
    // before the component destroys, we have to close bottom sheet
    this.openBottomSheet = false;
  },

  methods: {
    openCloseConfirmationModal() {
      this.openWarningModal = true;
    },

    confirmCloseAddEditForm() {
      this.openWarningModal = false;
      this.$emit("close-add-edit-form");
    },
    closeWarningModal() {
      this.openWarningModal = false;
    },
    // while moving to step 2, we have to validate the fields in step 1
    onMovingToStepTwo() {
      this.$refs.addDocGeneratorObserver
        .validate()
        .then((validationResponse) => {
          // response will return true/false based on validation
          if (validationResponse) {
            this.retrieveDocumentInputs();
          }
        });
    },
    // retry doc template list API
    retryFetching() {
      this.isFetchingTemplateList = true;
      this.tempFieldErrorObj["isError"] = false;
      this.$apollo.queries.listTemplatesInDropdown.refetch();
    },
    // after getting the document input values, we have to assign it in template content based on key value
    generateDocument() {
      let templateContent = this.selectedDocTemplateContent;
      let startDelimiter = "{";
      let endDelimiter = "}";
      let templateFields = [];
      let startDelimiterLength = 1;
      let endDelimiterLength = 1;
      let startDelimiterIndex = 0;
      let endDelimiterIndex = 0;
      let templateFieldNames = "";
      let templateContentLength = templateContent.length;

      for (let searchIndex = 0; searchIndex <= templateContentLength; ) {
        //Find the index of the delimiter "{" in the template content.
        startDelimiterIndex = templateContent.indexOf(
          startDelimiter,
          searchIndex
        );
        //If the start delimiter does not exist
        if (startDelimiterIndex === -1) {
          break;
        } else {
          startDelimiterIndex += startDelimiterLength;
          //Find the index of the delimiter "}" in the template content starts from the previous delimiter "{"
          endDelimiterIndex = templateContent.indexOf(
            endDelimiter,
            startDelimiterIndex
          );
          if (endDelimiterIndex === -1) {
            break;
          } else {
            templateFieldNames = templateContent.slice(
              startDelimiterIndex,
              endDelimiterIndex
            );
            templateFields.push({
              originalFieldName: templateFieldNames,
              joinedFieldName: templateFieldNames.split(" ").join("_"),
            });
            searchIndex = endDelimiterIndex + endDelimiterLength;
          }
        }
      }
      // after finding the available input fields, we have to replace values
      for (let field of templateFields) {
        let replaceableString = "{" + field.originalFieldName + "}",
          inputValue = "";
        if (replaceableString === "{Candidate CTC}") {
          inputValue = this.candidateCtc;
        } else if (replaceableString === "{Candidate Basic Pay}") {
          inputValue = this.candidateBasicPay;
        } else if (replaceableString === "{Signing Bonus}") {
          inputValue = this.signingBonus;
        } else {
          inputValue = this.documentInputFields[field.joinedFieldName];
        }
        // replace the string only it has values
        if (inputValue !== undefined) {
          var inputRegex = new RegExp(replaceableString, "g");
          templateContent = templateContent.replace(
            inputRegex,
            inputValue ? inputValue : ""
          );
        }
      }
      if (this.documentInputFields["Organization_Logo"]) {
        let orgLogoS3Path =
          Config.publicImageS3Path + "Signature-Images/imagePlaceholder.png";
        var logoRegex = new RegExp(orgLogoS3Path, "g");
        templateContent = templateContent.replace(
          logoRegex,
          this.documentInputFields["Organization_Logo"]
        );
      }
      if (this.businessAddressContent) {
        var addressRegex = new RegExp("{Address}", "g");
        templateContent = templateContent.replace(
          addressRegex,
          this.businessAddressContent
        );
      }
      this.employeeDocumentHtmlContent = templateContent;
      this.currentStep = 2;
      this.editorLoadInProgress = true;
      setTimeout(() => {
        this.editorLoadInProgress = false;
      }, 2000);
    },
    // while clicking previous button we have to reduce -1 from the current step
    backToPrevStep() {
      this.currentStep -= 1;
    },
    // while clicking "Preview" button we have to move the step 3 and assign the template content value from the editor
    previewDocument() {
      let docTemplateContent = this.$refs.documentEditor
        ? this.$refs.documentEditor.editorData
        : this.employeeDocumentHtmlContent;
      // add table styles when the table tag has no styles to avoid table preview issue
      docTemplateContent = docTemplateContent.replace(
        /<table>/g,
        "<table border='1' cellpadding='1' cellspacing='1'>"
      );
      this.employeeDocumentHtmlContent = docTemplateContent;
      this.currentStep = 3;
      setTimeout(() => {
        let content = this.employeeDocumentHtmlContent;
        let element = document.getElementById("empDocContent");
        element.innerHTML = content;
      }, 1000);
    },
    // while clicking save as draft, we have to make API call to update the document with 'draft' status
    saveAsDraft() {
      this.generateUpdateEmpDocument("Draft");
    },
    // while clicking Submit button(without signature component), we have to update the document with 'completed' status
    submitDocument() {
      this.generateUpdateEmpDocument("Completed");
    },
    // while clicking review button(wiht signature component), we have to update the document with 'inreview' status
    sendDocumentToReview() {
      this.generateUpdateEmpDocument("In Review");
    },
    // fetch employees/manager/admin list for authority/employees based on passed parameters
    fetchEmployeesListBasedOnSource(listEmpSource, fetchManagerAndAdmin) {
      let vm = this;
      vm.isFetchingEmpList = true;
      try {
        vm.$apollo
          .query({
            query: LIST_EMPLOYEES_IN_GENERATOR,
            variables: {
              source: listEmpSource,
              fetchManagerAndAdmin: fetchManagerAndAdmin,
            },
            client: "apolloClientO",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            let { employeeDetails, errorCode } =
              res.data.listEmployeesInGenerator;
            if (employeeDetails && !errorCode) {
              // when the source is authority and not for manager/admin the consider the response for signatory employees list
              if (
                listEmpSource === "authorizerEmployees" &&
                !fetchManagerAndAdmin
              ) {
                vm.signatoryEmployeesList = employeeDetails;
              }
              // when the source is authority and for manager/admin the consider the response for signatory admin/manager list
              if (
                listEmpSource === "authorizerEmployees" &&
                fetchManagerAndAdmin
              ) {
                vm.signatoryAdminManagerList = employeeDetails;
              }
              // when the source is generatorEmployees then consider the response for generated employees list
              if (listEmpSource === "generatorEmployees") {
                vm.employeeList = employeeDetails;
              }
            }
            vm.isFetchingEmpList = false;
          })
          .catch((err) => {
            vm.isFetchingEmpList = false;
            vm.handleEmployeesListError(err);
          });
      } catch {
        vm.isFetchingEmpList = false;
        vm.handleEmployeesListError();
      }
    },
    fetchCandidateList() {
      let vm = this;
      vm.isFetchingCandidateList = true;
      try {
        vm.$apollo
          .query({
            query: LIST_CANDIDATES,
            client: "apolloClientO",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            let { candidateDetails, errorCode } = res.data.listCandidates;
            if (candidateDetails && !errorCode) {
              vm.candidateList = candidateDetails;
            }
            vm.isFetchingCandidateList = false;
          })
          .catch((err) => {
            vm.isFetchingCandidateList = false;
            vm.handleCandidateError(err);
          });
      } catch {
        vm.isFetchingCandidateList = false;
        vm.handleCandidateError();
      }
    },
    onChangeTemplate() {
      this.showEmpList = false;
      this.showCandidateList = false;
      this.selectedEmployee = 0;
      this.selectedCandidate = 0;
      this.customComponentInputVariables = {};
      this.customComponentList = [];
      // find index of selected document template
      let index = this.docTemplateList.findIndex(
        (obj) => obj.documentTemplateId === this.selectedDocTemplateId
      );
      // based on index we can find selected document name
      if (index > -1) {
        this.templateEntityList = this.docTemplateList[index].entity;
      }
      if (this.templateEntityList.includes("Employee")) {
        this.showEmpList = true;
        this.fetchEmployeesListBasedOnSource("generatorEmployees", 0);
      }
      if (this.templateEntityList.includes("Candidate")) {
        this.showCandidateList = true;
        this.fetchCandidateList();
      }
      this.retrieveCustomComponentsOfSelectedTemplate(index);
    },
    // retrieve custom components based on the selected template
    retrieveCustomComponentsOfSelectedTemplate(temIndex) {
      let vm = this;
      vm.isPageLoading = true;
      vm.customComponentList = [];
      // based on index we can find selected document name
      if (temIndex > -1)
        vm.selectedDocTemplateContent =
          vm.docTemplateList[temIndex].templateContent;
      try {
        vm.$apollo
          .query({
            query: LIST_CUSTOM_COMPONENTS_IN_TEMPLATE,
            variables: {
              documentTemplateId: vm.selectedDocTemplateId,
            },
            client: "apolloClientO",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            const { templateCustomComponents } =
              res.data.listTemplateCustomComponents;
            if (templateCustomComponents) {
              vm.customComponentList = JSON.parse(templateCustomComponents);
              // when custom component length is greater than 0 then consider signature is required(As of now we have signature comp alone)
              if (
                vm.customComponentList &&
                Object.keys(vm.customComponentList).length > 0
              ) {
                vm.isSignatureRequired = true;
              }
              // ex: customComponentList => {Signing Authority: [Employee, Manager or Admin]}
              for (let componentKey in vm.customComponentList) {
                // ex: componentKey => Signing Authority
                // ex: vm.customComponentList[componentKey] => [Employee, Manager or Admin]
                for (let componentType of vm.customComponentList[
                  componentKey
                ]) {
                  // fetch signatory employees list
                  if (componentType === "Employee") {
                    vm.fetchEmployeesListBasedOnSource(
                      "authorizerEmployees",
                      0
                    );
                  }
                  // fetch signatory manager/admin list
                  if (componentType === "Manager or Admin") {
                    vm.fetchEmployeesListBasedOnSource(
                      "authorizerEmployees",
                      1
                    );
                  }
                  // fetch candidate list
                  if (
                    componentType === "Candidate" &&
                    vm.candidateList.length === 0
                  ) {
                    vm.fetchCandidateList();
                  }
                  // assign default value for signatory as empty(initial declaration). based on their selection it will update.
                  vm.customComponentInputVariables[componentType] = "";
                }
              }
            }
            vm.isPageLoading = false;
          })
          .catch((err) => {
            vm.handleCustomCompListFetchError(err);
          });
      } catch {
        vm.handleCustomCompListFetchError();
      }
    },
    // retrieve document inputs based on selected template and employee
    retrieveDocumentInputs() {
      let vm = this;
      vm.isPageLoading = true;
      try {
        // find index of selected document template
        let index = vm.docTemplateList.findIndex(
            (obj) => obj.documentTemplateId === vm.selectedDocTemplateId
          ),
          isRegisteredBusinessAddress = 0;
        // based on index we can find registered business address for the selected template
        if (index > -1)
          isRegisteredBusinessAddress =
            vm.docTemplateList[index].registeredBusinessAddress;
        vm.$apollo
          .query({
            query: RETRIEVE_DOCUMENT_INPUTS,
            variables: {
              documentTemplateId: vm.selectedDocTemplateId,
              employeeId: vm.selectedEmployee,
              candidateId: vm.selectedCandidate,
              registeredBusinessAddress: isRegisteredBusinessAddress,
            },
            client: "apolloClientO",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            const { documentDetails, businessAddress } =
              res.data.retrieveDocumentInputs;
            if (documentDetails) {
              vm.documentInputFields = JSON.parse(documentDetails);
            }
            let businessAddressHtmlContent = "";
            // form business address html content
            if (businessAddress && Object.keys(businessAddress).length > 0) {
              const { street1, street2, pincode, state, city, country } =
                businessAddress;
              let addressHtmlContent = "";
              if (city) {
                addressHtmlContent += `<strong>${city}, </strong>`;
              }
              if (state) {
                addressHtmlContent += `<strong>${state}, </strong>`;
              }
              if (country) {
                addressHtmlContent += `<strong>${country}, </strong>`;
              }
              if (pincode) {
                addressHtmlContent += `<strong>- ${pincode}&nbsp;</strong>`;
              }
              if (addressHtmlContent || street1 || street2) {
                businessAddressHtmlContent = `
                  <p style="text-align:right;">
                    <strong>${street1}, ${street2}&nbsp;</strong>
                    <br>&nbsp;
                    ${addressHtmlContent}
                  </p>
                `;
              }
              vm.businessAddressContent = businessAddressHtmlContent;
            }
            // after getting response we need to assign input values in the template content
            vm.generateDocument();
            vm.isPageLoading = false;
          })
          .catch((err) => {
            vm.isPageLoading = false;
            vm.handleDocTempInputFetchError(err);
          });
      } catch {
        vm.isPageLoading = false;
        vm.handleDocTempInputFetchError();
      }
    },
    // add/update document for the selected employee
    generateUpdateEmpDocument(documentStatus) {
      let vm = this;
      vm.isPageLoading = true;
      try {
        let generatedDocId = 0,
          selectedEmployeeId = 0,
          selectedCandidateId = 0,
          generatedDocumentName = "",
          generatedTemplateName = "",
          isRegisteredBusinessAddress = 0,
          signingAuthorities = "";
        // document content
        let docTemplateContent = this.$refs.documentEditor
          ? this.$refs.documentEditor.editorData
          : this.employeeDocumentHtmlContent;
        // add table styles when the table tag has no styles to avoid table preview issue
        docTemplateContent = docTemplateContent.replace(
          /<table>/g,
          "<table border='1' cellpadding='1' cellspacing='1'>"
        );
        if (vm.isEdit) {
          const {
            documentName,
            templateName,
            employeeId,
            generatedDocumentId,
            registeredBusinessAddress,
            authorizedSignatories,
            candidateId,
          } = vm.docGeneratorDetails;
          generatedDocId = generatedDocumentId;
          selectedEmployeeId = parseInt(employeeId);
          selectedCandidateId = parseInt(candidateId);
          generatedDocumentName = documentName;
          generatedTemplateName = templateName;
          isRegisteredBusinessAddress = registeredBusinessAddress;
          signingAuthorities = authorizedSignatories;
        } else {
          selectedEmployeeId = vm.selectedEmployee;
          selectedCandidateId = vm.selectedCandidate;
          generatedDocumentName = vm.documentName;
          // find selected document template index
          let index = vm.docTemplateList.findIndex(
            (obj) => obj.documentTemplateId === vm.selectedDocTemplateId
          );
          // based on the index we can find document name
          if (index > -1) {
            generatedTemplateName = vm.docTemplateList[index].title;
            isRegisteredBusinessAddress =
              vm.docTemplateList[index].registeredBusinessAddress;
          }
          // check availability before form custom component input
          if (vm.keysOfCustomCompList.length > 0) {
            for (let componentKey in vm.customComponentList) {
              if (componentKey === "Signing Authority") {
                let signatoryDetails = [],
                  customComponentCount = 0; // count to be incremented by 1 on each below for loop iteration
                // loop through signature authorities([Employee, Manager Or Admin])
                for (let componentType of vm.customComponentList[
                  componentKey
                ]) {
                  customComponentCount++;
                  // based on the authority we need to decide employee list
                  let empList =
                    componentType === "Manager or Admin"
                      ? vm.signatoryAdminManagerList
                      : componentType === "Candidate"
                      ? vm.candidateList
                      : vm.signatoryEmployeesList;
                  let empIdKey =
                    componentType === "Candidate"
                      ? "candidateId"
                      : "employeeId";
                  let empNameKey =
                    componentType === "Candidate"
                      ? "candidateName"
                      : "employeeName";
                  // find selected authority index based on employee id
                  let empIndex = empList.findIndex(
                      (obj) =>
                        obj[empIdKey] ===
                        vm.customComponentInputVariables[componentType]
                    ),
                    employeeName = "";
                  // based on the index we can find authority name
                  if (empIndex > -1) {
                    employeeName = empList[empIndex][empNameKey];
                  }
                  // signature default object
                  let signObj = {
                    signatureLevelId: customComponentCount, // Ex: 1
                    signatureKey: componentType, // Ex: Employee
                    signatureEmployeeId:
                      vm.customComponentInputVariables[componentType], // Ex: 1
                    signatureEmployeeName: employeeName, // Ex: John
                    status: "Not Signed", // Default status
                  };
                  // push the signature object in to array
                  signatoryDetails.push(signObj);
                }
                signingAuthorities = JSON.stringify(signatoryDetails);
              }
            }
          }
        }
        let additionalDetails = {};
        let jsonAdditionalDetails;
        if (vm.candidateBasicPay) {
          additionalDetails.basicPay = vm.candidateBasicPay;
        }
        if (vm.candidateCtc) {
          additionalDetails.ctc = vm.candidateCtc;
        }
        if (vm.signingBonus) {
          additionalDetails.signInBonus = vm.signingBonus;
        }
        if (!Object.keys(additionalDetails).length) {
          jsonAdditionalDetails = null;
        } else {
          jsonAdditionalDetails = JSON.stringify(additionalDetails);
        }

        vm.$apollo
          .mutate({
            mutation: GENERATE_UPDATE_DOCUMENT_FOR_EMPLOYEE,
            variables: {
              generatedDocumentId: generatedDocId, // as of now for add
              employeeId: selectedEmployeeId,
              candidateId: selectedCandidateId,
              documentName: generatedDocumentName,
              templateName: generatedTemplateName,
              additionalDetails: jsonAdditionalDetails,
              registeredBusinessAddress: isRegisteredBusinessAddress
                ? "Main Branch"
                : "Employee Location",
              documentContent: docTemplateContent,
              customComponents: signingAuthorities
                ? [
                    {
                      componentName: "Signing Authority",
                      componentInputs: signingAuthorities,
                    },
                  ]
                : [],
              status: documentStatus,
              signingAuthorities: signingAuthorities,
            },
            client: "apolloClientP",
          })
          .then(() => {
            let snackbarData = {
              isOpen: true,
              message:
                documentStatus === "Draft"
                  ? "Draft of your document has been saved successfully"
                  : documentStatus === "In Review"
                  ? "Review request has been sent successfully"
                  : "Document generated successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
            vm.isPageLoading = false;
            vm.$emit("add-edit-success");
          })
          .catch((addUpdateError) => {
            vm.handleAddEditError(addUpdateError);
          });
      } catch {
        vm.handleAddEditError();
      }
    },
    // handle error for generating/updating document template details
    handleAddEditError(err = "") {
      this.isPageLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodesWithValidation(err);
        if (errorCode) {
          switch (errorCode[0]) {
            case "_DB0000": // technical errors
              snackbarData.message =
                "It’s us! There seem to be some technical difficulties. Please try after some time.";
              break;
            case "_DB0101": // add access denied
              snackbarData.message =
                "Sorry, you don't have access rights to generate the document for employee(s).";
              break;
            case "_DB0102": // edit access denied
              snackbarData.message =
                "Sorry, you don't have access rights to update the employee's generated document.";
              break;
            case "_EC0006": // Record(s) are already deleted in the same or some other user session.
              snackbarData.message = `Unable to update the record as it was deleted already in the same or some other user session.`;
              this.$emit("add-edit-success");
              break;
            case "CDG0119": // Unable to send an email as the to employee/candidate details does not exist.
              snackbarData.message =
                "Document added successfully but unable to send an email as the employee/candidate record does not exist.";
              this.$emit("add-edit-success");
              break;
            case "CDG0120": // Signature link email is not sent to the signatories.(no email/error)
              snackbarData.message =
                "Document added successfully but unable to send an email as the employee/candidate email does not exist.";
              this.$emit("add-edit-success");
              break;
            // if any input validation error occurs, BAD_USER_INPUT was returned as error code from backend
            case "IVE0000": // invalid input req
            case "BAD_USER_INPUT":
              var validationErrors = errorCode[1];
              // add all the backend validation error messages as single sentence to present it to the users
              var validationMessages = "";
              if (validationErrors) {
                for (var errCode in validationErrors) {
                  if (
                    errCode === "IVE0229" || // document name validation error
                    errCode === "IVE0223" // template content validation error
                  ) {
                    validationMessages =
                      validationMessages + " " + validationErrors[errCode];
                  }
                }
              }
              if (validationMessages) {
                snackbarData.message = validationMessages;
              } else {
                // IVE0224 - Registered business address should be either main branch or location.
                // IVE0083 - Invalid employeeId
                // IVE0228 - Invalid candidate id.
                // IVE0222 - Invalid template name.
                snackbarData.message = `Something went wrong while ${
                  this.isEdit ? "updating" : "generating"
                } the employee's document. Please try after some time.`;
              }
              break;
            case "_UH0001": // unhandled error
            case "_DB0001": // Error while retrieving the employee access rights
            case "_DB0002": // Error while checking the employee access rights
            case "_DB0104": // While check access rights form not found
            case "_EC0007": // Invalid input field(s).
            case "CDG0007": // Error while adding or updating the employee document generator details.
            case "CDG0106": // Error while processing the request to add or update the employee document generator details.
            default:
              snackbarData.message = `Something went wrong while ${
                this.isEdit ? "updating" : "generating"
              } the employee's document. If you continue to see this issue, please contact the platform administrator.`;
              break;
          }
        } else {
          snackbarData.message = `Something went wrong while ${
            this.isEdit ? "updating" : "generating"
          } the employee's document. Please try after some time.`;
        }
      } else if (err && err.networkError) {
        snackbarData.message = handleNetworkErrors(err);
      } else {
        snackbarData.message = `Something went wrong while ${
          this.isEdit ? "updating" : "generating"
        } the employee's document. Please try after some time.`;
      }
      this.showAlert(snackbarData);
    },
    // handle backend errors for candidate list
    handleCandidateError(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      this.isPageLoading = false;
      if (err && err.graphQLErrors[0]) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000":
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_UH0001": // Something went wrong! Please contact the system admin.
          case "CDG0118": // Error while processing the request to list the candidate details.
          case "CDG0015": // Error while retrieving the candidate details.
          default:
            snackbarData.message =
              "Something went wrong while fetching candidates list. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData.message = handleNetworkErrors(err);
      } else {
        snackbarData.message =
          "Something went wrong while fetching candidates list. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    // handle template list backend error
    handleTempListError(err = "") {
      if (!this.tempFieldErrorObj["isError"]) {
        this.tempFieldErrorObj["errorType"] = "error";
        if (err && err.graphQLErrors[0]) {
          // error capture
          var errorCode = getErrorCodes(err);
          switch (errorCode) {
            case "_DB0000":
              this.tempFieldErrorObj["errorMessage"] =
                "It’s us! There seems to be some technical difficulties. Please try after some time.";
              break;
            case "CDG0121": // Template does not exist.
              this.tempFieldErrorObj.errorType = "empty";
              this.tempFieldErrorObj["errorMessage"] =
                "No template exists. Please configure the template prior to generating the document.";
              break;
            case "_UH0001": // Something went wrong! Please contact the system admin.
            case "CDG0008": // Error while listing the document templates.
            case "CDG0107": // Error while processing the request to document templates.
            default:
              this.tempFieldErrorObj["errorMessage"] =
                "Something went wrong while fetching employees list. If you continue to see this issue, please contact the platform administrator.";
              break;
          }
        } else if (err && err.networkError) {
          this.tempFieldErrorObj["errorMessage"] = handleNetworkErrors(err);
        } else {
          this.tempFieldErrorObj["errorMessage"] =
            "Something went wrong while fetching employees list. Please try after some time.";
        }
        this.tempFieldErrorObj["isError"] = true;
      }
      this.isFetchingTemplateList = false;
    },
    // handle backend errors for employees list in generator
    handleEmployeesListError(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      this.isPageLoading = false;
      if (err && err.graphQLErrors[0]) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000":
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_UH0001": // Something went wrong! Please contact the system admin.
          case "_EC0007": // Invalid input field(s).
          case "CDG0011": // Error while listing the employee details.
          case "CDG0110": // Error while processing the request to retrieve the employee details.
          default:
            snackbarData.message =
              "Something went wrong while fetching employees list. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData.message = handleNetworkErrors(err);
      } else {
        snackbarData.message =
          "Something went wrong while fetching employees list. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    // handle backend errors for custom components list fetch
    handleCustomCompListFetchError(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      this.isPageLoading = false;
      if (err && err.graphQLErrors[0]) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000":
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_EC0001": // No record found
            snackbarData.message = "Unable to retrieve custom components list.";
            break;
          case "_UH0001": // Something went wrong! Please contact the system admin.
          case "_EC0007": // Invalid input field(s).
          case "CDG0009": // Error while listing the template custom components.
          case "CDG0108": // Error while processing the request to list the document template custom components.
          default:
            snackbarData.message =
              "Something went wrong while fetching custom components list. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData.message = handleNetworkErrors(err);
      } else {
        snackbarData.message =
          "Something went wrong while fetching custom components list. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    // handle backend errors for retrieving document template input fields
    handleDocTempInputFetchError(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      this.isPageLoading = false;
      if (err && err.graphQLErrors[0]) {
        // error capture
        var errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000":
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "CDG0121": // Template does not exist.
            snackbarData.message =
              "Template does not exist as it was deleted same or some other user session.";
            break;
          case "_UH0001": // Something went wrong! Please contact the system admin.
          case "_EC0007": // Invalid input field(s).
          case "CDG0011": // Error while listing the employee details.
          case "CDG0125": // Error while processing the request to retrieve the document input details.
          default:
            snackbarData.message =
              "Something went wrong while fetching document template input values. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData.message = handleNetworkErrors(err);
      } else {
        snackbarData.message =
          "Something went wrong while fetching document template input values. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
