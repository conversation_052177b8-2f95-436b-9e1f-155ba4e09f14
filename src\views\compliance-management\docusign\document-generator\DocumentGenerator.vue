<template>
  <div>
    <v-row v-if="!showAddForm && !showEditForm && !showViewForm">
      <v-col cols="12" class="pa-0 mt-2">
        <div v-if="isListLoading" class="mt-6">
          <v-skeleton-loader class="mt-2 mb-2" type="table-thead" />
          <v-skeleton-loader
            v-for="i in 5"
            :key="i"
            class="mx-auto pt-4 mt-4"
            type="list-item-avatar"
          />
        </div>
        <AppFetchErrorScreen
          v-else-if="listFetchError"
          button-text="Retry"
          :content="errorContent"
          icon-name="refresh"
          image-name="compliance-management/document-generator/document-generator-empty"
          @button-click="retryFetching()"
        />
        <AppFetchErrorScreen
          v-else-if="documentTemplates.length === 0"
          :button-text="formAccess && formAccess.add ? 'Add New' : ''"
          image-name="compliance-management/document-generator/document-generator-empty"
          icon-name="add"
          @button-click="openAddForm()"
        />
        <ListDocumentGenerator
          v-else
          :form-access="formAccess"
          :table-items="documentTemplates"
          @open-add-form="openAddForm()"
          @action-on-doc-generator="onActionInList($event)"
          @delete-success="retryFetching()"
          @refetch-list="retryFetching()"
        />
      </v-col>
    </v-row>
    <v-row v-else justify="center">
      <v-col cols="12">
        <AddEditDocumentGenerator
          v-if="showAddForm || showEditForm"
          :doc-generator-details="selectedDocGenRecord"
          :is-edit="showEditForm"
          @close-add-edit-form="closeAddEditForm()"
          @add-edit-success="retryFetching()"
          @close-form-with-warning="closeFormWithWarning()"
          @change-to-config-tab="$emit('change-to-config-tab')"
        />
        <ViewDocumentGenerator
          v-else
          :form-access="formAccess"
          :doc-generator-details="selectedDocGenRecord"
          :doc-generator-content="docGeneratorContent"
          @close-view-form="closeViewForm()"
          @open-edit-form="openEditForm()"
        />
      </v-col>
    </v-row>
    <v-dialog
      v-model="openPrintModal"
      width="700"
      @click:outside="openPrintModal = false"
    >
      <v-card :min-width="isMobileView ? '' : 400" class="pb-4">
        <div class="d-flex pa-2 my-2 primary--text font-weight-medium">
          <div class="d-flex body-1">Print Document</div>
          <v-spacer />
          <v-icon color="secondary" @click="openPrintModal = false">
            close
          </v-icon>
        </div>
        <hr />
        <VuePerfectScrollbar class="scroll-area" :settings="scrollbarSettings">
          <div style="max-height: 450px" class="pa-5">
            <div id="printableContent" class="ck-content" />
          </div>
        </VuePerfectScrollbar>
        <div class="d-flex align-center justify-center my-3">
          <v-btn elevation="2" @click="openPrintModal = false"> Cancel </v-btn>
          <v-btn color="primary" class="ml-2" @click="printContent()">
            Print
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
    <AppLoading v-if="isLoading" />
  </div>
</template>

<script>
import ListDocumentGenerator from "./ListDocumentGenerator";
const AddEditDocumentGenerator = () => import("./AddEditDocumentGenerator");
const ViewDocumentGenerator = () => import("./ViewDocumentGenerator");
import { LIST_EMP_GENERATED_DOCUMENTS } from "@/graphql/compliance-management/docuSignQueries";
import { getErrorCodes, handleNetworkErrors } from "@/helper";
import { GET_PRESIGNED_URL } from "@/graphql/commonQueries";

export default {
  name: "DocumentGenerator",

  components: {
    ListDocumentGenerator,
    AddEditDocumentGenerator,
    ViewDocumentGenerator,
  },

  props: {
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
  },

  data: () => ({
    showAddForm: false,
    showViewForm: false,
    showEditForm: false,
    listFetchError: false,
    documentTemplates: [],
    selectedDocGenRecord: {},
    isListLoading: true,
    isLoading: false,
    docGeneratorContent: "",
    // pdf/print
    openPrintModal: false,
  }),

  apollo: {
    // list work schedules
    listEmployeeGeneratedDocuments: {
      query: LIST_EMP_GENERATED_DOCUMENTS,
      client: "apolloClientO",
      fetchPolicy: "no-cache",
      result({ data }) {
        if (data) {
          let { documentDetails, errorCode } =
            data.listEmployeeGeneratedDocuments;
          if (documentDetails && !errorCode) {
            this.documentTemplates = documentDetails;
            this.isListLoading = false;
          } else {
            this.handleListError();
          }
        } else {
          this.handleListError();
        }
      },
      // Error handling
      error(listError) {
        this.handleListError(listError);
      },
    },
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    scrollbarSettings() {
      return this.$store.state.scrollbarSettings;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },

  methods: {
    // refetch list
    retryFetching() {
      this.isListLoading = true;
      this.closeAddEditForm();
      this.listFetchError = false;
      this.$apollo.queries.listEmployeeGeneratedDocuments.refetch();
    },

    // open add form, and close other forms
    openAddForm() {
      this.showEditForm = false;
      this.showViewForm = false;
      this.showAddForm = true;
    },

    // close view form
    closeViewForm() {
      this.showViewForm = false;
    },

    // open edit form when edit button was triggered in view form
    openEditForm() {
      this.showViewForm = false;
      this.showAddForm = false;
      this.showEditForm = true;
    },

    // open view form when any of the employee document was clicked in the list
    getFileContentFromS3(uploadUrl, fileAction, action) {
      let vm = this;
      vm.$apollo
        .mutate({
          mutation: GET_PRESIGNED_URL,
          variables: {
            fileName: uploadUrl,
            action: fileAction,
            type: "docusign",
            destinationBucket: "",
            destinationFileKey: "",
          },
          client: "apolloClientA",
        })
        .then((response) => {
          const { s3DocumentDetails, presignedUrl, errorCode } =
            response.data.getPresignedUrl;
          if (!errorCode) {
            if (presignedUrl || s3DocumentDetails) {
              vm.docGeneratorContent = s3DocumentDetails
                ? JSON.parse(s3DocumentDetails)
                : vm.docGeneratorContent;
              if (action === "download") {
                window.open(presignedUrl);
              } else if (action === "print") {
                vm.openPrintModal = true;
                setTimeout(() => {
                  let content = vm.docGeneratorContent;
                  let element = document.getElementById("printableContent");
                  element.innerHTML = content;
                }, 1000);
              } else {
                vm.openViewForm();
              }
            }
          } else {
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message:
                "Something went wrong while retrieving the file contents.",
            };
            vm.showAlert(snackbarData);
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: "Something went wrong while retrieving the file contents.",
          };
          vm.showAlert(snackbarData);
        });
    },

    onActionInList(event) {
      let selectedRecord = event[0],
        action = event[1];
      this.selectedDocGenRecord = selectedRecord;
      this.docGeneratorContent = selectedRecord.documentContent;
      this.isLoading = true;
      let uploadUrl;
      if (action === "download") {
        uploadUrl =
          this.domainName +
          "/" +
          this.orgCode +
          "/" +
          "Document Generator" +
          "/" +
          selectedRecord.documentLink;
        this.getFileContentFromS3(uploadUrl, "view", action);
      } else {
        if (
          (selectedRecord.status === "Partially Signed" ||
            selectedRecord.status === "Completed") &&
          selectedRecord.documentName &&
          selectedRecord.authorizedSignatories
        ) {
          uploadUrl =
            this.domainName +
            "/" +
            this.orgCode +
            "/" +
            "Document Generator" +
            "/" +
            selectedRecord.generatedDocumentId +
            "/" +
            selectedRecord.generatedDocumentId +
            "?" +
            selectedRecord.documentName +
            ".txt";
          this.getFileContentFromS3(uploadUrl, "getdata", action);
        } else {
          this.isLoading = false;
          if (action === "print") {
            this.openPrintModal = true;
            setTimeout(() => {
              let content = this.docGeneratorContent;
              let element = document.getElementById("printableContent");
              element.innerHTML = content;
            }, 1000);
          } else {
            this.openViewForm();
          }
        }
      }
    },

    openViewForm() {
      this.showAddForm = false;
      this.showEditForm = false;
      this.showViewForm = true;
    },

    // while clicking "Print" button, we need to print the element using "printd" plugin
    printContent() {
      // const printInstance = new Printd();
      printInstance.print(document.getElementById("printableContent"));
    },

    // close add or edit form
    closeAddEditForm() {
      this.showAddForm = false;
      this.showEditForm = false;
    },

    closeFormWithWarning() {
      this.closeAddEditForm();
      this.$emit("view-not-supported");
    },

    // handle list employees generated documents error from BE
    handleListError(err = "") {
      this.isListLoading = false;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          // technical errors
          case "_DB0000":
            this.errorContent =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_DB0100": // access denied
            this.$emit("access-denied");
            break;
          // functional errors
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0002": // Error while checking the employee access rights
          case "_DB0104": // While check access rights form not found
          case "_UH0001": // unhandled error
          case "CDG0005": // Error while listing the generated document details.
          case "CDG0104": // Error while processing the request to list the generated document details.
          default:
            this.errorContent =
              "Something went wrong while fetching the employees generated document list. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        this.errorContent = handleNetworkErrors(err);
      } else {
        this.errorContent =
          "Something went wrong while fetching the employees generated document list. Please try after some time.";
      }
      this.listFetchError = true;
    },

    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
