<template>
  <div>
    <v-dialog
      v-model="openSendEmailModal"
      width="700"
      @click:outside="closeModal()"
    >
      <v-card :min-width="isMobileView ? '' : 400" class="pb-4">
        <div
          class="d-flex align-center pl-sm-6 pt-4 pb-3 primary--text font-weight-medium"
        >
          <div class="d-flex body-1">Send Document</div>
          <v-spacer />
          <v-icon color="secondary" class="pr-4" @click="closeModal()">
            close
          </v-icon>
        </div>
        <hr />
        <v-alert
          v-model="showWarningAlert"
          text
          dense
          type="warning"
          class="mb-0"
          dismissible
        >
          {{ alertMessage }}
        </v-alert>
        <VuePerfectScrollbar class="scroll-area" :settings="scrollbarSettings">
          <div style="max-height: 450px" class="pa-5">
            <v-form
              ref="emailTemplateForm"
              class="d-flex flex-column justify-center mt-2"
            >
              <ValidationObserver ref="emailSendObserver">
                <v-row class="mt-n8">
                  <v-col cols="12" class="pa-0 mb-sm-n4">
                    <v-row align="center">
                      <v-col cols="12" sm="2">
                        <v-label>To</v-label>
                      </v-col>
                      <v-col cols="12" :sm="showCC ? 10 : 8">
                        <ValidationProvider
                          ref="toEmailProvider"
                          v-slot="{ errors }"
                          name="To email"
                          rules="required"
                        >
                          <v-combobox
                            v-model="toEmailAddress"
                            :items="[]"
                            chips
                            clearable
                            multiple
                            :error-messages="errors"
                          >
                            <template
                              #selection="{ attrs, item, select, selected }"
                            >
                              <div style="overflow-hidden">
                                <v-chip
                                  v-bind="attrs"
                                  :input-value="selected"
                                  close
                                  color="#f7f7f7"
                                  @click="select"
                                  @click:close="removeToEmailAddress(item)"
                                >
                                  <span
                                    :style="
                                      isMobileView
                                        ? 'max-width: 150px'
                                        : 'max-width: 300px'
                                    "
                                    class="text-truncate"
                                  >
                                    <strong>{{ item }}</strong>
                                  </span>
                                </v-chip>
                              </div>
                            </template>
                          </v-combobox>
                        </ValidationProvider>
                      </v-col>
                      <v-col v-if="!showCC" cols="12" sm="2" class="pa-0">
                        <v-btn
                          text
                          small
                          color="secondary"
                          @click="showCC = true"
                        >
                          <v-icon size="17"> add </v-icon>
                          Add CC
                        </v-btn>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col v-if="showCC" cols="12" class="pa-0">
                    <v-row align="center">
                      <v-col cols="12" sm="2">
                        <v-label>CC</v-label>
                      </v-col>
                      <v-col cols="12" sm="10">
                        <ValidationProvider
                          ref="ccEmailProvider"
                          v-slot="{ errors }"
                          name="CC email"
                        >
                          <v-combobox
                            v-model="ccEmailAddress"
                            :items="[]"
                            chips
                            clearable
                            multiple
                            :error-messages="errors"
                          >
                            <template
                              #selection="{ attrs, item, select, selected }"
                            >
                              <div style="overflow-hidden">
                                <v-chip
                                  v-bind="attrs"
                                  :input-value="selected"
                                  close
                                  color="#f7f7f7"
                                  @click="select"
                                  @click:close="removeCCEmailAddress(item)"
                                >
                                  <span
                                    :style="
                                      isMobileView
                                        ? 'max-width: 150px'
                                        : 'max-width: 300px'
                                    "
                                    class="text-truncate"
                                  >
                                    <strong>{{ item }}</strong>
                                  </span>
                                </v-chip>
                              </div>
                            </template>
                          </v-combobox>
                        </ValidationProvider>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" class="pa-0 mb-sm-n4">
                    <v-row align="center">
                      <v-col cols="12" sm="2">
                        <v-label>Subject</v-label>
                      </v-col>
                      <v-col cols="12" sm="10">
                        <ValidationProvider
                          ref="emailSubjectProvider"
                          v-slot="{ errors }"
                          :name="$t('subject')"
                          rules="required|min:5|max:150"
                        >
                          <v-text-field
                            id="email-subject"
                            v-model="emailSubject"
                            filled
                            placeholder="Subject"
                            counter
                            maxlength="150"
                            :error-messages="errors"
                          />
                        </ValidationProvider>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" class="pa-0 mb-sm-n4">
                    <v-row align="center">
                      <v-col cols="12" sm="2">
                        <v-label>Body</v-label>
                      </v-col>
                      <v-col cols="12" sm="10">
                        <ValidationProvider
                          ref="emailContentProvider"
                          v-slot="{ errors }"
                          name="Content"
                          rules="required|min:5|max:500"
                        >
                          <v-textarea
                            id="email-template-content"
                            v-model="emailBody"
                            filled
                            counter
                            required
                            placeholder="Template"
                            maxlength="500"
                            :error-messages="errors"
                            rows="4"
                          />
                        </ValidationProvider>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </ValidationObserver>
            </v-form>
          </div>
        </VuePerfectScrollbar>
        <div class="d-flex align-center justify-center my-3">
          <v-btn color="secondary" outlined @click="closeModal()">
            Cancel
          </v-btn>
          <v-btn color="secondary" class="ml-2" @click="sendDocumentInMail()">
            Send
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
    <AppLoading v-if="loadingScreen" />
  </div>
</template>

<script>
import { getErrorCodesWithValidation, handleNetworkErrors } from "@/helper";
import { SEND_DOCUMENT_IN_EMAIL } from "@/graphql/compliance-management/docuSignQueries";

export default {
  name: "DocGeneratorEmailNotification",

  props: {
    notifyDocumentDetails: {
      type: Object,
      required: true,
    },
    openEmailModal: {
      type: Boolean,
      required: true,
    },
  },

  data: () => ({
    openSendEmailModal: false,
    emailSubject: "",
    emailBody: "",
    loadingScreen: false,
    toEmailAddress: [],
    ccEmailAddress: [],
    alertMessage: "",
    showWarningAlert: false,
    showCC: false,
  }),

  computed: {
    scrollbarSettings() {
      return this.$store.state.scrollbarSettings;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  watch: {
    showWarningAlert(val) {
      if (val) {
        setTimeout(() => {
          this.closeAlert();
        }, 3000);
      }
    },
  },

  mounted() {
    this.openSendEmailModal = this.openEmailModal;
    const { employeeFullName } = this.$store.state.userDetails;
    const {
      candidatePersonalEmail,
      candidateJobEmail,
      personalEmail,
      documentName,
    } = this.notifyDocumentDetails;
    if (this.notifyDocumentDetails.employeeEmail) {
      this.toEmailAddress.push(this.notifyDocumentDetails.employeeEmail);
    }
    if (personalEmail) {
      this.toEmailAddress.push(personalEmail);
    }
    if (candidateJobEmail) {
      this.toEmailAddress.push(candidateJobEmail);
    }
    if (candidatePersonalEmail) {
      this.toEmailAddress.push(candidatePersonalEmail);
    }
    this.emailSubject = employeeFullName + " sent you " + documentName;
    this.emailBody = "Please find attached " + documentName;
  },

  methods: {
    closeAlert() {
      this.showWarningAlert = false;
      this.alertMessage = "";
    },

    closeModal() {
      this.openSendEmailModal = false;
      this.$emit("close-notify-modal");
    },

    removeToEmailAddress(item) {
      this.toEmailAddress.splice(this.toEmailAddress.indexOf(item), 1);
      this.toEmailAddress = [...this.toEmailAddress];
    },

    removeCCEmailAddress(item) {
      this.ccEmailAddress.splice(this.ccEmailAddress.indexOf(item), 1);
      this.ccEmailAddress = [...this.ccEmailAddress];
    },

    sendDocumentInMail() {
      this.$refs.emailSendObserver.validate().then((validationResponse) => {
        if (validationResponse) {
          let vm = this;
          vm.loadingScreen = true;
          try {
            const { generatedDocumentId, documentLink } =
              vm.notifyDocumentDetails;
            vm.$apollo
              .query({
                query: SEND_DOCUMENT_IN_EMAIL,
                variables: {
                  documentId: generatedDocumentId,
                  s3DocumentName: documentLink,
                  emailSubject: vm.emailSubject,
                  emailBody: vm.emailBody,
                  toEmailIds: vm.toEmailAddress,
                  ccEmailIds: vm.ccEmailAddress,
                },
                client: "apolloClientO",
              })
              .then(() => {
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Document has been mailed successfully",
                };
                vm.showAlert(snackbarData);
                vm.loadingScreen = false;
                vm.closeModal();
              })
              .catch((mailSendErr) => {
                vm.handleDocumentSendError(mailSendErr);
              });
          } catch {
            vm.handleDocumentSendError();
          }
        }
      });
    },

    // handle document email send error
    handleDocumentSendError(err = "") {
      this.loadingScreen = false;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodesWithValidation(err);
        if (errorCode) {
          switch (errorCode[0]) {
            case "_DB0000": // technical issues.
              this.alertMessage =
                "It’s us! There seems to be some technical difficulties. Please try after some time.";
              break;
            case "CDG0116": // Empty file retrieved from s3.
              this.alertMessage =
                "Unable to send the document as it was deleted already or the document does not exist.";
              break;
            case "PBP0105": // Error in sending email
              this.alertMessage =
                "There seems to be some technical difficulties while sending an email. Please try after some time.";
              break;
            // if any input validation error occurs, BAD_USER_INPUT was returned as error code from backend
            case "BAD_USER_INPUT":
            case "IVE0000":
              var validationErrors = errorCode[1];
              // add all the backend validation error messages as single sentence to present it to the users
              var validationMessages = "";
              if (validationErrors) {
                for (var errCode in validationErrors) {
                  //  Please provide a valid share value.
                  if (
                    errCode === "IVE0226" || // Please provide a valid to email address.
                    errCode === "IVE0227" || // Only 50 email addresses are allowed.
                    errCode === "IVE0117" || // Please enter at least 5 characters and not more than 500 characters.
                    errCode === "IVE0116" // Please enter at least 5 characters and not more than 150 characters.
                  ) {
                    validationMessages = validationErrors[errCode];
                  }
                }
              }
              if (validationMessages) {
                this.alertMessage = validationMessages;
              } else {
                // IVE0225 - Please provide a valid from email address.
                this.alertMessage =
                  "Something went wrong while updating currency and share value. If you continue to see this issue, please contact the platform administrator.";
              }
              break;
            case "PBP0110": // Error in retrieve file from s3
            case "CDG0014": // Error while sending the document in email
            case "_EC0007": // Invalid input field(s).
            default:
              this.alertMessage =
                "Something went wrong while sending document to employees. If you continue to see this issue, please contact the platform administrator.";
              break;
          }
        } else {
          this.alertMessage =
            "Something went wrong while sending document to employees. Please try after some time.";
        }
      } else if (err && err.networkError) {
        this.alertMessage = handleNetworkErrors(err);
      } else {
        this.alertMessage =
          "Something went wrong while sending document to employees. Please try after some time.";
      }
      this.showWarningAlert = true;
    },

    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style>
.v-autocomplete.v-select.v-input--is-focused input {
  min-width: 200px !important;
}

@media screen and (max-width: 600px) {
  .v-autocomplete.v-select.v-input--is-focused input {
    min-width: 150px !important;
  }
}
</style>
