<template>
  <div class="pt-16 px-8 px-sm-4">
    <!-- Action buttons -->
    <div class="d-flex justify-end align-center mb-4 mr-15">
      <v-btn
        v-if="!formAccess || formAccess.add !== false"
        color="primary"
        class="mr-2"
        @click="$emit('open-add-form')"
      >
        <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
        Add New
      </v-btn>
      <v-btn color="primary" variant="outlined" @click="$emit('refetch-list')">
        <v-icon>fas fa-redo-alt</v-icon>
      </v-btn>
    </div>
    <div class="bg-white rounded-lg elevation-1">
      <v-data-table
        id="doc_template_table"
        v-model="selectedRecords"
        :headers="tableHeaders"
        :items="tableItems"
        item-key="generatedDocumentId"
        :items-per-page="50"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
        ]"
        :page="page"
        :search="searchValue"
        :show-select="false"
        fixed-header
        :height="$store.getters.getTableHeight(290)"
        :sort-by="[{ key: 'employeeName', order: 'asc' }]"
        class="rounded-lg"
        @update:page="page = $event"
        @page-count="pageCount = $event"
      >
        <template #no-results>
          <AppFetchErrorScreen
            key="no-results-screen"
            button-text="Clear All"
            icon-name="fas fa-sync"
            main-title="No matching search results found."
            content="Please try again by changing the filters or clear it to get all data."
            image-name="common/no-records"
            @button-click="clearFilter()"
          />
        </template>

        <!-- table item slots -->
        <template #item="{ item }">
          <tr
            :id="'doc_template_list__tr_' + item.generatedDocumentId"
            class="cursor-pointer hover:bg-grey-lighten-5"
            :class="
              isMobileView ? 'v-data-table__mobile-table-row ma-0 mt-2' : ''
            "
            @click="selectDocTemplateRecord(item)"
          >
            <!-- Employee Name -->
            <td class="pa-4">
              <div
                v-if="isMobileView"
                class="text-caption text-grey-darken-1 mb-1"
              >
                Employee Name
              </div>
              <div class="text-body-2 font-weight-medium text-grey-darken-3">
                {{ checkNullValue(item.employeeName) }}
              </div>
              <div
                v-if="!isMobileView && item.userDefinedEmployeeId"
                class="text-caption text-grey-darken-1 mt-1"
              >
                {{ item.userDefinedEmployeeId }}
              </div>
            </td>

            <!-- Candidate Name -->
            <td class="pa-4">
              <div
                v-if="isMobileView"
                class="text-caption text-grey-darken-1 mb-1"
              >
                Candidate Name
              </div>
              <div class="text-body-2 font-weight-medium text-grey-darken-3">
                {{ checkNullValue(item.candidateName) }}
              </div>
              <div
                v-if="!isMobileView && item.userDefinedCandidateId"
                class="text-caption text-grey-darken-1 mt-1"
              >
                {{ item.userDefinedCandidateId }}
              </div>
            </td>

            <!-- Document Name -->
            <td class="pa-4">
              <div
                v-if="isMobileView"
                class="text-caption text-grey-darken-1 mb-1"
              >
                Document Name
              </div>
              <div class="text-body-2 text-primary">
                {{ checkNullValue(item.documentName) }}
              </div>
            </td>

            <!-- Signatories -->
            <td class="pa-4">
              <div
                v-if="isMobileView"
                class="text-caption text-grey-darken-1 mb-1"
              >
                Signatories
              </div>
              <div class="d-flex align-center">
                <div
                  v-if="
                    item.authorizedSignatories &&
                    item.authorizedSignatories.length > 0
                  "
                  class="d-flex align-center"
                >
                  <v-menu :open-on-hover="!isMobileView" location="bottom">
                    <template #activator="{ props }">
                      <div v-bind="props" class="d-flex align-center">
                        <v-avatar
                          v-for="(signatory, index) in JSON.parse(
                            item.authorizedSignatories
                          )"
                          :key="index + 'avatar'"
                          size="32"
                          color="success"
                          class="mr-1 text-caption text-white font-weight-bold"
                        >
                          {{ letterAvatar(signatory.signatureEmployeeName) }}
                        </v-avatar>
                      </div>
                    </template>

                    <v-list>
                      <v-list-item
                        v-for="(signatory, index) in JSON.parse(
                          item.authorizedSignatories
                        )"
                        :key="index + 'menuList'"
                      >
                        <template #prepend>
                          <v-avatar
                            size="30"
                            color="success"
                            class="text-caption text-white font-weight-bold"
                          >
                            {{ letterAvatar(signatory.signatureEmployeeName) }}
                          </v-avatar>
                        </template>

                        <v-list-item-title class="text-truncate">
                          {{ signatory.signatureEmployeeName }}
                        </v-list-item-title>
                        <v-list-item-subtitle
                          :class="
                            signatory.status === 'Signed'
                              ? 'text-success'
                              : 'text-grey-darken-1'
                          "
                        >
                          {{ signatory.status }}
                        </v-list-item-subtitle>
                        <template #append>
                          <v-btn
                            v-if="
                              signatory.status === 'Not Signed' &&
                              signatory.emailId &&
                              !['Declined', 'Rejected', 'Draft'].includes(
                                item.status
                              )
                            "
                            color="primary"
                            size="small"
                            class="mr-1"
                            :disabled="
                              signatory?.signatureKey?.toLowerCase() ===
                                'candidate' &&
                              !checkIfSignedByAll(
                                JSON.parse(item?.authorizedSignatories)
                              )
                            "
                            @click.stop="
                              resendEmailToSignatories(item, signatory)
                            "
                          >
                            {{ signatory?.emailSent ? "Resend" : "Send" }}
                            <v-icon
                              size="13"
                              class="ml-1"
                              icon="fas fa-paper-plane"
                            ></v-icon>
                          </v-btn>
                        </template>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
                <div v-else class="text-grey-darken-1">-</div>
              </div>
            </td>

            <!-- Status -->
            <td class="pa-4">
              <div
                v-if="isMobileView"
                class="text-caption text-grey-darken-1 mb-1"
              >
                Status
              </div>
              <div class="d-flex align-center">
                <!-- Status Icon -->
                <v-icon
                  v-if="item.status === 'Completed'"
                  color="success"
                  size="18"
                  class="mr-2"
                  icon="fas fa-check-circle"
                ></v-icon>
                <v-icon
                  v-else-if="item.status === 'Partially Signed'"
                  color="primary"
                  size="18"
                  class="mr-2"
                  icon="fas fa-check-circle"
                ></v-icon>
                <v-icon
                  v-else-if="item.status === 'In Review'"
                  color="warning"
                  size="18"
                  class="mr-2"
                  icon="fas fa-search"
                ></v-icon>
                <v-icon
                  v-else-if="item.status === 'Draft'"
                  color="purple"
                  size="18"
                  class="mr-2"
                  icon="fas fa-pencil-alt"
                ></v-icon>
                <v-icon
                  v-else-if="
                    item.status === 'Declined' || item.status === 'Rejected'
                  "
                  color="error"
                  size="18"
                  class="mr-2"
                  icon="fas fa-times-circle"
                ></v-icon>
                <v-icon
                  v-else
                  color="grey-darken-1"
                  size="18"
                  class="mr-2"
                  icon="fas fa-question-circle"
                ></v-icon>

                <!-- Status Text -->
                <span
                  class="text-body-2 font-weight-medium"
                  :class="
                    item.status === 'Completed'
                      ? 'text-success'
                      : item.status === 'In Review'
                      ? 'text-warning'
                      : item.status === 'Partially Signed'
                      ? 'text-primary'
                      : item.status === 'Draft'
                      ? 'text-purple'
                      : item.status === 'Declined' || item.status === 'Rejected'
                      ? 'text-error'
                      : 'text-grey-darken-1'
                  "
                >
                  {{ item.status }}
                </span>
              </div>
            </td>
            <!-- Document Author -->
            <td class="pa-4">
              <div
                v-if="isMobileView"
                class="text-caption text-grey-darken-1 mb-1"
              >
                Document Author
              </div>
              <div class="text-body-2 text-grey-darken-3">
                {{ checkNullValue(item.addedBy) }}
              </div>
            </td>
            <!-- Action -->
            <td class="pa-4">
              <div
                v-if="isMobileView"
                class="text-caption text-grey-darken-1 mb-1"
              >
                Action
              </div>
              <div class="d-flex justify-end">
                <v-menu location="bottom end" close-on-content-click>
                  <template #activator="{ props }">
                    <v-btn
                      v-bind="props"
                      size="small"
                      color="grey-darken-1"
                      variant="text"
                      icon
                      @click.stop
                    >
                      <v-icon size="20" icon="fas fa-ellipsis-v"></v-icon>
                    </v-btn>
                  </template>
                  <v-list
                    class="action-dropdown-menu"
                    density="compact"
                    min-width="180"
                  >
                    <v-list-item
                      class="action-menu-item"
                      @click="onClickPrint(item)"
                    >
                      <template #prepend>
                        <v-icon class="action-icon" color="primary" icon="mdi-printer">
                        </v-icon>
                      </template>
                      <v-list-item-title class="action-text">
                        Print
                      </v-list-item-title>
                    </v-list-item>

                    <v-list-item
                      v-if="item.documentLink && item.status === 'Completed'"
                      class="action-menu-item"
                      @click="downloadPDF(item)"
                    >
                      <template #prepend>
                        <v-icon
                          class="action-icon"
                          color="success"
                          icon="mdi-file-pdf-box"
                        ></v-icon>
                      </template>
                      <v-list-item-title class="action-text">
                        Download PDF
                      </v-list-item-title>
                    </v-list-item>

                    <v-list-item
                      v-if="item.status === 'Completed'"
                      class="action-menu-item"
                      @click="sendEmailToEmployee(item)"
                    >
                      <template #prepend>
                        <v-icon
                          class="action-icon"
                          color="info"
                          icon="mdi-email-outline"
                        ></v-icon>
                      </template>
                      <v-list-item-title class="action-text">
                        Email
                      </v-list-item-title>
                    </v-list-item>

                    <v-divider
                      v-if="item.status !== 'Completed' && formAccess.delete"
                    />

                    <v-list-item
                      v-if="item.status !== 'Completed' && formAccess.delete"
                      class="action-menu-item action-menu-item--danger"
                      @click="
                        openDeleteConfirmationModal(item.generatedDocumentId)
                      "
                    >
                      <template #prepend>
                        <v-icon
                          class="action-icon"
                          color="error"
                          icon="mdi-delete"
                        ></v-icon>
                      </template>
                      <v-list-item-title
                        class="action-text action-text--danger"
                      >
                        Delete
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
    <AppWarningModel
      v-if="openDeleteConfirmation"
      :open-delete-confirmation="openDeleteConfirmation"
      image-name="common/delete-bin"
      close-button-text="Cancel"
      accept-button-text="Proceed"
      confirmation-content="Are you sure to delete the selected record?"
      @close-warning-modal="closeDeleteConfirmationModal()"
      @accept-modal="deleteEmpDocument()"
    />
    <AppLoading v-if="loadingScreen" />
    <DocGeneratorEmailNotification
      v-if="openSendEmailModal"
      :open-email-modal="openSendEmailModal"
      :notify-document-details="notifyDocumentDetails"
      @close-notify-modal="openSendEmailModal = false"
    />
  </div>
</template>

<script>
import { checkNullValue, getErrorCodes, handleNetworkErrors } from "@/helper";
import {
  DELETE_EMPLOYEE_GENERATED_DOCUMENT,
  RESEND_EMAIL_TO_SIGNATORIES,
} from "@/graphql/compliance-management/docuSignQueries";
const DocGeneratorEmailNotification = () => import("./EmailNotification");

export default {
  name: "ListDocumentGenerator",

  components: {
    DocGeneratorEmailNotification,
  },

  props: {
    tableItems: {
      type: Array,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
  },

  data: () => ({
    // pagination
    pageCount: 1,
    page: 1,
    itemsPerPage: 50,
    pageNumber: 50,
    openPrintModal: false,
    tableHeaders: [
      {
        title: "Employee Name",
        key: "employeeName",
        sortable: true,
      },
      {
        title: "Candidate Name",
        key: "candidateName",
        sortable: true,
      },
      {
        title: "Document Name",
        key: "documentName",
        sortable: true,
      },
      {
        title: "Signatories",
        key: "authorizedSignatories",
        sortable: false,
      },
      {
        title: "Status",
        key: "status",
        sortable: true,
      },
      {
        title: "Document Author",
        key: "documentAuthor",
        sortable: true,
      },
      {
        title: "Action",
        key: "action",
        align: "end",
        sortable: false,
      },
    ],
    selectedRecords: [],
    selectedRecord: {},
    openDeleteConfirmation: false,
    errorContent: "",
    deleteEmpDocId: null,
    loadingScreen: false,
    openSendEmailModal: false,
    notifyDocumentDetails: "",
  }),

  computed: {
    // search in shares list
    searchValue() {
      return this.$store.state.empSearchValue;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  watch: {
    // when page number is changed
    pageNumber(val) {
      if (val === "All") {
        this.pageCount = 1;
        this.itemsPerPage = this.tableItems.length;
        this.page = 1;
      } else {
        let pageCount = this.tableItems.length / this.pageNumber;
        pageCount = pageCount <= 1 ? 1 : pageCount;
        this.pageCount = Math.round(pageCount);
        this.itemsPerPage = this.pageNumber;
        this.page = 1;
      }
    },
  },

  methods: {
    checkNullValue,
    // pageNumber is emitted from its child component to update the count in parent
    fnChangePaginationCount(val) {
      this.pageNumber = val;
    },

    // Method to return first letter in capital of each word (replaces deprecated filter)
    letterAvatar(value, isSingleLetter) {
      if (!value) return "";
      var firstChar = value ? value.charAt(0).toUpperCase() : "";
      var lastChar = value
        ? value.split(" ").pop().charAt(0).toUpperCase()
        : "";
      //condition checked for single letter avatar
      if (isSingleLetter) {
        return firstChar;
      } else {
        return firstChar + lastChar;
      }
    },

    // select the record to view
    selectDocTemplateRecord(selectedRecord) {
      this.selectedRecord = selectedRecord;
      // Navigate to ViewDocumentGenerator with the selected record data
      this.$router.push({
        name: "ViewDocumentGenerator",
        params: {
          documentId: selectedRecord.generatedDocumentId,
        },
        query: {
          documentData: JSON.stringify(selectedRecord),
        },
      });
    },

    downloadPDF(selectedRecord) {
      this.$emit("action-on-doc-generator", [selectedRecord, "download"]);
    },

    // on delete employees generated document
    deleteEmpDocument() {
      let vm = this;
      vm.loadingScreen = true;
      vm.closeDeleteConfirmationModal();
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_EMPLOYEE_GENERATED_DOCUMENT,
            variables: {
              generatedDocumentId: vm.deleteEmpDocId,
            },
            client: "apolloClientP",
          })
          .then(() => {
            vm.loadingScreen = false;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Document deleted successfully.",
            };
            vm.clearFilter(); // clear search value
            vm.showAlert(snackbarData);
            vm.deleteEmpDocId = null;
            vm.$emit("delete-success");
          })
          .catch((deleteError) => {
            vm.handleDeleteError(deleteError);
          });
      } catch {
        vm.handleDeleteError();
      }
    },

    // resend email to signatories
    resendEmailToSignatories(selectedItem, selectedSignatory) {
      let vm = this;
      vm.loadingScreen = true;
      try {
        const { generatedDocumentId, documentName } = selectedItem;
        const {
          signatureEmployeeId,
          signatureEmployeeName,
          emailId,
          signatureKey,
        } = selectedSignatory;
        let isCandidate = signatureKey === "Candidate" ? 1 : 0;
        vm.$apollo
          .mutate({
            mutation: RESEND_EMAIL_TO_SIGNATORIES,
            variables: {
              documentId: generatedDocumentId,
              documentName: documentName,
              signatoryId: signatureEmployeeId,
              signatoryEmailAddress: emailId,
              signatoryName: signatureEmployeeName,
              isCandidate: isCandidate,
            },
            client: "apolloClientO",
          })
          .then(() => {
            vm.loadingScreen = false;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Email resent successfully to the signatory",
            };
            vm.showAlert(snackbarData);
          })
          .catch((resendErr) => {
            vm.handleResendEmailError(resendErr);
          });
      } catch {
        vm.handleResendEmailError();
      }
    },

    // open warning alert before triggering the delete event
    openDeleteConfirmationModal(deleteId) {
      this.deleteEmpDocId = deleteId;
      this.openDeleteConfirmation = true;
    },

    // while closing the delete warning alert
    closeDeleteConfirmationModal() {
      this.openDeleteConfirmation = false;
    },

    // send notification
    sendEmailToEmployee(item) {
      const { employeeEmail } = this.$store.state.userDetails;
      if (employeeEmail) {
        this.notifyDocumentDetails = item;
        this.openSendEmailModal = true;
      } else {
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message:
            "Your account is not associated with an email address. Hence you cannot share a document with others.",
        };
        this.showAlert(snackbarData);
      }
    },

    // clear search and filter data
    clearFilter() {
      this.$store.commit("UPDATE_TOPBAR_CLEAR_FLAG", true);
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    // while clicking print in the grid, we have to open the content in popup
    onClickPrint(selectedItem) {
      this.$emit("action-on-doc-generator", [selectedItem, "print"]);
    },

    // handle doc template delete error from backend
    handleDeleteError(err = "") {
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: "",
      };
      this.loadingScreen = false;
      this.deleteEmpDocId = null;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // technical issues.
            snackbarData["message"] =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_DB0103": // no delete access
            snackbarData["message"] =
              "Sorry, you don't have access to delete the employee's generated document. Please contact HR administrator.";
            break;
          case "_EC0006": // Record(s) are already deleted in the same or some other user session.
            snackbarData["message"] =
              "Unable to delete the record as it was deleted already in the same or some other user session.";
            break;
          case "_UH0001": //unhandled error
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0104": // While check access rights form not found
          case "_DB0002": // Error while checking the employee access rights
          case "CDG0105": // Error while processing the request to delete the generated document.
          case "CDG0006": // Error while deleting the generated document.
          case "_EC0007": // Invalid input field(s).
          default:
            snackbarData["message"] =
              "Something went wrong while deleting the employee's generated document. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData["message"] = handleNetworkErrors(err);
      } else {
        snackbarData["message"] =
          "Something went wrong while deleting the employee's generated document. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    // handle resend email error
    handleResendEmailError(err = "") {
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: "",
      };
      this.loadingScreen = false;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // technical issues.
            snackbarData["message"] =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_EC0007": // Invalid input field(s).
          case "_UH0001": // unhandled error
          case "PBP0105": // Error in sending email
          case "CDG0123": // Error while processing the request to resend the signature link to the signatory.
          case "CDG0016": // Error while resending the signature link to the signatory.
          default:
            snackbarData["message"] =
              "Something went wrong while resending the document to signatory. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData["message"] = handleNetworkErrors(err);
      } else {
        snackbarData["message"] =
          "Something went wrong while resending the document to signatory. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    checkIfSignedByAll(data) {
      const allNonCandidatesSigned = data
        .filter((item) => item.signatureKey.toLowerCase() !== "candidate")
        .every((item) => item.status.toLowerCase() === "signed");
      return allNonCandidatesSigned;
    },

    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
