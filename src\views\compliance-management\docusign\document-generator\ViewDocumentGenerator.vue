<template>
  <div>
    <v-card min-height="500" class="card-radius mt-10">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center primary--text font-weight-medium pl-4">
          <v-avatar class="mr-2" size="35" color="grey lighten-3">
            <v-icon size="25" color="grey"> description </v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            {{ currentDocumentDetails.documentName }}
          </div>
        </div>
        <div class="pa-3 d-flex">
          <v-btn
            v-if="
              currentFormAccess &&
              currentFormAccess.update &&
              currentDocumentDetails.status === 'Draft' &&
              !isMobileView
            "
            color="secondary"
            dense
            small
            class="mr-1"
            @click="$emit('open-edit-form')"
          >
            Edit
          </v-btn>
          <v-btn color="primary" dense small @click="closeView">
            <v-icon size="15" class="pr-1">fas fa-times</v-icon>
          </v-btn>
        </div>
      </div>
      <v-card-text>
        <v-row>
          <v-col cols="12" sm="6" class="px-md-12">
            <div class="primary--text body-1 font-weight-medium">
              Employee Name
            </div>
            <div class="grey--text body-1 mt-4">
              {{ checkNullValue(currentDocumentDetails.employeeName) }}
            </div>
          </v-col>
          <v-col cols="12" sm="6" class="px-md-12">
            <div class="primary--text body-1 font-weight-medium">
              Candidate Name
            </div>
            <div class="grey--text body-1 mt-4">
              {{ checkNullValue(currentDocumentDetails.candidateName) }}
            </div>
          </v-col>
          <v-col cols="12" class="px-md-12">
            <div class="primary--text body-1 font-weight-medium">
              Document Content
            </div>
            <div id="docContent" class="mt-4 ck-content" />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import { checkNullValue } from "@/helper";

export default {
  name: "ViewDocumentGenerator",

  props: {
    docGeneratorDetails: {
      type: Object,
      required: false,
      default: () => ({}),
    },
    formAccess: {
      type: [Object, Boolean],
      required: false,
      default: () => ({}),
    },
    docGeneratorContent: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      documentData: {},
      isLoading: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    // Use route data if available, otherwise use props
    currentDocumentDetails() {
      if (this.$route.query.documentData) {
        return this.documentData;
      }
      return this.docGeneratorDetails;
    },

    // Get form access from store if not provided as prop
    currentFormAccess() {
      if (this.formAccess && Object.keys(this.formAccess).length > 0) {
        return this.formAccess;
      }
      // You can add logic here to get form access from store
      return (
        this.$store.getters.formIdBasedAccessRights("your-form-id")
          ?.accessRights || {}
      );
    },
  },

  watch: {
    docGeneratorContent: {
      handler() {
        this.$nextTick(() => {
          this.loadDocumentContent();
        });
      },
      immediate: true,
    },
  },

  mounted() {
    // Check if we have route parameters (navigated from list)
    if (this.$route.query.documentData) {
      try {
        this.documentData = JSON.parse(this.$route.query.documentData);
      } catch (error) {
        console.error("Error parsing document data from route:", error);
        // Redirect back to list if data is invalid
        this.$router.push({ name: "DocuSign" });
        return;
      }
    }

    this.loadDocumentContent();
  },

  methods: {
    checkNullValue,

    // Navigation methods
    goBackToList() {
      this.$router.push({ name: "DocuSign" });
    },

    closeView() {
      // If we came from a route, go back to list
      if (this.$route.query.documentData) {
        this.goBackToList();
      } else {
        // If used as a component, emit close event
        this.$emit("close-view-form");
      }
    },

    loadDocumentContent() {
      let content = this.docGeneratorContent;
      let element = document.getElementById("docContent");

      console.log("ViewDocumentGenerator - loadDocumentContent called");
      console.log("Content received:", content);
      console.log("Content type:", typeof content);
      console.log("Content constructor:", content?.constructor?.name);

      if (element) {
        // Check if content is a Promise object (which would display as [object Promise])
        if (
          content &&
          typeof content === "object" &&
          typeof content.then === "function"
        ) {
          console.error("Content is a Promise object, this should not happen");
          element.innerHTML =
            '<p class="text-warning">Error loading document content. Please try again.</p>';
          return;
        }

        // Check if content is the string "[object Promise]"
        if (content === "[object Promise]") {
          console.error('Content is the string "[object Promise]"');
          element.innerHTML =
            '<p class="text-warning">Error loading document content. Please try again.</p>';
          return;
        }

        // Additional check for any object that might stringify to [object Promise]
        if (content && typeof content === "object") {
          console.error("Content is an object:", content);
          element.innerHTML =
            '<p class="text-warning">Error: Content is an object instead of string.</p>';
          return;
        }

        // Set the content if it's valid
        if (content && typeof content === "string") {
          console.log("Setting valid string content");
          element.innerHTML = content;
        } else {
          console.log("No valid content, showing default message");
          element.innerHTML =
            '<p class="text-muted">No document content available.</p>';
        }
      } else {
        console.error("docContent element not found");
      }
    },
  },
};
</script>
