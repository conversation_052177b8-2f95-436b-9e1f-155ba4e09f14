<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row v-if="originalList.length > 0" justify="center">
            <v-col
              cols="12"
              md="9"
              class="d-flex justify-end"
              v-if="!showAddEditForm && !showViewForm"
            >
              <EmployeeDefaultFilterMenu
                class="d-flex justify-end"
                :isFilter="false"
              />
              <DepartmentFilter
                ref="formFilterRef"
                :items="originalList"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="department-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList('Department error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="originalList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      :notes="`The Department Module in our HRMS solution provides a comprehensive structure for organizing your workforce across various hierarchical levels. This module enables you to define and manage your organization's structure with clear and customizable hierarchy levels, such as Group, Division, Department, Section, and Unit.`"
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      :notes="`By leveraging this hierarchy, organizations can ensure a streamlined workflow, enhance communication, and clarify reporting structures. Whether you're overseeing a large corporate entity or a growing startup, this module empowers you to visualize and optimize the entire organizational setup, ensuring that each unit is aligned with the overall business goals.`"
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddDepartments()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Add New
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      rounded="lg"
                      variant="flat"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else-if="isImportModel">
            <DepartmentImport
              @close-import-model="closeImportModel()"
              @refetch-data="refetchList()"
              :backupMainList="originalList"
            ></DepartmentImport>
          </div>
          <div v-else>
            <div>
              <div
                v-if="originalList.length > 0 && !isSmallTable"
                class="d-flex flex-wrap align-center my-3"
                :class="isMobileView ? 'flex-column' : ''"
                style="justify-content: space-between"
              >
                <div
                  class="d-flex align-center flex-wrap"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <v-btn
                    rounded="lg"
                    style="pointer-events: none"
                    variant="flat"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    >Active:
                    <span class="text-green font-weight-bold">{{
                      activeDepartments
                    }}</span>
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    style="pointer-events: none"
                    variant="flat"
                    class="ml-2"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    >Inactive:
                    <span class="text-red font-weight-bold">{{
                      inActiveDepartments
                    }}</span></v-btn
                  >
                </div>

                <div
                  class="d-flex align-center"
                  :class="isMobileView ? 'justify-center' : 'justify-end'"
                >
                  <v-btn
                    v-if="formAccess.add"
                    prepend-icon="fas fa-plus"
                    color="primary"
                    variant="elevated"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="onAddDepartments"
                  >
                    <template v-slot:prepend>
                      <v-icon></v-icon>
                    </template>
                    Add New
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    color="transparent"
                    variant="flat"
                    class="mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList('Refetch List')"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                  <v-menu v-model="openMoreMenu" transition="scale-transition">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        variant="plain"
                        class="mt-1 ml-n5 mr-n5"
                        v-bind="props"
                      >
                        <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                        <v-icon v-else>fas fa-caret-up</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="action in moreActions"
                        :key="action"
                        @click="onMoreAction(action)"
                      >
                        <v-hover>
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                              ><v-icon size="15" class="pr-2">{{
                                action.icon
                              }}</v-icon
                              >{{ action.key }}</v-list-item-title
                            >
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>

              <v-row>
                <v-col
                  v-if="originalList.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                  class="mb-12"
                >
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Department
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Department_Name ===
                                  item.Department_Name
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Department_Name"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Department_Name.length > 50
                                      ? props
                                      : ''
                                  "
                                >
                                  {{ checkNullValue(item.Department_Name) }}
                                  <div
                                    v-if="
                                      item?.Department_Code &&
                                      labelList?.['314']?.Field_Visiblity ===
                                        'Yes'
                                    "
                                    class="text-grey"
                                  >
                                    {{ checkNullValue(item.Department_Code) }}
                                  </div>
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Department Header
                          </div>
                          <v-tooltip
                            :text="item.Organization_Type"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Organization_Type.length > 100
                                    ? props
                                    : ''
                                "
                                :style="
                                  !isMobileView
                                    ? 'max-width: 500px; '
                                    : 'max-width: 200px; '
                                "
                              >
                                {{ checkNullValue(item.Organization_Type) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Parent Department
                          </div>
                          <v-tooltip
                            :text="item.Parent_Name"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Parent_Name?.length > 100 ? props : ''
                                "
                                :style="
                                  !isMobileView
                                    ? 'max-width: 500px; '
                                    : 'max-width: 200px; '
                                "
                              >
                                {{ checkNullValue(item.Parent_Name) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'width: max-content'
                          "
                          :cols="2"
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Status
                          </div>

                          <div
                            @click.stop="
                              {
                              }
                            "
                          >
                            <AppToggleButton
                              button-active-text="Active"
                              button-inactive-text="InActive"
                              button-active-color="#7de272"
                              button-inactive-color="red"
                              id-value="gab-analysis-based-on"
                              :current-value="
                                item.Department_Status === 'Active'
                                  ? true
                                  : false
                              "
                              :isDisableToggle="!formAccess.update"
                              :tooltipContent="
                                formAccess.update
                                  ? ''
                                  : `Sorry, you don't have access rights to update the status`
                              "
                              @chosen-value="updateStatus($event, item)"
                            ></AppToggleButton>
                          </div>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-center align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex justify-center align-center"
                            style="width: 100%"
                          >
                            Actions
                          </div>
                          <section
                            class="d-flex justify-start align-center"
                            style="width: 100%"
                          >
                            <ActionMenu
                              v-if="itemActions(item).length > 0"
                              :accessRights="checkAccess()"
                              @selected-action="onActions($event, item)"
                              :actions="itemActions(item)"
                              iconColor="grey"
                            ></ActionMenu>
                            <div v-else>
                              <p>-</p>
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewDepartmentHierarchy
                    :selectedItem="selectedItem"
                    :isEdit="isEdit"
                    :access-rights="formAccess"
                    :landedFormName="landedFormName"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  />
                </v-col>

                <v-col
                  :cols="originalList.length === 0 ? 12 : 7"
                  v-if="showAddEditForm && windowWidth >= 1264"
                >
                  <AddEditDepartmentHierarchy
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :originalList="originalList"
                    :landedFormName="landedFormName"
                    @close-form="closeAllForms()"
                    @edit-updated="refetchList('og updated successfully')"
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditDepartmentHierarchy
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :landedFormName="landedFormName"
        :originalList="originalList"
        @close-form="closeAllForms()"
        @edit-updated="refetchList('OG updated successfully')"
      />
      <ViewDepartmentHierarchy
        v-if="showViewForm"
        :selectedItem="selectedItem"
        :isEdit="isEdit"
        :access-rights="formAccess"
        :landedFormName="landedFormName"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      />
    </v-dialog>
    <AppWarningModal
      v-if="deleteModel"
      :open-modal="deleteModel"
      confirmation-heading="Are you sure to delete the selected record?"
      icon-name="far fa-times-circle"
      icon-Size="75"
      @close-warning-modal="deleteModel = false"
      @accept-modal="deleteDepartmentHierarchy()"
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const DepartmentFilter = defineAsyncComponent(() =>
  import("./DepartmentFilter.vue")
);
const AddEditDepartmentHierarchy = defineAsyncComponent(() =>
  import("./AddEditDepartmentHierarchy.vue")
);
const ViewDepartmentHierarchy = defineAsyncComponent(() =>
  import("./ViewDepartmentHierarchy.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
const DepartmentImport = defineAsyncComponent(() =>
  import("./DepartmentImport.vue")
);
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
// Queries
import {
  LIST_DEPARTMENTS,
  ADD_UPDATE_DEPARTMENT,
  DELETE_DEPARTMENT,
} from "@/graphql/organisation/department-hierarchy/departmentHierarchyQueries.js";
import mixpanel from "mixpanel-browser";
import FileExportMixin from "@/mixins/FileExportMixin";
import OrgStructureTabTranslationMixin from "@/mixins/OrgStructureTabTranslationMixin";
import Config from "@/config.js";

export default {
  name: "DepartmentHierarchy",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    ActionMenu,
    DepartmentFilter,
    AddEditDepartmentHierarchy,
    ViewDepartmentHierarchy,
    DepartmentImport,
  },
  mixins: [FileExportMixin, OrgStructureTabTranslationMixin],
  data: () => ({
    // list
    isFilterApplied: false,
    listLoading: false,
    isLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    // add/update
    isEdit: false,
    showAddEditForm: false,
    openMoreMenu: false,
    isImportModel: false,
    // view
    selectedItem: null,
    showViewForm: false,
    havingAccess: {},
    // tab
    currentTabItem: "",
    deleteModel: false,
  }),
  computed: {
    landedFormName() {
      let departmentForm = this.accessRights("2");
      if (departmentForm && departmentForm.customFormName) {
        return departmentForm.customFormName;
      } else return "Department";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights("2");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    orgStructureFormAccess() {
      return this.$store.getters.orgStructureFormAccess;
    },
    organizationGroupFormName() {
      let projectForm = this.accessRights("269");
      if (
        projectForm &&
        projectForm.customFormName &&
        projectForm.customFormName !== ""
      ) {
        return projectForm.customFormName;
      } else return "Organization Group";
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.orgStructureFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        if (formAccessArray && formAccessArray.includes("Organization Group")) {
          const index = formAccessArray.indexOf("Organization Group");
          formAccessArray[index] = this.organizationGroupFormName;
        }

        // Translate form names using the mixin
        return this.translateFormAccessArray(formAccessArray);
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Department",
            align: "start",
            key: "Department_Name",
          },
          {
            title: "Department Header",
            key: "Organization_Type",
          },
          {
            title: "Status",
            key: "Department_Status",
          },
        ];
      } else {
        return [
          {
            title: "Department",
            align: "start",
            key: "Department_Name",
          },
          {
            title: "Department Header",
            key: "Organization_Type",
          },
          {
            title: "Parent Department",
            key: "Parent_Name",
          },
          {
            title: "Status",
            key: "Department_Status",
          },
          {
            title: "Actions",
            key: "actions",
            sortable: false,
          },
        ];
      }
    },
    activeDepartments() {
      let empList = this.originalList.filter(
        (el) => el.Department_Status === "Active"
      );
      return empList.length;
    },
    inActiveDepartments() {
      let empList = this.originalList.filter(
        (el) => el.Department_Status === "InActive"
      );
      return empList.length;
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    moreActions() {
      let moreActions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      // Need to implement in future
      // if (this.formAccess.add) {
      //   moreActions.push({
      //     key: "Import",
      //     icon: "fas fa-file-import",
      //   });
      // }
      return moreActions;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    itemActions() {
      if (this.formAccess && this.formAccess.delete && this.formAccess.update)
        return ["Edit", "Delete"];
      else if (this.formAccess?.delete) return ["Delete"];
      else if (this.formAccess?.update) return ["Edit"];
      else return [];
    },
    checkAccess() {
      this.havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      this.havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? 1 : 0;
      return this.havingAccess;
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action === "Edit") {
        this.isEdit = true;
        this.showAddEditForm = true;
      } else if (action === "Delete") {
        this.deleteModel = true;
      }
    },
    openImportModel() {
      this.isImportModel = true;
    },
    closeImportModel() {
      this.isImportModel = false;
    },
    onTabChange(tab) {
      mixpanel.track("Department Hierarchy form tab changed");
      // Use the mixin method for handling translated tabs
      this.onTabChangeWithTranslation(tab);
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
      let filterObj = {
        DepartmentCode: "",
        Departments: "",
        DepartmentHeader: "",
        ParentDepartment: "",
        BonusType: "",
        Status: null,
      };
      this.applyFilter(filterObj);
    },
    applyFilter(filter) {
      let filteredList = this.originalList;
      if (filter && filter.DepartmentCode && filter.DepartmentCode.length) {
        filteredList = filteredList.filter((item) => {
          return filter.DepartmentCode.includes(item.Department_Code);
        });
      }
      if (filter && filter.Departments && filter.Departments.length) {
        filteredList = filteredList.filter((item) => {
          return filter.Departments.includes(item.Department_Name);
        });
      }
      if (filter && filter.DepartmentHeader && filter.DepartmentHeader.length) {
        filteredList = filteredList.filter((item) => {
          return filter.DepartmentHeader.includes(item.Organization_Type);
        });
      }
      if (filter && filter.ParentDepartment && filter.ParentDepartment.length) {
        filteredList = filteredList.filter((item) => {
          return filter.ParentDepartment.includes(item.Parent_Name);
        });
      }
      if (filter && filter.BonusType && filter.BonusType.length) {
        filteredList = filteredList.filter((item) => {
          return filter.BonusType.includes(item.Bonus_Type);
        });
      }
      if (filter && filter.Status && filter.Status.length) {
        filteredList = filteredList.filter((item) => {
          return filter.Status.includes(item.Department_Status);
        });
      }
      this.isFilterApplied = true;
      this.filteredList = filteredList;
      this.itemList = filteredList;
    },
    onMoreAction(actionType) {
      if (actionType.key === "Export") {
        this.exportReportFile(actionType);
      } else if (actionType.key === "Import") {
        this.isImportModel = true;
        this.$emit("open-import-model");
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.itemList));
      exportData = exportData.map((el) => ({
        ...el,
        Added_On: el.Added_On ? this.convertUTCToLocal(el.Added_On) : "",
        Updated_On: el.Updated_On ? this.convertUTCToLocal(el.Updated_On) : "",
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: [
          {
            header: this.labelList["314"]
              ? this.labelList["314"].Field_Alias
              : "Department Code",
            key: "Department_Code",
          },
          {
            header: "Department",
            key: "Department_Name",
          },
          {
            header: "Department Header",
            key: "Organization_Type",
          },
          {
            header: "Parent Department",
            key: "Parent_Name",
          },
          {
            header: "Bonus Type",
            key: "Bonus_Type",
          },
          {
            header: "Status",
            key: "Department_Status",
          },
          {
            header: "Description",
            key: "Description",
          },
          { header: "Created On", key: "Added_On" },
          { header: "Created By", key: "Added_By_Name" },
          { header: "Updated On", key: "Updated_On" },
          { header: "Updated By", key: "Updated_By_Name" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },

    openViewForm(item) {
      mixpanel.track("Department view form opened");
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    openEditForm() {
      mixpanel.track("Department edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    onAddDepartments() {
      mixpanel.track("Department add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      mixpanel.track("Department all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_DEPARTMENTS,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDepartments &&
            response.data.listDepartments.departments &&
            !response.data.listDepartments.errorCode
          ) {
            const departList = JSON.parse(
              response.data.listDepartments.departments
            );
            vm.itemList = departList;
            vm.originalList = departList;
            vm.onApplySearch();
            vm.listLoading = false;
            mixpanel.track("Department list retrieved");
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Department Hierarchy",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    deleteDepartmentHierarchy() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_DEPARTMENT,
          client: "apolloClientBB",
          variables: {
            Department_Id: parseInt(this.selectedItem?.Department_Id),
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.deleteDepartment &&
            !response.data.deleteDepartment.error
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: `${this.landedFormName} record deleted successfully!`,
            };
            vm.showAlert(snackbarData);
            vm.selectedItem = null;
            vm.deleteModel = false;
            vm.refetchList();
            vm.isLoading = false;
          } else {
            vm.handleDeleteErrors();
          }
        })
        .catch((error) => {
          vm.handleDeleteErrors(error);
        });
    },
    handleDeleteErrors(err = "") {
      this.deleteModel = false;
      this.isLoading = false;

      // Check if the error contains associated forms information
      if (
        err &&
        err.graphQLErrors &&
        err.graphQLErrors[0] &&
        err.graphQLErrors[0].extensions
      ) {
        const associatedForms = err.graphQLErrors[0].extensions.associatedForms;

        // Create a message to display the associated forms
        let errorMessage =
          "Department cannot be deleted as the following forms are associated with this department:\n\n";
        associatedForms.forEach((form, index) => {
          // Add a comma for all but the last item
          if (index === associatedForms.length - 1) {
            errorMessage += ` ${form}.`; // Add a period for the last item
          } else {
            errorMessage += ` ${form},\n`; // Add a comma and newline for other items
          }
        });
        errorMessage +=
          "\nPlease ensure the forms are reassigned or removed before making this change.";

        // Show alert with the associated forms
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: errorMessage,
        });
      } else {
        // Fallback to general error handling
        this.$store.dispatch("handleApiErrors", {
          error: err,
          action: "delete",
          form: "Department Hierarchy",
          isListError: false,
        });
      }
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },

    updateStatus(statusVal, item) {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_DEPARTMENT,
            variables: {
              Department_Id: item?.Department_Id,
              Department_Name: item?.Department_Name,
              Department_Code: item?.Department_Code
                ? item.Department_Code
                : "",
              Organization_Type_Id: item.Organization_Type_Id,
              Parent_Type_Id: item.Parent_Type_Id,
              BonusType_Id: item.BonusType_Id,
              Department_Status: statusVal[1] ? "Active" : "InActive",
              Description: item?.Description ? item.Description : "",
            },
            client: "apolloClientBB",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.landedFormName + " status updated successfully",
            };
            vm.showAlert(snackbarData);
            vm.refetchList("status-updated");
          })
          .catch((err) => {
            vm.handleAddUpdateError(err);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "Department Hierarchy",
        isListError: false,
      });
      this.refetchList("status-update-failed");
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.department-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .department-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
