<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
            >{{ isEdit ? $t("coreHr.edit") : $t("coreHr.new") }}</v-chip
          >
          <span class="pt-1 font-weight-bold">{{ landedFormName }}</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              variant="elevated"
              class="mb-2 primary"
              @click="validateSubtypeForm"
              >{{ $t("coreHr.save") }}</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >{{ $t("coreHr.save") }}</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" class="mr-1">fas fa-times</v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-5'"
        style="height: calc(100vh - 260px); overflow: scroll"
      >
        <v-card-text>
          <v-form ref="subtypeForm">
            <v-row>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <CustomSelect
                  :label="$t('coreHr.documentCategory')"
                  :item-selected="documentCategory"
                  :items="documentCategoryList"
                  :isRequired="true"
                  item-title="Category_Fields"
                  item-value="Category_Id"
                  :loading="documentCategoryListLoading"
                  :rules="[
                    required($t('coreHr.documentCategory'), documentCategory),
                  ]"
                  @selected-item="onChange($event, 'documentCategory')"
                ></CustomSelect>
              </v-col>

              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <CustomSelect
                  :label="$t('coreHr.documentType')"
                  :disabled="!documentCategory"
                  :item-selected="documentType"
                  :items="typeList"
                  :isRequired="true"
                  item-title="Document_Type"
                  item-value="Document_Type_Id"
                  :loading="documentTypeListLoading"
                  :rules="[required($t('coreHr.documentType'), documentType)]"
                  @selected-item="onChange($event, 'documentType')"
                >
                </CustomSelect>
              </v-col>

              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-text-field
                  v-model="documentSubtype"
                  variant="solo"
                  :rules="[
                    required($t('coreHr.documentSubtype'), documentSubtype),
                    multilingualNameNumericValidation(
                      $t('coreHr.documentSubtype'),
                      documentSubtype
                    ),
                    minLengthValidation(
                      $t('coreHr.documentSubtype'),
                      documentSubtype,
                      3
                    ),
                    maxLengthValidation(
                      $t('coreHr.documentSubtype'),
                      documentSubtype,
                      100
                    ),
                  ]"
                  @input="isFormDirty = true"
                >
                  <template v-slot:label>
                    {{ $t("coreHr.documentSubtype") }}
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>

              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <p class="v-label">
                  {{ $t("coreHr.enforceDuringOnboarding") }}
                </p>
                <v-switch
                  density="compact"
                  color="primary"
                  v-model="mandatory"
                  true-value="Yes"
                  false-value="No"
                  @change="isFormDirty = true"
                ></v-switch>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0 mb-2"
                v-if="mandatory == 'Yes'"
              >
                <CustomSelect
                  :label="$t('coreHr.documentEnforcementGroup')"
                  :isAutoComplete="true"
                  :item-selected="tags"
                  :items="tagsList"
                  itemTitle="Group_Name"
                  itemValue="Group_Id"
                  :select-properties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                  :loading="tagsListLoading"
                  @selected-item="onChange($event, 'tags')"
                ></CustomSelect>
                <v-btn
                  class="mt-n4 ml-n2"
                  color="primary"
                  variant="text"
                  size="small"
                  @click="showAddTagsForm = true"
                >
                  <v-icon class="mr-1" size="14">fas fa-plus</v-icon>
                  {{ $t("coreHr.addDocumentEnforcementGroup") }}
                </v-btn>
              </v-col>

              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 mb-2">
                <v-file-input
                  prepend-icon
                  :model-value="fileContent"
                  append-inner-icon="fas fa-paperclip"
                  variant="solo"
                  :rules="[
                    onlyForEmail == 'Yes'
                      ? required('Document', fileContentValidation)
                      : true,
                  ]"
                  accept="image/png, image/jpeg, image/jpg, application/pdf"
                  @update:model-value="onChangeFiles"
                  @click:clear="removeFiles"
                >
                  <template v-slot:label>
                    {{ $t("coreHr.documentDownloadedDuringOnboarding") }}
                    <span v-if="onlyForEmail == 'Yes'" style="color: red"
                      >*</span
                    >
                  </template>
                </v-file-input>
              </v-col>

              <v-col cols="12" class="px-md-6 pb-0 mb-2">
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("coreHr.instructionForPresentingDocument") }}
                </p>
                <div>
                  <div
                    ref="editor"
                    class="quill-editor"
                    @keydown="isFormDirty = true"
                  ></div>
                  <p class="text-red">{{ this.instructionError }}</p>
                </div>
                <ul class="mt-5" style="color: red">
                  <li>
                    {{
                      $t("coreHr.instructionHint1", { here: "{\{ here }\}" })
                    }}
                  </li>
                  <li>
                    {{
                      $t("coreHr.instructionHint2", { here: "{\{ here }\}" })
                    }}
                  </li>
                </ul>
              </v-col>

              <v-col
                v-if="moreDetailsList && moreDetailsList.length > 0"
                cols="12"
              >
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    ></AppWarningModal>
    <AppLoading v-if="isLoading"></AppLoading>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
            >Close</v-btn
          >
        </div>
      </template>
    </AppSnackBar>
    <v-dialog
      :model-value="showAddTagsForm"
      max-width="30%"
      @click:outside="(showAddTagsForm = false), (moreTags = [])"
    >
      <v-card :min-height="windowWidth > 700 ? 350 : ''" class="pa-5">
        <div class="text-h6 mb-10">Add new Document Enforcement Group</div>
        <div class="mb-auto" style="width: 70%">
          <v-form ref="enforcementGroupForm">
            <v-text-field
              ref="skillSet"
              v-model="input"
              :label="moreTags.length ? '' : 'New document Enforcement Group'"
              @update:modelValue="showAddIcon"
              variant="solo"
              :rules="[
                multilingualNameNumericValidation(
                  'group',
                  moreTags[moreTags.length - 1]
                ),
                required('New group', moreTags[0]),
              ]"
              @keydown.enter.prevent="addChip"
            >
              <template v-slot:default>
                <v-icon v-if="showIcon" @click="addChip" size="x-small"
                  >fas fa-plus</v-icon
                >
                <v-chip
                  v-for="(chip, index) in moreTags"
                  append-icon="fas fa-times-circle"
                  :key="index"
                  class="ma-1"
                  @click="removeChip(index)"
                  >{{ chip }}</v-chip
                >
              </template>
            </v-text-field>
          </v-form>
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="primary mr-2"
            variant="outlined"
            @click="(showAddTagsForm = false), (moreTags = [])"
          >
            <span class="primary">Cancel</span>
          </v-btn>
          <v-btn
            type="submit"
            variant="elevated"
            class="primary"
            @click="addMoreTags()"
            ><span class="primary">Submit</span></v-btn
          >
        </div>
      </v-card>
      <AppLoading v-if="addTagLoader"></AppLoading>
    </v-dialog>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";
import {
  ADD_UPDATE_DOCUMENT_SUBTYPE,
  ADD_NEW_TAGS,
  LIST_EMAIL_TEMPLATE,
} from "@/graphql/corehr/documentSubtypeQueries";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { LIST_DOC_CATEGORY, LIST_DOC_TYPE } from "@/graphql/dropDownQueries";
import { RETRIEVE_DOCUMENTS_TAG } from "@/graphql/onboarding/individualQueries.js";

import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";

export default {
  name: "AddDocumentSubtype",
  components: {
    MoreDetails,
    CustomSelect,
  },
  mixins: [validationRules],

  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    originalList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    landedFormName: {
      type: String,
      required: true,
    },
    maxOrgCode: {
      type: Number,
    },
  },
  emits: ["close-form", "edit-updated"],
  data() {
    return {
      // add/update
      documentCategory: null,
      documentType: null,
      documentSubtype: "",
      mandatory: "No",
      fileName: "",
      fileContent: null,
      isFileChanged: false,
      instruction: "",
      hasContent: false,
      onlyForEmail: "No",
      emailTemplate: null,
      input: "",
      tags: [],
      tagsList: [],
      moreTags: [],
      tagsListLoading: false,
      showIcon: false,
      isFormDirty: false,
      addedBy: "",
      addedOn: "",
      updatedBy: "",
      updatedOn: "",
      moreDetailsList: [],
      openMoreDetails: true,
      documentCategoryList: [],
      documentList: [],
      documentTypeList: [],
      // loading/error/other
      isLoading: false,
      openConfirmationPopup: false,
      validationMessages: [],
      showValidationAlert: false,
      documentCategoryListLoading: false,
      documentTypeListLoading: false,
      showMoreTags: false,
      showAddTagsForm: false,
      instructionError: "",
      addTagLoader: false,
      emailTemplateList: [],
      emailTemplateListLoading: false,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    fileContentValidation() {
      if (this.fileContent && this.fileContent.name) {
        return true;
      }
      return false;
    },
    isInputValid() {
      const rules = [
        this.alphaNumSpaceNewLineWithElevenSymbolValidation(this.input),
      ];
      return rules[0] === true ? true : false;
    },
    typeList() {
      return this.documentTypeList.filter((item) => {
        return item.Category_Id == this.documentCategory;
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initQuillEditor();
    });
    this.retrieveDocumentTagList();
    this.retrieveEmailTemplate();
    if (this.editFormData) {
      this.documentSubTypeId = this.editFormData.documentSubTypeId;
      this.documentCategory = this.editFormData.categoryId;
      this.documentType = this.editFormData.documentTypeId;
      this.documentSubtype = this.editFormData.documentSubType;
      this.fileName = this.editFormData.fileName;
      if (this.fileName) {
        this.fileContent = {
          name: this.formattedFileName(this.fileName),
        };
      }
      this.mandatory = this.editFormData.mandatory;
      this.fileSize = this.editFormData.fileSize;
      this.onlyForEmail = this.editFormData.onlyForEmail;
      this.emailTemplate = this.editFormData.emailTemplates;
    }
    this.retrieveDocumentCategory();
    this.retrieveDocumentType();
  },
  watch: {},
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File Name";
      }
      return "";
    },

    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },

    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
    },

    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },

    removeFiles() {
      this.fileName = "";
      this.fileSize = "";
    },

    addChip() {
      if (this.isInputValid && this.input.trim()) {
        this.moreTags.push(this.input.trim());
        this.input = "";
        this.showIcon = false;
      }
    },

    removeChip(index) {
      this.moreTags.splice(index, 1);
    },

    showAddIcon() {
      this.showIcon = !!this.input.trim();
    },

    initQuillEditor() {
      this.quill = new Quill(this.$refs.editor, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
      });
      if (this.editFormData) {
        this.quill.root.innerHTML = this.editFormData.instruction;
      }
    },

    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editor.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },

    async validateSubtypeForm() {
      let validInstruction = true;
      if (this.quill.root.innerHTML != "<p><br></p>") {
        const pattern = /\{\{\s*here\s*\}\}/;

        // Test the input string against the pattern
        validInstruction = pattern.test(this.quill.root.innerHTML);
        if (!validInstruction) {
          this.instructionError =
            "Please create the instruction according to hints provided";
        } else {
          this.instructionError = "";
        }
      }
      const { valid } = await this.$refs.subtypeForm.validate();
      if (valid && validInstruction) {
        this.validateDocuments();
      }
    },

    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        if (this.fileContent && this.fileContent.size && this.isFileChanged) {
          await this.uploadFileContents(this.fileContent);
        }
        this.addUpdateDocumentSubType();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },

    addUpdateDocumentSubType() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_DOCUMENT_SUBTYPE,
          variables: {
            documentSubTypeId: this.isEdit ? this.documentSubTypeId : null,
            documentTypeId: this.documentType,
            documentSubType: this.documentSubtype,
            onlyForEmail: this.onlyForEmail,
            instruction: this.quill.root.innerHTML,
            mandatory: this.mandatory,
            fileName: this.fileName,
            Group_Ids: this.tags,
            emailTemplates: this.emailTemplate,
          },
          client: "apolloClientBB",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.isEdit
              ? vm.landedFormName + " updated successfully."
              : vm.landedFormName + " added successfully.",
          };
          vm.showAlert(snackbarData);
          vm.$emit("edit-updated");
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: this.landedFormName.toLowerCase(),
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    async uploadFileContents() {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employee Document Download/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.fileName,
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .catch((error) => {
          throw error;
        });
    },
    onChange(value, field) {
      this.isFormDirty = true;
      this[field] = value;
      if (field == "documentCategory") {
        this.documentType = null;
      }
    },
    onChangeFiles(val) {
      this.fileContent = val;
      if (val) {
        this.isFormDirty = true;
        this.fileName =
          this.currentTimeStamp + "?subtype?1?" + this.fileContent.name;
        this.fileSize = this.fileContent.size.toString();
        this.isFileChanged = true;
      }
    },
    retrieveDocumentCategory() {
      let vm = this;
      vm.documentCategoryListLoading = true;
      vm.$apollo
        .query({
          query: LIST_DOC_CATEGORY,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentCategory &&
            !response.data.listDocumentCategory.errorCode
          ) {
            const { documentCategory } = response.data.listDocumentCategory;
            if (documentCategory?.length) {
              vm.documentCategoryList = documentCategory.map((item) => {
                return {
                  ...item,
                  Category_Fields: item.Vendor_Based
                    ? `${item.Category_Fields} (Vendor)`
                    : item.Category_Fields,
                };
              });
            }
          }
          vm.documentCategoryListLoading = false;
        })
        .catch(() => {
          vm.documentCategoryListLoading = false;
        });
    },
    retrieveDocumentType() {
      let vm = this;
      vm.documentTypeListLoading = true;
      vm.$apollo
        .query({
          query: LIST_DOC_TYPE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentType &&
            !response.data.listDocumentType.errorCode
          ) {
            const { documentType } = response.data.listDocumentType;
            vm.documentTypeList =
              documentType && documentType.length > 0 ? documentType : [];
          }
          vm.documentTypeListLoading = false;
        })
        .catch(() => {
          vm.documentTypeListLoading = false;
        });
    },
    retrieveEmailTemplate() {
      let vm = this;
      vm.emailTemplateListLoading = true;
      vm.$apollo
        .query({
          query: LIST_EMAIL_TEMPLATE,
          client: "apolloClientA",
          variables: {
            type: "Email",
            formIds: [178],
          },
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.getNotificationTemplates &&
            data.getNotificationTemplates.notificationTemplates &&
            data.getNotificationTemplates.notificationTemplates.length > 0
          ) {
            vm.emailTemplateList =
              data.getNotificationTemplates.notificationTemplates;
          } else {
            vm.emailTemplateList = [];
          }
          vm.emailTemplateListLoading = false;
        })
        .catch((err) => {
          this.handleListTemplateError(err);
        });
    },
    handleListTemplateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "email templates",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    retrieveDocumentTagList() {
      let vm = this;
      vm.tagsListLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_DOCUMENTS_TAG,
          client: "apolloClientV",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.listdocumentEnforcementGroup &&
            data.listdocumentEnforcementGroup.groupIds &&
            data.listdocumentEnforcementGroup.groupIds.length > 0
          ) {
            vm.tagsList = data.listdocumentEnforcementGroup.groupIds;
            if (this.editFormData.Group_Ids) {
              this.tags = this.editFormData.Group_Ids.split(",").map(Number);
            }
          }
          vm.tagsListLoading = false;
        })
        .catch(() => {
          vm.tagsListLoading = false;
        });
    },

    async addMoreTags() {
      let { valid } = await this.$refs.enforcementGroupForm.validate();
      if (valid) {
        let vm = this;
        vm.addTagLoader = true;
        vm.$apollo
          .mutate({
            mutation: ADD_NEW_TAGS,
            variables: { groupNames: vm.moreTags },
            client: "apolloClientW",
          })
          .then(() => {
            vm.addTagLoader = false;
            vm.retrieveDocumentTagList();
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Enforcement Group added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.showAddTagsForm = false;
          })
          .catch((err) => {
            this.handleAddTagsError(err);
          });
      }
    },
    handleAddTagsError(err = "") {
      this.addTagLoader = false;
      this.showAddTagsForm = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "adding",
          form: "Enforcement Group",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
<style scoped>
.quill-editor {
  height: 200px;
}
</style>
