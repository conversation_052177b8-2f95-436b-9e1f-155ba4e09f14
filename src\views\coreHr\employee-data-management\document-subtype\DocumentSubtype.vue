<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <DocumentSubTypeFilter
                v-if="!showAddEditForm && (itemList.length || isFilterApplied)"
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? $t('coreHr.retry') : ''"
            @button-click="refetchList('Document subtype error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      :notes="$t('coreHr.documentSubtypeHelp1')"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 primary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddDocumentSubType()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span>{{ $t("coreHr.addNew") }}</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter()"
                    >
                      {{ $t("coreHr.resetFilterSearch") }}
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      rounded="lg"
                      class="mt-1"
                      color="transparent"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <div
                v-if="originalList.length > 0 && !isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  v-if="formAccess.add"
                  prepend-icon="fas fa-plus"
                  variant="elevated"
                  rounded="lg"
                  class="mx-1 primary"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="onAddDocumentSubType()"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  <span>{{ $t("coreHr.addNew") }}</span>
                </v-btn>
                <v-btn
                  rounded="lg"
                  class="mt-1"
                  color="transparent"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action"
                      @click="onMoreAction(action)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                          >
                            <v-tooltip :text="action.message">
                              <template v-slot:activator="{ props }">
                                <div v-bind="action.message ? props : ''">
                                  <v-icon size="15" class="pr-2">{{
                                    action.icon
                                  }}</v-icon>
                                  {{ action.key }}
                                </div>
                              </template>
                            </v-tooltip>
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>

              <v-row>
                <v-col
                  v-if="originalList.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                  class="mb-12"
                >
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            {{ $t("coreHr.documentSubtype") }}
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.documentSubTypeId ===
                                  item.documentSubTypeId
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.documentSubType"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 200px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.documentSubType &&
                                    item.documentSubType.length > 22
                                      ? props
                                      : ''
                                  "
                                >
                                  {{ item.documentSubType }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            {{ $t("coreHr.documentType") }}
                          </div>
                          <section class="d-flex align-center">
                            <!-- <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.documentId === item.documentId
                              "
                              class="data-table-side-border d-flex"
                            ></div> -->
                            <v-tooltip
                              :text="item.documentType"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.documentType &&
                                    item.documentType.length > 50
                                      ? props
                                      : ''
                                  "
                                >
                                  {{ item.documentType }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            {{ $t("coreHr.documentCategory") }}
                          </div>
                          <section class="d-flex align-center">
                            <!-- <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.documentId === item.documentId
                              "
                              class="data-table-side-border d-flex"
                            ></div> -->
                            <div
                              class="text-subtitle-1 font-weight-regular text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                            >
                              {{ item.categoryFields }}
                            </div>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            {{ $t("coreHr.enforcedInSelfOnboarding") }}
                          </div>
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 500px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ checkNullValue(item.mandatory) }}
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          class="text-body-2 text-center"
                          style="width: 150px"
                          @click.stop="
                            {
                            }
                          "
                        >
                          <span
                            v-if="item.fileName"
                            style="text-decoration: underline"
                            @click="retrieveResumeDetails(item.fileName)"
                            class="text-blue cursor-pointer mb-2"
                          >
                            {{ $t("coreHr.viewDocument") }}
                          </span>
                          <div v-else>-</div>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          class="text-body-2 text-center"
                          style="width: 150px"
                          @click.stop="
                            {
                            }
                          "
                        >
                          <ActionMenu
                            v-if="formAccess && formAccess.delete"
                            @selected-action="onActions($event, item)"
                            :actions="['Delete']"
                            :disableActionButtons="
                              item.isDefault ? ['Delete'] : []
                            "
                            :tooltip-action-buttons="
                              item.isDefault ? ['Delete'] : []
                            "
                            :tooltip-message="
                              item.isDefault
                                ? 'This document cannot be deleted because its a default document'
                                : ''
                            "
                            :access-rights="havingAccess"
                            :is-present-tooltip="false"
                            iconColor="grey"
                          ></ActionMenu>
                          <div v-else>-</div>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewDocumentSubtype
                    :landed-form-name="landedFormName"
                    :selectedItem="selectedItem"
                    :isEdit="isEdit"
                    :access-rights="formAccess"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  />
                </v-col>

                <v-col
                  :cols="originalList.length === 0 ? 12 : 7"
                  v-if="showAddEditForm && windowWidth >= 1264"
                >
                  <AddEditDocumentSubtype
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :landed-form-name="landedFormName"
                    @close-form="closeAllForms()"
                    @edit-updated="
                      refetchList('Document subtype updated successfully')
                    "
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="3000"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert"
            >Close</v-btn
          >
        </div>
      </template>
    </AppSnackBar>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditDocumentSubtype
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :landed-form-name="landedFormName"
        :editFormData="selectedItem"
        @close-form="closeAllForms()"
        @edit-updated="refetchList('Document subtype updated successfully')"
      />
      <ViewDocumentSubtype
        v-if="showViewForm"
        :landed-form-name="landedFormName"
        :selectedItem="selectedItem"
        :isEdit="isEdit"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      />
    </v-dialog>
    <FilePreviewModal
      v-if="openModal"
      :fileName="showFile"
      folderName="Employee Document Download"
      fileRetrieveType="documents"
      @close-preview-modal="(openModal = false), (showFile = '')"
    ></FilePreviewModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewDocumentSubtype = defineAsyncComponent(() =>
  import("./ViewDocumentSubtype.vue")
);
const AddEditDocumentSubtype = defineAsyncComponent(() =>
  import("./AddEditDocumentSubtype.vue")
);
const DocumentSubTypeFilter = defineAsyncComponent(() =>
  import("./DocumentSubTypeFilter.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
import { checkNullValue } from "@/helper.js";
// Queries
import {
  LIST_DOCUMENT_SUBTYPE,
  DELETE_DOCUMENT_SUBTYPE,
} from "@/graphql/corehr/documentSubtypeQueries";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "DocumentSubtype",
  components: {
    EmployeeDefaultFilterMenu,
    AddEditDocumentSubtype,
    ViewDocumentSubtype,
    DocumentSubTypeFilter,
    NotesCard,
    ActionMenu,
    FilePreviewModal,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // list
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    // add/update
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    // view
    selectedItem: null,
    showViewForm: false,
    // tab
    currentTabItem: "",
    isFilterApplied: false,
    openMoreMenu: false,
    validationMessages: [],
    showValidationAlert: false,
    openModal: false,
    showFile: "",
  }),
  computed: {
    landedFormName() {
      return this.$t("coreHr.documentSubtype");
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    empDataManagementTabs() {
      return this.$store.getters.empDataManagementTabs;
    },
    formAccess() {
      let formAccessRights = this.accessRights(177);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accreditationFormName() {
      return (
        this.accessRights(351)?.customFormName ||
        "Accreditation Category and Type"
      );
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.empDataManagementTabs;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access of formAccess) {
          if (access.havingAccess || access.formId === 177) {
            formAccessArray.push(this.$t(access.displayFormName));
          }
        }
        if (formAccessArray.includes("Accreditation Category and Type")) {
          const index = formAccessArray.indexOf(
            "Accreditation Category and Type"
          );
          formAccessArray[index] = this.accreditationFormName;
        }
        return formAccessArray;
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    havingAccess() {
      let access = {};
      access["delete"] = this.formAccess.delete;
      return access;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: this.$t("coreHr.documentSubtype"),
            align: "start",
            key: "documentSubType",
          },
          {
            title: this.$t("coreHr.documentType"),
            key: "documentType",
          },
          {
            title: this.$t("coreHr.documentCategory"),
            key: "categoryFields",
          },
        ];
      } else {
        return [
          {
            title: this.$t("coreHr.documentSubtype"),
            align: "start",
            key: "documentSubType",
          },
          {
            title: this.$t("coreHr.documentType"),
            key: "documentType",
          },
          {
            title: this.$t("coreHr.documentCategory"),
            key: "categoryFields",
          },
          {
            title: this.$t("coreHr.enforcedInSelfOnboarding"),
            key: "mandatory",
          },
          {
            title: this.$t("coreHr.document"),
            key: "",
            sortable: false,
          },
          {
            title: this.$t("coreHr.actions"),
            key: "actions",
            sortable: false,
          },
        ];
      }
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = this.$t("coreHr.noDocumentSubtypes");
      }
      return msgText;
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    moreActions() {
      return [{ key: this.$t("coreHr.export"), icon: "fas fa-file-export" }];
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    onTabChange(tab) {
      mixpanel.track("Document subtype form tab changed");
      if (tab !== this.landedFormName) {
        if (tab === this.accreditationFormName) {
          tab = "Accreditation Category and Type";
        }
        const { formAccess } = this.empDataManagementTabs;
        let clickedForm = formAccess.find(
          (form) => this.$t(form.displayFormName) === tab
        );
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },

    retrieveResumeDetails(fileName) {
      let vm = this;
      vm.showFile = fileName;
      vm.openModal = true;
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },

    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = this.originalList;
    },

    applyFilter(filterObj) {
      let filteredList = this.originalList;
      this.isFilterApplied = true;
      if (filterObj?.documentCategory?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filterObj.documentCategory.includes(item.categoryId);
        });
      }
      if (filterObj?.documentType?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filterObj.documentType.includes(item.documentTypeId);
        });
      }
      if (filterObj?.tags?.length > 0) {
        filteredList = filteredList.filter((item) => {
          let iTags = item.Group_Ids
            ? item.Group_Ids.split(",").map(Number)
            : [];
          for (let i of iTags) {
            if (filterObj.tags.includes(i)) {
              return true;
            }
          }
          return false;
        });
      }
      if (filterObj.mandatory !== "") {
        filteredList = filteredList.filter((item) => {
          return item.mandatory == filterObj.mandatory;
        });
      }
      if (filterObj.onlyForEmail !== "") {
        filteredList = filteredList.filter((item) => {
          return item.onlyForEmail == filterObj.onlyForEmail;
        });
      }
      if (filterObj?.emailTemplate?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return item.emailTemplates == filterObj.emailTemplate;
        });
      }
      this.itemList = filteredList;
    },

    // resetFilter() {
    //   this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    //   this.itemList = this.originalList;
    // },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType.key === this.$t("coreHr.export")) {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: this.$t("coreHr.documentSubtype"),
          key: "documentSubType",
        },
        {
          header: this.$t("coreHr.documentType"),
          key: "documentType",
        },
        {
          header: this.$t("coreHr.documentCategory"),
          key: "categoryFields",
        },
        {
          header: this.$t("coreHr.enforcedInSelfOnboarding"),
          key: "mandatory",
        },
        {
          header: this.$t("coreHr.allowedForEmailCommunication"),
          key: "onlyForEmail",
        },
        {
          header: this.$t("coreHr.emailTemplates"),
          key: "emailTemplates",
        },
        {
          header: this.$t("coreHr.documentEnforcementGroup"),
          key: "Group_Names",
        },
        {
          header: this.$t("coreHr.instructionForPresentingDocument"),
          key: "instruction",
        },
        {
          header: this.$t("coreHr.addedOn"),
          key: "addedOn",
        },
        {
          header: this.$t("coreHr.addedBy"),
          key: "addedBy",
        },
        {
          header: this.$t("coreHr.updatedOn"),
          key: "updatedOn",
        },
        {
          header: this.$t("coreHr.updatedBy"),
          key: "updatedBy",
        },
      ];
      let documentSubtype = this.itemList;
      let exportOptions = {
        fileExportData: documentSubtype,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },

    openEditForm() {
      mixpanel.track("Document subtype edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openViewForm(item) {
      mixpanel.track("Document Subtype view form opened");
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    onAddDocumentSubType() {
      mixpanel.track("Document Subtype add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      mixpanel.track("Document Subtype all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      this.$apollo
        .query({
          query: LIST_DOCUMENT_SUBTYPE,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.listDocumentSubTypeDetails &&
            data.listDocumentSubTypeDetails.data &&
            data.listDocumentSubTypeDetails.data.length > 0 &&
            !data.listDocumentSubTypeDetails.errorCode
          ) {
            vm.itemList = [...data.listDocumentSubTypeDetails.data];
            vm.originalList = [...data.listDocumentSubTypeDetails.data];
            vm.listLoading = false;
            mixpanel.track("Document subtype list retrieved");
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    deleteSubtype(subtype) {
      let vm = this;
      vm.isLoading = true;
      vm.validationMessages = [];
      vm.showValidationAlert = false;
      vm.$apollo
        .mutate({
          mutation: DELETE_DOCUMENT_SUBTYPE,
          variables: { documentSubTypeId: subtype.documentSubTypeId },
          client: "apolloClientBB",
          fetchPolicy: "no-cache",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: this.$t("coreHr.documentSubtypeDeleted"),
          };
          vm.showAlert(snackbarData);
          vm.refetchList();
        })
        .catch((err) => {
          this.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      mixpanel.track("Document Subtype error in delete API");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "deleting",
          form: this.landedFormName.toLowerCase(),
          isListError: true,
        })
        .then((validationErrors) => {
          this.validationMessages[0] = validationErrors;
          this.showValidationAlert = true;
        });
    },

    handleListError(err = "") {
      mixpanel.track("Document Subtype error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName.toLowerCase(),
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.originalList = [];
      this.closeAllForms();
      this.fetchList();
    },

    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: this.landedFormName.toLowerCase(),
        isListError: false,
      });
      this.refetchList("status-update-failed");
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onActions(action, subtype) {
      if (action == "Delete") {
        this.deleteSubtype(subtype);
      }
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>

<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
