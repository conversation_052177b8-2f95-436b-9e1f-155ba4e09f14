<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row v-if="originalList.length > 0" justify="center">
            <v-col
              cols="12"
              md="9"
              class="d-flex justify-end"
              v-if="!showAddEditForm && !showViewForm"
            >
              <EmployeeDefaultFilterMenu
                class="d-flex justify-end"
                :isFilter="false"
              />
              <AirTicketFilter
                ref="formFilterRef"
                :items="originalList"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList('Department error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="originalList.length === 0"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="The Air Ticket Benefits Policy, aligned with labor regulations in Middle Eastern countries, provides annual air travel entitlements to eligible employees and their dependents. This benefit typically includes one round-trip economy class ticket per year between the employee's work location and their home country, applicable after completing 12 months of continuous service. It covers full-time employees on permanent contracts and may extend to dependents, such as a spouse and children under a specified age. The policy ensures support for employees’ travel needs while promoting work-life balance."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="The benefit is non-transferable, with unused tickets generally not carried forward unless specified in the employment contract. In certain cases, employees may opt for encashment if the ticket is not utilized, subject to company policy and local labor laws. Special conditions apply during resignation, termination, or emergency travel situations, where the entitlement may vary. Employees are advised to review their contracts and consult the HR department for specific details, as provisions may differ across countries in the Middle East."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="formAccess?.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Add New
                    </v-btn>
                    <v-btn
                      color="transparent"
                      rounded="lg"
                      variant="flat"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="itemList.length === 0"
            key="no-filter-screen"
            main-title="There are no configuration matched for the selected filters/searches."
            image-name="common/no-records"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span>Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div>
              <div
                v-if="originalList.length > 0"
                class="d-flex flex-wrap align-center my-3"
                :class="isMobileView ? 'flex-column' : ''"
                style="justify-content: space-between"
              >
                <div
                  class="d-flex align-center flex-wrap"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <v-btn
                    rounded="lg"
                    style="pointer-events: none"
                    variant="flat"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    >Active:
                    <span class="text-green font-weight-bold">{{
                      activeRecords
                    }}</span>
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    style="pointer-events: none"
                    variant="flat"
                    class="ml-2"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    >Inactive:
                    <span class="text-red font-weight-bold">{{
                      inActiveRecords
                    }}</span></v-btn
                  >
                </div>
                <div
                  class="d-flex align-center"
                  :class="isMobileView ? 'justify-center' : 'justify-end'"
                >
                  <v-btn
                    v-if="formAccess?.add"
                    color="primary"
                    variant="elevated"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="onAddForm()"
                  >
                    <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
                    Add New
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    color="transparent"
                    variant="flat"
                    class="mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList('Refetch List')"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                  <v-menu v-model="openMoreMenu" transition="scale-transition">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        variant="plain"
                        class="mt-1 ml-n1 mr-n5"
                        v-bind="props"
                      >
                        <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                        <v-icon v-else>fas fa-caret-up</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="action in moreActions"
                        :key="action"
                        @click="onMoreAction(action)"
                      >
                        <v-hover>
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                              ><v-icon size="15" class="pr-2">{{
                                action.icon
                              }}</v-icon
                              >{{ action.key }}</v-list-item-title
                            >
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>

              <v-row>
                <v-col v-if="originalList.length > 0" cols="12" class="mb-12">
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Destination City
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Air_Ticket_Setting_Id ===
                                  item.Air_Ticket_Setting_Id
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Destination_City"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  style="max-width: 200px"
                                  v-bind="
                                    item.Destination_City.length > 20
                                      ? props
                                      : ''
                                  "
                                >
                                  {{ checkNullValue(item.Destination_City) }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Destination Country
                          </div>
                          <v-tooltip
                            :text="item.Destination_Country"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Destination_Country.length > 20
                                    ? props
                                    : ''
                                "
                                style="max-width: 200px"
                              >
                                {{ checkNullValue(item.Destination_Country) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Air Ticket Category
                          </div>
                          <v-tooltip
                            :text="item.Air_Ticketing_Category"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Air_Ticketing_Category?.length > 20
                                    ? props
                                    : ''
                                "
                                style="max-width: 200px"
                              >
                                {{
                                  checkNullValue(item.Air_Ticketing_Category)
                                }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <!-- Large View - Air Fare Entitlement for Infant -->
                        <td
                          v-if="!isMobileView"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <v-tooltip
                            :text="String(item.Infant_Amount)"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Infant_Amount?.length > 100 ? props : ''
                                "
                                style="max-width: 200px"
                              >
                                {{ checkNullValue(item.Infant_Amount) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <!-- Large View - Air Fare Entitlement for Child -->
                        <td
                          v-if="!isMobileView"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <v-tooltip
                            :text="String(item.Child_Amount)"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Child_Amount?.length > 100 ? props : ''
                                "
                                style="max-width: 200px"
                              >
                                {{ checkNullValue(item.Child_Amount) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <!-- Large View - Air Fare Entitlement for Adult -->
                        <td
                          v-if="!isMobileView"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <v-tooltip
                            :text="String(item.Adult_Amount)"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Adult_Amount?.length > 100 ? props : ''
                                "
                                style="max-width: 200px"
                              >
                                {{ checkNullValue(item.Adult_Amount) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>

                        <!-- Mobile View - Air Fare Entitlement for Infant -->
                        <td
                          v-if="isMobileView"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Air Fare Entitlement for Infant
                            <span v-if="payrollCurrency">
                              (in {{ payrollCurrency }})</span
                            >
                          </div>
                          <v-tooltip
                            :text="String(item.Infant_Amount)"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Infant_Amount?.length > 20 ? props : ''
                                "
                                style="max-width: 250px"
                              >
                                {{ checkNullValue(item.Infant_Amount) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <!-- Mobile View - Air Fare Entitlement for Child -->
                        <td
                          v-if="isMobileView"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Air Fare Entitlement for Child
                            <span v-if="payrollCurrency">
                              (in {{ payrollCurrency }})</span
                            >
                          </div>
                          <v-tooltip
                            :text="String(item.Child_Amount)"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Child_Amount?.length > 20 ? props : ''
                                "
                                style="max-width: 250px"
                              >
                                {{ checkNullValue(item.Child_Amount) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <!-- Mobile View - Air Fare Entitlement for Adult -->
                        <td
                          v-if="isMobileView"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Air Fare Entitlement for Adult
                            <span v-if="payrollCurrency">
                              (in {{ payrollCurrency }})</span
                            >
                          </div>
                          <v-tooltip
                            :text="String(item.Adult_Amount)"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Adult_Amount?.length > 20 ? props : ''
                                "
                                style="max-width: 250px"
                              >
                                {{ checkNullValue(item.Adult_Amount) }}
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'max-width: 125px'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Status
                          </div>
                          <div
                            @click.stop="
                              {
                              }
                            "
                          >
                            <AppToggleButton
                              button-active-text="Active"
                              button-inactive-text="InActive"
                              button-active-color="#7de272"
                              button-inactive-color="red"
                              id-value="gab-analysis-based-on"
                              :current-value="
                                item.Status?.toLowerCase() === 'active'
                                  ? true
                                  : false
                              "
                              :isDisableToggle="!formAccess?.update"
                              :tooltipContent="
                                formAccess.update
                                  ? ''
                                  : `Sorry, you don't have access rights to update the status`
                              "
                              @chosen-value="updateStatus($event, item)"
                            ></AppToggleButton>
                          </div>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-center align-center'
                              : 'pa-2 pl-5'
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex justify-center align-center"
                            style="width: 100%"
                          >
                            Actions
                          </div>
                          <section
                            class="d-flex justify-start align-center"
                            style="width: 100%"
                          >
                            <ActionMenu
                              v-if="itemActions?.length > 0"
                              :accessRights="checkAccess"
                              @selected-action="onActions($event, item)"
                              :actions="itemActions"
                              iconColor="grey"
                            />
                            <div v-else>
                              <p>-</p>
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <AppLoading v-if="isLoading" />
    <ViewAitTicketPolicy
      v-if="showViewForm"
      :selectedItem="selectedItem"
      :access-rights="formAccess"
      :landedFormName="landedFormName"
      @close-form="closeAllForms()"
      @open-edit-form="openEditForm()"
    />
    <AddEditAitTicketPolicy
      v-if="showAddEditForm"
      :isEdit="isEdit"
      :editFormData="selectedItem"
      :landedFormName="landedFormName"
      :originalList="originalList"
      @close-form="closeAllForms()"
      @edit-updated="refetchList()"
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const AddEditAitTicketPolicy = defineAsyncComponent(() =>
  import("./AddEditAitTicketPolicy.vue")
);
const ViewAitTicketPolicy = defineAsyncComponent(() =>
  import("./ViewAitTicketPolicy.vue")
);
const AirTicketFilter = defineAsyncComponent(() =>
  import("./AirTicketFilter.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
// Queries
import {
  RETRIEVE_AIR_TICKET_SETTINGS,
  ADD_UPDATE_AIR_TICKET_SETTINGS,
} from "@/graphql/corehr/payrollDataManagement.js";
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "AirTicketingPolicy",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    ActionMenu,
    AddEditAitTicketPolicy,
    ViewAitTicketPolicy,
    AirTicketFilter,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // list
    isFilterApplied: false,
    listLoading: false,
    isLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    // add/update
    isEdit: false,
    showAddEditForm: false,
    openMoreMenu: false,
    // view
    selectedItem: null,
    showViewForm: false,
    // tab
    currentTabItem: "",
  }),
  computed: {
    landedFormName() {
      let formName = this.accessRights("319");
      if (formName?.customFormName && formName.customFormName !== "") {
        return formName.customFormName;
      } else return "Air Ticketing Policy";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights("319");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    coreHrPayrollDataManagementFormAcess() {
      return this.$store.getters.coreHrPayrollDataManagementFormAcess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.coreHrPayrollDataManagementFormAcess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      let headers = [
        {
          title: "Destination City",
          align: "start",
          key: "Destination_City",
        },
        {
          title: "Destination Country",
          key: "Destination_Country",
        },
        {
          title: "Air Ticket Category",
          key: "Air_Ticketing_Category",
        },
      ];
      if (!this.isMobileView)
        headers.push({
          title: `Air Fare Entitlement${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          align: "center",
          children: [
            { title: "Infant", value: "Infant_Amount", sortable: true },
            { title: "Child", value: "Child_Amount", sortable: true },
            { title: "Adult", value: "Adult_Amount", sortable: true },
          ],
        });
      else
        headers.push(
          {
            title: `Air Fare Entitlement for Infant${
              this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
            }`,
            key: "Infant_Amount",
          },
          {
            title: `Air Fare Entitlement for Child${
              this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
            }`,
            key: "Child_Amount",
          },
          {
            title: `Air Fare Entitlement for Adult${
              this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
            }`,
            key: "Adult_Amount",
          }
        );
      headers.push(
        {
          title: "Status",
          key: "Status",
        },
        {
          title: "Actions",
          key: "actions",
          sortable: false,
        }
      );
      return headers;
    },
    activeRecords() {
      let empList = this.originalList.filter(
        (el) => el.Status?.toLowerCase() === "active"
      );
      return empList.length;
    },
    inActiveRecords() {
      let empList = this.originalList.filter(
        (el) => el.Status?.toLowerCase() === "inactive"
      );
      return empList.length;
    },

    isSmallTable() {
      return this.showAddEditForm || this.showViewForm;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    moreActions() {
      let moreAction = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return moreAction;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    itemActions() {
      if (this.formAccess?.update) return ["Edit"];
      else return [];
    },
    checkAccess() {
      let havingAccess = {};
      havingAccess["update"] = this.formAccess?.update ? 1 : 0;
      return havingAccess;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    onActions(action, item) {
      this.selectedItem = item;
      if (action?.toLowerCase() === "edit") {
        this.isEdit = true;
        this.showAddEditForm = true;
      }
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.coreHrPayrollDataManagementFormAcess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/core-hr/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/core-hr/" + clickedForm.url;
        }
      }
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
        this.applyFilter({ Status: "Active" });
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.itemList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
      let filterObj = {
        DestinationCity: "",
        DestinationCountry: "",
        TicketCategory: "",
        InfantAmount: "",
        ChildAmount: "",
        AdultAmount: "",
        Status: "Active",
      };
      this.applyFilter(filterObj);
    },
    applyFilter(filter) {
      let filteredList = this.originalList;
      if (filter && filter.DestinationCity && filter.DestinationCity.length) {
        filteredList = filteredList.filter((item) => {
          return filter.DestinationCity.includes(item.Destination_City);
        });
      }
      if (filter?.DestinationCountry && filter.DestinationCountry.length) {
        filteredList = filteredList.filter((item) => {
          return filter.DestinationCountry.includes(item.Destination_Country);
        });
      }
      if (filter && filter.TicketCategory && filter.TicketCategory.length) {
        filteredList = filteredList.filter((item) => {
          return filter.TicketCategory.includes(item.Air_Ticketing_Category);
        });
      }
      if (filter?.InfantAmount && filter.InfantAmount.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Infant_Amount >= filter.InfantAmount[0] &&
            item.Infant_Amount <= filter.InfantAmount[1]
          );
        });
      }
      if (filter?.ChildAmount && filter.ChildAmount.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Child_Amount >= filter.ChildAmount[0] &&
            item.Child_Amount <= filter.ChildAmount[1]
          );
        });
      }
      if (filter?.AdultAmount && filter.AdultAmount.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Adult_Amount >= filter.AdultAmount[0] &&
            item.Adult_Amount <= filter.AdultAmount[1]
          );
        });
      }
      if (filter && filter.Status && filter.Status.length) {
        filteredList = filteredList.filter((item) => {
          return filter.Status.includes(item.Status);
        });
      }
      this.isFilterApplied = true;
      this.itemList = filteredList;
    },
    onMoreAction(actionType) {
      if (actionType.key === "Export") {
        this.exportReportFile(actionType);
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        Infant_Amount: this.payrollCurrency + " " + el.Infant_Amount,
        Child_Amount: this.payrollCurrency + " " + el.Child_Amount,
        Adult_Amount: this.payrollCurrency + " " + el.Adult_Amount,
        Added_On: el.Added_On ? this.convertUTCToLocal(el.Added_On) : "",
        Updated_On: el.Updated_On ? this.convertUTCToLocal(el.Updated_On) : "",
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: [
          {
            header: "Destination City",
            key: "Destination_City",
          },
          {
            header: "Destination Country",
            key: "Destination_Country",
          },
          {
            header: "Air Ticket Category",
            key: "Air_Ticketing_Category",
          },
          {
            header: "Air Fare Entitlement for Infant",
            key: "Infant_Amount",
          },
          {
            header: "Air Fare Entitlement for Child",
            key: "Child_Amount",
          },
          {
            header: "Air Fare Entitlement for Adult",
            key: "Adult_Amount",
          },
          {
            header: "Status",
            key: "Status",
          },
          { header: "Added On", key: "Added_On" },
          { header: "Added By", key: "Added_By" },
          { header: "Updated On", key: "Updated_On" },
          { header: "Updated By", key: "Updated_By" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    openEditForm() {
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    onAddForm() {
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_AIR_TICKET_SETTINGS,
          client: "apolloClientI",
          variables: {
            formId: 319,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveAirTicketSettings &&
            response.data.retrieveAirTicketSettings.airTicketSettingData &&
            !response.data.retrieveAirTicketSettings.errorCode
          ) {
            const tempData =
              response.data.retrieveAirTicketSettings.airTicketSettingData;
            vm.itemList = tempData;
            vm.originalList = tempData;
            vm.onApplySearch();
            vm.applyFilter({ Status: "Active" });
            vm.listLoading = false;
          } else {
            vm.handleListError(
              response.data.retrieveAirTicketSettings?.errorCode
            );
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.closeAllForms();
      this.fetchList();
    },

    updateStatus(statusVal, item) {
      let vm = this;
      const newStatus = Array.isArray(statusVal) ? statusVal[1] : statusVal;
      const currentStatus = item.Status === "Active";

      if (currentStatus === newStatus) {
        // If the status hasn't changed, do nothing
        return;
      }
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_AIR_TICKET_SETTINGS,
            variables: {
              formId: 319,
              airTicketSettingId: item.Air_Ticket_Setting_Id,
              city: item.Destination_City,
              country: item.Destination_Country,
              airTicketingCategory: item.Air_Ticketing_Category,
              status: statusVal[1] ? "Active" : "InActive",
              infantAmount: item.Infant_Amount,
              childAmount: item.Child_Amount,
              adultAmount: item.Adult_Amount,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.landedFormName + " status updated successfully",
            };
            vm.showAlert(snackbarData);
            vm.refetchList("status-updated");
          })
          .catch((err) => {
            vm.handleAddUpdateError(err);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "add/updating",
        form: this.landedFormName,
        isListError: false,
      });
      this.refetchList("status-update-failed");
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
