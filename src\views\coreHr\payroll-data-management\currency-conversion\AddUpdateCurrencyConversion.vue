<template>
  <v-overlay
    v-if="overlay"
    v-model="overlay"
    :persistent="true"
    @click:outside="onCloseOverlay()"
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card"
        :style="{
          height: windowHeight + 'px',
          width: windowWidth >= 1264 ? '50vw' : '100vw',
        }"
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">
            {{ isEditForm ? "Edit" : "Add" }}
            Currency Conversion
          </div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <!-- Scrollable Content -->
        <v-card-text
          class="overflow-y-auto px-0"
          :style="
            model
              ? { 'max-height': 'calc(90vh - 50px)' }
              : isMobileView
              ? { 'max-height': 'calc(85vh - 180px)' }
              : { 'max-height': 'calc(90vh - 170px)' }
          "
        >
          <v-form ref="currencyConversionForm">
            <v-row class="px-6 pt-6">
              <v-col cols="12" class="px-md-6 mt-2">
                <CustomSelect
                  :items="currencyList"
                  v-model="selectedClaimCurrency"
                  :itemSelected="selectedClaimCurrency"
                  item-title="currencyName"
                  item-value="currencyId"
                  label="Claim Currency"
                  :is-auto-complete="true"
                  :is-required="true"
                  :is-loading="currencyListLoading"
                  @selected-item="isFormDirty = true"
                  :rules="[required('Claim Currency', selectedClaimCurrency)]"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" class="px-md-6 my-2">
                <v-text-field
                  v-model="payrollCurrency"
                  label="Payroll Currency"
                  variant="solo"
                  readonly
                  :rules="[required('Payroll Currency', payrollCurrency)]"
                ></v-text-field>
              </v-col>
              <v-col cols="12" class="px-md-6">
                <span class="v-label mr-2 mt-n2">Conversion</span>
                <div class="d-flex align-center">
                  <span class="mr-0">Manual Conversion</span>
                  <v-switch
                    v-model="model"
                    :false-value="true"
                    :true-value="false"
                    hide-details
                    color="primary"
                    @update:modelValue="isFormDirty = true"
                  ></v-switch>
                  <span class="ml-4">Automatic Exchange Conversion</span>
                </div>
              </v-col>
              <!-- Exchange Rate Fields -->
              <v-col
                cols="12"
                class="px-md-6"
                v-if="model && selectedClaimCurrency && payrollCurrency"
              >
                <div class="d-flex align-center">
                  <!-- First Exchange Rate -->
                  <v-col class="pa-0">
                    <div class="text-subtitle-2 text-medium-emphasis mb-1">
                      {{ currency1 }} → {{ payrollCurrency }}
                    </div>
                    <v-text-field
                      v-model="exchangeRate1"
                      variant="solo"
                      placeholder="Ex: 24.5"
                      density="compact"
                      @update:modelValue="isFormDirty = true"
                      type="number"
                      :rules="[
                        model && selectedClaimCurrency && payrollCurrency
                          ? required('Conversion Rate', exchangeRate1)
                          : true,
                        minMaxNumberValidation(
                          'Conversion Rate',
                          parseFloat(exchangeRate1),
                          0,
                          1000000
                        ),
                        validateDecimal(exchangeRate1),
                      ]"
                    ></v-text-field>
                  </v-col>
                </div>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>

        <!-- Fixed NotesCard and Buttons -->
        <v-card-text class="px-0">
          <NotesCard
            v-if="!model"
            notes="Expense amount will be converted based on the international exchange rate at the time of adding expense."
            class="mt-2"
          ></NotesCard>
          <div class="card-actions-div">
            <v-card-actions class="d-flex align-end">
              <v-sheet class="align-center text-center" style="width: 100%">
                <v-row justify="center">
                  <v-col cols="12" class="d-flex justify-end pr-6">
                    <v-btn
                      rounded="lg"
                      class="mr-6"
                      @click="onCloseOverlay()"
                      elevation="4"
                    >
                      Cancel
                    </v-btn>
                    <v-btn
                      rounded="lg"
                      class="mr-1 primary"
                      @click="submitCurrencyConversionForm()"
                      :disabled="!isFormDirty"
                      variant="elevated"
                    >
                      Submit
                    </v-btn>
                  </v-col>
                </v-row>
              </v-sheet>
            </v-card-actions>
          </div>
        </v-card-text>
      </v-card>
      <AppLoading v-if="listLoading"></AppLoading>
    </template>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onClosePopup()"
  >
  </AppWarningModal>
</template>
<script>
import { defineAsyncComponent } from "vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import { checkNullValue } from "@/helper";
import {
  RETRIEVE_CURRENCY_DROPDOWN,
  ADD_UPDATE_CURRENCY_CONVERSION,
} from "@/graphql/corehr/payrollDataManagement.js";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import moment from "moment";
export default {
  name: "AddUpdateCurrencyConversion",
  emits: ["on-close-add-form", "refetch-list"],
  mixins: [validationRules],
  props: {
    selectedCurrencyConversionData: {
      type: Object,
      default: () => {},
    },
    isEditForm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      listLoading: false,
      overlay: true,
      openConfirmationPopup: false,
      isFormDirty: false,
      currencyList: [],
      selectedClaimCurrency: null,
      currencyListLoading: false,
      model: true,
      currency1: "",
      exchangeRate1: null,
    };
  },
  components: {
    CustomSelect,
    NotesCard,
  },
  watch: {
    selectedClaimCurrency(val) {
      if (val) {
        const record = this.currencyList?.find(
          (item) => item.currencyId === val
        );
        this.currency1 = record ? record.currencyCode : null;
      }
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.getCurrencyList();
    this.preFillCurrencyConversionForm();
  },
  methods: {
    checkNullValue,
    onCloseOverlay() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.onClosePopup();
        this.isFormDirty = false;
      }
    },
    onClosePopup() {
      this.openConfirmationPopup = false;
      this.$emit("on-close-add-form");
      this.isFormDirty = false;
    },
    async submitCurrencyConversionForm() {
      const { valid } = await this.$refs.currencyConversionForm.validate();
      if (valid) {
        this.addUpdateCurrencyConversionForm();
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    preFillCurrencyConversionForm() {
      if (this.isEditForm) {
        this.model =
          this.selectedCurrencyConversionData?.conversionType.toLowerCase() ===
          "manual"
            ? true
            : false;
        this.exchangeRate1 =
          this.selectedCurrencyConversionData?.conversionValue || null;
      }
    },
    cleanString(inputString) {
      return inputString?.trim().replace(/\s+/g, " ");
    },
    formatToDateObject(date) {
      // Check if the date is in DD/MM/YYYY format
      if (moment(date, "DD/MM/YYYY", true).isValid()) {
        return moment(date, "DD/MM/YYYY").toDate();
      }
      // Otherwise, assume it's in YYYY/MM/DD format
      return moment(date, "YYYY/MM/DD").toDate();
    },
    formatToOriginal(date) {
      // Check if the date is in DD/MM/YYYY format
      if (moment(date, "DD/MM/YYYY", true).isValid()) {
        return moment(date, "DD/MM/YYYY").format("YYYY-MM-DD");
      }
      // Otherwise, assume it's in YYYY/MM/DD format
      return moment(date, "YYYY/MM/DD").format("YYYY-MM-DD");
    },
    addUpdateCurrencyConversionForm() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_CURRENCY_CONVERSION,
          variables: {
            conversionId: vm.isEditForm
              ? vm.selectedCurrencyConversionData.conversionId
              : 0,
            claimCurrencyId: vm.selectedClaimCurrency,
            conversionType: vm.model ? "Manual" : "Auto",
            conversionValue: vm.exchangeRate1
              ? parseFloat(vm.exchangeRate1)
              : 0,
            formId: 355,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          if (res && res.data && res.data.addUpdateCurrencyConversion) {
            const { errorCode } = res.data.addUpdateCurrencyConversion;
            if (!errorCode) {
              let snackbarData = {
                isOpen: true,
                message: `Currency Conversion details ${
                  vm.isEditForm ? "updated" : "added"
                } successfully`,
                type: "success",
              };
              vm.showAlert(snackbarData);
              vm.listLoading = false;
              vm.$emit("refetch-list");
            } else {
              vm.handleUpdateError();
            }
          } else {
            vm.handleUpdateError();
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },
    handleUpdateError(err = "") {
      this.listLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEditForm ? "updating" : "adding",
        form: "currency conversion",
        isListError: false,
      });
    },
    async getCurrencyList() {
      let vm = this;
      vm.currencyListLoading = true;
      await vm.$apollo
        .query({
          query: RETRIEVE_CURRENCY_DROPDOWN,
          variables: {
            formId: 355,
            filtered: false,
          },
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveCurrencyDropdown &&
            !response.data.retrieveCurrencyDropdown.errorCode
          ) {
            vm.currencyList =
              response.data.retrieveCurrencyDropdown.currencyList;
            vm.selectedClaimCurrency =
              vm.selectedCurrencyConversionData?.claimCurrencyId || null;
          } else {
            vm.currencyList = [];
          }
          vm.currencyListLoading = false;
        })
        .catch((err) => {
          vm.currencyListLoading = false;
          vm.currencyList = [];
          vm.showAlert({
            isOpen: true,
            type: "warning",
            message: err || "Something went wrong. Please try again.",
          });
        });
    },
    validateDecimal(value) {
      if (!value) return true; // Allow empty values if not required
      const regex = /^\d+(\.\d{1,6})?$/; // Allows up to 6 decimal places
      return regex.test(value)
        ? true
        : "Provide Amount up to 6 decimal places.";
    },
  },
};
</script>
<style scoped>
.overlay-card {
  overflow-y: auto;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
</style>
