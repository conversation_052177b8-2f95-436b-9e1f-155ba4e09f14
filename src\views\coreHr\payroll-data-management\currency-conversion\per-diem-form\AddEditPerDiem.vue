<template>
  <v-overlay
    :model-value="showAddEditForm"
    @click:outside="
      isFormDirty ? (openConfirmationPopup = true) : onCloseOverlay()
    "
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="{
          height: windowHeight + 'px',
          width: !isMobileView ? '50vw' : '100vw',
        }"
      >
        <v-card-title
          :class="
            windowWidth < 770
              ? ' d-flex bg-white justify-end align-center fixed-title'
              : 'd-flex bg-primary justify-space-between align-center fixed-title'
          "
        >
          <div v-if="windowWidth >= 770" class="text-h6 text-medium ps-2">
            {{ isEditForm ? this.$t("common.edit") : this.$t("common.add") }}
            {{ this.$t("coreHr.perDiem") }}
          </div>

          <div v-else class="d-flex align-center">
            <v-btn
              rounded="lg"
              class="mr-3"
              variant="outlined"
              @click="
                isFormDirty
                  ? (openConfirmationPopup = true)
                  : $emit('on-close-add-form')
              "
            >
              {{ this.$t("common.cancel") }}
            </v-btn>
            <v-btn
              rounded="lg"
              class="primary"
              variant="elevated"
              :disabled="isLoading || !isFormDirty"
              @click="submitForm()"
            >
              {{
                isEditForm ? this.$t("common.update") : this.$t("common.save")
              }}
            </v-btn>
          </div>
          <v-btn
            v-if="windowWidth >= 770"
            icon
            class="clsBtn"
            variant="text"
            @click="
              isFormDirty ? (openConfirmationPopup = true) : onCloseOverlay()
            "
          >
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text
          class="card mb-3 px-0"
          style="overflow-y: auto; padding-bottom: 50px"
        >
          <div class="px-5 py-6">
            <v-form ref="perDiemForm">
              <v-row class="mb-2">
                <!-- Configuration Type -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <div class="v-label mr-4">
                    <span>{{ this.$t("coreHr.configurationType") }}</span>
                  </div>
                  <AppToggleButton
                    button-active-text="Country"
                    button-inactive-text="User Defined Location"
                    button-active-color="#7de272"
                    button-inactive-color="red"
                    id-value="gab-analysis-based-on"
                    :current-value="
                      formData.Type_Of_Configuration === 'Country'
                    "
                    @chosen-value="onChangeConfigurationType($event)"
                    @update:model-value="onChangeFieldType($event)"
                  />
                </v-col>
                <!-- Country Selection -->
                <v-col
                  v-if="
                    formData.Type_Of_Configuration?.toLowerCase() === 'country'
                  "
                  cols="12"
                  sm="6"
                  class="px-md-6"
                >
                  <CustomSelect
                    ref="countrySelection"
                    v-model="formData.Country_Code"
                    :items="countryList"
                    itemTitle="Country_Name"
                    itemValue="Country_Code"
                    :loading="countryListLoading"
                    clearable
                    :rules="[
                      required(
                        this.$t('coreHr.country'),
                        formData.Country_Code
                      ),
                    ]"
                    :is-required="true"
                    :label="this.$t('coreHr.country')"
                    :isAutoComplete="true"
                    :itemSelected="formData.Country_Code"
                    @selected-item="onCountryChange($event)"
                  />
                </v-col>
                <!-- Title Field -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <v-text-field
                    ref="titleField"
                    v-model="formData.Per_Diem_Title"
                    :rules="[
                      required(
                        this.$t('coreHr.title'),
                        formData.Per_Diem_Title
                      ),
                      validateWithRulesAndReturnMessages(
                        formData.Per_Diem_Title,
                        'Per_Diem_Title',
                        this.$t('coreHr.title')
                      ),
                    ]"
                    :disabled="formData.Country_Code"
                    variant="solo"
                    clearable
                    @update:model-value="onChangeFieldType($event, 'title')"
                  >
                    <template v-slot:label>
                      {{ this.$t("coreHr.title") }}
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Currency -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <CustomSelect
                    ref="currencySelection"
                    v-model="formData.Currency"
                    :items="currencyList"
                    itemTitle="claimCurrencyCodeName"
                    itemValue="conversionId"
                    sub-text="conversionTypeValue"
                    sub-text-title="Conversion Type"
                    clearable
                    :label="this.$t('coreHr.currency')"
                    :isAutoComplete="true"
                    :itemSelected="formData.Currency"
                    :loading="currencyLoading"
                    @selected-item="onChangeFieldType($event)"
                  />
                </v-col>
                <!-- Per Diem Rate -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <v-text-field
                    ref="perDiemRate"
                    v-model="formData.Per_Diem_Rate"
                    :rules="[
                      required(
                        this.$t('coreHr.perDiemRate'),
                        formData.Per_Diem_Rate
                      ),
                      validateWithRulesAndReturnMessages(
                        formData.Per_Diem_Rate,
                        'Per_Diem_Rate',
                        this.$t('coreHr.perDiemRate'),
                        true
                      ),
                    ]"
                    variant="solo"
                    type="number"
                    @update:model-value="
                      onChangeFieldType($event, 'perDiemRate')
                    "
                  >
                    <template v-slot:label>
                      {{ this.$t("coreHr.perDiemRate") }}
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Expense Type -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <CustomSelect
                    ref="expenseTypeSelection"
                    v-model="formData.Expense_Id"
                    :items="expenseTypeList"
                    itemTitle="Expense_Title"
                    itemValue="Expense_Id"
                    :loading="expenseTypeLoading"
                    clearable
                    :rules="[
                      required(
                        this.$t('coreHr.expenseType'),
                        formData.Expense_Id
                      ),
                    ]"
                    :is-required="true"
                    :label="this.$t('coreHr.expenseType')"
                    :isAutoComplete="true"
                    :itemSelected="formData.Expense_Id"
                    @selected-item="onChangeFieldType($event)"
                  />
                </v-col>
                <!-- Travel_Date -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <div class="v-label mr-4">
                    <span>{{ this.$t("coreHr.includeTravelDate") }}</span>
                  </div>
                  <AppToggleButton
                    button-active-text="Inclusion"
                    button-inactive-text="Exclusion"
                    button-active-color="#7de272"
                    button-inactive-color="red"
                    id-value="gab-analysis-based-on"
                    :current-value="formData.Travel_Date === 'Inclusion'"
                    @chosen-value="onChangeTravelDate($event)"
                    @update:model-value="onChangeFieldType($event)"
                  />
                </v-col>
                <!-- Status -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <div class="v-label mr-4">
                    <span>{{ this.$t("coreHr.status") }}</span>
                  </div>
                  <AppToggleButton
                    button-active-text="Active"
                    button-inactive-text="InActive"
                    button-active-color="#7de272"
                    button-inactive-color="red"
                    id-value="gab-analysis-based-on"
                    :isDisableToggle="!isEditForm"
                    :current-value="formData.Status === 'Active' ? true : false"
                    @chosen-value="onChangeStatus($event)"
                    @update:model-value="onChangeFieldType($event)"
                  />
                </v-col>
                <!-- Description Field -->
                <v-col cols="12" sm="12" class="px-md-6">
                  <v-textarea
                    ref="description"
                    v-model="formData.Description"
                    variant="solo"
                    auto-grow
                    :label="this.$t('coreHr.description')"
                    rows="3"
                    :rules="[
                      validateWithRulesAndReturnMessages(
                        formData.Description,
                        'departmentDescription',
                        this.$t('coreHr.description')
                      ),
                    ]"
                    clearable
                    @update:model-value="
                      onChangeFieldType($event, 'description')
                    "
                  />
                </v-col>
              </v-row>
            </v-form>
          </div>
        </v-card-text>
      </v-card>
      <v-card
        v-if="windowWidth >= 770"
        class="overlay-footer bottom-0 position-fixed w-100"
        elevation="16"
      >
        <div class="d-flex justify-end pa-4">
          <v-btn
            rounded="lg"
            class="mr-6"
            variant="outlined"
            @click="
              isFormDirty
                ? (openConfirmationPopup = true)
                : $emit('on-close-add-form')
            "
          >
            {{ this.$t("common.cancel") }}
          </v-btn>
          <v-btn
            rounded="lg"
            class="primary"
            variant="elevated"
            :disabled="isLoading || !isFormDirty"
            @click="submitForm()"
          >
            {{ isEditForm ? this.$t("common.update") : this.$t("common.save") }}
          </v-btn>
        </div>
      </v-card>
    </template>
  </v-overlay>
  <AppLoading v-if="isLoading" />
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onCloseOverlay()"
  />
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import { LIST_CITIES_NO_AUTH } from "@/graphql/dropDownQueries.js";
import { ADD_UPDATE_PER_DIEM_CONFIGURATION } from "@/graphql/corehr/payrollDataManagement.js";

import { GET_CURRENCY_LIST } from "@/graphql/employee-self-service/reimbursement";
export default {
  name: "AddEditPerDiem",
  components: {
    CustomSelect,
  },
  mixins: [validationRules],
  props: {
    selectedData: {
      type: Object,
      default: () => ({}),
    },
    landedFormName: {
      type: String,
      required: true,
    },
    isEditForm: {
      type: Boolean,
      default: false,
    },
    formId: {
      type: Number,
      required: true,
    },
  },
  emits: ["on-close-add-form", "refetch-list"],
  data() {
    return {
      // Form state
      showAddEditForm: true,
      isFormDirty: false,
      isLoading: false,
      openConfirmationPopup: false,

      // Loading states
      countryListLoading: false,
      expenseTypeLoading: false,
      currencyLoading: false,

      // Form data
      formData: {
        Per_Diem_Config_Id: null,
        Type_Of_Configuration: "Country",
        Travel_Date: "Inclusion",
        Country_Code: null,
        Per_Diem_Title: "",
        Currency: null,
        Expense_Id: "",
        Description: "",
        Per_Diem_Rate: null,
        Status: "Active",
      },

      // Lists
      countryList: [],
      currencyList: [],
      expenseTypeList: [],
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
  },
  watch: {
    selectedData: {
      handler(newData) {
        if (newData && Object.keys(newData).length > 0) {
          this.populateFormData(newData);
        }
      },
      immediate: true,
    },
  },
  mounted() {
    if (this.isEditForm && this.selectedData) {
      this.populateFormData(this.selectedData);
    }
    this.getCurrencyList();
    this.listCountryList();
    this.getDropdownDetails();
  },
  methods: {
    onCloseOverlay() {
      this.showAddEditForm = false;
      this.$emit("on-close-add-form");
    },

    onChangeFieldType() {
      this.isFormDirty = true;
    },

    populateFormData(data) {
      this.formData = {
        Per_Diem_Config_Id: data.Per_Diem_Config_Id || null,
        Type_Of_Configuration: data.Type_Of_Configuration || "Country",
        Travel_Date: data.Travel_Date || "Inclusion",
        Country_Code: data.Country_Code || null,
        Per_Diem_Title: data.Per_Diem_Title || "",
        Expense_Id: data.Expense_Id || "",
        Currency: data.Conversion_Id || null,
        Description: data.Description || "",
        Per_Diem_Rate: data.Per_Diem_Rate || null,
        Status: data.Status || "Active",
      };
    },

    getCountryCodeByName(countryName) {
      if (!countryName) return null;
      const country = this.countryList.find(
        (c) => c.Country_Name === countryName
      );
      return country ? country.Country_Code : null;
    },

    onCountryChange(value) {
      this.formData.Country_Code = value;
      this.isFormDirty = true;

      if (value) {
        const selectedCountry = this.countryList.find(
          (c) => c.Country_Code === value
        );
        if (selectedCountry) {
          this.formData.Per_Diem_Title = `${selectedCountry.Country_Name}`;
        }
      } else {
        this.formData.Per_Diem_Title = "";
      }
    },
    getCurrencyList() {
      let vm = this;
      vm.currencyLoading = true;
      vm.$apollo
        .query({
          query: GET_CURRENCY_LIST,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            formId: this.formId,
            status: "Active",
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listCurrencyConversion &&
            response.data.listCurrencyConversion.data
          ) {
            let codes = response.data.listCurrencyConversion.data;
            vm.currencyList =
              codes?.map((item) => ({
                ...item,
                conversionTypeValue: item.conversionValue
                  ? `${item.conversionType} - ${item.conversionValue}`
                  : item.conversionType || "",
                claimCurrencyCodeName: item.claimCurrencyCode
                  ? `${item.claimCurrencyName} - ${item.claimCurrencyCode}`
                  : item.claimCurrencyName,
              })) || [];
          }
          vm.currencyLoading = false;
        })
        .catch(() => {
          vm.currencyLoading = false;
        });
    },
    listCountryList() {
      let vm = this;
      vm.countryListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES_NO_AUTH,
          client: "apolloClientAS",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCityListWithState &&
            response.data.getCityListWithState.cityDetails
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            vm.countryList = Array.from(
              new Map(
                cityDetails?.map((item) => [item.Country_Code, item])
              ).values()
            );
          }
          vm.countryListLoading = false;
        })
        .catch(() => {
          vm.countryListLoading = false;
        });
    },
    onChangeConfigurationType(value) {
      this.formData.Country_Code = null;
      this.formData.Per_Diem_Title = "";
      this.formData.Type_Of_Configuration = value[1]
        ? "Country"
        : "User Defined Location";
      this.isFormDirty = true;
    },
    onChangeTravelDate(value) {
      this.isFormDirty = true;
      this.formData.Travel_Date = value[1] ? "Inclusion" : "Exclusion";
    },
    onChangeStatus(value) {
      this.formData.Status = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },
    getDropdownDetails() {
      let vm = this;
      vm.expenseTypeLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;
      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: vm.formId,
            key: ["expense_head"],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "expense_head") {
                vm.expenseTypeList = item.data || [];
              }
            });
            vm.expenseTypeList =
              vm.expenseTypeList?.filter(
                (item) => item.Config_Mapped?.toLowerCase() === "per diem"
              ) || [];
          } else {
            let err = res.data.retrieveDropdownDetails?.errorCode || "";
            vm.handleGetDropdownDetails(err);
          }
          vm.expenseTypeLoading = false;
        })
        .catch((err) => {
          vm.expenseTypeLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "dropdown details",
        isListError: false,
      });
    },

    async submitForm() {
      const { valid } = await this.$refs.perDiemForm.validate();
      if (!valid) return;

      this.isLoading = true;

      try {
        const variables = {
          formId: this.formId,
          Per_Diem_Config_Id: this.isEditForm
            ? this.formData.Per_Diem_Config_Id
            : null,
          Type_Of_Configuration: this.formData.Type_Of_Configuration,
          Country_Code: this.formData.Country_Code,
          Travel_Date: this.formData.Travel_Date,
          Per_Diem_Title: this.formData.Per_Diem_Title,
          Expense_Id: this.formData.Expense_Id
            ? parseInt(this.formData.Expense_Id)
            : null,
          Conversion_Id: this.formData.Currency
            ? parseInt(this.formData.Currency)
            : null,
          Description: this.formData.Description,
          Per_Diem_Rate: parseFloat(this.formData.Per_Diem_Rate),
          Status: this.formData.Status,
        };

        const { data } = await this.$apollo.mutate({
          mutation: ADD_UPDATE_PER_DIEM_CONFIGURATION,
          variables,
          client: "apolloClientBB",
        });

        if (
          data &&
          data.addUpdatePerDiemConfiguration &&
          !data.addUpdatePerDiemConfiguration.errorCode
        ) {
          this.showAlert({
            isOpen: true,
            message:
              data.addUpdatePerDiemConfiguration?.message ||
              "Per Diem configuration added/updated successfully.",
            type: "success",
          });
          this.$emit("refetch-list");
          this.onCloseOverlay();
        } else {
          this.handleAppUpdateErrors(
            data.addUpdatePerDiemConfiguration?.errorCode || ""
          );
        }
      } catch (error) {
        this.handleAppUpdateErrors(error);
      } finally {
        this.isLoading = false;
      }
    },
    handleAppUpdateErrors(error = "") {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: this.$t("common.updating") || "updating",
        form: this.landedFormName,
        isListError: false,
      });
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}

.fixed-title {
  position: sticky;
  top: 0;
  z-index: 1000;
}

.overlay-footer {
  z-index: 1001;
}
</style>
