<template>
  <div>
    <div v-if="formAccess">
      <div v-if="listLoading" class="mt-3">
        <v-skeleton-loader
          ref="skeleton1"
          type="table-heading"
          class="mx-auto"
        ></v-skeleton-loader>
        <div v-for="i in 3" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>

      <AppFetchErrorScreen
        v-else-if="isErrorInList"
        :content="errorContent"
        icon-name="fas fa-redo-alt"
        image-name="common/human-error-image"
        :button-text="this.$t('common.retry')"
        @button-click="refetchPerDiemList()"
      >
      </AppFetchErrorScreen>

      <div v-else-if="!listLoading && originalList?.length == 0">
        <AppFetchErrorScreen key="no-results-screen">
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                <v-col cols="12">
                  <NotesCard
                    :notes="this.$t('coreHr.perDiemNotesCard1')"
                    backgroundColor="transparent"
                    class="mb-2"
                  ></NotesCard>
                  <NotesCard
                    :notes="this.$t('coreHr.perDiemNotesCard2')"
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                </v-col>

                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <div v-if="formAccess && formAccess.add">
                    <v-btn
                      @click="addButtonClicked()"
                      class="px-6 primary mr-2"
                      variant="elevated"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span class="primary">{{
                        this.$t("coreHr.addPerDiem")
                      }}</span>
                    </v-btn>
                  </div>
                  <v-btn
                    color="transparent"
                    variant="flat"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchPerDiemList()"
                  >
                    <v-icon class="pr-1" color="grey">fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
      </div>

      <div v-else-if="itemList?.length == 0 && !listLoading">
        <AppFetchErrorScreen
          image-name="common/no-records"
          :main-title="this.$t('coreHr.noRecordsForFilters')"
        >
          <template #contentSlot>
            <div class="d-flex mb-2 flex-wrap justify-center">
              <v-btn
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click.stop="resetFilter()"
              >
                {{ this.$t("common.resetFilterSearch") }}
              </v-btn>
            </div>
          </template>
        </AppFetchErrorScreen>
      </div>

      <div v-else>
        <div
          v-if="itemList.length > 0"
          class="d-flex flex-wrap align-center my-3"
          :class="isMobileView ? 'flex-column' : ''"
          style="justify-content: flex-end"
        >
          <div
            class="d-flex align-center flex-wrap"
            :class="isMobileView ? 'justify-center' : 'justify-end'"
          >
            <v-btn
              v-if="formAccess.add"
              @click="addButtonClicked()"
              class="primary"
              variant="elevated"
              :size="isMobileView ? 'small' : 'default'"
            >
              <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
              <span class="primary">{{ this.$t("coreHr.addPerDiem") }}</span>
            </v-btn>
            <v-btn
              color="transparent"
              variant="flat"
              rounded="lg"
              :size="isMobileView ? 'small' : 'default'"
              @click="refetchPerDiemList()"
            >
              <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu class="mb-1 mt-1" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" v-bind="props">
                  <v-icon>fas fa-ellipsis-v</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          hover: isHovering,
                        }"
                      >
                        <v-icon size="15" class="pr-2">{{
                          action.icon
                        }}</v-icon>
                        {{ action.key }}
                      </v-list-item-title>
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>

        <v-data-table
          v-if="itemList.length > 0"
          :headers="tableHeaders"
          :items="itemList"
          :items-per-page="50"
          :items-per-page-options="[
            { value: 50, title: '50' },
            { value: 100, title: '100' },
            {
              value: -1,
              title: '$vuetify.dataFooter.itemsPerPageAll',
            },
          ]"
          fixed-header
          :height="
            $store.getters.getTableHeightBasedOnScreenSize(290, itemList)
          "
          class="elevation-1"
          style="box-shadow: none !important"
        >
          <template v-slot:item="{ item, index }">
            <tr
              style="z-index: 200; cursor: pointer"
              class="data-table-tr bg-white cursor-pointer"
              @click="viewPerDiemDetails(item, index)"
              :class="[
                isMobileView ? ' v-data-table__mobile-table-row ma-0 mt-2' : '',
              ]"
            >
              <!-- Configuration Type -->
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : 'pa-2 pl-5 font-weight-small'
                "
                :style="isMobileView ? '' : 'max-width: 200px'"
              >
                <div
                  v-if="isMobileView"
                  :class="
                    isMobileView
                      ? ' font-weight-bold d-flex align-center'
                      : ' font-weight-bold mt-2 d-flex align-center'
                  "
                >
                  {{ this.$t("coreHr.configurationType") }}
                </div>
                <v-tooltip :text="item.Type_Of_Configuration" location="top">
                  <template v-slot:activator="{ props }">
                    <section :class="isMobileView ? '' : 'text-truncate'">
                      <span
                        class="text-primary text-body-2 font-weight-regular"
                        v-bind="props"
                      >
                        {{ checkNullValue(item.Type_Of_Configuration) }}
                      </span>
                    </section>
                  </template>
                </v-tooltip>
              </td>
              <!-- Title -->
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : 'pa-2 pl-5 font-weight-small'
                "
                :style="isMobileView ? '' : 'max-width: 200px'"
              >
                <div
                  v-if="isMobileView"
                  :class="
                    isMobileView
                      ? ' font-weight-bold d-flex align-center'
                      : ' font-weight-bold mt-2 d-flex align-center'
                  "
                >
                  {{ this.$t("coreHr.title") }}
                </div>
                <v-tooltip :text="item.Per_Diem_Title" location="top">
                  <template v-slot:activator="{ props }">
                    <section :class="isMobileView ? '' : 'text-truncate'">
                      <span
                        class="text-body-2 font-weight-regular"
                        v-bind="props"
                      >
                        {{ checkNullValue(item.Per_Diem_Title) }}
                      </span>
                    </section>
                  </template>
                </v-tooltip>
              </td>
              <!-- Country -->
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : 'pa-2 pl-5 font-weight-small'
                "
                :style="isMobileView ? '' : 'max-width: 200px'"
              >
                <div
                  v-if="isMobileView"
                  :class="
                    isMobileView
                      ? ' font-weight-bold d-flex align-center'
                      : ' font-weight-bold mt-2 d-flex align-center'
                  "
                >
                  {{ this.$t("coreHr.country") }}
                </div>
                <v-tooltip :text="item.Country_Name" location="top">
                  <template v-slot:activator="{ props }">
                    <section :class="isMobileView ? '' : 'text-truncate'">
                      <span
                        class="text-body-2 font-weight-regular"
                        v-bind="props"
                      >
                        {{ checkNullValue(item.Country_Name) }}
                      </span>
                    </section>
                  </template>
                </v-tooltip>
              </td>
              <!-- Per Diem Rate -->
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : 'pa-2 pl-5'
                "
              >
                <div
                  v-if="isMobileView"
                  :class="
                    isMobileView
                      ? ' font-weight-bold d-flex align-center'
                      : ' font-weight-bold mt-2 d-flex align-center'
                  "
                >
                  {{ this.$t("coreHr.perDiemRate") }}
                </div>
                <section>
                  <span class="text-body-2 font-weight-regular">
                    {{ item.Country_Code || payrollCurrency }}
                    {{ checkNullValue(item.Per_Diem_Rate) }}
                  </span>
                </section>
              </td>
              <!--  Expense Type -->
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : 'pa-2 pl-5'
                "
              >
                <div
                  v-if="isMobileView"
                  :class="
                    isMobileView
                      ? ' font-weight-bold d-flex align-center'
                      : ' font-weight-bold mt-2 d-flex align-center'
                  "
                >
                  {{ this.$t("coreHr.expenseType") }}
                </div>
                <section>
                  <span class="text-body-2 font-weight-regular">
                    {{ checkNullValue(item.Expense_Title) }}
                  </span>
                </section>
              </td>
              <!-- Status -->
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : 'pa-2 pl-5'
                "
              >
                <div
                  v-if="isMobileView"
                  :class="
                    isMobileView
                      ? ' font-weight-bold d-flex align-center'
                      : ' font-weight-bold mt-2 d-flex align-center'
                  "
                >
                  {{ this.$t("coreHr.status") }}
                </div>
                <section
                  @click.stop="
                    {
                    }
                  "
                >
                  <span class="text-body-2 font-weight-regular">
                    <AppToggleButton
                      button-active-text="Active"
                      button-inactive-text="InActive"
                      button-active-color="#7de272"
                      button-inactive-color="red"
                      id-value="gab-analysis-based-on"
                      :current-value="item.Status === 'Active' ? true : false"
                      :isDisableToggle="!formAccess.update"
                      :tooltipContent="
                        formAccess.update
                          ? ''
                          : `Sorry, you don't have access rights to update the status`
                      "
                      @chosen-value="updateStatus($event, item)"
                    />
                  </span>
                </section>
              </td>
              <!-- Actions -->
              <td
                :class="
                  isMobileView
                    ? 'd-flex justify-space-between align-center'
                    : 'pa-2'
                "
                @click.stop=""
              >
                <div
                  v-if="isMobileView"
                  :class="
                    isMobileView
                      ? ' font-weight-bold d-flex align-center'
                      : ' font-weight-bold mt-2 d-flex align-center'
                  "
                >
                  {{ this.$t("common.actions") }}
                </div>
                <section>
                  <div class="d-flex justify-center">
                    <ActionMenu
                      v-if="getActions(item)?.length"
                      @selected-action="onActions($event, item)"
                      :actions="getActions(item)"
                      :access-rights="checkAccess()"
                      :isPresentTooltip="true"
                      iconColor="grey"
                    ></ActionMenu>
                    <section class="text-body-2 font-weight-medium" v-else>
                      -
                    </section>
                  </div>
                </section>
              </td>
            </tr>
          </template>
        </v-data-table>
      </div>
    </div>
    <AppAccessDenied v-else />

    <!-- Add/Edit Form Modal -->
    <AddEditPerDiem
      v-if="showAddEditForm"
      :selected-data="selectedData"
      :isEditForm="isEditForm"
      :formId="formId"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      @on-close-add-form="closeAllForms()"
      @refetch-list="refetchPerDiemList($event)"
    />

    <!-- View Form Modal -->
    <ViewPerDiem
      v-if="showViewForm"
      :selected-data="selectedData"
      :formId="formId"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      @close-view-details="closeAllForms()"
      @edit-per-diem-record="editPerDiem()"
    />

    <AppLoading v-if="isLoading" />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const AddEditPerDiem = defineAsyncComponent(() =>
  import("./AddEditPerDiem.vue")
);
const ViewPerDiem = defineAsyncComponent(() => import("./ViewPerDiem.vue"));
import { checkNullValue, convertUTCToLocal } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import {
  LIST_PER_DIEM_CONFIGURATION,
  ADD_UPDATE_PER_DIEM_CONFIGURATION,
} from "@/graphql/corehr/payrollDataManagement.js";
export default {
  name: "PerDiemList",
  components: {
    NotesCard,
    ActionMenu,
    AddEditPerDiem,
    ViewPerDiem,
  },
  mixins: [FileExportMixin, validationRules],
  props: {
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    formId: {
      type: Number,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  data: () => ({
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    isLoading: false,
    itemList: [],
    originalList: [],
    showAddEditForm: false,
    showViewForm: false,
    selectedData: null,
    isEditForm: false,
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    moreActions() {
      return [
        {
          key: this.$t("common.export"),
          icon: "fas fa-file-export",
        },
      ];
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    tableHeaders() {
      return [
        {
          title: this.$t("coreHr.configurationType"),
          align: "start",
          key: "Type_Of_Configuration",
        },
        {
          title: this.$t("coreHr.title"),
          key: "Per_Diem_Title",
        },
        { title: this.$t("coreHr.country"), key: "Country_Name" },
        {
          title: this.$t("coreHr.perDiemRate"),
          key: "Per_Diem_Rate",
        },
        {
          title: this.$t("coreHr.expenseType"),
          key: "Expense_Title",
        },
        {
          title: this.$t("coreHr.status"),
          key: "Status",
        },
        {
          title: this.$t("common.actions"),
          key: "action",
          align: "center",
          sortable: false,
        },
      ];
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.fetchList();
  },
  methods: {
    checkNullValue,

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.isErrorInList = false;
      vm.errorContent = "";
      vm.$apollo
        .query({
          query: LIST_PER_DIEM_CONFIGURATION,
          client: "apolloClientAZ",
          variables: {
            formId: this.formId,
          },
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          vm.listLoading = false;
          if (
            data &&
            data.listPerDiemConfiguration &&
            data.listPerDiemConfiguration.perDiemConfigurations &&
            !data.listPerDiemConfiguration.errorCode
          ) {
            const tempData =
              data.listPerDiemConfiguration.perDiemConfigurations;
            vm.itemList = tempData;
            vm.originalList = tempData;
          } else {
            vm.handleListError(data.listPerDiemConfiguration?.error || "");
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    getActions() {
      const optionMenu = [];
      if (this.formAccess?.update) {
        optionMenu.push("Edit");
      }
      return optionMenu;
    },

    checkAccess() {
      const havingAccess = {};
      havingAccess["update"] = this.formAccess?.update ? 1 : 0;
      return havingAccess;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },

    addButtonClicked() {
      this.isEditForm = false;
      this.showAddEditForm = true;
      this.selectedData = null;
    },

    onActions(type, item) {
      this.selectedData = item;
      if (type && type.toLowerCase() === "edit") {
        this.showAddEditForm = true;
        this.isEditForm = true;
        this.showViewForm = false;
      }
    },

    viewPerDiemDetails(item) {
      this.showViewForm = true;
      this.selectedData = item;
    },

    editPerDiem() {
      this.showViewForm = false;
      this.showAddEditForm = true;
      this.isEditForm = true;
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedData = null;
      this.isEditForm = false;
    },

    refetchPerDiemList() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.closeAllForms();
      this.fetchList();
    },

    resetFilter() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = this.originalList;
    },
    updateStatus(statusVal, item) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_PER_DIEM_CONFIGURATION,
          variables: {
            formId: this.formId,
            Per_Diem_Config_Id: item.Per_Diem_Config_Id,
            Type_Of_Configuration: item.Type_Of_Configuration,
            Country_Code: item.Country_Code,
            Travel_Date: item.Travel_Date,
            Per_Diem_Title: item.Per_Diem_Title,
            Expense_Id: item.Expense_Id,
            Conversion_Id: item.Conversion_Id,
            Status: statusVal[1] ? "Active" : "InActive",
            Description: item.Description,
            Per_Diem_Rate: item.Per_Diem_Rate,
          },
          client: "apolloClientBB",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.landedFormName + " status updated successfully",
          };
          vm.showAlert(snackbarData);
          vm.refetchPerDiemList();
        })
        .catch((err) => {
          vm.handleAddUpdateError(err);
        });
    },
    handleAddUpdateError(err = "") {
      this.refetchPerDiemList();
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: this.landedFormName,
        isListError: false,
      });
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onMoreAction(actionType) {
      if (actionType === this.$t("common.export")) {
        this.exportReportFile();
      }
    },

    exportReportFile() {
      // Transform data with timezone conversion for timestamp fields
      let perDiemList = this.originalList.map((item) => ({
        ...item,
        Per_Diem_Rate: item.Country_Code
          ? (item.Country_Code || this.payrollCurrency) +
            " " +
            item.Per_Diem_Rate
          : item.Per_Diem_Rate,
        // Convert timestamp fields to local timezone
        Added_On: item.Added_On ? convertUTCToLocal(item.Added_On) : "",
        Updated_On: item.Updated_On ? convertUTCToLocal(item.Updated_On) : "",
      }));

      let fileName = this.$t("coreHr.perDiem");

      // Complete export headers including all API response fields
      let exportHeaders = [
        {
          header: this.$t("coreHr.configurationType"),
          key: "Type_Of_Configuration",
        },
        { header: this.$t("coreHr.country"), key: "Country_Name" },
        { header: this.$t("coreHr.includeTravelDate"), key: "Travel_Date" },
        { header: this.$t("coreHr.title"), key: "Per_Diem_Title" },
        { header: this.$t("coreHr.perDiemRate"), key: "Per_Diem_Rate" },
        { header: this.$t("coreHr.expenseType"), key: "Expense_Title" },
        { header: this.$t("coreHr.currency"), key: "Currency_Name" },
        { header: this.$t("coreHr.status"), key: "Status" },
        { header: this.$t("coreHr.description"), key: "Description" },
        { header: "Added On", key: "Added_On" },
        { header: "Updated On", key: "Updated_On" },
        { header: "Added By", key: "Added_By_Name" },
        { header: "Updated By", key: "Updated_By_Name" },
      ];

      let exportOptions = {
        fileExportData: perDiemList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
