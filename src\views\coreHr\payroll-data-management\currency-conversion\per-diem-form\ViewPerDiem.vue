<template>
  <div>
    <v-overlay
      :model-value="showViewForm"
      @click:outside="closeEditForm()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:50vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">
              {{ this.$t("coreHr.viewPerDiem") }}
            </div>
            <v-btn icon class="clsBtn" variant="text" @click="closeEditForm()">
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </v-card-title>

          <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
            <div class="d-flex justify-end align-center">
              <v-btn
                v-if="formAccess?.update"
                @click="$emit('edit-per-diem-record')"
                class="mr-3 mt-3 text-primary"
                variant="text"
                rounded="lg"
              >
                <v-icon class="mr-1" size="15">fas fa-edit</v-icon
                >{{ this.$t("common.edit") }}
              </v-btn>
            </div>
            <div class="px-6 py-2">
              <v-row>
                <!-- Configuration Type -->
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ this.$t("coreHr.configurationType") }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedData.Type_Of_Configuration) }}
                  </p>
                </v-col>
                <!-- Country -->
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ this.$t("coreHr.country") }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedData.Country_Name) }}
                  </p>
                </v-col>
                <!-- Per Diem Title -->
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ this.$t("coreHr.title") }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedData.Per_Diem_Title) }}
                  </p>
                </v-col>
                <!-- Currency -->
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ this.$t("coreHr.currency") }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedData.Currency_Name) }}
                  </p>
                </v-col>
                <!-- Per Diem Rate -->
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ this.$t("coreHr.perDiemRate") }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ selectedData.Country_Code || payrollCurrency }}
                    {{ checkNullValue(selectedData.Per_Diem_Rate) }}
                  </p>
                </v-col>
                <!-- Expense Type -->
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ this.$t("coreHr.expenseType") }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedData.Expense_Title) }}
                  </p>
                </v-col>
                <!-- Travel Date -->
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ this.$t("coreHr.includeTravelDate") }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedData.Travel_Date) }}
                  </p>
                </v-col>
                <!-- Status -->
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ this.$t("coreHr.status") }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedData.Status) }}
                  </p>
                </v-col>
                <!-- Description -->
                <v-col cols="12" sm="12" md="12" class="px-md-6 pb-0 my-3">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ this.$t("coreHr.description") }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedData.Description) }}
                  </p>
                </v-col>
              </v-row>
              <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  />
                </v-col>
              </v-row>
            </div>
          </v-card-text>
        </v-card>
      </template>
    </v-overlay>
  </div>
</template>

<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "ViewPerDiem",
  components: {
    MoreDetails,
  },
  props: {
    selectedData: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
  },
  emits: ["close-view-details", "edit-per-diem-record"],
  data: () => ({
    showViewForm: true,
    moreDetailsList: [],
    openMoreDetails: true,
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
  },
  mounted() {
    this.prefillMoreDetails();
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    closeEditForm() {
      this.showViewForm = false;
      this.$emit("close-view-details");
    },

    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.selectedData.Added_On),
        addedByName = this.selectedData.Added_By_Name,
        updatedByName = this.selectedData.Updated_By_Name,
        updatedOn = this.convertUTCToLocal(this.selectedData.Updated_On);
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>

<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
