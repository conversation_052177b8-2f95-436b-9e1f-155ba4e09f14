<template>
  <v-overlay v-model="showOverlay" class="d-flex justify-end" persistent>
    <v-card :height="windowHeight" :width="componentWidth">
      <v-card-title
        class="d-flex justify-space-between align-center bg-primary"
      >
        <div class="text-h6">
          {{ editType ? editType + " " : "Add " }}{{ landedFormName }}
        </div>
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="closeForm()"
          color="white"
        ></v-btn>
      </v-card-title>
      <v-card-text class="add-update-content">
        <v-form ref="form">
          <v-row class="mt-3">
            <v-col cols="12" md="4">
              <CustomSelect
                ref="employeeSelect"
                density="comfortable"
                label="Employee"
                :disabled="editType !== ''"
                :is-auto-complete="true"
                :items="employeeList"
                item-title="employeeData"
                item-value="employeeId"
                sub-text="empStatus"
                :is-required="true"
                :select-properties="{ clearable: true }"
                :rules="[required('Employee', selectedEmployee)]"
                :loading="employeeListLoading"
                :item-selected="selectedEmployee"
                @selected-item="onChangeEmployee($event)"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="4">
              <CustomSelect
                ref="salaryTemplateSelect"
                density="comfortable"
                label="Salary Template"
                :items="templateList"
                :is-auto-complete="true"
                item-title="Template_Name"
                item-value="Template_Id"
                :is-required="true"
                :select-properties="{ clearable: true }"
                :rules="[required('Salary Template', selectedSalaryTemplate)]"
                :loading="salaryTemplateListLoading"
                :item-selected="selectedSalaryTemplate"
                @selected-item="onChangeTemplate($event)"
              ></CustomSelect>
            </v-col>
          </v-row>
          <div v-if="listLoading">
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else>
            <v-row>
              <v-col
                v-if="editType?.toLowerCase() === 'revise'"
                cols="12"
                sm="4"
                class="py-0"
              >
                <CustomSelect
                  ref="reviseTypeSelect"
                  density="comfortable"
                  label="Revise By"
                  :items="['Percentage', 'Amount']"
                  :item-selected="reviseType"
                  @selected-item="reviseType = $event"
                >
                </CustomSelect>
              </v-col>
              <v-col
                v-if="
                  reviseType?.toLowerCase() === 'percentage' &&
                  editType?.toLowerCase() === 'revise'
                "
                cols="12"
                sm="4"
                class="py-0"
              >
                <v-text-field
                  ref="percentageField"
                  variant="solo"
                  density="comfortable"
                  type="number"
                  v-model="percentage"
                  :rules="[required('Percentage', percentage)]"
                  @update:model-value="updatePercentage($event)"
                >
                  <template v-slot:append-inner>
                    <span
                      class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                      style="width: max-content; height: 100%"
                      >% of CTC</span
                    >
                  </template>
                  <template v-slot:label>
                    Percentage
                    <span class="text-red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-else-if="editType?.toLowerCase() === 'revise'"
                cols="12"
                sm="4"
                class="py-0"
              >
                <v-text-field
                  ref="amountField"
                  variant="solo"
                  density="comfortable"
                  type="number"
                  v-model="amount"
                  :rules="[required('Amount', amount)]"
                  @update:model-value="updatePercentage($event)"
                >
                  <template v-slot:label>
                    Amount
                    <span class="text-red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-if="editType?.toLowerCase() === 'revise'"
                cols="12"
                sm="4"
                class="py-0"
              >
                <v-text-field
                  ref="revisedSalaryEffectiveFromField"
                  readonly
                  variant="solo"
                  :rules="[
                    required(
                      'Salary Effective From',
                      formattedRevisedSalaryEffectiveFrom
                    ),
                  ]"
                  density="comfortable"
                  v-model="formattedRevisedSalaryEffectiveFrom"
                >
                  <template v-slot:prepend-inner>
                    <span
                      class="d-flex justify-center align-center bg-grey-lighten-3"
                      style="height: 100%; width: 40px"
                    >
                      <v-icon size="15" color="grey-darken-1"
                        >fas fa-calendar</v-icon
                      >
                    </span>
                  </template>
                  <template v-slot:label>
                    Salary Effective From
                    <span class="text-red">*</span>
                  </template>

                  <v-menu
                    activator="parent"
                    v-model="revisedSalaryEffectiveFromMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <Datepicker
                      v-model="revisedSalaryEffectiveFrom"
                      :inline="true"
                      :format="'MMMM, yyyy'"
                      :disabled-dates="getDisabledDates('effectivefrom')"
                      maximum-view="year"
                      minimum-view="month"
                      :open-date="
                        revisedSalaryEffectiveFrom
                          ? revisedSalaryEffectiveFrom
                          : new Date()
                      "
                      @update:modelValue="onChangeSalaryEffectiveFrom()"
                    />
                  </v-menu>
                </v-text-field>
              </v-col>
              <v-col
                v-if="editType?.toLowerCase() === 'revise'"
                cols="12"
                sm="4"
              >
                <v-text-field
                  ref="payoutMonthField"
                  readonly
                  variant="solo"
                  label="Payout Month"
                  density="comfortable"
                  :rules="[required('Payout Month', formattedPayoutMonth)]"
                  v-model="formattedPayoutMonth"
                >
                  <template v-slot:prepend-inner>
                    <span
                      class="d-flex justify-center align-center bg-grey-lighten-3"
                      style="height: 100%; width: 40px"
                    >
                      <v-icon size="15" color="grey-darken-1"
                        >fas fa-calendar</v-icon
                      >
                    </span>
                  </template>
                  <template v-slot:label>
                    Payout Month
                    <span class="text-red">*</span>
                  </template>

                  <v-menu
                    activator="parent"
                    v-model="payoutMonthMenu"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <Datepicker
                      v-model="payoutMonth"
                      :inline="true"
                      :format="'MMMM, yyyy'"
                      maximum-view="year"
                      minimum-view="month"
                      :disabled-dates="getDisabledDates('payoutmonth')"
                      :open-date="payoutMonth ? payoutMonth : new Date()"
                      @update:modelValue="payoutMonthMenu = false"
                    />
                  </v-menu>
                </v-text-field>
              </v-col>
              <v-col
                v-if="selectedSalaryTemplate || editType !== ''"
                cols="12"
                sm="4"
              >
                <v-tooltip
                  text="Select a 'Revise By' type and input a value in the adjacent field to change this field."
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="annualCTCField"
                      v-bind="editType?.toLowerCase() === 'revise' ? props : {}"
                      v-model="annualCTC"
                      variant="solo"
                      density="comfortable"
                      type="number"
                      :rules="[
                        required('Annual CTC', annualCTC),
                        minAnnualCTC
                          ? minNumberValidationWithMsg(
                              'Annual CTC',
                              annualCTC,
                              minAnnualCTC,
                              `The minimum wage for this employee is set at ${minAnnualCTC}. Please enter a value greater than or equal to ${minAnnualCTC}.`
                            )
                          : true,
                      ]"
                      :readonly="editType?.toLowerCase() === 'revise'"
                      @update:model-value="updateAnnualCTC"
                    >
                      <template v-slot:prepend-inner>
                        <span
                          class="d-flex justify-center align-center bg-grey-lighten-3"
                          style="height: 100%; width: 40px"
                        >
                          {{ payrollCurrency }}
                        </span>
                      </template>
                      <template v-slot:label>
                        Annual CTC
                        <span class="text-red">*</span>
                      </template>
                    </v-text-field>
                  </template>
                </v-tooltip>
              </v-col>
              <v-col
                v-if="selectedSalaryTemplate || editType !== ''"
                cols="12"
                sm="4"
              >
                <v-tooltip
                  text="This is an auto-calculated value. Update the Annual CTC to change it."
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-bind="props"
                      v-model="monthlyCTC"
                      variant="solo"
                      density="comfortable"
                      type="number"
                      :readonly="true"
                      style="pointer-events: none"
                    >
                      <template v-slot:prepend-inner>
                        <span
                          class="d-flex justify-center align-center bg-grey-lighten-3"
                          style="height: 100%; width: 40px"
                        >
                          {{ payrollCurrency }}
                        </span>
                      </template>
                      <template v-slot:label>
                        Monthly CTC
                        <span class="text-red">*</span>
                      </template>
                    </v-text-field>
                  </template>
                </v-tooltip>
              </v-col>
              <v-col
                cols="12"
                sm="4"
                v-if="
                  formId === 207 &&
                  editType?.toLowerCase() !== 'revise' &&
                  selectedSalaryTemplate
                "
              >
                <v-text-field
                  ref="effectiveDateField"
                  v-model="formattedEffectiveForm"
                  density="comfortable"
                  :readonly="true"
                  :rules="[required('Effective From', formattedEffectiveForm)]"
                  variant="solo"
                  style="pointer-events: none"
                >
                  <template v-slot:prepend-inner>
                    <span
                      class="d-flex justify-center align-center bg-grey-lighten-3"
                      style="height: 100%; width: 40px"
                    >
                      <v-icon size="15" color="grey-darken-1"
                        >fas fa-calendar</v-icon
                      >
                    </span>
                  </template>
                  <template v-slot:label>
                    Effective From
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
            </v-row>
            <v-card v-if="selectedSalaryTemplate" class="pa-2 mt-3">
              <v-row>
                <v-col cols="3" class="bg-grey-lighten-4">Components</v-col>
                <v-col class="bg-grey-lighten-4">Calculation Type</v-col>
                <v-col class="bg-grey-lighten-4 text-end">
                  <div class="pr-8">Monthly</div>
                </v-col>
                <v-col class="bg-grey-lighten-4 text-end">
                  <div class="pr-5">Annually</div>
                </v-col>
              </v-row>
              <v-row class="mt-2">
                <v-col class="font-weight-bold text-subtitle-1">
                  Earnings
                </v-col>
              </v-row>
              <v-row class="d-flex align-center mt-0">
                <v-col cols="3" class="text-body-1">
                  {{
                    templateDetails?.allowances?.basicPayArray[0]
                      ?.Allowance_Name || "Basic Pay"
                  }}
                </v-col>
                <v-col>
                  <v-text-field
                    ref="basicPayField"
                    v-if="
                      templateDetails?.BasicPay_Type?.toLowerCase() ===
                      'percentage'
                    "
                    v-model="basicPay"
                    variant="solo"
                    density="comfortable"
                    :rules="[
                      required('Basic Pay', basicPay),
                      twoDecimalPrecisionValidation(basicPay),
                    ]"
                    type="number"
                    @update:model-value="updateBasicPay"
                  >
                    <template v-slot:append-inner>
                      <span
                        v-if="
                          templateDetails?.BasicPay_Type?.toLowerCase() ===
                          'percentage'
                        "
                        class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                        style="width: max-content; height: 100%"
                        >% of CTC</span
                      >
                    </template>
                  </v-text-field>
                  <div v-else>Fixed Amount</div>
                </v-col>
                <v-col class="text-body-1 custom-input-position">
                  <v-text-field
                    ref="monthlyBasicPayField"
                    v-model="monthlyBasicPay"
                    :readonly="
                      templateDetails?.BasicPay_Type?.toLowerCase() ===
                      'percentage'
                    "
                    :style="
                      templateDetails?.BasicPay_Type?.toLowerCase() ===
                      'percentage'
                        ? 'pointer-events: none'
                        : ''
                    "
                    variant="solo"
                    density="comfortable"
                    :rules="[
                      required('Basic Pay', monthlyBasicPay),
                      twoDecimalPrecisionValidation(monthlyBasicPay),
                    ]"
                    type="number"
                    @update:model-value="updateBasicPay"
                  >
                  </v-text-field>
                </v-col>
                <v-col class="text-body-1 text-end">
                  <div class="pr-5">
                    {{
                      monthlyBasicPay ? (monthlyBasicPay * 12).toFixed(2) : 0
                    }}
                  </div>
                </v-col>
              </v-row>
              <div v-if="templateDetails.allowances?.allowanceArray?.length">
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'allowanceArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col cols="3" class="text-body-1 no-padding">
                    {{ innerItems.Allowance_Name }}
                  </v-col>
                  <v-col>
                    <v-text-field
                      :ref="'allowanceAmountField' + index"
                      v-if="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      v-model="innerItems.Percentage"
                      :rules="[
                        twoDecimalPrecisionValidation(innerItems.Percentage),
                      ]"
                      variant="solo"
                      density="comfortable"
                      type="number"
                      @update:model-value="updateAllowanceAmount(innerItems)"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                          style="width: max-content; height: 100%"
                          >% of Basic</span
                        >
                      </template>
                    </v-text-field>
                    <div v-else>Fixed Amount</div>
                  </v-col>
                  <v-col class="text-body-1 custom-input-position">
                    <v-text-field
                      :ref="'monthlyAllowanceAmountField' + index"
                      v-model="innerItems.Amount"
                      :readonly="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      variant="solo"
                      class="text-right"
                      density="comfortable"
                      :rules="[
                        twoDecimalPrecisionValidation(innerItems.Amount),
                      ]"
                      :style="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                          ? 'pointer-events: none'
                          : ''
                      "
                      type="number"
                      @update:model-value="calculateSumOfComponetsMinusCTC()"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col class="text-body-1 text-end">
                    <div class="pr-5">
                      {{
                        innerItems.Amount
                          ? (innerItems.Amount * 12).toFixed(2)
                          : 0
                      }}
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div
                v-if="templateDetails.allowances?.fixedAllowanceArray?.length"
              >
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'fixedAllowanceArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col cols="3" class="text-body-1">
                    {{ innerItems.Allowance_Name }}
                  </v-col>
                  <v-col> Fixed Amount </v-col>
                  <v-col
                    class="text-body-1"
                    :class="anyValueUpdated ? 'text-start' : 'text-end'"
                  >
                    <div :class="anyValueUpdated ? '' : 'pr-8'">
                      {{
                        anyValueUpdated
                          ? "System Calculated"
                          : (fixedAllowanceAmount || 0).toFixed(2)
                      }}
                    </div>
                  </v-col>
                  <v-col
                    class="text-body-1"
                    :class="anyValueUpdated ? 'text-start' : 'text-end'"
                  >
                    <div :class="anyValueUpdated ? '' : 'pr-5'">
                      {{
                        anyValueUpdated
                          ? "System Calculated"
                          : fixedAllowanceAmount
                          ? (fixedAllowanceAmount * 12).toFixed(2)
                          : 0
                      }}
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div
                v-if="templateDetails?.allowances?.reimbursementArray?.length"
              >
                <v-row class="mt-2">
                  <v-col class="font-weight-bold text-subtitle-1 no-padding">
                    Reimbursement
                  </v-col>
                </v-row>
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'reimbursementArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col cols="3" class="text-body-1 no-padding">
                    {{ innerItems.Allowance_Name }}
                  </v-col>
                  <v-col>
                    <v-text-field
                      :ref="'reimbursementAmountField' + index"
                      v-if="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      v-model="innerItems.Percentage"
                      :rules="[
                        twoDecimalPrecisionValidation(innerItems.Percentage),
                      ]"
                      variant="solo"
                      density="comfortable"
                      type="number"
                      @update:model-value="updateAllowanceAmount(innerItems)"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                          style="width: max-content; height: 100%"
                          >% of Basic</span
                        >
                      </template>
                    </v-text-field>
                    <div v-else>Fixed Amount</div>
                  </v-col>
                  <v-col class="text-body-1 custom-input-position">
                    <v-text-field
                      :ref="'monthlyReimbursementAmountField' + index"
                      v-model="innerItems.Amount"
                      :readonly="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        twoDecimalPrecisionValidation(innerItems.Amount),
                      ]"
                      :style="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                          ? 'pointer-events: none'
                          : ''
                      "
                      type="number"
                      @update:model-value="calculateSumOfComponetsMinusCTC"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col class="text-body-1 text-end">
                    <div class="pr-5">
                      {{
                        innerItems.Amount
                          ? (innerItems.Amount * 12).toFixed(2)
                          : 0
                      }}
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div
                v-if="
                  templateDetails?.allowances?.flexiBenefitPlanArray?.length
                "
              >
                <v-row class="mt-2">
                  <v-col class="font-weight-bold text-subtitle-1">
                    Flexi Benefit Plan
                  </v-col>
                </v-row>
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'flexiBenefitPlanArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col cols="3" class="text-body-1 no-padding">
                    {{ innerItems.Allowance_Name }}
                    <div>
                      <span class="text-caption">Max Amount: </span>
                      <span class="text-caption font-weight-medium"
                        >{{ payrollCurrency }}
                        {{ innerItems.FBP_Max_Declaration }}</span
                      >
                      <v-icon
                        size="12"
                        class="ml-2"
                        color="primary"
                        @click="openFBPExpansionPanel(innerItems)"
                        >fas fa-edit</v-icon
                      >
                    </div>
                  </v-col>
                  <v-col>
                    <v-text-field
                      :ref="'flexiAmountField' + index"
                      v-if="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      v-model="innerItems.Percentage"
                      :rules="[
                        twoDecimalPrecisionValidation(innerItems.Percentage),
                      ]"
                      variant="solo"
                      density="comfortable"
                      type="number"
                      @update:model-value="updateAllowanceAmount(innerItems)"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                          style="width: max-content; height: 100%"
                          >% of Basic</span
                        >
                      </template>
                    </v-text-field>
                    <div v-else>
                      <div>Fixed Amount</div>
                      <v-tooltip
                        v-if="
                          innerItems.Restrict_Employee_FBP_Override?.toLowerCase() ===
                          'yes'
                        "
                        text="Employees cannot change the FBP amount. If this FBP is selected, the maximum amount will be considered automatically in the FBP declaration."
                        :open-on-click="true"
                        location="top"
                        max-width="300px"
                      >
                        <template v-slot:activator="{ props }">
                          <div v-bind="props">
                            <v-icon size="10" class="mr-1" color="grey"
                              >fas fa-lock</v-icon
                            >
                            <span
                              class="text-caption text-grey-darken-1 text-decoration-underline"
                              >Restricted Component</span
                            >
                          </div>
                        </template>
                      </v-tooltip>
                    </div>
                  </v-col>
                  <v-col class="text-body-1 custom-input-position">
                    <v-text-field
                      :ref="'monthlyFlexiAmountField' + index"
                      v-model="innerItems.Amount"
                      :readonly="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                      "
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        twoDecimalPrecisionValidation(innerItems.Amount),
                      ]"
                      :style="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                        'percentage'
                          ? 'pointer-events: none'
                          : ''
                      "
                      type="number"
                      @update:model-value="calculateSumOfComponetsMinusCTC()"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col class="text-body-1 text-end">
                    <div class="pr-5">
                      {{
                        innerItems.Amount
                          ? (innerItems.Amount * 12).toFixed(2)
                          : 0
                      }}
                    </div>
                  </v-col>
                  <v-col v-if="innerItems.fbpPanels?.length" cols="12">
                    <v-expansion-panels
                      v-model="innerItems.fbpPanels"
                      class="mt-3"
                    >
                      <v-expansion-panel>
                        <v-expansion-panel-text>
                          <v-btn
                            icon="fas fa-times"
                            variant="text"
                            @click="innerItems.fbpPanels = []"
                            size="x-small"
                            class="position-absolute top-0 right-0"
                            color="grey-lighten-1"
                          ></v-btn>
                          <v-form :ref="`fbp-${innerItems.Allowance_Type_Id}`">
                            <v-row>
                              <v-col cols="12" sm="4">
                                <v-text-field
                                  v-model="innerItems.fbpMaxAmount"
                                  variant="solo"
                                  density="comfortable"
                                  :rules="[
                                    required(
                                      'Max Declaration Amount',
                                      innerItems.fbpMaxAmount
                                    ),
                                    minMaxNumberValidation(
                                      'Max Declaration Amount',
                                      innerItems.fbpMaxAmount,
                                      0,
                                      99999999
                                    ),
                                    twoDecimalPrecisionValidation(
                                      innerItems.fbpMaxAmount
                                    ),
                                  ]"
                                  type="number"
                                  @update:model-value="updateBasicPay"
                                >
                                  <template v-slot:label>
                                    Max Declaration Amount
                                    <span class="text-red">*</span>
                                  </template>
                                </v-text-field>
                              </v-col>
                            </v-row>
                            <div class="d-flex justify-end">
                              <v-btn
                                class="mt-3"
                                rounded="lg"
                                color="primary"
                                size="small"
                                variant="elevated"
                                @click="updateFBP(innerItems)"
                                >Save</v-btn
                              >
                            </div>
                          </v-form>
                        </v-expansion-panel-text>
                      </v-expansion-panel>
                    </v-expansion-panels>
                  </v-col>
                </v-row>
              </div>
              <div v-if="templateDetails?.allowances?.bonusArray?.length">
                <v-row class="mt-2">
                  <v-col class="font-weight-bold text-subtitle-1 no-padding">
                    Bonus
                  </v-col>
                </v-row>
                <v-row
                  v-for="(innerItems, index) in templateDetails.allowances[
                    'bonusArray'
                  ]"
                  :key="index"
                  class="d-flex align-center mt-0"
                >
                  <v-col
                    cols="3"
                    class="text-body-1 no-padding d-flex align-center"
                  >
                    <span class="mr-2">
                      {{ innerItems.Allowance_Name }}
                      <v-tooltip
                        v-if="
                          getBonusText &&
                          (innerItems.Allowance_Type?.toLowerCase() ===
                            'percentage' ||
                            innerItems.Allowance_Type?.toLowerCase() ===
                              'variable')
                        "
                        :text="getBonusText"
                      >
                        <template v-slot:activator="{ props }">
                          <span class="custom-info-icon">
                            <v-icon
                              color="grey-lighten-1"
                              size="10"
                              v-bind="props"
                              >fas fa-info</v-icon
                            >
                          </span>
                        </template>
                      </v-tooltip>
                    </span>
                    <div
                      class="text-caption bg-primary px-1 rounded elevation-2 d-flex align-center justify-center"
                      style="color: white; width: max-content"
                    >
                      {{ innerItems.Period }}
                    </div>
                  </v-col>
                  <v-col>
                    <v-text-field
                      :ref="'bonusAmountField' + index"
                      v-if="
                        innerItems?.Allowance_Type?.toLowerCase() ===
                          'percentage' ||
                        innerItems?.Allowance_Type?.toLowerCase() === 'variable'
                      "
                      v-model="innerItems.Percentage"
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        twoDecimalPrecisionValidation(innerItems.Percentage),
                      ]"
                      :hint="
                        innerItems.Amount
                          ? `${innerItems.Amount}(${innerItems.Period})`
                          : ''
                      "
                      :persistent-hint="true"
                      type="number"
                      @update:model-value="updateAllowanceAmount(innerItems)"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3"
                          style="width: max-content; height: 100%"
                          >%</span
                        >
                      </template>
                    </v-text-field>
                    <v-text-field
                      v-else
                      :ref="'bonusAmountField' + index"
                      v-model="innerItems.Amount"
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        twoDecimalPrecisionValidation(innerItems.Amount),
                      ]"
                      type="number"
                      @update:model-value="calculateSumOfComponetsMinusCTC"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                          style="width: max-content; height: 100%"
                          >Amount</span
                        >
                      </template>
                    </v-text-field>
                  </v-col>
                  <v-col
                    class="text-body-1 custom-input-position"
                    :class="anyValueUpdated ? 'text-start' : 'text-end'"
                  >
                    <div :class="anyValueUpdated ? '' : 'pr-8'">
                      {{
                        anyValueUpdated
                          ? "System Calculated"
                          : (
                              innerItems.Amount /
                              periodMap[innerItems.Period][0]
                            ).toFixed(2) || 0
                      }}
                    </div>
                  </v-col>
                  <v-col class="text-body-1 text-end">
                    <div class="pr-5">
                      {{
                        anyValueUpdated
                          ? "System Calculated"
                          : (
                              innerItems.Amount *
                              periodMap[innerItems.Period][1]
                            ).toFixed(2) || 0
                      }}
                    </div>
                  </v-col>
                </v-row>
              </div>
              <div v-if="templateDetails.retirals?.length">
                <v-row class="mt-2">
                  <v-col class="font-weight-bold text-subtitle-1 no-padding">
                    Retiral
                  </v-col>
                </v-row>
                <div
                  v-for="(innerItems, index) in templateDetails.retirals"
                  :key="index"
                >
                  <v-row class="d-flex align-center mt-0">
                    <v-col cols="3" class="text-body-1 d-flex align-center">
                      <span class="mr-2 d-flex align-center">
                        {{
                          innerItems.Retirals_Name?.toLowerCase() ===
                          "insurance"
                            ? innerItems.Insurance_Name
                            : getCustomFormName(innerItems.Form_Id)
                            ? getCustomFormName(innerItems.Form_Id)
                            : innerItems.Retirals_Name
                        }}

                        <v-tooltip
                          v-if="
                            getCustomText(innerItems.Allowance_Ids) &&
                            (innerItems.Retirals_Type?.toLowerCase() ===
                              'percentage' ||
                              innerItems.Retirals_Type?.toLowerCase() ===
                                'variable' ||
                              innerItems.Form_Id === 110)
                          "
                          :text="getCustomText(innerItems.Allowance_Ids)"
                        >
                          <template v-slot:activator="{ props }">
                            <span class="custom-info-icon ml-1">
                              <v-icon
                                color="grey-lighten-1"
                                size="10"
                                v-bind="props"
                                >fas fa-info</v-icon
                              >
                            </span>
                          </template>
                        </v-tooltip>
                        <v-icon
                          v-if="
                            (innerItems.Retirals_Name?.toLowerCase() ===
                              'insurance' &&
                              innerItems.Override_Insurance_Contribution_At_Employee_Level?.toLowerCase() ===
                                'yes' &&
                              innerItems.Slab_Wise?.toLowerCase() !== 'yes') ||
                            (innerItems.Form_Id == 52 &&
                              innerItems.Override_PF_Contribution_Rate_At_Employee_Level?.toLowerCase() ===
                                'yes' &&
                              innerItems.Slab_Wise?.toLowerCase() !== 'yes')
                          "
                          size="12"
                          class="ml-2"
                          color="primary"
                          @click="openExpansionPanel(innerItems)"
                          >fas fa-edit</v-icon
                        >
                      </span>
                      <div
                        v-if="
                          innerItems.Insurance_Type?.toLowerCase() === 'fixed'
                        "
                        class="text-caption bg-primary px-1 rounded elevation-2 d-flex align-center justify-center"
                        style="color: white; width: max-content"
                      >
                        {{ innerItems.Payment_Frequency }}
                      </div>
                    </v-col>
                    <v-col>
                      <div
                        v-if="
                          innerItems.Insurance_Type?.toLowerCase() === 'fixed'
                        "
                      >
                        {{
                          innerItems.Employer_Share_Amount &&
                          innerItems.Payment_Frequency
                            ? `${innerItems.Employer_Share_Amount} (${innerItems.Payment_Frequency})`
                            : "Fixed Amount"
                        }}
                      </div>
                      <div
                        v-else-if="
                          innerItems.Slab_Wise?.toLowerCase() === 'yes'
                        "
                      >
                        Slab Wise
                      </div>
                      <div v-else-if="innerItems.Form_Id === 52">
                        {{
                          innerItems.PF_Employer_Contribution == "Actual"
                            ? `${innerItems.Employer_Share_Percentage}% of Actual PF Wage`
                            : "Restrict Contribution to 15000 of PF Wage"
                        }}
                      </div>
                      <div
                        v-else-if="
                          innerItems?.Retirals_Type?.toLowerCase() ===
                            'percentage' ||
                          innerItems?.Retirals_Type?.toLowerCase() ===
                            'variable'
                        "
                      >
                        {{ innerItems.Percentage + "%" }}
                      </div>
                      <div v-else>Fixed Amount</div>
                    </v-col>
                    <v-col
                      class="text-body-1"
                      :class="anyValueUpdated ? 'text-start' : 'text-end'"
                    >
                      <div
                        v-if="
                          innerItems.Insurance_Type?.toLowerCase() === 'fixed'
                        "
                      >
                        <div :class="anyValueUpdated ? '' : 'pr-8'">
                          {{
                            anyValueUpdated
                              ? "System Calculated"
                              : (
                                  innerItems.Employer_Share_Amount /
                                  periodMap[innerItems.Payment_Frequency][0]
                                ).toFixed(2) || 0
                          }}
                        </div>
                      </div>
                      <div v-else-if="innerItems.Form_Id === 110">
                        <div :class="anyValueUpdated ? '' : 'pr-8'">
                          {{
                            anyValueUpdated
                              ? "System Calculated"
                              : (innerItems.Amount / 12)?.toFixed(2) || 0
                          }}
                        </div>
                      </div>
                      <div v-else :class="anyValueUpdated ? '' : 'pr-8'">
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : innerItems.Amount?.toFixed(2) || 0
                        }}
                      </div>
                    </v-col>
                    <v-col
                      class="text-body-1"
                      :class="anyValueUpdated ? 'text-start' : 'text-end'"
                    >
                      <div
                        v-if="
                          innerItems.Insurance_Type?.toLowerCase() === 'fixed'
                        "
                        :class="anyValueUpdated ? '' : 'pr-5'"
                      >
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : (
                                innerItems.Employer_Share_Amount *
                                periodMap[innerItems.Payment_Frequency][1]
                              ).toFixed(2) || 0
                        }}
                      </div>
                      <div v-else-if="innerItems.Form_Id === 110">
                        <div :class="anyValueUpdated ? '' : 'pr-5'">
                          {{
                            anyValueUpdated
                              ? "System Calculated"
                              : innerItems.Amount?.toFixed(2) || 0
                          }}
                        </div>
                      </div>
                      <div v-else :class="anyValueUpdated ? '' : 'pr-5'">
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : (innerItems.Amount * 12).toFixed(2) || 0
                        }}
                      </div>
                    </v-col>
                  </v-row>
                  <v-expansion-panels
                    v-if="innerItems.expansionPanel?.length"
                    v-model="innerItems.expansionPanel"
                    class="mt-3"
                  >
                    <v-expansion-panel>
                      <v-expansion-panel-text>
                        <v-btn
                          icon="fas fa-times"
                          variant="text"
                          @click="innerItems.expansionPanel = []"
                          size="x-small"
                          class="position-absolute top-0 right-0"
                          color="grey-lighten-1"
                        ></v-btn>
                        <v-form
                          :ref="`retirals-${
                            innerItems.Retirals_Id || innerItems.Form_Id
                          }`"
                        >
                          <v-row>
                            <v-col
                              v-if="innerItems.Form_Id !== 52"
                              cols="12"
                              sm="4"
                            >
                              <v-text-field
                                v-model="innerItems.tempEmployeeValue"
                                :label="
                                  innerItems.tempType === 'Percentage'
                                    ? 'Employee Share Percentage'
                                    : 'Employee Share Amount'
                                "
                                :rules="[
                                  minMaxNumberValidation(
                                    'Employee Share',
                                    innerItems.tempEmployeeValue,
                                    0,
                                    innerItems.tempType === 'Percentage'
                                      ? 100
                                      : 100000
                                  ),
                                ]"
                                variant="solo"
                                density="comfortable"
                                type="number"
                              ></v-text-field>
                            </v-col>
                            <v-col
                              v-if="innerItems.Form_Id !== 52"
                              cols="12"
                              sm="4"
                            >
                              <v-text-field
                                v-model="innerItems.tempEmployerValue"
                                :label="
                                  innerItems.tempType === 'Percentage'
                                    ? 'Employer Share Percentage'
                                    : 'Employer Share Amount'
                                "
                                :rules="[
                                  minMaxNumberValidation(
                                    'Employer Share',
                                    innerItems.tempEmployerValue,
                                    0,
                                    innerItems.tempType === 'Percentage'
                                      ? 100
                                      : 100000
                                  ),
                                ]"
                                variant="solo"
                                density="comfortable"
                                type="number"
                              ></v-text-field>
                            </v-col>
                            <v-col
                              v-if="innerItems.Form_Id == 52"
                              cols="12"
                              sm="4"
                            >
                              <CustomSelect
                                :item-selected="innerItems.tempEmployeeType"
                                :items="[
                                  {
                                    title: `${innerItems.Employee_Share_Percentage}% of Actual PF Wage`,
                                    value: 'actualWage',
                                  },
                                  {
                                    title: `Restrict to 15000 of PF Wage`,
                                    value: 'restrict',
                                  },
                                ]"
                                item-title="title"
                                item-value="value"
                                label="Employee Contribution Rate"
                                variant="solo"
                                density="comfortable"
                                :disabled="
                                  innerItems.disableEmployeeContribution
                                    ? true
                                    : false
                                "
                                @selected-item="
                                  innerItems.tempEmployeeType = $event
                                "
                              ></CustomSelect>
                            </v-col>
                            <v-col
                              v-if="innerItems.Form_Id === 52"
                              cols="12"
                              sm="4"
                            >
                              <CustomSelect
                                :item-selected="innerItems.tempEmployerType"
                                :items="[
                                  {
                                    title: `${innerItems.Employer_Share_Percentage}% of Actual PF Wage`,
                                    value: 'actualWage',
                                  },
                                  {
                                    title: `Restrict to 15000 of PF Wage`,
                                    value: 'restrict',
                                  },
                                ]"
                                item-title="title"
                                item-value="value"
                                label="Employer Contribution Rate"
                                variant="solo"
                                density="comfortable"
                                @selected-item="
                                  employerContributionChanged(
                                    innerItems,
                                    $event
                                  )
                                "
                              ></CustomSelect>
                            </v-col>
                          </v-row>
                        </v-form>
                        <div class="d-flex justify-end">
                          <v-btn
                            class="mt-3"
                            rounded="lg"
                            color="primary"
                            size="small"
                            variant="elevated"
                            @click="updateRetirals(innerItems)"
                            >Save</v-btn
                          >
                        </div>
                      </v-expansion-panel-text>
                    </v-expansion-panel>
                  </v-expansion-panels>
                </div>
              </div>
            </v-card>
            <v-card
              v-if="anyValueUpdated"
              class="pa-2 d-flex align-center mt-3"
            >
              <v-row>
                <v-col class="d-flex align-center">
                  <v-icon color="blue" class="mr-4">fas fa-info-circle</v-icon>
                  <div>
                    <div>
                      <span class="text-subtitle-1 font-weight-medium mr-2"
                        >System Calculated Components' Total</span
                      >
                      <span
                        class="text-blue text-subtitle-1 font-weight-medium cursor-pointer"
                        v-if="sumOfComponetsMinusCTC >= 0 && !componentsLoading"
                        @click="getSystemCalculatedComponets()"
                        >(Preview)</span
                      >
                      <span v-else-if="componentsLoading"
                        >Calculating
                        <v-progress-circular
                          size="15"
                          color="primary"
                          indeterminate
                        ></v-progress-circular
                      ></span>
                    </div>
                    <div
                      class="text-caption text-grey-darken-1"
                      v-if="sumOfComponetsMinusCTC < 0"
                    >
                      Amount must be greater than zero. Adjust the CTC or any of
                      the component's amount.
                    </div>
                  </div>
                </v-col>
                <v-col
                  cols="3"
                  :class="sumOfComponetsMinusCTC < 0 ? 'text-red' : ''"
                  class=""
                >
                  {{ sumOfComponetsMinusCTC?.toFixed(2) }}
                </v-col>
                <v-col
                  cols="3"
                  :class="sumOfComponetsMinusCTC < 0 ? 'text-red' : ''"
                  class=""
                >
                  {{ (sumOfComponetsMinusCTC * 12)?.toFixed(2) }}
                </v-col>
              </v-row>
            </v-card>
            <v-card v-if="selectedSalaryTemplate" class="pa-2 mt-3">
              <v-row>
                <v-col cols="6" class="bg-grey-lighten-4 text-body-1"
                  >Cost to Company (CTC)</v-col
                >
                <v-col class="bg-grey-lighten-4 text-end">
                  <div class="pr-8 text-body-1">
                    {{ payrollCurrency }}
                    {{ parseFloat(monthlyCTC)?.toFixed(2) }}
                  </div>
                </v-col>
                <v-col class="bg-grey-lighten-4 text-end">
                  <div class="pr-5 text-body-1">
                    {{ payrollCurrency }}
                    {{ parseFloat(annualCTC)?.toFixed(2) }}
                  </div>
                </v-col>
              </v-row>
            </v-card>
          </div>
        </v-form>
      </v-card-text>
      <v-card
        width="100%"
        class="pa-2 d-flex justify-end position-absolute bottom-0"
        elevation="16"
      >
        <v-btn
          class="mr-2"
          variant="text"
          color="primary"
          elevation="4"
          rounded="lg"
          @click="closeForm()"
          >Cancel</v-btn
        >
        <v-tooltip
          text="Please click on 'Preview' to get the System Calculated Components' before saving the template."
          location="top"
          max-width="300px"
        >
          <template v-slot:activator="{ props }">
            <div
              v-bind="
                anyValueUpdated || sumOfComponetsMinusCTC < 0 ? props : ''
              "
            >
              <v-btn
                color="primary"
                variant="elevated"
                rounded="lg"
                :disabled="anyValueUpdated || sumOfComponetsMinusCTC < 0"
                @click="validateForm()"
                >Save</v-btn
              >
            </div>
          </template>
        </v-tooltip>
      </v-card>
      <AppLoading v-if="isLoading"></AppLoading>
    </v-card>
  </v-overlay>
  <v-dialog v-model="showRetiralValidationDialog" max-width="800px" persistent>
    <v-card class="rounded-lg">
      <v-card-title>
        <v-icon class="mr-2" color="warning"
          >fas fa-exclamation-triangle</v-icon
        >
        Things to Note
      </v-card-title>
      <v-card-text class="pa-4">
        <v-card class="text-subtitle-2 mb-4 pa-4 bg-amber-lighten-4">
          <div class="text-body-2">
            Associating <strong>{{ selectedTemplateName }}</strong> with
            <strong>{{ selectedEmployeeName }}</strong> will result in
            <strong>{{ retiralMismatchesSummary.totalConflicts }}</strong>
            {{
              retiralMismatchesSummary.totalConflicts === 1
                ? "change"
                : "changes"
            }}
            to the employee's pay configuration.
          </div>
        </v-card>

        <!-- Retiral Benefits Mismatches Table -->
        <div class="mt-4">
          <v-table density="compact" class="border rounded">
            <thead>
              <tr class="bg-grey-lighten-4">
                <th class="text-left font-weight-bold">Retiral</th>
                <th class="text-center font-weight-bold">
                  Template Configuration
                </th>
                <th class="text-center font-weight-bold">
                  Employee Current Configuration
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="mismatch in retiralMismatches.filter(
                  (m) => m.hasConflict
                )"
                :key="mismatch.retiralType"
                class="border-b"
              >
                <td class="py-3">
                  <div class="font-weight-medium">
                    {{ mismatch.retiralName }}
                  </div>
                </td>
                <td class="text-center py-3">
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <v-chip
                        v-bind="props"
                        :color="mismatch.templateOffers ? 'success' : 'error'"
                        size="small"
                        variant="flat"
                      >
                        <v-icon
                          size="12"
                          class="mr-1"
                          :icon="
                            mismatch.templateOffers
                              ? 'fas fa-check'
                              : 'fas fa-times'
                          "
                        ></v-icon>
                        {{ mismatch.templateOffers ? "Enabled" : "Disabled" }}
                      </v-chip>
                    </template>
                    <span
                      >Template
                      {{ mismatch.templateOffers ? "includes" : "excludes" }}
                      this benefit</span
                    >
                  </v-tooltip>
                </td>
                <td class="text-center py-3">
                  <v-tooltip location="top">
                    <template v-slot:activator="{ props }">
                      <v-chip
                        v-bind="props"
                        :color="mismatch.employeeEligible ? 'success' : 'error'"
                        size="small"
                        variant="flat"
                      >
                        <v-icon
                          size="12"
                          class="mr-1"
                          :icon="
                            mismatch.employeeEligible
                              ? 'fas fa-check'
                              : 'fas fa-times'
                          "
                        ></v-icon>
                        {{ mismatch.employeeEligible ? "Enabled" : "Disabled" }}
                      </v-chip>
                    </template>
                    <span
                      >Employee is currently
                      {{
                        mismatch.employeeEligible
                          ? "eligible for"
                          : "not eligible for"
                      }}
                      this benefit</span
                    >
                  </v-tooltip>
                </td>
              </tr>
            </tbody>
          </v-table>
        </div>

        <!-- Summary of Changes -->
        <div class="mt-4">
          <v-card>
            <v-card-title>
              <div class="d-flex align-center">
                <v-icon size="16" color="info" class="mr-2"
                  >fas fa-list-ul</v-icon
                >
                <span class="font-weight-medium">Detailed Changes</span>
              </div>
            </v-card-title>
            <v-card-text>
              <div
                v-if="retiralMismatchesSummary.benefitsToEnable.length > 0"
                class="mb-3"
              >
                <div class="font-weight-medium text-success mb-2">
                  <v-icon size="14" class="mr-1">fas fa-plus-circle</v-icon>
                  Retirals to be Enabled ({{
                    retiralMismatchesSummary.benefitsToEnable.length
                  }}):
                </div>
                <v-chip
                  v-for="benefit in retiralMismatchesSummary.benefitsToEnable"
                  :key="benefit.retiralType + '_enable'"
                  color="success"
                  size="small"
                  variant="outlined"
                  class="mr-2 mb-1"
                >
                  {{ benefit.retiralName }}
                </v-chip>
              </div>

              <div
                v-if="retiralMismatchesSummary.benefitsToDisable.length > 0"
                class="mb-3"
              >
                <div class="font-weight-medium text-error mb-2">
                  <v-icon size="14" class="mr-1">fas fa-minus-circle</v-icon>
                  Retirals to be Disabled ({{
                    retiralMismatchesSummary.benefitsToDisable.length
                  }}):
                </div>
                <v-chip
                  v-for="benefit in retiralMismatchesSummary.benefitsToDisable"
                  :key="benefit.retiralType + '_disable'"
                  color="error"
                  size="small"
                  variant="outlined"
                  class="mr-2 mb-1"
                >
                  {{ benefit.retiralName }}
                </v-chip>
              </div>
            </v-card-text>
          </v-card>
        </div>

        <div class="text-body-2 mt-4 pa-3 bg-blue-lighten-5 rounded">
          <v-icon size="16" color="info" class="mr-2 mt-n1"
            >fas fa-info-circle</v-icon
          >
          <strong>Action Required:</strong> The above changes will be applied to
          the employee's pay configuration. Click <strong>Proceed</strong> to
          apply these changes, or <strong>Cancel</strong> to retain the
          employee's current configuration. Alternatively, choose a different
          template to apply a different retiral configuration.
        </div>
      </v-card-text>

      <v-card-actions class="pa-4">
        <v-spacer></v-spacer>
        <v-btn
          color="primary"
          variant="text"
          rounded="lg"
          elevation="4"
          @click="cancelRetiralValidation"
        >
          Cancel
        </v-btn>
        <v-btn
          color="primary"
          variant="elevated"
          rounded="lg"
          @click="updateEmployeeConfig"
        >
          Proceed
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <AppWarningModal
    v-if="openConfirmationModel"
    :open-modal="openConfirmationModel"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationModel = false"
    @accept-modal="$emit('close-form')"
  />
</template>

<script>
import { defineAsyncComponent } from "vue";
import {
  ADD_UPDATE_SALARY_DETAILS,
  RETRIEVE_SYSTEM_CALCULATED_COMPONENTS,
} from "@/graphql/corehr/salaryQueries.js";
import { UPDATE_RETIRALS_CONFIG } from "@/graphql/employee-profile/profileQueries.js";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { checkNullValue } from "@/helper";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);

import Datepicker from "vuejs3-datepicker";
export default {
  name: "AddUpdate",
  components: { CustomSelect, Datepicker },
  mixins: [validationRules],
  emits: ["close-form", "add-update-success"],
  props: {
    showForm: {
      type: Boolean,
      default: false,
    },
    landedFormName: {
      type: String,
      required: true,
    },
    payrollCurrency: {
      type: String,
      required: true,
    },
    editType: {
      type: String,
      default: "",
    },
    formId: {
      type: Number,
      default: 207,
    },
    selectedItem: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      showOverlay: false,
      employeeListLoading: false,
      selectedEmployee: null,
      employeeList: [],
      salaryTemplateListLoading: false,
      selectedSalaryTemplate: null,
      templateList: [],
      templateDetails: {},
      originalTemplateDetails: {},
      backupTemplateDetails: {},
      annualCTC: null,
      basicPay: null,
      monthlyBasicPay: null,
      isLoading: false,
      openConfirmationModel: false,
      monthlyCTC: null,
      formattedEffectiveForm: "",
      listLoading: false,
      anyValueUpdated: false,
      sumOfComponetsMinusCTC: 0,
      fixedAllowanceAmount: 0,
      componentsLoading: false,
      reviseType: "Percentage",
      percentage: null,
      amount: null,
      revisedSalaryEffectiveFromMenu: false,
      revisedSalaryEffectiveFrom: null,
      formattedRevisedSalaryEffectiveFrom: "",
      payoutMonth: null,
      payoutMonthMenu: false,
      formattedPayoutMonth: "",
      showRetiralValidationDialog: false,
      retiralMismatches: [],
      periodMap: {
        Monthly: [1, 12],
        Quarterly: [3, 4],
        HalfYearly: [6, 2],
        Annually: [12, 1],
      },
      monthlyGrossSalary: 0,
    };
  },
  computed: {
    providentFundConfiguration() {
      return this.$store.state.orgDetails.providentFundConfiguration;
    },
    getBonusText() {
      let formNames = [],
        total = 0;
      if (this.templateDetails.allowances) {
        if (this.templateDetails.allowances?.basicPayArray?.length) {
          this.templateDetails.allowances.basicPayArray.forEach((item) => {
            if (item.Form_Id?.includes(46)) {
              formNames.push(item.Allowance_Name);
              total += parseInt(this.monthlyBasicPay) || 0;
            }
          });
        }
        let keysToUpdate = ["allowanceArray", "flexiBenefitPlanArray"];
        keysToUpdate.forEach((key) => {
          if (this.templateDetails.allowances[key].length) {
            this.templateDetails.allowances[key]?.forEach((item) => {
              if (item.Form_Id?.includes(46)) {
                formNames.push(item.Allowance_Name);
                total += parseInt(item.Amount) || 0;
              }
            });
          }
        });
      }
      formNames = formNames.sort((a, b) => a.localeCompare(b));
      return formNames.length ? formNames.join(" + ") + " = " + total : "";
    },
    getCustomText() {
      return (formIds) => {
        let allowanceIds = formIds?.split(",");
        allowanceIds = new Set(allowanceIds);
        let formNames = [],
          total = 0;
        if (allowanceIds.size && this.templateDetails.allowances) {
          Object.keys(this.templateDetails.allowances).forEach((key) => {
            if (this.templateDetails.allowances[key].length) {
              this.templateDetails.allowances[key]?.forEach((item) => {
                if (allowanceIds.has(item.Allowance_Type_Id.toString())) {
                  formNames.push(item.Allowance_Name);
                  total += parseInt(item.Amount) || 0;
                }
              });
            }
          });
        }
        formNames = formNames.sort((a, b) => a.localeCompare(b));
        return formNames.length ? formNames.join(" + ") + " = " + total : "";
      };
    },
    componentWidth() {
      if (this.windowWidth > 1430) {
        return "60vw";
      } else if (this.windowWidth > 1260 && this.windowWidth <= 1430) {
        return "60vw";
      } else if (this.windowWidth > 960 && this.windowWidth <= 1260) {
        return "70vw";
      } else {
        return "100vw";
      }
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    getGroupName() {
      return (groupName) => {
        let name = "";
        if (groupName) {
          name = groupName.split("Array");
          if (name.length) {
            name = name[0];
            name = name.split(/(?=[A-Z])/).join(" ");
            name = name.charAt(0).toUpperCase() + name.slice(1);
          }
        }
        return name;
      };
    },
    formsBasedOnFormId() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    getCustomFormName() {
      return (formId) => {
        let form = this.formsBasedOnFormId(formId);
        return form ? form.customFormName : "";
      };
    },
    minAnnualCTC() {
      let emp = this.employeeList.find(
        (emp) => emp.employeeId === this.selectedEmployee
      );
      return emp && emp.minimumWage ? emp.minimumWage : 0;
    },
    getAllowanceAmount() {
      return (item) => {
        const mutiplier = item.Percentage / 100;
        const amount = (this.annualCTC * mutiplier) / 12;
        if (amount - parseInt(amount) > 0) {
          return amount.toFixed(2);
        } else {
          return amount;
        }
      };
    },
    selectedTemplateName() {
      const templateId = this.selectedSalaryTemplate;
      const template = this.templateList.find(
        (template) => template.Template_Id === templateId
      );
      return template ? template.Template_Name : "Salary Template";
    },
    selectedEmployeeName() {
      const employeeId = this.selectedEmployee;
      const employee = this.employeeList.find(
        (emp) => emp.employeeId === employeeId
      );
      return employee ? employee.employeeName : "Employee";
    },
    getDisabledDates() {
      return (type) => {
        if (type?.toLowerCase() === "effectivefrom") {
          if (this.templateDetails?.Effective_From) {
            return {
              to: new Date(this.templateDetails.Effective_From),
            };
          } else {
            return {};
          }
        } else if (type?.toLowerCase() === "payoutmonth") {
          if (this.revisedSalaryEffectiveFrom) {
            return {
              to: moment(this.revisedSalaryEffectiveFrom)
                .add(1, "month")
                .toDate(),
            };
          } else {
            return {};
          }
        }
      };
    },
    retiralMismatchesSummary() {
      const conflictingMismatches = this.retiralMismatches.filter(
        (m) => m.hasConflict
      );
      return {
        totalConflicts: conflictingMismatches.length,
        benefitsToEnable: conflictingMismatches.filter(
          (m) => m.templateOffers && !m.employeeEligible
        ),
        benefitsToDisable: conflictingMismatches.filter(
          (m) => !m.templateOffers && m.employeeEligible
        ),
        conflictingBenefitNames: conflictingMismatches.map(
          (m) => m.retiralName
        ),
      };
    },
  },
  watch: {
    showForm(val) {
      this.showOverlay = val;
    },
    revisedSalaryEffectiveFrom() {
      if (
        this.revisedSalaryEffectiveFrom &&
        moment(this.revisedSalaryEffectiveFrom).isValid()
      ) {
        this.formattedRevisedSalaryEffectiveFrom = moment(
          this.revisedSalaryEffectiveFrom
        ).format("MMM YYYY");
      }
    },
    payoutMonth() {
      if (this.payoutMonth && moment(this.payoutMonth).isValid()) {
        this.formattedPayoutMonth = moment(this.payoutMonth).format("MMM YYYY");
      }
    },
  },
  mounted() {
    this.showOverlay = this.showForm;
    this.getSalartTemplateList();
    if (this.editType) {
      this.selectedEmployee = this.selectedItem?.Employee_Id;
      this.selectedSalaryTemplate = this.selectedItem?.Template_Id;
      this.formattedEffectiveForm = this.selectedItem?.Effective_From;
      let variables = {
        formId: 207,
        isViewMode: true,
        isDropdown: false,
        id: 1,
        employeeId: this.selectedItem?.Employee_Id,
      };
      this.getSalaryDetails(variables);
    }
    this.getemployeeList();
  },
  methods: {
    checkNullValue,
    closeForm() {
      this.openConfirmationModel = true;
    },
    onChangeSalaryEffectiveFrom() {
      this.revisedSalaryEffectiveFromMenu = false;
      this.payoutMonth = null;
      this.formattedPayoutMonth = null;
    },
    onChangeTemplate(templateId) {
      this.selectedSalaryTemplate = templateId;
      if (templateId) {
        let variables = {
          formId: 206,
          isViewMode: true,
          isDropdown: false,
          templateId: templateId,
        };
        this.getSalaryDetails(variables);
      }
    },
    employerContributionChanged(item, event) {
      item.tempEmployerType = event;
      if (item.tempEmployerType === "actualWage") {
        item.tempEmployeeType = "actualWage";
        item.disableEmployeeContribution = true;
      } else {
        item.disableEmployeeContribution = false;
      }
    },
    openFBPExpansionPanel(item) {
      item.fbpMaxAmount = item.FBP_Max_Declaration;
      item.fbpPanels = [0];
    },
    async updateFBP(item) {
      let { valid } = await this.$refs[
        `fbp-${item.Allowance_Type_Id}`
      ][0].validate();
      if (valid) {
        item.FBP_Max_Declaration = item.fbpMaxAmount;
        item.fbpPanels = [];
      }
    },
    openExpansionPanel(item) {
      if (item.Form_Id == 52) {
        if (item.PF_Employee_Contribution === "Restrict") {
          item.tempEmployeeType = "restrict";
        } else {
          item.tempEmployeeType = "actualWage";
        }
        if (item.PF_Employer_Contribution === "Restrict") {
          item.tempEmployerType = "restrict";
        } else {
          item.tempEmployerType = "actualWage";
          item.disableEmployeeContribution = true;
        }
      } else {
        item.tempType =
          item.Retirals_Type?.toLowerCase() == "percentage"
            ? "Percentage"
            : "Amount";
        if (item.tempType === "Percentage") {
          item.tempEmployeeValue = item.Employee_Share_Percentage;
          item.tempEmployerValue = item.Employer_Share_Percentage;
        } else {
          item.tempEmployeeValue = item.Employee_Share_Amount;
          item.tempEmployerValue = item.Employer_Share_Amount;
        }
      }
      item.expansionPanel = [0];
    },
    async updateRetirals(item) {
      let { valid } = await this.$refs[
        `retirals-${item.Retirals_Id || item.Form_Id}`
      ][0].validate();
      if (valid && item.Form_Id !== 52) {
        item.Retirals_Type = item.tempType;
        if (item.tempType === "Percentage") {
          item.Employee_Share_Percentage = item.tempEmployeeValue;
          item.Employer_Share_Percentage = item.tempEmployerValue;
          item.Percentage = item.tempEmployeeValue;
          item.Amount = null;
          item.Employee_Share_Amount = null;
          item.Employer_Share_Amount = null;
        } else {
          item.Employee_Share_Amount = item.tempEmployeeValue;
          item.Employer_Share_Amount = item.tempEmployerValue;
          item.Percentage = null;
          item.Amount = item.tempEmployeeValue;
          item.Employee_Share_Percentage = null;
          item.Employer_Share_Percentage = null;
        }
        item.expansionPanel = [];
        this.calculateSumOfComponetsMinusCTC();
      } else if (valid && item.Form_Id == 52) {
        if (item.tempEmployeeType === "actualWage") {
          item.PF_Employee_Contribution = "Actual";
        } else {
          item.PF_Employee_Contribution = "Restrict";
        }
        if (item.tempEmployerType === "actualWage") {
          item.PF_Employer_Contribution = "Actual";
        } else {
          item.PF_Employer_Contribution = "Restrict";
        }
        item.expansionPanel = [];
        this.calculateSumOfComponetsMinusCTC();
      }
    },
    calculateSumOfComponetsMinusCTC() {
      this.anyValueUpdated = true;
      let total = 0;
      total += parseInt(this.monthlyBasicPay);
      if (this.templateDetails?.allowances?.allowanceArray?.length) {
        this.templateDetails.allowances.allowanceArray.forEach((item) => {
          total += parseInt(item.Amount);
        });
      }
      if (this.templateDetails?.allowances?.flexiBenefitPlanArray?.length) {
        this.templateDetails.allowances.flexiBenefitPlanArray.forEach(
          (item) => {
            total += parseInt(item.Amount);
          }
        );
      }
      if (this.templateDetails?.allowances?.reimbursementArray?.length) {
        this.templateDetails.allowances.reimbursementArray.forEach((item) => {
          total += parseInt(item.Amount);
        });
      }
      this.sumOfComponetsMinusCTC = this.monthlyCTC - total;
    },
    onChangeEmployee(event) {
      this.selectedEmployee = event;
      let selectedEmployee = this.employeeList.find(
        (emp) => emp.employeeId === event
      );
      const effectiveDate = selectedEmployee?.effectiveDate;
      this.formattedEffectiveForm = moment(effectiveDate).format(
        this.$store.state.orgDetails.orgDateFormat
      );
      this.validateRetiralBenefits();
    },
    updatePercentage(value) {
      if (this.reviseType?.toLowerCase() === "percentage") {
        let percentageValue = value ? value : 0;
        let changedAmount =
          (percentageValue / 100) * this.templateDetails.Annual_CTC;
        if (changedAmount - parseInt(changedAmount) > 0) {
          changedAmount = changedAmount.toFixed(2);
        }
        this.annualCTC =
          this.templateDetails.Annual_CTC + parseInt(changedAmount);
      } else {
        this.annualCTC = this.templateDetails.Annual_CTC + parseInt(value);
      }
      this.updateAnnualCTC();
    },
    async getemployeeList() {
      let vm = this;
      vm.employeeListLoading = true;
      await vm.$store
        .dispatch("getEmployeesList", {
          formId: this.editType === "" ? vm.formId : null,
          formName: "Salary Details",
          fetchPolicy: "no-cache",
          isSalaryEdit: this.editType !== "",
        })
        .then((empData) => {
          if (empData && empData.length) {
            let empList = empData.map((item) => ({
              ...item,
              employeeData: item.employeeName + " - " + item.userDefinedEmpId,
            }));
            vm.employeeList = [...empList];
          } else {
            vm.employeeList = [];
          }
          vm.employeeListLoading = false;
        })
        .catch((err) => {
          vm.employeeListLoading = false;
          vm.employeeList = [];
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "employee list",
            isListError: false,
          });
        });
    },
    async getSalartTemplateList() {
      let vm = this;
      vm.salaryTemplateListLoading = true;
      await vm.$store
        .dispatch("getSalaryDetails", { formId: 206, isDropdown: true })
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let templateList = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );
            if (templateList) {
              vm.templateList = templateList;
            } else {
              vm.templateList = [];
            }
          } else {
            vm.templateList = [];
          }
          vm.salaryTemplateListLoading = false;
        })
        .catch((err) => {
          vm.templateList = [];
          vm.salaryTemplateListLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "salary details",
            isListError: false,
          });
        });
    },
    async getSalaryDetails(variables) {
      let vm = this;
      vm.listLoading = true;
      await vm.$store
        .dispatch("getSalaryDetails", variables)
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let templateDetails = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );
            if (templateDetails) {
              vm.templateDetails = templateDetails[0];
              vm.originalTemplateDetails = JSON.parse(
                JSON.stringify(templateDetails[0])
              );
              if (
                vm.editType?.toLowerCase() === "revise" &&
                vm.selectedSalaryTemplate == vm.selectedItem?.Template_Id
              ) {
                vm.backupTemplateDetails = JSON.parse(
                  JSON.stringify(templateDetails[0])
                );
              }
              vm.processData();
              vm.validateRetiralBenefits();
            } else {
              vm.templateList = [];
            }
          } else {
            vm.templateList = [];
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.templateList = [];
          vm.listLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "salary details",
            isListError: false,
          });
        });
    },
    validateRetiralBenefits() {
      const { hasConflicts, mismatches } = this.processEmployeeRetiralBenefits(
        this.selectedEmployee,
        this.originalTemplateDetails
      );
      if (hasConflicts && this.selectedSalaryTemplate) {
        this.showRetiralValidationDialog = true;
        this.retiralMismatches = mismatches;
      }
    },
    processEmployeeRetiralBenefits(employeeId, templateDetails) {
      if (!employeeId || !templateDetails) {
        return { hasConflicts: false, mismatches: [] };
      }
      const selectedEmployee = this.employeeList.find(
        (emp) => emp.employeeId === employeeId
      );
      if (!selectedEmployee) {
        return { hasConflicts: false, mismatches: [] };
      }
      const retiralMappings = [
        {
          retiralType: "provident fund",
          retiralName: this.getCustomFormName(52),
          employeeEligible: parseInt(selectedEmployee.eligibleForPf) === 1,
        },
        {
          retiralType: "insurance",
          retiralName: "Insurance",
          employeeEligible:
            parseInt(selectedEmployee.eligibleForInsurance) === 1,
        },
        {
          retiralType: "nps",
          retiralName: this.getCustomFormName(126),
          employeeEligible: parseInt(selectedEmployee.eligibleForNps) === 1,
        },
        {
          retiralType: "gratuity",
          retiralName: this.getCustomFormName(110),
          employeeEligible:
            parseInt(selectedEmployee.eligibleForGratuity) === 1,
        },
      ];
      const mismatches = [];
      let hasConflicts = false;
      retiralMappings.forEach((mapping) => {
        const templateOffers = templateDetails.retirals?.some((retiral) => {
          const retiralName = retiral.Retirals_Name?.toLowerCase();
          return retiralName === mapping.retiralType;
        });

        const hasConflict = templateOffers !== mapping.employeeEligible;

        if (hasConflict) {
          hasConflicts = true;
        }
        mismatches.push({
          retiralType: mapping.retiralType,
          retiralName: mapping.retiralName,
          templateOffers,
          employeeEligible: mapping.employeeEligible,
          hasConflict,
        });
      });
      return { hasConflicts, mismatches };
    },
    cancelRetiralValidation() {
      this.showRetiralValidationDialog = false;
      if (this.editType !== "") {
        this.templateDetails.retirals =
          this.originalTemplateDetails.retirals.filter((retiral) =>
            this.retiralMismatches?.some((mismatch) => {
              const retiralName = retiral.Retirals_Name?.toLowerCase();
              return (
                mismatch.retiralType === retiralName &&
                mismatch.employeeEligible &&
                mismatch.templateOffers
              );
            })
          );
        this.processData();
      } else {
        this.selectedSalaryTemplate = null;
        this.templateDetails = {};
      }
    },
    updateEmployeeConfig() {
      this.showRetiralValidationDialog = false;
      let vm = this;
      vm.isLoading = true;

      // Get current employee eligibility
      const selectedEmployee = this.employeeList.find(
        (emp) => emp.employeeId === this.selectedEmployee
      );

      if (!selectedEmployee) {
        return;
      }

      // Determine retiral eligibility based on template offerings
      const templateRetirals = this.templateDetails?.retirals || [];
      const templateOffers = {
        eligibleForPf: templateRetirals?.some(
          (retiral) => retiral.Retirals_Name?.toLowerCase() === "provident fund"
        ),
        eligibleForInsurance: templateRetirals?.some(
          (retiral) => retiral.Retirals_Name?.toLowerCase() === "insurance"
        ),
        eligibleForNps: templateRetirals?.some(
          (retiral) => retiral.Retirals_Name?.toLowerCase() === "nps"
        ),
        eligibleForGratuity: templateRetirals?.some(
          (retiral) => retiral.Retirals_Name?.toLowerCase() === "gratuity"
        ),
      };

      // Only update eligibility if employee is not already eligible and template offers it
      const retiralConfig = {
        eligibleForPf: templateOffers.eligibleForPf ? 1 : 0,
        eligibleForInsurance: templateOffers.eligibleForInsurance ? 1 : 0,
        eligibleForNps: templateOffers.eligibleForNps ? 1 : 0,
        eligibleForGratuity: templateOffers.eligibleForGratuity ? 1 : 0,
      };

      vm.$apollo
        .mutate({
          mutation: UPDATE_RETIRALS_CONFIG,
          variables: {
            employeeId: vm.selectedEmployee,
            ...retiralConfig,
          },
          client: "apolloClientAD",
        })
        .then(() => {
          vm.isLoading = false;
        })
        .catch((err) => {
          this.cancelRetiralValidation();
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "updating",
            form: "retirals",
            isListError: false,
          });
        });
    },
    processData() {
      this.annualCTC = this.templateDetails?.Annual_CTC;
      let monthlyCTCAmount = this.annualCTC / 12;
      if (monthlyCTCAmount - parseInt(monthlyCTCAmount) > 0) {
        this.monthlyCTC = monthlyCTCAmount.toFixed(2);
      } else {
        this.monthlyCTC = monthlyCTCAmount;
      }
      this.monthlyGrossSalary = this.templateDetails?.Monthly_Gross_Salary;
      this.fixedAllowanceAmount =
        this.templateDetails?.allowances?.fixedAllowanceArray[0]?.Amount;
      if (this.templateDetails?.allowances?.basicPayArray?.length) {
        if (
          this.templateDetails?.allowances?.basicPayArray[0]?.Allowance_Type?.toLowerCase() ===
          "percentage"
        ) {
          this.templateDetails.BasicPay_Type = "Percentage";
          this.basicPay =
            this.templateDetails?.allowances?.basicPayArray[0]?.Percentage;
          const mutiplier = this.basicPay / 100;
          const basicPay = (this.annualCTC * mutiplier) / 12;
          if (basicPay - parseInt(basicPay) > 0) {
            this.monthlyBasicPay = basicPay.toFixed(2);
          } else {
            this.monthlyBasicPay = basicPay;
          }
        } else {
          this.monthlyBasicPay =
            this.templateDetails?.allowances?.basicPayArray[0]?.Amount;
          this.basicPay = null;
        }
      } else {
        this.monthlyBasicPay = 0;
        this.basicPay = null;
      }
      this.updateBasicPay("mounted");
      this.templateDetails?.allowances?.bonusArray.forEach((item) => {
        if (item.Allowance_Type?.toLowerCase() === "percentage") {
          item.Percentage = Number(item.Percentage ?? 0);
          const mutiplier = item.AllowanceWages || 0;
          const basicPay = ((item.Percentage / 100) * mutiplier).toFixed(2);
          item.Amount = basicPay ? basicPay : 0;
        } else {
          item.Percentage = null;
          item.Amount = Number(item.Amount ?? 0);
        }
      });
      if (
        this.templateDetails.retirals &&
        this.templateDetails.retirals.length
      ) {
        this.templateDetails.retirals.forEach((item) => {
          if (
            item.Retirals_Type?.toLowerCase() === "percentage" &&
            !item.Employer_Share_Amount
          ) {
            item.Percentage = Number(item.Employer_Share_Percentage ?? 0);
            const mutiplier = item.Employer_Retiral_Wages || 0;
            const basicPay = (item.Percentage / 100) * mutiplier;
            if (basicPay - parseInt(basicPay) > 0) {
              item.Amount = basicPay.toFixed(2);
            } else {
              item.Amount = basicPay;
            }
          } else {
            item.Percentage = item.Employer_Share_Percentage || 0;
            item.Amount = Number(item.Employer_Share_Amount ?? 0);
          }
        });
      }
    },
    updateAllowanceAmount(item) {
      const mutiplier = item.Percentage / 100;
      const amount = this.monthlyBasicPay * mutiplier;
      if (amount - parseInt(amount) > 0) {
        item.Amount = amount.toFixed(2);
      } else {
        item.Amount = amount;
      }
      this.calculateSumOfComponetsMinusCTC();
    },
    updateBasicPay(msg = "") {
      if (this.templateDetails?.BasicPay_Type?.toLowerCase() === "percentage") {
        const mutiplier = this.basicPay / 100;
        const basicPay = (this.annualCTC * mutiplier) / 12;
        if (basicPay - parseInt(basicPay) > 0) {
          this.monthlyBasicPay = basicPay.toFixed(2);
        } else {
          this.monthlyBasicPay = basicPay;
        }
      }
      let keysToUpdate = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
      ];
      keysToUpdate.forEach((key) => {
        this.templateDetails.allowances?.[key]?.forEach((innerItems) => {
          if (innerItems.Allowance_Type?.toLowerCase() === "percentage") {
            const mutiplier = innerItems.Percentage / 100;
            const basicPay = this.monthlyBasicPay * mutiplier;
            if (basicPay - parseInt(basicPay) > 0) {
              innerItems.Amount = basicPay.toFixed(2);
            } else {
              innerItems.Amount = basicPay;
            }
          }
        });
      });
      if (msg !== "mounted") {
        this.calculateSumOfComponetsMinusCTC();
      }
    },
    updateAnnualCTC() {
      let monthlyCTCAmount = this.annualCTC / 12;
      if (monthlyCTCAmount - parseInt(monthlyCTCAmount) > 0) {
        this.monthlyCTC = monthlyCTCAmount.toFixed(2);
      } else {
        this.monthlyCTC = monthlyCTCAmount;
      }
      this.updateBasicPay();
    },
    async validateForm() {
      let { valid } = await this.$refs.form.validate();
      if (valid) {
        this.addUpdateFormData();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          const fields = Array.isArray(field) ? field : [field];
          if (fields && fields[0] && fields[0].rules) {
            const allValid = fields[0].rules.every((value) => value === true);
            if (fields[0].rules.length > 0 && !allValid) {
              invalidFields.push(refName);
            }
          }
        });

        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              const fields = Array.isArray(fieldRef) ? fieldRef : [fieldRef];
              if (fields && fields[0] && fields[0].$el) {
                const element = fields[0].$el;
                if (element && element.scrollIntoView) {
                  element.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                } else if (element && element.getBoundingClientRect) {
                  const rect = element.getBoundingClientRect();
                  window.scrollTo({
                    top: window.scrollY + rect.top - 100, // adjust offset if needed
                    behavior: "smooth",
                  });
                }
              }
            }
          });
        }
      }
    },
    addUpdateFormData() {
      let vm = this;
      vm.isLoading = true;
      let allowanceArray = [];
      let retiralsArray = [];
      if (this.templateDetails?.allowances?.basicPayArray?.length) {
        allowanceArray.push({
          allowanceTypeId:
            this.templateDetails.allowances.basicPayArray[0].Allowance_Type_Id,
          allowanceType:
            this.templateDetails.allowances.basicPayArray[0].Allowance_Type,
          percentage: this.basicPay ? this.basicPay?.toString() : null,
          amount: this.monthlyBasicPay
            ? this.monthlyBasicPay?.toString()
            : null,
          allowanceWages:
            this.templateDetails.allowances.basicPayArray[0].AllowanceWages?.toString(),
        });
      }
      if (this.templateDetails?.allowances?.fixedAllowanceArray?.length) {
        this.templateDetails.allowances.fixedAllowanceArray.forEach((item) => {
          allowanceArray.push({
            allowanceTypeId: item.Allowance_Type_Id,
            allowanceType: item.Allowance_Type,
            percentage: null,
            amount: this.fixedAllowanceAmount?.toString() || "",
          });
        });
      }
      let keysToUpdate = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
        "bonusArray",
      ];
      if (this.templateDetails.allowances) {
        keysToUpdate.map((item) => {
          this.templateDetails.allowances[item]?.forEach((innerItems) => {
            allowanceArray.push({
              allowanceTypeId: innerItems.Allowance_Type_Id,
              allowanceType: innerItems.Allowance_Type,
              percentage: innerItems.Percentage?.toString(),
              amount: innerItems.Amount?.toString(),
              allowanceWages: innerItems.AllowanceWages?.toString(),
              fbpMaxDeclaration: innerItems.FBP_Max_Declaration?.toString(),
            });
          });
        });
        if (
          this.templateDetails.retirals &&
          this.templateDetails.retirals.length
        ) {
          this.templateDetails.retirals.forEach((item) => {
            retiralsArray.push({
              formId: item.Form_Id?.toString(),
              retiralsId: item.Retirals_Id?.toString(),
              retiralsType: item.Retirals_Type?.toString(),
              employeeSharePercentage:
                item.Employee_Share_Percentage?.toString(),
              employerSharePercentage:
                item.Employer_Share_Percentage?.toString(),
              employeeShareAmount: item.Employee_Share_Amount?.toString(),
              employerShareAmount: item.Employer_Share_Amount?.toString(),
              pfEmployeeContribution: item.PF_Employee_Contribution?.toString(),
              pfEmployerContribution: item.PF_Employer_Contribution?.toString(),
              employeeStatutoryLimit: item.Employee_Statutory_Limit?.toString(),
              employerStatutoryLimit: item.Employer_Statutory_Limit?.toString(),
              employeeRetiralWages: item.Employee_Retiral_Wages?.toString(),
              employerRetiralWages: item.Employer_Retiral_Wages?.toString(),
              eligibleForEPS: item.Eligible_for_EPS?.toString() || 0,
              adminCharge: item.Admin_Charge?.toString(),
              edliCharge: item.EDLI_Charge?.toString(),
            });
          });
        }
      }
      let variables = {};
      if (vm.formId === 207) {
        if (vm.editType?.toLowerCase() === "revise") {
          let percentage =
            ((vm.annualCTC - vm.backupTemplateDetails?.Annual_CTC) /
              vm.backupTemplateDetails?.Annual_CTC) *
            100;
          variables = {
            formId: 360,
            accessFormId: 360,
            isEditMode: false,
            templateId: vm.selectedSalaryTemplate,
            employeeId: vm.selectedEmployee,
            annualCTC: vm.annualCTC.toString(),
            effectiveFrom: moment(
              vm.formattedEffectiveForm,
              this.$store.state.orgDetails.orgDateFormat
            ).isValid()
              ? moment(
                  vm.formattedEffectiveForm,
                  this.$store.state.orgDetails.orgDateFormat
                ).format("YYYY-MM-DD")
              : "",
            effectiveTo: "",
            allowance: allowanceArray || [],
            annualGrossSalary: (this.monthlyGrossSalary * 12).toString(),
            monthlyGrossSalary: this.monthlyGrossSalary.toString(),
            retirals: retiralsArray || [],
            salaryEffectiveMonth: moment(
              vm.revisedSalaryEffectiveFrom
            ).isValid()
              ? moment(vm.revisedSalaryEffectiveFrom).format("M,YYYY")
              : "",
            payoutMonth: moment(vm.payoutMonth).isValid()
              ? moment(vm.payoutMonth).format("M,YYYY")
              : "",
            revisionType: vm.reviseType,
            revisionStatus: "Applied",
            reviseCtcByPercentage:
              vm.reviseType?.toLowerCase() === "percentage"
                ? vm.percentage
                : percentage?.toFixed(2).toString(),
            previousCtc: vm.backupTemplateDetails?.Annual_CTC?.toString(),
          };
        } else {
          variables = {
            formId: 207,
            accessFormId: 207,
            isEditMode: vm.editType !== "",
            templateId: vm.selectedSalaryTemplate,
            employeeId: vm.selectedEmployee,
            annualCTC: vm.annualCTC.toString(),
            effectiveFrom: moment(
              vm.formattedEffectiveForm,
              this.$store.state.orgDetails.orgDateFormat
            ).isValid()
              ? moment(
                  vm.formattedEffectiveForm,
                  this.$store.state.orgDetails.orgDateFormat
                ).format("YYYY-MM-DD")
              : "",
            effectiveTo: "",
            allowance: allowanceArray || [],
            annualGrossSalary: (this.monthlyGrossSalary * 12).toString(),
            monthlyGrossSalary: this.monthlyGrossSalary.toString(),
            retirals: retiralsArray || [],
          };
        }
      }
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_SALARY_DETAILS,
          variables: variables,
          client: "apolloClientF",
        })
        .then(() => {
          vm.isLoading = false;
          let msg = "";
          if (vm.editType?.toLowerCase() === "revise") {
            msg = "Salary details revised successfully.";
          } else if (vm.editType?.toLowerCase() === "edit") {
            msg = "Salary details updated successfully.";
          } else {
            msg = "Salary details added successfully.";
          }
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: msg,
          };
          vm.showAlert(snackbarData);
          vm.$emit("add-update-success");
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "adding/updating",
            form: "salary details",
            isListError: false,
          });
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getSystemCalculatedComponets() {
      let vm = this;
      vm.componentsLoading = true;
      let salaryDetails = {
        Employee_Salary_Id: vm.selectedItem?.Employee_Salary_Id,
        Employee_Id: vm.selectedEmployee,
        Annual_Ctc: vm.annualCTC,
        Basic_Pay: vm.monthlyBasicPay,
        Effective_From: moment(
          vm.formattedEffectiveForm,
          this.$store.state.orgDetails.orgDateFormat
        ).isValid()
          ? moment(
              vm.formattedEffectiveForm,
              this.$store.state.orgDetails.orgDateFormat
            ).format("YYYY-MM-DD")
          : "",
        Effective_To: "",
        ESI_Contribution_End_Date: null,
        Status: "Active",
      };
      let keysToSend = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
        "bonusArray",
      ];
      let allowanceDetails = [];
      allowanceDetails.push({
        Employee_Salary_Id: vm.selectedEmployee,
        Allowance_Type_Id:
          this.templateDetails?.allowances?.basicPayArray[0]?.Allowance_Type_Id,
        Allowance_Type:
          this.templateDetails?.allowances?.basicPayArray[0]?.Allowance_Type,
        Percentage: vm.basicPay,
        Amount: parseFloat(vm.monthlyBasicPay),
      });
      allowanceDetails.push({
        Employee_Salary_Id: vm.selectedEmployee,
        Allowance_Type_Id:
          this.templateDetails?.allowances?.fixedAllowanceArray[0]
            ?.Allowance_Type_Id,
        Allowance_Type: "Fixed",
        Percentage: null,
        Amount: parseFloat(vm.fixedAllowanceAmount),
      });
      keysToSend.forEach((key) => {
        this.templateDetails?.allowances?.[key].forEach((item) => {
          allowanceDetails.push({
            Employee_Salary_Id: vm.selectedEmployee,
            Allowance_Type_Id: item?.Allowance_Type_Id,
            Allowance_Type: item?.Allowance_Type,
            Percentage: item?.Percentage,
            Amount: parseFloat(item?.Amount),
          });
        });
      });

      let retiralDetails = [];
      this.templateDetails?.retirals.forEach((item) => {
        retiralDetails.push({
          Employee_Salary_Id: vm.selectedEmployee,
          Form_Id: item.Form_Id,
          Retirals_Id: item.Retirals_Id,
          Retirals_Type: item.Retirals_Type,
          Retiral_Wages: item.Retiral_Wages,
          Employee_Share_Percentage: item.Employee_Share_Percentage,
          Employer_Share_Percentage: item.Employer_Share_Percentage,
          Employee_Share_Amount: parseInt(item.Employee_Share_Amount),
          Employer_Share_Amount: parseInt(item.Employer_Share_Amount),
          PF_Employee_Contribution: item.PF_Employee_Contribution,
          PF_Employer_Contribution: item.PF_Employer_Contribution,
          Employee_Statutory_Limit: item.Employee_Statutory_Limit,
          Employer_Statutory_Limit: item.Employer_Statutory_Limit,
          Eligible_for_EPS: item.Eligible_for_EPS,
          Admin_Charge: item.Admin_Charge,
          EDLI_Charge: item.EDLI_Charge,
        });
      }) || [];
      vm.$apollo
        .query({
          query: RETRIEVE_SYSTEM_CALCULATED_COMPONENTS,
          client: "apolloClientAT",
          variables: {
            employeeId: vm.selectedEmployee,
            retiralDetails: JSON.stringify(retiralDetails),
            allowanceDetails: JSON.stringify(allowanceDetails),
            salaryDetails: JSON.stringify(salaryDetails),
            providentFundConfigurationValue: vm.providentFundConfiguration,
          },
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          if (
            data?.calculateSalary?.employeeRetiralDetails &&
            !data.calculateSalary.errorCode
          ) {
            let salaryStructure = JSON.parse(
              data?.calculateSalary?.salaryStructure
            );
            this.fixedAllowanceAmount = salaryStructure.fixedAllowance;
            this.monthlyGrossSalary = salaryStructure.grossSalary;
            let salaryComponents = JSON.parse(
              data?.calculateSalary?.employeeRetiralDetails
            );
            if (salaryComponents?.employeeSalaryRetirals?.length) {
              const matchedKeys = new Set(
                salaryComponents.employeeSalaryRetirals.map(
                  (item) => `${item.Retirals_Id}|${item.Form_Id}`
                )
              );
              this.templateDetails.retirals =
                this.templateDetails.retirals.filter((retiralItem) => {
                  const key = `${retiralItem.Retirals_Id}|${retiralItem.Form_Id}`;
                  const match = matchedKeys.has(key);

                  if (match) {
                    const matchedItem =
                      salaryComponents.employeeSalaryRetirals.find(
                        (item) =>
                          parseInt(item.Form_Id) ===
                            parseInt(retiralItem.Form_Id) &&
                          parseInt(item.Retirals_Id) ===
                            parseInt(retiralItem.Retirals_Id)
                      );

                    if (matchedItem) {
                      retiralItem.Employee_Share_Amount =
                        matchedItem.Employee_Share_Amount;
                      retiralItem.Employer_Share_Amount =
                        matchedItem.Employer_Share_Amount;
                      retiralItem.Amount = matchedItem.Employer_Share_Amount;
                    }
                  }

                  return match;
                });
            } else {
              this.templateDetails.retirals = [];
            }

            if (salaryComponents?.employeeSalaryBonus?.length) {
              salaryComponents.employeeSalaryBonus.forEach((item) => {
                this.templateDetails.allowances.bonusArray.forEach(
                  (bonusItem) => {
                    if (
                      parseInt(item.Allowance_Type_Id) ===
                      parseInt(bonusItem.Allowance_Type_Id)
                    ) {
                      bonusItem.Amount = item.Amount;
                      bonusItem.Percentage = item.Percentage;
                    }
                  }
                );
              });
            }
            if (salaryComponents?.employeeSalaryAllowance?.length) {
              salaryComponents.employeeSalaryAllowance.forEach((item) => {
                this.templateDetails.allowances?.allowanceArray?.forEach(
                  (allowanceItem) => {
                    if (
                      parseInt(item.Allowance_Type_Id) ===
                      parseInt(allowanceItem.Allowance_Type_Id)
                    ) {
                      allowanceItem.Amount = item.Amount || 0;
                    }
                  }
                );
                this.templateDetails.allowances?.reimbursementArray?.forEach(
                  (reimbursementItem) => {
                    if (
                      parseInt(item.Allowance_Type_Id) ===
                      parseInt(reimbursementItem.Allowance_Type_Id)
                    ) {
                      reimbursementItem.Amount = item.Amount || 0;
                    }
                  }
                );
                this.templateDetails.allowances?.flexiBenefitPlanArray?.forEach(
                  (flexiBenefitItem) => {
                    if (
                      parseInt(item.Allowance_Type_Id) ===
                      parseInt(flexiBenefitItem.Allowance_Type_Id)
                    ) {
                      flexiBenefitItem.Amount = item.Amount || 0;
                    }
                  }
                );
              });
            }
            vm.anyValueUpdated = false;
          } else {
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message:
                data?.calculateSalary?.message ||
                "Something went wrong. Please try after some time.",
            };
            vm.showAlert(snackbarData);
          }
          vm.componentsLoading = false;
        })
        .catch((err) => {
          vm.componentsLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "system calculated components",
            isListError: false,
          });
        });
    },
  },
};
</script>
<style scoped>
.add-update-content {
  max-height: calc(100vh - 130px) !important;
  overflow-y: scroll;
}

:deep(.v-field--prepended) {
  padding-inline-start: 0;
}

:deep(.v-field--appended) {
  padding-inline-end: 0;
}

.custom-info-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 15px;
  height: 15px;
  border: 1.5px solid #9e9e9e;
  border-radius: 50%;
}
:deep(.custom-input-position .v-field__input) {
  text-align: end !important;
}
</style>
