<template>
  <div>
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="d-flex justify-end"
                :isFilter="true"
                :isDefaultFilter="false"
                @apply-emp-filter="onApplyFilter()"
                @reset-emp-filter="resetFilter()"
              >
                <template v-slot:new-filter>
                  <v-row class="mr-2 mt-2">
                    <v-col :cols="!isMobileView ? 6 : 12" class="py-2">
                      <v-autocomplete
                        v-model="selectedEmployees"
                        label="Employee"
                        variant="solo"
                        density="compact"
                        color="primary"
                        item-title="Employee_Name"
                        item-value="Employee_Id"
                        multiple
                        closable-chips
                        chips
                        :items="employeeList"
                      ></v-autocomplete>
                    </v-col>
                    <v-col
                      v-if="formId === 360"
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                    >
                      <v-autocomplete
                        v-model="selectedRequestType"
                        label="Revision Type"
                        variant="solo"
                        density="compact"
                        color="primary"
                        multiple
                        closable-chips
                        chips
                        :items="requestTypeList"
                      ></v-autocomplete>
                    </v-col>
                    <v-col :cols="!isMobileView ? 6 : 12" class="py-2">
                      <v-menu
                        v-model="effectiveDateMenu"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                        min-width="auto"
                      >
                        <template v-slot:activator="{ props }">
                          <v-text-field
                            v-bind="props"
                            readonly
                            variant="solo"
                            label="Effective From"
                            color="primary"
                            density="compact"
                            v-model="formattedEffectiveFrom"
                          >
                            <template v-slot:prepend-inner>
                              <v-icon>fas fa-calendar</v-icon>
                            </template>
                          </v-text-field>
                        </template>
                        <v-date-picker
                          v-model="effectiveFrom"
                          @update:model-value="effectiveDateMenu = false"
                        ></v-date-picker>
                      </v-menu>
                    </v-col>
                    <v-col
                      v-if="formId === 360"
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                    >
                      <v-menu
                        v-model="payoutMonthMenu"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                        min-width="auto"
                      >
                        <template v-slot:activator="{ props }">
                          <v-text-field
                            v-bind="props"
                            readonly
                            variant="solo"
                            label="Payout Month"
                            density="compact"
                            v-model="formattedPayoutMonth"
                          >
                            <template v-slot:prepend-inner>
                              <v-icon>fas fa-calendar</v-icon>
                            </template>
                          </v-text-field>
                        </template>
                        <Datepicker
                          v-model="payoutMonth"
                          :inline="true"
                          :format="'MMMM, yyyy'"
                          maximum-view="year"
                          minimum-view="month"
                          :open-date="payoutMonth"
                          @update:modelValue="payoutMonthMenu = false"
                        />
                      </v-menu>
                    </v-col>
                    <v-col
                      v-if="formId === 360"
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                    >
                      <v-autocomplete
                        v-model="selectedStatus"
                        label="Status"
                        variant="solo"
                        density="compact"
                        multiple
                        closable-chips
                        chips
                        :items="statusList"
                      ></v-autocomplete>
                    </v-col>
                    <v-col v-if="formId === 207" :cols="12" class="py-2 px-5">
                      <p class="text-subtitle-1 text-grey-darken-1">
                        Annual CTC
                      </p>

                      <v-range-slider
                        v-model="annualCtcRange"
                        max="100000000"
                        :step="1"
                        :strict="true"
                        thumb-label
                        color="primary"
                      >
                        <template v-slot:prepend>
                          <v-text-field
                            v-model="annualCtcRange[0]"
                            :min="0"
                            :max="100000000"
                            density="compact"
                            style="width: 100px"
                            type="number"
                            variant="solo"
                            hide-details
                          ></v-text-field>
                        </template>
                        <template v-slot:append>
                          <v-text-field
                            v-model="annualCtcRange[1]"
                            :min="0"
                            :max="100000000"
                            density="compact"
                            style="width: 100px"
                            type="number"
                            variant="solo"
                            hide-details
                          ></v-text-field>
                        </template>
                      </v-range-slider>
                    </v-col>
                    <v-col v-if="formId === 207" :cols="12" class="py-2 px-5">
                      <p class="text-subtitle-1 text-grey-darken-1">
                        Basic Pay
                      </p>
                      <v-range-slider
                        v-model="basicPayRange"
                        max="1000000"
                        :step="1"
                        :strict="true"
                        thumb-label
                        color="primary"
                      >
                        <template v-slot:prepend>
                          <v-text-field
                            v-model="basicPayRange[0]"
                            :min="0"
                            :max="1000000"
                            density="compact"
                            style="width: 100px"
                            type="number"
                            variant="solo"
                            hide-details
                          ></v-text-field>
                        </template>
                        <template v-slot:append>
                          <v-text-field
                            v-model="basicPayRange[1]"
                            :min="0"
                            :max="1000000"
                            density="compact"
                            style="width: 100px"
                            type="number"
                            variant="solo"
                            hide-details
                          ></v-text-field>
                        </template>
                      </v-range-slider>
                    </v-col>
                    <v-col v-if="formId === 360" :cols="12" class="py-2 px-5">
                      <p class="text-subtitle-1 text-grey-darken-1">
                        Previous CTC
                      </p>

                      <v-range-slider
                        v-model="previousCtcRange"
                        max="100000000"
                        :step="1"
                        :strict="true"
                        thumb-label
                        color="primary"
                      >
                        <template v-slot:prepend>
                          <v-text-field
                            v-model="previousCtcRange[0]"
                            :min="0"
                            :max="100000000"
                            density="compact"
                            style="width: 100px"
                            type="number"
                            variant="solo"
                            hide-details
                          ></v-text-field>
                        </template>
                        <template v-slot:append>
                          <v-text-field
                            v-model="previousCtcRange[1]"
                            :min="0"
                            :max="100000000"
                            density="compact"
                            style="width: 100px"
                            type="number"
                            variant="solo"
                            hide-details
                          ></v-text-field>
                        </template>
                      </v-range-slider>
                    </v-col>
                    <v-col v-if="formId === 360" :cols="12" class="py-2 px-5">
                      <p class="text-subtitle-1 text-grey-darken-1">
                        Revised CTC
                      </p>
                      <v-range-slider
                        v-model="revisedCtcRange"
                        max="100000000"
                        :step="1"
                        :strict="true"
                        thumb-label
                        color="primary"
                      >
                        <template v-slot:prepend>
                          <v-text-field
                            v-model="revisedCtcRange[0]"
                            :min="0"
                            :max="100000000"
                            density="compact"
                            style="width: 100px"
                            type="number"
                            variant="solo"
                            hide-details
                          ></v-text-field>
                        </template>
                        <template v-slot:append>
                          <v-text-field
                            v-model="revisedCtcRange[1]"
                            :min="0"
                            :max="100000000"
                            density="compact"
                            style="width: 100px"
                            type="number"
                            variant="solo"
                            hide-details
                          ></v-text-field>
                        </template>
                      </v-range-slider>
                    </v-col>
                  </v-row>
                </template>
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <ProfileCard class="sub-tabs" v-if="formAccess?.view">
      <FormTab :model-value="openedSubTab">
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          color="primary"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            <div class="d-flex align-center">
              {{ tab.label }}
            </div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard>
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item
          :value="currentTabItem"
          :class="windowWidth < 1280 ? 'mb-14' : ''"
        >
          <div class="mt-3 mx-5">
            <div v-if="listLoading" class="mt-5">
              <!-- Skeleton loaders -->
              <v-skeleton-loader
                ref="skeleton1"
                type="table-heading"
                class="mx-auto"
              ></v-skeleton-loader>
              <div v-for="i in 3" :key="i" class="mt-4">
                <v-skeleton-loader
                  ref="skeleton2"
                  type="list-item-avatar"
                  class="mx-auto"
                ></v-skeleton-loader>
              </div>
            </div>
            <AppFetchErrorScreen
              v-else-if="isErrorInList"
              :content="errorContent"
              icon-name="fas fa-redo-alt"
              image-name="common/human-error-image"
              :button-text="'Retry'"
              @button-click="refetchList()"
            >
            </AppFetchErrorScreen>

            <AppFetchErrorScreen
              v-else-if="itemList.length === 0"
              key="no-results-screen"
              :main-title="
                originalList.length === 0
                  ? ''
                  : 'There are no salary templates for the selected filters/searches.'
              "
              :isSmallImage="originalList.length === 0"
              :image-name="originalList.length === 0 ? '' : 'common/no-records'"
            >
              <template #contentSlot>
                <div style="max-width: 80%">
                  <v-row
                    :style="
                      originalList.length === 0 ? 'background: white' : ''
                    "
                    class="rounded-lg pa-5 mb-4"
                  >
                    <v-col v-if="originalList.length === 0" cols="12">
                      <NotesCard
                        v-if="formId == 207"
                        notes="As a admin, configuring employee salary details is a critical step in ensuring accurate payroll processing, compliance with statutory regulations, and clear communication of compensation to employees. Each employee must be assigned a salary structure based on their role, grade, and location. The structure defines how the total CTC (Cost to Company) is broken down into components."
                        backgroundColor="transparent"
                        class="mb-4"
                      ></NotesCard>
                      <NotesCard
                        v-if="formId == 207"
                        notes="Salary details involves setting up the individual salary details for each employee based on the assigned CTC structure. This includes defining fixed and variable components such as Basic Pay, Special Allowance, Deductions, Bonuses, and Employer Contributions. Ensure each employee is mapped to the appropriate salary structure, and monthly salary components are correctly entered to enable accurate payroll processing and statutory compliance."
                        backgroundColor="transparent"
                        class="mb-4"
                      ></NotesCard>
                      <NotesCard
                        v-if="formId == 360"
                        notes="The Salary Revision Update feature allows the Admin to modify or revise an employee's salary details. This can include changes to basic pay, allowances, deductions or any salary component as per company policy or performance review or promotions. All revisions are tracked with salary effective date and payout month and can be reflected in payroll processing and payslips accordingly."
                        backgroundColor="transparent"
                        class="mb-4"
                      ></NotesCard>
                      <NotesCard
                        v-if="formId == 360"
                        notes="Admin can select the specific employee whose salary needs to be revised, either individually or through a bulk update. Every salary revision is logged with timestamp, admin user details, and revision history for compliance and transparency. After the approval, revised salary details automatically picked up during the payroll run for the relevant month. If the effective date is within the payroll period, the system can calculate arrears accordingly."
                        backgroundColor="transparent"
                        class="mb-4"
                      ></NotesCard>
                    </v-col>
                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                    >
                      <v-btn
                        v-if="
                          originalList.length === 0 &&
                          subFormAccess.add &&
                          formId == 207
                        "
                        variant="elevated"
                        class="ml-4 mt-1 primary"
                        prepend-icon="fas fa-plus"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="onActions('add')"
                      >
                        <span>Add Salary</span>
                      </v-btn>
                      <v-btn
                        v-if="originalList.length > 0"
                        color="primary"
                        variant="elevated"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="resetFilter()"
                      >
                        Reset Filter/Search
                      </v-btn>
                      <v-btn
                        v-if="originalList.length === 0"
                        rounded="lg"
                        class="mt-1"
                        color="transparent"
                        variant="flat"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="refetchList()"
                      >
                        <v-icon>fas fa-redo-alt</v-icon>
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </template>
            </AppFetchErrorScreen>

            <div v-else>
              <div
                style="width: 100%"
                :class="{
                  'py-1 mb-2': true,
                  'd-flex justify-end align-center': !isMobileView,
                  'd-flex justify-center align-center flex-wrap': isMobileView,
                }"
              >
                <v-btn
                  v-if="subFormAccess?.add && formId == 207"
                  color="primary"
                  variant="elevated"
                  class="mr-1"
                  rounded="lg"
                  prepend-icon="fas fa-plus"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="onActions('add')"
                >
                  Add Salary
                </v-btn>
                <v-btn
                  rounded="lg"
                  color="transparent"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn variant="plain" v-bind="props">
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in ['Export']"
                      :key="action"
                      @click="onActions(action)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'bg-hover': isHovering,
                            }"
                            >{{ action }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
              <v-row>
                <v-col cols="12">
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="onActions('view', item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          class="bg-white"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'min-width: 150px'
                          "
                          style="position: sticky; left: 0"
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Employee
                          </div>
                          <section
                            class="text-primary font-weight-medium d-flex align-center"
                          >
                            <div>
                              <v-tooltip
                                :text="item.Employee_Name"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <div v-bind="props">
                                    {{ checkNullValue(item.Employee_Name) }}
                                  </div>
                                </template>
                              </v-tooltip>
                              <v-tooltip
                                :text="item.User_Defined_EmpId?.toString()"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <div
                                    v-if="item.User_Defined_EmpId"
                                    v-bind="props"
                                    class="text-grey text-caption"
                                  >
                                    {{
                                      checkNullValue(item.User_Defined_EmpId)
                                    }}
                                  </div>
                                </template>
                              </v-tooltip>
                            </div>
                          </section>
                        </td>
                        <td
                          v-if="formId === 207"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'min-width: 150px'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Annual CTC
                          </div>
                          <section class="d-flex align-center">
                            {{ checkNullValue(item.Annual_CTC) }}
                          </section>
                        </td>
                        <td
                          v-if="formId === 207"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'min-width: 150px'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Basic Pay
                          </div>
                          <section class="d-flex align-center">
                            {{ checkNullValue(item.Basic_Pay) }}
                          </section>
                        </td>
                        <td
                          v-if="formId === 360"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'min-width: 150px'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Revision Type
                          </div>
                          <section class="d-flex align-center">
                            {{ checkNullValue(item.revisionType) }}
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'min-width: 150px'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Effective From
                          </div>
                          <section class="d-flex align-center">
                            {{
                              formId === 360
                                ? checkNullValue(item.Effective_Month)
                                : checkNullValue(item.Effective_From)
                            }}
                          </section>
                        </td>
                        <td
                          v-if="formId === 360"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'min-width: 150px'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Payout Month
                          </div>
                          <section class="d-flex align-center">
                            {{ checkNullValue(item.Payout_Month) }}
                          </section>
                        </td>
                        <td
                          v-if="formId === 360"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'min-width: 150px'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Previous CTC
                          </div>
                          <section class="d-flex align-center">
                            {{ checkNullValue(item.previousCtc) }}
                          </section>
                        </td>
                        <td
                          v-if="formId === 360"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'min-width: 150px'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Revised CTC
                          </div>
                          <section class="d-flex align-center">
                            {{ checkNullValue(item.Annual_CTC) }}
                          </section>
                        </td>
                        <td
                          v-if="formId === 360"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'min-width: 150px'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Status
                          </div>
                          <section
                            :class="getStatusColor(item.revisionStatus)"
                            class="d-flex align-center"
                          >
                            {{ checkNullValue(item.revisionStatus) }}
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Actions
                          </div>
                          <section class="d-flex justify-end align-center">
                            <ActionMenu
                              v-if="itemActions.length"
                              :accessRights="havingAccess"
                              @selected-action="onActions($event, item)"
                              :actions="itemActions"
                              iconColor="grey"
                            />
                            <div v-else>
                              <p>-</p>
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </div>
          </div>
          <AddUpdate
            v-if="showAddEditForm"
            :form-id="formId"
            :show-form="showAddEditForm"
            :payrollCurrency="payrollCurrency"
            :selected-item="selectedItem"
            :edit-type="editType"
            :landed-form-name="openedSubTab"
            @add-update-success="refetchList('updated')"
            @close-form="closeAllForms()"
          />
          <ViewForm
            v-if="showViewForm"
            :form-id="formId"
            :show-form="showViewForm"
            :selected-item="selectedItem"
            :payrollCurrency="payrollCurrency"
            :formName="openedSubTab"
            @open-edit-form="onActions('edit', $event)"
            @close-form="closeAllForms()"
          />
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <ApprovalFlowModal
      v-if="openApprovalModal"
      :task-id="selectedItem.Workflow_Instance_Id"
      @close-modal="closeAllForms()"
    />
  </div>
  <AppLoading v-if="isLoading" />
</template>

<script>
import { defineAsyncComponent } from "vue";
import {
  RETRIEVE_SALARY_LIST,
  DELETE_SALARY_DETAILS,
} from "@/graphql/corehr/salaryQueries.js";

import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue } from "@/helper";

const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const ViewForm = defineAsyncComponent(() => import("./ViewForm.vue"));
const AddUpdate = defineAsyncComponent(() => import("./AddUpdate.vue"));
import ApprovalFlowModal from "@/components/custom-components/ApprovalFlowModal.vue";
import Datepicker from "vuejs3-datepicker";
export default {
  name: "SalaryInfo",
  components: {
    ViewForm,
    AddUpdate,
    NotesCard,
    ActionMenu,
    Datepicker,
    EmployeeDefaultFilterMenu,
    ApprovalFlowModal,
  },
  mixins: [FileExportMixin],
  data: () => ({
    currentTabItem: "",
    landedFormName: "Salary Info",
    openedSubTab: "Salary Details",
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    itemList: [],
    originalList: [],
    payrollCurrency: "",
    selectedEmployees: [],
    selectedRequestType: [],
    effectiveDateMenu: false,
    effectiveFrom: null,
    formattedEffectiveFrom: null,
    basicPayRange: [],
    annualCtcRange: [],
    showAddEditForm: false,
    showViewForm: false,
    openMoreMenu: false,
    selectedItem: null,
    editType: "",
    payoutMonthMenu: false,
    payoutMonth: new Date(),
    formattedPayoutMonth: null,
    selectedStatus: [],
    previousCtcRange: [],
    revisedCtcRange: [],
    isLoading: false,
    openApprovalModal: false,
  }),
  computed: {
    formId() {
      if (this.openedSubTab?.toLowerCase() === "salary details") {
        return 207;
      } else if (this.openedSubTab?.toLowerCase() === "salary revision") {
        return 360;
      } else {
        return 0;
      }
    },
    formAccess() {
      let formAccess = this.$store.getters.formIdBasedAccessRights(207);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else return false;
    },
    getStatusColor() {
      return (status) => {
        if (status?.toLowerCase() === "applied") {
          return "text-blue";
        } else if (status?.toLowerCase() === "approved") {
          return "text-green";
        } else if (status?.toLowerCase() === "rejected") {
          return "text-red";
        } else {
          return "text-orange";
        }
      };
    },
    subFormAccess() {
      let formAccess = this.$store.getters.formIdBasedAccessRights(this.formId);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else return false;
    },
    isActiveSubTab() {
      return (val) => {
        return this.openedSubTab === val;
      };
    },
    subTabItems() {
      let revisionAccess = this.$store.getters.formIdBasedAccessRights(360);
      let initialTabs = [
        {
          label: "Salary Details",
          value: "Salary Details",
          disable: false,
        },
      ];
      if (
        revisionAccess &&
        revisionAccess.accessRights &&
        revisionAccess.accessRights["view"]
      ) {
        initialTabs.push({
          label: revisionAccess.customFormName || "Salary Revision",
          value: "Salary Revision",
          disable: false,
        });
      }
      return initialTabs;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.$store.getters.coreHrPayrollDataManagementFormAcess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        if (
          formAccessArray?.length &&
          this.formAccess &&
          this.approvalFormAccess
        ) {
          const index = formAccessArray.indexOf("Reports");
          if (!formAccessArray.includes("Approvals"))
            formAccessArray.splice(index, 0, "Approvals");
          // Insert before "Reports"
          else formAccessArray.push("Approvals");
        }
        return formAccessArray;
      }
      return [];
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    approvalFormAccess() {
      let formAccess = this.accessRights("184");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "";
      };
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    employeeList() {
      const seen = new Set();
      let uniqueEmployees = this.originalList.filter((emp) => {
        if (seen.has(emp.Employee_Id)) return false;
        seen.add(emp.Employee_Id);
        return true;
      });
      uniqueEmployees = uniqueEmployees.map((emp) => {
        return {
          Employee_Id: emp.Employee_Id,
          Employee_Name: emp.Employee_Name + " - " + emp.User_Defined_EmpId,
        };
      });
      return uniqueEmployees;
    },
    requestTypeList() {
      const seen = new Set();
      this.originalList.map((emp) => {
        if (seen.has(emp.Request_Type)) return false;
        seen.add(emp.Request_Type);
        return true;
      });
      return Array.from(seen).filter((item) => item);
    },
    statusList() {
      const seen = new Set();
      this.originalList.map((emp) => {
        seen.add(emp.revisionStatus);
        return true;
      });
      return Array.from(seen).filter((item) => item);
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    itemActions() {
      let actions = [];
      if (this.formId == 207) {
        if (this.subFormAccess.update) {
          actions.push("Edit");
        }
        if (this.subFormAccess.update) {
          actions.push("Revise");
        }
        if (this.subFormAccess.delete) {
          actions.push("Delete");
        }
      } else {
        // if (this.subFormAccess.view) {
        //   actions.push("Approval Workflow");
        // }
        if (this.subFormAccess.delete) {
          actions.push("Delete");
        }
      }
      return actions;
    },
    havingAccess() {
      return {
        view: this.subFormAccess.view,
        update: this.subFormAccess.update,
        revise: this.subFormAccess.update,
        delete: this.subFormAccess.delete,
        "approval workflow": this.subFormAccess.view,
      };
    },
    tableHeaders() {
      let headers = [];
      if (this.formId == 207) {
        headers.push(
          { title: "Employee", key: "Employee_Name", fixed: true },
          {
            title: this.payrollCurrency
              ? `Annual CTC (${this.payrollCurrency})`
              : `Annual CTC`,
            key: "Annual_CTC",
          },
          {
            title: this.payrollCurrency
              ? `Basic Pay (${this.payrollCurrency})`
              : `Basic Pay`,
            key: "Basic_Pay",
          },
          { title: "Effective From", key: "Effective_From" },
          {
            title: "Actions",
            key: "action",
            align: "end",
            sortable: false,
            width: "10%",
          }
        );
      }
      if (this.formId === 360) {
        headers.push(
          { title: "Employee", key: "Employee_Name", fixed: true },
          { title: "Revision Type", key: "revisionType" },
          { title: "Effective From", key: "Effective_Month" },
          { title: "Payout Month", key: "Payout_Month" },
          { title: "Previous CTC", key: "previousCtc" },
          { title: "Revised CTC", key: "revisedCtc" },
          { title: "Status", key: "revisionStatus" },
          {
            title: "Actions",
            key: "action",
            align: "end",
            sortable: false,
            width: "10%",
          }
        );
      }
      return headers;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    effectiveFrom(val) {
      if (val) {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        this.formattedEffectiveFrom = moment(val).format(orgDateFormat);
      }
    },
    payoutMonth(val) {
      if (val) {
        this.formattedPayoutMonth = moment(val).format("MMM YYYY");
      }
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    let url_string = window.location.href;
    let url = new URL(url_string);
    let tab = url.searchParams.get("formId");
    if (tab === "360") {
      this.openedSubTab = "Salary Revision";
    }
    this.fetchList();
  },
  methods: {
    checkNullValue,
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        if (tab?.toLowerCase() === "approvals") {
          this.$router.push("/approvals/approval-management?form_id=360");
          return;
        }
        const { formAccess } =
          this.$store.getters.coreHrPayrollDataManagementFormAcess;
        let clickedForm = formAccess[tab];
        this.$router.push("/core-hr/" + clickedForm.url);
      }
    },
    onChangeSubTabs(tab) {
      if (tab !== this.openedSubTab) {
        this.openedSubTab = tab;
        this.refetchList();
      }
    },
    onActions(action, item) {
      if (action?.toLowerCase() === "export") {
        this.exportData();
      } else if (action?.toLowerCase() === "view") {
        this.selectedItem = item;
        this.showViewForm = true;
        this.showAddEditForm = false;
      } else if (action?.toLowerCase() === "edit") {
        this.selectedItem = item;
        this.editType = "Edit";
        this.showAddEditForm = true;
      } else if (action?.toLowerCase() === "revise") {
        this.selectedItem = item;
        this.editType = "Revise";
        this.showAddEditForm = true;
      } else if (action?.toLowerCase() === "delete") {
        let variables = {};
        if (this.formId == 207) {
          variables = {
            formId: this.formId,
            employeeId: item.Employee_Id,
          };
        } else {
          variables = {
            formId: this.formId,
            id: item.Revision_Id,
          };
        }
        this.deleteSalaryDetails(variables);
      } else if (action?.toLowerCase() === "add") {
        this.selectedItem = null;
        this.editType = "";
        this.showAddEditForm = true;
      } else if (action?.toLowerCase() === "approval workflow") {
        this.selectedItem = item;
        this.openApprovalModal = true;
      }
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.openApprovalModal = false;
      this.editType = "";
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    onApplyFilter() {
      let filteredItems = this.originalList;
      if (this.selectedEmployees.length) {
        filteredItems = filteredItems.filter((item) => {
          return this.selectedEmployees.includes(item.Employee_Id);
        });
      }
      if (this.effectiveFrom) {
        filteredItems = filteredItems.filter((item) => {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return (
            moment(item.Effective_From, orgDateFormat).format("YYYY-MM-DD") ===
            moment(this.effectiveFrom).format("YYYY-MM-DD")
          );
        });
      }
      if (this.annualCtcRange.length) {
        filteredItems = filteredItems.filter((item) => {
          return (
            item.Annual_CTC >= this.annualCtcRange[0] &&
            item.Annual_CTC <= this.annualCtcRange[1]
          );
        });
      }
      if (this.basicPayRange.length) {
        filteredItems = filteredItems.filter((item) => {
          return (
            item.Basic_Pay >= this.basicPayRange[0] &&
            item.Basic_Pay <= this.basicPayRange[1]
          );
        });
      }
      if (this.formattedPayoutMonth) {
        filteredItems = filteredItems.filter((item) => {
          return item.Payout_Month === this.formattedPayoutMonth;
        });
      }
      if (this.previousCtcRange.length) {
        filteredItems = filteredItems.filter((item) => {
          return (
            item.previousCtc >= this.previousCtcRange[0] &&
            item.previousCtc <= this.previousCtcRange[1]
          );
        });
      }
      if (this.revisedCtcRange.length) {
        filteredItems = filteredItems.filter((item) => {
          return (
            item.revisedCtc >= this.revisedCtcRange[0] &&
            item.revisedCtc <= this.revisedCtcRange[1]
          );
        });
      }
      this.itemList = filteredItems;
    },
    resetFilter() {
      this.selectedEmployees = [];
      this.effectiveFrom = null;
      this.formattedEffectiveFrom = "";
      this.annualCtcRange = [];
      this.basicPayRange = [];
      this.payoutMonth = new Date();
      this.formattedPayoutMonth = "";
      this.itemList = this.originalList;
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_SALARY_LIST,
          client: "apolloClientF",
          variables: {
            formId: this.formId,
          },
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let salaryList = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );
            salaryList.forEach((element) => {
              element.Effective_From = this.formatDate(element.Effective_From);
              element.Effective_Month = moment(
                element.Salary_Effective_Month,
                "M,YYYY"
              ).isValid()
                ? moment(element.Salary_Effective_Month, "M,YYYY").format(
                    "MMM YYYY"
                  )
                : "";
              element.Payout_Month = moment(
                element.Payout_Month,
                "M,YYYY"
              ).isValid()
                ? moment(element.Payout_Month, "M,YYYY").format("MMM YYYY")
                : "";
            });
            vm.payrollCurrency = data.listSalaryTemplateDetails.currencySymbol;
            vm.originalList = salaryList;
            vm.itemList = salaryList;
          } else {
            vm.originalList = [];
            vm.itemList = [];
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.originalList = [];
          vm.itemList = [];
          this.$store
            .dispatch("handleApiErrors", {
              error: err,
              action: "retrieve",
              form: "salary template",
              isListError: true,
            })
            .then((errorMessages) => {
              this.errorContent = errorMessages;
              this.isErrorInList = true;
            });
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.closeAllForms();
      this.fetchList();
    },
    exportData() {
      let exportHeaders = [];
      if (this.formId === 207) {
        exportHeaders = [
          {
            header: "Employee Id",
            key: "User_Defined_EmpId",
          },
          {
            header: "Employee Name",
            key: "Employee_Name",
          },
          {
            header: `Annual CTC ${this.payrollCurrency}`,
            key: "Annual_CTC",
          },
          {
            header: `Basic Pay ${this.payrollCurrency}`,
            key: "Basic_Pay",
          },
          {
            header: "Effective From",
            key: "Effective_From",
          },
        ];
      }
      if (this.formId === 360) {
        exportHeaders = [
          {
            header: "Employee Id",
            key: "User_Defined_EmpId",
          },
          {
            header: "Employee Name",
            key: "Employee_Name",
          },
          {
            header: "Revision Type",
            key: "revisionType",
          },
          {
            header: "Effective From",
            key: "Effective_From",
          },
          {
            header: "Payout Month",
            key: "Payout_Month",
          },
          {
            header: `Previous CTC ${this.payrollCurrency}`,
            key: "previousCtc",
          },
          {
            header: `Revised CTC ${this.payrollCurrency}`,
            key: "Annual_CTC",
          },
          {
            header: "Status",
            key: "revisionStatus",
          },
        ];
      }
      const exportOptions = {
        fileExportData: this.itemList,
        fileName: this.openedSubTab,
        sheetName: this.openedSubTab,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },

    deleteSalaryDetails(variables) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_SALARY_DETAILS,
          client: "apolloClientF",
          variables: variables,
          fetchPolicy: "no-cache",
        })
        .then(() => {
          vm.refetchList();
          let snackbarData = {
            isOpen: true,
            message: "Salary details deleted successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
        })
        .catch((err) => {
          this.$store.dispatch("handleApiErrors", {
            error: err,
            action: "delete",
            form: "salary template",
            isListError: false,
          });
          vm.isLoading = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style scoped>
.container {
  padding: 58px 0px 0px 0px;
}
.sub-tabs {
  position: sticky;
  top: 118px;
  z-index: 100;
}
</style>
