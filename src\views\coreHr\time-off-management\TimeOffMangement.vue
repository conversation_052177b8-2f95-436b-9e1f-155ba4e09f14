<template>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppAccessDenied v-else-if="isAccessDenied"></AppAccessDenied>
</template>

<script>
export default {
  name: "TimeOffManagement",
  data() {
    return { isLoading: true, isAccessDenied: false };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(276);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    coreHrTimeOffTabs() {
      return this.$store.getters.coreHrTimeOffTabs;
    },
  },
  mounted() {
    if (this.formAccess?.view) {
      let { isAnyOneFormHaveAccess, formsWithAccess } = this.coreHrTimeOffTabs;
      if (isAnyOneFormHaveAccess) {
        for (let tab of formsWithAccess) {
          if (tab.havingAccess) {
            this.$router.push(tab.url);
            break;
          }
        }
      } else {
        this.isAccessDenied = true;
        this.isLoading = false;
      }
    } else {
      this.isAccessDenied = true;
      this.isLoading = false;
    }
  },
};
</script>
