<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :class="openedSubTab == 'activities' ? '' : 'mr-8'"
                :isFilter="false"
              ></EmployeeDefaultFilterMenu>
              <FormFilter
                v-if="!isLoading"
                :originalList="originalList"
                :selectedMonthYear="selectedMonthYear"
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="attendance-approval-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <ProfileCard class="mt-5 pt-2" style="height: 55px">
            <FormTab :model-value="openedSubTab" :hide-slider="true">
              <v-tab
                v-for="(tab, i) in subTabItems"
                :key="tab.value"
                :value="tab.value"
                :disabled="tab.disable"
                @click="onChangeSubTabs(tab.value)"
                class="d-flex align-center"
              >
                <div
                  :class="[
                    isActiveSubTab(tab.value)
                      ? 'text-primary font-weight-bold'
                      : 'text-grey-darken-2 font-weight-bold',
                  ]"
                  style="height: 45px"
                >
                  <div class="d-flex align-center" style="height: 30px">
                    {{ tab.label }}
                    <div
                      v-if="tabCount && tabCount.length > 0 && tabCount[i] > 0"
                      class="ml-2"
                      style="width: 30px; height: 100%"
                    >
                      <v-badge
                        v-if="openedSubTab === tab.value"
                        color="primary"
                        variant="elevated"
                        size="small"
                        :content="tabCount[i]"
                      ></v-badge>
                    </div>
                  </div>
                  <div
                    v-if="isActiveSubTab(tab.value)"
                    class="mt-3 mb-n4"
                    style="border-bottom: 4px solid"
                  ></div>
                </div>
              </v-tab>
            </FormTab>
          </ProfileCard>
          <v-row>
            <v-col cols="12">
              <v-window v-model="openedSubTab" style="width: 100%">
                <v-window-item value="attendanceGeneralApprovals">
                  <AttendanceGeneralApprovals
                    v-if="openedSubTab == 'attendanceGeneralApprovals'"
                    :itemsList="itemList"
                    :isErrorInApprovalList="isErrorInList"
                    :isListLoading="isLoading"
                    @refetch-list="fetchList($event)"
                    @change-count="tabCount = [$event, 0, 0]"
                    ref="attendanceGeneralApprovals"
                  ></AttendanceGeneralApprovals>
                </v-window-item>
                <v-window-item value="attendanceWeekOffApprovals">
                  <AttendanceWeekOffApprovals
                    v-if="openedSubTab == 'attendanceWeekOffApprovals'"
                    :itemsList="itemList"
                    :isErrorInApprovalList="isErrorInList"
                    :isListLoading="isLoading"
                    @refetch-list="fetchList($event)"
                    @change-count="tabCount = [0, $event, 0]"
                  ></AttendanceWeekOffApprovals>
                </v-window-item>
                <v-window-item value="attendanceRegularizationsApprovals">
                  <AttendanceRegularizationsApprovals
                    v-if="openedSubTab == 'attendanceRegularizationsApprovals'"
                    :itemsList="itemList"
                    :isErrorInApprovalList="isErrorInList"
                    :isListLoading="isLoading"
                    @refetch-list="fetchList($event)"
                    @change-count="tabCount = [0, 0, $event]"
                  ></AttendanceRegularizationsApprovals>
                </v-window-item>
              </v-window>
            </v-col>
          </v-row>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
</template>

<script>
import Config from "@/config.js";
import { defineAsyncComponent } from "vue";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import AttendanceGeneralApprovals from "./AttendanceGeneralApprovals.vue";
import AttendanceWeekOffApprovals from "./AttendanceWeekOffApprovals.vue";
import AttendanceRegularizationsApprovals from "./AttendanceRegularizationsApprovals.vue";
import { LIST_ATTENDANCE_APPROVAL_RECORDS } from "@/graphql/my-team/attendance.js";
import mixpanel from "mixpanel-browser";
const FormFilter = defineAsyncComponent(() =>
  import("./AttendanceApprovalFilter.vue")
);
export default {
  name: "AttendanceApprovals",

  components: {
    EmployeeDefaultFilterMenu,
    AttendanceGeneralApprovals,
    AttendanceWeekOffApprovals,
    AttendanceRegularizationsApprovals,
    FormFilter,
  },

  data() {
    return {
      currentTabItem: "",
      openedSubTab: "attendanceGeneralApprovals",
      isLoading: false,
      projectsBackupList: [],
      projectsFilteredList: [],
      tabCount: [],
      applyFilterCount: 0,
      resetFilterCount: 0,
      itemList: [],
      originalList: [],
      selectedMonthYear: null,
      flag: "normal",
      isErrorInList: false,
    };
  },

  computed: {
    // check the selected filter month is current month or not
    landedFormName() {
      return "Approvals";
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    attendanceFormAccess() {
      return this.$store.getters.attendanceFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.attendanceFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },

    subTabItems() {
      return [
        {
          label: "General Approvals",
          value: "attendanceGeneralApprovals",
          disable: false,
        },
        {
          label: "Week Off/ Holiday",
          value: "attendanceWeekOffApprovals",
          disable: false,
        },
        {
          label: "Regularization",
          value: "attendanceRegularizationsApprovals",
          disable: false,
        },
      ];
    },

    formAccess() {
      let formAccessRights = this.accessRights(313);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
  },
  watch: {
    openedSubTab(val) {
      if (val == "attendanceGeneralApprovals") {
        this.flag = "normal";
      } else if (val == "attendanceWeekOffApprovals") {
        this.flag = "weekoff";
      } else if (val == "attendanceRegularizationsApprovals") {
        this.flag = "regularized";
      }
      this.fetchList();
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.fetchList();
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
  },

  methods: {
    fetchList(selectedDate = null) {
      let vm = this;
      vm.isLoading = true;
      this.isErrorInList = false;
      vm.resetFilter();
      let month = null,
        year = null;
      if (selectedDate) {
        month = selectedDate.getMonth() + 1;
        year = selectedDate.getFullYear();
      } else {
        month = new Date().getMonth() + 1;
        year = new Date().getFullYear();
      }
      vm.$apollo
        .query({
          query: LIST_ATTENDANCE_APPROVAL_RECORDS,
          client: "apolloClientAC",
          variables: {
            attendanceMonth: month,
            attendanceYear: year,
            formId: 313,
            flag: this.flag,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listAttendanceApprovalRecords &&
            response.data.listAttendanceApprovalRecords
              .attendanceApprovalDetails &&
            !response.data.listAttendanceApprovalRecords.errorCode
          ) {
            let responseData =
              response.data.listAttendanceApprovalRecords
                .attendanceApprovalDetails;
            if (this.flag.toLowerCase() == "normal") {
              this.tabCount = [responseData.length, 0, 0];
            } else if (this.flag.toLowerCase() == "weekoff") {
              this.tabCount = [0, responseData.length, 0];
            } else if (this.flag.toLowerCase() == "regularized") {
              this.tabCount = [0, 0, responseData.length];
            }
            vm.itemList = responseData;
            vm.originalList = responseData;
            vm.isLoading = false;
          } else {
            let error = response.data.listAttendanceApprovalRecords.errorCode;
            vm.handleListError(error);
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Attendance Approvals",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    applyFilter(filteredArray) {
      this.itemList = filteredArray;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    resetFilter() {
      this.itemList = this.originalList;
      this.isFilter = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
      this.isLoading = true;
    },
    onTabChange(tab) {
      mixpanel.track("Approvals form tab changed");
      if (tab !== this.landedFormName) {
        const { formAccess } = this.attendanceFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/my-team/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/my-team/" + clickedForm.url;
        }
      }
    },
  },
};
</script>

<style>
.attendance-approval-container {
  padding: 2.5em 0em 0em 0em;
}
.v-dialog {
  box-shadow: none;
}
@media screen and (max-width: 805px) {
  .attendance-approval-container {
    padding: 2.5em 1em 0em 1em;
  }
}
</style>
