<template>
  <div>
    <div v-if="listLoading" class="ma-4">
      <v-skeleton-loader type="table-heading, table-tbody"></v-skeleton-loader>
    </div>
    <div v-else class="py-3 px-5">
      <div
        class="d-flex align-center mb-2"
        :class="isMobileView ? 'flex-column' : 'flex-row'"
      >
        <v-btn
          v-if="callingForm?.toLowerCase() === 'team'"
          variant="text"
          class="d-flex align-center pa-0"
          rounded="lg"
          dense
          @click="$emit('close-view-form')"
        >
          <template class="d-flex align-center">
            <v-icon color="primary">fas fa-angle-left fa-lg</v-icon>
            <span class="text-primary text-decoration-underline">Back</span>
          </template>
        </v-btn>
        <div class="d-flex align-center flex-wrap justify-center">
          <div class="d-flex ml-3 mr-2">
            <div class="mr-1 holiday-box"></div>
            <strong>H</strong> : Holiday
          </div>
          <div class="d-flex mr-2">
            <div class="mr-1 weekoff-box"></div>
            <strong>WO</strong> : Week Off
          </div>
          <div class="d-flex mr-2"><strong>L</strong> : Leave</div>
          <div class="d-flex mr-2"><strong>CO</strong> : Comp Off</div>
          <div class="d-flex mr-2"><strong>P</strong> : Present</div>
          <div class="d-flex mr-2"><strong>AB</strong> : Absent</div>
          <div class="d-flex mr-2">
            <strong>AEYP</strong> : Attendance entries yet to be processed
          </div>
        </div>
        <v-spacer></v-spacer>
        <v-menu
          v-if="callingForm?.toLowerCase() === 'team'"
          id="activitytracker_my_activity_date_picker"
          v-model="employeeListMenu"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
        >
          <template v-slot:activator="{ props: activatorProps }">
            <v-btn
              class="bg-white my-2 ml-2"
              :class="{
                'employee-list-btn': isMobileView,
              }"
              rounded="lg"
              dense
              v-bind="activatorProps"
            >
              <template v-slot:prepend>
                <v-icon color="primary" class="mr-1" size="17">
                  fas fa-user-alt
                </v-icon>
              </template>
              <span
                style="max-width: 300px"
                class="text-primary font-weight-bold text-truncate"
              >
                {{ employeeData }}
              </span>
              <template v-slot:append>
                <v-icon color="primary" class="ml-1" size="17">
                  {{
                    employeeListMenu ? "fas fa-caret-up" : "fas fa-caret-down"
                  }}
                </v-icon>
              </template>
            </v-btn>
          </template>
          <div
            ref="employeeListContainer"
            style="
              min-height: 100px;
              max-height: 300px;
              overflow-y: scroll;
              background-color: white;
            "
            class="white pa-2 pt-0"
          >
            <div
              style="
                position: sticky;
                top: 0;
                background-color: white;
                height: 40px;
              "
            >
              <v-text-field
                v-model="searchEmployee"
                density="compact"
                variant="underlined"
                hide-details
                @update:model-value="onSearchEmployee($event)"
              >
                <template v-slot:prepend-inner>
                  <v-icon>fas fa-search</v-icon>
                </template>
              </v-text-field>
            </div>
            <div v-if="allEmployeesList && allEmployeesList.length > 0">
              <div
                v-for="employee in allEmployeesList"
                :key="employee.employeeId"
                :ref="
                  employee.employeeId === employeeDetails?.Employee_Id
                    ? 'selectedEmployeeRef'
                    : null
                "
                @click="onChangeEmployee(employee)"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <div
                      v-bind="props"
                      class="pa-2 my-2 rounded-lg cursor-pointer"
                      :class="{
                        'bg-hover':
                          isHovering &&
                          employeeDetails?.Employee_Id !== employee.employeeId,
                        'bg-primary text-white':
                          employeeDetails?.Employee_Id === employee.employeeId,
                        'bg-grey-lighten-4 text-primary':
                          !isHovering &&
                          employeeDetails?.Employee_Id !== employee.employeeId,
                      }"
                    >
                      <div
                        class="text-body-2 text-break"
                        style="max-width: 300px"
                      >
                        {{ employee.employeeData }}
                      </div>
                    </div>
                  </template>
                </v-hover>
              </div>
            </div>
            <div
              v-else
              style="height: 100px"
              class="text-grey rounded-lg d-flex justify-center align-center"
            >
              No data available
            </div>
          </div>
        </v-menu>
        <v-btn class="bg-white my-2 ml-2" rounded="lg">
          <template v-slot:prepend>
            <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
          </template>
          {{ formattedSelectedMonth }}
          <v-menu
            activator="parent"
            :close-on-content-click="false"
            transition="scale-transition"
            offset-y
          >
            <Datepicker
              v-model="selectedMonthYear"
              :inline="true"
              :format="'MMMM, yyyy'"
              maximum-view="year"
              minimum-view="month"
              :open-date="selectedMonthYear"
              :disabled-dates="disabledDates"
              @update:modelValue="onChangeDate($event)"
            />
          </v-menu>
        </v-btn>
        <v-btn
          rounded="lg"
          color="transparent"
          variant="flat"
          class="mt-1"
          :size="isMobileView ? 'small' : 'default'"
          @click="$emit('refetch-list')"
        >
          <v-icon>fas fa-redo-alt</v-icon>
        </v-btn>
      </div>
      <v-row>
        <AppFetchErrorScreen
          v-if="
            attendanceDetails.length === 0 &&
            !employeeDetails.User_Defined_EmpId &&
            !employeeDetails.Employee_Name
          "
          key="no-results-screen"
          :isSmallImage="attendanceDetails.length === 0"
          :image-name="
            attendanceDetails.length === 0 ? '' : 'common/no-records'
          "
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row
                :style="
                  attendanceDetails.length === 0 ? 'background: white' : ''
                "
                class="rounded-lg pa-5 mb-4"
              >
                <v-col
                  v-if="
                    attendanceDetails.length === 0 &&
                    !employeeDetails.User_Defined_EmpId &&
                    !employeeDetails.Employee_Name
                  "
                  cols="12"
                >
                  <NotesCard
                    notes=""
                    backgroundColor="transparent"
                    class="mb-4"
                  >
                    <template v-slot:notesCardContent>
                      <div>
                        Job information for this employee is incomplete. Please
                        provide the necessary details, including the work
                        schedule and employment type, to enable attendance
                        tracking. You can update the employee's information in
                        the Team Summary section. Click
                        <a :href="`${baseUrl}v3/my-team/team-summary`">here</a>
                        to update.
                      </div>
                    </template>
                  </NotesCard>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <v-col v-else :cols="openViewModal && !isMobileView ? 7 : 12">
          <div
            class="bg-primary pa-2 d-flex justify-space-between align-center"
          >
            <v-icon
              size="20"
              @click="!disableLeft ? changeMonth(-1) : {}"
              :disabled="disableLeft"
              no-details
              >fas fa-chevron-left</v-icon
            >
            <span class="text-h6">{{ formattedSelectedMonth }}</span>
            <v-icon
              size="20"
              @click="disableRight ? {} : changeMonth(1)"
              :disabled="disableRight"
              :style="disableRight ? 'cursor: not-allowed' : ''"
              no-details
              >fas fa-chevron-right</v-icon
            >
          </div>
          <vue-cal
            class="attendance-calendar"
            :disable-views="['years', 'year', 'week', 'day']"
            :hideViewSelector="true"
            :hide-title-bar="true"
            active-view="month"
            :selected-date="selectedMonthYear"
          >
            <template #cell-content="{ cell }">
              <div
                :class="customColor(cell.formattedDate)"
                class="d-flex flex-column justify-space-between"
                style="height: 100%"
                @click="openViewForm(cell.formattedDate)"
              >
                <div class="d-flex flex-start pl-2 font-weight-bold">
                  {{ getDate(cell.formattedDate) }}
                </div>
                <div
                  class="font-weight-bold"
                  v-html="getCustomTitle(cell.formattedDate)"
                ></div>
                <div>
                  <span>
                    {{ getCheckInCheckOut(cell.formattedDate) }}
                  </span>
                  <span v-if="overlapDays(cell.formattedDate)"
                    ><sup>+1</sup></span
                  >
                </div>
              </div>
            </template>
          </vue-cal>
        </v-col>
        <v-col cols="5" v-if="openViewModal && !isMobileView">
          <CalenderViewDetails
            :enableWorkPlace="
              empData?.Enable_Work_Place ? empData?.Enable_Work_Place : 0
            "
            :selectedDate="selectedDate"
            :selectedItem="selectedItem"
            :selectedEmployee="employeeDetails?.Employee_Id"
            :employeeData="employeeData"
            :formAccess="formAccess"
            @on-close="closeView()"
          ></CalenderViewDetails>
        </v-col>
      </v-row>
    </div>
  </div>
  <v-dialog v-model="openViewPopup" width="900" height="90vh">
    <CalenderViewDetails
      :enableWorkPlace="
        empData?.Enable_Work_Place ? empData?.Enable_Work_Place : 0
      "
      :selectedDate="selectedDate"
      :selectedItem="selectedItem"
      :selectedEmployee="employeeDetails?.Employee_Id"
      :employeeData="employeeData"
      :formAccess="formAccess"
      @on-close="closeView()"
    ></CalenderViewDetails>
  </v-dialog>
</template>
<script>
import { defineAsyncComponent } from "vue";
const CalenderViewDetails = defineAsyncComponent(() =>
  import("./CalendarViewDetails.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import VueCal from "vue-cal";
import "vue-cal/dist/vuecal.css";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
export default {
  name: "CalenderView",
  components: {
    VueCal,
    CalenderViewDetails,
    NotesCard,
    Datepicker,
  },
  props: {
    callingForm: {
      type: String,
      default: "employee",
    },
    empData: {
      type: Object,
      default: null,
    },
    employeeList: {
      type: Array,
      default: () => [],
    },
    listLoading: {
      type: Boolean,
      default: false,
    },
    disabledDates: {
      type: Object,
      default: () => ({}),
    },
    attendanceDetails: {
      type: Array,
      default: () => [],
    },
    employeeDetails: {
      type: Object,
      default: () => ({}),
    },
    selectedMonth: {
      type: Date,
      default: new Date(),
    },
  },
  emits: [
    "close-view-form",
    "update:selected-month",
    "refetch-list",
    "update:selected-employee",
  ],
  data() {
    return {
      selectedMonthYear: "",
      allEmployeesList: [],
      selectedItem: null,
      openViewModal: false,
      selectedDate: null,
      employeeListMenu: false,
      searchEmployee: "",
    };
  },
  computed: {
    formattedSelectedMonth() {
      return moment(this.selectedMonthYear).format("MMMM, YYYY");
    },
    disableLeft() {
      let current = moment(this.selectedMonthYear);
      let past = moment(this.disabledDates.to);
      if (
        past.year() < current.year() ||
        (past.year() === current.year() && past.month() < current.month())
      ) {
        return false;
      } else {
        return true;
      }
    },
    disableRight() {
      let current = moment(this.selectedMonthYear);
      let future = moment(this.disabledDates.from);
      if (
        future.year() > current.year() ||
        (future.year() === current.year() && future.month() > current.month())
      ) {
        return false;
      } else {
        return true;
      }
    },
    employeeData() {
      const employee = this.employeeList.find(
        (item) => item.employeeId === this.employeeDetails?.Employee_Id
      );
      return employee ? employee.employeeData : null;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    formId() {
      let fId = this.callingForm === "team" ? 304 : 305;
      return fId;
    },
    formAccess() {
      let formAccessRights = this.accessRights(this.formId);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    getDate() {
      return (date) => {
        return moment(date).format("DD");
      };
    },
    customColor() {
      return (date) => {
        let attendanceObj = this.attendanceDetails.filter(
          (item) => item.Attendance_Date == date
        );
        if (
          attendanceObj[0]?.workscheduleHolidayInputs?.Week_Off_Exist?.toLowerCase() ===
          "yes"
        )
          return "blue-cell";
        else if (
          attendanceObj[0]?.workscheduleHolidayInputs?.Holiday_Exist?.toLowerCase() ===
          "yes"
        )
          return "holiday-cell";
        else if (moment(this.selectedDate).format("YYYY-MM-DD") == date)
          return "bg-grey-lighten-3";
        else return "";
      };
    },
    getCheckInCheckOut() {
      return (date) => {
        let attendanceObj = this.attendanceDetails.filter(
          (item) => item.Attendance_Date == date
        );
        if (attendanceObj[0]?.details.length) {
          let details = attendanceObj[0]?.details;
          let len = details.length;
          let startTime = moment(details[0].Attendance_PunchIn_Date).isValid()
            ? moment(details[0].Attendance_PunchIn_Date).format("HH:mm")
            : "Missed Swipe";
          let endTime = moment(
            details[len - 1].Attendance_PunchOut_Date
          ).isValid()
            ? moment(details[len - 1].Attendance_PunchOut_Date).format("HH:mm")
            : "Missed Swipe";
          return `${startTime} - ${endTime}`;
        }
      };
    },
    getCustomTitle() {
      return (date) => {
        let attendanceObj = this.attendanceDetails.filter(
          (item) => item.Attendance_Date == date
        );
        if (attendanceObj[0]?.details.length) {
          if (
            attendanceObj[0]?.workscheduleHolidayInputs?.Week_Off_Exist?.toLowerCase() ===
            "yes"
          ) {
            let string = attendanceObj[0]?.leaveCompOffObject?.day?.replace(
              "AA",
              "WO"
            );
            return this.getTitleAsHTML(string);
          } else if (
            attendanceObj[0]?.workscheduleHolidayInputs?.Holiday_Exist?.toLowerCase() ===
            "yes"
          ) {
            let string = attendanceObj[0]?.leaveCompOffObject?.day?.replace(
              "AA",
              "H"
            );
            return this.getTitleAsHTML(string);
          }
          return this.getTitleAsHTML(attendanceObj[0]?.leaveCompOffObject?.day);
        } else if (
          attendanceObj[0]?.Message?.toLowerCase() ===
          "attendance entries yet to be processed"
        ) {
          return "AEYP";
        } else {
          if (
            attendanceObj[0]?.workscheduleHolidayInputs?.Week_Off_Exist?.toLowerCase() ===
            "yes"
          ) {
            if (
              attendanceObj[0]?.workscheduleHolidayInputs?.Week_Off_Duration ===
              0.5
            ) {
              if (
                attendanceObj[0]?.leaveCompOffObject?.totalCompOffDuration > 0
              ) {
                return this.getTitleAsHTML("CO:WO");
              } else if (
                attendanceObj[0]?.leaveCompOffObject?.totalLeaveDuration > 0
              ) {
                return this.getTitleAsHTML("LL:WO");
              } else return this.getTitleAsHTML("AA:WO");
            } else {
              return "WO : WO";
            }
          } else if (
            attendanceObj[0]?.workscheduleHolidayInputs?.Holiday_Exist?.toLowerCase() ===
            "yes"
          )
            return "H : H";
          else if (
            moment().isAfter(moment(attendanceObj[0]?.Attendance_Date), "date")
          ) {
            return this.getTitleAsHTML(
              attendanceObj[0]?.leaveCompOffObject?.day
            );
          }
        }
      };
    },
    getTitleAsHTML() {
      return (title) => {
        if (!title) return "";
        let titleList = title.split(":");
        let titleHTML = [];
        for (let item of titleList) {
          switch (item) {
            case "PP":
              titleHTML.push("<span class='text-green'>P</span>");
              break;
            case "LL":
              titleHTML.push("<span class='text-green'>L</span>");
              break;
            case "CO":
              titleHTML.push("<span class='text-green'>CO</span>");
              break;
            case "AA":
              titleHTML.push("<span class='text-red'>AB</span>");
              break;
            default:
              titleHTML.push(`<span>${item}</span>`);
          }
        }
        return titleHTML.join(" : ");
      };
    },
    openViewPopup() {
      if (this.openViewModal && this.isMobileView) {
        return true;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    overlapDays() {
      return (date) => {
        let index = this.attendanceDetails.findIndex(
          (item) => item.Attendance_Date === date
        );
        if (index > -1) {
          if (
            this.attendanceDetails[index]?.workscheduleHolidayInputs
              ?.Twodays_Flag &&
            this.attendanceDetails[index].details?.length
          ) {
            return true;
          }
        }
        return false;
      };
    },
  },
  watch: {
    selectedMonth(val) {
      this.selectedMonthYear = val;
    },
    employeeListMenu(val) {
      if (val) {
        this.$nextTick(() => {
          const selectedElement = this.$refs.selectedEmployeeRef?.[0];
          const container = this.$refs.employeeListContainer;

          if (selectedElement && container) {
            container.scrollTo({
              top: selectedElement.offsetTop - container.offsetTop - 40,
            });
          }
        });
      }
    },
  },
  mounted() {
    this.selectedMonthYear = this.selectedMonth;
    this.allEmployeesList = this.employeeList;
  },
  methods: {
    onChangeDate(date) {
      this.selectedMonthYear = date;
      this.$emit("update:selected-month", date);
    },
    closeView() {
      this.openViewModal = false;
      this.selectedDate = null;
      this.selectedItem = null;
    },
    changeMonth(value) {
      if (value) {
        let newDate = moment(this.selectedMonthYear).add(value, "M");
        this.selectedMonthYear = newDate.toDate();
      }
    },
    onSearchEmployee(emp) {
      if (emp) {
        let filterList = this.employeeList.filter((item) => {
          if (item.employeeData.toLowerCase().includes(emp)) return item;
        });
        this.allEmployeesList = [...filterList];
      } else {
        this.allEmployeesList = [...this.employeeList];
      }
    },
    onChangeEmployee(emp) {
      this.employeeListMenu = false;
      this.allEmployeesList = [...this.employeeList];
      this.searchEmployee = "";
      if (emp) {
        this.$emit("update:selected-employee", emp);
      }
    },
    openViewForm(eventDate) {
      this.selectedDate = eventDate;
      let attendanceObj = this.attendanceDetails.filter(
        (item) => item.Attendance_Date == eventDate
      );
      if (
        attendanceObj.length &&
        attendanceObj[0].leaveCompOffObject &&
        attendanceObj[0].leaveCompOffObject.day
      ) {
        let dayArray = attendanceObj[0].leaveCompOffObject.day.split(":");
        if (
          dayArray.includes("PP") ||
          dayArray.includes("LL") ||
          dayArray.includes("CO") ||
          (dayArray.includes("AB") && attendanceObj[0].details?.length)
        ) {
          this.selectedItem = attendanceObj[0];
          this.openViewModal = true;
        } else {
          this.selectedItem = null;
          this.openViewModal = false;
        }
      } else {
        this.selectedItem = null;
        this.openViewModal = false;
      }
    },
  },
};
</script>
<style>
.vuecal {
  max-height: 80vh;
}
.vuecal__title-bar {
  background-color: rgb(var(--v-theme-primary));
  color: white;
}
.vuecal__cell {
  border: 0.1px solid #e8eaec;
  height: 108px;
  background-color: white;
}
.vuecal__event-title {
  display: flex;
  justify-content: center;
}
.blue-cell {
  background-color: #d7f0fd;
}
.holiday-cell {
  background: #f6eafc;
}
.holiday-box {
  height: 20px;
  width: 20px;
  border: 1px solid #ddd2e2;
  background: #f6eafc;
}
.weekoff-box {
  height: 20px;
  width: 20px;
  border: 1px solid #bee0f3;
  background: #d7f0fd;
}

.vuecal__weekdays-headings {
  background-color: rgb(var(--v-theme-hover));
}
.weekday-label {
  font-weight: bold !important;
  background-color: #f5f5f5;
  border: 0.1px solid #e8eaec;
}
.datepicker-calendar_view .vuejs3-datepicker__value {
  min-width: 160px;
  display: flex;
  align-items: center;
}
.datepicker-calendar_view .vuejs3-datepicker__calendar {
  right: 1px;
}
:deep(.employee-list-btn > .v-btn__content) {
  max-width: 100%;
  white-space: wrap;
}
</style>
