<template>
  <div class="text-center">
    <v-overlay
      v-if="overlay"
      v-model="overlay"
      class="d-flex justify-end overlay"
      persistent
      @click:outside="closeWindow()"
    >
      <template v-slot:default>
        <v-card
          rounded="lg"
          :style="{
            height: windowHeight + 'px',
            width: isMobileView ? '90vw' : '35vw',
          }"
        >
          <v-card-title
            class="d-flex justify-space-between align-center bg-primary"
          >
            <div class="text-h6">Bulk Attendance Update</div>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="closeWindow(true)"
              color="white"
            ></v-btn>
          </v-card-title>

          <v-card-text v-if="isLoading" class="d-flex justify-center">
            <AppLoading />
          </v-card-text>

          <v-card-text v-else class="overflow-y-auto" style="max-height: 85vh">
            <v-form ref="AttendanceForm" @submit.prevent="validateForm">
              <v-row class="px-sm-4 px-md-6 pt-sm-4">
                <!-- Add Attendance For -->
                <!-- <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <CustomSelect
                    :items="attendanceOptions"
                    v-model="selectedAttendance"
                    label="Add Attendance For"
                    itemValue="id"
                    itemTitle="name"
                    ref="attendanceSelect"
                    variant="solo"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :rules="[
                      required('Add Attendance For', selectedAttendance),
                    ]"
                    @selected-item="selectedAttendance = $event"
                    @update:model-value="deductFormChange()"
                    multiple
                  ></CustomSelect>
                </v-col> -->

                <!-- Punch In Date -->
                <!-- <v-col
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <v-text-field
                    v-model="punchInDate"
                    label="Punch In Date"
                    variant="solo"
                    readonly
                    :rules="[required('Punch In Date', punchInDate)]"
                  >
                    <template v-slot:label>
                      <span>Punch In Date </span>
                      <span style="color: red">*</span>
                    </template></v-text-field
                  >
                </v-col> -->

                <!-- Punch In Time -->
                <!-- <v-col
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <v-text-field
                    v-model="punchInTime"
                    label="Punch In Time"
                    variant="solo"
                    readonly
                    :rules="[required('Punch In Time', punchInTime)]"
                  >
                    <template v-slot:label>
                      <span>Punch In Time </span>
                      <span style="color: red">*</span>
                    </template></v-text-field
                  >
                </v-col> -->

                <!-- Punch Out Date -->
                <!-- <v-col
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <v-text-field
                    v-model="punchOutDate"
                    label="Punch Out Date"
                    variant="solo"
                    readonly
                  ></v-text-field>
                </v-col> -->

                <!-- Punch Out Time -->
                <!-- <v-col
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <v-text-field
                    v-model="punchOutTime"
                    label="Punch Out Time"
                    variant="solo"
                    readonly
                  ></v-text-field>
                </v-col> -->

                <!-- Add Comment -->
                <!-- <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                >
                  <v-textarea
                    v-model="comment"
                    rows="2"
                    row-height="10"
                    color="primary"
                    hide-details="auto"
                    variant="solo"
                    label="Add Comment"
                    counter="500"
                    @update:model-value="deductFormChange()"
                  ></v-textarea>
                </v-col> -->
              </v-row>
            </v-form>
          </v-card-text>

          <!-- Cancel and Submit buttons -->
          <!-- <v-col cols="12" class="d-flex justify-end pr-4">
            <div
              class="d-flex justify-end pa-2 position-absolute"
              style="bottom: 0"
            >
              <v-btn
                rounded="lg"
                variant="outlined"
                class="px-8 mr-2"
                @click="closeWindow(true)"
              >
                Cancel
              </v-btn>
              <v-btn
                :disabled="!isFormDirty"
                rounded="lg"
                class="px-8 mr-2"
                variant="elevated"
                color="primary"
                @click="validateForm()"
              >
                Submit
              </v-btn>
            </div>
          </v-col> -->
        </v-card>
      </template>
    </v-overlay>
  </div>
</template>
<script>
import { checkNullValue } from "@/helper";
// import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
// import { ARCHIVE_CANDIDATE_DETAILS } from "@/graphql/recruitment/recruitmentQueries.js";
// import CustomEmail from "@/views/common/customEmail/CustomEmail.vue";
// import moment from "moment";

export default {
  name: "CopyToOthersOverlayForm",
  components: {
    // CustomSelect,
    // CustomEmail,
  },
  props: {
    candidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    candidateId: {
      type: Number,
      required: true,
    },
    candidateIdSelected: {
      type: Number,
      required: true,
    },
    jobTitle: {
      default: "",
      type: String,
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    originalStatusList: {
      type: Array,
      required: true,
    },
  },
  mixins: [validationRules],
  emits: ["close-archive-candidates-window", "refetch-data"],

  data: () => ({
    isFormDirty: false,
    showConfirmation: false,
    overlay: true,
    isLoading: false,
    notificationTime: null,
    notifyCandidate: false,
    archiveReason: null,
    archiveComment: "",
    openCustomEmail: false,
    itemTalentList: [],
    selectedTalentPoolId: null,
    archiveStatusId: null,
    buttonText: "Submit",
    archiveReasons: [
      "Candidate Not Interested",
      "Candidate Not Reachable",
      "Not Fit for Current Need",
      "Not Qualified",
      "Not Suitable",
      "Out of Budget",
      "Over Qualified",
      "Under Qualified",
    ],
    notificationTimeList: [
      "Now",
      "After 2 Hours",
      "After 8 Hours",
      "After 24 Hours",
      "After 48 Hours",
    ],
  }),

  mounted() {
    this.getArchivedSatusId();
  },

  watch: {
    notifyCandidate(val) {
      if (val) {
        this.buttonText = "Next";
      } else {
        this.buttonText = "Submit";
      }
    },
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    orgDetails() {
      return this.$store.state.orgDetails;
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    loginEmployeeDetails() {
      return this.$store.state.orgDetails.userDetails;
    },
  },
  methods: {
    checkNullValue,
    deductFormChange() {
      this.isFormDirty = true;
    },
    // async validateForm() {
    //   let { valid } = await this.$refs.ArchiveForm.validate();
    //   if (valid) {
    //     if (this.notifyCandidate) {
    //       this.overlay = false;
    //       this.templateData = {
    //         Company_Name: this.companyName,
    //         Candidate_Name: [
    //           this.candidateDetails.First_Name,
    //           this.candidateDetails.Middle_Name,
    //           this.candidateDetails.Last_Name,
    //         ]
    //           .filter((name) => name)
    //           .join(" "),
    //         Recruiter_Name: this.loginEmployeeDetails.employeeFullName,
    //         Designation: this.loginEmployeeDetails.designationName,
    //         Job_Post_Name: this.candidateDetails.Job_Post_Name,
    //       };
    //       this.openCustomEmail = true;
    //     } else {
    //       this.archiveCandidate();
    //     }
    //   }
    // },

    // showAlert(snackbarData) {
    //   this.$store.commit("OPEN_SNACKBAR", snackbarData);
    // },

    // closeWindow(isSuccess) {
    //   this.$emit("close-archive-candidates-window", isSuccess);
    //   this.overlay = true;
    // },
    // closeCustomEmail() {
    //   this.templateData = "";
    //   this.templateType = "";
    //   this.comment = "";
    //   this.selectedItem = null;
    //   this.openCustomEmail = false;
    //   this.closeWindow();
    // },
    // emailSuccess() {
    //   this.openCustomEmail = false;
    //   this.archiveCandidate();
    //   this.closeWindow();
    // },

    // async archiveCandidate() {
    //   let vm = this;
    //   const formmatedNotificationTime = this.getFormattedDate(
    //     vm.notificationTime
    //   );
    //   try {
    //     vm.isLoading = true;

    //     const response = await vm.$apollo.mutate({
    //       mutation: ARCHIVE_CANDIDATE_DETAILS,
    //       client: "apolloClientAV",
    //       fetchPolicy: "no-cache",
    //       variables: {
    //         candidateId: parseInt(vm.candidateId),
    //         archiveReason: vm.archiveReason,
    //         notificationTime: formmatedNotificationTime,
    //         archiveComment: vm.archiveComment,
    //         archiveStatusId: this.archiveStatusId,
    //         mailContent: null,
    //       },
    //     });

    //     if (
    //       response &&
    //       response.data &&
    //       response.data.archiveCandidateDetails
    //     ) {
    //       const { errorCode, validationError } =
    //         response.data.archiveCandidateDetails;
    //       if (!errorCode && !validationError) {
    //         let snackbarData = {
    //           isOpen: true,
    //           type: "success",
    //           message: "Candidate archived successfully.",
    //         };
    //         vm.showAlert(snackbarData);
    //         vm.closeWindow(true);
    //         vm.$emit("refetch-data");
    //       } else {
    //         vm.handleArchiveCandidateError();
    //       }
    //     } else {
    //       vm.handleArchiveCandidateError();
    //     }
    //   } catch (err) {
    //     vm.handleArchiveCandidateError(err);
    //   } finally {
    //     vm.isLoading = false;
    //     this.overlay = true;
    //   }
    // },
    // handleArchiveCandidateError(err = "") {
    //   this.isLoading = false;
    //   this.trackingStatusLoading = false;
    //   this.$emit("refetch-data");
    //   this.$store.dispatch("handleApiErrors", {
    //     error: err,
    //     action: "archiving",
    //     form: "candidate details",
    //     isListError: false,
    //   });
    // },
    // getArchivedSatusId() {
    //   let archivedItem = this.originalStatusList.find(
    //     (item) => item.Status?.toLowerCase() === "archived"
    //   );
    //   this.archiveStatusId = archivedItem ? archivedItem.Id : null;
    // },
    // getFormattedDate(selectedItem) {
    //   if (!selectedItem) {
    //     return null;
    //   }
    //   let date = moment().utc();

    //   switch (selectedItem) {
    //     case "Now":
    //       date = null;
    //       break;
    //     case "After 2 Hours":
    //       date = date.add(2, "hours");
    //       break;
    //     case "After 8 Hours":
    //       date = date.add(8, "hours");
    //       break;
    //     case "After 24 Hours":
    //       date = date.add(24, "hours");
    //       break;
    //     case "After 48 Hours":
    //       date = date.add(48, "hours");
    //       break;
    //     default:
    //       return "Invalid selection";
    //   }
    //   return date ? date.format("YYYY-MM-DD HH:mm:ss") : null;
    // },
  },
};
</script>

<style scoped>
.overlay {
  height: 100% !important;
}

.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
