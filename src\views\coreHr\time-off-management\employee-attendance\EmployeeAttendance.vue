<template>
  <div>
    <!-- Main content area -->
    <v-container fluid class="attendance-log">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <!-- Loading skeleton or error screens -->
          <div v-if="listLoading" class="mt-3">
            <!-- Skeleton loaders -->
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <!-- Error screens for fetching data -->
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList('Attendance error refetch')"
          >
          </AppFetchErrorScreen>

          <!-- Content area for attendance -->
          <div v-else>
            <!-- Buttons for actions like add, refetch, and more actions -->
            <div
              class="d-flex flex-wrap align-center"
              :class="isMobileView ? 'flex-column' : 'flex-row'"
              style="justify-content: space-between"
            >
              <v-btn
                v-if="callingFrom?.toLowerCase() === 'team'"
                variant="text"
                class="d-flex align-center pa-0"
                rounded="lg"
                dense
                @click="goBackToList()"
              >
                <template class="d-flex align-center">
                  <v-icon color="primary">fas fa-angle-left fa-lg</v-icon>
                  <span class="text-primary text-decoration-underline"
                    >Back</span
                  >
                </template>
              </v-btn>
              <v-spacer></v-spacer>
              <v-menu
                v-if="callingFrom?.toLowerCase() === 'team'"
                id="activitytracker_my_activity_date_picker"
                v-model="employeeListMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
              >
                <template v-slot:activator="{ props: activatorProps }">
                  <v-btn
                    class="bg-white my-2 ml-2"
                    :class="{
                      'employee-list-btn': isMobileView,
                    }"
                    rounded="lg"
                    dense
                    v-bind="activatorProps"
                  >
                    <template v-slot:prepend>
                      <v-icon color="primary" class="mr-1" size="17">
                        fas fa-user-alt
                      </v-icon>
                    </template>
                    <span
                      style="max-width: 300px"
                      class="text-primary font-weight-bold text-truncate"
                    >
                      {{ employeeData }}
                    </span>
                    <template v-slot:append>
                      <v-icon color="primary" class="ml-1" size="17">
                        {{
                          employeeListMenu
                            ? "fas fa-caret-up"
                            : "fas fa-caret-down"
                        }}
                      </v-icon>
                    </template>
                  </v-btn>
                </template>
                <div
                  ref="employeeListContainer"
                  style="
                    min-height: 100px;
                    max-height: 300px;
                    overflow-y: scroll;
                    background-color: white;
                  "
                  class="white pa-2 pt-0"
                >
                  <div
                    style="
                      position: sticky;
                      top: 0;
                      background-color: white;
                      height: 40px;
                    "
                  >
                    <v-text-field
                      v-model="searchEmployee"
                      density="compact"
                      variant="underlined"
                      hide-details
                      @update:model-value="onSearchEmployee($event)"
                    >
                      <template v-slot:prepend-inner>
                        <v-icon>fas fa-search</v-icon>
                      </template>
                    </v-text-field>
                  </div>
                  <div v-if="allEmployeesList && allEmployeesList.length > 0">
                    <div
                      v-for="employee in allEmployeesList"
                      :key="employee.employeeId"
                      :ref="
                        employee.employeeId === employeeDetails?.Employee_Id
                          ? 'selectedEmployeeRef'
                          : null
                      "
                      @click="onChangeEmployee(employee)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <div
                            v-bind="props"
                            class="pa-2 my-2 rounded-lg cursor-pointer"
                            :class="{
                              'bg-hover':
                                isHovering &&
                                employeeDetails?.Employee_Id !==
                                  employee.employeeId,
                              'bg-primary text-white':
                                employeeDetails?.Employee_Id ===
                                employee.employeeId,
                              'bg-grey-lighten-4 text-primary':
                                !isHovering &&
                                employeeDetails?.Employee_Id !==
                                  employee.employeeId,
                            }"
                          >
                            <div
                              class="text-body-2 text-break"
                              style="max-width: 300px"
                            >
                              {{ employee.employeeData }}
                            </div>
                          </div>
                        </template>
                      </v-hover>
                    </div>
                  </div>
                  <div
                    v-else
                    style="height: 100px"
                    class="text-grey rounded-lg d-flex justify-center align-center"
                  >
                    No data available
                  </div>
                </div>
              </v-menu>
              <v-btn class="bg-white my-2 ml-2" rounded="lg">
                <template v-slot:prepend>
                  <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
                </template>
                {{ formattedSelectedMonth }}
                <v-menu
                  activator="parent"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                >
                  <Datepicker
                    v-model="selectedMonthYear"
                    :inline="true"
                    :format="'MMMM, yyyy'"
                    maximum-view="year"
                    minimum-view="month"
                    :open-date="selectedMonthYear"
                    :disabled-dates="disabledDates"
                    @update:modelValue="onChangeDate($event)"
                  />
                </v-menu>
              </v-btn>

              <div
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </div>
            </div>

            <!-- No results screen -->
            <AppFetchErrorScreen
              v-if="originalLogList.length === 0 && !showAddEditForm"
              key="no-results-screen"
              :main-title="emptyScenarioMsg"
              :isSmallImage="originalLogList.length === 0"
              :image-name="
                originalLogList.length === 0 ? '' : 'common/no-records'
              "
            >
              <template #contentSlot>
                <div style="max-width: 80%">
                  <v-row
                    :style="
                      originalLogList.length === 0 ? 'background: white' : ''
                    "
                    class="rounded-lg pa-5 mb-4"
                  >
                    <v-col
                      v-if="
                        originalLogList.length === 0 &&
                        !employeeDetails.User_Defined_EmpId &&
                        !employeeDetails.Employee_Name
                      "
                      cols="12"
                    >
                      <NotesCard
                        notes=""
                        backgroundColor="transparent"
                        class="mb-4"
                      >
                        <template v-slot:notesCardContent>
                          <div>
                            Job information for this employee is incomplete.
                            Please provide the necessary details, including the
                            work schedule and employment type, to enable
                            attendance tracking. You can update the employee's
                            information in the Team Summary section. Click
                            <a :href="`${baseUrl}v3/my-team/team-summary`"
                              >here</a
                            >
                            to update.
                          </div>
                        </template>
                      </NotesCard>
                    </v-col>
                  </v-row>
                </div>
              </template>
            </AppFetchErrorScreen>

            <!-- Data table for displaying Attendance Log -->
            <v-row>
              <v-col
                v-if="originalLogList.length > 0"
                class="mb-12 position-relative"
              >
                <v-progress-linear
                  v-if="listLoading"
                  indeterminate
                  color="primary"
                ></v-progress-linear>
                <v-data-table
                  :headers="tableHeaders"
                  :items="originalLogList"
                  item-value="Employee_Id"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      originalLogList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[50, 100, -1]"
                  hide-default-footer
                >
                  <template #[`header.Gross_Hours`]="{}">
                    <v-tooltip
                      text="The total hours worked, including regular hours, break hours and overtime."
                      location="top"
                      max-width="300"
                    >
                      <template v-slot:activator="{ props }">
                        <v-hover>
                          <template
                            v-slot:default="{ isHovering, props: hoverProps }"
                          >
                            <div class="d-flex align-center" v-bind="props">
                              <span v-bind="{ ...props, ...hoverProps }"
                                >Gross hours</span
                              >
                              <v-icon
                                :color="isHovering ? 'grey' : 'white'"
                                class="fas fa-arrow-up ml-2"
                                size="12"
                              />
                            </div>
                          </template>
                        </v-hover>
                      </template>
                    </v-tooltip>
                  </template>
                  <template #[`header.Effective_Hours`]="{}">
                    <v-tooltip
                      text="The total hours worked, excluding break hours and overtime."
                      location="top"
                      max-width="300"
                    >
                      <template v-slot:activator="{ props }">
                        <v-hover>
                          <template
                            v-slot:default="{ isHovering, props: hoverProps }"
                          >
                            <div class="d-flex align-center" v-bind="props">
                              <span v-bind="{ ...props, ...hoverProps }"
                                >Effective hours</span
                              >
                              <v-icon
                                :color="isHovering ? 'grey' : 'white'"
                                class="fas fa-arrow-up ml-2"
                                size="12"
                              />
                            </div>
                          </template>
                        </v-hover>
                      </template>
                    </v-tooltip>
                  </template>
                  <template v-slot:item="{ item }">
                    <v-menu
                      v-model="item.showMenu"
                      :close-on-content-click="true"
                      transition="scale-transition"
                      location="right"
                      offset-y
                    >
                      <template v-slot:activator="{ props }">
                        <tr
                          @click="showDetailsForm(item)"
                          class="data-table-tr bg-white cursor-pointer"
                          :class="
                            isMobileView
                              ? 'v-data-table__mobile-table-row ma-0 mt-2'
                              : ''
                          "
                          v-bind="props"
                        >
                          <td id="mobile-view-td" width="300px">
                            <div id="mobile-header" class="font-weight-bold">
                              Date
                            </div>
                            <section class="text-body-2 text-primary">
                              {{ checkNullValue(item.Display_Punching_Date) }}
                              <span
                                v-if="
                                  item?.workscheduleHolidayInputs?.Twodays_Flag
                                "
                              >
                                <sup>+1</sup></span
                              >
                              <span v-if="item.workscheduleHolidayInputs">
                                <v-tooltip text="Week Off">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      v-if="
                                        item?.workscheduleHolidayInputs?.Week_Off_Exist?.toLowerCase() ===
                                        'yes'
                                      "
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      WO</v-btn
                                    ></template
                                  ></v-tooltip
                                >
                                <v-tooltip text="Holiday">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      v-if="
                                        item?.workscheduleHolidayInputs?.Holiday_Exist?.toLowerCase() ===
                                        'yes'
                                      "
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      HO</v-btn
                                    >
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject?.leaveDetails?.length
                                "
                              >
                                <v-tooltip text="Leave">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      LE
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject
                                    ?.compensatoryOffDetails?.length
                                "
                              >
                                <v-tooltip text="Compensatory Off">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      CO
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span v-if="item?.Comp_Off_Attendance_Balance">
                                <v-tooltip text="Compensatory Off Balance">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      COB
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject?.shortTimeOffDetails
                                    ?.length
                                "
                              >
                                <v-tooltip text="Short Time Off">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      SO
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject?.onDutyLeaveDetails
                                    ?.length
                                "
                              >
                                <v-tooltip text="On Duty">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      OD
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  (item?.Missed_Status?.toLowerCase() || '') ===
                                  'missed swipe'
                                "
                              >
                                <v-tooltip text="Missed Swipe">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      MS
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                              <span
                                v-if="
                                  item?.leaveCompOffObject
                                    ?.onDutyshortTimeOffDetails?.length
                                "
                              >
                                <v-tooltip text="On Duty Hours">
                                  <template v-slot:activator="{ props }">
                                    <v-btn
                                      class="ml-1"
                                      size="x-small"
                                      color="hover"
                                      v-bind="props"
                                    >
                                      ODH
                                    </v-btn>
                                  </template>
                                </v-tooltip>
                              </span>
                            </section>
                          </td>
                          <td>
                            <div
                              v-if="!isMobileView"
                              id="mobile-header"
                              class="font-weight-bold"
                            >
                              Attendance Visual
                            </div>
                            <section
                              class="d-flex text-primary timeline-container nowrap mt-2"
                            >
                              <AttendanceTimeline
                                :attendanceRecords="formTimelineData(item)"
                                :message="item.Message"
                              ></AttendanceTimeline>
                              <v-icon
                                v-if="
                                  item.details[0] &&
                                  item.details[0].Checkin_Latitude
                                "
                                color="primary mt-4"
                                class="fas fa-map-marker-alt"
                                size="15"
                                @click="showMap(item, $event)"
                              ></v-icon>
                            </section>
                          </td>
                          <td id="mobile-view-td">
                            <div id="mobile-header" class="font-weight-bold">
                              Effective Hours
                            </div>
                            <section class="text-body-2 text-primary">
                              <template
                                v-if="
                                  !item.Effective_Hours &&
                                  !item.Gross_Hours &&
                                  !item.Arrival
                                "
                              >
                                {{
                                  getMessageForLeaves(item) ||
                                  isGreaterThanCurrentDate(item)
                                    ? ""
                                    : checkNullValue(item.Message)
                                }}
                              </template>
                              <template v-else>
                                {{
                                  formatTime(item.Effective_Hours)
                                    ? formatTime(item.Effective_Hours)
                                    : ""
                                }}
                              </template>
                            </section>
                          </td>
                          <td id="mobile-view-td">
                            <div id="mobile-header" class="font-weight-bold">
                              Gross Hours
                            </div>
                            <section class="text-body-2 text-primary">
                              {{
                                formatTime(item.Gross_Hours)
                                  ? formatTime(item.Gross_Hours)
                                  : ""
                              }}
                            </section>
                          </td>
                          <td id="mobile-view-td">
                            <div id="mobile-header" class="font-weight-bold">
                              Arrival
                            </div>
                            <section class="text-body-2 text-primary">
                              <template
                                v-if="item.Arrival?.toLowerCase() === 'late'"
                              >
                                {{ item.Arrival ? item.Arrival : "" }} -
                                {{
                                  formatTime(item.Late_By)
                                    ? formatTime(item.Late_By)
                                    : ""
                                }}
                              </template>
                              <template
                                v-if="item.Arrival?.toLowerCase() !== 'late'"
                              >
                                {{ item.Arrival ? item.Arrival : "" }}
                              </template>
                            </section>
                          </td>
                          <td
                            class="text-body-2 text-center"
                            id="mobile-view-td"
                          >
                            <div
                              v-if="isMobileView"
                              id="mobile-header"
                              class="font-weight-bold d-flex justify-left align-left"
                            >
                              Actions
                            </div>
                            <section class="d-flex justify-end align-center">
                              <ActionMenu
                                v-if="
                                  !isGreaterThanCurrentDate(item) &&
                                  actionItems(item).length
                                "
                                @selected-action="onActions($event, item)"
                                :actions="actionItems(item)"
                                :access-rights="formAccess"
                              />
                              <div v-else style="width: 63px" class="ma-2">
                                <p>-</p>
                              </div>
                            </section>
                          </td>
                        </tr>
                      </template>

                      <v-card
                        v-if="
                          (item.showMenu &&
                            !showViewForm &&
                            !showAddEditForm &&
                            !item.Message) ||
                          (item?.Message?.toLowerCase() !==
                            'shift not scheduled' &&
                            item?.Message?.toLowerCase() !==
                              'full day week-off')
                        "
                        class="ml-10"
                        style="max-width: 250px"
                      >
                        <v-card-title>
                          <span class="d-flex align-center">
                            <h5
                              class="mr-2 mb-0 text-truncate"
                              style="max-width: 200px"
                            >
                              {{ item?.workscheduleHolidayInputs?.Title }}
                              ({{ item?.Display_Punching_Date }})
                            </h5>

                            <v-icon
                              v-if="formAccess && item.details?.length"
                              color="primary"
                              class="far fa-eye"
                              @click="viewOverlayForm(item)"
                              size="20"
                            ></v-icon>
                          </span>

                          <div class="text-subtitle-1 mt-1">
                            Shift Time :
                            {{
                              this.convertToShiftHourCard(
                                item?.workscheduleHolidayInputs?.Regular_From
                              )
                            }}
                            -
                            {{
                              this.convertToShiftHourCard(
                                item?.workscheduleHolidayInputs?.Regular_To
                              )
                            }}
                            <span
                              v-if="
                                item?.workscheduleHolidayInputs?.Twodays_Flag
                              "
                            >
                              <sup>+1</sup></span
                            >
                          </div>
                          <div class="text-subtitle-1 mt-1">
                            Shift Margin :
                            {{
                              this.convertToShiftHourCard(
                                item?.workscheduleHolidayInputs
                                  ?.Consideration_From
                              )
                            }}
                            -
                            {{
                              this.convertToShiftHourCard(
                                item?.workscheduleHolidayInputs
                                  ?.Consideration_To
                              )
                            }}
                          </div>
                        </v-card-title>
                        <v-divider></v-divider>
                        <v-card-text
                          v-if="item?.details && item.details.length > 0"
                        >
                          <h3 class="mb-2">Attendance Entries</h3>
                          <v-list dense>
                            <v-list-item
                              v-for="(logItem, index) in item.details"
                              :key="index"
                            >
                              <div class="mb-2">
                                <v-list-item-title>
                                  <v-icon
                                    color="success mr-1"
                                    class="fas fa-arrow-up"
                                    size="15"
                                    style="transform: rotate(45deg)"
                                  ></v-icon>
                                  <span class="success mr-5">{{
                                    getHourAndMinutes(
                                      logItem.Actual_Punch_In_Time
                                    )
                                  }}</span>
                                  <v-icon
                                    v-if="
                                      isTimeValid(logItem.Actual_PunchOut_Time)
                                    "
                                    color="red mr-1"
                                    class="fas fa-arrow-down"
                                    size="15"
                                    style="transform: rotate(45deg)"
                                  ></v-icon>
                                  <span
                                    v-if="
                                      isTimeValid(logItem.Actual_PunchOut_Time)
                                    "
                                    class="error"
                                    >{{
                                      getHourAndMinutes(
                                        logItem.Actual_PunchOut_Time
                                      )
                                    }}</span
                                  >
                                </v-list-item-title>
                              </div>
                            </v-list-item>
                          </v-list>
                        </v-card-text>
                      </v-card>
                    </v-menu>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AddEditAttendance
      v-if="showAddEditForm"
      :showForm="showAddEditForm"
      :isEdit="isEdit"
      :editFormData="selectedLogItem"
      :openViewOverlayForm="false"
      :selectedEmployee="employeeDetails?.Employee_Id"
      :formAccess="formAccess"
      :landedFormName="landedFormName"
      :work-schedule-details="workScheduleDetails"
      @close-form="closeAllForms()"
      @form-updated="refetchList('Attendance was added/updated')"
    />
    <ViewAttendance
      v-if="openViewOverlayForm"
      :isEdit="isEdit"
      :selectedEmployee="employeeDetails?.Employee_Id"
      :formAccess="formAccess"
      :landedFormName="landedFormName"
      :selectedLogItem="selectedLogItem"
      :employeeName="employeeDetails?.Presentational_Name"
      :managerName="employeeDetails?.Manager_Name"
      :lastSalaryDate="lastSalaryDate"
      :isBeforeLastSalary="isBeforeLastSalary"
      @close-view-attendance-window="openViewOverlayForm = false"
      @refetch-data="refetchList()"
      @close-form="closeAllForms()"
      @open-edit-form="openEditForm()"
      @on-delete-entry="onDeleteEntry($event)"
      @on-edit-entry="onEditEntry($event)"
      @on-approve-entry="onApproveEntry($event)"
      @on-reject-entry="onRejectEntry($event)"
    ></ViewAttendance>
    <AttendanceMap
      v-if="openMapDialog"
      :openMapDialog="openMapDialog"
      :selectedEmployee="employeeDetails?.Employee_Id"
      :access-rights="formAccess"
      :landedFormName="landedFormName"
      :selectedLogItem="selectedLogItem"
      :employeeData="employeeData"
      @close-map-modal="openMapDialog = false"
    />
    <AppLoading v-if="isLoading"></AppLoading
    ><AppWarningModal
      v-if="conformationModel || rejectionModel"
      :open-modal="conformationModel || rejectionModel"
      :confirmation-heading="
        conformationModel
          ? 'Are you sure you want to approve the records?'
          : 'Are you sure you want to reject the records?'
      "
      :icon-name="
        conformationModel ? 'fas fa-check-circle' : 'fas fa-times-circle'
      "
      :icon-color="conformationModel ? 'success' : 'red'"
      :icon-Size="75"
      @close-warning-modal="
        conformationModel ? closeConfirmationModal() : closeRejectionModal()
      "
      @accept-modal="
        conformationModel
          ? onMultiApproval(
              'approve',
              'hr-workflow-task-management-approve text-green'
            )
          : onMultiApproval(
              'reject',
              'hr-workflow-task-management-reject text-red'
            )
      "
    />

    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      iconName="fas fa-trash"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDeleteAttendanceLog()"
    ></AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const ViewAttendance = defineAsyncComponent(() =>
  import("./ViewAttendance.vue")
);
const AddEditAttendance = defineAsyncComponent(() =>
  import("./AddEditAttendance.vue")
);
// const FormFilter = defineAsyncComponent(() => import("./AttendanceFilter.vue"));
// Async Components
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
import Datepicker from "vuejs3-datepicker";
import mixpanel from "mixpanel-browser";
import moment from "moment";
import Config from "@/config.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import AttendanceTimeline from "@/components/custom-components/AttendanceTimeline.vue";
import AttendanceMap from "./AttendanceMap.vue";

export default {
  name: "EmployeeAttendance",
  components: {
    NotesCard,
    ViewAttendance,
    AddEditAttendance,
    ActionMenu,
    AttendanceTimeline,
    AttendanceMap,
    Datepicker,
  },
  props: {
    listLoading: {
      type: Boolean,
      default: false,
    },
    employeeList: {
      type: Array,
      default: () => [],
    },
    selectedMonth: {
      type: Date,
      default: new Date(),
    },
    disabledDates: {
      type: Object,
      default: () => ({}),
    },
    originalLogList: {
      type: Array,
      default: () => [],
    },
    isErrorInList: {
      type: Boolean,
      default: false,
    },
    errorContent: {
      type: String,
      default: "",
    },
    employeeDetails: {
      type: Object,
      default: () => {},
    },
    callingFrom: {
      type: String,
      default: "employee",
    },
  },
  mixins: [FileExportMixin],
  emits: [
    "close-view-form",
    "refetch-list",
    "update:selected-month",
    "update:selected-employee",
  ],
  data: () => ({
    openWarningModal: false,
    openMoreMenu: false,
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    selectedLogItem: null,
    showViewForm: false,
    currentTabItem: "",
    selectedMonthYear: "",
    allEmployeesList: [],
    conformationModel: false,
    rejectionModel: false,
    openViewOverlayForm: false,
    selectedSingleLogItem: null,
    openMapDialog: false,
    employeeListMenu: false,
    searchEmployee: "",
  }),
  computed: {
    formattedSelectedMonth() {
      return moment(this.selectedMonthYear).format("MMMM, YYYY");
    },
    employeeData() {
      const employee = this.allEmployeesList.find(
        (item) => item.employeeId === this.employeeDetails?.Employee_Id
      );
      return employee ? employee.employeeData : null;
    },
    lastSalaryDate() {
      const employee = this.allEmployeesList.find(
        (item) => item.employeeId === this.employeeDetails?.Employee_Id
      );
      return employee ? employee.lastSalaryDate : null;
    },
    isBeforeLastSalary() {
      return (
        this.selectedLogItem &&
        moment(this.selectedLogItem?.Attendance_Date).isBefore(
          moment(this.lastSalaryDate)
        )
      );
    },
    landedFormName() {
      let attendance = this.accessRights("304");
      if (attendance && attendance.customFormName) {
        return attendance.customFormName;
      } else return "Attendance";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formId() {
      let fId = this.callingFrom === "team" ? "304" : "305";
      return parseInt(fId);
    },
    formAccess() {
      let formAccessRights = this.accessRights(this.formId);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        // Add "copy to others" action inheriting from "update" action
        formAccessRights.accessRights["copy to others"] =
          formAccessRights.accessRights["update"];

        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    tableHeaders() {
      return [
        {
          title: "Date",
          key: "Display_Punching_Date",
          align: "start",
        },
        {
          title: "Attendance Visual",
          key: "AttendanceVisual",
          align: "center",
        },
        {
          title: "Effective Hours",
          key: "Effective_Hours",
        },
        {
          title: "Gross Hours",
          key: "Gross_Hours",
        },
        {
          title: "Arrival",
          key: "Late_By",
        },
        {
          title: "Actions",
          align: "end",
          sortable: false,
        },
      ];
    },

    emptyScenarioMsg() {
      return this.originalLogList.length
        ? "There are no Attendance for the selected filters/searches."
        : "";
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isTimeValid() {
      return (time) => {
        return moment(time, "HH:mm:ss").isValid();
      };
    },
    getMessageForLeaves() {
      return (item) => {
        let msg = [];
        if (item?.leaveCompOffObject?.totalLeaveDuration == 1) {
          msg.push("Full day Leave");
        }
        if (item?.leaveCompOffObject?.totalCompOffDuration == 1) {
          msg.push("Full day Comp Off");
        }
        if (item?.leaveCompOffObject?.totalLeaveDuration == 0.5) {
          msg.push(
            `${
              item.leaveCompOffObject.leaveDetails[0]?.Leave_Period ||
              "Half day"
            } Leave`
          );
        }
        if (item?.leaveCompOffObject?.totalCompOffDuration == 0.5) {
          msg.push(
            `${
              item.leaveCompOffObject.compensatoryOffDetails[0]?.Period ||
              "Half day"
            } Comp Off`
          );
        }
        if (item?.leaveCompOffObject?.totalLeaveDuration == 0.25) {
          msg.push(
            `${
              item.leaveCompOffObject.leaveDetails[0]?.Leave_Period ||
              "Quarter day"
            } Leave`
          );
        }
        if (item?.leaveCompOffObject?.totalCompOffDuration == 0.25) {
          msg.push(
            `${
              item.leaveCompOffObject.compensatoryOffDetails[0]?.Period ||
              "Quarter day"
            } Comp Off`
          );
        }
        return msg.join(", ");
      };
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.selectedMonthYear = this.selectedMonth;
    this.currentTabItem = "Attendance Log";
    this.allEmployeesList = this.employeeList;
  },

  watch: {
    selectedMonth(val) {
      this.selectedMonthYear = val;
    },
    employeeListMenu(val) {
      if (val) {
        this.$nextTick(() => {
          const selectedElement = this.$refs.selectedEmployeeRef?.[0];
          const container = this.$refs.employeeListContainer;

          if (selectedElement && container) {
            container.scrollTo({
              top: selectedElement.offsetTop - container.offsetTop - 40,
            });
          }
        });
      }
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    onChangeDate(date) {
      this.selectedMonthYear = date;
      this.$emit("update:selected-month", date);
    },
    getTooltipMessage(item) {
      const message = item?.Message?.toLowerCase();
      const isBeforeLastSalary = moment(item?.Attendance_Date).isBefore(
        moment(this.lastSalaryDate)
      );
      if (isBeforeLastSalary) {
        return "This action cannot be performed on this record once the payslip is generated.";
      } else if (
        item.leaveCompOffObject?.totalCompOffDuration +
          item.leaveCompOffObject?.totalLeaveDuration >=
        1
      ) {
        return "This action cannot be performed on this record once the leave/comp-off is approved.";
      } else if (message === "no time entries logged") {
        return "You cannot perform this action on this record with no time entries logged.";
      } else if (message === "full day week-off") {
        return "You cannot perform this action on this record for a full day week-off.";
      } else if (message === "shift not scheduled") {
        return "This action cannot be performed on an unscheduled shift record.";
      } else if (message === "attendance entries yet to be processed") {
        return "You cannot perform this action on this record with attendance entries yet to be processed.";
      }
      return "";
    },
    isGreaterThanCurrentDate(item) {
      return moment(item.Attendance_Date).isAfter(moment());
    },
    showMap(item, e) {
      this.selectedLogItem = item;
      this.openMapDialog = true;
      e.stopPropagation();
    },
    getHourAndMinutes(timeString) {
      // Ensure the timeString is valid
      if (!timeString || typeof timeString !== "string") return null;

      // Split the timeString by ':' and return the first two parts
      const [hours, minutes] = timeString.split(":");
      return `${hours}:${minutes}`;
    },
    convertToShiftHourCard(dateTime) {
      if (!dateTime) return null;

      const timePart = dateTime.split(" ")[1];
      if (!timePart) return null;

      const [hours, minutes] = timePart.split(":").map(Number);

      // Format hours and minutes to always have two digits
      const formattedHours = hours < 10 ? `0${hours}` : hours;
      const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

      return `${formattedHours}:${formattedMinutes}`;
    },
    formatTime(timeString) {
      if (!timeString) return "";

      const [hours, minutes] = timeString.split(":").map(Number); // Convert hours and minutes to numbers
      const formattedTime = `${hours} Hrs ${minutes} Mins`;

      return formattedTime;
    },
    onEditEntry(item) {
      this.openViewOverlayForm = false;
      this.isEdit = true;
      this.selectedLogItem = { details: item };
      this.showAddEditForm = true;
    },
    onApproveEntry(item) {
      this.selectedLogItem = item;
      this.openViewOverlayForm = false;
      this.conformationModel = true;
      this.rejectionModel = false;
    },
    onRejectEntry(item) {
      this.selectedLogItem = item;
      this.openViewOverlayForm = false;
      this.rejectionModel = true;
      this.conformationModel = false;
    },
    closeConfirmationModal() {
      this.conformationModel = false;
    },
    closeRejectionModal() {
      this.rejectionModel = false;
    },
    onDeleteEntry(item) {
      this.selectedSingleLogItem = item;
      this.openViewOverlayForm = false;
      this.openWarningModal = true;
    },
    showDetailsForm(item) {
      if (item.showMenu) {
        item.showMenu = false;
      } else {
        this.$nextTick(() => {
          item.showMenu = true;
        });
      }
    },
    convertToShiftHour(dateTime) {
      if (!dateTime) return null;

      const timePart = dateTime.split(" ")[1];
      if (!timePart) return null;

      const [hours, minutes] = timePart.split(":").map(Number);

      return hours + minutes / 60;
    },
    convertToHour(dateTime) {
      if (!dateTime) return null;

      const timePart = dateTime.split(" ")[0]; // Extract the time part
      if (!timePart) return null;

      const [hours, minutes] = timePart.split(":").map(Number); // Split time and convert to numbers
      return hours + minutes / 60; // Convert to decimal hours
    },
    formTimelineData(item) {
      if (!item || typeof item !== "object") {
        return [
          {
            id: null,
            date: null,
            shiftStart: null,
            shiftEnd: null,
            segments: [],
          },
        ];
      }

      const workSchedule = item.workscheduleHolidayInputs || {};
      let details = item.details || [];
      details = details.filter(
        (detail) => detail?.Approval_Status?.toLowerCase() !== "rejected"
      );
      const segments = details.map((detail) => ({
        id: detail?.Attendance_Id || null,
        start: this.convertToHour(detail?.Display_PunchIn_Time) || 0,
        end: detail?.PunchOut_Date
          ? this.convertToHour(detail?.Display_PunchOut_Time)
          : null,
        showTooltip: true,
      }));
      const safeSegments = segments.length
        ? segments
        : [
            {
              id: null,
              start: 0,
              end: 0,
              showTooltip: false,
            },
          ];

      const attendanceRecord = {
        id: item.User_Defined_EmpId || null,
        date: item.Attendance_Date || null,
        shiftStart: this.convertToShiftHour(workSchedule?.Regular_From) || 0,
        shiftEnd: this.convertToShiftHour(workSchedule?.Regular_To) || 0,
        segments: [...safeSegments],
        considerationCheckin: Math.max(
          this.convertToShiftHour(workSchedule?.Consideration_From)
            ? this.convertToShiftHour(workSchedule?.Consideration_From)
            : 0,
          0
        ),
        considerationCheckout: this.convertToShiftHour(
          workSchedule?.Consideration_To
        )
          ? this.convertToShiftHour(workSchedule?.Consideration_To)
          : 0,
      };
      return [attendanceRecord];
    },
    onSearchEmployee(emp) {
      if (emp) {
        let filterList = this.employeeList.filter((item) => {
          if (item.employeeData.toLowerCase().includes(emp)) return item;
        });
        this.allEmployeesList = [...filterList];
      } else {
        this.allEmployeesList = [...this.employeeList];
      }
    },
    onChangeEmployee(emp) {
      this.employeeListMenu = false;
      this.allEmployeesList = [...this.employeeList];
      this.searchEmployee = "";
      if (emp) {
        this.$emit("update:selected-employee", emp);
      }
    },

    async onMultiApproval(selectedAction) {
      let vm = this;
      vm.isLoading = true;
      let attendanceIds = [];
      if (vm.selectedLogItem) {
        attendanceIds = [vm.selectedLogItem.Attendance_Id];
      }
      const newStatus =
        selectedAction.toLowerCase() === "approve" ? "Approved" : "Rejected";

      const formData = {
        attendanceIdArr: attendanceIds,
        status: newStatus,
        comments:
          selectedAction.toLowerCase() === "approve"
            ? "Approved successfully."
            : "Rejected due to issues.",
        isAction: "StatusUpdate",
      };

      const apiObj = {
        url: vm.baseUrl + "employees/attendance/status-multi-approval/",
        type: "POST",
        dataType: "json",
        data: formData,
      };

      try {
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );

        if (response?.success) {
          this.showAlert({
            isOpen: true,
            type: "success",
            message: "Attendance status updated successfully.",
          });

          this.conformationModel = false;
          this.rejectionModel = false;
          this.refetchList("Attendance status updated");
        } else {
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Something went wrong. Please try after some time.",
          });
        }
      } catch (err) {
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: err?.response?.data?.msg
            ? err.response.data.msg
            : "An error occurred while updating the attendance status. Please try again later.",
        });
      } finally {
        this.isLoading = false;
      }
    },

    onDeleteAttendanceLog() {
      let vm = this;
      vm.isLoading = true;
      let attendanceIds = null;
      if (vm.selectedSingleLogItem) {
        attendanceIds = [vm.selectedSingleLogItem.Attendance_Id];
      } else if (vm.selectedLogItem) {
        attendanceIds = vm.selectedLogItem.details.map(
          (item) => item.Attendance_Id
        );
      }
      const apiObj = {
        url: vm.baseUrl + "employees/attendance/delete-attendance/",
        type: "POST",
        async: false,
        dataType: "json",
        data: {
          attendanceId: attendanceIds,
          attendanceImportExist: 0,
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.success) {
            vm.isLoading = false;

            let snackbarData = {
              isOpen: true,
              type: "success",
              message: res.msg
                ? res.msg
                : "Attendance record deleted successfully.",
            };
            vm.showAlert(snackbarData);
            vm.refetchList("Attendance record deleted");
          } else {
            vm.isLoading = false;
            vm.openWarningModal = false;
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message:
                res?.msg && res?.msg?.length
                  ? res?.msg
                  : "Something went wrong while deleting the attendance record. Please try after some time.",
            };
            vm.showAlert(snackbarData);
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleDeleteError(err);
        });
    },
    handleDeleteError(err = "") {
      this.openWarningModal = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "delete",
        form: this.landedFormName,
        isListError: false,
      });
    },
    openEditForm() {
      mixpanel.track("Attendance Log edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
      this.openViewOverlayForm = false;
    },
    openViewForm() {
      mixpanel.track("Attendance Log view form opened");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.openViewOverlayForm = false;
    },
    openAddForm(item) {
      mixpanel.track("Attendance Log add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
      this.openViewOverlayForm = false;
      this.selectedLogItem = item;
      this.workScheduleDetails = item.workscheduleHolidayInputs;
    },
    closeAllForms() {
      mixpanel.track("Attendance Log all forms closed");
      this.showAddEditForm = false;
      this.openViewOverlayForm = false;
      this.showViewForm = false;
      this.selectedLogItem = null;
      this.isEdit = false;
      this.openWarningModal = false;
    },
    goBackToList() {
      this.$emit("close-view-form");
    },
    refetchList(msg) {
      mixpanel.track(msg);
      this.closeAllForms();
      this.$emit("refetch-list");
    },
    actionItems(item) {
      if (
        item.leaveCompOffObject?.totalCompOffDuration +
          item.leaveCompOffObject?.totalLeaveDuration >=
        1
      )
        return [];
      let items = [];
      if (this.formAccess?.add) items.push("Add");
      if (item.details.length > 0) {
        if (this.formAccess?.delete) items.push("Delete");
        if (this.formAccess?.view) items.push("View");
      }
      return items;
    },
    onActions(type, item) {
      const msg = item?.Message?.toLowerCase();
      const isBeforeLastSalary = moment(item?.Attendance_Date).isBefore(
        moment(this.lastSalaryDate)
      );
      if (msg === "shift not scheduled") {
        this.presentWarning(item);
      } else if (
        msg === "no time entries logged" ||
        msg === "full day week-off" ||
        msg === "attendance entries yet to be processed"
      ) {
        if (isBeforeLastSalary) {
          this.presentWarning(item);
        } else if (
          item.leaveCompOffObject?.totalCompOffDuration +
            item.leaveCompOffObject?.totalLeaveDuration >=
          1
        ) {
          this.presentWarning(item);
        } else if (
          type?.toLowerCase() === "view" ||
          type?.toLowerCase() === "delete"
        ) {
          this.presentWarning(item);
        } else if (type?.toLowerCase() === "add") {
          this.openAddForm(item);
        }
      } else {
        if (
          isBeforeLastSalary &&
          (type?.toLowerCase() === "add" || type?.toLowerCase() === "delete")
        ) {
          this.presentWarning(item);
        } else if (type && type.toLowerCase() === "delete") {
          this.onDelete(item);
        } else if (type && type.toLowerCase() === "add") {
          this.openAddForm(item);
        } else if (type && type.toLowerCase() === "edit") {
          this.onEdit(item);
        } else if (type && type.toLowerCase() === "view") {
          this.viewOverlayForm(item);
        }
      }
    },

    presentWarning(item) {
      let snackbarData = {
        isOpen: true,
        message:
          this.getTooltipMessage(item) ||
          "This action cannot be performed on this record.",
        type: "warning",
      };
      this.showAlert(snackbarData);
    },

    onEdit(item) {
      this.selectedLogItem = item;
      this.workScheduleDetails = item.workscheduleHolidayInputs;
      this.openEditForm();
    },
    onDelete(item) {
      this.selectedLogItem = item;
      this.openViewOverlayForm = false;
      this.openWarningModal = true;
    },
    onView(item) {
      this.selectedLogItem = item;
      this.openViewForm();
    },
    onBulkCopy(item) {
      this.selectedLogItem = item;
      this.openBulkCopyModal = true;
    },
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedLogItem = null;
    },
    exportReportFile() {
      const selectedEmployee = this.employeeList.find(
        (e) => e.employeeId === this.employeeDetails?.Employee_Id
      );

      const exportHeaders = [
        { header: "Employee Id", key: "employeeId" },
        { header: "Employee Name", key: "employeeName" },
        { header: "Check In Date", key: "checkInDate" },
        { header: "Check In Time", key: "checkInTime" },
        { header: "Work Schedule", key: "workSchedule" },
        { header: "Effective Hours", key: "effectiveHours" },
        { header: "Break Hours", key: "breakHours" },
        { header: "Actual Check In Time", key: "actualCheckInTime" },
        { header: "Check In Work Place", key: "checkInWorkPlace" },
        { header: "Check Out Date", key: "checkOutDate" },
        { header: "Check Out Time", key: "checkOutTime" },
        { header: "Actual Check Out Time", key: "actualCheckOutTime" },
        { header: "Check Out Work Place", key: "checkOutWorkPlace" },
        { header: "Gross Hours", key: "totalHours" },
        { header: "Late Arrival", key: "lateAttendance" },
        { header: "Late Arrival Hours", key: "lateAttendanceHours" },
        { header: "Actual Hours", key: "actualTotalHours" },
        { header: "Approval Status", key: "approvalStatus" },
        { header: "Check In Geo Coordinates", key: "checkInGeoCoordinates" },
        { header: "Check Out Geo Coordinates", key: "checkOutGeoCoordinates" },
        { header: "Check In Data Source", key: "checkInDataSource" },
        { header: "Check Out Data Source", key: "checkOutDataSource" },
        { header: "Check In Form Source", key: "checkInFormSource" },
        { header: "Check Out Form Source", key: "checkOutFormSource" },
        { header: "Reporting Manager", key: "reportingManager" },
        { header: "Approval Request Forwarded to", key: "forwardTo" },
        { header: "Auto Short Time off(Permissions)", key: "shortTimeOff" },
        { header: "Approved By", key: "approvedBy" },
        { header: "Approved On", key: "approvedOn" },
        { header: "Added By", key: "addedByName" },
        { header: "Added On", key: "addedOn" },
        { header: "Updated By", key: "updatedByName" },
        { header: "Updated On", key: "updatedOn" },
      ];

      const exportList = this.originalLogList
        .map((item) => {
          const { details, workscheduleHolidayInputs } = item;
          const detail = details && details.length > 0 ? details[0] : null;

          if (detail) {
            const {
              Attendance_Id,
              Approver_Name,
              PunchIn_Date,
              Display_PunchIn_Time,
              Actual_Punch_In_Time,
              Checkin_Work_Place,
              PunchOut_Date,
              Display_PunchOut_Time,
              Actual_PunchOut_Time,
              Checkout_Work_Place,
              Display_Late_Attendance,
              Late_Attendance_Time,
              Actual_Total_Hours,
              Approval_Status,
              Checkin_Longitude,
              Checkin_Latitude,
              Checkout_Longitude,
              Checkout_Latitude,
              Checkin_Data_Source,
              Checkout_Data_Source,
              Checkin_Form_Source,
              Checkout_Form_Source,
              Approved_By_Name,
              Approved_On,
              Added_By_Name,
              Added_On,
              Updated_By_Name,
              Updated_On,
            } = detail;

            return {
              attendanceId: Attendance_Id || "",
              employeeId: selectedEmployee?.employeeId || "",
              employeeName: selectedEmployee?.employeeName || "",
              reportingManager: selectedEmployee?.reportingManager || "",
              forwardTo: Approver_Name || "",
              checkInDate: PunchIn_Date || "",
              checkInTime: Display_PunchIn_Time || "",
              effectiveHours:
                parseFloat(selectedEmployee?.Effective_Hours) || 0,
              breakHours: parseFloat(selectedEmployee?.Break_Hours) || 0,
              totalHours: parseFloat(selectedEmployee?.Gross_Hours) || 0,
              actualCheckInTime: Actual_Punch_In_Time || "",
              checkInWorkPlace: Checkin_Work_Place || "",
              checkOutDate: PunchOut_Date || "",
              checkOutTime: Display_PunchOut_Time || "",
              actualCheckOutTime: Actual_PunchOut_Time || "",
              checkOutWorkPlace: Checkout_Work_Place || "",
              lateAttendance: Display_Late_Attendance || "",
              lateAttendanceHours: Late_Attendance_Time || "",
              actualTotalHours: parseFloat(Actual_Total_Hours) || 0,
              approvalStatus: Approval_Status || "",
              checkInGeoCoordinates:
                `${Checkin_Longitude},${Checkin_Latitude}` || "",
              checkOutGeoCoordinates:
                `${Checkout_Longitude},${Checkout_Latitude}` || "",
              checkInDataSource: Checkin_Data_Source || "",
              checkOutDataSource: Checkout_Data_Source || "",
              checkInFormSource: Checkin_Form_Source || "",
              checkOutFormSource: Checkout_Form_Source || "",
              approvedBy: Approved_By_Name || "",
              approvedOn: moment(Approved_On).isValid()
                ? this.convertUTCToLocal(Approved_On)
                : null,
              addedByName: Added_By_Name || "",
              addedOn: moment(Added_On).isValid()
                ? this.convertUTCToLocal(Added_On)
                : null,
              updatedByName: Updated_By_Name || "",
              updatedOn: moment(Updated_On).isValid()
                ? this.convertUTCToLocal(Updated_On)
                : null,
              workSchedule: workscheduleHolidayInputs?.Title || "",
            };
          }
          return null;
        })
        .filter((entry) => entry !== null);

      const exportOptions = {
        fileExportData: exportList,
        fileName: "Attendance Report",
        sheetName: "Attendance Details",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
      mixpanel.track("Attendance-Details-exported");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    viewOverlayForm(item) {
      this.selectedLogItem = item;
      this.workScheduleDetails = item.workscheduleHolidayInputs;
      this.openViewOverlayForm = true;
      item.showMenu = false;
    },
  },
};
</script>

<style scoped>
.attendance-log {
  padding: 0em 2em 0em 2em;
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

.notification-bar {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.new-badge {
  background-color: #ff6f61;
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  font-weight: bold;
  margin-right: 10px;
}

.v-data-table >>> tbody tr {
  cursor: pointer;
}

.v-data-table >>> tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.selected-row {
  background-color: rgba(0, 0, 0, 0.08) !important;
}

.relative-cell {
  position: relative;
}
.no-height-cell {
  max-height: 0;
  min-height: 0;
  height: 0;
  overflow: hidden;
  padding: 0; /* Optional: Remove padding if necessary */
}
.timeline-container {
  flex-grow: 1; /* Make AttendanceTimeline take up available space */
}

@media screen and (max-width: 805px) {
  .attendance-log {
    padding: 2em 1em 0em 1em;
  }
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__value) {
  min-width: 160px;
  display: flex;
  align-items: center;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__calendar) {
  right: 1px;
}
:deep(.employee-list-btn > .v-btn__content) {
  max-width: 100%;
  white-space: wrap;
}
</style>
