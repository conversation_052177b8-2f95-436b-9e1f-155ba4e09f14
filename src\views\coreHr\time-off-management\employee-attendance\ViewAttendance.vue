<template>
  <div class="text-center">
    <v-overlay
      v-if="overlay"
      v-model="overlay"
      class="d-flex justify-end overlay"
      persistent
      @click:outside="closeWindow()"
    >
      <template v-slot:default>
        <v-card rounded="lg" :style="cardWidth">
          <v-card-title
            class="d-flex justify-space-between align-center bg-primary"
          >
            <div class="text-h6">View Attendance</div>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="closeWindow(true)"
              color="white"
            ></v-btn>
          </v-card-title>

          <v-card-text v-if="isLoading" class="d-flex justify-center">
            <AppLoading />
          </v-card-text>

          <v-card-text v-else class="overflow-y-auto" style="max-height: 90vh">
            <v-window style="width: 100%">
              <v-window-item value="attendance">
                <div>
                  <v-card class="rounded-md small-card">
                    <v-card-text class="pa-3">
                      <v-row dense>
                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Employee Name
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{ checkNullValue(employeeName) }}
                          </p>
                        </v-col>

                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Reporting Manager
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{ checkNullValue(managerName) }}
                          </p> </v-col
                        ><v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Work Schedule
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              checkNullValue(
                                selectedLogItem?.workscheduleHolidayInputs
                                  ?.Title
                              )
                            }}
                          </p>
                        </v-col>

                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Effective Hours
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              selectedLogItem?.Effective_Hours
                                ? formatTime(selectedLogItem?.Effective_Hours)
                                : checkNullValue(
                                    selectedLogItem?.Effective_Hours
                                  )
                            }}
                          </p>
                        </v-col>

                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Gross Hours
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              selectedLogItem?.Gross_Hours
                                ? formatTime(selectedLogItem?.Gross_Hours)
                                : checkNullValue(selectedLogItem?.Gross_Hours)
                            }}
                          </p>
                        </v-col>

                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Break Hours
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              selectedLogItem?.Break_Hours
                                ? formatTime(selectedLogItem?.Break_Hours)
                                : checkNullValue(selectedLogItem?.Break_Hours)
                            }}
                          </p>
                        </v-col>

                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Late Arrival
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              selectedLogItem.Arrival?.toLowerCase() ==
                              "on time"
                                ? "No"
                                : "Yes"
                            }}
                          </p>
                        </v-col>

                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Late Arrival Hours
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              formatTimeToHrsMins(selectedLogItem.Late_By)
                                ? formatTimeToHrsMins(selectedLogItem.Late_By)
                                : "-"
                            }}
                          </p>
                        </v-col>
                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Early Checkout Hours
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              formatTimeToHrsMins(
                                selectedLogItem.Early_Checkout_Hours
                              )
                                ? formatTimeToHrsMins(
                                    selectedLogItem.Early_Checkout_Hours
                                  )
                                : "-"
                            }}
                          </p>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>

                  <!-- Repeated Data Cards -->
                  <v-container>
                    <v-card
                      v-for="(logItem, index) in selectedLogItem?.details"
                      :key="index"
                      class="rounded-md small-card pa-2 mb-2"
                    >
                      <v-container>
                        <v-row
                          v-if="!isMobileView"
                          :class="{
                            'd-flex justify-space-between align-center text-h6': true,
                          }"
                          dense
                        >
                          <v-col cols="1"></v-col>
                          <v-col cols="4">
                            <span class="text-success">Check In</span>
                          </v-col>
                          <v-col cols="4">
                            <span class="text-red">Check Out</span>
                          </v-col>
                          <v-col cols="2">
                            <div
                              :class="
                                getStatusBadgeClass(logItem?.Approval_Status)
                              "
                              class="badge d-flex align-center justify-center text-body-1"
                              style="max-width: fit-content"
                            >
                              {{ checkNullValue(logItem.Approval_Status) }}
                            </div>
                          </v-col>

                          <v-col cols="1" class="text-body-2">
                            <ActionMenu
                              v-if="getActions(logItem).length > 0"
                              @selected-action="onActions($event, logItem)"
                              :actions="getActions(logItem)"
                              :disableActionButtons="
                                isBeforeLastSalary ? ['Edit', 'Delete'] : []
                              "
                              :tooltipActionButtons="
                                isBeforeLastSalary ? ['Edit', 'Delete'] : []
                              "
                              :tooltipMessage="
                                isBeforeLastSalary === true
                                  ? 'You cannot perform this action on this record after payslip is generated.'
                                  : ''
                              "
                              :isPresentTooltip="false"
                              :access-rights="formAccess"
                            />
                          </v-col>
                        </v-row>
                        <v-row
                          v-if="isMobileView"
                          :class="{
                            'd-flex justify-space-between align-center': true,
                          }"
                          dense
                        >
                          <v-col cols="5">
                            <div
                              :class="
                                getStatusBadgeClass(logItem?.Approval_Status)
                              "
                              class="badge d-flex align-center justify-center"
                              style="max-width: fit-content"
                            >
                              {{ checkNullValue(logItem.Approval_Status) }}
                            </div>
                          </v-col>

                          <v-col
                            v-if="getActions(logItem).length > 0"
                            cols="3"
                            class="text-body-2"
                          >
                            <ActionMenu
                              @selected-action="onActions($event, logItem)"
                              :actions="getActions(logItem)"
                              :disableActionButtons="
                                isBeforeLastSalary ? ['Edit', 'Delete'] : []
                              "
                              :tooltipActionButtons="
                                isBeforeLastSalary ? ['Edit', 'Delete'] : []
                              "
                              :tooltipMessage="
                                isBeforeLastSalary === true
                                  ? 'You cannot perform this action on this record after payslip is generated.'
                                  : ''
                              "
                              :isPresentTooltip="false"
                              :access-rights="formAccess"
                            />
                          </v-col>
                        </v-row>
                        <v-row v-if="isMobileView" dense>
                          <v-col cols="1"></v-col>
                          <v-col cols="5">
                            <span class="text-success">Check In</span>
                          </v-col>
                          <v-col cols="5">
                            <span class="text-red">Check Out</span>
                          </v-col>
                        </v-row>
                        <v-row
                          :class="{
                            'text-subtitle-1 font-weight-regular':
                              !isMobileView,
                            'text-caption': isMobileView,
                          }"
                          dense
                        >
                          <v-col
                            cols="1"
                            class="d-flex align-center justify-center"
                          >
                            <v-tooltip text="Logged Time">
                              <template v-slot:activator="{ props }">
                                <v-icon
                                  color="primary"
                                  class="fas fa-clock mr-2"
                                  :size="isMobileView ? 15 : 20"
                                  v-bind="props"
                                ></v-icon></template
                            ></v-tooltip>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              {{
                                checkNullValue(
                                  formatDate(logItem.Attendance_PunchIn_Date)
                                )
                              }}
                            </span>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              {{
                                checkNullValue(
                                  formatDate(logItem.Attendance_PunchOut_Date)
                                )
                              }}
                            </span>
                          </v-col>
                        </v-row>
                        <v-row
                          dense
                          v-if="
                            logItem.Checkin_Work_Place ||
                            logItem.Checkout_Work_Place
                          "
                          :class="{
                            'text-subtitle-1 font-weight-regular':
                              !isMobileView,
                            'text-caption': isMobileView,
                          }"
                        >
                          <v-col
                            cols="1"
                            class="d-flex align-center justify-center"
                          >
                            <v-tooltip text="Work Place">
                              <template v-slot:activator="{ props }">
                                <v-icon
                                  color="primary"
                                  class="fas fa-laptop-house mr-2"
                                  :size="isMobileView ? 15 : 20"
                                  v-bind="props"
                                ></v-icon></template
                            ></v-tooltip>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              {{ checkNullValue(logItem.Checkin_Work_Place) }}
                            </span>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              {{ checkNullValue(logItem.Checkout_Work_Place) }}
                            </span>
                          </v-col>
                        </v-row>
                        <v-row
                          v-if="
                            (logItem.Checkin_Latitude &&
                              logItem.Checkin_Longitude) ||
                            (logItem.Checkout_Latitude &&
                              logItem.Checkout_Longitude)
                          "
                          dense
                          :class="{
                            'text-subtitle-1 font-weight-regular':
                              !isMobileView,
                            'text-caption': isMobileView,
                          }"
                        >
                          <v-col
                            cols="1"
                            class="d-flex align-center justify-center"
                          >
                            <v-tooltip text="Geo Coordinates">
                              <template v-slot:activator="{ props }">
                                <v-icon
                                  color="primary"
                                  class="fas fa-map-marker-alt mr-2"
                                  :size="isMobileView ? 15 : 20"
                                  v-bind="props"
                                ></v-icon>
                              </template>
                            </v-tooltip>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              <a
                                v-if="
                                  logItem?.Checkin_Latitude &&
                                  logItem?.Checkin_Longitude
                                "
                                href="#"
                                class="text-primary font-weight-bold"
                                @click.prevent="openMap('checkin')"
                                @click="showMap(logItem, 'checkin')"
                              >
                                {{
                                  logItem.Checkin_Latitude
                                    ? logItem.Checkin_Latitude +
                                      " - " +
                                      (logItem.Checkin_Longitude || "")
                                    : " - " + (logItem?.Checkin_Longitude || "")
                                }}
                              </a>
                              <span v-else> - </span>
                            </span>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              <a
                                v-if="
                                  logItem?.Checkout_Latitude &&
                                  logItem?.Checkout_Longitude
                                "
                                href="#"
                                class="text-primary font-weight-bold"
                                @click.prevent="openMap('checkout')"
                                @click="showMap(logItem, 'checkout')"
                              >
                                {{
                                  logItem.Checkout_Latitude
                                    ? logItem.Checkout_Latitude +
                                      " - " +
                                      (logItem.Checkout_Longitude || "")
                                    : " - " +
                                      (logItem?.Checkout_Longitude || "")
                                }}
                              </a>
                              <span v-else> - </span>
                            </span>
                          </v-col>
                        </v-row>
                        <v-row
                          v-if="
                            logItem.Checkin_Data_Source ||
                            logItem.Checkout_Data_Source
                          "
                          dense
                          :class="{
                            'text-subtitle-1 font-weight-regular':
                              !isMobileView,
                            'text-caption': isMobileView,
                          }"
                        >
                          <v-col
                            cols="1"
                            class="d-flex align-center justify-center"
                          >
                            <v-tooltip text="Data Source">
                              <template v-slot:activator="{ props }">
                                <v-icon
                                  color="primary"
                                  class="fas fa-sign-in-alt mr-2"
                                  :size="isMobileView ? 15 : 20"
                                  v-bind="props"
                                ></v-icon
                              ></template>
                            </v-tooltip>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              {{ checkNullValue(logItem.Checkin_Data_Source) }}
                            </span>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              {{ checkNullValue(logItem.Checkout_Data_Source) }}
                            </span>
                          </v-col>
                        </v-row>
                        <v-row
                          v-if="
                            logItem.Checkin_Form_Source ||
                            logItem.Checkout_Form_Source
                          "
                          dense
                          :class="{
                            'text-subtitle-1 font-weight-regular':
                              !isMobileView,
                            'text-caption': isMobileView,
                          }"
                        >
                          <v-col
                            cols="1"
                            class="d-flex align-center justify-center"
                          >
                            <v-tooltip text="Form Source">
                              <template v-slot:activator="{ props }">
                                <v-icon
                                  color="primary"
                                  class="fas fa-user mr-2"
                                  :size="isMobileView ? 15 : 20"
                                  v-bind="props"
                                ></v-icon>
                              </template>
                            </v-tooltip>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              {{ checkNullValue(logItem.Checkin_Form_Source) }}
                            </span>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <span>
                              {{ checkNullValue(logItem.Checkout_Form_Source) }}
                            </span>
                          </v-col>
                        </v-row>
                        <v-divider class="my-2"></v-divider>
                        <v-row
                          :class="{
                            'text-body-2': !isMobileView,
                            'text-caption': isMobileView,
                          }"
                          dense
                        >
                          <v-col cols="1"></v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <p class="text-grey-darken-1">
                              Auto Short Time Off (Permission)
                            </p>
                            <p class="font-weight-regular">
                              {{ checkNullValue(logItem.Auto_Short_Time_Off) }}
                            </p>
                          </v-col>
                          <v-col :cols="isMobileView ? 5 : 4">
                            <p class="text-grey-darken-1">Actual Hours</p>
                            <p class="font-weight-regular">
                              {{
                                logItem.Total_Hours
                                  ? formatTime(logItem.Total_Hours)
                                  : checkNullValue(logItem.Total_Hours)
                              }}
                            </p>
                          </v-col>
                        </v-row>
                        <v-row
                          :class="{
                            'text-body-2': !isMobileView,
                            'text-caption': isMobileView,
                          }"
                          dense
                        >
                          <v-col cols="1"></v-col>
                          <v-col cols="11">
                            <p class="text-grey-darken-1">
                              Approval request forwarded to
                            </p>
                            <p class="font-weight-regular">
                              {{ checkNullValue(logItem.Approver_Name) }}
                            </p>
                          </v-col>
                        </v-row>
                        <MoreDetails
                          class="mt-3"
                          :more-details-list="prefillMoreDetails(logItem)"
                          :open-close-card="false"
                        >
                        </MoreDetails>
                      </v-container>
                    </v-card>
                  </v-container>
                </div>
              </v-window-item>
            </v-window>
          </v-card-text>
        </v-card>
        <AttendanceMap
          v-if="openMapDialog"
          :openMapDialog="openMapDialog"
          :selectedEmployee="selectedEmployee"
          :access-rights="formAccess"
          :landedFormName="landedFormName"
          :selectedLogItem="selectedLogItem"
          :selectedProperties="selectedProperties"
          :employeeData="employeeName"
          @close-map-modal="openMapDialog = false"
        >
        </AttendanceMap>
      </template>
    </v-overlay>
  </div>
</template>
<script>
import { defineComponent, defineAsyncComponent } from "vue";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
const MoreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard")
);
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import AttendanceMap from "./AttendanceMap.vue";

export default defineComponent({
  name: "ViewAttendance",
  components: {
    MoreDetails,
    ActionMenu,
    AttendanceMap,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    selectedEmployee: {
      type: Number,
      default: 0,
    },
    managerName: {
      type: String,
      default: "",
    },
    employeeName: {
      type: String,
      required: true,
    },
    isBeforeLastSalary: {
      type: Boolean,
      required: false,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
    selectedLogItem: {
      type: Object,
      required: true,
    },
  },
  mixins: [validationRules],
  emits: ["close-view-attendance-window", "refetch-data"],

  data: () => ({
    moreDetailsList: [],
    isFormDirty: false,
    showConfirmation: false,
    overlay: true,
    isLoading: false,
    checkInMapDialog: false,
    checkOutMapDialog: false,
    selectedMarkerPosition: null,
    openMapDialog: false,
    selectedProperties: null,
  }),

  computed: {
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    getActions() {
      return (logItem) => {
        if (
          logItem.Approval_Status?.toLowerCase() === "approved" ||
          logItem.Approval_Status?.toLowerCase() === "rejected"
        ) {
          return this.formAccess?.delete ? ["Delete"] : [];
        }
        const baseActions = [];
        if (this.formAccess?.update) baseActions.push("Edit");
        if (this.formAccess?.delete) baseActions.push("Delete");
        if (
          this.$route.path !== "/employee-self-service/attendance" &&
          logItem.Approval_Status?.toLowerCase() !== "draft" &&
          this.formAccess?.update
        ) {
          baseActions.push("Approve", "Reject");
        }
        return baseActions;
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    orgDetails() {
      return this.$store.state.orgDetails;
    },
    loginEmployeeDetails() {
      return this.$store.state.orgDetails.userDetails;
    },
    cardWidth() {
      if (this.windowWidth < 600) {
        return "width:100vw; height: 100vh;";
      } else if (this.windowWidth >= 600 && this.windowWidth < 1050) {
        return "width:75vw; height: 100vh;";
      } else if (this.windowWidth >= 1050 && this.windowWidth < 1300) {
        return "width:60vw; height: 100vh;";
      } else {
        return "width:45vw; height: 100vh;";
      }
    },
    formatTimeToHrsMins() {
      return (timeString) => {
        if (!timeString) return "";

        const [hours, minutes] = timeString.split(":").map(Number); // Convert hours and minutes to numbers
        if (hours == "00" && minutes == "00") return "";
        const formattedTime = `${hours} Hrs ${minutes} Mins`;

        return formattedTime;
      };
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    prefillMoreDetails(item) {
      let detailsList = [];

      const Added_On = this.formatDate(item.Added_On),
        Added_By_Name = item.Added_By_Name,
        Updated_By_Name = item.Updated_By_Name,
        Updated_On = this.formatDate(item.Updated_On),
        Approver_Name = item.Approver_Name,
        Approved_On = this.formatDate(item.Approved_On);

      // Adding Added details
      if (Added_On && Added_By_Name && Added_On !== "Invalid date") {
        detailsList.push({
          actionDate: Added_On,
          actionBy: Added_By_Name,
          text: "Added",
        });
      }

      // Adding Updated details
      if (Updated_By_Name && Updated_On && Updated_On !== "Invalid date") {
        detailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By_Name,
          text: "Updated",
        });
      }

      if (Approver_Name && Approved_On && Approved_On !== "Invalid date") {
        detailsList.push({
          actionDate: Approved_On,
          actionBy: Approver_Name,
          text: "Reviewed",
        });
      }
      return detailsList;
    },
    showMap(logItem, type) {
      (this.selectedProperties = {
        lat:
          type === "checkin"
            ? logItem.Checkin_Latitude
            : logItem.Checkout_Latitude,
        long:
          type === "checkin"
            ? logItem.Checkin_Longitude
            : logItem.Checkout_Longitude,
        type: type,
        Attendance_Id: logItem.Attendance_Id,
      }),
        (this.openMapDialog = true);
    },
    onMarkerClick(markerPosition) {
      this.selectedMarkerPosition = markerPosition;
    },
    openMap(type) {
      if (type === "checkin") {
        this.checkInMapDialog = true;
        this.checkOutMapDialog = false; // Ensure other dialog is closed
      } else if (type === "checkout") {
        this.checkOutMapDialog = true;
        this.checkInMapDialog = false; // Ensure other dialog is closed
      }
    },
    formatTime(timeString) {
      if (!timeString) return "";

      const [hours, minutes] = timeString.split(":").map(Number); // Convert hours and minutes to numbers
      const formattedTime = `${hours} Hrs ${minutes} Mins`;

      return formattedTime;
    },
    onActions(type, item) {
      if (type && type.toLowerCase() === "delete") {
        if (this.formAccess && this.formAccess.delete) {
          this.$emit("on-delete-entry", item);
        } else {
          this.showAlert({
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          });
        }
      } else if (type && type.toLowerCase() === "edit") {
        if (this.formAccess && this.formAccess.update) {
          this.$emit("on-edit-entry", item);
        } else {
          this.showAlert({
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          });
        }
      } else if (type && type.toLowerCase() === "approve") {
        if (this.formAccess && this.formAccess.update) {
          this.$emit("on-approve-entry", item);
        } else {
          this.showAlert({
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          });
        }
      } else if (type && type.toLowerCase() === "reject") {
        if (this.formAccess && this.formAccess.update) {
          this.$emit("on-reject-entry", item);
        } else {
          this.showAlert({
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          });
        }
      }
    },
    getStatusBadgeClass(status) {
      if (!status) return ""; // Handle null or undefined status
      switch (status.toLowerCase()) {
        case "approved":
          return "approved-badge";
        case "rejected":
          return "rejected-badge";
        case "draft":
          return "draft-badge";
        case "applied":
          return "applied-badge";
        default:
          return ""; // Default class if no match
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeWindow(isSuccess) {
      this.$emit("close-view-attendance-window", isSuccess);
      this.overlay = true;
    },
  },
});
</script>

<style scoped>
.overlay {
  height: 100% !important;
}

.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.approved-badge {
  background: #e1f8cc;
  /* Lighter green */
  border: 4px solid #e1f8cc;
  color: #769e48;
  /* Darker shade of green */
}

.rejected-badge {
  background: #ffd8dd;
  /* Lighter red */
  border: 4px solid #ffd8dd;
  color: #a9404e;
  /* Darker shade of red */
}

.draft-badge {
  background: #fbe3c2;
  /* Lighter orange */
  border: 4px solid #fbe3c2;
  color: #a36b33;
  /* Darker shade of orange */
}

.applied-badge {
  background: #efdcfc;
  /* Lighter purple */
  border: 4px solid #efdcfc;
  color: #9462c0;
  /* Darker shade of purple */
}
</style>
