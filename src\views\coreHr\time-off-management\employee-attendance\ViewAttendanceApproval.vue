<template>
  <div class="text-center">
    <v-overlay
      v-if="overlay"
      v-model="overlay"
      class="d-flex justify-end overlay"
      persistent
      @click:outside="closeWindow()"
    >
      <template v-slot:default>
        <v-card
          rounded="lg"
          :style="
            isMobileView
              ? 'width:90vw; height: 100vh;'
              : 'width:45vw; height: 100vh;'
          "
        >
          <v-card-title
            class="d-flex justify-space-between align-center bg-primary"
          >
            <div class="text-h6">View Attendance</div>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="closeWindow(true)"
              color="white"
            ></v-btn>
          </v-card-title>

          <v-card-text v-if="isLoading" class="d-flex justify-center">
            <AppLoading />
          </v-card-text>

          <v-card-text v-else class="overflow-y-auto" style="max-height: 85vh">
            <v-window style="width: 100%">
              <v-window-item>
                <div>
                  <v-card class="rounded-md small-card">
                    <v-card-text class="pa-3">
                      <v-row dense>
                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Employee Name
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              checkNullValue(selectedItem.Presentational_Name)
                            }}
                          </p>
                        </v-col>

                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Reporting Manager
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{ checkNullValue(selectedItem.Manager_Name) }}
                          </p> </v-col
                        ><v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Work Schedule
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{ checkNullValue(selectedItem.Title) }}
                          </p>
                        </v-col>

                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Late Arrival
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              checkNullValue(
                                selectedItem.Display_Late_Attendance
                              )
                            }}
                          </p>
                        </v-col>

                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Late Arrival Hours
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              checkNullValue(selectedItem.Late_Attendance_Time)
                            }}
                          </p>
                        </v-col>
                        <v-col cols="12" sm="6" class="py-1">
                          <p class="text-body-2 text-grey-darken-1 mb-1">
                            Early Checkout Hours
                          </p>
                          <p class="text-body-2 font-weight-regular">
                            {{
                              checkNullValue(selectedItem.Early_Checkout_Hours)
                            }}
                          </p>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>

                  <!-- Repeated Data Cards -->
                  <v-container>
                    <v-row dense>
                      <v-col dense cols="12">
                        <v-card class="rounded-md small-card">
                          <v-card-title>
                            <div class="justify-space-between ml-4">
                              <v-row class="d-flex justify-space-between">
                                <v-col cols="12" sm="5">
                                  <p class="text-success ml-5">Check In</p>
                                </v-col>
                                <v-col cols="12" sm="5">
                                  <p class="text-red ml-11">Check Out</p>
                                </v-col>
                                <v-col cols="12" sm="2">
                                  <div class="text-body-2 text-center mt-4">
                                    <section
                                      class="d-flex flex-column justify-center align-center"
                                    >
                                      <span
                                        :class="
                                          getStatusBadgeClass(
                                            selectedItem.Approval_Status
                                          )
                                        "
                                        class="badge"
                                      >
                                        {{
                                          checkNullValue(
                                            selectedItem.Approval_Status
                                          )
                                        }}
                                      </span>
                                    </section>
                                  </div>
                                </v-col>
                              </v-row>
                            </div>
                            <div class="d-flex ml-8 mr-9"></div>
                          </v-card-title>
                          <v-card-text class="mt-n4">
                            <v-row dense>
                              <v-col cols="12" sm="6">
                                <v-tooltip text="Logged Time">
                                  <template v-slot:activator="{ props }">
                                    <v-icon
                                      color="primary"
                                      class="fas fa-clock mr-2"
                                      size="20"
                                      v-bind="props"
                                    ></v-icon></template
                                ></v-tooltip>
                                <span
                                  class="text-subtitle-1 font-weight-regular ml-2"
                                >
                                  {{
                                    checkNullValue(
                                      formatDate(
                                        selectedItem.Attendance_PunchIn_Date
                                      )
                                    )
                                  }}
                                </span>
                              </v-col>
                              <v-col cols="12" sm="6">
                                <span
                                  class="text-subtitle-1 font-weight-regular mr-10"
                                >
                                  {{
                                    checkNullValue(
                                      formatDate(
                                        selectedItem.Attendance_PunchOut_Date
                                      )
                                    )
                                  }}
                                </span>
                              </v-col>
                              <v-col
                                cols="12"
                                sm="6"
                                v-if="
                                  selectedItem.Checkin_Work_Place ||
                                  selectedItem.Checkout_Work_Place
                                "
                              >
                                <v-tooltip text="Work Place">
                                  <template v-slot:activator="{ props }">
                                    <v-icon
                                      color="primary"
                                      class="fas fa-laptop-house mr-2"
                                      size="20"
                                      v-bind="props"
                                    ></v-icon></template
                                ></v-tooltip>
                                <span
                                  class="text-subtitle-1 font-weight-regular ml-2"
                                >
                                  {{
                                    checkNullValue(
                                      selectedItem.Checkin_Work_Place
                                    )
                                  }}
                                </span>
                              </v-col>
                              <v-col
                                cols="12"
                                sm="6"
                                v-if="
                                  selectedItem.Checkin_Work_Place ||
                                  selectedItem.Checkout_Work_Place
                                "
                              >
                                <span
                                  class="text-subtitle-1 font-weight-regular mr-10"
                                >
                                  {{
                                    checkNullValue(
                                      selectedItem.Checkout_Work_Place
                                    )
                                  }}
                                </span>
                              </v-col>
                              <v-col
                                cols="12"
                                sm="6"
                                v-if="
                                  (selectedItem.Checkin_Latitude ||
                                    selectedItem.Checkin_Longitude) &&
                                  (selectedItem.Checkout_Latitude ||
                                    selectedItem.Checkout_Longitude)
                                "
                              >
                                <v-tooltip text="Geo Coordinates">
                                  <template v-slot:activator="{ props }">
                                    <v-icon
                                      color="primary"
                                      class="fas fa-map-marker-alt mr-2"
                                      size="20"
                                      v-bind="props"
                                    ></v-icon>
                                  </template>
                                </v-tooltip>
                                <span
                                  class="text-subtitle-1 font-weight-regular ml-2"
                                >
                                  <a
                                    v-if="
                                      selectedItem?.Checkin_Latitude &&
                                      selectedItem?.Checkin_Longitude
                                    "
                                    href="#"
                                    class="text-primary font-weight-bold"
                                    @click.prevent="openMap('checkin')"
                                    @click="showMap()"
                                  >
                                    {{
                                      selectedItem.Checkin_Latitude
                                        ? selectedItem.Checkin_Latitude +
                                          " - " +
                                          (selectedItem.Checkin_Longitude || "")
                                        : " - " +
                                          (selectedItem?.Checkin_Longitude ||
                                            "")
                                    }}
                                  </a>
                                  <span
                                    v-else
                                    class="text-subtitle-1 font-weight-regular mr-10"
                                  >
                                    -
                                  </span>
                                </span>
                              </v-col>

                              <v-col
                                cols="12"
                                sm="6"
                                v-if="
                                  (selectedItem.Checkin_Latitude ||
                                    selectedItem.Checkin_Longitude) &&
                                  (selectedItem.Checkout_Latitude ||
                                    selectedItem.Checkout_Longitude)
                                "
                              >
                                <span
                                  class="text-subtitle-1 font-weight-regular mr-10"
                                >
                                  <a
                                    v-if="
                                      selectedItem?.Checkout_Latitude &&
                                      selectedItem?.Checkout_Longitude
                                    "
                                    href="#"
                                    class="text-primary font-weight-bold"
                                    @click.prevent="openMap('checkout')"
                                    @click="showMap()"
                                  >
                                    {{
                                      selectedItem.Checkout_Latitude
                                        ? selectedItem.Checkout_Latitude +
                                          " - " +
                                          (selectedItem.Checkout_Longitude ||
                                            "")
                                        : " - " +
                                          (selectedItem?.Checkout_Longitude ||
                                            "")
                                    }}
                                  </a>
                                  <span
                                    v-else
                                    class="text-subtitle-1 font-weight-regular mr-10"
                                  >
                                    -
                                  </span>
                                </span>
                              </v-col>

                              <v-col
                                cols="12"
                                sm="6"
                                v-if="
                                  selectedItem?.Checkin_Data_Source ||
                                  selectedItem?.Checkout_Data_Source
                                "
                              >
                                <v-tooltip text="Data Source">
                                  <template v-slot:activator="{ props }">
                                    <v-icon
                                      color="primary"
                                      class="fas fa-sign-in-alt mr-2"
                                      size="20"
                                      v-bind="props"
                                    ></v-icon
                                  ></template>
                                </v-tooltip>
                                <span
                                  class="text-subtitle-1 font-weight-regular ml-2"
                                >
                                  {{
                                    checkNullValue(
                                      selectedItem.Checkin_Data_Source
                                    )
                                  }}
                                </span>
                              </v-col>
                              <v-col
                                cols="12"
                                sm="6"
                                v-if="
                                  selectedItem?.Checkin_Data_Source ||
                                  selectedItem?.Checkout_Data_Source
                                "
                              >
                                <span
                                  class="text-subtitle-1 font-weight-regular mr-10"
                                >
                                  {{
                                    checkNullValue(
                                      selectedItem.Checkout_Data_Source
                                    )
                                  }}
                                </span>
                              </v-col>
                              <v-col
                                cols="12"
                                sm="6"
                                v-if="
                                  selectedItem.Checkin_Form_Source ||
                                  selectedItem.Checkout_Form_Source
                                "
                              >
                                <v-tooltip text="Form Source">
                                  <template v-slot:activator="{ props }">
                                    <v-icon
                                      color="primary"
                                      class="fas fa-user mr-2"
                                      size="20"
                                      v-bind="props"
                                    ></v-icon>
                                  </template>
                                </v-tooltip>
                                <span
                                  class="text-subtitle-1 font-weight-regular ml-2"
                                >
                                  {{
                                    checkNullValue(
                                      selectedItem.Checkin_Form_Source
                                    )
                                  }}
                                </span>
                              </v-col>
                              <v-col
                                cols="12"
                                sm="6"
                                v-if="
                                  selectedItem.Checkin_Form_Source ||
                                  selectedItem.Checkout_Form_Source
                                "
                              >
                                <span
                                  class="text-subtitle-1 font-weight-regular mr-10"
                                >
                                  {{
                                    checkNullValue(
                                      selectedItem.Checkout_Form_Source
                                    )
                                  }}
                                </span>
                              </v-col>
                              <!-- Divider added here -->
                              <v-divider class="mb-1"></v-divider>
                              <v-row>
                                <v-col cols="12" sm="5">
                                  <p
                                    class="text-body-2 text-grey-darken-1 ml-10"
                                  >
                                    Auto Short Time Off (Permission)
                                  </p>
                                  <p
                                    class="text-body-2 font-weight-regular ml-10"
                                  >
                                    {{
                                      checkNullValue(
                                        selectedItem.Auto_Short_Time_Off
                                      )
                                    }}
                                  </p>
                                </v-col>
                                <v-col cols="12" sm="7">
                                  <div>
                                    <p
                                      class="text-body-2 text-grey-darken-1 ml-13"
                                    >
                                      Actual Hours
                                    </p>
                                    <p
                                      class="text-body-2 font-weight-regular ml-13"
                                    >
                                      {{
                                        selectedItem.Actual_Total_Hours
                                          ? formatTime(
                                              selectedItem.Actual_Total_Hours
                                            )
                                          : checkNullValue(
                                              selectedItem.Actual_Total_Hours
                                            )
                                      }}
                                    </p>
                                  </div></v-col
                                ></v-row
                              >
                              <v-col cols="12">
                                <p
                                  class="text-body-2 text-grey-darken-1 mb-1 ml-9"
                                >
                                  Approval request forwarded to
                                </p>
                                <p class="text-body-2 font-weight-regular ml-9">
                                  {{
                                    checkNullValue(selectedItem.Approver_Name)
                                  }}
                                </p></v-col
                              >
                              <v-col cols="12">
                                <MoreDetails
                                  :more-details-list="
                                    prefillMoreDetails(selectedItem)
                                  "
                                  :open-close-card="false"
                                ></MoreDetails>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-container>
                </div>
              </v-window-item>
            </v-window>
          </v-card-text>
        </v-card>
        <AttendanceMap
          v-if="openMapDialog"
          :openMapDialog="openMapDialog"
          :selectedEmployee="selectedEmployee"
          :landedFormName="landedFormName"
          :selectedLogItem="updatedSelectedItem"
          :employeeData="selectedItem.Presentational_Name"
          @close-map-modal="openMapDialog = false"
        ></AttendanceMap>
      </template>
    </v-overlay>
  </div>
</template>
<script>
import { defineComponent, defineAsyncComponent } from "vue";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
const MoreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard")
);
import AttendanceMap from "./AttendanceMap.vue";

export default defineComponent({
  name: "ViewAttendanceApproval",
  components: {
    MoreDetails,
    AttendanceMap,
  },
  props: {
    selectedEmployee: {
      type: Number,
      default: 0,
    },
    selectedItem: {
      type: Object,
      required: true,
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  mixins: [validationRules],
  emits: ["close-view-attendance-window", "refetch-data"],

  data: () => ({
    moreDetailsList: [],
    isFormDirty: false,
    showConfirmation: false,
    overlay: true,
    isLoading: false,
    checkInMapDialog: false,
    checkOutMapDialog: false,
    selectedMarkerPosition: null,
    openMapDialog: false,
    updatedSelectedItem: null,
  }),

  computed: {
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgDetails() {
      return this.$store.state.orgDetails;
    },
    loginEmployeeDetails() {
      return this.$store.state.orgDetails.userDetails;
    },
  },
  mounted() {
    this.updatedSelectedItem = this.selectedItem;
    this.updatedSelectedItem.details = [];
    this.updatedSelectedItem.details.push(this.selectedItem);
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    prefillMoreDetails(item) {
      let detailsList = [];

      const Added_On = this.formatDate(item.Added_On),
        Added_By_Name = item.Added_By_Name,
        Updated_By_Name = item.Updated_By_Name,
        Updated_On = this.formatDate(item.Updated_On),
        Approver_Name = item.Approver_Name,
        Approved_On = this.formatDate(item.Approved_On);

      // Adding Added details
      if (Added_On && Added_By_Name && Added_On !== "Invalid date") {
        detailsList.push({
          actionDate: Added_On,
          actionBy: Added_By_Name,
          text: "Added",
        });
      }

      // Adding Updated details
      if (Updated_By_Name && Updated_On && Updated_On !== "Invalid date") {
        detailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By_Name,
          text: "Updated",
        });
      }

      if (Approver_Name && Approved_On && Approved_On !== "Invalid date") {
        detailsList.push({
          actionDate: Approved_On,
          actionBy: Approver_Name,
          text: "Approved",
        });
      }
      return detailsList;
    },
    showMap() {
      this.openMapDialog = true;
    },
    onMarkerClick(markerPosition) {
      this.selectedMarkerPosition = markerPosition;
    },
    openMap(type) {
      if (type === "checkin") {
        this.checkInMapDialog = true;
        this.checkOutMapDialog = false; // Ensure other dialog is closed
      } else if (type === "checkout") {
        this.checkOutMapDialog = true;
        this.checkInMapDialog = false; // Ensure other dialog is closed
      }
    },
    formatTime(timeString) {
      if (!timeString) return "";

      const [hours, minutes] = timeString.split(":").map(Number);
      let formattedTime = "";
      if (hours && minutes) {
        formattedTime = `${hours} Hrs ${minutes} Mins`;
      } else if (hours) {
        formattedTime = `${hours} Hrs`;
      } else if (minutes) {
        formattedTime = `0 Hrs ${minutes} Mins`;
      }

      return formattedTime;
    },
    getStatusBadgeClass(status) {
      if (!status) return ""; // Handle null or undefined status
      switch (status.toLowerCase()) {
        case "approved":
          return "approved-badge";
        case "rejected":
          return "rejected-badge";
        case "draft":
          return "draft-badge";
        case "applied":
          return "applied-badge";
        default:
          return ""; // Default class if no match
      }
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeWindow(isSuccess) {
      this.$emit("close-view-attendance-window", isSuccess);
      this.overlay = true;
    },
  },
});
</script>

<style scoped>
.overlay {
  height: 100% !important;
}

.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.approved-badge {
  background: #e1f8cc; /* Lighter green */
  border: 4px solid #e1f8cc;
  color: #769e48; /* Darker shade of green */
}

.rejected-badge {
  background: #ffd8dd; /* Lighter red */
  border: 4px solid #ffd8dd;
  color: #a9404e; /* Darker shade of red */
}

.draft-badge {
  background: #fbe3c2; /* Lighter orange */
  border: 4px solid #fbe3c2;
  color: #a36b33; /* Darker shade of orange */
}

.applied-badge {
  background: #efdcfc; /* Lighter purple */
  border: 4px solid #efdcfc;
  color: #9462c0; /* Darker shade of purple */
}
</style>
