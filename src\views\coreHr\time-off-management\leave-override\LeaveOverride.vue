<template>
  <div>
    <AppTopBarTab
      v-if="mainTabs.length > 0"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :show-bottom-sheet="!listLoading"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row v-if="leaveOverrideListBackup.length > 0">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isDefaultFilter="false"
              :isFilter="false"
            ></EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid class="team-container">
      <section v-if="checkAccess">
        <v-window v-model="currentTabItem">
          <v-window-item :value="currentTabItem">
            <div v-if="listLoading" class="mt-3">
              <v-skeleton-loader
                ref="skeleton1"
                type="table-heading"
                class="mx-auto"
              ></v-skeleton-loader>
              <div v-for="i in 3" :key="i" class="mt-4">
                <v-skeleton-loader
                  ref="skeleton2"
                  type="list-item-avatar"
                  class="mx-auto"
                ></v-skeleton-loader>
              </div>
            </div>
            <div v-else-if="isErrorInList">
              <AppFetchErrorScreen
                image-name="common/common-error-image"
                :content="errorContent"
                icon-name="fas fa-redo-alt"
                button-text="Retry"
                :isSmallImage="true"
                @button-click="refreshLists()"
              >
              </AppFetchErrorScreen>
            </div>
            <v-row v-else>
              <v-col cols="12">
                <div>
                  <v-row v-if="!showHistoryView">
                    <v-col
                      v-if="!isAddViewEditLeaveOpened && !showHistoryView"
                      cols="12"
                      class="d-flex flex-wrap align-center pb-0"
                      :class="isMobileView ? 'flex-column' : ''"
                      style="justify-content: space-between"
                    >
                      <v-col class="pl-0 pb-0">
                        <CustomSelect
                          v-if="allEmployeesList.length !== 0"
                          label="Employee"
                          :items="allEmployeesList"
                          :isAutoComplete="true"
                          :itemSelected="selectedEmployee"
                          v-model="selectedEmployee"
                          itemTitle="employeeData"
                          itemValue="employeeId"
                          @selected-item="changeField($event)"
                          variant="solo"
                          clearable
                          class="pb-0"
                          style="max-width: 300px"
                        >
                        </CustomSelect>
                      </v-col>
                      <div
                        v-if="leaveOverrideListBackup.length !== 0"
                        class="d-flex align-center"
                        :class="isMobileView ? 'justify-center' : 'justify-end'"
                      >
                        <v-btn
                          color="transparent"
                          class="ml-1 mt-1"
                          variant="flat"
                          size="small"
                          @click="refreshLists()"
                          ><v-icon>fas fa-redo-alt</v-icon></v-btn
                        >
                        <v-menu
                          v-model="openMoreMenu"
                          transition="scale-transition"
                        >
                          <template v-slot:activator="{ props }">
                            <v-btn
                              variant="plain"
                              class="mt-1 ml-n3 mr-n5"
                              v-bind="props"
                            >
                              <v-icon v-if="!openMoreMenu"
                                >fas fa-ellipsis-v</v-icon
                              >
                              <v-icon v-else>fas fa-caret-up</v-icon>
                            </v-btn>
                          </template>
                          <v-list>
                            <v-list-item
                              v-for="action in moreActions"
                              :key="action.key"
                              @click="onMoreAction(action.key)"
                            >
                              <v-hover>
                                <template
                                  v-slot:default="{ isHovering, props }"
                                >
                                  <v-list-item-title
                                    v-bind="props"
                                    class="pa-3"
                                    :class="{
                                      'pink-lighten-5': isHovering,
                                    }"
                                    ><v-icon size="15" class="pr-2">{{
                                      action.icon
                                    }}</v-icon
                                    >{{ action.key }}</v-list-item-title
                                  >
                                </template>
                              </v-hover>
                            </v-list-item>
                          </v-list>
                        </v-menu>
                      </div>
                    </v-col>
                    <v-col
                      v-if="leaveOverrideListBackup.length !== 0"
                      :cols="
                        isAddViewEditLeaveOpened && windowWidth >= 1264 ? 5 : 12
                      "
                    >
                      <LeaveOverride
                        :items="leaveOverrideList"
                        :isSmallTable="isAddViewEditLeaveOpened"
                        :originalList="leaveOverrideListBackup"
                        :formAccess="leaveOverrideAccess"
                        @on-select-item="openViewForm($event)"
                        @refetch-list="refreshLists()"
                        @show-history="openHistoryView($event)"
                      ></LeaveOverride>
                    </v-col>
                    <v-col
                      v-if="
                        isAddViewEditLeaveOpened &&
                        windowWidth >= 1264 &&
                        leaveOverrideListBackup.length !== 0
                      "
                      :cols="
                        leaveOverrideList && leaveOverrideList.length === 0
                          ? 12
                          : 7
                      "
                    >
                      <div>
                        <ViewLeaveOverride
                          :employeeDetails="selectedEmployeeDetails"
                          :isSmallTable="isAddViewEditLeaveOpened"
                          @close-split-view="isAddViewEditLeaveOpened = false"
                          @refetch-list="refreshLists()"
                          @enable-loader="isLoading = true"
                          @disable-loader="isLoading = false"
                        /></div
                    ></v-col>
                  </v-row>
                  <HistoryLeaveOverride
                    v-else
                    :selectedHistory="selectedHistory"
                    :orgCode="orgCode"
                    @close-history-view="showHistoryView = false"
                  />
                </div>
                <AppFetchErrorScreen
                  v-if="leaveOverrideListBackup.length === 0"
                  key="no-results-screen"
                >
                  <template #contentSlot>
                    <div style="max-width: 80%" class="mx-auto">
                      <v-row
                        style="background: white"
                        class="rounded-lg pa-5 mb-4"
                      >
                        <v-col cols="12">
                          <NotesCard
                            notes="The Leave Override feature allows administrators to manually adjust an employee's leave records. This function is particularly useful in scenarios where there are discrepancies or special circumstances that require manual intervention."
                            backgroundColor="transparent"
                            class="mb-4"
                          ></NotesCard>
                          <NotesCard
                            notes="Administrators can manually add or subtract leave days from an employee's balance."
                            backgroundColor="transparent"
                            class="mb-4"
                          ></NotesCard>
                          <NotesCard
                            notes="It is a powerful tool to ensure that your organization's leave records remain accurate and fair. By providing flexibility and control over leave management, it helps address various scenarios that standard leave policies may not cover."
                            backgroundColor="transparent"
                            class="mb-4"
                          ></NotesCard>
                        </v-col>
                        <v-col
                          cols="12"
                          class="d-flex align-center justify-center mb-4"
                        >
                          <v-btn
                            rounded="lg"
                            class="mt-1 primary"
                            variant="elevated"
                            :size="isMobileView ? 'small' : 'default'"
                            @click="refreshLists()"
                          >
                            <v-icon>fas fa-redo-alt</v-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </div>
                  </template>
                </AppFetchErrorScreen>
              </v-col>
            </v-row>
          </v-window-item>
        </v-window>
      </section>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import LeaveOverride from "./LeaveOverrideList.vue";
import ViewLeaveOverride from "./EditViewLeaveOverride.vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import { GET_LEAVE_OVERRIDE_LIST } from "@/graphql/corehr/employeeDataQueries";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
import moment from "moment";
import mixpanel from "mixpanel-browser";
import { checkNullValue } from "@/helper";
import Config from "@/config.js";
import HistoryLeaveOverride from "./HistoryLeaveOverrideList.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);

export default defineComponent({
  name: "TimeOfManagement",
  mixins: [FileExportMixin],
  components: {
    NotesCard,
    LeaveOverride,
    ViewLeaveOverride,
    CustomSelect,
    HistoryLeaveOverride,
    EmployeeDefaultFilterMenu,
  },

  data() {
    return {
      // list
      leaveOverrideList: [],
      leaveOverrideListBackup: [],
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      // selected Employee
      selectedEmployee: 0,
      selectedEmployeeDetails: {},
      openMoreMenu: false,

      isFormDirty: false,
      currentYearLeaveEntitlement: 0,
      selectedLeaveTaken: 0,
      carryOverBalance: 0,
      isAddViewEditLeaveOpened: false,
      allEmployeesList: [],
      isFetchingEmployees: false,
      showHistoryView: false,
      selectedHistory: {},
      isLoading: false,
      currentTabItem: "",
    };
  },

  computed: {
    landedFormName() {
      return "Leave Override";
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    leaveOverrideAccess() {
      let formAccess = this.accessRights("277");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    leaveConfigAccess() {
      let formAccess = this.accessRights("263");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    checkAccess() {
      return (
        (this.leaveOverrideAccess &&
          (this.leaveOverrideAccess.admin === "admin" ||
            this.leaveOverrideAccess.isManager)) ||
        this.leaveConfigAccess
      );
    },
    leavePolicyFormAccess() {
      let formAccess = this.accessRights("347");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    leaveClosureFormAccess() {
      let formAccess = this.accessRights("356");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    coreHrTimeOffTabs() {
      return this.$store.getters.coreHrTimeOffTabs;
    },
    mainTabs() {
      let { isAnyOneFormHaveAccess, formsWithAccess } = this.coreHrTimeOffTabs;
      if (isAnyOneFormHaveAccess) {
        let tabs = [];
        for (let tab of formsWithAccess) {
          if (tab.havingAccess || tab.displayName === this.landedFormName)
            tabs.push(tab.displayName);
        }
        return tabs;
      }
      return [];
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      return [{ key: "Export", icon: "fas fa-file-export" }];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf("Leave Override");
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.getEmpList();
    this.getLeaveOverrideList(this.loginEmployeeId);
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    onTabChange(tabName) {
      if (tabName !== "Leave Override") {
        if (tabName === "Leave Policy") {
          this.$router.push("/core-hr/leave-policy");
        } else if (tabName === "Leave Closure") {
          this.$router.push("/core-hr/leave-closure");
        } else {
          this.$router.push("/core-hr/time-off-management/time-off-closure");
        }
      }
    },
    openHistoryView(historyData) {
      this.showHistoryView = true;
      this.selectedHistory = historyData;
    },
    openViewForm(item) {
      this.selectedEmployeeDetails = item;
      this.isAddViewEditLeaveOpened = true;
      mixpanel.track("leave-override-view-form-opened");
    },
    async getEmpList() {
      this.isFetchingEmployees = true;
      await this.$store
        .dispatch("getEmployeesList", {
          formName: "Leave Override",
          formId: 277,
        })
        .then((empData) => {
          this.allEmployeesList = empData.map((item) => ({
            ...item,
            employeeData: item.userDefinedEmpId
              ? item.employeeName + " - " + item.userDefinedEmpId
              : item.employeeName,
          }));
          this.allEmployeesList = this.allEmployeesList.filter(
            (el) => el.empStatus === "Active"
          );
          this.selectedEmployee = this.loginEmployeeId;
          this.isFetchingEmployees = false;
        })
        .catch((err) => {
          let snackbarData = {
            isOpen: true,
            message: "",
            type: "warning",
          };
          if (err === "error") {
            snackbarData.message =
              "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator.";
          } else {
            snackbarData.message = err;
          }
          this.showAlert(snackbarData);
          this.isFetchingEmployees = false;
        });
    },
    changeField(empId) {
      if (empId) {
        this.getLeaveOverrideList(empId);
      }
    },
    refreshLists() {
      if (this.selectedEmployee) {
        this.getLeaveOverrideList(this.selectedEmployee);
      } else {
        this.selectedEmployee = this.loginEmployeeId;
        this.getLeaveOverrideList(this.loginEmployeeId);
      }
    },
    getLeaveOverrideList(empId) {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_LEAVE_OVERRIDE_LIST,
          client: "apolloClientI",
          variables: {
            employeeId: empId,
            Org_Code: this.orgCode,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          vm.listLoading = false;
          if (
            res &&
            res.data &&
            res.data.listLeaveOverrideEmployeeDetails &&
            res.data.listLeaveOverrideEmployeeDetails
              .employeeEligibleLeaveDetails
          ) {
            const resultData =
              res.data.listLeaveOverrideEmployeeDetails
                .employeeEligibleLeaveDetails;
            this.leaveOverrideListBackup = resultData;
            this.leaveOverrideList = resultData;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    onApplySearch(query) {
      if (!query) {
        this.leaveOverrideList = this.leaveOverrideListBackup;
      } else {
        let searchValue = query.toString().toLowerCase();
        let searchItems = this.leaveOverrideListBackup;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.leaveOverrideList = searchItems;
      }
    },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = this.leaveOverrideList;
      exportData = exportData.map((el) => ({
        ...el,
        employeeId: el?.userDefinedEmpId,
        employeeName: el?.employeeName,
        leaveType: el?.leaveType,
        currentYearLeaveEntitlement: el?.currentYearTotalEligibleDays,
        carryOverBalance: el?.lastCOBalance,
        leavesTaken: el?.leavesTaken,
        pendingApproval: el?.totalAppliedLeaveDays,
        designation: el?.designationName,
        department: el?.departmentName,
        location: el?.locationName,
        carryOver: el?.carryOver,
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: "Leave override",
        sheetName: "Leave override",
        header: [
          { key: "employeeId", header: "Employee Id" },
          { key: "employeeName", header: "Employee Name" },
          { key: "leaveType", header: "Leave Type" },
          {
            key: "currentYearLeaveEntitlement",
            header: "Current Year Leave Entitlement",
          },
          { key: "carryOverBalance", header: "Carry Over Balance" },
          { key: "leavesTaken", header: "Leaves Taken" },
          { key: "pendingApproval", header: "Pending Approval" },
          { key: "designation", header: "Designation" },
          { key: "department", header: "Department" },
          { key: "location", header: "Location" },
          { key: "carryOver", header: "Carry Over" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Leave override",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
      mixpanel.track("leave-override-list-fetch-error");
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style>
.team-container {
  padding: 5em 3em 0em 3em;
}

@media screen and (max-width: 805px) {
  .team-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
