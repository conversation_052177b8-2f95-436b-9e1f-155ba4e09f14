<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row v-show="!isLoading" justify="center">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end"
              :class="originalList?.length ? '' : 'mr-8'"
              :isFilter="false"
            />
            <LeavesTypeFilter
              v-if="originalList?.length"
              ref="formFilterRef"
              :item-list="originalList"
              @reset-filter="resetFilter($event)"
              @apply-filter="applyFilter($event)"
            />
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container fluid class="container">
      <div v-if="formAccess">
        <div v-if="listLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          />
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            />
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          key="error-screen"
          icon-name="fas fa-redo-alt"
          image-name="common/human-error-image"
          button-text="Retry"
          @button-click="refetchList()"
        />
        <AppFetchErrorScreen
          v-else-if="originalList.length == 0"
          key="no-data-screen"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4" style="background: white">
                <v-col cols="12">
                  <NotesCard
                    notes="The Leave Policy Configuration enables administrators to define and customize leave policies according to organizational requirements. It streamlines the management of leave entitlements, accrual rules, and restrictions while ensuring compliance and efficiency. Administrators can configure various leave types such as Annual, Sick, Casual, and Maternity Leave and more. Custom leave policies can be applied to specific employee groups based on department, location, or other criteria, allowing different leave rules within the same organization. Accrual and carry-forward rules can be customized, allowing leave to accumulate monthly or follow a fixed credit system, with options for carry-forward limits and encashment policies."
                    backgroundColor="transparent"
                    class="mb-4"
                  />
                  <NotesCard
                    notes="The approval workflow supports multi-level approvals, assigning approvers based on department, designation, or hierarchy. Leave balance calculations and deductions are automated, restricting applications when the balance is insufficient and enabling pro-rata calculations for new joiners and resigned employees. This comprehensive configuration ensures a well-structured and transparent leave management process, enhancing efficiency for both employees and HR teams."
                    backgroundColor="transparent"
                    class="mb-4"
                  />
                </v-col>
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <!-- <v-btn
                    v-if="formAccess.add"
                    prepend-icon="fas fa-plus"
                    color="primary"
                    variant="elevated"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="openAddEditForm()"
                  >
                    <template v-slot:prepend>
                      <v-icon></v-icon>
                    </template>
                    Add Policy
                  </v-btn> -->
                  <v-btn
                    color="transparent"
                    variant="flat"
                    @click="refetchList()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="itemList.length == 0"
          key="no-results-screen"
          main-title="There are no leave types matched for the selected filters/searches."
          image-name="common/no-records"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4">
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    color="primary"
                    variant="elevated"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    @click="resetFilter()"
                  >
                    Reset Filter/Search
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <div v-else>
          <div
            class="d-flex flex-wrap align-center my-3"
            :class="isMobileView ? 'flex-column' : ''"
            style="justify-content: space-between"
          >
            <div
              class="d-flex align-center flex-wrap"
              :class="isMobileView ? 'justify-center' : ''"
            >
              <v-btn
                rounded="lg"
                style="pointer-events: none"
                variant="flat"
                :size="windowWidth <= 960 ? 'small' : 'default'"
                >Active:
                <span class="text-green font-weight-bold">{{
                  activeRecords
                }}</span>
              </v-btn>
              <v-btn
                rounded="lg"
                style="pointer-events: none"
                variant="flat"
                class="ml-2"
                :size="windowWidth <= 960 ? 'small' : 'default'"
                >Inactive:
                <span class="text-red font-weight-bold">{{
                  inActiveRecords
                }}</span></v-btn
              >
            </div>

            <div
              class="d-flex align-center"
              :class="isMobileView ? 'justify-center' : 'justify-end'"
            >
              <!-- <v-btn
                v-if="formAccess.add"
                prepend-icon="fas fa-plus"
                color="primary"
                variant="elevated"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="openAddEditForm()"
              >
                <template v-slot:prepend>
                  <v-icon></v-icon>
                </template>
                Add Policy
              </v-btn> -->
              <v-btn
                color="transparent"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu
                class="mb-1"
                v-model="openMoreMenu"
                transition="scale-transition"
              >
                <template v-slot:activator="{ props }">
                  <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                    <v-icon>fas fa-ellipsis-v</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action.key"
                    @click="onMoreAction(action.key)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'bg-hover': isHovering,
                          }"
                          ><v-icon
                            size="15"
                            class="mr-2"
                            :color="action.color"
                            >{{ action.icon }}</v-icon
                          >{{ action.key }}</v-list-item-title
                        >
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </div>
          <v-row>
            <v-col cols="12">
              <v-data-table
                :headers="tableHeaders"
                :items="itemList"
                :items-per-page="50"
                fixed-header
                :height="
                  itemList?.length > 11
                    ? $store.getters.getTableHeight(270)
                    : ''
                "
                item-value="LeaveType_Id"
                class="elevation-1"
                style="box-shadow: none !important"
                :class="isMobileView ? `mb-6` : ``"
              >
                <template v-slot:item="{ item }">
                  <tr
                    style="z-index: 200"
                    class="data-table-tr bg-white cursor-pointer"
                    @click="openViewForm(item)"
                    :class="[
                      isMobileView
                        ? ' v-data-table__mobile-table-row ma-0 mt-2'
                        : '',
                    ]"
                  >
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Leave
                      </div>
                      <section class="d-flex align-center">
                        <div
                          v-if="
                            !isMobileView &&
                            selectedItem &&
                            selectedItem.LeaveType_Id === item.LeaveType_Id
                          "
                          class="data-table-side-border d-flex"
                        />
                        <v-tooltip
                          :text="item.Leave_Name"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                              v-bind="props"
                            >
                              {{ checkNullValue(item.Leave_Name) }}
                            </div>
                          </template>
                        </v-tooltip>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-medium'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Leave Type
                      </div>
                      <section
                        class="text-body-2 text-truncate d-flex align-center"
                        style="max-width: 250px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Leave_Type) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Leave Eligibility (Days)
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Total_Days) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Encashment
                      </div>
                      <section>
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Encashment) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? ' d-flex justify-space-between align-center'
                          : ' pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Carry Over
                      </div>
                      <section>
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Carry_Over) }}
                        </span>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? 'font-weight-bold d-flex align-center'
                            : 'font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Status
                      </div>
                      <section
                        class="d-flex align-center justify-space-between"
                      >
                        <div class="d-flex align-center justify-space-around">
                          <span
                            id="w-80"
                            :class="statusColor(item.Leave_Status)"
                            class="text-body-2 font-weight-regular d-flex justify-center align-center text-center"
                            >{{ checkNullValue(item.Leave_Status) }}</span
                          >
                        </div>
                      </section>
                    </td>

                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-center align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="font-weight-bold d-flex justify-center align-center"
                        style="width: 100%"
                      >
                        Actions
                      </div>
                      <section
                        class="d-flex justify-center align-center"
                        style="width: 100%"
                      >
                        <ActionMenu
                          v-if="itemActions(item)?.length > 0"
                          @selected-action="onActions($event, item)"
                          :accessRights="checkAccess"
                          :actions="itemActions(item)"
                          iconColor="grey"
                        />
                        <div v-else>
                          <p>-</p>
                        </div>
                      </section>
                    </td>
                  </tr>
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </div>
      </div>
      <AppAccessDenied v-else />
    </v-container>
  </div>
  <AppLoading v-if="isLoading" />
  <AppWarningModal
    v-if="deleteModel"
    :open-modal="deleteModel"
    confirmation-heading="Are you sure to delete the selected record?"
    icon-name="fas fa-trash"
    icon-Size="75"
    @close-warning-modal="closeAllForms()"
    @accept-modal="onDeleteLeaveType()"
  />
</template>
<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const LeavesTypeFilter = defineAsyncComponent(() =>
  import("./LeavePolicyFilter.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
// Queries
import { LIST_LEAVE_TYPES } from "@/graphql/my-team/leaves.js";
import { checkNullValue } from "@/helper.js";

export default {
  name: "LeavePolicy",
  components: {
    EmployeeDefaultFilterMenu,
    LeavesTypeFilter,
    ActionMenu,
    NotesCard,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // table
    originalList: [],
    itemList: [],
    //export
    openMoreMenu: false,
    isFilterApplied: false,
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
    isLoading: false,
    deleteModel: false,
    // action
    showViewForm: false,
    showAddEditForm: false,
    isEdit: false,
    selectedItem: null,
  }),
  computed: {
    landedFormName() {
      let form = this.accessRights("347");
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "Leave Policy";
    },
    currentTabItem() {
      let index = this.mainTabList.indexOf(this.landedFormName);
      return "tab-" + index;
    },
    coreHrTimeOffTabs() {
      return this.$store.getters.coreHrTimeOffTabs;
    },
    mainTabList() {
      let { isAnyOneFormHaveAccess, formsWithAccess } = this.coreHrTimeOffTabs;
      if (isAnyOneFormHaveAccess) {
        let tabs = [];
        for (let tab of formsWithAccess) {
          if (tab.havingAccess || tab.displayName === this.landedFormName)
            tabs.push(tab.displayName);
        }
        return tabs;
      }
      return [];
    },
    leaveClosureFormAccess() {
      let formAccess = this.accessRights("356");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    formAccess() {
      let access = this.accessRights("347");
      if (access && access.accessRights && access.accessRights["view"]) {
        return access.accessRights;
      } else {
        return false;
      }
    },
    leaveOverrideCheckAccess() {
      return (
        (this.leaveOverrideAccess &&
          (this.leaveOverrideAccess.admin === "admin" ||
            this.leaveOverrideAccess.isManager)) ||
        this.leaveConfigAccess
      );
    },
    leaveOverrideAccess() {
      let formAccess = this.accessRights("277");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    leaveConfigAccess() {
      let formAccess = this.accessRights("263");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
          color: "primary",
        },
      ];
    },
    activeRecords() {
      let empList = this.originalList.filter(
        (el) => el.Leave_Status?.toLowerCase() === "active"
      );
      return empList.length;
    },
    inActiveRecords() {
      let empList = this.originalList.filter(
        (el) => el.Leave_Status?.toLowerCase() === "inactive"
      );
      return empList.length;
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    checkAccess() {
      let havingAccess = {};
      havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? 1 : 0;
      return havingAccess;
    },
    tableHeaders() {
      return [
        {
          title: "Leave",
          align: "start",
          key: "Leave_Name",
        },
        {
          title: "Leave Type",
          key: "Leave_Type",
        },
        { title: "Leave Eligibility (Days)", key: "Total_Days" },
        { title: "Encashment", key: "Encashment" },
        { title: "Carry Over", key: "Carry_Over" },
        { title: "Status", key: "Leave_Status" },
        {
          title: "Actions",
          key: "actions",
          align: "center",
          sortable: false,
        },
      ];
    },
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  mounted() {
    this.fetchList();
  },

  methods: {
    checkNullValue,
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    onTabChange(tabName) {
      if (tabName !== this.landedFormName) {
        let { formsWithAccess } = this.coreHrTimeOffTabs;
        let clickedTab = formsWithAccess.find(
          (tab) => tab.displayName === tabName
        );
        if (clickedTab && clickedTab.url) {
          this.$router.push(clickedTab.url);
        }
      }
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_LEAVE_TYPES,
          client: "apolloClientAC",
          variables: {
            formId: 332,
          },

          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listLeaveTypes &&
            response.data.listLeaveTypes.leaveTypeDetails &&
            !response.data.listLeaveTypes.errorCode
          ) {
            let tempData =
              JSON.parse(response.data.listLeaveTypes.leaveTypeDetails) || [];
            vm.originalList = tempData;
            vm.itemList = tempData;
            vm.resetFilter("Active");
            vm.listLoading = false;
          } else {
            vm.handleListError(response.data.listLeaveTypes?.errorCode || "");
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    onDeleteLeaveType() {
      let vm = this;
      vm.isLoading = true;
      const apiObj = {
        url: vm.baseUrl + "employees/leaves/delete-leave-type",
        type: "POST",
        async: false,
        dataType: "json",
        data: {
          leaveTypeId: vm.selectedItem.LeaveType_Id,
        },
      };
      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.success) {
            vm.showAlert({
              isOpen: true,
              type: res?.type || "success",
              message: res.msg
                ? res.msg
                : "Leave Type record deleted successfully.",
            });
            vm.refetchList();
            vm.closeAllForms();
          } else
            vm.showAlert({
              isOpen: true,
              type: res?.type || "warning",
              message:
                res?.msg && res?.msg?.length
                  ? res?.msg
                  : "Something went wrong while deleting the leave type record. Please try after some time.",
            });

          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "delete",
            form: vm.landedFormName,
            isListError: false,
          });
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.closeAllForms();
      this.fetchList();
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.deleteModel = false;
      this.selectedItem = null;
      this.isEdit = false;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        Document_Upload: el.Document_Upload == "1" ? "Yes" : "No",
        Applicable_During_Notice_Period:
          el.Applicable_During_Notice_Period == "1" ? "Yes" : "No",
        Enable_Proration: el.Enable_Proration == "1" ? "Yes" : "No",
        Auto_Encashment: el.Auto_Encashment == "1" ? "Yes" : "No",
        Show_Statistics_In_Dashboard:
          el.Show_Statistics_In_Dashboard == "1" ? "Yes" : "No",
        Restrict_Employee_To_Apply:
          el.Restrict_Employee_To_Apply == "1" ? "Yes" : "No",
        Added_On: el.Added_On ? this.formatDate(el.Added_On) : "",
        Updated_On: el.Updated_On ? this.formatDate(el.Updated_On) : "",
        LeavePeriod:
          el.Period == 1
            ? "Monthly"
            : el.Period == 3
            ? "Quarterly"
            : el.Period == 6
            ? "Half yearly"
            : "Annually",
      }));

      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: [
          {
            header: "Leave Name",
            key: "Leave_Name",
          },
          {
            header: "Leave Type",
            key: "Leave_Type",
          },
          {
            header: "Enable Personal Choice",
            key: "Enable_Personal_Choice",
          },
          {
            header: "Leave Enforcement Configuration",
            key: "Leave_Enforcement",
          },
          {
            header: "Leave Closure Based On",
            key: "Leave_Closure_Based_On",
          },
          {
            header: "Leave Closure Month",
            key: "LeaveClosureMonth",
          },
          {
            header: "Leave Closure Year",
            key: "Leave_Closure_Year",
          },
          {
            header: "Coverage",
            key: "Coverage",
          },
          {
            header: "Grade",
            key: "View_Grade",
          },
          {
            header: "Custom Group Name",
            key: "Custom_Group_Name",
          },
          {
            header: "Leave Eligibility In Days",
            key: "Total_Days",
          },
          {
            header: "Minimum Limit",
            key: "Minimum_Total_Days",
          },
          {
            header: "Maximum Limit",
            key: "Maximum_Limit",
          },
          {
            header: "Leave Applicable",
            key: "Applicable_During_Probation",
          },
          {
            header: "Leave Applicable After (In Days)",
            key: "Leave_Activation_Days",
          },
          {
            header: "Applicable during Notice Period",
            key: "Applicable_During_Notice_Period",
          },
          {
            header: "Enable Proration",
            key: "Enable_Proration",
          },
          {
            header: "Prorate Leave Balance From",
            key: "Prorate_Leave_Balance_From",
          },
          {
            header: "Prorate After (In Days)",
            key: "Prorate_After",
          },
          {
            header: "Carry Over",
            key: "Carry_Over",
          },
          {
            header: "Carry Over Limit",
            key: "CarryOver_Limit",
          },
          {
            header: "Accumulation Limit",
            key: "Carry_Over_Accumulation_Limit",
          },
          {
            header: "Leave Encashment",
            key: "Encashment",
          },
          {
            header: "Leave Encashment Limit",
            key: "Encashment_Limit",
          },
          {
            header: "Auto Encashment",
            key: "Auto_Encashment",
          },
          {
            header: "Leave Encashment For F&F",
            key: "Leave_Encashment_For_FF",
          },
          {
            header: "Leave Deduction For F&F",
            key: "Leave_Deduction_For_FF",
          },
          {
            header: "Frequency",
            key: "Frequency",
          },
          {
            header: "Display in Payslip",
            key: "Show_In_Payslip",
          },
          {
            header: "Document Submission",
            key: "Document_Upload",
          },
          {
            header: "Threshold limit for Document Submission",
            key: "Max_Days_For_Document_Upload",
          },
          {
            header: "Leave Period",
            key: "LeavePeriod",
          },
          {
            header: "Accrual",
            key: "Accrual",
          },
          {
            header: "Eligible Days Based On Period",
            key: "Eligible_Days_Based_On_Period",
          },
          {
            header: "Accumulate Eligible Days",
            key: "Accumulate_Eligible_Days",
          },
          {
            header: "Accumulate From",
            key: "Accumulate_From",
          },
          {
            header: "Accumulate After (In Days)",
            key: "Accumulate_After",
          },
          {
            header: "Gender",
            key: "Gender",
          },
          {
            header: "Leave Approval Cutoff (In Hours)",
            key: "Leave_Approval_Cutoff",
          },
          {
            header: "Leave Calculation Days",
            key: "Leave_Calculation_Days",
          },
          {
            header: "Advance Notification (In Days)",
            key: "Advance_Notification",
          },
          {
            header: "Show Balance",
            key: "Show_Statistics_In_Dashboard",
          },
          {
            header: "Restrict Employee To Apply",
            key: "Restrict_Employee_To_Apply",
          },
          {
            header: "Enable Leave Exception",
            key: "Enable_Leave_Exception",
          },
          {
            header: "Half Paid Leave Minimum Limit",
            key: "Minimum_Limit",
          },
          {
            header: "Half Paid Leave Deduction",
            key: "Half_Paid_Leave_Deduction",
          },
          {
            header: "Leave Unit",
            key: "Leave_Unit",
          },
          {
            header: "Leave Status",
            key: "Leave_Status",
          },
          {
            header: "Added By",
            key: "Added_By_Name",
          },
          {
            header: "Added On",
            key: "Added_On",
          },
          {
            header: "Updated By",
            key: "Updated_By_Name",
          },
          {
            header: "Updated On",
            key: "Updated_On",
          },
        ],
      };

      this.exportExcelFile(exportOptions);
    },
    resetFilter(value = null) {
      this.isFilterApplied = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      if (value)
        this.itemList = this.originalList?.filter(
          (el) => el.Leave_Status === value
        );
      else this.itemList = this.originalList;
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    applyFilter(filter) {
      this.isFilterApplied = true;
      this.itemList = filter;
    },
    openAddEditForm(openEdit = false) {
      if (openEdit) this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action === "Delete") this.deleteModel = true;
      else if (action === "Edit") this.openAddEditForm(true);
    },
    itemActions() {
      let items = [];
      if (this.formAccess?.delete) items.push("Delete");
      // if (this.formAccess?.update) items.push("Edit");
      return items;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
        this.resetFilter("Active");
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchKeys = [
          "Leave_Name",
          "Leave_Type",
          "Total_Days",
          "Encashment",
          "Carry_Over",
          "Leave_Status",
        ];

        this.itemList = this.originalList?.filter((item) => {
          return searchKeys?.some(
            (key) =>
              item[key] &&
              item[key].toString().toLowerCase()?.includes(searchValue)
          );
        });
      }
    },
    statusColor(status) {
      switch (status) {
        case "Active":
          return "text-green";
        case "Inactive":
          return "text-red";
        default:
          return "text-primary";
      }
    },
  },
};
</script>

<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
