<template>
  <div v-if="!viewApprovals" style="height: 100%">
    <v-row :class="itemList.length == 0 ? 'align-start' : ''" class="mb-12">
      <v-col :cols="12" md="2">
        <v-card
          :height="isMobileView ? 300 : 500"
          class="pa-4"
          :class="isMobileView ? 'mt-4' : ''"
        >
          <div style="max-height: 90%" class="overflow-y-scroll">
            <v-form ref="finalizeAndClosureForm">
              <custom-select
                label="Finalize and Closure Month"
                :is-required="true"
                :rules="[
                  required('Finalize and Closure Month', finalizeClosureMonth),
                ]"
                density="compact"
                :items="monthList"
                :item-selected="finalizeClosureMonth"
                @selected-item="onChangeItem($event, 'finalizeClosureMonth')"
              ></custom-select>
              <custom-select
                label="Designation"
                density="compact"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  closableChips: true,
                }"
                :is-auto-complete="true"
                :items="designationList"
                placeholder="Type minimum 3 characters to list"
                :no-data-text="noDataText"
                item-title="Designation_Name"
                item-value="Designation_Id"
                :item-selected="designation"
                @selected-item="onChangeItem($event, 'designation')"
                @update-search-value="callDesignationList($event)"
              ></custom-select>
              <custom-select
                label="Department"
                density="compact"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  closableChips: true,
                }"
                :items="dropdownList.departmentList"
                item-title="Department_Name"
                item-value="Department_Id"
                :item-selected="department"
                @selected-item="onChangeItem($event, 'department')"
              ></custom-select>
              <custom-select
                label="Location"
                density="compact"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  closableChips: true,
                }"
                :items="dropdownList.locationList"
                item-title="Location_Name"
                item-value="Location_Id"
                :item-selected="location"
                @selected-item="onChangeItem($event, 'location')"
              ></custom-select>
              <custom-select
                label="Employee Type"
                density="compact"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  closableChips: true,
                }"
                :items="dropdownList.empTypeList"
                item-title="Employee_Type"
                item-value="EmpType_Id"
                :item-selected="employeeType"
                @selected-item="onChangeItem($event, 'employeeType')"
              ></custom-select>
              <custom-select
                label="Business Unit"
                density="compact"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  closableChips: true,
                }"
                :items="dropdownList.businessUnitList"
                item-title="Employee_Type"
                item-value="EmpType_Id"
                :item-selected="businessUnit"
                @selected-item="onChangeItem($event, 'businessUnit')"
              ></custom-select>
              <custom-select
                v-if="
                  labelList[115]?.Field_Visiblity?.toLowerCase() === 'yes' &&
                  dropdownList.fieldForce &&
                  (isAdmin || isServiceProviderAdmin)
                "
                :label="labelList[115].Field_Alias || 'Service Provider'"
                density="compact"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  closableChips: true,
                }"
                :items="dropdownList.serviceProviderList"
                item-title="Service_Provider_Name"
                item-value="Service_Provider_Id"
                :item-selected="serviceProvider"
                @selected-item="onChangeItem($event, 'serviceProvider')"
              ></custom-select>
              <custom-select
                label="Status"
                density="compact"
                :items="[
                  'Pending Approval',
                  'Ready For Closure',
                  'Closure Completed',
                ]"
                :select-properties="{
                  multiple: true,
                  chips: true,
                  closableChips: true,
                }"
                :item-selected="closureStatus"
                @selected-item="onChangeItem($event, 'closureGeneration')"
              ></custom-select>
            </v-form>
          </div>
          <div class="position-absolute bottom-0 mb-4">
            <v-btn
              color="primary"
              variant="elevated"
              size="small"
              class="mr-4"
              rounded="lg"
              @click="validateForm()"
            >
              Apply
            </v-btn>
            <v-btn
              color="primary"
              rounded="lg"
              variant="text"
              size="small"
              elevation="4"
              @click="resetFilterValues()"
            >
              Reset
            </v-btn>
          </div>
        </v-card>
      </v-col>

      <v-col :cols="12" md="10">
        <div v-if="listLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 3" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <v-card
          v-else-if="itemList.length == 0"
          :height="isMobileView ? 700 : 500"
          class="d-flex px-5 py-10"
          :class="windowWidth < 800 ? 'flex-column' : ''"
        >
          <div
            :style="{
              width: windowWidth < 800 ? '100%' : '70%',
              height: windowWidth < 800 ? '' : '60%',
            }"
            class="d-flex flex-column justify-space-between mt-10"
          >
            <div>
              <div class="text-h5">Search with advanced filters</div>
              <div class="text-subtitle-1 text-grey">
                You can now find right employees for all your needs
              </div>
            </div>
            <div>
              <div class="text-subtitle-1 text-grey">To get started,</div>
              <div class="text-h6">Apply filter from the left</div>
            </div>
          </div>
          <img
            :src="imgUrl"
            :height="windowWidth < 800 ? 'auto' : ''"
            :width="windowWidth < 800 ? '100%' : ''"
          />
        </v-card>
        <v-data-table
          v-else
          v-model="selectedEmpRecords"
          :headers="headers"
          :items="itemList"
          fixed-header
          :show-select="!isMobileView"
          :height="
            $store.getters.getTableHeightBasedOnScreenSize(250, itemList)
          "
          :items-per-page="50"
          :items-per-page-options="[
            { value: 50, title: '50' },
            { value: 100, title: '100' },
            {
              value: -1,
              title: '$vuetify.dataFooter.itemsPerPageAll',
            },
          ]"
        >
          <template v-slot:[`header.data-table-select`]="{ selectAll }">
            <v-checkbox-btn
              v-model="selectAllBox"
              color="primary"
              false-icon="far fa-circle"
              true-icon="fas fa-check-circle"
              indeterminate-icon="fas fa-minus-circle"
              class="mt-1"
              @change="selectAll(selectAllBox)"
            ></v-checkbox-btn>
          </template>
          <template v-slot:item="{ item }">
            <tr
              class="data-table-tr bg-white cursor-pointer"
              :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
            >
              <td :class="isMobileView ? 'mt-3 mb-n5' : ''">
                <v-checkbox-btn
                  v-model="item.isSelected"
                  :disabled="item.Closure_Status?.toLowerCase() == 'generated'"
                  color="primary"
                  false-icon="far fa-circle"
                  true-icon="fas fa-check-circle"
                  class="mt-n2 ml-n2"
                  @click.stop="
                    {
                    }
                  "
                  @change="checkAllSelected()"
                ></v-checkbox-btn>
              </td>
              <td
                :class="isMobileView ? 'd-flex justify-space-between' : ''"
                :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
              >
                <div
                  v-if="isMobileView"
                  class="text-subtitle-1 text-grey-darken-1 mt-2"
                >
                  Employee Name
                </div>
                <section class="d-flex align-center">
                  <div
                    class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 300px; '
                        : 'max-width: 200px; '
                    "
                  >
                    <div>
                      {{ checkNullValue(item.Employee_Name) }}
                    </div>
                    <div class="text-grey">
                      {{ checkNullValue(item.User_Defined_EmpId) }}
                    </div>
                  </div>
                </section>
              </td>
              <td
                :class="isMobileView ? 'd-flex justify-space-between' : ''"
                :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
              >
                <div
                  v-if="isMobileView"
                  class="text-subtitle-1 text-grey-darken-1 mt-2"
                >
                  Designation
                </div>
                <section class="d-flex align-center">
                  <div
                    class="text-subtitle-1 font-weight-regular text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 300px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Designation) }}
                  </div>
                </section>
              </td>
              <td
                :class="isMobileView ? 'd-flex justify-space-between' : ''"
                :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
              >
                <div
                  v-if="isMobileView"
                  class="text-subtitle-1 text-grey-darken-1 mt-2"
                >
                  Department
                </div>
                <section class="d-flex align-center">
                  <div
                    class="text-subtitle-1 font-weight-regular text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 300px; '
                        : 'max-width: 200px; '
                    "
                  >
                    {{ checkNullValue(item.Department) }}
                  </div>
                </section>
              </td>
              <td
                :class="isMobileView ? 'd-flex justify-space-between' : ''"
                :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
              >
                <div
                  v-if="isMobileView"
                  class="text-subtitle-1 text-grey-darken-1 mt-2"
                >
                  Status
                </div>
                <section class="d-flex align-center">
                  <div
                    class="text-subtitle-1 font-weight-regular text-truncate"
                    :style="
                      !isMobileView
                        ? 'max-width: 300px; '
                        : 'max-width: 200px; '
                    "
                  >
                    <span :class="statusColor(item.Closure_Status)">
                      {{ checkNullValue(item.Closure_Status) }}
                    </span>
                  </div>
                </section>
              </td>
            </tr>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <v-card
      class="d-flex justify-space-between align-center pa-2 position-fixed bottom-0 right-0"
      :style="{ width: windowWidth < 1280 ? '100%' : 'calc(100% - 105px)' }"
    >
      <v-btn
        rounded="lg"
        variant="text"
        elevation="4"
        color="primary"
        style="height: 40px; margin-top: 10px"
        @click="closeForm()"
      >
        <span class="text-primary">Cancel</span>
      </v-btn>
      <div v-if="checkMixedRecords">
        <v-tooltip
          text="There is mix of payslip status. Please select appropriate employee records to either approve or generate payslip"
        >
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="checkMixedRecords ? props : {}"
              rounded="lg"
              size="small"
              class="mr-1 cursor-not-allow"
              color="primary"
              variant="elevated"
              :dense="isMobileView"
              style="height: 40px; margin-top: 10px"
            >
              {{ buttonText }}
            </v-btn>
          </template>
        </v-tooltip>
      </div>
      <div v-else>
        <v-btn
          rounded="lg"
          size="small"
          class="mr-1"
          variant="elevated"
          color="primary"
          :dense="isMobileView"
          :disabled="selectedItems.length == 0 || checkMixedRecords"
          style="height: 40px; margin-top: 10px"
          @click="onClickApprovalsButton()"
        >
          {{ buttonText }}
        </v-btn>
      </div>
    </v-card>
  </div>
  <ApprovalsForm
    v-else-if="viewApprovals"
    :approvalsList="approvalsList"
    :closureList="closureList"
    @close="viewApprovals = false"
  ></ApprovalsForm>
  <AppLoading v-if="isLoading" />
</template>
<script>
import moment from "moment";
import { defineAsyncComponent } from "vue";
import { checkNullValue } from "@/helper.js";
import validationRules from "@/mixins/validationRules";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
const ApprovalsForm = defineAsyncComponent(() => import("./ApprovalsForm.vue"));
export default {
  name: "FinalizeAndClosureGeneration",
  components: { CustomSelect, ApprovalsForm },
  props: {
    dropdownList: {
      type: Object,
      required: true,
    },
  },
  mixins: [validationRules],
  emits: ["close-form"],
  data() {
    return {
      finalizeClosureMonth: null,
      location: [],
      department: [],
      designation: [],
      employeeType: [],
      businessUnit: [],
      serviceProvider: [],
      selectAllBox: false,
      selectedEmpRecords: [],
      selectedItems: [],
      itemList: [],
      designationList: [],
      closureStatus: ["Pending Approval"],
      //flags
      listLoading: false,
      searchString: "",
      designationListLoading: false,
      headers: [
        { title: "Employee Name", key: "Employee_Name" },
        { title: "Designation", key: "Designation" },
        { title: "Department", key: "Department" },
        { title: "Status", key: "Closure_Status" },
      ],
      searchTimeOut: null,
      isLoading: false,
      approvalsList: [
        {
          name: "Attendance",
          pending: 0,
        },
        { name: "Leave", pending: 0 },
        { name: "Comp Off", pending: 0 },
        { name: "Short Time Off", pending: 0 },
        { name: "Resignation", pending: 0 },
        { name: "LOP Recovery", pending: 0 },
      ],
      closureList: [
        { name: "Leave Closure", pending: 0 },
        { name: "Attendance Finalization", pending: 0 },
        { name: "Unscheduled Shifts", pending: 0 },
        { name: "Assets Management", pending: 0 },
        { name: "Probation Employees", pending: 0 },
      ],
      viewApprovals: false,
    };
  },
  computed: {
    checkMixedRecords() {
      const statuses = new Set(
        this.selectedItems.map((item) =>
          item.Closure_Status?.toLowerCase().trim()
        )
      );

      const hasPending = statuses.has("pending approval");
      const hasReady = statuses.has("ready for closure");

      // Return true if both statuses are present (i.e., mixed)
      return hasPending && hasReady;
    },
    buttonText() {
      const allPendingApproval = this.selectedItems.every(
        (item) => item.Closure_Status?.toLowerCase() === "pending approval"
      );
      if (allPendingApproval) {
        return "View Approvals";
      } else {
        return "Generate Payslip";
      }
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessRights(
        "service-provider-admin"
      );
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    noDataText() {
      if (this.designationListLoading) {
        return "Loading...";
      } else if (
        !this.designationListLoading &&
        this.designation.length == 0 &&
        this.searchString.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    imgUrl() {
      return require("@/assets/images/coreHr/search-engine.png");
    },
    arrowUrl() {
      return require("@/assets/images/coreHr/left-arrow.svg");
    },
    statusColor() {
      return (status) => {
        if (status?.toLowerCase() === "pending approval") {
          return "text-orange";
        } else if (status?.toLowerCase() === "ready for closure") {
          return "text-blue";
        }
        return "text-green";
      };
    },
    monthList() {
      let monthList = [...this.$store.state.orgDetails.closureMonthJson]; // Create a copy
      monthList = monthList
        .sort((a, b) => {
          return moment(b, "MM,YYYY").diff(moment(a, "MM,YYYY"));
        })
        .map((el) => {
          return moment(el, "MM,YYYY").format("MMM YYYY");
        });
      return monthList;
    },
  },
  watch: {
    selectedEmpRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemList
        for (const item of this.itemList) {
          // Check if Employee_Id is present in selRecords
          if (selRecords.includes(item.Employee_Id)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
          if (item.Closure_Status?.toLowerCase() !== "generated") {
            let index = this.selectedItems.findIndex(
              (x) => x.Employee_Id === item.Employee_Id
            );
            if (index === -1) {
              this.selectedItems.push(item);
            }
          }
        }
      } else {
        // Iterate through itemList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
        this.selectedItems = [];
      }
    },
  },
  mounted() {
    this.finalizeClosureMonth = moment().format("MMM YYYY");
  },
  methods: {
    checkNullValue,
    onChangeItem(val, field) {
      this[field] = val;
    },
    closeForm() {
      this.$emit("close-form");
    },
    onClickApprovalsButton() {
      if (this.buttonText?.toLowerCase() === "generate payslip") {
        this.initiateClosureGeneration();
      } else {
        this.viewApprovals = true;
      }
    },
    async initiateClosureGeneration() {
      let vm = this;
      vm.isLoading = true;
      let empIds = this.selectedItems.map((el) => parseInt(el.Employee_Id));
      try {
        let apiObj = {
          url:
            this.baseUrl + "employees/timeoff-closure/generate-timeoff-closure",
          type: "POST",
          dataType: "json",
          data: {
            closureMonth: moment(this.finalizeClosureMonth, "MMM YYYY").format(
              "MM,YYYY"
            ),
            employeeId: empIds,
          },
        };
        let response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          this.selectedItems = [];
          this.selectAllBox = false;
          this.showAlert({
            isOpen: true,
            type: "success",
            message: response?.msg
              ? response.msg
              : "Closure generated successfully.",
          });
          this.fetchClosureRecords();
        } else {
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Unable to generate closure.",
          });
        }
        this.isLoading = false;
      } catch (error) {
        vm.isLoading = false;
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message: error
            ? error
            : "Something went wrong. Please try after some time.",
        };
        this.showAlert(snackbarData);
      }
    },
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectedItems = selectedItems;
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    resetFilterValues() {
      this.finalizeClosureMonth = null;
      this.location = [];
      this.department = [];
      this.designation = [];
      this.employeeType = [];
      this.serviceProvider = [];
      this.businessUnit = [];
      this.closureStatus = [];
      this.originalList = [];
      this.itemList = [];
    },
    callDesignationList(searchString) {
      this.searchString = searchString;
      if (this.searchTimeOut) clearTimeout(this.searchTimeOut);
      if (searchString.length >= 3 && !this.designation.length) {
        this.searchTimeOut = setTimeout(() => {
          this.getDesignationList(searchString);
        }, 300);
      }
    },
    async getDesignationList(searchString) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designationList = designationResult;
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.designationList = [];
        });
    },
    async validateForm() {
      let { valid } = await this.$refs.finalizeAndClosureForm.validate();
      if (valid) {
        this.fetchClosureRecords();
      }
    },
    async fetchClosureRecords() {
      this.listLoading = true;
      try {
        let apiObj = {
          url:
            this.baseUrl +
            "employees/timeoff-closure/list-timeoff-closure-employees",
          type: "POST",
          dataType: "json",
          data: {
            closureMonth: moment(this.finalizeClosureMonth, "MMM YYYY").format(
              "MM,YYYY"
            ),
            designation: [...this.designation],
            department: [...this.department],
            location: [...this.location],
            employeeType: [...this.employeeType],
            businessUnit: [...this.businessUnit],
            serviceProvider: [...this.serviceProvider],
            closureStatus: [...this.closureStatus],
          },
        };
        let response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          this.itemList = response.timeoffClosureEmployeeDetails;
          this.approvalsList = [
            {
              name: "Attendance",
              pending: response?.approvalsCount?.[1] || 0,
              url: `${this.baseUrl}employees/attendance-finalization`,
            },
            {
              name: "Leave",
              pending: response?.approvalsCount?.[2] || 0,
              url: `${this.baseUrl}v3/approvals/approval-management?form_id=31`,
            },
            {
              name: "Comp Off",
              pending: response?.approvalsCount?.[3] || 0,
              url: `${this.baseUrl}v3/approvals/approval-management?form_id=334`,
            },
            {
              name: "Short Time Off",
              pending: response?.approvalsCount?.[4] || 0,
              url: `${this.baseUrl}v3/approvals/approval-management?form_id=352`,
            },
            {
              name: "Resignation",
              pending: response?.approvalsCount?.[5] || 0,
              url: `${this.baseUrl}v3/approvals/approval-management?form_id=34`,
            },
            {
              name: "LOP Recovery",
              pending: response?.approvalsCount?.[6] || 0,
              url: `${this.baseUrl}v3/my-team/lop-recovery`,
            },
          ];
          this.closureList = [
            {
              name: "Leave Closure",
              pending: response?.approvalsCount?.[8] || 0,
              url: `${this.baseUrl}employees/leaves`,
            },
            {
              name: "Attendance Finalization",
              pending: response?.approvalsCount?.[9] || 0,
              url: `${this.baseUrl}employees/attendance-finalization`,
            },
            {
              name: "Unscheduled Shifts",
              pending: response?.approvalsCount?.[11] || 0,
              url: `${this.baseUrl}in/roster-management/shift-scheduling`,
            },
            {
              name: "Assets Management",
              pending: response?.approvalsCount?.[12] || 0,
              url: `${this.baseUrl}v3/my-team/team-summary`,
            },
            {
              name: "Probation Employees",
              pending: response?.approvalsCount?.[13] || 0,
              url: `${this.baseUrl}v3/my-team/team-summary`,
            },
          ];
        } else {
          this.itemList = [];
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: response?.msg
              ? response.msg
              : "Unable to fetch closure records.",
          });
        }
        this.listLoading = false;
      } catch (error) {
        this.listLoading = false;
        this.originalList = [];
        this.itemList = [];
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message: error
            ? error
            : "Something went wrong. Please try after some time.",
        };
        this.showAlert(snackbarData);
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
