<template>
  <div class="fill-height">
    <!-- top bar -->
    <AppTopBarTab
      v-if="currentTabItem"
      :tabs-list="tabsList"
      :current-tab="currentTabItem"
      :show-bottom-sheet="false"
      @tab-clicked="onTabChange($event)"
    />
    <!-- App bar tab contents - Updated for Vuetify 3 -->
    <v-window
      id="dashboard-tabs"
      v-model="currentTabItem"
      fluid
      touchless
      :class="showManagerDashboard ? '' : `dashboard-container`"
    >
      <!-- My Employee dashboard Tab -->
      <v-window-item :value="employeeDashboardTab">
        <EmployeeDashboard v-if="showEmployeeDashboard" />
      </v-window-item>
      <!-- Org dashboard Tab -->
      <v-window-item :value="adminDashboardTab">
        <AdminDashboard v-if="showAdminDashboard" />
      </v-window-item>
      <!-- Team Dashboard -->
      <v-window-item :value="managerDashboard">
        <ManagerDashboard v-if="showManagerDashboard" />
      </v-window-item>
    </v-window>
    <v-dialog v-model="openMobileUpdateModal" persistent max-width="350">
      <v-card>
        <v-card-title class="text-h5">
          <v-icon class="mr-2" color="orange"> notifications </v-icon>
          {{ $t("dashboard.newVersionTitle") }}
        </v-card-title>
        <v-card-subtitle class="font-weight-medium mt-1">
          {{ $t("dashboard.newVersionSubtitle") }}
        </v-card-subtitle>
        <v-card-text>{{ $t("dashboard.newVersionQuestion") }}</v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary"
            variant="outlined"
            density="comfortable"
            rounded="lg"
            @click="openMobileUpdateModal = false"
          >
            {{ $t("dashboard.dismissButton") }}
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            density="comfortable"
            rounded="lg"
            @click="redirectToPlaystoreApp()"
          >
            {{ $t("dashboard.updateButton") }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
// components

// Performance-optimized imports using defineAsyncComponent for heavy dashboard components
import { defineAsyncComponent } from "vue";
const EmployeeDashboard = defineAsyncComponent(() =>
  import("./employee-dashboard/EmployeeDashboard.vue")
);
const AdminDashboard = defineAsyncComponent(() =>
  import("./admin-dashboard/AdminDashboard.vue")
);
const ManagerDashboard = defineAsyncComponent(() =>
  import("./manager-dashboard/ManagerDashboard.vue")
);

export default {
  name: "MainDashboard",

  components: {
    EmployeeDashboard,
    AdminDashboard,
    ManagerDashboard,
  },

  data() {
    return {
      tabsList: [this.$t("dashboard.myDashboard")], // My Dashboard
      currentTabItem: "tab-0",
      employeeDashboardTab: "tab-0",
      adminDashboardTab: "",
      managerDashboard: "",
      openMobileUpdateModal: false,
    };
  },
  computed: {
    domainName() {
      return this.$store.getters.domain;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    teamSummaryFormAccess() {
      let formAccess = this.accessRights("243");
      if (formAccess && formAccess.accessRights) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    isManager() {
      return this.$store.state.isManager;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    // Lazy loading computed properties for performance optimization
    showEmployeeDashboard() {
      return this.currentTabItem === this.employeeDashboardTab;
    },
    showAdminDashboard() {
      return (
        this.currentTabItem === this.adminDashboardTab &&
        this.adminDashboardTab !== ""
      );
    },
    showManagerDashboard() {
      return (
        this.currentTabItem === this.managerDashboard &&
        this.managerDashboard !== ""
      );
    },
  },

  mounted() {
    // Enabling alert if mobile app is 1
    if (
      parseInt(this.$cookies.get("isRequestFromMobile")) === 1 &&
      parseInt(this.$cookies.get("isAppUptoDate")) === 0 &&
      this.domainName !== "cannyhr"
    ) {
      this.openMobileUpdateModal = true;
    } else {
      this.openMobileUpdateModal = false;
    }
    // Check rights for employee admin
    let employeeAdmin = this.teamSummaryFormAccess;
    // We present org dashboard only for admin, super admin, employee admin
    if (this.isAdmin || (employeeAdmin && employeeAdmin.admin === "admin")) {
      this.tabsList.push(this.$t("dashboard.organizationDashboard")); // Organization Dashboard
      this.adminDashboardTab = "tab-1";
    }
    // Present team dashboard only for managers
    else if (this.isManager || (employeeAdmin && employeeAdmin.isManager)) {
      this.tabsList.push(this.$t("dashboard.teamDashboard")); // Team Dashboard
      this.managerDashboard = "tab-1";
    }
  },

  methods: {
    // Redirect to playstore app
    redirectToPlaystoreApp() {
      // this.openMobileUpdateModal = false;
      window.location.href = this.baseUrl + "v3/dashboard";
    },
    // Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    // On tab change change the current selected tab item
    onTabChange(tabName) {
      if (tabName === this.$t("dashboard.organizationDashboard")) {
        // Organization Dashboard
        this.currentTabItem = this.adminDashboardTab;
      } else if (tabName === this.$t("dashboard.teamDashboard")) {
        // Team Dashboard
        this.currentTabItem = this.managerDashboard;
      } else {
        this.currentTabItem = this.employeeDashboardTab; // On default we show my dashboard only
      }
    },
  },
};
</script>

<style lang="css">
.card-spacing {
  padding: 1em;
}
@media screen and (min-width: 426px) {
  .dashboard-container {
    padding: 5em 2em 0em 1em;
  }
}
.v-carousel__controls {
  overflow: hidden;
  background-color: #260029 !important;
  position: fixed !important;
  margin-left: -20px;
}
.v-carousel__controls__item {
  color: #ff0358 !important;
}
.v-carousel
  > .v-carousel__controls
  > .v-item-group
  > .theme--light.v-btn--active::before {
  opacity: 1 !important;
}
.v-carousel
  > .v-carousel__controls
  > .v-item-group
  > .v-btn--icon.v-size--small
  .v-icon {
  opacity: 1 !important;
}
</style>
