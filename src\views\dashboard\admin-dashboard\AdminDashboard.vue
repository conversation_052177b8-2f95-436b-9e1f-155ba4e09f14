<template>
  <div class="fill-height">
    <!-- Desktop Admin Dashboard Layout -->
    <v-container v-if="!isMobileView" fluid class="main-container pa-4">
      <!-- Row 1: Admin Statistics Cards -->
      <v-row class="mb-4">
        <v-col cols="12" class="pa-1">
          <AdminStatisticsCards @open-snackbar="showAlert($event)" />
        </v-col>
      </v-row>

      <!-- Row 2: Leave Distribution | My Team Updates -->
      <v-row class="mb-4">
        <!-- Leave Distribution -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <LeaveDistribution class="card-highlight" />
        </v-col>
        <!-- My Team Updates -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <MyTeamUpdates class="card-highlight" />
        </v-col>
      </v-row>

      <!-- Row 3: My Team Availability | My Team Action -->
      <v-row>
        <!-- My Team Availability -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <MyTeamAvailability class="card-highlight" />
        </v-col>
        <!-- My Team Action -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <MyTeamAction class="card-highlight" calling-from="admin-dashboard" />
        </v-col>
      </v-row>
    </v-container>

    <!-- Mobile Admin Dashboard Layout - Carousel -->
    <v-container v-else fluid class="main-container pa-2">
      <v-carousel :show-arrows="false" height="100%" hide-delimiter-background>
        <!-- Admin Statistics Card -->
        <v-carousel-item>
          <v-card class="fill-height ma-2 rounded-lg" elevation="2">
            <div class="pa-2 fill-height">
              <AdminStatisticsCards @open-snackbar="showAlert($event)" />
            </div>
          </v-card>
        </v-carousel-item>

        <!-- Leave Distribution Card -->
        <v-carousel-item>
          <v-card class="fill-height ma-2 rounded-lg" elevation="2">
            <div class="pa-2 fill-height">
              <LeaveDistribution />
            </div>
          </v-card>
        </v-carousel-item>

        <!-- My Team Updates Card -->
        <v-carousel-item>
          <v-card class="fill-height ma-2 rounded-lg" elevation="2">
            <div class="pa-2 fill-height">
              <MyTeamUpdates />
            </div>
          </v-card>
        </v-carousel-item>

        <!-- My Team Availability Card -->
        <v-carousel-item>
          <v-card class="fill-height ma-2 rounded-lg" elevation="2">
            <div class="pa-2 fill-height">
              <MyTeamAvailability />
            </div>
          </v-card>
        </v-carousel-item>

        <!-- My Team Action Card -->
        <v-carousel-item>
          <v-card class="fill-height ma-2 rounded-lg" elevation="2">
            <div class="pa-2 fill-height">
              <MyTeamAction calling-from="admin-dashboard" />
            </div>
          </v-card>
        </v-carousel-item>
      </v-carousel>
    </v-container>
  </div>
</template>

<script>
// Performance-optimized imports using defineAsyncComponent for admin dashboard widgets
import { defineAsyncComponent } from "vue";
const AdminStatisticsCards = defineAsyncComponent(() =>
  import("./AdminStatisticsCards.vue")
);
const LeaveDistribution = defineAsyncComponent(() =>
  import("./LeaveDistribution.vue")
);
const MyTeamUpdates = defineAsyncComponent(() => import("./MyTeamUpdates.vue"));
const MyTeamAvailability = defineAsyncComponent(() =>
  import("./MyTeamAvailability.vue")
);
const MyTeamAction = defineAsyncComponent(() => import("./MyTeamAction.vue"));

export default {
  name: "AdminDashboard",

  components: {
    AdminStatisticsCards,
    LeaveDistribution,
    MyTeamUpdates,
    MyTeamAvailability,
    MyTeamAction,
  },

  data() {
    return {};
  },

  computed: {
    // Check if mobile view
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // Current window width
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },

  methods: {
    // Show alert/snackbar message
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.main-container {
  padding: 16px;
  height: 100%;
}
.card-highlight {
  transition: box-shadow 0.3s ease;
}

.card-highlight:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}
</style>
