<template>
  <div>
    <v-row>
      <!-- Active and Inactive Employees Count -->
      <v-col cols="12" sm="12" md="6" :lg="windowWidth > 1550 ? 2 : 6" xl="2">
        <v-card
          class="py-3 d-flex card-highlight rounded-lg uniform-card-height"
        >
          <!-- Employee Count Loading card -->
          <v-row v-if="isEmployeeCountLoading">
            <v-col v-for="i in 2" :key="i" cols="6" class="pa-1 px-3">
              <v-skeleton-loader class="mx-auto" height="55" type="image" />
            </v-col>
          </v-row>

          <!-- Employee Count Error Card -->
          <v-row v-else-if="errorInEmployeeCount">
            <v-col cols="12" class="pa-1 px-3">
              <StatisticsCardWithBg
                id="admindashboard_employeecount_refresh"
                image-name="admin-dashboard/inactive-employees"
                :is-error="true"
                @refresh-action="refreshEmployeeCount"
              />
            </v-col>
          </v-row>

          <!-- Active and Inactive employee Count Display Card -->
          <v-row v-else class="px-2">
            <v-col
              :cols="windowWidth > 380 ? 6 : 12"
              :class="windowWidth > 380 ? 'pa-3 pr-1' : 'pa-3 pb-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_active_emp_statistics"
                image-name="admin-dashboard/active-employees"
                card-bg-color="green-lighten-5"
                :title="activeEmployeesCount"
                :subtitle="$t('dashboard.active')"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 6 : 12"
              :class="windowWidth > 380 ? 'pa-3 pl-1' : 'pa-3 pt-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_inactive_emp_statistics"
                image-name="admin-dashboard/inactive-employees"
                card-bg-color="pink-lighten-5"
                :title="inactiveEmployeesCount"
                :subtitle="$t('dashboard.inactive')"
              />
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <!-- Employee's Present, absent and Leave Count -->
      <v-col cols="12" sm="12" md="6" :lg="windowWidth > 1550 ? 4 : 6" xl="4">
        <v-card
          class="py-3 d-flex card-highlight rounded-lg uniform-card-height"
        >
          <!-- Employee present & absent Loading card -->
          <v-row v-if="isEmpAvailabilityLoading">
            <v-col v-for="i in 3" :key="i" cols="4" class="pa-1 px-3">
              <v-skeleton-loader class="mx-auto" height="55" type="image" />
            </v-col>
          </v-row>

          <!-- Employee present & absent Count Error Card -->
          <v-row v-else-if="errorInEmpAvailabilityCount">
            <v-col cols="12" class="pa-1 px-3">
              <StatisticsCardWithBg
                id="admindashboard_presentemployee_refresh"
                image-name="admin-dashboard/present-employee"
                :is-error="true"
                @refresh-action="refreshEmpAvailability"
              />
            </v-col>
          </v-row>

          <!-- present & absent Count Display Card -->
          <v-row v-else class="px-2">
            <v-col
              :cols="windowWidth > 380 ? 2 : 12"
              :class="windowWidth > 380 ? 'pa-3 pr-1' : 'pa-3 pb-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_present_statistics"
                image-name="admin-dashboard/present-employee"
                :title="presentCount"
                :subtitle="$t('dashboard.present')"
                card-bg-color="light-green-lighten-5"
                :is-clickable="true"
                image-width="25"
                @card-click-action="redirectToAttendanceBox()"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 2 : 12"
              :class="windowWidth > 380 ? 'pa-3 px-1' : 'pa-3 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_absent_statistics"
                image-name="admin-dashboard/absent-time"
                :title="absentCount"
                :subtitle="$t('dashboard.absent')"
                card-bg-color="red-lighten-5"
                :is-clickable="true"
                image-width="25"
                @card-click-action="redirectToAttendanceBox()"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 2 : 12"
              :class="windowWidth > 380 ? 'pa-3 px-1' : 'pa-3 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_leave_statistics"
                image-name="admin-dashboard/leave-timer"
                card-bg-color="pink-lighten-5"
                :title="leaveCount"
                :subtitle="$t('dashboard.onLeave')"
                image-width="25"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 2 : 12"
              :class="windowWidth > 380 ? 'pa-3 pl-1' : 'pa-3 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_compoff_statistics"
                icon-name="fas fa-briefcase"
                card-bg-color="purple-lighten-5"
                :title="compOffCount"
                :subtitle="$t('dashboard.compOff')"
                icon-size="25"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 4 : 12"
              :class="windowWidth > 380 ? 'pa-3 pl-1' : 'pa-3 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_leave_statistics"
                image-name="admin-dashboard/person-with-top-clock"
                icon-name="fas fa-calendar"
                card-bg-color="blue-grey-lighten-5"
                :title="shiftNotScheduledCount"
                :subtitle="$t('dashboard.unscheduledShift')"
                icon-size="25"
              />
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <!-- Workplace Counts(Office, Work from home , field and others) -->
      <v-col cols="12" sm="12" md="6" :lg="windowWidth > 1550 ? 3 : 6" xl="3">
        <v-card
          class="py-3 d-flex card-highlight rounded-lg uniform-card-height"
        >
          <!-- Workplace count Loading card -->
          <v-row v-if="isWorkplaceCountLoading">
            <v-col v-for="i in 4" :key="i" cols="3" class="pa-1 px-3">
              <v-skeleton-loader class="mx-auto" height="55" type="image" />
            </v-col>
          </v-row>

          <!--Workplace Count Error Card -->
          <v-row v-else-if="errorInWorkplaceCount">
            <v-col cols="12" class="pa-1 px-3">
              <StatisticsCardWithBg
                id="admindashboard_workplacecount_refresh"
                image-name="admin-dashboard/company"
                :is-error="true"
                @refresh-action="refreshWorkplaceCount"
              />
            </v-col>
          </v-row>

          <!--Workplace Count Display card -->
          <v-row v-else class="px-2">
            <v-col
              :cols="windowWidth > 380 ? 3 : 12"
              :class="windowWidth > 380 ? 'pa-3 pr-1' : 'pa-3 pb-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_company_workplace_statistics"
                image-name="admin-dashboard/company"
                card-bg-color="lime-lighten-5"
                image-width="35"
                :title="officeCount"
                :subtitle="$t('dashboard.office')"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 3 : 12"
              :class="windowWidth > 380 ? 'pa-3 px-1' : 'pa-3 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_home_workplace_statistics"
                image-name="admin-dashboard/work-from-home"
                card-bg-color="light-blue-lighten-5"
                image-width="33"
                :title="workFromHomeCount"
                :subtitle="$t('dashboard.WFH')"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 3 : 12"
              :class="windowWidth > 380 ? 'pa-3 px-1' : 'pa-3 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_field_workplace_statistics"
                image-name="admin-dashboard/filed-work-tree"
                card-bg-color="purple-lighten-5"
                image-width="35"
                :title="fieldWorkCount"
                :subtitle="$t('dashboard.field')"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 3 : 12"
              :class="windowWidth > 380 ? 'pa-3 pl-1' : 'pa-3 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_other_workplace_statistics"
                image-name="admin-dashboard/other-workplace"
                card-bg-color="green-accent-1"
                image-width="25"
                :title="otherWorkplaceCount"
                :subtitle="$t('dashboard.others')"
              />
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <!-- Ontime and late arrival count of employees -->
      <v-col cols="12" sm="12" md="6" :lg="windowWidth > 1550 ? 3 : 6" xl="3">
        <v-card
          class="py-3 d-flex card-highlight rounded-lg uniform-card-height"
        >
          <!-- Attendance count Loading card -->
          <v-row v-if="isAttendanceCountLoading">
            <v-col v-for="i in 2" :key="i" cols="6" class="pa-1 px-3">
              <v-skeleton-loader class="mx-auto" height="55" type="image" />
            </v-col>
          </v-row>

          <!--Attendance Count Error Card -->
          <v-row v-else-if="errorInAttendanceCount">
            <v-col cols="12" class="pa-1 px-3">
              <StatisticsCardWithBg
                id="admindashboard_attendancecount_refresh"
                image-name="dashboard/at-work"
                :is-error="true"
                @refresh-action="refreshAttendanceCount"
              />
            </v-col>
          </v-row>

          <!--Attendance Count Display Card -->
          <v-row v-else class="px-2">
            <v-col
              :cols="windowWidth > 380 ? 6 : 12"
              :class="windowWidth > 380 ? 'pa-3 pr-1' : 'pa-3 pb-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_ontime_statistics"
                image-name="dashboard/at-work"
                card-bg-color="light-blue-lighten-5"
                :title="onTimeCount"
                :subtitle="$t('dashboard.onTimeArrivals')"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 6 : 12"
              :class="windowWidth > 380 ? 'pa-3 pl-1' : 'pa-3 pt-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_late_statistics"
                image-name="admin-dashboard/late-tortoise"
                card-bg-color="red-lighten-5"
                image-width="55"
                :title="lateArrivalCount"
                :subtitle="$t('dashboard.lateArrivals')"
              />
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
// Performance-optimized imports using defineAsyncComponent for heavy components
import { defineAsyncComponent } from "vue";

// GraphQL queries
import {
  GET_EMPLOYEES_COUNT,
  GET_ATTENDANCE_COUNT,
  GET_WORKPLACE_COUNT,
} from "@/graphql/dashboard/dashboardQueries";

const StatisticsCardWithBg = defineAsyncComponent(() =>
  import("@/components/helper-components/StatisticsCardWithBg.vue")
);

export default {
  name: "AdminStatisticsCards",

  components: {
    StatisticsCardWithBg,
  },

  emits: ["open-snackbar"],

  data() {
    return {
      // Employee count data
      activeEmployeesCount: 0,
      inactiveEmployeesCount: 0,
      errorInEmployeeCount: false,

      // Employee availability data
      presentCount: 0,
      absentCount: 0,
      leaveCount: 0,
      compOffCount: 0,
      shiftNotScheduledCount: 0,
      errorInEmpAvailabilityCount: false,
      isEmpAvailabilityLoading: false,

      // Workplace count data
      officeCount: 0,
      workFromHomeCount: 0,
      fieldWorkCount: 0,
      otherWorkplaceCount: 0,
      errorInWorkplaceCount: false,

      onTimeCount: 0,
      lateArrivalCount: 0,
      errorInAttendanceCount: false,
      isEmployeeCountLoading: false,
      isAttendanceCountLoading: false,
      isWorkplaceCountLoading: false,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    baseUrl() {
      // return "https://fieldforce.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
  },

  mounted() {
    this.getEmployeeCount();
    this.getAttendanceCount();
    this.getWorkplaceCount();
    this.getPresentEmployeeCount();
  },
  methods: {
    getEmployeeCount() {
      this.isEmployeeCountLoading = true;
      this.errorInEmployeeCount = false;
      this.$apollo
        .query({
          query: GET_EMPLOYEES_COUNT,
          client: "apolloClientC",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getEmployeeCount &&
            !response.data.getEmployeeCount.errorCode
          ) {
            let employeeCountData = response.data.getEmployeeCount;
            this.activeEmployeesCount =
              employeeCountData.activeEmployeesCount || 0;
            this.inactiveEmployeesCount =
              (employeeCountData.totalEmployeesCount || 0) -
              (employeeCountData.activeEmployeesCount || 0);
          } else {
            this.errorInEmployeeCount = true;
          }
          this.isEmployeeCountLoading = false;
        })
        .catch(() => {
          this.errorInEmployeeCount = true;
          this.isEmployeeCountLoading = false;
        });
    },
    getAttendanceCount() {
      this.isAttendanceCountLoading = true;
      this.errorInAttendanceCount = false;
      this.$apollo
        .query({
          query: GET_ATTENDANCE_COUNT,
          client: "apolloClientC",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getAttendanceCount &&
            !response.data.getAttendanceCount.errorCode
          ) {
            let attendanceCountData = response.data.getAttendanceCount;
            this.presentCount = attendanceCountData.onTimeArrivals || 0;
            this.absentCount = attendanceCountData.lateArrivals || 0;
          } else {
            this.errorInAttendanceCount = true;
          }
          this.isAttendanceCountLoading = false;
        })
        .catch(() => {
          this.errorInAttendanceCount = true;
          this.isAttendanceCountLoading = false;
        });
    },
    getWorkplaceCount() {
      this.isWorkplaceCountLoading = true;
      this.errorInWorkplaceCount = false;
      this.$apollo
        .query({
          query: GET_WORKPLACE_COUNT,
          client: "apolloClientC",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getWorkPlaceCount &&
            !response.data.getWorkPlaceCount.errorCode
          ) {
            let workPlaceCountData = response.data.getWorkPlaceCount;
            let workPlaceCount = workPlaceCountData.workPlaceCount;
            this.officeCount = workPlaceCount.office || 0;
            this.workFromHomeCount = workPlaceCount.workFromHome || 0;
            this.fieldWorkCount = workPlaceCount.field || 0;
            this.otherWorkplaceCount = workPlaceCount.others || 0;
          } else {
            this.errorInWorkplaceCount = true;
          }
          this.isWorkplaceCountLoading = false;
        })
        .catch(() => {
          this.errorInWorkplaceCount = true;
          this.isWorkplaceCountLoading = false;
        });
    },
    refreshEmployeeCount() {
      this.errorInEmployeeCount = false;
      this.getEmployeeCount();
    },
    refreshAttendanceCount() {
      this.errorInAttendanceCount = false;
      this.getAttendanceCount();
    },
    refreshWorkplaceCount() {
      this.errorInWorkplaceCount = false;
      this.getWorkplaceCount();
    },
    refreshEmpAvailability() {
      this.errorInEmpAvailabilityCount = false;
      this.getPresentEmployeeCount();
    },

    // Navigation methods
    redirectToAttendanceBox() {
      window.open(
        this.baseUrl + "employees/attendance/attendance-box",
        "_blank"
      );
    },
    redirectToLeaveApprovals() {
      window.open(this.baseUrl + "employees/leave-approvals", "_blank");
    },
    redirectToOtherApprovals() {
      window.open(this.baseUrl + "employees/other-approvals", "_blank");
    },
    redirectToEmployeeList() {
      window.open(this.baseUrl + "employees", "_blank");
    },
    async getPresentEmployeeCount() {
      let vm = this;
      vm.isEmpAvailabilityLoading = true;
      vm.errorInEmpAvailabilityCount = false;

      try {
        const apiObj = {
          url:
            vm.baseUrl +
            "employees/attendance/get-employee-details/isAction/Present Employees",
          type: "POST",
          async: false,
          dataType: "json",
          data: {
            requestResource: "HRAPPUI",
          },
        };

        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );

        let empAvailabilityData = response;

        if (empAvailabilityData && empAvailabilityData.success) {
          vm.presentCount = empAvailabilityData.presentCount || 0;
          vm.absentCount = empAvailabilityData.absentCount || 0;
          vm.compOffCount = empAvailabilityData.compOffCount || 0;
          vm.leaveCount = empAvailabilityData.leaveCount || 0;
          vm.shiftNotScheduledCount =
            empAvailabilityData.shiftNotScheduledCount || 0;
        } else {
          // in case of session expired we get expired message
          if (empAvailabilityData.msg === "Session Expired") {
            vm.userLogout();
          } else {
            vm.errorInEmpAvailabilityCount = true;
          }
        }
      } catch (getPresentEmpCountError) {
        if (getPresentEmpCountError.status == 200) {
          // status 200 in case of session expired so redirect user to auth
          vm.userLogout();
        } else {
          vm.errorInEmpAvailabilityCount = true;
        }
      } finally {
        vm.isEmpAvailabilityLoading = false;
      }
    },
    userLogout() {
      this.$store.dispatch("clearUserLock");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.card-highlight {
  transition: box-shadow 0.3s ease;
}

.card-highlight:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Uniform height for container cards */
.uniform-card-height {
  min-height: 90px !important;
}

/* Uniform height for all statistics cards */
:deep(.statistics-card-bg) {
  min-height: 70px !important;
  height: 70px !important;
  display: flex !important;
  align-items: center !important;
}

:deep(.statistics-card-bg .d-flex) {
  width: 100% !important;
  align-items: center !important;
}

/* Ensure consistent text sizing and alignment */
:deep(.statistics-card-bg .font-weight-bold) {
  font-size: 1.1rem !important;
  line-height: 1.2 !important;
}

:deep(.statistics-card-bg .font-weight-regular) {
  font-size: 0.75rem !important;
  line-height: 1.1 !important;
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .v-col {
    padding: 4px;
  }

  /* Slightly smaller height on mobile for better fit */
  :deep(.statistics-card-bg) {
    min-height: 65px !important;
    height: 65px !important;
  }
}

/* Tablet adjustments */
@media screen and (max-width: 960px) and (min-width: 601px) {
  :deep(.statistics-card-bg) {
    min-height: 68px !important;
    height: 68px !important;
  }
}
</style>
