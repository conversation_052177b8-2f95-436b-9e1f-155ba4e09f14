<template>
  <div style="height: 100%">
    <v-card
      class="pa-4 leave-distribution-card rounded-lg elevation-0"
      :height="isMobileView ? '90%' : '100%'"
    >
      <!-- Leave Distribution title -->
      <v-row>
        <v-col cols="12" class="py-2">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="blue"
              :size="22"
              class="mr-2"
            />
            <span class="ml-2 text-primary text-h6 font-weight-bold">
              {{ $t("dashboard.leaveDistribution") }}
            </span>
            <span class="ml-2 text-grey font-weight-medium">
              - {{ currentDate }}
            </span>
          </div>
        </v-col>
      </v-row>

      <!-- Leave distribution card loading screen -->
      <v-row v-if="isLeaveDistributionLoading" class="pa-1 pb-0">
        <v-col cols="12" class="py-0">
          <div
            class="d-flex flex-column align-center leave-distribution-content"
          >
            <v-row v-for="index in 3" :key="index" style="width: 100%">
              <v-col cols="4">
                <v-skeleton-loader class="mx-auto" height="50" type="image" />
              </v-col>
              <v-col cols="8" class="d-flex">
                <v-skeleton-loader
                  v-for="i in 4"
                  :key="i"
                  class="mx-auto"
                  type="avatar"
                />
              </v-col>
              <v-col v-if="index !== 3" cols="12">
                <v-divider />
              </v-col>
            </v-row>
          </div>
        </v-col>
      </v-row>

      <!-- Leave distribution Error And Empty screen -->
      <v-row
        v-else-if="
          errorInLeaveDistribution || leaveDistributionData.length === 0
        "
        class="pa-1 pb-0"
      >
        <v-col cols="12" class="py-0">
          <div class="d-flex align-center leave-distribution-content">
            <NoDataCardWithQuotes
              id="admin_dashboard_leave_distribution_refresh"
              image-name="admin-dashboard/leave-distribution"
              :primary-bold-text="$t('dashboard.relaxed')"
              :text-message="$t('dashboard.refreshedRecharged')"
              :is-small-card="false"
              :card-type="errorInLeaveDistribution ? 'error' : 'no-data'"
              :error-content="
                errorInLeaveDistribution
                  ? $t('dashboard.technicalDifficulties')
                  : ''
              "
              image-size="80%"
              :is-show-image="windowWidth > 960"
              @refresh-triggered="refreshLeaveDistribution()"
            />
          </div>
        </v-col>
      </v-row>

      <!-- Leave Distribution Content-->
      <v-row v-else>
        <v-col cols="12">
          <perfect-scrollbar
            class="w-100 overflow-y-auto overflow-x-hidden leave-distribution-scrollbar"
          >
            <div class="leave-distribution-content">
              <v-row
                v-for="(leaveDistribution, index) in leaveDistributionData"
                :key="leaveDistribution.leaveName"
                class="align-center"
                style="width: 100%; margin: 0px"
              >
                <v-col
                  cols="12"
                  xs="12"
                  :sm="windowWidth > 700 ? 3 : 4"
                  md="4"
                  :lg="windowWidth > 1580 ? 3 : 4"
                  class="pa-1"
                >
                  <div class="d-flex align-center">
                    <v-icon :color="leaveColorCode[index % 6]" size="13">
                      fas fa-circle
                    </v-icon>
                    <div
                      class="mx-1 font-weight-medium text-primary"
                      style="word-break: break-word; font-size: 15px"
                    >
                      {{ leaveDistribution.leaveName }}
                    </div>
                  </div>
                  <div class="pl-1 d-flex align-center">
                    <v-progress-linear
                      rounded
                      height="10"
                      :model-value="leaveDistribution.leavePercent"
                      :buffer-value="100"
                      :color="leaveColorCode[index % 6]"
                      style="width: 90px; border-radius: 15px"
                    />
                    <p
                      class="mb-0 mx-2 font-weight-medium text-primary"
                      style="word-break: break-word"
                    >
                      {{ leaveDistribution.leaveCount }}
                    </p>
                  </div>
                </v-col>
                <v-col
                  cols="12"
                  xs="12"
                  :sm="windowWidth > 700 ? 9 : 8"
                  md="8"
                  :lg="windowWidth > 1580 ? 9 : 8"
                  class="pa-1"
                >
                  <AvatarSlider
                    :avatar-array="
                      leaveDistribution.currentDateLeaveEmployeeDetails
                    "
                  />
                </v-col>
                <v-col
                  v-if="index !== leaveDistributionData.length - 1"
                  cols="12"
                >
                  <v-divider />
                </v-col>
              </v-row>
            </div>
          </perfect-scrollbar>
        </v-col>
      </v-row>
    </v-card>
  </div>
</template>

<script>
import moment from "moment";
import { orgDateFormatter } from "@/helper";
// GraphQL queries
import { GET_LEAVE_DISTRIBUTION } from "@/graphql/dashboard/dashboardQueries";
// components
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes";
import AvatarSlider from "@/components/helper-components/AvatarSlider";
export default {
  name: "LeaveDistribution",

  components: {
    AvatarSlider,
    NoDataCardWithQuotes,
  },

  data() {
    return {
      errorInLeaveDistribution: false,
      isLeaveDistributionLoading: false,
      // random colors
      leaveColorCode: [
        "red-lighten-3",
        "purple-lighten-3",
        "yellow-darken-2",
        "light-blue-lighten-3",
        "grey-lighten-1",
        "indigo-darken-1",
        "teal-darken-1",
        "secondary",
        "blue-darken-1",
        "lime",
      ],
    };
  },

  computed: {
    currentDate() {
      const today = moment();
      return orgDateFormatter(today);
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    //today leave count of employees
    todayLeaveCount() {
      let todayLeaveData = this.$store.state.dashboard.leaveDistributionData;
      return todayLeaveData.totalLeaveCount;
    },
    leaveDistributionData() {
      let todayLeaveData = this.$store.state.dashboard.leaveDistributionData;
      if (todayLeaveData && todayLeaveData.leaveDistribution) {
        let leaveDistribution = [...todayLeaveData.leaveDistribution];
        leaveDistribution.forEach((leave) => {
          // calculate the today leave percentage based on leave count and today total leave count
          let leavesTaken = leave.leaveCount ? leave.leaveCount : 0;
          let totalLeaveDays = this.todayLeaveCount;
          let percent = (leavesTaken / totalLeaveDays) * 100;
          leave["leavePercent"] = percent;
          // assign employee name and id as title and subtitle to pass it into the common component.
          leave.currentDateLeaveEmployeeDetails.forEach((leaveDetail) => {
            leaveDetail["title"] = leaveDetail["employee_name"];
            leaveDetail["subtitle"] = leaveDetail["user_defined_employee_id"];
          });
        });
        return leaveDistribution;
      } else return [];
    },
  },
  mounted() {
    this.fetchLeaveDistribution();
  },

  methods: {
    fetchLeaveDistribution() {
      let vm = this;
      vm.isLeaveDistributionLoading = true;
      vm.errorInLeaveDistribution = false;
      vm.$apollo
        .query({
          query: GET_LEAVE_DISTRIBUTION,
          client: "apolloClientC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getLeaveDistribution &&
            !response.data.getLeaveDistribution.errorCode
          ) {
            let tempData = response.data.getLeaveDistribution;
            let leaveData = {
              leaveDistribution: tempData.leaveDistribution,
              totalLeaveCount: tempData.totalLeaveCount,
            };
            vm.$store.commit("dashboard/LEAVE_DISTRIBUTION_DATA", leaveData);
          } else {
            vm.errorInLeaveDistribution = true;
          }
          vm.isLeaveDistributionLoading = false;
        })
        .catch(() => {
          vm.errorInLeaveDistribution = true;
          vm.isLeaveDistributionLoading = false;
        });
    },
    // Refresh leave distribution data
    refreshLeaveDistribution() {
      this.errorInLeaveDistribution = false;
      this.fetchLeaveDistribution();
    },
  },
};
</script>

<style scoped>
.leave-distribution-card {
  display: flex;
  flex-direction: column;
}

.leave-distribution-content {
  min-height: 250px;
  max-height: 300px;
}

/* Leave distribution scrollbar height - responsive */
.leave-distribution-scrollbar {
  height: 280px;
  max-height: 280px;
}

/* Perfect scrollbar styling */
:deep(.ps) {
  overflow-x: hidden !important;
}

:deep(.ps__rail-y) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-y) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

:deep(.ps__rail-x) {
  display: none !important;
}
/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .leave-distribution-scrollbar {
    height: 280px;
    max-height: 300px;
  }

  .leave-distribution-content {
    min-height: 150px;
    max-height: 250px;
  }
}

@media screen and (max-width: 960px) and (min-width: 601px) {
  .leave-distribution-scrollbar {
    height: 240px;
    max-height: 240px;
  }

  .leave-distribution-content {
    min-height: 225px;
    max-height: 300px;
  }
}
</style>
