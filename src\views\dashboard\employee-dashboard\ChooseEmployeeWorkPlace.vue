<template>
  <div>
    <v-dialog
      v-model="showModal"
      class="custom-modal-radius"
      width="800px"
      min-height="250px"
      scrollable
      @click:outside="$emit('close-modal')"
    >
      <v-card class="custom-modal-radius">
        <div class="d-flex justify-end">
          <v-icon
            id="dashboard_workplace_modalclose"
            color="primary"
            class="pr-3 pt-3"
            @click="$emit('close-modal')"
          >
            fas fa-times
          </v-icon>
        </div>

        <div class="d-flex justify-center mt-n4">
          <span class="mr-2">
            <v-progress-circular
              model-value="100"
              color="primary"
              :size="22"
              class="mr-2"
            />
          </span>
          <span class="text-primary font-weight-bold" style="font-size: 1.3em">
            {{
              showAutoAttendanceOption
                ? selectedWorkPlace + " " + $t("dashboard.request")
                : $t("dashboard.selectWorkPlace")
            }}
          </span>
        </div>

        <v-card-text class="pa-0">
          <v-container class="pb-0">
            <!-- Auto Attendance Duration Selection -->
            <v-row
              v-if="showAutoAttendanceOption"
              style="padding: 1em"
              align="center"
            >
              <v-col cols="12" class="d-flex align-center flex-column">
                <div class="text-body-1 pl-2">
                  {{ $t("dashboard.selectDurationFor") }}
                  {{ selectedWorkPlace.toLowerCase() }}
                </div>
                <v-btn-toggle
                  v-model="summaryChartOption"
                  mandatory
                  base-color="white"
                  color="primary"
                  density="compact"
                  class="ma-2"
                  @update:model-value="onClickOption($event)"
                >
                  <v-btn
                    v-for="(option, i) in summaryOptions"
                    :key="option + i"
                    :value="i"
                    style="border-radius: 0px !important"
                  >
                    {{ option }}
                  </v-btn>
                </v-btn-toggle>
              </v-col>
              <v-col cols="12" class="d-flex justify-center">
                <v-btn
                  rounded="lg"
                  variant="elevated"
                  color="primary"
                  @click="onSubmitAutoAttendance()"
                >
                  {{ $t("common.submit") }}
                </v-btn>
              </v-col>
            </v-row>

            <!-- Workplace Selection -->
            <v-row
              v-else
              style="padding: 1em"
              class="d-flex justify-space-around"
            >
              <div
                v-for="place in workPlaceData"
                :id="
                  'workplace' +
                  place.workPlace?.trim()?.replace(/\s/g, '_').toLowerCase()
                "
                :key="place.workPlaceId"
              >
                <v-tooltip
                  :text="formPreApprovalMessage(place)"
                  location="top"
                  max-width="300px"
                  :disabled="!formPreApprovalMessage(place).length"
                >
                  <template #activator="{ props }">
                    <v-card
                      v-bind="props"
                      :class="
                        formPreApprovalMessage(place).length
                          ? 'pointer-block'
                          : 'common-box-shadow'
                      "
                      class="work-place-card"
                      :color="
                        formPreApprovalMessage(place).length
                          ? 'grey-lighten-3'
                          : ''
                      "
                      @click="
                        formPreApprovalMessage(place).length
                          ? {}
                          : onSelectWorkPlace(place)
                      "
                    >
                      <div>
                        <img
                          :src="getWorkPlaceImage(place.workPlace)"
                          :width="windowWidth > 500 ? 120 : 100"
                          :height="windowWidth > 500 ? 100 : 80"
                          class="mt-4 mb-2 mx-1"
                          :alt="place.workPlace"
                        />
                      </div>
                      <span class="text-primary font-weight-medium my-2">
                        @ {{ place.workPlace }}
                      </span>
                    </v-card>
                  </template>
                </v-tooltip>
              </div>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: "ChooseEmployeeWorkPlace",

  props: {
    openModal: {
      type: Boolean,
      required: true,
    },
    workPlaceData: {
      type: Array,
      required: true,
    },
    wfhPreApprovalErrorMessage: {
      type: String,
      default: "",
      required: false,
    },
    failedPreApprovals: {
      type: Array,
      default: () => [],
      required: false,
    },
  },

  emits: ["close-modal", "on-place-select", "update-auto-attendance"],

  data() {
    return {
      showModal: false,
      showAutoAttendanceOption: false,
      selectedWorkPlaceId: null,
      selectedWorkPlace: "",
      summaryChartOption: 0,
      summaryOptions: ["Full Day", "First Half", "Second Half"],
    };
  },

  computed: {
    // To know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },

    // Get workplace images based on webp support
    getWorkPlaceImage() {
      return (workPlace) => {
        let workPlaceStr = workPlace.replace(/\s+/g, "-").toLowerCase();
        const extension = this.isBrowserSupportWebp ? "webp" : "png";
        try {
          return require(`@/assets/images/dashboard/${workPlaceStr}.${extension}`);
        } catch {
          // Fallback to office image if specific workplace image doesn't exist
          return require(`@/assets/images/dashboard/office.${extension}`);
        }
      };
    },

    // Screen size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  watch: {
    openModal: {
      immediate: true,
      handler(newVal) {
        this.showModal = newVal;
      },
    },
  },

  methods: {
    formPreApprovalMessage(workplace) {
      if (!workplace || !workplace.workPlace) {
        return "";
      }
      const lowerCaseWorkplace = workplace.workPlace.toLowerCase();
      const lowerCaseFailedPreApprovals = this.failedPreApprovals.map(
        (approval) => approval.toLowerCase()
      );
      if (lowerCaseFailedPreApprovals?.includes(lowerCaseWorkplace)) {
        return `${workplace.workPlace} pre-approval request is not applied or approved for the attendance date`;
      } else {
        return "";
      }
    },

    onSelectWorkPlace(item) {
      this.selectedWorkPlace = item.workPlace;
      this.selectedWorkPlaceId = item.workPlaceId;
      if (item.autoAttendance === "Yes") {
        this.showAutoAttendanceOption = true;
      } else {
        this.$emit("on-place-select", item.workPlaceId);
      }
    },

    onClickOption(opt) {
      this.summaryChartOption = opt;
    },

    onSubmitAutoAttendance() {
      let duration = this.summaryChartOption === 0 ? 1 : 0.5;
      let period =
        this.summaryChartOption === 0
          ? ""
          : this.summaryChartOption === 1
          ? "First Half"
          : "Second Half";
      this.$emit("update-auto-attendance", [
        this.selectedWorkPlaceId,
        duration,
        period,
      ]);
    },
  },
};
</script>

<style scoped>
.custom-modal-radius {
  border-radius: 15px !important;
}

.work-place-card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0.5em;
  border-bottom: 6px solid rgb(var(--v-theme-secondary));
  border-radius: 10px !important;
  margin: 0.7em 0.3em;
  cursor: pointer;
  transition: all 0.2s ease;
}
</style>
