<template>
  <div class="fill-height" :class="{ 'mobile-root': isMobileView }">
    <!-- Desktop Dashboard Layout -->
    <v-container v-if="!isMobileView" fluid class="main-container">
      <v-row>
        <!-- Employee Profile and Calendar Card -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <v-card class="card-highlight rounded-lg" height="100%">
            <v-row>
              <v-col
                :sm="windowWidth > 800 ? '6' : '12'"
                md="6"
                lg="6"
                xl="6"
                cols="12"
              >
                <EmployeeProfile @open-snackbar="showAlert($event)" />
              </v-col>
              <v-col
                cols="12"
                :sm="windowWidth > 800 ? '6' : '12'"
                md="6"
                lg="6"
                xl="6"
                style="width: 100%"
                class="pa-0"
              >
                <HolidaysAndCalendar />
              </v-col>
            </v-row>
          </v-card>
        </v-col>

        <!-- Leave History Card -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <v-card class="card-highlight rounded-lg" height="100%">
            <v-row>
              <v-col cols="12">
                <EmployeeLeaveHistory />
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>

      <v-row>
        <!-- Utilization and Time off -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <MyUtilizationAndTimeoff
            class="card-highlight"
            :random-colors="vuetifyColors"
          />
        </v-col>

        <!-- Actions -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <MyActions class="card-highlight" :random-colors="vuetifyColors" />
        </v-col>
      </v-row>

      <v-row>
        <!-- Compliance -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <MyCompliance class="card-highlight" :random-colors="vuetifyColors" />
        </v-col>

        <!-- Company Updates -->
        <v-col cols="12" sm="12" md="12" lg="6" xl="6" class="card-spacing">
          <MyCompanyUpdates class="card-highlight" />
        </v-col>
      </v-row>
    </v-container>

    <!-- Mobile Dashboard Layout (Carousel) -->
    <v-container v-if="isMobileView" fluid class="main-container">
      <v-carousel :show-arrows="false" height="100%" hide-delimiter-background>
        <v-carousel-item>
          <v-sheet color="white" class="rounded-lg fill-height">
            <EmployeeProfile @open-snackbar="showAlert($event)" />
          </v-sheet>
        </v-carousel-item>

        <v-carousel-item>
          <v-sheet color="white" class="rounded-lg fill-height">
            <HolidaysAndCalendar />
          </v-sheet>
        </v-carousel-item>

        <v-carousel-item>
          <v-sheet color="white" class="rounded-lg fill-height">
            <EmployeeLeaveHistory />
          </v-sheet>
        </v-carousel-item>

        <v-carousel-item>
          <v-sheet color="white" class="rounded-lg fill-height">
            <MyUtilizationAndTimeoff :random-colors="vuetifyColors" />
          </v-sheet>
        </v-carousel-item>

        <v-carousel-item>
          <v-sheet color="white" class="rounded-lg fill-height">
            <MyActions :random-colors="vuetifyColors" />
          </v-sheet>
        </v-carousel-item>

        <v-carousel-item>
          <v-sheet color="white" class="rounded-lg fill-height">
            <MyCompliance :random-colors="vuetifyColors" />
          </v-sheet>
        </v-carousel-item>

        <v-carousel-item>
          <v-sheet color="white" class="rounded-lg fill-height">
            <MyCompanyUpdates />
          </v-sheet>
        </v-carousel-item>
      </v-carousel>
    </v-container>

    <!-- Tax Regime Comparison Modal -->
    <TaxRegimeComparisonModal
      v-if="openTaxRegimeModal"
      :openTaxComparisonModal="openTaxRegimeModal"
      @close-tax-notify-modal="closeTaxComparisonModal"
      @open-snackbar="showAlert($event)"
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeProfile = defineAsyncComponent(() =>
  import("./EmployeeProfile.vue")
);
const HolidaysAndCalendar = defineAsyncComponent(() =>
  import("./HolidaysAndCalendar.vue")
);
const EmployeeLeaveHistory = defineAsyncComponent(() =>
  import("./EmployeeLeaveHistory.vue")
);
const MyUtilizationAndTimeoff = defineAsyncComponent(() =>
  import("./MyUtilizationAndTimeoff.vue")
);
const MyActions = defineAsyncComponent(() => import("./MyActions.vue"));
const MyCompliance = defineAsyncComponent(() => import("./MyCompliance.vue"));
const MyCompanyUpdates = defineAsyncComponent(() =>
  import("./MyCompanyUpdates.vue")
);
const TaxRegimeComparisonModal = defineAsyncComponent(() =>
  import("./TaxRegimeComparisonModal.vue")
);

export default {
  name: "EmployeeDashboard",

  components: {
    EmployeeProfile,
    EmployeeLeaveHistory,
    MyUtilizationAndTimeoff,
    HolidaysAndCalendar,
    MyActions,
    MyCompliance,
    TaxRegimeComparisonModal,
    MyCompanyUpdates,
  },

  data() {
    return {
      vuetifyColors: [
        // Vuetify base colors for random color generation
        "red",
        "light-green",
        "pink",
        "blue-grey",
        "purple",
        "orange",
        "yellow",
        "deep-purple",
        "lime",
        "indigo",
        "blue",
        "brown",
        "light-blue",
        "cyan",
        "amber",
        "teal",
        "deep-orange",
        "green",
      ],
    };
  },

  computed: {
    openTaxRegimeModal() {
      return this.$store.state.showTaxComparisonModal;
    },
    // Get watch position ID for geolocation
    getWatchPositionId() {
      return this.$store.state.dashboard?.watchPositionId;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // Screen size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    payrollCountry() {
      return this.$store.state.payrollCountry;
    },
  },

  mounted() {
    // Action call to retrieve and show tax regime amount comparison detail on dashboard load
    if (this.payrollCountry === "in") {
      this.$store.dispatch("retrieveTaxRegimeAmount");
    }
    // Initialize the geolocation watch
    this.$store.dispatch("dashboard/initializeGeolocationWatch");
  },

  unmounted() {
    // Clear watchPosition if it exists
    // As of now we have used this within dashboard only so no need in other routes
    if (this.getWatchPositionId) {
      this.$store.dispatch(
        "dashboard/clearGeoWatchPosition",
        this.getWatchPositionId
      );
    }
  },

  methods: {
    // Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeTaxComparisonModal() {
      this.$store.commit("SHOW_TAX_COMPARISON_MODAL", false);
    },
  },
};
</script>

<style lang="scss" scoped>
.main-container {
  padding: 16px;
  height: 100%;
}

// Mobile root container
.mobile-root {
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
}

.card-highlight {
  transition: box-shadow 0.3s ease;
}

.card-highlight:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}
</style>
