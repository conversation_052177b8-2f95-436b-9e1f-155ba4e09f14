<template>
  <v-card class="card-highlight rounded-lg d-flex flex-column" height="100%">
    <!-- Header Section -->
    <v-card-title class="d-flex align-center pa-4 pb-2">
      <v-progress-circular
        model-value="100"
        color="purple"
        :size="20"
        class="mr-3"
      />
      <span class="text-h6 font-weight-bold text-primary">
        {{ $t("dashboard.myLeaves") }}
      </span>
    </v-card-title>

    <!-- Leave history Loading -->
    <v-card-text v-if="isLeaveHistoryLoading" class="pa-4">
      <v-skeleton-loader
        v-for="i in 5"
        :key="i"
        type="list-item-avatar"
        class="mx-auto mb-2"
      />
    </v-card-text>

    <!-- Leave history Error card -->
    <v-card-text
      v-else-if="isErrorInLeaveHistory && !leaveHistoryNoViewAccess"
      class="pa-4"
    >
      <NoDataCardWithQuotes
        id="dashboard_leave_history_refresh"
        image-name="dashboard/leave-history"
        card-type="error"
        :is-small-card="false"
        image-size="150"
        @refresh-triggered="refreshLeaveHistory()"
      />
    </v-card-text>

    <!-- No data or no access card -->
    <v-card-text
      v-else-if="leaveHistory?.length === 0 || leaveHistoryNoViewAccess"
      class="pa-4"
      :style="imageHeight"
    >
      <NoDataCardWithQuotes
        image-name="dashboard/leave-history"
        :primary-bold-text="$t('dashboard.peopleWait')"
        :text-message="$t('dashboard.leaveWaitingMessage')"
        card-type="no-data"
        image-size="150"
        :is-show-image="windowWidth > 960"
        :is-access-denied="leaveHistoryNoViewAccess"
      />
    </v-card-text>

    <!-- Employee Leave Balance Card -->
    <v-card-text v-else class="leave-details-card ma-3 bg-grey-lighten-5">
      <!-- Show worked days and late attendance for a month -->
      <v-row align="center" class="rounded-lg">
        <v-col cols="12" class="d-flex" style="justify-content: space-evenly">
          <!-- At Work Statistics -->
          <div class="d-flex align-center mb-3" style="max-width: 140px">
            <div
              class="mx-2 font-weight-bold d-flex align-center text-grey-darken-2 text-body-2"
            >
              <img
                :src="getAtWorkImageUrl"
                height="auto"
                width="35%"
                class="pr-1"
              />
              {{ $t("dashboard.atWork") }}
            </div>
            <div class="font-weight-bold text-primary text-body-2">
              {{
                totalWorkedDays.currentMonthBusinessWorkingDays
                  ? totalWorkedDays.currentMonthWorkedDays +
                    "/" +
                    totalWorkedDays.currentMonthBusinessWorkingDays
                  : "-"
              }}
            </div>
          </div>

          <!-- Late Attendance Statistics -->
          <v-tooltip
            :text="getLateAttendanceTooltip"
            location="top"
            max-width="300"
          >
            <template #activator="{ props }">
              <div
                v-bind="props"
                class="d-flex align-center mb-3"
                style="max-width: 104px"
              >
                <div
                  class="mx-2 font-weight-bold d-flex align-center text-grey-darken-2 text-body-2"
                >
                  <img
                    :src="getLateImageUrl"
                    height="auto"
                    width="60%"
                    class="pr-1"
                  />
                  {{ $t("dashboard.late") }}
                </div>
                <div class="font-weight-bold text-primary text-body-2">
                  {{
                    !isErrorInLateAttendance &&
                    lateAttendanceData.usedLateAttendanceCount
                      ? lateAttendanceData.usedLateAttendanceCount
                      : "-"
                  }}
                </div>
              </div>
            </template>
          </v-tooltip>
        </v-col>
      </v-row>

      <!-- Leave History Table -->
      <div class="pa-4 w-100 overflow-hidden">
        <!-- Desktop Table Headers -->
        <v-row
          v-if="!isMobileView"
          class="text-subtitle-2 font-weight-bold mb-2"
        >
          <v-col cols="3" class="px-2"></v-col>

          <v-tooltip
            :text="$t('dashboard.leaveTakenTooltip')"
            location="top"
            max-width="300"
          >
            <template #activator="{ props }">
              <v-col v-bind="props" cols="2" class="px-2 text-center">
                {{ $t("dashboard.leaveTaken") }}
              </v-col>
            </template>
          </v-tooltip>

          <v-tooltip
            :text="$t('dashboard.leaveBalanceTooltip')"
            location="top"
            max-width="300"
          >
            <template #activator="{ props }">
              <v-col v-bind="props" cols="2" class="px-2 text-center">
                {{ $t("dashboard.leaveBalance") }}
              </v-col>
            </template>
          </v-tooltip>

          <v-tooltip
            :text="$t('dashboard.currentLeaveEligibilityTooltip')"
            location="top"
            max-width="300"
          >
            <template #activator="{ props }">
              <v-col v-bind="props" cols="3" class="px-2 text-center">
                {{ $t("dashboard.currentLeaveEligibility") }}
              </v-col>
            </template>
          </v-tooltip>

          <v-tooltip
            :text="$t('dashboard.totalLeaveEligibilityTooltip')"
            location="top"
            max-width="300"
          >
            <template #activator="{ props }">
              <v-col v-bind="props" cols="2" class="px-2 text-center">
                {{ $t("dashboard.totalLeaveEligibility") }}
              </v-col>
            </template>
          </v-tooltip>
        </v-row>

        <!-- Leave History List -->
        <perfect-scrollbar
          class="w-100 overflow-y-auto overflow-x-hidden leave-history-scrollbar"
        >
          <v-row
            v-for="(leave, index) in leaveHistory"
            :key="index"
            class="ma-0 rounded transition-all w-100 overflow-hidden"
            :class="{
              'rounded-lg my-3 pa-4 border-thin elevation-2': isMobileView,
            }"
          >
            <!-- Leave Name -->
            <v-col
              :cols="isMobileView ? 12 : 3"
              class="px-2 d-flex align-center"
            >
              <div class="d-flex align-center">
                <span
                  class="font-weight-regular text-body-2"
                  style="word-break: break-word"
                >
                  {{ leave.leaveName }}
                </span>
              </div>
            </v-col>

            <!-- Leave Taken -->
            <v-col :cols="isMobileView ? 6 : 2" class="px-0 text-center">
              <div v-if="isMobileView" class="text-caption text-grey-darken-1">
                {{ $t("dashboard.leaveTaken") }}
              </div>
              <span class="font-weight-regular">
                {{ leave.leavesTaken || 0 }}
              </span>
            </v-col>

            <!-- Leave Balance -->
            <v-col :cols="isMobileView ? 6 : 2" class="px-0 text-center">
              <div v-if="isMobileView" class="text-caption text-grey-darken-1">
                {{ $t("dashboard.leaveBalance") }}
              </div>
              <span class="font-weight-regular">
                {{ leave.leaveBalance || 0 }}
              </span>
            </v-col>

            <!-- Current Leave Eligibility -->
            <v-col :cols="isMobileView ? 6 : 3" class="px-0 pl-1 text-center">
              <div v-if="isMobileView" class="text-caption text-grey-darken-1">
                {{ $t("dashboard.currentEligibility") }}
              </div>
              <span class="font-weight-regular">
                {{ leave.currentLeaveEligibility || 0 }}
              </span>
            </v-col>

            <!-- Total Leave Eligibility -->
            <v-col :cols="isMobileView ? 6 : 2" class="pr-0 pl-1 text-center">
              <div v-if="isMobileView" class="text-caption text-grey-darken-1">
                {{ $t("dashboard.totalEligibility") }}
              </div>
              <span class="font-weight-regular">
                {{ leave.totalEligibleDays || 0 }}
              </span>
            </v-col>
          </v-row>
        </perfect-scrollbar>
      </div>
    </v-card-text>
  </v-card>
</template>
<script>
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes.vue";
import {
  LIST_LEAVE_HISTORY,
  GET_WORKED_DAYS,
} from "@/graphql/dashboard/dashboardQueries";

export default {
  name: "EmployeeLeaveHistory",

  components: {
    NoDataCardWithQuotes,
  },

  data() {
    return {
      // Worked data
      lateAttendanceData: "",
      isErrorInLateAttendance: false,
      // Leave data
      isErrorInLeaveHistory: false,
      isLeaveHistoryLoading: false,
      leaveHistoryNoViewAccess: false,
      leaveHistory: [],
      // Leave type colors for visual distinction
      leaveTypeColors: [
        "red",
        "pink",
        "purple",
        "deep-purple",
        "indigo",
        "blue",
        "light-blue",
        "cyan",
        "teal",
        "green",
        "light-green",
        "lime",
        "yellow",
        "amber",
        "orange",
        "deep-orange",
      ],
    };
  },

  computed: {
    // Current window width
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    // Get worked days from store
    totalWorkedDays() {
      return this.$store.state.dashboard.employeeLeaveUtilization;
    },
    // Dynamic image height based on screen size
    imageHeight() {
      if (this.windowWidth > 1570) {
        return "height: 100%;";
      } else if (this.windowWidth > 1260) {
        return "";
      } else if (this.windowWidth > 800) {
        return "height: 100%; ";
      } else {
        return "";
      }
    },
    // Check if mobile view
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // Late attendance tooltip text
    getLateAttendanceTooltip() {
      if (!this.isErrorInLateAttendance && this.lateAttendanceData) {
        return `Delay Entry Max Limit per month - ${this.lateAttendanceData.maximumLateAttendanceCount}. Leave will be initiated after every ${this.lateAttendanceData.lateAttendanceLeaveFrequency} day(s) of delayed entry.`;
      }
      return "";
    },
    // Get image URLs
    getAtWorkImageUrl() {
      const isWebpSupported = this.$store.state.isWebpSupport;
      const extension = isWebpSupported ? "webp" : "png";
      return require(`@/assets/images/dashboard/at-work.${extension}`);
    },
    getLateImageUrl() {
      const isWebpSupported = this.$store.state.isWebpSupport;
      const extension = isWebpSupported ? "webp" : "png";
      return require(`@/assets/images/dashboard/late.${extension}`);
    },
  },

  mounted() {
    this.fetchLeaveHistory();
    this.fetchLateAttendanceStatistics();
    // Fetch utilization details for mobile view
    // In desktop view, utilization is fetched asynchronously in dashboard
    // In mobile view, we need to fetch when the card is swiped or clicked
    /** reason for calling fetchUtilization,
     * if the dashboard is loaded in mobile view, we need to fetch utilization details, to get `At Work` value
     * because, in desktop view we make a call asynchronously in dashboard. so we get tha `At Work` value when Utilization card loads
     * but in mobile view, we make a call when the card is swiped or clicking the particular carousel. So we need to fetch utilization details only for mobile view
     */
    if (this.isMobileView) {
      this.$store
        .dispatch("dashboard/fetchUtilization")
        .then(() => {})
        .catch(() => {});
    }
  },

  methods: {
    fetchLeaveHistory() {
      let vm = this;
      vm.isLeaveHistoryLoading = true;
      vm.$apollo
        .query({
          query: LIST_LEAVE_HISTORY,
          client: "apolloClientC",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.empLeaveHistory &&
            response.data.empLeaveHistory.leaveHistory &&
            !response.data.empLeaveHistory.errorCode
          ) {
            let leaveHistoryData =
              JSON.parse(response.data.empLeaveHistory.leaveHistory) || [];
            leaveHistoryData =
              leaveHistoryData?.map((item) => {
                item.leavesTaken = item.leavesTaken ? item.leavesTaken : 0;
                item.currentLeaveEligibility =
                  item.eligibleDays || item.coDays
                    ? parseFloat(item.eligibleDays) + parseFloat(item.coDays)
                    : 0;
                item.totalEligibleDays =
                  item.currentYearEligibleDays || item.coDays
                    ? parseFloat(item.currentYearEligibleDays) +
                      parseFloat(item.coDays)
                    : 0;
                if (
                  item.accuralRestriction &&
                  item.accuralRestriction.toLowerCase() === "yes" &&
                  item.Accrual === "Based on the custom configuration" &&
                  [1, 3, 6]?.includes(item.Period)
                ) {
                  item.leaveBalance = parseFloat(
                    item.remainingDaysTillCurrentLeavePeriod
                  );
                } else {
                  item.leaveBalance =
                    parseFloat(item.currentLeaveEligibility) -
                    parseFloat(item.leavesTaken);
                }
                return item;
              }) || [];
            this.leaveHistory = leaveHistoryData;
          } else {
            vm.handleLeaveHistoryError(
              response.data.empLeaveHistory?.errorCode || ""
            );
            vm.isErrorInLeaveHistory = true;
          }
          vm.isLeaveHistoryLoading = false;
        })
        .catch((err) => {
          vm.handleLeaveHistoryError(err);
          vm.isErrorInLeaveHistory = true;
          vm.isLeaveHistoryLoading = false;
        });
    },
    handleLeaveHistoryError(err = "") {
      if (err && err.graphQLErrors?.length > 0) {
        var errorParams = err.graphQLErrors[0];
        var leaveError = JSON.parse(errorParams.message);
        var errorCode = leaveError.errorCode;
        switch (errorCode) {
          case "_DB0100":
            //view access denied
            this.leaveHistoryNoViewAccess = true;
            break;
          default:
            this.isErrorInLeaveHistory = true;
            break;
        }
      } else {
        this.isErrorInLeaveHistory = true;
      }
    },
    fetchLateAttendanceStatistics() {
      let vm = this;
      vm.isErrorInLateAttendance = false;
      vm.$apollo
        .query({
          query: GET_WORKED_DAYS,
          client: "apolloClientC",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getLateAttendanceStatistics &&
            response.data.getLateAttendanceStatistics
              .lateAttendanceStatistics &&
            !response.data.getLateAttendanceStatistics.errorCode
          ) {
            this.lateAttendanceData =
              JSON.parse(
                response.data.getLateAttendanceStatistics
                  .lateAttendanceStatistics
              ) || [];
          } else {
            vm.isErrorInLateAttendance = true;
          }
        })
        .catch(() => {
          vm.isErrorInLateAttendance = true;
        });
    },
    // Refetch leave history data
    refreshLeaveHistory() {
      this.isErrorInLeaveHistory = false;
      this.leaveHistoryNoViewAccess = false;
      this.fetchLeaveHistory();
    },
  },
};
</script>

<style lang="scss" scoped>
/* Custom styles that cannot be replaced with Vuetify utilities */

/* Scrollbar height - responsive */
.leave-history-scrollbar {
  height: 140px;
  max-height: 140px;
}
.leave-details-card {
  border-radius: 15px !important;
  overflow-x: hidden;
  overflow-y: hidden;
  height: 100%;
}

@media screen and (max-width: 960px) and (min-width: 601px) {
  .leave-history-scrollbar {
    height: 160px;
    max-height: 160px;
  }
}

/* Perfect scrollbar styling */
:deep(.ps) {
  overflow-x: hidden !important;
}

:deep(.ps__rail-y) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-y) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

:deep(.ps__rail-x) {
  display: none !important;
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .leave-history-scrollbar {
    height: 180px;
    max-height: 180px;
  }

  :deep(.v-card-title) {
    padding: 16px 16px 8px 16px !important;
    font-size: 1.1rem !important;
  }

  :deep(.v-card-text) {
    padding: 16px !important;
  }
}

/* Tablet adjustments */
@media screen and (max-width: 960px) and (min-width: 601px) {
  :deep(.v-card-title) {
    padding: 20px 20px 12px 20px !important;
  }

  :deep(.v-card-text) {
    padding: 20px !important;
  }
}
</style>
