<template>
  <div class="fill-height">
    <v-card
      class="rounded-lg elevation-0 d-flex flex-column"
      :height="isMobileView ? '90%' : '100%'"
    >
      <!-- Employee Profile Loading -->
      <v-card-text v-if="isEmployeeProfileLoading" class="pa-4">
        <v-skeleton-loader
          ref="skeleton"
          type="list-item-avatar-three-line"
          class="mx-auto mb-4"
        />
        <div v-for="i in 2" :key="i" class="d-flex justify-space-between mb-2">
          <v-skeleton-loader
            v-for="j in 2"
            ref="skeleton"
            :key="j"
            type="list-item"
            style="width: 100%"
          />
        </div>
        <div class="d-flex justify-space-between">
          <v-skeleton-loader
            v-for="j in 2"
            ref="skeleton"
            :key="j"
            type="chip"
          />
        </div>
      </v-card-text>

      <!-- Employee Profile Error screen -->
      <v-card-text
        v-if="isEmployeeDataError"
        class="pa-4 d-flex align-center justify-center"
        style="min-height: 300px"
      >
        <NoDataCardWithQuotes
          id="dashboard_empdetails_refresh"
          image-name="dashboard/sad-face-avatar"
          card-type="error"
          @refresh-triggered="refreshEmployeeDetails"
        />
      </v-card-text>

      <!-- Employee Profile Card -->
      <v-card-text
        v-if="!isEmployeeProfileLoading && !isEmployeeDataError"
        class="pa-0 d-flex"
        :class="{ 'mobile-card-text': isMobileView }"
      >
        <perfect-scrollbar
          class="w-100 overflow-y-auto overflow-x-hidden employee-profile-scrollbar"
          :class="{ 'mobile-scroll-area': isMobileView }"
        >
          <div
            class="rounded-xl pa-4 d-flex flex-column justify-center flex-grow-1"
            :class="{ 'mobile-content-area': isMobileView }"
            style="min-height: 100%"
          >
            <v-list class="pt-0">
              <v-list-item
                class="pa-0 mb-2"
                :class="{
                  'd-flex flex-column align-center justify-center text-center':
                    isMobileView,
                }"
              >
                <template #prepend>
                  <v-avatar
                    size="70"
                    :color="getRandomColors"
                    class="justify-center"
                  >
                    <img
                      v-if="employeeDetails.photoPath"
                      :src="employeeDetails.photoPath"
                      alt="Profile_Pic"
                      width="100%"
                      height="100%"
                    />
                    <span
                      v-else
                      style="
                        font-size: 2em;
                        color: rgb(var(--v-theme-primary-lighten-5));
                      "
                    >
                      {{ getLetterAvatar(employeeDetails.employeeName) }}
                    </span>
                  </v-avatar>
                </template>
                <v-list-item-title
                  class="text-primary text-h6 font-weight-bold"
                  style="line-height: 1.7 !important"
                >
                  {{ checkNullValue(employeeDetails.employeeName) }}
                </v-list-item-title>
                <v-list-item-subtitle
                  v-if="employeeDetails.userDefinedEmployeeId"
                  class="text-caption blue-grey-lighten-4 my-1"
                >
                  {{ employeeDetails.userDefinedEmployeeId }}
                </v-list-item-subtitle>
                <v-list-item-subtitle
                  v-if="employeeDetails.designation"
                  class="text-caption blue-grey-lighten-4 my-1"
                >
                  {{ employeeDetails.designation }}
                </v-list-item-subtitle>
                <v-list-item-subtitle
                  v-if="employeeDetails.department"
                  class="text-caption blue-grey-lighten-4 my-1"
                >
                  {{ employeeDetails.department }}
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>

            <!-- Business Hours - Desktop Layout -->
            <div
              v-if="employeeDetails.shiftType && !isMobileView"
              class="d-flex justify-space-between align-center text-caption blue-grey-lighten-4 mt-3 mb-2"
            >
              <span>{{ $t("dashboard.businessHours") }}</span>
              <div class="d-flex align-center">
                <v-icon size="15" color="blue-grey-lighten-4" class="mr-1">
                  fas fa-clock
                </v-icon>
                <span
                  >{{ employeeDetails.startTime }} -
                  {{ employeeDetails.endTime }}</span
                >
              </div>
            </div>

            <!-- Business Hours - Mobile Layout -->
            <div
              v-if="employeeDetails.shiftType && isMobileView"
              class="d-flex flex-column text-center text-caption blue-grey-lighten-4 mt-2 mb-1"
            >
              <span class="mb-1">{{ $t("dashboard.businessHours") }}</span>
              <div class="d-flex align-center justify-center">
                <v-icon size="15" color="blue-grey-lighten-4" class="mr-1">
                  fas fa-clock
                </v-icon>
                <span
                  >{{ employeeDetails.startTime }} -
                  {{ employeeDetails.endTime }}</span
                >
              </div>
            </div>

            <!-- Current Date and Time - Desktop Layout -->
            <div
              v-if="!isMobileView"
              class="d-flex justify-space-between align-center text-caption blue-grey-lighten-4 mt-3 mb-2"
            >
              <span>{{ currentDate }}</span>
              <div class="text-primary">
                {{ time }}
              </div>
            </div>

            <!-- Current Date and Time - Mobile Layout -->
            <div
              v-if="isMobileView"
              class="d-flex flex-column text-center text-caption blue-grey-lighten-4 mt-2 mb-1"
            >
              <span class="mb-1">{{ currentDate }}</span>
              <div class="text-primary mb-2">
                {{ time }}
              </div>
              <div class="text-primary font-weight-bold text-h5">
                <span v-if="inTimeHours === '00:00:00'">
                  {{ inTimeHours }}
                </span>
                <span v-else>
                  {{ formatSeconds(inTimeHours) }}
                </span>
              </div>
            </div>
            <div v-if="checkInAccessRights === 1" class="mt-6">
              <div
                v-if="checkAttendanceLoading"
                class="d-flex"
                style="justify-content: space-between"
              >
                <v-skeleton-loader
                  v-for="j in 2"
                  :key="j"
                  ref="skeleton"
                  type="chip"
                  class="mt-4"
                />
              </div>
              <div v-else-if="errorInCheckIn" class="text-center">
                <div class="text-primary font-weight-bold">
                  Please refresh to retrieve the current attendance status
                </div>
                <v-btn
                  variant="elevated"
                  color="primary"
                  rounded="lg"
                  size="small"
                  class="my-2"
                  @click="checkAttendance()"
                >
                  <v-icon size="15" class="pr-1"> fas fa-redo-alt </v-icon>
                  Retry
                </v-btn>
              </div>
              <div
                v-else
                class="d-flex align-center flex-wrap"
                :class="{
                  'justify-space-between': !isMobileView,
                  'justify-center my-3': isMobileView,
                }"
              >
                <v-chip
                  id="emp_dashboard_checkin_toggle"
                  pill
                  ripple
                  large
                  :color="
                    checkInButtonText === 'Check In'
                      ? 'green-lighten-4'
                      : 'red-lighten-4'
                  "
                  :text-color="
                    checkInButtonText === 'Check In' ? 'green' : 'red'
                  "
                  style="width: 135px; height: 45px"
                  :style="
                    isUpdatingAttendance ? 'cursor: block' : 'cursor: pointer'
                  "
                  class="font-weight-medium chipper"
                  @click="checkAttendanceConfiguration"
                >
                  <v-avatar
                    v-if="checkInButtonText === 'Check In'"
                    class="switch-button"
                    color="green"
                  >
                    <v-icon color="white" size="20"> fas fa-power-off </v-icon>
                  </v-avatar>
                  <span
                    :class="
                      checkInButtonText === 'Check In'
                        ? 'text-green'
                        : 'text-red'
                    "
                    class="mx-2"
                  >
                    {{ checkInButtonText }}
                  </span>
                  <v-avatar
                    v-if="checkInButtonText === 'Check Out'"
                    class="switch-button"
                    color="red"
                  >
                    <v-icon color="white" size="20"> fas fa-power-off </v-icon>
                  </v-avatar>
                </v-chip>
                <p
                  v-if="inTimeHours === '00:00:00' && !isMobileView"
                  class="font-weight-bold text-primary text-h5"
                >
                  {{ inTimeHours }}
                </p>
                <p
                  v-else-if="!isMobileView"
                  class="font-weight-bold text-primary text-h5"
                >
                  {{ formatSeconds(inTimeHours) }}
                </p>
              </div>
            </div>
            <div
              v-if="employeeAttendanceFormAccess"
              class="pa-2 d-flex justify-center"
            >
              <v-menu :location="isMobileView ? 'top' : 'bottom'">
                <template #activator="{ props }">
                  <span
                    class="text-primary text-decoration-underline"
                    style="cursor: pointer"
                    v-bind="props"
                    @click="getEmployeeCheckinCheckout()"
                  >
                    {{ $t("dashboard.viewAttendanceEntries") }}
                  </span>
                </template>
                <v-card
                  style="max-width: 250px"
                  class="pa-2 text-center"
                  elevation="4"
                >
                  <span class="font-weight-bold">{{
                    $t("dashboard.attendanceEntries")
                  }}</span>
                  <v-divider />
                  <div v-if="attendanceEntriesLoading">
                    <v-skeleton-loader type="text@6" class="mt-3" />
                  </div>
                  <v-list v-else-if="checkInCheckOut?.length" class="py-0">
                    <v-list-item
                      v-for="(item, index) in checkInCheckOut"
                      :key="index"
                      class="px-0"
                    >
                      <div
                        class="d-flex justify-space-between align-center w-100 py-2"
                      >
                        <div class="d-flex align-center">
                          <v-icon
                            color="success"
                            class="fas fa-arrow-up mr-2"
                            size="15"
                            style="transform: rotate(45deg)"
                          />
                          <span class="text-body-2">{{
                            getHourAndMinutes(item.CIn)
                          }}</span>
                        </div>
                        <div class="d-flex align-center">
                          <v-icon
                            v-if="item.COut"
                            color="red"
                            class="fas fa-arrow-down mr-2"
                            size="15"
                            style="transform: rotate(45deg)"
                          />
                          <span v-if="item.COut" class="text-body-2">
                            {{ getHourAndMinutes(item.COut) }}
                          </span>
                        </div>
                      </div>
                    </v-list-item>
                  </v-list>
                  <span v-else class="text-caption">No entries found</span>
                </v-card>
              </v-menu>
            </div>
          </div>
        </perfect-scrollbar>
      </v-card-text>
    </v-card>

    <!-- Attendance Configuration Modals -->
    <AttendanceAlertModal
      v-if="displayGeoFencing"
      class="d-flex justify-center align-center"
      :open-notify-modal="displayGeoFencing"
      @close-notify-modal="closeGeoFencingModal"
    >
      <template #topContent>
        <div class="d-flex justify-center mt-3">
          <v-sheet
            color="white"
            elevation="2"
            height="50"
            outlined
            rounded
            shaped
            width="400"
            class="d-flex justify-center align-center"
          >
            <v-icon
              class="pr-2"
              color="primary"
              style="font-weight: 200; font-size: 2em"
            >
              fas fa-clock
            </v-icon>
            {{ new Date().toString().slice(3, 25) }}
          </v-sheet>
        </div>
        <!-- For Futher Use
        <div class="mt-3 d-flex justify-center align-center">
          <v-avatar size="20vh" :color="getRandomColors" class="elevation-8">
            <img
              v-if="employeeDetails.photoPath"
              :src="employeeDetails.photoPath"
              alt="employeeimage"
            />
            <span v-else class="text-white text-h1">{{
              getLetterAvatar(employeeDetails.employeeName)
            }}</span>
          </v-avatar>
        </div> -->
        <GeoFencingMap
          class="mb-3"
          :center-point="geoFencingCenterPoint"
          :fence-radius="geoFencingRadius"
          :logged-position="userPosition"
          :button-text="checkInButtonText"
          @check-geo-fencing="checkGeoFencingLocation"
        />
      </template>
    </AttendanceAlertModal>

    <!-- Facial Recognition Modal -->
    <AttendanceAlertModal
      v-if="displayFacialRecognition"
      class="d-flex justify-center align-center"
      :open-notify-modal="displayFacialRecognition"
      @close-notify-modal="closeFacialModal"
    >
      <template #topContent>
        <div class="d-flex justify-center mt-3">
          <v-sheet
            color="white"
            elevation="2"
            height="50"
            outlined
            rounded
            shaped
            width="400"
            class="d-flex justify-center align-center"
          >
            <v-icon
              class="pr-2"
              color="#0CA49E"
              style="font-weight: 200; font-size: 2em"
            >
              fas fa-clock
            </v-icon>
            {{ new Date().toString().slice(3, 25) }}
          </v-sheet>
        </div>
        <camera
          ref="cameraComponent"
          :display-camera="displayCamera"
          :bottom-button-text="cameraBottomButtonText"
          :challenges="challenges"
          @send-base-64="getbase64"
        />
      </template>
    </AttendanceAlertModal>

    <!-- Workplace Selection Modal -->
    <ChooseEmployeeWorkPlace
      v-if="showSelectWorkplace"
      :open-modal="showSelectWorkplace"
      :work-place-data="workPlaceData.workPlaceDetails"
      :wfh-pre-approval-error-message="wfhPreApprovalErrorMessage"
      :failed-pre-approvals="failedPreApprovals"
      @close-modal="showSelectWorkplace = false"
      @on-place-select="selectedWorkplace($event)"
      @update-auto-attendance="updateAutoAttendance($event)"
    />

    <!-- Geo not accuracy popup show except in mobile app  -->
    <NotifyAlertModal
      v-if="openGeoNotAccurate"
      id="dashboard_no_accuracy_proceed_button"
      :open-notify-modal="openGeoNotAccurate"
      :modal-title="$t('dashboard.geoNotAccurate')"
      :modal-content="geoNotAccuracyContent"
      image-name="dashboard/gps-not-accurate"
      :button-text="$t('dashboard.proceedNow')"
      @close-notify-modal="openGeoNotAccurate = false"
      @button-action="getGeoCoordinate()"
    />

    <!-- GPS not enabled- refresh Popup  -->
    <NotifyAlertModal
      v-if="openGeoRefresh"
      id="dashboard_gps_refresh_button"
      :open-notify-modal="openGeoRefresh"
      :modal-title="$t('dashboard.gpsNotEnabled')"
      :modal-content="
        isMobileApp ? $t('dashboard.mobViewGPS') : $t('dashboard.deskViewGPS')
      "
      image-name="dashboard/location-not-found"
      :footer-notes="true"
      :button-text="
        isMobileApp ? $t('dashboard.proceed') : $t('dashboard.refresh')
      "
      @close-notify-modal="openGeoRefresh = false"
      @button-action="handleGeoRefresh()"
    >
      <template #footerContent>
        <p>
          If you face problem in enabling them, please follow the
          troubleshooting instructions here for
          <a
            class="link-text-highlight"
            rel="noopener noreferrer"
            target="_blank"
            href="https://support.google.com/accounts/answer/3467281?hl=en"
            >Android</a
          >
          or
          <a
            class="link-text-highlight"
            rel="noopener noreferrer"
            target="_blank"
            href="https://support.apple.com/en-il/HT207092"
            >IOS</a
          >
        </p>
      </template>
    </NotifyAlertModal>

    <!-- Loading Overlay -->
    <AppLoading v-if="isUpdatingAttendance || facialRecognitionLoading" />

    <v-dialog
      v-model="displayWrongDialog"
      persistent
      max-width="500"
      class="text-center"
    >
      <v-card>
        <v-card-title class="bg-grey-lighten-5 text-body-2 justify-center">
          {{ checkInButtonText }} Failed - {{ facialRecognitionErrorMsg }}
        </v-card-title>

        <div class="d-flex justify-center align-center my-2">
          <v-avatar size="100" :image="takenPhotoBase64" />
        </div>

        <v-card-title class="justify-center">
          {{ employeeDetails.employeeName }}
        </v-card-title>

        <div class="mb-5 d-flex justify-center align-center">
          <v-icon
            class="pr-2"
            color="#0CA49E"
            style="font-weight: 200; font-size: 2em"
          >
            fas fa-clock
          </v-icon>
          {{ new Date().toString().slice(3, 25) }}
        </div>

        <div
          v-if="isGeoFencingEnabled || isGeoEnforced"
          class="mb-5 d-flex justify-center align-center"
        >
          <v-icon
            class="pr-2"
            color="#0CA49E"
            style="font-weight: 200; font-size: 2em"
          >
            fas fa-map-marker-alt
          </v-icon>
          {{ addressOfLoggedIn }}
        </div>

        <v-card-actions>
          <v-spacer />
          <v-btn
            variant="elevated"
            rounded="lg"
            block
            color="red"
            @click="retryFacial"
          >
            Take another Photo
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import moment from "moment";
import { defineAsyncComponent } from "vue";
// Components
const ChooseEmployeeWorkPlace = defineAsyncComponent(() =>
  import("./ChooseEmployeeWorkPlace.vue")
);
import NotifyAlertModal from "@/components/custom-components/NotifyAlertModal.vue";
import AttendanceAlertModal from "@/components/custom-components/AttendanceAlertModal.vue";
import Camera from "@/components/custom-components/CameraObject.vue";
import GeoFencingMap from "@/components/custom-components/GeoFencingMap.vue";
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes.vue";
import {
  CHECK_ATTENDANCE_CONFIGURATION,
  CHECK_GEO_WORKPLACE_ENFORCE,
  GET_EMPLOYEE_DETAILS,
  FACIAL_RECOGNITION,
  CHECK_REGISTER_USER,
  VALIDATE_WFH_PREAPPORVAL_REQUEST,
  LIST_WORK_PLACES,
  GET_CHECKIN_CHECKOUT,
} from "@/graphql/dashboard/dashboardQueries";
import { isMobile } from "mobile-device-detect";
import {
  getErrorCodesWithValidation,
  getErrorCodesAndMessagesWithValidation,
  checkNullValue,
  handleNetworkErrors,
} from "@/helper";
export default {
  name: "EmployeeProfile",

  components: {
    NotifyAlertModal,
    ChooseEmployeeWorkPlace,
    NoDataCardWithQuotes,
    Camera,
    AttendanceAlertModal,
    GeoFencingMap,
  },

  data() {
    return {
      randomColors: [
        // these colors are base colors in vuetify, used to make random colors
        "red",
        "pink",
        "blue-grey",
        "purple",
        "orange",
        "deep-purple",
        "lime",
        "indigo",
        "blue",
        "cyan",
        "teal",
        "light-blue",
        "green",
      ],
      addressOfLoggedIn: "",
      userPosition: [],
      takenPhotoBase64: null,
      cameraBottomButtonText: "",
      displayWrongDialog: false,
      isFacialRecognitionEnabled: false,
      isGeoFencingEnabled: false,
      geoFencingCenterPoint: [],
      geoFencingRadius: [],
      displayCamera: false,
      capturedImage: "",
      displayFacialRecognition: false,
      displayGeoFencing: false,
      noOfChallenges: 0,
      challenges: [],
      enableLivenessDetection: false,
      attendanceConfigurationArray: [],
      //employee profile data
      isEmployeeDataError: false,
      employeeDetails: {},
      facialRecognitionErrorMsg: "",
      facialRecognitionLoading: false,

      //check-in data
      openGeoNotAccurate: false, //not accurate notify popup
      showSelectWorkplace: false, // list workplace popup
      openGeoRefresh: false, // geo location refresh popup
      checkInButtonText: "Check In",
      geoNotAccuracyContent: "",
      coordinates: {
        latitude: "",
        longitude: "",
      },
      workPlaceId: "",
      attendanceDuration: null,
      attendancePeriod: "",
      isAutoAttendance: false,
      errorInCheckIn: false,
      checkAttendanceLoading: false,
      isUpdatingAttendance: false,
      wfhPreApprovalErrorMessage: "",
      failedPreApprovals: [],

      //worked data
      employeeWorkedDays: "",
      lateAttendanceData: "",
      isErrorInLateAttendance: false,

      // time data
      time: moment(),
      inTimeHours: "00:00:00",
      timerStart: false,

      checkInCheckOut: [],
      attendanceEntriesLoading: false,

      // loading
      isEmployeeProfileLoading: false,
    };
  },

  computed: {
    getRandomColors() {
      let finalColor =
        this.randomColors[Math.floor(Math.random() * this.randomColors.length)];
      return finalColor + "-lighten-5";
    },

    getHourAndMinutes() {
      return (timeString) => {
        if (moment(timeString).isValid())
          return moment(timeString, "YYYY-MM-DD HH:mm:ss").format("HH:mm");
      };
    },
    isMobileApp() {
      if (this.findDevice === "Mobile Application") {
        return true;
      } else {
        return false;
      }
    },

    baseUrl() {
      // return "https://fieldforce.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    //get watch Position id
    getWatchPositionId() {
      return this.$store.state.dashboard.watchPositionId;
    },
    //display formatted current date
    currentDate() {
      const today = moment();
      return today.format("dddd - Do MMMM YYYY");
    },
    isGeoEnforced() {
      return this.$store.state.dashboard.isGeoEnforced;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    // find the user device to show the geo not accurate popup
    findDevice() {
      //isMobile covers both mobile and tablet device
      if (isMobile) {
        if (window.$cookies.get("isNativeMobileApp") == 1) {
          return "Mobile Application"; // if device is mobile and not used from browser consider it as mobile app
        } else {
          return "Mobile Browser";
        }
      } else {
        return "Web"; // if device is not mobile then it is Web app
      }
    },
    workPlaceData() {
      return this.$store.state.dashboard.workPlaceData;
    },
    checkInAccessRights() {
      let dashboardAttendance = this.$store.getters.formAccessRights(
        "dashboard-attendance"
      );
      if (dashboardAttendance) {
        let checkInRights = dashboardAttendance.accessRights;
        return checkInRights.optionalChoice;
      } else {
        return 0;
      }
    },
    employeeAttendanceFormAccess() {
      let formAccess = this.$store.state.formIdAccessRights[29];
      let newAttendanceAccess = this.$store.state.formIdAccessRights[305];
      if (
        (formAccess &&
          formAccess.accessRights &&
          formAccess.accessRights["view"]) ||
        (newAttendanceAccess &&
          newAttendanceAccess.accessRights &&
          newAttendanceAccess.accessRights["view"])
      ) {
        return true;
      }
      return false;
    },
    imageHeight() {
      if (this.windowWidth > 1570) {
        return "height: 100%;";
      } else if (this.windowWidth > 1260) {
        return "";
      } else if (this.windowWidth > 800) {
        return "height: 100%; ";
      } else {
        return "";
      }
    },
    needToUpdateAttendance() {
      return this.$store.state.dashboard.needToUpdateAttendance;
    },
    refreshNoAttendance() {
      return this.$store.state.dashboard.isCurrentRecordAvailable;
    },
  },
  watch: {
    //address of coordinates
    async coordinates(val) {
      if (
        (this.isGeoFencingEnabled && val != undefined) ||
        this.isGeoEnforced
      ) {
        this.addressOfLoggedIn = await this.$store.dispatch(
          "getAddressFromLatLng",
          {
            lat: val.latitude,
            lng: val.longitude,
            addressType: "city",
          }
        );
      }
    },

    //current time runner
    time(val) {
      this.time = val;
      //run check in timer based on current time changes
      if (this.timerStart) {
        this.inTimeHours = this.inTimeHours + 1;
      }
    },
    //watch seconds changes and update the timer
    inTimeHours(val) {
      this.inTimeHours = val;
    },

    //if user do check in attendance-regularization form we need to update attendance
    needToUpdateAttendance(val) {
      if (val) {
        this.checkAttendance();
      }
    },
  },
  mounted() {
    // Load employee profile details
    this.getEmployeeDetails();
    // Load geo enforcement and workplace settings
    this.checkGeoEnforceAndWorkPlace();
    //for showing the current time in the card
    setInterval(() => (this.time = moment().format("HH:mm:ss")));
    //check user already punched in or not
    this.checkAttendance();
    //Unless otherwise user refresh it won't update so on 5 minutes interval call check user's attendance
    setTimeout(() => this.checkAttendance(), 300000);
  },

  methods: {
    checkNullValue,
    // Get letter avatar from name - returns first letters of first couple of words
    getLetterAvatar(name) {
      if (!name) return "?";

      // Split name into words and filter out empty strings
      const words = name
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 0);

      if (words.length === 0) return "?";
      if (words.length === 1) return words[0].charAt(0).toUpperCase();

      // Return first letters of first two words
      return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
    },
    // Format seconds to time display
    formatSeconds(seconds) {
      if (!seconds && seconds !== 0) return "00:00:00";

      const hrs = String(Math.floor(seconds / 3600)).padStart(2, "0");
      const mins = String(Math.floor((seconds % 3600) / 60)).padStart(2, "0");
      const secs = String(seconds % 60).padStart(2, "0");

      return `${hrs}:${mins}:${secs}`;
    },
    // Refresh employee details
    refreshEmployeeDetails() {
      this.isEmployeeDataError = false;
      this.getEmployeeDetails();
    },
    // Get employee profile details
    getEmployeeDetails() {
      try {
        this.isEmployeeProfileLoading = true;
        this.isEmployeeDataError = false;

        this.$apollo
          .query({
            query: GET_EMPLOYEE_DETAILS,
            client: "apolloClientC",
          })
          .then(({ data }) => {
            if (data && Object.keys(data).length !== 0) {
              let responseData = data.getEmployeeDetails;
              if (responseData.employeeDetails) {
                this.employeeDetails = JSON.parse(responseData.employeeDetails);
                // Store login employee_id in localStorage only if it not empty and no employeeid exist in localstorage
                let employeeId = localStorage.getItem("LoginEmpId");
                if (this.employeeDetails.employeeId && !employeeId) {
                  localStorage.setItem(
                    "username",
                    this.employeeDetails.employeeName
                  );
                  localStorage.setItem(
                    "LoginEmpId",
                    this.employeeDetails.employeeId
                  );
                }
              } else {
                this.isEmployeeDataError = true;
              }
            } else {
              this.isEmployeeDataError = true;
            }
            this.isEmployeeProfileLoading = false;
          })
          .catch(() => {
            this.isEmployeeDataError = true;
            this.isEmployeeProfileLoading = false;
          });
      } catch {
        this.isEmployeeDataError = true;
        this.isEmployeeProfileLoading = false;
      }
    },
    // Check geo enforcement and workplace settings
    checkGeoEnforceAndWorkPlace() {
      try {
        this.$apollo
          .query({
            query: CHECK_GEO_WORKPLACE_ENFORCE,
            client: "apolloClientC",
          })
          .then(({ data }) => {
            if (data && Object.keys(data).length !== 0) {
              let responseData = data.checkGeoEnforceAndWorkPlace;
              if (responseData) {
                this.getEmployeeWorkPlace(
                  responseData.isWorkPlaceEnable,
                  responseData.isGeoEnable
                );
              }
            } else {
              this.updateGeoEnforce(false, {});
            }
          })
          .catch(() => {
            this.updateGeoEnforce(false, {});
          });
      } catch {
        this.updateGeoEnforce(false, {});
      }
    },
    // Update geo enforce settings
    updateGeoEnforce(geoEnabled, workplace) {
      this.$store.commit("dashboard/UPDATE_GEO_ENFORCE_LABEL", geoEnabled);
      this.$store.commit("dashboard/UPDATE_WORKPLACE_ENABLE", workplace);
    },
    getEmployeeWorkPlace(workPlaceEnable, geoEnabled) {
      let vm = this;
      let workplace = {
        isWorkPlaceEnabled: workPlaceEnable,
        workPlaceDetails: [],
      };
      vm.$apollo
        .query({
          query: LIST_WORK_PLACES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          if (
            data &&
            data.listWorkPlaces &&
            data.listWorkPlaces.employeeWorkPlace
          ) {
            let employeeWorkPlace = JSON.parse(
              data.listWorkPlaces.employeeWorkPlace
            );
            employeeWorkPlace = employeeWorkPlace.map((item) => {
              return {
                autoAttendance: item.Auto_Attendance,
                imageUrl: item.Image_Url,
                workPlace: item.Work_Place,
                workPlaceId: item.Work_Place_Id,
                preApproval: item.Pre_Approval,
              };
            });
            workplace.workPlaceDetails = employeeWorkPlace;
          }
          vm.updateGeoEnforce(geoEnabled, workplace);
        })
        .catch(() => {
          vm.updateGeoEnforce(geoEnabled, workplace);
        });
    },
    getEmployeeCheckinCheckout() {
      let vm = this;
      vm.attendanceEntriesLoading = true;
      let formAccess = this.$store.state.formIdAccessRights[29];
      vm.$apollo
        .query({
          query: GET_CHECKIN_CHECKOUT,
          client: "apolloClientAC",
          variables: {
            formId:
              formAccess &&
              formAccess.accessRights &&
              formAccess.accessRights.view
                ? 29
                : 305,
          },
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          if (
            data &&
            data.listAttendanceDashBoard &&
            data.listAttendanceDashBoard.attendanceDashBoardDetails &&
            data.listAttendanceDashBoard.attendanceDashBoardDetails.length
          ) {
            vm.checkInCheckOut =
              data.listAttendanceDashBoard.attendanceDashBoardDetails;
          } else {
            vm.checkInCheckOut = [];
          }
          vm.attendanceEntriesLoading = false;
        })
        .catch((error) => {
          vm.checkInCheckOut = [];
          vm.attendanceEntriesLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: error,
            action: "retrieving",
            form: "attendance details",
            isListError: false,
          });
        });
    },
    //get attendance configuration
    checkAttendanceConfiguration() {
      this.isUpdatingAttendance = true;
      this.wfhPreApprovalErrorMessage = "";

      try {
        let vm = this;
        vm.$apollo
          .query({
            query: CHECK_ATTENDANCE_CONFIGURATION,
            client: "apolloClientL",
            fetchPolicy: "no-cache",
          })
          .then(({ data }) => {
            let attendanceConfigurationArray =
              data.checkAttendanceConfiguration.attendanceConfigurationDetails;
            this.attendanceConfigurationArray = attendanceConfigurationArray;
            attendanceConfigurationArray.forEach((element) => {
              if (element.geoFencingEnabled === "Yes") {
                this.isGeoFencingEnabled = true;
                if (this.geoFencingCenterPoint) {
                  this.geoFencingCenterPoint.push(
                    element.centerPoint.split(",").map(Number)
                  );
                  this.geoFencingRadius.push(element.radius);
                } else {
                  const snackbarData = {
                    isOpen: true,
                    message: this.$t(
                      "dashboard.invalidGeoFencingConfiguration"
                    ),
                    type: "warning",
                  };
                  this.showAlert(snackbarData);
                }
              }
            });
            this.isUpdatingAttendance = false;
            // if (this.isFacialRecognitionEnabled) {
            //   this.checkIfFaceRegistered();
            // } else {
            this.checkGeoEnforce();
            // }
          })
          .catch(() => {
            this.isUpdatingAttendance = false;
            this.checkAttendanceConfigurationErrHandling();
            this.isFacialRecognitionEnabled = false;
            this.isGeoFencingEnabled = false;
          });
      } catch {
        this.isUpdatingAttendance = false;
        this.checkAttendanceConfigurationErrHandling();
      }
    },
    retryFacial() {
      this.displayWrongDialog = false;
      this.displayFacialRecognition = false;
      this.displayFacialRecognition = true;
      this.displayCamera = false;
      this.displayCamera = true;
      this.$refs.cameraComponent.takenPhotobase64.imageWithChallenges = [];
      this.$refs.cameraComponent.challengesCount = 0;
      if (this.$refs.cameraComponent.challengeName) {
        this.$refs.cameraComponent.challengeName =
          this.$refs.cameraComponent.faceChallenges[
            this.$refs.cameraComponent.challengesCount
          ]?.challengeName;
      }
      this.$refs.cameraComponent.toggleCamera();
      this.$refs.cameraComponent.toggleCamera();
    },
    closeFacialModal() {
      // Stop camera stream if it's running
      if (this.displayCamera) {
        this.displayCamera = false;
        if (this.$refs.cameraComponent?._data?.isCameraOpen) {
          this.$refs.cameraComponent?.stopCameraStream();
        }
      }

      // Close all facial recognition related modals
      this.displayFacialRecognition = false;
      this.displayWrongDialog = false;

      // Reset facial recognition state
      this.facialRecognitionErrorMsg = "";
    },
    //reload page to refresh the geo-location when not able to get geo-coordinates
    reloadPage() {
      this.openGeoRefresh = false;
      window.location.reload();
    },
    // Check geo enforcement
    checkGeoEnforce() {
      window?.postMessage("checkin", "*");
      //check whether geo enforced for that employee.
      //Incase of yes only will get the coordinates
      let deviceType = "";
      if (this.isGeoEnforced || this.isGeoFencingEnabled) {
        //If watchPosition enabled already clear it before getting geo-coordinates
        if (this.getWatchPositionId) {
          this.$store.dispatch(
            "dashboard/clearGeoWatchPosition",
            this.getWatchPositionId
          );
        }

        /* the geo coordinates is enforced then the user should use the mobile app for more accuracy,
              otherwise we need to get confirmation from the user that the location will not be accurate and
              the user wants to proceed check-in/checkout */
        if (this.findDevice === "Mobile Application") {
          //For Mobile Application we don't show geo not accurate popup get coordinates
          this.getGeoCoordinate();
        } else {
          //for browsers
          deviceType = isMobile ? "mobile" : "web";
          this.geoNotAccuracyContent =
            "The geo-coordinates might not be accurate in " +
            deviceType +
            " browsers as the data might be misrepresented due to the Wifi network, " +
            "proxy servers, or VPN. If you still want to proceed, please progress to the next step. " +
            " It is advised that you use a mobile app for capturing accurate geo coordinates.";
          this.openGeoNotAccurate = true;
        }
      } else {
        this.coordinates = {
          latitude: "",
          longitude: "",
        };
        this.validateWfhPreApprovalRequest();
      }
    },
    //function to get the geo coordinates
    getGeoCoordinate(fenceCheck) {
      let vm = this;
      vm.$store
        .dispatch("dashboard/fetchUserCoordinates")
        .then((response) => {
          let geoPosition = response[0]; // response[0] returns geo coordinates value
          let watchPositionId = response[1]; // response[1] returns watch position id
          vm.openGeoNotAccurate = false;
          vm.coordinates = {
            latitude: geoPosition.latitude,
            longitude: geoPosition.longitude,
          };
          vm.userPosition = Object.values(vm.coordinates);
          vm.openGeoRefresh = false;
          if (!fenceCheck) {
            vm.validateWfhPreApprovalRequest();
          }
          vm.$store.dispatch(
            "dashboard/clearGeoWatchPosition",
            watchPositionId
          );
          vm.$store.dispatch("dashboard/initializeGeolocationWatch");
        })
        .catch((error) => {
          //condition checked for handling geo-error
          // for geo location not supported handled in common function
          if (error[0] === "geo_error") {
            //  error[0] returns geo_error
            let watchPositionId = error[1]; // error[1] returns watch position id
            vm.openGeoNotAccurate = false;
            vm.openGeoRefresh = true;
            vm.isUpdatingAttendance = false;
            vm.$store.dispatch(
              "dashboard/clearGeoWatchPosition",
              watchPositionId
            );
            vm.$store.dispatch("dashboard/initializeGeolocationWatch");
          }
        });
    },
    //check whether work place is enabled if enable will show popup otherwise update the attendance
    checkWorkPlaceEnabled() {
      let workplaceEnabled = this.workPlaceData?.isWorkPlaceEnabled;
      if (workplaceEnabled) {
        this.isUpdatingAttendance = false;
        this.showSelectWorkplace = true;
      } else {
        this.checkGeoFenceEnabled();
      }
    },
    // Check geo fence enabled
    checkGeoFenceEnabled() {
      if (this.isGeoFencingEnabled) {
        if (this.isMobileApp) {
          this.getGeoCoordinate("fence");
        }
        this.isUpdatingAttendance = false;
        this.displayGeoFencing = true;
      } else {
        this.checkFacialEnabled();
      }
    },
    checkFacialEnabled() {
      if (this.isFacialRecognitionEnabled) {
        this.closeGeoFencingModal();
        this.checkIfFaceRegistered();
      } else {
        //Checking if it facial only enabled
        let facial = this.attendanceConfigurationArray.find((el) => {
          if (el.facialRecognitionEnabled === "Yes") {
            return el;
          }
        });
        if (facial) {
          this.noOfChallenges = facial.noOfChallenges;
          this.enableLivenessDetection = facial.enableLivenessDetection;
          this.isFacialRecognitionEnabled = true;
          this.checkFacialEnabled();
        } else {
          this.closeFacialModal();
          this.updateAttendance();
        }
      }
    },
    // Check if face is registered for facial recognition
    checkIfFaceRegistered() {
      this.isUpdatingAttendance = true;
      //checking if face has been registered for the employee
      if (this.isFacialRecognitionEnabled) {
        let vm = this;
        try {
          vm.$apollo
            .query({
              query: CHECK_REGISTER_USER,
              client: "apolloClientT",
              variables: {
                noOfChallenges: this.noOfChallenges,
              },
              fetchPolicy: "no-cache",
            })
            .then(({ data }) => {
              this.isUpdatingAttendance = false;
              if (
                data?.checkRegisterUserAndGetRandomChallenge?.message ===
                "User Exist."
              ) {
                if (
                  data?.checkRegisterUserAndGetRandomChallenge?.challenges
                    ?.length > 0
                ) {
                  this.challenges =
                    data?.checkRegisterUserAndGetRandomChallenge?.challenges;
                }
                this.displayFacialRecognition = true;
                this.cameraBottomButtonText =
                  "Verify and " + this.checkInButtonText;
                this.displayCamera = true;
              }
            })
            .catch(() => {
              this.isUpdatingAttendance = false;
              const snackbarData = {
                isOpen: true,
                message: this.$t("dashboard.faceNotRegistered"),
                type: "warning",
              };
              this.showAlert(snackbarData);
            });
        } catch {
          this.isUpdatingAttendance = false;
          const snackbarData = {
            isOpen: true,
            message: this.$t("dashboard.somethingWentWrong"),
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      }
    },
    // Update attendance
    async updateAttendance() {
      this.isUpdatingAttendance = true;

      try {
        const apiObj = {
          url:
            this.baseUrl +
            "employees/attendance/update-attendance/istheme/DashBoard",
          type: "POST",
          dataType: "json",
          data: {
            latitude: this.coordinates.latitude,
            longitude: this.coordinates.longitude,
            workPlaceId: this.workPlaceId,
            Checkin_Data_Source: this.findDevice,
            Checkout_Data_Source: this.findDevice,
            requestResource: "HRAPPUI",
            attendanceDuration: this.attendanceDuration,
            attendancePeriod: this.attendancePeriod,
          },
        };

        const attendanceData = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );

        if (attendanceData && attendanceData.success) {
          if (this.checkInButtonText === "Check In" && !this.isAutoAttendance) {
            this.checkInButtonText = "Check Out";
            this.resetTimer();
            this.inTimeHours = 1;
            this.startTimer();
            if (this.refreshNoAttendance) {
              //refresh no attendance only when have current date missed
              //check in attendance record in attendance-regularization
              this.$store.dispatch("dashboard/fetchNoAttendanceRecord");
            }
          } else {
            this.checkInButtonText = "Check In";
            this.resetTimer();
          }
          const snackbarData = {
            isOpen: true,
            message: attendanceData.msg,
            type: "success",
          };
          this.showAlert(snackbarData);
        } else {
          if (attendanceData.status == 200) {
            //we get this status 200 in case of session expired so redirect user to auth
            this.userLogout();
          } else if (attendanceData.msg === "Session Expired")
            this.userLogout();
          else {
            /* To handle internal server error */
            let snackbarData = {
              isOpen: true,
              message:
                attendanceData.msg ||
                this.$t("dashboard.attendanceAddedSuccessfully"),
              type: attendanceData?.type || "warning",
            };
            this.showAlert(snackbarData);
          }
        }
      } catch {
        const snackbarData = {
          isOpen: true,
          message: this.$t("dashboard.somethingWentWrong"),
          type: "warning",
        };
        this.showAlert(snackbarData);
      } finally {
        this.isUpdatingAttendance = false;
      }
    },
    // Check attendance status
    async checkAttendance() {
      if (this.checkInAccessRights) {
        this.checkAttendanceLoading = true;
        this.errorInCheckIn = false;

        try {
          const apiObj = {
            url:
              this.baseUrl +
              "employees/attendance/get-employee-details/isAction/Check_Attendance",
            type: "POST",
            dataType: "json",
            data: {
              requestResource: "HRAPPUI",
            },
          };

          const attendanceExistData = await this.$store.dispatch(
            "triggerControllerFunction",
            apiObj
          );

          if (attendanceExistData) {
            if (
              attendanceExistData.success === undefined ||
              attendanceExistData.success
            ) {
              if (attendanceExistData.PunchOut_Date) {
                this.checkInButtonText = "Check In";
                this.resetTimer();
              } else {
                let seconds = attendanceExistData.TimeSec || 0;
                this.inTimeHours = seconds;
                this.startTimer();
                this.checkInButtonText = "Check Out";
              }
            } else {
              this.userLogout();
            }
          } else {
            this.checkInButtonText = "Check In";
            this.resetTimer();
          }
        } catch {
          this.errorInCheckIn = true;
        } finally {
          this.checkAttendanceLoading = false;
        }
      }
    },
    // Timer functions
    resetTimer() {
      this.inTimeHours = "00:00:00";
      this.timerStart = false;
    },
    startTimer() {
      this.timerStart = true;
    },
    // Modal handlers
    closeGeoFencingModal() {
      this.displayGeoFencing = false;
    },
    //function to pass selected workplace id
    selectedWorkplace(workplaceId) {
      this.workPlaceId = workplaceId;
      this.attendanceDuration = null;
      this.attendancePeriod = "";
      this.isAutoAttendance = false;
      this.showSelectWorkplace = false;
      this.checkGeoFenceEnabled();
    },
    getErrorCodesAndMessagesWithValidation,
    //Validate the employee pre approval request is approved for the work from home(work place) for the attendance date
    validateWfhPreApprovalRequest() {
      //Get the employee work place
      this.isUpdatingAttendance = true;
      this.checkGeoEnforceAndWorkPlace();
      this.failedPreApprovals = [];

      try {
        let vm = this;
        vm.$apollo
          .query({
            query: VALIDATE_WFH_PREAPPORVAL_REQUEST,
            variables: {
              employeeId: vm.loginEmployeeId,
              source: "dashboard",
              punchInDateTime: "",
              punchOutDateTime: "",
              punchType:
                vm.checkInButtonText === "Check In" ? "punchin" : "punchout",
              checkInCheckoutWorkPlace: "",
            },
            client: "apolloClientI",
            fetchPolicy: "no-cache",
          })
          .then(() => {
            this.checkWorkPlaceEnabled();
          })
          .catch((error) => {
            if (error && error.graphQLErrors) {
              let errorCode =
                this.getErrorCodesAndMessagesWithValidation(error);
              if (errorCode) {
                if (errorCode[0] === "CHR0057") {
                  let failedPreApprovals =
                    error?.graphQLErrors?.[0]?.extensions?.failedPreApprovals ||
                    [];
                  if (failedPreApprovals && failedPreApprovals.length > 0) {
                    this.failedPreApprovals = failedPreApprovals;
                    failedPreApprovals = failedPreApprovals.map((preApproval) =>
                      preApproval.toLowerCase()
                    );
                    if (
                      failedPreApprovals?.includes("on duty") ||
                      failedPreApprovals?.includes("work from home")
                    ) {
                      this.checkWorkPlaceEnabled();
                    } else {
                      let errorMessage =
                        "The week-off or holiday request for the specified date has not been applied or granted.";
                      this.wfhPreApprovalRequestErrorHandling(errorMessage);
                    }
                  } else {
                    this.wfhPreApprovalRequestErrorHandling("");
                  }
                } else {
                  let errorMessage = "";
                  if (
                    errorCode[0] === "CHR0056" ||
                    errorCode[0] === "CHR0066" ||
                    errorCode[0] === "CHR0067"
                  ) {
                    errorMessage = errorCode[1];
                  }
                  this.wfhPreApprovalRequestErrorHandling(errorMessage);
                }
              } else {
                this.wfhPreApprovalRequestErrorHandling();
              }
            } else if (error && error.networkError) {
              this.wfhPreApprovalRequestErrorHandling(
                this.handleNetworkErrors(error)
              );
            } else {
              this.wfhPreApprovalRequestErrorHandling();
            }
          });
      } catch {
        this.wfhPreApprovalRequestErrorHandling();
      }
    },
    //Function to handle the error for work from home pre-approval request validation
    wfhPreApprovalRequestErrorHandling(message = "") {
      this.isUpdatingAttendance = false;
      let snackbarData = {
        isOpen: true,
        message: message
          ? message
          : "Something went wrong while validating the pre-approval request. Please try after some time.",
        type: "warning",
      };
      this.showAlert(snackbarData);
      this.isFacialRecognitionEnabled = false;
      this.isGeoFencingEnabled = false;
    },
    updateAutoAttendance(params) {
      this.workPlaceId = params[0];
      this.attendanceDuration = params[1];
      this.attendancePeriod = params[2];
      this.isAutoAttendance = true;
      this.showSelectWorkplace = false;
      this.checkGeoFenceEnabled();
    },
    // Geo location handlers
    checkGeoFencingLocation(value) {
      if (value.isInsideTheFence) {
        let facialConfiguration = this.attendanceConfigurationArray.filter(
          (el) => {
            if (el.centerPoint === value.insideCenterPoint) {
              return el;
            }
          }
        );
        facialConfiguration = facialConfiguration[0];
        if (facialConfiguration.facialRecognitionEnabled === "Yes") {
          this.noOfChallenges = facialConfiguration.noOfChallenges;
          this.enableLivenessDetection =
            facialConfiguration.enableLivenessDetection;
          this.isFacialRecognitionEnabled = true;
          this.checkFacialEnabled();
        } else {
          this.isFacialRecognitionEnabled = false;
          this.closeGeoFencingModal();
          this.updateAttendance();
        }
      } else {
        this.closeGeoFencingModal();
        const snackbarData = {
          isOpen: true,
          message: this.$t("dashboard.outsideGeoFence"),
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    // Facial recognition handlers
    getbase64(value) {
      this.facialRecognitionErrorMsg = "";
      this.facialRecognitionLoading = true;
      try {
        this.$apollo
          .query({
            query: FACIAL_RECOGNITION,
            variables: {
              enableLivenessDetection: value.enableLivenessDetection,
              imageWithChallenges: value.imageWithChallenges,
            },
            client: "apolloClientT",
            fetchPolicy: "no-cache",
          })
          .then(({ data }) => {
            if (data.faceVerification.message === "Face Verified.") {
              // Stop camera stream first
              this.$refs.cameraComponent?.stopCameraStream();

              // Close facial modal immediately to prevent reopening
              this.closeFacialModal();
              // Update attendance after modal is closed
              this.updateAttendance();
            } else {
              this.displayWrongDialog = true;
            }
          })
          .catch((error) => {
            this.takenPhotoBase64 = value.imageWithChallenges[0]?.sourceImage;
            this.handlefaceVerificationErrors(error);
          });
      } catch (error) {
        this.takenPhotoBase64 = value.imageWithChallenges[0]?.sourceImage;
        this.handlefaceVerificationErrors(error);
      } finally {
        this.facialRecognitionLoading = false;
      }
    },
    handlefaceVerificationErrors(err = "") {
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodesWithValidation(err);
        if (errorCode) {
          switch (errorCode[0]) {
            case "EFA0101": // face not matched
              this.facialRecognitionErrorMsg = "Face not matched";
              break;
            case "EFA0102": // face not registered
              this.facialRecognitionErrorMsg = "Face not registered";
              break;
            case "EFA0106": // no face detected
              this.facialRecognitionErrorMsg = "No face detected";
              break;
            case "EFA0113": // liveness detection failed
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed";
              break;
            case "EFA0115": // liveness detection failed - Mouth Open
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed - Mouth Open";
              break;
            case "EFA0116": // liveness detection failed - Mouth Close
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed - Mouth Close";
              break;
            case "EFA0117": // liveness detection failed - Smile
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed - Smile";
              break;
            case "EFA0118": // liveness detection failed - No Smile
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed - No Smile";
              break;
            case "EFA0109": // face verification failed
            default:
              this.facialRecognitionErrorMsg = "Face verification failed";
              break;
          }
        } else {
          this.facialRecognitionErrorMsg = "Face verification failed";
        }
      } else if (err && err.networkError) {
        this.facialRecognitionErrorMsg = this.handleNetworkErrors(err);
      } else {
        this.facialRecognitionErrorMsg = "Face verification failed";
      }
      this.$refs.cameraComponent.stopCameraStream();
      this.displayWrongDialog = true;
      const cameraBox = document.getElementsByClassName("camera-box")[0];
      if (cameraBox) {
        cameraBox.style.display = "none";
      }
    },
    // Error handlers
    checkAttendanceConfigurationErrHandling() {
      const snackbarData = {
        isOpen: true,
        message: this.$t("dashboard.attendanceConfigurationError"),
        type: "warning",
      };
      this.showAlert(snackbarData);
      this.isFacialRecognitionEnabled = false;
      this.isGeoFencingEnabled = false;
    },
    proceedWithInaccurateLocation() {
      this.openGeoNotAccurate = false;
      this.updateAttendance();
    },
    handleGeoRefresh() {
      this.openGeoRefresh = false;
      if (this.isMobileApp) {
        this.updateAttendance();
      } else {
        window.location.reload();
      }
    },
    handleNetworkErrors,
    // Utility functions
    showAlert(snackbarData) {
      this.$emit("close-attendance-modal");
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    userLogout() {
      this.$store.dispatch("clearUserLock");
    },
  },
};
</script>

<style lang="scss" scoped>
// Maintain hover behavior for list items
.v-list-item:hover {
  background-color: transparent !important;
}

// Smooth transitions for interactive elements (keeping for specific functionality)
.chipper {
  transition: 0.5s !important;
}

.chipper:after {
  transition: 0.5s !important;
}

.switch-button::after {
  transition: 0.5s;
}

// Avatar sizing for pill chips
.v-chip--pill .v-avatar {
  height: 43px !important;
  width: 43px !important;
}

// Employee profile scrollbar height - clean layout without redundant scrollbars
.employee-profile-scrollbar {
  height: 100%;
  max-height: 500px;
  overflow: hidden; // Let perfect-scrollbar handle all scrolling
  width: 100%;
}

// Mobile-specific scroll area adjustments
.mobile-scroll-area {
  width: 100% !important;
  max-width: 100% !important;
}

// Mobile card text adjustments
.mobile-card-text {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden;
}

// Mobile content area adjustments
.mobile-content-area {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 12px !important;
}

// Perfect scrollbar styling - hide scrollbar for clean appearance
:deep(.ps) {
  overflow: hidden !important;
  width: 100% !important;
}

:deep(.ps__rail-y) {
  display: none !important; // Hide vertical scrollbar rail
}

:deep(.ps__thumb-y) {
  display: none !important; // Hide vertical scrollbar thumb
}

:deep(.ps__rail-x) {
  display: none !important; // Hide horizontal scrollbar rail
}

// Card layout improvements for proper flex behavior (using Vuetify classes in template)
:deep(.v-card-text) {
  flex: 1 !important;
}

// Responsive design following original patterns
@media screen and (max-width: 600px) {
  .employee-profile-scrollbar {
    max-height: 400px;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  // Ensure mobile card takes full width without overflow
  :deep(.v-card) {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
  }

  // Mobile-specific adjustments for content area
  .mobile-content-area {
    padding: 8px !important;
    margin: 0 !important;
    width: 100% !important;
    box-sizing: border-box;
  }

  // Ensure list items don't cause overflow
  :deep(.v-list-item) {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

// Tablet adjustments
@media screen and (max-width: 960px) and (min-width: 601px) {
  .employee-profile-scrollbar {
    max-height: 450px;
    width: 100%;
  }
}
</style>
