<template>
  <div style="height: 100%" class="px-2 py-4">
    <div>
      <v-row class="holiday-and-calendar">
        <!-- Header Section -->
        <div class="pl-4 pt-4 d-flex flex-row align-center">
          <v-progress-circular
            model-value="100"
            color="red"
            :size="22"
            class="mr-2"
          />
          <span class="ml-2 text-h6 text-primary font-weight-bold">
            {{ $t("dashboard.myUpcomingHolidays") }}
          </span>
        </div>

        <!-- Holidays List -->
        <v-col cols="12">
          <!-- Holiday Loading Card -->
          <v-card v-if="isHolidayLoading" class="pa-2">
            <v-skeleton-loader
              v-for="i in 4"
              :key="i"
              ref="skeleton"
              type="list-item-avatar"
            />
          </v-card>

          <!-- Holiday Error Card -->
          <v-card
            v-else-if="isHolidayFetchError && !holidaysNoViewAccess"
            height="100%"
          >
            <NoDataCardWithQuotes
              id="dashboard_holidays_refresh"
              image-name="dashboard/holidays"
              image-size="50%"
              :is-show-image="windowWidth > 960"
              card-type="error"
              :is-wrap-content="true"
              @refresh-triggered="refetchHolidays()"
            />
          </v-card>

          <!-- No Data Card -->
          <v-card
            v-else-if="upcomingHolidays?.length === 0 || holidaysNoViewAccess"
            elevation="0"
          >
            <NoDataCardWithQuotes
              image-name="dashboard/holiday-empty-image"
              :primary-bold-text="$t('dashboard.autumnShowsUs')"
              :text-message="$t('dashboard.howBeautifulLetGo')"
              card-type="no-data"
              :is-access-denied="holidaysNoViewAccess"
              class="pa-2"
              :is-wrap-content="true"
            />
          </v-card>

          <!-- Holiday List Card -->
          <div v-else class="pa-0">
            <perfect-scrollbar
              class="w-100 overflow-y-auto overflow-x-auto holiday-list-scrollbar"
            >
              <v-list class="holiday-list" :width="'max-content'">
                <v-list-item
                  v-for="holiday in upcomingHolidays"
                  :key="holiday.Holiday_Date"
                  style="margin-bottom: 10px"
                >
                  <template #prepend>
                    <!-- Mandatory Holiday Avatar -->
                    <v-menu
                      v-if="holiday.Mandatory === 1"
                      :open-on-hover="!isMobileView"
                      location="start"
                      transition="slide-x-transition"
                      offset="5"
                      min-width="150"
                    >
                      <template #activator="{ props }">
                        <v-avatar
                          v-bind="props"
                          color="red-lighten-4"
                          size="26"
                          class="mr-3"
                        >
                          <span class="text-red-darken-3 font-weight-medium">
                            M
                          </span>
                        </v-avatar>
                      </template>
                      <v-card class="pa-2" width="160">
                        <div class="d-flex justify-space-between">
                          <span
                            class="text-red-darken-3 font-weight-medium pl-2"
                            style="font-size: 0.9em"
                          >
                            {{ $t("dashboard.mandatory") }}
                          </span>
                        </div>
                        <p
                          class="text-grey-darken-1 text-caption pa-2"
                          style="line-height: 1.2em"
                        >
                          {{ holiday.Description }}
                        </p>
                      </v-card>
                    </v-menu>

                    <!-- Personal Choice Holiday Avatar -->
                    <v-menu
                      v-else-if="holiday.Personal_Choice === 1"
                      :open-on-hover="!isMobileView"
                      location="start"
                      transition="slide-x-transition"
                      offset="5"
                      min-width="150"
                    >
                      <template #activator="{ props }">
                        <v-avatar
                          v-bind="props"
                          color="purple-lighten-4"
                          size="26"
                          class="mr-3"
                        >
                          <span class="text-purple font-weight-medium">
                            P
                          </span>
                        </v-avatar>
                      </template>
                      <v-card class="pa-2" width="160">
                        <div class="d-flex justify-space-between">
                          <span
                            class="text-purple font-weight-medium pl-2"
                            style="font-size: 0.9em"
                          >
                            {{ $t("dashboard.personalChoice") }}
                          </span>
                        </div>
                        <p
                          class="text-grey-darken-1 text-caption pa-2"
                          style="line-height: 1.2em"
                        >
                          {{ holiday.Description }}
                        </p>
                      </v-card>
                    </v-menu>

                    <!-- Regular Holiday Avatar -->
                    <v-menu
                      v-else
                      :open-on-hover="!isMobileView"
                      location="start"
                      transition="slide-x-transition"
                      offset="5"
                      min-width="150"
                    >
                      <template #activator="{ props }">
                        <v-avatar
                          v-bind="props"
                          color="cyan-lighten-4"
                          size="26"
                          class="mr-3"
                        >
                          <span class="text-cyan-darken-4 font-weight-medium">
                            H
                          </span>
                        </v-avatar>
                      </template>
                      <v-card class="pa-2" width="160">
                        <div class="d-flex justify-space-between">
                          <span
                            class="text-cyan-darken-4 font-weight-medium pl-2"
                            style="font-size: 0.9em"
                          >
                            {{ $t("dashboard.holiday") }}
                          </span>
                        </div>
                        <p
                          class="text-grey-darken-1 text-caption pa-2"
                          style="line-height: 1.2em"
                        >
                          {{ holiday.Description }}
                        </p>
                      </v-card>
                    </v-menu>
                  </template>

                  <!-- Holiday Content -->
                  <v-list-item-title
                    class="font-weight-regular holiday-content"
                  >
                    {{ formatDate(holiday.Holiday_Date) }} {{ "" }}
                    {{ holiday.Holiday_Name }}
                  </v-list-item-title>
                </v-list-item>
              </v-list>
            </perfect-scrollbar>
          </div>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script>
import moment from "moment";
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes.vue";
import { GET_ALL_HOLIDAYS } from "@/graphql/dashboard/dashboardQueries";

export default {
  name: "HolidaysAndCalendar",

  components: {
    NoDataCardWithQuotes,
  },

  data() {
    return {
      isHolidayFetchError: false,
      holidaysNoViewAccess: false,
      holidayList: [],
      isHolidayLoading: false,
    };
  },

  computed: {
    // Current window width
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // Filter holiday list for current and future dates
    upcomingHolidays() {
      return this.holidayList.filter(
        (holiday) =>
          moment(holiday.Holiday_Date) >= moment().subtract(1, "days")
      );
    },
    // Check if mobile view
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    this.fetchListHolidaysInDashboard();
  },

  methods: {
    fetchListHolidaysInDashboard() {
      let vm = this;
      vm.isHolidayFetchError = false;
      vm.isHolidayLoading = true;
      vm.$apollo
        .query({
          query: GET_ALL_HOLIDAYS,
          client: "apolloClientC",
        })
        .then(({ data }) => {
          try {
            if (Object.keys(data)?.length !== 0) {
              let responseData = data.listHolidaysInDashboard;
              if (responseData) {
                vm.holidayList = responseData.holidayDetails
                  ? JSON.parse(responseData.holidayDetails)
                  : [];
              }
            }
          } catch {
            vm.isHolidayFetchError = true;
          }
          vm.isHolidayLoading = false;
        })
        .catch((error) => {
          vm.isHolidayLoading = false;
          vm.isHolidayFetchError = true;
          vm.handleHolidayError(error);
        });
    },
    handleHolidayError(error = "") {
      if (error && error.graphQLErrors?.length > 0) {
        var errorParams = error.graphQLErrors[0];

        var leaveError = JSON.parse(errorParams.message);

        var errorCode = leaveError.errorCode;
        switch (errorCode) {
          case "_DB0100":
            //view access denied
            this.holidaysNoViewAccess = true;
            break;
          default:
            this.isHolidayFetchError = true;
            break;
          //any error code other than access rights will be present with same message.So handled with default.
        }
      } else {
        this.isHolidayFetchError = true;
      }
    },
    // Refetch holidays data
    refetchHolidays() {
      this.isHolidayFetchError = false;
      this.holidaysNoViewAccess = false;
      this.fetchListHolidaysInDashboard();
    },
    // Format date using moment.js
    formatDate(date) {
      return moment(date).format("ddd, D MMM");
    },
  },
};
</script>

<style lang="scss" scoped>
.holiday-and-calendar {
  height: 100%;
  margin: 0px;
  display: flex;
  flex-direction: column;
  overflow-x: auto;
}
/* Holiday list scrollbar height - increased for better desktop viewing */
.holiday-list-scrollbar {
  min-height: 280px;
  max-height: 300px;
}

/* Holiday list styling for horizontal scrolling */
.holiday-list {
  width: max-content !important;
  min-width: 100%;
}

/* Holiday content styling for horizontal scrolling */
.holiday-content {
  white-space: nowrap !important;
  min-width: max-content;
}

/* Perfect scrollbar styling */
:deep(.ps) {
  overflow-x: auto !important;
}

:deep(.ps__rail-y) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-y) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

:deep(.ps__rail-x) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-x) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

/* Tablet adjustments - matching EmployeeLeaveHistory */
@media screen and (max-width: 960px) and (min-width: 601px) {
  .holiday-list-scrollbar {
    height: 200px;
    max-height: 220px;
  }
}

/* Mobile adjustments - matching EmployeeLeaveHistory */
@media screen and (max-width: 600px) {
  .holiday-list-scrollbar {
    min-height: 220px;
    max-height: 250px;
  }
}
</style>
