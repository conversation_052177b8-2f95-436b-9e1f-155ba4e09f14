<template>
  <div>
    <AttendanceAlertModal
      v-if="displayGeoFencing"
      class="d-flex justify-center align-center"
      :open-notify-modal="displayGeoFencing"
      @close-notify-modal="closeGeoFencingModal"
    >
      <template #topContent>
        <div class="d-flex justify-center mt-3">
          <v-sheet
            color="white"
            elevation="2"
            height="50"
            border
            rounded
            width="400"
            class="d-flex justify-center align-center"
          >
            <v-icon
              class="pr-2"
              color="#0CA49E"
              style="font-weight: 200; font-size: 2em"
            >
              fas fa-clock
            </v-icon>
            {{ new Date().toString().slice(3, 25) }}
          </v-sheet>
        </div>
        <!-- For Futher Use
        <div class="mt-3 d-flex justify-center align-center">
          <v-avatar size="200" :color="getRandomColors" class="elevation-8">
            <img
              v-if="EmployeePhotoPath"
              :src="EmployeePhotoPath"
              alt="employeeimage"
            />
            <span v-else class="text-white text-h1">{{
              getLetterAvatar(EmployeeUserName)
            }}</span>
          </v-avatar>
        </div> -->
        <GeoFencingMap
          :center-point="geoFencingCenterPoint"
          :fence-radius="geoFencingRadius"
          :logged-position="userPosition"
          @check-geo-fencing="checkGeoFencingLocation"
        />
      </template>

      >
    </AttendanceAlertModal>

    <AttendanceAlertModal
      v-if="displayFacialRecognition"
      class="d-flex justify-center align-center"
      :open-notify-modal="displayFacialRecognition"
      image-name="dashboard/gps-not-accurate"
      @button-action="takePhoto()"
      @close-notify-modal="closeFacialModal"
    >
      <template #topContent>
        <div class="d-flex justify-center mt-3">
          <v-sheet
            color="white"
            elevation="2"
            height="50"
            border
            rounded
            width="400"
            class="d-flex justify-center align-center"
          >
            <v-icon
              class="pr-2"
              color="#0CA49E"
              style="font-weight: 200; font-size: 2em"
            >
              fas fa-clock
            </v-icon>
            {{ new Date().toString().slice(3, 25) }}
          </v-sheet>
        </div>
        <camera
          v-if="displayCamera"
          ref="cameraComponent"
          :bottom-button-text="cameraBottomButtonText"
          :challenges="challenges"
          @send-base-64="getbase64"
        />
      </template>
      <template #bottomContent>
        <div class="d-flex justify-center mt-3">
          <v-sheet
            color="white"
            elevation="2"
            height="50"
            border
            rounded
            width="400"
            class="d-flex justify-center align-center"
          >
            <v-icon
              class="pr-2"
              color="#0CA49E"
              style="font-weight: 200; font-size: 2em"
            >
              fas fa-clock
            </v-icon>
            {{ new Date().toString().slice(3, 25) }}
          </v-sheet>
        </div>
      </template>
      <template #footerContent>
        <p>
          If you face problem in enabling them, please follow the
          troubleshooting instructions here for
          <a
            class="link-text-highlight"
            rel="noopener noreferrer"
            target="_blank"
            href="https://support.google.com/accounts/answer/3467281?hl=en"
            >Android</a
          >
          or
          <a
            class="link-text-highlight"
            rel="noopener noreferrer"
            target="_blank"
            href="https://support.apple.com/en-il/HT207092"
            >IOS</a
          >
        </p>
      </template>
      >
    </AttendanceAlertModal>
    <v-dialog
      v-model="openAlert"
      class="missed-out-attendance-modal"
      width="650px"
      scrollable
      @click:outside="$emit('close-attendance-modal')"
    >
      <v-card class="missed-out-attendance-modal">
        <div class="d-flex justify-end">
          <v-icon
            id="attendance-regularization_close"
            color="primary"
            class="pr-3 pt-3"
            @click="$emit('close-attendance-modal')"
          >
            fas fa-times
          </v-icon>
        </div>
        <v-card-text>
          <v-container>
            <div
              class="d-flex justify-center flex-column"
              style="padding: 1em 2em"
            >
              <div v-if="isGeoEnforced">
                <!-- Geo Coordinates -->
                <v-row class="pa-3 align-center">
                  <v-progress-circular
                    model-value="100"
                    color="blue-accent-2"
                    :size="20"
                    class="mr-2"
                  />
                  <span class="text-subtitle-1 font-weight-bold text-primary"
                    >Geo Coordinates</span
                  >
                </v-row>
                <!-- Geo coordinate value -->
                <div
                  v-if="latitude && longitude"
                  class="bg-blue-grey-lighten-5 pa-3 rounded-lg"
                >
                  <v-card class="geo-coordinate-card common-box-shadow">
                    <v-icon class="mx-3" color="black" size="25">
                      fas fa-map-marker-alt
                    </v-icon>
                    <span class="text-primary"
                      >{{ latitude }} - {{ longitude }}</span
                    >
                  </v-card>
                </div>
                <!-- Geo coordinate may not accurate -->

                <div
                  v-else-if="openGeoNotAccurate"
                  class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                  :class="{ 'flex-column': windowWidth < 470 }"
                >
                  <v-icon color="warning" size="35"> warning </v-icon>
                  <div class="d-flex flex-column ml-3">
                    <span class="font-weight-bold text-primary mb-2"
                      >Geo-coordinates may not be accurate</span
                    >
                    <span
                      class="text-primary mb-2"
                      style="font-size: 0.8rem; text-align: justify"
                    >
                      {{ geoNotAccuracyContent }}</span
                    >
                    <v-btn
                      id="attendanceregularization_proceednow"
                      rounded="lg"
                      variant="elevated"
                      size="small"
                      color="primary"
                      max-width="120px"
                      @click="getGeoCoordinate"
                    >
                      Proceed now
                    </v-btn>
                  </div>
                </div>
                <!-- GPS not Enabled -->
                <div
                  v-else-if="openGeoRefresh"
                  class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                  :class="{ 'flex-column': windowWidth < 470 }"
                >
                  <v-icon class="rounded" color="warning" size="35">
                    warning
                  </v-icon>
                  <div class="d-flex flex-column ml-3">
                    <span class="font-weight-bold text-primary mb-2"
                      >GPS Not Enabled</span
                    >
                    <span
                      class="text-primary mb-2"
                      style="font-size: 0.8rem; text-align: justify"
                    >
                      Please check if you have given location access for this
                      app. If it's already given, GPS/Location service in your
                      smartphone/browser might be turned off. So please enable
                      them to add attendance.</span
                    >
                    <v-btn
                      id="attendanceregularization_georefresh"
                      variant="elevated"
                      rounded="lg"
                      color="primary"
                      max-width="100px"
                      @click="refreshLocation"
                    >
                      <v-icon
                        style="color: white !important"
                        class="mx-1"
                        size="20"
                      >
                        fas fa-redo-alt</v-icon
                      >Refresh
                    </v-btn>
                  </div>
                </div>
              </div>
              <!-- Select Your Workplace -->
              <v-row
                v-if="workPlaceData.isWorkPlaceEnabled"
                class="pa-3 mt-2 align-center"
              >
                <v-progress-circular
                  model-value="100"
                  color="green-accent-2"
                  :size="20"
                  class="mr-2"
                />
                <span class="text-subtitle-1 font-weight-bold text-primary"
                  >Select your workplace</span
                >
              </v-row>
              <div
                v-if="workPlaceData.isWorkPlaceEnabled"
                class="bg-blue-grey-lighten-5 pa-3 rounded-lg d-flex justify-center flex-wrap"
                :style="!enableWorkplace ? 'opacity:0.5' : ''"
              >
                <v-btn
                  v-for="place in workPlaceData.workPlaceDetails"
                  :id="`workplace_${place.workPlace
                    ?.trim()
                    .replace(/\s/g, '_')
                    .toLowerCase()}`"
                  :key="place.workPlaceId"
                  :color="
                    failedPreApprovals?.includes(place.workPlaceId)
                      ? 'grey-lighten-3'
                      : selectedWorkplaceId === place.workPlaceId
                      ? 'primary'
                      : 'white'
                  "
                  class="mx-2 my-2 rounded-0"
                  :class="
                    failedPreApprovals?.includes(place.workPlaceId)
                      ? 'pointer-block'
                      : 'common-box-shadow'
                  "
                  :disabled="!enableWorkplace"
                  @click="
                    failedPreApprovals?.includes(place.workPlaceId)
                      ? {}
                      : selectWorkplace(place.workPlaceId)
                  "
                >
                  <v-tooltip
                    v-if="failedPreApprovals?.includes(place.workPlaceId)"
                    activator="parent"
                    location="top"
                    max-width="300px"
                  >
                    {{ place.workPlace }} pre-approval request is not applied or
                    approved for the attendance date
                  </v-tooltip>

                  @ {{ place.workPlace }}
                </v-btn>
              </div>
              <!-- Check -in check-out entry -->
              <v-row class="pa-3 mt-2 align-center" style="flex-wrap: inherit">
                <v-progress-circular
                  model-value="100"
                  color="blue-accent-2"
                  :size="20"
                  class="mr-2"
                />
                <span class="text-subtitle-1 font-weight-bold text-primary"
                  >Choose your time of Check In and Check Out
                </span>
              </v-row>
              <!-- Check in Entry -->
              <div
                class="bg-blue-grey-lighten-5 pa-3 rounded-lg"
                :style="!enableAttendanceForm ? 'opacity:0.5' : ''"
              >
                <v-card class="common-box-shadow py-0 px-5" min-height="70">
                  <v-row class="align-center">
                    <v-col cols="12" xs="12" sm="3" class="d-flex align-center">
                      <v-avatar
                        color="green"
                        class="rounded-circle mr-2"
                        size="30"
                      >
                        <v-icon
                          color="white"
                          size="15"
                          style="font-weight: 600; font-size: 1.5em"
                        >
                          fas fa-power-off
                        </v-icon>
                      </v-avatar>
                      <span>Check In</span>
                    </v-col>
                    <v-col cols="12" xs="12" sm="9">
                      <div
                        class="d-flex align-center mt-2"
                        :class="{ 'flex-wrap': windowWidth < 470 }"
                      >
                        <!-- Check In Date Picker -->
                        <v-menu
                          v-model="checkInDateModal"
                          ref="checkInDateDialog"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                          min-width="auto"
                        >
                          <template #activator="{ props }">
                            <v-text-field
                              id="attendanceregularization_checkin_date"
                              v-model="checkInFormattedDate"
                              label="Date"
                              prepend-inner-icon="fas fa-calendar"
                              variant="solo"
                              density="comfortable"
                              v-bind="props"
                              class="mr-4"
                              :disabled="
                                attendanceType === 'missed-attendance' ||
                                !enableAttendanceForm
                              "
                              readonly
                            />
                          </template>
                          <v-date-picker
                            v-model="checkInDate"
                            :min="
                              attendanceType === 'missed-attendance'
                                ? ''
                                : splitConsiderationDate(
                                    attendanceData.consideration_from
                                  )
                            "
                            :max="
                              attendanceType === 'missed-attendance'
                                ? ''
                                : splitConsiderationDate(
                                    attendanceData.consideration_to
                                  )
                            "
                            color="primary"
                            @update:model-value="updateCheckInDate()"
                          >
                            <template v-slot:actions>
                              <v-btn
                                id="attendanceregularization_checkin_date_cancel"
                                variant="outlined"
                                rounded="lg"
                                @click="checkInDateModal = false"
                              >
                                Cancel
                              </v-btn>
                              <v-btn
                                id="attendanceregularization_checkin_date_save"
                                variant="elevated"
                                rounded="lg"
                                color="primary"
                                @click="checkInDateModal = false"
                              >
                                OK
                              </v-btn>
                            </template>
                          </v-date-picker>
                        </v-menu>

                        <!-- Check In Time picker -->
                        <v-menu
                          ref="checkInTimeDialog"
                          v-model="checkInTimeModal"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                          min-width="auto"
                        >
                          <template #activator="{ props }">
                            <v-text-field
                              id="attendanceregularization_checkin_time"
                              v-model="checkInTime"
                              label="Time"
                              prepend-inner-icon="fas fa-clock"
                              variant="solo"
                              density="comfortable"
                              v-bind="props"
                              :disabled="
                                attendanceType === 'missed-attendance' ||
                                !enableAttendanceForm
                              "
                              readonly
                            />
                          </template>
                          <v-time-picker
                            v-model="checkInTime"
                            color="primary"
                            format="24hr"
                            use-seconds
                            :min="
                              attendanceType === 'missed-attendance'
                                ? ''
                                : checkInMinTimeLimit(attendanceData)
                            "
                            :max="
                              attendanceType === 'missed-attendance'
                                ? ''
                                : checkInMaxTimeLimit(attendanceData)
                            "
                            @update:model-value="updateCheckInTime()"
                          >
                            <template v-slot:actions>
                              <v-btn
                                id="attendanceregularization_checkin_time_cancel"
                                variant="outlined"
                                rounded="lg"
                                @click="checkInTimeModal = false"
                              >
                                Cancel
                              </v-btn>
                              <v-btn
                                id="attendanceregularization_checkin_time_save"
                                variant="elevated"
                                rounded="lg"
                                color="primary"
                                @click="checkInTimeModal = false"
                              >
                                OK
                              </v-btn>
                            </template>
                          </v-time-picker>
                        </v-menu>
                      </div>
                    </v-col>
                  </v-row>
                </v-card>
                <!-- Check Out Entry -->
                <v-card
                  v-if="isCurrentDateRecord === 0"
                  class="common-box-shadow py-0 px-5 mt-3"
                  min-height="70"
                >
                  <v-row class="align-center">
                    <v-col cols="12" xs="12" sm="3" class="d-flex align-center">
                      <v-avatar
                        color="red"
                        class="rounded-circle mr-2"
                        size="30"
                      >
                        <v-icon
                          color="white"
                          size="15"
                          style="font-weight: 600; font-size: 1.5em"
                        >
                          fas fa-power-off
                        </v-icon>
                      </v-avatar>
                      <span>Check Out</span>
                    </v-col>
                    <v-col cols="12" xs="12" sm="9">
                      <div
                        class="d-flex align-center mt-2"
                        :class="{ 'flex-wrap': windowWidth < 470 }"
                      >
                        <v-menu
                          v-model="checkOutDateModal"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                          min-width="auto"
                        >
                          <template #activator="{ props }">
                            <v-text-field
                              id="attendanceregularization_checkout_date"
                              v-model="checkOutFormattedDate"
                              label="Date"
                              prepend-inner-icon="fas fa-calendar"
                              variant="solo"
                              density="comfortable"
                              v-bind="props"
                              class="mr-4"
                              :disabled="!enableAttendanceForm"
                              readonly
                            />
                          </template>
                          <v-date-picker
                            v-model="checkOutDate"
                            :min="checkInDate"
                            :max="
                              splitConsiderationDate(
                                attendanceData.consideration_to
                              )
                            "
                            color="primary"
                            @update:model-value="updateCheckOutDate()"
                          >
                            <template v-slot:actions>
                              <v-btn
                                id="attendanceregularization_checkout_date_cancel"
                                variant="outlined"
                                rounded="lg"
                                @click="checkOutDateModal = false"
                              >
                                Cancel
                              </v-btn>
                              <v-btn
                                id="attendanceregularization_checkout_date_save"
                                variant="elevated"
                                rounded="lg"
                                color="primary"
                                @click="checkOutDateModal = false"
                              >
                                OK
                              </v-btn>
                            </template>
                          </v-date-picker>
                        </v-menu>

                        <v-menu
                          v-model="checkOutTimeModal"
                          :close-on-content-click="false"
                          transition="scale-transition"
                          offset-y
                          min-width="auto"
                        >
                          <template #activator="{ props }">
                            <v-text-field
                              id="attendanceregularization_checkout_time"
                              v-model="checkOutTime"
                              label="Time"
                              prepend-inner-icon="fas fa-clock"
                              variant="solo"
                              density="comfortable"
                              readonly
                              v-bind="props"
                              :disabled="!enableAttendanceForm"
                            />
                          </template>
                          <v-time-picker
                            v-model="checkOutTime"
                            format="24hr"
                            use-seconds
                            :min="checkOutMinTimeLimit()"
                            :max="checkOutMaxTimeLimit(attendanceData)"
                            color="primary"
                          >
                            <template v-slot:actions>
                              <v-btn
                                id="attendanceregularization_checkout_time_cancel"
                                variant="outlined"
                                rounded="lg"
                                @click="checkOutTimeModal = false"
                              >
                                Cancel
                              </v-btn>
                              <v-btn
                                id="attendanceregularization_checkout_time_save"
                                variant="elevated"
                                rounded="lg"
                                color="primary"
                                @click="checkOutTimeModal = false"
                              >
                                OK
                              </v-btn>
                            </template>
                          </v-time-picker>
                        </v-menu>
                      </div>
                    </v-col>
                  </v-row>
                </v-card>
              </div>
              <div
                v-if="errorInAttendanceUpdate"
                class="d-flex justify-center mt-2"
              >
                <v-alert
                  density="compact"
                  type="error"
                  icon="warning"
                  closable
                  style="width: 100%"
                >
                  <span
                    class="text-primary mb-2"
                    style="font-size: 0.8rem; text-align: justify"
                    >{{ attendanceUpdateErrorMessage }}
                  </span>
                </v-alert>
              </div>
            </div>
          </v-container>
        </v-card-text>
        <v-card-actions class="d-flex justify-center pb-4">
          <v-btn
            id="attendanceregularization_submit"
            rounded="lg"
            color="primary"
            variant="elevated"
            class="my-3"
            :disabled="!enableSendRequestButton || isSendRequesting"
            @click="
              attendanceType === 'missed-attendance'
                ? checkGeoFenceEnabled(updateMissedCheckOutAttendance)
                : checkGeoFenceEnabled(updateMissedNoAttendance)
            "
          >
            Send Request
          </v-btn>
        </v-card-actions>
        <!-- loader -->
        <v-overlay
          absolute
          :model-value="isSendRequesting || isLoading"
          :opacity="1.0"
          color="#fff"
        >
          <v-progress-circular color="primary" indeterminate size="64" />
        </v-overlay>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="displayWrongDialog"
      persistent
      max-width="500"
      class="text-center"
    >
      <v-card>
        <v-card-title class="bg-grey-lighten-5 text-body-2 justify-center">
          {{ checkInButtonText }} Failed - {{ facialRecognitionErrorMsg }}
        </v-card-title>

        <div class="d-flex justify-center align-center my-2">
          <v-avatar size="100" :image="takenPhotoBase64" />
        </div>

        <v-card-title class="justify-center">
          {{ employeeDetails.employeeName }}
        </v-card-title>

        <div class="mb-5 d-flex justify-center align-center">
          <v-icon
            class="pr-2"
            color="#0CA49E"
            style="font-weight: 200; font-size: 2em"
          >
            fas fa-clock
          </v-icon>
          {{ new Date().toString().slice(3, 25) }}
        </div>

        <div
          v-if="isGeoFencingEnabled || isGeoEnforced"
          class="mb-5 d-flex justify-center align-center"
        >
          <v-icon
            class="pr-2"
            color="#0CA49E"
            style="font-weight: 200; font-size: 2em"
          >
            fas fa-map-marker-alt
          </v-icon>
          {{ addressOfLoggedIn }}
        </div>

        <v-card-actions>
          <v-spacer />
          <v-btn
            variant="elevated"
            rounded="lg"
            block
            color="red"
            @click="retryFacial"
          >
            Take another Photo
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <AppLoading v-if="isLoading" />
  </div>
</template>

<script>
import moment from "moment";
import {
  FACIAL_RECOGNITION,
  CHECK_ATTENDANCE_CONFIGURATION,
  CHECK_REGISTER_USER,
} from "@/graphql/dashboard/dashboardQueries";
import { isMobile } from "mobile-device-detect";
import AttendanceAlertModal from "@/components/custom-components/AttendanceAlertModal.vue";
import Camera from "@/components/custom-components/CameraObject.vue";
import GeoFencingMap from "@/components/custom-components/GeoFencingMap.vue";
import { getErrorCodesWithValidation, handleNetworkErrors } from "@/helper";

export default {
  name: "MissedOutAttendanceForm",
  components: {
    AttendanceAlertModal,
    Camera,
    GeoFencingMap,
  },

  props: {
    openAttendanceEntryModal: {
      type: Boolean,
      required: true,
    },
    attendanceType: {
      type: String,
      required: true,
    },
    attendanceData: {
      type: Object,
      required: true,
    },
    wfhPreApprovalErrorMessage: {
      type: String,
      default: "",
    },
    failedPreApprovals: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      SavedCoordinates: null,
      randomColors: [
        // these colors are base colors in vuetify, used to make random colors
        "red",
        "pink",
        "blue-grey",
        "purple",
        "orange",
        "deep-purple",
        "lime",
        "indigo",
        "blue",
        "cyan",
        "teal",
        "light-blue",
        "green",
      ],
      addressOfLoggedIn: "",
      coordinates: {
        latitude: "",
        longitude: "",
      },
      userPosition: [],
      takenPhotoBase64: null,
      cameraBottomButtonText: "Take Photo & Submit",
      displayWrongDialog: false,
      typeOfAttendance: "",
      isFacialRecognitionEnabled: false,
      isGeoFencingEnabled: false,
      geoFencingCenterPoint: [],
      geoFencingRadius: [],
      displayCamera: false,
      displayFacialRecognition: false,
      displayGeoFencing: false,
      checkInDate: "",
      checkInFormattedDate: "",
      checkInDateModal: false,
      checkInTimeModal: false,
      checkInTime: "",
      openGeoRefresh: false,
      isGeoEnforced: false,
      noOfChallenges: 0,
      challenges: [],
      enableLivenessDetection: false,
      attendanceConfigurationArray: [],
      facialRecognitionErrorMsg: "",

      checkOutDateModal: false,
      checkOutDate: "",
      checkOutFormattedDate: "",
      checkOutTimeModal: false,
      checkOutTime: "",

      latitude: "",
      longitude: "",
      geoNotAccuracyContent: "",
      openGeoNotAccurate: false,

      selectedWorkplaceId: "",
      checkInWorkPlace: "",

      errorInAttendanceUpdate: false,
      attendanceUpdateErrorMessage: "",
      isSendRequesting: false,
      openAlert: false,
      isLoading: false,
    };
  },
  computed: {
    getRandomColors() {
      let finalColor =
        this.randomColors[Math.floor(Math.random() * this.randomColors.length)];
      return finalColor + "-lighten-5";
    },

    EmployeeUserName() {
      return this.$store.state.userDetails.employeeFullName;
    },

    EmployeePhotoPath() {
      return this.$store.state.userDetails.employeePhotoPath;
    },

    workPlaceData() {
      return this.$store.state.dashboard.workPlaceData;
    },

    //to enable workplace check for geolocation in case of geo enforced
    enableWorkplace() {
      if (
        !this.isGeoEnforced ||
        (this.isGeoEnforced && this.latitude !== "" && this.longitude !== "")
      ) {
        return true;
      } else {
        return false;
      }
    },

    isCurrentDateRecord() {
      if (
        this.attendanceType === "no-attendance" &&
        this.attendanceData.iscurrentdate === 1
      ) {
        return 1;
      } else {
        return 0;
      }
    },
    //to enable check-in and check-out form
    //check for geolocation in case of geo enforced and
    //check for workplace in case of work place enforced
    enableAttendanceForm() {
      if (this.enableWorkplace) {
        if (
          !this.workPlaceData.isWorkPlaceEnabled ||
          (this.workPlaceData.isWorkPlaceEnabled && this.selectedWorkplaceId)
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    },

    //enable send request button  only of all mandatory data available
    enableSendRequestButton() {
      //reusing condition check for geo location and workplace data
      if (this.enableAttendanceForm) {
        //if its not current record then it must hold 4 data to enabe the submit button
        if (
          this.isCurrentDateRecord === 0 &&
          this.checkInDate &&
          this.checkInTime &&
          this.checkOutDate &&
          this.checkOutTime
        ) {
          return true;
        } else {
          //in case if its current date record we get only the check-in data
          if (
            this.isCurrentDateRecord === 1 &&
            this.checkInDate &&
            this.checkInTime
          ) {
            return true;
          } else {
            return false;
          }
        }
      } else {
        return false;
      }
    },

    //screen size
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    //get base url of the application
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    //get watch Position id
    getWatchPositionId() {
      return this.$store.state.dashboard.watchPositionId;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },

  watch: {
    //address of coordinates
    async coordinates(val) {
      if (
        (this.isGeoFencingEnabled && val != undefined) ||
        this.isGeoEnforced
      ) {
        this.addressOfLoggedIn = await this.$store.dispatch(
          "getAddressFromLatLng",
          {
            lat: val.latitude,
            lng: val.longitude,
            addressType: "city",
          }
        );
      }
    },

    isGeoEnforced() {
      if (this.isGeoEnforced) {
        //If watchposition enabled already clear it before getting geo-coordinates
        if (this.getWatchPositionId) {
          this.$store.dispatch(
            "dashboard/clearGeoWatchPosition",
            this.getWatchPositionId
          );
        }
        let deviceType = "";

        /* the geo coordinates is enforeced then the user should use the mobile app for more accuracy, 
            otherwise we need to get confirmation from the user that the location will not be accurate and 
            the user wants to proceed check-in/checkout */
        if (this.findDevice === "Mobile Application") {
          //For Mobile Application we don't show geo not accurate popup get coordinates
          this.getGeoCoordinate();
        } else {
          //for browsers
          deviceType = isMobile ? "mobile" : "web";
          this.geoNotAccuracyContent =
            "The geo-coordinates might not be accurate in " +
            deviceType +
            " browsers as the data might be misrepresented due to the Wifi network, " +
            "proxy servers, or VPN. If you still want to proceed, please progress to the next step. " +
            " It is advised that you use a mobile app for capturing accurate geo coordinates.";
          this.openGeoNotAccurate = true;
        }
      }
    },
  },

  //lifecycle hook to handle uncaught error in dashboard and its child component
  errorCaptured(err) {
    console.error("Missed Out Attendance Error:", err);
    let snackbarData = {
      isOpen: true,
      message:
        "Something went wrong while loading missed attendance data. Please try after some time.",
      type: "warning",
    };

    this.showAlert(snackbarData);
    return false;
  },
  mounted() {
    this.fetchAttendanceConfiguration();
    this.isGeoEnforced = this.$store.state.dashboard.isGeoEnforced;
    this.SavedCoordinates = this.$store.state.coordinates;
    this.openAlert = this.openAttendanceEntryModal;
    if (this.attendanceType === "missed-attendance") {
      let attendanceData = this.attendanceData;
      //for missed check Out we prefill punchIn date
      this.checkInTime = attendanceData.punchin_time;
      this.checkInDate = moment(attendanceData.punchin_date_ymd).format(
        "YYYY-MM-DD"
      );
      this.checkInFormattedDate = moment(
        attendanceData.punchin_date_ymd
      ).format("DD/MM/YYYY");

      //to prefill checkout date we use work shedule time
      let regularTo = attendanceData.regular_to?.trim().split(" ");

      this.checkOutDate = moment(regularTo[0]).format("YYYY-MM-DD");
      this.checkOutFormattedDate = moment(regularTo[0]).format("DD/MM/YYYY");
    } else {
      //For no attendance we prefill date based on consideration from and to date values
      let attendanceData = this.attendanceData;
      //split date and time to get date
      let considerationFrom = attendanceData.consideration_from
        ?.trim()
        .split(" ");

      //prefill checkin date
      this.checkInDate = moment(considerationFrom[0]).format("YYYY-MM-DD");
      this.checkInFormattedDate = moment(considerationFrom[0]).format(
        "DD/MM/YYYY"
      );
      if (this.isCurrentDateRecord === 0) {
        //to prefill checkout date we use work shedule time
        let regularTo = attendanceData.regular_to?.trim().split(" ");
        //prefill checkout date
        this.checkOutDate = moment(regularTo[0]).format("YYYY-MM-DD");
        this.checkOutFormattedDate = moment(regularTo[0]).format("DD/MM/YYYY");
      }
    }
  },
  methods: {
    //get attendance configuration
    fetchAttendanceConfiguration() {
      this.isLoading = true;
      try {
        let vm = this;
        vm.$apollo
          .query({
            query: CHECK_ATTENDANCE_CONFIGURATION,
            client: "apolloClientL",
            fetchPolicy: "no-cache",
          })
          .then(({ data }) => {
            let attendanceConfigurationArray =
              data?.checkAttendanceConfiguration
                ?.attendanceConfigurationDetails;
            this.attendanceConfigurationArray = attendanceConfigurationArray;
            this.geoFencingCenterPoint = [];
            this.geoFencingRadius = [];
            attendanceConfigurationArray?.forEach((element) => {
              if (element.geoFencingEnabled === "Yes") {
                this.isGeoEnforced = true;
                this.isGeoFencingEnabled = true;
                this.geoFencingCenterPoint.push(
                  element.centerPoint.split(",").map(Number)
                );
                this.geoFencingRadius.push(element.radius);
              }
            });
            this.isLoading = false;
          })
          .catch(() => {
            this.isLoading = false;
            this.isFacialRecognitionEnabled = false;
            this.isGeoFencingEnabled = false;
          });
      } catch {
        this.isLoading = false;
        this.isFacialRecognitionEnabled = false;
        this.isGeoFencingEnabled = false;
      }
    },
    retryFacial() {
      this.displayWrongDialog = false;
      this.displayFacialRecognition = false;
      this.displayFacialRecognition = true;
      this.displayCamera = false;
      this.displayCamera = true;
      this.$refs.cameraComponent.takenPhotobase64.imageWithChallenges = [];
      this.$refs.cameraComponent.challengesCount = 0;
      if (this.$refs.cameraComponent.challengeName) {
        this.$refs.cameraComponent.challengeName =
          this.$refs.cameraComponent.faceChallenges[
            this.$refs.cameraComponent.challengesCount
          ].challengeName;
      }
      this.$refs.cameraComponent.toggleCamera();
      this.$refs.cameraComponent.toggleCamera();
    },
    checkFacialEnabed() {
      if (this.isFacialRecognitionEnabled) {
        this.closeGeoFencingModal();
        this.checkIfFaceRegistered();
      } else {
        //Checking if it facial only enabled
        let facial = this.attendanceConfigurationArray.find((el) => {
          if (el.facialRecognitionEnabled === "Yes") {
            return el;
          }
        });
        if (facial) {
          this.noOfChallenges = facial.noOfChallenges;
          this.enableLivenessDetection = facial.enableLivenessDetection;
          this.isFacialRecognitionEnabled = true;
          this.checkFacialEnabed();
        } else {
          this.closeFacialModal();
          this.updateAttendance();
        }
      }
    },

    checkGeoFenceEnabled(type) {
      this.typeOfAttendance = type;
      if (this.isGeoFencingEnabled) {
        this.displayGeoFencing = true;
      } else {
        this.checkFacialEnabed();
      }
    },

    checkGeoFencingLocation(value) {
      if (value?.isInsideTheFence == true) {
        let facialConfiguration = this.attendanceConfigurationArray.filter(
          (el) => {
            if (el.centerPoint === value.insideCenterPoint) {
              return el;
            }
          }
        );
        facialConfiguration = facialConfiguration[0];
        if (facialConfiguration?.facialRecognitionEnabled === "Yes") {
          this.noOfChallenges = facialConfiguration.noOfChallenges;
          this.enableLivenessDetection =
            facialConfiguration.enableLivenessDetection;
          this.isFacialRecognitionEnabled = true;
          this.checkFacialEnabed();
        } else {
          this.isFacialRecognitionEnabled = false;
          this.closeGeoFencingModal();
          this.updateAttendance();
        }
      } else {
        this.closeGeoFencingModal();
        let snackbarData = {
          isOpen: true,
          message:
            "You are not in the geo fencing area. Please check your location and try again.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },

    checkIfFaceRegistered() {
      //checking if face has been registered for the employee
      if (this.isFacialRecognitionEnabled) {
        let vm = this;
        vm.isLoading = true;
        try {
          vm.$apollo
            .query({
              query: CHECK_REGISTER_USER,
              client: "apolloClientT",
              variables: {
                noOfChallenges: this.noOfChallenges,
              },
              fetchPolicy: "no-cache",
            })
            .then(({ data }) => {
              vm.isLoading = false;
              if (
                data?.checkRegisterUserAndGetRandomChallenge?.message ==
                "User Exist."
              ) {
                if (data.checkRegisterUserAndGetRandomChallenge) {
                  if (
                    this.typeOfAttendance === "updateMissedCheckOutAttendance"
                  ) {
                    this.cameraBottomButtonText = "Verify and Check Out";
                  } else {
                    this.cameraBottomButtonText = "Verify and Add Attendance";
                  }
                  if (
                    data.checkRegisterUserAndGetRandomChallenge?.challenges
                      .length
                  ) {
                    this.challenges =
                      data.checkRegisterUserAndGetRandomChallenge.challenges;
                  }
                }
                this.displayFacialRecognition = true;
                this.displayCamera = true;
              }
            })
            .catch(() => {
              vm.isLoading = false;
              let snackbarData = {
                isOpen: true,
                message: "Face has not been registered yet. Please register.",
                type: "warning",
              };
              this.showAlert(snackbarData);
            });
        } catch {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Something went wrong. Please try again.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      }
    },

    getbase64(value) {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .query({
            query: FACIAL_RECOGNITION,
            variables: {
              enableLivenessDetection: value.enableLivenessDetection,
              imageWithChallenges: value.imageWithChallenges,
            },
            client: "apolloClientT",
            fetchPolicy: "no-cache",
          })
          .then(({ data }) => {
            vm.isLoading = false;
            if (data.faceVerification.message === "Face Verified.") {
              // Stop camera stream first
              vm.$refs.cameraComponent?.stopCameraStream();

              // Close facial modal immediately to prevent reopening
              vm.closeFacialModal();
              // Update attendance after modal is closed
              vm.updateAttendance();
            } else {
              vm.isLoading = false;
              vm.displayWrongDialog = true;
              //display to take again
            }
          })
          .catch((error) => {
            vm.isLoading = false;
            vm.takenPhotoBase64 = value.imageWithChallenges[0].sourceImage;
            vm.handlefaceVerificationErrors(error);
          });
      } catch (error) {
        vm.isLoading = false;
        vm.takenPhotoBase64 = value.imageWithChallenges[0].sourceImage;
        vm.handlefaceVerificationErrors(error);
      }
    },

    updateAttendance() {
      this.typeOfAttendance();
    },

    handlefaceVerificationErrors(err = "") {
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodesWithValidation(err);
        if (errorCode) {
          switch (errorCode[0]) {
            case "EFA0101": // face not matched
              this.facialRecognitionErrorMsg = "Face not matched";
              break;
            case "EFA0102": // face not registered
              this.facialRecognitionErrorMsg = "Face not registered";
              break;
            case "EFA0106": // no face detected
              this.facialRecognitionErrorMsg = "No face detected";
              break;
            case "EFA0113": // liveness detection failed
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed";
              break;
            case "EFA0115": // liveness detection failed - Mouth Open
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed - Mouth Open";
              break;
            case "EFA0116": // liveness detection failed - Mouth Close
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed - Mouth Close";
              break;
            case "EFA0117": // liveness detection failed - Smile
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed - Smile";
              break;
            case "EFA0118": // liveness detection failed - No Smile
              this.facialRecognitionErrorMsg =
                "Liveness detection Validation failed - No Smile";
              break;
            case "EFA0109": // face verification failed
            default:
              this.facialRecognitionErrorMsg = "Face verification failed";
              break;
          }
        } else {
          this.facialRecognitionErrorMsg = "Face verification failed";
        }
      } else if (err && err.networkError) {
        this.facialRecognitionErrorMsg = handleNetworkErrors(err);
      } else {
        this.facialRecognitionErrorMsg = "Face verification failed";
      }
      this.$refs.cameraComponent.stopCameraStream();
      this.displayWrongDialog = true;
      document.getElementsByClassName("camera-box")[0].style.display = "none";
    },

    takePhoto() {
      this.displayCamera = true;
      document.getElementsByClassName("main-container-small")[0].style.display =
        "none";
    },
    closeFacialModal() {
      if (this.displayCamera) {
        this.displayCamera = false;
        if (this.$refs.cameraComponent._data?.isCameraOpen) {
          this.$refs.cameraComponent.stopCameraStream();
        }
      }
      this.displayFacialRecognition = false;
      this.displayWrongDialog = false;
      // Reset facial recognition state
      this.facialRecognitionErrorMsg = "";
    },
    closeGeoFencingModal() {
      this.displayGeoFencing = false;
    },
    //format user visible date in input field on date selected
    updateCheckInDate() {
      if (this.checkInDate) {
        const newFormattedDate = moment(this.checkInDate).format("DD/MM/YYYY");
        if (this.checkInFormattedDate !== newFormattedDate) {
          //based on checkin date we did validation for other data so resetting all data to avoid wrong entry
          this.checkOutTime = "";
          this.checkInTime = "";
        }
        this.checkInFormattedDate = newFormattedDate;
      }
    },

    //format checkout date in input field after click ok in date picker dialogue
    updateCheckOutDate() {
      if (this.checkOutDate) {
        const newFormattedDate = moment(this.checkOutDate).format("DD/MM/YYYY");
        //based on checkout date we did validation for check out so resetting check out time
        if (this.checkOutFormattedDate !== newFormattedDate) {
          this.checkOutTime = "";
        }
        this.checkOutFormattedDate = newFormattedDate;
      }
    },

    //while updating checkin time reset check out time.
    //because check out validation depend upon check in time as well.
    updateCheckInTime() {
      this.checkInTimeModal = false;
      this.checkOutTime = "";
    },

    // find the user device to show the geo not accurate popup
    findDevice() {
      //isMobile covers both mobile and tablet device
      if (isMobile) {
        if (window.$cookies.get("isNativeMobileApp") == 1) {
          return "Mobile Application"; // if device is mobile and not used from browser consider it as mobile app
        } else {
          return "Mobile Browser";
        }
      } else {
        return "Web"; // if device is not mobile then it is Web app
      }
    },

    //function to get the geo coordinates
    getGeoCoordinate() {
      window.postMessage("checkin", "*");
      let vm = this;
      vm.$store
        .dispatch("dashboard/fetchUserCoordinates")
        .then((response) => {
          let geoPosition = response[0]; // response[0] returns geocoordinantes value
          let watchPositionId = response[1]; // response[1] returns watch position id
          vm.openGeoNotAccurate = false;
          vm.openGeoRefresh = false;
          vm.latitude = geoPosition.latitude;
          vm.longitude = geoPosition.longitude;
          vm.coordinates = {
            latitude: geoPosition.latitude,
            longitude: geoPosition.longitude,
          };
          vm.userPosition = [geoPosition.latitude, geoPosition.longitude];
          vm.$store.dispatch(
            "dashboard/clearGeoWatchPosition",
            watchPositionId
          );
          vm.$store.dispatch("dashboard/initializeGeolocationWatch");
        })
        .catch((error) => {
          //condition checked for handling geo-error
          // for geolocation not suppored handled in common function
          if (error[0] === "geo_error") {
            //  error[0] returns geo_error
            let watchPositionId = error[1]; // error[1] returns watch position id
            vm.openGeoNotAccurate = false;
            vm.openGeoRefresh = true;
            vm.$store.dispatch(
              "dashboard/clearGeoWatchPosition",
              watchPositionId
            );
            vm.$store.dispatch("dashboard/initializeGeolocationWatch");
          }
        });
    },

    // selected workplace id
    selectWorkplace(workplaceId) {
      this.selectedWorkplaceId = workplaceId;
    },

    //function to split date to set min and max for date picker
    splitConsiderationDate(consideration_date) {
      if (consideration_date) {
        let dateLimit = consideration_date?.trim().split(" ");
        return dateLimit[0];
      } else return "";
    },

    //setting minimum time limit in time picker based on consideration date and time
    checkInMinTimeLimit(attendance) {
      if (attendance) {
        let considerationFrom = attendance.consideration_from
          ?.trim()
          .split(" ");
        let considerationTo = attendance.consideration_to?.trim().split(" ");
        if (
          this.checkInDate === considerationFrom[0] &&
          this.checkInDate === considerationTo[0]
        ) {
          return considerationFrom[1];
        } else if (this.checkInDate === considerationFrom[0]) {
          return considerationFrom[1];
        } else {
          return "";
        }
      }
    },

    //setting maximum time limit in time picker based on consideration date and time
    checkInMaxTimeLimit(attendance) {
      if (attendance) {
        let considerationFrom = attendance.consideration_from
          ?.trim()
          .split(" ");
        let considerationTo = attendance.consideration_to?.trim().split(" ");
        //for current date missed check in max time should be current time.
        if (attendance.iscurrentdate === 1) {
          let checkInMaxLimit = moment().format("kk:mm:ss"); //24hrs fomat
          return checkInMaxLimit;
        } else {
          if (
            this.checkInDate === considerationFrom[0] &&
            this.checkInDate === considerationTo[0]
          ) {
            return considerationTo[1];
          } else if (this.checkInDate === considerationTo[0]) {
            return considerationTo[1];
          } else {
            return "";
          }
        }
      }
    },

    //setting minimum time limit in time picker based on consideration date and time
    checkOutMinTimeLimit() {
      if (this.checkOutDate === this.checkInDate) {
        return this.checkInTime;
      } else {
        return "";
      }
    },

    //setting maximum time limit in time picker based on consideration date and time
    checkOutMaxTimeLimit(attendance) {
      if (attendance) {
        let considerationTo = attendance.consideration_to?.trim().split(" ");

        if (this.checkOutDate === considerationTo[0]) {
          return considerationTo[1];
        } else {
          return "";
        }
      }
    },

    //Function to show eror or success message inside dashboard
    showAlert(snackbarData) {
      this.$emit("close-attendance-modal");
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeErrorAlert() {
      this.errorInAttendanceUpdate = false;
      this.attendanceUpdateErrorMessage = "";
    },

    //function to update missed check out  attendance data
    updateMissedCheckOutAttendance() {
      let vm = this;
      vm.errorInAttendanceUpdate = false;
      vm.attendanceUpdateErrorMessage = "";
      vm.isSendRequesting = true;

      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      let attendanceId = vm.attendanceData.attendance_id;

      try {
        const apiobj = {
          url:
            vm.baseUrl +
            "employees/attendance/update-attendance/attendanceId/" +
            attendanceId +
            "/istheme/dashboardMissedCheckout",
          type: "POST",
          dataType: "json",
          data: {
            punchOutDate: vm.checkOutDate,
            punchOutTime: vm.checkOutTime,
            latitude: vm.latitude,
            longitude: vm.longitude,
            workPlaceId: vm.selectedWorkplaceId,
            Checkin_Data_Source: vm.findDevice(),
            Checkout_Data_Source: vm.findDevice(),
            requestResource: "HRAPPUI",
          },
        };
        vm.$store
          .dispatch("triggerControllerFunction", apiobj)
          .then((result) => {
            let attendanceData = result;
            vm.isSendRequesting = false;

            if (attendanceData && attendanceData.success) {
              snackbarData.message = attendanceData?.msg;
              snackbarData.type = "success";
              vm.retrieveMissedAttendanceData();

              vm.showAlert(snackbarData);
              vm.$emit("close-attendance-modal");
            } else {
              vm.errorInAttendanceUpdate = true;
              vm.attendanceUpdateErrorMessage = attendanceData?.msg;
            }
          })
          .catch((updateAttendanceError) => {
            vm.isSendRequesting = false;

            if (updateAttendanceError?.status == 200) {
              //we get this status 200 in case of session expired so redirect user to auth
              vm.userLogout();
            } else {
              vm.errorInAttendanceUpdate = true;
              vm.attendanceUpdateErrorMessage =
                "Something went wrong while updating the attendance. Please try after some time. ";
            }
          });
      } catch {
        vm.isSendRequesting = false;
        vm.errorInAttendanceUpdate = true;
        vm.attendanceUpdateErrorMessage =
          "Something went wrong while updating the attendance. Please try after some time. ";
      }
    },

    //function to update missed out attendance data (both check-in and check-out)
    updateMissedNoAttendance() {
      let vm = this;
      vm.errorInAttendanceUpdate = false;
      vm.attendanceUpdateErrorMessage = "";
      vm.isSendRequesting = true;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      //no attendance exist so send 0
      let attendanceId = 0;
      let employee_id = vm.loginEmployeeId;
      try {
        const apiobj = {
          url:
            vm.baseUrl +
            "employees/attendance/update-attendance/attendanceId/" +
            attendanceId +
            "/istheme/dashboardMissedCheckinCheckout",
          type: "POST",
          dataType: "json",
          data: {
            employeeId: employee_id ? employee_id : "",
            approverId: 0,
            punchInDate: vm.checkInDate,
            punchInTime: vm.checkInTime,
            punchInWorkPlace: vm.selectedWorkplaceId,
            punchOutDate: vm.checkOutDate,
            punchOutTime: vm.checkOutTime,
            punchOutWorkPlace: vm.selectedWorkplaceId,
            formSource: "Attendance Regularization",
            punchType:
              this.isCurrentDateRecord === 1 ? "Punch_In" : "Punch_Out",
            excludeBrkHrs: 0,
            status: "Draft",
            comment: "",
            lateAttendance: 0,
            latitude: vm.latitude,
            longitude: vm.longitude,
            Checkin_Data_Source: vm.findDevice(),
            Checkout_Data_Source: vm.findDevice(),
            requestResource: "HRAPPUI",
          },
        };
        vm.$store
          .dispatch("triggerControllerFunction", apiobj)
          .then((result) => {
            let attendanceData = result;
            vm.isSendRequesting = false;
            if (attendanceData && attendanceData.success) {
              snackbarData.message = attendanceData?.msg;
              snackbarData.type = "success";
              vm.retrieveNoAttendanceData();
              vm.showAlert(snackbarData);
              vm.$emit("close-attendance-modal");
            } else {
              vm.errorInAttendanceUpdate = true;
              if (!attendanceData?.msg) {
                attendanceData.msg =
                  "Something went wrong while updating the attendance using facial recognition. Please try after some time. ";
              }
              vm.attendanceUpdateErrorMessage = attendanceData?.msg;
            }
          })
          .catch((updateAttendanceError) => {
            vm.isSendRequesting = false;

            if (updateAttendanceError?.status == 200) {
              //we get this status 200 in case of session expired so redirect user to auth
              vm.userLogout();
            } else {
              vm.errorInAttendanceUpdate = true;
              vm.attendanceUpdateErrorMessage =
                "Something went wrong while adding the attendance. Please try after some time.";
            }
          });
      } catch {
        vm.isSendRequesting = false;
        vm.errorInAttendanceUpdate = true;
        vm.attendanceUpdateErrorMessage =
          "Something went wrong while adding the attendance. Please try after some time.";
      }
    },

    //incase of session expired error got from backend redirect to auth
    userLogout() {
      this.$store.dispatch("clearUserLock");
    },

    //reload page to refresh location
    refreshLocation() {
      window.location.reload();
    },

    //on sucesss call action to retrieve updated data
    retrieveMissedAttendanceData() {
      //call action to retrieve missed check out attendance entry by current logged employee
      this.$store.dispatch("dashboard/fetchMissedCheckOutAttendance");
    },

    retrieveNoAttendanceData() {
      if (this.isCurrentDateRecord === 1) {
        this.$store.commit("dashboard/REQUIRE_TO_UPDATE_ATTENDANCE", true);
      }

      //call action to retrieve no attendance entry by current logged employee for a month
      this.$store.dispatch("dashboard/fetchNoAttendanceRecord");
    },
    // Get letter avatar from name - returns first letters of first couple of words
    getLetterAvatar(name) {
      if (!name) return "?";

      // Split name into words and filter out empty strings
      const words = name
        ?.trim()
        .split(/\s+/)
        .filter((word) => word.length > 0);

      if (words.length === 0) return "?";
      if (words.length === 1) return words[0].charAt(0).toUpperCase();

      // Return first letters of first two words
      return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "https://unpkg.com/leaflet@1.6.0/dist/leaflet.css";

.missed-out-attendance-modal {
  border-top: 10px solid rgb(var(--v-theme-primary));
  border-radius: 10px;
}

.geo-coordinate-card {
  min-height: 50px;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}
</style>
