<template>
  <div style="height: 100%">
    <v-card class="pa-4 pb-1 company-update-card card-highlight rounded-lg">
      <!-- Company Update Card title -->
      <v-row>
        <v-col cols="12" class="py-2">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="cyan-accent-3"
              :size="22"
              class="mr-2"
            />
            <span class="ml-2 text-h6 font-weight-bold text-primary">
              {{ $t("dashboard.myCompanyUpdates") }}
            </span>
          </div>
        </v-col>
      </v-row>

      <!-- Get reminder loading screen -->
      <v-row v-if="isGetReminderLoading" class="pa-1 pb-0">
        <v-col cols="12" class="py-0">
          <div class="d-flex flex-column align-center team-updates-content">
            <v-row v-for="index in 2" :key="index" style="width: 100%">
              <v-col v-if="index === 2" cols="12">
                <v-divider />
              </v-col>
              <v-col cols="4">
                <v-skeleton-loader class="mx-auto" height="55" type="image" />
              </v-col>
              <v-col cols="8" class="d-flex">
                <v-skeleton-loader
                  v-for="i in 4"
                  :key="i"
                  class="mx-auto"
                  type="avatar"
                />
              </v-col>
            </v-row>
          </div>
        </v-col>
      </v-row>

      <!-- Get reminder Error And Empty screen -->
      <v-row
        v-else-if="errorInGetReminders || reminderLength === 0"
        class="pa-1 pb-0"
      >
        <v-col cols="12" class="py-0">
          <div class="d-flex align-center team-updates-content">
            <NoDataCardWithQuotes
              id="dashboard_companyupdates_refresh"
              image-name="dashboard/company-update"
              :text-message="$t('dashboard.companyUpdateMessage')"
              :secondary-bold-text="$t('dashboard.companyUpdateSecondary')"
              :is-small-card="false"
              :card-type="errorInGetReminders ? 'error' : 'no-data'"
              :error-content="
                errorInGetReminders ? $t('dashboard.technicalDifficulties') : ''
              "
              image-size="40%"
              :is-show-image="windowWidth > 960"
              @refresh-triggered="refreshReminders()"
            />
          </div>
        </v-col>
      </v-row>

      <!-- Company Update Content-->
      <v-row v-else class="pa-1 pb-0">
        <v-col cols="12">
          <perfect-scrollbar
            class="w-100 overflow-y-auto overflow-x-hidden company-updates-scrollbar"
          >
            <div class="team-updates-content">
              <!-- Birthday Wizards -->
              <v-row
                v-if="birthdayList.length > 0"
                class="align-center"
                style="width: 100%; margin: 0px"
              >
                <v-col
                  cols="12"
                  xs="12"
                  :sm="windowWidth > 700 ? 3 : 4"
                  md="4"
                  :lg="windowWidth > 1580 ? 3 : 4"
                  class="pa-1"
                >
                  <CardIconTitle
                    :card-title="$t('dashboard.birthdays')"
                    :count="birthdayList.length"
                    icon-name="fas fa-birthday-cake"
                    icon-color="purple"
                    icon-background="purple-lighten-5"
                  />
                </v-col>
                <v-col
                  cols="12"
                  xs="12"
                  :sm="windowWidth > 700 ? 9 : 8"
                  md="8"
                  :lg="windowWidth > 1580 ? 9 : 8"
                  class="px-1 py-0"
                >
                  <AvatarSlider :avatar-array="birthdayList" />
                </v-col>
              </v-row>

              <!-- Work Anniversary Wizards -->
              <v-col
                v-if="workAnniversaryList.length > 0 && birthdayList.length > 0"
                cols="12"
              >
                <v-divider />
              </v-col>
              <v-row
                v-if="workAnniversaryList.length > 0"
                class="align-center"
                style="width: 100%; margin: 0px"
              >
                <v-col
                  cols="12"
                  xs="12"
                  :sm="windowWidth > 700 ? 3 : 4"
                  md="4"
                  :lg="windowWidth > 1580 ? 3 : 4"
                  class="pa-1"
                >
                  <CardIconTitle
                    :card-title="$t('dashboard.workAnniversary')"
                    :count="workAnniversaryList.length"
                    icon-name="fas fa-briefcase"
                    icon-color="teal"
                    icon-background="teal-lighten-5"
                  />
                </v-col>
                <v-col
                  cols="12"
                  xs="12"
                  :sm="windowWidth > 700 ? 9 : 8"
                  md="8"
                  :lg="windowWidth > 1580 ? 9 : 8"
                  class="px-1 py-0"
                >
                  <AvatarSlider :avatar-array="workAnniversaryList" />
                </v-col>
              </v-row>

              <!-- Awards -->
              <v-col
                v-if="
                  awardsList.length > 0 &&
                  (workAnniversaryList.length > 0 || birthdayList.length > 0)
                "
                cols="12"
              >
                <v-divider />
              </v-col>
              <v-row
                v-if="awardsList.length > 0"
                class="align-center"
                style="width: 100%; margin: 0px"
              >
                <v-col
                  cols="12"
                  xs="12"
                  :sm="windowWidth > 700 ? 3 : 4"
                  md="4"
                  :lg="windowWidth > 1580 ? 3 : 4"
                  class="pa-1"
                >
                  <CardIconTitle
                    :card-title="$t('dashboard.awards')"
                    :count="awardsList.length"
                    icon-name="fas fa-trophy"
                    icon-color="amber"
                    icon-background="amber-lighten-5"
                  />
                </v-col>
                <v-col
                  cols="12"
                  xs="12"
                  :sm="windowWidth > 700 ? 9 : 8"
                  md="8"
                  :lg="windowWidth > 1580 ? 9 : 8"
                  class="px-1 py-0"
                >
                  <AvatarSlider :avatar-array="awardsList" />
                </v-col>
              </v-row>
            </div>
          </perfect-scrollbar>
        </v-col>
      </v-row>
    </v-card>

    <!-- Leave Closure Warning Modal -->
    <NotifyAlertModal
      v-if="showLeaveClosureWarning"
      :open-notify-modal="showLeaveClosureWarning"
      :modal-title="$t('dashboard.leaveClosureTitle')"
      :modal-content="$t('dashboard.leaveClosureContent')"
      image-name="dashboard/leave-closure-warning"
      button-text=""
      image-width="300"
      @close-notify-modal="
        leaveClosureDetails['showLeaveClosureWarningPopup'] = false
      "
    >
      <template #bodyContent>
        <div class="py-2 px-2 text-primary text-body-2">
          {{ $t("dashboard.leaveClosureActivities") }}
        </div>
        <div class="py-1 px-2 text-primary text-body-2">
          1. {{ $t("dashboard.leaveApprovalDeadline") }}
          {{ leaveClosureEndDateOrgDateFormat }}.
        </div>
        <div class="pb-1 px-2 text-primary text-body-2">
          2. {{ $t("dashboard.initiateLeaveEncashment") }}
        </div>
        <div class="pb-1 px-2 text-primary text-body-2">
          3. {{ $t("dashboard.runLeaveClosureProcess") }}
        </div>
        <div class="px-2 text-primary text-body-2">
          4. {{ $t("dashboard.grantLeaveCredit") }}
          {{ checkNullValue(leaveClosureStartYear) }}
          {{ $t("dashboard.leaveCreditPolicy") }}
        </div>
        <div class="pt-4">
          <v-btn
            color="primary"
            rounded="lg"
            variant="elevated"
            @click="redirectToLeaveForm()"
          >
            {{ $t("dashboard.goAhead") }}
          </v-btn>
        </div>
      </template>
    </NotifyAlertModal>

    <!-- Birthday Wish Modal -->
    <v-dialog
      v-model="birthdayWishModal"
      max-width="500"
      @click:outside="closeBirthdayWishModal()"
    >
      <v-card>
        <v-card-text class="pa-0">
          <img
            :src="getImageUrl"
            width="100%"
            height="300"
            style="margin-bottom: -150px"
          />
          <div class="text-h5 font-weight-bold text-center mt-n12">
            <div class="text-primary pb-2">
              {{ $t("dashboard.happyBirthday") }}
            </div>
            <div class="text-primary">
              {{ loginEmpName }}
            </div>
            <img
              :src="getCakeImageUrl"
              width="50%"
              height="200"
              class="mx-auto"
            />
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// Plugins
import moment from "moment";
// Queries
import { GET_REMINDERS } from "@/graphql/dashboard/dashboardQueries";

// Components
import AvatarSlider from "@/components/helper-components/AvatarSlider.vue";
import NotifyAlertModal from "@/components/custom-components/NotifyAlertModal.vue";
import CardIconTitle from "@/components/helper-components/CardIconTitle.vue";
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes.vue";
// Helper functions
import { orgDateFormatter, checkNullValue } from "@/helper";

export default {
  name: "MyCompanyUpdates",

  components: {
    AvatarSlider,
    CardIconTitle,
    NotifyAlertModal,
    NoDataCardWithQuotes,
  },

  data() {
    return {
      errorInGetReminders: false,
      birthdayWishModal: false,
      loginEmpName: "",
      isGetReminderLoading: false,
      // Leave closure warning modal
      leaveClosureDetails: {
        leaveClosureStartDate: "",
        leaveClosureEndDate: "",
        showLeaveClosureWarningPopup: 0,
      },
    };
  },

  computed: {
    // To know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getImageUrl() {
      const extension = this.isBrowserSupportWebp ? "webp" : "png";
      return require(`@/assets/images/dashboard/birthday-celebration.${extension}`);
    },
    getCakeImageUrl() {
      const extension = this.isBrowserSupportWebp ? "webp" : "png";
      return require(`@/assets/images/dashboard/birthday-cake-img.${extension}`);
    },

    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // Leave closure start year from start date
    leaveClosureStartYear() {
      if (
        this.leaveClosureDetails &&
        this.leaveClosureDetails.leaveClosureStartDate
      ) {
        // Get year from leave closure start date
        let startYear = moment(
          this.leaveClosureDetails.leaveClosureStartDate
        ).year();
        return parseInt(startYear) + 1; // For getting next leave year
      }
      return "";
    },
    // Leave closure end date based on org date format
    leaveClosureEndDateOrgDateFormat() {
      if (
        this.leaveClosureDetails &&
        this.leaveClosureDetails.leaveClosureEndDate
      ) {
        // Get the leave closure end date based on org date format
        return orgDateFormatter(this.leaveClosureDetails.leaveClosureEndDate);
      }
      return "";
    },
    // Show leave closure popup only for admin and showLeaveClosureWarningPopup is 1
    showLeaveClosureWarning() {
      if (
        this.leaveClosureDetails &&
        this.leaveClosureDetails.showLeaveClosureWarningPopup &&
        this.$store.state.isAdmin &&
        this.orgCode !== "cbit" &&
        !this.isMobileView
      ) {
        return true;
      }
      return false;
    },
    // Get all reminders(birthday, awards, work anniversaries)
    getReminders() {
      return this.$store.state.dashboard.reminders || {};
    },
    // Formatting birthday list data to use it inside avatarslider component incase of data exist
    birthdayList() {
      if (this.getReminders.birthday) {
        let parsedBirthday = JSON.parse(this.getReminders.birthday);
        let currentDay = moment().format("D MMM");
        let birthdayArray = [];
        for (var index = 0; index < parsedBirthday.length; index++) {
          let birthday = {
            title: parsedBirthday[index].Employee_First_Name,
            tooltip: {
              name: parsedBirthday[index].Employee_Name,
              employeeId: parsedBirthday[index].User_Defined_EmpId
                ? parsedBirthday[index].User_Defined_EmpId
                : "",
              designation: parsedBirthday[index].Designation
                ? parsedBirthday[index].Designation
                : "",
              department: parsedBirthday[index].Department
                ? parsedBirthday[index].Department
                : "",
            },
            subtitle:
              currentDay === parsedBirthday[index].Date_of_Birth
                ? this.$t("dashboard.today")
                : parsedBirthday[index].Date_of_Birth,
            photoPath: parsedBirthday[index].Profile_Pic,
          };
          birthdayArray.push(birthday);
        }
        return birthdayArray;
      } else {
        return [];
      }
    },
    // Formatting workAnniversary list data to use it inside avatarslider component incase of data exist
    workAnniversaryList() {
      if (this.getReminders.workAnniversary) {
        let parsedWorkAnniversary = JSON.parse(
          this.getReminders.workAnniversary
        );
        let workAnniversaryArray = [];
        for (var index = 0; index < parsedWorkAnniversary.length; index++) {
          let workAnniversary = {
            title: parsedWorkAnniversary[index].Employee_First_Name,
            tooltip: {
              name: parsedWorkAnniversary[index].Employee_Name,
              employeeId: parsedWorkAnniversary[index].User_Defined_EmpId
                ? parsedWorkAnniversary[index].User_Defined_EmpId
                : "",
              designation: parsedWorkAnniversary[index].Designation
                ? parsedWorkAnniversary[index].Designation
                : "",
              department: parsedWorkAnniversary[index].Department
                ? parsedWorkAnniversary[index].Department
                : "",
            },
            subtitle: parsedWorkAnniversary[index].Date_of_Join,
            photoPath: parsedWorkAnniversary[index].Profile_Pic,
          };
          workAnniversaryArray.push(workAnniversary);
        }
        return workAnniversaryArray;
      } else {
        return [];
      }
    },
    // Formatting awards list data to use it inside avatarslider component incase of data exist
    awardsList() {
      if (this.getReminders.awards) {
        let parsedAwards = JSON.parse(this.getReminders.awards);
        let awardsArray = [];
        for (var index = 0; index < parsedAwards.length; index++) {
          let award = {
            title: parsedAwards[index].Employee_First_Name,
            tooltip: {
              name: parsedAwards[index].Employee_Name,
              employeeId: parsedAwards[index].User_Defined_EmpId
                ? parsedAwards[index].User_Defined_EmpId
                : "",
              designation: parsedAwards[index].Designation
                ? parsedAwards[index].Designation
                : "",
              department: parsedAwards[index].Department
                ? parsedAwards[index].Department
                : "",
            },
            subtitle: parsedAwards[index].Award_Name,
            photoPath: parsedAwards[index].Profile_Pic,
          };
          awardsArray.push(award);
        }
        return awardsArray;
      } else {
        return [];
      }
    },
    // Find count of total reminders
    reminderLength() {
      let remindersLength =
        this.birthdayList.length +
        this.awardsList.length +
        this.workAnniversaryList.length;
      return remindersLength;
    },
    // Get login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  mounted() {
    this.fetchReminders();
  },

  methods: {
    checkNullValue,
    fetchReminders() {
      let vm = this;
      vm.isGetReminderLoading = true;
      vm.errorInGetReminders = false;
      vm.$apollo
        .query({
          query: GET_REMINDERS,
          client: "apolloClientC",
        })
        .then(({ data }) => {
          try {
            let { errorCode, reminders } = data.getReminders;
            // check for getReminder value. If not received from backend show error
            if (!errorCode) {
              this.$store.commit("dashboard/UPDATE_REMINDERS", reminders);
              this.errorInGetReminders = false;
              this.leaveClosureDetails = reminders.leaveClosure
                ? JSON.parse(reminders.leaveClosure)
                : this.leaveClosureDetails;
              let birthdayDetails = reminders.birthday
                ? JSON.parse(reminders.birthday)
                : [];
              if (
                birthdayDetails.length > 0 &&
                !window.$cookies.get("birthdayWishShowed")
              ) {
                let loginEmpBirthday = birthdayDetails.filter(
                  (el) => el.Employee_Id === this.loginEmployeeId
                );
                if (loginEmpBirthday.length > 0) {
                  let currentDay = moment().format("D MMM");
                  let birthDay = moment(
                    loginEmpBirthday[0].Date_of_Birth
                  ).format("D MMM");
                  this.loginEmpName = loginEmpBirthday[0].Employee_Name;
                  this.birthdayWishModal = currentDay === birthDay;
                }
              }
            } else {
              // if any error occurs, data is returned empty object
              vm.errorInGetReminders = true;
            }
          } catch {
            vm.errorInGetReminders = true;
          }
          vm.isGetReminderLoading = false;
        })
        .catch(() => {
          vm.isGetReminderLoading = false;
          vm.errorInGetReminders = true;
        });
    },
    // Function to refetch reminders incase of error
    refreshReminders() {
      this.errorInGetReminders = false;
      this.fetchReminders();
    },
    redirectToLeaveForm() {
      const leave_url = this.baseUrl + "/employees/leaves";
      window.open(leave_url);
    },
    closeBirthdayWishModal() {
      this.birthdayWishModal = false;
      window.$cookies.set("birthdayWishShowed", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.company-update-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.team-updates-content {
  min-height: 250px;
}

/* Company updates scrollbar height - responsive */
.company-updates-scrollbar {
  height: 280px;
  max-height: 280px;
}

/* Perfect scrollbar styling */
:deep(.ps) {
  overflow-x: hidden !important;
}

:deep(.ps__rail-y) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-y) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

:deep(.ps__rail-x) {
  display: none !important;
}

.card-highlight {
  transition: box-shadow 0.3s ease;
}

.card-highlight:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .company-updates-scrollbar {
    height: 280px;
    max-height: 300px;
  }

  .team-updates-content {
    min-height: 200px;
  }
}

@media screen and (max-width: 960px) and (min-width: 601px) {
  .company-updates-scrollbar {
    height: 240px;
    max-height: 240px;
  }

  .team-updates-content {
    min-height: 225px;
  }
}
</style>
