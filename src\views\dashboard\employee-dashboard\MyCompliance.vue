<template>
  <div class="fill-height">
    <v-card class="card-highlight rounded-lg" height="100%">
      <div class="d-flex">
        <RingWaveAnimation
          v-if="
            !isLoadingCard && !isErrorInAction && totalExpiredDocsLength > 0
          "
          class="my-compliance-animation"
          :display-text="totalExpiredDocsLength"
        />
        <v-progress-circular
          v-else
          model-value="100"
          color="blue-accent-2"
          :size="22"
          class="mt-5 ml-4"
        />
        <span class="text-h6 text-primary font-weight-bold mt-4 ml-4">
          {{ $t("dashboard.myCompliance") }}
        </span>
      </div>

      <!-- Loading State -->
      <div v-if="isLoadingCard">
        <v-skeleton-loader
          v-for="i in 3"
          :key="i"
          class="mx-auto mt-4"
          type="list-item-avatar-two-line"
        />
      </div>

      <!-- Error or Empty State -->
      <div
        v-else-if="isErrorInAction || totalExpiredDocsLength === 0"
        class="d-flex align-center mt-3"
        style="height: 15rem"
      >
        <NoDataCardWithQuotes
          image-name="dashboard/actions-empty-image"
          :primary-bold-text="$t('dashboard.lifeIsPercent')"
          :text-message="$t('dashboard.whatHappensToYou')"
          :bottom-bold-text="$t('dashboard.andPercent')"
          :bottom-text-message="$t('dashboard.howYouReactToIt')"
          :is-small-card="false"
          image-size="70%"
          :is-show-image="windowWidth > 960"
          :card-type="isErrorInAction ? 'error' : 'no-data'"
          :error-content="
            isErrorInAction ? $t('dashboard.technicalDifficulties') : ''
          "
          @refresh-triggered="fetchComplianceList()"
        />
      </div>

      <!-- Compliance Content -->
      <v-row v-else class="pa-4 mt-n4">
        <v-col cols="12">
          <perfect-scrollbar
            class="w-100 overflow-y-auto overflow-x-hidden compliance-scrollbar"
          >
            <div class="action-content">
              <!-- License Documents -->
              <PresentActionCards
                v-for="(licenseItem, e) in licenseExpiredList"
                :key="e + '-license'"
                :card-property="fnMakeRandomColors()"
                :title="licenseItem.Document_Name"
                :sub-title-bold="$t('dashboard.expiryDate')"
                :sub-title-text="licenseItem.expiryDate"
                :list-index="e"
                :is-clickable="isFormHasViewRights('employees')"
                icon-name="fas fa-file-alt"
                @action-triggered="
                  employeeFormRedirectionUrl(
                    'v3/employee-self-service/my-profile'
                  )
                "
              >
                <template #actionContent>
                  <v-btn
                    :color="
                      licenseItem.Status === 'Expired' ? 'error' : 'warning'
                    "
                    size="small"
                    variant="flat"
                    class="text-caption"
                  >
                    <span
                      :class="
                        licenseItem.Status === 'Expired'
                          ? 'text-white'
                          : 'text-primary'
                      "
                    >
                      {{ licenseItem.Status }}
                    </span>
                  </v-btn>
                </template>
              </PresentActionCards>

              <!-- Passport Documents -->
              <PresentActionCards
                v-for="(passportItem, f) in passportExpiredList"
                :key="f + '-passport'"
                :card-property="fnMakeRandomColors()"
                :title="passportItem.Document_Name"
                :sub-title-bold="$t('dashboard.expiryDate')"
                :sub-title-text="passportItem.expiryDate"
                :list-index="f"
                :is-clickable="isFormHasViewRights('employees')"
                icon-name="fas fa-file-alt"
                @action-triggered="
                  employeeFormRedirectionUrl(
                    'v3/employee-self-service/my-profile'
                  )
                "
              >
                <template #actionContent>
                  <v-btn
                    :color="
                      passportItem.Status === 'Expired' ? 'error' : 'warning'
                    "
                    size="small"
                    variant="flat"
                    class="text-caption"
                  >
                    <span
                      :class="
                        passportItem.Status === 'Expired'
                          ? 'text-white'
                          : 'text-primary'
                      "
                    >
                      {{ passportItem.Status }}
                    </span>
                  </v-btn>
                </template>
              </PresentActionCards>

              <!-- Accreditation Documents -->
              <PresentActionCards
                v-for="(accreditationItem, g) in accreditationsExpiredList"
                :key="g + '-accreditation'"
                :card-property="fnMakeRandomColors()"
                :title="accreditationItem.Document_Name"
                :sub-title-bold="$t('dashboard.expiryDate')"
                :sub-title-text="accreditationItem.expiryDate"
                :list-index="g"
                :is-clickable="isFormHasViewRights('employees')"
                icon-name="fas fa-file-alt"
                @action-triggered="
                  employeeFormRedirectionUrl(
                    'v3/employee-self-service/my-profile'
                  )
                "
              >
                <template #actionContent>
                  <v-btn
                    :color="
                      accreditationItem.Status === 'Expired'
                        ? 'error'
                        : 'warning'
                    "
                    size="small"
                    variant="flat"
                    class="text-caption"
                  >
                    <span
                      :class="
                        accreditationItem.Status === 'Expired'
                          ? 'text-white'
                          : 'text-primary'
                      "
                    >
                      {{ accreditationItem.Status }}
                    </span>
                  </v-btn>
                </template>
              </PresentActionCards>
            </div>
          </perfect-scrollbar>
        </v-col>
      </v-row>
    </v-card>
  </div>
</template>

<script>
// Custom components
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes.vue";
import PresentActionCards from "@/components/helper-components/PresentActionCards.vue";
import RingWaveAnimation from "@/components/helper-components/RingWaveAnimation.vue";
// Queries
import { GET_EXPIRED_DOCUMENT_LIST } from "@/graphql/dashboard/dashboardQueries";

export default {
  name: "MyCompliance",

  components: {
    NoDataCardWithQuotes,
    PresentActionCards,
    RingWaveAnimation,
  },

  props: {
    randomColors: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      isErrorInList: false,
      isLoadingCard: false,
      licenseExpiredList: [],
      passportExpiredList: [],
      accreditationsExpiredList: [],
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    // Base url of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    // Return form details
    formDetails() {
      return this.$store.getters.formAccessRights;
    },
    // Function to calculate the whole actions list
    totalExpiredDocsLength() {
      // Total count of expired documents
      let actionsList =
        this.licenseExpiredList.length +
        this.passportExpiredList.length +
        this.accreditationsExpiredList.length;
      return actionsList;
    },
    // To check actions error
    isErrorInAction() {
      if (this.isErrorInList && this.totalExpiredDocsLength === 0) return true;
      else return false;
    },
  },

  mounted() {
    this.fetchComplianceList();
  },

  methods: {
    fetchComplianceList() {
      this.isErrorInList = false;
      this.isLoadingCard = true;
      this.$apollo
        .query({
          query: GET_EXPIRED_DOCUMENT_LIST,
          variables: {
            selfService: 1,
          },
          client: "apolloClientL",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data.retrieveDashboardExpiredDocsDetails) {
            const { ExpiredDocuments } =
              res.data.retrieveDashboardExpiredDocsDetails;
            if (ExpiredDocuments && Object.keys(ExpiredDocuments).length > 0) {
              const {
                getAccredidationDetails,
                getPassportDetails,
                getLicenseDetails,
              } = ExpiredDocuments;
              this.passportExpiredList = getPassportDetails || [];
              this.licenseExpiredList = getLicenseDetails || [];
              this.accreditationsExpiredList = getAccredidationDetails || [];
            } else {
              this.isErrorInList = true;
            }
          } else {
            this.isErrorInList = true;
          }
          this.isLoadingCard = false;
        })
        .catch(() => {
          this.isErrorInList = true;
          this.isLoadingCard = false;
        });
    },
    // Function to form random colors for actions card background, icon color and avatar background
    fnMakeRandomColors() {
      let finalColor =
        this.randomColors[Math.floor(Math.random() * this.randomColors.length)];
      return {
        background: "#F9FBFC", // Same background for all actions card
        bg: finalColor + "-lighten-4", // Combine vuetify base color with lighten-4 property
        color: finalColor, // Set vuetify base color as icon color
      };
    },
    // Filter particular form url under employee modules
    employeeFormRedirectionUrl(formURL) {
      const redirectionUrl = this.baseUrl + formURL;
      window.location.href = redirectionUrl;
    },
    // Check for view rights in forms for redirection
    // If they don't have view rights we will not do redirection
    isFormHasViewRights(formName) {
      let form = this.formDetails(formName);
      if (form && form.accessRights && form.accessRights.view) {
        return true;
      }
      return false;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style lang="scss" scoped>
.action-content {
  height: 250px;
}
.my-compliance-animation {
  margin-top: 19px;
  margin-left: 25px;
}

/* Compliance scrollbar height - responsive */
.compliance-scrollbar {
  height: 280px;
  max-height: 280px;
}

/* Perfect scrollbar styling */
:deep(.ps) {
  overflow-x: hidden !important;
}

:deep(.ps__rail-y) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-y) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

:deep(.ps__rail-x) {
  display: none !important;
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .compliance-scrollbar {
    height: 280px;
    max-height: 300px;
  }

  .action-content {
    height: 450px;
  }
}

@media screen and (max-width: 960px) and (min-width: 601px) {
  .compliance-scrollbar {
    height: 240px;
    max-height: 240px;
  }

  .action-content {
    min-height: 225px;
  }
}
</style>
