<template>
  <v-card class="pl-4 pr-2 card-highlight rounded-lg" height="100%">
    <v-row class="pa-4">
      <!-- My Utilization Section -->
      <v-col
        cols="12"
        md="5"
        :sm="windowWidth <= 750 ? '12' : '5'"
        xs="12"
        class="my-utilization-card"
      >
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="purple-accent-2"
            :size="22"
            class="mr-2"
          />
          <span class="ml-2 text-h6 font-weight-bold text-primary">
            {{ $t("dashboard.myUtilization") }}
          </span>
        </div>

        <!-- Utilization Loading -->
        <div v-if="utilizationLoading">
          <v-skeleton-loader
            class="mx-auto mt-4"
            width="200"
            height="200"
            type="image"
            style="border-radius: 50%"
          />
          <v-skeleton-loader
            v-for="i in 2"
            :key="i"
            class="mx-auto d-flex justify-center pt-2"
            max-width="300"
            type="text"
          />
        </div>

        <!-- Utilization Error -->
        <div v-else-if="errorInUtilization" class="mt-6 pt-6">
          <ErrorContentCard
            :error-content="$t('dashboard.technicalDifficulties')"
            @refresh-triggered="getUtilizationDetails()"
          />
        </div>

        <!-- Utilization Chart -->
        <div v-else>
          <div id="chart" class="d-flex justify-center">
            <apexchart
              v-if="series.length > 0"
              width="250"
              type="donut"
              :options="chartOptions"
              :series="series"
            />
          </div>
          <div class="d-flex justify-center pt-2 text-body-2">
            <span class="pr-2">
              <v-icon size="15" color="green" class="font-weight-bold">
                far fa-circle
              </v-icon>
            </span>
            {{ $t("dashboard.myTotalWorkedDays") }}
            <span class="text-primary px-2 font-weight-bold">{{
              totalWorkedDays
            }}</span>
            <v-tooltip
              :text="$t('dashboard.utilizationTooltip')"
              location="right"
              max-width="300"
            >
              <template #activator="{ props }">
                <v-icon v-bind="props" size="17" color="info">
                  fas fa-info-circle
                </v-icon>
              </template>
            </v-tooltip>
          </div>
          <div
            class="d-flex justify-center pt-2 text-primary text-body-2 text-center"
          >
            {{ $t("dashboard.organizationWorkingDays") }}
            <span class="pl-2">{{ orgWorkingDays }}</span>
          </div>
        </div>
      </v-col>

      <!-- My Time Off Section -->
      <v-col
        cols="12"
        md="7"
        :sm="windowWidth <= 750 ? '12' : '7'"
        xs="12"
        class="pl-4"
      >
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="amber"
              :size="22"
              class="mr-2"
            />
            <span class="ml-2 text-h6 font-weight-bold text-primary">
              {{ $t("dashboard.myTimeOff") }}
            </span>
          </div>
          <div v-if="!timeOffListLoading" class="d-flex justify-center">
            <v-menu
              v-if="showApplyBtn && !errorInTimeOff"
              offset-y
              transition="scale-transition"
              style="z-index: 0 !important"
            >
              <template #activator="{ props }">
                <v-btn
                  v-bind="props"
                  size="small"
                  color="primary"
                  rounded="lg"
                  variant="elevated"
                >
                  <v-icon color="white" size="18" class="mr-1">
                    fas fa-plus-circle
                  </v-icon>
                  {{ $t("dashboard.apply") }}
                </v-btn>
              </template>
              <v-list>
                <v-list-item v-for="(item, index) in timeOffItems" :key="index">
                  <v-list-item-title
                    v-if="timeOffItemsRights[index]"
                    class="pa-2 text-caption timeoff-item-list cursor-pointer"
                    @click="fnRedirectTimeOffs(item)"
                  >
                    {{ item }}
                  </v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
          <div v-else class="mt-n5">
            <v-skeleton-loader class="mx-auto pt-4" type="table-cell" />
          </div>
        </div>

        <!-- Time Off Loading -->
        <div v-if="timeOffListLoading">
          <v-skeleton-loader
            v-for="i in 3"
            :key="i"
            class="mx-auto pt-4 mt-n2"
            type="list-item-avatar-two-line"
          />
        </div>

        <!-- Time Off Error or Empty -->
        <div
          v-else-if="errorInTimeOff || totalTimeoffLength === 0"
          class="d-flex align-center"
          style="height: 15rem"
        >
          <NoDataCardWithQuotes
            image-name="dashboard/timeoff-empty-image"
            :primary-bold-text="$t('dashboard.timeManagement')"
            :text-message="$t('dashboard.aKeyToSuccess')"
            :is-small-card="false"
            :card-type="errorInTimeOff ? 'error' : 'no-data'"
            :error-content="
              errorInTimeOff ? $t('dashboard.technicalDifficulties') : ''
            "
            image-size="100%"
            :is-show-image="windowWidth > 960"
            @refresh-triggered="fetchListLeavesAndShortTimeOff()"
          />
        </div>

        <!-- Time Off Content -->
        <div v-else class="pt-2">
          <perfect-scrollbar
            class="w-100 overflow-y-auto overflow-x-hidden timeoff-scrollbar"
          >
            <div class="timeoff-body">
              <!-- Leaves -->
              <PresentActionCards
                v-for="(leavesList, j) in filteredLeavesList"
                :key="j + '-leaves'"
                :card-property="fnMakeRandomColors()"
                :title="leavesList.leaveName"
                :sub-title-bold="
                  leavesList.totalDays + ' ' + $t('dashboard.days')
                "
                :sub-title-text="
                  leavesList.startDate && leavesList.endDate
                    ? leavesList.startDate + ' - ' + leavesList.endDate
                    : leavesList.startDate
                    ? leavesList.startDate
                    : leavesList.endDate
                "
                :action-text="leavesList.approvalStatus"
                :action-text-class="getStatusClass(leavesList.approvalStatus)"
                :list-index="j"
                icon-name="far fa-clock"
                :is-clickable="
                  employeeSelfServiceLeavesFormAccess ? true : false
                "
                @action-triggered="redirectToLeaves()"
              >
                <template #buttonAction>
                  <v-btn
                    size="small"
                    elevation="2"
                    variant="outlined"
                    @click.stop="onOpenApprovalFlowModal(leavesList.leaveId)"
                  >
                    <span class="text-caption">
                      {{ $t("dashboard.viewStatus") }}
                    </span>
                  </v-btn>
                </template>
              </PresentActionCards>

              <!-- Short Time Off -->
              <PresentActionCards
                v-for="(shortOffList, k) in filteredShortOffList"
                :key="k + '-shortoff'"
                :card-property="fnMakeRandomColors()"
                title="Short Time Off"
                :sub-title-bold="
                  shortOffList.totalHours + ' ' + $t('dashboard.hours')
                "
                :sub-title-text="fnFormShortTimeOffDate(shortOffList.startDate)"
                :action-text="shortOffList.approvalStatus"
                :action-text-class="getStatusClass(shortOffList.approvalStatus)"
                :list-index="k"
                icon-name="far fa-clock"
              />

              <!-- Comp Off -->
              <PresentActionCards
                v-for="(compOffList, l) in filteredCompOffList"
                :key="l + '-compoff'"
                :card-property="fnMakeRandomColors()"
                title="Compensatory Off"
                :sub-title-bold="
                  compOffList.duration + ' ' + $t('dashboard.days')
                "
                :sub-title-text="compOffList.compensatoryDate"
                :action-text="compOffList.approvalStatus"
                :action-text-class="getStatusClass(compOffList.approvalStatus)"
                :list-index="l"
                icon-name="far fa-clock"
              >
                <template v-if="compOffList?.processInstanceId" #buttonAction>
                  <v-btn
                    size="small"
                    elevation="2"
                    variant="outlined"
                    @click.stop="
                      onOpenApprovalFlowModalForCompOff(
                        compOffList.processInstanceId
                      )
                    "
                  >
                    {{ $t("dashboard.viewStatus") }}
                  </v-btn>
                </template>
              </PresentActionCards>
            </div>
          </perfect-scrollbar>
        </div>
      </v-col>
    </v-row>

    <!-- Approval Flow Modal -->
    <ApprovalFlowModal
      v-if="openApprovalModal"
      :open-approval-modal="openApprovalModal"
      :leave-id="selectedLeaveId"
      :task-id="selectedProcessId"
      @close-modal="closeApprovalModal"
    />
  </v-card>
</template>

<script>
// Components
import { defineAsyncComponent } from "vue";
const PresentActionCards = defineAsyncComponent(() =>
  import("@/components/helper-components/PresentActionCards.vue")
);
const ApprovalFlowModal = defineAsyncComponent(() =>
  import("@/components/custom-components/ApprovalFlowModal.vue")
);
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes.vue";
import ErrorContentCard from "@/components/helper-components/ErrorContentCard.vue";
// Queries
import { LIST_TIMEOFF_QUERY } from "@/graphql/dashboard/dashboardQueries";
import VueApexCharts from "vue3-apexcharts";

export default {
  name: "MyUtilizationAndTimeoff",

  components: {
    PresentActionCards,
    NoDataCardWithQuotes,
    ErrorContentCard,
    ApprovalFlowModal,
    apexchart: VueApexCharts,
  },

  props: {
    randomColors: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      leaveColorCode: [
        "#EF9A9A",
        "#CE93D8",
        "#FBC02D",
        "#81D4FA",
        "#78909C",
        "#3949AB",
        "#00897B",
        "#D81B60",
        "#1E88E5",
        "#CDDC39",
      ],
      series: [],
      chartOptions: {},
      totalWorkedDays: "",
      utilizationPercentage: "0%",
      orgWorkingDays: "",
      timeOffItems: ["Leave", "Short time off", "Compensatory off"],
      timeOffItemsRights: [0, 0, 0],
      showApplyBtn: false,
      errorInTimeOff: false,
      errorInUtilization: false,
      rolesCount: 0,
      utilizationLoading: false,
      openApprovalModal: false,
      selectedLeaveId: 0,
      selectedProcessId: "",
      timeOffListLoading: false,
    };
  },

  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    /**
     * Filter leaves based on the end date
     * endDate greater than current date (allowed status: Applied or Cancel Applied)
     * or otherwise all status are applicable
     */
    filteredLeavesList() {
      let levDetails = this.$store.state.dashboard.leavesLists || [];
      let leavesLists = levDetails.filter(
        (list) =>
          (new Date(list.endDate).getTime() >= new Date().getTime() ||
            list.approvalStatus === "Applied" ||
            list.approvalStatus === "Cancel Applied") &&
          list.approvalStatus !== "Returned"
      );
      return leavesLists;
    },
    /**
     * Filter short time off based on the end date
     * endDate greater than current date (allowed status: Applied or Cancel Applied)
     * or otherwise all status are applicable
     */
    filteredShortOffList() {
      let shortTimeoffDetails =
        this.$store.state.dashboard.shortTimeOffLists || [];
      let shortTimeoffLists = shortTimeoffDetails.filter(
        (list) =>
          (new Date(list.endDateTime).getTime() >= new Date().getTime() ||
            list.approvalStatus === "Applied" ||
            list.approvalStatus === "Cancel Applied") &&
          list.approvalStatus !== "Returned"
      );
      return shortTimeoffLists;
    },
    /**
     * Filter comp off based on the end date
     * compoff date greater than current date (allowed status: Applied or Cancel Applied)
     * or otherwise all status are applicable
     */
    filteredCompOffList() {
      let compoffDetails = this.$store.state.dashboard.compOffLists || [];
      let compOffLists = compoffDetails.filter(
        (list) =>
          (new Date(list.compensatoryDate).getTime() >= new Date().getTime() ||
            list.approvalStatus === "Applied" ||
            list.approvalStatus === "Cancel Applied") &&
          list.approvalStatus !== "Returned"
      );
      return compOffLists;
    },
    formIdBasedAccessRight() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    // Calculate total time off length
    totalTimeoffLength() {
      return (
        this.filteredLeavesList.length +
        this.filteredShortOffList.length +
        this.filteredCompOffList.length
      );
    },
    // Form Accresss for new Leaves form Employee Self Service
    employeeSelfServiceLeavesFormAccess() {
      let fAccess = this.formIdBasedAccessRight(333);
      if (fAccess && fAccess.accessRights && fAccess.accessRights["view"]) {
        return fAccess.accessRights;
      } else return false;
    },
    myUtilizationResponse() {
      //assigned leave utilization data from store
      //Assigning values from store by creating new object to avoid store data being get affected by local changes
      let utilization = Object.assign(
        {},
        this.$store.state.dashboard.employeeLeaveUtilization
      );
      return utilization;
    },
  },
  mounted() {
    this.getUtilizationDetails();
    this.fetchAccessRights();
    this.fetchListLeavesAndShortTimeOff();
  },

  methods: {
    fetchListLeavesAndShortTimeOff() {
      let vm = this;
      vm.timeOffListLoading = true;
      vm.errorInTimeOff = false;
      vm.$apollo
        .query({
          query: LIST_TIMEOFF_QUERY,
          client: "apolloClientC",
        })
        .then(({ data }) => {
          try {
            if (Object.keys(data).length !== 0) {
              let responseData = data.listLeavesAndShortTimeOff;
              if (responseData) {
                this.$store.commit(
                  "dashboard/UPDATE_LEAVES_LIST",
                  responseData.leaveDetails
                    ? JSON.parse(responseData.leaveDetails)
                    : []
                );
                this.$store.commit(
                  "dashboard/UPDATE_SHORT_TIMEOFF_LISTS",
                  responseData.shortTimeOffDetails
                    ? JSON.parse(responseData.shortTimeOffDetails)
                    : []
                );
                this.$store.commit(
                  "dashboard/UPDATE_COMPOFF_LISTS",
                  responseData.compensatoryOffDetails
                    ? JSON.parse(responseData.compensatoryOffDetails)
                    : []
                );
              }
            }
          } catch {
            vm.errorInTimeOff = true;
          }
          vm.timeOffListLoading = false;
        })
        .catch(() => {
          vm.timeOffListLoading = false;
          vm.errorInTimeOff = true;
        });
    },
    onOpenApprovalFlowModal(leaveId) {
      this.selectedLeaveId = leaveId;
      this.selectedProcessId = "";
      this.openApprovalModal = true;
    },
    onOpenApprovalFlowModalForCompOff(processId) {
      this.selectedProcessId = processId;
      this.selectedLeaveId = 0;
      this.openApprovalModal = true;
    },
    // Function to form random colors for actions card background, icon color and avatar background
    fnMakeRandomColors() {
      let finalColor =
        this.randomColors[Math.floor(Math.random() * this.randomColors.length)];
      return {
        background: "#F9FBFC", // Same background for all actions card
        bg: finalColor + "-lighten-4", // Combine vuetify base color with lighten-4 property
        color: finalColor, // Set vuetify base color as icon color
      };
    },
    // Get status class for approval status
    getStatusClass(status) {
      switch (status) {
        case "Approved":
          return "text-success";
        case "Rejected":
        case "Returned":
        case "Cancelled":
          return "text-error";
        case "Applied":
          return "text-info";
        default:
          return "text-warning";
      }
    },
    // Fetch utilization details
    getUtilizationDetails() {
      this.utilizationLoading = true;
      this.errorInUtilization = false;
      this.$store
        .dispatch("dashboard/fetchUtilization")
        .then(() => {
          this.fnFormatMyUtilization(); // form the chart based on data
          this.utilizationLoading = false;
        })
        .catch(() => {
          this.utilizationLoading = false;
          this.errorInUtilization = true;
        });
    },
    // function to fetch utilization list details
    fnFormatMyUtilization() {
      let self = this;
      try {
        let chartLabels = [];
        let leaveBalance = [];
        let themeColors = [];

        // check whether data is available for utilization
        if (Object.keys(this.myUtilizationResponse || {}).length) {
          // sample response
          let myUtilizationResponse = this.myUtilizationResponse;

          // leaves json
          let LeaveWithPercent = myUtilizationResponse.leaves;
          let LeaveWithPercentValues = [];

          // loop and get the percentage of leaves
          for (var value in LeaveWithPercent) {
            LeaveWithPercentValues.push(parseFloat(LeaveWithPercent[value]));
          }

          chartLabels = Object.keys(LeaveWithPercent);
          leaveBalance = LeaveWithPercentValues;
          themeColors = self.leaveColorCode;
          self.totalWorkedDays = myUtilizationResponse.totalWorkedDays;
          self.orgWorkingDays = myUtilizationResponse.totalBusinessWorkingDays;
          self.utilizationPercentage =
            myUtilizationResponse.utilizationPercentage;

          if (leaveBalance.length === 0) {
            leaveBalance[0] = 100;
            themeColors = ["#4CAF50"];
            chartLabels = ["Total Utilization"];
          }
        } else {
          leaveBalance[0] = 100;
          themeColors = ["#4CAF50"];
          chartLabels = ["Total Utilization"];
          self.utilizationPercentage = 100;
          self.totalWorkedDays = "-";
          self.orgWorkingDays = "-";
        }

        self.series = leaveBalance;
        self.chartOptions = {
          colors: themeColors,
          labels: chartLabels,
          tooltip: {
            enabled: true,
            y: {
              formatter: function (val) {
                return self.utilizationPercentage ? val + "%" : 0 + "%";
              },
            },
          },
          responsive: [
            {
              breakpoint: 576,
              options: {
                chart: {
                  width: 250,
                },
                legend: {
                  show: false,
                },
              },
            },

            {
              breakpoint: 768,
              options: {
                chart: {
                  width: 200,
                },
                legend: {
                  show: false,
                },
              },
            },
          ],
          legend: {
            show: false,
          },
          dataLabels: {
            enabled: false,
          },
          plotOptions: {
            pie: {
              donut: {
                size: "70%",
                labels: {
                  show: true,
                  name: {
                    show: false,
                  },
                  value: {
                    show: true,
                    fontSize: "1.5em",
                    fontFamily: "roboto",
                    color: "#66BB6A",
                    offsetY: 10,
                    formatter: function (val) {
                      return self.utilizationPercentage ? val + "%" : 0 + "%"; //return the value in percentage
                    },
                  },

                  total: {
                    show: true,
                    showAlways: true,
                    label: "Total",
                    color: "#18214d",
                    formatter: function () {
                      return parseFloat(self.utilizationPercentage) + "%";
                    },
                  },
                },
              },
            },
          },
        };
      } catch {
        self.errorInUtilization = true;
      }
    },
    // function to form start date and end date
    fnFormShortTimeOffDate(startDate) {
      if (startDate) {
        // date format is 01 Apr 2020 12:00 but we need to split date alone for short timeoff
        let startPeriod = startDate.split(" "); // returns ["01", "Apr", "2020", "12:00"]
        return startPeriod[0] + " " + startPeriod[1] + " " + startPeriod[2]; // join first three array params
      }
    },
    // function called when timeoff forms list is clicked in the menu
    fnRedirectTimeOffs(item) {
      if (item === "Leave") {
        var timeoff_url =
          this.baseUrl + "v3/employee-self-service/leave-request";
        window.open(timeoff_url);
      } else if (item === "Short time off") {
        var short_timeoff_orl =
          this.baseUrl + "v3/employee-self-service/short-time-off";
        window.open(short_timeoff_orl);
      } else {
        var compoff_url =
          this.baseUrl + "v3/employee-self-service/compensatory-off";
        window.open(compoff_url);
      }
    },
    // function to fetch access rights to display apply button(list of timeoff)
    fetchAccessRights() {
      // array for forms for which rights has to be fetched
      let formsArray = ["leaves", "short-time-off", "compensatory-off"],
        rightsArray = this.timeOffItemsRights,
        rolesCount = 0; // check for apply btn presence
      // check the rights exists for the forms
      for (var i in formsArray) {
        var formRights = this.accessRights(formsArray[i]);
        if (formRights && formRights.accessRights.add) {
          rightsArray[i] = 1; // to check rights to list in menu
          rolesCount++;
        }
      }
      this.rolesCount = rolesCount;
      // to show apply button when any one of these forms has rights.
      if (rolesCount <= 3 && rolesCount >= 1) {
        this.showApplyBtn = true;
      }
    },
    // Close approval modal
    closeApprovalModal() {
      this.openApprovalModal = false;
      this.selectedLeaveId = 0;
      this.selectedProcessId = "";
    },
    redirectToLeaves() {
      if (this.employeeSelfServiceLeavesFormAccess) {
        let url = this.baseUrl + "v3/employee-self-service/leave-request";
        window.open(url, "_blank");
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style lang="scss" scoped>
.primary-main-title {
  color: rgb(var(--v-theme-primary));
  font-size: 1.25em;
  line-height: 1.7 !important;
}

.timeoff-body {
  height: 250px;
}

.timeoff-item-list {
  border-radius: 8px;
  transition: background-color 0.2s;
}

.timeoff-item-list:hover {
  background-color: #f5f5f5;
}

/* Timeoff scrollbar height - responsive */
.timeoff-scrollbar {
  height: 280px;
  max-height: 280px;
}

/* Perfect scrollbar styling */
:deep(.ps) {
  overflow-x: hidden !important;
}

:deep(.ps__rail-y) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-y) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

:deep(.ps__rail-x) {
  display: none !important;
}

/* Responsive adjustments */
@media screen and (max-width: 750px) {
  .my-utilization-card {
    border-right: none;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }

  .timeoff-body {
    height: 300px;
  }
}

@media screen and (max-width: 600px) {
  .timeoff-scrollbar {
    height: 280px;
    max-height: 300px;
  }

  .timeoff-body {
    height: 400px;
  }
}

@media screen and (max-width: 960px) and (min-width: 601px) {
  .timeoff-scrollbar {
    height: 240px;
    max-height: 240px;
  }
}
</style>
