<template>
  <div>
    <v-dialog
      v-model="openAlertModal"
      scrollable
      persistent
      class="rounded-lg"
      :width="!showTaxRegimeChooseCard ? '850px' : '700px'"
    >
      <v-card v-if="showTaxRegimeChooseCard" class="rounded-lg">
        <div class="select-tax-regime-header">
          <v-img
            alt="money-bag"
            width="35"
            height="35"
            :src="getMoneyBagImageUrl"
            class="ma-4 mr-2"
          />

          <p class="text-body-1 text-primary font-weight-bold px-4 py-3 ma-0">
            {{ $t("dashboard.compareTaxOutgo") }}
          </p>

          <v-icon
            color="primary"
            class="pa-4 pl-2"
            size="20"
            @click="$emit('close-tax-notify-modal')"
          >
            fas fa-times
          </v-icon>
        </div>
        <div style="border: 2px solid #f0d527">
          <div class="d-flex pa-2" style="background: #fffad8">
            <v-icon color="#FFDD00" size="35">
              fas fa-exclamation-triangle
            </v-icon>
            <div class="px-7 text-body-1 text-primary">
              {{ $t("dashboard.taxRegimeNotification") }}
            </div>
          </div>
        </div>
        <v-card-text class="pb-0">
          <v-container class="pb-0">
            <div
              :style="
                windowWidth < 500
                  ? 'padding:1em 2em'
                  : 'padding:2em 2em 0em 2em'
              "
            >
              <v-row class="flex-column">
                <p
                  class="text-primary font-weight-bold"
                  style="font-size: 1.3em"
                >
                  {{ $t("dashboard.totalIncomeFinancialYear") }}
                </p>

                <p class="text-primary">
                  {{ regimeComparisonDetails.Currency_Symbol }}
                  {{
                    regimeComparisonDetails.Total_Income
                      ? regimeComparisonDetails.Total_Income
                      : "-"
                  }}
                </p>
              </v-row>
              <v-row class="">
                <v-radio-group v-model="selectedTaxRegime" style="width: 100%">
                  <v-card
                    v-for="(regime, index) in regimeClassification"
                    :key="index"
                    class="tax-block-box"
                    :class="{
                      'selected-regime': regime.value === selectedTaxRegime,
                      'mb-0': regime.value === 'DO_NOT_REMIND',
                    }"
                  >
                    <div>
                      <v-radio :value="regime.value" color="primary" />
                    </div>

                    <div class="d-flex flex-column pl-4">
                      <div class="d-flex align-baseline flex-wrap mb-4">
                        <p
                          class="text-primary font-weight-bold mb-0 mr-2"
                          :style="
                            regime.value === 'DO_NOT_REMIND'
                              ? 'margin:1em 0em;font-size: 1.1em;'
                              : 'font-size: 1.1em;'
                          "
                        >
                          {{ regime.regime }}
                          <span
                            v-if="regime.value === 'DO_NOT_REMIND'"
                            class="text-error font-weight-bold"
                            style="font-size: 16px"
                          >
                            ({{ $t("dashboard.stopNotificationWarning") }})
                          </span>
                        </p>
                        <v-chip
                          v-if="regime.value === recommendedRegime"
                          color="cyan"
                          label
                          size="small"
                          text-color="white"
                        >
                          {{ $t("dashboard.recommended") }}
                        </v-chip>
                      </div>
                      <p
                        v-if="regime.value !== 'DO_NOT_REMIND'"
                        class="text-primary font-weight-medium mb-2"
                        style="font-size: 1.1em"
                      >
                        {{ $t("dashboard.totalTax") }} :
                        {{ regimeComparisonDetails.Currency_Symbol }}
                        {{
                          regime.value === "OLD_REGIME"
                            ? regimeComparisonDetails.Old_Regime_Tax
                            : regimeComparisonDetails.New_Regime_Tax
                        }}
                      </p>
                    </div>
                  </v-card>
                </v-radio-group>
              </v-row>
            </div>
          </v-container>
        </v-card-text>
        <div
          :style="
            windowWidth < 500 ? 'padding:1em 2em' : 'padding:2em 2em 0em 2em'
          "
          class="mt-n4"
        >
          <div
            class="d-flex"
            :class="windowWidth <= 500 ? 'flex-column mt-1' : 'align-center'"
          >
            <span class="pr-4 text-primary font-weight-bold">
              {{ $t("dashboard.businessIncomeQuestion") }}
            </span>
            <v-radio-group
              v-model="businessIncome"
              inline
              :class="windowWidth <= 500 ? 'mt-n1' : ''"
            >
              <v-radio color="primary" value="Yes">
                <template #label>
                  <div class="text-primary font-weight-medium">
                    {{ $t("common.yes") }}
                  </div>
                </template>
              </v-radio>
              <v-radio color="primary" value="No">
                <template #label>
                  <div class="text-primary font-weight-medium">
                    {{ $t("common.no") }}
                  </div>
                </template>
              </v-radio>
            </v-radio-group>
          </div>
          <v-checkbox
            v-model="acceptAcknowledgement"
            color="primary"
            class="mt-0"
            hide-details
          >
            <template #label>
              <div class="text-primary font-weight-medium">
                {{
                  businessIncome === "Yes"
                    ? $t("dashboard.businessIncomeAcknowledgement")
                    : $t("dashboard.regimeChangeAcknowledgement")
                }}
              </div>
            </template>
          </v-checkbox>
        </div>
        <v-card-actions class="d-flex justify-center my-3">
          <v-btn
            variant="outlined"
            rounded="lg"
            class="mr-3"
            @click="$emit('close-tax-notify-modal')"
          >
            {{ $t("common.cancel") }}
          </v-btn>
          <v-btn
            color="primary"
            rounded="lg"
            variant="elevated"
            :disabled="!acceptAcknowledgement"
            :loading="isLoading"
            @click="submitSelectedTaxRegime"
          >
            {{ $t("common.submit") }}
          </v-btn>
        </v-card-actions>
        <v-overlay absolute :value="isLoading" :opacity="'0.9'" color="#fff">
          <v-progress-circular color="primary" indeterminate size="64" />
        </v-overlay>
      </v-card>

      <!-- Tax Declaration Video Card -->
      <v-card v-else class="pa-2 rounded-lg">
        <v-card-text class="light-blue-lighten-4">
          <div class="d-flex justify-end mt-n3 mr-n4">
            <v-icon
              color="primary"
              class="pa-4 pl-2"
              @click="$emit('close-tax-notify-modal')"
            >
              fas fa-times
            </v-icon>
          </div>
          <v-row>
            <v-col cols="12">
              <div class="text-h6 text-primary font-weight-bold my-4">
                {{ $t("dashboard.taxDeclarationHelp") }}
              </div>
              <div
                class="d-flex justify-center my-4"
                :class="windowWidth <= 500 ? 'flex-column' : ''"
              >
                <v-btn
                  color="primary"
                  rounded="lg"
                  variant="elevated"
                  @click="showTaxRegimeChooseCard = true"
                >
                  {{ $t("dashboard.noInvestmentToDeclare") }}
                </v-btn>
                <!-- </div>
              <div class="mt-4"> -->
                <v-btn
                  color="primary"
                  rounded="lg"
                  :class="windowWidth <= 500 ? 'mt-4' : 'ml-4'"
                  variant="elevated"
                  @click="openTaxDeclarationPage()"
                >
                  {{ $t("dashboard.proceedWithITDeclaration") }}
                </v-btn>
              </div>
            </v-col>
            <v-col cols="12" md="6" sm="12" class="d-none">
              <v-card
                class="pa-2 common-box-shadow rounded-lg d-flex justify-center"
              >
                <v-video
                  aspect-ratio="16/9"
                  src="https://www.youtube.com/watch?v=0XeUp6ZYhzA"
                />
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: "TaxRegimeComparisonModal",

  props: {
    openTaxComparisonModal: {
      type: Boolean,
      required: true,
    },
  },

  emits: ["close-tax-notify-modal", "open-snackbar"],

  data() {
    return {
      selectedTaxRegime: "",
      regimeClassification: [
        {
          value: "OLD_REGIME",
          regime: "Old Regime",
        },
        {
          value: "NEW_REGIME",
          regime: "New Regime",
        },
        {
          value: "DO_NOT_REMIND",
          regime: "Stop the notification",
        },
      ],
      recommendedRegime: "",
      isLoading: false,
      openAlertModal: false,
      acceptAcknowledgement: false,
      showTaxRegimeChooseCard: false,
      businessIncome: "No",
    };
  },

  computed: {
    // Retrieve tax amount calculated data from store
    regimeComparisonDetails() {
      return this.$store.state.regimeComparisonDetails;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    baseUrl() {
      // return "https://fieldforce.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    // To know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    // Load webp if browser support webp image format or load png
    getMoneyBagImageUrl() {
      const extension = this.isBrowserSupportWebp ? "webp" : "png";
      return require(`@/assets/images/layout/money-bag.${extension}`);
    },
  },

  mounted() {
    this.openAlertModal = this.openTaxComparisonModal;
    // Based on the low tax amount highlight that particular regime by select and with recommend label
    if (
      this.regimeComparisonDetails.Old_Regime_Tax <
      this.regimeComparisonDetails.New_Regime_Tax
    ) {
      this.selectedTaxRegime = "OLD_REGIME";
      this.recommendedRegime = "OLD_REGIME";
    } else {
      this.selectedTaxRegime = "NEW_REGIME";
      this.recommendedRegime = "NEW_REGIME";
    }
  },

  methods: {
    openTaxDeclarationPage() {
      let redirectionUrl = this.baseUrl + "payroll/tax-declarations";
      window.open(redirectionUrl, "_blank");
    },
    // submit action after selecting one of the tax regime or selected stop notification option
    submitSelectedTaxRegime() {
      this.isLoading = true;
      let snackbarData = {
        isOpen: true,
        message: this.$t("dashboard.taxRegimeUpdateError"),
        type: "warning",
      };

      try {
        let vm = this;

        // Create API object for the standardized dispatch pattern
        const apiObj = {
          url: vm.baseUrl + "payroll/tax-rules/update-employee-tax-regime",
          type: "POST",
          dataType: "json",
          data: {
            REGIME_TYPE: vm.selectedTaxRegime,
            currentYearBusinessIncomeExist: vm.businessIncome,
            requestResource: "HRAPPUI",
          },
        };

        vm.$store
          .dispatch("triggerControllerFunction", apiObj)
          .then((regimeResponseData) => {
            vm.isLoading = false;
            vm.$emit("close-tax-notify-modal");
            const { msg, success, type } = regimeResponseData;
            if (regimeResponseData && msg) {
              //on successful selection hide the tax selection modal and top notification bar
              if (success) {
                vm.$store.commit("SHOW_TAX_REGIME_SELECTION_OPTION", false);
                snackbarData.message = msg;
                snackbarData.type = type;
                vm.$emit("open-snackbar", snackbarData);
              } else {
                //incase of session expired we get expired message based on that clear user token and redirect to auth
                if (msg === "Session Expired") {
                  vm.$store.dispatch("clearUserLock");
                } else {
                  //on any error hide only the tax choosing modal and show error message
                  snackbarData.message = msg;
                  snackbarData.type = type;
                  vm.$emit("open-snackbar", snackbarData);
                }
              }
            } else {
              vm.$emit("open-snackbar", snackbarData);
            }
          })
          .catch((updateTaxRegimeError) => {
            vm.isLoading = false;
            vm.$emit("close-tax-notify-modal");
            //on error we hide the tax regime selection modal
            //on session expire error we clear session and redirect to auth
            if (updateTaxRegimeError.status === 200) {
              vm.$store.dispatch("clearUserLock");
            } else {
              vm.$emit("open-snackbar", snackbarData);
            }
          });
      } catch {
        this.isLoading = false;
        this.$emit("close-tax-notify-modal");
        this.$emit("open-snackbar", snackbarData);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.select-tax-regime-header {
  background-color: rgb(var(--v-theme-primary-lighten-5)) !important;
  color: black;
  font-size: 1.3em;
  text-transform: inherit;
  font-weight: 500;
  display: flex;
  align-items: flex-start;
  opacity: 0.8;
}

.tax-block-box {
  display: flex;
  flex-direction: row;
  margin-bottom: 25px;
  align-items: center;
  padding: 0.5em 1.5em;
  border-radius: 0.7em;
  box-shadow: 1px 2px 22px -6px #b4b4b4 !important;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tax-block-box:hover {
  box-shadow: 1px 2px 30px -4px #999 !important;
}

.selected-regime {
  border: 3px solid rgb(var(--v-theme-primary));
}

.embed-responsive-item {
  min-height: 260px;
  min-width: 350px;
  border-radius: 8px;
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .embed-responsive-item {
    min-height: 200px;
    min-width: 280px;
  }

  .tax-block-box {
    padding: 0.5em 1em;
    margin-bottom: 15px;
  }

  .select-tax-regime-header {
    font-size: 1.1em;
  }
}
</style>
