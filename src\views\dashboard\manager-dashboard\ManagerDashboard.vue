<template>
  <div>
    <!--Dashboard card designs  -->
    <v-container fluid class="main-container">
      <v-row class="mb-2">
        <v-card width="100%" class="pa-5 d-flex card-highlight rounded-lg">
          <!-- Employee present & absent Loading card -->
          <v-row v-if="isEmpAvailabilityLoading" justify="center">
            <v-col v-for="i in 5" :key="i" cols="2" class="pa-1 px-3">
              <v-skeleton-loader class="mx-auto" height="55" type="image" />
            </v-col>
          </v-row>

          <!-- Employee present & absent Count Error Card -->
          <v-row v-else-if="errorInEmpAvailabilityCount">
            <v-col cols="12" class="pa-1 px-3 d-flex justify-center">
              <StatisticsCardWithBg
                id="admindashboard_presentemployee_refresh"
                image-name="admin-dashboard/present-employee"
                :is-error="true"
                @refresh-action="refreshEmpAvailability"
              />
            </v-col>
          </v-row>

          <v-row v-else justify="center">
            <v-col
              :cols="windowWidth > 380 ? 2 : 12"
              :class="windowWidth > 380 ? 'pa-0 pr-1' : 'pa-0 pb-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_present_statistics"
                image-name="admin-dashboard/present-employee"
                :title="presentCount"
                subtitle="Present"
                card-bg-color="light-green-lighten-5"
                :is-clickable="true"
                image-width="25"
                @card-click-action="redirectToAttendanceBox()"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 2 : 12"
              :class="windowWidth > 380 ? 'pa-0 px-1' : 'pa-0 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_absent_statistics"
                image-name="admin-dashboard/absent-time"
                card-bg-color="red-lighten-5"
                :title="absentCount"
                subtitle="Absent"
                :is-clickable="true"
                image-width="25"
                @card-click-action="redirectToAttendanceBox()"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 2 : 12"
              :class="windowWidth > 380 ? 'pa-0 px-1' : 'pa-0 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_leave_statistics"
                image-name="admin-dashboard/leave-timer"
                card-bg-color="pink-lighten-5"
                :title="leaveCount"
                subtitle="Leave"
                image-width="25"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 2 : 12"
              :class="windowWidth > 380 ? 'pa-0 pl-1' : 'pa-0 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_leave_statistics"
                image-name="admin-dashboard/person-with-top-clock"
                icon-name="fas fa-briefcase"
                card-bg-color="purple-lighten-5"
                :title="compOffCount"
                subtitle="Compoff"
                icon-size="28"
              />
            </v-col>
            <v-col
              :cols="windowWidth > 380 ? 2 : 12"
              :class="windowWidth > 380 ? 'pa-0 pl-1' : 'pa-0 py-1'"
            >
              <StatisticsCardWithBg
                id="admindashboard_leave_statistics"
                image-name="admin-dashboard/person-with-top-clock"
                icon-name="fas fa-calendar"
                card-bg-color="blue-grey-lighten-5"
                :title="shiftNotScheduledCount"
                subtitle="Open Shift"
                icon-size="28"
              />
            </v-col>
          </v-row>
        </v-card>
      </v-row>
      <section v-if="!isMobileView">
        <v-row>
          <!-- My Team Action-->
          <v-col cols="12" sm="12" md="12" lg="12" xl="12" class="card-spacing">
            <MyTeamAction calling-from="manager-dashboard" />
          </v-col>
        </v-row>
      </section>
      <section v-else class="pa-2">
        <v-carousel
          :show-arrows="false"
          height="100%"
          light
          hide-delimiter-background
        >
          <v-carousel-item>
            <v-sheet color="white" class="rounded-lg">
              <MyTeamAction calling-from="manager-dashboard" />
            </v-sheet>
          </v-carousel-item>
        </v-carousel>
      </section>
    </v-container>
  </div>
</template>

<script>
// import axios from "axios";
import MyTeamAction from "../admin-dashboard/MyTeamAction.vue";
import StatisticsCardWithBg from "@/components/helper-components/StatisticsCardWithBg.vue";

export default {
  name: "ManagerDashboard",

  components: { MyTeamAction, StatisticsCardWithBg },

  data: () => ({
    presentCount: 0,
    absentCount: 0,
    leaveCount: 0,
    compOffCount: 0,
    shiftNotScheduledCount: 0,
    errorInEmpAvailabilityCount: false,
    isEmpAvailabilityLoading: false,
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    baseUrl() {
      // return "https://fieldforce.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
  },

  mounted() {
    this.getPresentEmployeeCount();
  },

  methods: {
    //redirect to attendance box to see present and absent count of employees
    redirectToAttendanceBox() {
      var redirectionUrl = this.baseUrl + "employees/attendance/attendance-box";
      window.open(redirectionUrl);
    },
    refreshEmpAvailability() {
      this.errorInEmpAvailabilityCount = false;
      this.getPresentEmployeeCount();
    },

    async getPresentEmployeeCount() {
      let vm = this;
      try {
        vm.isEmpAvailabilityLoading = true;
        const payload = {
          url:
            vm.baseUrl +
            "employees/attendance/get-employee-details/isAction/Present%20Employees",
          type: "POST",
          dataType: "json",
          data: {
            requestResource: "HRAPPUI",
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          payload
        );
        const empAvailabilityData = response;
        if (empAvailabilityData && empAvailabilityData.success) {
          vm.presentCount = empAvailabilityData.presentCount || 0;
          vm.absentCount = empAvailabilityData.absentCount || 0;
          vm.compOffCount = empAvailabilityData.compOffCount || 0;
          vm.leaveCount = empAvailabilityData.leaveCount || 0;
          vm.shiftNotScheduledCount =
            empAvailabilityData.shiftNotScheduledCount || 0;
        } else {
          // session expired case
          if (empAvailabilityData.msg === "Session Expired") {
            vm.userLogout();
          } else {
            vm.errorInEmpAvailabilityCount = true;
          }
          vm.handleListError();
        }
      } catch (error) {
        // In case API returns 200 but session expired
        if (error.response && error.response.status === 200) {
          vm.userLogout();
        } else {
          vm.errorInEmpAvailabilityCount = true;
        }
        vm.handleListError(error);
      } finally {
        vm.isEmpAvailabilityLoading = false;
      }
    },
    handleListError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "dashboard",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    //incase of session expired error got from backend redirect to auth
    userLogout() {
      this.$store.dispatch("clearUserLock");
    },
  },
};
</script>
