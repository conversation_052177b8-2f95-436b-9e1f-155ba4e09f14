<template>
  <div>
    <AppFetchErrorScreen
      v-if="itemList.length === 0"
      key="no-results-screen"
      :main-title="$t('dataLossPrevention.noRecordsMatch')"
      image-name="common/no-records"
    />
    <div v-else>
      <v-data-table
        :key="key"
        :headers="tableHeaders"
        :items="itemList"
        fixed-header
        :height="$store.getters.getTableHeightBasedOnScreenSize(290, itemList)"
        :items-per-page="50"
        :items-per-page-options="itemsPerPageOptions"
        :footer-props="footerProps"
        hide-default-footer
      >
        <template v-slot:bottom>
          <div class="v-data-table-footer">
            <div class="v-data-table-footer__items-per-page">
              <span>{{ $t("dataLossPrevention.itemsPerPage") }}:</span>
              <v-select
                :items="itemsPerPageOptions"
                :model-value="itemsPerPage"
                @update:model-value="updateItemsPerPage"
                density="compact"
                variant="outlined"
                hide-details
                class="ml-2"
                style="width: 80px"
              ></v-select>
            </div>
            <div class="v-data-table-footer__info">
              <div class="v-data-table-footer__pagination">
                {{ paginationText }}
              </div>
            </div>
            <div class="v-data-table-footer__pagination">
              <v-pagination
                :model-value="page"
                @update:model-value="updatePage"
                :length="pageCount"
                :total-visible="5"
                density="comfortable"
              ></v-pagination>
            </div>
          </div>
        </template>
        <template #[`header.productiveTime`]="{}">
          <v-tooltip
            :text="$t('dataLossPrevention.productiveTooltip')"
            location="top"
            max-width="300"
          >
            <template v-slot:activator="{ props }">
              <v-hover>
                <template v-slot:default="{ isHovering, props: hoverProps }">
                  <div class="d-flex align-center" v-bind="props">
                    <span v-bind="{ ...props, ...hoverProps }">{{
                      $t("dataLossPrevention.productive")
                    }}</span>
                    <v-icon
                      :color="isHovering ? 'grey' : 'white'"
                      class="fas fa-arrow-up ml-2"
                      size="12"
                    />
                  </div>
                </template>
              </v-hover>
            </template>
          </v-tooltip>
        </template>
        <template #[`header.unProductiveTime`]="{}">
          <v-tooltip
            :text="$t('dataLossPrevention.unproductiveTooltip')"
            location="top"
            max-width="300"
          >
            <template v-slot:activator="{ props }">
              <v-hover>
                <template v-slot:default="{ isHovering, props: hoverProps }">
                  <div class="d-flex align-center" v-bind="props">
                    <span v-bind="{ ...props, ...hoverProps }">{{
                      $t("dataLossPrevention.unproductive")
                    }}</span>
                    <v-icon
                      :color="isHovering ? 'grey' : 'white'"
                      class="fas fa-arrow-up ml-2"
                      size="12"
                    />
                  </div>
                </template>
              </v-hover>
            </template>
          </v-tooltip>
        </template>
        <template #[`header.neutralTime`]="{}">
          <v-tooltip
            :text="$t('dataLossPrevention.neutralTooltip')"
            location="top"
            max-width="300"
          >
            <template v-slot:activator="{ props }">
              <v-hover>
                <template v-slot:default="{ isHovering, props: hoverProps }">
                  <div class="d-flex align-center" v-bind="props">
                    <span v-bind="{ ...props, ...hoverProps }">{{
                      $t("dataLossPrevention.neutral")
                    }}</span>
                    <v-icon
                      :color="isHovering ? 'grey' : 'white'"
                      class="fas fa-arrow-up ml-2"
                      size="12"
                    />
                  </div>
                </template>
              </v-hover>
            </template>
          </v-tooltip>
        </template>
        <template #[`header.userProductivityPercentage`]="{}">
          <v-tooltip
            :text="$t('dataLossPrevention.userProductivityTooltip')"
            location="top"
            max-width="300"
          >
            <template v-slot:activator="{ props }">
              <v-hover>
                <template v-slot:default="{ isHovering, props: hoverProps }">
                  <div class="d-flex align-center" v-bind="props">
                    <span v-bind="{ ...props, ...hoverProps }">{{
                      $t("dataLossPrevention.userProductivity")
                    }}</span>
                    <v-icon
                      :color="isHovering ? 'grey' : 'white'"
                      class="fas fa-arrow-up ml-2"
                      size="12"
                    />
                  </div>
                </template>
              </v-hover>
            </template>
          </v-tooltip>
        </template>
        <template v-slot:item="{ item }">
          <tr
            class="data-table-tr bg-white cursor-pointer"
            :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
            @click="$emit('open-details', item)"
          >
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1 mt-2"
              >
                {{ $t("dataLossPrevention.employeeName") }}
              </div>
              <section class="d-flex align-items-center">
                <div
                  v-if="!isMobileView"
                  class="activity-status-border"
                  :class="
                    item.trackingStatus?.toLowerCase() === 'tracking' ||
                    item.trackingStatus?.toLowerCase() === 'tracked'
                      ? 'active-status-border'
                      : item.trackingStatus?.toLowerCase() === 'idle'
                      ? 'idle-status-border'
                      : 'inactive-status-border'
                  "
                ></div>

                <v-tooltip
                  :text="item.employeeName"
                  location="bottom"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-body-2 font-weight-light text-primary text-truncate"
                      :style="
                        !isMobileView
                          ? 'max-width: 300px; '
                          : 'max-width: 200px; '
                      "
                      v-bind="props"
                    >
                      {{ checkNullValue(item.employeeName) }}
                      <div v-if="item?.userDefinedEmployeeId" class="text-grey">
                        {{ checkNullValue(item.userDefinedEmployeeId) }}
                      </div>
                    </div>
                  </template>
                </v-tooltip>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1 mt-2"
              >
                {{ $t("dataLossPrevention.productive") }}
              </div>
              <section>
                <v-skeleton-loader
                  v-if="teamProductivityLoading"
                  type="text"
                ></v-skeleton-loader>
                <div
                  v-else
                  class="text-body-2 font-weight-light text-truncate"
                  :style="{
                    color: '#23C601',
                    maxWidth: !isMobileView ? '300px' : '200px',
                  }"
                >
                  {{ checkNullDateHoursValues(item.productiveTime) + " h" }}
                </div>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1 mt-2"
              >
                {{ $t("dataLossPrevention.unproductive") }}
              </div>
              <section>
                <v-skeleton-loader
                  v-if="teamProductivityLoading"
                  type="text"
                ></v-skeleton-loader>
                <div
                  v-else
                  class="text-body-2 font-weight-light text-truncate"
                  :style="{
                    color: '#FF4444',
                    maxWidth: !isMobileView ? '300px' : '200px',
                  }"
                >
                  {{ checkNullDateHoursValues(item.unProductiveTime) + " h" }}
                </div>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1 mt-2"
              >
                {{ $t("dataLossPrevention.neutral") }}
              </div>
              <section>
                <v-skeleton-loader
                  v-if="teamProductivityLoading"
                  type="text"
                ></v-skeleton-loader>
                <div
                  v-else
                  class="text-body-2 font-weight-light text-truncate"
                  :style="{
                    color: '#FF7CAE',
                    maxWidth: !isMobileView ? '300px' : '200px',
                  }"
                >
                  {{ checkNullDateHoursValues(item.neutralTime) + " h" }}
                </div>
              </section>
            </td>
            <td
              v-if="checkSelectedDateIsCurrentDate"
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1 mt-2"
              >
                {{ $t("dataLossPrevention.userProductivity") }}
              </div>
              <section>
                <div
                  class="text-body-2 font-weight-light text-truncate"
                  :style="
                    !isMobileView ? 'max-width: 300px; ' : 'max-width: 200px; '
                  "
                >
                  {{
                    item.userProductivityPercentage
                      ? item.userProductivityPercentage + "%"
                      : "0%"
                  }}
                </div>
              </section>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
  </div>
</template>
<script>
import { checkNullValue } from "@/helper";
export default {
  name: "ListEmployees",
  props: {
    itemList: {
      type: Array,
      required: true,
      default: () => [],
    },
    productivityList: {
      type: Array,
      required: true,
      default: () => [],
    },
    teamProductivityLoading: {
      type: Boolean,
      default: false,
    },
    selectedDate: {
      type: Date,
      required: true,
    },
    hasSummary: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      key: 0, // Force re-render key
      itemsPerPage: 50,
      page: 1,
    };
  },
  watch: {
    "$i18n.locale"() {
      // Force component re-render when language changes
      this.key += 1;
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      let tabs = [
        {
          title: this.$t("dataLossPrevention.employeeName"),
          key: "employeeName",
        },
        {
          title: this.$t("dataLossPrevention.productive"),
          key: "productiveTime",
        },
        {
          title: this.$t("dataLossPrevention.unproductive"),
          key: "unProductiveTime",
        },
        { title: this.$t("dataLossPrevention.neutral"), key: "neutralTime" },
      ];
      if (this.checkSelectedDateIsCurrentDate)
        tabs.push({
          title: this.$t("dataLossPrevention.userProductivity"),
          key: "userProductivityPercentage",
        });
      return tabs;
    },
    checkSelectedDateIsCurrentDate() {
      return this.hasSummary?.toLowerCase() === "yes";
    },
    checkNullDateHoursValues() {
      return (dateHour) => {
        let formatDate = dateHour;
        formatDate = formatDate ? formatDate.toString().toLowerCase() : "";
        if (formatDate.includes("nan") || !formatDate) {
          return "00:00";
        } else {
          return dateHour;
        }
      };
    },
    itemsPerPageOptions() {
      return [
        { value: 50, title: "50" },
        { value: 100, title: "100" },
        {
          value: -1,
          title: this.$t("dataLossPrevention.itemsPerPageAll"),
        },
      ];
    },
    footerProps() {
      return {
        itemsPerPageText: this.$t("dataLossPrevention.itemsPerPage"),
        itemsPerPageAllText: this.$t("dataLossPrevention.itemsPerPageAll"),
        pageText: `{0}-{1} ${this.$t("dataLossPrevention.of")} {2}`,
      };
    },
    pageCount() {
      return Math.ceil(this.itemList.length / this.itemsPerPage);
    },
    paginationText() {
      const start = (this.page - 1) * this.itemsPerPage + 1;
      const end = Math.min(this.page * this.itemsPerPage, this.itemList.length);
      return `${start}-${end} ${this.$t("dataLossPrevention.of")} ${
        this.itemList.length
      }`;
    },
  },
  methods: {
    checkNullValue,
    updateItemsPerPage(value) {
      this.itemsPerPage = value;
      this.page = 1; // Reset to first page
    },
    updatePage(value) {
      this.page = value;
    },
  },
};
</script>
<style scoped>
.activity-status-border {
  margin-left: -1.1em;
  margin-right: 10px;
  height: 3.8em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}

.active-status-border {
  border-left: 7px solid #4caf50;
}

.inactive-status-border {
  border-left: 7px solid #f44336;
}

.idle-status-border {
  border-left: 7px solid #ffc107;
}

.v-data-table-footer {
  display: flex;
  align-items: center;
  padding: 0 16px;
  min-height: 52px;
  border-top: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.v-data-table-footer__items-per-page {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.v-data-table-footer__info {
  display: flex;
  align-items: center;
}

.v-data-table-footer__pagination {
  font-size: 0.875rem;
  color: rgba(var(--v-theme-on-surface), var(--v-medium-emphasis-opacity));
}
</style>
