<template>
  <div>
    <v-container fluid class="logger-container">
      <div v-if="formAccess && formAccess.view">
        <div v-if="isLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 3" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="searchList.length === 0"
          key="no-results-screen"
          :main-title="emptyScenarioMsg"
          :isSmallImage="false"
          :image-name="'common/no-records'"
        >
          <template #contentSlot>
            <div style="max-width: 80%; min-width: 50%">
              <v-row class="rounded-lg pa-5 mb-2" :style="'background: white'">
                <v-col cols="12">
                  <div
                    v-if="formAccess.admin"
                    class="d-flex align-center justify-center"
                  >
                    <img
                      :src="getFooterImage"
                      style="width: 50px; height: auto"
                      class="ml-n5 mr-5"
                      alt="idea-bulb"
                    />
                    {{ $t("dataLossPrevention.noKeyLogsAvailable") }}
                    <span
                      class="text-primary text-decoration-underline ml-1"
                      @click="handleHereClick()"
                    >
                      {{ $t("dataLossPrevention.configureHere") }}</span
                    >
                  </div>
                  <NotesCard
                    v-else-if="formAccess.isManager"
                    :notes="$t('dataLossPrevention.talkToAdmin')"
                    backgroundColor="transparent"
                    class="mb-4"
                  />
                </v-col>
              </v-row>
              <v-row class="rounded-lg pa-5 mb-4">
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <div>
                    <v-menu
                      v-model="activityDate"
                      :close-on-content-click="false"
                      transition="scale-transition"
                      offset-y
                      min-width="auto"
                    >
                      <template v-slot:activator="{ props }">
                        <v-text-field
                          ref="activityDate"
                          v-model="formatedActivityDate"
                          prepend-inner-icon="fas fa-calendar"
                          readonly
                          :label="$t('dataLossPrevention.date')"
                          v-bind="props"
                          variant="solo"
                          class="mr-3 mt-5"
                          density="compact"
                          style="min-width: 150px"
                        >
                        </v-text-field>
                      </template>
                      <v-date-picker
                        v-model="selectedDate"
                        :max="maxDate"
                        :min="minDate"
                        @update:model-value="fetchEmployeeList()"
                      />
                    </v-menu>
                  </div>
                  <div style="min-width: 150px" class="mr-3">
                    <CustomSelect
                      density="compact"
                      :label="$t('dataLossPrevention.workSchedule')"
                      :items="workScheduleList"
                      :is-loading="isWorkScheduleLoader"
                      v-model="workSchedule"
                      item-title="WorkSchedule_Name"
                      variant="solo"
                      item-value="WorkSchedule_Id"
                      sub-text="Time_Zone"
                      :item-selected="workSchedule"
                      hide-details
                      @selected-item="
                        (workSchedule = $event), fetchEmployeeList()
                      "
                      @update:model-value="onChangeWorkSchedule(workSchedule)"
                    />
                  </div>
                  <div class="mr-3 mt-5">
                    <CustomSelect
                      density="compact"
                      variant="solo"
                      :label="$t('dataLossPrevention.timezone')"
                      v-model="employeeTimeZone"
                      min-width="150px"
                      :item-selected="employeeTimeZone"
                      :items="[]"
                      :readonly="true"
                      @selected-item="employeeTimeZone"
                    />
                  </div>
                  <div class="mt-5" style="max-width: 250px; min-width: 150px">
                    <CustomSelect
                      density="compact"
                      v-model="selectedManager"
                      variant="solo"
                      :label="$t('dataLossPrevention.selectManager')"
                      item-title="employee_name"
                      item-value="employee_id"
                      :is-loading="managerLoader"
                      :items="managerList"
                      :item-selected="selectedManager"
                      @selected-item="selectedManager = $event"
                      @update:model-value="changeManager()"
                    />
                  </div>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <v-row v-else-if="searchList.length > 0">
          <v-col
            md="12"
            cols="12"
            xs="12"
            sm="12"
            class="d-flex mr-2 align-center"
            :class="{
              'flex-column': isMobileView,
              'justify-center': windowWidth < 1264,
              'justify-space-between': windowWidth >= 1264,
            }"
            style="flex-wrap: wrap"
          >
            <v-btn-toggle
              v-if="displayScreen?.toLowerCase() === 'employees'"
              v-model="empStatus"
              mandatory
              base-color="white"
              color="primary"
              density="compact"
              @update:model-value="filterItemList($event)"
            >
              <v-btn
                :color="
                  empStatus?.toLowerCase() === 'all' ? 'primary' : 'white'
                "
                value="all"
                >{{ $t("dataLossPrevention.all") }} ({{ totalCount }})</v-btn
              >
              <v-btn
                :color="
                  empStatus?.toLowerCase() === 'online' ? 'primary' : 'white'
                "
                value="online"
                >{{ $t("dataLossPrevention.online") }} ({{
                  onlineCount
                }})</v-btn
              >
              <v-btn
                :color="
                  empStatus?.toLowerCase() === 'offline' ? 'primary' : 'white'
                "
                value="offline"
                >{{ $t("dataLossPrevention.offline") }} ({{
                  offlineCount
                }})</v-btn
              >
            </v-btn-toggle>
            <div
              v-if="displayScreen?.toLowerCase() === 'details'"
              class="d-flex mr-2 align-center"
              :class="{
                'flex-column': isMobileView,
                'justify-center': windowWidth < 1264,
                'justify-end': windowWidth >= 1264,
              }"
            >
              <v-btn
                variant="outlined"
                class="mr-3"
                rounded="lg"
                @click="onClickBack()"
              >
                <v-icon color="primary">fas fa-chevron-left</v-icon
                >{{ $t("dataLossPrevention.back") }}
              </v-btn>
              <v-card
                height="40"
                class="d-flex align-center pa-2 mr-3 font-weight-bold"
                rounded="lg"
              >
                <span>{{ selectedEmployee.employeeName }}</span> &nbsp;
                <span v-if="selectedEmployee.userDefinedEmployeeId">
                  <span class="custom-label">
                    {{ $t("dataLossPrevention.employeeIdShort") }}:
                  </span>
                  &nbsp;
                  {{ selectedEmployee.userDefinedEmployeeId }}
                </span>
              </v-card>
              <v-btn
                rounded="lg"
                variant="outlined"
                @click="fetchChangeListEmployees()"
              >
                {{ $t("dataLossPrevention.change") }}
                <v-icon class="ml-3">fas fa-redo-alt</v-icon>
              </v-btn>
            </div>
            <div
              class="d-flex mr-2 align-center"
              :class="{
                'flex-column': isMobileView,
                'justify-center': windowWidth < 1264,
                'justify-end': windowWidth >= 1264,
              }"
            >
              <div>
                <v-menu
                  v-model="activityDate"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="activityDate"
                      v-model="formatedActivityDate"
                      prepend-inner-icon="fas fa-calendar"
                      :label="$t('dataLossPrevention.date')"
                      readonly
                      v-bind="props"
                      variant="solo"
                      class="mr-3 mt-5"
                      density="compact"
                      style="min-width: 150px"
                    >
                    </v-text-field>
                  </template>
                  <v-date-picker
                    v-model="selectedDate"
                    :max="maxDate"
                    :min="minDate"
                    @update:model-value="fetchEmployeeList()"
                  />
                </v-menu>
              </div>
              <div class="mr-3">
                <CustomSelect
                  density="compact"
                  :label="$t('dataLossPrevention.workSchedule')"
                  :items="workScheduleList"
                  :is-loading="isWorkScheduleLoader"
                  v-model="workSchedule"
                  item-title="WorkSchedule_Name"
                  item-value="WorkSchedule_Id"
                  sub-text="Time_Zone"
                  variant="solo"
                  min-width="150px"
                  :item-selected="workSchedule"
                  hide-details
                  :readonly="displayScreen?.toLowerCase() === 'details'"
                  @selected-item="(workSchedule = $event), fetchEmployeeList()"
                  @update:model-value="onChangeWorkSchedule(workSchedule)"
                />
              </div>
              <div class="mr-3 mt-5">
                <CustomSelect
                  density="compact"
                  variant="solo"
                  :label="$t('dataLossPrevention.timezone')"
                  v-model="employeeTimeZone"
                  min-width="150px"
                  :item-selected="employeeTimeZone"
                  :items="[]"
                  :readonly="true"
                  @selected-item="employeeTimeZone"
                />
              </div>
              <div v-if="displayScreen?.toLowerCase() === 'employees'">
                <CustomSelect
                  density="compact"
                  hide-details
                  v-model="selectedManager"
                  :label="$t('dataLossPrevention.selectManager')"
                  item-title="employee_name"
                  item-value="employee_id"
                  :items="managerList"
                  :is-loading="managerLoader"
                  variant="solo"
                  min-width="150px"
                  max-width="200px"
                  :item-selected="selectedManager"
                  @selected-item="selectedManager = $event"
                  @update:model-value="changeManager()"
                />
              </div>
              <v-btn
                rounded="lg"
                variant="text"
                @click="
                  displayScreen?.toLowerCase() === 'details'
                    ? fetchAppsActivity()
                    : fetchEmployeeList()
                "
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn
                    variant="plain"
                    rounded="lg"
                    class="ml-n1 mr-n5"
                    v-bind="props"
                  >
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action"
                    @click="onMoreAction(action)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            hover: isHovering,
                          }"
                        >
                          <v-tooltip :text="action.message">
                            <template v-slot:activator="{ props }">
                              <div v-bind="action.message ? props : ''">
                                <v-icon size="15" class="pr-2">{{
                                  action.icon
                                }}</v-icon>
                                {{ action.key }}
                              </div>
                            </template>
                          </v-tooltip>
                        </v-list-item-title>
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </v-col>
          <v-col cols="12">
            <v-window v-model="displayScreen">
              <v-window-item value="employees">
                <ListEmployees
                  v-if="displayScreen?.toLowerCase() === 'employees'"
                  :itemList="itemList"
                  :hasSummary="hasSummary"
                  :selected-date="selectedDate"
                  :productivityList="productivityList"
                  @open-details="openEmployeeDetails($event)"
                />
              </v-window-item>
              <v-window-item value="details">
                <EmployeeTrackingDetails
                  :formId="formId"
                  v-if="displayScreen?.toLowerCase() === 'details'"
                  :selectedEmployeeId="selectedEmployee.employeeId"
                  listAppsUrlsType="apps"
                  source="myTeamActivity"
                  listType="logger"
                  :timezone="employeeTimeZone"
                  :selectedDate="selectedDate"
                  :selectedWorkSchedule="workSchedule"
                  :refreshCallView="refreshCallView"
                  @refresh-done="refreshCallView = false"
                  @app-url-data="appUrlActivityData($event)"
                />
              </v-window-item>
            </v-window>
          </v-col>
        </v-row>
      </div>
      <AppAccessDenied v-else />
    </v-container>
    <AppLoading v-if="listLoading" />
    <div v-else>
      <EmployeesListModal
        v-if="openListModal"
        :show-modal="openListModal"
        :employeesList="employeesList"
        :showFilterSearch="true"
        selectStrategy="single"
        :modalTitle="$t('dataLossPrevention.employees')"
        employeeIdKey="employee_id"
        userDefinedEmpIdKey="user_defined_empid"
        :employeeIdLabel="$t('dataLossPrevention.employeeId')"
        :employeeNameLabel="$t('dataLossPrevention.employeeName')"
        employeeNameKey="employee_name"
        :designationNameLabel="$t('dataLossPrevention.role')"
        designationKey="role_name"
        :deptNameLabel="$t('dataLossPrevention.manager')"
        deptNameKey="manager_name"
        :showFilter="false"
        :isApplyFilter="true"
        @on-select-employee="onSelectEmployee($event)"
        @close-modal="openListModal = false"
      />
    </div>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
const ListEmployees = defineAsyncComponent(() => import("./ListEmployees.vue"));
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
const EmployeeTrackingDetails = defineAsyncComponent(() =>
  import("./EmployeeTrackingDetails.vue")
);
const EmployeesListModal = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeesListModal.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);

import { LIST_WORK_SCHEDULE } from "@/graphql/productivity-monitoring/activityDashboardQueries";
import {
  LIST_EMPLOYEE_MANAGERS,
  LIST_MY_TEAM_ACTIVITY_DETAILS,
  LIST_MY_TEAM_APP_URL_PRODUCTIVITY,
  LIST_EMPLOYEES,
} from "@/graphql/data-loss-prevention/keyLoggerQueries";
import { convertUTCToLocal } from "@/helper";
import { GET_EMPLOYEE_WORK_SCHEDULE_DETAILS } from "@/graphql/productivity-monitoring/activityDashboardQueries";
export default {
  name: "TeamKeyLogs",
  components: {
    ListEmployees,
    CustomSelect,
    EmployeeTrackingDetails,
    EmployeesListModal,
    NotesCard,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      isLoading: false,
      listLoading: false,
      displayScreen: "employees",
      originalList: [],
      itemList: [],
      searchList: [],
      emptyScenarioMsg: this.$t("dataLossPrevention.noEmployeesFound"),
      selectedDate: null,
      activityDate: false,
      formatedActivityDate: "",
      isWorkScheduleLoader: false,
      workScheduleList: [],
      workSchedule: 0,
      managerLoader: false,
      managerList: [],
      selectedManager: 0,
      employeeTimeZone: "",
      openMoreMenu: false,
      empStatus: "all",
      productivityList: [],
      selectedEmployee: null,
      openListModal: false,
      // The counts for the employees
      totalCount: 0,
      onlineCount: 0,
      offlineCount: 0,
      employeesList: [],
      appUrlData: [],
      refreshCallView: false,
      hasSummary: "",
    };
  },
  props: {
    formAccess: {
      type: Object,
      required: true,
    },
    searchData: {
      type: String,
      default: "",
    },
    formId: {
      type: Number,
      required: true,
    },
  },
  emits: ["my-team-loader"],
  computed: {
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    allManagerOption() {
      return {
        employee_name: this.$t("dataLossPrevention.all"),
        employee_id: 0,
      };
    },
    allWorkScheduleOption() {
      return {
        WorkSchedule_Name: this.$t("dataLossPrevention.all"),
        WorkSchedule_Id: 0,
        Time_Zone: "",
      };
    },
    getFooterImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/common/idea-bulb.webp");
      else return require("@/assets/images/common/idea-bulb.png");
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    dashboardType() {
      return this.$store.state.planDashboardType;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    moreActions() {
      return [
        {
          key: this.$t("dataLossPrevention.export"),
          icon: "fas fa-file-export",
        },
      ];
    },
    minDate() {
      return moment()
        .subtract(2, "months")
        .startOf("month")
        .format("YYYY-MM-DD");
    },
    maxDate() {
      return moment().format("YYYY-MM-DD");
    },
    schedule() {
      const workSchedule = this.workScheduleList.find(
        (ws) => ws.WorkSchedule_Id === this.workSchedule
      );
      return workSchedule ? workSchedule.WorkSchedule_Name : null;
    },
    appTrackingMode() {
      return this.$store.state.appTrackingMode;
    },
  },

  watch: {
    isLoading() {
      this.$emit("my-team-loader", this.isLoading);
    },
    selectedDate(val) {
      if (val) {
        this.activityDate = false;
        let dateValue = this.formatDate(val);
        this.formatedActivityDate = dateValue;
      }
    },
    searchData(val) {
      this.onApplySearch(val);
    },
    searchList: {
      immediate: true,
      handler(val) {
        if (val) {
          let onlineCount = 0;
          let offlineCount = 0;
          this.searchList.forEach((item) => {
            const status = item.trackingStatus.toLowerCase();
            if (
              status === "tracking" ||
              status === "idle" ||
              status === "tracked"
            ) {
              onlineCount++;
            } else if (status === "not tracking" || status === "not tracked") {
              offlineCount++;
            }
          });

          this.totalCount = onlineCount + offlineCount;
          this.onlineCount = onlineCount;
          this.offlineCount = offlineCount;
        }
      },
    },
  },
  created() {
    this.fetchWorkScheduleList();
    this.fetchManagerList();
    this.fetchEmployeeWorkDetails();
  },

  mounted() {
    this.selectedDate = new Date();
  },
  methods: {
    convertUTCToLocal,
    fetchAppsActivity() {
      this.refreshCallView = true;
    },
    handleHereClick() {
      window.open(
        this.baseUrl + "v3/settings/data-loss-prevention/key-logging",
        "_blank"
      );
    },
    onApplySearch(val) {
      if (!val) {
        this.filterItemList(this.empStatus);
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.itemList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    filterItemList(status) {
      if (status?.toLowerCase() === "online") {
        this.empStatus = "online";
        let filteredList = this.searchList.filter(
          (item) =>
            item.trackingStatus.toLowerCase() === "tracking" ||
            item.trackingStatus.toLowerCase() === "idle" ||
            item.trackingStatus.toLowerCase() === "tracked"
        );
        this.itemList = filteredList;
      } else if (status?.toLowerCase() === "offline") {
        this.empStatus = "offline";
        let filteredList = this.searchList.filter(
          (item) =>
            item.trackingStatus.toLowerCase() === "not tracking" ||
            item.trackingStatus.toLowerCase() === "not tracked"
        );
        this.itemList = filteredList;
      } else if (status?.toLowerCase() === "all") {
        this.empStatus = "all";
        this.itemList = this.searchList;
      }
    },
    onSelectEmployee(employee) {
      const transformedEmployee = {
        userDefinedEmployeeId: employee.user_defined_empid,
        managerId: employee.manager_id,
        employeeId: employee.employee_id,
        employeeName: employee.employee_name,
      };
      this.openListModal = false;
      this.selectedEmployee = transformedEmployee;
      this.displayScreen = "details";
    },
    openEmployeeDetails(employee) {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.displayScreen = "details";
      this.selectedEmployee = employee;
    },
    fetchChangeListEmployees() {
      this.openListModal = true;
      let vm = this;
      vm.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_EMPLOYEES,
          client: "apolloClientE",
          variables: {
            isCallFromReportForm: 0, //to identify the source of listEmployees is from reports or not
            subscribedPlan: "EMPLOYEEMONITORINGDASHBOARD",
            workScheduleId: vm.workSchedule,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listEmployees &&
            response.data.listEmployees.listEmployees &&
            !response.data.listEmployees.errorCode
          ) {
            vm.employeesList = JSON.parse(
              response.data.listEmployees.listEmployees
            );
            vm.openListModal = true;
            vm.listLoading = false;
          } else {
            vm.employeesList = [];
            vm.openListModal = false;
            vm.listLoading = false;
          }
        })
        .catch((err) => {
          vm.openListModal = false;
          vm.listLoading = false;
          vm.handlefetchChangeListEmployeessError(err);
        });
    },
    handlefetchChangeListEmployeessError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: this.$t("dataLossPrevention.keyLogs"),
        isListError: false,
      });
    },
    fetchWorkScheduleList() {
      let vm = this;
      vm.isWorkScheduleLoader = true;
      vm.$apollo
        .query({
          query: LIST_WORK_SCHEDULE,
          client: "apolloClientC",
          variables: {
            formName: "productivitymonitoring",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          const { errorCode, workSchedule } = response.data.listWorkSchedule;
          if (!errorCode && workSchedule) {
            let WSList = [vm.allWorkScheduleOption];
            vm.workScheduleList = WSList.concat(workSchedule);
            vm.isWorkScheduleLoader = false;
          } else {
            vm.isWorkScheduleLoader = false;
          }
        })
        .catch((err) => {
          vm.isWorkScheduleLoader = false;
          vm.handleWorkSchduleError(err);
        });
    },
    handleWorkSchduleError(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: this.$t("dataLossPrevention.keyLogs"),
        isListError: false,
      });
    },
    changeManager() {
      this.empStatus = "all";
      if (this.selectedManager === 0) {
        this.itemList = this.originalList;
        this.searchList = JSON.parse(JSON.stringify(this.itemList));
      } else {
        this.itemList = this.originalList.filter(
          (item) => item.managerId === this.selectedManager
        );
        this.searchList = JSON.parse(JSON.stringify(this.itemList));
      }
    },
    fetchManagerList() {
      let vm = this;
      vm.managerLoader = true;
      vm.$apollo
        .query({
          query: LIST_EMPLOYEE_MANAGERS,
          client: "apolloClientE",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.listManagerDetails &&
            data.listManagerDetails.managerData
          ) {
            let selectedManager = [vm.allManagerOption];
            vm.managerList = selectedManager.concat(
              data.listManagerDetails.managerData
            );
          }
          vm.managerLoader = false;
        })
        .catch((err) => {
          vm.managerLoader = false;
          vm.handleManagerError(err);
        });
    },
    handleManagerError(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: this.$t("dataLossPrevention.keyLogs"),
        isListError: false,
      });
    },
    onChangeWorkSchedule(val) {
      this.empStatus = "all";
      this.selectedManager = 0;
      if (!val) {
        this.employeeTimeZone = "Asia/Kolkata";
      } else {
        this.employeeTimeZone = this.workScheduleList.find(
          (ws) => ws.WorkSchedule_Id === this.workSchedule
        ).TimeZone_Id;
      }
    },
    fetchEmployeeWorkDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_EMPLOYEE_WORK_SCHEDULE_DETAILS,
          client: "apolloClientK",
          variables: { employeeId: vm.loginEmployeeId },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getEmployeeWorkScheduleDetails &&
            response.data.getEmployeeWorkScheduleDetails
              .employeeWorkScheduleDetails
          ) {
            vm.employeeTimeZone =
              response.data.getEmployeeWorkScheduleDetails.employeeWorkScheduleDetails.timeZone;
            vm.workSchedule =
              response.data.getEmployeeWorkScheduleDetails.employeeWorkScheduleDetails.workScheduleId;
            vm.isLoading = false;
            vm.fetchEmployeeList();
          } else {
            let error = response.data.getEmployeeWorkScheduleDetails.errorCode;
            vm.handleWorkDetailsError(error);
            vm.isLoading = false;
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleWorkDetailsError(err);
        });
    },
    handleWorkDetailsError(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: this.$t("dataLossPrevention.keyLogs"),
        isListError: false,
      });
    },
    fetchEmployeeList() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: LIST_MY_TEAM_ACTIVITY_DETAILS,
          client: "apolloClientK",
          variables: {
            date: moment(vm.selectedDate).format("YYYY-MM-DD"),
            workScheduleId: vm.workSchedule,
            subscribedPlan: vm.dashboardType,
            timeZone: vm.employeeTimeZone,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.listMyTeamActivityDetails &&
            data.listMyTeamActivityDetails.myTeamDetails &&
            data.listMyTeamActivityDetails.myTeamDetails.length > 0
          ) {
            let { myTeamDetails, hasSummary } = data.listMyTeamActivityDetails;
            if (
              this.appTrackingMode &&
              this.appTrackingMode?.toLowerCase() === "stealth mode"
            ) {
              myTeamDetails = myTeamDetails.filter((el) => el.assetId);
            }
            vm.originalList = JSON.parse(JSON.stringify(myTeamDetails));
            vm.itemList = JSON.parse(JSON.stringify(myTeamDetails));
            vm.searchList = [...myTeamDetails];
            vm.hasSummary = hasSummary;
            let teamIds = myTeamDetails.map((emp) => emp.employeeId);
            if (teamIds) {
              vm.getEmployeeProductivity(teamIds);
            }
          } else {
            vm.isLoading = false;
            vm.itemList = [];
            vm.searchList = [];
            vm.originalList = [];
            vm.hasSummary = "";
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleEmployeeListError(err);
        });
    },
    handleEmployeeListError(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: this.$t("dataLossPrevention.keyLogs"),
        isListError: false,
      });
    },
    async getEmployeeProductivity(teamIds) {
      let vm = this;
      vm.isLoading = true;
      vm.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      await vm.$apollo
        .query({
          query: LIST_MY_TEAM_APP_URL_PRODUCTIVITY,
          client: "apolloClientK",
          variables: {
            date: moment(vm.selectedDate).format("YYYY-MM-DD"),
            workScheduleId: vm.workSchedule,
            timeZone: vm.employeeTimeZone,
            employeeId: teamIds,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.listMyTeamAppUrlProductivity &&
            data.listMyTeamAppUrlProductivity.teamAppUrlProductivityDetails &&
            data.listMyTeamAppUrlProductivity.teamAppUrlProductivityDetails
              .length
          ) {
            let { teamAppUrlProductivityDetails } =
              data.listMyTeamAppUrlProductivity;
            for (var teamProductivity of teamAppUrlProductivityDetails) {
              let empIdIndex = this.originalList.findIndex(
                (x) => x.employeeId === parseInt(teamProductivity.employeeId)
              );
              if (empIdIndex > -1) {
                vm.originalList[empIdIndex]["productiveTime"] =
                  teamProductivity.productiveTime;
                vm.originalList[empIdIndex]["unProductiveTime"] =
                  teamProductivity.unProductiveTime;
                vm.originalList[empIdIndex]["neutralTime"] =
                  teamProductivity.neutralTime;
              }
            }
            vm.searchList = JSON.parse(JSON.stringify(this.originalList));
            vm.itemList = JSON.parse(JSON.stringify(this.originalList));
            vm.productivityList = teamAppUrlProductivityDetails;
            vm.changeManager();
            vm.isLoading = false;
          } else {
            vm.isLoading = false;
            vm.productivityList = [];
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleProductivityError(err);
        });
    },
    handleProductivityError(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: this.$t("dataLossPrevention.keyLogs"),
        isListError: false,
      });
    },
    onMoreAction(action) {
      this.openMoreMenu = false;
      if (action.key == this.$t("dataLossPrevention.export")) {
        this.displayScreen?.toLowerCase() === "employees"
          ? this.exportFile()
          : this.exportAppsURLs();
      }
    },
    appUrlActivityData(data) {
      if (!data) return;
      else this.appUrlData = data;
    },
    presentWithoutSecs(datetimeStr) {
      if (!datetimeStr) return "";
      const [date, time] = datetimeStr.split(" ");
      const [hours, minutes] = time.split(":");
      return `${date} ${hours}:${minutes}`;
    },
    exportAppsURLs() {
      let exportData = JSON.parse(JSON.stringify(this.appUrlData));
      exportData = exportData.map((el) => ({
        ...el,
        activityStartDateTime: el.activityStartDateTime
          ? this.presentWithoutSecs(
              this.convertUTCToLocal(el.activityStartDateTime)
            )
          : "",
        activityEndDateTime: el.activityEndDateTime
          ? this.presentWithoutSecs(
              this.convertUTCToLocal(el.activityEndDateTime)
            )
          : "",
      }));
      let exportHeaders = [
        {
          header: this.$t("dataLossPrevention.applicationName"),
          key: "Application_Name",
        },
        {
          header: this.$t("dataLossPrevention.applicationTitle"),
          key: "App_Title",
        },
        { header: this.$t("dataLossPrevention.keyLogs"), key: "keyStrokes" },
        {
          header: this.$t("dataLossPrevention.activityStartTime"),
          key: "activityStartDateTime",
        },
        {
          header: this.$t("dataLossPrevention.activityEndTime"),
          key: "activityEndDateTime",
        },
      ];
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.$t("dataLossPrevention.keyLogs"),
        sheetName: this.$t("dataLossPrevention.keyLogs"),
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    exportFile() {
      let exportHeaders = [
        {
          header: this.$t("dataLossPrevention.employeeId"),
          key: "userDefinedEmployeeId",
        },
        {
          header: this.$t("dataLossPrevention.employeeName"),
          key: "employeeName",
        },
        {
          header: this.$t("dataLossPrevention.productive"),
          key: "productiveTime",
        },
        {
          header: this.$t("dataLossPrevention.unproductiveTime"),
          key: "unProductiveTime",
        },
        {
          header: this.$t("dataLossPrevention.neutral"),
          key: "neutralTime",
        },
        {
          header: this.$t("dataLossPrevention.userProductivity"),
          key: "userProductivityPercentage",
        },
      ];
      let UserData = this.originalList;
      let exportOptions = {
        fileExportData: UserData,
        fileName: this.$t("dataLossPrevention.keyLogs") + " Details",
        sheetName: this.$t("dataLossPrevention.employeeName"),
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    onClickBack() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.refreshCallView = false;
      this.displayScreen = "employees";
      this.selectedEmployee = null;
    },
  },
};
</script>
<style scoped>
.logger-container {
  padding: 1em 2em 0em 1em;
}

@media screen and (max-width: 805px) {
  .logger-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
