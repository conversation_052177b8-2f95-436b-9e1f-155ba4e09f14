<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="insights-container">
      <v-window v-model="currentTabItem" v-if="formAccess && formAccess.view">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="itemList.length === 0"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? 'common/no-records' : ''"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    md="12"
                    cols="12"
                    xs="12"
                    sm="12"
                    class="d-flex ml-md-4 align-center"
                    :class="{
                      'flex-column': isMobileView,
                      'justify-center': windowWidth < 1264,
                      'justify-end': windowWidth >= 1264,
                    }"
                    style="flex-wrap: wrap"
                  >
                    <v-btn
                      class="bg-white my-2 ml-2"
                      :size="isMobileView ? 'small' : 'default'"
                      rounded="lg"
                    >
                      <v-icon color="primary" size="14"
                        >fas fa-calendar-alt</v-icon
                      >
                      <span class="text-caption px-1"
                        >{{ $t("dataLossPrevention.workedDate") }}:</span
                      >
                      <flat-pickr
                        v-model="appliedDateRange"
                        :config="flatPickerOptions"
                        :placeholder="
                          $t('dataLossPrevention.dateRangeSelector')
                        "
                        class="ml-2 mt-1 date-range-picker-custom-bg"
                        style="outline: 0px; color: var(--v-primary-base)"
                        @onChange="onChangeDateRange"
                      ></flat-pickr>
                    </v-btn>
                    <v-menu
                      v-model="customGroupMenu"
                      :close-on-content-click="true"
                      transition="scale-transition"
                      offset-y
                      min-width="290px"
                    >
                      <template v-slot:activator="{ props: activatorProps }">
                        <v-btn
                          class="white my-2 ml-2"
                          rounded="lg"
                          dense
                          v-bind="activatorProps"
                        >
                          <span class="text-caption px-1"
                            >{{ $t("dataLossPrevention.customGroup") }}:</span
                          >
                          <span class="text-primary font-weight-bold"> </span>
                          {{ selectedCustomGroup ? selectedCustomGroup : "-" }}
                          <v-icon color="white" class="ml-1" size="17">{{
                            customGroupMenu
                              ? "fas fa-caret-up"
                              : "fas fa-caret-down"
                          }}</v-icon>
                        </v-btn>
                      </template>
                      <div
                        style="
                          min-height: 100px;
                          max-height: 300px;
                          overflow-y: scroll;
                          background-color: white;
                        "
                        class="white pa-2"
                      >
                        <div
                          v-if="customGroupList && customGroupList.length > 0"
                        >
                          <div
                            v-for="customGroup in customGroupList"
                            :key="customGroup.WorkSchedule_Id"
                            @click="onSelectCustomGroup(customGroup)"
                          >
                            <v-hover>
                              <template v-slot:default="{ isHovering, props }">
                                <div
                                  v-bind="props"
                                  class="pa-2 my-2 rounded-lg cursor-pointer"
                                  :class="{
                                    'bg-hover':
                                      isHovering &&
                                      selectedCustomGroup !==
                                        customGroup.Custom_Group_Name,
                                    'bg-primary text-white':
                                      selectedCustomGroup ===
                                      customGroup.Custom_Group_Name,
                                    'bg-grey-lighten-4':
                                      !isHovering &&
                                      selectedCustomGroup !==
                                        customGroup.Custom_Group_Name,
                                  }"
                                >
                                  <div class="text-body-2">
                                    {{ customGroup.Custom_Group_Name }}
                                  </div>
                                  <div
                                    style="font-size: 12px"
                                    class="pt-1"
                                    :class="{
                                      'text-white':
                                        selectedCustomGroup ===
                                        customGroup.Custom_Group_Name,
                                      'text-primary text-lighten-3':
                                        selectedCustomGroup !==
                                        customGroup.Custom_Group_Name,
                                    }"
                                  ></div>
                                </div>
                              </template>
                            </v-hover>
                          </div>
                        </div>
                        <div
                          v-else
                          style="height: 100px"
                          class="text-grey rounded-lg d-flex justify-center align-center"
                        >
                          {{ $t("dataLossPrevention.noDataAvailable") }}
                        </div>
                      </div>
                    </v-menu>
                    <v-menu
                      id="activitytracker_my_activity_date_picker"
                      v-model="workScheduleMenu"
                      :close-on-content-click="true"
                      transition="scale-transition"
                      offset-y
                      min-width="290px"
                    >
                      <template v-slot:activator="{ props: activatorProps }">
                        <v-btn
                          class="bg-white my-2 ml-2"
                          rounded="lg"
                          dense
                          v-bind="activatorProps"
                        >
                          <v-icon color="primary" class="mr-1" size="17"
                            >far fa-clock</v-icon
                          >
                          <span class="text-caption primary px-1"
                            >{{ $t("dataLossPrevention.workSchedule") }}:</span
                          >
                          <span class="text-primary font-weight-bold">
                            {{
                              employeeWorkSchedule ? employeeWorkSchedule : "-"
                            }}
                          </span>
                          <v-icon color="primary" class="ml-1" size="17">{{
                            workScheduleMenu
                              ? "fas fa-caret-up"
                              : "fas fa-caret-down"
                          }}</v-icon>
                        </v-btn>
                      </template>
                      <div
                        style="
                          min-height: 100px;
                          max-height: 300px;
                          overflow-y: scroll;
                          background-color: white;
                        "
                        class="white pa-2"
                      >
                        <div
                          v-if="workScheduleList && workScheduleList.length > 0"
                        >
                          <div
                            v-for="workSchedule in workScheduleList"
                            :key="workSchedule.WorkSchedule_Id"
                            @click="onSelectWorkSchedule(workSchedule)"
                          >
                            <v-hover>
                              <template v-slot:default="{ isHovering, props }">
                                <div
                                  v-bind="props"
                                  class="pa-2 my-2 rounded-lg cursor-pointer"
                                  :class="{
                                    'bg-hover':
                                      isHovering &&
                                      employeeWorkSchedule !==
                                        workSchedule.WorkSchedule_Name,
                                    'bg-primary text-white':
                                      employeeWorkSchedule ===
                                      workSchedule.WorkSchedule_Name,
                                    'bg-grey-lighten-4 text-primary':
                                      !isHovering &&
                                      employeeWorkSchedule !==
                                        workSchedule.WorkSchedule_Name,
                                  }"
                                >
                                  <div class="text-body-2">
                                    {{ workSchedule.WorkSchedule_Name }}
                                  </div>
                                  <div
                                    style="font-size: 12px"
                                    class="pt-1"
                                    :class="{
                                      'text-white':
                                        employeeWorkSchedule ===
                                        workSchedule.WorkSchedule_Name,
                                      'text-primary text-lighten-3':
                                        employeeWorkSchedule !==
                                        workSchedule.WorkSchedule_Name,
                                    }"
                                  >
                                    {{ workSchedule.Time_Zone }}
                                  </div>
                                </div>
                              </template>
                            </v-hover>
                          </div>
                        </div>
                        <div
                          v-else
                          style="height: 100px"
                          class="text-grey rounded-lg d-flex justify-center align-center"
                        >
                          {{ $t("dataLossPrevention.noDataAvailable") }}
                        </div>
                      </div>
                    </v-menu>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <v-row
            v-else-if="itemList.length > 0"
            class="d-flex justify-end mt-3"
          >
            <v-col
              md="12"
              cols="12"
              xs="12"
              sm="12"
              class="d-flex ml-md-4 align-center"
              :class="{
                'flex-column': isMobileView,
                'justify-center': windowWidth < 1264,
                'justify-end': windowWidth >= 1264,
              }"
              style="flex-wrap: wrap"
            >
              <v-btn
                class="bg-white my-2 ml-2"
                :size="isMobileView ? 'small' : 'default'"
                rounded="lg"
              >
                <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
                <span class="text-caption px-1"
                  >{{ $t("dataLossPrevention.workedDate") }}:</span
                >
                <flat-pickr
                  v-model="appliedDateRange"
                  :config="flatPickerOptions"
                  :placeholder="$t('dataLossPrevention.dateRangeSelector')"
                  class="ml-2 mt-1 date-range-picker-custom-bg"
                  style="outline: 0px; color: var(--v-primary-base)"
                  @onChange="onChangeDateRange"
                ></flat-pickr>
              </v-btn>
              <v-menu
                v-model="customGroupMenu"
                :close-on-content-click="true"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <template v-slot:activator="{ props: activatorProps }">
                  <v-btn
                    class="white my-2 ml-2"
                    rounded="lg"
                    dense
                    v-bind="activatorProps"
                  >
                    <span class="text-caption px-1"
                      >{{ $t("dataLossPrevention.customGroup") }}:</span
                    >
                    <span class="text-primary font-weight-bold"> </span>
                    {{ selectedCustomGroup ? selectedCustomGroup : "-" }}
                    <v-icon color="white" class="ml-1" size="17">{{
                      customGroupMenu ? "fas fa-caret-up" : "fas fa-caret-down"
                    }}</v-icon>
                  </v-btn>
                </template>
                <div
                  style="
                    min-height: 100px;
                    max-height: 300px;
                    overflow-y: scroll;
                    background-color: white;
                  "
                  class="white pa-2"
                >
                  <div v-if="customGroupList && customGroupList.length > 0">
                    <div
                      v-for="customGroup in customGroupList"
                      :key="customGroup.WorkSchedule_Id"
                      @click="onSelectCustomGroup(customGroup)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <div
                            v-bind="props"
                            class="pa-2 my-2 rounded-lg cursor-pointer"
                            :class="{
                              'bg-hover':
                                isHovering &&
                                selectedCustomGroup !==
                                  customGroup.Custom_Group_Name,
                              'bg-primary text-white':
                                selectedCustomGroup ===
                                customGroup.Custom_Group_Name,
                              'bg-grey-lighten-4':
                                !isHovering &&
                                selectedCustomGroup !==
                                  customGroup.Custom_Group_Name,
                            }"
                          >
                            <div class="text-body-2">
                              {{ customGroup.Custom_Group_Name }}
                            </div>
                            <div
                              style="font-size: 12px"
                              class="pt-1"
                              :class="{
                                'text-white':
                                  selectedCustomGroup ===
                                  customGroup.Custom_Group_Name,
                                'text-primary text-lighten-3':
                                  selectedCustomGroup !==
                                  customGroup.Custom_Group_Name,
                              }"
                            ></div>
                          </div>
                        </template>
                      </v-hover>
                    </div>
                  </div>
                  <div
                    v-else
                    style="height: 100px"
                    class="text-grey rounded-lg d-flex justify-center align-center"
                  >
                    {{ $t("dataLossPrevention.noDataAvailable") }}
                  </div>
                </div>
              </v-menu>
              <v-menu
                id="activitytracker_my_activity_date_picker"
                v-model="workScheduleMenu"
                :close-on-content-click="true"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <template v-slot:activator="{ props: activatorProps }">
                  <v-btn
                    class="bg-white my-2 ml-2"
                    rounded="lg"
                    dense
                    v-bind="activatorProps"
                  >
                    <v-icon color="primary" class="mr-1" size="17"
                      >far fa-clock</v-icon
                    >
                    <span class="text-caption primary px-1"
                      >{{ $t("dataLossPrevention.workSchedule") }}:</span
                    >
                    <span class="text-primary font-weight-bold">
                      {{ employeeWorkSchedule ? employeeWorkSchedule : "-" }}
                    </span>
                    <v-icon color="primary" class="ml-1" size="17">{{
                      workScheduleMenu ? "fas fa-caret-up" : "fas fa-caret-down"
                    }}</v-icon>
                  </v-btn>
                </template>
                <div
                  style="
                    min-height: 100px;
                    max-height: 300px;
                    overflow-y: scroll;
                    background-color: white;
                  "
                  class="white pa-2"
                >
                  <div v-if="workScheduleList && workScheduleList.length > 0">
                    <div
                      v-for="workSchedule in workScheduleList"
                      :key="workSchedule.WorkSchedule_Id"
                      @click="onSelectWorkSchedule(workSchedule)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <div
                            v-bind="props"
                            class="pa-2 my-2 rounded-lg cursor-pointer"
                            :class="{
                              'bg-hover':
                                isHovering &&
                                employeeWorkSchedule !==
                                  workSchedule.WorkSchedule_Name,
                              'bg-primary text-white':
                                employeeWorkSchedule ===
                                workSchedule.WorkSchedule_Name,
                              'bg-grey-lighten-4 text-primary':
                                !isHovering &&
                                employeeWorkSchedule !==
                                  workSchedule.WorkSchedule_Name,
                            }"
                          >
                            <div class="text-body-2">
                              {{ workSchedule.WorkSchedule_Name }}
                            </div>
                            <div
                              style="font-size: 12px"
                              class="pt-1"
                              :class="{
                                'text-white':
                                  employeeWorkSchedule ===
                                  workSchedule.WorkSchedule_Name,
                                'text-primary text-lighten-3':
                                  employeeWorkSchedule !==
                                  workSchedule.WorkSchedule_Name,
                              }"
                            >
                              {{ workSchedule.Time_Zone }}
                            </div>
                          </div>
                        </template>
                      </v-hover>
                    </div>
                  </div>
                  <div
                    v-else
                    style="height: 100px"
                    class="text-grey rounded-lg d-flex justify-center align-center"
                  >
                    {{ $t("dataLossPrevention.noDataAvailable") }}
                  </div>
                </div>
              </v-menu>
              <v-menu
                id="activitytracker_my_activity_date_picker"
                v-model="customFieldsMenu"
                :close-on-content-click="true"
                transition="scale-transition"
                offset-y
                min-width="290px"
              >
                <template v-slot:activator="{ props: activatorProps }">
                  <v-btn
                    class="bg-white my-2 ml-2"
                    rounded="lg"
                    dense
                    v-bind="activatorProps"
                  >
                    <span class="text-primary font-weight-bold">
                      {{ chartType }}
                    </span>
                    <v-icon color="primary" class="ml-1" size="17">{{
                      customFieldsMenu ? "fas fa-caret-up" : "fas fa-caret-down"
                    }}</v-icon>
                  </v-btn>
                </template>
                <div
                  style="
                    min-height: 100px;
                    max-height: 300px;
                    overflow-y: scroll;
                    background-color: white;
                  "
                  class="white pa-2"
                >
                  <div v-if="chartTypeList && chartTypeList.length > 0">
                    <div
                      v-for="type in chartTypeList"
                      :key="type"
                      @click="onSelectItem(type, 'chartType')"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <div
                            v-bind="props"
                            class="pa-2 my-2 rounded-lg cursor-pointer"
                            :class="{
                              'bg-hover': isHovering && type !== chartType,
                              'bg-primary text-white': chartType === type,
                              'bg-grey-lighten-4 text-primary':
                                !isHovering && type !== chartType,
                            }"
                          >
                            <div class="text-body-2">
                              {{ type }}
                            </div>
                          </div>
                        </template>
                      </v-hover>
                    </div>
                  </div>
                  <div
                    v-else
                    style="height: 100px"
                    class="text-grey rounded-lg d-flex justify-center align-center"
                  >
                    {{ $t("dataLossPrevention.noDataAvailable") }}
                  </div>
                </div>
              </v-menu>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn
                    variant="plain"
                    class="mt-1 ml-n3 mr-n5"
                    v-bind="props"
                  >
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action"
                    @click="onMoreAction(action)"
                  >
                    <v-hover v-bind="props">
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'pink-lighten-5': isHovering,
                          }"
                        >
                          <v-tooltip :text="action.message">
                            <template v-slot:activator="{ props }">
                              <div v-bind="action.message ? props : ''">
                                <v-icon size="15" class="pr-2">{{
                                  action.icon
                                }}</v-icon>
                                {{ action.key }}
                              </div>
                            </template>
                          </v-tooltip>
                        </v-list-item-title>
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </v-col>
            <v-data-table
              :headers="tableHeaders"
              :items="itemList"
              fixed-header
              :height="
                $store.getters.getTableHeightBasedOnScreenSize(290, itemList)
              "
              :items-per-page="50"
              :items-per-page-options="[
                { value: 50, title: '50' },
                { value: 100, title: '100' },
                {
                  value: -1,
                  title: $t('dataLossPrevention.itemsPerPageAll'),
                },
              ]"
              :footer-props="{
                itemsPerPageText: $t('dataLossPrevention.itemsPerPage'),
              }"
            >
              <template v-slot:item="{ item }">
                <tr
                  class="data-table-tr bg-white cursor-pointer"
                  :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
                >
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.employeeName") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ item.employeeName }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.location") }}
                      {{ $t("dataLossPrevention.summary") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ item.Work_Location }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                    v-if="chartType == $t('dataLossPrevention.systemUpTime')"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.systemUpTime") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ convertToHrs(item.sumOfSystemUpTime) }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                    v-if="chartType == $t('dataLossPrevention.systemUpTime')"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.systemUpTime") }} /
                      {{ $t("dataLossPrevention.day") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ convertToHrs(item.averageSystemUpTime) }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                    v-if="chartType == $t('dataLossPrevention.activeTime')"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.activeTime") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ convertToHrs(item.sumOfActiveTime) }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                    v-if="chartType == $t('dataLossPrevention.activeTime')"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.activeTime") }} /
                      {{ $t("dataLossPrevention.day") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ convertToHrs(item.averageActiveTime) }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                    v-if="
                      chartType == $t('dataLossPrevention.computerActivity')
                    "
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.computerActivity") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ convertToHrs(item.sumOfComputerActivityTime) }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                    v-if="
                      chartType == $t('dataLossPrevention.computerActivity')
                    "
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.computerActivity") }} /
                      {{ $t("dataLossPrevention.day") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ convertToHrs(item.averageComputerActivityTime) }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                    v-if="chartType == $t('dataLossPrevention.productiveTime')"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.productiveTime") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ convertToHrs(item.sumOfProductiveAppsAndUrls) }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                    v-if="chartType == $t('dataLossPrevention.productiveTime')"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.productiveTime") }} /
                      {{ $t("dataLossPrevention.day") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ convertToHrs(item.averageProductiveAppsAndUrls) }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.activeDays") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ item.activeDays }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.daysOffice") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ item.countOffice }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.daysRemote") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ item.countRemote }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      {{ $t("dataLossPrevention.daysHybrid") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{ item.countHybrid }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      % {{ $t("dataLossPrevention.inOffice") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{
                          item.percentageOfficeRecords > 0
                            ? item.percentageOfficeRecords.toFixed(2)
                            : 0
                        }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      % {{ $t("dataLossPrevention.inRemote") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{
                          item.percentageRemoteRecords > 0
                            ? item.percentageRemoteRecords.toFixed(2)
                            : 0
                        }}
                      </div>
                    </section>
                  </td>
                  <td
                    :class="isMobileView ? 'd-flex justify-space-between' : ''"
                    :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
                  >
                    <div
                      v-if="isMobileView"
                      class="text-subtitle-1 text-grey-darken-1 mt-2"
                    >
                      % {{ $t("dataLossPrevention.inHybrid") }}
                    </div>
                    <section class="d-flex align-center">
                      <div
                        class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                        :style="
                          !isMobileView
                            ? 'max-width: 200px; '
                            : 'max-width: 200px; '
                        "
                      >
                        {{
                          item.percentageOfficeOrRemoteRecords > 0
                            ? item.percentageOfficeOrRemoteRecords.toFixed(2)
                            : 0
                        }}
                      </div>
                    </section>
                  </td>
                </tr>
              </template>
            </v-data-table>
            <v-col></v-col>
          </v-row>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>
<script>
import moment from "moment";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import { LIST_WORK_SCHEDULE } from "@/graphql/productivity-monitoring/activityDashboardQueries";
// import {
//   RETRIEVE_USER_DATA_LIST,
//   RETRIEVE_LOCATION_DATA_LIST,
// } from "@/graphql/productivity-monitoring/locationInsightsQueries";
import FileExportMixin from "@/mixins/FileExportMixin";
import { RETRIEVE_USER_DATA_LIST } from "@/graphql/productivity-monitoring/locationInsightsQueries";
export default {
  name: "UserActivity",
  components: { flatPickr, EmployeeDefaultFilterMenu },
  mixins: [FileExportMixin],
  data() {
    return {
      appliedDateRange: null,
      workScheduleMenu: false,
      customGroupMenu: false,
      customFieldsMenu: false,
      employeeWorkSchedule: "",
      selectedCustomGroup: "All",
      startDate: "",
      endDate: "",
      currentDateRange: "",
      isExceed31Days: false,
      customGroupList: [],
      workScheduleList: [],
      employeeWorkScheduleId: 0,
      customGroupId: 0,
      chartTypeList: [
        this.$t("dataLossPrevention.systemUpTime"),
        this.$t("dataLossPrevention.activeTime"),
        this.$t("dataLossPrevention.computerActivity"),
        this.$t("dataLossPrevention.productiveTime"),
      ],
      chartType: this.$t("dataLossPrevention.activeTime"),
      originalList: [],
      itemList: [],
      currentTabItem: "",
      isLoading: false,
      openMoreMenu: false,
      listLoading: false,
    };
  },
  computed: {
    convertToHrs() {
      return (val) => {
        let hours = Math.floor(val / 60);
        let minutes = parseInt(val % 60);
        minutes = minutes < 10 ? "0" + minutes : minutes;
        return `${hours}h ${minutes}m`;
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date, withTime = false) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "-";
      };
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = this.$t("dataLossPrevention.locationDataEmpty");
      }
      return msgText;
    },
    landedFormName() {
      return this.$t("dataLossPrevention.locationIntelligence");
    },
    mainTabs() {
      return [this.$t("dataLossPrevention.locationIntelligence")];
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("295");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      // Get the current date
      const currentDate = moment().toDate();
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment().format(this.$store.state.orgDetails.orgDateFormat),
        // Disable the current date
        disable: [currentDate],
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    moreActions() {
      return [
        {
          key: this.$t("dataLossPrevention.export"),
          icon: "fas fa-file-export",
        },
      ];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    tableHeaders() {
      if (this.chartType == this.$t("dataLossPrevention.systemUpTime")) {
        return [
          {
            title: this.$t("dataLossPrevention.employeeName"),
            key: "employeeName",
          },
          {
            title:
              this.$t("dataLossPrevention.location") +
              " " +
              this.$t("dataLossPrevention.summary"),
            key: "Work_Location",
          },
          {
            title: this.$t("dataLossPrevention.systemUpTime"),
            key: "sumOfSystemUpTime",
          },
          {
            title:
              this.$t("dataLossPrevention.systemUpTime") +
              " / " +
              this.$t("dataLossPrevention.day"),
            key: "averageSystemUpTime",
          },
          {
            title: this.$t("dataLossPrevention.activeDays"),
            key: "activeDays",
          },
          {
            title: this.$t("dataLossPrevention.daysOffice"),
            key: "countOffice",
          },
          {
            title: this.$t("dataLossPrevention.daysRemote"),
            key: "countRemote",
          },
          {
            title: this.$t("dataLossPrevention.daysHybrid"),
            key: "countHybrid",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inOffice"),
            key: "percentageOfficeRecords",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inRemote"),
            key: "percentageRemoteRecords",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inHybrid"),
            key: "percentageOfficeOrRemoteRecords",
          },
        ];
      } else if (this.chartType == this.$t("dataLossPrevention.activeTime")) {
        return [
          {
            title: this.$t("dataLossPrevention.employeeName"),
            key: "employeeName",
          },
          {
            title:
              this.$t("dataLossPrevention.location") +
              " " +
              this.$t("dataLossPrevention.summary"),
            key: "Work_Location",
          },
          {
            title: this.$t("dataLossPrevention.activeTime"),
            key: "sumOfActiveTime",
          },
          {
            title:
              this.$t("dataLossPrevention.activeTime") +
              " / " +
              this.$t("dataLossPrevention.day"),
            key: "averageActiveTime",
          },
          {
            title: this.$t("dataLossPrevention.activeDays"),
            key: "activeDays",
          },
          {
            title: this.$t("dataLossPrevention.daysOffice"),
            key: "countOffice",
          },
          {
            title: this.$t("dataLossPrevention.daysRemote"),
            key: "countRemote",
          },
          {
            title: this.$t("dataLossPrevention.daysHybrid"),
            key: "countHybrid",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inOffice"),
            key: "percentageOfficeRecords",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inRemote"),
            key: "percentageRemoteRecords",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inHybrid"),
            key: "percentageOfficeOrRemoteRecords",
          },
        ];
      } else if (
        this.chartType == this.$t("dataLossPrevention.computerActivity")
      ) {
        return [
          {
            title: this.$t("dataLossPrevention.employeeName"),
            key: "employeeName",
          },
          {
            title:
              this.$t("dataLossPrevention.location") +
              " " +
              this.$t("dataLossPrevention.summary"),
            key: "Work_Location",
          },
          {
            title: this.$t("dataLossPrevention.computerActivity"),
            key: "sumOfComputerActivityTime",
          },
          {
            title:
              this.$t("dataLossPrevention.computerActivity") +
              " / " +
              this.$t("dataLossPrevention.day"),
            key: "averageComputerActivityTime",
          },
          {
            title: this.$t("dataLossPrevention.activeDays"),
            key: "activeDays",
          },
          {
            title: this.$t("dataLossPrevention.daysOffice"),
            key: "countOffice",
          },
          {
            title: this.$t("dataLossPrevention.daysRemote"),
            key: "countRemote",
          },
          {
            title: this.$t("dataLossPrevention.daysHybrid"),
            key: "countHybrid",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inOffice"),
            key: "percentageOfficeRecords",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inRemote"),
            key: "percentageRemoteRecords",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inHybrid"),
            key: "percentageOfficeOrRemoteRecords",
          },
        ];
      } else if (
        this.chartType == this.$t("dataLossPrevention.productiveTime")
      ) {
        return [
          {
            title: this.$t("dataLossPrevention.employeeName"),
            key: "employeeName",
          },
          {
            title:
              this.$t("dataLossPrevention.location") +
              " " +
              this.$t("dataLossPrevention.summary"),
            key: "Work_Location",
          },
          {
            title: this.$t("dataLossPrevention.productiveTime"),
            key: "sumOfProductiveAppsAndUrls",
          },
          {
            title:
              this.$t("dataLossPrevention.productiveTime") +
              " / " +
              this.$t("dataLossPrevention.day"),
            key: "averageProductiveAppsAndUrls",
          },
          {
            title: this.$t("dataLossPrevention.activeDays"),
            key: "activeDays",
          },
          {
            title: this.$t("dataLossPrevention.daysOffice"),
            key: "countOffice",
          },
          {
            title: this.$t("dataLossPrevention.daysRemote"),
            key: "countRemote",
          },
          {
            title: this.$t("dataLossPrevention.daysHybrid"),
            key: "countHybrid",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inOffice"),
            key: "percentageOfficeRecords",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inRemote"),
            key: "percentageRemoteRecords",
          },
          {
            title: "% " + this.$t("dataLossPrevention.inHybrid"),
            key: "percentageOfficeOrRemoteRecords",
          },
        ];
      }
      return [];
    },
  },
  mounted() {
    this.currentTabItem = "tab-0";
    this.getCurrentDateRange();
    this.retrieveCustomGroups();
    this.fetchWorkScheduleList();
    this.fetchEmpWorkScheduleDetails();
    this.fetchEmpDetails();
  },
  watch: {
    appliedDateRange() {
      if (this.isExceed31Days) {
        this.appliedDateRange = this.currentDateRange;
        this.isExceed31Days = false;
      }
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  methods: {
    onMoreAction(action) {
      this.openMoreMenu = false;
      if (action.key == this.$t("dataLossPrevention.export")) {
        this.exportFile();
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = JSON.parse(JSON.stringify(this.originalList));
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = JSON.parse(JSON.stringify(this.originalList));
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    exportFile() {
      let exportHeaders = [
        {
          header: this.$t("dataLossPrevention.employeeName"),
          key: "employeeName",
        },
        {
          header:
            this.$t("dataLossPrevention.location") +
            " " +
            this.$t("dataLossPrevention.summary"),
          key: "Work_Location",
        },
        {
          header: this.$t("dataLossPrevention.systemUpTime"),
          key: "sumOfSystemUpTime",
        },
        {
          header:
            this.$t("dataLossPrevention.systemUpTime") +
            " / " +
            this.$t("dataLossPrevention.day"),
          key: "averageSystemUpTime",
        },
        {
          header: this.$t("dataLossPrevention.activeTime"),
          key: "sumOfActiveTime",
        },
        {
          header:
            this.$t("dataLossPrevention.activeTime") +
            " / " +
            this.$t("dataLossPrevention.day"),
          key: "averageActiveTime",
        },
        {
          header: this.$t("dataLossPrevention.computerActivity"),
          key: "sumOfComputerActivityTime",
        },
        {
          header:
            this.$t("dataLossPrevention.computerActivity") +
            " / " +
            this.$t("dataLossPrevention.day"),
          key: "averageComputerActivityTime",
        },
        {
          header: this.$t("dataLossPrevention.productiveTime"),
          key: "sumOfProductiveAppsAndUrls",
        },
        {
          header:
            this.$t("dataLossPrevention.productiveTime") +
            " / " +
            this.$t("dataLossPrevention.day"),
          key: "averageProductiveAppsAndUrls",
        },
        {
          header: this.$t("dataLossPrevention.activeDays"),
          key: "activeDays",
        },
        {
          header: this.$t("dataLossPrevention.daysOffice"),
          key: "countOffice",
        },
        {
          header: this.$t("dataLossPrevention.daysRemote"),
          key: "countRemote",
        },
        {
          header: this.$t("dataLossPrevention.daysHybrid"),
          key: "countHybrid",
        },
        {
          header: "% " + this.$t("dataLossPrevention.inOffice"),
          key: "percentageOfficeRecords",
        },
        {
          header: "% " + this.$t("dataLossPrevention.inRemote"),
          key: "percentageRemoteRecords",
        },
        {
          header: "% " + this.$t("dataLossPrevention.inHybrid"),
          key: "percentageOfficeOrRemoteRecords",
        },
      ];
      let UserData = this.itemList;
      let exportOptions = {
        fileExportData: UserData,
        fileName:
          this.$t("dataLossPrevention.locationIntelligence") +
          " " +
          this.$t("dataLossPrevention.employeeData"),
        sheetName: this.$t("dataLossPrevention.locationIntelligence"),
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    onChangeDateRange(selectedDates, dateStr) {
      this.isExceed31Days = false;
      if (selectedDates.length > 1) {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        const startMoment = moment(selectedDates[0], orgDateFormat);
        const endMoment = moment(selectedDates[1], orgDateFormat);
        const diffInDays = endMoment.diff(startMoment, "days") + 1; // Adding 1 to include both the start and end dates
        if (diffInDays <= 31) {
          // Parse the dates from the given format
          let parsedStartDate = moment(selectedDates[0], "DD/MM/YYYY");
          let parsedEndDate = moment(selectedDates[1], "DD/MM/YYYY");

          // Format the dates into "YYYY-MM-DD" format
          this.startDate = parsedStartDate.format("YYYY-MM-DD");
          this.endDate = parsedEndDate.format("YYYY-MM-DD");
          this.fetchEmpDetails();
        } else {
          this.isExceed31Days = true;
        }
      } else {
        this.$emit("on-change-date-range", dateStr);
      }
      if (this.isExceed31Days) {
        this.appliedDateRange = this.currentDateRange;
        let snackbarData = {
          isOpen: true,
          message: this.$t("dataLossPrevention.dateRangeExceeded"),
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    getCurrentDateRange() {
      // Get current month's 1st date
      const firstDayOfMonth = moment().startOf("month").format("DD/MM/YYYY");
      // Get yesterday's date
      const yesterdayDate = moment().subtract(1, "day").format("DD/MM/YYYY");
      // Parse the dates from the given format
      let parsedStartDate = moment(firstDayOfMonth, "DD/MM/YYYY");
      let parsedEndDate = moment(yesterdayDate, "DD/MM/YYYY");
      const differenceInDays = parsedEndDate.diff(parsedStartDate, "days");
      if (differenceInDays > 0) {
        // Format the dates into "YYYY-MM-DD" format
        this.startDate = parsedStartDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.currentDateRange = firstDayOfMonth + " to " + yesterdayDate;
      } else {
        this.startDate = parsedEndDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.currentDateRange = yesterdayDate + " to " + yesterdayDate;
      }
      this.appliedDateRange = this.currentDateRange;
    },
    async retrieveCustomGroups() {
      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: "productivityMonitoring",
        })
        .then((groupList) => {
          if (groupList && groupList.length) {
            const customGroups = groupList;
            let CGList = [
              {
                Custom_Group_Name: "All",
                Custom_Group_Id: 0,
              },
            ];
            this.customGroupList = CGList.concat(customGroups);
          } else {
            this.customGroupList = [];
          }
        })
        .catch(() => {
          this.customGroupList = [];
        });
    },
    fetchWorkScheduleList() {
      let vm = this;
      vm.$apollo
        .query({
          query: LIST_WORK_SCHEDULE,
          client: "apolloClientC",
          variables: {
            formName: "cxo-dashboard",
          },
        })
        .then((response) => {
          const { errorCode, workSchedule } = response.data.listWorkSchedule;
          if (!errorCode && workSchedule) {
            let WSList = [
              {
                WorkSchedule_Name: "All",
                WorkSchedule_Id: 0,
                Time_Zone: "",
              },
            ];
            vm.workScheduleList = WSList.concat(workSchedule);
          } else {
            vm.handleWorkScheduleListErr();
          }
        })
        .catch(() => {
          vm.handleWorkScheduleListErr();
        });
    },

    handleWorkScheduleListErr() {
      this.workScheduleList = [];
    },

    onSelectCustomGroup(customGroup) {
      this.customGroupId = customGroup.Custom_Group_Id;
      this.selectedCustomGroup = customGroup.Custom_Group_Name;
      this.fetchEmpDetails();
    },

    onSelectWorkSchedule(workSchedule) {
      this.workScheduleMenu = false;
      this.employeeWorkScheduleId = workSchedule.WorkSchedule_Id;
      this.employeeWorkSchedule = workSchedule.WorkSchedule_Name;
      this.fetchEmpDetails();
      if (workSchedule.WorkSchedule_Name === "All") {
        this.employeeTimezone = this.loginEmployeeWSDetails.timeZone;
      } else {
        this.employeeTimezone = workSchedule.TimeZone_Id;
      }
    },

    async fetchEmpWorkScheduleDetails() {
      let vm = this;
      try {
        await this.$store
          .dispatch("getEmpWorkScheduleDetails", {
            employeeId: vm.loginEmployeeId,
          })
          .then((employeeWorkScheduleDetails) => {
            const { workSchedule, workScheduleId, timeZone } =
              employeeWorkScheduleDetails;
            vm.employeeWorkScheduleId = workScheduleId;
            vm.employeeWorkSchedule = workSchedule;
            vm.employeeTimezone = timeZone;
            vm.loginEmployeeWSDetails = employeeWorkScheduleDetails;
          })
          .catch(() => {
            vm.handleEmpWorkScheduleError();
          });
      } catch {
        vm.handleEmpWorkScheduleError();
      }
    },

    // set default value((1) when we get error while retrieving employee's work-schedule details
    handleEmpWorkScheduleError() {
      this.employeeWorkScheduleId = 1;
      this.employeeTimezone = "";
    },
    onSelectItem(value, field) {
      this[field] = value;
    },

    fetchEmpDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_USER_DATA_LIST,
          client: "apolloClientK",
          variables: {
            formId: 295,
            startDate: vm.startDate,
            endDate: vm.endDate,
            workScheduleId: vm.employeeWorkScheduleId,
            customGroupId: vm.customGroupId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.getUserWiseActivityData &&
            data.getUserWiseActivityData.userWiseActivityData &&
            data.getUserWiseActivityData.userWiseActivityData.length > 0
          ) {
            this.originalList =
              data.getUserWiseActivityData.userWiseActivityData;
            this.itemList = data.getUserWiseActivityData.userWiseActivityData;
          } else {
            this.originalList = [];
            this.itemList = [];
          }
          vm.listLoading = false;
        });
    },
  },
};
</script>
<style>
.insights-container {
  padding: 4em 2em 0em 3em;
}
@media screen and (max-width: 805px) {
  .insights-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
