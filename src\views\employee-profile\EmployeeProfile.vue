<template>
  <div>
    <AppTopBarTab
      v-if="mainTabs.length > 0"
      :tabs-list="mainTabs"
      :show-bottom-sheet="false"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    ></AppTopBarTab>
    <v-container fluid class="profile-container">
      <v-window v-model="currentTabItem">
        <v-window-item value="tab-0">
          <ProfileTopCard
            callingFrom="profile"
            actionType="edit"
            :empFormUpdateAccess="employeeFormUpdateAccess"
            :selectedEmpId="loginEmployeeId"
          ></ProfileTopCard>
          <AllProfileDetails
            callingFrom="profile"
            actionType="edit"
            :empFormUpdateAccess="employeeFormUpdateAccess"
            :selectedEmpId="loginEmployeeId"
          />
        </v-window-item>
      </v-window>
    </v-container>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import ProfileTopCard from "./profile-details/ProfileTopCard.vue";
import AllProfileDetails from "./profile-details/AllProfileDetails.vue";

export default defineComponent({
  name: "EmployeeProfile",

  components: { ProfileTopCard, AllProfileDetails },

  data() {
    return {
      currentTabItem: "",
    };
  },

  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    mainTabs() {
      let tabs = ["My Profile"];
      if (this.empDirViewRights()) {
        tabs.push("Employee Directory");
      }
      return tabs;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    employeeFormUpdateAccess() {
      let formAccess = this.accessRights("my-profile");
      if (formAccess && formAccess.accessRights["update"]) {
        return true;
      } else {
        return false;
      }
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },

  mounted() {
    this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("Employee Profile Error:", err);
    let msg =
      "Something went wrong while loading the employee profile form. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  methods: {
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onTabChange(tab) {
      if (tab !== "My Profile" && this.empDirViewRights) {
        window.location.href = this.baseUrl + "in/employees/employee-directory";
      }
    },
    empDirViewRights() {
      var formRights = this.accessRights("employee-directory");
      if (formRights && formRights.accessRights["view"]) {
        return true;
      } else {
        return false;
      }
    },
  },
});
</script>

<style>
.profile-container {
  padding: 5em 3em 0em 3em;
}

@media screen and (max-width: 805px) {
  .profile-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
