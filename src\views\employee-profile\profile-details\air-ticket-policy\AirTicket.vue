<template>
  <div v-if="listLoading || isLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs()"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else class="pb-12 mb-12">
    <div v-if="!openedEditForm" class="d-flex justify-end mt-n2 mr-n2">
      <v-icon @click="refetchAPIs()" size="17" color="grey"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <AirTicketPolicy
      :selectedEmpId="selectedEmpId"
      :airTicketDetails="airTicketDetails"
      :oldAirTicketData="oldAirTicketData"
      :formAccess="formAccess"
      :actionType="actionType"
      :callingFrom="callingFrom"
      @fetch-api="refetchAPIs()"
    />
    <AirTicketAccural
      v-if="selectedEmpStatus?.toLowerCase() === 'active'"
      class="py-2"
      :airTicketDetails="airTicketDetails"
    />
    <ViewAirticketSummary
      class="py-2"
      :settlementSummaryData="settlementSummaryData"
    />
  </div>
</template>
<script>
import {
  RETRIEVE_EMP_AIR_TICKET_POLICY,
  RETRIEVE_AIR_TICKET_SETTLEMENT_SUMMARY,
} from "@/graphql/corehr/payrollDataManagement.js";
import { RETRIEVE_EMPLOYEE_CHANGES } from "@/graphql/employee-profile/profileQueries";
import AirTicketPolicy from "./air-ticket/AirTicketPolicyView.vue";
import AirTicketAccural from "./air-ticket-accural/AirTicketAccural.vue";
import ViewAirticketSummary from "./airticket-settlement-summary/ViewAirticketSummary.vue";
export default {
  name: "ViewEmployeeDetails",
  components: { AirTicketPolicy, AirTicketAccural, ViewAirticketSummary },
  props: {
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
  },
  data() {
    return {
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      openedEditForm: false,
      airTicketDetails: [],
      oldAirTicketData: null,
      isLoading: false,
      settlementSummaryData: [],
    };
  },
  computed: {},
  mounted() {
    this.fetchAirTicketDetails();
    this.fetchSettlementSummary();
  },
  methods: {
    refetchAPIs() {
      this.isErrorInList = false;
      this.openedEditForm = false;
      this.fetchAirTicketDetails();
      this.fetchSettlementSummary();
    },
    fetchAirTicketDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_AIR_TICKET_POLICY,
          client: "apolloClientAC",
          variables: {
            employeeId: parseInt(vm.selectedEmpId),
            formId: vm.callingFrom === "profile" ? 323 : 322,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveEmpAirTicketPolicy &&
            response.data.retrieveEmpAirTicketPolicy.data &&
            !response.data.retrieveEmpAirTicketPolicy.errorCode
          ) {
            vm.airTicketDetails = response.data.retrieveEmpAirTicketPolicy.data;
            this.getEmployeeChanges();
          } else {
            vm.handleListError(
              response.data.retrieveEmpAirTicketPolicy?.errorCode || ""
            );
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    fetchSettlementSummary() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_AIR_TICKET_SETTLEMENT_SUMMARY,
          client: "apolloClientI",
          variables: {
            employeeId: parseInt(vm.selectedEmpId),
            formId: vm.callingFrom === "profile" ? 323 : 322,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveAirTicketSettlementSummary &&
            response.data.retrieveAirTicketSettlementSummary
              .settlementSummary &&
            !response.data.retrieveAirTicketSettlementSummary.errorCode
          ) {
            vm.settlementSummaryData =
              response.data.retrieveAirTicketSettlementSummary.settlementSummary;
            vm.isLoading = false;
          } else {
            vm.isLoading = false;
            vm.handleListError(
              response.data.retrieveAirTicketSettlementSummary?.errorCode
            );
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Air Ticket Details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    getEmployeeChanges() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMPLOYEE_CHANGES,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
            tables: ["emp_air_ticket_policy"],
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrievePendingEmployeeChanges
          ) {
            const { pendingChanges } =
              response.data.retrievePendingEmployeeChanges;
            if (
              pendingChanges &&
              pendingChanges.changes &&
              pendingChanges.changes.length > 0
            ) {
              for (let change of pendingChanges.changes) {
                if (change.Table_Name === "emp_air_ticket_policy") {
                  vm.oldAirTicketData = vm.airTicketDetails[0];
                  vm.airTicketDetails = [JSON.parse(change.New_Data)];
                }
              }
            }
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
  },
};
</script>
