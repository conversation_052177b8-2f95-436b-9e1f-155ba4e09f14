<template>
  <div class="rounded-lg ma-1" color="#FDFEFF">
    <div v-if="!showEditForm">
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="blue"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Air Ticket Policy</span
          >
        </div>
        <div
          v-if="
            formAccess?.add &&
            airTicketData &&
            Object.keys(airTicketData)?.length === 0 &&
            callingFrom === `team`
          "
        >
          <v-btn @click="openAddDialog" color="primary" variant="text">
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon>Add
          </v-btn>
        </div>
        <div v-else-if="enableEdit">
          <v-btn @click="openEditDialog" color="primary" variant="text">
            <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
          </v-btn>
        </div>
      </div>
      <div
        v-if="
          airTicketData &&
          Object.keys(airTicketData)?.length === 0 &&
          oldAirTicketData &&
          Object.keys(oldAirTicketData)?.length === 0
        "
        class="d-flex align-center justify-start fill-height text-h6 text-grey pa-4"
      >
        No Air Ticket Policy have been added
      </div>
      <v-row v-else class="pa-4 ma-2 card-blue-background">
        <FieldDiff
          :oldDataAvailable="oldAirTicketData ? true : false"
          label="Place of Origin"
          :newValue="
            airTicketData.Destination_City && airTicketData.Destination_Country
              ? airTicketData.Destination_City +
                ' - ' +
                airTicketData.Destination_Country
              : airTicketData.Destination_City
          "
          :oldValue="
            oldAirTicketData?.Destination_City &&
            oldAirTicketData?.Destination_Country
              ? oldAirTicketData.Destination_City +
                ' - ' +
                oldAirTicketData.Destination_Country
              : oldAirTicketData?.Destination_City
          "
        />
        <FieldDiff
          :oldDataAvailable="oldAirTicketData ? true : false"
          label="Air Ticket Category"
          :newValue="airTicketData.Air_Ticketing_Category"
          :oldValue="oldAirTicketData?.Air_Ticketing_Category"
        />
        <FieldDiff
          :oldDataAvailable="oldAirTicketData ? true : false"
          :label="
            'Air Fare Entitlement' +
            (payrollCurrency ? ` (in ${payrollCurrency})` : '')
          "
          :newValue="airFareEntitlement(airTicketData)"
          :oldValue="airFareEntitlement(oldAirTicketData)"
        />
        <FieldDiff
          :oldDataAvailable="oldAirTicketData ? true : false"
          label="Effective From"
          :newValue="airTicketData.Effective_Date_Enable"
          :oldValue="oldAirTicketData?.Effective_Date_Enable"
        />
        <FieldDiff
          :oldDataAvailable="oldAirTicketData ? true : false"
          label="Effective Date"
          :newValue="formatDate(airTicketData.Effective_Date)"
          :oldValue="
            oldAirTicketData
              ? formatDate(oldAirTicketData.Effective_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldAirTicketData ? true : false"
          label="Eligibility of Ticket Claim (in Months)"
          :newValue="airTicketData.Eligibility_Of_Ticket_Claim_Months"
          :oldValue="oldAirTicketData?.Eligibility_Of_Ticket_Claim_Months"
        />
        <FieldDiff
          :oldDataAvailable="oldAirTicketData ? true : false"
          label="Air Ticket to Dependent"
          :newValue="airTicketData.Air_Ticket_To_Dependent"
          :oldValue="oldAirTicketData?.Air_Ticket_To_Dependent"
        />
        <FieldDiff
          :oldDataAvailable="oldAirTicketData ? true : false"
          label="Dependent Relationship"
          :newValue="
            airTicketData?.Dependent_Relationship
              ? JSON.parse(airTicketData?.Dependent_Relationship)?.join(', ')
              : null
          "
          :oldValue="
            oldAirTicketData?.Dependent_Relationship
              ? JSON.parse(oldAirTicketData?.Dependent_Relationship)?.join(', ')
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldAirTicketData ? true : false"
          label="Status"
          :newValue="airTicketData.Status"
          :oldValue="oldAirTicketData?.Status"
        />
      </v-row>
    </div>
    <div v-else>
      <AirTicketPolicyEdit
        :selectedEmpId="selectedEmpId"
        :isEdit="isEdit"
        :airTicketDetails="airTicketDetails"
        :actionType="actionType"
        :callingFrom="callingFrom"
        @close-edit-form="closeEditForm()"
        @edit-updated="editUpdated()"
      />
    </div>
  </div>
</template>
<script>
import { checkNullValue } from "@/helper";
import moment from "moment";
import AirTicketPolicyEdit from "./AirTicketPolicyEdit.vue";
import { defineAsyncComponent } from "vue";
const FieldDiff = defineAsyncComponent(() =>
  import("@/components/custom-components/FieldDiff.vue")
);
export default {
  name: "AirTicketPolicyView",
  components: { AirTicketPolicyEdit, FieldDiff },
  props: {
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    airTicketDetails: {
      type: Array,
      default: () => [],
    },
    oldAirTicketData: {
      type: [Array, Object],
      required: false,
    },
  },
  data() {
    return {
      showEditForm: false,
      airTicketData: {},
      isEdit: false,
    };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.formAccess &&
        this.formAccess.update &&
        this.formAccess.admin === "admin" &&
        this.callingFrom === "team"
      );
    },
    airFareEntitlement() {
      return (data) => {
        if (data && Object.keys(data)?.length) {
          return `Infant - ${data.Infant_Amount || 0}, Child - ${
            data.Child_Amount || 0
          }, Adult - ${data.Adult_Amount || 0}`;
        } else {
          return "";
        }
      };
    },
  },
  watch: {
    airTicketDetails: {
      handler(newVal) {
        this.airTicketData = newVal?.length ? newVal[0] : newVal || {};
      },
      deep: true,
    },
  },

  mounted() {
    this.airTicketData = this.airTicketDetails?.length
      ? this.airTicketDetails[0]
      : this.airTicketDetails || {};
  },

  methods: {
    checkNullValue,
    editUpdated() {
      this.showEditForm = false;
      this.isEdit = false;
      this.$emit("fetch-api");
    },
    openAddDialog() {
      this.isEdit = false;
      this.showEditForm = true;
    },
    openEditDialog() {
      this.isEdit = true;
      this.showEditForm = true;
    },
    closeEditForm() {
      this.isEdit = false;
      this.showEditForm = false;
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
