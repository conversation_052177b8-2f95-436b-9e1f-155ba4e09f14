<template>
  <div
    v-if="awardDetails && awardDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
  >
    No awards have been received
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in awardArray"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:550px; border-left: 7px solid ${generateRandomColor()}; height:180px;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip
                  :text="data.newAward?.Award_Name || data.oldAward?.Award_Name"
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="
                        data.newAward?.Award_Name || data.oldAward?.Award_Name
                          ? props
                          : ''
                      "
                    >
                      <span
                        v-if="
                          data.oldAward?.Award_Name &&
                          data.newAward?.Award_Name &&
                          data.oldAward?.Award_Name?.toLowerCase() !==
                            data.newAward?.Award_Name?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{ checkNullValue(data.oldAward.Award_Name) }}
                      </span>
                      <span
                        v-if="data.newAward"
                        :class="[
                          (data.oldAward &&
                            data.oldAward.Award_Name?.toLowerCase() !==
                              data.newAward.Award_Name?.toLowerCase()) ||
                          (!data.oldAward && oldAwardDetails)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{ checkNullValue(data.newAward?.Award_Name) }}
                      </span>
                      <span
                        v-else-if="data.oldAward"
                        class="text-error text-decoration-line-through"
                      >
                        {{ formatDate(data.oldAward.Award_Name) }}
                      </span>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n6">
        <span
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Received on </b>
              <span class="mb-3">
                <span
                  v-if="
                    data.oldAward?.Received_On &&
                    data.newAward?.Received_On &&
                    data.oldAward?.Received_On !== data.newAward?.Received_On
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ formatDate(data.oldAward.Received_On) }}
                </span>
                <span
                  v-if="data.newAward"
                  :class="[
                    (data.oldAward &&
                      data.oldAward.Received_On?.toLowerCase() !==
                        data.newAward.Received_On?.toLowerCase()) ||
                    (!data.oldAward && oldAwardDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ formatDate(data.newAward?.Received_On) }}
                </span>
                <span
                  v-else-if="data.oldAward"
                  class="text-error text-decoration-line-through"
                >
                  {{ formatDate(data.oldAward.Received_On) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Received For </b>
              <span class="mb-3">
                <span
                  v-if="
                    data.oldAward?.Received_For &&
                    data.newAward?.Received_For &&
                    data.oldAward?.Received_For?.toLowerCase() !==
                      data.newAward?.Received_For?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldAward.Received_For) }}
                </span>
                <span
                  v-if="data.newAward"
                  :class="[
                    (data.oldAward &&
                      data.oldAward.Received_For?.toLowerCase() !==
                        data.newAward.Received_For?.toLowerCase()) ||
                    (!data.oldAward && oldAwardDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newAward?.Received_For) }}
                </span>
                <span
                  v-else-if="data.oldAward"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldAward.Received_For) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-40px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Received From </b>
              <span class="mb-3">
                <span
                  v-if="
                    data.oldAward?.Received_From &&
                    data.newAward?.Received_From &&
                    data.oldAward?.Received_From?.toLowerCase() !==
                      data.newAward?.Received_From?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldAward.Received_From) }}
                </span>
                <span
                  v-if="data.newAward"
                  :class="[
                    (data.oldAward &&
                      data.oldAward.Received_From?.toLowerCase() !==
                        data.newAward.Received_From?.toLowerCase()) ||
                    (!data.oldAward && oldAwardDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newAward?.Received_From) }}
                </span>
                <span
                  v-else-if="data.oldAward"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldAward.Received_From) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import moment from "moment";
import { generateRandomColor } from "@/helper";
import { checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ViewAwardDetails",
  components: { ActionMenu },
  props: {
    awardDetails: {
      type: Object,
      required: true,
    },
    oldAwardDetails: {
      type: [Array, Boolean],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return { havingAccess: {} };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    awardArray() {
      const oldAward = this.oldAwardDetails || [];
      const newAward = this.awardDetails || [];

      let idSet = new Set();
      let mergedDetails = [];

      newAward.forEach((newItem) => {
        const id = newItem.Award_Id;
        idSet.add(id);
        const oldItem = oldAward.find((old) => old.Award_Id === id);
        mergedDetails.push({
          newAward: newItem,
          oldAward: oldItem || null,
        });
      });

      oldAward.forEach((oldItem) => {
        const id = oldItem.Award_Id;
        if (!idSet.has(id)) {
          mergedDetails.push({
            newAward: null,
            oldAward: oldItem,
          });
        }
      });

      return mergedDetails;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.awardDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", [selectedActionItem, "award"]);
      } else {
        this.$emit("on-open-edit", [selectedActionItem, "award"]);
      }
    },
  },
};
</script>
