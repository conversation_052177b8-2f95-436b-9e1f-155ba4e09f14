<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Education Details</span
        >
        <v-spacer></v-spacer>
        <v-icon color="grey" size="25" @click="$emit('close-education-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditEducationForm">
          <v-row>
            <v-col
              v-if="labelList[161].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="courseList"
                :label="labelList[161].Field_Alias"
                :itemSelected="
                  educationFormData.Education_Type
                    ? parseInt(educationFormData.Education_Type)
                    : null
                "
                :rules="[
                  labelList[161].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[161].Field_Alias}`,
                        educationFormData.Education_Type
                      )
                    : true,
                ]"
                itemValue="Course_Id"
                itemTitle="Course_Name"
                :isRequired="labelList[161].Mandatory_Field == 'Yes'"
                @selected-item="
                  onChangeCustomSelectField($event, 'Education_Type')
                "
                :isAutoComplete="true"
                :isLoading="courseListFetching"
                :noDataText="
                  courseListFetching ? 'Loading...' : 'No data available'
                "
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[162].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                v-if="labelList[162].Predefined === 'Yes'"
                :items="specializationList"
                :label="labelList[162].Field_Alias"
                :itemSelected="
                  educationFormData.Specialization_Id
                    ? parseInt(educationFormData.Specialization_Id)
                    : null
                "
                :rules="[
                  labelList[162].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[162].Field_Alias}`,
                        educationFormData.Specialization_Id
                      )
                    : true,
                ]"
                itemValue="Specialization_Id"
                itemTitle="Specialization"
                :isRequired="labelList[162].Mandatory_Field == 'Yes'"
                @selected-item="
                  onChangeCustomSelectField($event, 'Specialization_Id')
                "
                :isAutoComplete="true"
                :isLoading="instituteAndSpecializationLoading"
                :noDataText="
                  instituteAndSpecializationLoading
                    ? 'Loading...'
                    : 'No data available'
                "
              ></CustomSelect>
              <v-text-field
                v-else
                v-model="educationFormData.Specialisation"
                :rules="[
                  labelList[162].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[162].Field_Alias}`,
                        educationFormData.Specialisation
                      )
                    : true,
                  educationFormData.Specialisation
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.Specialisation,
                        'specialisation',
                        `${labelList[162].Field_Alias}`
                      )
                    : true,
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  <span>{{ labelList[162].Field_Alias }}</span>
                  <span
                    v-if="labelList[162].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <v-col
              v-if="labelList[163].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                v-if="labelList[163].Predefined === 'Yes'"
                :items="institutionList"
                :label="labelList[163].Field_Alias"
                :itemSelected="
                  educationFormData.Institution_Id
                    ? parseInt(educationFormData.Institution_Id)
                    : null
                "
                :rules="[
                  labelList[163].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[163].Field_Alias}`,
                        educationFormData.Institution_Id
                      )
                    : true,
                ]"
                itemValue="Institution_Id"
                itemTitle="Institution"
                :isRequired="labelList[163].Mandatory_Field == 'Yes'"
                @selected-item="
                  onChangeCustomSelectField($event, 'Institution_Id')
                "
                :isAutoComplete="true"
                :isLoading="instituteAndSpecializationLoading"
                :noDataText="
                  instituteAndSpecializationLoading
                    ? 'Loading...'
                    : 'No data available'
                "
              ></CustomSelect>
              <v-text-field
                v-else
                v-model="educationFormData.Institute_Name"
                :rules="[
                  labelList[163].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[163].Field_Alias}`,
                        educationFormData.Institute_Name
                      )
                    : true,
                  educationFormData.Institute_Name
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.Institute_Name,
                        'instituteName',
                        `${labelList[163].Field_Alias}`
                      )
                    : true,
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  <span>{{ labelList[163].Field_Alias }}</span>
                  <span
                    v-if="labelList[163].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col
              v-if="labelList[164].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <v-text-field
                v-model="educationFormData.University"
                :rules="[
                  labelList[164].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[164].Field_Alias}`,
                        educationFormData.University
                      )
                    : true,
                  educationFormData.University
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.University,
                        'university',
                        `${labelList[164].Field_Alias}`
                      )
                    : true,
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  <span>{{ labelList[164].Field_Alias }}</span>
                  <span
                    v-if="labelList[164].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col
              v-if="labelList[293].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="yearList"
                :label="labelList[293].Field_Alias"
                :itemSelected="educationFormData.Year_Of_Start"
                :rules="[
                  labelList[293].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[293].Field_Alias}`,
                        educationFormData.Year_Of_Start
                      )
                    : true,
                ]"
                :isRequired="labelList[293].Mandatory_Field == 'Yes'"
                @selected-item="
                  onChangeCustomSelectField($event, 'Year_Of_Start')
                "
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[167].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="yearList"
                :label="labelList[167].Field_Alias"
                :itemSelected="educationFormData.Year_Of_Passing"
                :rules="[
                  labelList[167].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[167].Field_Alias}`,
                        educationFormData.Year_Of_Passing
                      )
                    : true,
                ]"
                :isRequired="labelList[167].Mandatory_Field == 'Yes'"
                @selected-item="
                  onChangeCustomSelectField($event, 'Year_Of_Passing')
                "
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[168].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <v-menu
                v-model="startDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="Start Date"
                    v-model="formattedStartDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="
                      labelList['168'].Mandatory_Field === 'Yes'
                        ? [
                            required(
                              labelList['168'].Field_Alias,
                              formattedStartDate
                            ),
                          ]
                        : [true]
                    "
                    variant="solo"
                    readonly
                    v-bind="props"
                  >
                    <template v-slot:label>
                      {{ labelList["168"].Field_Alias }}
                      <span
                        v-if="labelList['168'].Mandatory_Field === 'Yes'"
                        style="color: red"
                        >*</span
                      >
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="educationFormData.Start_Date"
                  :min="new Date(selectedStartDate)"
                  :max="new Date(educationStartMax)"
                  @update:model-value="onChangeFields"
                ></v-date-picker>
              </v-menu>
            </v-col>
            <v-col
              v-if="labelList[169].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <v-menu
                v-model="endDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="End Date"
                    v-model="formattedEndDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="
                      labelList['169'].Mandatory_Field === 'Yes'
                        ? [
                            required(
                              labelList['169'].Field_Alias,
                              formattedEndDate
                            ),
                          ]
                        : [true]
                    "
                    variant="solo"
                    readonly
                    v-bind="props"
                  >
                    <template v-slot:label>
                      {{ labelList["169"].Field_Alias }}
                      <span
                        v-if="labelList['169'].Mandatory_Field === 'Yes'"
                        style="color: red"
                        >*</span
                      >
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="educationFormData.End_Date"
                  :min="new Date(educationEndDateMin)"
                  @update:model-value="onChangeFields"
                ></v-date-picker>
              </v-menu>
            </v-col>
            <v-col
              v-if="labelList[170].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="cityList"
                :label="labelList[170].Field_Alias"
                item-value="City_Name"
                item-title="cityStateDetails"
                :isLoading="isCityListLoading"
                :ref="'City'"
                :isAutoComplete="true"
                :isRequired="labelList[170].Mandatory_Field == 'Yes'"
                :rules="[
                  labelList[170].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[170].Field_Alias}`,
                        educationFormData.City
                      )
                    : true,
                ]"
                :noDataText="
                  isCityListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="educationFormData.City"
                @selected-item="onChangeCustomSelectField($event, 'City')"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[171].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <formTextFeild
                :ref="'State'"
                v-model="educationFormData.State"
                :rules="[
                  labelList[171].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[171].Field_Alias}`,
                        educationFormData.State
                      )
                    : true,
                  educationFormData.State
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.State,
                        'state',
                        `${labelList[171].Field_Alias}`
                      )
                    : true,
                ]"
              >
                <template v-slot:label>
                  <span>{{ labelList[171].Field_Alias }}</span>
                  <span
                    v-if="labelList[171].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></formTextFeild
              >
            </v-col>
            <v-col
              v-if="labelList[172].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                v-model="educationFormData.Country"
                :items="countryList"
                :label="labelList[172].Field_Alias"
                :isAutoComplete="true"
                :isLoading="countryListLoading"
                :noDataText="
                  countryListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="
                  educationFormData.Country ? educationFormData.Country : ''
                "
                itemValue="Country_Code"
                itemTitle="Country_Name"
                :isRequired="labelList[172].Mandatory_Field == 'Yes'"
                :rules="[
                  labelList[172].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[172].Field_Alias}`,
                        educationFormData.Country
                      )
                    : true,
                ]"
                ref="Country"
                @selected-item="onChangeCustomSelectField($event, 'Country')"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[165].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <v-text-field
                v-model="educationFormData.Percentage"
                :rules="[
                  labelList[165].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[165].Field_Alias}`,
                        educationFormData.Percentage
                      )
                    : true,
                  educationFormData.Percentage
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.Percentage,
                        'percentage',
                        `${labelList[165].Field_Alias}`
                      )
                    : true,
                ]"
                variant="solo"
                type="number"
                @update:model-value="onChangeFields"
                ><template v-slot:label>
                  <span>{{ labelList[165].Field_Alias }}</span>
                  <span
                    v-if="labelList[165].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col
              v-if="labelList[166].Field_Visiblity == 'Yes'"
              cols="12"
              md="6"
            >
              <v-text-field
                v-model="educationFormData.Grade"
                :rules="[
                  labelList[166].Mandatory_Field == 'Yes'
                    ? required(
                        `${labelList[166].Field_Alias}`,
                        educationFormData.Grade
                      )
                    : true,
                  educationFormData.Grade
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.Grade,
                        'grade',
                        `${labelList[166].Field_Alias}`
                      )
                    : true,
                ]"
                @update:model-value="formatInput"
                variant="solo"
                ><template v-slot:label>
                  <span>{{ labelList[166].Field_Alias }}</span>
                  <span
                    v-if="labelList[166].Mandatory_Field == 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="this.$emit('close-education-form')"
                  class="ma-2 pa-2"
                  variant="outlined"
                  >Cancel</v-btn
                >
                <v-btn
                  class="ma-2 pa-2"
                  :disabled="!isFormDirty"
                  color="primary"
                  @click="validateEducationDetails"
                  >Save</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules.js";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { ADD_UPDATE_EDUCATION_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import { LIST_CITIES } from "@/graphql/dropDownQueries.js";
import {
  LIST_COURSE,
  LIST_SPECIALIZATION_INSTITUTE,
} from "@/graphql/dropDownQueries";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AddEditEducationDetails",
  mixins: [validationRules],
  props: {
    selectedEducationDetails: {
      type: Object,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
  },
  components: {
    CustomSelect,
  },
  emits: ["refetch-career-details", "close-education-form"],
  data() {
    return {
      educationFormData: {
        Education_Type: null,
        Specialisation: "",
        Institute_Name: "",
        Institution_Name_table: "",
        Specialization_Name: "",
        University: "",
        Year_Of_Start: null,
        Year_Of_Passing: null,
        Percentage: null,
        Grade: "",
        Institution_Id: null,
        Specialization_Id: null,
      },
      backupEducationFormData: {},
      // list
      courseListFetching: false,
      courseList: [],
      institutionList: [],
      specializationList: [],
      instituteAndSpecializationLoading: false,
      // edit
      isFormDirty: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      //Date-picker
      formattedStartDate: "",
      formattedEndDate: "",
      startDateMenu: false,
      endDateMenu: false,
      cityList: [],
      isCityListLoading: false,
      countryListLoading: false,
      countryList: [],
    };
  },
  watch: {
    "educationFormData.City": function (val) {
      for (let item of this.cityList) {
        if (item && item.cityStateDetails && item.City_Name == val) {
          let details = item.cityStateDetails.split(", ");
          this.educationFormData["State"] = details[1];
          this.educationFormData["Country"] = item.Country_Code;
        }
      }
    },
    "educationFormData.Start_Date": function (val) {
      if (val) {
        this.startDateMenu = false;
        this.formattedStartDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "educationFormData.End_Date": function (val) {
      if (val) {
        this.endDateMenu = false;
        this.formattedEndDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedEducationDetails &&
      Object.keys(this.selectedEducationDetails).length > 0
    ) {
      this.educationFormData = JSON.parse(
        JSON.stringify(this.selectedEducationDetails)
      );
      if (this.educationFormData.Start_Date) {
        this.formattedStartDate = this.formatDate(
          this.educationFormData?.Start_Date
        );
        this.educationFormData.Start_Date = this.educationFormData.Start_Date
          ? new Date(this.educationFormData.Start_Date)
          : null;
      }
      if (this.educationFormData.End_Date) {
        this.formattedEndDate = this.formatDate(
          this.educationFormData?.End_Date
        );
        this.educationFormData.End_Date = this.educationFormData.End_Date
          ? new Date(this.educationFormData.End_Date)
          : null;
      }
      if (this.educationFormData.Institution_Name_table) {
        this.educationFormData.Institute_Name =
          this.educationFormData.Institution_Name_table;
      }
      if (this.educationFormData.Specialization_Name) {
        this.educationFormData.Specialisation =
          this.educationFormData.Specialization_Name;
      }
      this.backupEducationFormData = JSON.parse(
        JSON.stringify(this.educationFormData)
      );
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
    this.retrieveEducationCourse();
    this.listEduInstitutionAndSpecializatio();
    this.listCity();
    this.retrieveCountries();
  },

  // function for getting the available years
  computed: {
    yearList() {
      let empDob = this.selectedEmployeeDob;
      let year = 0;
      if (empDob) {
        year = new Date().getFullYear() - new Date(empDob).getFullYear();
      } else {
        year = 80;
      }
      const now = new Date().getUTCFullYear();
      const years = Array(now - (now - year))
        .fill("")
        .map((v, idx) => now - idx);
      return years;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    currentDate() {
      const today = new Date();
      return today;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    selectedStartDate() {
      if (
        this.selectedEmployeeDob &&
        this.selectedEmployeeDob !== "0000-00-00"
      ) {
        return new Date(this.selectedEmployeeDob);
      } else return null;
    },
    educationStartMax() {
      if (
        this.educationFormData.End_Date &&
        this.educationFormData.End_Date !== "0000-00-00"
      ) {
        const issueDateMs = new Date(this.educationFormData.End_Date);
        return issueDateMs;
      }
      return this.currentDate;
    },
    educationEndDateMin() {
      if (
        this.educationFormData.Start_Date &&
        this.educationFormData.Start_Date !== "0000-00-00"
      ) {
        const issueDateMs = new Date(this.educationFormData.Start_Date);
        return issueDateMs;
      }
      return this.selectedStartDate;
    },
  },

  methods: {
    // method to transform the text into upperCase
    formatInput() {
      this.educationFormData.Grade = this.educationFormData.Grade.toUpperCase();
      this.onChangeFields();
    },

    onChangeFields() {
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.educationFormData[field] = value;
      if (field === "Institution_Id") {
        this.educationFormData["Institute_Name"] = this.institutionList.find(
          (el) => el.Institution_Id === value
        )?.Institution;
      }
    },

    async validateEducationDetails() {
      const { valid } = await this.$refs.addEditEducationForm.validate();
      mixpanel.track("EmpProfile-career-edu-submit-click");
      if (valid) {
        this.educationFormData.Start_Date = moment(
          this.educationFormData.Start_Date
        ).isValid()
          ? moment(this.educationFormData.Start_Date).format("YYYY-MM-DD")
          : null;
        this.educationFormData.End_Date = moment(
          this.educationFormData.End_Date
        ).isValid()
          ? moment(this.educationFormData.End_Date).format("YYYY-MM-DD")
          : null;
        this.backupEducationFormData.Start_Date = moment(
          this.backupEducationFormData.Start_Date
        ).isValid()
          ? moment(this.backupEducationFormData.Start_Date).format("YYYY-MM-DD")
          : null;
        this.backupEducationFormData.End_Date = moment(
          this.backupEducationFormData.End_Date
        ).isValid()
          ? moment(this.backupEducationFormData.End_Date).format("YYYY-MM-DD")
          : null;
        this.backupEducationFormData.Percentage = this.backupEducationFormData
          .Percentage
          ? "" + this.backupEducationFormData.Percentage
          : null;
        if (
          JSON.stringify(this.educationFormData) ===
          JSON.stringify(this.backupEducationFormData)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.updateEducationDetails();
        }
      }
    },

    updateEducationDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_EDUCATION_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            educationId: vm.educationFormData.Education_Id,
            educationType: parseInt(vm.educationFormData.Education_Type),
            Specialization_Id: parseInt(vm.educationFormData.Specialization_Id),
            Institution_Id: parseInt(vm.educationFormData.Institution_Id),
            specialisation: vm.educationFormData.Specialisation,
            instituteName: vm.educationFormData.Institute_Name,
            university: vm.educationFormData.University,
            yearOfStart: parseInt(vm.educationFormData.Year_Of_Start),
            yearOfPassing: parseInt(vm.educationFormData.Year_Of_Passing),
            startDate: moment(vm.educationFormData.Start_Date).isValid()
              ? moment(vm.educationFormData.Start_Date).format("YYYY-MM-DD")
              : null,
            endDate: moment(vm.educationFormData.End_Date).isValid()
              ? moment(vm.educationFormData.End_Date).format("YYYY-MM-DD")
              : null,
            city: vm.educationFormData.City,
            state: vm.educationFormData.State,
            country: vm.educationFormData.Country,
            percentage: parseFloat(vm.educationFormData.Percentage),
            grade: vm.educationFormData.Grade,
            formId: vm.callingFrom === "profile" ? 18 : 243,
            formStatus: vm.actionType === "edit" ? 1 : 0,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          const { message } = res.data.addUpdateEducationDetails;
          mixpanel.track("EmpProfile-career-edu-update-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: message?.includes("approval")
              ? "Education details submitted for approval."
              : vm.formType === "edit"
              ? "Education details updated successfully"
              : "Education details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-career-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-career-edu-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "education details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveEducationCourse() {
      let vm = this;
      vm.courseListFetching = true;
      vm.$apollo
        .query({
          query: LIST_COURSE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listCourseDetails &&
            !response.data.listCourseDetails.errorCode
          ) {
            const { courseDetails } = response.data.listCourseDetails;
            vm.courseList =
              courseDetails && courseDetails.length > 0 ? courseDetails : [];
          }
          vm.courseListFetching = false;
        })
        .catch(() => {
          vm.courseListFetching = false;
        });
    },
    listEduInstitutionAndSpecializatio() {
      let vm = this;
      vm.instituteAndSpecializationLoading = true;
      vm.$apollo
        .query({
          query: LIST_SPECIALIZATION_INSTITUTE,
          client: "apolloClientV",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveListEduInstitutionAndSpecialization &&
            response.data.retrieveListEduInstitutionAndSpecialization
              .institution &&
            response.data.retrieveListEduInstitutionAndSpecialization
              .institution.length > 0
          ) {
            this.institutionList =
              response.data.retrieveListEduInstitutionAndSpecialization.institution;
            this.specializationList =
              response.data.retrieveListEduInstitutionAndSpecialization.specialization;
          }
          vm.instituteAndSpecializationLoading = false;
        })
        .catch(() => {
          vm.instituteAndSpecializationLoading = false;
          this.institutionList = [];
          this.specializationList = [];
        });
    },
    listCity() {
      let vm = this;
      vm.isCityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES,
          client: "apolloClientAC",
          variables: {
            Form_Id: 243,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCityListWithState &&
            !response.data.getCityListWithState.errorCode
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            vm.cityList = cityDetails;
          }
          vm.isCityListLoading = false;
        })
        .catch(() => {
          vm.isCityListLoading = false;
        });
    },
    async retrieveCountries() {
      this.countryListLoading = true;
      this.countryList = [];
      await this.$store
        .dispatch("listCountries")
        .then((langList) => {
          this.countryList = langList;
          this.countryListLoading = false;
        })
        .catch(() => {
          this.countryListLoading = false;
        });
    },
  },
};
</script>
