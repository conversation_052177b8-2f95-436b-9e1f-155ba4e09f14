<template>
  <div
    v-if="educationDetails && educationDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
  >
    No education details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in educationArray"
    :key="index"
    class="card-item d-flex rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:250px; max-width:650px; border-left: 7px solid ${generateRandomColor()}; height:auto;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="mr-2 d-flex flex-column justify-start">
                <v-tooltip
                  :text="data.newEdu?.Course_Name || data.oldEdu?.Course_Name"
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="
                        data.newEdu?.Course_Name || data.oldEdu?.Course_Name
                          ? props
                          : ''
                      "
                    >
                      <span
                        v-if="
                          data.oldEdu?.Course_Name &&
                          data.newEdu?.Course_Name &&
                          data.oldEdu?.Course_Name?.toLowerCase() !==
                            data.newEdu?.Course_Name?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1 text-primary font-weight-bold text-h6"
                      >
                        {{ checkNullValue(data.oldEdu.Course_Name) }}
                      </span>
                      <span
                        v-if="data.newEdu"
                        :class="[
                          'text-primary font-weight-bold text-h6',
                          (data.oldEdu &&
                            data.oldEdu.Course_Name?.toLowerCase() !==
                              data.newEdu.Course_Name?.toLowerCase()) ||
                          (!data.oldEdu && oldEducationDetails)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{ checkNullValue(data.newEdu?.Course_Name) }}
                      </span>
                      <span
                        v-else-if="data.oldEdu"
                        class="text-error text-decoration-line-through"
                      >
                        {{ checkNullValue(data.oldEdu.Course_Name) }}
                      </span>
                      <span
                        v-if="
                          data.newEdu?.Year_Of_Passing ||
                          data.oldEdu?.Year_Of_Passing
                        "
                      >
                        -
                        <span
                          v-if="
                            data.oldEdu?.Year_Of_Passing &&
                            data.newEdu?.Year_Of_Passing &&
                            data.oldEdu?.Year_Of_Passing?.toString() !==
                              data.newEdu?.Year_Of_Passing?.toString()
                          "
                          class="text-decoration-line-through text-error mr-1 text-primary font-weight-bold text-h6"
                        >
                          {{ checkNullValue(data.oldEdu.Year_Of_Passing) }}
                        </span>
                        <span
                          v-if="data.newEdu"
                          :class="[
                            'text-primary font-weight-bold text-h6',
                            (data.oldEdu &&
                              data.oldEdu.Year_Of_Passing?.toString() !==
                                data.newEdu.Year_Of_Passing?.toString()) ||
                            (!data.oldEdu && oldEducationDetails)
                              ? 'text-success'
                              : '',
                          ]"
                        >
                          {{ checkNullValue(data.newEdu?.Year_Of_Passing) }}
                        </span>
                        <span
                          v-else-if="data.oldEdu"
                          class="text-error text-decoration-line-through"
                        >
                          {{ checkNullValue(data.oldEdu.Year_Of_Passing) }}
                        </span>
                      </span>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n8">
        <span
          class="d-flex align-start flex-column ml-3 my-3"
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div
              v-if="labelList[162].Field_Visiblity == 'Yes'"
              class="mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[162].Field_Alias }}
              </b>
              <span class="py-2 text-truncate" :style="{ maxWidth: '225px' }">
                <div
                  v-if="isSpecializationChanged(data)"
                  class="text-decoration-line-through text-error mr-1"
                >
                  <v-tooltip
                    :text="
                      data.oldEdu.Specialization_Id
                        ? data.oldEdu.Specialization_Name
                        : data.oldEdu.Specialisation
                    "
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="
                          (data.oldEdu.Specialization_Id
                            ? data.oldEdu.Specialization_Name
                            : data.oldEdu.Specialisation
                          )?.length > 20
                            ? props
                            : {}
                        "
                      >
                        {{
                          data.oldEdu.Specialization_Id
                            ? checkNullValue(data.oldEdu.Specialization_Name)
                            : checkNullValue(data.oldEdu.Specialisation)
                        }}
                      </span>
                    </template>
                  </v-tooltip>
                </div>
                <div
                  v-if="data.newEdu"
                  :class="[
                    (data.oldEdu &&
                      ((data.oldEdu.Specialization_Id &&
                        data.oldEdu?.Specialization_Id?.toString() !==
                          data.newEdu?.Specialization_Id?.toString()) ||
                        (data.oldEdu.Specialisation &&
                          data.oldEdu?.Specialisation?.toLowerCase() !==
                            data.newEdu?.Specialisation?.toLowerCase()))) ||
                    (!data.oldEdu && oldEducationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  <v-tooltip
                    :text="
                      data.newEdu.Specialization_Id
                        ? data.newEdu.Specialization_Name
                        : data.newEdu.Specialisation
                    "
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="
                          (data.newEdu.Specialization_Id
                            ? data.newEdu.Specialization_Name
                            : data.newEdu.Specialisation
                          )?.length > 20
                            ? props
                            : {}
                        "
                      >
                        {{
                          data.newEdu.Specialization_Id
                            ? checkNullValue(data.newEdu.Specialization_Name)
                            : checkNullValue(data.newEdu.Specialisation)
                        }}
                      </span>
                    </template>
                  </v-tooltip>
                </div>
                <span
                  v-else-if="data.oldEdu"
                  class="text-error text-decoration-line-through"
                >
                  <v-tooltip
                    :text="
                      data.oldEdu.Specialization_Id
                        ? data.oldEdu.Specialization_Name
                        : data.oldEdu.Specialisation
                    "
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="
                          (data.oldEdu.Specialization_Id
                            ? data.oldEdu.Specialization_Name
                            : data.oldEdu.Specialisation
                          )?.length > 20
                            ? props
                            : {}
                        "
                      >
                        {{
                          data.oldEdu.Specialization_Id
                            ? checkNullValue(data.oldEdu.Specialization_Name)
                            : checkNullValue(data.oldEdu.Specialisation)
                        }}
                      </span>
                    </template>
                  </v-tooltip>
                </span>
              </span>
            </div>
            <div
              v-if="labelList[164].Field_Visiblity == 'Yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[164].Field_Alias }}
              </b>
              <span style="width: 100%" class="py-2">
                <span
                  v-if="
                    data.oldEdu?.University &&
                    data.newEdu?.University &&
                    data.oldEdu?.University?.toLowerCase() !==
                      data.newEdu?.University?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldEdu.University) }}
                </span>
                <div
                  v-if="data.newEdu"
                  :class="[
                    (data.oldEdu &&
                      data.oldEdu.University?.toLowerCase() !==
                        data.newEdu.University?.toLowerCase()) ||
                    (!data.oldEdu && oldEducationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newEdu?.University) }}
                </div>
                <span
                  v-else-if="data.oldEdu"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldEdu.University) }}
                </span>
              </span>
            </div>
            <div
              v-if="labelList[166].Field_Visiblity == 'Yes'"
              class="d-flex flex-column justify-start"
            >
              <b class="mt-1 mr-2 text-grey justify-start"
                >{{ labelList[166].Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldEdu?.Grade &&
                    data.newEdu?.Grade &&
                    data.oldEdu?.Grade?.toLowerCase() !==
                      data.newEdu?.Grade?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldEdu.Grade) }}
                </span>
                <span
                  v-if="data.newEdu"
                  :class="[
                    (data.oldEdu &&
                      data.oldEdu.Grade?.toLowerCase() !==
                        data.newEdu.Grade?.toLowerCase()) ||
                    (!data.oldEdu && oldEducationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newEdu?.Grade) }}
                </span>
                <span
                  v-else-if="data.oldEdu"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldEdu.Grade) }}
                </span>
              </span>
            </div>
            <div
              v-if="labelList[293]?.Field_Visiblity?.toLowerCase() == 'yes'"
              class="d-flex flex-column justify-start"
            >
              <b class="mt-1 mr-2 text-grey justify-start"
                >{{ labelList[293].Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldEdu?.Year_Of_Start &&
                    data.newEdu?.Year_Of_Start &&
                    data.oldEdu?.Year_Of_Start?.toLowerCase() !==
                      data.newEdu?.Year_Of_Start?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldEdu.Year_Of_Start) }}
                </span>
                <span
                  v-if="data.newEdu"
                  :class="[
                    (data.oldEdu &&
                      data.oldEdu.Year_Of_Start?.toLowerCase() !==
                        data.newEdu.Year_Of_Start?.toLowerCase()) ||
                    (!data.oldEdu && oldEducationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newEdu?.Year_Of_Start) }}
                </span>
                <span
                  v-else-if="data.oldEdu"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldEdu.Year_Of_Start) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
        <span
          class="d-flex align-start flex-column ml-3 my-3"
          :style="
            !isMobileView
              ? 'width:50%'
              : 'width:100% ; margin-top:-28px !important;margin-bottom: 10px !important;'
          "
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div
              v-if="labelList[163].Field_Visiblity == 'Yes'"
              class="d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[163].Field_Alias }}
              </b>
              <span class="py-2 text-truncate" :style="{ maxWidth: '225px' }">
                <span
                  v-if="
                    (data.oldEdu?.Institution_Id ||
                      data.oldEdu?.Institute_Name) &&
                    (data.newEdu?.Institution_Id ||
                      data.newEdu?.Institute_Name) &&
                    ((data.oldEdu.Institution_Id &&
                      data.oldEdu?.Institution_Id?.toString() !==
                        data.newEdu?.Institution_Id?.toString()) ||
                      (data.oldEdu.Institute_Name &&
                        data.oldEdu?.Institute_Name?.toLowerCase() !==
                          data.newEdu?.Institute_Name?.toLowerCase()))
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  <v-tooltip
                    :text="
                      data.oldEdu.Institution_Id
                        ? data.oldEdu.Institution_Name_table
                        : data.oldEdu.Institute_Name
                    "
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="
                          (data.oldEdu.Institution_Id
                            ? data.oldEdu.Institution_Name_table
                            : data.oldEdu.Institute_Name
                          )?.length > 20
                            ? props
                            : {}
                        "
                      >
                        {{
                          data.oldEdu.Institution_Id
                            ? checkNullValue(data.oldEdu.Institution_Name_table)
                            : checkNullValue(data.oldEdu.Institute_Name)
                        }}
                      </span>
                    </template>
                  </v-tooltip>
                </span>
                <div
                  v-if="data.newEdu"
                  :class="[
                    (data.oldEdu &&
                      ((data.oldEdu.Institution_Id &&
                        data.oldEdu?.Institution_Id?.toString() !==
                          data.newEdu?.Institution_Id?.toString()) ||
                        (data.oldEdu.Institute_Name &&
                          data.oldEdu?.Institute_Name?.toLowerCase() !==
                            data.newEdu?.Institute_Name?.toLowerCase()))) ||
                    (!data.oldEdu && oldEducationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  <v-tooltip
                    :text="
                      data.newEdu.Institution_Id
                        ? data.newEdu.Institution_Name_table
                        : data.newEdu.Institute_Name
                    "
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="
                          (data.newEdu.Institution_Id
                            ? data.newEdu.Institution_Name_table
                            : data.newEdu.Institute_Name
                          )?.length > 20
                            ? props
                            : {}
                        "
                        class="text-truncate"
                        style="max-width: 225px"
                      >
                        {{
                          data.newEdu.Institution_Id
                            ? checkNullValue(data.newEdu.Institution_Name_table)
                            : checkNullValue(data.newEdu.Institute_Name)
                        }}
                      </span>
                    </template>
                  </v-tooltip>
                </div>
                <span
                  v-else-if="data.oldEdu"
                  class="text-error text-decoration-line-through"
                >
                  <v-tooltip
                    :text="
                      data.oldEdu.Institution_Id
                        ? data.oldEdu.Institution_Name_table
                        : data.oldEdu.Institute_Name
                    "
                  >
                    <template v-slot:activator="{ props }">
                      <span
                        v-bind="
                          (data.oldEdu.Institution_Id
                            ? data.oldEdu.Institution_Name_table
                            : data.oldEdu.Institute_Name
                          )?.length > 20
                            ? props
                            : {}
                        "
                        class="text-truncate"
                        style="max-width: 225px"
                      >
                        {{
                          data.oldEdu.Institution_Id
                            ? checkNullValue(data.oldEdu.Institution_Name_table)
                            : checkNullValue(data.oldEdu.Institute_Name)
                        }}
                      </span>
                    </template>
                  </v-tooltip>
                </span>
              </span>
            </div>
            <div
              v-if="labelList[165].Field_Visiblity == 'Yes'"
              class="d-flex flex-column justify-start mt-2"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[165].Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldEdu?.Percentage &&
                    data.newEdu?.Percentage &&
                    data.oldEdu?.Percentage?.toString() !==
                      data.newEdu?.Percentage?.toString()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldEdu.Percentage) }}
                </span>
                <span
                  v-if="data.newEdu"
                  :class="[
                    (data.oldEdu &&
                      data.oldEdu.Percentage?.toString() !==
                        data.newEdu.Percentage?.toString()) ||
                    (!data.oldEdu && oldEducationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newEdu?.Percentage) }}
                </span>
                <span
                  v-else-if="data.oldEdu"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldEdu.Percentage) }}
                </span>
              </span>
            </div>
            <div
              class="d-flex flex-column justify-start"
              v-if="labelList[167]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <b class="mb-1 text-grey justify-start">
                {{ labelList[167].Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldEdu?.Year_Of_Passing &&
                    data.newEdu?.Year_Of_Passing &&
                    data.oldEdu?.Year_Of_Passing?.toString() !==
                      data.newEdu?.Year_Of_Passing?.toString()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldEdu.Year_Of_Passing) }}
                </span>
                <span
                  v-if="data.newEdu"
                  :class="[
                    (data.oldEdu &&
                      data.oldEdu.Year_Of_Passing?.toString() !==
                        data.newEdu.Year_Of_Passing?.toString()) ||
                    (!data.oldEdu && oldEducationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newEdu?.Year_Of_Passing) }}
                </span>
                <span
                  v-else-if="data.oldEdu"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldEdu.Year_Of_Passing) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ViewEducationDetails",
  components: { ActionMenu },
  props: {
    oldEducationDetails: {
      type: [Array, Object],
      required: false,
    },
    educationDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return { havingAccess: {} };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    educationArray() {
      const oldEdu = this.oldEducationDetails || [];
      const newEdu = this.educationDetails || [];

      let idSet = new Set();
      let mergedDetails = [];

      newEdu.forEach((newItem) => {
        const id = newItem.Education_Id;
        idSet.add(id);
        const oldItem = oldEdu.find((old) => old.Education_Id === id);
        mergedDetails.push({
          newEdu: newItem,
          oldEdu: oldItem || null,
        });
      });

      oldEdu.forEach((oldItem) => {
        const id = oldItem.Education_Id;
        if (!idSet.has(id)) {
          mergedDetails.push({
            newEdu: null,
            oldEdu: oldItem,
          });
        }
      });

      return mergedDetails;
    },
    isSpecializationChanged() {
      return (data) => {
        const oldSpecId = data.oldEdu?.Specialization_Id;
        const newSpecId = data.newEdu?.Specialization_Id;
        const oldSpecText = data.oldEdu?.Specialisation;
        const newSpecText = data.newEdu?.Specialisation;

        const isSpecializationChanged =
          (oldSpecId || oldSpecText) &&
          (newSpecId || newSpecText) &&
          ((oldSpecId && oldSpecId.toString() !== newSpecId?.toString()) ||
            (oldSpecText &&
              oldSpecText.toLowerCase() !== newSpecText?.toLowerCase()));
        return isSpecializationChanged;
      };
    },
    isNewSpecialization() {
      return (data) => {
        const oldSpecId = data.oldEdu?.Specialization_Id;
        const newSpecId = data.newEdu?.Specialization_Id;
        const oldSpecText = data.oldEdu?.Specialisation;
        const newSpecText = data.newEdu?.Specialisation;

        const isSpecChanged =
          (data.oldEdu &&
            ((oldSpecId && oldSpecId.toString() !== newSpecId?.toString()) ||
              (oldSpecText &&
                oldSpecText.toLowerCase() !== newSpecText?.toLowerCase()))) ||
          (!data.oldEdu && this.oldEducationDetails);
        return isSpecChanged;
      };
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.educationDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", [selectedActionItem, "education"]);
      } else {
        this.$emit("on-open-edit", [selectedActionItem, "education"]);
      }
    },
  },
};
</script>
