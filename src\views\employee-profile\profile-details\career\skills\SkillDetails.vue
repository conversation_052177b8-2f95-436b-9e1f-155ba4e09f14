<template>
  <v-card class="rounded-lg" elevation="0">
    <div v-if="openBottomSheet">
      <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
        >Skill Details</span
      >
      <v-form ref="addEditSkillsForm" class="pa-2">
        <v-row>
          <v-col cols="12" md="4" sm="6">
            <v-combobox
              v-model="primarySkills"
              label="Primary Skills"
              multiple
              chips
              closable-chips
              :rules="[
                primarySkills.length > 0
                  ? validateWithRulesAndReturnMessages(
                      pSkills,
                      'skills',
                      'Primary Skills'
                    )
                  : true,
              ]"
              variant="solo"
              @update:modelValue="onChangeFields"
            ></v-combobox>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <v-combobox
              v-model="secondarySkills"
              label="Secondary Skills"
              multiple
              chips
              closable-chips
              :rules="[
                secondarySkills.length > 0
                  ? validateWithRulesAndReturnMessages(
                      sSkills,
                      'skills',
                      'Secondary Skills'
                    )
                  : true,
              ]"
              variant="solo"
              @update:modelValue="onChangeFields"
            ></v-combobox>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <v-combobox
              v-model="knownSkills"
              label="Known Skills"
              multiple
              chips
              closable-chips
              :rules="[
                knownSkills.length > 0
                  ? validateWithRulesAndReturnMessages(
                      kSkills,
                      'skills',
                      'Known Skills'
                    )
                  : true,
              ]"
              variant="solo"
              @update:modelValue="onChangeFields"
            ></v-combobox>
          </v-col>
          <v-col cols="12" md="4" sm="6">
            <v-combobox
              v-model="handsOnList"
              label="Hands On"
              multiple
              chips
              closable-chips
              :rules="[
                handsOnList.length > 0
                  ? validateWithRulesAndReturnMessages(
                      handsOn,
                      'skills',
                      'Hands On'
                    )
                  : true,
              ]"
              variant="solo"
              @update:modelValue="onChangeFields"
            ></v-combobox>
          </v-col>
        </v-row>
      </v-form>
    </div>
    <div v-else>
      <div class="d-flex">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="orange"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold">
            Skill Details
          </span>
        </div>
        <span v-if="enableEdit" class="d-flex justify-end ml-auto">
          <v-btn color="primary" variant="text" @click="openEditForm">
            <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit</v-btn
          >
        </span>
      </div>

      <v-row class="pa-4 ma-2 card-blue-background">
        <FieldDiff
          :oldDataAvailable="oldSkillDetails ? true : false"
          label="Primary Skills"
          :newValue="skillsFormData.Primary_Skill"
          :oldValue="oldSkillDetails?.Primary_Skill"
        />
        <FieldDiff
          :oldDataAvailable="oldSkillDetails ? true : false"
          label="Secondary Skills"
          :newValue="skillsFormData.Secondary_Skill"
          :oldValue="oldSkillDetails?.Secondary_Skill"
        />
        <FieldDiff
          :oldDataAvailable="oldSkillDetails ? true : false"
          label="Known Skills"
          :newValue="skillsFormData.Known_Skills"
          :oldValue="oldSkillDetails?.Known_Skills"
        />
        <FieldDiff
          :oldDataAvailable="oldSkillDetails ? true : false"
          label="Hands On"
          :newValue="skillsFormData.Hands_On"
          :oldValue="oldSkillDetails?.Hands_On"
        />
      </v-row>
    </div>
  </v-card>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue } from "@/helper";
import validationRules from "@/mixins/validationRules.js";
const FieldDiff = defineAsyncComponent(() =>
  import("@/components/custom-components/FieldDiff.vue")
);
import { ADD_UPDATE_SKILL_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "SkillDetails",

  components: {
    FieldDiff,
  },

  props: {
    skillDetails: {
      type: Object,
      required: false,
    },
    oldSkillDetails: {
      type: Object,
      required: false,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    callingFrom: {
      type: String,
      default: "",
    },
    isApprovalView: {
      type: Boolean,
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
  },

  mixins: [validationRules],

  emits: ["refetch-career-details", "edit-opened", "edit-closed"],

  data() {
    return {
      skillsFormData: {},
      editedSkillDetails: {},
      primarySkills: [],
      secondarySkills: [],
      knownSkills: [],
      handsOnList: [],
      backupPrimarySkills: [],
      backupSecondarySkills: [],
      backupKnownSkills: [],
      backupHandsOnList: [],
      openWarningModal: false,
      // edit
      isFormDirty: false,
      openBottomSheet: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    pSkills() {
      return this.primarySkills && this.primarySkills.length > 0
        ? this.primarySkills.join(", ")
        : "";
    },
    sSkills() {
      return this.secondarySkills && this.secondarySkills.length > 0
        ? this.secondarySkills.join(", ")
        : "";
    },
    kSkills() {
      return this.knownSkills && this.knownSkills.length > 0
        ? this.knownSkills.join(", ")
        : "";
    },
    handsOn() {
      return this.handsOnList && this.handsOnList.length > 0
        ? this.handsOnList.join(", ")
        : "";
    },
    enableEdit() {
      return (
        !this.isApprovalView &&
        (this.empFormUpdateAccess ||
          (this.formAccess &&
            this.formAccess.update &&
            this.formAccess.admin === "admin"))
      );
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    openBottomSheet(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (Array.isArray(this.skillDetails) && this.skillDetails?.length > 0) {
      this.skillsFormData = JSON.parse(JSON.stringify(this.skillDetails[0]));
    } else if (typeof this.skillDetails === "object") {
      this.skillsFormData = JSON.parse(JSON.stringify(this.skillDetails));
    }
  },

  methods: {
    checkNullValue,
    formatSkillValues() {
      this.primarySkills = this.editedSkillDetails.Primary_Skill
        ? this.editedSkillDetails.Primary_Skill.split(",")
        : [];
      this.secondarySkills = this.editedSkillDetails.Secondary_Skill
        ? this.editedSkillDetails.Secondary_Skill.split(",")
        : [];
      this.knownSkills = this.editedSkillDetails.Known_Skills
        ? this.editedSkillDetails.Known_Skills.split(",")
        : [];
      this.handsOnList = this.editedSkillDetails.Hands_On
        ? this.editedSkillDetails.Hands_On.split(",")
        : [];
      this.backupPrimarySkills = JSON.parse(JSON.stringify(this.primarySkills));
      this.backupSecondarySkills = JSON.parse(
        JSON.stringify(this.secondarySkills)
      );
      this.backupKnownSkills = JSON.parse(JSON.stringify(this.knownSkills));
      this.backupHandsOnList = JSON.parse(JSON.stringify(this.handsOnList));
    },
    onChangeFields() {
      this.isFormDirty = true;
    },

    openEditForm() {
      this.editedSkillDetails = this.skillsFormData;
      this.formatSkillValues();
      this.openBottomSheet = true;
      this.$emit("edit-opened");
      mixpanel.track("EmpProfile-career-skills-edit-opened");
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.isFormDirty = false;
        this.primarySkills = [];
        this.secondarySkills = [];
        this.knownSkills = [];
        this.handsOnList = [];
        this.openBottomSheet = false;
        this.editedSkillDetails = {};

        mixpanel.track("EmpProfile-career-skills-edit-closed");
        this.$emit("edit-closed");
      }
    },

    async validateEditForm() {
      const { valid } = await this.$refs.addEditSkillsForm.validate();

      mixpanel.track("EmpProfile-career-skills-submit-click");
      if (valid) {
        if (
          JSON.stringify(this.primarySkills) ===
            JSON.stringify(this.backupPrimarySkills) &&
          JSON.stringify(this.secondarySkills) ===
            JSON.stringify(this.backupSecondarySkills) &&
          JSON.stringify(this.knownSkills) ===
            JSON.stringify(this.backupKnownSkills) &&
          JSON.stringify(this.handsOnList) ===
            JSON.stringify(this.backupHandsOnList)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.updateSkillsDetails();
        }
      }
    },

    updateSkillsDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_SKILL_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            primarySkill: vm.pSkills,
            secondarySkill: vm.sSkills,
            knownSkills: vm.kSkills,
            handsOn: vm.handsOn,
            formId: vm.callingFrom === "profile" ? 18 : 243,
            formStatus: vm.actionType?.toLowerCase() === "add" ? 0 : 1,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          const { message } = res.data.addUpdateSkillDetails;
          mixpanel.track("EmpProfile-career-skills-edit-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: message?.includes("approval")
              ? "Skills updation is submitted for approval."
              : "Skills updated successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.isFormDirty = false;
          vm.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
          vm.closeEditForm();
          vm.$emit("refetch-career-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-career-skills-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "skills",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.chips-container {
  position: relative;
}
</style>
