<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Training Details</span
        >
        <v-spacer></v-spacer>
        <v-icon color="grey" size="25" @click="$emit('close-training-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addTrainingForm">
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="trainingFormData.Training_Name"
                :rules="[
                  required('Training', trainingFormData.Training_Name),
                  validateWithRulesAndReturnMessages(
                    trainingFormData.Training_Name,
                    'trainingName',
                    'Training'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Training<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-menu
                v-model="startDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="Start Date"
                    v-model="formattedStartDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Start Date', formattedStartDate)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Start Date<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="trainingFormData.Training_Start_Date"
                  :min="selectedEmpDobDate"
                  :max="currentDate"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="6">
              <v-menu
                v-model="endDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="End Date"
                    v-model="formattedEndDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('End Date', formattedEndDate)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      End Date<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="trainingFormData.Training_End_Date"
                  :min="trainingEndDateMin"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Duration</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{
                  trainingFormData.Training_Duration
                    ? convertMonthToYearMonthsDays(
                        trainingFormData.Training_Duration
                      )
                    : "-"
                }}
              </p>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="trainingFormData.Trainer"
                :rules="[
                  required('Trainer ', trainingFormData.Trainer),
                  validateWithRulesAndReturnMessages(
                    trainingFormData.Trainer,
                    'trainer',
                    'Trainer '
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Trainer<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="trainingFormData.Center"
                :rules="[
                  required('Center', trainingFormData.Center),
                  validateWithRulesAndReturnMessages(
                    trainingFormData.Center,
                    'center',
                    'Center'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Center<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="this.$emit('close-training-form')"
                  class="ma-2 pa-2"
                  variant="outlined"
                  >Cancel</v-btn
                >
                <v-btn
                  class="ma-2 pa-2"
                  @click="validateTrainingDetails"
                  color="primary"
                  variant="elevated"
                  :disabled="!isFormDirty"
                  >Save</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules.js";
import moment from "moment";
import { ADD_UPDATE_TRAINING_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import { convertMonthToYearMonthsDays, getDaysDifference } from "@/helper";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AddEditTrainingDetails",
  props: {
    selectedTrainingDetails: {
      type: Object,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
  },
  mixins: [validationRules],
  emits: ["refetch-career-details", "close-training-form"],
  data() {
    return {
      trainingFormData: {
        Training_Name: "",
        Training_Start_Date: null,
        Training_End_Date: null,
        Training_Duration: "",
        Trainer: "",
        Center: "",
      },
      backupTrainingFormData: {},
      isFormDirty: false,
      // edit
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      //Date-picker
      formattedStartDate: "",
      formattedEndDate: "",
      startDateMenu: false,
      endDateMenu: false,
    };
  },

  // using this to make all the future dates disabled
  computed: {
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    selectedEmpDobDate() {
      if (
        this.selectedEmployeeDob &&
        this.selectedEmployeeDob !== "0000-00-00"
      ) {
        return moment(this.selectedEmployeeDob).format("YYYY-MM-DD");
      } else return null;
    },
    trainingStartMax() {
      if (
        this.trainingFormData.Training_End_Date &&
        this.trainingFormData.Training_End_Date !== "0000-00-00"
      ) {
        return moment(this.trainingFormData.Training_End_Date).format(
          "YYYY-MM-DD"
        );
      }
      return this.currentDate;
    },
    trainingEndDateMin() {
      if (
        this.trainingFormData.Training_Start_Date &&
        this.trainingFormData.Training_Start_Date !== "0000-00-00"
      ) {
        return moment(this.trainingFormData.Training_Start_Date).format(
          "YYYY-MM-DD"
        );
      }
      return this.selectedEmpDobDate;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  watch: {
    "trainingFormData.Training_Start_Date": function (val) {
      // Do something with the new value and/or old value here
      this.calculateDuration();
      if (val) {
        this.startDateMenu = false;
        this.formattedStartDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "trainingFormData.Training_End_Date": function (val) {
      this.calculateDuration();
      if (val) {
        this.endDateMenu = false;
        this.formattedEndDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedTrainingDetails &&
      Object.keys(this.selectedTrainingDetails).length > 0
    ) {
      this.trainingFormData = JSON.parse(
        JSON.stringify(this.selectedTrainingDetails)
      );
      if (this.trainingFormData.Training_Start_Date) {
        this.formattedStartDate = this.formatDate(
          this.trainingFormData?.Training_Start_Date
        );
        this.trainingFormData.Training_Start_Date = this.trainingFormData
          .Training_Start_Date
          ? new Date(this.trainingFormData.Training_Start_Date)
          : null;
      }
      if (this.trainingFormData.Training_End_Date) {
        this.formattedEndDate = this.formatDate(
          this.trainingFormData?.Training_End_Date
        );
        this.trainingFormData.Training_End_Date = this.trainingFormData
          .Training_End_Date
          ? new Date(this.trainingFormData.Training_End_Date)
          : null;
      }
      this.backupTrainingFormData = JSON.parse(
        JSON.stringify(this.trainingFormData)
      );
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
  },

  methods: {
    convertMonthToYearMonthsDays,
    calculateDuration() {
      let dayDifference = getDaysDifference(
        this.trainingFormData.Training_Start_Date,
        this.trainingFormData.Training_End_Date
      );
      this.trainingFormData["Training_Duration"] = (dayDifference / 30).toFixed(
        2
      );
    },
    onChangeFields() {
      this.isFormDirty = true;
    },

    async validateTrainingDetails() {
      const { valid } = await this.$refs.addTrainingForm.validate();
      mixpanel.track("EmpProfile-career-training-submit-click");
      if (valid) {
        this.trainingFormData.Training_Start_Date = moment(
          this.trainingFormData.Training_Start_Date
        ).isValid()
          ? moment(this.trainingFormData.Training_Start_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.trainingFormData.Training_End_Date = moment(
          this.trainingFormData.Training_End_Date
        ).isValid()
          ? moment(this.trainingFormData.Training_End_Date).format("YYYY-MM-DD")
          : null;
        this.backupTrainingFormData.Training_Start_Date = moment(
          this.backupTrainingFormData.Training_Start_Date
        ).isValid()
          ? moment(this.backupTrainingFormData.Training_Start_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.backupTrainingFormData.Training_End_Date = moment(
          this.backupTrainingFormData.Training_End_Date
        ).isValid()
          ? moment(this.backupTrainingFormData.Training_End_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        if (
          JSON.stringify(this.trainingFormData) ===
          JSON.stringify(this.backupTrainingFormData)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.updateTrainingDetails();
        }
      }
    },

    updateTrainingDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_TRAINING_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            trainingId: vm.trainingFormData.Training_Id,
            trainingName: vm.trainingFormData.Training_Name,
            trainingStartDate: moment(
              vm.trainingFormData.Training_Start_Date
            ).isValid()
              ? moment(vm.trainingFormData.Training_Start_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            trainingEndDate: moment(
              vm.trainingFormData.Training_End_Date
            ).isValid()
              ? moment(vm.trainingFormData.Training_End_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            trainingDuration: vm.trainingFormData.Training_Duration,
            trainer: vm.trainingFormData.Trainer,
            center: vm.trainingFormData.Center,
            formId: vm.callingFrom === "profile" ? 18 : 243,
            formStatus: vm.actionType === "edit" ? 1 : 0,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          const { message } = res.data.addUpdateTrainingDetails;
          mixpanel.track("EmpProfile-career-training-update-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: message?.includes("approval")
              ? "Training details submitted for approval."
              : vm.formType === "edit"
              ? "Training details updated successfully"
              : "Training details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-career-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-career-training-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "training details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
