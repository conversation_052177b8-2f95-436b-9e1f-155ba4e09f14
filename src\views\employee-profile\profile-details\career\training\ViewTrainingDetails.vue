<template>
  <div
    v-if="trainingDetails && trainingDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
  >
    No training details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in trainingArray"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:550px; max-width:550px; border-left: 7px solid ${generateRandomColor()}; height:auto;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip
                  :text="
                    data.newtraining?.Training_Name ||
                    data.oldtraining?.Training_Name
                  "
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="
                        data.newtraining?.Training_Name ||
                        data.oldtraining?.Training_Name
                          ? props
                          : ''
                      "
                    >
                      <span
                        v-if="
                          data.oldtraining?.Training_Name &&
                          data.newtraining?.Training_Name &&
                          data.oldtraining?.Training_Name?.toString() !==
                            data.newtraining?.Training_Name?.toString()
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{ checkNullValue(data.oldtraining.Training_Name) }}
                      </span>
                      <span
                        v-if="data.newtraining"
                        :class="[
                          (data.oldtraining &&
                            data.oldtraining.Training_Name?.toString() !==
                              data.newtraining.Training_Name?.toString()) ||
                          (!data.oldtraining && oldTrainingDetails)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{ checkNullValue(data.newtraining?.Training_Name) }}
                      </span>
                      <span
                        v-else-if="data.oldtraining"
                        class="text-error text-decoration-line-through"
                      >
                        {{ checkNullValue(data.oldtraining.Training_Name) }}
                      </span>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n8">
        <span
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Trainer </b>
              <span class="py-1">
                <span
                  v-if="
                    data.oldtraining?.Trainer &&
                    data.newtraining?.Trainer &&
                    data.oldtraining?.Trainer?.toLowerCase() !==
                      data.newtraining?.Trainer?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldtraining.Trainer) }}
                </span>
                <span
                  v-if="data.newtraining"
                  :class="[
                    (data.oldtraining &&
                      data.oldtraining.Trainer?.toLowerCase() !==
                        data.newtraining.Trainer?.toLowerCase()) ||
                    (!data.oldtraining && oldTrainingDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newtraining?.Trainer) }}
                </span>
                <span
                  v-else-if="data.oldtraining"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldtraining.Trainer) }}
                </span>
              </span>
            </div>
            <div class="mt-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Start Date </b>
              <span class="py-1">
                <span
                  v-if="
                    data.oldtraining?.Training_Start_Date &&
                    data.newtraining?.Training_Start_Date &&
                    data.oldtraining?.Training_Start_Date?.toString() !==
                      data.newtraining?.Training_Start_Date?.toString()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ formatDate(data.oldtraining.Training_Start_Date) }}
                </span>
                <span
                  v-if="data.newtraining"
                  :class="[
                    (data.oldtraining &&
                      data.oldtraining.Training_Start_Date?.toString() !==
                        data.newtraining.Training_Start_Date?.toString()) ||
                    (!data.oldtraining && oldTrainingDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ formatDate(data.newtraining?.Training_Start_Date) }}
                </span>
                <span
                  v-else-if="data.oldtraining"
                  class="text-error text-decoration-line-through"
                >
                  {{ formatDate(data.oldtraining.Training_Start_Date) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Training Duration </b>
              <span class="py-1">
                <span
                  v-if="
                    data.oldtraining?.Training_Duration &&
                    data.newtraining?.Training_Duration &&
                    data.oldtraining?.Training_Duration?.toString() !==
                      data.newtraining?.Training_Duration?.toString()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{
                    convertMonthToYearMonthsDays(
                      data.oldtraining.Training_Duration
                    )
                  }}
                </span>
                <span
                  v-if="data.newtraining"
                  :class="[
                    (data.oldtraining &&
                      data.oldtraining.Training_Duration?.toString() !==
                        data.newtraining.Training_Duration?.toString()) ||
                    (!data.oldtraining && oldTrainingDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{
                    convertMonthToYearMonthsDays(
                      data.newtraining?.Training_Duration
                    )
                  }}
                </span>
                <span
                  v-else-if="data.oldtraining"
                  class="text-error text-decoration-line-through"
                >
                  {{
                    convertMonthToYearMonthsDays(
                      data.oldtraining.Training_Duration
                    )
                  }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-40px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Center </b>
              <span class="py-1">
                <span
                  v-if="
                    data.oldtraining?.Center &&
                    data.newtraining?.Center &&
                    data.oldtraining?.Center?.toLowerCase() !==
                      data.newtraining?.Center?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldtraining.Center) }}
                </span>
                <span
                  v-if="data.newtraining"
                  :class="[
                    (data.oldtraining &&
                      data.oldtraining.Center?.toLowerCase() !==
                        data.newtraining.Center?.toLowerCase()) ||
                    (!data.oldtraining && oldTrainingDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newtraining?.Center) }}
                </span>
                <span
                  v-else-if="data.oldtraining"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldtraining.Center) }}
                </span>
              </span>
            </div>
            <div class="mt-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">End Date </b>
              <span class="py-1">
                <span
                  v-if="
                    data.oldtraining?.Training_End_Date &&
                    data.newtraining?.Training_End_Date &&
                    data.oldtraining?.Training_End_Date?.toString() !==
                      data.newtraining?.Training_End_Date?.toString()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ formatDate(data.oldtraining.Training_End_Date) }}
                </span>
                <span
                  v-if="data.newtraining"
                  :class="[
                    (data.oldtraining &&
                      data.oldtraining.Training_End_Date?.toString() !==
                        data.newtraining.Training_End_Date?.toString()) ||
                    (!data.oldtraining && oldTrainingDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ formatDate(data.newtraining?.Training_End_Date) }}
                </span>
                <span
                  v-else-if="data.oldtraining"
                  class="text-error text-decoration-line-through"
                >
                  {{ formatDate(data.oldtraining.Training_End_Date) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import moment from "moment";
import {
  generateRandomColor,
  checkNullValue,
  convertMonthToYearMonthsDays,
} from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ViewTrainingDetails",
  components: { ActionMenu },
  props: {
    trainingDetails: {
      type: Object,
      required: true,
    },
    oldTrainingDetails: {
      type: [Array, Object],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return { havingAccess: {} };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    trainingArray() {
      const oldtraining = this.oldTrainingDetails || [];
      const newtraining = this.trainingDetails || [];

      let idSet = new Set();
      let mergedDetails = [];

      newtraining.forEach((newItem) => {
        const id = newItem.Training_Id;
        idSet.add(id);
        const oldItem = oldtraining.find((old) => old.Training_Id === id);
        mergedDetails.push({
          newtraining: newItem,
          oldtraining: oldItem || null,
        });
      });

      oldtraining.forEach((oldItem) => {
        const id = oldItem.Training_Id;
        if (!idSet.has(id)) {
          mergedDetails.push({
            newtraining: null,
            oldtraining: oldItem,
          });
        }
      });

      return mergedDetails;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    convertMonthToYearMonthsDays,
    checkAccess() {
      this.havingAccess["update"] =
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.trainingDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", [selectedActionItem, "training"]);
      } else {
        this.$emit("on-open-edit", [selectedActionItem, "training"]);
      }
    },
  },
};
</script>
