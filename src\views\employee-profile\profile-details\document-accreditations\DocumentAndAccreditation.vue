<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs('error')"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else class="fill-height">
    <div class="d-flex justify-end mt-n2 mr-n2">
      <v-icon @click="refetchAPIs('refresh')" size="17" color="grey"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <Documents
      :documentDetailsData="documentDetails"
      :oldDocumentDetailsData="oldDocumentDetailsData"
      :selectedEmpId="selectedEmpId"
      :formAccess="formAccess"
      :empFormUpdateAccess="empFormUpdateAccess"
      :callingFrom="callingFrom"
      :actionType="actionType"
      @refetch-doc-accreditation-details="refetchAPIs('update')"
    ></Documents>
    <Accreditations
      :accreditationDetailsData="accreditationDetails"
      :oldAccreditationDetailsData="oldAccreditationDetailsData"
      :selectedEmpId="selectedEmpId"
      :formAccess="formAccess"
      :empFormUpdateAccess="empFormUpdateAccess"
      :callingFrom="callingFrom"
      :actionType="actionType"
      :selectedEmployeeDob="selectedEmployeeDob"
      @refetch-doc-accreditation-details="refetchAPIs('update')"
    ></Accreditations>
  </div>
</template>

<script>
import Documents from "./document/Documents.vue";
import Accreditations from "./accreditations/Accreditations.vue";
import {
  RETRIEVE_EMP_DOCUMENT_ACCREDITATION_INFO,
  RETRIEVE_EMPLOYEE_CHANGES,
} from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "DocumentAndAccreditation",
  components: {
    Accreditations,
    Documents,
  },
  props: {
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    callingFrom: {
      type: String,
      default: "",
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
  },
  emits: ["details-retrieved"],
  data: () => ({
    documentDetails: [],
    accreditationDetails: [],
    oldDocumentDetailsData: null,
    oldAccreditationDetailsData: null,
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
  }),
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.selectedEmpId || this.callingFrom === "profile") {
      this.getDocumentAndAccreditationDetails();
    }
  },
  methods: {
    refetchAPIs(type) {
      this.isErrorInList = false;
      mixpanel.track("EmpProfile-docAcc-refetch");
      this.getDocumentAndAccreditationDetails(type);
    },
    getDocumentAndAccreditationDetails(type = "") {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_DOCUMENT_ACCREDITATION_INFO,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-docAcc-fetch-success");
          if (response && response.data && response.data.retrieveDocumentInfo) {
            const { documentDetails, accreditationDetails } =
              response.data.retrieveDocumentInfo;
            vm.documentDetails = documentDetails
              ? JSON.parse(documentDetails)
              : [];
            vm.accreditationDetails = accreditationDetails
              ? JSON.parse(accreditationDetails)
              : [];
            vm.$emit("details-retrieved", type);
            this.getEmployeeChanges();
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      mixpanel.track("EmpProfile-docAcc-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "document & accreditation details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    getEmployeeChanges() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMPLOYEE_CHANGES,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
            tables: ["emp_document_category", "employee_accreditation_details"],
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-employeeChanges-fetch-success");
          if (
            response &&
            response.data &&
            response.data.retrievePendingEmployeeChanges
          ) {
            const { pendingChanges } =
              response.data.retrievePendingEmployeeChanges;
            if (
              pendingChanges &&
              pendingChanges.changes &&
              pendingChanges.changes.length > 0
            ) {
              for (let change of pendingChanges.changes) {
                if (change.Table_Name === "emp_document_category") {
                  vm.oldDocumentDetailsData = vm.documentDetails;
                  vm.documentDetails = JSON.parse(change.New_Data);
                } else if (
                  change.Table_Name === "employee_accreditation_details"
                ) {
                  vm.oldAccreditationDetailsData = vm.accreditationDetails;
                  vm.accreditationDetails = JSON.parse(change.New_Data);
                }
              }
            }
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
  },
};
</script>
