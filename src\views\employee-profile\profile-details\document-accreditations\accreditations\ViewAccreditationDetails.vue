<template>
  <div
    v-if="accreditationArray && accreditationArray.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No {{ labelList[229]?.Field_Alias.toLowerCase() }} have been submitted
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in accreditationArray"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:550px; max-width:550px; border-left: 7px solid ${generateRandomColor()}; height:auto;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex justify-start">
                <v-tooltip
                  :text="
                    data.newAcc?.Accreditation_Category ||
                    data.oldAcc?.Accreditation_Category
                  "
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="
                        data.newAcc?.Accreditation_Category ||
                        data.oldAcc?.Accreditation_Category
                          ? props
                          : ''
                      "
                    >
                      <span
                        v-if="
                          data.oldAcc?.Accreditation_Category &&
                          data.newAcc?.Accreditation_Category &&
                          data.oldAcc?.Accreditation_Category?.toLowerCase() !==
                            data.newAcc?.Accreditation_Category?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{
                          checkNullValue(data.oldAcc?.Accreditation_Category)
                        }}
                      </span>
                      <span
                        v-if="data.newAcc"
                        :class="[
                          (data.oldAcc &&
                            data.oldAcc?.Accreditation_Category?.toLowerCase() !==
                              data.newAcc?.Accreditation_Category?.toLowerCase()) ||
                          (!data.oldAcc && oldAccreditationDetails)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{
                          checkNullValue(data.newAcc?.Accreditation_Category)
                        }}
                      </span>
                      <span
                        v-else-if="data.oldAcc"
                        class="text-error text-decoration-line-through"
                      >
                        {{
                          checkNullValue(data.oldAcc?.Accreditation_Category)
                        }}
                      </span>
                    </div>
                  </template>
                </v-tooltip>
                <div
                  v-if="data.Dependent_Id"
                  class="text-primary font-weight-bold text-h6 text-truncate"
                >
                  ({{
                    (data.Dependent_First_Name
                      ? data.Dependent_First_Name
                      : "-") +
                    " " +
                    (data.Dependent_Last_Name
                      ? data.Dependent_Last_Name
                      : "-") +
                    "-" +
                    (data.Relationship ? data.Relationship : "-")
                  }})
                </div>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n3">
        <span
          :style="!isMobileView ? 'width:60%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">
                {{ labelList[229].Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldAcc?.Accreditation_Type &&
                    data.newAcc?.Accreditation_Type &&
                    data.oldAcc?.Accreditation_Type?.toLowerCase() !==
                      data.newAcc?.Accreditation_Type?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldAcc?.Accreditation_Type) }}
                </span>
                <span
                  v-if="data.newAcc"
                  :class="[
                    (data.oldAcc &&
                      data.oldAcc?.Accreditation_Type?.toLowerCase() !==
                        data.newAcc?.Accreditation_Type?.toLowerCase()) ||
                    (!data.oldAcc && oldAccreditationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newAcc?.Accreditation_Type) }}
                </span>
                <span
                  v-else-if="data.oldAcc"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldAcc?.Accreditation_Type) }}
                </span>
              </span>
            </div>
            <div
              v-if="labelList[245]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[245]?.Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldAcc?.Received_Date &&
                    data.newAcc?.Received_Date &&
                    data.oldAcc?.Received_Date !== data.newAcc?.Received_Date
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ formatDate(data.oldAcc?.Received_Date) }}
                </span>
                <span
                  v-if="data.newAcc"
                  :class="[
                    (data.oldAcc &&
                      data.oldAcc?.Received_Date !==
                        data.newAcc?.Received_Date) ||
                    (!data.oldAcc && oldAccreditationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ formatDate(data.newAcc?.Received_Date) }}
                </span>
                <span
                  v-else-if="data.oldAcc"
                  class="text-error text-decoration-line-through"
                >
                  {{ formatDate(data.oldAcc?.Received_Date) }}
                </span>
              </span>
            </div>
            <div
              v-if="labelList[391]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[391]?.Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    (data.oldAcc?.Exam_Rating ||
                      data.oldAcc?.Exam_Rating == 0) &&
                    (data.newAcc?.Exam_Rating ||
                      data.newAcc?.Exam_Rating == 0) &&
                    data.oldAcc?.Exam_Rating !== data.newAcc?.Exam_Rating
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{
                    data.oldAcc?.Exam_Rating || data.oldAcc?.Exam_Rating == 0
                      ? data.oldAcc?.Exam_Rating
                      : "-"
                  }}
                </span>
                <span
                  v-if="data.newAcc"
                  :class="[
                    (data.oldAcc &&
                      data.oldAcc?.Exam_Rating?.toLowerCase() !==
                        data.newAcc?.Exam_Rating?.toLowerCase()) ||
                    (!data.oldAcc && oldAccreditationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{
                    data.newAcc?.Exam_Rating || data.newAcc?.Exam_Rating == 0
                      ? data.newAcc?.Exam_Rating
                      : "-"
                  }}
                </span>
                <span
                  v-else-if="data.oldAcc"
                  class="text-error text-decoration-line-through"
                >
                  {{
                    data.oldAcc?.Exam_Rating || data.oldAcc?.Exam_Rating == 0
                      ? data.oldAcc?.Exam_Rating
                      : "-"
                  }}
                </span>
              </span>
            </div>
            <div
              v-if="labelList[392]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[392]?.Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldAcc?.Exam_Date_Year &&
                    data.newAcc?.Exam_Date_Year &&
                    data.oldAcc?.Exam_Date_Year !== data.newAcc?.Exam_Date_Year
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldAcc?.Exam_Date_Year) }}
                </span>
                <span
                  v-if="data.newAcc"
                  :class="[
                    (data.oldAcc &&
                      data.oldAcc?.Exam_Date_Year !==
                        data.newAcc?.Exam_Date_Year) ||
                    (!data.oldAcc && oldAccreditationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newAcc?.Exam_Date_Year) }}
                </span>
                <span
                  v-else-if="data.oldAcc"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldAcc?.Exam_Date_Year) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Status </b>
              <span
                class="py-2"
                :class="
                  checkStatus(data.Expiry_Date) === 'Expired'
                    ? 'text-red'
                    : 'text-green'
                "
              >
                <span
                  v-if="
                    data.oldAcc?.Expiry_Date &&
                    data.newAcc?.Expiry_Date &&
                    data.oldAcc?.Expiry_Date !== data.newAcc?.Expiry_Date
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkStatus(data.oldAcc?.Expiry_Date) }}
                </span>
                <span
                  v-if="data.newAcc"
                  :class="[
                    (data.oldAcc &&
                      data.oldAcc?.Expiry_Date !== data.newAcc?.Expiry_Date) ||
                    (!data.oldAcc && oldAccreditationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkStatus(data.newAcc?.Expiry_Date) }}
                </span>
                <span
                  v-else-if="data.oldAcc"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkStatus(data.oldAcc?.Expiry_Date) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:40%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div
              v-if="labelList[390]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[390]?.Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldAcc?.Identifier &&
                    data.newAcc?.Identifier &&
                    data.oldAcc?.Identifier?.toLowerCase() !==
                      data.newAcc?.Identifier?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldAcc?.Identifier) }}
                </span>
                <span
                  v-if="data.newAcc"
                  :class="[
                    (data.oldAcc &&
                      data.oldAcc?.Identifier?.toLowerCase() !==
                        data.newAcc?.Identifier?.toLowerCase()) ||
                    (!data.oldAcc && oldAccreditationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newAcc?.Identifier) }}
                </span>
                <span
                  v-else-if="data.oldAcc"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldAcc?.Identifier) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start">Expiry Date </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldAcc?.Expiry_Date &&
                    data.newAcc?.Expiry_Date &&
                    data.oldAcc?.Expiry_Date !== data.newAcc?.Expiry_Date
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ formatDate(data.oldAcc?.Expiry_Date) }}
                </span>
                <span
                  v-if="data.newAcc"
                  :class="[
                    (data.oldAcc &&
                      data.oldAcc?.Expiry_Date !== data.newAcc?.Expiry_Date) ||
                    (!data.oldAcc && oldAccreditationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ formatDate(data.newAcc?.Expiry_Date) }}
                </span>
                <span
                  v-else-if="data.oldAcc"
                  class="text-error text-decoration-line-through"
                >
                  {{ formatDate(data.oldAcc?.Expiry_Date) }}
                </span>
              </span>
            </div>
            <div
              v-if="labelList[393]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mr-2 d-flex flex-column justify-start"
            >
              <b class="mb-1 text-grey justify-start"
                >{{ labelList[393]?.Field_Alias }}
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldAcc?.Exam_Date_Month &&
                    data.newAcc?.Exam_Date_Month &&
                    data.oldAcc?.Exam_Date_Month !==
                      data.newAcc?.Exam_Date_Month
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldAcc?.Exam_Date_Month) }}
                </span>
                <span
                  v-if="data.newAcc"
                  :class="[
                    (data.oldAcc &&
                      data.oldAcc?.Exam_Date_Month !==
                        data.newAcc?.Exam_Date_Month) ||
                    (!data.oldAcc && oldAccreditationDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newAcc?.Exam_Date_Month) }}
                </span>
                <span
                  v-else-if="data.oldAcc"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldAcc?.Exam_Date_Month) }}
                </span>
              </span>
            </div>
            <div
              v-if="data.newAcc?.File_Name"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <span
                style="text-decoration: underline"
                @click="retrieveAccreditations(data.newAcc?.File_Name)"
                class="text-green cursor-pointer"
              >
                View Document</span
              >
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employee Accreditation"
    fileRetrieveType="accreditation"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

export default {
  name: "ViewAccreditationDetails",
  components: { FilePreviewModal, ActionMenu },

  props: {
    accreditationDetails: {
      type: Object,
      required: true,
    },
    oldAccreditationDetails: {
      type: [Array, Object],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return {
      retrievedFileName: "",
      openModal: false,
      havingAccess: {},
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    checkStatus() {
      return (expDate) => {
        let currentDate = moment().format("YYYY-MM-DD");
        let isBeforeCurrentDate = moment(expDate).isBefore(currentDate);
        return isBeforeCurrentDate ? "Expired" : "Active";
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    accreditationArray() {
      const oldAcc = this.oldAccreditationDetails || [];
      const newAcc = this.accreditationDetails || [];

      let idSet = new Set();
      let mergedDetails = [];

      newAcc.forEach((newItem) => {
        const id = newItem.Accreditation_Detail_Id;
        idSet.add(id);
        const oldItem = oldAcc.find(
          (old) => old.Accreditation_Detail_Id === id
        );
        mergedDetails.push({
          newAcc: newItem,
          oldAcc: oldItem || null,
        });
      });

      oldAcc.forEach((oldItem) => {
        const id = oldItem.Accreditation_Detail_Id;
        if (!idSet.has(id)) {
          mergedDetails.push({
            newAcc: null,
            oldAcc: oldItem,
          });
        }
      });

      return mergedDetails;
    },
  },
  methods: {
    //using the generateRandomColor function of helper.js file
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.accreditationDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    retrieveAccreditations(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },
  },
};
</script>
