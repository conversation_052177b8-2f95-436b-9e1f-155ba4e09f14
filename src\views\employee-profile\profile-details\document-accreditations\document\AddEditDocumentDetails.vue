<template>
  <v-card class="rounded-lg pa-4">
    <v-card-title class="d-flex">
      <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
        >Document Details</span
      >
      <v-spacer></v-spacer>
      <v-icon color="grey" size="25" @click="$emit('close-document-form')"
        >fas fa-times</v-icon
      >
    </v-card-title>
    <v-card-text>
      <v-alert v-if="showValidationAlert" prominent type="warning">
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="primary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </v-alert>
      <v-form ref="addEditDocumentForm">
        <v-row>
          <v-col
            v-if="documentFormData.Document_Sub_Type_Id"
            cols="12"
            sm="6"
            md="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">Document Category</p>
            <v-skeleton-loader
              v-if="documentCategoryListLoading"
              class="mt-4"
              type="list-item"
              width="50%"
            ></v-skeleton-loader>
            <p v-else class="text-subtitle-1 font-weight-regular mt-2">
              {{ documentCategoryBasedOnType }}
            </p>
          </v-col>
          <v-col
            v-if="documentFormData.Document_Sub_Type_Id"
            cols="12"
            sm="6"
            md="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">Document Type</p>
            <v-skeleton-loader
              v-if="documentTypeListLoading"
              class="mt-4"
              type="list-item"
              width="50%"
            ></v-skeleton-loader>
            <p v-else class="text-subtitle-1 font-weight-regular mt-2">
              {{ documentTypeBasedOnSubType }}
            </p>
          </v-col>
          <v-col cols="12" sm="12" lg="6" md="6" xl="6">
            <CustomSelect
              :items="documentSubTypeList"
              label="Document Sub Type"
              :itemSelected="documentFormData.Document_Sub_Type_Id"
              :rules="[
                required(
                  'Document Sub Type',
                  documentFormData.Document_Sub_Type_Id
                ),
              ]"
              itemValue="Document_Sub_Type_Id"
              itemTitle="Document_Sub_Type"
              :isRequired="true"
              @selected-item="
                onChangeCustomSelectField($event, 'Document_Sub_Type_Id')
              "
              :isAutoComplete="true"
              :isLoading="documentSubTypeListLoading"
              :noDataText="
                documentSubTypeListLoading ? 'Loading...' : 'No data available'
              "
            ></CustomSelect>
          </v-col>
          <v-col cols="12" sm="12" lg="6" md="6" xl="6">
            <v-file-input
              prepend-icon=""
              clearable
              show-size
              :model-value="fileContent"
              append-inner-icon="fas fa-paperclip"
              variant="solo"
              :rules="[required('Document', fileContentRuleValue)]"
              accept="image/png, image/jpeg, image/jpg, application/pdf"
              @update:modelValue="onChangeFiles"
              @click:clear="removeFiles"
            >
              <template v-slot:label>
                Document<span style="color: red">*</span>
              </template>
            </v-file-input>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <div class="d-flex justify-end">
              <v-btn
                @click="$emit('close-document-form')"
                variant="outlined"
                class="ma-2 pa-2"
                >Cancel</v-btn
              >
              <v-btn
                :disabled="!isFormDirty"
                class="ma-2 pa-2"
                variant="elevated"
                color="primary"
                @click="validateDocumentDetails"
              >
                Save
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { ADD_UPDATE_DOCUMENT_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import {
  LIST_SUB_DOC_TYPE,
  LIST_DOC_CATEGORY,
  LIST_DOC_TYPE,
} from "@/graphql/dropDownQueries";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AddEditDocumentDetails",
  props: {
    selectedDocumentDetails: {
      type: Object,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    callingFrom: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
  },
  components: {
    CustomSelect,
  },
  emits: ["refetch-doc-accreditation-details", "close-document-form"],
  mixins: [validationRules],
  data() {
    return {
      selectedFile: [],
      documentFormData: {
        Document_Name: "",
        Document_Sub_Type_Id: "",
        File_Name: null,
        File_Size: null,
      },
      backupDocumentFormData: {},
      // list
      documentSubTypeList: [],
      documentSubTypeListLoading: false,
      documentCategoryList: [],
      documentCategoryListLoading: false,
      documentTypeList: [],
      documentTypeListLoading: false,
      // edit
      isFileChanged: false,
      isFormDirty: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      // file
      fileContent: null,
    };
  },

  computed: {
    domainName() {
      return this.$store.getters.domain;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    fileContentRuleValue() {
      return this.fileContent && this.fileContent.name
        ? this.fileContent.name
        : null;
    },
    documentCategoryBasedOnType() {
      let docCategory = "-";
      let subTypeId = this.documentFormData.Document_Sub_Type_Id;
      if (
        subTypeId &&
        this.documentSubTypeList.length > 0 &&
        this.documentCategoryList.length > 0 &&
        this.documentTypeList.length > 0
      ) {
        let selectedSubType = this.documentSubTypeList.filter(
          (el) => el.Document_Sub_Type_Id === subTypeId
        );
        let typeId =
          selectedSubType && selectedSubType.length > 0
            ? selectedSubType[0].Document_Type_Id
            : 0;
        if (typeId) {
          let selectedType = this.documentTypeList.filter(
            (el) => el.Document_Type_Id === typeId
          );
          let categoryId =
            selectedType && selectedType.length > 0
              ? selectedType[0].Category_Id
              : 0;
          if (categoryId) {
            let selectedCategory = this.documentCategoryList.filter(
              (el) => el.Category_Id === categoryId
            );
            let categoryName =
              selectedCategory && selectedCategory.length > 0
                ? selectedCategory[0].Category_Fields
                : "-";
            docCategory = categoryName;
          }
        }
      }
      return docCategory;
    },
    documentTypeBasedOnSubType() {
      let docType = "-";
      let subTypeId = this.documentFormData.Document_Sub_Type_Id;
      if (subTypeId && this.documentTypeList.length > 0) {
        let selectedSubType = this.documentSubTypeList.filter(
          (el) => el.Document_Sub_Type_Id === subTypeId
        );
        let typeId =
          selectedSubType && selectedSubType.length > 0
            ? selectedSubType[0].Document_Type_Id
            : 0;
        if (typeId) {
          let selectedType = this.documentTypeList.filter(
            (el) => el.Document_Type_Id === typeId
          );
          let docTypeName =
            selectedType && selectedType.length > 0
              ? selectedType[0].Document_Type
              : "-";
          docType = docTypeName;
        }
      }
      return docType;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedDocumentDetails &&
      Object.keys(this.selectedDocumentDetails).length > 0
    ) {
      this.documentFormData = JSON.parse(
        JSON.stringify(this.selectedDocumentDetails)
      );
      if (this.documentFormData["File_Name"]) {
        this.fileContent = {
          name: this.formattedFileName(this.documentFormData["File_Name"]),
          size: this.documentFormData["File_Size"]
            ? this.documentFormData["File_Size"]
            : 100,
        };
      } else {
        this.fileContent = null;
      }
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
    this.backupDocumentFormData = JSON.parse(
      JSON.stringify(this.documentFormData)
    );
    this.retrieveDocumentSubType();
    this.retrieveDocumentCategory();
    this.retrieveDocumentType();
  },

  methods: {
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File Name";
      }
      return "";
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.documentFormData[field] = value;
    },
    onChangeFiles(value) {
      this.fileContent = value;
      if (value) {
        mixpanel.track("EmpProfile-doc-file-changed");
        this.documentFormData["File_Name"] =
          this.selectedEmpId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent.name;
        this.documentFormData["File_Size"] = this.fileContent.size.toString();
        this.documentFormData.Document_Name = this.fileContent.name;
        this.isFileChanged = true;
      }
      this.onChangeFields();
    },
    removeFiles() {
      mixpanel.track("EmpProfile-doc-file-removed");
      this.documentFormData["File_Name"] = "";
      this.documentFormData["File_Size"] = "";
      this.fileContent = null;
      this.onChangeFields();
    },
    async validateDocumentDetails() {
      const { valid } = await this.$refs.addEditDocumentForm.validate();
      mixpanel.track("EmpProfile-doc-submit-clicked");
      if (valid) {
        if (
          JSON.stringify(this.documentFormData) ===
          JSON.stringify(this.backupDocumentFormData)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.validateDocuments();
        }
      }
    },

    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        if (this.fileContent && this.fileContent.size && this.isFileChanged) {
          await this.uploadFileContents(this.fileContent);
        }
        this.updateDocumentDetails();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },
    updateDocumentDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_DOCUMENT_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            documentId: vm.documentFormData.Document_Id,
            documentName: vm.documentFormData.Document_Name,
            documentSubType: vm.documentFormData.Document_Sub_Type_Id,
            fileName: vm.documentFormData["File_Name"],
            fileSize: vm.documentFormData["File_Size"],
            formId: vm.callingFrom === "profile" ? 18 : 243,
            formStatus: vm.actionType === "edit" ? 1 : 0,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          const { message } = res.data.addUpdateEmployeeDocuments;
          mixpanel.track("EmpProfile-doc-edit-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: message?.includes("approval")
              ? "Document submitted for approval."
              : vm.formType === "edit"
              ? "Document details updated successfully"
              : "Document details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-doc-accreditation-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-doc-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "document details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    async uploadFileContents() {
      mixpanel.track("EmpProfile-doc-file-upload-start");
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employees Document Upload/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.documentFormData["File_Name"],
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .catch((error) => {
          throw error;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveDocumentSubType() {
      let vm = this;
      vm.documentSubTypeListLoading = true;
      vm.$apollo
        .query({
          query: LIST_SUB_DOC_TYPE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentSubType &&
            !response.data.listDocumentSubType.errorCode
          ) {
            const { documentSubType } = response.data.listDocumentSubType;
            vm.documentSubTypeList =
              documentSubType && documentSubType.length > 0
                ? documentSubType
                : [];
          }
          vm.documentSubTypeListLoading = false;
        })
        .catch(() => {
          vm.documentSubTypeListLoading = false;
        });
    },

    retrieveDocumentCategory() {
      let vm = this;
      vm.documentCategoryListLoading = true;
      vm.$apollo
        .query({
          query: LIST_DOC_CATEGORY,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentCategory &&
            !response.data.listDocumentCategory.errorCode
          ) {
            const { documentCategory } = response.data.listDocumentCategory;
            vm.documentCategoryList =
              documentCategory && documentCategory.length > 0
                ? documentCategory
                : [];
          }
          vm.documentCategoryListLoading = false;
        })
        .catch(() => {
          vm.documentCategoryListLoading = false;
        });
    },

    retrieveDocumentType() {
      let vm = this;
      vm.documentTypeListLoading = true;
      vm.$apollo
        .query({
          query: LIST_DOC_TYPE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentType &&
            !response.data.listDocumentType.errorCode
          ) {
            const { documentType } = response.data.listDocumentType;
            vm.documentTypeList =
              documentType && documentType.length > 0 ? documentType : [];
          }
          vm.documentTypeListLoading = false;
        })
        .catch(() => {
          vm.documentTypeListLoading = false;
        });
    },
  },
};
</script>
<style>
input::selection {
  background: green;
}
</style>
