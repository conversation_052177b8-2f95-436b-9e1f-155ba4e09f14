<template>
  <div class="d-flex justify-space-between align-center">
    <v-row>
      <v-col cols="12" class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="green"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
          >Document Details</span
        >
      </v-col>
    </v-row>
    <v-dialog
      transition="dialog-bottom-transition"
      v-model="showAddEditDocumentForm"
      width="70%"
    >
      <template v-slot:activator="{ props }">
        <v-btn
          v-if="enableAdd"
          v-bind="props"
          color="primary"
          variant="text"
          @click="openAddForm()"
        >
          <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
        >
      </template>
      <AddEditDocumentDetails
        :selectedDocumentDetails="selectedDocumentDetails"
        :selectedEmpId="selectedEmpId"
        :callingFrom="callingFrom"
        :actionType="actionType"
        @close-document-form="closeAddEditForm"
        @refetch-doc-accreditation-details="submitDocumentForm"
      />
    </v-dialog>
  </div>
  <div class="d-flex justify center" v-if="!isMobileView">
    <v-slide-group
      class="pa-4"
      selected-class="bg-secondary"
      prev-icon="fas fa-chevron-circle-left"
      next-icon="fas fa-chevron-circle-right"
      show-arrows
    >
      <v-slide-group-item>
        <ViewDocuments
          :documentDetails="documentDetails"
          :oldDocumentDetails="oldDocumentDetailsData"
          :formAccess="formAccess"
          :empFormUpdateAccess="empFormUpdateAccess"
          @on-open-edit="openEditForm"
          @on-delete="showDeleteConfirmationModal"
        />
      </v-slide-group-item>
    </v-slide-group>
  </div>
  <div v-else class="d-flex flex-column mt-6 align-center justify-center">
    <ViewDocuments
      :documentDetails="documentDetails"
      :oldDocumentDetails="oldDocumentDetailsData"
      :formAccess="formAccess"
      :empFormUpdateAccess="empFormUpdateAccess"
      @on-open-edit="openEditForm"
      @on-delete="showDeleteConfirmationModal"
    />
  </div>
  <AppWarningModal
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure you want to delete this record?"
    icon-name="fas fa-trash"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onDeleteDocument()"
  >
  </AppWarningModal>
</template>
<script>
import { defineAsyncComponent } from "vue";
import ViewDocuments from "./ViewDocuments.vue";
const AddEditDocumentDetails = defineAsyncComponent(() =>
  import("./AddEditDocumentDetails.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
export default {
  name: "DocumentsMain",
  components: {
    AddEditDocumentDetails,
    ViewDocuments,
  },
  props: {
    documentDetailsData: {
      type: Array,
      required: true,
    },
    oldDocumentDetailsData: {
      type: Array,
      required: false,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    callingFrom: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
  },
  emits: ["refetch-doc-accreditation-details"],
  data: () => ({
    // add/edit
    documentDetails: [],
    selectedDocumentDetails: {},
    showAddEditDocumentForm: false,
    // delete
    selectedDocumentDeleteRecord: {},
    openWarningModal: false,
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    enableAdd() {
      return (
        this.empFormUpdateAccess || (this.formAccess && this.formAccess.add)
      );
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.documentDetailsData && this.documentDetailsData.length > 0) {
      this.documentDetails = this.documentDetailsData;
    }
  },
  methods: {
    submitDocumentForm() {
      this.closeAddEditForm();
      this.$emit("refetch-doc-accreditation-details");
    },
    closeAddEditForm() {
      this.showAddEditDocumentForm = false;
      this.selectedDocumentDetails = {};
    },
    openAddForm() {
      mixpanel.track("EmpProfile-doc-add-opened");
      this.showAddEditDocumentForm = true;
    },
    openEditForm(selectedItem) {
      mixpanel.track("EmpProfile-doc-edit-opened");
      this.selectedDocumentDetails = selectedItem;
      this.showAddEditDocumentForm = true;
    },
    showDeleteConfirmationModal(selectedItem) {
      this.selectedDocumentDeleteRecord = selectedItem;
      this.openWarningModal = true;
    },
    onDeleteDocument() {
      this.onCloseWarningModal();
    },
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedDocumentDeleteRecord = {};
    },
  },
};
</script>
<style scoped>
.slider-parent {
  display: flex !important;
  gap: 6% !important;
  position: relative;
}
.positioning-menu {
  position: absolute;
  top: 10px;
  right: 10px;
}

.overflow-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.truncate-text {
  display: inline-block;
  max-width: 150px; /* Adjust the value as needed */
}

.active-status-border {
  border-left: 7px solid #4caf50 !important;
}
</style>
