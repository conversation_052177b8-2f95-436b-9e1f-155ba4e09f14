<template>
  <div
    v-if="documentDetails && documentDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No documents have been uploaded
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in documentArray"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width: 400px; border-left: 7px solid ${generateRandomColor()}; height:200px;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip
                  :text="
                    data.newDoc?.Document_Name || data.oldDoc?.Document_Name
                  "
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="
                        data.newDoc?.Document_Name || data.oldDoc?.Document_Name
                          ? props
                          : ''
                      "
                    >
                      <span
                        v-if="
                          data.oldDoc?.Document_Name &&
                          data.newDoc?.Document_Name &&
                          data.oldDoc?.Document_Name?.toLowerCase() !==
                            data.newDoc?.Document_Name?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{ checkNullValue(data.oldDoc?.Document_Name) }}
                      </span>
                      <span
                        v-if="data.newDoc"
                        :class="[
                          (data.oldDoc &&
                            data.oldDoc?.Document_Name?.toLowerCase() !==
                              data.newDoc?.Document_Name?.toLowerCase()) ||
                          (!data.oldDoc && oldDocumentDetails)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{ checkNullValue(data.newDoc?.Document_Name) }}
                      </span>
                      <span
                        v-else-if="data.oldDoc"
                        class="text-error text-decoration-line-through"
                      >
                        {{ checkNullValue(data.oldDoc?.Document_Name) }}
                      </span>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>

      <div class="card-columns w-100 mt-n6">
        <span
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Category </b>
              <v-tooltip
                :text="
                  data.newDoc?.Category_Fields || data.oldDoc?.Category_Fields
                "
                location="bottom"
              >
                <template v-slot:activator="{ props }">
                  <div
                    class="py-2"
                    :style="
                      isMobileView ? 'max-width: 200px' : 'max-width:140px'
                    "
                    v-bind="
                      data.newDoc?.Category_Fields ||
                      data.oldDoc?.Category_Fields
                        ? props
                        : ''
                    "
                  >
                    <span
                      v-if="
                        data.oldDoc?.Category_Fields &&
                        data.newDoc?.Category_Fields &&
                        data.oldDoc?.Category_Fields?.toLowerCase() !==
                          data.newDoc?.Category_Fields?.toLowerCase()
                      "
                      class="text-decoration-line-through text-error mr-1"
                    >
                      {{ checkNullValue(data.oldDoc?.Category_Fields) }}
                    </span>
                    <span
                      v-if="data.newDoc"
                      :class="[
                        (data.oldDoc &&
                          data.oldDoc?.Category_Fields?.toLowerCase() !==
                            data.newDoc?.Category_Fields?.toLowerCase()) ||
                        (!data.oldDoc && oldDocumentDetails)
                          ? 'text-success'
                          : '',
                      ]"
                    >
                      {{ checkNullValue(data.newDoc?.Category_Fields) }}
                    </span>
                    <span
                      v-else-if="data.oldDoc"
                      class="text-error text-decoration-line-through"
                    >
                      {{ checkNullValue(data.oldDoc?.Category_Fields) }}
                    </span>
                  </div>
                </template>
              </v-tooltip>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Sub Type </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldDoc?.Document_Sub_Type &&
                    data.newDoc?.Document_Sub_Type &&
                    data.oldDoc?.Document_Sub_Type?.toLowerCase() !==
                      data.newDoc?.Document_Sub_Type?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldDoc?.Document_Sub_Type) }}
                </span>
                <span
                  v-if="data.newDoc"
                  :class="[
                    (data.oldDoc &&
                      data.oldDoc?.Document_Sub_Type?.toLowerCase() !==
                        data.newDoc?.Document_Sub_Type?.toLowerCase()) ||
                    (!data.oldDoc && oldDocumentDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newDoc?.Document_Sub_Type) }}
                </span>
                <span
                  v-else-if="data.oldDoc"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldDoc?.Document_Sub_Type) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Type </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldDoc?.Document_Type &&
                    data.newDoc?.Document_Type &&
                    data.oldDoc?.Document_Type?.toLowerCase() !==
                      data.newDoc?.Document_Type?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldDoc?.Document_Type) }}
                </span>
                <span
                  v-if="data.newDoc"
                  :class="[
                    (data.oldDoc &&
                      data.oldDoc?.Document_Type?.toLowerCase() !==
                        data.newDoc?.Document_Type?.toLowerCase()) ||
                    (!data.oldDoc && oldDocumentDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newDoc?.Document_Type) }}
                </span>
                <span
                  v-else-if="data.oldDoc"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldDoc?.Document_Type) }}
                </span>
              </span>
            </div>
            <div
              v-if="data.newDoc?.File_Name"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <span class="text-blue-grey-darken-3 font-weight-bold"></span>
              <span class="text-blue-grey-darken-6">
                <span
                  style="text-decoration: underline"
                  @click="retrieveDocuments(data.newDoc?.File_Name)"
                  class="text-green"
                >
                  View Document</span
                >
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employees Document Upload"
    fileRetrieveType="documents"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

export default {
  name: "ViewDocuments",
  components: { FilePreviewModal, ActionMenu },

  props: {
    documentDetails: {
      type: Object,
      required: true,
    },
    oldDocumentDetails: {
      type: [Array, Object],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return {
      retrievedFileName: "",
      openModal: false,
      havingAccess: {},
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    documentArray() {
      const oldDoc = this.oldDocumentDetails || [];
      const newDoc = this.documentDetails || [];

      let idSet = new Set();
      let mergedDetails = [];

      newDoc.forEach((newItem) => {
        const id = newItem.Document_Id;
        idSet.add(id);
        const oldItem = oldDoc.find((old) => old.Document_Id === id);
        mergedDetails.push({
          newDoc: newItem,
          oldDoc: oldItem || null,
        });
      });

      oldDoc.forEach((oldItem) => {
        const id = oldItem.Document_Id;
        if (!idSet.has(id)) {
          mergedDetails.push({
            newDoc: null,
            oldDoc: oldItem,
          });
        }
      });

      return mergedDetails;
    },
  },
  methods: {
    //using the generateRandomColor function of helper.js file
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.documentDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    retrieveDocuments(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },
  },
};
</script>
