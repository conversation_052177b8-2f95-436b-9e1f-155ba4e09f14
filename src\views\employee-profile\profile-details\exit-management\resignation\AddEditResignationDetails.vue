<template>
  <v-card class="rounded-lg pa-4">
    <v-card-title class="d-flex align-center">
      <span
        class="text-h6 text-grey-darken-1 font-weight-bold mb-4 flex-grow-1 text-center"
        >Resignation</span
      >
      <v-icon
        color="primary"
        class="mt-n8"
        size="20"
        @click="$emit('close-resignation-form')"
        >fas fa-times</v-icon
      >
    </v-card-title>

    <v-card-text>
      <v-form ref="addEditDocumentForm">
        <v-row>
          <v-col cols="12" sm="4" md="4" class="px-md-6 pb-0 mb-2">
            <v-menu
              v-model="resignationMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="formattedResignation"
                  v-model="formattedResignation"
                  prepend-inner-icon="fas fa-calendar"
                  readonly
                  :disabled="true"
                  :rules="[
                    required('Date of Resignation', formattedResignation),
                  ]"
                  v-bind="props"
                  :clearable="true"
                  variant="solo"
                  ><template v-slot:label>
                    Date of Resignation
                    <span style="color: red">*</span>
                  </template></v-text-field
                >
              </template>
              <v-date-picker
                v-model="selectedResignationDate"
                @update:model-value="isFormDirty = true"
              ></v-date-picker> </v-menu
          ></v-col>
          <v-col cols="12" sm="4" md="4" class="px-md-6 pb-0 mb-2">
            <v-menu
              v-model="exitDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="formattedExitDate"
                  v-model="formattedExitDate"
                  prepend-inner-icon="fas fa-calendar"
                  readonly
                  v-bind="props"
                  :clearable="true"
                  variant="solo"
                  :disabled="true"
                  ><template v-slot:label>
                    Date of Exit
                    <span style="color: red">*</span>
                  </template></v-text-field
                >
              </template>
              <v-date-picker
                v-model="selectedExitDate"
                @update:model-value="isFormDirty = true"
              />
            </v-menu>
          </v-col>
          <v-col cols="12" sm="4" md="4" class="px-md-6 pb-0 mb-2">
            <div class="ml-2">
              <p class="text-subtitle-2 text-grey-darken-1">
                <strong>Notice Period</strong>
              </p>

              <p
                v-if="noticeperiod"
                class="text-subtitle-3 font-weight-regular"
              >
                {{ parseInt(noticeperiod) }} day(s)
              </p>
              <p v-else class="text-subtitle-3 font-weight-regular">-</p>
            </div>
          </v-col>
          <v-col
            v-if="labelList[462]?.Field_Visiblity?.toLowerCase() === 'yes'"
            cols="12"
            class="px-md-6 pb-0"
          >
            <v-file-input
              ref="documentUpload"
              prepend-icon=""
              clearable
              chips
              :model-value="selectedDocument"
              append-inner-icon="fas fa-paperclip"
              variant="solo"
              :rules="[
                labelList[462].Mandatory_Field?.toLowerCase() == 'yes'
                  ? required(
                      labelList[462].Field_Alias || `Documents`,
                      selectedDocument
                    )
                  : true,
                fileTypeRule(selectedDocument),
              ]"
              :persistent-hint="true"
              hint="Max Size: 3MB. Allowed formats: .jpg, .jpeg, .png, .pdf, .doc, .docx"
              accept=".jpg, .jpeg, .png, .pdf, .doc, .docx"
              @update:model-value="onUploadFile($event)"
              @click:clear="selectedDocument = null"
            >
              <template v-slot:label>
                {{ labelList[462].Field_Alias || "Documents"
                }}<span
                  v-if="labelList[462].Mandatory_Field?.toLowerCase() == 'yes'"
                  style="color: red"
                  >*</span
                >
              </template>
            </v-file-input>
          </v-col>
          <v-col cols="12" class="px-md-6 pb-0">
            <v-textarea
              v-model="selectedComments"
              variant="solo"
              auto-grow
              label="Comments"
              rows="3"
              :rules="[
                minLengthValidation('Comments', selectedComments, 5),
                maxLengthValidation('Comments', selectedComments, 600),
                validateWithRulesAndReturnMessages(
                  selectedComments,
                  'description',
                  'Comments'
                ),
              ]"
              @update:model-value="isFormDirty = true"
            />
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <div class="d-flex justify-end">
              <v-btn
                @click="$emit('close-resignation-form')"
                variant="outlined"
                rounded="lg"
                class="ma-2 pa-2"
                >Cancel</v-btn
              >
              <v-btn
                :disabled="!selectedResignationDate && !selectedExitDate"
                class="ma-2 pa-2"
                color="primary"
                rounded="lg"
                variant="elevated"
                @click="validateresignationDetails"
              >
                Save
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-card>
  <AppLoading v-if="isLoading || reasonLoading"></AppLoading>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import {
  CREATE_RESIGNATION,
  GET_EMPLOYEE_RESIGNATION,
  GET_EMPLOYEE_NOTIFICATION_PERIOD_DAYS,
} from "@/graphql/my-team/exitManagement.js";

export default {
  name: "AddEditResignationDetails",
  props: {
    selectedEmpId: {
      type: Number,
      default: 0,
    },
  },
  emits: ["close-resignation-form", "added-new-record"],
  mixins: [validationRules],
  data() {
    return {
      // Date Picker
      resignationMenu: false,
      exitDateMenu: false,
      formattedResignation: "",
      formattedExitDate: "",
      selectedResignationDate: null,
      selectedExitDate: null,
      noticeperiod: null,
      selectedComments: "",
      selectedDocument: null,
      reasonItems: [],
      // edit
      isFormDirty: false,
      isLoading: false,
      reasonLoading: false,
      isNoticePeriod: false,
    };
  },

  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  watch: {
    selectedResignationDate(val) {
      if (val) {
        this.resignationMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedResignation = dateValue;
      }
    },
    selectedExitDate(val) {
      if (val) {
        this.exitDateMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedExitDate = dateValue;
      }
    },
  },

  mounted() {
    this.getEmployeeNotificationPeriodDays();
    this.retrieveRelievingReasonDetails();
  },

  methods: {
    onChangeFields() {
      this.isFormDirty = true;
    },
    getEmployeeNotificationPeriodDays() {
      let vm = this;
      vm.isNoticePeriod = true;
      vm.$apollo
        .query({
          query: GET_EMPLOYEE_NOTIFICATION_PERIOD_DAYS,
          client: "apolloClientZ",
          variables: {
            envelope: {
              loggedInUserId: vm.loginEmployeeId,
              orgCode: vm.orgCode,
              formId: 292,
            },
            employeeId: vm.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getEmployeeNotificationPeriodDays &&
            !response.data.getEmployeeNotificationPeriodDays.error
          ) {
            vm.noticeperiod =
              response.data.getEmployeeNotificationPeriodDays.result;
            vm.calculateRetriveNoticePeriod(vm.noticeperiod);
            vm.isNoticePeriod = false;
          } else {
            vm.handleNoticePeriodError();
          }
        })
        .catch((err) => {
          vm.handleNoticePeriodError(err);
        });
    },
    handleNoticePeriodError(err = "") {
      this.isNoticePeriod = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Resignation",
        isListError: true,
      });
    },
    calculateRetriveNoticePeriod(noticePeriod) {
      this.selectedResignationDate = new Date();
      const resignationDate = this.selectedResignationDate;
      resignationDate.setHours(0, 0, 0, 0);

      const exitDate = new Date(moment().add(noticePeriod, "days"));

      this.selectedExitDate = exitDate;
      exitDate.setHours(0, 0, 0, 0);
      this.noticeperiod = noticePeriod
        ? noticePeriod
        : (exitDate - resignationDate) / (1000 * 3600 * 24);
    },
    onUploadFile(file) {
      if (!file) return;

      this.isFormDirty = true;
      this.selectedDocument = file;

      const timestamp = moment().unix();

      if (!this.selectedDocument.formattedName) {
        this.selectedDocument.formattedName = `${this.selectedEmpId}?${timestamp}?${this.selectedDocument.name}`;
      }
    },
    async validateresignationDetails() {
      const { valid } = await this.$refs.addEditDocumentForm.validate();
      if (valid) {
        try {
          this.isLoading = true;
          // Upload Document file
          if (this.selectedDocument) {
            await this.uploadDocumentFile(this.selectedDocument);
          }
          // If not in edit mode, add a new resignation record
          this.addResignationRecord();
        } catch (error) {
          this.isLoading = false;
          this.$store.dispatch("handleApiErrors", {
            error: error,
            action: "uploading",
            form: "document",
            isListError: false,
          });
        }
      }
    },
    addResignationRecord() {
      let vm = this;
      vm.isLoading = true;
      try {
        let reason = vm.reasonItems.find((item) => item?.reasonId == 3);
        if (!reason) {
          reason = vm.reasonItems[0];
        }

        vm.$apollo
          .mutate({
            mutation: CREATE_RESIGNATION,
            variables: {
              envelope: {
                loggedInUserId: vm.loginEmployeeId,
                orgCode: vm.orgCode,
                formId: 292,
              },
              employeeId: vm.selectedEmpId,
              esicReason: String(reason?.esicReasonName || ""),
              reasonId: parseInt(reason?.reasonId),
              relievingReasonComment: vm.selectedComments
                ? vm.selectedComments
                : "",
              appliedDate: moment(vm.selectedResignationDate).isValid()
                ? moment(vm.selectedResignationDate).format("YYYY/MM/DD")
                : null,
              resignationDate: moment(vm.selectedExitDate).isValid()
                ? moment(vm.selectedExitDate).format("YYYY/MM/DD")
                : null,
              fileName: vm.selectedDocument?.formattedName || "",
            },
            client: "apolloClientZ",
          })
          .then((response) => {
            if (
              response?.data?.createResignation &&
              !response.data.createResignation.error &&
              response.data.createResignation.result?.success
            ) {
              let createResignationResponse = response.data.createResignation
                .result
                ? response.data.createResignation.result
                : {};
              vm.$emit("added-new-record", createResignationResponse);
              var snackbarData = {
                isOpen: true,
                type: "success",
                message: "Resignation created successfully.",
              };
            } else {
              let msg =
                response.data.createResignation.error?.message ||
                response.data.createResignation.result?.validation[0]?.message;
              snackbarData = {
                isOpen: true,
                type: "warning",
                message: msg,
              };
            }
            vm.isLoading = false;
            vm.$emit("close-resignation-form");
            vm.showAlert(snackbarData);
          })
          .catch((err) => {
            vm.handleAddRecordError(err);
          });
      } catch (err) {
        vm.handleAddRecordError(err);
      }
    },
    handleAddRecordError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "adding",
        form: "Resignation",
        isListError: false,
      });
    },

    async uploadDocumentFile(file) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Resignation Documents/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + file.formattedName,
          action: "upload",
          type: "documents",
          fileContent: file,
        })
        .catch((error) => {
          throw error;
        });
    },
    retrieveRelievingReasonDetails() {
      let vm = this;
      vm.reasonLoading = true;
      vm.$apollo
        .query({
          query: GET_EMPLOYEE_RESIGNATION,
          client: "apolloClientZ",
          variables: {
            envelope: {
              loggedInUserId: this.loginEmployeeId,
              orgCode: this.orgCode,
              formId: 292,
            },
            resignationId: vm.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getEmployeeResignation &&
            !response.data.getEmployeeResignation.error
          ) {
            vm.reasonItems =
              response.data.getEmployeeResignation?.relievingReasonDetails ||
              [];
          } else {
            vm.handleReasonItemsError();
          }
          vm.reasonLoading = false;
        })
        .catch((err) => {
          vm.reasonLoading = false;
          vm.handleReasonItemsError(err);
        });
    },
    handleReasonItemsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Resignation",
        isListError: true,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
input::selection {
  background: green;
}
</style>
