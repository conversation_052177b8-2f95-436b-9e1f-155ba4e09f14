<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Asset Details</span
        >
        <v-spacer></v-spacer>
        <v-icon color="grey" size="25" @click="$emit('close-asset-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditAssetForm">
          <v-row>
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="assetFormData.Asset_Name"
                variant="solo"
                :rules="[
                  required('Asset', assetFormData.Asset_Name),
                  validateWithRulesAndReturnMessages(
                    assetFormData.Asset_Name,
                    'assetName',
                    'Asset'
                  ),
                ]"
                :disabled="
                  selectedEmpStatus === 'InActive' && formType === 'edit'
                "
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  Asset<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="assetFormData.Serial_No"
                variant="solo"
                :rules="[
                  required('Serial No', assetFormData.Serial_No),
                  validateWithRulesAndReturnMessages(
                    assetFormData.Serial_No,
                    'serialNo',
                    'Serial No'
                  ),
                ]"
                :disabled="
                  selectedEmpStatus === 'InActive' && formType === 'edit'
                "
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  Serial No<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col cols="12" sm="6">
              <section
                class="text-body-2"
                :class="
                  selectedEmpStatus === 'InActive' && formType === 'edit'
                    ? 'cursor-not-allow'
                    : ''
                "
              >
                <v-menu
                  v-model="receivedDateMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      ref="Received Date"
                      v-model="formattedReceivedDate"
                      prepend-inner-icon="fas fa-calendar"
                      :rules="[
                        required('Received Date', formattedReceivedDate),
                      ]"
                      readonly
                      v-bind="props"
                      :disabled="
                        selectedEmpStatus === 'InActive' && formType === 'edit'
                      "
                      :style="
                        selectedEmpStatus === 'InActive' && formType === 'edit'
                          ? 'pointer-events: none;'
                          : ''
                      "
                      @input="onChangeFields()"
                      variant="solo"
                    >
                      <template v-slot:label>
                        Received Date<span style="color: red">*</span>
                      </template></v-text-field
                    >
                  </template>
                  <v-date-picker
                    v-model="assetFormData.Receive_Date"
                    :min="selectedEmpDojDate"
                    :max="receiveDateMax"
                  />
                </v-menu>
              </section>
            </v-col>
            <v-col cols="12" sm="6">
              <v-menu
                v-model="ReturnDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="Return Date"
                    v-model="formattedReturnDate"
                    prepend-inner-icon="fas fa-calendar"
                    :disabled="!assetFormData.Receive_Date"
                    v-bind="props"
                    @input="onChangeFields()"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Return Date
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="assetFormData.Return_Date"
                  :min="returnDateMin"
                />
              </v-menu>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="$emit('close-asset-form')"
                  class="ma-2 pa-2"
                  variant="outlined"
                >
                  Cancel
                </v-btn>
                <v-btn
                  class="ma-2 pa-1"
                  :disabled="!isFormDirty"
                  color="primary"
                  @click="validateAssetDetails()"
                >
                  Save
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { ADD_UPDATE_ASSET_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AddEditAssetDetails",
  mixins: [validationRules],
  props: {
    assetDetails: {
      type: Object,
      required: false,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmpDoj: {
      type: String,
      default: "",
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
  },
  emits: ["refetch-asset-details", "close-asset-form"],
  data() {
    return {
      assetFormData: {
        Asset_Name: "",
        Serial_No: "",
        Receive_Date: null,
        Return_Date: null,
      },
      backupAssetFormData: {},
      //Date-picker
      formattedReceivedDate: null,
      formattedReturnDate: null,
      ReturnDateMenu: false,
      receivedDateMenu: false,
      isFormDirty: false,
      // edit
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
    };
  },

  computed: {
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    receiveDateMax() {
      if (
        this.assetFormData.Return_Date &&
        this.assetFormData.Return_Date !== "0000-00-00"
      ) {
        return moment(this.assetFormData.Return_Date).format("YYYY-MM-DD");
      }
      return this.currentDate;
    },
    returnDateMin() {
      if (
        this.assetFormData.Receive_Date &&
        this.assetFormData.Receive_Date !== "0000-00-00"
      ) {
        return moment(this.assetFormData.Receive_Date).format("YYYY-MM-DD");
      }
      return null;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    selectedEmpDojDate() {
      if (this.selectedEmpDoj && this.selectedEmpDoj !== "0000-00-00") {
        return moment(this.selectedEmpDoj).format("YYYY-MM-DD");
      } else return null;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  watch: {
    "assetFormData.Receive_Date": function (val) {
      // Do something with the new value and/or old value here
      if (val) {
        this.receivedDateMenu = false;
        this.formattedReceivedDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "assetFormData.Return_Date": function (val) {
      // Do something with the new value and/or old value here
      if (val) {
        this.ReturnDateMenu = false;
        this.formattedReturnDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.assetDetails && Object.keys(this.assetDetails).length > 0) {
      this.assetFormData = JSON.parse(JSON.stringify(this.assetDetails));
      if (this.assetFormData.Receive_Date) {
        this.formattedReceivedDate = this.formatDate(
          this.assetFormData?.Receive_Date
        );
        this.assetFormData.Receive_Date = this.assetFormData.Receive_Date
          ? new Date(this.assetFormData.Receive_Date)
          : null;
      }
      if (this.assetFormData.Return_Date) {
        this.formattedReturnDate = this.formatDate(
          this.assetFormData?.Return_Date
        );
        this.assetFormData.Return_Date = this.assetFormData.Return_Date
          ? new Date(this.assetFormData.Return_Date)
          : null;
      }
      this.backupAssetFormData = JSON.parse(JSON.stringify(this.assetFormData));
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
  },

  methods: {
    onChangeFields() {
      this.isFormDirty = true;
    },

    async validateAssetDetails() {
      const { valid } = await this.$refs.addEditAssetForm.validate();
      mixpanel.track("EmpProfile-asset-edit-submit-click");
      if (valid) {
        this.assetFormData.Receive_Date = moment(
          this.assetFormData.Receive_Date
        ).isValid()
          ? moment(this.assetFormData.Receive_Date).format("YYYY-MM-DD")
          : null;
        this.assetFormData.Return_Date = moment(
          this.assetFormData.Return_Date
        ).isValid()
          ? moment(this.assetFormData.Return_Date).format("YYYY-MM-DD")
          : null;

        this.backupAssetFormData.Receive_Date = moment(
          this.backupAssetFormData.Receive_Date
        ).isValid()
          ? moment(this.backupAssetFormData.Receive_Date).format("YYYY-MM-DD")
          : null;
        this.backupAssetFormData.Return_Date = moment(
          this.backupAssetFormData.Return_Date
        ).isValid()
          ? moment(this.backupAssetFormData.Return_Date).format("YYYY-MM-DD")
          : null;
        if (
          JSON.stringify(this.assetFormData) ===
          JSON.stringify(this.backupAssetFormData)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.updateAssetDetails();
        }
      }
    },

    updateAssetDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_ASSET_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            assetId: vm.assetFormData.Asset_Id,
            assetName: vm.assetFormData.Asset_Name,
            serialNo: vm.assetFormData.Serial_No,
            receiveDate: moment(vm.assetFormData.Receive_Date).isValid()
              ? moment(vm.assetFormData.Receive_Date).format("YYYY-MM-DD")
              : null,
            returnDate: moment(vm.assetFormData.Return_Date).isValid()
              ? moment(vm.assetFormData.Return_Date).format("YYYY-MM-DD")
              : null,
            formId:
              vm.callingFrom && vm.callingFrom.toLowerCase() === "profile"
                ? 18
                : 243,
            formStatus:
              vm.actionType && vm.actionType.toLowerCase() === "add" ? 0 : 1,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          mixpanel.track("EmpProfile-asset-edit-success");
          if (res && res.data && res.data.addUpdateAssetDetails) {
            const { errorCode, message } = res.data.addUpdateAssetDetails;
            if (!errorCode) {
              vm.isLoading = false;
              let snackbarData = {
                isOpen: true,
                message:
                  message && message.toLowerCase().includes("approval")
                    ? "Asset details updation is submitted for approval."
                    : vm.formType && vm.formType.toLowerCase() === "edit"
                    ? "Asset details updated successfully"
                    : "Asset details added successfully",
                type: "success",
              };
              vm.showAlert(snackbarData);
              vm.$emit("refetch-asset-details");
            } else {
              vm.handleUpdateError();
            }
          } else {
            vm.handleUpdateError();
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-asset-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "asset details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
