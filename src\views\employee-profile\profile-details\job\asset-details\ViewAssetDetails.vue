<template>
  <div
    v-if="assetDetails && assetDetails.length === 0 && !isApprovalView"
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No asset details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(assetItem, index) in mergedAssets"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:400px;border-left: 7px solid ${generateRandomColor()}; height:180px`
        : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip
                  :text="
                    formAssetName(
                      assetItem.newAsset?.Asset_Name,
                      assetItem.oldAsset?.Asset_Name
                    )
                  "
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="props"
                    >
                      <span
                        v-if="
                          assetItem.oldAsset &&
                          assetItem.newAsset &&
                          assetItem.oldAsset.Asset_Name !==
                            assetItem.newAsset.Asset_Name
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{ checkNullValue(assetItem.oldAsset.Asset_Name) }}
                      </span>
                      <span
                        :class="
                          (assetItem.oldAsset &&
                            assetItem.newAsset &&
                            assetItem.oldAsset.Asset_Name !==
                              assetItem.newAsset.Asset_Name) ||
                          (!assetItem.oldAsset && oldAssetDetailsData)
                            ? 'text-success'
                            : ''
                        "
                      >
                        {{
                          checkNullValue(
                            assetItem.newAsset?.Asset_Name ||
                              assetItem.oldAsset?.Asset_Name
                          )
                        }}
                      </span>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>

      <div class="w-100 mt-n6">
        <div class="card-columns">
          <span
            :style="!isMobileView ? 'width:50%' : 'width:100%'"
            class="d-flex align-start flex-column"
          >
            <v-card-text class="text-body-1 font-weight-regular">
              <div class="mt-2 mr-2 d-flex flex-column justify-start">
                <b class="mr-2 text-grey justify-start">Received Date </b>
                <span class="pb-1 pt-1">
                  <span
                    v-if="
                      assetItem.oldAsset &&
                      assetItem.newAsset &&
                      assetItem.oldAsset.Receive_Date !==
                        assetItem.newAsset.Receive_Date
                    "
                    class="text-decoration-line-through text-error mr-1"
                  >
                    {{ formatDate(assetItem.oldAsset.Receive_Date) }}
                  </span>
                  <span
                    :class="
                      (assetItem.oldAsset &&
                        assetItem.newAsset &&
                        assetItem.oldAsset.Receive_Date !==
                          assetItem.newAsset.Receive_Date) ||
                      (!assetItem.oldAsset && oldAssetDetailsData)
                        ? 'text-success'
                        : ''
                    "
                  >
                    {{
                      formatDate(
                        assetItem.newAsset?.Receive_Date ||
                          assetItem.oldAsset?.Receive_Date
                      )
                    }}
                  </span>
                </span>
              </div>
              <div class="mt-2 mr-2 d-flex flex-column justify-start">
                <b class="mr-2 text-grey justify-start">Serial No </b>
                <span class="mb-3">
                  <span
                    v-if="
                      assetItem.oldAsset &&
                      assetItem.newAsset &&
                      assetItem.oldAsset.Serial_No !==
                        assetItem.newAsset.Serial_No
                    "
                    class="text-decoration-line-through text-error mr-1"
                  >
                    {{ checkNullValue(assetItem.oldAsset.Serial_No) }}
                  </span>
                  <span
                    :class="
                      (assetItem.oldAsset &&
                        assetItem.newAsset &&
                        assetItem.oldAsset.Serial_No !==
                          assetItem.newAsset.Serial_No) ||
                      (!assetItem.oldAsset && oldAssetDetailsData)
                        ? 'text-success'
                        : ''
                    "
                  >
                    {{
                      checkNullValue(
                        assetItem.newAsset?.Serial_No ||
                          assetItem.oldAsset?.Serial_No
                      )
                    }}
                  </span>
                </span>
              </div>
            </v-card-text>
          </span>
          <span
            :style="
              !isMobileView
                ? 'width:50%'
                : 'margin-top:-40px !important;margin-bottom: 10px !important;width:100%'
            "
            class="d-flex align-start flex-column"
          >
            <v-card-text class="text-body-1 font-weight-regular">
              <div class="mt-2 mr-2 d-flex flex-column justify-start">
                <b class="mr-2 text-grey justify-start">Return Date </b>
                <span class="pb-1 pt-1">
                  <span
                    v-if="
                      assetItem.oldAsset &&
                      assetItem.newAsset &&
                      assetItem.oldAsset.Return_Date !==
                        assetItem.newAsset.Return_Date
                    "
                    class="text-decoration-line-through text-error mr-1"
                  >
                    {{ formatDate(assetItem.oldAsset.Return_Date) }}
                  </span>
                  <span
                    :class="
                      (assetItem.oldAsset &&
                        assetItem.newAsset &&
                        assetItem.oldAsset.Return_Date !==
                          assetItem.newAsset.Return_Date) ||
                      (!assetItem.oldAsset && oldAssetDetailsData)
                        ? 'text-success'
                        : ''
                    "
                  >
                    {{
                      formatDate(
                        assetItem.newAsset?.Return_Date ||
                          assetItem.oldAsset?.Return_Date
                      )
                    }}
                  </span>
                </span>
              </div>
            </v-card-text>
          </span>
        </div>
      </div>
    </div>
    <div v-if="enableEdit && !isApprovalView" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import moment from "moment";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ViewAssetDetails",
  data() {
    return {
      havingAccess: {},
    };
  },
  props: {
    assetDetails: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    isApprovalView: {
      type: Boolean,
      default: false,
    },
    oldAssetDetailsData: {
      type: Array,
      required: false,
    },
  },
  components: { ActionMenu },
  emits: ["on-delete", "on-open-edit"],
  computed: {
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    employeeEdit() {
      return this.$store.state.orgDetails.employeeEdit;
    },
    enableEdit() {
      return (
        (this.empFormUpdateAccess && this.employeeEdit) ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    mergedAssets() {
      const oldAssets = this.oldAssetDetailsData || [];
      const newAssets = this.assetDetails || [];

      const idSet = new Set();

      oldAssets.forEach((asset) => {
        if (asset?.Asset_Id) idSet.add(asset.Asset_Id);
      });
      newAssets.forEach((asset) => {
        if (asset?.Asset_Id) idSet.add(asset.Asset_Id);
      });

      const merged = [];
      Array.from(idSet).map((id) => {
        const oldAsset = oldAssets.find((a) => a?.Asset_Id === id);
        const newAsset = newAssets.find((a) => a?.Asset_Id === id);
        merged.push({ key: id, oldAsset, newAsset });
      });
      // Handle newAssets with no Asset_Id
      newAssets.forEach((asset, index) => {
        if (!asset?.Asset_Id) {
          merged.push({
            key: `new-${index}`,
            oldAsset: undefined,
            newAsset: asset,
          });
        }
      });
      return merged;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        (this.empFormUpdateAccess && this.employeeEdit) ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin &&
          this.formAccess.admin.toLowerCase() === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem =
        this.mergedAssets[index].newAsset || this.mergedAssets[index].oldAsset;
      if (action && action.toLowerCase() === "delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    formAssetName(newName, oldName) {
      if (newName || oldName) {
        return newName || oldName || "";
      } else return "";
    },
  },
};
</script>

<style scoped>
.text-success {
  color: green;
}
.text-error {
  color: red;
}
</style>
