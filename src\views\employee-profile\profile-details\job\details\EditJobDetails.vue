<template>
  <v-dialog
    v-model="showRolesAssociation"
    v-if="showRolesAssociation"
    @click:outside="showRolesAssociation = false"
    max-width="1300px"
  >
    <RoleAccessRights
      :selectedItem="selectedRole"
      action-type="empView"
      @close-form="showRolesAssociation = false"
    >
    </RoleAccessRights>
  </v-dialog>
  <div v-if="isMounted">
    <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
      >Job Info</span
    >
    <v-form ref="editJobDetailsForm" class="pa-2">
      <v-row class="mb-8">
        <v-col cols="12" md="4" sm="6">
          <div class="d-flex mt-n2">
            <p class="custom-label">
              Role (Access Rights)<span style="color: red">*</span>
            </p>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  v-if="actionType !== 'add'"
                  class="ml-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="max-width: 350px !important">
                Role access defines the specific permissions and privileges
                granted to users based on their assigned roles. These
                permissions determine what actions a user can perform and what
                data they can access within the application.
              </div>
            </v-tooltip>
          </div>
          <div v-if="!higherHierachyRole && !dropdownListFetching">
            <p class="text-subtitle-1 font-weight-regular mt-3">
              {{ checkNullValue(jobDetails.Roles_Name) }}
            </p>
          </div>
          <CustomSelect
            v-else
            :items="roles"
            label=""
            :isRequired="true"
            :rules="[
              required('Role (Access Rights)', editedJobDetails.Roles_Id),
            ]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Roles_Id"
            itemValue="Roles_Id"
            itemTitle="Roles_Name"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="roleAccess"
            :disabled="!isActive"
            :disabledValue="
              isSuperAdmin ? [] : ['Super Admin', 'Super Admin(T)']
            "
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="onChangeCustomSelectField($event, 'Roles_Id', null)"
          ></CustomSelect>
          <v-btn
            v-if="higherHierachyRole"
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openRolesForm"
            :disabled="!isActive"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add Role</v-btn
          >
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <div class="d-flex mt-n2">
            <p class="custom-label">
              Date of Join<span style="color: red">*</span>
            </p>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  v-if="actionType !== 'add' && !allowDOJToEdit"
                  class="ml-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="max-width: 350px !important">
                Updating Date of Join is restricted as
                attendance/salary/leave/leave balance import record already
                exists for this employee
              </div>
            </v-tooltip>
          </div>
          <section
            class="text-body-2"
            :class="allowDOJToEdit ? '' : 'cursor-not-allow'"
          >
            <v-menu
              v-model="dojMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="dateOfJoin"
                  v-model="formattedDateOfJoin"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[required('Date of Joining', formattedDateOfJoin)]"
                  readonly
                  :disabled="!allowDOJToEdit || !isActive"
                  v-bind="props"
                  variant="solo"
                >
                </v-text-field>
              </template>
              <v-date-picker
                v-model="editedJobDetails.Date_Of_Join"
                :min="selectedEmpDobDate"
                @update:modelValue="
                  onChangeFields('dateOfJoinErrorMsg', 'Date_Of_Join')
                "
              ></v-date-picker>
            </v-menu>
            <span
              v-if="dojErrorMessage && dojErrorMessage.length"
              class="text-caption"
              style="color: #b00020"
            >
              {{ dojErrorMessage }}
            </span>
          </section>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="designations"
            label="Designation"
            :isRequired="true"
            :rules="[required('Designation', editedJobDetails.Designation_Id)]"
            :isLoading="designationListLoading"
            :noDataText="noDataText"
            :itemSelected="editedJobDetails.Designation_Id"
            itemValue="Designation_Id"
            itemTitle="Designation_Name"
            placeholder="Type minimum 3 characters to list"
            :isAutoComplete="true"
            :disabled="!isActive"
            appendIcon="fas fa-redo-alt"
            ref="designations"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="
              onChangeCustomSelectField(
                $event,
                'Designation_Id',
                0,
                'Designation_Id_Effective_Date'
              )
            "
            @update-search-value="callDesignationList($event)"
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openDesignationsForm"
            :disabled="!isActive"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
            Designation</v-btn
          >
        </v-col>
        <v-col
          v-if="labelList[424]?.Field_Visiblity.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <CustomSelect
            ref="selectedServiceProvider"
            v-model="selectedJobRoles"
            :items="jobRolesList"
            :isLoading="jobRolesListLoading"
            item-title="Job_Role"
            item-value="Job_Role_Id"
            :label="labelList[424].Field_Alias"
            :isAutoComplete="true"
            :isRequired="labelList[424].Mandatory_Field.toLowerCase() === 'yes'"
            :rules="[
              labelList[424].Mandatory_Field.toLowerCase() == 'yes'
                ? selectedJobRoles
                  ? required(
                      `${labelList[424].Field_Alias}`,
                      selectedJobRoles[0]
                    )
                  : required(`${labelList[424].Field_Alias}`, selectedJobRoles)
                : true,
            ]"
            variant="solo"
            :selectProperties="{
              multiple: true,
              chips: true,
              closableChips: true,
              clearable: true,
            }"
            :itemSelected="selectedJobRoles"
            @selected-item="selectedJobRoles = $event"
            @update:model-value="onChangeFields"
          ></CustomSelect>
        </v-col>
        <v-col
          v-if="actionType === 'edit' && editedJobDetails.User_Defined_EmpId"
          cols="12"
          md="4"
          sm="6"
        >
          <section
            class="text-body-2"
            :class="effectiveDateProps[0].disable ? 'cursor-not-allow' : ''"
          >
            <v-menu
              v-model="designationEffectiveDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="designationEffectiveDate"
                  v-model="formattedDesignationEffectiveDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[
                    required(
                      'Designation Transfer Effective Date',
                      formattedDesignationEffectiveDate
                    ),
                  ]"
                  readonly
                  :disabled="effectiveDateProps[0].disable || !isActive"
                  v-bind="props"
                  variant="solo"
                >
                  <template v-slot:label>
                    Designation Transfer Effective Date
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </template>
              <v-date-picker
                v-if="jobDetails.Designation_Id_Effective_Date"
                v-model="editedJobDetails.Designation_Id_Effective_Date"
                :month="getMonthOfDate(effectiveDateProps[0].min)"
                :year="getYearOfDate(effectiveDateProps[0].min)"
                :min="effectiveDateProps[0].min"
                :max="effectiveDateProps[0].max"
                @update:modelValue="
                  onChangeFields('', '', 'Designation_Id_Effective_Date')
                "
              ></v-date-picker>
              <v-date-picker
                v-else
                v-model="effectiveDateProps[0].defaultDate"
                :month="getMonthOfDate(effectiveDateProps[0].min)"
                :year="getYearOfDate(effectiveDateProps[0].min)"
                :min="effectiveDateProps[0].min"
                :max="effectiveDateProps[0].max"
                @update:modelValue="
                  onChangeDefaultEffectiveDate(
                    0,
                    'Designation_Id_Effective_Date'
                  )
                "
              ></v-date-picker>
            </v-menu>
          </section>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="departments"
            label="Department"
            :isRequired="true"
            :rules="[required('Department', editedJobDetails.Department_Id)]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Department_Id"
            itemValue="Department_Id"
            itemTitle="Department_Name"
            :isAutoComplete="true"
            :disabled="!isActive"
            appendIcon="fas fa-redo-alt"
            ref="department"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="
              onChangeCustomSelectField(
                $event,
                'Department_Id',
                1,
                'Department_Id_Effective_Date'
              )
            "
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openDepartmentsForm"
            :disabled="!isActive"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
            Department</v-btn
          >
        </v-col>
        <v-col
          v-if="actionType === 'edit' && editedJobDetails.User_Defined_EmpId"
          cols="12"
          md="4"
          sm="6"
        >
          <section
            class="text-body-2"
            :class="effectiveDateProps[1].disable ? 'cursor-not-allow' : ''"
          >
            <v-menu
              v-model="departmentEffectiveDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="departmentEffectiveDate"
                  v-model="formattedDepartmentEffectiveDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[
                    required(
                      'Department Transfer Effective Date',
                      formattedDepartmentEffectiveDate
                    ),
                  ]"
                  readonly
                  :disabled="effectiveDateProps[1].disable || !isActive"
                  v-bind="props"
                  variant="solo"
                >
                  <template v-slot:label>
                    Department Transfer Effective Date
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </template>
              <v-date-picker
                v-if="jobDetails.Department_Id_Effective_Date"
                v-model="editedJobDetails.Department_Id_Effective_Date"
                :month="getMonthOfDate(effectiveDateProps[1].min)"
                :year="getYearOfDate(effectiveDateProps[1].min)"
                :min="effectiveDateProps[1].min"
                :max="effectiveDateProps[1].max"
                @update:modelValue="
                  onChangeFields('', '', 'Department_Id_Effective_Date')
                "
              ></v-date-picker>
              <v-date-picker
                v-else
                v-model="effectiveDateProps[1].defaultDate"
                :month="getMonthOfDate(effectiveDateProps[1].min)"
                :year="getYearOfDate(effectiveDateProps[1].min)"
                :min="effectiveDateProps[1].min"
                :max="effectiveDateProps[1].max"
                @update:modelValue="
                  onChangeDefaultEffectiveDate(
                    1,
                    'Department_Id_Effective_Date'
                  )
                "
              ></v-date-picker>
            </v-menu>
          </section>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="locations"
            label="Location"
            :isRequired="true"
            :rules="[required('Location', editedJobDetails.Location_Id)]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Location_Id"
            itemValue="Location_Id"
            itemTitle="Location_Name"
            :isAutoComplete="true"
            :disabled="!isActive"
            appendIcon="fas fa-redo-alt"
            ref="location"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="
              onChangeCustomSelectField(
                $event,
                'Location_Id',
                2,
                'Location_Id_Effective_Date'
              )
            "
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openLocationsForm"
            :disabled="!isActive"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
            Location</v-btn
          >
        </v-col>
        <v-col
          v-if="actionType === 'edit' && editedJobDetails.User_Defined_EmpId"
          cols="12"
          md="4"
          sm="6"
        >
          <section
            class="text-body-2"
            :class="effectiveDateProps[2].disable ? 'cursor-not-allow' : ''"
          >
            <v-menu
              v-model="locationEffectiveDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="locationEffectiveDate"
                  v-model="formattedLocationEffectiveDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[
                    required(
                      'Relocation Effective Date',
                      formattedLocationEffectiveDate
                    ),
                  ]"
                  readonly
                  :disabled="effectiveDateProps[2].disable || !isActive"
                  v-bind="props"
                  variant="solo"
                >
                  <template v-slot:label>
                    Relocation Effective Date
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </template>
              <v-date-picker
                v-if="jobDetails.Location_Id_Effective_Date"
                v-model="editedJobDetails.Location_Id_Effective_Date"
                :month="getMonthOfDate(effectiveDateProps[2].min)"
                :year="getYearOfDate(effectiveDateProps[2].min)"
                :min="effectiveDateProps[2].min"
                :max="effectiveDateProps[2].max"
                @update:modelValue="
                  onChangeFields('', '', 'Location_Id_Effective_Date')
                "
              ></v-date-picker>
              <v-date-picker
                v-else
                v-model="effectiveDateProps[2].defaultDate"
                :month="getMonthOfDate(effectiveDateProps[2].min)"
                :year="getYearOfDate(effectiveDateProps[2].min)"
                :min="effectiveDateProps[2].min"
                :max="effectiveDateProps[2].max"
                @update:modelValue="
                  onChangeDefaultEffectiveDate(2, 'Location_Id_Effective_Date')
                "
              ></v-date-picker>
            </v-menu>
          </section>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="workSchedules"
            label="Work Schedule"
            :isRequired="true"
            :rules="[required('Work Schedule', editedJobDetails.Work_Schedule)]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Work_Schedule"
            itemValue="WorkSchedule_Id"
            itemTitle="Title"
            :isAutoComplete="true"
            :disabled="!isActive"
            appendIcon="fas fa-redo-alt"
            ref="workSchedule"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="
              onChangeCustomSelectField(
                $event,
                'Work_Schedule',
                3,
                'Work_Schedule_Effective_Date'
              )
            "
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openWorkScheduleForm"
            :disabled="!isActive"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add Work
            Schedule</v-btn
          >
        </v-col>
        <v-col
          v-if="actionType === 'edit' && editedJobDetails.User_Defined_EmpId"
          cols="12"
          md="4"
          sm="6"
        >
          <section
            class="text-body-2"
            :class="effectiveDateProps[3].disable ? 'cursor-not-allow' : ''"
          >
            <v-menu
              v-model="workScheduleEffectiveDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="workScheduleEffectiveDate"
                  v-model="formattedWorkScheduleEffectiveDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[
                    required(
                      'Work Schedule Transfer Effective Date',
                      formattedWorkScheduleEffectiveDate
                    ),
                  ]"
                  readonly
                  :disabled="effectiveDateProps[3].disable || !isActive"
                  v-bind="props"
                  variant="solo"
                >
                  <template v-slot:label>
                    Work Schedule Transfer Effective Date
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </template>
              <v-date-picker
                v-if="jobDetails.Work_Schedule_Effective_Date"
                v-model="editedJobDetails.Work_Schedule_Effective_Date"
                :month="getMonthOfDate(effectiveDateProps[3].min)"
                :year="getYearOfDate(effectiveDateProps[3].min)"
                :min="effectiveDateProps[3].min"
                :max="effectiveDateProps[3].max"
                @update:modelValue="onChangeFields()"
              ></v-date-picker>
              <v-date-picker
                v-else
                v-model="effectiveDateProps[3].defaultDate"
                :month="getMonthOfDate(effectiveDateProps[3].min)"
                :year="getYearOfDate(effectiveDateProps[3].min)"
                :min="effectiveDateProps[3].min"
                :max="effectiveDateProps[3].max"
                @update:modelValue="
                  onChangeDefaultEffectiveDate(
                    3,
                    'Work_Schedule_Effective_Date'
                  )
                "
              ></v-date-picker>
            </v-menu>
          </section>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="employeeTypes"
            label="Employee Type"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.EmpType_Id"
            itemValue="EmpType_Id"
            itemTitle="Employee_Type"
            :isRequired="true"
            :rules="[required('Employee Type', editedJobDetails.EmpType_Id)]"
            :isAutoComplete="true"
            :disabled="!isActive"
            appendIcon="fas fa-redo-alt"
            ref="employeeType"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="
              onChangeCustomSelectField(
                $event,
                'EmpType_Id',
                5,
                'EmpType_Id_Effective_Date'
              )
            "
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openEmployeeTypeForm"
            :disabled="!isActive"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add Employee
            Type</v-btn
          >
        </v-col>
        <v-col
          v-if="actionType === 'edit' && editedJobDetails.User_Defined_EmpId"
          cols="12"
          md="4"
          sm="6"
        >
          <section
            class="text-body-2"
            :class="effectiveDateProps[5].disable ? 'cursor-not-allow' : ''"
          >
            <v-menu
              v-model="empTypeEffectiveDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="employeeTypeEffectiveDate"
                  v-model="formattedEmpTypeEffectiveDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[
                    required(
                      'Employee Type Transfer Effective Date',
                      formattedEmpTypeEffectiveDate
                    ),
                  ]"
                  readonly
                  :disabled="effectiveDateProps[5].disable || !isActive"
                  v-bind="props"
                  variant="solo"
                >
                  <template v-slot:label>
                    Employee Type Transfer Effective Date
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </template>
              <v-date-picker
                v-if="jobDetails.EmpType_Id_Effective_Date"
                v-model="editedJobDetails.EmpType_Id_Effective_Date"
                :month="getMonthOfDate(effectiveDateProps[5].min)"
                :year="getYearOfDate(effectiveDateProps[5].min)"
                :min="effectiveDateProps[5].min"
                :max="effectiveDateProps[5].max"
                @update:modelValue="onChangeFields()"
              ></v-date-picker>
              <v-date-picker
                v-else
                v-model="effectiveDateProps[5].defaultDate"
                :month="getMonthOfDate(effectiveDateProps[5].min)"
                :year="getYearOfDate(effectiveDateProps[5].min)"
                :min="effectiveDateProps[5].min"
                :max="effectiveDateProps[5].max"
                @update:modelValue="
                  onChangeDefaultEffectiveDate(5, 'EmpType_Id_Effective_Date')
                "
              ></v-date-picker>
            </v-menu>
          </section>
        </v-col>
        <v-col v-if="editedJobDetails.Field_Force" cols="12" md="4" sm="6">
          <div class="d-flex mt-n2" v-if="actionType === 'edit'">
            <p class="custom-label">
              {{ getCustomFieldName(115, "Service Provider")
              }}<span style="color: red">*</span>
            </p>
            <v-tooltip location="right">
              <template v-slot:activator="{ props }">
                <v-icon class="ml-1" v-bind="props" size="small" color="info">
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="max-width: 350px !important">
                To transfer an employee from one
                {{ getCustomFieldName(115, "Service Provider").toLowerCase() }}
                to another, kindly initiate the resignation process
              </div>
            </v-tooltip>
          </div>
          <CustomSelect
            :items="serviceProviders"
            :label="
              actionType === 'edit'
                ? ''
                : getCustomFieldName(115, 'Service Provider')
            "
            :isRequired="true"
            :rules="[
              required(
                getCustomFieldName(115, 'Service Provider'),
                editedJobDetails.Service_Provider_Id
              ),
            ]"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Service_Provider_Id"
            itemValue="Service_Provider_Id"
            itemTitle="Service_Provider_Name"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="serviceProvider"
            :disabled="
              (actionType === 'edit' &&
                editedJobDetails &&
                editedJobDetails.Service_Provider_Id) ||
              !isActive
            "
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="
              onChangeCustomSelectField($event, 'Service_Provider_Id')
            "
          ></CustomSelect>
        </v-col>
        <!-- Work Email -->
        <v-col v-if="entomoIntegrationEnabled" cols="12" md="4" sm="6">
          <v-text-field
            class="mt-3"
            v-model.trim="editedJobDetails.Emp_Email"
            variant="solo"
            :rules="
              selectedEmployeeDetails.Allow_User_Signin &&
              !selectedEmployeeDetails.Enable_Sign_In_With_Mobile_No &&
              isEntomoSyncTypePush
                ? [
                    required(
                      labelList[467]?.Field_Alias || 'Work Email',
                      editedJobDetails.Emp_Email
                    ),
                    validateWithRulesAndReturnMessages(
                      editedJobDetails.Emp_Email,
                      'empEmail',
                      labelList[467]?.Field_Alias || 'Work Email'
                    ),
                    alreadyExistErrMsg['Emp_Email'],
                  ]
                : checkFieldAvailability(editedJobDetails.Emp_Email)
                ? [
                    validateWithRulesAndReturnMessages(
                      editedJobDetails.Emp_Email,
                      'empEmail',
                      labelList[467]?.Field_Alias || 'Work Email'
                    ),
                    alreadyExistErrMsg['Emp_Email'],
                  ]
                : [true]
            "
            :disabled="!isActive"
            ref="workEmail"
            @update:model-value="onChangeFields"
            @change="validateFieldAlreadyExist('Emp_Email', 'Work Email')"
          >
            <template v-slot:label>
              {{ labelList[467]?.Field_Alias || "Work Email"
              }}<span
                v-if="
                  isEntomoSyncTypePush &&
                  selectedEmployeeDetails.Allow_User_Signin &&
                  !selectedEmployeeDetails.Enable_Sign_In_With_Mobile_No
                "
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </v-col>
        <v-col
          v-else-if="labelList[467]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-text-field
            class="mt-3"
            v-model.trim="editedJobDetails.Emp_Email"
            variant="solo"
            :rules="
              (selectedEmployeeDetails.Allow_User_Signin &&
                !selectedEmployeeDetails.Enable_Sign_In_With_Mobile_No) ||
              labelList[467]?.Mandatory_Field?.toLowerCase() === 'yes'
                ? [
                    required(
                      labelList[467]?.Field_Alias,
                      editedJobDetails.Emp_Email
                    ),
                    validateWithRulesAndReturnMessages(
                      editedJobDetails.Emp_Email,
                      'empEmail',
                      labelList[467]?.Field_Alias
                    ),
                    alreadyExistErrMsg['Emp_Email'],
                  ]
                : checkFieldAvailability(editedJobDetails.Emp_Email)
                ? [
                    validateWithRulesAndReturnMessages(
                      editedJobDetails.Emp_Email,
                      'empEmail',
                      labelList[467]?.Field_Alias
                    ),
                    alreadyExistErrMsg['Emp_Email'],
                  ]
                : [true]
            "
            :disabled="!isActive"
            ref="workEmail"
            @update:model-value="onChangeFields"
            @change="validateFieldAlreadyExist('Emp_Email', 'Work Email')"
          >
            <template v-slot:label>
              {{ labelList[467]?.Field_Alias
              }}<span
                v-if="
                  labelList[467]?.Mandatory_Field?.toLowerCase() === 'yes' ||
                  (selectedEmployeeDetails.Allow_User_Signin &&
                    !selectedEmployeeDetails.Enable_Sign_In_With_Mobile_No)
                "
                style="color: red"
                >*</span
              >
            </template>
          </v-text-field>
        </v-col>
        <v-col
          v-if="labelList[447]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <div class="d-flex mt-n2">
            <p class="custom-label">
              {{ labelList[447]?.Field_Alias
              }}<span
                v-if="labelList[447]?.Mandatory_Field?.toLowerCase() === 'yes'"
                style="color: red"
                >*</span
              >
            </p>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  v-if="actionType !== 'add'"
                  class="ml-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="max-width: 350px !important">
                The state-imposed tax based on the employee's profession. The
                tax amount is determined by the nature of the profession-
                whether they are classified as a Director or a Employee(Other
                Professional)
              </div>
            </v-tooltip>
          </div>
          <CustomSelect
            :items="empProfessions"
            label=""
            :isRequired="
              labelList[447]?.Mandatory_Field?.toLowerCase() === 'yes'
            "
            :rules="[
              labelList[447]?.Mandatory_Field?.toLowerCase() === 'yes'
                ? required(
                    labelList[447]?.Field_Alias,
                    editedJobDetails.Emp_Profession
                  )
                : true,
            ]"
            :isLoading="empProfessionListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Emp_Profession"
            itemValue="Profession_Id"
            itemTitle="Profession_Name"
            tooltipMessage=""
            appendIcon="fas fa-redo-alt"
            :disabled="!isActive"
            ref="employeeProfession"
            @append-icon-clicked="retrieveEmpProfessions()"
            @selected-item="onChangeCustomSelectField($event, 'Emp_Profession')"
          ></CustomSelect>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <CustomSelect
            :items="managers"
            label="Manager"
            :isLoading="dropdownListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="
              editedJobDetails.Manager_Id ? editedJobDetails.Manager_Id : ''
            "
            itemValue="Manager_Id"
            itemTitle="managerIdName"
            :selectProperties="{
              clearable: true,
            }"
            :disabled="!isActive"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="manager"
            @append-icon-clicked="retrieveDropdownDetails()"
            @selected-item="
              onChangeCustomSelectField(
                $event,
                'Manager_Id',
                4,
                'Manager_Id_Effective_Date'
              )
            "
          ></CustomSelect>
        </v-col>
        <v-col
          v-if="actionType === 'edit' && editedJobDetails.User_Defined_EmpId"
          cols="12"
          md="4"
          sm="6"
        >
          <section
            class="text-body-2"
            :class="effectiveDateProps[4].disable ? 'cursor-not-allow' : ''"
          >
            <v-menu
              v-model="managerEffectiveDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="managerEffectiveDate"
                  v-model="formattedManagerEffectiveDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[
                    required(
                      'Manager Transfer Effective Date',
                      formattedManagerEffectiveDate
                    ),
                  ]"
                  readonly
                  :disabled="effectiveDateProps[4].disable || !isActive"
                  v-bind="props"
                  variant="solo"
                >
                  <template v-slot:label>
                    Manager Transfer Effective Date
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </template>
              <v-date-picker
                v-if="jobDetails.Manager_Id_Effective_Date"
                v-model="editedJobDetails.Manager_Id_Effective_Date"
                :month="getMonthOfDate(effectiveDateProps[4].min)"
                :year="getYearOfDate(effectiveDateProps[4].min)"
                :min="effectiveDateProps[4].min"
                :max="effectiveDateProps[4].max"
                @update:modelValue="onChangeFields()"
              ></v-date-picker>
              <v-date-picker
                v-else
                v-model="effectiveDateProps[4].defaultDate"
                :month="getMonthOfDate(effectiveDateProps[4].min)"
                :year="getYearOfDate(effectiveDateProps[4].min)"
                :min="effectiveDateProps[4].min"
                :max="effectiveDateProps[4].max"
                @update:modelValue="
                  onChangeDefaultEffectiveDate(4, 'Manager_Id_Effective_Date')
                "
              ></v-date-picker>
            </v-menu>
          </section>
        </v-col>
        <v-col
          v-if="labelList[385]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <CustomSelect
            :items="businessUnits"
            :label="labelList[385]?.Field_Alias"
            :isLoading="businessUnitListFetching"
            :noDataText="
              dropdownListFetching ? 'Loading...' : 'No data available'
            "
            :itemSelected="editedJobDetails.Business_Unit_Id"
            itemValue="businessUnitId"
            itemTitle="businessUnit"
            :isRequired="
              labelList[385]?.Mandatory_Field?.toLowerCase() === 'yes'
            "
            :rules="
              labelList['385']?.Mandatory_Field?.toLowerCase() === 'yes'
                ? [
                    required(
                      labelList['385']?.Field_Alias,
                      editedJobDetails.Business_Unit_Id
                    ),
                  ]
                : [true]
            "
            :selectProperties="{
              clearable: true,
            }"
            :isAutoComplete="true"
            appendIcon="fas fa-redo-alt"
            ref="businessUnit"
            @append-icon-clicked="retrieveBusinessUnit()"
            @selected-item="
              onChangeCustomSelectField(
                $event,
                'Business_Unit_Id',
                6,
                'Business_Unit_Id_Effective_Date'
              )
            "
            :disabled="!isActive"
          ></CustomSelect>
          <v-btn
            class="mt-n4 ml-n2"
            color="primary"
            variant="text"
            size="small"
            @click="openBusinessUnitForm"
            :disabled="!isActive"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
            {{ labelList[385]?.Field_Alias }}
          </v-btn>
        </v-col>
        <v-col
          v-if="actionType === 'edit' && editedJobDetails.User_Defined_EmpId"
          cols="12"
          md="4"
          sm="6"
        >
          <section
            class="text-body-2"
            :class="effectiveDateProps[6].disable ? 'cursor-not-allow' : ''"
          >
            <v-menu
              v-model="businessUnitEffectiveDateMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="businessUnitEffectiveDate"
                  v-model="formattedBusinessUnitEffectiveDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[
                    required(
                      `${labelList[385]?.Field_Alias}' Transfer Effective Date'`,
                      formattedBusinessUnitEffectiveDate
                    ),
                  ]"
                  readonly
                  :disabled="effectiveDateProps[6].disable || !isActive"
                  v-bind="props"
                  variant="solo"
                >
                  <template v-slot:label>
                    {{ labelList[385]?.Field_Alias }} Transfer Effective Date
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </template>
              <v-date-picker
                v-if="jobDetails.Business_Unit_Id_Effective_Date"
                v-model="editedJobDetails.Business_Unit_Id_Effective_Date"
                :month="getMonthOfDate(effectiveDateProps[6].min)"
                :year="getYearOfDate(effectiveDateProps[6].min)"
                :min="effectiveDateProps[6].min"
                :max="effectiveDateProps[6].max"
                @update:modelValue="onChangeFields()"
              ></v-date-picker>
              <v-date-picker
                v-else
                v-model="effectiveDateProps[6].defaultDate"
                :month="getMonthOfDate(effectiveDateProps[6].min)"
                :year="getYearOfDate(effectiveDateProps[6].min)"
                :min="effectiveDateProps[6].min"
                :max="effectiveDateProps[6].max"
                @update:modelValue="
                  onChangeDefaultEffectiveDate(
                    6,
                    'Business_Unit_Id_Effective_Date'
                  )
                "
              ></v-date-picker>
            </v-menu>
          </section>
        </v-col>
        <v-col
          v-if="benefitsApplicable && labelList[305].Field_Visiblity == 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <v-text-field
            v-model.trim="editedJobDetails.Pf_PolicyNo"
            label="PF Number"
            :rules="[
              checkFieldAvailability(editedJobDetails.Pf_PolicyNo)
                ? alreadyExistErrMsg['Pf_PolicyNo']
                : true,
              checkFieldAvailability(editedJobDetails.Pf_PolicyNo)
                ? validateWithRulesAndReturnMessages(
                    editedJobDetails.Pf_PolicyNo,
                    'pf',
                    labelList[305].Field_Alias
                  )
                : true,
              labelList[305].Mandatory_Field == 'Yes'
                ? required(
                    labelList[305].Field_Alias,
                    editedJobDetails.Pf_PolicyNo
                  )
                : true,
            ]"
            :disabled="!isActive"
            variant="solo"
            ref="pfNumber"
            @update:model-value="onChangeFields('Pf_PolicyNo')"
            @change="validateFieldAlreadyExist('Pf_PolicyNo', 'PF Number')"
          >
            <template v-slot:label>
              {{ labelList[305].Field_Alias }}
              <span v-if="labelList[305].Mandatory_Field == 'Yes'">*</span>
            </template>
          </v-text-field>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <v-text-field
            v-model="editedJobDetails.Job_Code"
            label="Job Code"
            variant="solo"
            :rules="[
              checkFieldAvailability(editedJobDetails.Job_Code)
                ? validateWithRulesAndReturnMessages(
                    editedJobDetails.Job_Code,
                    'jobCode',
                    'Job Code'
                  )
                : true,
            ]"
            :disabled="!isActive"
            ref="jobCode"
            @update:model-value="onChangeFields"
          ></v-text-field>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <section
            class="text-body-2"
            :class="
              allowProbationDateToEdit &&
              editedJobDetails.Date_Of_Join &&
              editedJobDetails.Designation_Id
                ? ''
                : 'cursor-not-allow'
            "
          >
            <v-menu
              v-model="probationMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="Probation Date"
                  v-model="formattedProbationDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[required('Probation Date', formattedProbationDate)]"
                  readonly
                  :disabled="
                    !editedJobDetails.Date_Of_Join ||
                    !editedJobDetails.Designation_Id ||
                    !allowProbationDateToEdit ||
                    !isActive
                  "
                  v-bind="props"
                  variant="solo"
                >
                  <template v-slot:label>
                    Probation Date
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </template>
              <v-date-picker
                v-model="editedJobDetails.Probation_Date"
                :min="probationMin"
                @update:modelValue="onChangeFields()"
              ></v-date-picker>
            </v-menu>
            <span
              v-if="probationErrorMessage && probationErrorMessage.length"
              class="text-caption"
              style="color: #b00020"
            >
              {{ probationErrorMessage }}
            </span>
          </section>
        </v-col>
        <v-col
          v-if="labelList['151'] && labelList['151'].Field_Visiblity === 'Yes'"
          cols="12"
          md="4"
          sm="6"
        >
          <CustomSelect
            ref="selectedOrganizationGroup"
            v-model="selectedOrganizationGroup"
            :items="orgGroupList"
            :isLoading="orgGroupListLoading"
            item-title="organizationGroupFullName"
            item-value="organizationGroupId"
            :label="labelList['151'].Field_Alias"
            :isAutoComplete="true"
            :isRequired="
              labelList['151'].Mandatory_Field === 'Yes' ? true : false
            "
            :rules="
              labelList['151'].Mandatory_Field === 'Yes'
                ? [
                    required(
                      labelList['151'].Field_Alias,
                      selectedOrganizationGroup
                    ),
                  ]
                : [true]
            "
            variant="solo"
            :itemSelected="editedJobDetails.Organization_Group_Id"
            @selected-item="
              onChangeCustomSelectField($event, 'Organization_Group_Id')
            "
          />
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">Confirmed</p>
          <v-switch
            color="primary"
            v-model="editedJobDetails.Confirmed"
            :true-value="1"
            :false-value="0"
            @update:model-value="onChangeFields"
            :disabled="!isActive"
          ></v-switch>
        </v-col>
        <v-col v-if="editedJobDetails.Confirmed" cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Confirmation Date
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  class="ml-1 mt-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="width: 350px !important">
                The confirmation date is automatically updated overnight via an
                automated process, which is determined by the probation end
                date. It's essential to review and, if necessary, adjust the
                probation end date to ensure accuracy when confirming an
                employee.
              </div>
            </v-tooltip>
          </p>
          <p class="text-subtitle-1 font-weight-regular pt-3">
            {{ formatDate(confirmationDate) }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Commission Based Employee
          </p>
          <v-switch
            color="primary"
            v-model="editedJobDetails.Commission_Employee"
            :true-value="1"
            :false-value="0"
            @update:model-value="onChangeFields"
            :disabled="!fieldsEditable"
          ></v-switch>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <div class="d-flex">
            <p class="text-subtitle-1 text-grey-darken-1">
              Attendance Enforced Payment
            </p>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  class="ml-1 mt-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="width: 350px !important">
                Enabling this flag generates the payslip for this employee only
                if this employee added attendance, leave or the compoff details
                for all the working days.
              </div>
            </v-tooltip>
          </div>
          <v-switch
            color="primary"
            v-model="editedJobDetails.Attendance_Enforced_Payment"
            :true-value="1"
            :false-value="0"
            @update:model-value="onChangeFields"
            :disabled="!fieldsEditable"
          ></v-switch>
        </v-col>
        <v-col cols="12" md="4" sm="6" v-if="labelList[307].Field_Visiblity">
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ labelList[307].Field_Alias }}
            <span v-if="labelList[307].Mandatory_Field == 'Yes'">*</span>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  class="ml-1 mt-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="width: 350px !important">
                Activating a TDS (Tax Deducted at Source) exemption will suspend
                the calculation and withholding of tax for an employee until
                such exemption is revoked. It's important to note that the
                responsibility for deducting tax at the time of payment and
                subsequently remitting it to the Tax Authority rests with the
                employer.
              </div>
            </v-tooltip>
          </p>
          <v-tooltip
            text="Since the payslip has already been generated, updates to this field are unavailable. If any changes are required, please delete the Salary Payslip and Full & Final Settlement."
          >
            <template v-slot:activator="{ props }">
              <v-card
                width="max-content"
                variant="plain"
                v-bind="fieldsEditable ? '' : props"
                :class="fieldsEditable ? '' : 'cursor-not-allowed'"
              >
                <v-switch
                  color="primary"
                  v-model="editedJobDetails.TDS_Exemption"
                  :true-value="1"
                  :false-value="0"
                  @update:model-value="onChangeFields"
                  :disabled="!fieldsEditable"
                ></v-switch>
              </v-card>
            </template>
          </v-tooltip>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <div class="d-flex">
            <p class="text-subtitle-1 text-grey-darken-1">Employee Status</p>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  class="ml-1 mt-1"
                  v-bind="props"
                  size="small"
                  color="info"
                  v-if="actionType !== 'add'"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="width: 350px !important">
                Employee(s) can be made inactive only through
                applying/initiating the resignation in the exit management form
              </div>
            </v-tooltip>
          </div>
          <p class="text-subtitle-1 font-weight-regular mt-2">
            {{ editedJobDetails.Emp_Status }}
          </p>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <p class="custom-label mt-n3 mb-n2">Previous Experience</p>
          <section class="d-flex text-body-2 mt-2">
            <v-text-field
              v-model="prevExpInYear"
              variant="solo"
              type="number"
              :rules="[
                checkFieldAvailability(prevExpInYear)
                  ? validateWithRulesAndReturnMessages(
                      prevExpInYear,
                      'previousExpYear',
                      'Previous Year',
                      true
                    )
                  : true,
              ]"
              ref="previousYear"
              @update:model-value="onChangeFields"
              :disabled="!isActive"
            ></v-text-field>
            <span class="px-1 mt-5 text-grey">Years</span>
            <v-text-field
              v-model="prevExpInMonth"
              variant="solo"
              type="number"
              :rules="[
                checkFieldAvailability(prevExpInMonth)
                  ? validateWithRulesAndReturnMessages(
                      prevExpInMonth,
                      'previousExpMonth',
                      'Previous Month',
                      true
                    )
                  : true,
              ]"
              ref="previousMonth"
              @update:model-value="onChangeFields"
              :disabled="!isActive"
            ></v-text-field>
            <span class="px-1 mt-4 text-grey">Months</span>
          </section>
        </v-col>
        <v-col
          v-if="labelList[490]?.Field_Visiblity?.toLowerCase() === 'yes'"
          cols="12"
          md="6"
        >
          <v-textarea
            v-model="editedJobDetails.Custom_Field_1"
            variant="solo"
            auto-grow
            rows="1"
            :rules="[
              labelList[490].Mandatory_Field?.toLowerCase() === 'yes'
                ? required(
                    labelList[490].Field_Alias,
                    editedJobDetails.Custom_Field_1
                  )
                : true,
              minMaxStringValidation(
                labelList[490].Field_Alias,
                editedJobDetails.Custom_Field_1,
                labelList[490].Min_Validation,
                labelList[490].Max_Validation
              ),
            ]"
            @update:model-value="onChangeFields"
            :disabled="!isActive"
          >
            <template v-slot:label>
              {{ labelList[490]?.Field_Alias }}
              <span
                v-if="labelList[490].Mandatory_Field?.toLowerCase() === 'yes'"
                style="color: red"
                >*</span
              >
            </template>
          </v-textarea>
        </v-col>
      </v-row>
    </v-form>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateEditForm()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          size="small"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
  <AppWarningModal
    v-if="showRoleChangeAlert"
    :open-modal="showRoleChangeAlert"
    confirmation-heading="Assigning roles determines application access and may grant elevated privileges. Please review the access before making the role update"
    icon-name=""
    acceptButtonText="Review"
    closeButtonText="Close"
    @close-warning-modal="showRoleChangeAlert = false"
    @accept-modal="openRoleAccessModal()"
  ></AppWarningModal>
</template>
<script>
import { defineAsyncComponent } from "vue";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  ADD_UPDATE_JOB_DETAILS,
  VALIDATE_DATE_OF_JOIN_AND_PROBATION_DATE_TO_CHANGE,
  VALIDATE_FIELD_AVAILABILITY,
  RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION,
  VALIDATE_BENEFIT_APPLICABLE_BASED_ON_EMP_TYPE,
} from "@/graphql/employee-profile/profileQueries.js";
import { RETRIEVE_ORGANISATION_GROUP_LIST } from "@/graphql/corehr/organisationGroupQueries";
import {
  LIST_EMP_PROFESSION,
  LIST_BUSINESS_UNIT,
} from "@/graphql/dropDownQueries";
import { GET_EFFECTIVE_DATES } from "@/graphql/corehr/employeeDataQueries.js";
import { checkNullValue, getCustomFieldName } from "@/helper";
const RoleAccessRights = defineAsyncComponent(() =>
  import("../../../../settings/coreHr/roles/RoleAccessRights.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import { LIST_JOB_ROLES } from "@/graphql/onboarding/individualQueries.js";

export default {
  name: "EditJobDetails",
  mixins: [validationRules],
  emits: ["close-edit-form", "edit-updated"],
  props: {
    jobDetails: {
      type: Object,
      required: true,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmployeeDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    selectedEmpDoj: {
      type: String,
      default: "",
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
  },
  components: {
    CustomSelect,
    RoleAccessRights,
  },
  data() {
    return {
      editedJobDetails: {},
      backupJobDetails: {},
      isFormDirty: false,
      isMounted: false,
      prevExpInYear: null,
      prevExpInMonth: null,
      allowDOJToEdit: false,
      dojErrorMessage: "",
      allowProbationDateToEdit: false,
      probationErrorMessage: "",
      alreadyExistErrMsg: {
        Emp_Email: true,
        Pf_PolicyNo: true,
      },
      benefitsApplicable: false,
      openWarningModal: false,
      //Date-picker
      dojMenu: false,
      formattedDateOfJoin: "",
      probationMenu: false,
      formattedProbationDate: "",
      businessUnitEffectiveDateMenu: false,
      formattedBusinessUnitEffectiveDate: "",
      managerEffectiveDateMenu: false,
      formattedManagerEffectiveDate: "",
      empTypeEffectiveDateMenu: false,
      formattedEmpTypeEffectiveDate: "",
      workScheduleEffectiveDateMenu: false,
      formattedWorkScheduleEffectiveDate: "",
      locationEffectiveDateMenu: false,
      formattedLocationEffectiveDate: "",
      departmentEffectiveDateMenu: false,
      formattedDepartmentEffectiveDate: "",
      designationEffectiveDateMenu: false,
      formattedDesignationEffectiveDate: "",
      // list
      managers: [],
      locations: [],
      workSchedules: [],
      employeeTypes: [],
      designations: [],
      departments: [],
      serviceProviders: [],
      empProfessions: [],
      businessUnits: [],
      roles: [],
      dropdownListFetching: false,
      empProfessionListFetching: false,
      businessUnitListFetching: false,
      designationListLoading: false,
      // roles
      showRoleChangeAlert: false,
      showRolesAssociation: false,
      // edit
      openBottomSheet: true,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      // effective dates
      effectiveDates: [],
      searchString: "",
      orgGroupList: [],
      orgGroupListLoading: false,
      selectedOrganizationGroup: null,
      effectiveDateProps: [
        {
          disable: false,
          min: "",
          max: "",
          value: "",
        },
        {
          disable: false,
          min: "",
          max: "",
          value: "",
        },
        {
          disable: false,
          min: "",
          max: "",
          value: "",
        },
        {
          disable: false,
          min: "",
          max: "",
          value: "",
        },
        {
          disable: false,
          min: "",
          max: "",
          value: "",
        },
        {
          disable: false,
          min: "",
          max: "",
          value: "",
        },
      ],
      probationMinDate: "",
      fieldsEditable: false,
      jobRolesListLoading: false,
      jobRolesList: [],
      selectedJobRoles: [],
    };
  },

  computed: {
    higherHierachyRole() {
      // First check if the current logged-in user's role exists in the roles list
      const currentUserRole = this.selectedEmployeeDetails.Roles_Id;
      const currentUserRoleExists = this.roles.some(
        (role) => role.Roles_Id === currentUserRole
      );

      // If the current user's role doesn't exist in the list, they can't edit roles
      if (currentUserRole && !currentUserRoleExists) {
        return false;
      }

      return this.roles.some(
        (el) =>
          el.Roles_Id == this.selectedEmployeeDetails.Roles_Id ||
          this.selectedEmployeeDetails.Roles_Id === null ||
          this.isSuperAdmin
      );
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    // returns baseurl of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    noDataText() {
      if (this.designationListLoading) {
        return "Loading...";
      } else if (
        !this.designationListLoading &&
        this.designations.length == 0 &&
        this.searchString.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    selectedEmpDobDate() {
      if (
        this.selectedEmployeeDob &&
        this.selectedEmployeeDob !== "0000-00-00"
      ) {
        return moment(this.selectedEmployeeDob).format("YYYY-MM-DD");
      } else return null;
    },
    probationMin() {
      if (this.probationMinDate && this.probationMinDate !== "0000-00-00") {
        return this.probationMinDate;
      } else if (
        this.editedJobDetails.Date_Of_Join &&
        this.editedJobDetails.Date_Of_Join !== "0000-00-00"
      ) {
        return moment(this.editedJobDetails.Date_Of_Join).format("YYYY-MM-DD");
      } else return null;
    },
    previousExperience() {
      let yearsToMonth = this.prevExpInYear * 12;
      yearsToMonth = parseInt(yearsToMonth);
      let months = this.prevExpInMonth ? this.prevExpInMonth : 0;
      months = parseInt(months);
      return yearsToMonth + months;
    },
    isProbationDateUpdated() {
      let retrievedProbation = this.jobDetails.Probation_Date
        ? moment(this.jobDetails.Probation_Date).format("YYYY-MM-DD")
        : "";
      let editedProbation = this.editedJobDetails.Probation_Date
        ? moment(this.editedJobDetails.Probation_Date).format("YYYY-MM-DD")
        : "";
      if (retrievedProbation !== editedProbation) {
        return true;
      }
      return false;
    },
    isDOJUpdated() {
      let retrievedDOJ = this.jobDetails.Date_Of_Join
        ? moment(this.jobDetails.Date_Of_Join).format("YYYY-MM-DD")
        : "";
      let editedDOJ = this.editedJobDetails.Date_Of_Join
        ? moment(this.editedJobDetails.Date_Of_Join).format("YYYY-MM-DD")
        : "";
      if (retrievedDOJ !== editedDOJ) {
        return true;
      }
      return false;
    },
    isJobDetailsUpdated() {
      if (
        this.jobDetails.Designation_Id !==
          this.editedJobDetails.Designation_Id ||
        this.jobDetails.Department_Id !== this.editedJobDetails.Department_Id ||
        this.jobDetails.Location_Id !== this.editedJobDetails.Location_Id ||
        this.jobDetails.Work_Schedule !== this.editedJobDetails.Work_Schedule ||
        this.jobDetails.EmpType_Id !== this.editedJobDetails.EmpType_Id
      ) {
        return true;
      }
      return false;
    },
    confirmationDate() {
      let editedProbationDate = this.editedJobDetails.Probation_Date
        ? moment(this.editedJobDetails.Probation_Date).format("YYYY-MM-DD")
        : "";
      let retrivedProbationDate = this.jobDetails.Probation_Date
        ? moment(this.jobDetails.Probation_Date).format("YYYY-MM-DD")
        : "";
      if (
        (editedProbationDate !== retrivedProbationDate ||
          this.editedJobDetails.Confirmation_Date == "0000-00-00" ||
          !this.editedJobDetails.Confirmation_Date) &&
        this.editedJobDetails.Probation_Date
      ) {
        return moment(this.editedJobDetails.Probation_Date)
          .add(1, "day")
          .format("YYYY-MM-DD");
      } else {
        return this.editedJobDetails.Confirmation_Date;
      }
    },
    selectedRole() {
      let filteredRoles = this.roles.filter(
        (el) => el.Roles_Id === this.editedJobDetails.Roles_Id
      );
      return filteredRoles[0];
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return true;
      } else return false;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    autoUpdateEffectiveDateForJobDetails() {
      return this.$store.state.orgDetails.autoUpdateEffectiveDateForJobDetails;
    },
    isActive() {
      if (this.selectedEmpStatus.toLowerCase() === "active") {
        return true;
      } else {
        return false;
      }
    },
    isTeamSummaryPage() {
      return this.$route.path.includes("team-summary");
    },
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    "editedJobDetails.Date_Of_Join": function (val) {
      if (val) {
        this.dojMenu = false;
        this.formattedDateOfJoin = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedJobDetails.Work_Schedule_Effective_Date": function (val) {
      if (val) {
        this.workScheduleEffectiveDateMenu = false;
        this.formattedWorkScheduleEffectiveDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedJobDetails.Location_Id_Effective_Date": function (val) {
      if (val) {
        this.locationEffectiveDateMenu = false;
        this.formattedLocationEffectiveDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedJobDetails.Department_Id_Effective_Date": function (val) {
      if (val) {
        this.departmentEffectiveDateMenu = false;
        this.formattedDepartmentEffectiveDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedJobDetails.Designation_Id_Effective_Date": function (val) {
      if (val) {
        this.designationEffectiveDateMenu = false;
        this.formattedDesignationEffectiveDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedJobDetails.Manager_Id_Effective_Date": function (val) {
      if (val) {
        this.managerEffectiveDateMenu = false;
        this.formattedManagerEffectiveDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedJobDetails.EmpType_Id_Effective_Date": function (val) {
      if (val) {
        this.empTypeEffectiveDateMenu = false;
        this.formattedEmpTypeEffectiveDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedJobDetails.Business_Unit_Id_Effective_Date": function (val) {
      if (val) {
        this.businessUnitEffectiveDateMenu = false;
        this.formattedBusinessUnitEffectiveDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedJobDetails.Probation_Date": function (val) {
      if (val) {
        this.probationMenu = false;
        this.formattedProbationDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedJobDetails.Designation_Id": {
      handler(val) {
        if (val) {
          this.fetchJobRoles(val);
        }
      },
      immediate: true,
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.editedJobDetails = JSON.parse(JSON.stringify(this.jobDetails));
    this.fieldsEditable = this.editedJobDetails.salaryNotGenerated;
    if (this.editedJobDetails && this.editedJobDetails.Date_Of_Join) {
      this.formattedDateOfJoin = this.formatDate(
        this.editedJobDetails?.Date_Of_Join
      );
      this.editedJobDetails.Date_Of_Join = this.editedJobDetails.Date_Of_Join
        ? new Date(this.editedJobDetails.Date_Of_Join)
        : null;
    }
    if (this.editedJobDetails.Work_Schedule_Effective_Date) {
      this.formattedWorkScheduleEffectiveDate = this.formatDate(
        this.editedJobDetails?.Work_Schedule_Effective_Date
      );
      this.editedJobDetails.Work_Schedule_Effective_Date = this.editedJobDetails
        .Work_Schedule_Effective_Date
        ? new Date(this.editedJobDetails.Work_Schedule_Effective_Date)
        : null;
    }
    if (this.editedJobDetails.Location_Id_Effective_Date) {
      this.formattedLocationEffectiveDate = this.formatDate(
        this.editedJobDetails?.Location_Id_Effective_Date
      );
      this.editedJobDetails.Location_Id_Effective_Date = this.editedJobDetails
        .Location_Id_Effective_Date
        ? new Date(this.editedJobDetails.Location_Id_Effective_Date)
        : null;
    }
    if (this.editedJobDetails.EmpType_Id_Effective_Date) {
      this.formattedEmpTypeEffectiveDate = this.formatDate(
        this.editedJobDetails?.EmpType_Id_Effective_Date
      );
      this.editedJobDetails.EmpType_Id_Effective_Date = this.editedJobDetails
        .EmpType_Id_Effective_Date
        ? new Date(this.editedJobDetails.EmpType_Id_Effective_Date)
        : null;
    }
    this.selectedOrganizationGroup =
      this.editedJobDetails?.Organization_Group_Id;
    if (this.editedJobDetails.Department_Id_Effective_Date) {
      this.formattedDepartmentEffectiveDate = this.formatDate(
        this.editedJobDetails?.Department_Id_Effective_Date
      );
      this.editedJobDetails.Department_Id_Effective_Date = this.editedJobDetails
        .Department_Id_Effective_Date
        ? new Date(this.editedJobDetails.Department_Id_Effective_Date)
        : null;
    }
    if (this.editedJobDetails.Designation_Id_Effective_Date) {
      this.formattedDesignationEffectiveDate = this.formatDate(
        this.editedJobDetails?.Designation_Id_Effective_Date
      );
      this.editedJobDetails.Designation_Id_Effective_Date = this
        .editedJobDetails.Designation_Id_Effective_Date
        ? new Date(this.editedJobDetails.Designation_Id_Effective_Date)
        : null;
    }
    if (this.editedJobDetails.Manager_Id_Effective_Date) {
      this.formattedManagerEffectiveDate = this.formatDate(
        this.editedJobDetails?.Manager_Id_Effective_Date
      );
      this.editedJobDetails.Manager_Id_Effective_Date = this.editedJobDetails
        .Manager_Id_Effective_Date
        ? new Date(this.editedJobDetails.Manager_Id_Effective_Date)
        : null;
    }
    if (this.editedJobDetails.Business_Unit_Id_Effective_Date) {
      this.formattedBusinessUnitEffectiveDate = this.formatDate(
        this.editedJobDetails?.Business_Unit_Id_Effective_Date
      );
      this.editedJobDetails.Business_Unit_Id_Effective_Date = this
        .editedJobDetails.Business_Unit_Id_Effective_Date
        ? new Date(this.editedJobDetails.Business_Unit_Id_Effective_Date)
        : null;
    }
    if (this.editedJobDetails.Probation_Date) {
      this.formattedProbationDate = this.formatDate(
        this.editedJobDetails?.Probation_Date
      );
      this.editedJobDetails.Probation_Date = this.editedJobDetails
        .Probation_Date
        ? new Date(this.editedJobDetails.Probation_Date)
        : null;
    }
    this.prevExpInYear = parseInt(
      this.editedJobDetails["Previous_Employee_Experience"] / 12
    );
    this.prevExpInMonth = parseInt(
      this.editedJobDetails["Previous_Employee_Experience"] % 12
    );
    for (let i = 0; i < 7; i++) {
      this.effectiveDateProps.push({
        disable: true,
        min: null,
        max: null,
        value: null,
      });
    }
    this.retrieveDropdownDetails();
    this.retrieveEmpProfessions();
    this.retrieveBusinessUnit();
    this.fetchOrganizationGroups();
    if (this.actionType === "add") {
      this.onChangeFields();
      this.allowDOJToEdit = true;
      this.allowProbationDateToEdit = true;
    } else {
      this.getEffectiveDates();
      this.validateDateOfJoinToEdit();
      this.validateBenefitApplicable();
      const {
        Date_Of_Join,
        Designation_Id_Effective_Date,
        Department_Id_Effective_Date,
        Location_Id_Effective_Date,
        Work_Schedule_Effective_Date,
        Manager_Id_Effective_Date,
        EmpType_Id_Effective_Date,
        Business_Unit_Id_Effective_Date,
      } = this.editedJobDetails;
      if (Date_Of_Join) {
        if (
          !Designation_Id_Effective_Date ||
          Designation_Id_Effective_Date == "0000-00-00"
        ) {
          this.editedJobDetails.Designation_Id_Effective_Date = Date_Of_Join;
        }
        if (
          !Department_Id_Effective_Date ||
          Department_Id_Effective_Date == "0000-00-00"
        ) {
          this.editedJobDetails.Department_Id_Effective_Date = Date_Of_Join;
        }
        if (
          !Location_Id_Effective_Date ||
          Location_Id_Effective_Date == "0000-00-00"
        ) {
          this.editedJobDetails.Location_Id_Effective_Date = Date_Of_Join;
        }
        if (
          !Work_Schedule_Effective_Date ||
          Work_Schedule_Effective_Date == "0000-00-00"
        ) {
          this.editedJobDetails.Work_Schedule_Effective_Date = Date_Of_Join;
        }
        if (
          !Manager_Id_Effective_Date ||
          Manager_Id_Effective_Date == "0000-00-00"
        ) {
          this.editedJobDetails.Manager_Id_Effective_Date = Date_Of_Join;
        }
        if (
          !EmpType_Id_Effective_Date ||
          EmpType_Id_Effective_Date == "0000-00-00"
        ) {
          this.editedJobDetails.EmpType_Id_Effective_Date = Date_Of_Join;
        }
        if (
          !Business_Unit_Id_Effective_Date ||
          Business_Unit_Id_Effective_Date == "0000-00-00"
        ) {
          this.editedJobDetails.Business_Unit_Id_Effective_Date = Date_Of_Join;
        }
      }
    }
    if (this.editedJobDetails.Designation_Id) {
      this.designations = [
        {
          Designation_Id: this.selectedEmployeeDetails.Designation_Id,
          Designation_Name: this.selectedEmployeeDetails.Designation_Name,
        },
      ];
    }
    this.backupJobDetails = JSON.parse(JSON.stringify(this.editedJobDetails));
    this.isMounted = true;
  },

  methods: {
    checkNullValue,
    getCustomFieldName,
    getMonthOfDate(date) {
      return moment(date).month();
    },
    getYearOfDate(date) {
      return moment(date).year();
    },
    onChangeDefaultEffectiveDate(index, effectiveDateFiled) {
      this.isFormDirty = true;
      this.editedJobDetails[effectiveDateFiled] =
        this.effectiveDateProps[index].defaultDate;
    },
    checkFieldAvailability(value) {
      if (value) {
        let strValue = value.toString();
        return strValue.trim().length > 0;
      } else return false;
    },
    onChangeFields(
      dateErrMsgVariable = "",
      field = "",
      effectiveDateField = ""
    ) {
      if (field === "Date_Of_Join") {
        if (
          moment(this.editedJobDetails.Date_Of_Join).format("YYYY-MM-DD") !==
          this.jobDetails.Date_Of_Join
        ) {
          this.retrieveProbationDate();
          for (
            let eIndex = 0;
            eIndex < this.effectiveDateProps.length;
            eIndex++
          ) {
            this.effectiveDateProps[eIndex].disable = true;
          }
          let doj = this.editedJobDetails.Date_Of_Join;
          this.editedJobDetails.Designation_Id_Effective_Date = doj;
          this.editedJobDetails.Department_Id_Effective_Date = doj;
          this.editedJobDetails.Location_Id_Effective_Date = doj;
          this.editedJobDetails.Work_Schedule_Effective_Date = doj;
          this.editedJobDetails.EmpType_Id_Effective_Date = doj;
          this.editedJobDetails.Manager_Id_Effective_Date = doj;
          this.editedJobDetails.Business_Unit_Id_Effective_Date = doj;
        } else {
          this.editedJobDetails.Designation_Id_Effective_Date = new Date(
            this.jobDetails.Designation_Id_Effective_Date
          );
          this.editedJobDetails.Department_Id_Effective_Date = new Date(
            this.jobDetails.Department_Id_Effective_Date
          );
          this.editedJobDetails.Location_Id_Effective_Date = new Date(
            this.jobDetails.Location_Id_Effective_Date
          );
          this.editedJobDetails.Work_Schedule_Effective_Date = new Date(
            this.jobDetails.Work_Schedule_Effective_Date
          );
          this.editedJobDetails.Manager_Id_Effective_Date = new Date(
            this.jobDetails.Manager_Id_Effective_Date
          );
          this.editedJobDetails.EmpType_Id_Effective_Date = new Date(
            this.jobDetails.EmpType_Id_Effective_Date
          );
          this.editedJobDetails.Business_Unit_Id_Effective_Date = new Date(
            this.jobDetails.Business_Unit_Id_Effective_Date
          );

          this.editedJobDetails.Designation_Id = this.jobDetails.Designation_Id;
          this.editedJobDetails.Department_Id = this.jobDetails.Department_Id;
          this.editedJobDetails.Location_Id = this.jobDetails.Location_Id;
          this.editedJobDetails.Work_Schedule = this.jobDetails.Work_Schedule;
          this.editedJobDetails.Manager_Id = this.jobDetails.Manager_Id;
          this.editedJobDetails.EmpType_Id = this.jobDetails.EmpType_Id;
          this.editedJobDetails.Business_Unit_Id =
            this.jobDetails.Business_Unit_Id;
        }
      }
      if (dateErrMsgVariable) this[dateErrMsgVariable] = "";
      if (
        effectiveDateField &&
        this.autoUpdateEffectiveDateForJobDetails === "Yes"
      ) {
        this.editedJobDetails.Designation_Id_Effective_Date =
          this.editedJobDetails[effectiveDateField];
        this.editedJobDetails.Department_Id_Effective_Date =
          this.editedJobDetails[effectiveDateField];
        this.editedJobDetails.Location_Id_Effective_Date =
          this.editedJobDetails[effectiveDateField];
      }
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(
      value,
      field,
      effectiveDateIndex = null,
      effectiveDateField = "",
      isFormDirty = true
    ) {
      this.editedJobDetails[field] = value;
      if (isFormDirty) {
        this.onChangeFields();
      } else {
        this.isFormDirty = false;
      }
      if (field === "Designation_Id") {
        this.retrieveProbationDate();
      }
      if (field === "EmpType_Id") {
        this.validateBenefitApplicable();
      }
      if (field === "Roles_Id") {
        this.showRoleChangeAlert = true;
      }
      if (effectiveDateIndex !== null && effectiveDateField) {
        if (!this.jobDetails.Date_Of_Join && this.actionType === "edit") {
          if (this.editedJobDetails.Date_Of_Join) {
            this.effectiveDateProps[effectiveDateIndex].disable = true;
            this.editedJobDetails[effectiveDateField] =
              this.editedJobDetails.Date_Of_Join;
          }
        } else if (this.editedJobDetails[field] !== this.jobDetails[field]) {
          if (
            moment(this.editedJobDetails.Date_Of_Join).format("YYYY-MM-DD") ===
            this.jobDetails.Date_Of_Join
          ) {
            this.effectiveDateProps[effectiveDateIndex].disable = false;
            this.editedJobDetails[effectiveDateField] = null;
            if (!this.editedJobDetails.Designation_Id_Effective_Date) {
              this.formattedDesignationEffectiveDate = "";
            }
            if (!this.editedJobDetails.Manager_Id_Effective_Date) {
              this.formattedManagerEffectiveDate = "";
            }
            if (!this.editedJobDetails.EmpType_Id_Effective_Date) {
              this.formattedEmpTypeEffectiveDate = "";
            }
            if (!this.editedJobDetails.Work_Schedule_Effective_Date) {
              this.formattedWorkScheduleEffectiveDate = "";
            }
            if (!this.editedJobDetails.Location_Id_Effective_Date) {
              this.formattedLocationEffectiveDate = "";
            }
            if (!this.editedJobDetails.Department_Id_Effective_Date) {
              this.formattedDepartmentEffectiveDate = "";
            }
            if (!this.editedJobDetails.Business_Unit_Id_Effective_Date) {
              this.formattedBusinessUnitEffectiveDate = "";
            }
          } else {
            this.effectiveDateProps[effectiveDateIndex].disable = true;
            this.editedJobDetails[effectiveDateField] = new Date(
              this.editedJobDetails.Date_Of_Join
            );
          }
        } else {
          this.editedJobDetails[effectiveDateField] = new Date(
            this.jobDetails[effectiveDateField]
          );
          this.effectiveDateProps[effectiveDateIndex].disable = true;
        }
      }
    },

    openRoleAccessModal() {
      this.showRoleChangeAlert = false;
      this.showRolesAssociation = true;
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    closeEditForm() {
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.$emit("close-edit-form");
      }
    },

    async validateEditForm() {
      let isFormValid = await this.$refs.editJobDetailsForm.validate();
      mixpanel.track("EmpProfile-job-edit-submit-clicked");
      if (isFormValid && isFormValid.valid) {
        this.editedJobDetails.Date_Of_Join = moment(
          this.editedJobDetails.Date_Of_Join
        ).isValid()
          ? moment(this.editedJobDetails.Date_Of_Join).format("YYYY-MM-DD")
          : null;
        this.editedJobDetails.Probation_Date = moment(
          this.editedJobDetails.Probation_Date
        ).isValid()
          ? moment(this.editedJobDetails.Probation_Date).format("YYYY-MM-DD")
          : null;
        this.editedJobDetails.Designation_Id_Effective_Date = moment(
          this.editedJobDetails.Designation_Id_Effective_Date
        ).isValid()
          ? moment(this.editedJobDetails.Designation_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.editedJobDetails.Department_Id_Effective_Date = moment(
          this.editedJobDetails.Department_Id_Effective_Date
        ).isValid()
          ? moment(this.editedJobDetails.Department_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.editedJobDetails.Location_Id_Effective_Date = moment(
          this.editedJobDetails.Location_Id_Effective_Date
        ).isValid()
          ? moment(this.editedJobDetails.Location_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.editedJobDetails.Work_Schedule_Effective_Date = moment(
          this.editedJobDetails.Work_Schedule_Effective_Date
        ).isValid()
          ? moment(this.editedJobDetails.Work_Schedule_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.editedJobDetails.Manager_Id_Effective_Date = moment(
          this.editedJobDetails.Manager_Id_Effective_Date
        ).isValid()
          ? moment(this.editedJobDetails.Manager_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.editedJobDetails.EmpType_Id_Effective_Date = moment(
          this.editedJobDetails.EmpType_Id_Effective_Date
        ).isValid()
          ? moment(this.editedJobDetails.EmpType_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.editedJobDetails.Business_Unit_Id_Effective_Date = moment(
          this.editedJobDetails.Business_Unit_Id_Effective_Date
        ).isValid()
          ? moment(
              this.editedJobDetails.Business_Unit_Id_Effective_Date
            ).format("YYYY-MM-DD")
          : null;
        this.editedJobDetails.Confirmation_Date = moment(
          this.editedJobDetails.Confirmation_Date
        ).isValid()
          ? moment(this.editedJobDetails.Confirmation_Date).format("YYYY-MM-DD")
          : null;

        this.backupJobDetails.Date_Of_Join = moment(
          this.backupJobDetails.Date_Of_Join
        ).isValid()
          ? moment(this.backupJobDetails.Date_Of_Join).format("YYYY-MM-DD")
          : null;
        this.backupJobDetails.Probation_Date = moment(
          this.backupJobDetails.Probation_Date
        ).isValid()
          ? moment(this.backupJobDetails.Probation_Date).format("YYYY-MM-DD")
          : null;
        this.backupJobDetails.Designation_Id_Effective_Date = moment(
          this.backupJobDetails.Designation_Id_Effective_Date
        ).isValid()
          ? moment(this.backupJobDetails.Designation_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.backupJobDetails.Department_Id_Effective_Date = moment(
          this.backupJobDetails.Department_Id_Effective_Date
        ).isValid()
          ? moment(this.backupJobDetails.Department_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.backupJobDetails.Location_Id_Effective_Date = moment(
          this.backupJobDetails.Location_Id_Effective_Date
        ).isValid()
          ? moment(this.backupJobDetails.Location_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.backupJobDetails.Work_Schedule_Effective_Date = moment(
          this.backupJobDetails.Work_Schedule_Effective_Date
        ).isValid()
          ? moment(this.backupJobDetails.Work_Schedule_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.backupJobDetails.Manager_Id_Effective_Date = moment(
          this.backupJobDetails.Manager_Id_Effective_Date
        ).isValid()
          ? moment(this.backupJobDetails.Manager_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.backupJobDetails.EmpType_Id_Effective_Date = moment(
          this.backupJobDetails.EmpType_Id_Effective_Date
        ).isValid()
          ? moment(this.backupJobDetails.EmpType_Id_Effective_Date).format(
              "YYYY-MM-DD"
            )
          : null;
        this.backupJobDetails.Business_Unit_Id_Effective_Date = moment(
          this.backupJobDetails.Business_Unit_Id_Effective_Date
        ).isValid()
          ? moment(
              this.backupJobDetails.Business_Unit_Id_Effective_Date
            ).format("YYYY-MM-DD")
          : null;
        this.backupJobDetails.Confirmation_Date = moment(
          this.backupJobDetails.Confirmation_Date
        ).isValid()
          ? moment(this.backupJobDetails.Confirmation_Date).format("YYYY-MM-DD")
          : null;
        if (
          JSON.stringify(this.editedJobDetails) ===
          JSON.stringify(this.backupJobDetails)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.updateJobDetails();
        }
      } else {
        // Check the validity of each field
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "roleAccess",
                "designations",
                "department",
                "location",
                "workSchedule",
                "employeeType",
                "serviceProvider",
                "employeeProfession",
                "manager",
                "businessUnit",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 2, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },

    updateJobDetails() {
      let vm = this;
      vm.isLoading = true;
      let dojInFormat = "";
      if (
        (vm.editedJobDetails.Date_Of_Join &&
          moment(vm.editedJobDetails.Date_Of_Join).isValid() &&
          !vm.editedJobDetails.User_Defined_EmpId) ||
        vm.actionType === "add"
      ) {
        dojInFormat = moment(vm.editedJobDetails.Date_Of_Join).format(
          "YYYY-MM-DD"
        );
      }
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_JOB_DETAILS,
          variables: {
            Employee_Id: vm.selectedEmpId,
            Designation_Id: vm.editedJobDetails.Designation_Id,
            Designation_Id_Effective_Date: dojInFormat
              ? dojInFormat
              : moment(
                  vm.editedJobDetails.Designation_Id_Effective_Date
                ).isValid()
              ? moment(
                  vm.editedJobDetails.Designation_Id_Effective_Date
                ).format("YYYY-MM-DD")
              : null,
            Department_Id: vm.editedJobDetails.Department_Id,
            Department_Id_Effective_Date: dojInFormat
              ? dojInFormat
              : moment(
                  vm.editedJobDetails.Department_Id_Effective_Date
                ).isValid()
              ? moment(vm.editedJobDetails.Department_Id_Effective_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            Location_Id: vm.editedJobDetails.Location_Id,
            Location_Id_Effective_Date: dojInFormat
              ? dojInFormat
              : moment(vm.editedJobDetails.Location_Id_Effective_Date).isValid()
              ? moment(vm.editedJobDetails.Location_Id_Effective_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            Job_Code: vm.editedJobDetails.Job_Code,
            Emp_Email: vm.editedJobDetails.Emp_Email,
            Manager_Id: vm.editedJobDetails.Manager_Id,
            Manager_Id_Effective_Date: dojInFormat
              ? dojInFormat
              : moment(vm.editedJobDetails.Manager_Id_Effective_Date).isValid()
              ? moment(vm.editedJobDetails.Manager_Id_Effective_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            Emp_Profession: vm.editedJobDetails.Emp_Profession,
            Confirmed: vm.editedJobDetails.Confirmed,
            Confirmation_Date:
              vm.editedJobDetails.Confirmed &&
              moment(vm.confirmationDate).isValid()
                ? moment(vm.confirmationDate).format("YYYY-MM-DD")
                : null,
            Probation_Date: moment(vm.editedJobDetails.Probation_Date).isValid()
              ? moment(vm.editedJobDetails.Probation_Date).format("YYYY-MM-DD")
              : null,
            EmpType_Id: vm.editedJobDetails.EmpType_Id,
            EmpType_Id_Effective_Date: dojInFormat
              ? dojInFormat
              : moment(vm.editedJobDetails.EmpType_Id_Effective_Date).isValid()
              ? moment(vm.editedJobDetails.EmpType_Id_Effective_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            Commission_Employee: vm.editedJobDetails.Commission_Employee,
            Work_Schedule: vm.editedJobDetails.Work_Schedule,
            Work_Schedule_Effective_Date: dojInFormat
              ? dojInFormat
              : moment(
                  vm.editedJobDetails.Work_Schedule_Effective_Date
                ).isValid()
              ? moment(vm.editedJobDetails.Work_Schedule_Effective_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            TDS_Exemption: vm.editedJobDetails.TDS_Exemption,
            Organization_Group_Id: vm.editedJobDetails.Organization_Group_Id,
            Attendance_Enforced_Payment:
              vm.editedJobDetails.Attendance_Enforced_Payment,
            Previous_Employee_Experience: vm.previousExperience,
            Service_Provider_Id: vm.editedJobDetails.Service_Provider_Id
              ? vm.editedJobDetails.Service_Provider_Id
              : 0,
            Date_Of_Join: moment(vm.editedJobDetails.Date_Of_Join).isValid()
              ? moment(vm.editedJobDetails.Date_Of_Join).format("YYYY-MM-DD")
              : null,
            isUpdate: vm.actionType === "add" ? 0 : 1,
            Business_Unit_Id: vm.editedJobDetails.Business_Unit_Id,
            Business_Unit_Id_Effective_Date: dojInFormat
              ? dojInFormat
              : moment(
                  vm.editedJobDetails.Business_Unit_Id_Effective_Date
                ).isValid()
              ? moment(
                  vm.editedJobDetails.Business_Unit_Id_Effective_Date
                ).format("YYYY-MM-DD")
              : null,
            Pf_PolicyNo: vm.editedJobDetails.Pf_PolicyNo
              ? vm.editedJobDetails.Pf_PolicyNo
              : "",
            Roles_Id: vm.editedJobDetails.Roles_Id
              ? vm.editedJobDetails.Roles_Id
              : null,
            isProbationDateUpdated: vm.isProbationDateUpdated,
            isJobDetailsUpdated: vm.isJobDetailsUpdated,
            isDOJUpdated: vm.isDOJUpdated,
            Job_Role_Ids: vm.selectedJobRoles,
            Custom_Field_1: vm.editedJobDetails.Custom_Field_1,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          mixpanel.track("EmpProfile-job-edit-success");
          if (res && res.data && res.data.updateJobDetails) {
            const { message } = res.data.updateJobDetails;
            vm.isLoading = false;
            let snackbarData = {
              isOpen: true,
              message:
                vm.actionType === "add" && !vm.selectedEmpDoj
                  ? "Job details added successfully"
                  : message?.includes("approval")
                  ? "Job details updation is submitted for approval."
                  : "Job details updated successfully",
              type: "success",
            };
            vm.showAlert(snackbarData);
            vm.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
            vm.$emit("edit-updated");
          } else {
            vm.handleUpdateError();
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-job-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action:
            this.actionType === "add" && !this.selectedEmpDoj
              ? "adding"
              : "updating",
          form: "job details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveDropdownDetails() {
      this.dropdownListFetching = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const {
              departments,
              locations,
              employeeType,
              workSchedules,
              managers,
              serviceProvider,
              roles,
            } = res.data.getDropDownBoxDetails;
            this.departments = departments;
            this.locations = locations;
            this.employeeTypes = employeeType;
            this.workSchedules = workSchedules;
            this.managers =
              managers && managers.length > 0
                ? managers.filter((el) => {
                    return el.Manager_Id != this.selectedEmpId;
                  })
                : [];
            this.managers = this.managers.map((item) => ({
              ...item,
              managerIdName:
                item.Manager_Name + " - " + item.Manager_User_Defined_EmpId,
            }));
            this.serviceProviders = serviceProvider;
            this.roles = roles;
            if (this.jobDetails.Manager_Id) {
              // Remove Manager_Id_Effective_Date if the manager not available in the list
              let isManagerAvailable = this.managers.some(
                (el) => el.Manager_Id === this.jobDetails.Manager_Id
              );
              if (!isManagerAvailable) {
                this.onChangeCustomSelectField(
                  null,
                  "Manager_Id",
                  4,
                  "Manager_Id_Effective_Date"
                );
              }
            }
          }
          this.dropdownListFetching = false;
        })
        .catch(() => {
          this.dropdownListFetching = false;
        });
    },

    retrieveEmpProfessions() {
      let vm = this;
      vm.empProfessionListFetching = true;
      vm.$apollo
        .query({
          query: LIST_EMP_PROFESSION,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listEmpProfession &&
            !response.data.listEmpProfession.errorCode
          ) {
            const { professions } = response.data.listEmpProfession;
            vm.empProfessions =
              professions && professions.length > 0 ? professions : [];
            if (
              vm.empProfessions.length > 0 &&
              !vm.editedJobDetails.Emp_Profession
            ) {
              vm.editedJobDetails.Emp_Profession = 1;
            }
          }
          vm.empProfessionListFetching = false;
        })
        .catch(() => {
          vm.empProfessionListFetching = false;
        });
    },

    retrieveBusinessUnit() {
      let vm = this;
      vm.businessUnitListFetching = true;
      vm.$apollo
        .query({
          query: LIST_BUSINESS_UNIT,
          client: "apolloClientI",
          variables: {
            action: "active",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listBusinessUnitInDropdown &&
            !response.data.listBusinessUnitInDropdown.errorCode
          ) {
            const { settings } = response.data.listBusinessUnitInDropdown;
            vm.businessUnits = settings && settings.length > 0 ? settings : [];
          }
          vm.businessUnitListFetching = false;
        })
        .catch(() => {
          vm.businessUnitListFetching = false;
        });
    },

    getEffectiveDates() {
      let vm = this;
      if (vm.editedJobDetails.User_Defined_EmpId) {
        vm.$apollo
          .query({
            query: GET_EFFECTIVE_DATES,
            client: "apolloClientI",
            variables: {
              employeeIds: [vm.editedJobDetails.User_Defined_EmpId],
            },
            fetchPolicy: "no-cache",
          })
          .then(async (response) => {
            if (response && response.data && response.data.getEffectiveDate) {
              const { getEffectiveDate } = response.data.getEffectiveDate;
              vm.effectiveDates = getEffectiveDate ? getEffectiveDate : [];
              if (vm.effectiveDates && vm.effectiveDates.length) {
                let effectiveDateKeys = [
                  "Designation_Id_Effective_Date",
                  "Department_Id_Effective_Date",
                  "Location_Id_Effective_Date",
                  "Work_Schedule_Effective_Date",
                  "Manager_Id_Effective_Date",
                  "EmpType_Id_Effective_Date",
                  "Business_Unit_Id_Effective_Date",
                ];
                const { Effective_Date } = vm.effectiveDates[0];
                let currentDate = moment().format("YYYY-MM-DD");
                vm.probationMinDate = Effective_Date;
                for (let i = 0; i < effectiveDateKeys.length; i++) {
                  if (
                    !vm.jobDetails[effectiveDateKeys[i]] ||
                    vm.jobDetails[effectiveDateKeys[i]] === "0000-00-00"
                  ) {
                    vm.jobDetails[effectiveDateKeys[i]] = moment(
                      vm.jobDetails.Date_Of_Join
                    ).subtract(1, "days");
                  }
                  if (Effective_Date) {
                    let effectiveDate = moment(
                      vm.jobDetails[effectiveDateKeys[i]]
                    ).add(1, "days");
                    let minDate = moment
                      .max(moment(Effective_Date), effectiveDate)
                      .format("YYYY-MM-DD");
                    let maxDate = currentDate;

                    //Check if min date and max date are correct else keep the min date as max date
                    if (!moment(maxDate).isAfter(minDate)) {
                      maxDate = minDate;
                    }

                    vm.effectiveDateProps[i] = {
                      min: minDate,
                      max: maxDate,
                      value: minDate,
                      disable: true,
                    };
                  } else {
                    let effectiveDate = moment(
                      vm.jobDetails[effectiveDateKeys[i]]
                    ).add(1, "days");
                    let dateOfJoin = moment(vm.jobDetails.Date_Of_Join);
                    let minDate = moment
                      .max(dateOfJoin, effectiveDate)
                      .format("YYYY-MM-DD");
                    let maxDate = currentDate;

                    //Check if min date and max date are correct else keep the min date as max date
                    if (!moment(maxDate).isAfter(minDate)) {
                      maxDate = minDate;
                    }

                    vm.effectiveDateProps[i] = {
                      min: minDate,
                      max: maxDate,
                      defaultDate: maxDate ? new Date(maxDate) : null,
                      value: minDate,
                      disable: true,
                    };
                  }
                }
                //Validate incase of getEffective is called later the dropdown
                if (
                  vm.jobDetails.Manager_Id &&
                  vm.managers &&
                  vm.managers.length
                ) {
                  // Remove Manager_Id_Effective_Date if the manager not available in the list
                  let isManagerAvailable = vm.managers.some(
                    (el) => el.Manager_Id === vm.jobDetails.Manager_Id
                  );
                  if (!isManagerAvailable) {
                    vm.onChangeCustomSelectField(
                      null,
                      "Manager_Id",
                      4,
                      "Manager_Id_Effective_Date"
                    );
                  }
                }
              }
            } else {
              vm.handleEffectiveDate();
            }
          })
          .catch((err) => {
            vm.handleEffectiveDate(err);
          });
      }
    },
    handleEffectiveDate(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "effective date",
        isListError: false,
      });
    },
    validateDateOfJoinToEdit() {
      let vm = this;
      if (vm.editedJobDetails.Date_Of_Join) {
        vm.$apollo
          .query({
            query: VALIDATE_DATE_OF_JOIN_AND_PROBATION_DATE_TO_CHANGE,
            client: "apolloClientAC",
            variables: {
              employeeId: parseInt(vm.selectedEmpId),
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateDOJAndProbationDate &&
              !response.data.validateDOJAndProbationDate.errorCode
            ) {
              const {
                dateOfJoinEdit,
                probationDateEdit,
                dateOfJoinErrorMessage,
                probationErrorMessage,
              } = response.data.validateDOJAndProbationDate;
              vm.allowDOJToEdit = dateOfJoinEdit;
              vm.dojErrorMessage = !dateOfJoinEdit
                ? dateOfJoinErrorMessage
                : "";
              vm.allowProbationDateToEdit = probationDateEdit;
              vm.probationErrorMessage = !probationDateEdit
                ? probationErrorMessage
                : "";
            } else {
              this.$store.dispatch("handleApiErrors", {
                error: "",
                action: "validating",
                form: "date of join",
                isListError: false,
              });
            }
          })
          .catch((err) => {
            this.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: "date of join",
              isListError: false,
            });
          });
      } else {
        vm.allowDOJToEdit = true;
        if (!vm.editedJobDetails.Probation_Date) {
          vm.allowProbationDateToEdit = true;
        }
      }
    },
    validateFieldAlreadyExist(field, label) {
      let vm = this;
      if (
        vm.editedJobDetails[field] &&
        vm.editedJobDetails[field] !== vm.jobDetails[field]
      ) {
        vm.$apollo
          .query({
            query: VALIDATE_FIELD_AVAILABILITY,
            client: "apolloClientAC",
            variables: {
              employeeId: vm.editedJobDetails.Employee_Id,
              columnValue: vm.editedJobDetails[field],
              columnName: field,
              tableName: "emp_job",
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateCommonAvailability &&
              !response.data.validateCommonAvailability.errorCode
            ) {
              const { isAvailable } = response.data.validateCommonAvailability;
              if (!isAvailable) {
                vm.alreadyExistErrMsg[field] = label + " already exist";
              } else {
                vm.alreadyExistErrMsg[field] = true;
              }
            }
            vm.$refs.editJobDetailsForm.validate();
          })
          .catch((err) => {
            vm.alreadyExistErrMsg[field] = true;
            vm.$refs.editJobDetailsForm.validate();
            let fieldLabel = label.toLowerCase();
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: fieldLabel,
              isListError: false,
            });
          });
      }
    },
    retrieveProbationDate() {
      let vm = this;
      if (
        vm.editedJobDetails.Designation_Id &&
        vm.editedJobDetails.Date_Of_Join
      ) {
        vm.$apollo
          .query({
            query: RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION,
            client: "apolloClientAC",
            variables: {
              designationId: vm.editedJobDetails.Designation_Id,
              dateOfJoin: moment(vm.editedJobDetails.Date_Of_Join).format(
                "YYYY-MM-DD"
              ),
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveProbationDate &&
              !response.data.retrieveProbationDate.errorCode
            ) {
              const { probationDate } = response.data.retrieveProbationDate;
              vm.editedJobDetails.Probation_Date = new Date(probationDate);
            } else {
              vm.$store.dispatch("handleApiErrors", {
                error: "",
                action: "retrieving",
                form: "probation date",
                isListError: false,
              });
            }
          })
          .catch((err) => {
            this.$store.dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: "probation date",
              isListError: false,
            });
          });
      }
    },
    validateBenefitApplicable() {
      let vm = this;
      if (vm.editedJobDetails.EmpType_Id) {
        vm.$apollo
          .query({
            query: VALIDATE_BENEFIT_APPLICABLE_BASED_ON_EMP_TYPE,
            client: "apolloClientAC",
            variables: {
              employeeTypeId: parseInt(vm.editedJobDetails.EmpType_Id),
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateBenefitsApplicable &&
              !response.data.validateBenefitsApplicable.errorCode
            ) {
              const { benefitsApplicable } =
                response.data.validateBenefitsApplicable;
              vm.benefitsApplicable = benefitsApplicable;
            } else {
              this.$store.dispatch("handleApiErrors", {
                error: "",
                action: "validating",
                form: "benefits applicable",
                isListError: false,
              });
            }
          })
          .catch((err) => {
            this.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: "benefits applicable",
              isListError: false,
            });
          });
      }
    },

    callDesignationList(searchString) {
      this.searchString = searchString;
      if (searchString.length >= 3 && !this.editedJobDetails.Designation_Id) {
        this.getDesignationList(searchString);
      }
    },

    async getDesignationList(searchString) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designations = designationResult;
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.designations = [];
        });
    },
    fetchOrganizationGroups() {
      let vm = this;
      vm.orgGroupListLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_ORGANISATION_GROUP_LIST,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: { formId: vm.isTeamSummaryPage ? 243 : 18 },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listOrganizationGroup &&
            !response.data.listOrganizationGroup.errorCode
          ) {
            const { organizationGroupObject } =
              response.data.listOrganizationGroup;
            if (organizationGroupObject) {
              const orgList = organizationGroupObject.organizationGroupList
                ? organizationGroupObject.organizationGroupList
                : [];
              vm.orgGroupList = orgList.filter(
                (group) => group.status === "Active"
              );
            } else {
              vm.orgGroupList = [];
            }
          } else {
            vm.orgGroupList = [];
          }
          vm.orgGroupListLoading = false;
        })
        .catch((err) => {
          vm.orgGroupListLoading = false;
          vm.handleOrgGroupError(err);
        });
    },
    handleOrgGroupError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "organization unit configuration",
        isListError: false,
      });
    },

    openRolesForm() {
      window.open(this.baseUrl + "v3/settings/core-hr/roles", "_blank");
    },

    openDesignationsForm() {
      window.open(this.baseUrl + "in/core-hr/designations", "_blank");
    },

    openDepartmentsForm() {
      window.open(this.baseUrl + "v3/core-hr/department-hierarchy", "_blank");
    },

    openLocationsForm() {
      window.open(this.baseUrl + "v3/core-hr/locations", "_blank");
    },

    openWorkScheduleForm() {
      window.open(this.baseUrl + "in/core-hr/work-schedule", "_blank");
    },

    openBusinessUnitForm() {
      window.open(this.baseUrl + "v3/core-hr/business-unit", "_blank");
    },

    openEmployeeTypeForm() {
      window.open(this.baseUrl + "v3/core-hr/employee-type", "_blank");
    },
    fetchJobRoles(designationId) {
      let vm = this;
      vm.jobRolesListLoading = true;
      vm.isErrorInSeconLineManager = false;
      vm.$apollo
        .query({
          query: LIST_JOB_ROLES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
          variables: {
            formId: vm.isTeamSummaryPage ? 243 : 18,
            designationId: parseInt(designationId),
          },
        })
        .then(({ data }) => {
          vm.jobRolesListLoading = false;
          if (data && data.listJobRoles) {
            const response = data.listJobRoles;
            if (!response.errorCode) {
              vm.jobRolesList = JSON.parse(response.jobRoles);
              const jobRoleIdsSet = new Set(
                this.jobRolesList.map((job) => job.Job_Role_Id)
              );
              this.selectedJobRoles = (
                this.selectedEmployeeDetails?.Job_Role_Details?.map(
                  (role) => role.Job_Role_Id
                ) || []
              ).filter((id) => jobRoleIdsSet.has(id));
            } else {
              vm.jobRolesList = [];
            }
          } else {
            vm.jobRolesList = [];
          }
        })
        .catch(() => {
          vm.jobRolesListLoading = false;
          vm.jobRolesList = [];
        });
    },
  },
};
</script>
