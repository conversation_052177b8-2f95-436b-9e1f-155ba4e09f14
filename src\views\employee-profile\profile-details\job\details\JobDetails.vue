<template>
  <v-card class="rounded-lg ma-1" elevation="0">
    <div v-if="showEditForm">
      <EditJobDetails
        ref="editJobDetails"
        :jobDetails="jobDetails"
        :selectedEmployeeDob="selectedEmployeeDob"
        :actionType="actionType"
        :selectedEmpId="selectedEmpId"
        :selectedEmployeeDetails="selectedEmployeeDetails"
        :selectedEmpDoj="selectedEmpDoj"
        :selectedEmpStatus="selectedEmpStatus"
        @edit-updated="editUpdated"
        @close-edit-form="closeEditForm"
      >
      </EditJobDetails>
    </div>
    <div v-else>
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="primary"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Job Details</span
          >
        </div>
        <div
          v-if="formAccess && formAccess.update && formAccess.admin === 'admin'"
        >
          <v-btn @click="openEditDialog" color="primary" variant="text">
            <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
          </v-btn>
        </div>
      </div>

      <v-row class="pa-4 ma-2 card-blue-background">
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Role (Access Rights)"
          :new-value="jobDetails.Roles_Name"
          :old-value="oldJobDetailsData?.Roles_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Date of Join"
          :new-value="formatDate(jobDetails.Date_Of_Join)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Date_Of_Join)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="labelList[424]?.Field_Visiblity.toLowerCase() === 'yes'"
          :label="labelList[424]?.Field_Alias || 'Job Roles'"
          :new-value="getJobRoleNames(jobDetails.Job_Role_Details)"
          :old-value="
            oldJobDetailsData
              ? getJobRoleNames(oldJobDetailsData?.Job_Role_Details)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Designation"
          :new-value="jobDetails.Designation_Name"
          :old-value="oldJobDetailsData?.Designation_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Designation Transfer Effective Date"
          :new-value="formatDate(jobDetails.Designation_Id_Effective_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Designation_Id_Effective_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Department"
          :new-value="jobDetails.Department_Name"
          :old-value="oldJobDetailsData?.Department_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Department Transfer Effective Date"
          :new-value="formatDate(jobDetails.Department_Id_Effective_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Department_Id_Effective_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Location"
          :new-value="jobDetails.Location_Name"
          :old-value="oldJobDetailsData?.Location_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Relocation Effective Date"
          :new-value="formatDate(jobDetails.Location_Id_Effective_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Location_Id_Effective_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Work Schedule"
          :new-value="jobDetails.Work_Schedule_Name"
          :old-value="oldJobDetailsData?.Work_Schedule_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Work Schedule Transfer Effective Date"
          :new-value="formatDate(jobDetails.Work_Schedule_Effective_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Work_Schedule_Effective_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Employee Type"
          :new-value="jobDetails.Employee_Type"
          :old-value="oldJobDetailsData?.Employee_Type"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Employee Type Transfer Effective Date"
          :new-value="formatDate(jobDetails.EmpType_Id_Effective_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.EmpType_Id_Effective_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="jobDetails.Field_Force"
          :label="
            getCustomFieldName(115, 'Service Provider') || 'Service Provider'
          "
          :new-value="jobDetails.Service_Provider_Name"
          :old-value="oldJobDetailsData?.Service_Provider_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="
            labelList[467]?.Field_Visiblity?.toLowerCase() === 'yes' ||
            entomoIntegrationEnabled
          "
          :label="labelList[467]?.Field_Alias || 'Work Email'"
          :new-value="jobDetails.Emp_Email"
          :old-value="oldJobDetailsData?.Emp_Email"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="labelList[447]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[447]?.Field_Alias || 'Employee Profession'"
          :new-value="jobDetails.Profession_Name"
          :old-value="oldJobDetailsData?.Profession_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Manager"
          :new-value="jobDetails.Manager_Name"
          :old-value="oldJobDetailsData?.Manager_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Manager Transfer Effective Date"
          :new-value="formatDate(jobDetails.Manager_Id_Effective_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Manager_Id_Effective_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="labelList[385]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[385]?.Field_Alias || 'Business Unit'"
          :new-value="jobDetails.Business_Unit"
          :old-value="oldJobDetailsData?.Business_Unit"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="labelList[385]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[385]?.Field_Alias + ' Transfer Effective Date'"
          :new-value="formatDate(jobDetails.Business_Unit_Id_Effective_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Business_Unit_Id_Effective_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="labelList[305]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[305]?.Field_Alias || 'PF Number'"
          :new-value="jobDetails.Pf_PolicyNo"
          :old-value="oldJobDetailsData?.Pf_PolicyNo"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Job Code"
          :new-value="jobDetails.Job_Code"
          :old-value="oldJobDetailsData?.Job_Code"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Probation Date"
          :new-value="formatDate(jobDetails.Probation_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Probation_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="labelList[151]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[151]?.Field_Alias || 'Organization Group'"
          :new-value="jobDetails.Organization_Group"
          :old-value="oldJobDetailsData?.Organization_Group"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Confirmed"
          :new-value="parseInt(jobDetails.Confirmed) ? 'Yes' : 'No'"
          :old-value="
            oldJobDetailsData
              ? parseInt(oldJobDetailsData.Confirmed)
                ? 'Yes'
                : 'No'
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="
            parseInt(jobDetails.Confirmed) ||
            (oldJobDetailsData && parseInt(oldJobDetailsData.Confirmed))
          "
          label="Confirmation Date"
          :new-value="formatDate(jobDetails.Confirmation_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Confirmation_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Commission Based Employee"
          :new-value="parseInt(jobDetails.Commission_Employee) ? 'Yes' : 'No'"
          :old-value="
            oldJobDetailsData
              ? parseInt(oldJobDetailsData.Commission_Employee)
                ? 'Yes'
                : 'No'
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Attendance Enforced Payment"
          :new-value="
            parseInt(jobDetails.Attendance_Enforced_Payment) ? 'Yes' : 'No'
          "
          :old-value="
            oldJobDetailsData
              ? parseInt(oldJobDetailsData.Attendance_Enforced_Payment)
                ? 'Yes'
                : 'No'
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="labelList[307]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[307]?.Field_Alias || 'TDS Exemption'"
          :new-value="parseInt(jobDetails.TDS_Exemption) ? 'Yes' : 'No'"
          :old-value="
            oldJobDetailsData
              ? parseInt(oldJobDetailsData.TDS_Exemption)
                ? 'Yes'
                : 'No'
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Employee Status"
          :new-value="jobDetails.Emp_Status"
          :old-value="oldJobDetailsData?.Emp_Status"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          label="Previous Experience"
          :new-value="
            convertMonthToYearMonthsDays(
              jobDetails.Previous_Employee_Experience
            )
          "
          :old-value="
            oldJobDetailsData
              ? convertMonthToYearMonthsDays(
                  oldJobDetailsData.Previous_Employee_Experience
                )
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="jobDetails.Emp_Status?.toLowerCase() === 'inactive'"
          label="Exit Date"
          :new-value="formatDate(jobDetails.Emp_InActive_Date)"
          :old-value="
            oldJobDetailsData
              ? formatDate(oldJobDetailsData.Emp_InActive_Date)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="jobDetails.Emp_Status?.toLowerCase() === 'inactive'"
          label="Relieving Reason"
          :new-value="jobDetails.Relieving_Reason"
          :old-value="oldJobDetailsData?.Relieving_Reason"
        />
        <FieldDiff
          :oldDataAvailable="oldJobDetailsData ? true : false"
          v-if="labelList[490]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[490]?.Field_Alias || 'Custom Field 1'"
          :new-value="jobDetails.Custom_Field_1"
          :old-value="oldJobDetailsData?.Custom_Field_1"
        />
      </v-row>
    </div>
  </v-card>
</template>

<script>
import { defineAsyncComponent } from "vue";
import {
  checkNullValue,
  convertMonthToYearMonthsDays,
  getCustomFieldName,
} from "@/helper";
import moment from "moment";
const EditJobDetails = defineAsyncComponent(() =>
  import("./EditJobDetails.vue")
);
const FieldDiff = defineAsyncComponent(() =>
  import("@/components/custom-components/FieldDiff.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "JobDetails",
  components: {
    EditJobDetails,
    FieldDiff,
  },
  props: {
    jobDetailsData: {
      type: [Object, Array],
      required: true,
    },
    oldJobDetailsData: {
      type: [Object, Array],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmployeeDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    selectedEmpDoj: {
      type: String,
      default: "",
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
  },
  emits: ["refetch-job-details", "edit-opened", "edit-closed"],
  data() {
    return {
      showEditForm: false,
      jobDetails: {
        Employee_Type: "",
        EmpType_Id_Effective_Date: null,
        Designation_Name: "",
        Designation_Id_Effective_Date: null,
        Department_Name: "",
        Department_Id_Effective_Date: null,
        Location_Name: "",
        Location_Id_Effective_Date: null,
        Work_Schedule_Name: "",
        Work_Schedule_Effective_Date: null,
        Job_Code: "",
        Manager_Name: "",
        Manager_Id: 0,
        Manager_Id_Effective_Date: null,
        Date_Of_Join: null,
        Attendance_Enforced_Payment: 0,
        Probation_Date: null,
        Previous_Employee_Experience: 0,
        Emp_Email: "",
        Commission_Employee: 0,
        Emp_Status: "",
        Profession_Name: "",
        Emp_Profession: "Other Professionals",
        TDS_Exemption: 0,
        Confirmed: 0,
        Confirmation_Date: null,
        Field_Force: 0,
        Pf_PolicyNo: "",
      },
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
  },
  watch: {
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.jobDetailsData && this.jobDetailsData.length > 0) {
      this.jobDetails = this.jobDetailsData[0];
    } else if (this.jobDetailsData) {
      this.jobDetails = this.jobDetailsData;
    }
    if (this.actionType === "add" && !this.selectedEmpDoj) {
      this.showEditForm = true;
    }
  },

  methods: {
    checkNullValue,
    convertMonthToYearMonthsDays,
    getCustomFieldName,
    getJobRoleNames(jobRoleDetails) {
      return (
        jobRoleDetails?.map((role) => role.Job_Role_Name).join(", ") || null
      );
    },
    editUpdated() {
      this.showEditForm = false;
      this.$emit("refetch-job-details");
    },
    openEditDialog() {
      this.showEditForm = true;
      mixpanel.track("EmpProfile-job-edit-opened");
      this.$emit("edit-opened");
    },
    closeEditForm() {
      this.showEditForm = false;
      mixpanel.track("EmpProfile-job-edit-closed");
      this.$emit("edit-closed");
    },
  },
};
</script>
