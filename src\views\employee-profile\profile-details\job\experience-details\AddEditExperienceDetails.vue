<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Experience Details</span
        >
        <v-spacer></v-spacer>
        <v-icon color="grey" size="25" @click="$emit('close-experience-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditExperienceForm">
          <v-row>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model="experienceFormData.Prev_Company_Name"
                variant="solo"
                :rules="[
                  required('Company', experienceFormData.Prev_Company_Name),
                  validateWithRulesAndReturnMessages(
                    experienceFormData.Prev_Company_Name,
                    'companyName',
                    'Company'
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Company<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model="experienceFormData.Designation"
                variant="solo"
                :rules="[
                  required('Designation', experienceFormData.Designation),
                  validateWithRulesAndReturnMessages(
                    experienceFormData.Designation,
                    'designation',
                    'Designation'
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Designation<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model="experienceFormData.Prev_Company_Location"
                variant="solo"
                :rules="[
                  required(
                    'Location',
                    experienceFormData.Prev_Company_Location
                  ),
                  validateWithRulesAndReturnMessages(
                    experienceFormData.Prev_Company_Location,
                    'companyLocation',
                    'Location'
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Location<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-menu
                v-model="fromMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="startDate"
                    v-model="formattedFrom"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('From', formattedFrom)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      From<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="experienceFormData.Start_Date_Join"
                  :min="selectedEmpDobDate"
                  :max="startDateMax"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-menu
                v-model="fromTo"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="endDate"
                    v-model="formattedTo"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('To', formattedTo)]"
                    readonly
                    :disabled="!experienceFormData.Start_Date_Join"
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      To<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="experienceFormData.End_Date"
                  :min="endDateMin"
                  :max="selectedEmpDojDate"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Duration</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{
                  experienceFormData && experienceFormData.Duration
                    ? convertMonthToYearMonthsDays(experienceFormData.Duration)
                    : "-"
                }}
              </p>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-file-input
                prepend-icon=""
                show-size
                :model-value="fileContent"
                append-inner-icon="fas fa-paperclip"
                label="Document"
                variant="solo"
                accept="image/png, image/jpeg, image/jpg, application/pdf"
                @update:modelValue="onChangeFiles"
                @click:clear="removeFiles"
              ></v-file-input>
            </v-col>
          </v-row>
          <div
            v-if="
              labelList[372]?.Field_Visiblity?.toLowerCase() === 'yes' ||
              labelList[373]?.Field_Visiblity?.toLowerCase() === 'yes' ||
              labelList[374]?.Field_Visiblity?.toLowerCase() === 'yes'
            "
            class="text-grey-darken-1 font-weight-bold my-1"
          >
            Reference Detail(s)
            <span class="d-flex justify-end mt-n6">
              <v-btn
                v-if="experienceFormData.References?.length"
                color="primary"
                class="ml-2"
                variant="text"
                @click="
                  experienceFormData.References.push({
                    Reference_Name: null,
                    Reference_Email: null,
                    Reference_Number: null,
                  })
                "
              >
                <v-icon class="mr-1" size="15">fas fa-plus</v-icon> Add
                New</v-btn
              >
            </span>
          </div>
          <v-row
            v-for="(reference, i) in experienceFormData.References"
            :key="`reference-${i}`"
            dense
          >
            <!-- Reference Name -->
            <v-col
              v-if="labelList[372]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                :ref="`referenceName-${i}`"
                clearable
                v-model="reference.Reference_Name"
                :rules="[
                  labelList[372]?.Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(
                        labelList[372]?.Field_Alias,
                        reference.Reference_Name
                      )
                    : true,
                  validateWithRulesAndReturnMessages(
                    reference.Reference_Name,
                    'skillName',
                    labelList[372]?.Field_Alias
                  ),
                ]"
                @update:model-value="onChangeFields()"
                variant="solo"
              >
                <template v-slot:label>
                  <span>{{ labelList[372]?.Field_Alias }}</span>
                  <span
                    v-if="
                      labelList[372]?.Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>

            <!-- Reference Email -->
            <v-col
              v-if="labelList[373]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <v-text-field
                :ref="`referenceEmail-${i}`"
                clearable
                v-model="reference.Reference_Email"
                :rules="[
                  labelList[373]?.Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(
                        labelList[373]?.Field_Alias,
                        reference.Reference_Email
                      )
                    : true,
                  validateWithRulesAndReturnMessages(
                    reference.Reference_Email,
                    'empEmail',
                    labelList[373]?.Field_Alias
                  ),
                ]"
                @update:model-value="onChangeFields()"
                variant="solo"
              >
                <template v-slot:label>
                  <span>{{ labelList[373]?.Field_Alias }}</span>
                  <span
                    v-if="
                      labelList[373]?.Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>

            <!-- Reference Number -->
            <v-col
              v-if="labelList[374]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="4"
              sm="6"
            >
              <div class="d-flex align-center">
                <!-- Text Field -->
                <v-text-field
                  :ref="`referenceNumber-${i}`"
                  clearable
                  v-model="reference.Reference_Number"
                  :rules="[
                    labelList[374]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[374]?.Field_Alias,
                          reference.Reference_Number
                        )
                      : true,
                    reference.Reference_Number
                      ? minLengthValidation(
                          labelList[374]?.Field_Alias,
                          reference.Reference_Number,
                          6
                        )
                      : true,
                  ]"
                  :counter="15"
                  :maxlength="15"
                  class="flex-grow-1"
                  @update:model-value="onChangeFields()"
                  variant="solo"
                >
                  <template v-slot:label>
                    <span>{{ labelList[374]?.Field_Alias }}</span>
                    <span
                      v-if="
                        labelList[374]?.Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>

                <!-- Icons -->
                <div class="ml-2">
                  <!-- Delete Icon -->
                  <v-icon
                    v-if="experienceFormData.References.length > 1"
                    size="15"
                    class="fas fa-trash ml-1"
                    color="primary"
                    @click="experienceFormData.References.splice(i, 1)"
                  />
                </div>
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="$emit('close-experience-form')"
                  class="ma-2 pa-2"
                  color="primary"
                  variant="text"
                  rounded="lg"
                  elevation="2"
                >
                  Cancel
                </v-btn>
                <v-btn
                  :disabled="!isFormDirty"
                  class="ma-2 pa-1"
                  rounded="lg"
                  color="primary"
                  @click="validateExperienceDetails()"
                >
                  Save
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { ADD_UPDATE_EXPERIENCE_DETAILS } from "@/graphql/employee-profile/profileQueries.js";
import {
  convertMonthToYearMonthsDays,
  getDaysDifference,
  getMonthDifference,
  getYearDifference,
} from "@/helper";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AddEditExperienceDetails",
  mixins: [validationRules],
  props: {
    experienceDetails: {
      type: Object,
      required: false,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmployeeDob: {
      type: String,
      default: "",
    },
    selectedEmpDoj: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
  },
  emits: ["refetch-experience-details", "close-experience-form"],
  data() {
    return {
      experienceFormData: {
        Prev_Company_Name: "",
        Designation: "",
        Prev_Company_Location: "",
        Start_Date_Join: null,
        End_Date: null,
        Duration: "",
        File_Name: null,
        File_Size: null,
        References: [
          {
            Reference_Name: null,
            Reference_Email: null,
            Reference_Number: null,
          },
        ],
      },
      backupExperienceFormData: {},
      //Date-picker
      formattedFrom: "",
      fromMenu: false,
      fromTo: false,
      formattedTo: "",
      isFormDirty: false,
      // edit
      isFileChanged: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      // file
      fileContent: null,
    };
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    selectedEmpDobDate() {
      if (
        this.selectedEmployeeDob &&
        this.selectedEmployeeDob !== "0000-00-00"
      ) {
        return moment(this.selectedEmployeeDob).format("YYYY-MM-DD");
      } else return null;
    },
    selectedEmpDojDate() {
      if (this.selectedEmpDoj && this.selectedEmpDoj !== "0000-00-00") {
        return moment(this.selectedEmpDoj).format("YYYY-MM-DD");
      } else return null;
    },
    startDateMax() {
      if (
        this.experienceFormData.End_Date &&
        this.experienceFormData.End_Date !== "0000-00-00"
      ) {
        return moment(this.experienceFormData.End_Date).format("YYYY-MM-DD");
      }
      return this.selectedEmpDojDate;
    },
    endDateMin() {
      if (
        this.experienceFormData.Start_Date_Join &&
        this.experienceFormData.Start_Date_Join !== "0000-00-00"
      ) {
        return moment(this.experienceFormData.Start_Date_Join).format(
          "YYYY-MM-DD"
        );
      }
      return this.selectedEmpDobDate;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    domainName() {
      return this.$store.getters.domain;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },
  watch: {
    "experienceFormData.Start_Date_Join": function (val) {
      // Do something with the new value and/or old value here
      this.calculateDuration();
      if (val) {
        this.fromMenu = false;
        this.formattedFrom = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "experienceFormData.End_Date": function (val) {
      // Do something with the new value and/or old value here
      this.calculateDuration();
      if (val) {
        this.toMenu = false;
        this.formattedTo = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    // Set the form data when component is created
    if (
      this.experienceDetails &&
      Object.keys(this.experienceDetails).length > 0
    ) {
      this.experienceFormData = JSON.parse(
        JSON.stringify(this.experienceDetails)
      );
      if (this.experienceFormData.Start_Date_Join) {
        this.formattedFrom = this.formatDate(
          this.experienceFormData?.Start_Date_Join
        );
        this.experienceFormData.Start_Date_Join = this.experienceFormData
          .Start_Date_Join
          ? new Date(this.experienceFormData.Start_Date_Join)
          : null;
      }
      if (this.experienceFormData.End_Date) {
        this.formattedTo = this.formatDate(this.experienceFormData?.End_Date);
        this.experienceFormData.End_Date = this.experienceFormData.End_Date
          ? new Date(this.experienceFormData.End_Date)
          : null;
      }
      if (
        this.experienceFormData["File_Name"] &&
        this.experienceFormData["File_Size"]
      ) {
        this.fileContent = {
          name: this.formattedFileName(this.experienceFormData["File_Name"]),
          size: this.experienceFormData["File_Size"],
        };
      } else {
        this.fileContent = null;
      }
      // Initialize References if not present
      if (this.experienceFormData["Experience_Reference"]?.length) {
        this.experienceFormData["References"] = this.experienceFormData[
          "Experience_Reference"
        ].map((ref) => ({
          Reference_Name: ref.Reference_Name || null,
          Reference_Email: ref.Reference_Email || null,
          Reference_Number: ref.Reference_Number || null,
        }));
      } else {
        this.experienceFormData["References"] = [
          {
            Reference_Name: null,
            Reference_Email: null,
            Reference_Number: null,
          },
        ];
      }
      this.backupExperienceFormData = JSON.parse(
        JSON.stringify(this.experienceFormData)
      );
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
  },
  methods: {
    convertMonthToYearMonthsDays,
    calculateDuration() {
      let dayDifference = getDaysDifference(
        this.experienceFormData.Start_Date_Join,
        this.experienceFormData.End_Date
      );
      this.experienceFormData["Duration"] = (dayDifference / 30).toFixed(2);
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File";
      }
      return "";
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onChangeFiles(value) {
      this.fileContent = value;
      if (this.fileContent && this.fileContent.name) {
        mixpanel.track("EmpProfile-exp-file-changed");
        this.experienceFormData["File_Name"] =
          this.selectedEmpId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent.name;
        this.experienceFormData["File_Size"] = this.fileContent.size.toString();
        this.isFileChanged = true;
      }
      this.onChangeFields();
    },
    removeFiles() {
      mixpanel.track("EmpProfile-exp-file-removed");
      this.experienceFormData["File_Name"] = "";
      this.experienceFormData["File_Size"] = "";
      this.fileContent = null;
      this.onChangeFields();
    },
    async validateExperienceDetails() {
      const { valid } = await this.$refs.addEditExperienceForm.validate();
      mixpanel.track("EmpProfile-exp-edit-submit-click");
      if (valid) {
        this.experienceFormData.Start_Date_Join = moment(
          this.experienceFormData.Start_Date_Join
        ).isValid()
          ? moment(this.experienceFormData.Start_Date_Join).format("YYYY-MM-DD")
          : null;
        this.experienceFormData.End_Date = moment(
          this.experienceFormData.End_Date
        ).isValid()
          ? moment(this.experienceFormData.End_Date).format("YYYY-MM-DD")
          : null;
        this.backupExperienceFormData.Start_Date_Join = moment(
          this.backupExperienceFormData.Start_Date_Join
        ).isValid()
          ? moment(this.backupExperienceFormData.Start_Date_Join).format(
              "YYYY-MM-DD"
            )
          : null;
        this.backupExperienceFormData.End_Date = moment(
          this.backupExperienceFormData.End_Date
        ).isValid()
          ? moment(this.backupExperienceFormData.End_Date).format("YYYY-MM-DD")
          : null;
        if (
          JSON.stringify(this.experienceFormData) ===
          JSON.stringify(this.backupExperienceFormData)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.validateDocuments();
        }
      }
    },

    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        if (this.fileContent && this.fileContent.size && this.isFileChanged) {
          await this.uploadFileContents(this.fileContent);
        }
        this.updateExperienceDetails();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },
    updateExperienceDetails() {
      let vm = this;
      vm.isLoading = true;
      let yearDifference = getYearDifference(
        this.experienceFormData.Start_Date_Join,
        this.experienceFormData.End_Date
      );
      let monthDifference = getMonthDifference(
        this.experienceFormData.Start_Date_Join,
        this.experienceFormData.End_Date
      );
      let referDetails = [];
      if (this.labelList[372]?.Field_Visiblity?.toLowerCase() !== "yes") {
        referDetails = null;
      } else referDetails = vm.experienceFormData.References;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_EXPERIENCE_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            experienceId: vm.experienceFormData.Experience_Id,
            companyName: vm.experienceFormData.Prev_Company_Name,
            designation: vm.experienceFormData.Designation,
            startDate: moment(vm.experienceFormData.Start_Date_Join).isValid()
              ? moment(vm.experienceFormData.Start_Date_Join).format(
                  "YYYY-MM-DD"
                )
              : null,
            endDate: moment(vm.experienceFormData.End_Date).isValid()
              ? moment(vm.experienceFormData.End_Date).format("YYYY-MM-DD")
              : null,
            companyLocation: vm.experienceFormData.Prev_Company_Location,
            duration: vm.experienceFormData.Duration,
            years: yearDifference,
            referenceDetails: referDetails,
            months: monthDifference,
            fileName: vm.experienceFormData["File_Name"],
            fileSize: vm.experienceFormData["File_Size"],
            formId: vm.callingFrom === "profile" ? 18 : 243,
            formStatus: vm.actionType === "add" ? 0 : 1,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          const { message } = res.data.addUpdateExperienceDetails;
          mixpanel.track("EmpProfile-exp-edit-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: message?.includes("approval")
              ? "Experience details is submitted for approval."
              : vm.formType === "edit"
              ? "Experience details updated successfully"
              : "Experience details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-experience-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-exp-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "experience details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    async uploadFileContents() {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employee Experience/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.experienceFormData["File_Name"],
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .catch((error) => {
          throw error;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
