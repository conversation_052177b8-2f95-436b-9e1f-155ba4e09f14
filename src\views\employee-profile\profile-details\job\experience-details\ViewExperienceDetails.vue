<template>
  <div
    v-if="
      experienceDetails && experienceDetails.length === 0 && !isApprovalView
    "
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No experience details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(expItem, index) in mergedExperience"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `width:450px; border-left: 7px solid ${generateRandomColor()}; height:auto; `
        : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip
                  :text="
                    expItem.newExp?.Prev_Company_Name ||
                    expItem.oldExp?.Prev_Company_Name
                  "
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      class="text-truncate"
                      v-bind="
                        expItem.newExp?.Prev_Company_Name ||
                        expItem.oldExp?.Prev_Company_Name
                          ? props
                          : ''
                      "
                    >
                      <!-- Strike-through old value when changed -->
                      <span
                        v-if="
                          expItem.oldExp &&
                          expItem.newExp &&
                          expItem.oldExp.Prev_Company_Name?.toLowerCase() !==
                            expItem.newExp.Prev_Company_Name?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1 text-primary font-weight-bold text-h6"
                      >
                        {{ checkNullValue(expItem.oldExp.Prev_Company_Name) }}
                      </span>
                      <!-- New value with success styling -->
                      <span
                        v-if="expItem.newExp"
                        :class="[
                          'text-primary font-weight-bold text-h6',
                          (expItem.oldExp &&
                            expItem.oldExp.Prev_Company_Name?.toLowerCase() !==
                              expItem.newExp?.Prev_Company_Name?.toLowerCase()) ||
                          (!expItem.oldExp && oldExperienceDetailsData)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{ checkNullValue(expItem.newExp?.Prev_Company_Name) }}
                      </span>
                      <!-- Strike-through only old value when deleted -->
                      <span
                        v-else-if="expItem.oldExp"
                        class="text-error text-decoration-line-through text-primary font-weight-bold text-h6"
                      >
                        {{ checkNullValue(expItem.oldExp.Prev_Company_Name) }}
                      </span>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>

      <div class="card-columns w-100 mt-n6">
        <span
          :style="!isMobileView ? 'width:67%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Designation </b>
              <v-tooltip
                :text="
                  expItem.newExp?.Designation || expItem.oldExp?.Designation
                "
                location="bottom"
                max-width="400"
              >
                <template v-slot:activator="{ props }">
                  <span class="pb-1 pt-1" v-bind="props">
                    <div
                      :style="
                        isMobileView ? 'max-width: 200px' : 'max-width:140px'
                      "
                      class="text-truncate"
                    >
                      <!-- Strike-through old value when changed -->
                      <span
                        v-if="
                          expItem.oldExp &&
                          expItem.newExp &&
                          expItem.oldExp.Designation?.toLowerCase() !==
                            expItem.newExp.Designation?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{ checkNullValue(expItem.oldExp.Designation) }}
                      </span>
                      <!-- New value with success styling -->
                      <span
                        v-if="expItem.newExp"
                        :class="[
                          (expItem.oldExp &&
                            expItem.oldExp.Designation?.toLowerCase() !==
                              expItem.newExp?.Designation?.toLowerCase()) ||
                          (!expItem.oldExp && oldExperienceDetailsData)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{ checkNullValue(expItem.newExp?.Designation) }}
                      </span>
                      <!-- Strike-through only old value when deleted -->
                      <span
                        v-else-if="expItem.oldExp"
                        class="text-error text-decoration-line-through"
                      >
                        {{ checkNullValue(expItem.oldExp.Designation) }}
                      </span>
                    </div>
                  </span>
                </template>
              </v-tooltip>
            </div>
            <div
              v-if="isMobileView"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start">Location </b>
              <span class="py-2">
                <!-- Strike-through old value when changed -->
                <span
                  v-if="
                    expItem.oldExp &&
                    expItem.newExp &&
                    expItem.oldExp.Prev_Company_Location?.toLowerCase() !==
                      expItem.newExp.Prev_Company_Location?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(expItem.oldExp.Prev_Company_Location) }}
                </span>
                <!-- New value with success styling -->
                <span
                  v-if="expItem.newExp"
                  :class="[
                    (expItem.oldExp &&
                      expItem.oldExp.Prev_Company_Location?.toLowerCase() !==
                        expItem.newExp?.Prev_Company_Location?.toLowerCase()) ||
                    (!expItem.oldExp && oldExperienceDetailsData)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(expItem.newExp?.Prev_Company_Location) }}
                </span>
                <!-- Strike-through only old value when deleted -->
                <span
                  v-else-if="expItem.oldExp"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(expItem.oldExp.Prev_Company_Location) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">From </b>
              <span class="pb-1 pt-1">
                <!-- Strike-through old value when changed -->
                <span
                  v-if="
                    expItem.oldExp &&
                    expItem.newExp &&
                    expItem.oldExp.Start_Date_Join !==
                      expItem.newExp.Start_Date_Join
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ formatDate(expItem.oldExp.Start_Date_Join) }}
                </span>
                <!-- New value with success styling -->
                <span
                  v-if="expItem.newExp"
                  :class="[
                    (expItem.oldExp &&
                      expItem.oldExp.Start_Date_Join !==
                        expItem.newExp?.Start_Date_Join) ||
                    (!expItem.oldExp && oldExperienceDetailsData)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ formatDate(expItem.newExp?.Start_Date_Join) }}
                </span>
                <!-- Strike-through only old value when deleted -->
                <span
                  v-else-if="expItem.oldExp"
                  class="text-error text-decoration-line-through"
                >
                  {{ formatDate(expItem.oldExp.Start_Date_Join) }}
                </span>
              </span>
            </div>
            <div
              v-if="!isMobileView"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start">Duration </b>
              <span class="pb-1 pt-1">
                {{ durationInDays(expItem.newExp) }}
              </span>
            </div>
            <!-- Wrapper for Menu -->
            <div
              v-if="labelList[360]?.Field_Visiblity?.toLowerCase() === 'yes'"
              class="mt-2 d-flex flex-column justify-start text-body-1"
            >
              <!-- Vuetify Menu -->
              <v-menu open-on-hover location="bottom" offset-y>
                <!-- Menu Activator -->
                <template v-slot:activator="{ props }">
                  <div
                    :style="
                      isMobileView ? 'max-width: 200px' : 'max-width:140px'
                    "
                    class="text-truncate"
                    v-bind="props"
                  >
                    <v-btn
                      variant="text"
                      class="text-decoration-underline ml-n3"
                      color="primary"
                    >
                      Reference(s)
                    </v-btn>
                  </div>
                </template>
                <!-- Menu Content -->
                <v-card class="pa-3" max-width="500px">
                  <div
                    v-if="!displayData(expItem).Experience_Reference?.length"
                  >
                    No references available
                  </div>
                  <div v-else>
                    <div
                      v-for="(reference, index) in displayData(expItem)
                        .Experience_Reference"
                      :key="index"
                      class="mb-2"
                    >
                      <!-- Reference Name -->
                      <div v-if="labelList[360]?.Field_Visiblity === 'Yes'">
                        {{ labelList[360]?.Field_Alias }}:
                        {{ checkNullValue(reference.Reference_Name) }}
                      </div>

                      <!-- Reference Email -->
                      <div v-if="labelList[361]?.Field_Visiblity === 'Yes'">
                        {{ labelList[361]?.Field_Alias }}:
                        {{ checkNullValue(reference.Reference_Email) }}
                      </div>

                      <!-- Reference Number -->
                      <div v-if="labelList[362]?.Field_Visiblity === 'Yes'">
                        {{ labelList[362]?.Field_Alias }}:
                        {{ checkNullValue(reference.Reference_Number) }}
                      </div>

                      <!-- Separator for multiple references -->
                      <v-divider
                        v-if="
                          index <
                          displayData(expItem).Experience_Reference?.length - 1
                        "
                        class="my-2"
                      />
                    </div>
                  </div>
                </v-card>
              </v-menu>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div
              v-if="!isMobileView"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start">Location </b>
              <v-tooltip
                :text="
                  expItem.newExp?.Prev_Company_Location ||
                  expItem.oldExp?.Prev_Company_Location
                "
                location="bottom"
                max-width="400"
              >
                <template v-slot:activator="{ props }">
                  <span class="py-2" v-bind="props">
                    <div
                      :style="
                        isMobileView ? 'max-width: 200px' : 'max-width:140px'
                      "
                      class="text-truncate"
                    >
                      <!-- Strike-through old value when changed -->
                      <span
                        v-if="
                          expItem.oldExp &&
                          expItem.newExp &&
                          expItem.oldExp.Prev_Company_Location?.toLowerCase() !==
                            expItem.newExp.Prev_Company_Location?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{
                          checkNullValue(expItem.oldExp.Prev_Company_Location)
                        }}
                      </span>
                      <!-- New value with success styling -->
                      <span
                        v-if="expItem.newExp"
                        :class="[
                          (expItem.oldExp &&
                            expItem.oldExp.Prev_Company_Location?.toLowerCase() !==
                              expItem.newExp?.Prev_Company_Location?.toLowerCase()) ||
                          (!expItem.oldExp && oldExperienceDetailsData)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{
                          checkNullValue(expItem.newExp?.Prev_Company_Location)
                        }}
                      </span>
                      <!-- Strike-through only old value when deleted -->
                      <span
                        v-else-if="expItem.oldExp"
                        class="text-error text-decoration-line-through"
                      >
                        {{
                          checkNullValue(expItem.oldExp.Prev_Company_Location)
                        }}
                      </span>
                    </div>
                  </span>
                </template>
              </v-tooltip>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">To </b>
              <span class="pb-1 pt-1">
                <!-- Strike-through old value when changed -->
                <span
                  v-if="
                    expItem.oldExp &&
                    expItem.newExp &&
                    expItem.oldExp.End_Date !== expItem.newExp.End_Date
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ formatDate(expItem.oldExp.End_Date) }}
                </span>
                <!-- New value with success styling -->
                <span
                  v-if="expItem.newExp"
                  :class="[
                    (expItem.oldExp &&
                      expItem.oldExp.End_Date !== expItem.newExp?.End_Date) ||
                    (!expItem.oldExp && oldExperienceDetailsData)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ formatDate(expItem.newExp?.End_Date) }}
                </span>
                <!-- Strike-through only old value when deleted -->
                <span
                  v-else-if="expItem.oldExp"
                  class="text-error text-decoration-line-through"
                >
                  {{ formatDate(expItem.oldExp.End_Date) }}
                </span>
              </span>
            </div>
            <div
              v-if="isMobileView"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start">Duration </b>
              <span class="pb-1 pt-1">
                {{ durationInDays(expItem.newExp) }}
              </span>
            </div>
            <div
              v-if="expItem.newExp?.File_Name || expItem.oldExp?.File_Name"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <span
                v-if="
                  expItem.oldExp?.File_Name &&
                  expItem.oldExp?.File_Name !== expItem.newExp?.File_Name
                "
                style="cursor: pointer"
                @click="retrieveExpDocuments(expItem.oldExp?.File_Name)"
                class="text-subtitle-1 font-weight-regular text-decoration-line-through text-error mr-1"
              >
                Previous Document
              </span>
              <span
                v-if="expItem.newExp?.File_Name"
                style="text-decoration: underline; cursor: pointer"
                @click="retrieveExpDocuments(expItem.newExp?.File_Name)"
                class="text-green"
              >
                View Document</span
              >
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employee Experience"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
import {
  generateRandomColor,
  checkNullValue,
  convertMonthToYearMonthsDays,
} from "@/helper";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ViewExperienceDetails",
  props: {
    experienceDetails: {
      type: Object,
      required: true,
    },
    oldExperienceDetailsData: {
      type: [Array, Object],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    isApprovalView: {
      type: Boolean,
      default: false,
    },
  },
  components: { ActionMenu, FilePreviewModal },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return {
      openModal: false,
      retrievedFileName: "",
      havingAccess: {},
    };
  },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    employeeEdit() {
      return this.$store.state.orgDetails.employeeEdit;
    },
    enableEdit() {
      return (
        (this.empFormUpdateAccess && this.employeeEdit) ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    durationInDays() {
      return (experienceDetails) => {
        if (experienceDetails?.Start_Date_Join && experienceDetails?.End_Date) {
          let startDate = moment(experienceDetails?.Start_Date_Join);
          let endDate = moment(experienceDetails?.End_Date);
          let totalDays = endDate.diff(startDate, "days");

          // Break down the duration into years, months, and days
          let years = Math.floor(totalDays / 365);
          totalDays %= 365;
          let months = Math.floor(totalDays / 30);
          let days = totalDays % 30;
          let durationString = [];
          if (years > 0) {
            durationString.push(`${years} years`);
          }
          if (months > 0) {
            durationString.push(`${months} months`);
          }
          if (days > 0) {
            durationString.push(`${days} days`);
          }
          return durationString.join(" ");
        } else return "-";
      };
    },
    mergedExperience() {
      const oldExp = this.oldExperienceDetailsData || [];
      const newExp = this.experienceDetails || [];

      const idSet = new Set();
      const merged = [];

      // Add new experience items
      newExp.forEach((newItem) => {
        const id = newItem.Experience_Id;
        idSet.add(id);
        const oldItem = oldExp.find((old) => old.Experience_Id === id);
        merged.push({
          newExp: newItem,
          oldExp: oldItem || null,
        });
      });

      // Add old experience items that don't exist in new data (deleted items)
      oldExp.forEach((oldItem) => {
        const id = oldItem.Experience_Id;
        if (!idSet.has(id)) {
          merged.push({
            newExp: null,
            oldExp: oldItem,
          });
        }
      });

      return merged;
    },
    displayData() {
      return (item) => {
        if (this.oldExperienceDetailsData) {
          return item.newExp || item.oldExp;
        }
        return item.newExp;
      };
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    convertMonthToYearMonthsDays,
    checkAccess() {
      this.havingAccess["update"] =
        (this.empFormUpdateAccess && this.employeeEdit) ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem;
      if (this.oldExperienceDetailsData) {
        const mergedItem = this.mergedExperience[index];
        selectedActionItem = mergedItem.newExp || mergedItem.oldExp;
      } else {
        selectedActionItem = this.experienceDetails[index];
      }
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    retrieveExpDocuments(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },
  },
};
</script>

<style scoped>
.text-success {
  color: green;
}
.text-error {
  color: red;
}
.text-decoration-line-through {
  text-decoration: line-through;
}
</style>
