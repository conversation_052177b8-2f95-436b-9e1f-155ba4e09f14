<template>
  <div>
    <div class="d-flex justify-space-between align-center">
      <v-row>
        <v-col cols="12" class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="red"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="d-flex text-h6 text-grey-darken-1 font-weight-bold"
            >Bank Details</span
          >
        </v-col>
      </v-row>
      <v-dialog
        transition="dialog-bottom-transition"
        v-model="showAddEditBankForm"
        width="70%"
      >
        <template v-slot:activator="{ props }">
          <v-btn
            v-if="canUpdateBankDetails"
            v-bind="props"
            color="primary"
            variant="text"
            @click="openAddForm()"
          >
            <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add</v-btn
          >
        </template>
        <AddEditBankDetails
          :callingFrom="callingFrom"
          :action-type="actionType"
          :selectedBankDetails="selectedBankDetails"
          :selectedEmpId="selectedEmpId"
          @close-bank-form="closeAddEditForm"
          @refetch-other-details="onUpdateAddSuccess"
        />
      </v-dialog>
    </div>
    <div class="d-flex justify center" v-if="!isMobileView">
      <v-slide-group
        class="px-4"
        selected-class="bg-secondary"
        prev-icon="fas fa-chevron-circle-left"
        next-icon="fas fa-chevron-circle-right"
        show-arrows
      >
        <v-slide-group-item>
          <ViewBankDetails
            :bankDetails="bankDetails"
            :oldBankDetails="oldBankDetailsData"
            :formAccess="formAccess"
            :selectedEmpStatus="selectedEmpStatus"
            :callingFrom="callingFrom"
            @on-open-edit="openEditForm"
            @on-delete="showDeleteConfirmationModal"
          />
        </v-slide-group-item>
      </v-slide-group>
    </div>
    <div v-else class="d-flex flex-column mt-6 align-center justify-center">
      <ViewBankDetails
        :bankDetails="bankDetails"
        :oldBankDetails="oldBankDetailsData"
        :formAccess="formAccess"
        :selectedEmpStatus="selectedEmpStatus"
        :callingFrom="callingFrom"
        @on-open-edit="openEditForm"
        @on-delete="showDeleteConfirmationModal"
      />
    </div>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const AddEditBankDetails = defineAsyncComponent(() =>
  import("./AddEditBankDetails.vue")
);
import ViewBankDetails from "./ViewBankDetails.vue";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "OtherDetails",
  components: { ViewBankDetails, AddEditBankDetails },
  props: {
    bankDetailsData: {
      type: Array,
      required: true,
    },
    oldBankDetailsData: {
      type: Array,
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
  },
  emits: ["refetch-other-details"],
  data() {
    return {
      bankDetails: [],
      selectedBankDetails: {},
      showAddEditBankForm: false,
      showDeleteConfirmationModal: false,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    isActive() {
      if (this.selectedEmpStatus.toLowerCase() == "active") {
        return true;
      } else {
        return false;
      }
    },
    profileFormAccess() {
      let formAccess = this.accessRights("18");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["add"]
      ) {
        return true;
      } else {
        return false;
      }
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    employeeSettings() {
      return this.$store.state.orgDetails?.employeeSettings || {};
    },
    isWorkflowProfileEnabled() {
      return (
        this.employeeSettings?.Enable_Workflow_Profile?.toLowerCase() === "yes"
      );
    },
    canUpdateBankDetails() {
      // For profile context, use profile form access and workflow
      if (this.callingFrom?.toLowerCase() === "profile") {
        return this.isWorkflowProfileEnabled && this.profileFormAccess;
      }
      // For other contexts, use existing logic
      return (
        this.formAccess &&
        this.formAccess.add &&
        this.formAccess.admin?.toLowerCase() === "admin" &&
        this.isActive
      );
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.bankDetailsData && this.bankDetailsData.length > 0) {
      this.bankDetails = this.bankDetailsData;
    }
  },
  methods: {
    onUpdateAddSuccess() {
      this.closeAddEditForm();
      this.$emit("refetch-other-details");
    },
    closeAddEditForm() {
      mixpanel.track("EmpProfile-other-bank-add-edit-closed");
      this.showAddEditBankForm = false;
      this.selectedBankDetails = {};
    },
    openAddForm() {
      mixpanel.track("EmpProfile-other-bank-add-opened");
      this.selectedBankDetails = {};
      this.showAddEditBankForm = true;
    },
    openEditForm(selectedItem) {
      mixpanel.track("EmpProfile-other-bank-edit-opened");
      this.selectedBankDetails = selectedItem;
      this.showAddEditBankForm = true;
    },
  },
};
</script>
<style scoped>
.text-subtitle-1 font-weight-regular {
  color: #222121 !important;
  font-size: 15px;
  margin: 12px 0px;
  overflow-wrap: break-word;
  max-width: 360px;
}
.bottom-navigation :deep() .v-bottom-navigation__content {
  background-color: white;
  justify-content: flex-start !important;
  align-items: center !important;
}
.bottom-navigation :deep() .v-bottom-navigation__content > .v-btn {
  font-size: inherit;
  height: 45px;
  max-width: 120px;
  min-width: 100px;
  font-size: 1.2rem;
  text-transform: none;
  transition: inherit;
  width: auto;
  border-radius: 0;
  margin-left: 20px !important;
}
</style>
