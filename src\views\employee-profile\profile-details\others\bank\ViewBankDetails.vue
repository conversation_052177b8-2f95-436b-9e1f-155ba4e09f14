<template>
  <div
    v-if="bankDetails && bankDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
  >
    No bank details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in bankArray"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width: 500px;border-left: 7px solid ${generateRandomColor()}; min-height:350px;`
        : `border-left: 7px solid ${generateRandomColor()}; max-width: calc(100% - 60px)`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="mt-n7" style="width: 90%">
        <span>
          <v-card-text class="w-100 text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start w-100">
                <v-tooltip
                  :text="data.newBank?.bankName || data.oldBank?.bankName"
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="d-flex flex-column text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 90%'
                      "
                      v-bind="
                        data.newBank?.bankName || data.oldBank?.bankName
                          ? props
                          : ''
                      "
                    >
                      <span
                        v-if="
                          data.oldBank?.bankName &&
                          data.newBank?.bankName &&
                          data.oldBank?.bankName?.toLowerCase() !==
                            data.newBank?.bankName?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{ checkNullValue(data.oldBank.bankName) }}
                      </span>
                      <span
                        v-if="data.newBank"
                        :class="[
                          (data.oldBank &&
                            data.oldBank.bankName?.toLowerCase() !==
                              data.newBank.bankName?.toLowerCase()) ||
                          (!data.oldBank && oldBankDetails)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{ checkNullValue(data.newBank?.bankName) }}
                      </span>
                      <span
                        v-else-if="data.oldBank"
                        class="text-error text-decoration-line-through"
                      >
                        {{ checkNullValue(data.oldBank.bankName) }}
                      </span>
                      <span
                        v-if="data.newBank?.Status || data.oldBank?.Status"
                        :class="
                          data.newBank?.Status === 'Active'
                            ? 'text-green'
                            : 'text-red'
                        "
                      >
                        -
                        <span
                          v-if="
                            data.oldBank?.Status &&
                            data.newBank?.Status &&
                            data.oldBank?.Status?.toLowerCase() !==
                              data.newBank?.Status?.toLowerCase()
                          "
                          class="text-decoration-line-through text-error mr-1"
                        >
                          {{ checkNullValue(data.oldBank.Status) }}
                        </span>
                        <span
                          v-if="data.newBank"
                          :class="[
                            (data.oldBank &&
                              data.oldBank.Status?.toLowerCase() !==
                                data.newBank.Status?.toLowerCase()) ||
                            (!data.oldBank && oldBankDetails)
                              ? 'text-success'
                              : '',
                          ]"
                        >
                          {{ checkNullValue(data.newBank?.Status) }}
                        </span>
                        <span
                          v-else-if="data.oldBank"
                          class="text-error text-decoration-line-through"
                        >
                          {{ checkNullValue(data.oldBank.Status) }}
                        </span>
                      </span>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>

      <div class="card-columns w-100 mt-n6">
        <span
          :style="!isMobileView ? 'width:55%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="w-100 text-body-1 font-weight-regular">
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">
                {{ labelList[454]?.Field_Alias || "Account Number" }}
              </b>
              <span class="d-flex flex-column py-2">
                <span
                  v-if="
                    data.oldBank?.Bank_Account_Number &&
                    data.newBank?.Bank_Account_Number &&
                    data.oldBank?.Bank_Account_Number?.toLowerCase() !==
                      data.newBank?.Bank_Account_Number?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.Bank_Account_Number) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.Bank_Account_Number?.toLowerCase() !==
                        data.newBank.Bank_Account_Number?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.Bank_Account_Number) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.Bank_Account_Number) }}
                </span>
              </span>
            </div>
            <div
              v-if="labelList[355]?.Field_Visiblity === 'Yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start"
                >{{ labelList[355]?.Field_Alias }}
              </b>
              <v-tooltip
                :text="
                  data.newBank?.Bank_Account_Name ||
                  data.oldBank?.Bank_Account_Name
                "
                location="bottom"
                max-width="400"
              >
                <template v-slot:activator="{ props }">
                  <span class="pb-1 pt-1" v-bind="props">
                    <div
                      :style="
                        isMobileView ? 'max-width: 200px' : 'max-width:140px'
                      "
                      class="text-truncate"
                    >
                      <span
                        v-if="
                          data.oldBank?.Bank_Account_Name &&
                          data.newBank?.Bank_Account_Name &&
                          data.oldBank?.Bank_Account_Name?.toLowerCase() !==
                            data.newBank?.Bank_Account_Name?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{ checkNullValue(data.oldBank.Bank_Account_Name) }}
                      </span>
                      <span
                        v-if="data.newBank"
                        :class="[
                          (data.oldBank &&
                            data.oldBank.Bank_Account_Name?.toLowerCase() !==
                              data.newBank.Bank_Account_Name?.toLowerCase()) ||
                          (!data.oldBank && oldBankDetails)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{ checkNullValue(data.newBank?.Bank_Account_Name) }}
                      </span>
                      <span
                        v-else-if="data.oldBank"
                        class="text-error text-decoration-line-through"
                      >
                        {{ checkNullValue(data.oldBank.Bank_Account_Name) }}
                      </span>
                    </div>
                  </span>
                </template>
              </v-tooltip>
            </div>
            <div
              v-if="labelList[136].Field_Visiblity === 'Yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start"
                >{{ labelList[136].Field_Alias }}
              </b>
              <span class="d-flex flex-column py-2">
                <span
                  v-if="
                    data.oldBank?.IFSC_Code &&
                    data.newBank?.IFSC_Code &&
                    data.oldBank?.IFSC_Code?.toLowerCase() !==
                      data.newBank?.IFSC_Code?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.IFSC_Code) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.IFSC_Code?.toLowerCase() !==
                        data.newBank.IFSC_Code?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.IFSC_Code) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.IFSC_Code) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Credit Account </b>
              <span class="d-flex flex-column py-2">
                <span
                  v-if="
                    data.oldBank?.Credit_Account &&
                    data.newBank?.Credit_Account &&
                    data.oldBank?.Credit_Account?.toLowerCase() !==
                      data.newBank?.Credit_Account?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.Credit_Account) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.Credit_Account?.toLowerCase() !==
                        data.newBank.Credit_Account?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.Credit_Account) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.Credit_Account) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">
                {{ labelList[238].Field_Alias }}
              </b>
              <span
                class="d-flex flex-column py-2"
                :style="isMobileView ? 'max-width: 180px' : 'max-width: 350px'"
              >
                <span
                  v-if="
                    data.oldBank?.Street &&
                    data.newBank?.Street &&
                    data.oldBank?.Street?.toLowerCase() !==
                      data.newBank?.Street?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.IFSC_Code) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.Street?.toLowerCase() !==
                        data.newBank.Street?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.Street) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.Street) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">State/Province </b>
              <span class="d-flex flex-column py-2">
                <span
                  v-if="
                    data.oldBank?.State &&
                    data.newBank?.State &&
                    data.oldBank?.State?.toLowerCase() !==
                      data.newBank?.State?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.State) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.State?.toLowerCase() !==
                        data.newBank.State?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.State) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.State) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="w-100 text-body-1 font-weight-regular">
            <div
              v-if="labelList[445]?.Field_Visiblity.toLowerCase() === 'yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start"
                >{{ labelList[445].Field_Alias }}
              </b>
              <span class="d-flex flex-column py-2">
                <span
                  v-if="
                    data.oldBank?.Account_Type &&
                    data.newBank?.Account_Type &&
                    data.oldBank?.Account_Type?.toLowerCase() !==
                      data.newBank?.Account_Type?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.Account_Type) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.Account_Type?.toLowerCase() !==
                        data.newBank.Account_Type?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.Account_Type) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.Account_Type) }}
                </span>
              </span>
            </div>
            <div
              v-if="labelList[444]?.Field_Visiblity.toLowerCase() === 'yes'"
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <b class="mr-2 text-grey justify-start"
                >{{ labelList[444].Field_Alias }}
              </b>
              <span class="d-flex flex-column py-2">
                <span
                  v-if="
                    data.oldBank?.Branch_Name &&
                    data.newBank?.Branch_Name &&
                    data.oldBank?.Branch_Name?.toLowerCase() !==
                      data.newBank?.Branch_Name?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.Branch_Name) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.Branch_Name?.toLowerCase() !==
                        data.newBank.Branch_Name?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.Branch_Name) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.Branch_Name) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">Beneficiary Id </b>
              <span class="d-flex flex-column py-2">
                <span
                  v-if="
                    data.oldBank?.Beneficiary_Id &&
                    data.newBank?.Beneficiary_Id &&
                    data.oldBank?.Beneficiary_Id?.toLowerCase() !==
                      data.newBank?.Beneficiary_Id?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.Beneficiary_Id) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.Beneficiary_Id?.toLowerCase() !==
                        data.newBank.Beneficiary_Id?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.Beneficiary_Id) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.Beneficiary_Id) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">City </b>
              <span class="d-flex flex-column py-2">
                <span
                  v-if="
                    data.oldBank?.City &&
                    data.newBank?.City &&
                    data.oldBank?.City?.toLowerCase() !==
                      data.newBank?.City?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.City) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.City?.toLowerCase() !==
                        data.newBank.City?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.City) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.City) }}
                </span>
              </span>
            </div>
            <div class="mt-2 mr-2 d-flex flex-column justify-start">
              <b class="mr-2 text-grey justify-start">{{
                labelList[149].Field_Alias
              }}</b>
              <span class="d-flex flex-column py-2">
                <span
                  v-if="
                    data.oldBank?.Zip &&
                    data.newBank?.Zip &&
                    data.oldBank?.Zip?.toLowerCase() !==
                      data.newBank?.Zip?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldBank.Zip) }}
                </span>
                <span
                  v-if="data.newBank"
                  :class="[
                    (data.oldBank &&
                      data.oldBank.Zip?.toLowerCase() !==
                        data.newBank.Zip?.toLowerCase()) ||
                    (!data.oldBank && oldBankDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newBank?.Zip) }}
                </span>
                <span
                  v-else-if="data.oldBank"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldBank.Zip) }}
                </span>
              </span>
            </div>
            <div
              v-if="
                (data.newBank?.File_Name || data.oldBank?.File_Name) &&
                labelList[449]?.Field_Visiblity?.toLowerCase() === 'yes'
              "
              class="mt-2 mr-2 d-flex flex-column justify-start"
            >
              <span class="d-flex flex-column py-2">
                <span
                  style="text-decoration: underline; cursor: pointer"
                  @click="
                    retrieveDocuments(
                      data.newBank?.File_Name || data.oldBank?.File_Name
                    )
                  "
                  class="text-green"
                >
                  View Document
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div
      v-if="canUpdateBankDetails"
      style="position: absolute; top: 5px; right: 10px"
    >
      <ActionMenu
        v-if="selectedEmpStatus.toLowerCase() === 'active'"
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
  <FilePreviewModal
    v-if="openModal"
    :fileName="retrievedFileName"
    folderName="Employee Bank Details"
    fileRetrieveType="documents"
    @close-preview-modal="openModal = false"
  ></FilePreviewModal>
</template>

<script>
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import { defineAsyncComponent } from "vue";

const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

export default {
  name: "ViewBanks",
  components: { ActionMenu, FilePreviewModal },

  props: {
    bankDetails: {
      type: Object,
      required: true,
    },
    oldBankDetails: {
      type: [Array, Object],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return {
      retrievedFileName: "",
      openModal: false,
      havingAccess: {},
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    employeeSettings() {
      return this.$store.state.orgDetails?.employeeSettings || {};
    },
    isWorkflowProfileEnabled() {
      return (
        this.employeeSettings?.Enable_Workflow_Profile?.toLowerCase() === "yes"
      );
    },
    profileFormAccess() {
      let formAccess = this.accessRights("18");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["update"]
      ) {
        return true;
      } else {
        return false;
      }
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    canUpdateBankDetails() {
      // For profile context, use profile form access and workflow
      if (this.callingFrom?.toLowerCase() === "profile") {
        return this.isWorkflowProfileEnabled && this.profileFormAccess;
      }
      // For other contexts, use existing logic
      return (
        this.formAccess &&
        this.formAccess.update &&
        this.formAccess.admin?.toLowerCase() === "admin"
      );
    },
    bankArray() {
      const oldBank = this.oldBankDetails || [];
      const newBank = this.bankDetails || [];

      let idSet = new Set();
      let mergedDetails = [];

      newBank.forEach((newItem) => {
        const id = newItem.Bank_Id;
        idSet.add(id);
        const oldItem = oldBank.find((old) => old.Bank_Id === id);
        mergedDetails.push({
          newBank: newItem,
          oldBank: oldItem || null,
        });
      });

      oldBank.forEach((oldItem) => {
        const id = oldItem.Bank_Id;
        if (!idSet.has(id)) {
          mergedDetails.push({
            newBank: null,
            oldBank: oldItem,
          });
        }
      });

      return mergedDetails;
    },
  },
  methods: {
    //using the generateRandomColor function of helper.js file
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      // For profile context, use profile form access and workflow
      if (this.callingFrom?.toLowerCase() === "profile") {
        this.havingAccess["update"] =
          this.isWorkflowProfileEnabled && this.profileFormAccess ? 1 : 0;
      } else {
        // For other contexts, use existing logic
        this.havingAccess["update"] =
          this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin"
            ? 1
            : 0;
      }
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.bankDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    retrieveDocuments(fileName) {
      this.retrievedFileName = fileName;
      this.openModal = true;
    },
  },
};
</script>
