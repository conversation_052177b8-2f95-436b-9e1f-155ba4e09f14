<template>
  <v-card class="rounded-lg pa-4">
    <v-card-title class="d-flex">
      <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
        >Insurance Details</span
      >
      <v-spacer></v-spacer>
      <v-icon color="grey" size="25" @click="closeAddEditForm"
        >fas fa-times</v-icon
      >
    </v-card-title>
    <v-card-text>
      <v-alert v-if="showValidationAlert" prominent type="warning">
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </v-alert>
      <v-form ref="addEditInsuranceForm">
        <v-row>
          <v-col
            v-if="!templateEnabled"
            cols="12"
            sm="12"
            lg="6"
            md="6"
            xl="6"
            class="pa-1"
          >
            <CustomSelect
              :items="insuranceTypeList"
              label="Insurance Type"
              :itemSelected="insuranceFormData.Insurance_Type"
              itemValue="key"
              itemTitle="text"
              @selected-item="
                onChangeCustomSelectField($event, 'Insurance_Type')
              "
              :rules="[
                required('Insurance Type', insuranceFormData.Insurance_Type),
              ]"
              :isRequired="true"
            ></CustomSelect>
          </v-col>

          <v-col cols="12" sm="12" lg="6" md="6" xl="6" class="pa-1">
            <CustomSelect
              :items="insuranceList"
              label="Insurance"
              :itemSelected="insuranceFormData.InsuranceType_Id"
              itemValue="InsuranceType_Id"
              itemTitle="Insurance_Name"
              @selected-item="
                onChangeCustomSelectField($event, 'InsuranceType_Id')
              "
              :rules="[
                required('Insurance', insuranceFormData.InsuranceType_Id),
              ]"
              :isRequired="true"
              :isLoading="insuranceListLoading"
              :noDataText="
                insuranceListLoading ? 'Loading...' : 'No data available'
              "
            ></CustomSelect>
          </v-col>

          <v-col cols="12" sm="12" lg="6" md="6" xl="6" class="pa-1">
            <v-text-field
              v-model="insuranceFormData.Policy_No"
              :rules="[
                alreadyExistErrMsg['Policy_No'],
                required(
                  'Insurance No / Insurance Policy Number',
                  insuranceFormData.Policy_No
                ),
                validateWithRulesAndReturnMessages(
                  insuranceFormData.Policy_No,
                  'policyNo',
                  'Insurance No / Insurance Policy Number'
                ),
              ]"
              variant="solo"
              @update:model-value="onChangeFields('Policy_No')"
              @change="
                validateFieldAlreadyExist(
                  'Policy_No',
                  'Insurance No / Insurance Policy Number'
                )
              "
            >
              <template v-slot:label>
                Insurance No / Insurance Policy Number<span style="color: red"
                  >*</span
                >
              </template>
            </v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <div class="d-flex justify-end">
              <v-btn
                @click="closeAddEditForm"
                variant="outlined"
                class="ma-2 pa-2"
                >Cancel</v-btn
              >
              <v-btn
                :disabled="!isFormDirty"
                class="ma-2 pa-2"
                color="primary"
                variant="elevated"
                @click="validateInsuranceDetails"
              >
                Save
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import { LIST_INSURANCE } from "@/graphql/dropDownQueries";
import {
  ADD_UPDATE_INSURANCE_DETAILS,
  VALIDATE_FIELD_AVAILABILITY,
} from "@/graphql/employee-profile/profileQueries.js";
import { RETRIEVE_INSURANCE_RULES } from "@/graphql/tax-and-statutory-compliance/insuranceType";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AddEditInsuranceDetails",
  props: {
    selectedInsuranceDetails: {
      type: Object,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    callingFrom: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
  },
  components: { CustomSelect },
  mixins: [validationRules],
  emits: ["refetch-other-details", "close-insurance-form"],
  data() {
    return {
      insuranceTypes: [],
      insuranceFormData: {
        Employee_Id: 0,
        InsuranceType_Id: null,
        Policy_No: null,
        Policy_Id: 0,
        Insurance_Type: "",
        Insurance_Name: "",
      },
      backupInsuranceFormData: {},
      // edit
      isFormDirty: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      // list
      insuranceTypeList: [
        { key: "insurance", text: "Insurance" },
        { key: "fixedHealthInsurance", text: "Fixed Health Insurance" },
      ],
      insuranceList: [],
      insuranceListLoading: false,
      alreadyExistErrMsg: {
        Policy_No: true,
      },
    };
  },

  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    templateEnabled() {
      return this.$store.state.templateEnabled;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedInsuranceDetails &&
      Object.keys(this.selectedInsuranceDetails).length > 0
    ) {
      this.insuranceFormData = JSON.parse(
        JSON.stringify(this.selectedInsuranceDetails)
      );
      this.backupInsuranceFormData = JSON.parse(
        JSON.stringify(this.insuranceFormData)
      );
      this.formType = "edit";
      if (!this.templateEnabled) {
        this.retrieveInsuranceLists();
      }
    } else {
      this.formType = "add";
    }
    if (this.templateEnabled) {
      this.fetchInsuranceTypeDetails();
    }
  },

  methods: {
    validateFieldAlreadyExist(field, label) {
      let vm = this;
      if (
        vm.insuranceFormData[field] &&
        vm.selectedInsuranceDetails &&
        vm.insuranceFormData[field] !== vm.selectedInsuranceDetails[field]
      ) {
        vm.$apollo
          .query({
            query: VALIDATE_FIELD_AVAILABILITY,
            client: "apolloClientAC",
            variables: {
              employeeId: vm.selectedEmpId,
              columnValue: vm.insuranceFormData[field],
              columnName: field,
              tableName: "emp_insurancepolicyno",
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateCommonAvailability &&
              !response.data.validateCommonAvailability.errorCode
            ) {
              const { isAvailable } = response.data.validateCommonAvailability;
              if (!isAvailable) {
                vm.alreadyExistErrMsg[field] = label + " already exist";
                vm.$refs.addEditInsuranceForm.validate();
              } else {
                vm.alreadyExistErrMsg[field] = true;
              }
            }
          })
          .catch((err) => {
            vm.alreadyExistErrMsg[field] = true;
            vm.$refs.addEditInsuranceForm.validate();
            let fieldLabel = label.toLowerCase();
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: fieldLabel,
              isListError: false,
            });
          });
      }
    },
    closeAddEditForm() {
      this.$emit("close-insurance-form");
    },
    fetchInsuranceTypeDetails() {
      let vm = this;
      vm.insuranceListLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_INSURANCE_RULES,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.retrieveInsuranceRules) {
            let mainList = JSON.parse(
              response.data.retrieveInsuranceRules.insuranceRulesData
            );
            vm.insuranceList = mainList;

            vm.insuranceListLoading = false;
          } else {
            vm.insuranceListLoading = false;
            vm.insuranceList = [];
          }
        })
        .catch(() => {
          vm.insuranceListLoading = false;
          vm.insuranceList = [];
        });
    },

    onChangeFields(field) {
      this.isFormDirty = true;
      if (field) this.alreadyExistErrMsg[field] = true;
    },

    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.insuranceFormData[field] = value;
      if (field === "Insurance_Type" && value) {
        this.insuranceFormData["InsuranceType_Id"] = null;
        this.retrieveInsuranceLists();
      }
    },

    async validateInsuranceDetails() {
      const { valid } = await this.$refs.addEditInsuranceForm.validate();
      mixpanel.track("EmpProfile-other-insurance-submit-click");
      if (valid) {
        if (
          JSON.stringify(this.insuranceFormData) ===
          JSON.stringify(this.backupInsuranceFormData)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.updateInsuranceDetails();
        }
      }
    },

    updateInsuranceDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_INSURANCE_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId,
            policyId: vm.insuranceFormData.Policy_Id,
            insuranceTypeId: vm.insuranceFormData.InsuranceType_Id,
            policyNo: vm.insuranceFormData.Policy_No,
            insuranceType: vm.insuranceFormData.Insurance_Type,
            formId: vm.callingFrom == "profile" ? 18 : 243,
            formStatus: vm.actionType == "edit" ? 1 : 0,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          const { message } = res.data.addUpdateInsuranceDetails;
          mixpanel.track("EmpProfile-other-insurance-edit-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: message?.includes("approval")
              ? "Insurance Details submitted for approval."
              : vm.formType === "edit"
              ? "Insurance details updated successfully"
              : "Insurance details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-other-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("EmpProfile-other-insurance-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "insurance details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveInsuranceLists() {
      let vm = this;
      vm.insuranceListLoading = true;
      vm.$apollo
        .query({
          query: LIST_INSURANCE,
          variables: {
            employeeId: vm.selectedEmpId,
            type: vm.insuranceFormData.Insurance_Type,
          },
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listInsuranceType &&
            !response.data.listInsuranceType.errorCode
          ) {
            const { insuranceType } = response.data.listInsuranceType;
            let insType = insuranceType ? JSON.parse(insuranceType) : [];
            vm.insuranceList = insType && insType.length > 0 ? insType : [];
          }
          vm.insuranceListLoading = false;
        })
        .catch(() => {
          vm.insuranceListLoading = false;
        });
    },
  },
};
</script>
<style>
input::selection {
  background: green;
}
</style>
