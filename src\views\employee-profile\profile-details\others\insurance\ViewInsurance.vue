<template>
  <div
    v-if="insuranceDetails && insuranceDetails.length === 0"
    class="d-flex align-center justify-center fill-height text-h6 text-grey py-4"
  >
    No insurance details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(data, index) in insuranceArray"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width: 400px;border-left: 7px solid ${generateRandomColor()}; min-height:120px;`
        : `border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip
                  :text="
                    data.newIns?.Insurance_Name || data.oldIns?.Insurance_Name
                  "
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="
                        data.newIns?.Insurance_Name ||
                        data.oldIns?.Insurance_Name
                          ? props
                          : ''
                      "
                    >
                      <span
                        v-if="
                          data.oldIns?.Insurance_Name &&
                          data.newIns?.Insurance_Name &&
                          data.oldIns?.Insurance_Name?.toLowerCase() !==
                            data.newIns?.Insurance_Name?.toLowerCase()
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{ checkNullValue(data.oldIns.Insurance_Name) }}
                      </span>
                      <span
                        v-if="data.newIns"
                        :class="[
                          (data.oldIns &&
                            data.oldIns.Insurance_Name?.toLowerCase() !==
                              data.newIns.Insurance_Name?.toLowerCase()) ||
                          (!data.oldIns && oldInsuranceDetails)
                            ? 'text-success'
                            : '',
                        ]"
                      >
                        {{ checkNullValue(data.newIns?.Insurance_Name) }}
                      </span>
                      <span
                        v-else-if="data.oldIns"
                        class="text-error text-decoration-line-through"
                      >
                        {{ checkNullValue(data.oldIns.Insurance_Name) }}
                      </span>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="card-columns w-100 mt-n6">
        <span
          v-if="!templateEnabled"
          :style="!isMobileView ? 'width:50%' : 'width:100%'"
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="mr-2 d-flex flex-column justify-start">
              <b class="text-grey mb-1 justify-start">Insurance Type </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldIns?.Insurance_Type &&
                    data.newIns?.Insurance_Type &&
                    data.oldIns?.Insurance_Type?.toLowerCase() !==
                      data.newIns?.Insurance_Type?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldIns.Insurance_Type) }}
                </span>
                <span
                  v-if="data.newIns"
                  :class="[
                    (data.oldIns &&
                      data.oldIns.Insurance_Type?.toLowerCase() !==
                        data.newIns.Insurance_Type?.toLowerCase()) ||
                    (!data.oldIns && oldInsuranceDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newIns?.Insurance_Type) }}
                </span>
                <span
                  v-else-if="data.oldIns"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldIns.Insurance_Type) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
        <span
          :style="
            !isMobileView
              ? 'width:50%'
              : 'margin-top:-28px !important;margin-bottom: 10px !important;width:100%'
          "
          class="d-flex align-start flex-column"
        >
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex flex-column justify-start">
              <b class="mb-1 text-grey justify-start"
                >Insurance No / Insurance Policy Number
              </b>
              <span class="py-2">
                <span
                  v-if="
                    data.oldIns?.Policy_No &&
                    data.newIns?.Policy_No &&
                    data.oldIns?.Policy_No?.toLowerCase() !==
                      data.newIns?.Policy_No?.toLowerCase()
                  "
                  class="text-decoration-line-through text-error mr-1"
                >
                  {{ checkNullValue(data.oldIns.Policy_No) }}
                </span>
                <span
                  v-if="data.newIns"
                  :class="[
                    (data.oldIns &&
                      data.oldIns.Policy_No?.toLowerCase() !==
                        data.newIns.Policy_No?.toLowerCase()) ||
                    (!data.oldIns && oldInsuranceDetails)
                      ? 'text-success'
                      : '',
                  ]"
                >
                  {{ checkNullValue(data.newIns?.Policy_No) }}
                </span>
                <span
                  v-else-if="data.oldIns"
                  class="text-error text-decoration-line-through"
                >
                  {{ checkNullValue(data.oldIns.Policy_No) }}
                </span>
              </span>
            </div>
          </v-card-text>
        </span>
      </div>
    </div>
    <div
      v-if="formAccess && formAccess.update && formAccess.admin === 'admin'"
      class="mt-n5 ml-auto"
    >
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "ViewInsurance",
  components: { ActionMenu },

  props: {
    insuranceDetails: {
      type: Object,
      required: true,
    },
    oldInsuranceDetails: {
      type: [Array, Object],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  data() {
    return { havingAccess: {} };
  },
  computed: {
    templateEnabled() {
      return this.$store.state.templateEnabled;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    insuranceArray() {
      const oldIns = this.oldInsuranceDetails || [];
      const newIns = this.insuranceDetails || [];

      let idSet = new Set();
      let mergedDetails = [];

      newIns.forEach((newItem) => {
        const id = newItem.Policy_Id;
        idSet.add(id);
        const oldItem = oldIns.find((old) => old.Policy_Id === id);
        mergedDetails.push({
          newIns: newItem,
          oldIns: oldItem || null,
        });
      });

      oldIns.forEach((oldItem) => {
        const id = oldItem.Policy_Id;
        if (!idSet.has(id)) {
          mergedDetails.push({
            newIns: null,
            oldIns: oldItem,
          });
        }
      });

      return mergedDetails;
    },
  },
  methods: {
    //using the generateRandomColor function of helper.js file
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.formAccess &&
        this.formAccess.update &&
        this.formAccess.admin === "admin"
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem = this.insuranceDetails[index];
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
  },
};
</script>
