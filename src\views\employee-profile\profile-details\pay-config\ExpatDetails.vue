<template>
  <div>
    <div class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="yellow"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"> Expat </span>
      </div>
      <div v-if="!showEditForm && formAccess && formAccess.update && allowEdit">
        <v-btn @click="openEditForm" color="primary" variant="text">
          <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
        </v-btn>
      </div>
    </div>
    <div v-if="showEditForm">
      <v-form ref="expatDetailsObserver">
        <v-row class="pa-4 ma-2">
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[478]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <CustomSelect
              :items="['Gross', 'Gross Up']"
              :itemSelected="editExpatDetailsData.Salary_Calculation_Scheme"
              style="max-width: 250px"
              :label="labelList[478].Field_Alias || 'Salary Calculation Scheme'"
              :isRequired="
                labelList[478].Mandatory_Field?.toLowerCase() === 'yes'
                  ? true
                  : false
              "
              :rules="[
                labelList[478].Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[478].Field_Alias || 'Salary Calculation Scheme',
                      editExpatDetailsData.Salary_Calculation_Scheme
                    )
                  : true,
              ]"
              @selected-item="
                onChangeCustomSelectField($event, 'Salary_Calculation_Scheme')
              "
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[479]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <v-text-field
              v-model="editExpatDetailsData.Minimum_Wage"
              :label="labelList[479].Field_Alias || 'Minimum Wage'"
              style="max-width: 250px"
              type="number"
              variant="solo"
              :rules="[
                labelList[479].Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[479].Field_Alias || 'Minimum Wage',
                      editExpatDetailsData.Minimum_Wage
                    )
                  : true,
                editExpatDetailsData.Minimum_Wage
                  ? minNumberValidation(
                      labelList[479].Field_Alias || 'Minimum Wage',
                      editExpatDetailsData.Minimum_Wage,
                      0
                    )
                  : true,
              ]"
              @update:model-value="onChangeFields('minimum_wage')"
            >
              <template v-slot:label>
                {{ labelList[479].Field_Alias || "Minimum Wage" }}
                <span
                  v-if="labelList[479].Mandatory_Field?.toLowerCase() === 'yes'"
                  style="color: red"
                  >*</span
                >
              </template>
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[480]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <CustomSelect
              :items="['Employment Start', 'Residency Threshold']"
              :itemSelected="editExpatDetailsData.TDS_Effective_From"
              style="max-width: 250px"
              :label="labelList[480].Field_Alias || 'TDS to be Deducted'"
              :isRequired="
                labelList[480].Mandatory_Field?.toLowerCase() === 'yes'
                  ? true
                  : false
              "
              :rules="[
                labelList[480].Mandatory_Field?.toLowerCase() === 'yes'
                  ? required(
                      labelList[480].Field_Alias || 'TDS to be Deducted',
                      editExpatDetailsData.TDS_Effective_From
                    )
                  : true,
              ]"
              @selected-item="
                onChangeCustomSelectField($event, 'TDS_Effective_From')
              "
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            md="4"
            sm="6"
            v-if="labelList[481]?.Field_Visiblity?.toLowerCase() === 'yes'"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList[481].Field_Alias || "FRRO Registration Completed" }}
            </p>
            <v-switch
              color="primary"
              v-model="editExpatDetailsData.FRRO_Registration"
              :true-value="'Yes'"
              :false-value="'No'"
              @update:model-value="onChangeFields()"
            ></v-switch>
          </v-col>
        </v-row>
      </v-form>
    </div>
    <div v-else>
      <v-row class="pa-4 ma-2 card-blue-background">
        <FieldDiff
          v-if="labelList[478]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :oldDataAvailable="oldExpatDetailsData ? true : false"
          :label="labelList[478].Field_Alias || 'Salary Calculation Scheme'"
          :newValue="expatDetailsData.Salary_Calculation_Scheme"
          :oldValue="oldExpatDetailsData?.Salary_Calculation_Scheme"
        />
        <FieldDiff
          v-if="labelList[479]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :oldDataAvailable="oldExpatDetailsData ? true : false"
          :label="labelList[479].Field_Alias || 'Minimum Wage'"
          :newValue="expatDetailsData.Minimum_Wage"
          :oldValue="oldExpatDetailsData?.Minimum_Wage"
        />
        <FieldDiff
          v-if="labelList[480]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :oldDataAvailable="oldExpatDetailsData ? true : false"
          :label="labelList[480].Field_Alias || 'TDS to be deducted'"
          :newValue="expatDetailsData.TDS_Effective_From"
          :oldValue="oldExpatDetailsData?.TDS_Effective_From"
        />
        <FieldDiff
          v-if="labelList[481]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :oldDataAvailable="oldExpatDetailsData ? true : false"
          :label="labelList[481].Field_Alias || 'FRRO Registration Completed'"
          :newValue="expatDetailsData.FRRO_Registration"
          :oldValue="oldExpatDetailsData?.FRRO_Registration"
        />
      </v-row>
    </div>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center" class="mb-10">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            variant="text"
            color="primary"
            elevation="4"
            rounded="lg"
            size="small"
            style="height: 40px"
            @click="closeEditForm()"
            ><span class="text-primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1"
            color="primary"
            variant="elevated"
            style="height: 40px"
            :disabled="!isFormDirty"
            @click="validateExpatDetails()"
          >
            Save
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>
<script>
import { defineAsyncComponent } from "vue";
import mixpanel from "mixpanel-browser";
import { checkNullValue } from "@/helper.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import { UPDATE_TDS_CONTRACTOR_CONFIG } from "@/graphql/employee-profile/profileQueries.js";
import Config from "@/config";
const FieldDiff = defineAsyncComponent(() =>
  import("@/components/custom-components/FieldDiff.vue")
);

export default {
  name: "ExpatDetails",
  components: { FieldDiff, CustomSelect },
  mixins: [validationRules],
  props: {
    expatDetailsData: {
      type: Object,
      required: true,
    },
    oldExpatDetailsData: {
      type: Object,
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      required: true,
    },
    allowEdit: {
      type: Boolean,
      required: true,
    },
    callingFrom: {
      type: String,
      required: true,
    },
    actionType: {
      type: String,
      required: true,
    },
    selectedEmpStatus: {
      type: String,
      required: true,
    },
    validationData: {
      type: Object,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      required: true,
    },
  },
  emits: ["update-success", "edit-form-opened"],

  data() {
    return {
      editExpatDetailsData: {},
      backupExpatDetailsData: {},
      showEditForm: false,
      isFormDirty: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      openWarningModal: false,
      openBottomSheet: false,
      fieldsEditable: true,
    };
  },
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  watch: {
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    openBottomSheet(val) {
      this.$emit("edit-form-opened", val);
    },
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.fieldsEditable =
      this.selectedEmpStatus == "Active" ||
      this.validationData?.salaryNotGenerated;
  },
  methods: {
    checkNullValue,

    onChangeFields() {
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.editExpatDetailsData[field] = value;
    },

    openEditForm() {
      mixpanel.track("EmpProfile-payConfig-expat-edit-opened");
      this.editExpatDetailsData = JSON.parse(
        JSON.stringify(this.expatDetailsData)
      );
      this.backupExpatDetailsData = JSON.parse(
        JSON.stringify(this.editExpatDetailsData)
      );
      this.showEditForm = true;
      this.openBottomSheet = true;
    },

    closeEditForm() {
      mixpanel.track("EmpProfile-payConfig-expat-edit-closed");
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.showEditForm = false;
      }
    },
    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    async validateExpatDetails() {
      const { valid } = await this.$refs.expatDetailsObserver.validate();
      mixpanel.track("EmpProfile-payConfig-expat-submit-clicked");
      if (valid) {
        if (
          JSON.stringify(this.editExpatDetailsData) ===
          JSON.stringify(this.backupExpatDetailsData)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.updateExpatDetails();
        }
      }
    },

    updateExpatDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_TDS_CONTRACTOR_CONFIG,
          variables: {
            employeeId: vm.selectedEmpId,
            eligibleForContractorTds:
              vm.editExpatDetailsData.Eligible_For_Contractor_Tds || 0,
            taxSectionId: vm.editExpatDetailsData.Tax_Section_Id || 0,
            eligibleForPt: vm.editExpatDetailsData.Eligible_For_PT,
            salaryCalculationScheme: vm.editExpatDetailsData
              .Salary_Calculation_Scheme
              ? vm.editExpatDetailsData.Salary_Calculation_Scheme
              : null,
            minimumWage: vm.editExpatDetailsData.Minimum_Wage
              ? parseFloat(vm.editExpatDetailsData.Minimum_Wage)
              : null,
            tdsEffectiveFrom: vm.editExpatDetailsData.TDS_Effective_From
              ? vm.editExpatDetailsData.TDS_Effective_From
              : null,
            frroRegistration: vm.editExpatDetailsData.FRRO_Registration
              ? vm.editExpatDetailsData.FRRO_Registration
              : "No",
            formId: vm.callingFrom === "profile" ? 18 : 243,
            formStatus: vm.actionType === "edit" ? 1 : 0,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          const { message } = res.data.addUpdateTaxDetails;
          mixpanel.track("EmpProfile-payConfig-expat-details-edit-success");
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: message?.includes("approval")
              ? "Expat details submitted for approval."
              : "Expat details updated successfully.",
          };
          vm.showAlert(snackbarData);
          vm.openBottomSheet = false;
          vm.showEditForm = false;
          vm.isLoading = false;
          vm.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
          vm.$store.commit(
            "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
            "0-false"
          );
          vm.$emit("update-success");
        })
        .catch((updateError) => {
          vm.handleUpdateError(updateError);
        });
    },

    handleUpdateError(err) {
      mixpanel.track("EmpProfile-payConfig-expat-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "expat details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
