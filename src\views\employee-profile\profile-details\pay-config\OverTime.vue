<template>
  <div class="mt-4">
    <div class="d-flex align-center justify-space-between">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="purple"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        <span class="text-h6 text-grey-darken-1 font-weight-bold"
          >Overtime</span
        >
      </div>
      <div v-if="!showEditForm && formAccess && formAccess.update && allowEdit">
        <v-btn @click="openEditForm" color="primary" variant="text">
          <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
        </v-btn>
      </div>
    </div>
    <div v-if="showEditForm">
      <v-form ref="overtimeObserver">
        <v-row class="pa-4 ma-2">
          <v-col
            v-if="labelList['87'] && labelList['87'].Field_Visiblity === 'Yes'"
            cols="12"
            md="4"
            sm="6"
          >
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ labelList["87"].Field_Alias }}
            </p>
            <v-tooltip
              text="Since the payslip has already been generated, updates to this field are unavailable. If any changes are required, please delete the Salary Payslip and Full & Final Settlement."
            >
              <template v-slot:activator="{ props }">
                <v-card
                  width="max-content"
                  variant="plain"
                  v-bind="fieldsEditable ? '' : props"
                  :class="fieldsEditable ? '' : 'cursor-not-allowed'"
                >
                  <v-switch
                    color="primary"
                    v-model="editOvertimeData.Eligible_For_Overtime"
                    :disabled="!fieldsEditable"
                    :true-value="1"
                    :false-value="0"
                    @update:model-value="onChangeFields"
                  ></v-switch>
                </v-card>
              </template>
            </v-tooltip>
          </v-col>
        </v-row>
      </v-form>
    </div>
    <div v-else>
      <v-row class="pa-4 ma-2 card-blue-background">
        <FieldDiff
          v-if="labelList['87'] && labelList['87'].Field_Visiblity === 'Yes'"
          :oldDataAvailable="oldOvertimeData ? true : false"
          :label="labelList['87'].Field_Alias"
          :newValue="viewOvertimeData.Eligible_For_Overtime ? 'Yes' : 'No'"
          :oldValue="oldOvertimeData?.Eligible_For_Overtime ? 'Yes' : 'No'"
        >
          <template v-slot:label-tooltip>
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-icon
                  class="ml-1 mt-1"
                  v-bind="props"
                  size="small"
                  color="info"
                >
                  fas fa-info-circle
                </v-icon>
              </template>
              <div style="width: 350px !important">
                Enabling this flag will allow the employees to claim additional
                wages
              </div>
            </v-tooltip>
          </template>
        </FieldDiff>
      </v-row>
    </div>
  </div>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px; margin-top: 10px"
            @click="closeEditForm()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            :dense="isMobileView"
            :disabled="!isFormDirty"
            style="height: 40px; margin-top: 10px"
            @click="validateOvertime()"
          >
            <span class="primary">Save</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="secondary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDiscardChanges()"
  ></AppWarningModal>
</template>

<script>
import { UPDATE_OVERTIME_CONFIG } from "@/graphql/employee-profile/profileQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import { defineAsyncComponent } from "vue";
const FieldDiff = defineAsyncComponent(() =>
  import("@/components/custom-components/FieldDiff.vue")
);

export default {
  name: "OverTime",
  components: { FieldDiff },
  props: {
    overtimeData: {
      type: Object,
      required: true,
    },
    formAccess: {
      type: [Boolean, Object],
      required: true,
    },
    allowEdit: {
      type: Boolean,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    validationData: {
      type: Object,
      required: true,
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    actionType: {
      type: String,
      default: "",
    },
    oldOvertimeData: {
      type: [Array, Object],
      required: false,
    },
  },

  emits: ["update-success", "edit-form-opened"],

  data: () => ({
    viewOvertimeData: {},
    editOvertimeData: {},
    backupOvertimeData: {},
    showEditForm: false,
    isFormDirty: false,
    isLoading: false,
    openMoreDetails: false,
    validationMessages: [],
    showValidationAlert: false,
    openWarningModal: false,
    openBottomSheet: false,
    fieldsEditable: true,
  }),
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.viewOvertimeData = this.overtimeData;
    this.fieldsEditable =
      this.selectedEmpStatus == "Active" ||
      this.validationData?.salaryNotGenerated;
  },

  watch: {
    isFormDirty(val) {
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", val);
    },
    openBottomSheet(val) {
      this.$emit("edit-form-opened", val);
    },
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },

  methods: {
    onChangeFields() {
      this.isFormDirty = true;
    },

    openEditForm() {
      mixpanel.track("EmpProfile-payConfig-overtime-edit-opened");
      this.editOvertimeData = JSON.parse(JSON.stringify(this.viewOvertimeData));
      this.backupOvertimeData = JSON.parse(
        JSON.stringify(this.editOvertimeData)
      );
      this.showEditForm = true;
      this.openBottomSheet = true;
    },

    closeEditForm() {
      mixpanel.track("EmpProfile-payConfig-overtime-edit-closed");
      if (this.isFormDirty) {
        this.openWarningModal = true;
      } else {
        this.openBottomSheet = false;
        this.showEditForm = false;
      }
    },

    onDiscardChanges() {
      this.openWarningModal = false;
      this.isFormDirty = false;
      this.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
      this.closeEditForm();
    },

    async validateOvertime() {
      const { valid } = await this.$refs.overtimeObserver.validate();
      mixpanel.track("EmpProfile-payConfig-overtime-submit-clicked");
      if (valid) {
        if (
          JSON.stringify(this.editOvertimeData) ===
          JSON.stringify(this.backupOvertimeData)
        ) {
          let snackbarData = {
            isOpen: true,
            message: "No modifications have been made.",
            type: "info",
          };
          this.showAlert(snackbarData);
        } else {
          this.updateOvertime();
        }
      }
    },

    updateOvertime() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_OVERTIME_CONFIG,
          variables: {
            employeeId: vm.selectedEmpId,
            eligibleForOvertime: vm.editOvertimeData.Eligible_For_Overtime
              ? vm.editOvertimeData.Eligible_For_Overtime
              : 0,
            formId: vm.callingFrom === "profile" ? 18 : 243,
            formStatus: vm.actionType === "edit" ? 1 : 0,
          },
          client: "apolloClientAD",
        })
        .then((res) => {
          const { message } = res.data.addUpdateOvertimeDetails;
          mixpanel.track("EmpProfile-payConfig-overtime-edit-success");
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: message?.includes("approval")
              ? "Overtime configuration submitted for approval."
              : "Overtime configuration updated successfully.",
          };
          vm.showAlert(snackbarData);
          vm.openBottomSheet = false;
          vm.showEditForm = false;
          vm.isLoading = false;
          vm.$store.commit("employeeProfile/UPDATE_EDIT_FORM_CHANGED", false);
          vm.$store.commit(
            "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
            "0-false"
          );
          vm.$emit("update-success");
        })
        .catch((updateError) => {
          vm.handleUpdateError(updateError);
        });
    },

    handleUpdateError(err) {
      mixpanel.track("EmpProfile-payConfig-overtime-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "overtime configuration",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
