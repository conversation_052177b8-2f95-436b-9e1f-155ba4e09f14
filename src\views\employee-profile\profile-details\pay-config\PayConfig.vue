<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList">
    <AppFetchErrorScreen
      image-name="common/common-error-image"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      button-text="Retry"
      :isSmallImage="true"
      @button-click="refetchAPIs()"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else-if="Object.keys(labelList).length === 0">
    <AppFetchErrorScreen
      content="No fields configured. Please contact the platform administrator"
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else class="pb-12 mb-12">
    <div v-if="!openedEditForm" class="d-flex justify-end mt-n2 mr-n2">
      <v-icon @click="refetchAPIs()" size="17" color="grey"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <Retirals
      :form-access="formAccess"
      :retirals-data="retiralDetailsData"
      :oldRetiralsData="oldRetiralsData"
      :allow-edit="callingFrom != 'profile' && !openedEditForm"
      :callingFrom="callingFrom"
      :actionType="actionType"
      :selectedEmpStatus="selectedEmpStatus"
      :validationData="validationData"
      :selectedEmpId="selectedEmpId"
      @edit-form-opened="openedEditForm = $event"
      @update-success="refetchAPIs()"
    ></Retirals>
    <OverTime
      :form-access="formAccess"
      :overtime-data="overtimeDetailsData"
      :oldOvertimeData="oldOvertimeData"
      :allow-edit="callingFrom != 'profile' && !openedEditForm"
      :callingFrom="callingFrom"
      :actionType="actionType"
      :selectedEmpStatus="selectedEmpStatus"
      :validationData="validationData"
      :selectedEmpId="selectedEmpId"
      @edit-form-opened="openedEditForm = $event"
      @update-success="refetchAPIs()"
    ></OverTime>
    <BondRecovery
      :form-access="formAccess"
      :bond-recovery-data="bondRecoveryDetailsData"
      :oldBondRecoveryData="oldBondRecoveryData"
      :allow-edit="callingFrom != 'profile' && !openedEditForm"
      :callingFrom="callingFrom"
      :actionType="actionType"
      :selectedEmpStatus="selectedEmpStatus"
      :validationData="validationData"
      :selectedEmpId="selectedEmpId"
      @edit-form-opened="openedEditForm = $event"
      @update-success="refetchAPIs()"
    ></BondRecovery>
    <TaxConfig
      :tds-data="taxConfigDetailsData"
      :oldTDSContractorData="oldTDSContractorData"
      :form-access="formAccess"
      :allow-edit="callingFrom != 'profile' && !openedEditForm"
      :callingFrom="callingFrom"
      :actionType="actionType"
      :selectedEmpStatus="selectedEmpStatus"
      :section-list="tdsSectionList"
      :validationData="validationData"
      :selectedEmpId="selectedEmpId"
      @edit-form-opened="openedEditForm = $event"
      @update-success="refetchAPIs()"
    ></TaxConfig>
    <ExpatDetails
      :expat-details-data="expatDetailsData"
      :old-expat-details-data="oldExpatDetailsData"
      :form-access="formAccess"
      :allow-edit="callingFrom != 'profile' && !openedEditForm"
      :calling-from="callingFrom"
      :action-type="actionType"
      :selected-emp-status="selectedEmpStatus"
      :validation-data="validationData"
      :selected-emp-id="selectedEmpId"
      @edit-form-opened="openedEditForm = $event"
      @update-success="refetchAPIs()"
    ></ExpatDetails>
    <MoreDetails
      v-if="moreDetailsList.length"
      :more-details-list="moreDetailsList"
      :open-close-card="openMoreDetails"
      @on-open-close="openMoreDetails = $event"
    ></MoreDetails>
  </div>
</template>

<script>
import {
  RETRIEVE_SALARY_CONFIG,
  RETRIEVE_EMPLOYEE_CHANGES,
} from "@/graphql/employee-profile/profileQueries.js";
import Retirals from "./Retirals";
import OverTime from "./OverTime";
import BondRecovery from "./BondRecovery";
import TaxConfig from "./TaxConfig";
import ExpatDetails from "./ExpatDetails";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { convertUTCToLocal } from "@/helper.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "ViewEmployeeDetails",
  components: {
    Retirals,
    OverTime,
    BondRecovery,
    TaxConfig,
    ExpatDetails,
    MoreDetails,
  },
  props: {
    employeeData: {
      type: Object,
      required: true,
    },
    selectedEmpStatus: {
      type: String,
      default: "",
    },
    formAccess: {
      type: Object,
      required: true,
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
    actionType: {
      type: String,
      default: "",
    },
    callingFrom: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      validationData: {},
      retiralDetailsData: {},
      overtimeDetailsData: {},
      bondRecoveryDetailsData: {},
      taxConfigDetailsData: {},
      expatDetailsData: {},
      oldRetiralsData: null,
      oldOvertimeData: null,
      oldBondRecoveryData: null,
      oldTDSContractorData: null,
      oldExpatDetailsData: null,
      tdsSectionList: [],
      moreDetailsList: [],
      openedEditForm: false,
      salaryNotGenerated: false,
      openMoreDetails: false,
    };
  },
  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.getSalaryConfigurations();
  },
  methods: {
    formMoreDetails() {
      this.moreDetailsList = [];
      const { Added_On, Added_By_Name, Updated_By_Name, Updated_On } =
        this.retiralDetailsData;
      if (Added_On && Added_By_Name) {
        this.moreDetailsList.push({
          actionDate: convertUTCToLocal(Added_On),
          actionBy: Added_By_Name,
          text: "Added",
        });
      }
      if (Updated_By_Name && Updated_On) {
        this.moreDetailsList.push({
          actionDate: convertUTCToLocal(Updated_On),
          actionBy: Updated_By_Name,
          text: "Updated",
        });
      }
    },
    refetchAPIs() {
      this.isErrorInList = false;
      this.openedEditForm = false;
      mixpanel.track("EmpProfile-payConfig-refetch");
      this.getSalaryConfigurations();
      if (Object.keys(this.labelList).length == 0) {
        this.getLabelList();
      }
    },
    getSalaryConfigurations() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_SALARY_CONFIG,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-payConfig-fetch-success");
          if (
            response &&
            response.data &&
            response.data.retrieveSalaryConfiguration
          ) {
            const {
              retiralDetails,
              bondRecoveryDetails,
              overtimeDetails,
              contractDetails,
              validations,
              taxDetails,
            } = response.data.retrieveSalaryConfiguration;
            vm.retiralDetailsData = retiralDetails
              ? JSON.parse(retiralDetails)
              : {};
            vm.retiralDetailsData =
              Object.keys(vm.retiralDetailsData).length > 0
                ? vm.retiralDetailsData
                : {
                    Eligible_For_Pension: 1,
                  };
            vm.bondRecoveryDetailsData = bondRecoveryDetails
              ? JSON.parse(bondRecoveryDetails)
              : {};
            vm.overtimeDetailsData = overtimeDetails
              ? JSON.parse(overtimeDetails)
              : {};
            vm.taxConfigDetailsData = taxDetails ? JSON.parse(taxDetails) : [];
            vm.taxConfigDetailsData =
              vm.taxConfigDetailsData.length > 0
                ? vm.taxConfigDetailsData[0]
                : {
                    Eligible_For_PT: 1,
                  };
            vm.expatDetailsData = vm.taxConfigDetailsData;
            vm.tdsSectionList = contractDetails
              ? JSON.parse(contractDetails)
              : [];
            vm.validationData = validations ? JSON.parse(validations) : {};
            vm.salaryNotGenerated = vm.validationData.salaryNotGenerated;
            vm.formMoreDetails();
            vm.getEmployeeChanges();
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "", type = "") {
      mixpanel.track("EmpProfile-payConfig-fetch-error");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form:
            type === "label"
              ? "pay configuration label details"
              : "pay configuration details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    getEmployeeChanges() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMPLOYEE_CHANGES,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.selectedEmpId,
            tables: [
              "bond_recovery_details",
              "overtime_details",
              "retiral_details",
              "tax_details",
            ],
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("EmpProfile-employeeChanges-fetch-success");
          if (
            response &&
            response.data &&
            response.data.retrievePendingEmployeeChanges
          ) {
            const { pendingChanges } =
              response.data.retrievePendingEmployeeChanges;
            if (
              pendingChanges &&
              pendingChanges.changes &&
              pendingChanges.changes.length > 0
            ) {
              for (let change of pendingChanges.changes) {
                if (change.Table_Name === "retiral_details") {
                  vm.oldRetiralsData = vm.retiralDetailsData;
                  vm.retiralDetailsData = JSON.parse(change.New_Data);
                } else if (change.Table_Name === "overtime_details") {
                  vm.oldOvertimeData = vm.overtimeDetailsData;
                  vm.overtimeDetailsData = JSON.parse(change.New_Data);
                } else if (change.Table_Name === "bond_recovery_details") {
                  vm.oldBondRecoveryData = vm.bondRecoveryDetailsData;
                  vm.bondRecoveryDetailsData = JSON.parse(change.New_Data);
                } else if (change.Table_Name === "tax_details") {
                  vm.oldTDSContractorData = vm.taxConfigDetailsData;
                  vm.oldExpatDetailsData = vm.expatDetailsData;
                  vm.taxConfigDetailsData = JSON.parse(change.New_Data);
                  vm.expatDetailsData = vm.taxConfigDetailsData;
                }
              }
            }
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
  },
};
</script>
