<template>
  <div
    v-if="
      dependentDetails && dependentDetails.length === 0 && !oldDependentDetails
    "
    class="d-flex align-center justify-center fill-height text-h6 text-grey"
  >
    No dependent details have been added
  </div>
  <v-card
    elevation="3"
    v-for="(depItem, index) in mergedDependents"
    :key="index"
    class="card-item d-flex pa-4 rounded-lg ma-2"
    color="grey-lighten-5"
    :style="
      !isMobileView
        ? `min-width:400px; border-left: 7px solid ${generateRandomColor()};height:auto`
        : `overflow:visible;border-left: 7px solid ${generateRandomColor()}`
    "
  >
    <div class="d-flex flex-column" style="width: 100%">
      <div class="w-100 mt-n7">
        <span>
          <v-card-text class="text-body-1 font-weight-regular">
            <div class="d-flex">
              <div class="d-flex flex-column justify-start">
                <v-tooltip
                  :text="
                    formDependentName(
                      depItem.newDep?.Dependent_First_Name,
                      depItem.newDep?.Dependent_Last_Name
                    ) ||
                    formDependentName(
                      depItem.oldDep?.Dependent_First_Name,
                      depItem.oldDep?.Dependent_Last_Name
                    )
                  "
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-primary font-weight-bold text-h6 text-truncate"
                      :style="
                        isMobileView ? 'max-width: 180px' : 'max-width: 350px'
                      "
                      v-bind="props"
                    >
                      <span
                        v-if="
                          depItem.oldDep &&
                          depItem.newDep &&
                          formDependentName(
                            depItem.oldDep.Dependent_First_Name,
                            depItem.oldDep.Dependent_Last_Name
                          ) !==
                            formDependentName(
                              depItem.newDep.Dependent_First_Name,
                              depItem.newDep.Dependent_Last_Name
                            )
                        "
                        class="text-decoration-line-through text-error mr-1"
                      >
                        {{
                          checkNullValue(
                            formDependentName(
                              depItem.oldDep.Dependent_First_Name,
                              depItem.oldDep.Dependent_Last_Name
                            )
                          )
                        }}
                      </span>
                      <span
                        v-if="depItem.newDep"
                        :class="
                          (depItem.oldDep &&
                            formDependentName(
                              depItem.oldDep.Dependent_First_Name,
                              depItem.oldDep.Dependent_Last_Name
                            ) !==
                              formDependentName(
                                depItem.newDep.Dependent_First_Name,
                                depItem.newDep.Dependent_Last_Name
                              )) ||
                          (!depItem.oldDep && oldDependentDetails)
                            ? 'text-success'
                            : ''
                        "
                      >
                        {{
                          checkNullValue(
                            formDependentName(
                              depItem.newDep.Dependent_First_Name,
                              depItem.newDep.Dependent_Last_Name
                            )
                          )
                        }}
                      </span>
                      <span
                        v-else-if="depItem.oldDep"
                        class="text-error text-decoration-line-through"
                      >
                        {{
                          checkNullValue(
                            formDependentName(
                              depItem.oldDep.Dependent_First_Name,
                              depItem.oldDep.Dependent_Last_Name
                            )
                          )
                        }}
                      </span>
                    </div>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </v-card-text>
        </span>
      </div>
      <div class="d-flex flex-column">
        <div class="card-columns w-100 mt-n6">
          <span
            :style="!isMobileView ? 'width:67%' : 'width:100%'"
            class="d-flex align-start flex-column"
          >
            <v-card-text class="text-body-1 font-weight-regular">
              <div class="mt-2 mr-2 d-flex flex-column justify-start">
                <b class="mr-2 text-grey justify-start">Relationship </b>
                <span class="pb-1 pt-1">
                  <div
                    :style="
                      isMobileView ? 'max-width: 200px' : 'max-width:140px'
                    "
                    class="text-truncate"
                  >
                    <span
                      v-if="
                        depItem.oldDep &&
                        depItem.newDep &&
                        depItem.oldDep.Relationship?.toLowerCase() !==
                          depItem.newDep.Relationship?.toLowerCase()
                      "
                      class="text-decoration-line-through text-error mr-1"
                    >
                      {{ checkNullValue(depItem.oldDep.Relationship) }}
                    </span>
                    <span
                      v-if="depItem.newDep"
                      :class="
                        (depItem.oldDep &&
                          depItem.oldDep.Relationship?.toLowerCase() !==
                            depItem.newDep.Relationship?.toLowerCase()) ||
                        (!depItem.oldDep && oldDependentDetails)
                          ? 'text-success'
                          : ''
                      "
                    >
                      {{ checkNullValue(depItem.newDep.Relationship) }}
                    </span>
                    <span
                      v-else-if="depItem.oldDep"
                      class="text-error text-decoration-line-through"
                    >
                      {{ checkNullValue(depItem.oldDep.Relationship) }}
                    </span>
                  </div>
                </span>
              </div>
              <div class="mt-2 mr-2 d-flex flex-column justify-start">
                <b class="mr-2 text-grey justify-start">Gender </b>
                <span class="pb-1 pt-1">
                  <span
                    v-if="
                      depItem.oldDep &&
                      depItem.newDep &&
                      depItem.oldDep.Gender?.toLowerCase() !==
                        depItem.newDep.Gender?.toLowerCase()
                    "
                    class="text-decoration-line-through text-error mr-1"
                  >
                    {{ checkNullValue(depItem.oldDep.Gender) }}
                  </span>
                  <span
                    v-if="depItem.newDep"
                    :class="
                      (depItem.oldDep &&
                        depItem.oldDep.Gender?.toLowerCase() !==
                          depItem.newDep.Gender?.toLowerCase()) ||
                      (!depItem.oldDep && oldDependentDetails)
                        ? 'text-success'
                        : ''
                    "
                  >
                    {{ checkNullValue(depItem.newDep.Gender) }}
                  </span>
                  <span
                    v-else-if="depItem.oldDep"
                    class="text-error text-decoration-line-through"
                  >
                    {{ checkNullValue(depItem.oldDep.Gender) }}
                  </span>
                </span>
              </div>
            </v-card-text>
          </span>
          <span
            :style="
              !isMobileView
                ? 'width:50%'
                : 'margin-top:-40px !important;margin-bottom: 10px !important;width:100%'
            "
            class="d-flex align-start flex-column"
          >
            <v-card-text class="text-body-1 font-weight-regular">
              <div class="mt-2 mr-2 d-flex flex-column justify-start">
                <b class="mr-2 text-grey justify-start">Date of Birth </b>
                <span class="pb-1 pt-1">
                  <span
                    v-if="
                      depItem.oldDep &&
                      depItem.newDep &&
                      depItem.oldDep.Dependent_DOB !==
                        depItem.newDep.Dependent_DOB
                    "
                    class="text-decoration-line-through text-error mr-1"
                  >
                    {{ formatDate(depItem.oldDep.Dependent_DOB) }}
                  </span>
                  <span
                    v-if="depItem.newDep"
                    :class="
                      (depItem.oldDep &&
                        depItem.oldDep.Dependent_DOB !==
                          depItem.newDep.Dependent_DOB) ||
                      (!depItem.oldDep && oldDependentDetails)
                        ? 'text-success'
                        : ''
                    "
                  >
                    {{ formatDate(depItem.newDep.Dependent_DOB) }}
                  </span>
                  <span
                    v-else-if="depItem.oldDep"
                    class="text-error text-decoration-line-through"
                  >
                    {{ formatDate(depItem.oldDep.Dependent_DOB) }}
                  </span>
                </span>
              </div>
            </v-card-text>
          </span>
        </div>
      </div>
    </div>
    <div v-if="enableEdit" class="mt-n5 ml-auto">
      <ActionMenu
        @selected-action="handleActions($event, index)"
        :accessRights="checkAccess()"
      ></ActionMenu>
    </div>
  </v-card>
</template>

<script>
import moment from "moment";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

export default {
  name: "DependentDetails",
  data() {
    return { havingAccess: {} };
  },
  props: {
    dependentDetails: {
      type: Object,
      required: true,
    },
    oldDependentDetails: {
      type: [Array, Object],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
  },
  emits: ["on-delete", "on-open-edit"],
  components: { ActionMenu },
  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    mergedDependents() {
      const oldDeps = this.oldDependentDetails || [];
      const newDeps = this.dependentDetails || [];

      const idSet = new Set();

      oldDeps.forEach((dep) => {
        if (dep?.Dependent_Id) idSet.add(dep.Dependent_Id);
      });
      newDeps.forEach((dep) => {
        if (dep?.Dependent_Id) idSet.add(dep.Dependent_Id);
      });

      const merged = [];
      Array.from(idSet).map((id) => {
        const oldDep = oldDeps.find((d) => d?.Dependent_Id === id);
        const newDep = newDeps.find((d) => d?.Dependent_Id === id);
        merged.push({ key: id, oldDep, newDep });
      });
      // Handle newDeps with no Dependent_Id
      newDeps.forEach((dep, index) => {
        if (!dep?.Dependent_Id) {
          merged.push({ key: `new-${index}`, oldDep: undefined, newDep: dep });
        }
      });
      return merged;
    },
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
          ? 1
          : 0;
      return this.havingAccess;
    },
    handleActions(action, index) {
      let selectedActionItem =
        this.mergedDependents[index].newDep ||
        this.mergedDependents[index].oldDep;
      if (action === "Delete") {
        this.$emit("on-delete", selectedActionItem);
      } else {
        this.$emit("on-open-edit", selectedActionItem);
      }
    },
    formDependentName(firstName, lastName) {
      if (firstName || lastName) {
        return (firstName || "") + " " + (lastName || "");
      } else return "";
    },
  },
};
</script>

<style scoped>
.text-success {
  color: green;
}
.text-error {
  color: red;
}
.text-decoration-line-through {
  text-decoration: line-through;
}
</style>
