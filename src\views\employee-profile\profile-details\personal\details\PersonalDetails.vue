<template>
  <div class="rounded-lg ma-1" color="#FDFEFF">
    <div v-if="!showEditForm">
      <div class="d-flex align-center justify-space-between">
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="blue"
            :size="22"
            class="mr-2"
          ></v-progress-circular>
          <span class="text-h6 text-grey-darken-1 font-weight-bold"
            >Personal Details</span
          >
        </div>
        <div v-if="enableEdit">
          <v-btn @click="openEditDialog" color="primary" variant="text">
            <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
          </v-btn>
        </div>
      </div>

      <v-row class="pa-4 ma-2 card-blue-background">
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Employee Id"
          :newValue="personalDetails.User_Defined_EmpId"
          :oldValue="oldPersonalDetailsData?.User_Defined_EmpId"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[474]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[474].Field_Alias || 'Biometric Integration Id'"
          :newValue="personalDetails.External_EmpId"
          :oldValue="oldPersonalDetailsData?.External_EmpId"
        />
        <FieldDiff
          v-if="labelList[483]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          :label="labelList[483]?.Field_Alias || 'Salutation'"
          :newValue="personalDetails.Salutation"
          :oldValue="oldPersonalDetailsData?.Salutation"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="First Name"
          :newValue="personalDetails.Emp_First_Name"
          :oldValue="oldPersonalDetailsData?.Emp_First_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Middle Name"
          :newValue="personalDetails.Emp_Middle_Name"
          :oldValue="oldPersonalDetailsData?.Emp_Middle_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Last Name"
          :newValue="personalDetails.Emp_Last_Name"
          :oldValue="oldPersonalDetailsData?.Emp_Last_Name"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[487]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[487]?.Field_Alias || 'Custom Field 1'"
          :newValue="personalDetails.Custom_Field_1"
          :oldValue="oldPersonalDetailsData?.Custom_Field_1"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[488]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[488]?.Field_Alias || 'Custom Field 2'"
          :newValue="personalDetails.Custom_Field_2"
          :oldValue="oldPersonalDetailsData?.Custom_Field_2"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[489]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[489]?.Field_Alias || 'Custom Field 3'"
          :newValue="personalDetails.Custom_Field_3"
          :oldValue="oldPersonalDetailsData?.Custom_Field_3"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Suffix"
          :newValue="personalDetails.Appellation"
          :oldValue="oldPersonalDetailsData?.Appellation"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Gender"
          :newValue="personalDetails.Gender"
          :oldValue="oldPersonalDetailsData?.Gender"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[348]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[348].Field_Alias || 'Gender Expression'"
          :newValue="personalDetails.Gender_Expression"
          :oldValue="oldPersonalDetailsData?.Gender_Expression"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[347]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[347].Field_Alias || 'Gender Identity'"
          :newValue="personalDetails.Gender_Identity"
          :oldValue="oldPersonalDetailsData?.Gender_Identity"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[207].Field_Visiblity.toLowerCase() == 'yes'"
          :label="labelList[207].Field_Alias || 'Pronoun'"
          :newValue="personalDetails.Pronoun"
          :oldValue="oldPersonalDetailsData?.Pronoun"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[208].Field_Visiblity.toLowerCase() == 'yes'"
          :label="labelList[208].Field_Alias || 'Gender Orientations'"
          :newValue="personalDetails.Gender_Orientations"
          :oldValue="oldPersonalDetailsData?.Gender_Orientations"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[399]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[399]?.Field_Alias || 'Marital Status'"
          :newValue="personalDetails.maritalStatusName"
          :oldValue="oldPersonalDetailsData?.maritalStatusName"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Date of Birth"
          :newValue="formatDate(personalDetails.DOB)"
          :oldValue="
            oldPersonalDetailsData
              ? formatDate(oldPersonalDetailsData?.DOB)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[401]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[401]?.Field_Alias || 'Blood Group'"
          :newValue="personalDetails.Blood_Group"
          :oldValue="oldPersonalDetailsData?.Blood_Group"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Nationality"
          :newValue="personalDetails.Nationality"
          :oldValue="oldPersonalDetailsData?.Nationality"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList['212']?.Field_Visiblity.toLowerCase() === 'yes'"
          :label="labelList[212].Field_Alias || 'Aadhaar Card Number'"
          :newValue="personalDetails.Aadhaar_Card_Number"
          :oldValue="oldPersonalDetailsData?.Aadhaar_Card_Number"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList['213']?.Field_Visiblity.toLowerCase() === 'yes'"
          :label="labelList[213].Field_Alias || 'PAN'"
          :newValue="personalDetails.PAN"
          :oldValue="oldPersonalDetailsData?.PAN"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Personal Email"
          :newValue="personalDetails.Personal_Email"
          :oldValue="oldPersonalDetailsData?.Personal_Email"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList['214']?.Field_Visiblity.toLowerCase() === 'yes'"
          :label="labelList[214].Field_Alias || 'UAN'"
          :newValue="personalDetails.UAN"
          :oldValue="oldPersonalDetailsData?.UAN"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList['188']?.Field_Visiblity.toLowerCase() === 'yes'"
          :label="labelList[188].Field_Alias || 'Tax Code'"
          :newValue="personalDetails.Tax_Code"
          :oldValue="oldPersonalDetailsData?.Tax_Code"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Manager"
          :newValue="parseInt(personalDetails.Is_Manager) ? 'Yes' : 'No'"
          :oldValue="
            oldPersonalDetailsData
              ? parseInt(oldPersonalDetailsData?.Is_Manager)
                ? 'Yes'
                : 'No'
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Recruiter"
          :newValue="personalDetails.Is_Recruiter"
          :oldValue="oldPersonalDetailsData?.Is_Recruiter"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Allow User Sign In"
          :newValue="parseInt(personalDetails.Allow_User_Signin) ? 'Yes' : 'No'"
          :oldValue="
            oldPersonalDetailsData
              ? parseInt(oldPersonalDetailsData?.Allow_User_Signin)
                ? 'Yes'
                : 'No'
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[295]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[295].Field_Alias || 'Place Of Birth'"
          :newValue="personalDetails.Place_Of_Birth"
          :oldValue="oldPersonalDetailsData?.Place_Of_Birth"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[232]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[232].Field_Alias || 'Preferred First Name'"
          :newValue="personalDetails.Emp_Pref_First_Name"
          :oldValue="oldPersonalDetailsData?.Emp_Pref_First_Name"
        />

        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[380]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[380]?.Field_Alias || 'Languages'"
          :newValue="
            personalDetails.Languages && personalDetails.Languages.length > 0
              ? formLanguagesNames(personalDetails.Languages)
              : '-'
          "
          :oldValue="
            oldPersonalDetailsData
              ? oldPersonalDetailsData?.Languages.length > 0
                ? formLanguagesNames(oldPersonalDetailsData.Languages)
                : '-'
              : null
          "
        />

        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Hobbies"
          :newValue="personalDetails.Hobbies"
          :oldValue="oldPersonalDetailsData?.Hobbies"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[242]?.Field_Visiblity.toLowerCase() == 'yes'"
          :label="labelList[242].Field_Alias || 'Caste'"
          :newValue="personalDetails.Caste"
          :oldValue="oldPersonalDetailsData?.Caste"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[276]?.Field_Visiblity.toLowerCase() == 'yes'"
          :label="labelList[276].Field_Alias || 'Ethnic Race'"
          :newValue="personalDetails.Ethnic_Race"
          :oldValue="oldPersonalDetailsData?.Ethnic_Race"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[296]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[296].Field_Alias || 'Religion'"
          :newValue="personalDetails.Religion"
          :oldValue="oldPersonalDetailsData?.Religion"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList[382]?.Field_Visiblity?.toLowerCase() === 'yes'"
          :label="labelList[382]?.Field_Alias || 'Physically Challenged'"
          :newValue="
            parseInt(personalDetails.Physically_Challenged) ? 'Yes' : 'No'
          "
          :oldValue="
            oldPersonalDetailsData
              ? parseInt(oldPersonalDetailsData?.Physically_Challenged)
                ? 'Yes'
                : 'No'
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Military Service"
          :newValue="parseInt(personalDetails.Military_Service) ? 'Yes' : 'No'"
          :oldValue="
            oldPersonalDetailsData
              ? parseInt(oldPersonalDetailsData?.Military_Service)
                ? 'Yes'
                : 'No'
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          label="Smoker"
          :newValue="parseInt(personalDetails.Smoker) ? 'Yes' : 'No'"
          :oldValue="
            oldPersonalDetailsData
              ? parseInt(oldPersonalDetailsData?.Smoker)
                ? 'Yes'
                : 'No'
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="parseInt(personalDetails.Smoker)"
          label="Smoker As Of"
          :newValue="formatDate(personalDetails.Smokerasof)"
          :oldValue="
            oldPersonalDetailsData
              ? formatDate(oldPersonalDetailsData?.Smokerasof)
              : null
          "
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList['223'] && labelList['223'].Field_Visiblity === 'Yes'"
          :label="labelList[223].Field_Alias || 'Statutory Insurance Number'"
          :newValue="personalDetails.Statutory_Insurance_Number"
          :oldValue="oldPersonalDetailsData?.Statutory_Insurance_Number"
        />
        <FieldDiff
          :oldDataAvailable="oldPersonalDetailsData ? true : false"
          v-if="labelList['224'] && labelList['224'].Field_Visiblity === 'Yes'"
          :label="labelList[224].Field_Alias || 'PRAN No'"
          :newValue="personalDetails.PRAN_No"
          :oldValue="oldPersonalDetailsData?.PRAN_No"
        />
      </v-row>
      <div
        class="d-flex align-center justify-space-between mb-2"
        v-if="labelList[375]?.Field_Visiblity?.toLowerCase() === 'yes'"
      >
        <div class="d-flex align-center">
          <v-progress-circular
            model-value="100"
            color="danger"
            :size="18"
            class="mx-2"
          ></v-progress-circular>
          <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
            >Language(s) Known</span
          >
        </div>
      </div>
      <Language
        v-if="labelList[375]?.Field_Visiblity?.toLowerCase() === 'yes'"
        :personalDetails="personalDetails"
        :oldPersonalDetails="oldPersonalDetailsData || {}"
        :generateRandomColor="generateRandomColor"
        :checkNullValue="checkNullValue"
      />
    </div>
    <div v-else>
      <EditPersonalDetails
        ref="editPersonalDetails"
        :personalDetails="personalDetails"
        :actionType="actionType"
        :selectedEmpStatus="selectedEmpStatus"
        :callingFrom="callingFrom"
        :selectedEmpId="selectedEmpId"
        @edit-updated="editUpdated($event)"
        @close-edit-form="closeEditForm"
        @close-add-form="$emit('close-add-form')"
      >
      </EditPersonalDetails>
    </div>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
// components
const EditPersonalDetails = defineAsyncComponent(() =>
  import("./EditPersonalDetails.vue")
);
const FieldDiff = defineAsyncComponent(() =>
  import("@/components/custom-components/FieldDiff.vue")
);
const Language = defineAsyncComponent(() => import("./Language.vue"));
import { checkNullValue, generateRandomColor } from "@/helper";
import moment from "moment";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "PersonalDetails",
  components: {
    EditPersonalDetails,
    FieldDiff,
    Language,
  },
  props: {
    personalDetailsData: {
      type: [Array, Object],
      required: true,
    },
    oldPersonalDetailsData: {
      type: [Array, Object],
      required: false,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    empFormUpdateAccess: {
      type: Boolean,
      default: false,
    },
    actionType: {
      type: String,
      default: "",
    },
    selectedEmpStatus: {
      type: String,
      default: "Active",
    },
    callingFrom: {
      type: String,
      default: "",
    },
    selectedEmpId: {
      type: Number,
      default: 0,
    },
  },
  emits: [
    "refetch-personal-details",
    "edit-opened",
    "edit-closed",
    "close-add-form",
  ],
  data() {
    return {
      showEditForm: false,
      personalDetails: {
        Employee_Id: 0,
        User_Defined_EmpId: "",
        Salutation: null,
        Emp_First_Name: "",
        Emp_Middle_Name: "",
        Emp_Last_Name: "",
        Emp_Pref_First_Name: "",
        Gender: null,
        DOB: null,
        Place_Of_Birth: "",
        Marital_Status: null,
        Blood_Group: null,
        Military_Service: 0,
        Statutory_Insurance_Number: null,
        PRAN_No: null,
        Nationality: "",
        Personal_Email: "",
        Religion: "",
        Caste: "",
        Ethnic_Race: "",
        Is_Manager: 0,
        Physically_Challenged: 0,
        Smoker: 0,
        Smokerasof: null,
        Aadhaar_Card_Number: "",
        PAN: "",
        UAN: "",
        Allow_User_Signin: 0,
        Enable_Sign_In_With_Mobile_No: 0,
        Sign_In_Mobile_Number: "",
        Work_Email: "",
        Sign_In_Mobile_No_Country_Code: "",
        External_EmpId: "",
        Languages: [],
        Hobbies: "",
        Form_Status: "",
        Photo_Path: "",
      },
    };
  },

  computed: {
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    enableEdit() {
      return (
        this.empFormUpdateAccess ||
        (this.formAccess &&
          this.formAccess.update &&
          this.formAccess.admin === "admin")
      );
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  watch: {
    showEditForm(val) {
      let editFormOpened =
        this.$store.state.employeeProfile.isEditFormOpenedCount;
      let eCount = 0;
      if (editFormOpened && editFormOpened.includes("-")) {
        eCount = editFormOpened.split("-");
        eCount = eCount[0];
        eCount = parseInt(eCount) + 1;
      }
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        eCount + "-" + val
      );
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.personalDetailsData && this.personalDetailsData.length > 0) {
      this.personalDetails = this.personalDetailsData[0];
    } else {
      this.personalDetails = this.personalDetailsData;
    }
    if (this.actionType === "add" && !this.selectedEmpId) {
      this.showEditForm = true;
    }
  },

  methods: {
    checkNullValue,
    generateRandomColor,
    formLanguagesNames(languages) {
      return languages.map((el) => el.Language_Name).join(", ");
    },
    editUpdated(empId) {
      mixpanel.track("EmpProfile-personalDetails-edit-updated");
      this.showEditForm = false;
      this.$emit("refetch-personal-details", empId);
    },
    openEditDialog() {
      mixpanel.track("EmpProfile-personalDetails-edit-opened");
      this.showEditForm = true;
      this.$emit("edit-opened");
    },
    closeEditForm() {
      mixpanel.track("EmpProfile-personalDetails-edit-closed");
      this.showEditForm = false;
      this.$emit("edit-closed");
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
