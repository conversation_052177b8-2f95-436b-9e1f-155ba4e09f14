<template>
  <v-card rounded="lg">
    <!-- Main Layout: Chart + Controls -->
    <div
      class="d-flex"
      :class="{ 'flex-column-reverse': windowWidth < 600 }"
      style="height: 100%"
    >
      <!-- Chart Container -->
      <div
        class="flex-grow-1"
        style="position: relative"
        :style="{ width: windowWidth < 600 ? '100%' : 'calc(100% - 64px)' }"
      >
        <div
          ref="chartContainer"
          class="d3-org-chart"
          :style="{ height: chartHeight + 'px', width: '100%' }"
        ></div>
      </div>

      <!-- Chart Controls - Right Side -->
      <div
        class="chart-controls-panel pa-3"
        :class="windowWidth < 600 ? '' : 'flex-column'"
      >
        <!-- Chart Action Controls -->
        <v-tooltip bottom>
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              elevation="3"
              color="primary"
              size="small"
              icon
              class="mb-1"
              @click="fitChart"
            >
              <v-icon size="18">fas fa-compress-alt</v-icon>
            </v-btn>
          </template>
          <span>Fit to Screen</span>
        </v-tooltip>

        <v-tooltip bottom>
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              elevation="3"
              color="primary"
              size="small"
              icon
              class="mb-1"
              @click="expandAll"
            >
              <v-icon size="18">fas fa-expand-arrows-alt</v-icon>
            </v-btn>
          </template>
          <span>Expand All</span>
        </v-tooltip>

        <v-tooltip bottom>
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              elevation="3"
              color="primary"
              size="small"
              icon
              class="mb-1"
              @click="collapseAll"
            >
              <v-icon size="18">fas fa-compress-arrows-alt</v-icon>
            </v-btn>
          </template>
          <span>Collapse All</span>
        </v-tooltip>

        <v-tooltip bottom>
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              elevation="3"
              color="primary"
              size="small"
              icon
              class="mb-1"
              @click="zoomIn"
            >
              <v-icon size="18">fas fa-plus</v-icon>
            </v-btn>
          </template>
          <span>Zoom In</span>
        </v-tooltip>

        <v-tooltip bottom>
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              elevation="3"
              color="primary"
              size="small"
              icon
              class="mb-1"
              @click="zoomOut"
            >
              <v-icon size="18">fas fa-minus</v-icon>
            </v-btn>
          </template>
          <span>Zoom Out</span>
        </v-tooltip>

        <v-tooltip bottom>
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              elevation="3"
              color="primary"
              size="small"
              icon
              class="mb-1"
              @click="flip"
            >
              <v-icon size="18">fas fa-exchange-alt</v-icon>
            </v-btn>
          </template>
          <span>Flip</span>
        </v-tooltip>

        <v-tooltip bottom>
          <template v-slot:activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              elevation="3"
              color="primary"
              size="small"
              icon
              class="mb-1"
            >
              <v-icon size="18">fas fa-file-export</v-icon>
              <v-menu activator="parent">
                <v-list class="pa-1">
                  <v-hover v-for="format in exportFormats" :key="format.value">
                    <template v-slot:default="{ isHovering, props }">
                      <div
                        v-bind="props"
                        class="pa-1 my-2 rounded-lg cursor-pointer text-subtitle-2"
                        style="min-width: 70px"
                        :class="{
                          'bg-hover': isHovering,
                          'bg-grey-lighten-4 text-primary': !isHovering,
                        }"
                        @click="exportChart(format.value)"
                      >
                        {{ format.label }}
                      </div>
                    </template>
                  </v-hover>
                </v-list>
              </v-menu>
            </v-btn>
          </template>
          <span>Export</span>
        </v-tooltip>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="chart-loading">
      <v-progress-circular
        indeterminate
        color="primary"
        size="64"
      ></v-progress-circular>
      <p class="loading-text">Loading organization chart...</p>
    </div>

    <!-- Error State -->
    <div v-if="hasError" class="chart-error">
      <v-icon size="48" color="error">mdi-alert-circle</v-icon>
      <p class="error-text">Failed to load organization chart</p>
      <v-btn variant="outlined" @click="$emit('retry')">
        <v-icon size="16">mdi-refresh</v-icon>
        Retry
      </v-btn>
    </div>
  </v-card>
</template>

<script>
import {
  ref,
  onMounted,
  onUnmounted,
  watch,
  createApp,
  computed,
  getCurrentInstance,
} from "vue";
import { OrgChart } from "d3-org-chart";
import EmployeeCard from "./EmployeeCard.vue";
import store from "@/store";
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "D3OrgChart",
  mixins: [FileExportMixin],
  props: {
    organizationData: {
      type: Array,
      required: true,
      default: () => [],
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    hasError: {
      type: Boolean,
      default: false,
    },
    chartHeight: {
      type: Number,
      default: 600,
    },
    serviceProviderId: {
      type: Number,
      default: null,
    },
    serviceProviderName: {
      type: String,
      default: "",
    },
  },
  emits: ["node-click", "retry"],
  methods: {
    // Excel export method using the mixin
    exportToExcel() {
      if (!this.organizationData || this.organizationData.length === 0) {
        return;
      }

      const exportHeaders = [
        { header: "Employee Id", key: "id" },
        { header: "Employee Name", key: "name" },
        { header: "Department", key: "department" },
        { header: "Designation", key: "designation" },
        { header: "Location", key: "location" },
        { header: "Is Manager", key: "manager" },
        { header: "Gender", key: "gender" },
        { header: "Reportees", key: "_totalSubordinates" },
      ];

      const modifiedData = this.organizationData
        .filter((employee) => {
          return employee.id !== "0";
        })
        .map((employee) => {
          return {
            id: employee.id,
            name: employee.name,
            department: employee.department,
            designation: employee.designation,
            location: employee.location,
            manager: employee.isManager ? "Yes" : "No",
            gender: employee.gender,
            _totalSubordinates: employee._totalSubordinates,
          };
        });

      const exportOptions = {
        fileExportData: modifiedData,
        fileName: "Organization Chart",
        sheetName: "Organization Chart",
        header: exportHeaders,
      };

      // Use the mixin method
      this.exportExcelFile(exportOptions);
    },
  },
  setup(props, { emit }) {
    const instance = getCurrentInstance();
    const windowWidth = computed(() => {
      return store.state.windowWidth;
    });
    const chartContainer = ref(null);
    const chart = ref(null);
    const zoomLevel = ref(1);
    const isFlipped = ref(false);

    // Export format options
    const exportFormats = ref([
      { value: "png", label: "PNG" },
      { value: "svg", label: "SVG" },
      { value: "pdf", label: "PDF" },
      { value: "excel", label: "EXCEL" },
    ]);

    // Initialize the organization chart
    const initChart = () => {
      if (!chartContainer.value || props.organizationData.length === 0) {
        return;
      }

      try {
        // Clear any existing chart
        if (chart.value && chartContainer.value) {
          chartContainer.value.innerHTML = "";
        }

        // Create new chart instance
        chart.value = new OrgChart()
          .container(chartContainer.value)
          .data(props.organizationData)
          .nodeWidth(() => 250)
          .nodeHeight(() => 120)
          .childrenMargin(() => 50)
          .compactMarginBetween(() => 35)
          .compactMarginPair(() => 30)
          .neighbourMargin(() => 20)
          .siblingsMargin(() => 20)
          .layout(isFlipped.value ? "left" : "top") // Set layout based on current state
          .nodeContent((d) => {
            // Create a temporary Vue app to render the EmployeeCard component
            const app = createApp(EmployeeCard, {
              employeeData: d.data,
              serviceProviderId: props.serviceProviderId,
              serviceProviderName: props.serviceProviderName,
            });
            const vm = app.mount(document.createElement("div"));
            const html = vm.$el.outerHTML;
            app.unmount();
            return html;
          })
          .onNodeClick((nodeId) => {
            emit("node-click", nodeId);
          })
          .onExpandOrCollapse(() => {
            // Preserve layout when nodes are expanded or collapsed
            ensureLayoutConsistency();
          })
          .render();

        // Update zoom level
        updateZoomLevel();
      } catch (error) {
        // Provide more specific error messages
        let errorMessage =
          "Failed to load organization chart. Please try after some time.";
        if (error.message && error.message.includes("cycle")) {
          errorMessage =
            "Circular reference detected in organization structure. Please contact your administrator.";
        } else if (error.message && error.message.includes("data")) {
          errorMessage =
            "Invalid organization data format. Please refresh the page.";
        }

        let snackbarData = {
          isOpen: true,
          message: errorMessage,
          type: "warning",
        };
        store.commit("OPEN_SNACKBAR", snackbarData);
      }
    };

    // Update zoom level from chart
    const updateZoomLevel = () => {
      if (chart.value && chart.value.getChartState) {
        const state = chart.value.getChartState();
        zoomLevel.value = state.scale || 1;
      }
    };

    // Helper function to ensure layout consistency
    const ensureLayoutConsistency = () => {
      if (chart.value) {
        chart.value.layout(isFlipped.value ? "left" : "top");
      }
    };

    // Chart control methods
    const fitChart = () => {
      if (chart.value) {
        ensureLayoutConsistency();
        chart.value.fit();
        updateZoomLevel();
      }
    };

    const expandAll = () => {
      if (chart.value) {
        chart.value.expandAll();
        // Preserve layout after expanding all nodes
        ensureLayoutConsistency();
        chart.value.render();
        updateZoomLevel();
      }
    };

    const collapseAll = () => {
      if (chart.value) {
        chart.value.collapseAll();
        // Preserve layout after collapsing all nodes
        ensureLayoutConsistency();
        chart.value.render();
        updateZoomLevel();
      }
    };

    const zoomIn = () => {
      if (chart.value) {
        chart.value.zoomIn();
        updateZoomLevel();
      }
    };

    const zoomOut = () => {
      if (chart.value) {
        chart.value.zoomOut();
        updateZoomLevel();
      }
    };

    const flip = () => {
      if (chart.value) {
        isFlipped.value = !isFlipped.value;
        ensureLayoutConsistency();
        chart.value.render().fit();
        updateZoomLevel();
      }
    };

    const exportChart = (format) => {
      if (chart.value) {
        if (format?.toLowerCase() === "png") {
          chart.value.exportImg({ full: true });
        } else if (format?.toLowerCase() === "pdf") {
          downloadPdf();
        } else if (format?.toLowerCase() === "svg") {
          chart.value.exportSvg({ full: true });
        } else if (format?.toLowerCase() === "excel") {
          // Call the methods version using the instance
          instance.ctx.exportToExcel();
        }
      }
    };

    const downloadPdf = () => {
      if (!chart.value) return;

      chart.value.exportImg({
        save: false,
        full: true,
        onLoad: (base64) => {
          try {
            // Import jsPDF dynamically if not already imported
            import("jspdf")
              .then(({ jsPDF }) => {
                const pdf = new jsPDF();
                const img = new Image();

                img.onload = function () {
                  // Calculate dimensions to fit the page properly
                  const pdfWidth = pdf.internal.pageSize.getWidth();
                  const pdfHeight = pdf.internal.pageSize.getHeight();
                  const margin = 10;
                  const maxWidth = pdfWidth - margin * 2;
                  const maxHeight = pdfHeight - margin * 2;

                  // Calculate scaled dimensions maintaining aspect ratio
                  const imgAspectRatio = img.width / img.height;
                  let scaledWidth = maxWidth;
                  let scaledHeight = maxWidth / imgAspectRatio;

                  if (scaledHeight > maxHeight) {
                    scaledHeight = maxHeight;
                    scaledWidth = maxHeight * imgAspectRatio;
                  }

                  // Center the image
                  const x = (pdfWidth - scaledWidth) / 2;
                  const y = (pdfHeight - scaledHeight) / 2;

                  pdf.addImage(img, "JPEG", x, y, scaledWidth, scaledHeight);

                  // Generate filename with timestamp
                  const timestamp = new Date()
                    .toISOString()
                    .slice(0, 19)
                    .replace(/:/g, "-");
                  pdf.save(`organization-chart-${timestamp}.pdf`);
                };

                img.onerror = function () {
                  pdfError();
                };

                img.src = base64;
              })
              .catch(() => {
                pdfError();
              });
          } catch (error) {
            pdfError();
          }
        },
      });
    };
    const pdfError = () => {
      let snackbarData = {
        isOpen: true,
        message: "Failed to generate PDF. Please try after some time.",
        type: "warning",
      };
      store.commit("OPEN_SNACKBAR", snackbarData);
    };

    // Watch for data changes
    watch(
      () => props.organizationData,
      (newData) => {
        if (newData && newData.length > 0) {
          initChart();
        }
      },
      { deep: true }
    );

    // Lifecycle hooks
    onMounted(() => {
      if (props.organizationData.length > 0) {
        initChart();
      }
    });

    onUnmounted(() => {
      if (chart.value && chartContainer.value) {
        chartContainer.value.innerHTML = "";
        chart.value = null;
      }
    });

    return {
      chartContainer,
      zoomLevel,
      fitChart,
      expandAll,
      collapseAll,
      zoomIn,
      zoomOut,
      flip,
      exportChart,
      windowWidth,
      exportFormats,
    };
  },
};
</script>

<style scoped>
.d3-org-chart {
  width: 100%;
  overflow: auto;
  background: #f8f9fa;
  position: relative;
}

/* Chart Controls Panel */
.chart-controls-panel {
  background: #ffffff;
  border-left: 1px solid #e9ecef;
  display: flex;
}

.chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  z-index: 10;
}

.loading-text {
  font-size: 16px;
  color: #5f6368;
  margin: 0;
}

.chart-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  z-index: 10;
}

.error-text {
  font-size: 16px;
  color: #d32f2f;
  margin: 0;
}

/* D3 chart specific styles */
:deep(.node) {
  cursor: pointer;
}

:deep(.node-button-g) {
  cursor: pointer;
}

:deep(.link) {
  stroke: #ccc;
  stroke-width: 2px;
  fill: none;
}
</style>
