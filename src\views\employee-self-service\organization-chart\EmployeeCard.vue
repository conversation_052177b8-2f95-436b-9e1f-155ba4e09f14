<template>
  <div class="employee-card">
    <div
      style="
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 12px;
        border: 2px solid #e9ecef;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      "
    ></div>
    <div
      :style="{
        position: 'relative',
        padding: '16px',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
      }"
    >
      <div
        :style="{
          display: 'flex',
          alignItems: 'flex-start',
          gap: '12px',
        }"
      >
        <!-- Organization Icon or Employee Avatar -->
        <div :style="{ flexShrink: 0 }">
          <div
            v-if="employeeData.org"
            :style="{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, #4285f4 0%, #34a853 100%)',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '18px',
            }"
          >
            <!-- Use inline SVG instead of icon font for better export compatibility -->
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 384 512"
              width="18"
              height="18"
              fill="white"
            >
              <path
                d="M48 0C21.5 0 0 21.5 0 48L0 464c0 26.5 21.5 48 48 48l96 0 0-80c0-26.5 21.5-48 48-48s48 21.5 48 48l0 80 96 0c26.5 0 48-21.5 48-48l0-416c0-26.5-21.5-48-48-48L48 0zM64 240c0-8.8 7.2-16 16-16l32 0c8.8 0 16 7.2 16 16l0 32c0 8.8-7.2 16-16 16l-32 0c-8.8 0-16-7.2-16-16l0-32zm112-16l32 0c8.8 0 16 7.2 16 16l0 32c0 8.8-7.2 16-16 16l-32 0c-8.8 0-16-7.2-16-16l0-32c0-8.8 7.2-16 16-16zm80 16c0-8.8 7.2-16 16-16l32 0c8.8 0 16 7.2 16 16l0 32c0 8.8-7.2 16-16 16l-32 0c-8.8 0-16-7.2-16-16l0-32zM80 96l32 0c8.8 0 16 7.2 16 16l0 32c0 8.8-7.2 16-16 16l-32 0c-8.8 0-16-7.2-16-16l0-32c0-8.8 7.2-16 16-16zm80 16c0-8.8 7.2-16 16-16l32 0c8.8 0 16 7.2 16 16l0 32c0 8.8-7.2 16-16 16l-32 0c-8.8 0-16-7.2-16-16l0-32zM272 96l32 0c8.8 0 16 7.2 16 16l0 32c0 8.8-7.2 16-16 16l-32 0c-8.8 0-16-7.2-16-16l0-32c0-8.8 7.2-16 16-16z"
              />
            </svg>
          </div>
          <div
            v-else
            :style="{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              overflow: 'hidden',
              border: '2px solid #e9ecef',
            }"
          >
            <div
              :style="{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '14px',
                fontWeight: '600',
                color: 'white',
                borderRadius: '50%',
                background: getRandomColorClass(),
              }"
            >
              {{ getEmployeeInitials() }}
            </div>
          </div>
        </div>

        <!-- Employee Info -->
        <div
          :style="{
            flex: 1,
            minWidth: 0,
          }"
        >
          <div
            :style="{
              height: '4px',
              borderRadius: '2px',
              marginBottom: '8px',
              width: '100%',
              background: getStatusBarClass(),
            }"
          ></div>
          <div
            :style="{
              fontSize: '14px',
              fontWeight: '600',
              color: '#202124',
              marginBottom: '4px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }"
          >
            {{ employeeData.name }}
          </div>
          <div
            :style="{
              fontSize: '12px',
              color: '#5f6368',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }"
          >
            {{ getEmployeeTitle }}
          </div>
        </div>
      </div>

      <!-- Employee Stats -->
      <div
        style="display: flex; align-items: center; margin-top: 8px"
        :style="{
          justifyContent: employeeData.isManager ? 'space-between' : 'flex-end',
        }"
      >
        <div
          v-if="employeeData.isManager"
          style="
            display: flex;
            align-items: center;
            gap: 4px;
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            color: #e65100;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
          "
        >
          <!-- Crown SVG icon for better export compatibility -->
          <svg
            width="13"
            height="13"
            viewBox="0 0 24 24"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5 16L3 6l5.5 4L12 4l3.5 6L21 6l-2 10H5zm2.7-2h8.6l.9-4.4L14 12l-2-4-2 4-3.2-2.4L7.7 14z"
            />
          </svg>
          <span>Manager</span>
        </div>
        <div
          v-if="employeeData._totalSubordinates > 0"
          style="
            display: flex;
            align-items: center;
            gap: 4px;
            color: #5f6368;
            font-size: 11px;
          "
        >
          <!-- Team/People SVG icon for better export compatibility -->
          <svg
            xmlns="http://www.w3.org/2000/svg"
            version="1.1"
            width="11"
            height="11"
            viewBox="0 0 1024 1024"
          >
            <g id="icomoon-ignore"></g>
            <path
              d="M731 87.8c-65 16.6-108.4 65-115.8 129.2l-1.4 12.2 15.8 10.2c37 24.2 61 62 71 111.8l4 20.4 14.4 4.2c22 6.6 38.4 8.6 58 7.4 36.6-2.2 68.4-16.6 95.2-43.2 19-18.8 29.2-35.2 37.4-59.6 4.4-13.4 5.2-18.8 5-42.4 0-32-3.2-47.8-14.6-71-18.8-38.8-55.2-68.2-97.4-79-19.6-5.2-51.8-5.2-71.6-0.2zM224 92.8c-58.4 13-102.8 54.4-117.4 109.2-4.6 17.6-4.4 50.6 0.2 68 6.4 23.4 20 46.8 38 65 9 9 21.6 19.8 28.2 24 15.2 10 42.2 20.2 61.2 23.2 27.6 4.2 66.6-1.2 89-12.4l9.6-4.8 3.2-17c7.2-35.8 25.2-67.8 52.2-92.6l16.2-14.8-2.2-18c-3.8-30.8-13-53-31.8-77.2-19-24.2-53.2-45.4-83.8-52.2-16.8-3.8-46.8-3.8-62.6-0.4zM497.4 238.2c-88.8 12-148.6 94.4-127.6 175.8 12.2 47.6 44 87.8 85.2 108.2 22.8 11.2 37.8 14.6 64 14.6 41.4 0 73.6-13.4 104.2-43.8 15.4-15.4 19.6-20.8 27.4-37 12.4-25.4 16.4-45.4 14.8-75-2.6-44-17-75.4-48.4-104.6-31.6-29.6-76.8-44-119.6-38.2zM174.6 392.2c-85.4 11-156.4 75.2-171.8 155.2-4 20.8-4.2 192-0.2 197.4 5.4 7.4 111.8 30.4 171.4 37 20.4 2.4 39.8 4.6 43.2 5.2l6.4 1 1-40.4c0.8-35.8 1.6-42.6 6-58.2 13.6-46.6 30-77.2 56.8-105.6 24.6-26 55.2-46.8 85.6-58.4 8.8-3.2 16.4-6.4 16.8-6.8 0.6-0.4-3.2-5-8.2-10.2-19.4-20-35.6-47-42.2-70.4-2.6-8.8-7.4-39.4-7.4-46.4 0-2-141.4-1.6-157.4 0.6zM703.2 405.6c-3 37.6-17.6 71.8-41.6 97.4l-13.8 14.8 6.6 3.6c3.6 2 13.8 7 22.6 11.2 20.8 10 51.2 30.4 65.2 43.8 21.4 20.6 42 54.6 52.4 86.6 8.4 25.4 11.4 48 11.4 84.2 0 18 0.4 32.8 1 32.8 6.6 0 85.2-11 101-14.2 35.8-7.2 78-20.8 104-33.4l11-5.4 0.6-74c0.4-47.6-0.2-79.8-1.6-90-9.8-68.8-60.4-136-119.6-158.8-30.8-11.8-38.4-12.6-121.6-13.6l-76.2-1-1.4 16zM429 546.2c-45.6 6.8-99.2 36.4-126.4 70.2-11.4 14.2-26.2 40.8-31.6 56.6-8.2 24.6-9.2 34.6-11 116-1 44-2.4 86-3 93.2l-1.4 13 11.8 3.4c16.4 4.8 97.6 24.4 121.6 29.2 48 9.6 60.4 10.8 127 11.6 107.8 1.6 140.4-2.8 224-29.8 13.8-4.4 25.4-8.4 26-9 5.4-5 10.4-157.4 6-189-3-23.4-10.2-46.2-20.2-65.8-22.4-43.2-74-82.6-126-96-15.8-4.2-21.8-4.4-100.8-5-46.2-0.2-89.4 0.4-96 1.4z"
            />
          </svg>
          <span>{{ employeeData._totalSubordinates || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "EmployeeCard",
  props: {
    employeeData: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    serviceProviderId: {
      type: Number,
      default: null,
    },
    serviceProviderName: {
      type: String,
      default: "",
    },
  },
  computed: {
    getEmployeeTitle() {
      if (this.employeeData.id == "0") {
        if (this.serviceProviderId) return this.serviceProviderName;
        return "Organization";
      }
      return this.employeeData.designation || "";
    },
  },
  methods: {
    getEmployeeInitials() {
      if (!this.employeeData.name) return "??";

      const nameParts = this.employeeData.name.trim().split(" ");
      if (nameParts.length === 1) {
        // Single name - take first two characters
        return nameParts[0].substring(0, 2).toUpperCase();
      } else {
        // Multiple names - take first character of first and last name
        const firstInitial = nameParts[0].charAt(0);
        const lastInitial = nameParts[nameParts.length - 1].charAt(0);
        return (firstInitial + lastInitial).toUpperCase();
      }
    },

    getRandomColorClass() {
      // Generate a consistent random color based on employee ID or name
      const seed = this.employeeData.id || this.employeeData.name || "default";
      const colors = [
        "linear-gradient(135deg, #4285f4 0%, #1a73e8 100%)",
        "linear-gradient(135deg, #ea4335 0%, #d33b2c 100%)",
        "linear-gradient(135deg, #34a853 0%, #137333 100%)",
        "linear-gradient(135deg, #ff9800 0%, #f57c00 100%)",
        "linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)",
        "linear-gradient(135deg, #00bcd4 0%, #0097a7 100%)",
        "linear-gradient(135deg, #e91e63 0%, #c2185b 100%)",
        "linear-gradient(135deg, #795548 0%, #5d4037 100%)",
        "linear-gradient(135deg, #607d8b 0%, #455a64 100%)",
        "linear-gradient(135deg, #ff5722 0%, #d84315 100%)",
      ];

      // Simple hash function to get consistent color for same employee
      let hash = 0;
      for (let i = 0; i < seed.length; i++) {
        const char = seed.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32-bit integer
      }

      const colorIndex = Math.abs(hash) % colors.length;
      return colors[colorIndex];
    },

    getStatusBarClass() {
      if (this.employeeData.org)
        return "linear-gradient(90deg, #4285f4 0%, #34a853 100%)";
      if (this.employeeData.isManager)
        return "linear-gradient(90deg, #ff9800 0%, #f57c00 100%)";
      return "linear-gradient(90deg, #34a853 0%, #137333 100%)";
    },
  },
};
</script>

<style scoped>
.employee-card {
  position: relative;
  width: 250px;
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.employee-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .employee-card {
    width: 220px;
    height: 110px;
  }
}
</style>
