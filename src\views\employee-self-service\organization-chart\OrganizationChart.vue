<template>
  <div>
    <!-- App Top Bar with Tabs -->
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
      />
    </div>

    <!-- Main Container -->
    <v-container fluid class="organization-chart-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div
            v-if="fieldforce && isAdmin"
            class="d-flex justify-end my-2 mx-3"
          >
            <CustomSelect
              max-width="300px"
              density="comfortable"
              :label="labelList[115]?.Field_Alias || 'Service Provider'"
              :items="serviceProviderList"
              :loading="serviceProviderLoading"
              :item-selected="selectedServiceProvider"
              :select-properties="{
                clearable: true,
              }"
              item-title="Service_Provider_Name"
              item-value="Service_Provider_Id"
              @selected-item="selectedServiceProvider = $event"
              hide-details
            ></CustomSelect>
          </div>
          <!-- Chart Container -->
          <div class="mx-3 my-2">
            <D3OrgChart
              :organization-data="chartData"
              :serviceProviderId="selectedServiceProvider"
              :serviceProviderName="
                labelList[115]?.Field_Alias || 'Service Provider'
              "
              :is-loading="isLoading"
              :has-error="hasError"
              :chart-height="chartHeight"
              @node-click="handleNodeClick"
              @retry="refreshChart"
            />
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>

    <!-- Employee Details Modal -->
    <v-dialog v-model="showEmployeeModal" max-width="500" persistent>
      <v-card v-if="selectedEmployee" rounded="lg">
        <v-btn
          style="position: absolute; top: 0px; right: 0px"
          icon
          variant="text"
          @click="closeEmployeeModal"
        >
          <v-icon>fas fa-times</v-icon>
        </v-btn>

        <v-card-text class="pa-5 employee-modal-content">
          <div
            class="d-flex align-start ga-5"
            :class="
              windowWidth < 800 ? 'flex-column align-center text-center' : ''
            "
          >
            <div class="flex-shrink-0">
              <div
                class="initials-avatar-large"
                :class="getRandomColorClass(selectedEmployee)"
              >
                {{ getEmployeeInitials(selectedEmployee.employeeName) }}
              </div>
            </div>

            <div class="flex-grow" style="max-width: 100%; overflow: hidden">
              <div
                class="d-flex align-center mb-1"
                style="width: 100%; overflow: hidden"
              >
                <v-tooltip
                  :text="selectedEmployee.employeeName"
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-h6 text-truncate"
                      style="flex: 1; max-width: 100%"
                      v-bind="props"
                    >
                      {{ selectedEmployee.employeeName || "" }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
              <div
                v-if="selectedEmployee.designationName"
                class="d-flex align-center mb-1"
                style="width: 100%; overflow: hidden"
              >
                <v-tooltip text="Designation" location="top" max-width="400">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      v-bind="props"
                      size="14"
                      class="mr-2 text-grey-darken-1 flex-shrink-0"
                      >fas fa-id-badge</v-icon
                    >
                  </template>
                </v-tooltip>
                <v-tooltip
                  :text="selectedEmployee.designationName"
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <span
                      class="text-grey-darken-1 text-truncate"
                      style="flex: 1; min-width: 0"
                      v-bind="props"
                    >
                      {{ selectedEmployee.designationName || "" }}
                    </span>
                  </template>
                </v-tooltip>
              </div>
              <div
                v-if="selectedEmployee.departmentName"
                class="d-flex align-center mb-1"
                style="width: 100%; overflow: hidden"
              >
                <v-tooltip text="Department" location="top" max-width="400">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      v-bind="props"
                      size="14"
                      class="mr-2 text-grey-darken-1 flex-shrink-0"
                      >fas fa-building</v-icon
                    >
                  </template>
                </v-tooltip>
                <v-tooltip
                  :text="selectedEmployee.departmentName"
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <span
                      class="text-grey-darken-1 text-truncate"
                      style="flex: 1; min-width: 0"
                      v-bind="props"
                    >
                      {{ selectedEmployee.departmentName || "" }}
                    </span>
                  </template>
                </v-tooltip>
              </div>
              <div
                v-if="selectedEmployee.location"
                class="d-flex align-center mb-1"
                style="width: 100%; overflow: hidden"
              >
                <v-tooltip text="Location" location="top" max-width="400">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      v-bind="props"
                      size="14"
                      class="mr-2 text-grey-darken-1 flex-shrink-0"
                      >fas fa-map-marker-alt</v-icon
                    >
                  </template>
                </v-tooltip>
                <v-tooltip
                  :text="selectedEmployee.location"
                  location="top"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <span
                      class="text-grey-darken-1 text-truncate"
                      style="flex: 1; min-width: 0"
                      v-bind="props"
                    >
                      {{ selectedEmployee.location || "" }}
                    </span>
                  </template>
                </v-tooltip>
              </div>
              <div class="d-flex ga-2 flex-wrap">
                <v-chip
                  v-if="selectedEmployee.isManager"
                  color="orange"
                  variant="outlined"
                  size="small"
                >
                  <v-icon size="14" class="mr-1">fas fa-crown</v-icon>
                  Manager
                </v-chip>
                <v-chip
                  :color="getGenderChipColor(selectedEmployee.gender)"
                  variant="outlined"
                  size="small"
                >
                  {{ selectedEmployee.gender || "Not specified" }}
                </v-chip>
              </div>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { GET_ORGANIZATION_CHART_LIST } from "@/graphql/employee-self-service/lopRecoveryQueries.js";
import D3OrgChart from "./D3OrgChart.vue";
import AppTopBarTab from "@/components/base-components/AppTopBarTab.vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "OrganizationChart",
  components: {
    D3OrgChart,
    AppTopBarTab,
    CustomSelect,
  },
  data() {
    return {
      // Data state
      organizationData: [],
      organizationInfo: null,
      chartData: [],

      // UI state
      isLoading: false,
      hasError: false,
      errorMessage: "",

      // Employee modal
      showEmployeeModal: false,
      selectedEmployee: null,

      // Tab configuration
      currentTabItem: "tab-0",
      mainTabs: ["Organization Chart"],

      // Service provider
      serviceProviderList: [],
      serviceProviderLoading: false,
      selectedServiceProvider: null,
      fieldforce: 0,
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    chartHeight() {
      return this.$store.state.windowInnerHeight - 200;
    },
    // Get organization code from store
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },

    // Get logged in employee ID
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },

    // Calculate total employees
    totalEmployees() {
      return this.organizationData.length;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(187);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    loginEmpServiceProviderId() {
      return this.$store.state.orgDetails.serviceProviderId;
    },
  },
  watch: {
    selectedServiceProvider() {
      this.fetchOrganizationData();
    },
  },
  mounted() {
    if (this.formAccess) {
      if (!this.isAdmin) {
        this.selectedServiceProvider = this.loginEmpServiceProviderId;
      }
      this.fetchOrganizationData();
      this.getDropdownData();
    }

    // Add window resize listener
    window.addEventListener("resize", this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // Fetch organization data from API
    fetchOrganizationData() {
      this.isLoading = true;
      this.hasError = false;
      this.$apollo
        .query({
          query: GET_ORGANIZATION_CHART_LIST,
          variables: {
            loginEmployeeId: this.loginEmployeeId,
            orgCode: this.orgCode,
            serviceProviderId: this.selectedServiceProvider,
          },
          client: "apolloClientAU",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response?.data?.getOrganizationChartList) {
            const result = response.data.getOrganizationChartList;
            this.organizationInfo = result.orgDetail;
            this.organizationData = result.employeeList || [];
            this.processChartData();
          }
        })
        .catch((error) => {
          this.$store.dispatch("handleApiErrors", {
            error: error,
            action: "retrieving",
            form: "organization chart",
            isListError: false,
          });
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    getDropdownData() {
      this.serviceProviderLoading = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 187 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { serviceProvider, fieldForce } =
              res.data.getDropDownBoxDetails;
            this.serviceProviderList = serviceProvider;
            this.fieldforce = fieldForce;
          }
          this.serviceProviderLoading = false;
        })
        .catch(() => {
          this.serviceProviderLoading = false;
        });
    },

    // Process raw data for d3-org-chart
    processChartData() {
      // First, create a map of all valid employee IDs
      const validEmployeeIds = new Set(
        this.organizationData
          .map((emp) => emp.employeeId?.toString())
          .filter(Boolean)
      );

      const chartData = [];

      // Add organization root node
      if (this.organizationInfo) {
        chartData.push({
          id: "0",
          name: this.organizationInfo.orgName,
          title: "",
          parentId: "",
          org: true,
          _totalSubordinates: this.organizationData.length,
        });
      }

      // Add employee nodes with cycle detection
      this.organizationData.forEach((employee) => {
        const employeeId = employee.employeeId?.toString();
        let managerId = employee.managerId?.toString();

        // Prevent self-reference cycles
        if (managerId === employeeId) {
          managerId = "0"; // Point to organization root instead
        }

        // If managerId doesn't exist in our employee list, point to root
        if (managerId && !validEmployeeIds.has(managerId)) {
          managerId = "0";
        }

        // Default to organization root if no valid manager
        if (!managerId) {
          managerId = "0";
        }

        chartData.push({
          id: employeeId,
          name: employee.employeeName,
          designation: employee.designationName,
          location: employee.location,
          department: employee.departmentName,
          managerName: employee.managerName,
          gender: employee.gender,
          isManager: employee.isManager,
          parentId: managerId,
          className: employee.className,
          _totalSubordinates: this.calculateSubordinates(employee.employeeId),
        });
      });

      // Additional cycle detection - check for circular chains
      const processedData = this.detectAndBreakCycles(chartData);
      this.chartData = processedData;
    },

    // Calculate subordinates for an employee
    calculateSubordinates(employeeId) {
      return this.organizationData.filter((emp) => emp.managerId === employeeId)
        .length;
    },

    // Detect and break cycles in the organizational hierarchy
    detectAndBreakCycles(chartData) {
      const nodeMap = new Map();
      chartData.forEach((node) => nodeMap.set(node.id, node));

      // Find all cycles and determine which edges to break
      const cyclicEdges = new Set();
      const visited = new Set();
      const recursionStack = new Set();
      const pathNodes = [];

      // DFS function to detect cycles and identify specific edges to break
      const findCycles = (nodeId) => {
        if (recursionStack.has(nodeId)) {
          // Found a cycle - identify the cyclic edge to break
          const cycleStartIndex = pathNodes.indexOf(nodeId);
          const cyclePath = pathNodes.slice(cycleStartIndex);

          // Break the edge that creates the cycle (last edge in the cycle)
          const lastNodeInCycle = cyclePath[cyclePath.length - 1];
          const firstNodeInCycle = cyclePath[0];
          cyclicEdges.add(`${lastNodeInCycle}->${firstNodeInCycle}`);
          return true;
        }

        if (visited.has(nodeId)) {
          return false;
        }

        visited.add(nodeId);
        recursionStack.add(nodeId);
        pathNodes.push(nodeId);

        const node = nodeMap.get(nodeId);
        if (
          node &&
          node.parentId &&
          node.parentId !== "" &&
          nodeMap.has(node.parentId)
        ) {
          findCycles(node.parentId);
        }

        recursionStack.delete(nodeId);
        pathNodes.pop();
        return false;
      };

      // Check each node for cycles
      chartData.forEach((node) => {
        if (!visited.has(node.id)) {
          findCycles(node.id);
        }
      });

      // Break only the specific cyclic edges, preserve other relationships
      const fixedData = chartData.map((node) => {
        if (
          node.id !== "0" &&
          cyclicEdges.has(`${node.id}->${node.parentId}`)
        ) {
          return { ...node, parentId: "0" };
        }
        return node;
      });

      return fixedData;
    },

    // Handle node click events
    handleNodeClick(nodeId) {
      const employee = this.organizationData.find(
        (emp) => emp.employeeId === parseInt(nodeId.id)
      );
      if (employee) {
        this.selectedEmployee = employee;
        this.showEmployeeModal = true;
      }
    },

    // Close employee modal
    closeEmployeeModal() {
      this.showEmployeeModal = false;
      this.selectedEmployee = null;
    },

    // Refresh chart data
    refreshChart() {
      this.fetchOrganizationData();
    },
    // Get organization logo URL
    getOrgLogoUrl(logoPath) {
      const baseUrl = this.$store.getters.getBaseUrl || "";
      return `${baseUrl}/uploads/org-logos/${logoPath}`;
    },

    // Get employee initials
    getEmployeeInitials(name) {
      if (!name) return "??";

      const nameParts = name.trim().split(" ");
      if (nameParts.length === 1) {
        // Single name - take first two characters
        return nameParts[0].substring(0, 2).toUpperCase();
      } else {
        // Multiple names - take first character of first and last name
        const firstInitial = nameParts[0].charAt(0);
        const lastInitial = nameParts[nameParts.length - 1].charAt(0);
        return (firstInitial + lastInitial).toUpperCase();
      }
    },

    // Handle logo error
    handleLogoError(event) {
      event.target.style.display = "none";
    },

    // Get random color class for employee
    getRandomColorClass(employee) {
      // Generate a consistent random color based on employee ID or name
      const seed = employee.id || employee.name || "default";
      const colors = [
        "color-1",
        "color-2",
        "color-3",
        "color-4",
        "color-5",
        "color-6",
        "color-7",
        "color-8",
        "color-9",
        "color-10",
      ];

      // Simple hash function to get consistent color for same employee
      let hash = 0;
      for (let i = 0; i < seed.length; i++) {
        const char = seed.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32-bit integer
      }

      const colorIndex = Math.abs(hash) % colors.length;
      return colors[colorIndex];
    },

    // Get gender chip color
    getGenderChipColor(gender) {
      if (gender === "Male") return "blue";
      if (gender === "Female") return "pink";
      return "grey";
    },
  },
};
</script>

<style scoped>
.organization-chart-container {
  padding: 60px 0px 0px 0px;
}

.topPosition {
  position: absolute;
  top: 0;
  right: 0;
}

.initials-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  font-weight: 600;
  border: 3px solid #e9ecef;
}

.initials-avatar-large.color-1 {
  background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
}

.initials-avatar-large.color-2 {
  background: linear-gradient(135deg, #ea4335 0%, #d33b2c 100%);
}

.initials-avatar-large.color-3 {
  background: linear-gradient(135deg, #34a853 0%, #137333 100%);
}

.initials-avatar-large.color-4 {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.initials-avatar-large.color-5 {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

.initials-avatar-large.color-6 {
  background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
}

.initials-avatar-large.color-7 {
  background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
}

.initials-avatar-large.color-8 {
  background: linear-gradient(135deg, #795548 0%, #5d4037 100%);
}

.initials-avatar-large.color-9 {
  background: linear-gradient(135deg, #607d8b 0%, #455a64 100%);
}

.initials-avatar-large.color-10 {
  background: linear-gradient(135deg, #ff5722 0%, #d84315 100%);
}
</style>
