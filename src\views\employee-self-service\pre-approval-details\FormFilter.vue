<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="secondary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col
            v-if="callingFrom === 'team'"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedEmpName"
              color="secondary"
              :items="empNameList"
              label="Employee"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
              item-title="employeeName"
              item-value="employeeId"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedApprovalType"
              color="secondary"
              :items="approvalType"
              label="Approval Type"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
          <v-col class="py-2 mb-6" :cols="windowWidth > 600 ? 6 : 12">
            <datepicker
              :format="orgDateFormat"
              v-model="selectedStartDate"
              :placeholder="'Start Date'"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col class="py-2 mb-6" :cols="windowWidth > 600 ? 6 : 12">
            <datepicker
              :format="orgDateFormat"
              v-model="selectedEndDate"
              :placeholder="'End Date'"
              :disabled-dates="{
                to: new Date(minDate),
              }"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedPreApprovalStatus"
              color="secondary"
              :items="preApprovalStatusList"
              label="Status"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              variant="solo"
            >
            </v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 secondary"
          rounded="lg"
          @click.stop="fnApplyFilter('manual')"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";

export default defineComponent({
  name: "FormFilter",

  components: {
    Datepicker,
  },

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    callingFrom: {
      type: String,
      default: "employee",
    },
  },

  data: () => ({
    openFormFilter: false,
    empNameList: [],
    selectedEmpName: [],
    approvalType: [
      "Work from home",
      "Work during week off",
      "Work during holiday",
      "On Duty",
      "Overtime Work",
    ],
    selectedApprovalType: [],
    preApprovalStatusList: [
      "Applied",
      "Approved",
      "Rejected",
      "Cancelled",
      "Cancel Applied",
    ],
    selectedPreApprovalStatus: [],
    selectedStartDate: "",
    selectedEndDate: "",
  }),

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    minDate() {
      if (this.selectedStartDate) {
        const issueDateMs = new Date(this.selectedStartDate)
          .toISOString()
          .substring(0, 10);
        return issueDateMs;
      }
      return null;
    },
  },

  watch: {
    items() {
      this.formFilterData();
    },
  },

  mounted() {
    this.formFilterData();
  },

  methods: {
    // apply filter
    fnApplyFilter() {
      this.openFormFilter = false;
      let filteredArray = this.items;

      if (this.selectedEmpName.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmpName.includes(item.userDefinedEmpId);
        });
      }

      if (this.selectedApprovalType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedApprovalType.includes(item.preApprovalType);
        });
      }
      if (this.selectedStartDate) {
        filteredArray = filteredArray.filter((item) => {
          if (item) {
            return moment(item.startDate).isSameOrAfter(
              moment(this.selectedStartDate).format("YYYY-MM-DD")
            );
          } else return item;
        });
      }
      if (this.selectedEndDate) {
        filteredArray = filteredArray.filter((item) => {
          if (item) {
            return moment(item.endDate).isSameOrBefore(
              moment(this.selectedEndDate).format("YYYY-MM-DD")
            );
          } else return item;
        });
      }
      if (this.selectedPreApprovalStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedPreApprovalStatus.includes(item.status);
        });
      }
      this.$emit("apply-filter", filteredArray);
    },

    // reset filter
    resetFilterValues() {
      this.selectedEmpName = [];
      this.selectedEmpId = [];
      this.selectedApprovalType = [];
      this.selectedStartDate = "";
      this.selectedEndDate = "";
      this.selectedPreApprovalStatus = [];
      this.openFormFilter = false;
      this.$emit("reset-filter");
    },
    resetAllModelValues() {
      this.selectedEmpName = [];
      this.selectedEmpId = [];
      this.selectedApprovalType = [];
      this.selectedStartDate = "";
      this.selectedEndDate = "";
      this.selectedPreApprovalStatus = [];
      this.openFormFilter = false;
    },
    formFilterData() {
      for (let item of this.items) {
        if (item && (item.employeeName || item.userDefinedEmpId)) {
          this.empNameList.push({
            employeeName: item.employeeName + " - " + item.userDefinedEmpId,
            employeeId: item.userDefinedEmpId,
          });
        }
      }
      this.empNameList = this.removeDuplicatesFromArrayOfObject(
        this.empNameList,
        "employeeId"
      );
    },

    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
  },
});
</script>
