<template>
  <div class="d-flex align-center" v-click-outside="closeDropdown">
    <div class="position-relative" style="width: 100%">
      <!-- Text Field for Search Input -->
      <v-text-field
        v-model="searchString"
        :placeholder="placeholder"
        prepend-inner-icon="fas fa-search"
        :variant="variant"
        :density="density"
        clearable
        hide-details
        :class="isMobileView ? 'mt-3' : ''"
        :loading="isLoading"
        @input="onSearchInput"
        @click:clear="clearSearch"
        @focus="debouncedFocus"
        @blur="onFieldBlur"
      />

      <!-- Dropdown Menu for Results -->
      <v-menu
        v-model="showDropdown"
        :activator="menuActivator"
        :close-on-content-click="false"
        :close-on-back="false"
        location="bottom start"
        offset="4"
        :max-height="windowHeight * 0.6 + `px`"
        :min-width="isMobileView ? `400px` : `700px`"
        transition="slide-y-transition"
        :z-index="9999"
      >
        <v-card
          class="rounded-lg elevation-4"
          :max-height="windowHeight * 0.6 + `px`"
          style="overflow-y: auto"
        >
          <!-- Show results or appropriate message -->
          <div v-if="positionList.length > 0">
            <div
              v-for="(position, index) in positionList"
              :key="`position-${position.Originalpos_Id}-${index}`"
              class="pa-2"
            >
              <v-card
                class="rounded-lg elevation-1 pa-2 mb-1 cursor-pointer"
                @click="onPositionSelected(position)"
                @mouseenter="hoveredIndex = index"
                @mouseleave="hoveredIndex = -1"
                :class="hoveredIndex === index ? 'bg-grey-lighten-4' : ''"
              >
                <v-row no-gutters align="center">
                  <!-- Icon Section -->
                  <v-col cols="auto" class="mr-3">
                    <v-avatar
                      :style="{
                        backgroundColor: getPositionAvatarColor(
                          position.Originalpos_Id
                        ),
                      }"
                      size="40"
                    >
                      <v-icon size="20" color="white"> fas fa-sitemap </v-icon>
                    </v-avatar>
                  </v-col>

                  <!-- Position Information Section -->
                  <v-col class="flex-grow-1">
                    <div
                      class="text-subtitle-1 font-weight-medium text-high-emphasis mb-2"
                    >
                      {{ truncateText(position.Position_Title, 30) }}
                      <span
                        v-if="position.Position_Code && position.Position_Title"
                      >
                        -
                      </span>
                      {{ truncateText(position.Position_Code, 20) }}
                    </div>

                    <!-- Group Information -->
                    <div
                      v-if="position.Group_Code || position.Group_Name"
                      class="d-flex align-center mb-1"
                    >
                      <v-icon size="12" class="mr-2 text-medium-emphasis">
                        fas fa-layer-group
                      </v-icon>
                      <span class="text-caption text-medium-emphasis">
                        Group: {{ truncateText(position.Group_Name, 30) }}
                        <span v-if="position.Group_Code && position.Group_Name">
                          -
                        </span>
                        {{ truncateText(position.Group_Code, 20) }}
                      </span>
                    </div>
                  </v-col>

                  <!-- Action Arrow -->
                  <v-col cols="auto">
                    <v-icon
                      color="primary"
                      size="16"
                      class="text-medium-emphasis mx-4"
                    >
                      fas fa-location-arrow
                    </v-icon>
                  </v-col>
                </v-row>
              </v-card>
            </div>
          </div>

          <!-- No Data / Loading / Message State -->
          <div v-else class="py-1 text-center" style="min-width: 300px">
            <div class="text-caption text-medium">
              {{ noDataText }}
            </div>
          </div>
        </v-card>
      </v-menu>
    </div>
  </div>
</template>

<script>
import { GET_POSITION_GLOBAL_SEARCH } from "@/graphql/mpp/manPowerPlanningQueries";
import { checkNullValue, colorCode } from "@/helper";
import debounce from "lodash.debounce";
export default {
  name: "MPPGlobalSearch",

  directives: {
    "click-outside": {
      beforeMount(el, binding) {
        el.clickOutsideEvent = function (event) {
          if (!(el === event.target || el.contains(event.target))) {
            binding.value(event);
          }
        };
        document.addEventListener("click", el.clickOutsideEvent);
      },
      unmounted(el) {
        document.removeEventListener("click", el.clickOutsideEvent);
      },
    },
  },

  props: {
    formId: {
      type: Number,
      required: true,
    },
    screenTab: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "Search for positions by title or code.",
    },
    variant: {
      type: String,
      default: "solo",
    },
    density: {
      type: String,
      default: "compact",
    },
    limit: {
      type: Number,
      default: 500,
    },
    forecastYear: {
      type: Number,
      default: null,
    },
  },

  emits: ["position-selected"],

  data() {
    return {
      searchString: "",
      positionList: [],
      isLoading: false,
      // Dropdown control properties
      showDropdown: false,
      menuActivator: null,
      hoveredIndex: -1,
      isFieldFocused: false,
      // Pagination properties
      totalApiCount: 0,
      apiCallCount: 0,
      totalRecords: 0,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    noDataText() {
      if (this.isLoading) {
        return "Loading...";
      } else if (
        !this.isLoading &&
        this.positionList.length === 0 &&
        this.searchString.length >= 3
      ) {
        return "No positions found";
      } else {
        return "Type minimum 3 characters to search";
      }
    },
  },

  created() {
    // Initialize debounced search function
    this.debouncedSearch = debounce(this.performSearch, 300);
    // Initialize debounced focus handler to prevent rapid multiple focus events
    this.debouncedFocus = debounce(this.handleFieldFocus, 300);
  },

  mounted() {
    // Set the menu activator to the text field
    this.$nextTick(() => {
      const textField = this.$el.querySelector(".v-text-field");
      if (textField) {
        this.menuActivator = textField;
      }
    });
  },

  methods: {
    checkNullValue,

    // Text truncation method
    truncateText(text, maxLength) {
      if (!text) return "";
      return text.length > maxLength
        ? text.substring(0, maxLength) + "..."
        : text;
    },

    onSearchInput(event) {
      // Handle the input event from v-text-field
      const value = event.target ? event.target.value : event;
      this.searchString = value || "";
      this.debouncedSearch();
    },

    handleFieldFocus() {
      // Set focus state and show dropdown if conditions are met
      this.isFieldFocused = true;

      // Only show dropdown if it's not already shown and we have valid conditions
      if (!this.showDropdown) {
        if (
          this.positionList.length > 0 ||
          (this.searchString.length >= 3 && !this.isLoading)
        ) {
          this.showDropdown = true;
        }
      }
    },

    onFieldBlur() {
      // Set focus state to false with a small delay to allow for dropdown interactions
      setTimeout(() => {
        this.isFieldFocused = false;
      }, 150);
    },

    closeDropdown() {
      this.showDropdown = false;
      this.hoveredIndex = -1;
      this.isFieldFocused = false;
    },
    performSearch() {
      if (this.searchString.length < 3) {
        this.positionList = [];
        this.resetPagination();
        this.showDropdown = false;
        return;
      }

      this.isLoading = true;
      this.resetPagination();

      // Only show dropdown if field is focused or we already have it open
      if (this.isFieldFocused || this.showDropdown) {
        this.showDropdown = true;
      }

      try {
        // Use the store dispatch pattern for API calls
        const apiObj = {
          query: GET_POSITION_GLOBAL_SEARCH,
          variables: {
            searchValue: this.searchString,
            formId: this.formId,
            screenTab: this.screenTab,
            offset: 0,
            limit: this.limit,
            forecastingYear: this.forecastYear,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        };

        this.$apollo
          .query(apiObj)
          .then((response) => {
            if (
              response.data &&
              response.data.getPositionGlobalSearch &&
              !response.data.getPositionGlobalSearch.errorCode?.length
            ) {
              const responseData = response.data.getPositionGlobalSearch;
              this.positionList = responseData.positions || [];

              // Handle pagination if totalCount is available
              if (responseData.totalRecords) {
                this.totalRecords = parseInt(responseData.totalRecords);
                this.apiCallCount = 1;
                this.totalApiCount = Math.ceil(this.totalRecords / this.limit);

                // Load additional pages if needed
                for (let i = 1; i < this.totalApiCount; i++) {
                  this.loadMorePositions(i);
                }
              }
            } else {
              this.positionList = [];
              this.handleSearchError(
                response.data.getPositionGlobalSearch?.errorCode ||
                  "No data returned from API"
              );
            }
          })
          .catch((error) => {
            this.positionList = [];
            this.handleSearchError(error);
          })
          .finally(() => {
            // Only set loading to false if this is the only API call
            if (this.totalApiCount <= 1) {
              this.isLoading = false;
            }
          });
      } catch (error) {
        this.positionList = [];
        this.handleSearchError(error);
        this.isLoading = false;
      }
    },
    loadMorePositions(pageIndex = 1) {
      const apiOffset = parseInt(pageIndex) * this.limit;

      const apiObj = {
        query: GET_POSITION_GLOBAL_SEARCH,
        variables: {
          searchValue: this.searchString,
          formId: this.formId,
          screenTab: this.screenTab,
          offset: apiOffset,
          limit: this.limit,
          forecastingYear: this.forecastYear,
        },
        client: "apolloClientAG",
        fetchPolicy: "no-cache",
      };

      this.$apollo
        .query(apiObj)
        .then((response) => {
          if (
            response.data &&
            response.data.getPositionGlobalSearch &&
            !response.data.getPositionGlobalSearch.errorCode?.length
          ) {
            const additionalPositions =
              response.data.getPositionGlobalSearch.positions || [];

            // Append new positions to existing list
            this.positionList = [...this.positionList, ...additionalPositions];

            this.apiCallCount = this.apiCallCount + 1;

            // Set loading to false when all API calls are complete
            if (this.totalApiCount === this.apiCallCount) {
              this.isLoading = false;
            }
          } else {
            this.handleSearchError(
              response.data.getPositionGlobalSearch?.errorCode ||
                "Error loading additional positions"
            );
          }
        })
        .catch((error) => {
          this.handleSearchError(error);
        });
    },
    resetPagination() {
      this.totalApiCount = 0;
      this.apiCallCount = 0;
      this.totalRecords = 0;
    },

    onPositionSelected(position) {
      if (position) {
        this.$emit("position-selected", position);
        // Keep the dropdown open for additional selections
        // Only reset hover state
        this.hoveredIndex = -1;
      }
    },
    handleSearchError(error = "") {
      this.$store.dispatch("handleApiErrors", {
        error: error,
        action: "searching",
        form: "Man Power Planning",
        isListError: true,
      });
    },
    clearSearch() {
      this.searchString = "";
      this.positionList = [];
      this.resetPagination();
      this.showDropdown = false;
      this.hoveredIndex = -1;
      this.isFieldFocused = false;
    },
    getInitials(title) {
      if (!title) return "?";

      const words = title.trim().split(" ");
      if (words.length === 1) {
        return words[0].charAt(0).toUpperCase();
      }

      return (
        words[0].charAt(0) + words[words.length - 1].charAt(0)
      ).toUpperCase();
    },
    getPositionAvatarColor(positionId) {
      // Use position ID to consistently assign same color (1-10 range for colorCode function)
      const colorIndex = positionId
        ? (Math.abs(positionId.toString().length) % 10) + 1
        : 1;
      return colorCode(colorIndex);
    },
  },
};
</script>

<style scoped>
/* Minimal custom styles - using Vuetify classes for most styling */
.v-card {
  transition: all 0.2s ease-in-out;
}

.cursor-pointer {
  cursor: pointer;
}

.position-relative {
  position: relative;
}

/* Ensure dropdown appears above other elements */
:deep(.v-overlay__content) {
  z-index: 9999 !important;
}

/* Smooth transitions for hover effects */
.v-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
}
</style>
