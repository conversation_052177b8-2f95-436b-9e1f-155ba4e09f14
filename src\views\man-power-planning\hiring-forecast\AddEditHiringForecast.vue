<template>
  <div class="py-6">
    <div v-if="windowWidth < 770" class="fixed-title">
      <v-sheet class="align-center text-center">
        <v-col cols="12" class="d-flex justify-end">
          <v-btn
            rounded="lg"
            class="primary mr-6"
            variant="outlined"
            @click="$emit('close-overlay')"
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="mr-1 primary"
            variant="elevated"
            :disabled="!isFormDirty || sumOfMonthValue < 1"
            @click="submitHiringList()"
          >
            Submit
          </v-btn>
        </v-col>
      </v-sheet>
    </div>
    <v-form ref="hiringForecastForm" class="mx-4">
      <v-row class="mt-4">
        <v-col cols="12" class="py-5 d-flex" v-if="!enableCustomPosition">
          <CustomSelect
            label="Position Title"
            :item-selected="selectedPosition"
            :items="positionList"
            v-model="selectedPosition"
            :loading="positionListLoading"
            :isRequired="true"
            clearable
            :isAutoComplete="true"
            itemTitle="Pos_full_Name"
            itemValue="Organization_Structure_Id"
            :disabled="!!selectedForecast"
            :rules="[required('Position Title', selectedPosition)]"
            @update:model-value="onChangePosition($event)"
          ></CustomSelect>
          <v-btn
            rounded="lg"
            class="ml-2 primary px-2 mt-3"
            variant="elevated"
            :disabled="!!selectedForecast"
            @click="addNewPosition()"
          >
            Add New
          </v-btn>
        </v-col>
        <v-col cols="12" class="d-flex" v-else>
          <v-text-field
            v-model="tempSelectedPositionTitle"
            :rules="[
              required('Position Title', tempSelectedPositionTitle),
              validateWithRulesAndReturnMessages(
                tempSelectedPositionTitle,
                'positionTitle',
                'Position Title'
              ),
            ]"
            ref="PositionTitle"
            variant="solo"
            :disabled="
              !!(
                selectedPositionData &&
                (selectedPositionData.Originalpos_Id ||
                  selectedPositionData.Organization_Structure_Id)
              )
            "
            label="Position Title"
            @update:model-value="onChangeMonth()"
            ><template v-slot:label>
              Position Title
              <span style="color: red">*</span>
            </template></v-text-field
          >
          <v-btn
            rounded="lg"
            class="ml-2 px-1 primary mt-3"
            @click="addNewPosition()"
            :disabled="
              !!(
                selectedPositionData &&
                (selectedPositionData.Originalpos_Id ||
                  selectedPositionData.Organization_Structure_Id)
              )
            "
          >
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-col>
        <v-row v-if="enableCustomPosition">
          <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
            <CustomSelect
              v-model="selectedGroup"
              :items="positionGroup"
              label="Group"
              item-title="Pos_full_Name"
              :isAutoComplete="true"
              :clearable="true"
              :itemSelected="selectedGroup"
              item-value="Originalpos_Id"
              @selected-item="isFormDirty = true"
              @update:model-value="updateGroup()"
            />
          </v-col>
          <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
            <CustomSelect
              v-model="selectedDivision"
              :items="divisionList"
              label="Division"
              item-title="Pos_full_Name"
              item-value="Originalpos_Id"
              :isAutoComplete="true"
              :clearable="true"
              :itemSelected="selectedDivision"
              @selected-item="isFormDirty = true"
              :disabled="!selectedGroup"
              :isLoading="isDropdownListLoading"
              @update:model-value="updateDivision()"
            />
          </v-col>
          <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
            <CustomSelect
              v-model="selectedDepartment"
              :items="departmentList"
              label="Department"
              item-title="Pos_full_Name"
              item-value="Originalpos_Id"
              :isAutoComplete="true"
              :clearable="true"
              :itemSelected="selectedDepartment"
              @selected-item="isFormDirty = true"
              :disabled="!selectedDivision"
              :isLoading="isDropdownListLoading"
              @update:model-value="updateDepartment()"
            />
          </v-col>
          <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
            <CustomSelect
              v-model="selectedSection"
              :items="sectionList"
              label="Section"
              item-title="Pos_full_Name"
              item-value="Originalpos_Id"
              :isAutoComplete="true"
              :clearable="true"
              :itemSelected="selectedSection"
              @selected-item="isFormDirty = true"
              :disabled="!selectedDepartment"
              :isLoading="isDropdownListLoading"
              @update:model-value="updateSection()"
            />
          </v-col>
        </v-row>
      </v-row>
      <div>
        <v-card class="rounded-lg pa-3 my-5">
          <v-row>
            <v-col :cols="windowWidth >= 1264 ? 3 : 6">
              <div class="d-flex align-center justify-center flex-column">
                <div class="font-weight-bold py-2">Approved Positions</div>
                <div class="font-weight-bold py-3">
                  {{
                    checkNullValue(this.selectedPositionData?.Approved_Position)
                  }}
                </div>
              </div>
            </v-col>
            <v-col :cols="windowWidth >= 1264 ? 3 : 6">
              <div class="d-flex align-center justify-center flex-column">
                <div class="font-weight-bold py-2">Warm Bodies</div>
                <div class="font-weight-bold py-3">
                  {{ checkNullValue(this.selectedPositionData?.Warm_Bodies) }}
                </div>
              </div>
            </v-col>
            <v-col :cols="windowWidth >= 1264 ? 3 : 6">
              <div class="d-flex align-center justify-center flex-column">
                <div class="font-weight-bold py-2">
                  Approved Vacant Positions
                </div>
                <div class="font-weight-bold py-3">
                  {{ checkNullValue(calculateToHired.hiringForecast) }}
                </div>
              </div>
            </v-col>
            <v-col :cols="windowWidth >= 1264 ? 3 : 6">
              <div class="d-flex align-center justify-center flex-column">
                <div class="font-weight-bold py-2">Vacancies for TO Review</div>
                <div class="font-weight-bold py-3">
                  {{ checkNullValue(calculateToHired.sourcingForecast) }}
                </div>
              </div>
            </v-col>
          </v-row>
        </v-card>
        <div class="mt-10">
          <v-row>
            <v-col
              :cols="windowWidth >= 1264 ? 3 : 6"
              class="my-5"
              v-for="months in monthList"
              :key="months"
            >
              <v-text-field
                v-model="months.value"
                :ref="'positionTitle' + months.month"
                :rules="[
                  numericRequiredValidation('Field', months.value),
                  numericValidation(months.month, months.value),
                ]"
                :isRequired="true"
                variant="solo"
                :disabled="months.disabled"
                max="999"
                maxLength="3"
                @update:model-value="onChangeMonth()"
                ><template v-slot:label>
                  {{ months.month + ", " + months.year }}
                  <span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
          </v-row>
        </div>
      </div>
    </v-form>
    <div v-if="windowWidth >= 770" class="card-actions-div">
      <v-sheet
        class="align-center text-center overlay-footer"
        elevation="16"
        style="width: 100%"
      >
        <v-col cols="12" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            class="primary mr-6"
            variant="outlined"
            @click="$emit('close-overlay')"
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="mr-1 primary"
            variant="elevated"
            :disabled="!isFormDirty || sumOfMonthValue < 1"
            @click="submitHiringList()"
          >
            Submit
          </v-btn>
        </v-col>
      </v-sheet>
    </div>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    icon-name="fas fa-exchange-alt"
    icon-color="amber"
    v-if="openConfirmModal"
    :open-modal="openConfirmModal"
    confirmation-heading="Are you sure you want to change the position title"
    @close-warning-modal="onCancelPositionTitle()"
    @accept-modal="onChangePositionTitle()"
  ></AppWarningModal>
  <AppWarningModal
    icon-name="fas fa-exchange-alt"
    icon-color="amber"
    v-if="openChangePosition"
    :open-modal="openChangePosition"
    confirmation-heading="Are you sure you want to change the position title"
    @close-warning-modal="onCancelCustomPosition()"
    @accept-modal="onChangeCustomPosition()"
  ></AppWarningModal>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  HIRING_FORECAST_ADD_UPDATE,
  HIRING_POSITION_LIST,
} from "@/graphql/mpp/manPowerPlanningQueries";
import { ORG_STRUCTURE_BASED_ON_GROUP } from "@/graphql/mpp/newPositionQueries";
import { checkNullValue } from "@/helper";
import validationRules from "@/mixins/validationRules";
import moment from "moment";

export default {
  name: "AddEditHiringForecast",
  emits: ["refresh-list", "close-overlay"],
  props: {
    selectedForecastYear: {
      type: Number,
      required: true,
    },
    selectedPositionTitle: {
      type: Number,
      required: true,
    },
    selectedDivisionTitle: {
      type: [Number, String],
      required: true,
    },
    selectedForecast: {
      type: Object,
      default: () => {},
    },
    hireForecastData: {
      type: Object,
      default: () => {},
    },
    positionGroup: {
      type: Array,
      required: true,
    },
    parentPathForListForecastPosition: {
      type: String,
      required: true,
    },
    selectPositionParentId: {
      type: String,
      required: true,
    },
    isValidHierarchy: {
      type: Boolean,
      required: true,
    },
  },
  mixins: [validationRules],
  data() {
    return {
      positionList: [],
      positionTitle: null,
      selectedPosition: null,
      selectedPositionData: null,
      monthList: [],
      openConfirmModal: false,
      openChangePosition: false,
      positionListLoading: false,
      tempPositionTitle: null,
      isLoading: false,
      isDropdownListLoading: false,
      isFormDirty: false,
      hiringForeCastLimitToCallAPI: 10000,
      totalApiCount: 0,
      apiCallCount: 0,
      groupTotalApiCount: 0,
      groupApiCallCount: 0,
      enableCustomPosition: false,
      tempSelectedPositionTitle: null,
      selectedGroup: null,
      selectedDivision: null,
      divisionList: [],
      tempDivision: null,
      selectedDepartment: null,
      departmentList: [],
      selectedSection: null,
      sectionList: [],
    };
  },
  components: {
    CustomSelect,
  },
  computed: {
    sumOfMonthValue() {
      return this.monthList.reduce(
        (acc, month) =>
          acc +
          (month?.value != ""
            ? typeof parseInt(month?.value) === "number"
              ? parseInt(month?.value)
              : 0
            : 0),
        0
      );
    },
    calculateToHired() {
      const sumOfValue = this.sumOfMonthValue;
      let result = { hiringForecast: 0, sourcingForecast: 0 };
      if (
        (sumOfValue || 0) >
        (this.selectedPositionData?.Approved_Position || 0) -
          (this.selectedPositionData?.Warm_Bodies || 0)
      ) {
        result.hiringForecast =
          (this.selectedPositionData?.Approved_Position || 0) -
          (this.selectedPositionData?.Warm_Bodies || 0);
        result.sourcingForecast =
          (sumOfValue || 0) - (result.hiringForecast || 0);
        return result;
      } else {
        result.hiringForecast = sumOfValue || 0;
        result.sourcingForecast = 0;
        return result;
      }
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    filteredYearMonth() {
      return this.hireForecastData.End_Month === 12
        ? `${this.selectedForecastYear}-01-01`
        : `${this.selectedForecastYear}-${
            this.hireForecastData.End_Month + 1
          }-01`;
    },
  },
  mounted() {
    if (
      !this.isValidHierarchy &&
      Object.keys(this.selectedForecast || {}).length === 0
    )
      this.showAlert({
        isOpen: true,
        type: "warning",
        message:
          "Please select at least one filter (Position Group, Division, Department, or Section) to proceed with adding a hiring forecast.",
      });
    this.updateHiringForecast("mounted");
    // this.retrieveTotalPositionList();
  },
  watch: {
    selectedForecast() {
      this.retrieveTotalPositionList();
    },
  },
  methods: {
    checkNullValue,
    updateHiringForecast(type) {
      const currentMonth = moment().month() + 1;
      const currentYear = moment().year();
      if (this.selectedForecast && this.selectedForecast.foreCastList) {
        const forecastList = JSON.parse(this.selectedForecast.foreCastList);
        const {
          Organization_Structure_Id,
          Originalpos_Id,
          Approved_Position,
          Warm_Bodies,
          To_Be_Hired,
          To_Be_Source,
          Position_Title,
          Division_Code,
          Group_Id,
          Pos_Name,
          Department_Code,
          Section_Code,
          Division_Id,
          Department_Id,
          Section_Id,
        } = this.selectedForecast;
        this.selectedPositionData = {
          Organization_Structure_Id,
          Pos_Name,
          Division_Code,
          Originalpos_Id,
          Approved_Position,
          Warm_Bodies,
          To_Be_Hired,
          To_Be_Source,
        };
        this.tempPositionTitle = Organization_Structure_Id;
        this.selectedPosition = Organization_Structure_Id;
        if (Originalpos_Id === null) {
          this.selectedGroup = parseInt(Group_Id) || null;
          this.tempSelectedPositionTitle = Position_Title;
          this.enableCustomPosition = true;
        }
        this.selectedGroup = parseInt(Group_Id) || null;
        this.selectedDivision = Division_Id || null;
        this.selectedDepartment = Department_Id || null;
        this.selectedSection = Section_Id || null;
        if (Division_Code) {
          this.tempDivision = Division_Code;
        }
        if (Department_Code) {
          this.selectedDepartment = Department_Code;
        }
        if (Section_Code) {
          this.selectedSection = Section_Code;
        }
        this.monthList = moment.months()?.map((month, index) => {
          const date = moment(this.filteredYearMonth)
            .clone()
            .add(index, "months");

          const monthName = date.format("MMM");
          const monthNumber = parseInt(date.format("MM"));
          const yearValue = parseInt(date.format("YYYY"));
          const selectedValue = forecastList.find(
            (list) =>
              list?.Forecast_Month === monthNumber &&
              list.Forecast_Year === yearValue
          );
          const isDisabled =
            yearValue < currentYear ||
            (yearValue === currentYear && monthNumber < currentMonth);
          return {
            id: index,
            month: monthName,
            monthNumber: monthNumber,
            value: selectedValue?.No_Of_Position ?? 0,
            year: yearValue,
            hiringForecastId: selectedValue?.Hiring_Forecast_Id ?? 0,
            disabled: isDisabled,
          };
        });
        this.retrieveCountGroupPosition(type);
        this.retrieveTotalPositionList();
      } else {
        this.tempDivision = null;
        this.monthList = moment.months()?.map((month, index) => {
          const date = moment(this.filteredYearMonth)
            .clone()
            .add(index, "months");

          const monthName = date.format("MMM");
          const monthNumber = parseInt(date.format("MM"));
          const yearValue = parseInt(date.format("YYYY"));
          const isDisabled =
            yearValue < currentYear ||
            (yearValue === currentYear && monthNumber < currentMonth);
          return {
            id: index + 1,
            month: monthName,
            value: 0,
            monthNumber: monthNumber,
            year: yearValue,
            hiringForecastId: 0,
            disabled: isDisabled,
          };
        });
        this.retrieveCountGroupPosition(type);
        this.retrieveTotalPositionList();
      }
    },
    addNewPosition() {
      if (this.isFormDirty) {
        this.openChangePosition = true;
      } else {
        this.addNewPositionOption();
      }
    },
    onChangePositionTitle() {
      this.tempPositionTitle = this.selectedPosition;
      this.openConfirmModal = false;
      this.isFormDirty = false;
      this.selectedPositionData = this.positionList.find(
        (list) => list.Organization_Structure_Id === this.selectedPosition
      );
      this.monthList = this.monthList.map((item) => ({
        ...item,
        value: 0,
      }));
      if (!this.selectedPositionData.Originalpos_Id) {
        if (this.selectedPosition) {
          this.isFormDirty = false;
          this.enableCustomPosition = true;
          let positionTitleData = this.positionList.find(
            (list) => list.Organization_Structure_Id === this.selectedPosition
          );
          if (positionTitleData?.Pos_Name) {
            this.tempSelectedPositionTitle = positionTitleData?.Pos_Name;
          }
          this.selectedPositionData = null;
        }
      }
    },
    onChangePosition() {
      this.isFormDirty = true;
      if (this.selectedPosition) {
        let posTitleData = this.positionList.find(
          (list) => list.Organization_Structure_Id === this.selectedPosition
        );
        let posName = posTitleData?.Pos_Name || "";
        if (this.tempPositionTitle && this.isFormDirty) {
          this.openConfirmModal = true;
        } else {
          const positionData = this.positionList.find(
            (list) => list.Organization_Structure_Id === this.selectedPosition
          );
          if (positionData && positionData.Originalpos_Id) {
            this.selectedPositionData = positionData;
          } else {
            this.enableCustomPosition = true;
            this.tempSelectedPositionTitle = positionData?.Pos_Name || posName;
            this.selectedPositionData = null;
          }
          this.tempPositionTitle = this.selectedPosition;
        }
      }
    },
    onChangeMonth() {
      this.isFormDirty = true;
    },
    onCancelPositionTitle() {
      this.selectedPosition = this.tempPositionTitle;
      this.openConfirmModal = false;
    },
    onCancelCustomPosition() {
      this.openChangePosition = false;
    },
    onChangeCustomPosition() {
      this.openChangePosition = false;
      this.addNewPositionOption();
    },
    addNewPositionOption() {
      this.isFormDirty = false;
      this.enableCustomPosition = !this.enableCustomPosition;
      this.monthList = this.monthList.map((item) => ({
        ...item,
        value: 0,
      }));
      this.selectedPosition = null;
      this.selectedPositionData = {
        Warm_Bodies: 0,
        Approved_Position: 0,
        Position_Request_Id: 0,
        Originalpos_Id: 0,
      };
      this.tempSelectedPositionTitle = null;
      this.selectedDivision = null;
      this.selectedGroup = null;
    },
    async submitHiringList() {
      const { valid } = await this.$refs.hiringForecastForm.validate();
      if (valid) {
        if (
          this.enableCustomPosition &&
          (this.selectedDivision === undefined ||
            this.selectedDivision === null) &&
          (this.selectedGroup === undefined || this.selectedGroup === null)
        ) {
          let snackbarData = {
            isOpen: true,
            message: `Please select division or group.`,
            type: "warning",
          };
          this.showAlert(snackbarData);
          return;
        }
        this.isLoading = true;
        let foreCastListData = this.monthList?.map((item) => ({
          year: item.year,
          month: item.monthNumber,
          noOfPosition: parseInt(item.value),
          hiringForecastId:
            this.selectedForecast && this.selectedForecast.foreCastList
              ? item.hiringForecastId
              : 0,
        }));
        const positionTitleData = this.positionList.find(
          (list) => list.Organization_Structure_Id === this.selectedPosition
        );

        this.$apollo
          .mutate({
            mutation: HIRING_FORECAST_ADD_UPDATE,
            client: "apolloClientAH",
            fetchPolicy: "no-cache",
            variables: {
              originalPositionId: this.enableCustomPosition
                ? null
                : this.selectedPositionData?.Originalpos_Id?.toString(),
              action:
                this.selectedForecast && this.selectedForecast.foreCastList
                  ? "update"
                  : "add",
              positionTitle: this.enableCustomPosition
                ? this.tempSelectedPositionTitle || ""
                : positionTitleData?.Pos_Name || "",
              divisionId:
                this.enableCustomPosition &&
                this.selectedDivision?.toString().toLowerCase() != "nodivision"
                  ? this.selectedDivision?.toString() || ""
                  : "",
              groupId:
                this.enableCustomPosition && this.selectedGroup != "nogroup"
                  ? this.selectedGroup?.toString() || ""
                  : "",
              departmentId:
                this.enableCustomPosition &&
                this.selectedDepartment?.toString().toLowerCase() !=
                  "nodepartment"
                  ? this.selectedDepartment?.toString() || ""
                  : "",
              sectionId:
                this.enableCustomPosition &&
                String(this.selectedSection || "").toLowerCase() != "nosection"
                  ? this.selectedSection?.toString() || ""
                  : "",
              foreCastList: foreCastListData,
              organizationStructureId: this.enableCustomPosition
                ? this.selectedPositionData?.Organization_Structure_Id || 0
                : positionTitleData?.Organization_Structure_Id,
            },
          })
          .then((res) => {
            this.isLoading = false;
            if (res && res.data) {
              let snackbarData = {
                isOpen: true,
                message: `Hiring forecast ${
                  this.selectedForecast && this.selectedForecast.foreCastList
                    ? "updated"
                    : "added"
                } successfully`,
                type: "success",
              };
              window.scrollTo(0, 0);
              this.showAlert(snackbarData);
              this.$emit("refresh-list");
            }
          })
          .catch((err) => {
            this.isLoading = false;
            this.addEditErrorHandle(err);
          });
      }
    },
    updateGroup() {
      if (this.selectedGroup) {
        this.tempDivision = null;
        this.selectedSection = null;
        this.selectedDepartment = null;
        this.selectedDivision = null;
        this.retrieveCountGroupPosition("group");
        this.isFormDirty = true;
        this.monthList = this.monthList.map((item) => ({
          ...item,
          value: 0,
        }));
      }
    },
    updateDivision() {
      this.selectedSection = null;
      this.selectedDepartment = null;
      this.retrieveCountGroupPosition("division");
    },
    updateDepartment() {
      this.selectedSection = null;
      this.retrieveCountGroupPosition("department");
    },
    updateSection() {
      this.retrieveCountGroupPosition();
    },
    retrieveCountGroupPosition(type = "") {
      this.isDropdownListLoading = true;
      // this.selectedDivision = this.tempDivision;
      let parentId = "0";
      if (type?.toLowerCase() === "mounted")
        parentId =
          this.selectedPositionTitle?.toString()?.toLowerCase() !== "nogroup"
            ? this.selectedPositionTitle?.toString() || ""
            : this.selectedDivisionTitle?.toString() || "";
      else if (this.selectedSection) parentId = this.selectedSection;
      else if (
        this.selectedDepartment &&
        this.selectedDepartment.toString().toLowerCase() !== "nodepartment"
      )
        parentId = this.selectedDepartment;
      else if (
        this.selectedDivision &&
        this.selectedDivision.toString().toLowerCase() !== "nodivision"
      )
        parentId = this.selectedDivision;
      else if (
        this.selectedGroup &&
        this.selectedGroup.toString().toLowerCase() !== "nogroup"
      ) {
        parentId = this.selectedGroup;
      } else if (!this.selectedGroup) {
        parentId = "";
      }
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(parentId),
            limit: this.hiringForeCastLimitToCallAPI,
            offset: 0,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.listDetailsBasedOnGroupCode) {
            if (res.data.listDetailsBasedOnGroupCode.positionDetails) {
              const tempData =
                res.data.listDetailsBasedOnGroupCode.positionDetails;
              if (type === "group")
                this.divisionList = tempData.divisionList || [];
              if (type === "division")
                this.departmentList = tempData.deptList || [];
              if (type === "department")
                this.sectionList = tempData.sectionList || [];

              if (type === "mounted") {
                this.divisionList = tempData.divisionList || [];
                this.departmentList = tempData.deptList || [];
                this.sectionList = tempData.sectionList || [];
              }
              if (
                !this.divisionList.some(
                  (item) => item.Pos_Code === "nodivision"
                )
              )
                this.divisionList.unshift({
                  Pos_Name: "No Division",
                  Pos_Code: "nodivision",
                  Pos_full_Name: "No Division",
                  Originalpos_Id: "nodivision",
                });

              if (
                !this.departmentList.some(
                  (item) => item.Pos_Code === "nodepartment"
                )
              )
                this.departmentList.unshift({
                  Pos_Name: "No Department",
                  Pos_Code: "nodepartment",
                  Pos_full_Name: "No Department",
                  Originalpos_Id: "nodepartment",
                });
              // ✅ Need for validating for later use
              // if (tempData.divisionList && tempData.divisionList.length > 0) {
              //   const result = tempData.divisionList.find(
              //     (item) => item?.Pos_Code === this.selectedDivision
              //   );
              //   this.selectedDivision = result?.Originalpos_Id || null;
              // }
              // if (tempData.deptList && tempData.deptList.length > 0) {
              //   const result = tempData.deptList.find(
              //     (item) => item?.Pos_Code === this.selectedDepartment
              //   );
              //   this.selectedDepartment = result?.Originalpos_Id || null;
              // }
              // if (tempData.sectionList && tempData.sectionList.length > 0) {
              //   const result = tempData.sectionList.find(
              //     (item) => item?.Pos_Code === this.selectedSection
              //   );
              //   this.selectedSection = result?.Originalpos_Id || null;
              // }
            } else {
              this.divisionList = [];
              this.departmentList = [];
              this.sectionList = [];
            }
            let { totalRecords } = res.data.listDetailsBasedOnGroupCode;
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              this.groupApiCallCount = 1;
              this.groupTotalApiCount = Math.ceil(
                totalRecords / this.hiringForeCastLimitToCallAPI
              );
              for (let i = 1; i < this.groupTotalApiCount; i++) {
                this.updateGroupPosition(i, parentId, type);
              }
            }
          }
          this.isDropdownListLoading = false;
        })
        .catch((err) => {
          this.addEditErrorHandle(err);
          this.isDropdownListLoading = false;
        });
    },
    updateGroupPosition(index = 1, parentId = "", type = "") {
      this.isLoading = true;
      let apiOffset = parseInt(index) * this.hiringForeCastLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.isFormDirty = true;
      this.$apollo
        .query({
          query: ORG_STRUCTURE_BASED_ON_GROUP,
          variables: {
            formId: 290,
            postionParentId: String(parentId),
            limit: this.hiringForeCastLimitToCallAPI,
            offset: apiOffset,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listDetailsBasedOnGroupCode &&
            res.data.listDetailsBasedOnGroupCode.positionDetails
          ) {
            const tempData =
              res.data.listDetailsBasedOnGroupCode.positionDetails;
            if (
              tempData.divisionList &&
              tempData.divisionList.length > 0 &&
              type === "group"
            ) {
              this.divisionList = [
                ...this.divisionList,
                ...tempData.divisionList,
              ];
            }
            if (
              tempData.deptList &&
              tempData.deptList.length > 0 &&
              type === "division"
            ) {
              this.departmentList = [
                ...this.departmentList,
                ...tempData.deptList,
              ];
            }
            if (
              tempData.sectionList &&
              tempData.sectionList.length > 0 &&
              type === "department"
            ) {
              this.sectionList = [...this.sectionList, ...tempData.sectionList];
            }
            if (type === "mounted") {
              if (tempData.divisionList && tempData.divisionList.length > 0) {
                this.divisionList = [
                  ...this.divisionList,
                  ...tempData.divisionList,
                ];
              }
              if (tempData.deptList && tempData.deptList.length > 0) {
                this.departmentList = [
                  ...this.departmentList,
                  ...tempData.deptList,
                ];
              }
              if (tempData.sectionList && tempData.sectionList.length > 0) {
                this.sectionList = [
                  ...this.sectionList,
                  ...tempData.sectionList,
                ];
              }
            }
            this.groupApiCallCount = this.groupApiCallCount + 1;
            if (this.groupTotalApiCount === this.groupApiCallCount) {
              this.isLoading = false;
            }
          } else {
            this.isLoading = false;
          }
        })
        .catch((err) => {
          this.addEditErrorHandle(err);
          this.divisionList = [];
          this.isLoading = false;
        });
    },
    addEditErrorHandle(err) {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action:
            this.selectedForecast && this.selectedForecast.foreCastList
              ? "updating"
              : "adding",
          form: "Hiring Forecast details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    retrieveTotalPositionList() {
      this.positionList = [];
      this.positionListLoading = true;
      this.$apollo
        .query({
          query: HIRING_POSITION_LIST,
          fetchPolicy: "no-cache",
          client: "apolloClientAG",
          variables: {
            formId: 287,
            postionParentId: this.selectPositionParentId,
            forecastingYear: this.selectedForecastYear,
            action:
              this.selectedForecast && this.selectedForecast.foreCastList
                ? "update"
                : "add",
            originalPositionId:
              this.selectedPositionData?.Originalpos_Id?.toString() || "",
            offset: 0,
            limit: this.hiringForeCastLimitToCallAPI,
            parentPath: this.parentPathForListForecastPosition,
          },
        })
        .then((res) => {
          this.positionListLoading = false;
          if (
            res &&
            res.data &&
            res.data.listForecastPosition &&
            res.data.listForecastPosition.positionList
          ) {
            this.positionList = res.data.listForecastPosition.positionList;
            let { totalCountResult } = res.data.listForecastPosition;
            if (totalCountResult > 0) {
              totalCountResult = parseInt(totalCountResult);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(
                totalCountResult / this.hiringForeCastLimitToCallAPI
              );
              for (let i = 1; i < this.totalApiCount; i++) {
                this.retrievePositionTitle(i);
              }
            }
          }
        })
        .catch((err) => {
          this.positionListLoading = false;
          this.positionList = [];
          this.handleListError(err);
        });
    },
    retrievePositionTitle(index = 1) {
      let apiOffset = parseInt(index) * this.hiringForeCastLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      this.positionListLoading = true;
      this.$apollo
        .query({
          query: HIRING_POSITION_LIST,
          fetchPolicy: "no-cache",
          client: "apolloClientAG",
          variables: {
            formId: 287,
            postionParentId: this.selectPositionParentId,
            forecastingYear: this.selectedForecastYear,
            action:
              this.selectedForecast && this.selectedForecast.foreCastList
                ? "update"
                : "add",
            originalPositionId:
              this.selectedPositionData?.Originalpos_Id?.toString() || "",
            offset: apiOffset,
            limit: this.hiringForeCastLimitToCallAPI,
            parentPath: this.parentPathForListForecastPosition,
          },
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.listForecastPosition &&
            res.data.listForecastPosition.positionList
          ) {
            this.positionList = [
              ...this.positionList,
              ...res.data.listForecastPosition.positionList,
            ];
            this.apiCallCount = this.apiCallCount + 1;
            if (this.totalApiCount === this.apiCallCount) {
              this.positionListLoading = false;
            }
          } else {
            this.positionListLoading = false;
          }
        })
        .catch((err) => {
          this.positionListLoading = false;
          this.positionList = [];
          this.handleListError(err);
        });
    },
    handleListError(err = "", formName) {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
<style scoped>
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.overlay-footer {
  height: 7%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}
.fixed-title {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 10;
}
</style>
