<template>
  <div>
    <v-overlay
      :model-value="showAddForm"
      @click:outside="onCloseOverlay()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="{
            height: windowHeight + 'px',
            width: windowWidth <= 1264 ? '100vw' : '93vw',
          }"
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">
              Job Analysis Questionnaire
            </div>
            <v-btn
              icon
              class="clsBtn cursor-pointer"
              variant="text"
              @click="onCloseOverlay()"
            >
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </v-card-title>
          <v-form ref="educationForm" class="px-8">
            <v-card class="pa-3 mt-5">
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="amber-lighten-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Education Requirements
              </div>
              <div class="my-2">
                <v-row>
                  <v-col cols="12" class="mx-3">
                    <v-radio-group v-model="selectedEducationData">
                      <div
                        v-for="(item, i) in educationLabelData"
                        :key="i"
                        class="mx-auto px-2 mt-3 bg-white border-sm d-flex justify-start align-center w-100 rounded-lg"
                      >
                        <div class="d-flex flex-column justify-start w-100">
                          <v-radio
                            color="primary"
                            :label="item.label"
                            :value="item.label"
                          ></v-radio>
                          <v-textarea
                            v-if="selectedEducationData == item.label"
                            v-model="selectedEducationDescription"
                            rows="2"
                            row-height="8"
                            counter="150"
                            color="primary"
                            maxlength="150"
                            hide-details="auto"
                            variant="solo"
                            :isRequired="true"
                            :autofocus="true"
                            label=""
                            :rules="[
                              required(
                                'Please specify the course',
                                selectedEducationDescription
                              ),
                              validateWithRulesAndReturnMessages(
                                selectedEducationDescription,
                                'educationDescription',
                                'Please specify the course'
                              ),
                            ]"
                            @update:model-value="onChangeFields()"
                          >
                            <template v-slot:label>
                              <span>Please specify the course</span>
                              <span style="color: red">*</span>
                            </template></v-textarea
                          >
                        </div>
                      </div>
                    </v-radio-group>
                  </v-col>
                </v-row>
              </div>
            </v-card>

            <v-card class="pa-3 mt-10">
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="deep-orange-darken-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Experience Requirements
              </div>
              <div class="my-4">
                <v-data-table
                  :headers="experienceHeaders"
                  :items="experienceFormData"
                  fixed-header
                  :hide-default-footer="true"
                  style="box-shadow: none !important"
                  class="elevation-1"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white"
                      :class="
                        isMobileView
                          ? ' v-data-table__mobile-table-row mt-2'
                          : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 100}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          Type of Job/Positions or Area of Expertise
                        </div>
                        <section class="d-flex align-center">
                          {{ checkNullValue(item.Type_Of_Jobs) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 100}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          No. of Years/Months Required
                        </div>
                        <section class="d-flex align-center">
                          {{
                            `${item?.Years || 0} Years, 
                            ${item?.Months || 0} Months`
                          }}
                        </section>
                      </td>
                      <td
                        class="text-body-2 text-center"
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        @click.stop="
                          {
                          }
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
                        >
                          Actions
                        </div>
                        <ActionMenu
                          @selected-action="onActionsExperience($event, item)"
                          :actions="['Edit', 'Delete']"
                          :access-rights="formAccess"
                          iconColor="grey"
                        ></ActionMenu>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </div>
              <div class="d-flex justify-start py-2 pb-8">
                <v-btn
                  @click="addNewExperience()"
                  class="px-6 mr-2 primary"
                  variant="elevated"
                  size="default"
                >
                  <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
                  <span class="primary"> Add New</span></v-btn
                >
              </div>
            </v-card>
          </v-form>
          <div class="card-actions-div">
            <v-card-actions class="d-flex align-end">
              <v-sheet class="align-center text-center" style="width: 100%">
                <v-row justify="center">
                  <v-col cols="12" class="d-flex justify-space-between pr-6">
                    <div class="d-flex align-center">
                      <v-btn
                        rounded="lg"
                        class="mr-6 primary"
                        @click="onGotoDuties()"
                        variant="outlined"
                      >
                        Previous
                      </v-btn>
                      <v-btn
                        rounded="lg"
                        v-if="selectedPositionData?.Position_Title"
                        class="mr-6 primary"
                        @click="onGotoNextLicense()"
                        variant="outlined"
                        :disabled="disabledNext"
                      >
                        Skip
                      </v-btn>
                    </div>
                    <div class="d-flex align-center">
                      <v-btn
                        rounded="lg"
                        class="mr-6 primary"
                        @click="onCloseOverlay()"
                        variant="outlined"
                      >
                        Cancel
                      </v-btn>
                      <v-btn
                        rounded="lg"
                        class="mr-1 primary"
                        @click="validateEducationForm()"
                        :disabled="!isFormDirty"
                        variant="elevated"
                      >
                        Save & Continue
                      </v-btn>
                    </div>
                  </v-col>
                </v-row>
              </v-sheet>
            </v-card-actions>
          </div>
        </v-card>
        <v-overlay
          :model-value="openAddEditExperience"
          @click:outside="
            {
            }
          "
          persistent
          class="d-flex justify-end"
        >
          <template v-slot:default="{}">
            <v-card
              class="overlay-card"
              :style="{
                height: windowHeight + 'px',
                width: windowWidth <= 1264 ? '100vw' : '40vw',
              }"
            >
              <v-card-title class="d-flex bg-primary">
                <span class="text-h6 text-white font-weight-bold py-2">
                  {{
                    selectedExperienceFormData &&
                    selectedExperienceFormData.Experience_Id
                      ? "Edit"
                      : "Add"
                  }}
                  Experience Requirements</span
                >
                <v-spacer></v-spacer>
                <div
                  @click="openAddEditExperience = false"
                  icon
                  class="clsBtn cursor-pointer"
                  variant="text"
                >
                  <v-icon size="25">fas fa-times</v-icon>
                </div>
              </v-card-title>
              <v-divider></v-divider>
              <v-card-text>
                <v-card
                  class="mx-auto px-3 pt-2 bg-white"
                  rounded="lg"
                  variant="text"
                >
                  <div>
                    <v-form ref="experienceRefForm">
                      <v-row class="d-flex justify-start align-center">
                        <v-col cols="12">
                          <v-text-field
                            v-model="selectedJobPositions"
                            ref="jobPositionsRef"
                            variant="solo"
                            :isRequired="true"
                            :rules="[
                              required(
                                'Type of Job/Positions or Area of Expertise',
                                selectedJobPositions
                              ),
                              validateWithRulesAndReturnMessages(
                                selectedJobPositions,
                                'typeOfJobs',
                                'Type of Job/Positions or Area of Expertise'
                              ),
                            ]"
                            label=""
                            @update:model-value="onChangeFields()"
                          >
                            <template v-slot:label>
                              Type of Job/Positions or Area of Expertise
                              <span style="color: red">*</span>
                            </template></v-text-field
                          >
                        </v-col>
                        <v-col cols="12">
                          <p class="custom-label">
                            Total Experience
                            <span style="color: red">*</span>
                          </p>
                          <div class="d-flex">
                            <formTextFeild
                              ref="totalExperienceInYears"
                              v-model="noOfYears"
                              type="number"
                              :isRequired="true"
                              :rules="[
                                numericRequiredValidation(
                                  'Total Experience (In years)',
                                  noOfYears
                                ),
                                minMaxNumberValidation(
                                  'Total Experience (In years) maximum is 100',
                                  noOfYears,
                                  0,
                                  100
                                ),
                              ]"
                              @update:model-value="onChangeFields()"
                              ><template v-slot:append-inner>
                                Years
                              </template></formTextFeild
                            >
                            <formTextFeild
                              ref="totalExperienceInMonths"
                              v-model="noOfMonths"
                              type="number"
                              :isRequired="true"
                              :rules="[
                                numericRequiredValidation(
                                  'Total Experience (In months)',
                                  noOfMonths
                                ),
                                minMaxNumberValidation(
                                  'Total Experience (In months) maximum is 12',
                                  noOfMonths,
                                  0,
                                  12
                                ),
                              ]"
                              class="ml-3"
                              @update:model-value="onChangeFields()"
                              ><template v-slot:append-inner>
                                Months
                              </template></formTextFeild
                            >
                          </div>
                        </v-col>
                      </v-row>
                    </v-form>
                  </div>
                </v-card>
                <div class="card-actions-div">
                  <v-card-actions class="d-flex align-end">
                    <v-sheet
                      class="align-center text-center"
                      style="width: 100%"
                    >
                      <v-row justify="center">
                        <v-col cols="12" class="d-flex justify-end pr-6">
                          <v-btn
                            rounded="lg"
                            variant="outlined"
                            class="mr-3 primary"
                            @click="openAddEditExperience = false"
                          >
                            Cancel
                          </v-btn>
                          <v-btn
                            rounded="lg"
                            class="secondary"
                            variant="elevated"
                            @click="submitExperience()"
                            :disabled="!isFormDirty"
                          >
                            {{
                              selectedExperienceFormData &&
                              selectedExperienceFormData.Experience_Id
                                ? "Update"
                                : "Submit"
                            }}
                          </v-btn>
                        </v-col></v-row
                      ></v-sheet
                    ></v-card-actions
                  >
                </div></v-card-text
              ></v-card
            ></template
          >
        </v-overlay>
      </template></v-overlay
    >
  </div>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit Education & Experience form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="confirmClose()"
  >
  </AppWarningModal>
  <AppWarningModal
    v-if="openConfirmationDelete"
    :open-modal="openConfirmationDelete"
    confirmation-heading="Are you sure you want to delete this record ?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openConfirmationDelete = false"
    @accept-modal="confirmDelete()"
  >
  </AppWarningModal>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import {
  ADD_EDIT_EDUCATION,
  ADD_EDIT_EXPERIENCES,
  DELETE_JOB_ASSESSMENT,
  EDUCATION_STATIC_DATA_LIST,
} from "@/graphql/mpp/newPositionQueries";
import { checkNullValue } from "@/helper";
import validationRules from "@/mixins/validationRules";

export default {
  name: "EducationExperience",
  mixins: [validationRules],
  emits: [
    "submitEducationalForm",
    "closeEducationalForm",
    "goto-duties",
    "goto-next-license-form",
    "refresh-form-data",
  ],
  props: {
    showEducationalForm: {
      type: Boolean,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
    selectedPositionData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      showAddForm: false,
      openConfirmationPopup: false,
      openAddEditExperience: false,
      isFormDirty: false,
      educationLabelData: [],
      experienceFormData: [],
      selectedJobPositions: null,
      selectedEducationData: null,
      selectedEducationDescription: null,
      noOfMonths: null,
      noOfYears: null,
      selectedExperienceFormData: {},
      isLoading: false,
      openConfirmationDelete: false,
    };
  },
  components: {
    ActionMenu,
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    disabledNext() {
      if (this.isFormDirty) {
        return true;
      } else {
        if (this.selectedEducationDescription) {
          return false;
        }
        return true;
      }
    },
    experienceHeaders() {
      return [
        {
          title: "Type of Job/Positions or Area of Expertise",
          align: "start",
          key: "Type_Of_Jobs",
        },
        {
          title: "No. of Years/Months Required",
          align: "start",
          sortable: false,
          key: "Months",
        },
        {
          title: "Actions",
          align: "start",
          key: "action",
          sortable: false,
        },
      ];
    },
  },
  watch: {
    showEducationalForm(val) {
      this.showAddForm = val;
      if (val) {
        this.retrieveEducationalDetails();
      }
    },
    selectedPositionData(val) {
      if (val && val.Experience) {
        this.experienceFormData = val.Experience;
      } else {
        this.experienceFormData = [];
      }
    },
  },
  methods: {
    checkNullValue,
    onActionsExperience(key, item) {
      this.selectedExperienceFormData = item;
      if (key === "Edit") {
        this.openAddEditExperience = true;
        this.selectedJobPositions = item?.Type_Of_Jobs;
        this.noOfMonths = item?.Months;
        this.noOfYears = item?.Years;
      } else {
        this.openConfirmationDelete = true;
      }
    },
    confirmDelete() {
      this.openConfirmationDelete = false;
      this.handleDeleteExperience(this.selectedExperienceFormData);
    },
    addNewExperience() {
      this.openAddEditExperience = true;
      this.selectedExperienceFormData = null;
      this.selectedJobPositions = "";
      this.noOfMonths = null;
      this.noOfYears = null;
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onCloseOverlay() {
      this.openConfirmationPopup = true;
    },
    onGotoDuties() {
      this.showAddForm = false;
      this.$emit("goto-duties");
    },
    onGotoNextLicense() {
      this.showAddForm = false;
      this.$emit("goto-next-license-form");
    },
    confirmClose() {
      this.openConfirmationPopup = false;
      this.showAddForm = false;
      this.isFormDirty = false;
      this.$emit("closeEducationalForm");
    },
    submitEducationForm() {
      this.showAddForm = false;
      this.isFormDirty = false;
      this.$emit("refresh-form-data");
      this.$emit("submitEducationalForm");
    },
    handleDeleteExperience(item) {
      this.isLoading = true;
      const requestPayload = {
        deleteId: item.Experience_Id,
        tableKeyword: "experience",
        positionRequestId: this.selectedPositionData?.Position_Request_Id || 0,
        status:
          (this.selectedPositionData && this.selectedPositionData.Status) ||
          "Draft",
      };
      this.$apollo
        .mutate({
          mutation: DELETE_JOB_ASSESSMENT,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: requestPayload,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.deleteOpenPositionSubTable &&
            res.data.deleteOpenPositionSubTable.message
          ) {
            let snackbarData = {
              isOpen: true,
              message: res.data.deleteOpenPositionSubTable.message,
              type: "success",
            };
            this.showAlert(snackbarData);
            this.selectedExperienceFormData = null;
            this.$emit("refresh-form-data");
          } else {
            this.handleEducationExperienceErrors();
          }
        })
        .catch((err) => {
          this.handleEducationExperienceErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    retrieveEducationalDetails() {
      this.isLoading = true;
      this.$apollo
        .query({
          query: EDUCATION_STATIC_DATA_LIST,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.retrieveEducationLabelList &&
            res.data.retrieveEducationLabelList.educationLabelList &&
            res.data.retrieveEducationLabelList.educationLabelList.length
          ) {
            this.educationLabelData =
              res.data.retrieveEducationLabelList.educationLabelList.map(
                (item) => {
                  const selectedData =
                    this.selectedPositionData?.education?.find(
                      (list) =>
                        list.Education_Type.toLowerCase().trim() ===
                        item.Education_Type.toLowerCase().trim()
                    ) || {};
                  return {
                    educationRequirementId: item.Mpp_Education_Requirements_Id,
                    mppEducationRequirementsDescriptionsId:
                      (selectedData &&
                        selectedData.Mpp_Education_Requirements_Descriptions_Id) ||
                      0,
                    label:
                      (selectedData && selectedData.Education_Type) ||
                      item.Education_Type,
                  };
                }
              );
            this.selectedEducationDescription =
              this.selectedPositionData?.education[0]?.Description || "";
            this.selectedEducationData =
              this.selectedPositionData?.education[0]?.Education_Type || "";
          } else {
            this.educationLabelData = [];
          }
        })
        .catch((err) => {
          this.handleEducationExperienceErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    validateEducationForm() {
      if (this.selectedEducationData) {
        this.submitEducation();
      } else {
        let snackbarData = {
          isOpen: true,
          message:
            "Please add at least one educational requirement before submitting the form.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    async submitEducation() {
      const { valid } = await this.$refs.educationForm.validate();
      if (!valid) {
        return;
      }
      let requestPayload = {
        status:
          (this.selectedPositionData && this.selectedPositionData.Status) ||
          "Draft",
        action:
          this.selectedPositionData &&
          this.selectedPositionData.education &&
          this.selectedPositionData.education.length
            ? "update"
            : "add",
        positionRequestId: this.selectedPositionData?.Position_Request_Id || 0,
        input: [],
      };
      const selectedEducation = this.educationLabelData.find(
        (item) => item.label === this.selectedEducationData
      );
      if (selectedEducation) {
        requestPayload.input = [
          {
            educationRequirementId:
              selectedEducation.educationRequirementId || 0,
            positionRequestId:
              this.selectedPositionData?.Position_Request_Id || 0,
            mppEducationRequirementsDescriptionsId: this.selectedPositionData
              .education.length
              ? this.selectedPositionData.education[0]
                  .Mpp_Education_Requirements_Descriptions_Id
              : selectedEducation.mppEducationRequirementsDescriptionsId || 0,
            educationDescription:
              this.selectedEducationDescription.trim() || "",
          },
        ];
      }
      this.isLoading = true;
      this.$apollo
        .mutate({
          mutation: ADD_EDIT_EDUCATION,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: requestPayload,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.addUpdateEducationRequirementsDescriptions &&
            res.data.addUpdateEducationRequirementsDescriptions.message
          ) {
            let snackbarData = {
              isOpen: true,
              message:
                res.data.addUpdateEducationRequirementsDescriptions.message,
              type: "success",
            };
            this.showAlert(snackbarData);
            this.submitEducationForm();
          } else {
            this.handleEducationExperienceErrors();
          }
        })
        .catch((err) => {
          this.handleEducationExperienceErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    async submitExperience() {
      const { valid } = await this.$refs.experienceRefForm.validate();
      if (!valid) {
        return;
      }
      this.isLoading = true;
      let requestPayload = {
        experienceId: 0,
        typeOfJobs: this.selectedJobPositions,
        months: parseInt(this.noOfMonths),
        years: parseInt(this.noOfYears),
        positionRequestId: this.selectedPositionData?.Position_Request_Id || 0,
        status:
          (this.selectedPositionData && this.selectedPositionData.Status) ||
          "Draft",
        eventId:
          (this.selectedPositionData && this.selectedPositionData.Event_Id) ||
          null,
      };

      if (
        this.selectedExperienceFormData &&
        this.selectedExperienceFormData.Experience_Id
      ) {
        requestPayload.experienceId =
          this.selectedExperienceFormData.Experience_Id;
        this.typeOfJobs = this.selectedJobPositions;
        this.months = parseInt(this.noOfMonths) || 0;
        this.years = parseInt(this.noOfYears) || 0;
        this.positionRequestId =
          this.selectedPositionData?.Position_Request_Id || 0;
        requestPayload.status =
          (this.selectedPositionData && this.selectedPositionData.Status) ||
          "Draft";
        requestPayload.eventId =
          (this.selectedPositionData && this.selectedPositionData.Event_Id) ||
          null;
      }
      this.$apollo
        .mutate({
          mutation: ADD_EDIT_EXPERIENCES,
          client: "apolloClientAH",
          fetchPolicy: "no-cache",
          variables: requestPayload,
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.addUpdateExperience &&
            res.data.addUpdateExperience.message
          ) {
            let snackbarData = {
              isOpen: true,
              message: res.data.addUpdateExperience.message,
              type: "success",
            };
            this.showAlert(snackbarData);
            this.openAddEditExperience = false;
            this.$emit("refresh-form-data");
          } else {
            this.handleEducationExperienceErrors();
          }
        })
        .catch((err) => {
          this.handleEducationExperienceErrors(err);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    handleEducationExperienceErrors(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "Updating",
          form: "Job Analysis Questionnaire details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style scoped>
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.overlay-card {
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
