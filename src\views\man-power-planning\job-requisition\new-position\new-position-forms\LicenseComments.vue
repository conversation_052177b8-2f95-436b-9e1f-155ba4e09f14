<template>
  <div>
    <v-overlay
      :model-value="showAddForm"
      @click:outside="onCloseOverlay()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="{
            height: windowHeight + 'px',
            width: windowWidth <= 1264 ? '100vw' : '93vw',
          }"
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">
              Job Analysis Questionnaire
            </div>
            <v-btn
              icon
              class="clsBtn cursor-pointer"
              variant="text"
              @click="onCloseOverlay()"
            >
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </v-card-title>
          <v-form ref="commentsForm" class="px-8">
            <v-card class="pa-3 mt-5">
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="pink-lighten-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                License And/ Or Certifications
              </div>
              <div class="my-4">
                <v-row>
                  <v-col cols="12">
                    <v-radio-group
                      inline
                      color="primary"
                      @update:model-value="isFormDirty = true"
                      v-model="selectedLicense"
                    >
                      <v-radio label="Preferred" value="Preferred"></v-radio>
                      <v-radio label="Required" value="Required"></v-radio>
                      <v-radio
                        label="Not Required"
                        value="Not Required"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                  <v-col cols="12" md="6" sm="12" class="py-0">
                    <v-textarea
                      v-if="selectedLicense !== 'Not Required'"
                      v-model="licenseDescription"
                      variant="solo"
                      auto-grow
                      :isRequired="true"
                      rows="1"
                      :rules="[
                        required(
                          'License and/ or Certifications description',
                          licenseDescription
                        ),
                        validateWithRulesAndReturnMessages(
                          licenseDescription,
                          'licenseCertificateDetails',
                          'License and/ or Certifications description'
                        ),
                      ]"
                      @update:model-value="isFormDirty = true"
                      :counter="150"
                      ><template v-slot:label>
                        License and/ or Certifications description
                        <span style="color: red">*</span>
                      </template></v-textarea
                    >
                  </v-col>
                </v-row>
              </div>
            </v-card>
            <v-card class="pa-3 mt-5">
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="teal-darken-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Operating Network / Working Relationships
              </div>
              <div class="my-4">
                <v-row>
                  <v-col cols="12" md="6">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      Internal
                      <span style="color: red">*</span>
                    </p>
                    <v-text-field
                      ref="inter"
                      v-model="input"
                      @update:modelValue="showAddIcon"
                      variant="solo"
                      :rules="[
                        alphaNumSpaceNewLineWithElevenSymbolValidation(input),
                        required('Internal Operating Network', internalSet[0]),
                        maxLengthValidation(
                          'Internal Operating Network',
                          input,
                          300
                        ),
                      ]"
                      @keydown.enter.prevent="addChip"
                    >
                      <template v-slot:default>
                        <v-icon v-if="showIcon" @click="addChip" size="x-small"
                          >fas fa-plus</v-icon
                        >
                        <v-chip
                          v-for="(chip, index) in internalSet"
                          append-icon="fas fa-times-circle"
                          :key="index"
                          class="ma-1"
                          @click="removeChip(index)"
                        >
                          {{ chip }}
                        </v-chip>
                      </template></v-text-field
                    >
                  </v-col>
                  <v-col cols="12" md="6">
                    <p class="text-subtitle-1 text-grey-darken-1">
                      External
                      <span style="color: red">*</span>
                    </p>
                    <v-text-field
                      ref="external"
                      v-model="inputExternal"
                      @update:modelValue="showAddIconExternal"
                      variant="solo"
                      :rules="[
                        alphaNumSpaceNewLineWithElevenSymbolValidation(
                          inputExternal
                        ),
                        required('External Operating Network', externalSet[0]),
                        maxLengthValidation(
                          'External Operating Network',
                          inputExternal,
                          300
                        ),
                      ]"
                      @keydown.enter.prevent="addExternalChip"
                    >
                      <template v-slot:default>
                        <v-icon
                          v-if="showIconExternal"
                          @click="addExternalChip"
                          size="x-small"
                          >fas fa-plus</v-icon
                        >
                        <v-chip
                          v-for="(chip, index) in externalSet"
                          append-icon="fas fa-times-circle"
                          :key="index"
                          class="ma-1"
                          @click="removeExternalChip(index)"
                        >
                          {{ chip }}
                        </v-chip>
                      </template></v-text-field
                    >
                  </v-col>
                </v-row>
              </div>
            </v-card>
            <v-card class="pa-3 mt-5">
              <div
                class="d-flex align-center text-h6 text-grey-darken-1 font-weight-bold"
              >
                <v-progress-circular
                  model-value="100"
                  color="purple-darken-2"
                  :size="22"
                  class="mr-2"
                ></v-progress-circular>
                Additional Comments
              </div>
              <div class="my-4">
                <v-row>
                  <v-col cols="12" md="6" sm="12" class="pb-3">
                    <v-textarea
                      v-model="description"
                      variant="solo"
                      auto-grow
                      rows="1"
                      :rules="[
                        validateWithRulesAndReturnMessages(
                          description,
                          'comments',
                          'Additional Comments'
                        ),
                      ]"
                      @update:model-value="isFormDirty = true"
                      :counter="600"
                      ><template v-slot:label>
                        Add any additional details for this new position that
                        wasn't captured earlier
                      </template></v-textarea
                    >
                  </v-col>
                </v-row>
              </div>
            </v-card></v-form
          >
          <div class="card-actions-div">
            <v-card-actions class="d-flex align-end">
              <v-sheet class="align-center text-center" style="width: 100%">
                <v-row justify="center">
                  <v-col cols="12" class="d-flex justify-space-between pr-6">
                    <v-btn
                      rounded="lg"
                      class="mr-6 primary"
                      @click="onGotoEducation()"
                      variant="outlined"
                    >
                      Previous
                    </v-btn>
                    <div class="d-flex align-center">
                      <v-btn
                        rounded="lg"
                        variant="outlined"
                        class="mr-3 primary"
                        @click="onCloseOverlay()"
                      >
                        Cancel
                      </v-btn>
                      <v-btn
                        rounded="lg"
                        class="primary"
                        variant="elevated"
                        @click="addEditLicenseComments()"
                        :disabled="!isFormDirty"
                      >
                        Submit
                      </v-btn>
                    </div>
                  </v-col></v-row
                ></v-sheet
              ></v-card-actions
            >
          </div></v-card
        >
      </template></v-overlay
    >
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit License form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="confirmClose()"
  >
  </AppWarningModal>
</template>
<script>
import { ADD_EDIT_EXT_INT_NEW_POSITION } from "@/graphql/mpp/newPositionQueries";
import validationRules from "@/mixins/validationRules";

export default {
  name: "LicenseComments",
  emits: [
    "submitLicenseForm",
    "closeLicenseForm",
    "enable-workflow-form-data",
    "goto-education-form",
    "refresh-form-data",
    "edit-form-submit",
  ],
  mixins: [validationRules],
  props: {
    showLicenseForm: {
      type: Boolean,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
    selectedPositionData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      showAddForm: false,
      openConfirmationPopup: false,
      selectedLicense: null,
      description: null,
      isFormDirty: false,
      showIcon: true,
      showIconExternal: true,
      internalSet: [],
      externalSet: [],
      input: null,
      inputExternal: null,
      isLoading: false,
      licenseDescription: "",
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    isInputValid() {
      const rules = [
        this.alphaNumSpaceNewLineWithElevenSymbolValidation(this.input),
        this.required("Internal", this.internalSet[0]),
      ];
      return rules[0] === true ? true : false;
    },
    isExternalInputValid() {
      const rules = [
        this.alphaNumSpaceNewLineWithElevenSymbolValidation(this.input),
        this.required("Internal", this.internalSet[0]),
      ];
      return rules[0] === true ? true : false;
    },
  },
  watch: {
    showLicenseForm(val) {
      this.showAddForm = val;
    },
    selectedPositionData(val) {
      if (val) {
        this.description = val?.Comments;
        if (
          val?.Internal_Operating_Network &&
          val?.Internal_Operating_Network.length
        ) {
          this.internalSet = val?.Internal_Operating_Network;
        } else {
          this.internalSet = [];
        }
        if (
          val?.External_Operating_Network &&
          val?.External_Operating_Network.length
        ) {
          this.externalSet = val?.External_Operating_Network;
        } else {
          this.externalSet = [];
        }
        if (val?.License_Certificate) {
          this.selectedLicense = val.License_Certificate;
        } else {
          this.selectedLicense = null;
        }
        if (val?.License_Certificate_Details) {
          this.licenseDescription = val.License_Certificate_Details;
        } else {
          this.licenseDescription = "";
        }
      }
    },
  },
  methods: {
    onCloseOverlay() {
      this.openConfirmationPopup = true;
    },
    confirmClose() {
      this.openConfirmationPopup = false;
      this.showAddForm = false;
      this.isFormDirty = false;
      this.$emit("closeLicenseForm");
    },
    async addEditLicenseComments() {
      if (this.selectedLicense == null) {
        let snackbarData = {
          isOpen: true,
          message: "Please select license certificate",
          type: "warning",
        };
        this.showAlert(snackbarData);
      } else {
        const { valid } = await this.$refs.commentsForm.validate();
        if (valid) {
          this.isLoading = true;
          let requestPayload = {
            positionRequestId:
              this.selectedPositionData?.Position_Request_Id || 0,
            internalOperatingNetwork: this.internalSet,
            externalOperatingNetwork: this.externalSet,
            licenseCertificate: this.selectedLicense,
            licenseCertificateDetails: this.licenseDescription,
            comments: this.description,
            eventId:
              (this.selectedPositionData &&
                this.selectedPositionData.Event_Id) ||
              null,
            status:
              (this.selectedPositionData && this.selectedPositionData.Status) ||
              "Draft",
          };
          this.$apollo
            .mutate({
              mutation: ADD_EDIT_EXT_INT_NEW_POSITION,
              client: "apolloClientAH",
              fetchPolicy: "no-cache",
              variables: requestPayload,
            })
            .then((res) => {
              if (
                res &&
                res.data &&
                res.data.updateExtIntNewPosition &&
                res.data.updateExtIntNewPosition.message
              ) {
                let snackbarData = {
                  isOpen: true,
                  message: res.data.updateExtIntNewPosition.message,
                  type: "success",
                };
                this.showAlert(snackbarData);
                if (
                  this.selectedPositionData &&
                  this.selectedPositionData.Status &&
                  this.selectedPositionData.Status.toLowerCase() ===
                    "waiting for approval"
                ) {
                  this.$emit("edit-form-submit");
                } else {
                  this.$emit("refresh-form-data");
                  this.$emit("enable-workflow-form-data");
                }
              } else {
                this.handleEducationExperienceErrors();
              }
            })
            .catch((err) => {
              this.handleEducationExperienceErrors(err);
            })
            .finally(() => {
              this.isLoading = false;
            });
        }
      }
    },
    onGotoEducation() {
      this.showAddForm = false;
      this.$emit("goto-education-form");
    },
    handleEducationExperienceErrors(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "Updating",
          form: "Job Analysis Questionnaire details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message: validationMessages[0],
          };
          this.showAlert(snackbarData);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    showAddIcon() {
      this.showIcon = !!this.input.trim();
    },
    addChip() {
      if (this.isInputValid && this.input.trim() && this.input.length < 300) {
        this.internalSet.push(this.input.trim());
        this.input = "";
        this.showIcon = false;
        this.isFormDirty = true;
      }
    },
    removeChip(index) {
      this.internalSet.splice(index, 1);
      this.isFormDirty = true;
    },
    showAddIconExternal() {
      this.showIconExternal = !!this.inputExternal.trim();
    },
    addExternalChip() {
      if (
        this.isExternalInputValid &&
        this.inputExternal.trim() &&
        this.inputExternal.length < 300
      ) {
        this.externalSet.push(this.inputExternal.trim());
        this.inputExternal = "";
        this.showIconExternal = false;
        this.isFormDirty = true;
      }
    },
    removeExternalChip(index) {
      this.externalSet.splice(index, 1);
      this.isFormDirty = true;
    },
  },
};
</script>
<style scoped>
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.overlay-card {
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
