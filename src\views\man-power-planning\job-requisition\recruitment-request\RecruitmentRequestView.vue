<template>
  <div>
    <v-overlay
      :model-value="showViewForm"
      @click:outside="onCloseView()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:50vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">
              <span class="text-wrap">
                Approved & Forecasted Positions Details</span
              >
            </div>
            <div class="d-flex align-center">
              <v-btn icon class="clsBtn" variant="text" @click="onCloseView()">
                <v-icon>fas fa-times</v-icon>
              </v-btn>
            </div>
          </v-card-title>
          <div class="d-flex justify-end align-center">
            <v-btn
              v-if="
                formAccess &&
                formAccess.update &&
                selectedPositionData &&
                (loginEmployeeName?.toLowerCase() ===
                  selectedPositionData?.Added_By?.toLowerCase() ||
                  isAdmin) &&
                enableEdit(selectedPositionData)
              "
              @click="onEditPosition()"
              class="mr-3 mt-3 bg-white text-primary"
              variant="text"
              rounded="lg"
            >
              <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
          <div
            class="px-6"
            v-if="selectedPositionData && selectedPositionData.Pos_Name"
          >
            <v-row>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Position</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Pos_Name)
                    }}{{
                      selectedPositionData.Pos_Code
                        ? " - " + selectedPositionData.Pos_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Group</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Group_Name)
                    }}{{
                      selectedPositionData.Group_Code
                        ? " - " + selectedPositionData.Group_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Division</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Division_Name)
                    }}{{
                      selectedPositionData.Division_Code
                        ? " - " + selectedPositionData.Division_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Department</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Department_Name)
                    }}{{
                      selectedPositionData.Department_Code
                        ? " - " + selectedPositionData.Department_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Section</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Section_Name)
                    }}{{
                      selectedPositionData.Section_Code
                        ? " - " + selectedPositionData.Section_Code
                        : ""
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Cost Center</div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Cost_Center) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Reason for Replacement
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{
                      checkNullValue(
                        selectedPositionData.Reason_For_Replacement
                      )
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Replacement For
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Replacement_For) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Position Level
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.Position_Level) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  Employee Type
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{
                      checkNullValue(selectedPositionData.Employee_Type_Name)
                    }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">
                  No of Positions
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.No_Of_Position) }}
                  </section>
                </div>
              </v-col>
              <v-col
                v-if="
                  customGroupCoverage &&
                  customGroupCoverage.toLowerCase() === 'custom group'
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 pb-0"
              >
                <div class="text-subtitle-1 font-weight-bold">
                  Recruiters Group for Notification
                </div>
                <div class="value-text">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedPositionData.CustomGroupName) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                <div class="text-subtitle-1 font-weight-bold">Status</div>
                <div class="value-text">
                  <section class="text-body-2">
                    <span
                      id="w-80"
                      :class="getStatusClass(selectedPositionData?.Status)"
                      class="text-body-2 font-weight-regular d-flex justify-start align-center"
                      >{{ selectedPositionData?.Status }}</span
                    >
                  </section>
                </div>
              </v-col>
            </v-row>
            <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails> </v-col
            ></v-row></div></v-card></template
    ></v-overlay>
  </div>
</template>

<script>
import { checkNullValue } from "@/helper";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";

export default {
  name: "RecruitmentRequestView",
  emits: ["close-view-details", "edit-position-details"],
  props: {
    selectedPositionData: {
      type: Object,
      required: true,
    },
    enableView: {
      type: Boolean,
      required: true,
    },
    customGroupCoverage: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showViewForm: false,
      moreDetailsList: [],
      openMoreDetails: true,
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    formAccess() {
      let formAccess = this.accessRights("291");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },
  watch: {
    enableView: {
      handler(val) {
        this.showViewForm = val;
        this.prefillMoreDetails();
      },
      immediate: true,
    },
  },
  components: {
    MoreDetails,
  },
  mounted() {
    this.showViewForm = this.enableView;
  },
  methods: {
    checkNullValue,
    onCloseView() {
      this.showViewForm = false;
      this.$emit("close-view-details");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(
          new Date(this.selectedPositionData.Added_On + ".000Z")
        ),
        addedByName = this.selectedPositionData.Added_By,
        updatedByName = this.selectedPositionData.Updated_By,
        updatedOn = this.formatDate(
          new Date(this.selectedPositionData.Updated_On + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    getStatusClass(status) {
      if (status === "Open") {
        return "text-amber-darken-4";
      } else if (status === "Closed") {
        return "text-amber";
      } else if (status === "Shortlisted") {
        return "text-purple-darken-4";
      } else if (status === "Scheduled For Interview") {
        return "text-green";
      } else if (status === "Approved") {
        return "text-brown-darken-4";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    onEditPosition() {
      this.showViewForm = false;
      this.$emit("edit-position-details");
    },
    enableEdit(item) {
      if (item?.Status?.toLowerCase() === "rejected") {
        return false;
      }
      if (!item?.jobPostStatus) {
        return true;
      } else if (item?.jobPostStatus?.toLowerCase() === "open") {
        return false;
      } else if (item?.jobPostStatus?.toLowerCase() === "closed") {
        if (item?.candidateCount <= 0) {
          return true;
        } else {
          return false;
        }
      }
    },
  },
};
</script>
<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
</style>
