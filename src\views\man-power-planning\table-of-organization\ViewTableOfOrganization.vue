<template>
  <v-overlay
    :model-value="enableView"
    @click:outside="onCloseView()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-to-card position-relative"
        :style="
          windowWidth <= 1264
            ? 'width:100vw; height: 100vh'
            : 'width:50vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2 text-wrap">
            Table of Organization Details
          </div>
          <div class="d-flex align-center">
            <v-btn icon class="clsBtn" variant="text" @click="onCloseView()">
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </div>
        </v-card-title>
        <div class="d-flex justify-end align-center">
          <v-btn
            v-if="
              formAccess && formAccess.update && selectedItem.Status !== 'Draft'
            "
            @click="onEditPosition()"
            class="mr-3 mt-3 bg-white text-primary"
            variant="text"
            rounded="lg"
          >
            <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
          </v-btn>
        </div>
        <div class="pa-6" v-if="selectedItem && selectedItem.Pos_Name">
          <v-row>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Position Title</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(selectedItem.Pos_Name) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Position Code</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(selectedItem.Pos_Code) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Job Title Code</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(selectedItem.Job_Title_Code) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Cost Center</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(selectedItem.Cost_Code) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">
                No. of Approved Positions
              </div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(selectedItem.Approved_Position) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Warm Bodies</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(selectedItem.Warm_Bodies) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">
                Approved Vacant Positions
              </div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(selectedItem.To_Be_Hired) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Career Band</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(careerBand) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">
                Skills and Compentencies
              </div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(skills) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Education</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(education) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Experience</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(experience) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">
                Work Conditions
              </div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(conditions) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">
                Operating Network (Internal)
              </div>
              <div class="value-text">
                <section class="text-body-2">
                  {{
                    internalRelation?.length
                      ? internalRelation.filter((item) => item).join(", ")
                      : "-"
                  }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">
                Operating Network (External)
              </div>
              <div class="value-text">
                <section class="text-body-2">
                  {{
                    externalRelation?.length
                      ? externalRelation.filter((item) => item).join(", ")
                      : "-"
                  }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">
                Minimum Requirements
              </div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(requirements) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Status</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(selectedItem.Status) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">Job Summary</div>
              <div class="value-text">
                <section class="text-body-2">
                  {{ checkNullValue(jobSummary) }}
                </section>
              </div>
            </v-col>
            <v-col cols="12" class="px-md-6 pb-0">
              <div class="text-subtitle-1 font-weight-bold">
                Job Description
              </div>
              <div id="view-editor">
                <div ref="editorView" class="quill-editorView"></div>
              </div>
            </v-col>
            <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0"></v-col>
            <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails> </v-col
            ></v-row>
          </v-row>
        </div>
      </v-card>
    </template>
  </v-overlay>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import { checkNullValue } from "@/helper";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";
import { RETRIEVE_POSITION_JOB_SUMMARY } from "@/graphql/settings/irukka-integration/jobPostFormQueries";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";

export default {
  name: "ViewTableOfOrganization",
  emits: ["close-form", "open-edit-form"],
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    enableView: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      jobDescription: "",
      isLoading: false,
      careerBand: "",
      jobSummary: "",
      skills: "",
      education: "",
      experience: "",
      conditions: "",
      internalRelation: [],
      externalRelation: [],
      requirements: "",
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    formAccess() {
      let formAccess = this.accessRights("288");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },
  components: {
    MoreDetails,
  },
  mounted() {
    this.prefillMoreDetails();
    this.retrieveJobDescription(this.selectedItem.Organization_Structure_Id);
  },
  methods: {
    checkNullValue,
    onCloseView() {
      this.$emit("close-form");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(
          new Date(this.selectedItem.Added_On + ".000Z")
        ),
        addedByName = this.selectedItem.Added_By,
        updatedByName = this.selectedItem.Updated_By,
        updatedOn = this.formatDate(
          new Date(this.selectedItem.Updated_On + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    getStatusClass(status) {
      if (status === "Open") {
        return "text-amber-darken-4";
      } else if (status === "Closed") {
        return "text-amber";
      } else if (status === "Shortlisted") {
        return "text-purple-darken-4";
      } else if (status === "Scheduled For Interview") {
        return "text-green";
      } else if (status === "Approved") {
        return "text-brown-darken-4";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    onEditPosition() {
      this.showViewForm = false;
      this.$emit("open-edit-form");
    },
    retrieveJobDescription(positionId) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_POSITION_JOB_SUMMARY,
          variables: {
            originalPositionId: "" + positionId,
            formId: 288,
          },
          fetchPolicy: "no-cache",
          client: "apolloClientAG",
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.retrievePositionJobSummary &&
            data.retrievePositionJobSummary.summary
          ) {
            let { summary } = data.retrievePositionJobSummary;
            vm.jobDescription = summary.Job_Description;
            vm.careerBand = summary.Career_Band;
            vm.jobSummary = summary.Job_Summary;
            vm.skills = summary.Skills_Competencies;
            vm.education = summary.Education;
            vm.experience = summary.Experience;
            vm.conditions = summary.Working_Conditions;
            vm.internalRelation = summary.Working_Relationship_Internal;
            vm.externalRelation = summary.Working_Relationship_External;
            vm.requirements = summary.Minimum_Requirements;
            vm.initQuillEditor();
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editorView, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.quill.root.innerHTML = this.jobDescription;
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
      });
      this.quill.enable(false);
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editorView.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
  },
};
</script>
<style>
.overlay-to-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
#view-editor .quill-editorView {
  height: auto;
}
#view-editor > .ql-toolbar {
  display: none !important;
}
#view-editor .ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
#view-editor .ql-editor {
  padding: 0px;
}
</style>
