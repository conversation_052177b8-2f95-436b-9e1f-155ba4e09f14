<template>
  <div>
    <AppAccessDenied v-if="!mainTabs?.isAnyOneFormHaveAccess"></AppAccessDenied>
  </div>
</template>
<script setup>
import { computed, onMounted } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";

const store = useStore();
const router = useRouter();

const mainTabs = computed(() => {
  return store.getters.myDeclarationTabs;
});
onMounted(() => {
  if (mainTabs.value?.isAnyOneFormHaveAccess) {
    let { formsWithAccess } = mainTabs.value;
    for (let form of formsWithAccess) {
      if (form.havingAccess) {
        router.push("/my-finance/my-declarations/" + form.url);
        break;
      }
    }
  }
});
</script>
