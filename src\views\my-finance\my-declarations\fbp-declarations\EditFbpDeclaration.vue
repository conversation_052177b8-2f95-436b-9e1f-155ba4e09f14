<template>
  <v-form ref="fbpDeclarationForm">
    <v-row>
      <v-col cols="12" class="d-flex justify-end mb-4">
        <div>
          <v-btn
            rounded="lg"
            variant="text"
            size="small"
            elevation="4"
            color="primary"
            @click="emit('close-edit-form')"
          >
            {{ t("common.cancel") }}
          </v-btn>
          <v-btn
            rounded="lg"
            class="ml-3"
            size="small"
            color="primary"
            @click="validateFbpDeclaration()"
          >
            {{ t("common.save") }}
          </v-btn>
        </div>
      </v-col>
      <v-col cols="12">
        <v-row>
          <v-col cols="12" sm="4" md="3">
            <div class="d-flex align-center">
              Total Allocated Amount
              <v-tooltip
                text="This is the total amount that has been allocated to you which you can avail of for your FBP (Flexible Benefit Plan) components within the limit set for each component."
                location="bottom"
                max-width="300"
                :open-on-click="true"
              >
                <template v-slot:activator="{ props }">
                  <div v-bind="props">
                    <v-icon color="blue" class="ml-1" size="x-small"
                      >fas fa-info-circle</v-icon
                    >
                  </div>
                </template>
              </v-tooltip>
            </div>
            <div class="text-h6">
              {{ payrollCurrency }} {{ totalAllocatedAmount }}
            </div>
          </v-col>
          <v-col cols="12" sm="4" md="3">
            <div class="d-flex align-center">
              Availed FBP amount
              <v-tooltip
                text="This is the monthly amount that you’ve availed for your FBP components from the allocated amount. You'll have to submit bills and claim reimbursements to get tax exemption, else, this amount will be considered taxable income at the end of the fiscal year."
                location="bottom"
                max-width="300"
                :open-on-click="true"
              >
                <template v-slot:activator="{ props }">
                  <div v-bind="props">
                    <v-icon color="blue" class="ml-1" size="x-small"
                      >fas fa-info-circle</v-icon
                    >
                  </div>
                </template>
              </v-tooltip>
            </div>
            <div class="text-h6">
              {{ payrollCurrency }} {{ availedFbpAmount }}
            </div>
          </v-col>
          <v-col cols="12" sm="4" md="3">
            <div class="d-flex align-center">
              Balance Amount
              <v-tooltip
                text="This is the leftover amount based on your FBP declaration, which will be considered as taxable income."
                location="bottom"
                max-width="300"
                :open-on-click="true"
              >
                <template v-slot:activator="{ props }">
                  <div v-bind="props">
                    <v-icon color="blue" class="ml-1" size="x-small"
                      >fas fa-info-circle</v-icon
                    >
                  </div>
                </template>
              </v-tooltip>
            </div>
            <div class="text-h6">{{ payrollCurrency }} {{ balanceAmount }}</div>
          </v-col>
        </v-row>
        <v-row v-if="!isMobileView">
          <v-col class="bg-grey-lighten-4">Component Name</v-col>
          <v-col class="bg-grey-lighten-4">Pay Type</v-col>
          <v-col class="bg-grey-lighten-4 text-end">
            <div class="pr-8">Monthly</div>
          </v-col>
          <v-col class="bg-grey-lighten-4 text-end">
            <div class="pr-5">Annually</div>
          </v-col>
        </v-row>
        <v-row
          v-for="item in itemList"
          :key="item.Allowance_Type_Id"
          :style="isMobileView ? 'border-top: 1px solid #ccc' : ''"
        >
          <v-col
            cols="12"
            sm="3"
            :class="
              isMobileView ? 'd-flex align-center justify-space-between' : ''
            "
          >
            <div v-if="isMobileView" class="font-weight-bold">
              Component Name
            </div>
            <div class="d-flex align-start">
              <v-checkbox
                class="small-checkbox"
                v-model="item.isSelected"
                color="primary"
                @change="toggleSelect(item)"
                density="compact"
                hide-details
              ></v-checkbox>
              <div class="ml-2 mt-1 d-flex flex-column">
                <span class="text-subtitle-1 font-weight-medium">
                  {{ item.Allowance_Name }}
                </span>
                <span class="text-caption text-grey-darken-1">
                  Max Amount: {{ payrollCurrency }}
                  {{ item.FBP_Max_Declaration }} / month
                </span>
              </div>
            </div>
          </v-col>
          <v-col
            cols="12"
            sm="3"
            :class="
              isMobileView ? 'd-flex align-center justify-space-between' : ''
            "
          >
            <div v-if="isMobileView" class="font-weight-bold">Pay Type</div>
            <div>
              <div>Fixed Amount</div>
              <v-tooltip
                v-if="
                  item.Restrict_Employee_FBP_Override?.toLowerCase() === 'yes'
                "
                text="You cannot change the FBP amount. If this FBP is selected, the maximum amount will be considered automatically in the FBP declaration."
                :open-on-click="true"
                location="top"
                max-width="300px"
              >
                <template v-slot:activator="{ props }">
                  <div v-bind="props">
                    <v-icon size="10" class="mr-1" color="grey"
                      >fas fa-lock</v-icon
                    >
                    <span
                      class="text-caption text-grey-darken-1 text-decoration-underline"
                      >Restricted Component</span
                    >
                  </div>
                </template>
              </v-tooltip>
            </div>
          </v-col>
          <v-col
            cols="12"
            sm="3"
            :class="
              isMobileView ? 'd-flex align-center justify-space-between' : ''
            "
          >
            <div v-if="isMobileView" class="font-weight-bold">Monthly</div>
            <v-text-field
              v-model="item.Amount"
              variant="solo"
              density="comfortable"
              :rules="[
                minMaxNumberValidation(
                  'Amount',
                  parseFloat(item.Amount),
                  0,
                  parseFloat(item.FBP_Max_Declaration)
                ),
                noDecimalValidation(item.Amount),
              ]"
              type="number"
              :disabled="
                !item.isSelected ||
                item.Restrict_Employee_FBP_Override?.toLowerCase() === 'yes'
              "
              class="custom-input-position"
              style="max-width: 300px"
            >
            </v-text-field>
          </v-col>
          <v-col
            cols="12"
            sm="3"
            :class="
              isMobileView
                ? 'd-flex align-center justify-space-between'
                : 'text-end'
            "
          >
            <div v-if="isMobileView" class="font-weight-bold">Annually</div>
            <div :class="isMobileView ? '' : 'pr-5'">
              {{ item.Amount * 12 ? (item.Amount * 12).toFixed(2) : 0 }}
            </div>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-form>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script setup>
import {
  defineProps,
  defineEmits,
  onMounted,
  ref,
  computed,
  getCurrentInstance,
} from "vue";
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
import { useValidation } from "@/composables/validationComposables";
const { t } = useI18n();
const store = useStore();
const instance = getCurrentInstance();
const { minMaxNumberValidation, noDecimalValidation } = useValidation();
const props = defineProps({
  landingFormName: {
    type: String,
    required: true,
  },
  originalList: {
    type: Array,
    required: true,
  },
  fixedAllowanceArray: {
    type: Array,
    required: true,
  },
  employeeId: {
    type: Number,
    required: true,
  },
  templateId: {
    type: Number,
    required: true,
  },
});
const emit = defineEmits(["close-edit-form"]);
const itemList = ref([]);

onMounted(() => {
  itemList.value = JSON.parse(JSON.stringify(props.originalList)).map(
    (item) => ({
      ...item,
      isSelected: item.Amount > 0 ? true : false,
    })
  );
});
const payrollCurrency = computed(() => {
  return store.state.payrollCurrency;
});
// Helper function to format numbers with commas
const formatCurrency = (amount) => {
  return parseFloat(amount).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

const totalAllocatedAmount = computed(() => {
  let amount = 0;
  props.originalList.forEach((item) => {
    amount += item.Amount ? parseFloat(item.Amount) : 0;
  });
  props.fixedAllowanceArray.forEach((item) => {
    amount += item.Amount ? parseFloat(item.Amount) : 0;
  });
  return formatCurrency(amount.toFixed(2));
});
const availedFbpAmount = computed(() => {
  let amount = 0;
  itemList.value.forEach((item) => {
    amount += item.Amount ? parseFloat(item.Amount) : 0;
  });
  return formatCurrency(amount.toFixed(2));
});
const balanceAmount = computed(() => {
  const balance = (
    parseFloat(totalAllocatedAmount.value.replace(/,/g, "")) -
    parseFloat(availedFbpAmount.value.replace(/,/g, ""))
  ).toFixed(2);
  return formatCurrency(balance);
});
const isMobileView = computed(() => {
  return store.state.isMobileWindowSize;
});

const toggleSelect = (item) => {
  if (!item.isSelected) {
    item.Amount = 0;
  } else if (item.Restrict_Employee_FBP_Override?.toLowerCase() === "yes") {
    item.Amount = item.FBP_Max_Declaration;
  }
};

const fbpDeclarationForm = ref(null);
const validateFbpDeclaration = async () => {
  let { valid } = await fbpDeclarationForm.value.validate();
  // Parse the formatted balance amount to check if it's negative
  const balanceAmountValue = parseFloat(balanceAmount.value.replace(/,/g, ""));
  if (balanceAmountValue < 0) {
    let snackbarData = {
      isOpen: true,
      message:
        "Availed FBP amount cannot be greater than total allocated amount.",
      type: "info",
    };
    store.commit("OPEN_SNACKBAR", snackbarData);
    return;
  }
  if (valid && balanceAmountValue >= 0) {
    saveFbpDeclaration();
  }
};
const isLoading = ref(false);
import { ADD_UPDATE_SALARY_DETAILS } from "@/graphql/corehr/salaryQueries";
const saveFbpDeclaration = () => {
  isLoading.value = true;
  let allowanceArray = [];
  itemList.value.forEach((item) => {
    allowanceArray.push({
      allowanceTypeId: item.Allowance_Type_Id,
      amount: item.Amount?.toString(),
      allowanceType: "Amount",
      fbpMaxDeclaration: item.FBP_Max_Declaration?.toString(),
    });
  });
  props.fixedAllowanceArray.forEach((item) => {
    allowanceArray.push({
      allowanceTypeId: item.Allowance_Type_Id,
      amount: parseFloat(balanceAmount.value.replace(/,/g, ""))?.toString(),
      allowanceType: "Amount",
    });
  });
  instance.proxy.$apollo
    .mutate({
      mutation: ADD_UPDATE_SALARY_DETAILS,
      variables: {
        formId: 207,
        accessFormId: 388,
        isEditMode: true,
        employeeId: props.employeeId,
        templateId: props.templateId,
        allowance: allowanceArray,
      },
      client: "apolloClientF",
    })
    .then(() => {
      isLoading.value = false;
      let snackbarData = {
        isOpen: true,
        message: "FBP declaration updated successfully.",
        type: "success",
      };
      store.commit("OPEN_SNACKBAR", snackbarData);
      emit("edit-updated");
    })
    .catch((err) => {
      isLoading.value = false;
      store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "FBP declaration",
        isListError: false,
      });
    });
};
</script>
<style scoped>
:deep(.custom-input-position .v-field__input) {
  text-align: end !important;
}
:deep(.small-checkbox .v-icon) {
  font-size: 18px !important;
}
</style>
