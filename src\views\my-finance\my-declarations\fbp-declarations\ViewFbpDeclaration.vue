<template>
  <v-row>
    <v-col cols="12" class="d-flex justify-end mb-4">
      <v-btn
        v-if="
          props.formAccess?.update &&
          props.lockStatus?.toLowerCase() == 'unlock'
        "
        variant="text"
        color="primary"
        density="compact"
        @click="emit('open-edit-form')"
      >
        <span class="mr-2">{{ t("common.update") }}</span>
        <v-icon size="13">fas fa-edit</v-icon>
      </v-btn>
    </v-col>
    <v-col cols="12">
      <v-row>
        <v-col cols="12" sm="4" md="3">
          <div class="d-flex align-center">
            Total Allocated Amount
            <v-tooltip
              text="This is the total amount that has been allocated to you which you can avail of for your FBP (Flexible Benefit Plan) components within the limit set for each component."
              location="bottom"
              max-width="300"
              :open-on-click="true"
            >
              <template v-slot:activator="{ props }">
                <div v-bind="props">
                  <v-icon color="blue" class="ml-1" size="x-small"
                    >fas fa-info-circle</v-icon
                  >
                </div>
              </template>
            </v-tooltip>
          </div>
          <div class="text-h6">
            {{ payrollCurrency }} {{ totalAllocatedAmount }}
          </div>
        </v-col>
        <v-col cols="12" sm="4" md="3">
          <div class="d-flex align-center">
            Availed FBP amount
            <v-tooltip
              text="This is the monthly amount that you’ve availed for your FBP components from the allocated amount. You'll have to submit bills and claim reimbursements to get tax exemption, else, this amount will be considered taxable income at the end of the fiscal year."
              location="bottom"
              max-width="300"
              :open-on-click="true"
            >
              <template v-slot:activator="{ props }">
                <div v-bind="props">
                  <v-icon color="blue" class="ml-1" size="x-small"
                    >fas fa-info-circle</v-icon
                  >
                </div>
              </template>
            </v-tooltip>
          </div>
          <div class="text-h6">
            {{ payrollCurrency }} {{ availedFbpAmount }}
          </div>
        </v-col>
        <v-col cols="12" sm="4" md="3">
          <div class="d-flex align-center">
            Balance Amount
            <v-tooltip
              text="This is the leftover amount based on your FBP declaration, which will be considered as taxable income."
              location="bottom"
              max-width="300"
              :open-on-click="true"
            >
              <template v-slot:activator="{ props }">
                <div v-bind="props">
                  <v-icon color="blue" class="ml-1" size="x-small"
                    >fas fa-info-circle</v-icon
                  >
                </div>
              </template>
            </v-tooltip>
          </div>
          <div class="text-h6">
            {{ payrollCurrency }}
            {{ balanceAmount }}
          </div>
        </v-col>
      </v-row>
      <v-row v-if="!isMobileView">
        <v-col class="bg-grey-lighten-4">Component Name</v-col>
        <v-col class="bg-grey-lighten-4">Pay Type</v-col>
        <v-col class="bg-grey-lighten-4 text-end">
          <div class="pr-8">Monthly</div>
        </v-col>
        <v-col class="bg-grey-lighten-4 text-end">
          <div class="pr-5">Annually</div>
        </v-col>
      </v-row>
      <v-row
        v-for="item in props.originalList"
        :key="item.Allowance_Type_Id"
        :style="isMobileView ? 'border-top: 1px solid #ccc' : ''"
      >
        <v-col
          cols="12"
          sm="3"
          :class="
            isMobileView ? 'd-flex align-center justify-space-between' : ''
          "
        >
          <div v-if="isMobileView" class="font-weight-bold">Component Name</div>
          {{ item.Allowance_Name }}
        </v-col>
        <v-col
          cols="12"
          sm="3"
          :class="
            isMobileView ? 'd-flex align-center justify-space-between' : ''
          "
        >
          <div v-if="isMobileView" class="font-weight-bold">Pay Type</div>
          Fixed Amount
        </v-col>
        <v-col
          cols="12"
          sm="3"
          :class="
            isMobileView
              ? 'd-flex align-center justify-space-between'
              : 'text-end'
          "
        >
          <div v-if="isMobileView" class="font-weight-bold">Monthly</div>
          <div :class="isMobileView ? '' : 'pr-8'">
            {{ item.Amount?.toFixed(2) }}
          </div>
        </v-col>
        <v-col
          cols="12"
          sm="3"
          :class="
            isMobileView
              ? 'd-flex align-center justify-space-between'
              : 'text-end'
          "
        >
          <div v-if="isMobileView" class="font-weight-bold">Annually</div>
          <div :class="isMobileView ? '' : 'pr-5'">
            {{ item.Amount * 12 ? (item.Amount * 12).toFixed(2) : 0 }}
          </div>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>
<script setup>
import { defineProps, defineEmits, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useStore } from "vuex";
const { t } = useI18n();
const store = useStore();
const props = defineProps({
  landingFormName: {
    type: String,
    required: true,
  },
  formAccess: {
    type: [Boolean, Object],
    required: true,
  },
  originalList: {
    type: Array,
    required: true,
  },
  fixedAllowanceArray: {
    type: Array,
    required: true,
  },
  lockStatus: {
    type: String,
    required: true,
  },
});
const emit = defineEmits(["open-edit-form"]);
const payrollCurrency = computed(() => {
  return store.state.payrollCurrency;
});
const formatCurrency = (amount) => {
  return parseFloat(amount).toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};
const totalAllocatedAmount = computed(() => {
  let amount = 0;
  props.originalList.forEach((item) => {
    amount += item.Amount ? parseFloat(item.Amount) : 0;
  });
  props.fixedAllowanceArray.forEach((item) => {
    amount += item.Amount ? parseFloat(item.Amount) : 0;
  });
  return formatCurrency(amount.toFixed(2));
});
const availedFbpAmount = computed(() => {
  let amount = 0;
  props.originalList.forEach((item) => {
    amount += item.Amount ? parseFloat(item.Amount) : 0;
  });
  return formatCurrency(amount.toFixed(2));
});
const balanceAmount = computed(() => {
  const balance = (
    parseFloat(totalAllocatedAmount.value.replace(/,/g, "")) -
    parseFloat(availedFbpAmount.value.replace(/,/g, ""))
  ).toFixed(2);
  return formatCurrency(balance);
});
const isMobileView = computed(() => {
  return store.state.isMobileWindowSize;
});
</script>
