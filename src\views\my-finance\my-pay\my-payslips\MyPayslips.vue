<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template v-slot:topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="8" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="false"
              ></EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList && !isLoading"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !isLoading"
            key="no-results-screen"
            :main-title="
              originalList?.length
                ? 'There are no records for the selected filters/searches.'
                : ''
            "
            :image-name="originalList?.length === 0 ? '' : 'common/no-records'"
            :isSmallImage="originalList.length === 0"
          >
            <template #contentSlot>
              <div class="d-flex mb-2 flex-wrap justify-center align-center">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="The 'My Payslip' feature in the Employee Self-Service (ESS) portal allows you to securely view and download your monthly salary slips anytime, anywhere. With just a few clicks, you can access detailed information about your earnings, deductions, and net pay for any selected month. This convenient feature helps you stay informed about your payroll details without needing to contact HR."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="You can also access and download previous payslips for your records, with options to print or save them as PDF files. Designed to promote transparency and ease of use, the 'My Payslip' section ensures that all your salary-related information is just a click away—secure, accessible, and always up to date."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn-toggle
                      v-if="
                        payrollPeriod?.toLowerCase() === 'bimonthly' &&
                        originalList.length === 0
                      "
                      v-model="payslipType"
                      mandatory
                      base-color="white"
                      color="primary mr-2"
                      density="compact"
                      @update:model-value="refetchList()"
                    >
                      <v-btn
                        :color="
                          payslipType?.toLowerCase() === 'monthly'
                            ? 'primary'
                            : 'white'
                        "
                        value="Monthly"
                        >Monthly</v-btn
                      >
                      <v-btn
                        :color="
                          payslipType?.toLowerCase() === 'bimonthly'
                            ? 'primary'
                            : 'white'
                        "
                        value="BiMonthly"
                        >Bi-Monthly</v-btn
                      >
                    </v-btn-toggle>
                    <v-btn
                      v-if="originalList.length === 0"
                      class="bg-white ma-2"
                      rounded="lg"
                    >
                      <template v-slot:prepend>
                        <v-icon color="primary" size="14"
                          >fas fa-calendar-alt</v-icon
                        >
                      </template>
                      {{ formattedSelectedMonth }}
                      <v-menu
                        activator="parent"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                      >
                        <Datepicker
                          v-model="selectedYear"
                          :value="selectedYear"
                          :inline="true"
                          :format="'YYYY'"
                          maximum-view="year"
                          minimum-view="year"
                          :disabled-dates="getDisabledDates"
                          @update:modelValue="fetchPayslips($event)"
                        />
                      </v-menu>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      class="mt-1"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter()"
                    >
                      Reset Filter/Search
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div
              v-if="originalList.length > 0"
              class="d-flex flex-wrap align-center my-3"
              :class="isMobileView ? 'flex-column' : ''"
              style="justify-content: space-between"
            >
              <div
                class="d-flex align-center flex-wrap"
                :class="isMobileView ? 'justify-center' : ''"
              >
                <v-btn-toggle
                  v-if="payrollPeriod?.toLowerCase() === 'bimonthly'"
                  v-model="payslipType"
                  mandatory
                  base-color="white"
                  color="primary"
                  density="compact"
                  @update:model-value="refetchList()"
                >
                  <v-btn
                    :color="
                      payslipType?.toLowerCase() === 'monthly'
                        ? 'primary'
                        : 'white'
                    "
                    value="Monthly"
                    >Monthly</v-btn
                  >
                  <v-btn
                    :color="
                      payslipType?.toLowerCase() === 'bimonthly'
                        ? 'primary'
                        : 'white'
                    "
                    value="BiMonthly"
                    >Bi-Monthly</v-btn
                  >
                </v-btn-toggle>
              </div>
              <div
                class="d-flex align-center"
                :class="isMobileView ? 'justify-center' : 'justify-end'"
              >
                <v-btn class="bg-white my-2" rounded="lg">
                  <template v-slot:prepend>
                    <v-icon color="primary" size="14"
                      >fas fa-calendar-alt</v-icon
                    >
                  </template>
                  {{ formattedSelectedMonth }}
                  <v-menu
                    activator="parent"
                    :close-on-content-click="false"
                    transition="scale-transition"
                    offset-y
                  >
                    <Datepicker
                      v-model="selectedYear"
                      :value="selectedYear"
                      :inline="true"
                      :format="'YYYY'"
                      maximum-view="year"
                      minimum-view="year"
                      :disabled-dates="getDisabledDates"
                      @update:modelValue="fetchPayslips($event)"
                    />
                  </v-menu>
                </v-btn>
                <v-btn
                  rounded="lg"
                  class="mt-1"
                  color="transparent"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                          >
                            <v-tooltip :text="action.message">
                              <template v-slot:activator="{ props }">
                                <div v-bind="action.message ? props : ''">
                                  <v-icon size="15" class="pr-2">{{
                                    action.icon
                                  }}</v-icon>
                                  {{ action.key }}
                                </div>
                              </template>
                            </v-tooltip>
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>
            <v-row>
              <v-col cols="12" class="mb-12">
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Salary Month
                        </div>
                        <section :style="isMobileView ? 'max-width: 60%' : ''">
                          {{ checkNullValue(item.Formatted_Salary_Month) }}
                        </section>
                      </td>
                      <td
                        v-if="payslipType?.toLowerCase() === 'bimonthly'"
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Pay Period
                        </div>
                        <section :style="isMobileView ? 'max-width: 60%' : ''">
                          {{ checkNullValue(item.Pay_Period) }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Total Earnings
                          {{
                            payrollCurrency ? ` (in ${payrollCurrency})` : ""
                          }}
                        </div>
                        <section :style="isMobileView ? 'max-width: 60%' : ''">
                          {{ item.Total_Earning || 0 }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Total Deductions
                          {{
                            payrollCurrency ? ` (in ${payrollCurrency})` : ""
                          }}
                        </div>
                        <section :style="isMobileView ? 'max-width: 60%' : ''">
                          {{ item.Total_Deduction || 0 }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Net Pay
                          {{
                            payrollCurrency ? ` (in ${payrollCurrency})` : ""
                          }}
                        </div>
                        <section :style="isMobileView ? 'max-width: 60%' : ''">
                          {{ item.Total_Salary || 0 }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        ></div>
                        <section
                          :style="isMobileView ? 'max-width: 60%' : ''"
                          class="text-blue text-decoration-underline cursor-pointer"
                          @click="onClickGenerate('generate', item)"
                        >
                          View
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
  <AppLoading v-if="isLoading" />
  <FilePreviewModal
    v-if="openDocumentModal"
    :fileName="selectedItem?.S3_FileName"
    :folder-name="folderName"
    fileRetrieveType="documents"
    @close-preview-modal="(openDocumentModal = false), (folderName = '')"
  >
    <template v-slot:message>
      <div class="text-subtitle-2 text-grey-darken-1 font-weight-medium">
        <v-icon size="15" color="blue" class="mr-1">
          fas fa-info-circle
        </v-icon>
        If the payslip is password protected, use the following format to open
        it:
      </div>
      <div class="text-caption font-weight-medium">
        Password = First 4 letters of your first name (as in the application) +
        your Date of Birth in DDMMYYYY format.
      </div>
      <v-card class="mt-1 pa-2 bg-blue-lighten-5">
        <span class="text-subtitle-2 font-weight-bold">For example:</span>
        <ul style="list-style-position: inside">
          <li class="text-caption">
            Employee Name : Johnson Smith - DOB : 26/01/1990 (PDF password will
            be
            <strong>john26011990</strong>)
          </li>
          <li class="text-caption">
            Employee Name : A.John Smith - DOB : 26/01/1990 (PDF password will
            be
            <strong>a.jo26011990</strong>)
          </li>
          <li class="text-caption">
            Employee Name : Joe Richard - DOB : 26/01/1990 (PDF password will be
            <strong>joer26011990</strong>)
          </li>
        </ul>
      </v-card>

      <div class="text-subtitle-2 text-red mt-2">
        **Note: The password is case sensitive.
      </div>
    </template>
  </FilePreviewModal>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import { GENERATE_MY_PAYSLIP } from "@/graphql/my-finance/myPayslip";
import { RETRIEVE_BIMONTHLY_PAYSLIP_LIST } from "@/graphql/payroll/salaryPayslip.js";

const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

import FileExportMixin from "@/mixins/FileExportMixin";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
export default {
  name: "MyPayslips",
  components: {
    NotesCard,
    Datepicker,
    EmployeeDefaultFilterMenu,
    FilePreviewModal,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      currentTabItem: "",
      allForms: [346, 345],
      listLoading: false,
      isLoading: false,
      isErrorInList: false,
      errorContent: "",
      originalList: [],
      itemList: [],
      openMoreMenu: false,
      selectedYear: new Date(),
      dateOfJoin: "",
      selectedItem: null,
      payslipType: "Monthly",
      isSyntrumEnabled: false,
      openDocumentModal: false,
      folderName: "",
    };
  },
  computed: {
    mainTabs() {
      let tabs = [];
      tabs = this.allVisibleTabs.map((tab) => {
        return tab.formName;
      });
      return tabs;
    },
    allVisibleTabs() {
      let tabs = [];
      tabs = this.allForms
        .map((tab) => {
          let form = this.accessRights(tab);
          if (
            form?.accessRights?.view ||
            form.customFormName === this.landedFormName
          )
            return { formName: form.customFormName, formId: form.formId };
        })
        .filter((tab) => tab);
      return tabs;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    landedFormName() {
      return this.accessRights(345)?.customFormName;
    },
    formAccess() {
      let formAccessRights = this.accessRights(345);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      return [{ key: "Export", icon: "fas fa-file-export" }];
    },
    formattedSelectedMonth() {
      return moment(this.selectedYear).format("YYYY");
    },
    getDisabledDates() {
      return {
        from: new Date(),
        to: new Date(this.dateOfJoin),
      };
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    payrollPeriod() {
      return this.$store.state.orgDetails.Payroll_Period;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    tableHeaders() {
      let headers = [
        {
          title: "Salary Month",
          align: "start",
          key: "Formatted_Salary_Month",
        },
        {
          title: `Total Earnings ${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          key: "Total_Earning",
        },
        {
          title: `Total Deductions ${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          key: "Total_Deduction",
        },
        {
          title: `Net Pay ${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          key: "Total_Salary",
        },
        {
          title: "Payslip",
          sortable: false,
        },
      ];
      if (this.payslipType?.toLowerCase() === "bimonthly") {
        headers.splice(1, 0, {
          title: "Pay Period",
          key: "Pay_Period",
        });
      }
      return headers;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchPayslips();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    onTabChange(tabName) {
      let form = this.allVisibleTabs.find((tab) => tab.formName === tabName);
      if (form && form.formId) {
        if (form.formId == 346) {
          this.$router.push("/my-finance/my-pay");
        }
      }
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.resetFilter();
      this.fetchPayslips();
    },
    resetFilter() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onMoreAction(action) {
      this.openMoreMenu = false;
      if (action?.toLowerCase() === "export") {
        this.exportReportFile();
      }
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: "Employee ID",
          key: "User_Defined_EmpId",
        },
        {
          header: "Employee Name",
          key: "Employee_Name",
        },
        {
          header: "Department",
          key: "Department_Name",
        },
        {
          header: "Location",
          key: "Location_Name",
        },
        {
          header: "Designation",
          key: "Designation_Name",
        },
        {
          header: "Salary Month",
          key: "Formatted_Salary_Month",
        },
        {
          header: `Total Earnings ${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          key: "Total_Earning",
        },
        {
          header: `Total Deductions ${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          key: "Total_Deduction",
        },
        {
          header: `Net Pay ${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          key: "Total_Salary",
        },
        {
          header: "Payment Status",
          key: "Payment_Status",
        },
        {
          header: "Added On",
          key: "Generated_On",
        },
        {
          header: "Added By",
          key: "Generated_By",
        },
      ];
      if (this.payslipType?.toLowerCase() === "bimonthly")
        exportHeaders.splice(6, 0, {
          header: "Pay Period",
          key: "Pay_Period",
        });

      let dataList = this.itemList.map((item) => {
        return {
          ...item,
          Generated_On: moment(item.Generated_On).isValid()
            ? convertUTCToLocal(item.Generated_On)
            : "",
        };
      });

      const exportOptions = {
        fileExportData: dataList,
        fileName: "Payslip Report",
        sheetName: "Payslips",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });

        this.itemList = searchItems;
      }
    },
    fetchPayslips() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_BIMONTHLY_PAYSLIP_LIST,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: {
            year: parseInt(moment(this.selectedYear).format("YYYY")),
            formId: 345,
            employeeId: vm.loginEmployeeId,
            isMonthly:
              vm.payslipType?.toLowerCase() === "monthly" ? true : false,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listSalaryPayslip &&
            !response.data.listSalaryPayslip.errorCode
          ) {
            vm.itemList = response.data.listSalaryPayslip.salaryPayslips;
            vm.isSyntrumEnabled =
              response.data.listSalaryPayslip?.isSyntrumEnabled;
            vm.originalList = response.data.listSalaryPayslip.salaryPayslips;
            let { employeeDateOfJoin, payrollStartDate } =
              response.data.listSalaryPayslip;
            vm.dateOfJoin =
              new Date(employeeDateOfJoin) > new Date(payrollStartDate)
                ? employeeDateOfJoin
                : payrollStartDate;
            vm.listLoading = false;
          } else {
            vm.originalList = [];
            vm.itemList = [];
            vm.handleListError(
              response.data.listSalaryPayslip?.errorCode || ""
            );
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.isErrorInList = true;
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "payslips",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
        });
    },
    onClickGenerate(type, item) {
      this.selectedItem = item;
      if (type?.toLowerCase() === "generate" && this.isSyntrumEnabled) {
        this.generatePayslip();
      } else {
        if (item.S3_FileName) {
          const year = item.Salary_Month?.split(",")[1];
          this.folderName = `Salary Payslip/${year}/${this.payslipType}`;
          this.openDocumentModal = true;
        } else {
          let snackbarData = {
            isOpen: true,
            message:
              "Payslip is being generated. This may take a moment. If the file name doesn’t appear shortly, please refresh the page and try again.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      }
    },
    generatePayslip() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: GENERATE_MY_PAYSLIP,
          client: "apolloClientAH",
          variables: {
            employeeId: vm.loginEmployeeId,
            month: parseInt(vm.selectedItem.Salary_Month?.split(",")[0]),
            year: parseInt(vm.selectedItem.Salary_Month?.split(",")[1]),
            formId: 345,
          },
        })
        .then((response) => {
          vm.isLoading = false;
          if (response?.data?.generateSyntrumPayslipDetails?.data) {
            this.folderName = `Salary Payslip/${moment(vm.selectedYear).format(
              "YYYY"
            )}/${this.payslipType}`;
            this.selectedItem.S3_FileName =
              response.data.generateSyntrumPayslipDetails.data + ".pdf";
            this.openDocumentModal = true;
            vm.refetchList();
          } else {
            vm.showAlert({
              isOpen: true,
              message: "Payslip generation failed",
              type: "warning",
            });
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          if (err) {
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              form: "payslip",
              action: "generating",
              isListError: false,
            });
          }
        });
    },
    showAlert(data) {
      this.$store.commit("OPEN_SNACKBAR", data);
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
