<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
      </AppTopBarTab>
    </div>
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <AppFetchErrorScreen
            v-if="isErrorInList && !isLoading"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="getSyntrumEnabled()"
          >
          </AppFetchErrorScreen>
          <v-row v-else>
            <v-col cols="12" sm="6" md="4" lg="3">
              <v-card height="100px" class="pa-5 d-flex align-center">
                <div v-if="enableSkeletonLoading" style="width: 100%">
                  <v-skeleton-loader
                    class="mx-auto"
                    type="paragraph"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <p class="text-grey-darken-1 text-body-1">Cost to Company</p>
                  <p class="text-h6">
                    {{ payrollCurrency }}
                    <span v-if="isSyntrumEnabled">
                      {{ totalYearlyValue(salaryDetails[0]) }} / Annum
                    </span>
                    <span v-else>
                      {{ totalYearlyValue(combinedSalaryDetails[0]) }}
                      / Annum
                    </span>
                  </p>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" sm="6" md="4" lg="3">
              <v-card height="100px" class="pa-5 d-flex align-center">
                <div v-if="enableSkeletonLoading" style="width: 100%">
                  <v-skeleton-loader
                    class="mx-auto"
                    type="paragraph"
                  ></v-skeleton-loader>
                </div>
                <div
                  v-else
                  class="d-flex align-center justify-space-between"
                  style="width: 100%"
                >
                  <div class="text-h4">Payroll</div>
                  <div>
                    <span class="text-grey-darken-1 text-body-1"
                      >Pay Cycle</span
                    >
                    <p class="text-h6">Monthly</p>
                  </div>
                  <div></div>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" md="10">
              <v-card class="pa-4">
                <div v-if="enableSkeletonLoading">
                  <v-skeleton-loader
                    class="mx-auto"
                    type="heading"
                  ></v-skeleton-loader>
                </div>
                <div v-else class="text-sm-h4 text-h5 text-primary mb-4">
                  Salary Timeline
                </div>
                <div v-if="enableSkeletonLoading">
                  <v-skeleton-loader
                    class="mx-auto"
                    type="article"
                  ></v-skeleton-loader>
                </div>
                <!-- Syntrum Enabled Template -->
                <div v-if="isSyntrumEnabled">
                  <v-card
                    v-for="(salary, index) in salaryDetails"
                    :key="'syntrum-' + index"
                    class="mb-4"
                  >
                    <v-card-title>
                      <v-container :class="windowWidth < 365 ? 'px-0' : ''">
                        <v-row>
                          <v-col cols="12" sm="6" md="6">
                            <v-row align="center" no-gutters>
                              <v-col cols="auto" class="mr-2">
                                <v-progress-circular
                                  model-value="100"
                                  :color="salary.color"
                                  :size="22"
                                ></v-progress-circular>
                              </v-col>
                              <v-col>
                                <span class="text-h6">Salary Revision</span>
                              </v-col>
                            </v-row>
                          </v-col>
                          <v-col cols="12" sm="6" md="6">
                            <v-row
                              align="center"
                              no-gutters
                              class="justify-start"
                            >
                              <v-col cols="auto" class="mr-2">
                                <span
                                  class="text-grey-darken-1 text-subtitle-1 text-sm-h6"
                                >
                                  Effective
                                  {{
                                    formattedEffectiveDate(salary.effectiveDate)
                                  }}
                                </span>
                              </v-col>
                              <v-col cols="auto" v-if="index === 0">
                                <span
                                  class="bg-primary text-white text-body-2 text-sm-body-1 px-2 rounded-lg"
                                >
                                  Current
                                </span>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-container>
                    </v-card-title>
                    <v-card-text>
                      <v-container :class="windowWidth < 365 ? 'px-0' : ''">
                        <!-- For Futher Usage
                        <v-row>
                          <v-col
                            :cols="windowWidth < 365 ? '6' : '5'"
                            sm="3"
                            md="3"
                            lg="2"
                            :class="windowWidth < 380 ? 'pr-0' : ''"
                            class="pr-0"
                          >
                            <div
                              class="text-grey-darken-1 text-body-2 text-sm-body-1"
                            >
                              Regular Salary
                            </div>
                            <div class="text-subtitle-1 text-sm-h6">
                              {{ payrollCurrency }}
                              {{ totalYearlyValueSyntrum(salary) }}
                            </div>
                          </v-col>
                          <v-col cols="1" class="d-flex align-center px-0"
                            >=</v-col
                          >
                          <v-col
                            cols="5"
                            sm="3"
                            md="3"
                            lg="2"
                            :class="windowWidth < 380 ? 'pr-0' : ''"
                            class="pl-0"
                          >
                            <div
                              class="text-grey-darken-1 text-body-2 text-sm-body-1"
                            >
                              Total Salary
                            </div>
                            <div class="text-subtitle-1 text-sm-h6">
                              {{ payrollCurrency }}
                              {{ totalYearlyValueSyntrum(salary) }}
                            </div>
                          </v-col>
                        </v-row> -->
                        <v-row>
                          <v-col
                            cols="12"
                            sm="6"
                            md="6"
                            class="py-0 text-grey-darken-1 text-body-2 text-sm-body-1"
                            >Cost to Company</v-col
                          >
                        </v-row>
                        <v-row>
                          <v-col
                            cols="12"
                            sm="6"
                            md="6"
                            class="py-0 text-subtitle-1 text-sm-h6"
                            >{{ payrollCurrency }} {{ totalValue(salary) }} /
                            Month</v-col
                          >
                          <v-col
                            cols="12"
                            sm="6"
                            md="6"
                            class="py-0 text-decoration-underline cursor-pointer text-subtitle-1 text-sm-h6 text-blue d-flex align-center"
                            @click="onClickViewSalaryBreakup(salary)"
                            >Salary Breakup
                          </v-col>
                        </v-row>
                      </v-container>
                    </v-card-text>
                  </v-card>
                </div>

                <!-- Non-Syntrum Template -->
                <div v-else>
                  <v-card
                    v-for="(salary, index) in combinedSalaryDetails"
                    :key="'non-syntrum-' + index"
                    class="mb-4"
                  >
                    <v-card-title>
                      <v-container :class="windowWidth < 365 ? 'px-0' : ''">
                        <v-row>
                          <v-col cols="12" sm="6" md="6">
                            <v-row align="center" no-gutters>
                              <v-col cols="auto" class="mr-2">
                                <v-progress-circular
                                  model-value="100"
                                  :color="salary.color"
                                  :size="22"
                                ></v-progress-circular>
                              </v-col>
                              <v-col>
                                <span class="text-h6">Salary Revision</span>
                              </v-col>
                            </v-row>
                          </v-col>
                          <v-col cols="12" sm="6" md="6">
                            <v-row
                              align="center"
                              no-gutters
                              class="justify-start"
                            >
                              <v-col cols="auto" class="mr-2">
                                <span
                                  class="text-grey-darken-1 text-subtitle-1 text-sm-h6"
                                >
                                  Effective
                                  {{
                                    formattedEffectiveDate(
                                      salary.Effective_From
                                    )
                                  }}
                                </span>
                              </v-col>
                              <v-col cols="auto" v-if="salary.isCurrent">
                                <span
                                  class="bg-primary text-white text-body-2 text-sm-body-1 px-2 rounded-lg"
                                >
                                  Current
                                </span>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-container>
                    </v-card-title>
                    <v-card-text>
                      <v-container :class="windowWidth < 365 ? 'px-0' : ''">
                        <!-- For Futher Usage
                        <v-row>
                          <v-col
                            :cols="windowWidth < 365 ? '6' : '5'"
                            sm="3"
                            md="3"
                            lg="2"
                            :class="windowWidth < 380 ? 'pr-0' : ''"
                            class="pr-0"
                          >
                            <div
                              class="text-grey-darken-1 text-body-2 text-sm-body-1"
                            >
                              Regular Salary
                            </div>
                            <div class="text-subtitle-1 text-sm-h6">
                              {{ payrollCurrency }}
                              {{ totalYearlyValue(salary) }}
                            </div>
                          </v-col>
                          <v-col cols="1" class="d-flex align-center px-0"
                            >=</v-col
                          >
                          <v-col
                            cols="5"
                            sm="3"
                            md="3"
                            lg="2"
                            :class="windowWidth < 380 ? 'pr-0' : ''"
                            class="pl-0"
                          >
                            <div
                              class="text-grey-darken-1 text-body-2 text-sm-body-1"
                            >
                              Total Salary
                            </div>
                            <div class="text-subtitle-1 text-sm-h6">
                              {{ payrollCurrency }}
                              {{ totalYearlyValue(salary) }}
                            </div>
                          </v-col>
                        </v-row> -->
                        <v-row>
                          <v-col
                            cols="12"
                            sm="6"
                            md="6"
                            class="py-0 text-grey-darken-1 text-body-2 text-sm-body-1"
                            >Cost to Company</v-col
                          >
                        </v-row>
                        <v-row>
                          <v-col
                            cols="12"
                            sm="6"
                            md="6"
                            class="py-0 text-subtitle-1 text-sm-h6"
                            >{{ payrollCurrency }} {{ totalValue(salary) }} /
                            Month</v-col
                          >
                          <v-col
                            cols="12"
                            sm="6"
                            md="6"
                            class="py-0 text-decoration-underline cursor-pointer text-subtitle-1 text-sm-h6 text-blue d-flex align-center"
                            @click="onClickViewSalaryBreakup(salary)"
                            >Salary Breakup
                          </v-col>
                        </v-row>
                      </v-container>
                    </v-card-text>
                  </v-card>
                </div>
              </v-card>
            </v-col>
          </v-row>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <div v-if="viewSalaryBreakup">
    <SalaryBreakupSyntrum
      v-if="isSyntrumEnabled"
      :show-breakup="viewSalaryBreakup"
      :is-syntrum-enabled="isSyntrumEnabled"
      :revision-data="selectedSalaryDetails"
      :salary-details="salaryDropdown"
      @close-form="onCloseSalaryBreakup()"
    />
    <SalaryBreakup
      v-else
      :show-breakup="viewSalaryBreakup"
      :is-syntrum-enabled="isSyntrumEnabled"
      :revision-data="selectedSalaryDetails"
      :salary-details="salaryDropdown"
      @close-form="onCloseSalaryBreakup()"
    />
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { GET_MY_SALARY } from "@/graphql/my-finance/mySalary";
import { RETRIEVE_LIST_SALARY_PAYSLIP } from "@/graphql/payroll/salaryPayslip.js";
import { RETRIEVE_SALARY_LIST } from "@/graphql/corehr/salaryQueries.js";
import { generateRandomColor } from "@/helper";
import moment from "moment";
const SalaryBreakupSyntrum = defineAsyncComponent(() =>
  import("./SalaryBreakupSyntrum.vue")
);
const SalaryBreakup = defineAsyncComponent(() => import("./SalaryBreakup.vue"));
export default {
  name: "MySalary",
  components: {
    SalaryBreakup,
    SalaryBreakupSyntrum,
  },
  data() {
    return {
      currentTabItem: "",
      viewSalaryBreakup: false,
      salaryDetails: [],
      selectedSalaryDetails: null,
      detailsLoading: false,
      isErrorInList: false,
      errorContent: "",
      allForms: [346, 345],
      salaryDropdown: [],
      isLoading: false,
      listLoading: false,
      syntrumLoading: false,
      isSyntrumEnabled: false,
      salaryCurrentDetails: [],
      salaryCurrentHistoryList: [],
    };
  },
  computed: {
    enableSkeletonLoading() {
      return (
        this.listLoading ||
        this.isLoading ||
        this.detailsLoading ||
        this.syntrumLoading
      );
    },
    mainTabs() {
      let tabs = [];
      tabs = this.allVisibleTabs.map((tab) => {
        return tab.formName;
      });
      return tabs;
    },
    allVisibleTabs() {
      let tabs = [];
      tabs = this.allForms
        .map((tab) => {
          let form = this.accessRights(tab);
          if (
            form?.accessRights?.view ||
            form.customFormName === this.landedFormName
          )
            return { formName: form.customFormName, formId: form.formId };
        })
        .filter((tab) => tab);
      return tabs;
    },
    landedFormName() {
      return this.accessRights(346)?.customFormName;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(346);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formattedEffectiveDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          return moment(date).format("MMM DD, YYYY");
        }
        return "-";
      };
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    totalYearlyValueSyntrum() {
      return (salary) => {
        if (salary?.revisionItems?.length)
          return (this.totalValue(salary) * 12).toFixed(2);
        return 0;
      };
    },
    totalValue() {
      return (salary) => {
        if (salary?.revisionItems?.length)
          return (
            salary.revisionItems
              .find((item) => item.itemName.toLowerCase() === "full_inc")
              ?.value?.toFixed(2) || 0
          );
        return 0;
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    // Combined salary details based on isSyntrumEnabled flag
    combinedSalaryDetails() {
      if (this.isSyntrumEnabled) {
        return this.salaryDetails;
      } else {
        // Combine current and historical data for non-Syntrum and sort by effective date
        const combined = [
          ...this.salaryCurrentDetails,
          ...this.salaryCurrentHistoryList,
        ];
        return combined.sort((a, b) => {
          const dateA = new Date(a.Effective_From);
          const dateB = new Date(b.Effective_From);
          return dateB - dateA; // Sort in descending order (newest first)
        });
      }
    },
    totalYearlyValue() {
      return (salary) => {
        if (salary?.revisionItems?.length) return this.totalValue(salary) * 12;
        return 0;
      };
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.getSyntrumEnabled();
  },
  methods: {
    generateRandomColor,
    onClickViewSalaryBreakup(salary) {
      this.selectedSalaryDetails = salary;
      this.viewSalaryBreakup = true;
    },
    onCloseSalaryBreakup() {
      this.viewSalaryBreakup = false;
      this.selectedSalaryDetails = null;
    },
    getSyntrumEnabled() {
      let vm = this;
      vm.syntrumLoading = true;
      vm.isErrorInList = false;
      vm.errorContent = "";
      vm.$apollo
        .query({
          query: RETRIEVE_LIST_SALARY_PAYSLIP,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: {
            formId: 346,
            isMonthly: true,
            employeeId: vm.loginEmployeeId,
            year: moment().year(),
          },
        })
        .then((response) => {
          if (response && response.data && response.data.listSalaryPayslip) {
            vm.isSyntrumEnabled =
              response.data.listSalaryPayslip?.isSyntrumEnabled;
            if (vm.isSyntrumEnabled) {
              vm.getSalaryDetails();
              vm.retrieveSalaryDetails();
            } else {
              vm.retrieveCurrentSalaryDetails();
              vm.retriveSalaryHistoryDetails();
            }
          }
          vm.syntrumLoading = false;
        })
        .catch((err) => {
          vm.syntrumLoading = false;
          this.$store
            .dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: vm.landedFormName,
              isListError: true,
            })
            .then((errorMessages) => {
              this.errorContent = errorMessages;
              this.isErrorInList = true;
            });
        });
    },
    retriveSalaryHistoryDetails() {
      let vm = this;
      vm.listLoading = true;

      return vm.$apollo
        .query({
          query: RETRIEVE_SALARY_LIST,
          client: "apolloClientF",
          variables: {
            formId: 346,
            isViewMode: true,
            employeeId: vm.loginEmployeeId,
            includeHistoricalRecords: true,
          },
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let salaryList = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );

            // Handle new API response structure for historical data (same as current)
            if (Array.isArray(salaryList) && salaryList.length > 0) {
              // Process historical salary data with new structure
              vm.salaryCurrentHistoryList = salaryList.map((salary) => ({
                ...salary,
                revisionItems: vm.processSalaryComponents(salary),
                isCurrent: false, // Historical data is not current
                color: vm.generateRandomColor(),
              }));
            } else {
              vm.salaryCurrentHistoryList = [];
            }

            // Set currency from response if not already set
            if (!vm.payrollCurrency) {
              vm.payrollCurrency =
                data.listSalaryTemplateDetails.currencySymbol;
            }
          } else {
            vm.salaryCurrentHistoryList = [];
          }
          vm.listLoading = false;
          return vm.salaryCurrentHistoryList;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.salaryCurrentHistoryList = [];
          vm.$store
            .dispatch("handleApiErrors", {
              error: err,
              action: "retrieve",
              form: vm.landedFormName,
              isListError: true,
            })
            .then((errorMessages) => {
              vm.errorContent = errorMessages;
              vm.isErrorInList = true;
            });
        });
    },
    retrieveCurrentSalaryDetails() {
      let vm = this;
      vm.detailsLoading = true;
      vm.errorContent = "";
      vm.isErrorInList = false;

      return vm.$apollo
        .mutate({
          mutation: RETRIEVE_SALARY_LIST,
          client: "apolloClientF",
          variables: {
            formId: 346,
            isViewMode: true,
            employeeId: vm.loginEmployeeId,
            includeHistoricalRecords: false,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listSalaryTemplateDetails &&
            response.data.listSalaryTemplateDetails.templateDetails
          ) {
            let data = JSON.parse(
              response.data.listSalaryTemplateDetails.templateDetails
            );

            // Handle new API response structure (listSalaryTemplateDetails)
            if (Array.isArray(data) && data.length > 0) {
              // Process current salary data with new structure
              vm.salaryCurrentDetails = data.map((salary) => ({
                ...salary,
                color: vm.generateRandomColor(),
                revisionItems: vm.processSalaryComponents(salary),
                isCurrent: true, // All items from current API call are current
              }));

              // Set currency from response
              vm.payrollCurrency =
                response.data.listSalaryTemplateDetails.currencySymbol;
            } else {
              vm.salaryCurrentDetails = [];
              vm.isErrorInList = true;
              vm.errorContent = "No current salary data found.";
            }
          } else {
            vm.salaryCurrentDetails = [];
            vm.isErrorInList = true;
            vm.errorContent = "No salary data found.";
          }
          vm.detailsLoading = false;
          return vm.salaryCurrentDetails;
        })
        .catch((err) => {
          vm.salaryCurrentDetails = [];
          vm.detailsLoading = false;
          vm.$store
            .dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: vm.landedFormName,
              isListError: true,
            })
            .then((errorMessages) => {
              vm.errorContent =
                errorMessages || "Something went wrong. Please try again.";
              vm.isErrorInList = true;
            });
        });
    },
    retrieveSalaryDetails() {
      let vm = this;
      vm.isLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;

      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            key: "hmc_salary_pay_master",
          },
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.retrieveDropdownDetails &&
            data.retrieveDropdownDetails.dropdownDetails
          ) {
            const tempData = JSON.parse(
              data.retrieveDropdownDetails.dropdownDetails
            );
            if (tempData && tempData.length) {
              vm.salaryDropdown = tempData[0].data || [];
            } else {
              vm.salaryDropdown = [];
            }
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: error,
            action: "retrieving",
            form: "salary details",
            isListError: false,
          });
        });
    },
    async getSalaryDetails() {
      let vm = this;
      vm.detailsLoading = true;
      this.errorContent = "";
      this.isErrorInList = false;
      vm.$apollo
        .mutate({
          mutation: GET_MY_SALARY,
          client: "apolloClientAH",
          variables: {
            formId: 346,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.generateSalaryInformation &&
            response.data.generateSalaryInformation.data
          ) {
            let data = JSON.parse(response.data.generateSalaryInformation.data);
            if (data.result) {
              let result = data.result.data.json;
              const remarksData = result?.remarks?.data;
              if (remarksData && remarksData.length > 0) {
                const sortedSalaryDetails = [...remarksData].sort(
                  (a, b) =>
                    new Date(b.effectiveDate) - new Date(a.effectiveDate)
                );
                this.salaryDetails = [...sortedSalaryDetails];
                this.salaryDetails.forEach((salary) => {
                  salary.color = this.generateRandomColor();
                });
              } else {
                this.isErrorInList = true;
                this.errorContent =
                  result?.remarks?.message + " Please try after some time." ||
                  "Something went wrong.";
                let snackbarData = {
                  isOpen: true,
                  message:
                    result?.remarks?.message + " - " + result?.remarks?.code ||
                    "Something went wrong.",
                  type: "warning",
                };
                vm.showAlert(snackbarData);
              }
            } else {
              this.salaryDetails = {};
              this.isErrorInList = true;
              this.errorContent =
                data.error?.json?.message || "Something went wrong.";
              let snackbarData = {
                isOpen: true,
                message: data?.error?.json?.message || "Something went wrong.",
                type: "warning",
              };
              vm.showAlert(snackbarData);
            }
          }
          vm.detailsLoading = false;
        })
        .catch((err) => {
          this.salaryDetails = {};
          vm.detailsLoading = false;
          vm.$store
            .dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: "salary",
              isListError: true,
            })
            .then((errorMessages) => {
              this.errorContent =
                errorMessages || "Something went wrong. Please try again.";
              this.isErrorInList = true;
            });
        });
    },
    // Helper method to process salary components from new API structure
    processSalaryComponents(salary) {
      if (!salary.allowances) return [];

      let revisionItems = [];

      // Process basic pay
      if (salary.allowances.basicPayArray?.length) {
        revisionItems.push({
          itemName: "basic_pay",
          value: salary.Basic_Pay || 0,
          itemId: "basic_pay",
        });
      }

      // Process allowances
      if (salary.allowances.allowanceArray?.length) {
        salary.allowances.allowanceArray.forEach((allowance) => {
          revisionItems.push({
            itemName: allowance.Allowance_Name.toLowerCase().replace(
              /\s+/g,
              "_"
            ),
            value: allowance.Amount || 0,
            itemId: allowance.Allowance_Id,
          });
        });
      }

      // Process fixed allowances
      if (salary.allowances.fixedAllowanceArray?.length) {
        salary.allowances.fixedAllowanceArray.forEach((allowance) => {
          revisionItems.push({
            itemName: allowance.Allowance_Name.toLowerCase().replace(
              /\s+/g,
              "_"
            ),
            value: allowance.Amount || 0,
            itemId: allowance.Allowance_Id,
          });
        });
      }

      // Process bonuses
      if (salary.allowances.bonusArray?.length) {
        salary.allowances.bonusArray.forEach((bonus) => {
          revisionItems.push({
            itemName: bonus.Allowance_Name.toLowerCase().replace(/\s+/g, "_"),
            value: bonus.Amount || 0,
            itemId: bonus.Allowance_Id,
          });
        });
      }

      // Process flexi benefits
      if (salary.allowances.flexiBenefitPlanArray?.length) {
        salary.allowances.flexiBenefitPlanArray.forEach((benefit) => {
          revisionItems.push({
            itemName: benefit.Allowance_Name.toLowerCase().replace(/\s+/g, "_"),
            value: benefit.Amount || 0,
            itemId: benefit.Allowance_Id,
          });
        });
      }

      // Process reimbursements
      if (salary.allowances.reimbursementArray?.length) {
        salary.allowances.reimbursementArray.forEach((reimbursement) => {
          revisionItems.push({
            itemName: reimbursement.Allowance_Name.toLowerCase().replace(
              /\s+/g,
              "_"
            ),
            value: reimbursement.Amount || 0,
            itemId: reimbursement.Allowance_Id,
          });
        });
      }

      return revisionItems;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onTabChange(tabName) {
      let form = this.allVisibleTabs.find((tab) => tab.formName === tabName);
      if (form && form.formId) {
        if (form.formId == 345) {
          this.$router.push("/my-finance/my-pay/my-payslip");
        }
      }
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
