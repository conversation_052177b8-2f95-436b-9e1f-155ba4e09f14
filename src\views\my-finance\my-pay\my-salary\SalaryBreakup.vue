<template>
  <v-overlay
    v-model="showOverlay"
    class="d-flex justify-end"
    @click:outside="$emit('close-form')"
  >
    <v-card
      :style="{
        height: windowHeight + 'px',
        width: windowWidth < 800 ? '100vw' : '60vw',
      }"
    >
      <v-card-title
        class="d-flex justify-space-between align-center bg-primary"
      >
        <div class="text-h6">Salary details</div>
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="$emit('close-form')"
          color="white"
        ></v-btn>
      </v-card-title>
      <v-card-text v-if="isLoading">
        <div v-for="i in 3" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </v-card-text>
      <v-card-text
        v-else
        style="height: calc(100vh - 100px); overflow-y: scroll"
      >
        <v-row dense justify="center">
          <v-col cols="12" md="6">
            <v-card outlined class="pa-2 text-center mt-2">
              <v-card-subtitle class="text-grey-darken-1 font-weight-medium">
                Effective Date
              </v-card-subtitle>
              <div class="text-body-1 font-weight-bold">
                {{ formattedEffectiveDate }}
              </div>
            </v-card>
          </v-col>
        </v-row>

        <v-divider class="my-4"></v-divider>

        <v-card class="pa-2 mt-3" v-if="Object.keys(salaryDetails).length">
          <v-row>
            <v-col cols="6" class="bg-grey-lighten-3">Components </v-col>
            <v-col class="bg-grey-lighten-3 text-end"
              >Monthly (in {{ payrollCurrency }})</v-col
            >
            <v-col class="bg-grey-lighten-3 text-end">
              <div class="pr-5">Annually (in {{ payrollCurrency }})</div>
            </v-col>
          </v-row>
          <v-row class="mt-2">
            <v-col class="font-weight-bold text-subtitle-1 no-padding">
              Earnings
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="6" class="text-subtitle-2 no-padding">
              Basic Pay
            </v-col>
            <v-col class="text-subtitle-2 no-padding text-end">
              {{ salaryDetails.Amount || 0 }}
            </v-col>
            <v-col class="text-subtitle-2 no-padding text-end">
              <div class="pr-5">
                {{ salaryDetails.Amount ? salaryDetails.Amount * 12 : 0 }}
              </div>
            </v-col>
          </v-row>
          <div
            v-if="salaryDetails.allowances?.['allowanceArray'].length"
            class="mt-3"
          >
            <v-row
              v-for="(innerItems, index) in salaryDetails.allowances[
                'allowanceArray'
              ]"
              :key="index"
            >
              <v-col cols="6" class="text-subtitle-2 no-padding">
                {{ innerItems.Allowance_Name }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                {{ innerItems.Amount || 0 }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                <div class="pr-5">
                  {{ innerItems.Amount * 12 || 0 }}
                </div>
              </v-col>
            </v-row>
          </div>
          <div
            v-if="salaryDetails.allowances?.['fixedAllowanceArray'].length"
            class="mt-3"
          >
            <v-row
              v-for="(innerItems, index) in salaryDetails.allowances[
                'fixedAllowanceArray'
              ]"
              :key="index"
            >
              <v-col cols="6" class="text-subtitle-2 no-padding">
                {{ innerItems.Allowance_Name }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                {{ innerItems.Amount || 0 }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                <div class="pr-5">
                  {{ innerItems.Amount * 12 || 0 }}
                </div>
              </v-col>
            </v-row>
          </div>
          <v-divider class="mt-4" thickness="3" color="black"></v-divider>
          <div
            v-for="(item, index) in [
              'reimbursementArray',
              'flexiBenefitPlanArray',
            ]"
            :key="index"
          >
            <div v-if="salaryDetails.allowances[item].length">
              <v-row class="mt-2">
                <v-col class="font-weight-bold text-subtitle-1 no-padding">
                  {{ getGroupName(item) }}
                </v-col>
              </v-row>
              <v-row
                v-for="(innerItems, index) in salaryDetails.allowances[item]"
                :key="index"
              >
                <v-col cols="6" class="text-subtitle-2 no-padding">
                  {{ innerItems.Allowance_Name }}
                </v-col>
                <v-col class="text-subtitle-2 no-padding text-end">
                  {{ innerItems.Amount || 0 }}
                </v-col>
                <v-col class="text-subtitle-2 no-padding text-end">
                  <div class="pr-5">
                    {{ innerItems.Amount * 12 || 0 }}
                  </div>
                </v-col>
              </v-row>
              <v-divider class="mt-4" thickness="3" color="black"></v-divider>
            </div>
          </div>
          <div class="mt-1">
            <v-row>
              <v-col cols="6" class="no-padding"> Gross Salary </v-col>
              <v-col class="no-padding text-end">
                {{ salaryDetails.Monthly_Gross_Salary || 0 }}
              </v-col>
              <v-col class="no-padding text-end">
                <div class="pr-5">
                  {{ salaryDetails.Annual_Gross_Salary || 0 }}
                </div>
              </v-col>
            </v-row>
            <v-divider class="mt-4" thickness="3" color="black"></v-divider>
          </div>
          <div
            v-if="salaryDetails.allowances?.['bonusArray'].length"
            class="mt-3"
          >
            <v-row class="mt-2">
              <v-col class="font-weight-bold text-subtitle-1 no-padding">
                Bonus
              </v-col>
            </v-row>
            <v-row
              v-for="(innerItems, index) in salaryDetails.allowances[
                'bonusArray'
              ]"
              :key="index"
            >
              <v-col cols="6" class="text-subtitle-2 no-padding">
                {{ innerItems.Allowance_Name }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                {{ innerItems.Amount || 0 }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                <div class="pr-5">
                  {{ innerItems.Amount * 12 || 0 }}
                </div>
              </v-col>
            </v-row>
            <v-divider class="mt-4" thickness="3" color="black"></v-divider>
          </div>
          <div v-if="salaryDetails.retirals?.length" class="mb-2">
            <v-row class="mt-2">
              <v-col class="font-weight-bold text-subtitle-1 no-padding">
                Retiral
              </v-col>
            </v-row>
            <v-row
              v-for="(innerItems, index) in salaryDetails.retirals"
              :key="index"
            >
              <v-col
                cols="6"
                class="text-subtitle-2 no-padding d-flex align-center"
              >
                <span class="mr-2">{{
                  innerItems.Retirals_Name?.toLowerCase() === "insurance"
                    ? innerItems.Insurance_Name
                    : innerItems.Retirals_Name
                }}</span>
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                {{ innerItems.Amount || 0 }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                <div class="pr-5">
                  {{ innerItems.Amount * 12 || 0 }}
                </div>
              </v-col>
            </v-row>
          </div>
        </v-card>
        <v-card class="pa-2 mt-3">
          <v-row>
            <v-col cols="6" class="bg-grey-lighten-4">Cost to Company</v-col>
            <v-col class="bg-grey-lighten-4 text-end">
              {{ payrollCurrency }}
              {{
                salaryDetails?.Annual_CTC
                  ? Math.ceil(salaryDetails?.Annual_CTC / 12)
                  : 0
              }}
            </v-col>
            <v-col class="bg-grey-lighten-4 text-end">
              <div class="pr-5">
                {{ payrollCurrency }}
                {{ salaryDetails?.Annual_CTC || 0 }}
              </div>
            </v-col>
          </v-row>
        </v-card>

        <v-card-text v-if="revisionData.remarks" class="mt-4">
          <div class="text-caption">{{ revisionData.remarks }}</div>
        </v-card-text>
      </v-card-text>
    </v-card>
  </v-overlay>
</template>
<script>
export default {
  name: "SalaryBreakup",
  emits: ["close-form"],
  props: {
    showBreakup: {
      type: Boolean,
      default: false,
    },
    revisionData: {
      type: Object,
      default: () => {},
    },
    isSyntrumEnabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      showOverlay: false,
      isLoading: false,
      salaryDetails: {},
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formattedEffectiveDate() {
      return this.formatDate(
        this.isSyntrumEnabled
          ? this.revisionData.effectiveDate
          : this.revisionData.Effective_From
      );
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    getGroupName() {
      return (groupName) => {
        let name = "";
        if (groupName) {
          name = groupName.split("Array");
          if (name.length) {
            name = name[0];
            name = name.split(/(?=[A-Z])/).join(" ");
            name = name.charAt(0).toUpperCase() + name.slice(1);
          }
        }
        return name;
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
  },
  watch: {
    showBreakup(val) {
      this.showOverlay = val;
      if (val) {
        this.getSalaryDetails();
      }
    },
  },
  mounted() {
    this.showOverlay = this.showBreakup;
    if (this.showBreakup) {
      this.getSalaryDetails();
    }
  },
  methods: {
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    },
    calculateYearlyValue(monthlyValue) {
      return monthlyValue * 12;
    },
    async getSalaryDetails() {
      let vm = this;
      vm.isLoading = true;

      let apiVariables = {
        formId: 346,
        isViewMode: true,
        id: null,
        employeeId: this.loginEmployeeId,
      };

      await vm.$store
        .dispatch("getSalaryDetails", apiVariables)
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let salaryList = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );
            if (salaryList.length) {
              vm.salaryDetails = salaryList[0];
              vm.processData();
            } else {
              vm.salaryDetails = {};
            }
          } else {
            vm.salaryDetails = {};
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "salary template details",
            isListError: false,
          });
        });
    },
    processData() {
      if (this.salaryDetails?.allowances?.basicPayArray?.length) {
        let basicPayObj = this.salaryDetails.allowances.basicPayArray[0];
        if (basicPayObj.Allowance_Type?.toLowerCase() === "percentage") {
          const mutiplier = basicPayObj.Percentage / 100;
          const basicPay = Math.ceil(
            (this.salaryDetails.Annual_CTC * mutiplier) / 12
          );
          this.salaryDetails.Amount = basicPay ? basicPay : 0;
        } else {
          this.salaryDetails.Amount = basicPayObj.Amount;
        }
      } else {
        this.salaryDetails.Amount = 0;
      }
      let keysToUpdate = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
        "bonusArray",
      ];
      if (this.salaryDetails?.allowances) {
        keysToUpdate.map((item) => {
          this.salaryDetails.allowances[item]?.forEach((innerItems) => {
            if (!innerItems.Amount) {
              if (innerItems.Allowance_Type?.toLowerCase() === "percentage") {
                const mutiplier = innerItems.Percentage / 100;
                const basicPay = Math.ceil(
                  this.salaryDetails.Amount * mutiplier
                );
                innerItems.Amount = basicPay ? basicPay : 0;
              }
            }
          });
        });
      }
      if (this.salaryDetails?.retirals && this.salaryDetails.retirals.length) {
        this.salaryDetails.retirals.forEach((item) => {
          if (
            item.Retirals_Type?.toLowerCase() === "percentage" &&
            !item.Employer_Share_Amount
          ) {
            let percentage = item.Employer_Share_Percentage / 100 || 0;
            const mutiplier = item.Employer_Retiral_Wages || 0;
            const basicPay = Math.ceil(percentage * mutiplier);
            item.Amount = basicPay ? basicPay : 0;
          } else {
            item.Percentage = null;
            item.Amount = Number(item.Employer_Share_Amount ?? 0);
          }
        });
      }
    },
  },
};
</script>
<style scoped>
.no-padding {
  padding-bottom: 0 !important;
}
</style>
