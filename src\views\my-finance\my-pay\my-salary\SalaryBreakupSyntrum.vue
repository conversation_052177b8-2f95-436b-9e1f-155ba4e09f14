<template>
  <v-overlay
    v-model="showOverlay"
    class="d-flex justify-end"
    @click:outside="$emit('close-form')"
  >
    <v-card height="100vh" :width="windowWidth < 800 ? '100vw' : '60vw'">
      <v-card-title
        class="d-flex justify-space-between align-center bg-primary"
      >
        <div class="text-h6">Salary details</div>
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="$emit('close-form')"
          color="white"
        ></v-btn>
      </v-card-title>
      <v-card-text v-if="isLoading">
        <div v-for="i in 3" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </v-card-text>
      <v-card-text
        v-else
        style="height: calc(100vh - 100px); overflow-y: scroll"
      >
        <v-row dense justify="center">
          <v-col cols="12" md="6">
            <v-card outlined class="pa-2 text-center mt-2">
              <v-card-subtitle class="text-grey-darken-1 font-weight-medium">
                Effective Date
              </v-card-subtitle>
              <div class="text-body-1 font-weight-bold">
                {{ formattedEffectiveDate }}
              </div>
            </v-card>
          </v-col>
        </v-row>

        <v-divider class="my-4"></v-divider>

        <v-container v-if="isMobileView">
          <v-row dense justify="center">
            <v-col :cols="windowWidth < 365 ? '4' : '6'"> EARNINGS </v-col>
            <v-col class="text-right"> MONTHLY </v-col>
            <v-col class="text-right"> ANNUALLY </v-col>
          </v-row>
        </v-container>

        <v-table>
          <thead>
            <tr>
              <th>EARNINGS</th>
              <th class="text-right">MONTHLY</th>
              <th class="text-right">ANNUALLY</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in salaryList" :key="item.itemId">
              <td>{{ itemName(item.itemName) }}</td>
              <td class="text-right">{{ roundOfNumber(item.value) }}</td>
              <td class="text-right">
                {{ roundOfNumber(calculateYearlyValue(item.value)) }}
              </td>
            </tr>
            <tr class="bg-grey-lighten-4">
              <td class="text-subtitle-1 font-weight-bold">
                Cost to Company (CTC)
              </td>
              <td class="text-right text-subtitle-1 font-weight-bold">
                {{ payrollCurrency }} {{ roundOfNumber(totalValue) }}
              </td>
              <td class="text-right text-subtitle-1 font-weight-bold">
                {{ payrollCurrency }} {{ roundOfNumber(totalYearlyValue) }}
              </td>
            </tr>
          </tbody>
        </v-table>

        <v-card-text v-if="revisionData.remarks" class="mt-4">
          <div class="text-caption">{{ revisionData.remarks }}</div>
        </v-card-text>
      </v-card-text>
    </v-card>
  </v-overlay>
</template>
<script>
export default {
  name: "SalaryBreakupSyntrum",
  emits: ["close-form"],
  props: {
    showBreakup: {
      type: Boolean,
      default: false,
    },
    revisionData: {
      type: Object,
      default: () => {},
    },
    salaryDetails: {
      type: Array,
      default: () => [],
    },
    isSyntrumEnabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      showOverlay: false,
      isLoading: false,
      salaryList: [],
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formattedEffectiveDate() {
      return this.formatDate(
        this.isSyntrumEnabled
          ? this.revisionData.effectiveDate
          : this.revisionData.Effective_From
      );
    },
    totalValue() {
      if (this.revisionData?.revisionItems?.length)
        return this.revisionData.revisionItems.reduce(
          (sum, item) =>
            sum +
            (item.itemName.toLowerCase() === "full_inc"
              ? 0
              : parseInt(item.value)),
          0
        );
      return 0;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    totalYearlyValue() {
      return this.totalValue * 12;
    },
    roundOfNumber() {
      return (num) => {
        return num.toFixed(2);
      };
    },
    itemName() {
      return (itemCode) => {
        if (this.salaryDetails?.length) {
          let itemValue = this.salaryDetails.filter((item) => {
            if (itemCode.toLowerCase() === item.Salary_Pay_Code.toLowerCase())
              return item;
          });
          if (
            itemValue &&
            itemValue[0] &&
            itemValue[0].Salary_Pay_Description
          ) {
            return itemValue[0].Salary_Pay_Description;
          }
          return itemCode.replace(/_/g, " ");
        }
      };
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  watch: {
    showBreakup(val) {
      this.showOverlay = val;
    },
    revisionData(val) {
      if (val && val.revisionItems?.length) {
        let items = val.revisionItems.filter(
          (item) => item.itemName.toLowerCase() !== "full_inc"
        );
        this.salaryList = items;
      }
    },
  },
  mounted() {
    this.showOverlay = this.showBreakup;
    if (this.revisionData?.revisionItems?.length) {
      let items = this.revisionData.revisionItems.filter(
        (item) => item.itemName.toLowerCase() !== "full_inc"
      );
      this.salaryList = items;
    }
  },
  methods: {
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    },
    calculateYearlyValue(monthlyValue) {
      return monthlyValue * 12;
    },
  },
};
</script>
<style></style>
