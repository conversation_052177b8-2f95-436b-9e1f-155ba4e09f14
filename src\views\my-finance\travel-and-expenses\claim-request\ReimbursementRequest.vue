<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <!-- Top bar content for filtering -->
        <template v-slot:topBarContent>
          <v-row justify="center" v-if="!showAddEditForm">
            <v-col cols="12" md="8" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="true"
                :is-default-filter="false"
                @apply-emp-filter="applyFilter()"
                @reset-emp-filter="resetFilter()"
              >
                <template #new-filter>
                  <v-row class="mr-2 mt-2">
                    <v-col :cols="12" class="py-2" v-if="formId === 338">
                      <v-autocomplete
                        v-model="selectedEmployees"
                        :items="filterEmployeeList"
                        label="Employee"
                        variant="solo"
                        color="primary"
                        item-value="empId"
                        item-title="empName"
                        multiple
                        closable-chips
                        chips
                        single-line
                      ></v-autocomplete>
                    </v-col>
                    <v-col :cols="!isMobileView ? 6 : 12" class="py-2">
                      <v-autocomplete
                        v-model="selectedStatus"
                        :items="statusList"
                        label="Status"
                        variant="solo"
                        color="primary"
                        multiple
                        closable-chips
                        chips
                        single-line
                      ></v-autocomplete>
                    </v-col>
                    <v-col
                      v-if="
                        fieldForce &&
                        formId == 338 &&
                        (isAdmin || isServiceProviderAdmin)
                      "
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                    >
                      <v-autocomplete
                        v-model="selectedServiceProvider"
                        variant="solo"
                        :label="labelList[115]?.Field_Alias"
                        color="primary"
                        :items="serviceProviderList"
                        item-title="Service_Provider_Name"
                        item-value="Service_Provider_Id"
                        multiple
                        closable-chips
                        chips
                        single-line
                      ></v-autocomplete>
                    </v-col>
                  </v-row>
                </template>
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="expanse-claim-container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList && !listLoading"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList()"
          ></AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="
              itemList.length === 0 && !listLoading && !showAddEditForm
            "
            key="no-results-screen"
            :main-title="
              originalList?.length
                ? 'There are no records for the selected filters/searches.'
                : ''
            "
            :image-name="originalList?.length === 0 ? '' : 'common/no-records'"
            :isSmallImage="originalList.length === 0"
          >
            <template #contentSlot>
              <div class="d-flex mb-2 flex-wrap justify-center align-center">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      v-if="formId === 338"
                      notes="The Reimbursement Management feature streamlines the process of handling employee expense claims while ensuring compliance with company policies. Employees can submit claims for various work-related expenses such as travel, meals, internet, and office supplies. The system allows employees to attach supporting documents, such as receipts and invoices, ensuring transparency and accountability."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      v-if="formId === 338"
                      notes="Reimbursement requests are automatically routed to managers or the finance team for approval. Multi-level approval workflows can be configured based on claim amounts or employee hierarchy. Once approved, the claims are processed for disbursement, integrating seamlessly with payroll systems or accounting systems. This feature reduces administrative burden, minimizes processing delays, and ensures timely reimbursement while maintaining financial compliance."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      v-if="formId === 339"
                      notes="The Reimbursement Request feature enables employees to submit and track expense claims independently. Employees can enter expense details, select categories, specify amounts, and attach supporting documents such as invoices or receipts. The system ensures compliance by validating claims against predefined expense categories and limits."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      v-if="formId === 339"
                      notes="Once submitted, requests are routed to the appropriate approvers, such as the Reporting Manager or Finance Team. Approvers have the option to approve, reject, or request modifications before finalizing the claim. Approved reimbursements are processed for payment, ensuring timely disbursement. This feature simplifies expense tracking, enhances transparency, and provides employees with a seamless experience for managing work-related reimbursements."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0"
                      class="bg-white my-2"
                      rounded="lg"
                    >
                      <template v-slot:prepend>
                        <v-icon color="primary" size="14"
                          >fas fa-calendar-alt</v-icon
                        >
                      </template>
                      {{ formattedSelectedMonth }}
                      <v-menu
                        activator="parent"
                        :close-on-content-click="false"
                        transition="scale-transition"
                        offset-y
                      >
                        <Datepicker
                          v-model="selectedMonthYear"
                          :value="selectedMonthYear"
                          :inline="true"
                          :format="'MMMM, yyyy'"
                          maximum-view="year"
                          minimum-view="month"
                          @update:modelValue="onChangeDate($event)"
                        />
                      </v-menu>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      prepend-icon="fas fa-plus"
                      variant="elevated"
                      rounded="lg"
                      class="mx-1 primary"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onActions('Add')"
                    >
                      <span>Add Claim</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      class="mt-1"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter()"
                    >
                      Reset Filter/Search
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div
              v-if="originalList.length > 0 && !showAddEditForm"
              class="d-flex align-center my-3"
              :class="
                isMobileView
                  ? 'justify-center flex-column'
                  : 'justify-space-between'
              "
            >
              <v-btn class="bg-white my-2" rounded="lg">
                <template v-slot:prepend>
                  <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
                </template>
                {{ formattedSelectedMonth }}
                <v-menu
                  activator="parent"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                >
                  <Datepicker
                    v-model="selectedMonthYear"
                    :value="selectedMonthYear"
                    :inline="true"
                    :format="'MMMM, yyyy'"
                    maximum-view="year"
                    minimum-view="month"
                    @update:modelValue="onChangeDate($event)"
                  />
                </v-menu>
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn
                prepend-icon="fas fa-plus"
                variant="elevated"
                rounded="lg"
                class="mx-1 primary"
                :size="isMobileView ? 'small' : 'default'"
                @click="onActions('Add')"
              >
                <span>Add Claim</span>
              </v-btn>
              <div class="d-flex align-center">
                <v-btn
                  rounded="lg"
                  class="mt-1"
                  color="transparent"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                          >
                            <v-tooltip :text="action.message">
                              <template v-slot:activator="{ props }">
                                <div v-bind="action.message ? props : ''">
                                  <v-icon size="15" class="pr-2">{{
                                    action.icon
                                  }}</v-icon>
                                  {{ action.key }}
                                </div>
                              </template>
                            </v-tooltip>
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>
            <v-row>
              <v-col cols="12" class="mb-12" v-if="!showAddEditForm">
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="onActions('Edit', item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        v-if="formId === 338"
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Employee Name
                        </div>
                        <section>
                          <div style="max-width: 200px" class="text-truncate">
                            <span
                              class="text-primary text-body-2 font-weight-medium"
                            >
                              <v-tooltip
                                :text="item.Employee_Name"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-bind="
                                      item.Employee_Name &&
                                      item.Employee_Name.length > 20
                                        ? props
                                        : ''
                                    "
                                    >{{
                                      checkNullValue(item.Employee_Name)
                                    }}</span
                                  >
                                </template>
                              </v-tooltip>
                              <v-tooltip
                                :text="item.User_Defined_EmpId?.toString()"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <div
                                    v-if="item.User_Defined_EmpId"
                                    v-bind="
                                      item.User_Defined_EmpId &&
                                      item.User_Defined_EmpId.length > 20
                                        ? props
                                        : ''
                                    "
                                    class="text-grey"
                                  >
                                    {{
                                      checkNullValue(item.User_Defined_EmpId)
                                    }}
                                  </div>
                                </template>
                              </v-tooltip>
                            </span>
                          </div>
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Claim Amount
                        </div>
                        <section
                          :class="formId == 339 ? 'text-primary' : ''"
                          :style="isMobileView ? 'max-width: 60%' : ''"
                        >
                          {{ checkNullValue(item.Actual_Total_Amount) }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Approved Amount
                        </div>
                        <section>
                          {{ checkNullValue(item.Total_Amount) }}
                        </section>
                      </td>
                      <td
                        :class="isMobileView ? 'd-flex align-center' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Status
                        </div>
                        <section :class="getCustomClass(item.Approval_Status)">
                          {{ checkNullValue(item.Approval_Status) }}
                        </section>
                      </td>
                      <td
                        class="align-center"
                        :class="isMobileView ? 'd-flex' : ''"
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="font-weight-bold"
                          style="width: 60%"
                        >
                          Actions
                        </div>
                        <section class="d-flex justify-end align-center">
                          <ActionMenu
                            v-if="item.actions?.length"
                            @selected-action="onActions($event, item)"
                            :actions="item.actions"
                            :access-rights="checkAccess"
                          />
                          <div style="width: 50px" v-else>-</div>
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <EmployeesListModal
      v-if="showEmployeeList"
      :show-modal="showEmployeeList"
      :employeesList="allEmployeesList"
      :showFilterSearch="true"
      selectStrategy="single"
      :isApplyFilter="true"
      employeeIdKey="employeeId"
      userDefinedEmpIdKey="userDefinedEmpId"
      employeeNameKey="employeeName"
      deptNameKey="departmentName"
      designationKey="designationName"
      departmentIdKey="departmentId"
      designationIdKey="designationId"
      locationIdKey="locationId"
      empTypeIdKey="empTypeId"
      workScheduleIdKey="workSchedule"
      @on-select-employee="onSelectEmployee($event)"
      @close-modal="showEmployeeList = false"
    ></EmployeesListModal>
    <AddEditReimbursement
      v-if="showAddEditForm"
      :form-id="formId"
      :selected-item="selectedItem"
      :selected-employee-id="selectedEmployee"
      :employees-list="allEmployeesList"
      :form-access="formAccess"
      :is-edit="isEdit"
      @refetch-list="refetchList()"
      @close-form="closeAllForms()"
    ></AddEditReimbursement>
    <ApprovalFlowModal
      v-if="openApprovalModal"
      :task-id="selectedItem.Process_Instance_Id"
      @close-modal="closeAllForms(false)"
    />
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const AddEditReimbursement = defineAsyncComponent(() =>
  import("./AddEditReimbursement.vue")
);
const EmployeesListModal = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeesListModal")
);
import ApprovalFlowModal from "@/components/custom-components/ApprovalFlowModal.vue";

import { RETRIEVE_REIMBURSEMENT_LIST } from "@/graphql/employee-self-service/reimbursement";
import FileExportMixin from "@/mixins/FileExportMixin";
import Datepicker from "vuejs3-datepicker";
import { checkNullValue } from "@/helper";
import moment from "moment";
export default {
  name: "ReimbursementRequest",
  mixins: [FileExportMixin],
  components: {
    NotesCard,
    EmployeeDefaultFilterMenu,
    ActionMenu,
    Datepicker,
    AddEditReimbursement,
    EmployeesListModal,
    ApprovalFlowModal,
  },
  props: {
    formId: {
      type: Number,
      default: 339,
    },
    callingFrom: {
      type: String,
      default: "myFinance",
    },
  },
  data() {
    return {
      currentTabItem: "tab-1",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      itemList: [],
      originalList: [],
      filterList: [],
      isSearchApplied: false,
      isFilterApplied: false,
      openMoreMenu: false,
      showEditForm: false,
      selectedEmployees: [],
      selectedStatus: [],
      selectedMonthYear: new Date(),
      showAddEditForm: false,
      showEmployeeList: false,
      isFetchingEmployees: false,
      allEmployeesList: [],
      selectedEmployee: null,
      selectedItem: null,
      isEdit: false,
      selectedServiceProvider: [],
      serviceProviderList: [],
      fieldForce: 0,
      openApprovalModal: false,
    };
  },
  computed: {
    travelFormId() {
      let fId = this.callingFrom === "team" ? "341" : "342";
      return parseInt(fId);
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    checkAccess() {
      let havingAccess = {};
      havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      havingAccess["approval workflow"] = this.formAccess?.view ? 1 : 0;
      return havingAccess;
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessRights(
        "service-provider-admin"
      );
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    mainTabs() {
      let tabs = [];
      if (this.customFormAccess(this.travelFormId)?.view) {
        tabs.push(this.travelCustomizedFormName);
      }
      tabs.push("Claim Request");
      if (this.formAccess && this.approvalFormAccess && this.formId == 338) {
        tabs.push("Approvals");
      }
      return tabs;
    },
    approvalFormAccess() {
      let formAccess = this.accessRights(184);
      if (formAccess && formAccess.accessRights["view"]) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    travelCustomizedFormName() {
      if (
        this.customFormAccess(this.travelFormId) &&
        this.customFormAccess(this.travelFormId).customFormName
      ) {
        return this.formAccess.customFormName;
      }
      return "Travel Request";
    },
    customFormAccess() {
      return (formId) => {
        let travelRequestFormAccess = this.accessRights(formId);
        if (
          travelRequestFormAccess &&
          travelRequestFormAccess.accessRights &&
          travelRequestFormAccess.accessRights["view"]
        ) {
          return travelRequestFormAccess.accessRights;
        } else {
          return false;
        }
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formattedSelectedMonth() {
      return moment(this.selectedMonthYear).format("MMMM, YYYY");
    },
    formAccess() {
      let formAccessRights = this.accessRights(this.formId);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      return [{ key: "Export", icon: "fas fa-file-export" }];
    },
    tableHeaders() {
      let headers = [];
      if (this.formId == 338) {
        headers.push({
          title: "Employee Name",
          align: "start",
          key: "Employee_Name",
        });
      }
      headers.push(
        {
          title: "Claim Amount",
          key: "Actual_Total_Amount",
        },
        {
          title: "Approved Amount",
          key: "Total_Amount",
        },
        {
          title: "Status",
          key: "Approval_Status",
        },
        {
          title: "Actions",
          align: "end",
          sortable: false,
        }
      );
      return headers;
    },
    getCustomClass() {
      return (status) => {
        if (status?.toLowerCase() === "pending approval") {
          return "text-blue";
        } else if (status?.toLowerCase() === "draft") {
          return "text-purple";
        } else if (status?.toLowerCase() === "approved") {
          return "text-green";
        } else {
          return "text-red";
        }
      };
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    filterEmployeeList() {
      return Array.from(
        new Map(
          this.originalList.map((item) => [
            item.Employee_Id,
            {
              empId: item.Employee_Id,
              empName: item.Employee_Name + " - " + item.User_Defined_EmpId,
            },
          ])
        ).values()
      );
    },
    statusList() {
      let allStatus = Array.from(
        new Map(
          this.originalList.map((item) => [
            item.Approval_Status,
            item.Approval_Status,
          ])
        ).values()
      );
      allStatus.sort();
      return allStatus;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf("Claim Request");
    this.retrieveReimbursementList();
    this.fetchDropdownData();
    if (this.formId == 338) this.getEmpList();
  },
  methods: {
    checkNullValue,
    onMoreAction(action) {
      this.openMoreMenu = false;
      if (action?.toLowerCase() === "export") {
        this.exportReportFile();
      }
    },
    onTabChange(tabName) {
      if (tabName == this.travelCustomizedFormName) {
        if (this.callingFrom == "team")
          this.$router.push("/my-team/travel-and-expenses/travel-request");
        else {
          this.$router.push("/my-finance/travel-and-expenses/travel-request");
        }
      }
      if (tabName === "Approvals") {
        this.$router.push(`/approvals/approval-management?form_id=${267}`);
      }
    },
    fetchDropdownData() {
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { fieldForce, serviceProvider } =
              res.data.getDropDownBoxDetails;
            this.serviceProviderList = serviceProvider;
            this.fieldForce = fieldForce;
          }
        })
        .catch(() => {
          this.fieldForce = 0;
          this.serviceProviderList = [];
        });
    },
    exportReportFile() {
      let exportHeaders = [];
      if (this.formId == 338) {
        exportHeaders.push(
          {
            header: "Employee Id",
            key: "User_Defined_EmpId",
          },
          {
            header: "Employee Name",
            key: "Employee_Name",
          }
        );
      }
      exportHeaders.push(
        {
          header: "Claim Month",
          key: "Submission_Date",
        },
        {
          header: "Claim Amount",
          key: "Actual_Total_Amount",
        },
        {
          header: "Approved Amount",
          key: "Total_Amount",
        },
        {
          header: "Status",
          key: "Approval_Status",
        },
        {
          header: "Added On",
          key: "Added_On",
        },
        {
          header: "Added By",
          key: "Added_By_Name",
        },
        {
          header: "Reviewed On",
          key: "Approved_On",
        },
        {
          header: "Reviewed By",
          key: "Approved_By_Name",
        }
      );
      let dataList = this.itemList.map((item) => {
        return {
          ...item,
          Added_On: moment(item.Added_On).isValid() ? item.Added_On : "",
          Approved_On: moment(item.Approved_On).isValid()
            ? item.Approved_On
            : "",
          Submission_Date: moment(item.Submission_Date).isValid()
            ? moment(item.Submission_Date).format("MMMM, YYYY")
            : "",
        };
      });

      const exportOptions = {
        fileExportData: dataList,
        fileName: "Reimbursement Report",
        sheetName: "Reimbursements",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
    onApplySearch(val) {
      if (!val) {
        this.isSearchApplied = false;
        this.itemList = this.isFilterApplied
          ? this.filterList
          : this.originalList;
      } else {
        this.isSearchApplied = true;
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.isFilterApplied
          ? this.filterList
          : this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });

        this.itemList = searchItems;
        if (!this.isFilterApplied) this.filterList = searchItems;
      }
    },
    onChangeDate(event) {
      this.selectedMonthYear = event;
      this.refetchList();
    },
    onActions(type, item) {
      if (type?.toLowerCase() === "edit") {
        if (this.formAccess.view) {
          this.selectedItem = item;
          this.isEdit = true;
          this.selectedEmployee = item.Employee_Id;
          this.showAddEditForm = true;
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else if (type?.toLowerCase() === "add") {
        if (this.formAccess.add) {
          if (this.formId == 338) {
            this.showEmployeeList = true;
          } else {
            this.showAddEditForm = true;
            this.isEdit = false;
            this.selectedEmployee = this.loginEmployeeId;
          }
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else if (type?.toLowerCase() === "approval workflow") {
        this.openApprovalModal = true;
        this.selectedItem = item;
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    applyFilter() {
      let filteredArray = this.isSearchApplied
        ? this.filterList
        : this.originalList;
      this.isFilterApplied = true;
      if (this.selectedEmployees.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmployees.includes(item.Employee_Id);
        });
      }
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.Approval_Status);
        });
      }
      if (this.selectedServiceProvider.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedServiceProvider.includes(
            item.Service_Provider_Id
          );
        });
      }
      this.itemList = filteredArray;
      if (!this.isSearchApplied) {
        this.filterList = filteredArray;
      }
    },
    resetFilter() {
      this.selectedEmployees = [];
      this.selectedStatus = [];
      this.selectedServiceProvider = [];
      this.itemList = this.isSearchApplied
        ? this.filterList
        : this.originalList;
      this.isFilterApplied = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    closeAllForms(callAPi = true) {
      this.showAddEditForm = false;
      this.selectedItem = null;
      this.selectedEmployee = null;
      this.isEdit = false;
      this.openApprovalModal = false;
      if (callAPi) this.retrieveReimbursementList();
    },
    async getEmpList() {
      let formId = this.formId;

      await this.$store
        .dispatch("getEmployeesList", {
          formName: "Attendance",
          formId: formId,
          flag: "payslipreq",
        })
        .then((empData) => {
          this.allEmployeesList = empData.map((item) => ({
            ...item,
            employeeData: item.employeeName + " - " + item.userDefinedEmpId,
          }));
        })
        .catch((err) => {
          let snackbarData = {
            isOpen: true,
            message: "",
            type: "warning",
          };
          if (err === "error") {
            snackbarData.message =
              "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator.";
          } else {
            snackbarData.message = err;
          }
          this.showAlert(snackbarData);
        });
    },
    onSelectEmployee(employee) {
      this.selectedEmployee = employee.employeeId;
      this.showEmployeeList = false;
      this.showAddEditForm = true;
      this.isEdit = false;
    },
    refetchList() {
      this.errorContent = "";
      this.isErrorInList = false;
      this.closeAllForms();
      this.resetFilter();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.retrieveReimbursementList();
    },
    retrieveReimbursementList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_REIMBURSEMENT_LIST,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            formId: vm.formId,
            employeeId: vm.formId == 338 ? null : vm.loginEmployeeId,
            reimbursementMonth: parseInt(
              moment(vm.selectedMonthYear).format("M")
            ),
            reimbursementYear: parseInt(
              moment(vm.selectedMonthYear).format("YYYY")
            ),
          },
        })
        .then((response) => {
          if (response?.data?.listReimbursement?.reimbursementDetails?.length) {
            let detailObj =
              response.data.listReimbursement.reimbursementDetails.map(
                (item) => {
                  let [month, year] =
                    item.Submission_Date.split(",").map(Number);
                  let submissionDate = new Date(year, month - 1, 1);
                  let actions = [];

                  // Add Edit action based on status
                  if (
                    item.Approval_Status?.toLowerCase() === "draft" ||
                    item.Approval_Status?.toLowerCase() === "pending approval"
                  ) {
                    actions.push("Edit");
                  }

                  // Add Approval Workflow if Process_Instance_Id exists
                  if (item.Process_Instance_Id) {
                    actions.push("Approval Workflow");
                  }

                  return {
                    ...item,
                    Submission_Date: submissionDate,
                    actions: actions,
                  };
                }
              );
            vm.itemList = detailObj;
            vm.filterList = detailObj;
            vm.originalList = detailObj;
          } else {
            vm.itemList = [];
            vm.originalList = [];
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "reimbursement",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
<style scoped>
.expanse-claim-container {
  padding: 5em 2em 0em 3em;
}
@media screen and (max-width: 805px) {
  .expanse-claim-container {
    padding: 5em 1em 0em 1em;
  }
}
:deep(.reimbursement-filter_datepicker .vuejs3-datepicker__calendar) {
  position: relative !important;
}
</style>
