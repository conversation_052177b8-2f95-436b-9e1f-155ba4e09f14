<template>
  <v-app>
    <v-main v-if="!isLoading">
      <v-container fluid class="pa-0">
        <v-card
          v-if="!errorContent?.length"
          class="full-height pa-3"
          style="overflow: scroll"
        >
          <div class="d-flex justify-space-between">
            <v-icon
              color="primary"
              class="font-weight-bold my-auto"
              :size="20"
              @click="this.$emit('close-form')"
              >fas fa-arrow-left
            </v-icon>
            <v-btn
              v-if="allowEdit"
              class="ml-auto"
              @click="this.$emit('open-edit-form', travelRequestSelected)"
              color="primary"
              variant="text"
            >
              <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
            </v-btn>
            <v-icon
              :size="15"
              color="grey"
              class="my-auto ml-1"
              @click="
                retrieveTravelRequestData(this.travelRequestSelected.requestId)
              "
              >fas fa-redo-alt</v-icon
            >
          </div>
          <div
            class="d-flex ml-7 align-center pt-4 text-h6 text-grey-darken-1 font-weight-bold"
          >
            <v-progress-circular
              v-if="!isMobileView"
              model-value="100"
              color="orange"
              :size="22"
              class="mr-2"
            ></v-progress-circular>
            Travel Request Details
            <span>
              <v-chip
                class="ml-4"
                :color="statusColor(travelRequest.status)"
                outlined
                @click="openApprovalModal = true"
                small
              >
                {{ travelRequest.status }}
              </v-chip>
            </span>
          </div>

          <ApprovalFlowModal
            v-if="openApprovalModal && travelRequest.processInstanceId"
            :task-id="travelRequest.processInstanceId"
            @close-modal="openApprovalModal = false"
          ></ApprovalFlowModal>

          <v-card-text class="pa-6">
            <!-- Basic Information -->
            <v-row class="pa-3">
              <v-col v-if="callingFrom === 'team'" cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Employee Id
                </div>
                <div class="text-body-1 mb-4">
                  {{ checkNullValue(travelRequest.userDefinedEmpId) }}
                </div>
              </v-col>
              <v-col v-if="callingFrom === 'team'" cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Employee Name
                </div>
                <div class="text-body-1 mb-4">
                  {{ checkNullValue(travelRequest.employeeName) }}
                </div>
              </v-col>
              <v-col cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Travel Title
                </div>
                <v-tooltip
                  :text="travelRequest?.tripName || ''"
                  max-width="400"
                  location="bottom"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      v-bind="travelRequest?.tripName?.length > 25 ? props : ''"
                      class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                    >
                      {{ checkNullValue(travelRequest.tripName) }}
                    </div>
                  </template>
                </v-tooltip>
              </v-col>

              <v-col cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Travel Type
                </div>
                <div class="text-body-1 mb-4">
                  {{ checkNullValue(travelRequest.travelType) }}
                </div>
              </v-col>
              <v-col
                v-if="
                  travelRequest.travelType?.toLowerCase() === 'international'
                "
                cols="12"
                md="4"
              >
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Visa Required
                </div>
                <div class="text-body-1 mb-4">
                  {{ travelRequest.visaRequired ? "Yes" : "No" }}
                </div>
              </v-col>

              <v-col
                v-if="
                  travelRequest.travelType?.toLowerCase() === 'international' &&
                  travelRequest.visaRequired
                "
                cols="12"
                md="4"
              >
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Destination Country for Visa
                </div>
                <div class="text-body-1 mb-4">
                  {{ checkNullValue(travelRequest.destinationCountry) }}
                </div>
              </v-col>

              <v-col cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Travel Start Date
                </div>
                <div class="text-body-1 mb-4">
                  {{ formatDate(travelRequest.travelStartDate) }}
                </div>
              </v-col>
              <v-col cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Travel End Date
                </div>
                <div class="text-body-1 mb-4">
                  {{ formatDate(travelRequest.travelEndDate) }}
                </div>
              </v-col>

              <v-col cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Budget Amount
                </div>
                <div class="text-body-1 mb-4">
                  {{
                    payrollCurrency +
                    " " +
                    checkNullValue(travelRequest.budgetAmount)
                  }}
                </div>
              </v-col>

              <v-col cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Seat Preference
                </div>
                <div class="text-body-1 mb-4">
                  {{ checkNullValue(travelRequest.seatPreference) }}
                </div>
              </v-col>

              <v-col cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Meal Preference
                </div>
                <div class="text-body-1 mb-4">
                  {{ checkNullValue(travelRequest.mealPreference) }}
                </div>
              </v-col>

              <v-col cols="12" md="4">
                <div class="text-subtitle-1 font-weight-bold mb-2">
                  Business Purpose
                </div>
                <v-tooltip
                  open-on-click
                  v-if="travelRequest?.businessPurpose?.length > 40"
                  max-width="400"
                >
                  <template v-slot:activator="{ props }">
                    <div
                      class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                      v-bind="props"
                    >
                      {{ checkNullValue(travelRequest.businessPurpose) }}
                    </div>
                  </template>
                  <span>{{
                    checkNullValue(travelRequest.businessPurpose)
                  }}</span>
                </v-tooltip>
                <div v-else class="text-body-1 mb-4">
                  {{ checkNullValue(travelRequest.businessPurpose) }}
                </div>
              </v-col>
            </v-row>

            <!-- Flight Section -->
            <v-card v-if="travelRequest.flightDetails?.length" class="mb-6">
              <v-card-title
                :class="isMobileView ? 'text-subtitle-2' : 'text-h6'"
                class="bg-grey-lighten-4 py-4 px-6 d-flex align-center"
              >
                <v-progress-circular
                  model-value="100"
                  color="light-blue-darken-1"
                  :size="isMobileView ? 18 : 22"
                  class="mr-2"
                ></v-progress-circular>
                Flight Details - {{ travelRequest.tripType }}
              </v-card-title>
              <v-card-text class="mt-4">
                <v-expansion-panels v-model="openFlightPanels" multiple>
                  <v-expansion-panel
                    v-for="(flight, index) in travelRequest.flightDetails"
                    :key="index"
                  >
                    <v-expansion-panel-title
                      :class="isMobileView ? 'py-1 px-2' : ''"
                    >
                      <template v-slot:actions>
                        <v-icon
                          :size="isMobileView ? 'small' : 'default'"
                        ></v-icon>
                      </template>
                      <div class="d-flex align-center">
                        <span
                          :class="[
                            'font-weight-bold',
                            isMobileView ? 'text-body-2' : '',
                          ]"
                          >Flight {{ index + 1 }}</span
                        >
                        <v-chip
                          :class="isMobileView ? 'ml-2 text-caption' : 'ml-4'"
                          :color="flight.selfBooking ? 'success' : 'info'"
                          :size="isMobileView ? 'x-small' : 'small'"
                          style="white-space: nowrap"
                        >
                          {{
                            flight.selfBooking ? "Self Booking" : "Travel Desk"
                          }}
                        </v-chip>
                      </div>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            From
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(flight.departFrom) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            To
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(flight.arriveAt) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Flight Preference
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(flight.airlinePreference) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Departure Date
                          </div>
                          <div class="text-body-1 mb-4">
                            {{
                              checkNullValue(formatDate(flight.departureDate))
                            }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Departure Time Preference
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(flight.departureTimePreference) }}
                          </div>
                        </v-col>

                        <v-col
                          v-if="
                            travelRequest?.tripType?.toLowerCase() ===
                            'round trip'
                          "
                          cols="12"
                          md="4"
                        >
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Return Date
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(formatDate(flight.returnDate)) }}
                          </div>
                        </v-col>

                        <v-col
                          v-if="
                            travelRequest?.tripType?.toLowerCase() ===
                            'round trip'
                          "
                          cols="12"
                          md="4"
                        >
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Return Time Preference
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(flight.arrivalTimePreference) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Description
                          </div>
                          <v-tooltip
                            open-on-click
                            v-if="flight.entityDescription?.length > 40"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                class="text-body-1 text-truncate"
                                v-bind="props"
                              >
                                {{ checkNullValue(flight.entityDescription) }}
                              </div>
                            </template>
                            <span>{{
                              checkNullValue(flight.entityDescription)
                            }}</span>
                          </v-tooltip>
                          <div v-else class="text-body-1">
                            {{ checkNullValue(flight.entityDescription) }}
                          </div>
                        </v-col>
                      </v-row>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>

            <!-- Hotels Section -->
            <v-card v-if="travelRequest.hotelStays?.length" class="mb-6">
              <v-card-title
                :class="isMobileView ? 'text-subtitle-2' : 'text-h6'"
                class="bg-grey-lighten-4 py-4 px-6 d-flex align-center"
              >
                <v-progress-circular
                  model-value="100"
                  color="green-darken-2"
                  :size="isMobileView ? 18 : 22"
                  class="mr-2"
                ></v-progress-circular>
                Hotel Details
              </v-card-title>
              <v-card-text class="mt-4">
                <v-expansion-panels v-model="openHotelPanels" multiple>
                  <v-expansion-panel
                    v-for="(hotel, index) in travelRequest.hotelStays"
                    :key="index"
                  >
                    <v-expansion-panel-title
                      :class="isMobileView ? 'py-1 px-2' : ''"
                    >
                      <template v-slot:actions>
                        <v-icon
                          :size="isMobileView ? 'small' : 'default'"
                        ></v-icon>
                      </template>
                      <div class="d-flex align-center">
                        <span
                          :class="[
                            'font-weight-bold',
                            isMobileView ? 'text-body-2' : '',
                          ]"
                          >Hotel {{ index + 1 }}</span
                        >
                        <v-chip
                          :class="isMobileView ? 'ml-2 text-caption' : 'ml-4'"
                          :color="hotel.selfBooking ? 'success' : 'info'"
                          :size="isMobileView ? 'x-small' : 'small'"
                        >
                          <span class="text-truncate">
                            {{
                              hotel.selfBooking ? "Self Booking" : "Travel Desk"
                            }}
                          </span>
                        </v-chip>
                      </div>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Location
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(hotel.location) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Check In
                          </div>
                          <div class="text-body-1 mb-4">
                            {{
                              checkNullValue(
                                formatDateTime(hotel.checkInDatetime)
                              )
                            }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Check Out
                          </div>
                          <div class="text-body-1 mb-4">
                            {{
                              checkNullValue(
                                formatDateTime(hotel.checkOutDatetime)
                              )
                            }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Hotel Preference
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(hotel.hotelPreference) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Description
                          </div>
                          <v-tooltip
                            open-on-click
                            v-if="hotel.entityDescription?.length > 40"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                class="text-body-1 text-truncate"
                                v-bind="props"
                              >
                                {{ checkNullValue(hotel.entityDescription) }}
                              </div>
                            </template>
                            <span>{{
                              checkNullValue(hotel.entityDescription)
                            }}</span>
                          </v-tooltip>
                          <div v-else class="text-body-1">
                            {{ checkNullValue(hotel.entityDescription) }}
                          </div>
                        </v-col>
                      </v-row>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>

            <!-- Car Rentals Section -->
            <v-card v-if="travelRequest.carDetails?.length" class="mb-6">
              <v-card-title
                :class="isMobileView ? 'text-subtitle-2' : 'text-h6'"
                class="bg-grey-lighten-4 py-4 px-6 d-flex align-center"
              >
                <v-progress-circular
                  model-value="100"
                  color="orange"
                  :size="isMobileView ? 18 : 22"
                  class="mr-2"
                ></v-progress-circular>
                Car Rental Details
              </v-card-title>
              <v-card-text class="mt-4">
                <v-expansion-panels v-model="openCarRentalPanels" multiple>
                  <v-expansion-panel
                    v-for="(car, index) in travelRequest.carDetails"
                    :key="index"
                  >
                    <v-expansion-panel-title
                      :class="isMobileView ? 'py-1 px-2' : ''"
                    >
                      <template v-slot:actions>
                        <v-icon
                          :size="isMobileView ? 'small' : 'default'"
                        ></v-icon>
                      </template>
                      <div class="d-flex align-center">
                        <span
                          :class="[
                            'font-weight-bold',
                            isMobileView ? 'text-body-2' : '',
                          ]"
                          >Car {{ index + 1 }}</span
                        >
                        <v-chip
                          :class="isMobileView ? 'ml-2 text-caption' : 'ml-4'"
                          :color="car.selfBooking ? 'success' : 'info'"
                          :size="isMobileView ? 'x-small' : 'small'"
                        >
                          {{ car.selfBooking ? "Self Booking" : "Travel Desk" }}
                        </v-chip>
                      </div>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            From
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(car.departFrom) }}
                          </div>
                        </v-col>
                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Pick-up Date and Time
                          </div>
                          <div class="text-body-1 mb-4">
                            {{
                              checkNullValue(formatDateTime(car.pickUpDateTime))
                            }}
                          </div>
                        </v-col>
                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            To
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(car.arriveAt) }}
                          </div>
                        </v-col>
                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Drop-off Date and Time
                          </div>
                          <div class="text-body-1 mb-4">
                            {{
                              checkNullValue(formatDateTime(car.dropDateTime))
                            }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Car Type
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(car.carType) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Driver Needed
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ car.driverNeeded ? "Yes" : "No" }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Description
                          </div>
                          <v-tooltip
                            open-on-click
                            v-if="car.entityDescription?.length > 40"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                class="text-body-1 text-truncate"
                                v-bind="props"
                              >
                                {{ checkNullValue(car.entityDescription) }}
                              </div>
                            </template>
                            <span>{{
                              checkNullValue(car.entityDescription)
                            }}</span>
                          </v-tooltip>
                          <div v-else class="text-body-1">
                            {{ checkNullValue(car.entityDescription) }}
                          </div>
                        </v-col>
                      </v-row>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>

            <!-- Buses Section -->
            <v-card v-if="travelRequest.busDetails?.length" class="mb-6">
              <v-card-title
                :class="isMobileView ? 'text-subtitle-2' : 'text-h6'"
                class="bg-grey-lighten-4 py-4 px-6 d-flex align-center"
              >
                <v-progress-circular
                  model-value="100"
                  color="light-blue-darken-1"
                  :size="isMobileView ? 18 : 22"
                  class="mr-2"
                ></v-progress-circular>
                Bus Details
              </v-card-title>
              <v-card-text class="mt-4">
                <v-expansion-panels v-model="openBusPanels" multiple>
                  <v-expansion-panel
                    v-for="(bus, index) in travelRequest.busDetails"
                    :key="index"
                  >
                    <v-expansion-panel-title
                      :class="isMobileView ? 'py-1 px-2' : ''"
                    >
                      <template v-slot:actions>
                        <v-icon
                          :size="isMobileView ? 'small' : 'default'"
                        ></v-icon>
                      </template>
                      <div class="d-flex align-center">
                        <span
                          :class="[
                            'font-weight-bold',
                            isMobileView ? 'text-body-2' : '',
                          ]"
                          >Bus {{ index + 1 }}</span
                        >
                        <v-chip
                          :class="isMobileView ? 'ml-2 text-caption' : 'ml-4'"
                          :color="bus.selfBooking ? 'success' : 'info'"
                          :size="isMobileView ? 'x-small' : 'small'"
                        >
                          {{ bus.selfBooking ? "Self Booking" : "Travel Desk" }}
                        </v-chip>
                      </div>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Depart From
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(bus.departFrom) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Arrive At
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(bus.arriveAt) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Departure Date
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(formatDate(bus.departureDate)) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Time Preference
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(bus.timePreference) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Description
                          </div>
                          <v-tooltip
                            open-on-click
                            v-if="bus.entityDescription?.length > 40"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                class="text-body-1 text-truncate"
                                v-bind="props"
                              >
                                {{ checkNullValue(bus.entityDescription) }}
                              </div>
                            </template>
                            <span>{{
                              checkNullValue(bus.entityDescription)
                            }}</span>
                          </v-tooltip>
                          <div v-else class="text-body-1">
                            {{ checkNullValue(bus.entityDescription) }}
                          </div>
                        </v-col>
                      </v-row>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>

            <!-- Trains Section -->
            <v-card v-if="travelRequest.trainDetails?.length" class="mb-6">
              <v-card-title
                :class="isMobileView ? 'text-subtitle-2' : 'text-h6'"
                class="bg-grey-lighten-4 py-4 px-6 d-flex align-center"
              >
                <v-progress-circular
                  model-value="100"
                  color="green-darken-2"
                  :size="isMobileView ? 18 : 22"
                  class="mr-2"
                ></v-progress-circular>
                Train Details
              </v-card-title>
              <v-card-text class="mt-4">
                <v-expansion-panels v-model="openTrainPanels" multiple>
                  <v-expansion-panel
                    v-for="(train, index) in travelRequest.trainDetails"
                    :key="index"
                  >
                    <v-expansion-panel-title
                      :class="isMobileView ? 'py-1 px-2' : ''"
                    >
                      <template v-slot:actions>
                        <v-icon
                          :size="isMobileView ? 'small' : 'default'"
                        ></v-icon>
                      </template>
                      <div class="d-flex align-center">
                        <span
                          :class="[
                            'font-weight-bold',
                            isMobileView ? 'text-body-2' : '',
                          ]"
                          >Train {{ index + 1 }}</span
                        >
                        <v-chip
                          :class="isMobileView ? 'ml-2 text-caption' : 'ml-4'"
                          :color="train.selfBooking ? 'success' : 'info'"
                          :size="isMobileView ? 'x-small' : 'small'"
                        >
                          {{
                            train.selfBooking ? "Self Booking" : "Travel Desk"
                          }}
                        </v-chip>
                      </div>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Depart From
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(train.departFrom) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Arrive At
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(train.arriveAt) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Departure Date
                          </div>
                          <div class="text-body-1 mb-4">
                            {{
                              checkNullValue(formatDate(train.departureDate))
                            }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Time Preference
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(train.timePreference) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Description
                          </div>
                          <v-tooltip
                            open-on-click
                            v-if="train.entityDescription?.length > 40"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                class="text-body-1 text-truncate"
                                v-bind="props"
                              >
                                {{ checkNullValue(train.entityDescription) }}
                              </div>
                            </template>
                            <span>{{
                              checkNullValue(train.entityDescription)
                            }}</span>
                          </v-tooltip>
                          <div v-else class="text-body-1">
                            {{ checkNullValue(train.entityDescription) }}
                          </div>
                        </v-col>
                      </v-row>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>

            <!-- Per Diem Section -->
            <v-card v-if="travelRequest.perDiemDetails?.length" class="mb-6">
              <v-card-title
                :class="isMobileView ? 'text-subtitle-2' : 'text-h6'"
                class="bg-grey-lighten-4 py-4 px-6 d-flex align-center"
              >
                <v-progress-circular
                  model-value="100"
                  color="green-darken-2"
                  :size="isMobileView ? 18 : 22"
                  class="mr-2"
                ></v-progress-circular>
                Per Diem Details
              </v-card-title>
              <v-card-text class="mt-4">
                <v-expansion-panels v-model="openPerDiemPanels" multiple>
                  <v-expansion-panel
                    v-for="(perDiem, index) in travelRequest.perDiemDetails"
                    :key="index"
                  >
                    <v-expansion-panel-title
                      :class="isMobileView ? 'py-1 px-2' : ''"
                    >
                      <template v-slot:actions>
                        <v-icon
                          :size="isMobileView ? 'small' : 'default'"
                        ></v-icon>
                      </template>
                      <div class="d-flex align-center">
                        <span
                          :class="[
                            'font-weight-bold',
                            isMobileView ? 'text-body-2' : '',
                          ]"
                          >Per Diem {{ index + 1 }}</span
                        >
                      </div>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <v-row>
                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Per Diem Title
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(perDiem.perDiemTitle) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Per Diem Rate
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(perDiem.perDiemRate) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Start Date
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(formatDate(perDiem.startDate)) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            End Date
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ checkNullValue(formatDate(perDiem.endDate)) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Per Diem Amount
                          </div>
                          <div class="text-body-1 mb-4">
                            {{ calculatePerDiemDays(perDiem) }}
                          </div>
                        </v-col>

                        <v-col cols="12" md="4">
                          <div class="text-subtitle-1 font-weight-bold mb-2">
                            Description
                          </div>
                          <v-tooltip
                            open-on-click
                            v-if="perDiem.entityDescription?.length > 40"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div
                                class="text-body-1 text-truncate"
                                v-bind="props"
                              >
                                {{ checkNullValue(perDiem.entityDescription) }}
                              </div>
                            </template>
                            <span>{{
                              checkNullValue(perDiem.entityDescription)
                            }}</span>
                          </v-tooltip>
                          <div v-else class="text-body-1">
                            {{ checkNullValue(perDiem.entityDescription) }}
                          </div>
                        </v-col>
                      </v-row>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-card-text>
            </v-card>

            <!-- More Details Section -->
            <v-col v-if="moreDetailsList.length > 0" cols="12">
              <MoreDetails
                :more-details-list="moreDetailsList"
                :open-close-card="openMoreDetails"
                @on-open-close="openMoreDetails = $event"
              ></MoreDetails>
            </v-col>
          </v-card-text>
        </v-card>
        <AppFetchErrorScreen
          v-else
          class="d-flex align-center"
          :content="errorContent"
          icon-name="fas fa-redo-alt"
          button-text="Retry"
          @button-click="
            retrieveTravelRequestData(this.travelRequestSelected.requestId)
          "
        >
        </AppFetchErrorScreen>
      </v-container>
    </v-main>
  </v-app>
  <AppLoading v-if="isLoading" />
</template>

<script>
import { defineComponent } from "vue";
import { RETRIEVE_TRAVEL_REQUEST } from "@/graphql/employee-self-service/travelRequestQueries.js";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import ApprovalFlowModal from "@/components/custom-components/ApprovalFlowModal.vue";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import moment from "moment";

export default defineComponent({
  name: "ViewEmployeeTravel",
  components: {
    MoreDetails,
    ApprovalFlowModal,
  },
  emits: ["close-form", "open-edit-form"],
  props: {
    travelRequestSelected: {
      type: Object,
      required: true,
    },
    formId: {
      type: Number,
      required: true,
    },
    travelCustomizedFormName: {
      type: String,
      default: "Travel Request",
    },
    formAccess: {
      type: Object,
      required: true,
    },
    callingFrom: {
      type: String,
      default: "employee",
    },
  },
  data() {
    return {
      isLoading: true,
      errorContent: "",
      travelRequest: {},
      openFlightPanels: [],
      openHotelPanels: [],
      openCarRentalPanels: [],
      openBusPanels: [],
      openTrainPanels: [],
      openPerDiemPanels: [],
      moreDetailsList: [],
      openMoreDetails: true,
      openApprovalModal: false,
    };
  },
  mounted() {
    this.retrieveTravelRequestData(this.travelRequestSelected.requestId);
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    allowEdit() {
      if (
        this.formAccess.update &&
        (this.travelRequestSelected.status?.toLowerCase() === "applied" ||
          this.travelRequestSelected.status?.toLowerCase() === "rejected" ||
          this.travelRequestSelected.status?.toLowerCase() === "cancelled")
      ) {
        return true;
      }
      return false;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    formatDateTime() {
      return (date) => {
        if (date) {
          let orgDateFormat =
            this.$store.state.orgDetails.orgDateFormat + " HH:mm";
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    calculatePerDiemDays(perDiem) {
      if (perDiem.startDate && perDiem.endDate && perDiem.perDiemRate) {
        const startDate = moment(perDiem.startDate);
        const endDate = moment(perDiem.endDate);

        // Calculate the difference in days (inclusive of both start and end dates)
        const daysDifference = endDate.diff(startDate, "days") + 1;

        // Ensure the result is not negative
        return (Math.max(0, daysDifference) * perDiem.perDiemRate).toFixed(2);
      } else {
        return "-";
      }
    },
    statusColor(status) {
      switch (status) {
        case "Applied":
          return "primary";
        case "Approved":
          return "green";
        case "Rejected":
          return "red";
        case "Returned":
          return "amber";
        case "Cancelled":
          return "amber";
        default:
          return "";
      }
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.travelRequest.addedOn),
        addedByName = this.travelRequest.addedByName,
        updatedByName = this.travelRequest.updatedByName,
        updatedOn = this.convertUTCToLocal(this.travelRequest.updatedOn);
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    retrieveTravelRequestData(requestId) {
      let vm = this;
      vm.errorContent = "";
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_TRAVEL_REQUEST,
          client: "apolloClientAC",
          variables: {
            requestId: requestId,
            formId: this.formId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveEmployeeTravel &&
            response.data.retrieveEmployeeTravel.travelDetails
          ) {
            vm.travelRequest =
              response.data.retrieveEmployeeTravel.travelDetails;
            vm.prefillMoreDetails();
            vm.isLoading = false;
          } else {
            vm.handleRetrieveError(
              (err = ""),
              this.travelCustomizedFormName?.toLowerCase()
            );
          }
        })
        .catch((err) => {
          vm.handleRetrieveError(
            err,
            this.travelCustomizedFormName?.toLowerCase()
          );
        });
    },
    handleRetrieveError(err = "", formName) {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
        });
    },
  },
  watch: {
    "travelRequest.flightDetails": {
      handler(newFlights) {
        if (newFlights?.length) {
          this.openFlightPanels = newFlights.map((_, index) => index);
        }
      },
      immediate: true,
    },
    "travelRequest.hotelStays": {
      handler(newHotels) {
        if (newHotels?.length) {
          this.openHotelPanels = newHotels.map((_, index) => index);
        }
      },
      immediate: true,
    },
    "travelRequest.carDetails": {
      handler(newCarRentals) {
        if (newCarRentals?.length) {
          this.openCarRentalPanels = newCarRentals.map((_, index) => index);
        }
      },
      immediate: true,
    },
    "travelRequest.busDetails": {
      handler(newBuses) {
        if (newBuses?.length) {
          this.openBusPanels = newBuses.map((_, index) => index);
        }
      },
      immediate: true,
    },
    "travelRequest.trainDetails": {
      handler(newTrains) {
        if (newTrains?.length) {
          this.openTrainPanels = newTrains.map((_, index) => index);
        }
      },
      immediate: true,
    },
  },
});
</script>

<style scoped>
.full-height {
  min-height: 100vh;
}

.v-card-title {
  color: #1867c0;
}

.text-subtitle-1 {
  color: rgba(0, 0, 0, 0.6);
}

.text-body-1 {
  color: rgba(0, 0, 0, 0.87);
}

.v-chip {
  white-space: nowrap !important;
  max-width: none !important;
}
</style>
