<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="false"
              ></EmployeeDefaultFilterMenu>
              <FormFilter
                v-if="!isLoading"
                :originalList="originalList"
                :show-view-form="true"
                :hide-date="true"
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="attendance-delete-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList && !isLoading"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0"
            key="no-results-screen"
            :main-title="'There are no records for the selected filters/searches.'"
            :image-name="
              originalList?.length === 0
                ? 'workflow/empty-approval'
                : 'common/no-records'
            "
            :isSmallImage="originalList.length === 0"
          >
            <template #contentSlot>
              <div class="d-flex mb-2 flex-wrap justify-center align-center">
                <v-btn
                  class="bg-white my-2 ml-2"
                  :style="'width: max-content'"
                  :size="isMobileView ? 'small' : 'default'"
                  rounded="lg"
                  @click="$refs.datePicker.fp.open()"
                >
                  <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
                  <span class="text-caption px-1">Date:</span>
                  <flat-pickr
                    ref="datePicker"
                    v-model="appliedDateRange"
                    :config="flatPickerOptions"
                    placeholder="Select Date Range"
                    class="ml-2 mt-1 date-range-picker-custom-bg"
                    style="
                      outline: 0px;
                      color: var(--v-primary-base);
                      width: 170px;
                    "
                    @onChange="onChangeDateRange"
                  ></flat-pickr>
                </v-btn>
                <v-btn
                  v-if="originalList.length === 0"
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-btn
                  v-if="originalList.length > 0"
                  color="primary"
                  variant="elevated"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="resetFilter()"
                >
                  Reset Filter/Search
                </v-btn>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <!-- Right Side (Buttons and Menu) -->
            <div
              v-if="originalList.length"
              class="d-flex align-center mb-1"
              :class="{
                'w-100': true,
                'flex-column': isMobileView,
                'mr-2': isMobileView,
                'justify-center': isMobileView,
                'justify-end': !isMobileView,
              }"
            >
              <v-btn
                class="bg-white my-2 ml-2"
                :style="'width: max-content'"
                :size="isMobileView ? 'small' : 'default'"
                rounded="lg"
                @click="$refs.datePicker.fp.open()"
              >
                <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
                <span class="text-caption px-1">Date:</span>
                <flat-pickr
                  ref="datePicker"
                  v-model="appliedDateRange"
                  :config="flatPickerOptions"
                  placeholder="Select Date Range"
                  class="ml-2 mt-1 date-range-picker-custom-bg"
                  style="
                    outline: 0px;
                    color: var(--v-primary-base);
                    width: 170px;
                  "
                  @onChange="onChangeDateRange"
                ></flat-pickr>
              </v-btn>
              <div>
                <v-btn
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'bg-hover': isHovering,
                            }"
                          >
                            {{ action.key }}
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>
            <v-row>
              <v-col
                cols="12"
                v-if="isMobileView"
                class="pa-0 d-flex justify-end"
              >
                <v-tooltip :text="enableDisableDeleteIcon[1]">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      :disabled="!enableDisableDeleteIcon[0]"
                    >
                      <v-icon
                        color="red"
                        size="20"
                        v-bind="props"
                        :class="
                          enableDisableDeleteIcon[0]
                            ? 'list-delete-enabled'
                            : 'list-delete-disabled'
                        "
                        @click="
                          enableDisableDeleteIcon[0] ? onClickDelete() : {}
                        "
                        >fas fa-trash-alt</v-icon
                      >
                    </v-btn>
                  </template>
                </v-tooltip>
              </v-col>
              <v-col v-if="originalList.length > 0" class="mb-12">
                <v-data-table
                  v-model="selectedRecords"
                  :headers="tableHeaders"
                  :items="itemList"
                  item-value="Attendance_Id"
                  fixed-header
                  :show-select="!isMobileView"
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:[`header.data-table-select`]="{ selectAll }">
                    <v-checkbox-btn
                      v-model="selectAllBox"
                      color="primary"
                      false-icon="far fa-circle"
                      true-icon="fas fa-check-circle"
                      :indeterminate="
                        selectedItems.length !== 0 &&
                        selectedItems.length !== itemList.length
                      "
                      indeterminate-icon="fas fa-minus-circle"
                      @change="selectAll(selectAllBox)"
                    ></v-checkbox-btn>
                  </template>
                  <template v-slot:item="{ item }">
                    <tr
                      @click="openViewForm(item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView ? 'v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td :class="isMobileView ? 'mt-3 mb-n5' : ''">
                        <v-checkbox-btn
                          v-model="item.isSelected"
                          color="primary"
                          false-icon="far fa-circle"
                          true-icon="fas fa-check-circle"
                          class="mt-n2 ml-n2"
                          @click.stop="
                            {
                            }
                          "
                          @change="checkAllSelected()"
                        ></v-checkbox-btn>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold mt-2">
                          Employee
                        </div>
                        <section class="d-flex align-center">
                          <!-- <div
                            v-if="
                              !isMobileView &&
                              selectedItem &&
                              selectedItem.Employee_Id === item.Employee_Id
                            "
                            class="data-table-side-border d-flex"
                          ></div> -->

                          <div style="max-width: 200px" class="text-truncate">
                            <span
                              class="text-primary text-body-2 font-weight-medium"
                            >
                              <v-tooltip
                                :text="item.Employee_Name"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-bind="
                                      item.Employee_Name &&
                                      item.Employee_Name.length > 20
                                        ? props
                                        : ''
                                    "
                                    >{{ item.Employee_Name }}</span
                                  >
                                </template>
                              </v-tooltip>
                              <v-tooltip
                                :text="item.User_Defined_EmpId?.toString()"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <div
                                    v-if="item.User_Defined_EmpId"
                                    v-bind="
                                      item.User_Defined_EmpId &&
                                      item.User_Defined_EmpId.length > 20
                                        ? props
                                        : ''
                                    "
                                    class="text-grey"
                                  >
                                    {{
                                      checkNullValue(item.User_Defined_EmpId)
                                    }}
                                  </div>
                                </template>
                              </v-tooltip>
                            </span>
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold">
                          Date
                        </div>
                        <section class="text-body-2">
                          {{ checkNullValue(formatDate(item.AttendanceDate)) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold">
                          Check In
                        </div>
                        <section class="text-body-2">
                          {{
                            checkNullValue(
                              formatDateTime(item.Attendance_PunchIn_Date)
                            )
                          }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold">
                          Check Out
                        </div>
                        <section class="text-body-2">
                          {{
                            checkNullValue(
                              formatDateTime(item.Attendance_PunchOut_Date)
                            )
                          }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold">
                          Gross Hours
                        </div>
                        <section class="text-body-2">
                          {{ checkNullValue(formatTime(item.Total_Hours)) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold">
                          Late Arrival
                        </div>
                        <section>
                          {{ checkNullValue(item.Display_Late_Attendance) }}
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold">
                          Status
                        </div>
                        <section :class="customClass(item.Approval_Status)">
                          {{ checkNullValue(item.Approval_Status) }}
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <ViewRecord
    v-if="showViewForm"
    :isEdit="false"
    :selectedEmployee="selectedEmployee"
    :access-rights="formAccess"
    :landedFormName="landedFormName"
    :selectedItem="selectedItem"
    @close-view-attendance-window="showViewForm = false"
    @refetch-data="refetchList()"
    @close-form="closeAllForms()"
  ></ViewRecord>
  <AppWarningModal
    v-if="conformationModel"
    :open-modal="conformationModel"
    :confirmation-heading="'Are you sure you want to delete the records?'"
    :icon-name="'fas fa-trash-alt'"
    :icon-color="'red'"
    :icon-Size="75"
    @close-warning-modal="conformationModel = false"
    @accept-modal="onDeleteAttendanceLog()"
  />
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
import { LIST_ATTENDANCE_APPROVAL_RECORDS } from "@/graphql/my-team/attendance.js";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
const FormFilter = defineAsyncComponent(() =>
  import(
    "../../../coreHr/time-off-management/employee-attendance/AttendanceApprovalFilter.vue"
  )
);
const ViewRecord = defineAsyncComponent(() =>
  import(
    "../../../coreHr/time-off-management/employee-attendance/ViewAttendanceApproval.vue"
  )
);
export default {
  name: "AttendanceDelete",
  components: { EmployeeDefaultFilterMenu, FormFilter, ViewRecord, flatPickr },
  mixins: [FileExportMixin],
  data() {
    return {
      currentTabItem: "tab-2",
      appliedDateRange: null,
      isErrorInList: false,
      originalList: [],
      itemList: [],
      isLoading: false,
      listLoading: false,
      openMoreMenu: false,
      selectedRecords: [],
      selectAllBox: false,
      showViewForm: false,
      selectedItem: null,
      selectedItems: [],
      conformationModel: false,
      startDate: "",
      endDate: "",
    };
  },
  computed: {
    landedFormName() {
      return "Attendance Bulk Action";
    },
    attendanceFormAccess() {
      return this.$store.getters.attendanceFormAccess;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.attendanceFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    customClass() {
      return (status) => {
        if (status && status.toLowerCase() === "approved") {
          return "text-green";
        } else if (status && status.toLowerCase() === "rejected") {
          return "text-red";
        } else if (status && status.toLowerCase() === "applied") {
          return "text-blue";
        }
      };
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment().format(this.$store.state.orgDetails.orgDateFormat),
      };
    },
    formatTime() {
      return (timeString) => {
        if (!timeString) return "";

        const [hours, minutes] = timeString.split(":").map(Number);
        let formattedTime = "";
        if (hours && minutes) {
          formattedTime = `${hours} Hrs ${minutes} Mins`;
        } else if (hours) {
          formattedTime = `${hours} Hrs`;
        } else if (minutes) {
          formattedTime = `0 Hrs ${minutes} Mins`;
        }

        return formattedTime;
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(320);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    formatDateTime() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat =
            this.$store.state.orgDetails.orgDateFormat + " HH:mm";
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    moreActions() {
      let actions = [
        {
          key: "Delete",
          icon: "fas fa-trash-alt",
        },
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    tableHeaders() {
      return [
        {
          title: "Employee",
          align: "start",
          key: "Employee_Name",
        },
        {
          title: "Date",
          key: "AttendanceDate",
        },
        {
          title: "Check-In",
          key: "Attendance_PunchIn_Date",
        },
        {
          title: "Check Out",
          key: "Attendance_PunchOut_Date",
        },
        {
          title: "Gross Hours",
          key: "Total_Hours",
        },
        {
          title: "Late Arrival",
          key: "Display_Late_Attendance",
        },
        {
          title: "Status",
          key: "status",
          align: "center",
          sortable: false,
        },
      ];
    },
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    selectedRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemLogList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.Attendance_Id)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
          let index = this.selectedItems.findIndex(
            (x) => x.Attendance_Id === item.Attendance_Id
          );
          if (index === -1) {
            this.selectedItems.push(item);
          }
        }
      } else {
        // Iterate through itemLogList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
        this.selectedItems = [];
      }
    },
  },
  mounted() {
    this.getCurrentDateRange();
  },
  methods: {
    checkNullValue,
    getCurrentDateRange() {
      // Current Date
      const currentDate = moment();
      // Get current date -7
      const currentDateMinusSeven = moment().subtract(6, "days");
      this.startDate = currentDateMinusSeven.format("YYYY-MM-DD");
      this.endDate = currentDate.format("YYYY-MM-DD");
      this.appliedDateRange =
        currentDateMinusSeven.format("DD/MM/YYYY") +
        " to " +
        currentDate.format("DD/MM/YYYY");
      this.fetchList();
    },
    onChangeDateRange(selectedDates) {
      if (selectedDates.length > 1) {
        // Parse the dates from the given format
        let parsedStartDate = moment(selectedDates[0], "DD/MM/YYYY");
        let parsedEndDate = moment(
          selectedDates.length > 1 ? selectedDates[1] : selectedDates[0],
          "DD/MM/YYYY"
        );

        // Format the dates into "YYYY-MM-DD" format
        this.startDate = parsedStartDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.fetchList();
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });

        this.itemList = searchItems;
      }
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.selectedItems = [];
      this.selectAllBox = false;
      this.closeAllForms();
      this.fetchList();
    },
    closeAllForms() {
      this.showViewForm = false;
      this.selectedItem = null;
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      let month = moment(this.startDate, "YYYY-MM-DD").format("M");
      let year = moment(this.startDate, "YYYY-MM-DD").format("YYYY");
      vm.$apollo
        .query({
          query: LIST_ATTENDANCE_APPROVAL_RECORDS,
          client: "apolloClientAC",
          variables: {
            attendanceMonth: parseInt(month),
            attendanceYear: parseInt(year),
            startDate: this.startDate,
            endDate: this.endDate,
            formId: 320,
            flag: "bulkoperation",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listAttendanceApprovalRecords &&
            response.data.listAttendanceApprovalRecords
              .attendanceApprovalDetails &&
            !response.data.listAttendanceApprovalRecords.errorCode
          ) {
            let responseData =
              response.data.listAttendanceApprovalRecords
                .attendanceApprovalDetails;
            vm.itemList = responseData;
            vm.originalList = responseData;
          } else {
            let error = response.data.listAttendanceApprovalRecords.errorCode;
            vm.originalList = [];
            vm.itemList = [];
            vm.handleListError(error);
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Attendance Approvals",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectedItems = selectedItems;
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    onDeleteAttendanceLog() {
      let vm = this;
      this.conformationModel = false;
      vm.isLoading = true;
      let attendanceIds = this.selectedItems.map((item) => item.Attendance_Id);

      const apiObj = {
        url: vm.baseUrl + "employees/attendance/delete-attendance/",
        type: "POST",
        async: false,
        dataType: "json",
        data: {
          attendanceId: attendanceIds,
          attendanceImportExist: 0,
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          let snackbarData = {};
          if (res && res.success) {
            this.selectedItems = [];
            snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.landedFormName + " deleted successfully.",
            };
            vm.refetchList("Attendance record deleted");
          } else {
            snackbarData = {
              isOpen: true,
              type: "warning",
              message:
                res?.msg && res?.msg?.length
                  ? res?.msg
                  : "Something went wrong while deleting the attendance record. Please try after some time.",
            };
          }
          vm.isLoading = false;
          vm.showAlert(snackbarData);
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "delete",
            form: "Attendance",
            isListError: false,
          });
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onMoreAction(actionType) {
      if (actionType?.toLowerCase() === "export") {
        this.exportReportFile();
      } else if (actionType?.toLowerCase() === "delete") {
        if (this.selectedItems.length) {
          if (this.formAccess.delete) {
            this.conformationModel = true;
          } else {
            this.showAlert({
              isOpen: true,
              type: "warning",
              message: "You don't have access to perform this action.",
            });
          }
        } else {
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: "Please select at least one record to delete.",
          });
        }
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee", key: "Employee_Name" },
        { header: "Date", key: "AttendanceDate" },
        { header: "Check In", key: "Attendance_PunchIn_Date" },
        { header: "Check Out", key: "Attendance_PunchOut_Date" },
        { header: "Gross Hours", key: "Total_Hours" },
        { header: "Late Arrival", key: "Display_Late_Attendance" },
        { header: "Status", key: "Approval_Status" },
        { header: "Added By", key: "Added_By_Name" },
        { header: "Added On", key: "Added_On" },
        { header: "Updated By", key: "Updated_By_Name" },
        { header: "Updated On", key: "Updated_On" },
      ];

      let dataList = this.itemList.map((el) => {
        return {
          ...el,
          AttendanceDate: this.formatDate(el.AttendanceDate),
          Attendance_PunchIn_Date: this.formatDateTime(
            el.Attendance_PunchIn_Date
          ),
          Attendance_PunchOut_Date: this.formatDateTime(
            el.Attendance_PunchOut_Date
          ),
          Added_On: this.formatDateTime(el.Added_On),
          Updated_On: this.formatDateTime(el.Updated_On),
          Approved_On: this.formatDateTime(el.Approved_On),
        };
      });

      const exportOptions = {
        fileExportData: dataList,
        fileName: "Attendance Bulk Action",
        sheetName: "Attendance Details",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
    resetFilter() {
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = this.originalList;
    },
    applyFilter(filteredArray) {
      this.itemList = filteredArray;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.attendanceFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/my-team/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/my-team/" + clickedForm.url;
        }
      }
    },
  },
};
</script>
<style>
.attendance-delete-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .attendance-delete-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
