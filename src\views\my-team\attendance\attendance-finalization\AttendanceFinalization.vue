<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="true"
                :isDefaultFilter="false"
                @apply-emp-filter="filterAppliedCount += 1"
                @reset-emp-filter="resetFilter()"
              >
                <template v-slot:new-filter>
                  <v-row class="mr-2">
                    <v-col
                      :cols="12"
                      class="py-2"
                      v-if="openedSubTab == 'Early Checkout'"
                    >
                      <v-autocomplete
                        v-model="filterObj.selectedEmployees"
                        variant="solo"
                        label="Employee"
                        color="primary"
                        :items="employeeList"
                        item-value="employeeId"
                        item-title="employeeData"
                        multiple
                        closable-chips
                        chips
                        single-line
                        :loading="employeeListLoading"
                      ></v-autocomplete>
                    </v-col>
                    <v-col
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                      v-if="openedSubTab == 'Early Checkout'"
                    >
                      <v-text-field
                        v-model="filterObj.selectedMinFirstIn"
                        :active="minFirstInMenu"
                        :focused="minFirstInMenu"
                        label="Min First In"
                        variant="solo"
                        prepend-inner-icon="fas fa-clock"
                        readonly
                        single-line
                      >
                        <v-menu
                          v-model="minFirstInMenu"
                          :close-on-content-click="false"
                          activator="parent"
                          transition="scale-transition"
                        >
                          <v-time-picker
                            :hide-header="true"
                            v-model="filterObj.selectedMinFirstIn"
                            format="24hr"
                            @update:minute="minFirstInMenu = false"
                          ></v-time-picker>
                        </v-menu>
                      </v-text-field>
                    </v-col>
                    <v-col
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                      v-if="openedSubTab == 'Early Checkout'"
                    >
                      <v-text-field
                        v-model="filterObj.selectedMaxFirstIn"
                        :active="maxFirstInMenu"
                        :focused="maxFirstInMenu"
                        label="Max First In"
                        variant="solo"
                        prepend-inner-icon="fas fa-clock"
                        readonly
                        single-line
                      >
                        <v-menu
                          v-model="maxFirstInMenu"
                          :close-on-content-click="false"
                          activator="parent"
                          transition="scale-transition"
                        >
                          <v-time-picker
                            :hide-header="true"
                            v-model="filterObj.selectedMaxFirstIn"
                            format="24hr"
                            @update:minute="maxFirstInMenu = false"
                          ></v-time-picker>
                        </v-menu>
                      </v-text-field>
                    </v-col>
                    <v-col
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                      v-if="openedSubTab == 'Early Checkout'"
                    >
                      <v-text-field
                        v-model="filterObj.selectedMinLastOut"
                        :active="minLastOutMenu"
                        :focused="minLastOutMenu"
                        label="Min Last Out"
                        variant="solo"
                        prepend-inner-icon="fas fa-clock"
                        readonly
                        single-line
                      >
                        <v-menu
                          v-model="minLastOutMenu"
                          :close-on-content-click="false"
                          activator="parent"
                          transition="scale-transition"
                        >
                          <v-time-picker
                            :hide-header="true"
                            v-model="filterObj.selectedMinLastOut"
                            format="24hr"
                            @update:minute="minLastOutMenu = false"
                          ></v-time-picker>
                        </v-menu>
                      </v-text-field>
                    </v-col>
                    <v-col
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                      v-if="openedSubTab == 'Early Checkout'"
                    >
                      <v-text-field
                        v-model="filterObj.selectedMaxLastOut"
                        :active="maxLastOutMenu"
                        :focused="maxLastOutMenu"
                        label="Max Last Out"
                        variant="solo"
                        prepend-inner-icon="fas fa-clock"
                        readonly
                        single-line
                      >
                        <v-menu
                          v-model="maxLastOutMenu"
                          :close-on-content-click="false"
                          activator="parent"
                          transition="scale-transition"
                        >
                          <v-time-picker
                            :hide-header="true"
                            v-model="filterObj.selectedMaxLastOut"
                            format="24hr"
                            @update:minute="maxLastOutMenu = false"
                          ></v-time-picker>
                        </v-menu>
                      </v-text-field>
                    </v-col>
                    <v-col
                      :cols="12"
                      class="py-2"
                      v-if="openedSubTab == 'Early Checkout'"
                    >
                      <div style="font-size: 1em; color: grey" class="ml-1">
                        Early Checkout Hours
                      </div>
                      <v-range-slider
                        v-model="filterObj.rangeHour"
                        :min="0"
                        :max="600"
                        :step="1"
                        color="primary"
                        class="align-center"
                      >
                        <template v-slot:prepend>
                          <v-card
                            style="width: 70px; height: 30px"
                            elevation="2"
                            class="d-flex justify-center align-center"
                          >
                            {{ convertMinToHrsMin(filterObj.rangeHour[0]) }}
                          </v-card>
                        </template>
                        <template v-slot:append>
                          <v-card
                            style="width: 70px; height: 30px"
                            elevation="2"
                            class="d-flex justify-center align-center"
                          >
                            {{ convertMinToHrsMin(filterObj.rangeHour[1]) }}
                          </v-card>
                        </template>
                      </v-range-slider>
                    </v-col>
                    <v-col
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                      v-if="openedSubTab != 'Early Checkout'"
                    >
                      <v-autocomplete
                        variant="solo"
                        v-model="filterObj.selectedEmpType"
                        color="primary"
                        :items="empTypeList"
                        label="Employee Type"
                        multiple
                        closable-chips
                        chips
                        density="compact"
                        single-line
                        :loading="loadingData"
                        item-title="Employee_Type"
                        item-value="EmpType_Id"
                      >
                      </v-autocomplete>
                    </v-col>
                    <v-col
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                      v-if="openedSubTab != 'Early Checkout'"
                    >
                      <v-autocomplete
                        variant="solo"
                        v-model="filterObj.selectedDepartment"
                        color="primary"
                        :items="departmentList"
                        label="Department"
                        multiple
                        closable-chips
                        chips
                        density="compact"
                        single-line
                        :loading="loadingData"
                        item-title="Department_Name"
                        item-value="Department_Name"
                      >
                      </v-autocomplete>
                    </v-col>
                    <v-col
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                      v-if="openedSubTab != 'Early Checkout'"
                    >
                      <v-autocomplete
                        variant="solo"
                        v-model="filterObj.selectedLocation"
                        color="primary"
                        :items="locationList"
                        label="Location"
                        multiple
                        closable-chips
                        chips
                        density="compact"
                        single-line
                        :loading="loadingData"
                        item-title="Location_Name"
                        item-value="Location_Name"
                      >
                      </v-autocomplete>
                    </v-col>
                    <v-col
                      v-if="fieldForce && (isAdmin || isServiceProviderAdmin)"
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                    >
                      <v-autocomplete
                        variant="solo"
                        v-model="filterObj.selectedServiceProvider"
                        color="primary"
                        :items="serviceProviderList"
                        :label="getCustomFieldName(115, 'Service Provider')"
                        multiple
                        closable-chips
                        chips
                        density="compact"
                        single-line
                        :loading="loadingData"
                        item-title="Service_Provider_Name"
                        item-value="Service_Provider_Id"
                      >
                      </v-autocomplete>
                    </v-col>
                    <v-col
                      v-if="openedSubTab == 'Attendance (Incomplete)'"
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                    >
                      <v-autocomplete
                        variant="solo"
                        v-model="filterObj.selectedStatus"
                        color="primary"
                        :items="['Applied', 'Draft']"
                        label="Status"
                        multiple
                        closable-chips
                        chips
                        density="compact"
                        single-line
                        :loading="loadingData"
                      >
                      </v-autocomplete>
                    </v-col>
                  </v-row>
                </template>
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="attendance-finalization">
      <v-window
        v-model="currentTabItem"
        v-if="formAccess?.view && allFormAccess"
      >
        <v-window-item :value="currentTabItem">
          <v-card
            v-if="bannerText && showBanner"
            :class="{
              'ma-4 pa-2 d-flex': true,
              'justify-space-between align-center': !isMobileView,
              'flex-column': isMobileView,
            }"
            style="
              background: linear-gradient(to left, #f7f793 0%, #fff9d1 100%);
            "
          >
            <NotesCard background-color="transparent">
              <template v-slot:notesCardContent>
                <span
                  :class="{ 'text-caption': openedSubTab == 'No Attendance' }"
                  >{{ bannerText }}</span
                >
              </template>
            </NotesCard>
            <v-icon color="primary" @click="showBanner = false"
              >fas fa-times</v-icon
            >
          </v-card>
          <ProfileCard>
            <FormTab :model-value="openedSubTab">
              <v-tab
                v-for="(tab, i) in subTabItems"
                :key="tab.value"
                :value="tab.value"
                :disabled="tab.disable"
                color="primary"
                @click="onChangeSubTabs(tab.value)"
              >
                <div
                  :class="[
                    isActiveSubTab(tab.value)
                      ? 'text-primary font-weight-bold'
                      : 'text-grey-darken-2 font-weight-bold',
                  ]"
                >
                  <div class="d-flex align-center">
                    {{ tab.label }}
                    <div
                      v-if="
                        openedSubTab == tab.value &&
                        tabCount &&
                        tabCount.length > 0 &&
                        tabCount[i] > 0
                      "
                      class="rounded-pill bg-primary pa-1 ml-2 d-flex align-center justify-center"
                    >
                      {{ tabCount[i] }}
                    </div>
                  </div>
                </div>
              </v-tab>
            </FormTab>
          </ProfileCard>
          <v-window v-model="openedSubTab">
            <v-window-item value="Attendance (Incomplete)">
              <AttendanceIncomplete
                v-if="openedSubTab == 'Attendance (Incomplete)'"
                :salaryStartDate="salaryStartDate"
                :salaryEndDate="salaryLastDate"
                :filterObj="filterObj"
                :filterAppliedCount="filterAppliedCount"
                :pre-req="preReq"
                :payslip-employee-ids="payslipEmployeeIds"
                @reset-filter="resetFilter()"
                @list-count="tabCount = [$event, 0, 0, 0]"
                @date-range="setDateRange($event)"
              ></AttendanceIncomplete>
            </v-window-item>
            <v-window-item value="No Attendance">
              <NoAttendance
                v-if="openedSubTab == 'No Attendance'"
                :salaryStartDate="salaryStartDate"
                :salaryEndDate="salaryLastDate"
                :filterObj="filterObj"
                :filterAppliedCount="filterAppliedCount"
                :pre-req="preReq"
                :payslip-employee-ids="payslipEmployeeIds"
                @reset-filter="resetFilter()"
                @list-count="tabCount = [0, $event, 0, 0]"
                @date-range="setDateRange($event)"
              ></NoAttendance>
            </v-window-item>
            <v-window-item value="Early Checkout">
              <early-check-out
                v-if="openedSubTab == 'Early Checkout'"
                :salaryStartDate="salaryStartDate"
                :salaryEndDate="salaryLastDate"
                :filterObj="filterObj"
                :filterAppliedCount="filterAppliedCount"
                :pre-req="preReq"
                :payslip-employee-ids="payslipEmployeeIds"
                @reset-filter="resetFilter()"
                @list-count="tabCount = [0, 0, $event, 0]"
                @date-range="setDateRange($event)"
              ></early-check-out>
            </v-window-item>
            <v-window-item value="Shortage">
              <AttendanceShortage
                v-if="openedSubTab == 'Shortage'"
                :salaryStartDate="salaryStartDate"
                :salaryEndDate="salaryLastDate"
                :filterObj="filterObj"
                :filterAppliedCount="filterAppliedCount"
                :pre-req="preReq"
                :payslip-employee-ids="payslipEmployeeIds"
                @reset-filter="resetFilter()"
                @list-count="tabCount = [0, 0, 0, $event]"
                @date-range="setDateRange($event)"
              ></AttendanceShortage>
            </v-window-item>
          </v-window>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";

import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import NotesCard from "@/components/helper-components/NotesCard.vue";
const AttendanceIncomplete = defineAsyncComponent(() =>
  import("./AttendanceIncomplete.vue")
);
const NoAttendance = defineAsyncComponent(() => import("./NoAttendance.vue"));
const AttendanceShortage = defineAsyncComponent(() =>
  import("./AttendanceShortage.vue")
);
const EarlyCheckOut = defineAsyncComponent(() => import("./EarlyCheckOut.vue"));
import { getCustomFieldName } from "@/helper";
export default {
  name: "AttendanceFinalization",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    AttendanceIncomplete,
    NoAttendance,
    AttendanceShortage,
    EarlyCheckOut,
  },
  data: () => {
    return {
      landedFormName: "Attendance Finalization",
      currentTabItem: "",
      isLoading: false,
      openedSubTab: "",
      showBanner: true,
      salaryStartDate: "",
      salaryLastDate: "",
      loadingData: false,
      employeeList: [], // employee list for early checkout
      departmentList: [],
      empTypeList: [],
      locationList: [],
      serviceProviderList: [],
      minFirstInMenu: false,
      maxFirstInMenu: false,
      minLastOutMenu: false,
      maxLastOutMenu: false,
      dropdownLoading: false,
      employeeListLoading: false,
      fieldForce: 0,
      filterObj: {
        selectedStatus: [],
        selectedServiceProvider: [],
        selectedLocation: [],
        selectedEmpType: [],
        selectedDepartment: [],
        selectedEmployees: [],
        selectedMinFirstIn: null,
        selectedMinLastOut: null,
        selectedMaxFirstIn: null,
        selectedMaxLastOut: null,
        rangeHour: [],
      },
      filterAppliedCount: 0,
      tabCount: [0, 0, 0, 0],
      preReq: 0,
      payslipEmployeeIds: [],
    };
  },
  computed: {
    attendanceFormAccess() {
      return this.$store.getters.attendanceFormAccess;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    convertMinToHrsMin() {
      return (totalMinutes) => {
        if (totalMinutes >= 0) {
          let hours = Math.floor(totalMinutes / 60);
          let minutes = totalMinutes % 60;
          return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
            2,
            "0"
          )}`;
        } else {
          return "  ";
        }
      };
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.attendanceFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessRights(
        "service-provider-admin"
      );
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isManager() {
      return this.$store.state.isManager;
    },
    formAccess() {
      let formAccessRights = this.accessRights(357);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"] &&
        (formAccessRights.accessRights["admin"] === "admin" || this.isManager)
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    subTabItems() {
      let initialTabs = [];
      if (this.accessRights(367)?.accessRights?.view) {
        initialTabs.push({
          label: "Attendance (Incomplete)",
          value: "Attendance (Incomplete)",
          disable: false,
        });
      }
      if (this.accessRights(368)?.accessRights?.view) {
        initialTabs.push({
          label: "No Attendance",
          value: "No Attendance",
          disable: false,
        });
      }
      if (this.accessRights(324)?.accessRights?.view) {
        initialTabs.push({
          label: "Early Checkout",
          value: "Early Checkout",
          disable: false,
        });
      }
      if (this.accessRights(369)?.accessRights?.view) {
        initialTabs.push({
          label: "Shortage",
          value: "Shortage",
          disable: false,
        });
      }
      return initialTabs;
    },
    isActiveSubTab() {
      return (val) => {
        return this.openedSubTab === val;
      };
    },
    AttendanceIncompleteAccess() {
      let formAccessRights = this.accessRights(367);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    NoAttendanceAccess() {
      let formAccessRights = this.accessRights(368);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    AttendanceShortageAccess() {
      let formAccessRights = this.accessRights(369);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    EarlyCheckoutAccess() {
      let formAccessRights = this.accessRights(324);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    allFormAccess() {
      return (
        this.AttendanceIncompleteAccess?.view ||
        this.NoAttendanceAccess?.view ||
        this.AttendanceShortageAccess?.view ||
        this.EarlyCheckoutAccess?.view
      );
    },
    bannerText() {
      if (this.openedSubTab === "Attendance (Incomplete)") {
        return "The attendance auto closure process helps HRs to close all outstanding draft attendance records that are within the work schedule configuration and approve all outstanding attendance records in bulk without any user intervention.";
      } else if (this.openedSubTab === "No Attendance") {
        return "The 'LOP Initiation' process assists HRs in streamlining the employee's attendance data by allowing them to initiate the automatic creation of Loss of Pay(Unpaid leave) records. This process applies to employees, who do not have attendance or full leave record. It is a mandatory process for employees with attendance enforcement configuration, otherwise, it's an optional process. This may be your last opportunity to add leaves or attendance before initiating the LOP creation process. Give it a go!";
      } else {
        return "";
      }
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    decodeBase64Url() {
      return (data) => {
        if (data) {
          return atob(data);
        } else {
          return "";
        }
      };
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    let url_string = window.location.href;
    let url = new URL(url_string);
    this.preReq = parseInt(url.searchParams.get("pre-req"));
    if (this.preReq) {
      let tab = url.searchParams.get("tab");
      if (tab?.toLowerCase() === "earlycheckout") {
        this.openedSubTab = "Early Checkout";
      } else if (tab?.toLowerCase() === "shortage") {
        this.openedSubTab = "Shortage";
      } else if (tab?.toLowerCase() === "noattendance") {
        this.openedSubTab = "No Attendance";
      } else {
        this.openedSubTab = "Attendance (Incomplete)";
      }
      const decodedString = this.decodeBase64Url(url.searchParams.get("data"));
      if (decodedString) {
        const params = new URLSearchParams(decodedString);
        this.salaryStartDate = params.get("salaryStartDate");
        this.salaryLastDate = params.get("salaryEndDate");
        this.payslipEmployeeIds = params.get("payslipEmployeeIds")?.split(",");
      }
    }
    if (this.allFormAccess) {
      this.setDefaultSubTab();
      if (this.subTabItems.length) {
        this.fetchDropdownData();
        this.getEmpList();
        this.hideBannerText();
      }
    }
  },
  methods: {
    getCustomFieldName,
    setDefaultSubTab() {
      // Set the first available tab based on form access
      if (this.accessRights(367)?.accessRights?.view) {
        this.openedSubTab = "Attendance (Incomplete)";
      } else if (this.accessRights(368)?.accessRights?.view) {
        this.openedSubTab = "No Attendance";
      } else if (this.accessRights(324)?.accessRights?.view) {
        this.openedSubTab = "Early Checkout";
      } else if (this.accessRights(369)?.accessRights?.view) {
        this.openedSubTab = "Shortage";
      }
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.attendanceFormAccess;
        let clickedForm = formAccess[tab];
        this.$router.push("/my-team/" + clickedForm.url);
      }
    },
    hideBannerText() {
      setTimeout(() => {
        this.showBanner = false;
      }, 60000);
    },
    onChangeSubTabs(tab) {
      this.showBanner = true;
      this.openedSubTab = tab;
      this.hideBannerText();
    },
    fetchDropdownData() {
      this.dropdownLoading = true;
      if (!this.isDropdownDataRetrieved) {
        this.$store
          .dispatch("getDefaultDropdownList", { formId: 357 })
          .then((res) => {
            if (
              res.data &&
              res.data.getDropDownBoxDetails &&
              !res.data.getDropDownBoxDetails.errorCode
            ) {
              const {
                departments,
                locations,
                employeeType,
                fieldForce,
                serviceProvider,
              } = res.data.getDropDownBoxDetails;
              this.departmentList = departments;
              this.locationList = locations;
              this.empTypeList = employeeType;
              this.serviceProviderList = serviceProvider;
              this.fieldForce = fieldForce;
              this.loadingData = false;
            } else {
              this.handleDropdownDataError();
            }
            this.dropdownLoading = false;
          })
          .catch(() => {
            this.dropdownLoading = false;
            this.handleDropdownDataError();
          });
      }
    },
    async getEmpList() {
      let vm = this;
      vm.employeeListLoading = true;

      await vm.$store
        .dispatch("getEmployeesList", {
          formName: "Attendance",
          formId: 357,
        })
        .then((empData) => {
          if (empData && empData.length) {
            let empList = empData.map((item) => ({
              ...item,
              employeeData: item.employeeName + " - " + item.userDefinedEmpId,
            }));
            vm.employeeList = [...empList];
          } else {
            vm.employeeList = [];
          }
          vm.employeeListLoading = false;
        })
        .catch((err) => {
          vm.employeeListLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "list",
            form: "employees",
            isListError: false,
          });
        });
    },
    resetFilter() {
      this.filterObj = {
        selectedStatus: [],
        selectedServiceProvider: [],
        selectedLocation: [],
        selectedEmpType: [],
        selectedDepartment: [],
        selectedEmployees: [],
        selectedMinFirstIn: null,
        selectedMinLastOut: null,
        selectedMaxFirstIn: null,
        selectedMaxLastOut: null,
        rangeHour: [],
      };
      this.filterAppliedCount = 0;
    },
    setDateRange(dateRange) {
      this.salaryStartDate = dateRange.startDate;
      this.salaryLastDate = dateRange.endDate;
    },
  },
};
</script>
<style scoped>
.attendance-finalization {
  padding: 3.7em 0em 0em 0em;
}
@media screen and (max-width: 805px) {
  .attendance-finalization {
    padding: 5em 1em 0em 1em;
  }
}
</style>
