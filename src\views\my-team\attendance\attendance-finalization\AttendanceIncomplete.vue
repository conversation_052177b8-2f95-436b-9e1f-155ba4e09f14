<template>
  <div class="mt-3 mx-5" style="height: calc(100vh - 180px)">
    <div v-if="listLoading" class="mt-3">
      <!-- Skeleton loaders -->
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList && !listLoading"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      @button-click="refetchList()"
    >
      <template #contentSlot>
        <div class="d-flex mb-2 flex-wrap justify-center align-center">
          <v-btn
            class="bg-white my-2 ml-2"
            :style="'width: max-content'"
            :size="isMobileView ? 'small' : 'default'"
            rounded="lg"
            @click="$refs.datePicker.fp.open()"
          >
            <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
            <span class="text-caption px-1">Date:</span>
            <flat-pickr
              ref="datePicker"
              v-model="appliedDateRange"
              :config="flatPickerOptions"
              placeholder="Select Date Range"
              class="ml-2 mt-1 date-range-picker-custom-bg"
              style="outline: 0px; color: var(--v-primary-base); width: 170px"
              @onChange="onChangeDateRange"
            ></flat-pickr>
          </v-btn>
          <v-btn
            color="transparent"
            class="mt-1"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            @click="refetchList()"
          >
            <v-icon>fas fa-redo-alt</v-icon>
          </v-btn>
        </div>
      </template>
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="itemList.length === 0"
      key="no-results-screen"
      :main-title="'There are no records for the selected filters/searches.'"
      :image-name="
        originalList?.length === 0
          ? 'workflow/empty-approval'
          : 'common/no-records'
      "
      :isSmallImage="originalList.length === 0"
    >
      <template #contentSlot>
        <div class="d-flex mb-2 flex-wrap justify-center align-center">
          <v-btn
            class="bg-white my-2 ml-2"
            :style="'width: max-content'"
            :size="isMobileView ? 'small' : 'default'"
            rounded="lg"
            @click="$refs.datePicker.fp.open()"
          >
            <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
            <span class="text-caption px-1">Date:</span>
            <flat-pickr
              ref="datePicker"
              v-model="appliedDateRange"
              :config="flatPickerOptions"
              placeholder="Select Date Range"
              class="ml-2 mt-1 date-range-picker-custom-bg"
              style="outline: 0px; color: var(--v-primary-base); width: 170px"
              @onChange="onChangeDateRange"
            ></flat-pickr>
          </v-btn>
          <v-btn
            v-if="originalList.length === 0"
            color="transparent"
            class="mt-1"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            @click="refetchList()"
          >
            <v-icon>fas fa-redo-alt</v-icon>
          </v-btn>
          <v-btn
            v-if="originalList.length > 0"
            color="primary"
            variant="elevated"
            class="ml-4 mt-1"
            rounded="lg"
            :size="isMobileView ? 'small' : 'default'"
            @click="$emit('reset-filter')"
          >
            Reset Filter/Search
          </v-btn>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <div
        v-if="originalList.length"
        :class="{
          'mb-3': !isMobileView,
          'd-flex': true,
          'justify-space-between': !isMobileView,
          'align-center': true,
          'flex-column': isMobileView,
        }"
      >
        <v-btn
          class="bg-white mr-2"
          :class="isMobileView ? 'mb-2' : ''"
          :style="'width: max-content'"
          :size="isMobileView ? 'small' : 'default'"
          rounded="lg"
          @click="$refs.datePicker.fp.open()"
        >
          <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
          <span class="text-caption px-1">Date:</span>
          <flat-pickr
            ref="datePicker"
            v-model="appliedDateRange"
            :config="flatPickerOptions"
            placeholder="Select Date Range"
            class="ml-2 mt-1 date-range-picker-custom-bg"
            style="outline: 0px; color: var(--v-primary-base); width: 170px"
            @onChange="onChangeDateRange"
          ></flat-pickr>
        </v-btn>
        <div :class="isMobileView ? 'd-flex flex-column' : 'd-flex'">
          <v-btn
            v-if="formAccess?.add && isAdmin"
            color="primary"
            :class="isMobileView ? 'mb-2' : ''"
            class="mr-2"
            rounded="lg"
            @click="handleInitiateAction()"
          >
            {{ initiateBtnText }}
          </v-btn>
          <div class="d-flex align-center justify-center">
            <v-btn
              color="transparent"
              class="mt-1"
              variant="flat"
              :size="isMobileView ? 'small' : 'default'"
              @click="refetchList()"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu v-model="openMoreMenu" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
                  <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                  <v-icon v-else>fas fa-caret-up</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'bg-hover': isHovering,
                        }"
                      >
                        {{ action.key }}
                      </v-list-item-title>
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
      </div>
      <v-data-table
        v-model="selectedRecords"
        :headers="tableHeaders"
        :items="itemList"
        item-value="attendance_id"
        fixed-header
        :show-select="!isMobileView"
        :height="$store.getters.getTableHeightBasedOnScreenSize(290, itemList)"
        :items-per-page="50"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          {
            value: -1,
            title: '$vuetify.dataFooter.itemsPerPageAll',
          },
        ]"
      >
        <template v-slot:[`header.data-table-select`]="{ selectAll }">
          <div class="d-flex justify-center align-center">
            <v-checkbox-btn
              v-model="selectAllBox"
              color="primary"
              false-icon="far fa-circle"
              true-icon="fas fa-check-circle"
              :indeterminate="
                selectedItems.length !== 0 &&
                selectedItems.length !== itemList.length
              "
              indeterminate-icon="fas fa-minus-circle"
              @change="selectAll(selectAllBox)"
            ></v-checkbox-btn>
          </div>
        </template>
        <template v-slot:item="{ item }">
          <tr
            class="data-table-tr bg-white"
            :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
          >
            <td :class="isMobileView ? 'mt-3 mb-n5' : ''">
              <v-checkbox-btn
                v-model="item.isSelected"
                color="primary"
                false-icon="far fa-circle"
                true-icon="fas fa-check-circle"
                class="mt-n2 ml-n2"
                @click.stop="
                  {
                  }
                "
                @change="checkAllSelected()"
              ></v-checkbox-btn>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Employee</div>
              <section>
                <div style="max-width: 200px" class="text-truncate">
                  <span class="text-primary text-body-2 font-weight-medium">
                    <v-tooltip :text="item.employee_name" location="bottom">
                      <template v-slot:activator="{ props }">
                        <span
                          v-bind="
                            item.employee_name && item.employee_name.length > 20
                              ? props
                              : ''
                          "
                          >{{ checkNullValue(item.employee_name) }}</span
                        >
                      </template>
                    </v-tooltip>
                    <v-tooltip
                      :text="item.user_defined_empid?.toString()"
                      location="bottom"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          v-if="item.user_defined_empid"
                          v-bind="
                            item.user_defined_empid &&
                            item.user_defined_empid.length > 20
                              ? props
                              : ''
                          "
                          class="text-grey"
                        >
                          {{ checkNullValue(item.user_defined_empid) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </span>
                </div>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Date In</div>
              <section>
                {{ checkNullValue(item.punchin_date) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Time In</div>
              <section>
                {{ formatTime(item.punchin_time) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Date Out</div>
              <section>
                {{ checkNullValue(item.punchout_date) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Time Out</div>
              <section>
                {{ formatTime(item.punchout_time) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Total Hours
              </div>
              <section>
                {{ checkNullValue(item.total_hours) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Status</div>
              <section :class="getCustomClass(item.approval_status)">
                {{ checkNullValue(item.approval_status) }}
              </section>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
  </div>
  <v-dialog
    v-model="openWarningModal"
    :max-width="invalidReasonArray.length != 0 ? '900px' : '600px'"
    @click:outside="openWarningModal = false"
  >
    <v-card class="rounded-lg">
      <div class="d-flex justify-end">
        <v-icon
          color="primary"
          class="pr-4 pt-4 font-weight-bold"
          @click="openWarningModal = false"
          >fas fa-times</v-icon
        >
      </div>
      <v-container>
        <v-row class="pa-4">
          <v-col
            :lg="invalidReasonArray.length != 0 ? 6 : 12"
            :md="invalidReasonArray.length != 0 ? 6 : 12"
            sm="12"
            cols="12"
          >
            <v-card-title v-if="imageName || iconName">
              <v-row v-if="!imageName" class="justify-center">
                <v-icon :color="iconColor" size="60" class="font-weight-bold">{{
                  iconName
                }}</v-icon>
              </v-row>
              <v-row v-else class="justify-center">
                <img
                  width="50px"
                  height="auto"
                  :src="basePath + 'vue/assets/images/' + imageName + '.webp'"
                  @error="imageFormatSwap"
                  alt="delete"
                />
              </v-row>
            </v-card-title>
            <v-card-text class="justify-center mt-2">
              <v-row class="justify-center text-h6">
                <span class="text-primary pr-2">{{ validRecords }}</span
                ><span>out of </span>
                <span class="text-primary pl-2 pr-2">{{ totalRecords }}</span>
                <span class="text-align-center"
                  >record(s) can be
                  {{
                    initiateBtnText === "Approve"
                      ? "approved"
                      : initiateBtnText === "Initiate Auto Closure"
                      ? "initiated for auto closure"
                      : "auto closed and approved"
                  }}</span
                >
              </v-row>
              <v-row class="justify-center pt-5 px-3">
                <span style="text-align: center">
                  *By clicking the '
                  {{ initiateBtnText }}
                  ' button, you can
                  <span class="font-weight-bold">
                    {{ initiateBtnText }}
                  </span>
                  for eligible record(s).
                </span>
              </v-row>
            </v-card-text>
            <v-card-actions class="d-flex justify-center">
              <v-btn
                rounded="lg"
                variant="text"
                elevation="4"
                color="primary"
                @click="onCancelValidationModel()"
                class="cancel-button"
              >
                Cancel
              </v-btn>
              <v-btn
                rounded="lg"
                color="primary"
                variant="elevated"
                class="ma-2"
                @click="initiateAutoClosure()"
                :disabled="validRecords === 0 ? true : false"
              >
                {{ initiateBtnText }}
              </v-btn>
            </v-card-actions>
          </v-col>
          <v-col
            md="6"
            cols="12"
            v-if="invalidReasonArray.length != 0"
            class="bg-hover rounded-lg"
          >
            <v-row
              class="mt-2 pa-3 font-weight-medium text-primary"
              style="word-break: break-word"
            >
              The attendance record(s) cannot be processed because of one or
              more of the following reason(s).
            </v-row>
            <v-row
              class="pa-3 pb-1"
              style="flex-wrap: inherit"
              v-for="(msg, index) in invalidReasonArray"
              :key="index"
            >
              <v-avatar color="primary" size="20">
                {{ index + 1 }}
              </v-avatar>
              <span class="ml-3 text-grey-darken-2">{{ msg }}</span>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
    </v-card>
  </v-dialog>
  <AppWarningModal
    v-if="initiateModel"
    :open-modal="initiateModel"
    :confirmation-heading="''"
    :icon-name="''"
    :icon-color="'primary'"
    closeButtonText="No"
    acceptButtonText="Yes"
    icon-Size="75"
    @close-warning-modal="initiateModel = false"
    @accept-modal="validateClosure()"
  >
    <template v-slot:warningModalContent>
      <div
        class="text-h6 justify-center font-weight-bold text-primary text-center px-6 pt-2"
      >
        {{ initiateModalHeading }}
      </div>
      <div class="text-center mt-3 text-grey">
        {{ initiateModalText }}
      </div>
    </template>
  </AppWarningModal>
  <AppWarningModal
    v-if="openConfirmationModel"
    :open-modal="openConfirmationModel"
    :confirmation-heading="confirmationModalObj.message"
    :icon-name="confirmationModalObj.icon"
    :icon-color="confirmationModalObj.color"
    :icon-Size="75"
    @close-warning-modal="openConfirmationModel = false"
    @accept-modal="confirmationModalObj.method()"
  />
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import moment from "moment";
import { checkNullValue } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "AttendanceIncomplete",
  components: { flatPickr },
  mixins: [FileExportMixin],
  emits: ["reset-filter", "list-count", "date-range"],
  props: {
    salaryStartDate: { type: String, required: true },
    salaryEndDate: { type: String, required: true },
    filterObj: { type: Object, default: () => ({}) },
    filterAppliedCount: { type: Number, default: 0 },
    preReq: { type: Number, default: 0 },
    payslipEmployeeIds: { type: Array, default: () => [] },
  },
  data() {
    return {
      listLoading: false,
      originalList: [],
      itemList: [],
      isErrorInList: false,
      errorContent: "Something went wrong. Please try after some time.",
      appliedDateRange: null,
      startDate: "",
      endDate: "",
      openMoreMenu: false,
      selectAllBox: false,
      selectedItems: [],
      selectedRecords: [],
      isLoading: false,
      openConfirmationModel: false,
      confirmationModalObj: {
        message: "",
        icon: "",
        method: "",
      },
      openWarningModal: false,
      invalidReasonArray: [],
      validRecords: 0,
      totalRecords: 0,
      validAttendanceId: [],
      initiateModel: false,
    };
  },
  computed: {
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    formatTime() {
      return (time) => {
        if (time) {
          let timeString = moment(time, "HH:mm:ss").format("HH:mm");
          return timeString;
        } else {
          return "-";
        }
      };
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment()
          .subtract(1, "days")
          .format(this.$store.state.orgDetails.orgDateFormat),
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    getCustomClass() {
      return (status) => {
        if (status?.toLowerCase() === "applied") {
          return "text-blue";
        } else if (status?.toLowerCase() === "draft") {
          return "text-purple";
        } else {
          return "";
        }
      };
    },
    tableHeaders() {
      return [
        {
          title: "Employee",
          align: "start",
          key: "employee_name",
        },
        {
          title: "Date In",
          key: "punchin_date",
        },
        {
          title: "Time In",
          key: "punchin_time",
        },
        {
          title: "Date Out",
          key: "punchout_date",
        },
        {
          title: "Time Out",
          key: "punchout_time",
        },
        {
          title: "Total Hours",
          key: "total_hours",
        },
        {
          title: "Status",
          key: "approval_status",
        },
      ];
    },
    initiateBtnText() {
      if (this.selectedItems.length) {
        let allApplied = this.selectedItems.every(
          (el) => el.approval_status?.toLowerCase() === "applied"
        );
        let allDraft = this.selectedItems.every(
          (el) => el.approval_status?.toLowerCase() === "draft"
        );
        if (allApplied) {
          return "Approve";
        } else if (allDraft) {
          return "Initiate Auto Closure";
        }
        return "Initiate Auto Closure & Approval";
      }
      return "Initiate Auto Closure & Approval";
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    formAccess() {
      let formAccessRights = this.accessRights(367);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    initiateModalHeading() {
      let msg = "";
      if (this.initiateBtnText === "Initiate Auto Closure & Approval") {
        msg =
          "Are you sure to Initiate Auto Closure & Approval for the selected record(s)?";
      } else if (this.initiateBtnText === "Initiate Auto Closure") {
        msg =
          "Are you sure to Initiate Auto Closure for the selected record(s)?";
      } else {
        msg = "Are you sure to Approve the selected record(s)?";
      }
      return msg;
    },
    initiateModalText() {
      let msg = "";
      if (this.initiateBtnText === "Initiate Auto Closure & Approval") {
        msg =
          "By initiating Auto Closure and Approve, all pending attendance records assigned to the reporting manager will be approved, and the check-out time will be automatically updated based on the associated work schedule.";
      } else if (this.initiateBtnText === "Initiate Auto Closure") {
        msg =
          "By initiating auto closure, the check-out time for the selected records will be automatically updated based on the associated work schedule.";
      } else {
        msg =
          "By initiating approval, all pending attendance records assigned to the reporting manager will be approved.";
      }
      return msg;
    },
    moreActions() {
      let actions = [
        {
          key: "Delete",
          icon: "fas fa-trash-alt",
        },
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
  },
  watch: {
    selectedRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemLogList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.attendance_id)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
          let index = this.selectedItems.findIndex(
            (x) => x.attendance_id === item.attendance_id
          );
          if (index === -1) {
            this.selectedItems.push(item);
          }
        }
      } else {
        // Iterate through itemLogList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
        this.selectedItems = [];
      }
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
    filterAppliedCount(val) {
      if (val) {
        this.applyFilter();
      } else {
        this.itemList = this.originalList;
      }
    },
    itemList: {
      handler(newList) {
        this.$emit("list-count", newList.length);
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.setDateRange(this.salaryStartDate, this.salaryEndDate);
    this.fetchNoAttendanceList();
  },
  // Reset the itemList to the original list when the count is zero
  methods: {
    checkNullValue,
    setDateRange(startDate, endDate) {
      if (!startDate || !endDate) return;
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      this.appliedDateRange =
        moment(startDate).format(orgDateFormat) +
        " to " +
        moment(endDate).format(orgDateFormat);
      this.startDate = startDate;
      this.endDate = endDate;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });

        this.itemList = searchItems;
      }
    },
    applyFilter() {
      if (this.filterAppliedCount) {
        let filteredArray = this.originalList;
        if (this.filterObj.selectedStatus.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedStatus.includes(item.approval_status);
          });
        }
        if (this.filterObj.selectedDepartment.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedDepartment.includes(
              item.department_name
            );
          });
        }
        if (this.filterObj.selectedLocation.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedLocation.includes(item.location_name);
          });
        }
        if (this.filterObj.selectedServiceProvider.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedServiceProvider.includes(
              item.approval_status
            );
          });
        }
        if (this.filterObj.selectedEmpType.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedEmpType.includes(item.location_name);
          });
        }
        this.itemList = filteredArray;
      }
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.selectedItems = [];
      this.selectAllBox = false;
      this.fetchNoAttendanceList();
    },
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectedItems = selectedItems;
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    async fetchNoAttendanceList() {
      let vm = this;
      vm.isErrorInList = false;
      vm.errorContent = "";
      vm.listLoading = true;
      try {
        const apiObj = {
          url:
            vm.baseUrl +
            "employees/attendance-finalization/list-attendance-finalization",
          type: "POST",
          dataType: "json",
          data: {
            actualSubTab: "attTab",
            department: [],
            employeeId: this.preReq ? this.payslipEmployeeIds : [],
            employeeType: [],
            endDate: this.endDate,
            finalizationMethod: "attendance",
            location: [],
            startDate: this.startDate,
            status: ["Applied", "Draft"],
          },
        };
        const response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          this.startDate = response.filterMinDate;
          this.endDate = response.filterMaxDate;
          this.setDateRange(response.filterMinDate, response.filterMaxDate);
          this.$emit("date-range", {
            startDate: response.filterMinDate,
            endDate: response.filterMaxDate,
          });
          if (response.attendanceData?.aaData?.length) {
            vm.originalList = response.attendanceData.aaData;
            vm.itemList = response.attendanceData.aaData;
          } else {
            vm.originalList = [];
            vm.itemList = [];
          }
        } else {
          vm.originalList = [];
          vm.itemList = [];
          vm.handleFetchAttendanceError();
        }
      } catch (error) {
        vm.originalList = [];
        vm.itemList = [];
        vm.handleFetchAttendanceError(error);
      } finally {
        vm.listLoading = false;
      }
    },
    handleFetchAttendanceError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Attendance (Incomplete)",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    onChangeDateRange(selectedDates) {
      if (selectedDates.length > 1) {
        // Parse the dates from the given format
        let parsedStartDate = moment(selectedDates[0], "DD/MM/YYYY");
        let parsedEndDate = moment(
          selectedDates.length > 1 ? selectedDates[1] : selectedDates[0],
          "DD/MM/YYYY"
        );

        // Format the dates into "YYYY-MM-DD" format
        this.startDate = parsedStartDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.fetchNoAttendanceList();
      }
    },
    onCancelValidationModel() {
      this.validRecords = 0;
      this.totalRecords = 0;
      this.invalidReasonArray = [];
      this.validAttendanceId = [];
      this.openWarningModal = false;
      this.openConfirmationModel = false;
    },
    handleInitiateAction() {
      if (this.selectedItems.length && this.formAccess?.add) {
        this.initiateModel = true;
      } else if (!this.formAccess || !this.formAccess?.add) {
        let snackbarData = {
          isOpen: true,
          message: "You don't have access to perform this action.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      } else if (!this.selectedItems.length) {
        let snackbarData = {
          isOpen: true,
          message: "Please select at least one record.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    async validateClosure() {
      let vm = this;
      vm.isLoading = true;
      vm.openConfirmationModel = false;
      try {
        let attendanceIds = this.selectedItems.map(
          (item) => item.attendance_id
        );
        const apiObj = {
          url:
            vm.baseUrl +
            "employees/attendance-finalization/validate-attendance-finalization/",
          type: "POST",
          dataType: "json",
          data: {
            attendanceId: attendanceIds,
          },
        };
        const response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          let text =
            this.initiateBtnText === "Approve"
              ? "approved"
              : this.initiateBtnText === "Initiate Auto Closure"
              ? "initiated for auto closure"
              : "auto closed and approved";
          this.validAttendanceId = response.validatedResult.validAttendanceIds;
          this.popupMessage1 = "record(s), can be " + text;
          this.popupMessage2 = this.approveAutoClosureBtnText;
          if (attendanceIds.length === this.validAttendanceId.length) {
            this.initiateAutoClosure(this.validAttendanceId);
          } else {
            this.validRecords = this.validAttendanceId.length;
            this.totalRecords = attendanceIds.length;
            this.invalidReasonArray = response.validatedResult.invalidReason;
            this.openWarningModal = true;
            this.isLoading = false;
          }
        } else {
          this.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              response?.msg ||
              "Something went wrong. Please try after some time.",
            type: "warning",
          };
          vm.showAlert(snackbarData);
        }
      } catch (error) {
        this.isLoading = false;
        this.handleInitiateActionError(error);
      }
    },
    async initiateAutoClosure() {
      this.isLoading = true;
      this.openWarningModal = false;
      this.initiateModel = false;
      try {
        const apiObj = {
          url:
            this.baseUrl +
            "employees/attendance-finalization/initiate-auto-closure-and-approval//",
          type: "POST",
          dataType: "json",
          data: {
            attendanceId: this.validAttendanceId,
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          var successText =
            this.approveAutoClosureBtnText === "Approve"
              ? "approved"
              : this.approveAutoClosureBtnText === "Initiate auto closure"
              ? "auto closed"
              : "approved and auto closed";
          let snackbarData = {
            isOpen: true,
            message:
              "Attendance record(s) are " + successText + " successfully",
            type: "success",
          };
          this.showAlert(snackbarData);
          this.refetchList();
        } else {
          let snackbarData = {
            isOpen: true,
            message:
              response?.msg ||
              "Something went wrong. Please try after some time.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } catch (error) {
        this.handleInitiateActionError(error);
      } finally {
        this.isLoading = false;
      }
    },
    handleInitiateActionError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "initiating",
        form: "Attendance (Incomplete)",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onMoreAction(actionType) {
      if (actionType?.toLowerCase() === "export") {
        this.exportReportFile();
      } else if (actionType?.toLowerCase() === "delete") {
        if (this.formAccess?.delete) {
          if (this.selectedItems.length) {
            this.confirmationModalObj = {
              message:
                "Are you sure you want to delete the selected record(s)?",
              icon: "fas fa-trash",
              method: this.deleteAttendanceRecords,
              color: "red",
            };
            this.openConfirmationModel = true;
          } else {
            let snackbarData = {
              isOpen: true,
              message: "Please select at least one record to delete.",
              type: "warning",
            };
            this.showAlert(snackbarData);
          }
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Employee Id", key: "user_defined_empid" },
        { header: "Employee", key: "employee_name" },
        { header: "Date In", key: "punchin_date" },
        { header: "Time In", key: "punchin_time" },
        { header: "Date Out", key: "punchout_date" },
        { header: "Time Out", key: "punchout_time" },
        { header: "Total Hours", key: "total_hours" },
        { header: "Status", key: "approval_status" },
      ];

      const exportOptions = {
        fileExportData: this.itemList,
        fileName: "Early Checkout Report",
        sheetName: "Early checkout Details",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
    onClickDelete() {
      if (this.formAccess?.delete) {
        this.confirmationModalObj = {
          message: "Are you sure you want to delete the selected record(s)?",
          icon: "fas fa-trash",
          method: this.deleteAttendanceRecords,
          color: "red",
        };
        this.openConfirmationModel = true;
      } else {
        let snackbarData = {
          isOpen: true,
          message: "You don't have access to perform this action.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    async deleteAttendanceRecords() {
      this.isLoading = true;
      this.openConfirmationModel = false;
      try {
        let empIds = this.selectedItems.map((item) => item.attendance_id);
        let apiObj = {
          url:
            this.baseUrl +
            "employees/attendance-finalization/delete-attendance-finalization/",
          type: "POST",
          dataType: "json",
          data: {
            attendanceId: empIds,
            attendanceImportExist: 0,
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          let snackbarData = {
            isOpen: true,
            message: "Record deleted successfully.",
            type: "success",
          };
          this.showAlert(snackbarData);
          this.refetchList();
        } else {
          let snackbarData = {
            isOpen: true,
            message: response?.msg || "Error in deleting record(s).",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } catch (error) {
        let snackbarData = {
          isOpen: true,
          message: response?.msg || "Error in deleting record(s).",
          type: "warning",
        };
        this.showAlert(snackbarData);
      } finally {
        this.isLoading = false;
      }
    },
  },
};
</script>
