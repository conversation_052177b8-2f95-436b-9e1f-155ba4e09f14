<template>
  <div class="mt-3 mx-5" style="height: calc(100vh - 180px)">
    <div v-if="listLoading" class="mt-3">
      <!-- Skeleton loaders -->
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList && !listLoading"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      @button-click="refetchList()"
    >
      <template #contentSlot>
        <div class="d-flex mb-2 flex-wrap justify-center align-center">
          <v-btn
            class="bg-white mr-2"
            :style="'width: max-content'"
            :size="isMobileView ? 'small' : 'default'"
            rounded="lg"
            @click="$refs.datePicker.fp.open()"
          >
            <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
            <span class="text-caption px-1">Date:</span>
            <flat-pickr
              ref="datePicker"
              v-model="appliedDateRange"
              :config="flatPickerOptions"
              placeholder="Select Date Range"
              class="ml-2 mt-1 date-range-picker-custom-bg"
              style="outline: 0px; color: var(--v-primary-base); width: 170px"
              @onChange="onChangeDateRange"
            ></flat-pickr>
          </v-btn>
          <v-btn
            color="transparent"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            @click="refetchList()"
          >
            <v-icon>fas fa-redo-alt</v-icon>
          </v-btn>
        </div>
      </template>
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="itemList.length === 0"
      key="no-results-screen"
      :main-title="enforcementMessage"
      :image-name="
        originalList?.length === 0
          ? 'workflow/empty-approval'
          : 'common/no-records'
      "
      :isSmallImage="originalList.length === 0"
    >
      <template #contentSlot>
        <div
          v-if="isAttendanceEnforcedPayment?.toLowerCase() === 'no'"
          class="text-h5 text-center font-weight-bold"
          width="60%"
        >
          The feature to enforce payment based on attendance is currently
          disabled. If you would like to activate this feature, you have the
          option to configure it at either the
          <a
            :href="baseUrl + 'organization/organization-settings'"
            target="_blank"
            class="text-primary"
            >organization</a
          >,
          <a
            :href="baseUrl + 'v3/my-team/team-summary'"
            target="_blank"
            class="text-primary"
            >employee</a
          >, or
          <a
            :href="baseUrl + 'in/core-hr/designations'"
            target="_blank"
            class="text-primary"
            >designation</a
          >
          level.
        </div>
        <div v-else class="d-flex mb-2 flex-wrap justify-center align-center">
          <v-btn
            class="bg-white mr-2"
            :style="'width: max-content'"
            :size="isMobileView ? 'small' : 'default'"
            rounded="lg"
            @click="$refs.datePicker.fp.open()"
          >
            <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
            <span class="text-caption px-1">Date:</span>
            <flat-pickr
              ref="datePicker"
              v-model="appliedDateRange"
              :config="flatPickerOptions"
              placeholder="Select Date Range"
              class="ml-2 mt-1 date-range-picker-custom-bg"
              style="outline: 0px; color: var(--v-primary-base); width: 170px"
              @onChange="onChangeDateRange"
            ></flat-pickr>
          </v-btn>
          <v-btn
            v-if="originalList.length === 0"
            color="transparent"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            @click="refetchList()"
          >
            <v-icon>fas fa-redo-alt</v-icon>
          </v-btn>
          <v-btn
            v-if="originalList.length > 0"
            color="primary"
            variant="elevated"
            rounded="lg"
            :size="isMobileView ? 'small' : 'default'"
            @click="$emit('reset-filter')"
          >
            Reset Filter/Search
          </v-btn>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <v-card
        v-if="originalList.length"
        :class="{
          'ma-4 pa-2 d-flex': true,
          'align-center': !isMobileView,
        }"
        style="background: linear-gradient(to left, #f7f793 0%, #fff9d1 100%)"
      >
        <img
          :src="getFooterImage"
          style="width: 30px; height: 30px"
          class="ml-1 mr-5"
          alt="idea-bulb"
        />
        <span>
          Please note that it is only applicable to standard business days and
          excludes weekends and holidays. If you wish to request Unpaid Leave
          for a weekend or holiday, you must utilize the
          <a :href="baseUrl + 'v3/my-team/leave-request'" target="_blank"
            >leave</a
          >
          module.
        </span>
      </v-card>
      <div
        v-if="originalList.length"
        :class="{
          'mb-3': !isMobileView,
          'd-flex': true,
          'justify-space-between': !isMobileView,
          'align-center': true,
          'flex-column': isMobileView,
        }"
      >
        <v-btn
          class="bg-white mr-2"
          :class="isMobileView ? 'mb-2' : ''"
          :style="'width: max-content'"
          :size="isMobileView ? 'small' : 'default'"
          rounded="lg"
          @click="$refs.datePicker.fp.open()"
        >
          <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
          <span class="text-caption px-1">Date:</span>
          <flat-pickr
            ref="datePicker"
            v-model="appliedDateRange"
            :config="flatPickerOptions"
            placeholder="Select Date Range"
            class="ml-2 mt-1 date-range-picker-custom-bg"
            style="outline: 0px; color: var(--v-primary-base); width: 170px"
            @onChange="onChangeDateRange"
          ></flat-pickr>
        </v-btn>
        <div :class="isMobileView ? 'd-flex flex-column' : 'd-flex'">
          <v-menu
            v-if="formAccess?.add && isAdmin"
            v-model="LOPMenu"
            max-height="200px"
            transition="scale-transition"
          >
            <template v-slot:activator="{ props }">
              <v-btn
                color="primary"
                class="mr-2"
                :class="isMobileView ? 'mb-2' : ''"
                rounded="lg"
                v-bind="props"
                @click="fnSelectLOPList()"
              >
                Initiate Unpaid Leave
                <v-icon v-if="!LOPMenu">fas fa-caret-down</v-icon>
                <v-icon v-else>fas fa-caret-up</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="lopType in lopTypes"
                :key="lopType.LeaveType_Id"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <div
                      v-bind="props"
                      class="pa-2 mb-1 rounded-lg cursor-pointer"
                      :class="{
                        'bg-hover': isHovering,
                        'bg-grey-lighten-4 text-primary': !isHovering,
                      }"
                      @click="initiateLOP(lopType.LeaveType_Id)"
                    >
                      {{ lopType.Leave_Name }}
                    </div>
                  </template>
                </v-hover>
              </v-list-item>
            </v-list>
          </v-menu>
          <v-btn
            v-if="formAccess?.add && isAdmin"
            color="primary"
            :class="isMobileView ? 'mb-2' : ''"
            class="mr-2"
            rounded="lg"
            @click="initiateAttendance()"
          >
            Initiate Attendance
          </v-btn>
          <div class="d-flex align-center justify-center">
            <v-btn
              color="transparent"
              class="mt-1"
              variant="flat"
              :size="isMobileView ? 'small' : 'default'"
              @click="refetchList()"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu v-model="openMoreMenu" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
                  <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                  <v-icon v-else>fas fa-caret-up</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'bg-hover': isHovering,
                        }"
                      >
                        {{ action.key }}
                      </v-list-item-title>
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
      </div>
      <v-data-table
        v-model="selectedRecords"
        :headers="tableHeaders"
        :items="itemList"
        item-value="unique_id"
        fixed-header
        :show-select="!isMobileView"
        :height="$store.getters.getTableHeightBasedOnScreenSize(350, itemList)"
        :items-per-page="50"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          {
            value: -1,
            title: '$vuetify.dataFooter.itemsPerPageAll',
          },
        ]"
      >
        <template v-slot:[`header.data-table-select`]="{ selectAll }">
          <div class="d-flex justify-center align-center">
            <v-checkbox-btn
              v-model="selectAllBox"
              color="primary"
              false-icon="far fa-circle"
              true-icon="fas fa-check-circle"
              :indeterminate="
                selectedItems.length > 0 && selectedItems.length < 50
              "
              indeterminate-icon="fas fa-minus-circle"
              @change="selectAll(selectAllBox)"
            ></v-checkbox-btn>
          </div>
        </template>
        <template v-slot:item="{ item }">
          <tr
            class="data-table-tr bg-white cursor-pointer"
            :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
          >
            <td :class="isMobileView ? 'mt-3 mb-n5' : ''">
              <v-checkbox-btn
                v-model="item.isSelected"
                color="primary"
                false-icon="far fa-circle"
                true-icon="fas fa-check-circle"
                class="mt-n2 ml-n2"
                @click.stop="
                  {
                  }
                "
                @change="checkAllSelected()"
              ></v-checkbox-btn>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Employee</div>
              <section>
                <div style="max-width: 200px" class="text-truncate">
                  <span class="text-primary text-body-2 font-weight-medium">
                    <v-tooltip :text="item.employee_name" location="bottom">
                      <template v-slot:activator="{ props }">
                        <span
                          v-bind="
                            item.employee_name && item.employee_name.length > 20
                              ? props
                              : ''
                          "
                          >{{ checkNullValue(item.employee_name) }}</span
                        >
                      </template>
                    </v-tooltip>
                    <v-tooltip
                      :text="item.user_defined_empid?.toString()"
                      location="bottom"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          v-if="item.user_defined_empid"
                          v-bind="
                            item.user_defined_empid &&
                            item.user_defined_empid.length > 20
                              ? props
                              : ''
                          "
                          class="text-grey"
                        >
                          {{ checkNullValue(item.user_defined_empid) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </span>
                </div>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Date</div>
              <section>
                {{ checkNullValue(item.d_absent_date) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Duration</div>
              <section>
                {{ checkNullValue(item.leave_duration) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Leave Period
              </div>
              <section>
                {{ checkNullValue(item.leave_period) }}
              </section>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
  </div>
  <AppWarningModal
    v-if="confirmationModel"
    :open-modal="confirmationModel"
    :confirmation-heading="'Are you sure to proceed with selected Unpaid Leave for the selected record(s)?'"
    :icon-name="''"
    :icon-color="'red'"
    :icon-Size="'75'"
    @close-warning-modal="confirmationModel = false"
    @accept-modal="applyOrValidateAutoLOP()"
  />
  <AppWarningModal
    v-if="intitiateAttendanceModel"
    :open-modal="intitiateAttendanceModel"
    :confirmation-heading="''"
    :icon-name="''"
    :icon-color="'primary'"
    closeButtonText="No"
    acceptButtonText="Yes"
    icon-Size="75"
    @close-warning-modal="intitiateAttendanceModel = false"
    @accept-modal="initiateAutoAttendance()"
  >
    <template v-slot:warningModalContent>
      <div
        class="text-h6 justify-center font-weight-bold text-primary text-center px-6 pt-2"
      >
        Are you sure to proceed with this action?
      </div>
      <div class="text-center mt-3 text-grey">
        For the selected record(s), the Check-In and Check-Out times will be
        based on the Work Schedule assigned to the respective employees
      </div>
    </template>
  </AppWarningModal>
  <AppWarningModal
    v-if="validationModel"
    :open-modal="validationModel"
    :confirmation-heading="''"
    :icon-name="'fas fa-exclamation-circle'"
    :icon-color="'amber-lighten-1'"
    closeButtonText="Cancel"
    acceptButtonText="Initiate Unpaid Leave"
    :acceptButtonDisable="validRecords.length === 0"
    icon-Size="75"
    @close-warning-modal="validationModel = false"
    @accept-modal="processValidRecords()"
  >
    <template v-slot:warningModalContent>
      <div
        v-if="validRecords.length !== selectedItems.length"
        class="text-h6 text-center text-grey mt-3"
      >
        <span class="text-primary">{{ validRecords.length }}</span> out of
        <span class="text-primary">{{ selectedItems.length }}</span> records are
        eligible for this Unpaid leave.
      </div>
      <div class="text-caption text-center mt-3">
        Please note that it is only applicable to standard business days and
        excludes weekends and holidays. If you wish to request Unpaid Leave for
        a weekend or holiday, you must utilize the leave module.
      </div>
      <div class="text-subtitle-1 text-grey-darken-1 text-center mt-3">
        *By clicking the ' Initiate Unpaid Leave ' button, you can apply loss of
        pay for eligible record(s).
      </div>
    </template>
  </AppWarningModal>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue } from "@/helper";
export default {
  name: "NoAttendance",
  components: { flatPickr },
  mixins: [FileExportMixin],
  emits: ["reset-filter", "list-count", "date-range"],
  props: {
    salaryStartDate: { type: String, required: true },
    salaryEndDate: { type: String, required: true },
    filterObj: { type: Object, default: () => ({}) },
    filterAppliedCount: { type: Number, default: 0 },
    preReq: { type: Number, default: 0 },
    payslipEmployeeIds: { type: Array, default: () => [] },
  },
  data() {
    return {
      listLoading: false,
      originalList: [],
      itemList: [],
      isErrorInList: false,
      errorContent: "Something went wrong. Please try after some time.",
      appliedDateRange: null,
      startDate: "",
      endDate: "",
      openMoreMenu: false,
      selectAllBox: false,
      selectedItems: [],
      selectedRecords: [],
      isLoading: false,
      LOPMenu: false,
      lopTypes: [],
      validationModel: false,
      validRecords: [],
      leaveTypeId: null,
      intitiateAttendanceModel: false,
      confirmationModel: false,
      isAttendanceEnforcedPayment: "",
    };
  },
  computed: {
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getFooterImage() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/common/idea-bulb.webp");
      else return require("@/assets/images/common/idea-bulb.png");
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
        maxDate: moment()
          .subtract(1, "days")
          .format(this.$store.state.orgDetails.orgDateFormat),
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    tableHeaders() {
      return [
        {
          title: "Employee",
          align: "start",
          key: "employee_name",
        },
        {
          title: "Date",
          key: "d_absent_date",
        },
        {
          title: "Duration",
          key: "leave_duration",
        },
        {
          title: "Leave Period",
          key: "leave_period",
        },
      ];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(368);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    enforcementMessage() {
      if (this.isAttendanceEnforcedPayment?.toLowerCase() === "no") return "";
      return "There are no records for the selected filters/searches.";
    },
  },
  watch: {
    selectedRecords(selRecords) {
      if (this.selectAllBox) {
        const selSet = new Set(selRecords);

        this.itemList.forEach((item) => {
          item.isSelected = selSet.has(item.unique_id);
        });
        this.selectedItems = this.itemList.filter((item) =>
          selSet.has(item.unique_id)
        );
      } else {
        // Iterate through itemLogList
        this.itemList.forEach((item) => {
          item.isSelected = false;
        });
        this.selectedItems = [];
      }
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
    filterAppliedCount(val) {
      if (val) {
        this.applyFilter();
      } else {
        this.itemList = this.originalList;
      }
    },
    itemList: {
      handler(newList) {
        this.$emit("list-count", newList.length);
      },
      deep: true,
    },
  },
  mounted() {
    this.setDateRange(this.salaryStartDate, this.salaryEndDate);
    this.fetchNoAttendanceList();
    this.fetchLOPList();
  },
  methods: {
    checkNullValue,
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.selectedItems = [];
      this.selectAllBox = false;
      this.fetchNoAttendanceList();
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });

        this.itemList = searchItems;
      }
    },
    applyFilter() {
      if (this.filterAppliedCount) {
        let filteredArray = this.originalList;
        if (this.filterObj.selectedDepartment.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedDepartment.includes(
              item.department_name
            );
          });
        }
        if (this.filterObj.selectedLocation.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedLocation.includes(item.location_name);
          });
        }
        if (this.filterObj.selectedServiceProvider.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedServiceProvider.includes(
              item.approval_status
            );
          });
        }
        if (this.filterObj.selectedEmpType.length) {
          filteredArray = filteredArray.filter((item) => {
            return this.filterObj.selectedEmpType.includes(item.location_name);
          });
        }
        this.itemList = filteredArray;
      }
    },
    setDateRange(startDate, endDate) {
      if (!startDate || !endDate) return;
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      this.appliedDateRange =
        moment(startDate).format(orgDateFormat) +
        " to " +
        moment(endDate).format(orgDateFormat);
      this.startDate = startDate;
      this.endDate = endDate;
    },
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectedItems = selectedItems;
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    async fetchNoAttendanceList() {
      let vm = this;
      vm.isErrorInList = false;
      vm.errorContent = "";
      vm.listLoading = true;
      try {
        const apiObj = {
          url:
            vm.baseUrl +
            "employees/attendance-finalization/list-attendance-finalization",
          type: "POST",
          dataType: "json",
          data: {
            actualSubTab: "noAttTab",
            department: [],
            employeeId: this.preReq ? this.payslipEmployeeIds : [],
            employeeType: [],
            endDate: this.endDate,
            finalizationMethod: "noAttendance",
            location: [],
            startDate: this.startDate,
            status: [],
          },
        };
        const response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          this.isAttendanceEnforcedPayment =
            response.noAttendanceData.attendanceEnforcedPayment;
          this.startDate = response.filterMinDate;
          this.endDate = response.filterMaxDate;
          this.setDateRange(this.startDate, this.endDate);
          this.$emit("date-range", {
            startDate: this.startDate,
            endDate: this.endDate,
          });
          if (response.noAttendanceData?.aaData?.length) {
            let details = response.noAttendanceData.aaData.filter(
              (el) => this.convertHMToSecs(el.attendance_hours) === 0
            );
            details = details.map((item, index) => {
              return {
                ...item,
                unique_id: index + 1,
              };
            });
            vm.originalList = details;
            vm.itemList = details;
          } else {
            vm.originalList = [];
            vm.itemList = [];
          }
        } else {
          vm.originalList = [];
          vm.itemList = [];
          vm.handleFetchAttendanceError();
        }
      } catch (error) {
        vm.originalList = [];
        vm.itemList = [];
        vm.handleFetchAttendanceError(error);
      } finally {
        vm.listLoading = false;
      }
    },
    handleFetchAttendanceError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "No Attendance",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    convertHMToSecs(hmTime) {
      if (!hmTime) return 0; // Handle null, undefined, or 0

      if (typeof hmTime === "number") return hmTime; // Already in seconds

      if (
        typeof hmTime === "string" &&
        (hmTime.includes(":") || hmTime.includes("."))
      ) {
        let delimiter = hmTime.includes(":") ? ":" : ".";
        let timeSplit = hmTime.split(delimiter).map(Number);

        if (
          timeSplit.length === 2 &&
          !isNaN(timeSplit[0]) &&
          !isNaN(timeSplit[1])
        ) {
          return timeSplit[0] * 3600 + timeSplit[1] * 60; // Convert to seconds
        }
      }

      let parsedTime = parseInt(hmTime, 10);
      return isNaN(parsedTime) ? 0 : parsedTime; // Return integer if valid, otherwise 0
    },
    onChangeDateRange(selectedDates) {
      if (selectedDates.length > 1) {
        // Parse the dates from the given format
        let parsedStartDate = moment(selectedDates[0], "DD/MM/YYYY");
        let parsedEndDate = moment(
          selectedDates.length > 1 ? selectedDates[1] : selectedDates[0],
          "DD/MM/YYYY"
        );

        // Format the dates into "YYYY-MM-DD" format
        this.startDate = parsedStartDate.format("YYYY-MM-DD");
        this.endDate = parsedEndDate.format("YYYY-MM-DD");
        this.fetchNoAttendanceList();
      }
    },
    onMoreAction(actionType) {
      if (actionType?.toLowerCase() === "export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Employee Id", key: "user_defined_empid" },
        { header: "Employee", key: "employee_name" },
        { header: "Designation", key: "designation_name" },
        { header: "Department", key: "department_name" },
        { header: "Location", key: "location_name" },
        { header: "Contact Number", key: "contact_details" },
        { header: "Date", key: "d_absent_date" },
        { header: "Duration", key: "leave_duration" },
        { header: "Leave Period", key: "leave_period" },
      ];

      const exportOptions = {
        fileExportData: this.itemList,
        fileName: "Missed Attendance and Leave",
        sheetName: "Attendance Finalization",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
    fnSelectLOPList() {
      if (this.lopTypes.length == 0) {
        this.LOPMenu = false;
        let msg = "";
        if (this.selectedItems.length > 0) {
          msg = "Please add Unpaid leave type to initiate Unpaid leave";
        } else if (this.selectedItems.length == 0) {
          msg = "Please select minimum one record";
        } else {
          msg = "There are no record(s) exist to initiate Unpaid leave";
        }

        let snackbarData = {
          isOpen: true,
          message: msg,
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async fetchLOPList() {
      let vm = this;
      try {
        const apiObj = {
          url:
            vm.baseUrl + "employees/attendance-finalization/get-unpaid-leaves",
          type: "POST",
          dataType: "json",
        };
        const response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          if (response.leaveTypes?.length) {
            this.lopTypes = response.leaveTypes;
          } else {
            this.lopTypes = [];
          }
        } else {
          this.handleFetchLopListError();
        }
      } catch (error) {
        this.handleFetchLopListError(error);
      }
    },
    handleFetchLopListError(err = "") {
      this.lopTypes = [];
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "unpaid leave types",
        isListError: false,
      });
    },
    initiateLOP(leaveTypeId) {
      this.leaveTypeId = leaveTypeId;
      if (this.selectedItems.length) {
        if (this.formAccess?.add) {
          this.confirmationModel = true;
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please select at least one record.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    async applyOrValidateAutoLOP() {
      let vm = this;
      vm.isLoading = true;
      vm.confirmationModel = false;
      let autoLOPDetails = vm.selectedItems.map((item) => ({
        Employee_Id: item.employee_id,
        Absent_Date: item.absent_date,
        Leave_Duration: item.leave_duration,
        Leave_Period: item.leave_period,
        Hours: item.hours,
        Contact_Details: item.contact_details,
      }));
      try {
        const apiObj = {
          url:
            vm.baseUrl + "employees/attendance-finalization/validate-auto-lop/",
          type: "POST",
          data: {
            Auto_LOP_Details: autoLOPDetails,
            LeaveType_Id: this.leaveTypeId,
            startDate: this.startDate,
            endDate: this.endDate,
            employeeType: [],
            location: [],
            department: [],
          },
          dataType: "json",
        };
        const response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          this.validRecords = response.validatedResult.Valid_Auto_LOP_Records;
          this.validationModel = true;
        } else if (response?.msg) {
          let snackbarData = {
            isOpen: true,
            message: response.msg,
            type: "warning",
          };
          this.showAlert(snackbarData);
        } else {
          this.handleValidateRecordError();
        }
      } catch (error) {
        this.handleValidateRecordError(error);
      } finally {
        vm.isLoading = false;
      }
    },
    handleValidateRecordError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "validating",
        form: "records for unpaid leave",
        isListError: false,
      });
    },
    async processValidRecords() {
      let vm = this;
      vm.isLoading = true;
      vm.validationModel = false;
      try {
        const apiObj = {
          url:
            vm.baseUrl + "employees/attendance-finalization/initiate-auto-lop/",
          type: "POST",
          data: {
            Auto_LOP_Details: this.validRecords,
            LeaveType_Id: this.leaveTypeId,
            startDate: this.startDate,
            endDate: this.endDate,
          },
          dataType: "json",
        };
        let response = await vm.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          let snackbarData = {
            isOpen: true,
            message: response.msg || "Unpaid leave initiated successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.selectedItems = [];
          vm.fetchNoAttendanceList();
        } else if (response?.msg) {
          let snackbarData = {
            isOpen: true,
            message: response.msg,
            type: "warning",
          };
          vm.showAlert(snackbarData);
        } else {
          this.handleInitiateLOPError();
        }
      } catch (error) {
        this.handleInitiateLOPError(error);
      } finally {
        vm.isLoading = false;
      }
    },
    handleInitiateLOPError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "initiating",
        form: "Unpaid leave",
        isListError: false,
      });
    },
    initiateAttendance() {
      if (this.selectedItems.length) {
        if (this.formAccess?.add) {
          this.intitiateAttendanceModel = true;
        } else {
          let snackbarData = {
            isOpen: true,
            message: "You don't have access to perform this action.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please select at least one record.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    async initiateAutoAttendance() {
      this.isLoading = true;
      this.intitiateAttendanceModel = false;
      try {
        let attendanceEntries = this.selectedItems.map((item) => ({
          Employee_Id: item.employee_id,
          Absent_Date: item.absent_date,
        }));
        let apiObj = {
          url:
            this.baseUrl +
            "employees/attendance-finalization/initiate-auto-attendance/",
          type: "POST",
          dataType: "json",
          data: {
            Auto_Attendance_Details: attendanceEntries,
          },
        };
        let response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          let snackbarData = {
            isOpen: true,
            message: response.msg || "Attendance initiated successfully",
            type: "success",
          };
          this.showAlert(snackbarData);
          this.selectedItems = [];
          this.fetchNoAttendanceList();
        } else if (response?.msg) {
          let snackbarData = {
            isOpen: true,
            message: response.msg,
            type: "warning",
          };
          this.showAlert(snackbarData);
        } else {
          this.handleMarkAttendanceError();
        }
      } catch (error) {
        this.handleMarkAttendanceError(error);
      } finally {
        this.isLoading = false;
      }
    },
    handleMarkAttendanceError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "marking",
        form: "Attendance",
        isListError: false,
      });
    },
  },
};
</script>
