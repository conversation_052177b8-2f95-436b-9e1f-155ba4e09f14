<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="true"
                :isDefaultFilter="false"
                @apply-emp-filter="filterAppliedCount += 1"
                @reset-emp-filter="resetFilter()"
              >
                <template v-slot:new-filter>
                  <v-row class="mr-2">
                    <v-col :cols="12" class="py-2">
                      <v-autocomplete
                        v-model="filterObj.selectedEmployees"
                        variant="solo"
                        label="Employee"
                        color="primary"
                        :items="employeeList"
                        item-value="employeeId"
                        item-title="employeeData"
                        multiple
                        closable-chips
                        chips
                        single-line
                        :loading="employeeListLoading"
                      ></v-autocomplete>
                    </v-col>
                    <v-col :cols="!isMobileView ? 6 : 12" class="py-2">
                      <v-text-field
                        v-model="filterObj.selectedMinFirstIn"
                        :active="minFirstInMenu"
                        :focused="minFirstInMenu"
                        label="Min First In"
                        variant="solo"
                        prepend-inner-icon="fas fa-clock"
                        readonly
                        single-line
                      >
                        <v-menu
                          v-model="minFirstInMenu"
                          :close-on-content-click="false"
                          activator="parent"
                          transition="scale-transition"
                        >
                          <v-time-picker
                            :hide-header="true"
                            v-model="filterObj.selectedMinFirstIn"
                            format="24hr"
                            @update:minute="minFirstInMenu = false"
                          ></v-time-picker>
                        </v-menu>
                      </v-text-field>
                    </v-col>
                    <v-col :cols="!isMobileView ? 6 : 12" class="py-2">
                      <v-text-field
                        v-model="filterObj.selectedMaxFirstIn"
                        :active="maxFirstInMenu"
                        :focused="maxFirstInMenu"
                        label="Max First In"
                        variant="solo"
                        prepend-inner-icon="fas fa-clock"
                        readonly
                        single-line
                      >
                        <v-menu
                          v-model="maxFirstInMenu"
                          :close-on-content-click="false"
                          activator="parent"
                          transition="scale-transition"
                        >
                          <v-time-picker
                            :hide-header="true"
                            v-model="filterObj.selectedMaxFirstIn"
                            format="24hr"
                            @update:minute="maxFirstInMenu = false"
                          ></v-time-picker>
                        </v-menu>
                      </v-text-field>
                    </v-col>
                    <v-col :cols="!isMobileView ? 6 : 12" class="py-2">
                      <v-text-field
                        v-model="filterObj.selectedMinLastOut"
                        :active="minLastOutMenu"
                        :focused="minLastOutMenu"
                        label="Min Last Out"
                        variant="solo"
                        prepend-inner-icon="fas fa-clock"
                        readonly
                        single-line
                      >
                        <v-menu
                          v-model="minLastOutMenu"
                          :close-on-content-click="false"
                          activator="parent"
                          transition="scale-transition"
                        >
                          <v-time-picker
                            :hide-header="true"
                            v-model="filterObj.selectedMinLastOut"
                            format="24hr"
                            @update:minute="minLastOutMenu = false"
                          ></v-time-picker>
                        </v-menu>
                      </v-text-field>
                    </v-col>
                    <v-col :cols="!isMobileView ? 6 : 12" class="py-2">
                      <v-text-field
                        v-model="filterObj.selectedMaxLastOut"
                        :active="maxLastOutMenu"
                        :focused="maxLastOutMenu"
                        label="Max Last Out"
                        variant="solo"
                        prepend-inner-icon="fas fa-clock"
                        readonly
                        single-line
                      >
                        <v-menu
                          v-model="maxLastOutMenu"
                          :close-on-content-click="false"
                          activator="parent"
                          transition="scale-transition"
                        >
                          <v-time-picker
                            :hide-header="true"
                            v-model="filterObj.selectedMaxLastOut"
                            format="24hr"
                            @update:minute="maxLastOutMenu = false"
                          ></v-time-picker>
                        </v-menu>
                      </v-text-field>
                    </v-col>
                    <v-col :cols="12" class="py-2">
                      <div style="font-size: 1em; color: grey" class="ml-1">
                        Early Checkout Hours
                      </div>
                      <v-range-slider
                        v-model="filterObj.rangeHour"
                        :min="0"
                        :max="600"
                        :step="1"
                        color="primary"
                        class="align-center"
                      >
                        <template v-slot:prepend>
                          <v-card
                            style="width: 70px; height: 30px"
                            elevation="2"
                            class="d-flex justify-center align-center"
                          >
                            {{ convertMinToHrsMin(filterObj.rangeHour[0]) }}
                          </v-card>
                        </template>
                        <template v-slot:append>
                          <v-card
                            style="width: 70px; height: 30px"
                            elevation="2"
                            class="d-flex justify-center align-center"
                          >
                            {{ convertMinToHrsMin(filterObj.rangeHour[1]) }}
                          </v-card>
                        </template>
                      </v-range-slider>
                    </v-col>
                    <v-col
                      v-if="fieldForce && (isAdmin || isServiceProviderAdmin)"
                      :cols="!isMobileView ? 6 : 12"
                      class="py-2"
                    >
                      <v-autocomplete
                        variant="solo"
                        v-model="filterObj.selectedServiceProvider"
                        color="primary"
                        :items="serviceProviderList"
                        :label="getCustomFieldName(115, 'Service Provider')"
                        multiple
                        closable-chips
                        chips
                        density="compact"
                        single-line
                        :loading="dropdownLoading"
                        item-title="Service_Provider_Name"
                        item-value="Service_Provider_Id"
                      >
                      </v-autocomplete>
                    </v-col>
                  </v-row>
                </template>
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="attendance-finalization">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <EarlyCheckOut
            :salaryStartDate="salaryStartDate"
            :salaryEndDate="salaryLastDate"
            :filterObj="filterObj"
            :filterAppliedCount="filterAppliedCount"
            @reset-filter="resetFilter()"
          ></EarlyCheckOut>
        </v-window-item>
      </v-window>
    </v-container>
  </div>
</template>
<script>
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import { defineAsyncComponent } from "vue";
const EarlyCheckOut = defineAsyncComponent(() =>
  import("../attendance-finalization/EarlyCheckOut.vue")
);
import moment from "moment";
import { getCustomFieldName } from "@/helper";
export default {
  name: "EarlyCheckout",
  components: {
    EmployeeDefaultFilterMenu,
    EarlyCheckOut,
  },
  data() {
    return {
      currentTabItem: "tab-1",
      mainTabs: ["Attendance Finalization", "Early Checkout"],
      isLoading: false,
      filterAppliedCount: 0,
      salaryStartDate: "",
      salaryLastDate: "",
      payslipEmployeeIds: [],
      minFirstInMenu: false,
      maxFirstInMenu: false,
      minLastOutMenu: false,
      maxLastOutMenu: false,
      dropdownLoading: false,
      employeeListLoading: false,
      employeeList: [],
      serviceProviderList: [],
      fieldForce: 0,
      filterObj: {
        selectedServiceProvider: [],
        selectedEmployees: [],
        selectedMinFirstIn: null,
        selectedMinLastOut: null,
        selectedMaxFirstIn: null,
        selectedMaxLastOut: null,
        rangeHour: [],
      },
      preReq: 0,
    };
  },
  computed: {
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessRights(
        "service-provider-admin"
      );
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(324);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else {
        return false;
      }
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    convertMinToHrsMin() {
      return (totalMinutes) => {
        if (totalMinutes >= 0) {
          let hours = Math.floor(totalMinutes / 60);
          let minutes = totalMinutes % 60;
          return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(
            2,
            "0"
          )}`;
        } else {
          return "  ";
        }
      };
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    let url_string = window.location.href;
    let url = new URL(url_string);
    this.preReq = parseInt(url.searchParams.get("pre-req"));
    if (this.preReq) {
      const decodedString = this.decodeBase64Url(url.searchParams.get("data"));
      if (decodedString) {
        const params = new URLSearchParams(decodedString);
        this.salaryStartDate = params.get("salaryStartDate");
        this.salaryLastDate = params.get("salaryEndDate");
        this.filterObj.selectedEmployees = params
          .get("payslipEmployeeIds")
          ?.split(",");
      } else {
        this.getSalaryDates();
      }
    } else {
      this.getSalaryDates();
    }
    this.getEmpList();
    this.fetchDropdownData();
  },
  methods: {
    getCustomFieldName,
    getSalaryDates() {
      let vm = this;
      vm.isLoading = true;
      const apiObj = {
        url: vm.baseUrl + "payroll/salary-payslip/get-salary-day/",
        type: "POST",
        async: false,
        dataType: "json",
        data: {
          salaryMonthYear: moment().format("M,YYYY"),
        },
      };
      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res) {
            vm.salaryStartDate = moment(res.salaryDates.Salary_Date).format(
              "YYYY-MM-DD"
            );
            let startDateObj = moment(res.salaryDates.Salary_Date);
            let lastDateObj = moment(res.salaryDates.Last_SalaryDate);
            if (
              moment().isBefore(startDateObj) ||
              moment().isAfter(lastDateObj)
            ) {
              vm.salaryLastDate = lastDateObj.format("YYYY-MM-DD");
            } else {
              vm.salaryLastDate = moment()
                .subtract(1, "day")
                .format("YYYY-MM-DD");
            }
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
          vm.salaryStartDate = moment().startOf("month").format("YYYY-MM-DD");
          vm.salaryLastDate = moment().format("YYYY-MM-DD");
        });
    },
    onTabChange(tab) {
      if (tab === "Attendance Finalization") {
        window.location.href =
          this.baseUrl + "employees/attendance-finalization";
      }
    },
    resetFilter() {
      this.filterObj = {
        selectedServiceProvider: [],
        selectedEmployees: [],
        selectedMinFirstIn: null,
        selectedMinLastOut: null,
        selectedMaxFirstIn: null,
        selectedMaxLastOut: null,
        rangeHour: [],
      };
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.filterAppliedCount = 0;
    },
    async getEmpList() {
      let vm = this;
      vm.employeeListLoading = true;

      await vm.$store
        .dispatch("getEmployeesList", {
          formName: "Attendance",
          formId: 324,
        })
        .then((empData) => {
          if (empData && empData.length) {
            let empList = empData.map((item) => ({
              ...item,
              employeeData: item.employeeName + " - " + item.userDefinedEmpId,
            }));
            vm.employeeList = [...empList];
          } else {
            vm.employeeList = [];
          }
          vm.employeeListLoading = false;
        })
        .catch((err) => {
          vm.employeeListLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "list",
            form: "employees",
            isListError: false,
          });
        });
    },
    fetchDropdownData() {
      this.dropdownLoading = true;
      if (!this.isDropdownDataRetrieved) {
        this.$store
          .dispatch("getDefaultDropdownList", { formId: 324 })
          .then((res) => {
            if (
              res.data &&
              res.data.getDropDownBoxDetails &&
              !res.data.getDropDownBoxDetails.errorCode
            ) {
              const { fieldForce, serviceProvider } =
                res.data.getDropDownBoxDetails;
              this.serviceProviderList = serviceProvider;
              this.fieldForce = fieldForce;
            } else {
              this.handleDropdownDataError();
            }
            this.dropdownLoading = false;
          })
          .catch(() => {
            this.dropdownLoading = false;
            this.handleDropdownDataError();
          });
      }
    },
  },
};
</script>
<style scoped>
.attendance-finalization {
  padding: 3.7em 0em 0em 0em;
}
@media screen and (max-width: 805px) {
  .attendance-finalization {
    padding: 5em 1em 0em 1em;
  }
}
</style>
