<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="5" lg="7" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu class="justify-end" :isFilter="false">
              </EmployeeDefaultFilterMenu>
              <ExitManagementFilter
                v-if="!showAddEditForm && (itemList.length || isFilterApplied)"
                ref="formFilterRef"
                :itemList="originalList"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="exit-management">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList('Resignation error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="Exit Management refers to the structured and automated process for handling employee departures, ensuring a smooth transition for both the organization and the employee. It encompasses several workflows, from resignation submission to final clearance, with the goal of maintaining compliance, transparency, and efficiency during the offboarding process."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="This process streamlines the exit process by automating approval workflows, clearance procedures, knowledge transfer process, revocation of access, data security, notice period tracking and document generation(Relieving Letter, Experience Letter, Final payslips)."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                    ><div v-if="formAccess && formAccess.add">
                      <v-btn
                        v-if="originalList.length === 0"
                        variant="elevated"
                        class="ml-4 mt-1 primary"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="showEmployeeList()"
                      >
                        <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                        <span>Initiate</span>
                      </v-btn>
                    </div>

                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      rounded="lg"
                      color="transparent"
                      class="ml-2 mt-1 primary"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <div
                v-if="originalList.length > 0 && !isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <div v-if="formAccess && formAccess.add">
                  <v-btn
                    variant="elevated"
                    rounded="lg"
                    class="mx-1 primary"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="showEmployeeList()"
                  >
                    <v-icon size="15">fas fa-plus</v-icon>
                    <span> Initiate</span>
                  </v-btn>
                </div>

                <v-btn
                  rounded="lg"
                  class="mt-1"
                  variant="flat"
                  color="transparent"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n5 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action"
                      @click="onMoreAction(action)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                          >
                            <v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>

              <v-row>
                <v-col
                  v-if="originalList.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                  class="mb-12"
                >
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      itemList.length > 11
                        ? $store.getters.getTableHeight(270)
                        : ''
                    "
                    :item-value="userDefinedEmpId"
                    :items-per-page="50"
                    class="elevation-1"
                    color="primary"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Employee
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.resignationId ===
                                  item.resignationId
                              "
                              class="data-table-side-border selected-item-border-color d-flex"
                            ></div>
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 200px; '
                                  : 'max-width: 100px; '
                              "
                            >
                              <span class="text-primary font-weight-regular">
                                {{ checkNullValue(item.employeeName) }}
                              </span>
                              <div
                                v-if="item.userDefinedEmpId"
                                class="text-grey"
                              >
                                {{ checkNullValue(item.userDefinedEmpId) }}
                              </div>
                            </div>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Designation
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.employeeDesignationId ===
                                  item.employeeDesignationId
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.employeeDesignationName"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.employeeDesignationName.length > 50
                                      ? props
                                      : ''
                                  "
                                >
                                  {{
                                    checkNullValue(item.employeeDesignationName)
                                  }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Department
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.employeeDepartmentName ===
                                  item.employeeDepartmentName
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.employeeDepartmentName"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.employeeDepartmentName.length > 50
                                      ? props
                                      : ''
                                  "
                                >
                                  {{
                                    checkNullValue(item.employeeDepartmentName)
                                  }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Applied Date
                          </div>
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 500px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ checkNullValue(formatDate(item.appliedDate)) }}
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Exit Date
                          </div>
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 500px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{
                              checkNullValue(formatDate(item.resignationDate))
                            }}
                          </section>
                        </td>

                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Status
                          </div>
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :class="statusColor(item.resignationStatus)"
                            :style="
                              !isMobileView
                                ? 'max-width: 500px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ checkNullValue(item.resignationStatus) }}
                          </section>
                        </td>
                        <td
                          v-if="!showViewForm && !isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-center align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            class="font-weight-bold d-flex justify-center align-center"
                            style="width: 100%"
                            v-if="isMobileView"
                          >
                            Actions
                          </div>
                          <section
                            class="d-flex justify-center align-center"
                            style="width: 100%"
                          >
                            <ActionMenu
                              v-if="itemActions(item).length > 0"
                              @selected-action="onActions($event, item)"
                              :actions="itemActions(item)"
                              :access-rights="checkAccess()"
                              :isPresentTooltip="true"
                              iconColor="grey"
                            ></ActionMenu>
                            <div v-else>
                              <p>-</p>
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewExitManagement
                    :selectedItem="selectedItem"
                    :isEdit="isEdit"
                    :access-rights="formAccess"
                    @open-close-model="closeCancelModel()"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                    @open-dynamic-form="openWorkFlowFromViewForm"
                  />
                </v-col>

                <v-col
                  :cols="originalList.length === 0 ? 12 : 7"
                  v-if="showAddEditForm && windowWidth >= 1264"
                >
                  <AddEditExitManagement
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :resignationList="itemList"
                    :addFormEmployeeDetails="addFormEmployeeDetails"
                    @close-form="closeAllForms()"
                    @edit-updated="
                      refetchList('Resignation updated successfully')
                    "
                    @added-new-record="openWorkflowModel"
                  />
                </v-col>

                <v-col cols="7" v-if="showWorkFlowModel && windowWidth >= 1264">
                  <FormRender
                    v-if="showWorkFlowModel"
                    :form-data="formJsonData"
                    :resignationId="dynamicViewResignationId"
                    :taskId="
                      dynamicFormViewNodeData?.taskId
                        ? dynamicFormViewNodeData.taskId
                        : createTaskId
                    "
                    :formResponseId="formResponseId"
                    :conversationalId="conversationalId"
                    :processInstanceId="processInstanceId"
                    :show-approval="showApproval"
                    @form-update-success="callUpdateTask()"
                    @close-form-render="showWorkFlowModel = false"
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditExitManagement
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :addFormEmployeeDetails="addFormEmployeeDetails"
        :resignationList="itemList"
        @close-form="closeAllForms()"
        @edit-updated="refetchList('Resignation updated successfully')"
        @added-new-record="openWorkflowModel"
      />
      <ViewExitManagement
        v-if="showViewForm"
        :selectedItem="selectedItem"
        :isEdit="isEdit"
        :access-rights="formAccess"
        @open-close-model="closeCancelModel()"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
        @open-dynamic-form="openWorkFlowFromViewForm"
      />
      <FormRender
        v-if="showWorkFlowModel"
        :form-data="formJsonData"
        :resignationId="dynamicViewResignationId"
        :taskId="
          dynamicFormViewNodeData?.taskId
            ? dynamicFormViewNodeData.taskId
            : createTaskId
        "
        :formResponseId="formResponseId"
        :conversationalId="conversationalId"
        :processInstanceId="processInstanceId"
        :show-approval="showApproval"
        @form-update-success="callUpdateTask()"
        @close-form-render="showWorkFlowModel = false"
      />
    </v-dialog>
    <AppLoading v-if="isEmployeesLoder"></AppLoading>
    <div v-else>
      <EmployeesListModal
        v-if="showEmpListModal"
        :show-modal="showEmpListModal"
        :employeesList="allEmployeesList"
        :showFilter="false"
        :showFilterSearch="true"
        selectStrategy="single"
        :isApplyFilter="true"
        employeeIdKey="idPrefix"
        userDefinedEmpIdKey="idPrefix"
        employeeNameKey="name"
        deptNameKey="departmentName"
        designationKey="designationName"
        departmentIdKey="departmentId"
        designationIdKey="designationId"
        @on-select-employee="onAddExitManagemnt($event)"
        @close-modal="showEmpListModal = false"
      />
    </div>
    <AppWarningModal
      v-if="cancelModel"
      :open-modal="cancelModel"
      confirmation-heading="Are you sure to cancel the Resignation?"
      icon-name="far fa-times-circle"
      icon-Size="50"
      :acceptButtonDisable="checkCancelButton"
      @close-warning-modal="closeAllForms()"
      @accept-modal="validateCancelResignation()"
      ><template v-slot:warningModalContent>
        <div style="max-width: 80%" v-if="checkInactive()">
          <v-row class="rounded-lg pa-5">
            <v-col cols="12">
              <div class="text-primary">
                The employee is currently inactive. Kindly update the employee's
                work email in the team summary to ensure they receive
                application access.
              </div></v-col
            ></v-row
          >
        </div>
        <div class="mt-2">
          <v-textarea
            ref="selectedReasonForCancel"
            v-model="selectedReasonForCancel"
            :rules="[
              required('Reason for Cancelling', selectedReasonForCancel),
              firstCharacterValidation(selectedReasonForCancel),
              minLengthValidation(
                'Reason for Cancelling',
                selectedReasonForCancel,
                5
              ),
              validateWithRulesAndReturnMessages(
                selectedReasonForCancel,
                'description',
                'Reason for Cancelling'
              ),
            ]"
            v-bind="props"
            rows="3"
            :clearable="true"
            auto-grow
            variant="solo"
            min-width="350px"
            ><template v-slot:label>
              Reason for Cancelling
              <span style="color: red">*</span>
            </template></v-textarea
          >
        </div>
      </template></AppWarningModal
    >

    <!-- Validation Alert Modal -->
    <AppWarningModal
      v-if="showValidationAlert"
      :open-modal="showValidationAlert"
      confirmation-heading="Validation Errors"
      icon-name="fas fa-exclamation-triangle"
      icon-Size="50"
      :show-cancel-button="false"
      accept-button-text=""
      close-button-text=""
      @close-warning-modal="closeValidationAlert()"
      @accept-modal="closeValidationAlert()"
    >
      <template v-slot:warningModalContent>
        <div class="mt-3">
          <div class="text-body-1 mb-4 text-center">
            Please resolve the following issues before proceeding:
          </div>
          <div class="validation-errors-container">
            <div
              v-for="(message, index) in validationMessages"
              :key="index"
              class="validation-error-item mb-3 pa-3"
              style="
                background-color: #fef7f7;
                border: 1px solid #ffcdd2;
                border-radius: 8px;
                border-left: 4px solid #f44336;
              "
            >
              <div class="d-flex align-start">
                <v-icon color="error" size="20" class="mr-3 mt-1">
                  fas fa-exclamation-circle
                </v-icon>
                <div
                  class="text-body-2 text-error font-weight-medium"
                  style="line-height: 1.5"
                >
                  {{ message }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewExitManagement = defineAsyncComponent(() =>
  import("./ViewExitManagement.vue")
);
const AddEditExitManagement = defineAsyncComponent(() =>
  import("./AddEditExitManagement.vue")
);
const EmployeesListModal = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeesListModal.vue")
);
const FormRender = defineAsyncComponent(() =>
  import("@/views/workflow/approval-management/FormRender.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import { checkNullValue } from "@/helper.js";
import validationRules from "@/mixins/validationRules";

// Queries
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import {
  GET_ALL_RESIGNATION,
  GET_ALL_EMPLOYEES,
  WITHDRAWN_CANCEL_RESIGNATION,
} from "@/graphql/my-team/exitManagement.js";
import { GET_DYNAMIC_FORM_DETAILS } from "@/graphql/workflow/approvalManagementQueries.js";

import Config from "@/config.js";
import moment from "moment";
import ExitManagementFilter from "./ExitManagementFilter.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
import axios from "axios";

export default {
  name: "ExitManagement",
  components: {
    EmployeeDefaultFilterMenu,
    AddEditExitManagement,
    ViewExitManagement,
    EmployeesListModal,
    ActionMenu,
    ExitManagementFilter,
    FormRender,
    NotesCard,
  },
  mixins: [FileExportMixin, validationRules],
  data: () => ({
    // list
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    havingAccess: {},
    //close
    cancelModel: false,
    selectedReasonForCancel: "",
    // add/update
    isLoading: false,
    isEdit: false,
    isEmployeesLoder: false,
    allEmployeesList: [],
    addFormEmployeeDetails: null,
    showAddEditForm: false,
    showEmpListModal: false,
    actionType: "",
    createTaskId: null,
    //workflow
    isUpdatingForm: false,
    showWorkFlowModel: false,
    isDynamicForm: false,
    dynamicViewResignationId: null,
    formResponseId: null,
    createResignationResponse: null,
    dynamicFormViewNodeData: null,
    showApproval: false,
    conversationalId: 0,
    formJsonData: {},
    processInstanceId: "",
    // view
    selectedItem: null,
    showViewForm: false,
    // validation
    showValidationAlert: false,
    validationMessages: [],
    // tab
    currentTabItem: "tab-0",
    isFilterApplied: false,
    openMoreMenu: false,
  }),
  computed: {
    landedFormName() {
      return "Exit Management";
    },
    domainName() {
      return this.$store.getters.domain;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessIdRights("219");
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    formAccess() {
      let formAccessRights = this.accessIdRights("34");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"] &&
        (formAccessRights.accessRights["admin"] ||
          this.isServiceProviderAdmin ||
          formAccessRights.accessRights["isManager"])
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    checkCancelButton() {
      if (
        this.selectedReasonForCancel &&
        this.selectedReasonForCancel.trim().length >= 5
      ) {
        return false;
      } else {
        return true;
      }
    },
    mainTabs() {
      let tab = [];
      if (
        this.formAccess &&
        this.formAccess.view &&
        this.approvalFormAccess &&
        this.approvalFormAccess.view
      ) {
        tab.push("Resignation", "Approvals");
      } else if (
        this.formAccess &&
        this.formAccess.view &&
        !this.approvalFormAccess.view
      ) {
        tab.push("Resignation");
      } else if (this.approvalFormAccess && !this.formAccess.view) {
        tab.push("Approvals");
      } else {
        tab.push("Resignation");
      }
      return tab;
    },
    approvalFormAccess() {
      let formAccess = this.accessIdRights("184");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Employee Name",
            align: "start",
            key: "employeeName",
          },
          {
            title: "Status",
            key: "resignationStatus",
          },
        ];
      } else {
        return [
          {
            title: "Employee Name",
            align: "start",

            key: "employeeName",
          },
          {
            title: "Designation",
            key: "employeeDesignationName",
          },
          {
            title: "Department",
            key: "employeeDepartmentName",
          },
          {
            title: "Date of Resignation",
            key: "appliedDate",
          },
          {
            title: "Date of Exit",
            key: "resignationDate",
          },
          {
            title: "Status",
            key: "resignationStatus",
          },
          {
            title: "Actions",
            key: "action",
            align: "center",
            sortable: false,
          },
        ];
      }
    },
    isSmallTable() {
      return (
        !this.openFormInModal &&
        (this.showAddEditForm || this.showViewForm || this.showWorkFlowModel)
      );
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText =
          "There are no Exit Management for the selected filters/searches.";
      }
      return msgText;
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm || this.showWorkFlowModel) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    moreActions() {
      return [
        { key: "Export with filter", icon: "fas fa-file-export" },
        { key: "Export all", icon: "fas fa-file-export" },
      ];
    },
  },

  mounted() {
    this.fetchList();
  },

  watch: {
    searchValue: {
      handler(val) {
        this.onApplySearch(val);
      },
      immediate: true,
    },
  },

  methods: {
    checkNullValue,
    checkAccess() {
      this.havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      this.havingAccess["cancel"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      return this.havingAccess;
    },
    onActions(type, item) {
      this.selectedItem = item;
      if (type && type.toLowerCase() === "cancel") {
        if (
          this.selectedItem?.resignationStatus?.toLowerCase() === "approved"
        ) {
          this.fetchResignationAjax();
        } else {
          this.cancelModel = true;
          this.selectedReasonForCancel = null;
        }
      }
    },
    onTabChange(tabName) {
      if (tabName === "Resignation") {
        this.currentTabItem = "tab-0";
      } else if (tabName == "Approvals") {
        // this.isGetJobpostDetailsLoading = true;
        this.$router.push("/approvals/approval-management?form_id=34");
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    statusColor(status) {
      if (status === "Approved" || status === "Applied") {
        return "text-green";
      } else if (status === "Canceled" || status === "Withdrawn") {
        return "text-amber";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-primary";
      }
    },
    itemActions(item) {
      if (
        this.formAccess &&
        this.formAccess.update &&
        (item.resignationStatus.toLowerCase() === "applied" ||
          item.resignationStatus.toLowerCase() === "approved" ||
          item.resignationStatus.toLowerCase() === "incomplete")
      ) {
        return ["Cancel"];
      } else return [];
    },
    async validateCancelResignation() {
      this.cancelModel = false;
      if (this.selectedReasonForCancel) {
        this.cancelResignation(this.selectedItem);
      }
    },
    async fetchResignationAjax() {
      try {
        this.isLoading = true;
        let employeeId = this.selectedItem?.employeeId;
        let url =
          this.baseUrl +
          "default/employee-info/list-approver-details/employeeId/" +
          +employeeId +
          "/formName/Resignation/loginEmployeeId/" +
          this.loginEmployeeId;

        const response = await axios.get(url);
        if (
          response.data?.payslipGenerated &&
          response.data?.fullAndFinalSettlementInitiated
        ) {
          var snackbarData = {
            isOpen: true,
            type: "warning",
            message:
              "Resignation cannot be canceled as the employee full and final settlement is initiated.",
          };
          this.selectedItem = null;
          this.showAlert(snackbarData);
        } else {
          this.cancelModel = true;
          this.selectedReasonForCancel = null;
        }
      } catch (error) {
        this.handleAxiosError(error);
      } finally {
        this.isLoading = false;
      }
    },
    handleAxiosError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Resignation",
        isListError: true,
      });
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.map((item) => ({
          ...item,
          employeeData: item.employeeName + " " + item.userDefinedEmpId,
        }));
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        searchItems = searchItems.map((item) => ({
          ...item,
          employeeData: "",
        }));
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
      let filterObj = {
        selectedDesignation: [],
        selectedDepartment: [],
        selectedResignationDate: "",
        selectedExitDate: "",
        selectedStatus: [],
        selectedLocations: [],
        selectedTypes: [],
      };
      this.applyFilter(filterObj);
    },

    applyFilter(filter) {
      let filteredList = this.originalList;
      if (filter.selectedDesignation && filter.selectedDesignation.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.selectedDesignation.includes(
            item.employeeDesignationName
          );
        });
      }
      if (filter.selectedDepartment && filter.selectedDepartment.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.selectedDepartment.includes(
            item.employeeDepartmentName
          );
        });
      }
      if (filter.selectedStatus && filter.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.selectedStatus.includes(item.resignationStatus);
        });
      }
      if (filter.selectedLocations && filter.selectedLocations.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.selectedLocations.includes(item.locationName);
        });
      }
      if (filter.selectedTypes && filter.selectedTypes.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.selectedTypes.includes(item.employeeType);
        });
      }
      if (
        filter.selectedResignationDate &&
        filter.selectedResignationDate !== "" &&
        filter.selectedResignationDate !== "Invalid date"
      ) {
        filteredList = filteredList.filter((item) => {
          let selectedResignationDate = moment(
            filter.selectedResignationDate
          ).format("YYYY-MM-DD");
          let appliedDate = moment(item.appliedDate).format("YYYY-MM-DD");
          return selectedResignationDate === appliedDate;
        });
      }
      if (
        filter.selectedExitDate &&
        filter.selectedExitDate !== "" &&
        filter.selectedExitDate !== "Invalid date"
      ) {
        filteredList = filteredList.filter((item) => {
          let selectedExitDate = moment(filter.selectedExitDate).format(
            "YYYY-MM-DD"
          );
          let resignationDate = moment(item.resignationDate).format(
            "YYYY-MM-DD"
          );
          return selectedExitDate === resignationDate;
        });
      }
      this.isFilterApplied = true;
      this.itemList = filteredList;
    },
    firstCharacterValidation(value) {
      if (value && value.trimStart() === "") {
        return "The first character cannot be a space.";
      }
      return true;
    },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      const actionKey = actionType.key.toLowerCase();
      if (actionKey === "export with filter") {
        this.exportReportFile(this.itemList);
      } else if (actionKey === "export all") {
        this.exportReportFile(this.originalList);
      }
      this.openMoreMenu = false;
    },
    closeCancelModel() {
      this.cancelModel = true;
      this.selectedReasonForCancel = null;
    },
    checkInactive() {
      if (
        new Date(this.selectedItem?.resignationDate) < new Date() &&
        this.selectedItem?.resignationStatus === "Approved"
      ) {
        return true;
      }
      return false;
    },
    exportReportFile(lists) {
      let exportHeaders = [
        {
          header: "Employee Id",
          key: "userDefinedEmpId",
        },
        {
          header: "Employee Name",
          key: "employeeName",
        },
        {
          header: "Gender",
          key: "employeeGender",
        },
        {
          header: "Employee Type",
          key: "employeeType",
        },
        {
          header: "Status",
          key: "resignationStatus",
        },
        {
          header: "Date of Resignation",
          key: "appliedDate",
        },
        {
          header: "Date of Exit",
          key: "resignationDate",
        },
        {
          header: "Relieving Reason",
          key: "esicReasonName",
        },
        {
          header: "Designation",
          key: "employeeDesignationName",
        },
        {
          header: "Department",
          key: "employeeDepartmentName",
        },
        {
          header: "Location",
          key: "locationName",
        },
        {
          header: "Added On",
          key: "addedOn",
        },
        {
          header: "Added By",
          key: "addedUserName",
        },
        {
          header: "Updated On",
          key: "updatedOn",
        },
        {
          header: "Updated By",
          key: "updatedUserName",
        },
        {
          header: "Comment",
          key: "relievingReasonComment",
        },
      ];
      let ExitManagement = lists;
      let exportOptions = {
        fileExportData: ExitManagement,
        fileName: "Resignation",
        sheetName: "Resignation",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },

    openEditForm() {
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },
    showEmployeeList() {
      this.showEmpListModal = true;
      this.getAllEmployees();
    },

    onAddExitManagemnt(employee) {
      this.addFormEmployeeDetails = employee;
      this.showEmpListModal = false;
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.cancelModel = false;
      this.showEmpListModal = false;
      this.selectedItem = null;
      this.isEdit = false;
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_ALL_RESIGNATION,
          client: "apolloClientZ",
          variables: {
            envelope: {
              loggedInUserId: this.loginEmployeeId,
              orgCode: this.orgCode,
              formId: 34,
            },
            filter: {
              searchValue: "",
              limit: 1000,
              offset: 0,
              status: [],
              employees: [],
              noticeDate: {
                start: "",
                end: "",
              },
              resignationDate: {
                start: "",
                end: "",
              },
              designationId: null,
              departmentId: null,
            },
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getAllResignation &&
            !response.data.getAllResignation.error
          ) {
            vm.itemList = response.data.getAllResignation.result;
            vm.originalList = response.data.getAllResignation.result;
            vm.listLoading = false;
            vm.onApplySearch();
          } else {
            this.listLoading = false;
            vm.handleListError();
          }
        })
        .catch((err) => {
          this.listLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Resignation",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    getAllEmployees() {
      let vm = this;
      vm.isEmployeesLoder = true;
      vm.$apollo
        .query({
          query: GET_ALL_EMPLOYEES,
          client: "apolloClientZ",
          variables: {
            envelope: {
              loggedInUserId: this.loginEmployeeId,
              orgCode: this.orgCode,
              formId: 34,
            },
            filter: {
              departmentId: 0,
              designationId: 0,
              employeeTypeId: 0,
              locationId: 0,
              searchValue: "",
              status: ["Active"],
            },
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getAllEmployees &&
            !response.data.getAllEmployees.error
          ) {
            vm.allEmployeesList = response.data.getAllEmployees.result;
            this.allEmployeesList = this.allEmployeesList.filter(
              (el) => el.status === "Active"
            );
            vm.isEmployeesLoder = false;
          } else {
            this.isEmployeesLoder = false;
            vm.handleEmployeesError();
          }
        })
        .catch((err) => {
          this.isEmployeesLoder = false;
          vm.handleEmployeesError(err);
        });
    },
    handleEmployeesError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Resignation",
        isListError: true,
      });
    },

    cancelResignation(item) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: WITHDRAWN_CANCEL_RESIGNATION,
          client: "apolloClientZ",
          variables: {
            envelope: {
              loggedInUserId: this.loginEmployeeId,
              orgCode: this.orgCode,
              formId: 34,
            },
            approvalStatus: "Canceled",
            comment: this.selectedReasonForCancel,
            resignationId: item?.resignationId,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.withdrawnCancelResignation &&
            !response.data.withdrawnCancelResignation.error
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Resignation Canceled successfully!",
            };
            vm.showAlert(snackbarData);
            vm.refetchList();
            vm.isLoading = false;
          } else {
            let errMsg =
              response?.data?.withdrawnCancelResignation?.error ?? "";
            let extendedErrors =
              response?.data?.withdrawnCancelResignation?.extendedErrors ?? [];
            vm.handleCancelErrors(errMsg, extendedErrors);
          }
        })
        .catch((error) => {
          vm.handleCancelErrors(error, []);
        });
    },
    handleCancelErrors(err = "", extendedErrors = []) {
      this.isLoading = false;

      // Handle extendedErrors first if they exist
      if (extendedErrors && extendedErrors.length > 0) {
        let backupValidations = extendedErrors;
        let validationErrors = {};

        for (let error of backupValidations) {
          if (error?.toLowerCase() === "ess0138") {
            validationErrors[
              "ESS0138"
            ] = `ESS0138 - Employee ID is already assigned to an active employee. Please confirm and update the active employee's Employee ID to cancel the resignation.`;
          } else if (error?.toLowerCase() === "ess0140") {
            validationErrors[
              "ESS0140"
            ] = `ESS0140 - Employee email is already registered for an active employee. Please confirm and update the active employee's email to cancel the resignation.`;
          } else if (error?.toLowerCase() === "ess0141") {
            const biometricFieldName =
              this.labelList[474]?.Field_Alias || "Biometric Integration ID";
            validationErrors[
              "ESS0141"
            ] = `ESS0141 - ${biometricFieldName} is already assigned to an active employee. Please confirm and update the active employee's ${biometricFieldName} to cancel the resignation.`;
          } else if (error?.toLowerCase() === "ess0136") {
            const bankAccountFieldName =
              this.labelList[455]?.Field_Alias || "Bank account number";
            validationErrors[
              "ESS0136"
            ] = `ESS0136 - ${bankAccountFieldName} is already linked to an active employee. Please confirm and update the active employee's ${bankAccountFieldName.toLowerCase()} to cancel the resignation.`;
          } else if (error?.toLowerCase() === "ess0139") {
            validationErrors[
              "ESS0139"
            ] = `ESS0139 - Mobile number is already linked to an active employee. Please confirm and update the active employee's mobile number to cancel the resignation.`;
          } else if (error?.toLowerCase() === "ess0146") {
            const uanFieldName = this.labelList[214]?.Field_Alias || "UAN";
            validationErrors[
              "ESS0146"
            ] = `ESS0146 - ${uanFieldName} is already linked to an active employee. Please confirm and update the active employee's ${uanFieldName} to cancel the resignation.`;
          } else if (error?.toLowerCase() === "ess0142") {
            const panFieldName = this.labelList[213]?.Field_Alias || "PAN";
            validationErrors[
              "ESS0142"
            ] = `ESS0142 - ${panFieldName} is already linked to an active employee. Please confirm and update the active employee's ${panFieldName} to cancel the resignation.`;
          } else if (error?.toLowerCase() === "ess0143") {
            validationErrors[
              "ESS0143"
            ] = `ESS0143 - National Identity Number (Aadhar/Social Security) is already linked to an active employee. Please confirm and update the active employee's National Identity Number (Aadhar/Social Security) to cancel the resignation.`;
          } else if (error?.toLowerCase() === "ess0144") {
            const pfFieldName = this.labelList[305]?.Field_Alias || "PF Number";
            validationErrors[
              "ESS0144"
            ] = `ESS0144 - ${pfFieldName} is already linked to an active employee. Please confirm and update the active employee's ${pfFieldName} to cancel the resignation.`;
          }
        }

        let validationMessages = [];
        for (var eCode in validationErrors) {
          validationMessages.push(validationErrors[eCode]);
        }
        this.validationMessages = validationMessages;
        if (this.validationMessages && this.validationMessages.length) {
          this.showValidationAlert = true;
        }
        return;
      }

      // Check if the error code is "ERE0130"
      if (err?.code === "ERE0130") {
        var snackbarData = {
          isOpen: true,
          type: "warning",
          message: err.message,
        };
        this.showAlert(snackbarData);
      } else {
        // Handle other errors as usual
        this.$store.dispatch("handleApiErrors", {
          error: err,
          action: "cancel",
          form: "Resignation",
          isListError: false,
        });
      }
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },
    openWorkflowModel(createResignationResponse) {
      this.fetchList();
      this.createResignationResponse = createResignationResponse;
      this.closeAllForms();
      if (createResignationResponse?.dynamicFormTemplate) {
        this.dynamicViewResignationId = parseInt(
          createResignationResponse?.resignation?.resignationId
        );
        this.formJsonData = JSON.parse(
          createResignationResponse.dynamicFormTemplate?.template
        );
        this.createTaskId = createResignationResponse?.workflow?.workflowTaskId;
        this.showApproval = true;
        this.showWorkFlowModel = true;
      }
    },
    openWorkFlowFromViewForm(clickResponse, resignationId, taskAction) {
      this.dynamicViewResignationId = parseInt(resignationId);
      this.closeAllForms();
      this.dynamicFormViewNodeData = clickResponse;
      if (this.dynamicFormViewNodeData?.taskId) {
        this.getDynamicFormDetails(
          this.dynamicFormViewNodeData?.taskId,
          this.dynamicFormViewNodeData?.formIdentifier,
          this.dynamicFormViewNodeData?.processInstanceId,
          taskAction?.task_action
        );
      }
    },
    async getDynamicFormDetails(
      workflowTaskId,
      dynamicFormId,
      processInstanceId,
      taskAction
    ) {
      let vm = this;
      vm.isDynamicForm = true;
      vm.processInstanceId = processInstanceId;
      await vm.$apollo
        .query({
          query: GET_DYNAMIC_FORM_DETAILS,
          variables: {
            envelope: {
              orgCode: vm.orgCode,
              loggedInUserId: vm.loginEmployeeId,
              formId: 34,
            },
            dynamicFormId: parseInt(dynamicFormId),
            workflowTaskId: workflowTaskId,
          },
          client: "apolloClientZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getWorkflowTaskDynamicFormDetails &&
            response.data.getWorkflowTaskDynamicFormDetails.result
          ) {
            const { dynamicFormTemplates, dynamicFormResponse } =
              response.data.getWorkflowTaskDynamicFormDetails.result;
            let formJson = "";
            if (dynamicFormResponse) {
              // response of the form -- empty if it is not submitted before, or the previous response is returned
              formJson = dynamicFormResponse.formResponse;
            } else {
              // form template for new data
              formJson = dynamicFormTemplates.template;
            }
            vm.formJsonData = JSON.parse(formJson);
            vm.conversationalId = dynamicFormTemplates?.conversational;
            vm.formResponseId = dynamicFormResponse
              ? parseInt(dynamicFormResponse?.formResponseId)
              : null;
            vm.showApproval = taskAction ? true : false;
            vm.showWorkFlowModel = true;
            vm.isDynamicForm = false;
          } else {
            vm.handleDynamicFormRetrieveError();
          }
        })
        .catch(() => {
          vm.handleDynamicFormRetrieveError();
        });
    },
    handleDynamicFormRetrieveError() {
      this.isDynamicForm = false;
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message:
          "Something went wrong while retrieving the form details. Please try after some time.",
      };
      this.showAlert(snackbarData);
    },
    callUpdateTask() {
      this.showAddEditForm = false;
      let inputVariables = {
        task_id: this.createResignationResponse?.workflow?.workflowTaskId
          ? this.createResignationResponse?.workflow?.workflowTaskId
          : this.dynamicFormViewNodeData?.taskId,
        status: "approve",
        completed_by: this.loginEmployeeId,
        form_data: {},
        status_id: "1002",
        remarks: "",
      };
      let type = "formSubmission";
      this.updateTask(inputVariables, type);
    },
    updateTask(inputParams, type = "") {
      let vm = this;
      vm.showWorkFlowModel = false;
      try {
        vm.isLoading = true;
        axios
          .post(Config.workflowUrl + "/task/update", inputParams, {
            headers: {
              org_code: vm.orgCode,
              employee_id: vm.loginEmployeeId,
              db_prefix: vm.domainName,
              irukka_id_token: this.irukkaIdToken,
              Authorization: window.$cookies.get("accessToken")
                ? window.$cookies.get("accessToken")
                : "",
              refresh_token: window.$cookies.get("refreshToken")
                ? window.$cookies.get("refreshToken")
                : null,
              partnerid: window.$cookies.get("partnerid")
                ? window.$cookies.get("partnerid")
                : "-",
              additional_headers: JSON.stringify({
                d_code: window.$cookies.get("d_code"),
                b_code: window.$cookies.get("b_code"),
                org_code: vm.orgCode,
                user_ip: this.$store.state.userIpAddress,
                Authorization: window.$cookies.get("accessToken")
                  ? window.$cookies.get("accessToken")
                  : null,
                refresh_token: window.$cookies.get("refreshToken")
                  ? window.$cookies.get("refreshToken")
                  : null,
                partnerid: window.$cookies.get("partnerid")
                  ? window.$cookies.get("partnerid")
                  : "-",
              }),
            },
          })
          .then(() => {
            vm.isLoading = false;
            let snackbarData;
            if (type === "formSubmission") {
              snackbarData = {
                isOpen: true,
                message: "Form submission completed and approved successfully",
                type: "success",
              };
            } else {
              snackbarData = {
                isOpen: true,
                message: "Approved Successfully",
                type: "success",
              };
            }
            vm.showAlert(snackbarData);
            vm.showAddEditForm = false;
            this.createResignationResponse = null;
            this.dynamicFormViewNodeData = null;
            this.createTaskId = null;
            vm.fetchList();
          })
          .catch(function (err) {
            vm.handleUpdateTaskError(err);
          });
      } catch {
        vm.handleUpdateTaskError(err);
      }
    },

    handleUpdateTaskError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message:
          this.actionType === "approve"
            ? "Something went wrong while approving the record. Please try after some time."
            : "Something went wrong while rejecting the record. Please try after some time.",
        type: "warning",
      };
      if (err && err.response && err.response.data) {
        let errorCode = err.response.data.errorCode;
        if (errorCode) {
          switch (errorCode) {
            case "ERR-756": // for multiple scenarios
              if (err.response.data.message === "Task id not found") {
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as it was already deleted/approved/rejected in the same or some other user session."
                    : "Unable to reject the record as it was already deleted/approved/rejected in the same or some other user session.";
              } else if (
                err.response.data.message ===
                "Unable to update status as the employee's available leave balance is less than applied leave"
              ) {
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as the employee's available leave balance is less than applied leave."
                    : "Unable to reject the record as the employee's available leave balance is less than applied leave.";
              } else if (
                err.response.data.message ===
                "Error while processing the request to update the leave status. Please contact the system admin."
              ) {
                snackbarData.message =
                  "Something went wrong while processing the request to update the leave status. Please contact the platform administrator.";
              } else if (
                err.response.data.message ===
                "Error while updating the workflow status. Please contact the system admin."
              ) {
                snackbarData.message =
                  "Something went wrong while updating the workflow status. Please contact the platform administrator.";
              } else if (
                err.response.data.message ===
                "Error while processing the request to update the leave workflow status."
              ) {
                snackbarData.message =
                  "Something went wrong while processing the request to update the leave workflow status. Please contact the platform administrator.";
              } else if (
                err.response.data.message ===
                  "Leave cannot be updated as the full and final settlement is initiated or settled for the employee" ||
                err.response.data.message ===
                  "Leave cannot be added or updated as the full and final settlement is initiated or settled for the employee.Kindly delete the F & F settlement in order to make the necessary modifications."
              ) {
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as the employee's full and final settlement is initiated or settled"
                    : "Unable to reject the record as the employee's full and final settlement is initiated or settled";
              } else if (
                err.response.data.message ===
                "Error while processing the request to update the lop recovery workflow status."
              ) {
                snackbarData.message =
                  "Something went wrong while processing the request to update the lop recovery status. Please contact the platform administrator.";
              } else if (
                err.response.data.message === "The leave record does not exist."
              ) {
                snackbarData.message =
                  this.actionType === "approve"
                    ? "Unable to approve the record as the leave record does not exits"
                    : "Unable to reject the record as the leave record does not exits";
              } else {
                snackbarData.message =
                  "Something went wrong while processing the request to update the status. Please contact the platform administrator.";
              }
              this.$emit("on-status-update");
              break;
            case "ERR-753": // task id not found
              snackbarData.message =
                this.actionType === "approve"
                  ? "Unable to approve the record as it was already deleted/approved/rejected in the same or some other user session."
                  : "Unable to reject the record as it was already deleted/approved/rejected in the same or some other user session.";
              this.$emit("on-status-update");
              break;
            case "ERR-799": // Status id not found
              snackbarData.message =
                this.actionType === "approve"
                  ? "Unable to approve the record as the status is invalid. If you continue to see this issue please contact the platform administrator."
                  : "Unable to reject the record as the status is invalid. If you continue to see this issue please contact the platform administrator.";
              this.$emit("on-status-update");
              break;
            case "ERR-754": // task already approved or rejected
              snackbarData.message =
                this.actionType === "approve"
                  ? "Unable to approve the record as it was already approved in the same or some other user session."
                  : "Unable to reject the record as it was already rejected in the same or some other user session.";
              this.$emit("on-status-update");
              break;
            case "ERR-798": // Error while executing the query
            default:
              this.actionType === "approve"
                ? "Something went wrong while approving the record. If you continue to see this issue please contact the platform administrator."
                : "Something went wrong while rejecting the record. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        }
      }
      this.showAlert(snackbarData);
      this.showAddEditForm = false;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
@import url("../../../assets/css/dynamic-form-builder.css");
.exit-management {
  padding: 5em 3em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 3.5em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}
.selected-item-border-color {
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .exit-management {
    padding: 5em 1em 0em 1em;
  }
}
</style>
