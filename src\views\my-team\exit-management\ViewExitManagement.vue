<template>
  <div>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon color="primary" size="20">fas fa-file-alt</v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">Resignation</div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-tooltip
            :text="
              isFullFinalEnabled
                ? 'Full and Final Settlement & Payslip initiated for the employee.'
                : ''
            "
            location="top"
          >
            <template v-slot:activator="{ props }">
              <v-btn
                @click="!isFullFinalEnabled ? $emit('open-close-model') : ''"
                size="small"
                variant="outlined"
                v-bind="isFullFinalEnabled ? props : {}"
                rounded="lg"
                class="mr-2"
                :class="isFullFinalEnabled ? 'cursor-not-allowed' : ''"
                v-if="
                  isEmployee &&
                  accessRights?.update &&
                  (selectedFormData.resignationStatus.toLowerCase() ===
                    `applied` ||
                    selectedFormData.resignationStatus.toLowerCase() ===
                      `approved` ||
                    selectedFormData.resignationStatus.toLowerCase() ===
                      `incomplete`)
                "
                >Cancel</v-btn
              >
              <v-btn
                @click="!isFullFinalEnabled ? $emit('open-edit-form') : ''"
                size="small"
                color="primary"
                variant="elevated"
                v-bind="isFullFinalEnabled ? props : {}"
                :class="isFullFinalEnabled ? 'cursor-not-allowed' : ''"
                rounded="lg"
                v-if="
                  isEmployee &&
                  accessRights.update &&
                  (selectedFormData.resignationStatus.toLowerCase() ===
                    `applied` ||
                    selectedFormData.resignationStatus.toLowerCase() ===
                      `approved` ||
                    selectedFormData.resignationStatus.toLowerCase() ===
                      `incomplete`)
                "
                >Edit</v-btn
              >
              <v-icon class="mx-1" color="primary" @click="$emit('close-form')">
                fas fa-times
              </v-icon>
            </template></v-tooltip
          >
        </div>
      </div>
      <div
        :style="
          isMobileView
            ? 'height: calc(100vh - 400px); overflow: scroll'
            : 'height: 430px; overflow: scroll;'
        "
      >
        <v-card-text>
          <v-tabs
            v-if="selectedItem.workflowInstanceId"
            v-model="viewFormTab"
            color="primary"
            align-tabs="center"
            class="mt-n4"
            style="box-shadow: 0 4px 6px -6px #222"
          >
            <v-tab :value="1">Details</v-tab>
            <v-tab :value="2">Approval Flow</v-tab>
          </v-tabs>
          <div v-if="isLoadingCard" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <v-window v-model="viewFormTab" v-else style="width: 100%">
            <v-window-item :value="1">
              <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
                <v-col
                  cols="12"
                  sm="6"
                  lg="6"
                  :class="isMobileView ? ' ml-4' : ''"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">Employee Id</p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedFormData.userDefinedEmpId) }}
                  </p>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  lg="6"
                  :class="isMobileView ? ' ml-4' : ''"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Employee Name
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedFormData.employeeName) }}
                  </p>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  lg="6"
                  :class="isMobileView ? ' ml-4' : ''"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Date of Resignation
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(formatDate(selectedFormData.appliedDate))
                    }}
                  </p>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  lg="6"
                  :class="isMobileView ? ' ml-4' : ''"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">Date of Exit</p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        formatDate(selectedFormData.resignationDate)
                      )
                    }}
                  </p>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  lg="6"
                  :class="isMobileView ? ' ml-4' : ''"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Reason for Resignation
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      !isEmployee
                        ? "-"
                        : checkNullValue(selectedFormData.esicReasonName)
                    }}
                  </p>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  lg="6"
                  :class="isMobileView ? ' ml-4' : ''"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">Status</p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(selectedFormData.resignationStatus) }}
                  </p>
                </v-col>
                <v-col
                  v-if="
                    !isEmployee
                      ? labelList[462]?.Field_Visiblity?.toLowerCase() === 'yes'
                      : labelList[461]?.Field_Visiblity?.toLowerCase() === 'yes'
                  "
                  cols="12"
                  sm="6"
                  lg="6"
                  :class="isMobileView ? ' ml-4' : ''"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{
                      !isEmployee
                        ? labelList[462]?.Field_Alias
                        : labelList[461]?.Field_Alias
                    }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    <v-btn
                      v-if="selectedFormData.fileName"
                      @click="retrieveFileContents(selectedFormData.fileName)"
                      variant="text"
                      color="primary"
                      style="text-decoration: underline"
                    >
                      {{ extractFileName(selectedFormData.fileName) }}
                    </v-btn>
                    <span v-else>-</span>
                  </p>
                </v-col>
                <v-col
                  cols="12"
                  sm="6"
                  lg="6"
                  :class="isMobileView ? ' ml-4' : ''"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">Comment</p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        !isEmployee
                          ? selectedFormData.withdrawnCancellationComment
                          : selectedFormData.relievingReasonComment
                      )
                    }}
                  </p>
                </v-col>
              </v-row>
              <v-row class="px-sm-8 px-md-10 mb-2">
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  ></MoreDetails> </v-col
              ></v-row>
            </v-window-item>
            <v-window-item :value="2">
              <organization-chart :datasource="orgChartData">
                <template v-slot="{ nodeData }">
                  <div class="d-flex flex-column my-2">
                    <i :class="nodeData.iconName"></i>
                    <div class="mt-2 text-primary">
                      {{ nodeData.title }}
                    </div>
                    <div class="text-blue d-flex justify-center">
                      {{ nodeData.name }}
                      <v-tooltip
                        v-if="nodeData.name === 'To be decided during runtime'"
                        text="The reporting manager who is associated with the employee in the employee module will be considered"
                        location="top"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div v-bind="props">
                            <v-icon color="blue" class="ml-1"
                              >fas fa-info-circle</v-icon
                            >
                          </div>
                        </template>
                      </v-tooltip>
                    </div>
                    <div class="d-flex align-center justify-center">
                      <v-tooltip
                        v-if="nodeData.completedOn"
                        :text="nodeData.completedOn"
                        location="top"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div v-bind="props">
                            <v-icon class="ml-2" color="indigo-accent-2"
                              >fas fa-clock</v-icon
                            >
                          </div>
                        </template>
                      </v-tooltip>
                      <v-tooltip
                        v-if="nodeData.remarks"
                        :text="nodeData.remarks"
                        location="top"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div v-bind="props">
                            <v-icon class="ml-2" color="amber"
                              >fas fa-comment-alt</v-icon
                            >
                          </div>
                        </template>
                      </v-tooltip>
                      <div
                        v-if="
                          nodeData.formDetails && nodeData.status_id !== '1002'
                        "
                      >
                        <i
                          class="hr-workflow-task-management-form cursor-pointer pl-2"
                          style="color: #ff6666; font-size: 18px"
                          @click="openDynamicForm(nodeData.formDetails)"
                        ></i>
                      </div>
                    </div>
                  </div>
                </template>
              </organization-chart>
            </v-window-item>
          </v-window>
        </v-card-text>
      </div>
    </v-card>
    <AppLoading v-if="isLoading" />
  </div>
  <FilePreviewModal
    v-if="openDocumentModal"
    :fileName="retrievedFileName"
    folderName="Resignation Documents"
    fileRetrieveType="documents"
    @close-preview-modal="(openDocumentModal = false), (retrievedFileName = '')"
  />
</template>

<script>
import { defineAsyncComponent } from "vue";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";
import axios from "axios";
import OrganizationChart from "vue3-organization-chart";
import { GET_WORK_FLOW_VIEW } from "@/graphql/workflow/approvalManagementQueries.js";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "ViewExitManagement",
  components: {
    MoreDetails,
    OrganizationChart,
    FilePreviewModal,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
    isEmployee: {
      type: [Boolean],
      default: true,
    },
  },
  emits: [
    "close-form",
    "open-edit-form",
    "open-close-model",
    "open-dynamic-form",
  ],
  data() {
    return {
      viewFormTab: null,
      isLoadingCard: false,
      startNode: "",
      orgChartData: {},
      keyMap: [],
      isCalled: false,
      orgchartArray: [],
      moreDetailsList: [],
      openMoreDetails: true,
      selectedFormData: {},
      employeeCheck: null,
      isLoading: false,
      isFullFinalEnabled: false,
      openDocumentModal: false,
      retrievedFileName: "",
    };
  },

  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
  },

  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.selectedFormData = Object.assign({}, newData);
        this.prefillMoreDetails();
        this.viewFormTab = 1;
      },
    },
    viewFormTab: {
      handler(newData) {
        if (
          newData === 2 &&
          this.selectedItem?.workflowInstanceId &&
          this.isEmployee
        ) {
          this.fetchWorkflowData();
        }
      },
      immediate: true,
    },
  },
  mounted() {
    if (this.selectedItem?.workflowInstanceId && !this.isEmployee) {
      this.fetchWorkflowData();
    }
    if (
      this.isEmployee &&
      this.selectedItem?.resignationStatus?.toLowerCase() === "approved"
    ) {
      this.fetchResignationAjax();
    }
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    closeEditForm() {
      this.$emit("close-form");
    },
    extractFileName(filePath) {
      let fileName = filePath?.split("?")[2] || ""; // Extracting file name from `?`-separated format
      return fileName?.length > 25 ? fileName.slice(0, 22) + "..." : fileName;
    },
    retrieveFileContents(doc) {
      this.retrievedFileName = doc;
      this.openDocumentModal = true;
    },
    async fetchResignationAjax() {
      try {
        this.isLoading = true;
        let employeeId = this.selectedItem?.employeeId;
        let url =
          this.baseUrl +
          "default/employee-info/list-approver-details/employeeId/" +
          +employeeId +
          "/formName/Resignation/loginEmployeeId/" +
          this.loginEmployeeId;

        const response = await axios.get(url);
        if (
          response.data?.payslipGenerated &&
          response.data?.fullAndFinalSettlementInitiated
        ) {
          this.isFullFinalEnabled = true;
        }
      } catch (error) {
        this.handleAxiosError(error);
      } finally {
        this.isLoading = false;
      }
    },
    handleAxiosError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Resignation",
        isListError: true,
      });
    },
    fetchWorkflowData() {
      let vm = this;
      vm.isLoadingCard = true;
      vm.$apollo
        .query({
          query: GET_WORK_FLOW_VIEW,
          variables: {
            taskId: vm.selectedItem?.workflowInstanceId,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getWorkFlowView &&
            !response.data.getWorkFlowView.errorCode
          ) {
            var resultData = JSON.parse(response.data.getWorkFlowView.details);
            this.employeeCheck = resultData.nodeDetails.nodes;
            this.orgchartArray = [];
            this.keyMap = [];
            this.orgChartData = {};
            this.isCalled = false;
            this.empGroupData = resultData.empGroupData;
            this.prepareChartJson(resultData.nodeDetails);
            this.isLoadingCard = false;
          } else {
            vm.handleWorkflowDateError();
          }
        })
        .catch((err) => {
          vm.handleWorkflowDateError(err);
        });
    },
    handleWorkflowDateError(err = "") {
      this.isLoadingCard = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "resignation",
        isListError: true,
      });
    },
    prepareChartJson(workflowData) {
      this.startNode = workflowData["startNode"];
      this.prepareNode(workflowData.nodes[this.startNode], workflowData.nodes);
    },
    prepareNode(node, nodes) {
      for (let i = 0; i < node["next"].length; i++) {
        const currentNode = nodes[node["next"][i]];
        let userDetails = currentNode.data.completed_by
          ? currentNode.data.completed_by
          : currentNode.data.assignee;
        let values = this.empGroupData;
        if (!this.keyMap.includes(node["next"][i])) {
          this.keyMap.push(node["next"][i]);
          let parentNode = "";
          for (let objKey in nodes) {
            if (nodes[objKey].next.includes(node["next"][i])) {
              parentNode = nodes[objKey].nodeNo
                ? nodes[objKey].nodeNo
                : this.startNode;
              break;
            }
          }
          if (currentNode.type == "userTask") {
            if (currentNode.data.modalTaskData) {
              // Handle node with modalTaskData
              const modalData = currentNode.data.modalTaskData;
              let name = "";
              let values = this.empGroupData;

              // Determine name based on approver type
              if (
                modalData.typeOfApprove === "user" &&
                modalData.approveByUser
              ) {
                name =
                  values[`user${modalData.approveByUser.id}`]?.["Name"] ||
                  "Pending User";
              } else if (
                modalData.typeOfApprove === "group" &&
                modalData.approveByGroup
              ) {
                name = modalData.approveByGroup?.["text"] || "Pending Group";
              }

              this.orgchartArray.push({
                id: node["next"][i],
                customGroupId: modalData.approveByGroup
                  ? modalData.approveByGroup.id
                  : "",
                name: name,
                title: modalData.title || "Pending Approval",
                Parent: parentNode,
                iconName:
                  "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                remarks: currentNode.data.remarks || "",
                formDetails:
                  this.filterFormId === 34 &&
                  modalData.formId &&
                  modalData.formId.id !== "0" &&
                  parseInt(modalData.formId.id) > 0
                    ? {
                        formIdentifier: currentNode.data.form_identifier,
                        taskId: currentNode.data.task_id,
                        processInstanceId: currentNode.data.process_instance_id,
                      }
                    : "",
              });
            } else {
              //approved status
              if (userDetails && currentNode.data.status_id === "1002") {
                this.orgchartArray.push({
                  id: node["next"][i],
                  name: values[`user${userDetails}`]["Name"],
                  title: currentNode.data.description,
                  Parent: parentNode,
                  iconName:
                    "text-h3 hr-workflow-task-management-approvedtask text-green",
                  remarks: currentNode.data.remarks,
                  completedOn: currentNode?.data?.completed_date
                    ? this.formatDate(
                        new Date(currentNode.data.completed_date + "00Z"),
                        true,
                        true
                      )
                    : null,
                  formDetails:
                    currentNode.data.form_identifier &&
                    parseInt(currentNode.data.form_identifier) > 0
                      ? {
                          formIdentifier: currentNode.data.form_identifier,
                          taskId: currentNode.data.task_id,
                          processInstanceId:
                            currentNode.data.process_instance_id,
                        }
                      : "",
                });
              } else if (currentNode.data.status_id === "1007") {
                //rejected status
                this.orgchartArray.push({
                  id: node["next"][i],
                  name: values[`user${userDetails}`]["Name"],
                  title: currentNode.data.description,
                  Parent: parentNode,
                  iconName:
                    "text-h3 hr-workflow-task-management-reject text-red",
                  remarks: currentNode.data.remarks,
                  completedOn: currentNode?.data?.completed_date
                    ? this.formatDate(
                        new Date(currentNode.data.completed_date + "00Z"),
                        true,
                        true
                      )
                    : null,
                  formDetails:
                    currentNode.data.form_identifier &&
                    parseInt(currentNode.data.form_identifier) > 0
                      ? {
                          formIdentifier: currentNode.data.form_identifier,
                          taskId: currentNode.data.task_id,
                          processInstanceId:
                            currentNode.data.process_instance_id,
                        }
                      : "",
                });
              } else if (
                currentNode.data.status_id === "1001" ||
                currentNode.data.status_id === "1006"
              ) {
                // user task
                if (userDetails) {
                  if (
                    parseInt(this.loginEmployeeId) === parseInt(userDetails) ||
                    this.isAnyAdmin
                  ) {
                    this.orgchartArray.push({
                      id: node["next"][i],
                      name: values[`user${userDetails}`]["Name"],
                      title: currentNode.data.description,
                      Parent: parentNode,
                      iconName:
                        "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                      remarks: currentNode.data.remarks,
                      formDetails:
                        currentNode.data.form_identifier &&
                        parseInt(currentNode.data.form_identifier) > 0
                          ? {
                              formIdentifier: currentNode.data.form_identifier,
                              taskId: currentNode.data.task_id,
                              processInstanceId:
                                currentNode.data.process_instance_id,
                            }
                          : "",
                    });
                  } else {
                    this.orgchartArray.push({
                      id: node["next"][i],
                      name: values[`user${userDetails}`]["Name"],
                      title: currentNode.data.description,
                      Parent: parentNode,
                      iconName:
                        "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                      remarks: currentNode.data.remarks,
                    });
                  }
                }
                // group task
                else {
                  // Parse custom_group_id data, handle errors if it's not valid JSON or doesn't contain expected data
                  let groupData = {};
                  try {
                    groupData = JSON.parse(currentNode.data.custom_group_id); // Parse the custom_group_id string
                  } catch (error) {
                    groupData = null; // Set to null or handle it in some way if parsing fails
                  }

                  // Check if groupData is valid and contains the expected structure
                  const groupName =
                    groupData &&
                    groupData[`group${currentNode.data.custom_group_id}`]
                      ? groupData[`group${currentNode.data.custom_group_id}`][
                          "Name"
                        ]
                      : ""; // Fallback if Name is not found

                  this.orgchartArray.push({
                    id: node["next"][i],
                    name: groupName,
                    title: currentNode.data.description,
                    Parent: parentNode,
                    iconName:
                      "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                    remarks: currentNode.data.remarks,
                  });
                }
              } else {
                let empData = "";
                // check the type of approverUser
                if (currentNode.data.modalTaskData.typeOfApprove === "user") {
                  // check if the approverData is available
                  if (currentNode.data.modalTaskData.approveByUser) {
                    empData =
                      values[
                        `${currentNode.data.modalTaskData.typeOfApprove}${currentNode.data.modalTaskData.approveByUser.id}`
                      ];
                  } else {
                    empData = {
                      Name: "To be decided during runtime",
                    };
                  }
                } else {
                  empData =
                    values[
                      `${currentNode.data.modalTaskData.typeOfApprove}${currentNode.data.modalTaskData.approveByGroup.id}`
                    ];
                }
                this.orgchartArray.push({
                  id: node["next"][i],
                  name: empData["Name"],
                  title: currentNode.data.modalTaskData.title,
                  Parent: parentNode,
                  iconName:
                    "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                  remarks: currentNode.data.remarks,
                  formDetails:
                    currentNode.data.modalTaskData.formId.id != undefined &&
                    currentNode.data.modalTaskData.formId.id > 0
                      ? {
                          formIdentifier: currentNode.data.form_identifier,
                          taskId: currentNode.data.task_id,
                          processInstanceId:
                            currentNode.data.process_instance_id,
                        }
                      : "",
                });
              }
            }
          } else {
            this.orgchartArray.push({
              id: node["next"][i],
              name: "",
              title: "",
              Parent: parentNode,
              iconName: "text-h3 hr-workflow-approval-management text-primary",
              remarks: "",
            });
          }
          this.prepareNode(nodes[node["next"][i]], nodes);
        }
      }
      let nodeLength = Object.keys(nodes).length;
      nodeLength = nodeLength - 1;
      if (nodeLength === this.orgchartArray.length && !this.isCalled) {
        this.isCalled = true;
        let formedOrgChartData = this.formOrgChartData(this.orgchartArray);
        this.orgChartData =
          formedOrgChartData && formedOrgChartData.length > 0
            ? formedOrgChartData[0].children &&
              formedOrgChartData[0].children.length > 0
              ? formedOrgChartData[0].children[0]
              : []
            : [];
        this.isLoadingCard = false;
      }
    },
    formOrgChartData(items) {
      var tree = [],
        mappedArr = {};
      // Build a hash table and map items to objects
      items.forEach(function (item) {
        var id = item.id;
        // in case of duplicates
        if (!mappedArr.hasOwnProperty(id)) {
          mappedArr[id] = item; // the extracted id as key, and the item as value
          mappedArr[id].children = []; // under each item, add a key "children" with an empty array as value
        }
      });

      // If root-level nodes are not included in hash table, include them
      items.forEach(function (item) {
        var parentId = item.Parent;
        let nodeId =
          parentId === "taWorkStart" ? "taWorkStart" : "node_" + parentId;
        let nodeObj = mappedArr[nodeId];

        if (!mappedArr.hasOwnProperty(parentId)) {
          // make up an item for root-level node
          let newItem = {
            id: parentId,
            name: nodeObj && nodeObj.name ? nodeObj.name : "",
            title: nodeObj && nodeObj.title ? nodeObj.title : "",
            iconName: nodeObj && nodeObj.iconName ? nodeObj.iconName : "",
            // Parent: "",
            Parent: nodeObj && nodeObj.Parent ? nodeObj.Parent : "",
            children: [],
            remarks: nodeObj && nodeObj.remarks ? nodeObj.remarks : "",
            completedOn:
              nodeObj && nodeObj.completedOn ? nodeObj.completedOn : "",
            formDetails:
              nodeObj && nodeObj.formDetails ? nodeObj.formDetails : "",
          };
          mappedArr[parentId] = newItem; // the parent id as key, and made-up an item as value
          if (newItem.Parent) {
            delete mappedArr["node_" + parentId];
          }
        }
      });

      // Loop over hash table
      for (var id in mappedArr) {
        if (mappedArr.hasOwnProperty(id)) {
          let mappedElem = mappedArr[id];

          // If the element is not at the root level, add it to its parent array of children. Note this will continue till we have only root level elements left
          if (mappedElem.Parent) {
            var parentId = mappedElem.Parent;
            mappedArr[parentId].children.push(mappedElem);
          }

          // If the element is at the root level, directly push to the tree
          else {
            tree.push(mappedElem);
          }
        }
      }

      return tree;
    },
    openDynamicForm(formDetails) {
      // Find the task node in this.employeeCheck based on the task_id of nodeData
      const taskNode = Object.values(this.employeeCheck).find(
        (node) => node.data && node.data.task_id === formDetails.taskId
      );

      // Check if the taskNode exists, and the taskNode approveType is not "Approve"
      if (taskNode) {
        if (
          taskNode.data &&
          taskNode.data.assignee ==
            parseInt(this.$store.state.orgDetails.employeeId, 10)
        ) {
          // Emit the dynamic form event if all conditions are satisfied
          this.$emit(
            "open-dynamic-form",
            formDetails,
            this.selectedItem?.resignationId,
            taskNode.data
          );
        }
      }
    },

    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.selectedItem.addedOn,
        addedByName = this.selectedItem.addedUserName,
        updatedByName = this.selectedItem.updatedUserName,
        updatedOn = this.selectedItem.updatedOn;
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>

<style scoped>
@import "../../../assets/css/orgchart.css";
</style>
