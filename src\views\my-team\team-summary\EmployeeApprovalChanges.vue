<template>
  <div>
    <v-dialog
      :model-value="modelValue"
      fullscreen
      scrollable
      transition="dialog-bottom-transition"
    >
      <v-card>
        <v-toolbar>
          <CustomSelect
            v-model="carouselIndex"
            class="mx-2 mt-5"
            :items="sectionTitles"
            itemTitle="title"
            itemValue="value"
            label="Changes"
            :isRequired="true"
            :itemSelected="carouselIndex"
            :disableBreak="true"
          ></CustomSelect>
          <v-spacer></v-spacer>
          <v-btn
            v-if="!isApprovalHistory"
            color="success"
            @click="$emit('approve-reject', 'approve')"
            ><v-icon size="18" class="mt-1 mr-1">fas fa-check-circle</v-icon
            ><span v-if="!isMobileView">Approve</span></v-btn
          >
          <v-btn
            v-if="!isApprovalHistory"
            color="error"
            @click="$emit('approve-reject', 'reject')"
            ><v-icon size="18" class="mt-1 mr-1">fas fa-times-circle</v-icon
            ><span v-if="!isMobileView">Reject</span></v-btn
          >
          <v-btn icon @click="$emit('update:modelValue', false)">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-toolbar>
        <v-carousel
          v-model="carouselIndex"
          :hide-delimiters="!isMobileView"
          progress="primary"
          :show-arrows="!isMobileView ? 'hover' : false"
          height="90vh"
          class="pa-2"
        >
          <v-carousel-item
            v-for="(section, index) in dynamicSections"
            :key="index"
          >
            <div style="height: 100%; overflow-y: auto">
              <component
                :is="section.component"
                :ref="`section-${index}`"
                :[section.newDataProp]="section.data.New_Data"
                :[section.oldDataProp]="section.data.Old_Data"
                :isApprovalView="true"
              />
              <component
                v-if="section.component === 'TaxConfig'"
                :is="'ExpatDetails'"
                :ref="`section-expat-${index}`"
                :expat-details-data="section.data.New_Data"
                :old-expat-details-data="section.data.Old_Data"
                :isApprovalView="true"
              />
            </div>
          </v-carousel-item>
        </v-carousel>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import PersonalDetails from "../../employee-profile/profile-details/personal/details/PersonalDetails.vue";
import JobDetails from "../../employee-profile/profile-details/job/details/JobDetails.vue";
import DependentDetails from "../../employee-profile/profile-details/personal/dependent/DependentDetails.vue";
import PassportDetails from "../../employee-profile/profile-details/personal/passport/PassportDetails.vue";
import DrivingLicenseDetails from "../../employee-profile/profile-details/personal/license/DrivingLicenseDetails.vue";
import ContactInfo from "../../employee-profile/profile-details/contact/ContactInfo.vue";
import ExperienceDetails from "../../employee-profile/profile-details/job/experience-details/ExperienceDetails.vue";
import AssetDetails from "../../employee-profile/profile-details/job/asset-details/AssetDetails.vue";
import SkillDetails from "../../employee-profile/profile-details/career/skills/SkillDetails.vue";
import EducationDetails from "../../employee-profile/profile-details/career/education/ViewEducationDetails.vue";
import CertificateDetails from "../../employee-profile/profile-details/career/certification/ViewCertificationDetails.vue";
import TrainingDetails from "../../employee-profile/profile-details/career/training/ViewTrainingDetails.vue";
import AwardDetails from "../../employee-profile/profile-details/career/awards/ViewAwardsDetails.vue";
import BankDetails from "../../employee-profile/profile-details/others/bank/ViewBankDetails.vue";
import InsuranceDetails from "../../employee-profile/profile-details/others/insurance/ViewInsurance.vue";
import DocumentDetails from "../../employee-profile/profile-details/document-accreditations/document/ViewDocuments.vue";
import AccreditationDetails from "../../employee-profile/profile-details/document-accreditations/accreditations/ViewAccreditationDetails.vue";
import BondRecovery from "../../employee-profile/profile-details/pay-config/BondRecovery.vue";
import OverTime from "../../employee-profile/profile-details/pay-config/OverTime.vue";
import Retirals from "../../employee-profile/profile-details/pay-config/Retirals.vue";
import TaxConfig from "../../employee-profile/profile-details/pay-config/TaxConfig.vue";
import ExpatDetails from "../../employee-profile/profile-details/pay-config/ExpatDetails.vue";
import AirTicketPolicy from "../../employee-profile/profile-details/air-ticket-policy/air-ticket/AirTicketPolicyView.vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "EmployeeApprovalChanges",
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    instanceData: {
      type: Object,
      default: () => ({}),
    },
    isApprovalHistory: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    PersonalDetails,
    JobDetails,
    DependentDetails,
    PassportDetails,
    DrivingLicenseDetails,
    ContactInfo,
    ExperienceDetails,
    AssetDetails,
    SkillDetails,
    EducationDetails,
    CertificateDetails,
    AwardDetails,
    TrainingDetails,
    BankDetails,
    InsuranceDetails,
    DocumentDetails,
    AccreditationDetails,
    BondRecovery,
    OverTime,
    Retirals,
    TaxConfig,
    ExpatDetails,
    AirTicketPolicy,
    CustomSelect,
  },
  data() {
    return {
      carouselIndex: 0,
      sectionTitles: [],
      dynamicSections: [],
      multipleDataSections: [
        "emp_dependent",
        "emp_experience",
        "emp_assets",
        "emp_education",
        "emp_awards",
        "emp_training",
        "emp_certificates",
        "emp_bankdetails",
        "emp_insurancepolicyno",
        "emp_document_category",
        "employee_accreditation_details",
      ],
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    sectionConfig() {
      return {
        emp_personal_info: {
          component: "PersonalDetails",
          title: "Personal Details",
          newDataProp: "personalDetailsData",
          oldDataProp: "oldPersonalDetailsData",
        },
        emp_job: {
          component: "JobDetails",
          title: "Job Details",
          newDataProp: "jobDetailsData",
          oldDataProp: "oldJobDetailsData",
        },
        emp_dependent: {
          component: "DependentDetails",
          title: "Dependent Details",
          newDataProp: "dependentDetailsData",
          oldDataProp: "oldDependentDetailsData",
        },
        emp_passport: {
          component: "PassportDetails",
          title: "Passport Details",
          newDataProp: "passportDetailsData",
          oldDataProp: "oldPassportDetailsData",
        },
        emp_drivinglicense: {
          component: "DrivingLicenseDetails",
          title: "Driving License Details",
          newDataProp: "licenseDetailsData",
          oldDataProp: "oldDrivingLicenseDetailsData",
        },
        contact_details: {
          component: "ContactInfo",
          title: "Contact Details",
          newDataProp: "contactDetailsData",
          oldDataProp: "oldContactDetailsData",
        },
        emp_experience: {
          component: "ExperienceDetails",
          title: "Experience Details",
          newDataProp: "experienceDetailsData",
          oldDataProp: "oldExperienceDetailsData",
        },
        emp_assets: {
          component: "AssetDetails",
          title: "Asset Details",
          newDataProp: "assetDetailsData",
          oldDataProp: "oldAssetDetailsData",
        },
        emp_skillset: {
          component: "SkillDetails",
          title: "Skill Details",
          newDataProp: "skillDetails",
          oldDataProp: "oldSkillDetails",
        },
        emp_education: {
          component: "EducationDetails",
          title: "Education Details",
          newDataProp: "educationDetails",
          oldDataProp: "oldEducationDetails",
        },
        emp_certifications: {
          component: "CertificateDetails",
          title: "Certificate Details",
          newDataProp: "certificationDetails",
          oldDataProp: "oldCertificationDetails",
        },
        emp_training: {
          component: "TrainingDetails",
          title: "Training Details",
          newDataProp: "trainingDetails",
          oldDataProp: "oldTrainingDetails",
        },
        emp_awards: {
          component: "AwardDetails",
          title: "Award Details",
          newDataProp: "awardDetails",
          oldDataProp: "oldAwardDetails",
        },
        emp_bankdetails: {
          component: "BankDetails",
          title: "Bank Details",
          newDataProp: "bankDetails",
          oldDataProp: "oldBankDetails",
        },
        emp_insurancepolicyno: {
          component: "InsuranceDetails",
          title: "Insurance Details",
          newDataProp: "insuranceDetails",
          oldDataProp: "oldInsuranceDetails",
        },
        emp_document_category: {
          component: "DocumentDetails",
          title: "Documents",
          newDataProp: "documentDetails",
          oldDataProp: "oldDocumentDetails",
        },
        employee_accreditation_details: {
          component: "AccreditationDetails",
          title: this.labelList[229]?.Field_Alias || "Accreditations",
          newDataProp: "accreditationDetails",
          oldDataProp: "oldAccreditationDetails",
        },
        bond_recovery_details: {
          component: "BondRecovery",
          title: "Bond Recovery Details",
          newDataProp: "bondRecoveryData",
          oldDataProp: "oldBondRecoveryData",
        },
        overtime_details: {
          component: "OverTime",
          title: "Overtime Details",
          newDataProp: "overtimeData",
          oldDataProp: "oldOvertimeData",
        },
        retiral_details: {
          component: "Retirals",
          title: "Retirals Details",
          newDataProp: "retiralsData",
          oldDataProp: "oldRetiralsData",
        },
        tax_details: {
          component: "TaxConfig",
          title: "Tax Details",
          newDataProp: "tdsData",
          oldDataProp: "oldTDSContractorData",
        },
        emp_air_ticket_policy: {
          component: "AirTicketPolicy",
          title: "Air Ticket Policy",
          newDataProp: "airTicketDetails",
          oldDataProp: "oldAirTicketData",
        },
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.initializeSections();
  },
  methods: {
    initializeSections() {
      if (!this.instanceData?.Request_Items?.length) return;

      this.instanceData.Request_Items.forEach((item) => {
        const tableName =
          item.Table_Name && item.Table_Name.toLowerCase
            ? item.Table_Name.toLowerCase()
            : null;
        if (!tableName) return;
        const config = this.sectionConfig[tableName];
        if (!config) return;

        const existingSection = this.dynamicSections.find(
          (section) => section.component === config.component
        );

        const newDataParsed = safeParse(
          item.New_Data,
          this.multipleDataSections.includes(tableName) ? [] : {}
        );
        const oldDataParsed = safeParse(
          item.Old_Data,
          this.multipleDataSections.includes(tableName) ? [] : {}
        );

        if (this.multipleDataSections.includes(tableName) && existingSection) {
          const changeType =
            item.Change_Type && item.Change_Type.toLowerCase
              ? item.Change_Type.toLowerCase()
              : null;
          if (changeType === "add") {
            const lastData = newDataParsed[newDataParsed.length - 1];
            existingSection.data.New_Data.push(lastData);
          } else if (changeType === "edit") {
            const updatedId = item.Reference_Id;
            let idField = "Dependent_Id"; // Default for dependent details

            // Determine the ID field based on table name
            if (tableName === "emp_assets") {
              idField = "Asset_Id";
            } else if (tableName === "emp_experience") {
              idField = "Experience_Id";
            } else if (tableName === "emp_education") {
              idField = "Education_Id";
            } else if (tableName === "emp_certifications") {
              idField = "Certification_Id";
            } else if (tableName === "emp_training") {
              idField = "Training_Id";
            } else if (tableName === "emp_awards") {
              idField = "Award_Id";
            } else if (tableName === "emp_bankdetails") {
              idField == "Bank_Id";
            } else if (tableName === "emp_insurancepolicyno") {
              idField == "Policy_Id";
            } else if (tableName === "emp_document_category") {
              idField == "Document_Id";
            } else if (tableName === "employee_accreditation_details") {
              idField == "Accreditation_Detail_Id";
            }

            const updatedData = newDataParsed.find(
              (data) => data[idField] === updatedId
            );
            if (updatedData) {
              existingSection.data.New_Data.forEach((data) => {
                if (data[idField] === updatedId) {
                  Object.assign(data, updatedData);
                }
              });
            }
          }
        } else {
          if (!this.sectionTitles.some((t) => t.title === config.title)) {
            this.sectionTitles.push({
              title: config.title,
              value: this.dynamicSections.length,
            });
          }

          this.dynamicSections.push({
            component: config.component,
            title: config.title,
            newDataProp: config.newDataProp,
            oldDataProp: config.oldDataProp,
            data: {
              New_Data: newDataParsed,
              Old_Data: oldDataParsed,
            },
          });
        }
      });

      function safeParse(str, fallback) {
        try {
          return JSON.parse(str || "");
        } catch {
          return fallback;
        }
      }
    },
  },
};
</script>

<style scoped>
:deep(.v-window__controls .v-btn--icon) {
  opacity: 0.5;
  transition: opacity 0.2s;
  background-color: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  min-width: 0 !important;
  width: auto !important;
  height: auto !important;
  border-radius: 0 !important;
}

:deep(.v-window__controls .v-btn--icon:hover),
:deep(.v-window__controls .v-btn--icon:focus) {
  opacity: 1;
  background-color: transparent !important;
  box-shadow: none !important;
}

:deep(.v-window__controls .v-btn--icon .v-icon) {
  color: #260029 !important; /* Or your preferred color */
  font-size: 2rem; /* Optional: adjust icon size */
}
</style>
