<template>
  <RoleAccessRights
    v-if="showRolesAssociation"
    :selectedItem="selectedRole"
    action-type="associate"
    :employeeIds="selectedEmpIds"
    @close-form="showRolesAssociation = false"
    @refetch-list="onAssociateSuccess()"
  ></RoleAccessRights>
  <div v-else-if="itemList.length > 0 && !showImportModal">
    <div
      class="d-flex flex-wrap align-center my-2"
      :class="isMobileView ? 'flex-column' : ''"
      style="justify-content: space-between"
    >
      <div
        class="d-flex align-center flex-wrap"
        :class="isMobileView ? 'justify-center' : ''"
      >
        <v-btn
          rounded="lg"
          style="pointer-events: none"
          variant="flat"
          :size="windowWidth <= 960 ? 'small' : 'default'"
          >Active:
          <span class="text-green font-weight-bold">{{ activeEmployees }}</span>
          ( Draft:
          <span class="text-purple font-weight-bold">{{ draftEmployees }}</span
          >)
        </v-btn>
        <v-btn
          rounded="lg"
          style="pointer-events: none"
          variant="flat"
          class="ml-2"
          :size="windowWidth <= 960 ? 'small' : 'default'"
          >Inactive:
          <span class="text-red font-weight-bold">{{
            inactiveEmployees
          }}</span></v-btn
        >
      </div>
      <div
        class="d-flex align-center"
        :class="isMobileView ? 'justify-center' : 'justify-end'"
      >
        <v-btn
          v-if="
            formAccess &&
            formAccess.add &&
            formAccess.admin === 'admin' &&
            !isPayrollAdmin
          "
          variant="elevated"
          class="mt-1 secondary"
          rounded="lg"
          :size="windowWidth <= 960 ? 'small' : 'default'"
          @click="$emit('add-employee')"
        >
          <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
          <span class="primary">Add Employee</span>
        </v-btn>
        <v-btn
          v-if="isSuperAdmin"
          color="white"
          rounded="lg"
          class="ml-1 mt-1"
          @click="onAssignRoles()"
          :size="windowWidth <= 960 ? 'small' : 'default'"
          >Assign Roles</v-btn
        >
        <v-btn
          color="transparent"
          class="ml-1 mt-1"
          variant="flat"
          size="small"
          @click="$emit('refetch-list')"
          ><v-icon color="grey">fas fa-redo-alt</v-icon></v-btn
        >
        <v-menu v-model="openMoreMenu" transition="scale-transition">
          <template v-slot:activator="{ props }">
            <v-btn variant="plain" class="mt-1 ml-n3 mr-n5" v-bind="props">
              <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
              <v-icon v-else>fas fa-caret-up</v-icon>
            </v-btn>
          </template>
          <v-list>
            <v-list-item
              v-for="action in moreActions"
              :key="action.key"
              @click="onMoreAction(action.key)"
            >
              <v-hover>
                <template v-slot:default="{ isHovering, props }">
                  <v-list-item-title
                    v-bind="props"
                    class="pa-3"
                    :class="{
                      'pink-lighten-5': isHovering,
                    }"
                    ><v-icon size="15" class="pr-2">{{ action.icon }}</v-icon
                    >{{ action.key }}</v-list-item-title
                  >
                </template>
              </v-hover>
            </v-list-item>
          </v-list>
        </v-menu>
      </div>
    </div>
    <v-data-table
      v-model="selectedEmpRecords"
      :headers="tableHeaders"
      :items="itemList"
      :items-per-page="50"
      :show-select="!isMobileView && isSuperAdmin"
      :sort-by="[{ key: 'employeeId', order: 'asc' }]"
      :height="
        $store.getters.getTableHeightBasedOnScreenSize(250, itemList, true)
      "
      :items-per-page-options="[
        { value: 50, title: '50' },
        { value: 100, title: '100' },
        { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
      ]"
      fixed-header
      item-value="employeeId"
    >
      <template v-slot:[`header.data-table-select`]="{ selectAll }">
        <v-checkbox-btn
          v-model="selectAllBox"
          color="primary"
          false-icon="far fa-circle"
          true-icon="fas fa-check-circle"
          indeterminate-icon="fas fa-minus-circle"
          class="mt-1"
          @change="selectAll(selectAllBox)"
        ></v-checkbox-btn>
      </template>
      <template v-slot:item="{ item }">
        <tr
          @click="onSelectItem(item)"
          class="data-table-tr bg-white cursor-pointer"
          :class="
            isMobileView ? 'v-data-table__mobile-table-row ma-0 mt-2' : ''
          "
        >
          <td v-if="isSuperAdmin" :class="isMobileView ? 'mt-3 mb-n5' : ''">
            <v-checkbox-btn
              v-model="item.isSelected"
              color="primary"
              false-icon="far fa-circle"
              true-icon="fas fa-check-circle"
              class="mt-n2 ml-n2"
              @click.stop="
                {
                }
              "
              @change="checkAllSelected()"
            ></v-checkbox-btn>
          </td>
          <td
            id="mobile-view-td"
            style="position: sticky; left: 0"
            class="bg-white"
          >
            <div id="mobile-header" class="font-weight-bold mt-2">
              Employee Name
            </div>
            <section style="max-width: 200px" class="text-truncate">
              <span class="text-primary text-body-2 font-weight-medium">
                <v-tooltip :text="item.employeeName" location="bottom">
                  <template v-slot:activator="{ props }">
                    <span
                      v-bind="
                        item.employeeName && item.employeeName.length > 20
                          ? props
                          : ''
                      "
                      ><span v-if="item.salutation">{{
                        item.salutation + ". "
                      }}</span
                      >{{ item.employeeName }}</span
                    >
                  </template>
                </v-tooltip>
                <v-tooltip :text="item.userDefinedEmpId" location="bottom">
                  <template v-slot:activator="{ props }">
                    <div
                      v-if="item.userDefinedEmpId"
                      v-bind="
                        item.userDefinedEmpId &&
                        item.userDefinedEmpId.length > 20
                          ? props
                          : ''
                      "
                      class="text-grey"
                    >
                      {{ item.userDefinedEmpId }}
                    </div>
                  </template>
                </v-tooltip>
              </span>
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">
              Designation
            </div>
            <v-tooltip
              :text="item.designationName"
              location="bottom"
              max-width="400"
            >
              <template v-slot:activator="{ props }">
                <section
                  style="max-width: 200px"
                  class="text-truncate text-body-2 text-primary"
                  v-bind="props"
                >
                  {{ checkNullValue(item.designationName) }}
                </section>
              </template>
            </v-tooltip>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">
              Department
            </div>
            <v-tooltip
              :text="item.departmentName"
              location="bottom"
              max-width="400"
            >
              <template v-slot:activator="{ props }">
                <section
                  style="max-width: 150px"
                  class="text-truncate text-body-2 text-primary"
                  v-bind="props"
                >
                  {{ checkNullValue(item.departmentName) }}
                </section>
              </template>
            </v-tooltip>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">
              {{ dojHeader }}
            </div>
            <section class="text-body-2 text-primary">
              {{ checkNullValue(item.dateOfJoin) }}
            </section>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Location</div>
            <v-tooltip
              :text="item.locationName"
              location="bottom"
              max-width="400"
            >
              <template v-slot:activator="{ props }">
                <section
                  style="max-width: 150px"
                  class="text-truncate text-body-2 text-primary"
                  v-bind="props"
                >
                  {{ checkNullValue(item.locationName) }}
                </section>
              </template>
            </v-tooltip>
          </td>
          <td id="mobile-view-td">
            <div id="mobile-header" class="font-weight-bold mt-2">Status</div>
            <section class="d-flex align-center justify-space-between">
              <section
                class="text-body-2 font-weight-bold"
                :class="
                  item.empStatusAll == 'Draft'
                    ? 'text-purple'
                    : item.empStatusAll === 'Active'
                    ? 'text-green'
                    : 'text-red'
                "
              >
                {{ checkNullValue(item.empStatusAll) }}
              </section>
              <!-- <v-btn
                v-if="formAccess && formAccess.delete && !isPayrollAdmin && !isMobileView"
                icon="fas fa-trash-alt"
                size="x-small"
                color="red"
                @click.stop="onDelete(item)"
              >
                <v-icon color="white"></v-icon>
              </v-btn> -->
              <v-btn
                v-if="
                  formAccess &&
                  formAccess.add &&
                  formAccess.admin === 'admin' &&
                  !isPayrollAdmin &&
                  item.empStatusAll === 'InActive'
                "
                size="small"
                color="blue"
                variant="outlined"
                rounded="lg"
                @click.stop="onClone(item)"
              >
                <v-icon color="blue" class="mr-1">fas fa-clone</v-icon>
                Clone
              </v-btn>
            </section>
          </td>
        </tr>
      </template>
    </v-data-table>
  </div>
  <div v-else-if="showImportModal">
    <ImportEmployeeData
      v-if="showImportModal"
      @close-modal="showImportModal = false"
    ></ImportEmployeeData>
  </div>
  <AppFetchErrorScreen
    v-else
    key="no-results-screen"
    main-title="There are no team members matched for the selected filters/searches."
    image-name="common/no-records"
  >
    <template #contentSlot>
      <div style="max-width: 80%">
        <v-row class="rounded-lg pa-5 mb-4">
          <v-col cols="12" class="d-flex align-center justify-center mb-4">
            <v-btn
              color="primary"
              variant="elevated"
              class="ml-4 mt-1"
              rounded="lg"
              :size="windowWidth <= 960 ? 'small' : 'default'"
              @click="$emit('reset-filter')"
            >
              Reset Filter/Search
            </v-btn>
          </v-col>
        </v-row>
      </div>
    </template>
  </AppFetchErrorScreen>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    iconName="fas fa-trash"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="confirmDelete()"
  ></AppWarningModal>
  <CloneEmployeeModal
    v-if="openCloneModal"
    :open-modal="openCloneModal"
    :selected-employee="selectedActionItem"
    @close-modal="onCloseWarningModal"
    @employee-cloned="onCloneSuccess()"
    @employee-clone-failed="$emit('refetch-list')"
  ></CloneEmployeeModal>
  <ExportEmployeeDataDialog
    :open-dialog="openExportMenu"
    screen="summary"
    :formId="243"
    @close-modal="openExportMenu = false"
  >
  </ExportEmployeeDataDialog>
  <v-dialog
    v-model="openRolesSelectionPopup"
    @click:outside="openRolesSelectionPopup = false"
    max-width="500px"
  >
    <v-card min-height="400" color="primary" class="rounded-lg">
      <v-card-title>
        <div class="d-flex justify-end" style="width: 100%">
          <v-icon
            color="white"
            size="20"
            class="mr-2 mt-2"
            @click="openRolesSelectionPopup = false"
            >fas fa-times</v-icon
          >
        </div>
      </v-card-title>
      <div
        class="d-flex justify-center align-center text-white font-weight-bold text-h5"
        style="margin-top: 20%"
      >
        <v-progress-circular
          model-value="100"
          color="yellow"
          :size="22"
          class="mr-2"
        ></v-progress-circular>
        Assign Roles
      </div>
      <v-card-text class="d-flex justify-center">
        <CustomSelect
          :items="rolesList"
          placeholder="Select Roles"
          :itemSelected="selectedRoleId"
          itemValue="Roles_Id"
          itemTitle="Roles_Name"
          style="max-width: 300px; height: 50px"
          @selected-item="onSelectRoles($event)"
        ></CustomSelect>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import { checkNullValue, getCustomFieldName } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
const CloneEmployeeModal = defineAsyncComponent(() =>
  import("./CloneEmployeeModal.vue")
);
import ExportEmployeeDataDialog from "./employee-data-import/ExportEmployeeDataDialog.vue";
import ImportEmployeeData from "./employee-data-import/ImportEmployeeData.vue";
const RoleAccessRights = defineAsyncComponent(() =>
  import("../../settings/coreHr/roles/RoleAccessRights.vue")
);
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default defineComponent({
  name: "ListMyTeam",

  emits: ["on-select-item", "reset-filter", "add-employee", "refetch-list"],
  mixins: [FileExportMixin],

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    originalList: {
      type: Array,
      required: true,
      default: () => [],
    },
    formAccess: {
      type: Object,
      required: true,
    },
    isPayrollAdmin: {
      type: Boolean,
      default: false,
    },
  },

  components: {
    CloneEmployeeModal,
    ExportEmployeeDataDialog,
    ImportEmployeeData,
    RoleAccessRights,
    CustomSelect,
  },

  data: () => ({
    // list
    itemList: [],
    openMoreMenu: false,
    openExportMenu: false,
    showImportModal: false,
    selectAllBox: false,
    selectedEmpRecords: [],
    // role
    selectedEmpIds: [],
    selectedRoleId: null,
    selectedRole: {},
    rolesList: [],
    showRolesAssociation: false,
    openRolesSelectionPopup: false,
    // delete/warning
    openWarningModal: false,
    selectedActionItem: null,

    // clone
    openCloneModal: false,

    resetFilterCount: 0,
    applyFilterCount: 0,
    isLoading: false,
  }),
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    windowWidth() {
      return this.$store.state.windowWidth;
    },
    dojHeader() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      return "Date of Join (" + orgDateFormat + ")";
    },
    tableHeaders() {
      return [
        {
          title: "Employee Name",
          key: "employeeName",
          fixed: true,
        },
        {
          title: "Designation",
          key: "designationName",
        },
        { title: "Department", key: "departmentName" },
        { title: this.dojHeader, key: "dateOfJoinWithoutFormat" },
        { title: "Location", key: "locationName" },
        { title: "Status", key: "empStatusAll" },
      ];
    },
    activeEmployees() {
      let empList = this.originalList.filter(
        (el) => el.empStatusAll === "Active"
      );
      return empList.length;
    },
    draftEmployees() {
      let empList = this.originalList.filter(
        (el) => el.empStatusAll === "Draft"
      );
      return empList.length;
    },
    inactiveEmployees() {
      let empList = this.originalList.filter(
        (el) => el.empStatusAll === "InActive"
      );
      return empList.length;
    },
    moreActions() {
      let actions = [
        {
          key: "Export employees",
          icon: "fas fa-file-export",
        },
      ];
      if (this.formAccess && this.formAccess.view && this.isAdmin) {
        actions.push({
          key: "Export employees with filter",
          icon: "fas fa-file-export",
        });
      }
      if (this.formAccess && this.formAccess.update && this.isAdmin) {
        actions.push({
          key: "Import employees",
          icon: "fas fa-file-import",
        });
      }
      return actions;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return true;
      } else return false;
    },
    isAdmin() {
      let formAccessRights = this.accessRights("22");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["update"]
      ) {
        return true;
      } else return false;
    },
    selectedItems() {
      let selected = this.itemList.filter((el) => el.isSelected === true);
      return selected && selected.length > 0 ? selected : [];
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (this.items.length) {
      this.itemList = this.items;
      this.itemList = this.itemList.map((item) => ({
        ...item,
        isSelected: false,
      }));
      this.onApplySearch();
    }
  },

  watch: {
    selectedEmpRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.employeeId)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through itemList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
      }
    },
    items(val) {
      this.itemList = val;
      this.onApplySearch();
    },
    searchValue() {
      this.onApplySearch();
    },
  },

  methods: {
    checkNullValue,
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    onSelectItem(item) {
      this.$emit("on-select-item", item);
    },
    onApplySearch(itemValues) {
      let val = this.searchValue;
      let listItems = itemValues ? itemValues : this.items;
      if (!val) {
        this.itemList = listItems;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = listItems;
        searchItems = searchItems.filter((item) => {
          let newObj = {
            userDefinedEmpId: item.userDefinedEmpId,
            designationName: item.designationName,
            departmentName: item.departmentName,
            dateOfJoin: item.dateOfJoin,
            locationName: item.locationName,
            employeeName: item.employeeName,
            empStatusAll: item.empStatusAll,
            Emp_Email: item.Emp_Email,
          };
          return Object.keys(newObj).some((k) => {
            if (
              item[k] &&
              item[k].toString().toLowerCase().includes(searchValue)
            ) {
              return true; // Include if search value is found in the current key's value
            } else {
              return false;
            }
          });
        });
        this.itemList = searchItems;
      }
    },
    onDelete(item) {
      this.openWarningModal = true;
      this.selectedActionItem = item;
    },

    onClone(item) {
      this.openCloneModal = true;
      this.selectedActionItem = item;
      mixpanel.track("MyTeam-clone-btn-click");
    },

    //function to delete single record
    confirmDelete() {
      this.openWarningModal = false;
    },

    // function close the warning modal
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.openCloneModal = false;
      this.selectedActionItem = null;
    },
    onMoreAction(actionType) {
      if (actionType === "Export employees") {
        mixpanel.track("MyTeam-export-employees-click");
        this.exportReportFile();
      } else if (actionType === "Export employees with filter") {
        mixpanel.track("MyTeam-export-employees-with-filter-click");
        this.openExportMenu = true;
      } else if (actionType === "Import employees") {
        mixpanel.track("MyTeam-import-employees-click");
        this.showImportModal = true;
      }
      this.openMoreMenu = false;
    },
    onCloneSuccess() {
      this.onCloseWarningModal();
      mixpanel.track("MyTeam-clone-success");
      this.$emit("refetch-list", true);
    },

    onAssociateSuccess() {
      this.showRolesAssociation = false;
      this.selectAllBox = false;
      this.itemList = this.itemList.map((item) => {
        item["isSelected"] = false;
        return item;
      });
      mixpanel.track("MyTeam-role-assign-success");
    },

    onSelectRoles(value) {
      this.openRolesSelectionPopup = false;
      this.selectedRoleId = value;
      this.selectedRole = this.rolesList.filter((el) => el.Roles_Id === value);
      this.selectedRole = this.selectedRole[0];
      let empIds = [];
      for (let emp of this.selectedItems) {
        empIds.push(emp.employeeId);
      }
      this.selectedEmpIds = empIds;
      this.showRolesAssociation = true;
      mixpanel.track("MyTeam-role-selection");
    },

    onAssignRoles() {
      mixpanel.track("MyTeam-role-assign-btn-click");
      let selected = this.selectedItems;
      if (selected && selected.length > 0) {
        this.fetchRolesList();
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please select at least one record to proceed",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },

    fetchRolesList() {
      let vm = this;
      vm.isLoading = true;
      vm.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            let { roles } = res.data.getDropDownBoxDetails;
            vm.rolesList = roles;
            vm.selectedRoleId = "";
            vm.openRolesSelectionPopup = true;
            vm.isLoading = false;
          } else {
            vm.handleRolesListError();
          }
        })
        .catch((err) => {
          vm.handleRolesListError(err);
        });
    },

    handleRolesListError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "roles",
        isListError: false,
      });
    },

    exportReportFile() {
      let exportHeaders = [
        {
          header: "Employee Id",
          key: "userDefinedEmpId",
        },
        {
          header: "First Name",
          key: "empNameWithSalutation",
        },
        {
          header: "Middle Name",
          key: "employeeMiddleName",
        },
        {
          header: "Last Name",
          key: "employeeLastName",
        },
        {
          header: "Biometric Integration Id",
          key: "externalEmpId",
        },
        { header: "Date of Birth", key: "dateOfBirth" },
        { header: "Gender", key: "gender" },
        { header: "Marital Status", key: "maritalStatusName" },
        { header: "Blood Group", key: "bloodGroup" },
        { header: "Personal Email", key: "personalEmail" },
        { header: "Mobile Number", key: "mobileNo" },
        { header: "Work Email", key: "Emp_Email" },
        { header: this.dojHeader, key: "dateOfJoin" },
        { header: "Is Recruiter", key: "isRecruiter" },
        { header: "Is Manager", key: "isManager" },
        {
          header: "Designation",
          key: "designationName",
        },
        { header: "Department", key: "departmentName" },
        { header: "Location", key: "locationName" },
        { header: "Work Schedule", key: "workScheduleName" },
        { header: "Employee Type", key: "employeeType" },
        { header: "Business Unit / Cost Center", key: "businessUnitName" },
      ];
      if (this.fieldForce) {
        exportHeaders.push({
          header: getCustomFieldName(115, "Service Provider"),
          key: "Service_Provider_Name",
        });
      }
      exportHeaders.push(
        { header: "Probation Date", key: "probationDateFormatted" },
        { header: "Confirmation Date", key: "confirmationDate" },
        { header: "Allow User Signin", key: "allowUserSignin" },
        { header: "Roles", key: "rolesName" },
        { header: this.labelList[213].Field_Alias, key: "PAN" },
        { header: "Status", key: "empStatusAll" }
      );

      let exportOptions = {
        fileExportData: this.itemList,
        fileName: "My Team Members",
        sheetName: "My Team Members",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
      mixpanel.track("MyTeam-employees-exported");
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>
