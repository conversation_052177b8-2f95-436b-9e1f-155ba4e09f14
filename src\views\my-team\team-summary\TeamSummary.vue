<template>
  <div>
    <AppTopBarTab
      v-if="mainTabs.length > 0"
      :tabs-list="mainTabs"
      :show-bottom-sheet="!listLoading && !showViewEditForm"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row v-show="!listLoading && !showViewEditForm">
          <v-col
            cols="12"
            class="d-flex"
            :class="myTeamListBackup.length > 0 ? '' : 'justify-end'"
            style="margin-left: -108px"
          >
            <EmployeeDefaultFilterMenu
              v-if="myTeamListBackup.length > 0"
              class="justify-end"
              :reset-filter-count="resetFilterCount"
              :appliedFilterCount="appliedFilterCount"
              :list-items="myTeamListBackup"
              :isApplyFilter="true"
              departmentIdKey="departmentId"
              designationIdKey="designationId"
              locationIdKey="locationId"
              empTypeIdKey="empTypeId"
              workScheduleIdKey="workSchedule"
              rolesIdKey="rolesId"
              :showServiceProvider="true"
              :showRoles="true"
              @reset-emp-filter="resetFilter('filter')"
              @applied-filter="applyFilter($event)"
            >
              <template #bottom-filter-menu>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedIsManagerFlag"
                    color="primary"
                    :items="['Yes', 'No']"
                    label="Is Manager"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                  >
                  </v-select>
                </v-col>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedIsRecruiterFlag"
                    color="primary"
                    :items="['Yes', 'No']"
                    label="Is Recruiter"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                  >
                  </v-select>
                </v-col>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedStatus"
                    color="primary"
                    :items="['Active', 'InActive']"
                    label="Employee Status"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                  >
                  </v-select>
                </v-col>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedFormStatus"
                    color="primary"
                    :items="[
                      { title: 'Completed', value: 1 },
                      { title: 'Draft', value: 0 },
                    ]"
                    label="Form Status"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                  >
                  </v-select>
                </v-col>
              </template>
            </EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <v-container fluid class="team-container">
      <section v-if="checkAccess">
        <v-window v-model="currentTabItem">
          <v-window-item value="tab-0">
            <div v-if="listLoading" class="mt-3">
              <v-skeleton-loader
                ref="skeleton1"
                type="table-heading"
                class="mx-auto"
              ></v-skeleton-loader>
              <div v-for="i in 3" :key="i" class="mt-4">
                <v-skeleton-loader
                  ref="skeleton2"
                  type="list-item-avatar"
                  class="mx-auto"
                ></v-skeleton-loader>
              </div>
            </div>
            <div v-else-if="isErrorInList">
              <AppFetchErrorScreen
                image-name="common/common-error-image"
                :content="errorContent"
                icon-name="fas fa-redo-alt"
                button-text="Retry"
                :isSmallImage="true"
                @button-click="refetchAPIs()"
              >
              </AppFetchErrorScreen>
            </div>
            <v-row v-else>
              <v-col v-if="showViewEditForm" cols="12">
                <ProfileTopCard
                  :selectedEmpId="selectedEmpId"
                  :formAccess="myTeamAccess"
                  :selectedEmpStatus="selectedEmpStatus"
                  :actionType="actionType"
                  :updateCount="updateCount"
                  :selectedEmployeeDetails="selectedEmployeeDetails"
                  callingFrom="team"
                  :myTeamList="myTeamList"
                  @close-profile="closeViewEditForm()"
                  @form-submitted="refetchAPIs()"
                  @on-change-employee="onChangeEmployee($event)"
                ></ProfileTopCard>
                <AllProfileDetails
                  :selectedEmpId="selectedEmpId"
                  :selectedEmpStatus="selectedEmpStatus"
                  :selectedEmpDateOfBirth="selectedEmpDateOfBirth"
                  :formAccess="myTeamAccess"
                  :actionType="actionType"
                  :selectedEmployeeDetails="selectedEmployeeDetails"
                  callingFrom="team"
                  :myTeamList="myTeamListBackup"
                  :empChangeCount="empChangeCount"
                  :showTab="selectedTab"
                  @close-add-form="closeViewEditForm()"
                  @employee-id-retrieved="selectedEmpId = $event"
                  @details-updated="updateCount += 1"
                  @employee-details-updated="onEmpDetailsUpdated($event)"
                  @close-profile="closeViewEditForm()"
                />
              </v-col>
              <v-col v-else cols="12">
                <AppFetchErrorScreen
                  v-if="myTeamListBackup.length === 0"
                  key="no-results-screen"
                >
                  <template #contentSlot>
                    <div style="max-width: 80%" class="mx-auto">
                      <v-row
                        style="background: white"
                        class="rounded-lg pa-5 mb-4"
                      >
                        <v-col cols="12">
                          <NotesCard
                            notes="Utilize the Team Summary Form to manage team details efficiently. Update information promptly to maintain accuracy and enhance team collaboration."
                            backgroundColor="transparent"
                            class="mb-4"
                          ></NotesCard>
                          <NotesCard
                            notes="Access permissions vary based on roles. Ensure you have the necessary permissions to view, edit, and delete information. Respect data privacy and confidentiality."
                            backgroundColor="transparent"
                            class="mb-4"
                          ></NotesCard>
                          <NotesCard
                            notes="Edit, add, or delete information when necessary for accurate records. Be cautious with deletions to avoid data loss. Regularly update details to reflect changes in the team."
                            backgroundColor="transparent"
                            class="mb-4"
                          ></NotesCard>
                        </v-col>
                        <v-col
                          cols="12"
                          class="d-flex align-center justify-center mb-4"
                        >
                          <v-btn
                            v-if="
                              myTeamAccess &&
                              myTeamAccess.add &&
                              myTeamAccess.admin === 'admin' &&
                              !payrollAdminAccess
                            "
                            variant="elevated"
                            color="primary"
                            class="ml-4 mt-1"
                            rounded="lg"
                            :size="isMobileView ? 'small' : 'default'"
                            @click="openAddForm()"
                          >
                            <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                            Add Employee
                          </v-btn>
                          <v-btn
                            color="white"
                            rounded="lg"
                            class="ml-2 mt-1"
                            :size="isMobileView ? 'small' : 'default'"
                            @click="refetchAPIs()"
                          >
                            <v-icon>fas fa-redo-alt</v-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </div>
                  </template>
                </AppFetchErrorScreen>
                <ListMyTeam
                  v-else
                  :items="myTeamList"
                  :originalList="myTeamListBackup"
                  :formAccess="myTeamAccess"
                  :isPayrollAdmin="payrollAdminAccess"
                  @on-select-item="openViewForm($event)"
                  @reset-filter="resetFilter('grid')"
                  @refetch-list="refetchAPIs($event)"
                  @add-employee="openAddForm()"
                ></ListMyTeam>
              </v-col>
            </v-row>
          </v-window-item>
        </v-window>
      </section>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppWarningModal
      v-if="openRedirectModal"
      :open-modal="openRedirectModal"
      iconName="fas fa-check-circle"
      closeButtonText=""
      acceptButtonText=""
      confirmationHeading="Employee cloned successfully."
      iconColor="green"
      @close-warning-modal="openRedirectModal = false"
    >
      <template v-slot:warningModalContent>
        <div class="text-center mt-3">
          When cloning employee data, the bank account details will be set to
          inactive. Please update the status to active or add new bank account
          details as needed in the team summary.
        </div>
      </template>
    </AppWarningModal>
  </div>
</template>

<script>
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu";
import { defineComponent, defineAsyncComponent } from "vue";
import ListMyTeam from "./ListMyTeam.vue";
const ProfileTopCard = defineAsyncComponent(() =>
  import("../../employee-profile/profile-details/ProfileTopCard.vue")
);
const AllProfileDetails = defineAsyncComponent(() =>
  import("../../employee-profile/profile-details/AllProfileDetails.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import {
  RETRIEVE_TOTAL_EMP_COUNT,
  LIST_MY_TEAM_EMPLOYEES,
} from "@/graphql/employee-profile/profileQueries.js";
import moment from "moment";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default defineComponent({
  name: "TeamSummary",

  components: {
    EmployeeDefaultFilterMenu,
    ListMyTeam,
    ProfileTopCard,
    AllProfileDetails,
    NotesCard,
  },

  data() {
    return {
      // list
      myTeamList: [],
      myTeamListBackup: [],
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryButton: true,
      employeesLimitToCallAPI: 3400,
      totalApiCount: 0,
      apiCallCount: 0,
      // filter
      selectedStatus: ["Active"],
      selectedIsManagerFlag: [],
      selectedIsRecruiterFlag: [],
      selectedFormStatus: [],
      resetFilterCount: 0,
      appliedFilterCount: 0,
      // view/edit
      showViewEditForm: false,
      selectedEmpId: 0,
      selectedEmpStatus: "Active",
      selectedEmpDateOfBirth: "",
      selectedEmployeeDetails: {},
      actionType: "",
      updateCount: 0,
      empChangeCount: 0,
      selectedTab: "Personal_Info",
      openRedirectModal: false,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    myTeamAccess() {
      let formAccess = this.accessRights("243");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    payrollAdminAccess() {
      let formAccess = this.accessRights("149");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["update"]
      ) {
        return true;
      } else {
        return false;
      }
    },
    payConfigAccess() {
      let formAccess = this.accessRights("263");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    checkAccess() {
      return (
        (this.myTeamAccess &&
          (this.myTeamAccess.admin === "admin" ||
            this.myTeamAccess.isManager)) ||
        (this.payConfigAccess && this.payrollAdminAccess)
      );
    },
    currentTabItem() {
      let index = this.mainTabs.indexOf("My Team");
      return "tab-" + index;
    },
    teamSummaryReportsFormAccess() {
      let formAccessRights = this.accessRights("350");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    approvalFormAccess() {
      let formAccess = this.accessRights("184");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    mainTabs() {
      let tabs = [];
      if (this.checkAccess) tabs.push("My Team");
      if (this.approvalFormAccess && this.presentTeamSummaryApprovalsTab)
        tabs.push("Approvals");
      if (this.teamSummaryReportsFormAccess)
        tabs.push(this.accessRights("350")?.customFormName || "Reports");
      return tabs;
    },
    presentTeamSummaryApprovalsTab() {
      const employeeSettings =
        this.$store.state.orgDetails?.employeeSettings || {};
      return (
        employeeSettings?.Enable_Workflow_Profile?.toLowerCase() === "yes" ||
        employeeSettings?.Enable_Workflow_Team_Summary?.toLowerCase() === "yes"
      );
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("My Team Error:", err);
    let msg =
      "Something went wrong while loading the my team form. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    mixpanel.track(msg);
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.$store.commit(
      "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
      "0-false"
    );
    if (this.$route.params && this.$route.params.tabName) {
      this.selectedTab = this.$route.params.tabName;
    }
    this.getTotalEmpCount();
  },

  methods: {
    refetchAPIs(showContent) {
      if (showContent) this.openRedirectModal = true;
      mixpanel.track("MyTeam-list-refetch");
      this.isErrorInList = false;
      this.updateCount = 0;
      this.showViewEditForm = false;
      this.actionType = "";
      this.selectedEmpId = 0;
      this.selectedEmployeeDetails = {};
      this.myTeamList = [];
      this.myTeamListBackup = [];
      this.apiCallCount = 0;
      this.totalApiCount = 0;
      this.getTotalEmpCount();
    },
    onTabChange(tabName) {
      if (tabName !== "My Team") {
        this.listLoading = true;
        if (tabName?.toLowerCase() === "approvals")
          this.$router.push("/approvals/approval-management?form_id=243");
        else this.$router.push("/my-team/team-summary/reports");
      }
    },
    onEmpDetailsUpdated(details) {
      const combinedObj = { ...this.selectedEmployeeDetails, ...details };
      this.selectedEmployeeDetails = combinedObj;
    },
    onChangeEmployee(item) {
      this.selectedEmployeeDetails = item;
      this.selectedEmpId = item.employeeId;
      this.selectedEmpStatus = item.empStatus;
      this.selectedEmpDateOfBirth = item.dob;
      if (item.formStatus) {
        this.actionType = "edit";
      } else {
        this.actionType = "add";
      }
      this.empChangeCount += 1;
      mixpanel.track("MyTeam-employee-changed");
    },
    openViewForm(item) {
      this.selectedEmployeeDetails = item;
      this.selectedEmpId = item.employeeId;
      this.selectedEmpStatus = item.empStatus;
      this.selectedEmpDateOfBirth = item.dob;
      if (item.formStatus) {
        this.actionType = "edit";
      } else {
        this.actionType = "add";
      }
      this.showViewEditForm = true;
      mixpanel.track("MyTeam-employee-view-form-opened");
    },
    openAddForm() {
      this.actionType = "add";
      this.selectedEmployeeDetails = {};
      this.selectedEmpId = 0;
      this.selectedEmpStatus = "Active";
      this.selectedEmpDateOfBirth = "";
      this.showViewEditForm = true;
      mixpanel.track("MyTeam-employee-add-form-opened");
    },
    closeViewEditForm() {
      this.$store.commit(
        "employeeProfile/UPDATE_EDIT_FORM_OPENED_COUNT",
        "0-false"
      );
      this.selectedTab = "Personal_Info";
      if (this.updateCount > 0) {
        this.refetchAPIs();
      } else {
        this.showViewEditForm = false;
        this.actionType = "";
        this.selectedEmpId = 0;
        this.selectedEmployeeDetails = {};
      }
      mixpanel.track("MyTeam-employee-view-form-closed");
    },
    resetFilter(calledFrom) {
      if (calledFrom === "grid") {
        this.resetFilterCount += 1;
      }
      this.selectedIsManagerFlag = [];
      this.selectedIsRecruiterFlag = [];
      this.selectedStatus = [];
      this.selectedFormStatus = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.myTeamList = this.myTeamListBackup;
      mixpanel.track("MyTeam-employee-filter-reset");
    },

    applyFilter(filteredArray, calledFrom = "") {
      if (calledFrom === "API") {
        this.appliedFilterCount += 1;
      }
      let filteredList = filteredArray;
      if (this.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedStatus.includes(item.empStatus);
        });
      }
      if (this.selectedIsManagerFlag.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedIsManagerFlag.includes(item.isManager);
        });
      }
      if (this.selectedIsRecruiterFlag.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedIsRecruiterFlag.includes(item.isRecruiter);
        });
      }
      if (this.selectedFormStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedFormStatus.includes(item.formStatus);
        });
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.myTeamList = filteredList;
      mixpanel.track("MyTeam-employee-filter-applied");
    },
    getTotalEmpCount() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_TOTAL_EMP_COUNT,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("MyTeam-list-fetch-success");
          if (
            response &&
            response.data &&
            response.data.retrieveEmployeeCount
          ) {
            let { totalCount } = response.data.retrieveEmployeeCount;
            if (totalCount > 0) {
              totalCount = parseInt(totalCount);
              this.apiCallCount = 0;
              this.totalApiCount = Math.ceil(
                totalCount / this.employeesLimitToCallAPI
              );
              for (let i = 0; i < this.totalApiCount; i++) {
                this.getMyTeamEmployees(i);
              }
            } else {
              vm.listLoading = false;
            }
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    getMyTeamEmployees(index) {
      let vm = this;
      vm.listLoading = true;
      let apiOffset = parseInt(index) * vm.employeesLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      vm.$apollo
        .query({
          query: LIST_MY_TEAM_EMPLOYEES,
          client: "apolloClientAC",
          variables: {
            employeeId: vm.loginEmployeeId,
            offset: apiOffset,
            limit: vm.employeesLimitToCallAPI,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          mixpanel.track("MyTeam-list-fetch-success");
          if (response && response.data && response.data.listMyTeam) {
            let { listMyTeam } = response.data.listMyTeam;
            listMyTeam = JSON.parse(listMyTeam);
            listMyTeam = listMyTeam.map((item) => {
              let empName = "";
              if (item["employeeFirstName"])
                empName += item["employeeFirstName"] + " ";
              if (item["employeeMiddleName"])
                empName += item["employeeMiddleName"] + " ";
              if (item["employeeLastName"]) empName += item["employeeLastName"];
              item["employeeName"] = empName;
              item["empNameWithSalutation"] = item.salutation
                ? item.salutation + ". " + item.employeeFirstName
                : item.employeeFirstName || "";
              item["dateOfJoinWithoutFormat"] = item.dateOfJoin
                ? new Date(item.dateOfJoin)
                : "-";
              item["dateOfJoin"] = item.dateOfJoin
                ? this.formatDate(item.dateOfJoin)
                : "-";
              item["dateOfBirth"] = item.dob ? this.formatDate(item.dob) : "-";
              item["probationDateFormatted"] = item.probationDate
                ? this.formatDate(item.probationDate)
                : "-";
              item["confirmationDate"] = item.probationDate
                ? this.formatDate(
                    moment(item.probationDate, "YYYY-MM-DD")
                      .add(1, "days")
                      .format("YYYY-MM-DD")
                  )
                : "";
              item["allowUserSignin"] = item.Allow_User_Signin ? "Yes" : "No";
              item["isManager"] = item.isManager ? "Yes" : "No";
              item["mobileNo"] = item.Mobile_No_Country_Code
                ? item.Mobile_No_Country_Code + item.Mobile_No
                : item.Mobile_No;
              item["empStatusAll"] =
                item.formStatus == 0 ? "Draft" : item.empStatus;
              return item;
            });
            vm.myTeamList = vm.myTeamList.concat(listMyTeam);
            vm.myTeamListBackup = vm.myTeamListBackup.concat(listMyTeam);
            vm.apiCallCount += 1;
            if (vm.totalApiCount === vm.apiCallCount) {
              vm.listLoading = false;
              vm.applyFilter(vm.myTeamList, "API");
              if (this.$route.params && this.$route.params.employeeId) {
                let decodedEmployeeId = atob(this.$route.params.employeeId);
                let item = vm.myTeamList.find(
                  (data) => data.employeeId === parseInt(decodedEmployeeId)
                );
                if (item) {
                  vm.openViewForm(item);
                }
              }
            }
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "team summary",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
      mixpanel.track("MyTeam-list-fetch-error");
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style>
.team-container {
  padding: 5em 3em 0em 3em;
}

@media screen and (max-width: 805px) {
  .team-container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
