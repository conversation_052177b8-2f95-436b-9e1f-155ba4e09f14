<template>
  <div>
    <v-overlay
      :model-value="showViewForm"
      @click:outside="onCloseView()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card position-relative"
          :style="
            windowWidth <= 1264
              ? 'width:100vw; height: 100vh'
              : 'width:35vw; height: 100vh'
          "
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center"
          >
            <div class="text-h6 text-medium ps-2">View Compensatory Off</div>
            <div class="d-flex align-center">
              <v-btn icon class="clsBtn" variant="text" @click="onCloseView()">
                <v-icon>fas fa-times</v-icon>
              </v-btn>
            </div>
          </v-card-title>

          <v-card-text class="overflow-y-auto" style="max-height: 90vh">
            <div
              v-if="
                formAccess?.update &&
                selectedCompOffData?.Approval_Status.toLowerCase() === 'applied'
              "
              class="d-flex justify-end align-center"
            >
              <v-btn
                @click="onEditPosition()"
                class="mr-3 mt-3 bg-white text-primary"
                variant="text"
                rounded="lg"
              >
                <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
              </v-btn>
            </div>
            <div class="px-6">
              <v-row>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Employee Id
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{
                        checkNullValue(selectedCompOffData.User_Defined_EmpId)
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">Employee</div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedCompOffData.Employee_Name) }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Worked Date
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedCompOffData.Compensated_Date) }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">Duration</div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedCompOffData.Duration) }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">Period</div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedCompOffData.Period) }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">
                    Compensatory Off Date
                  </div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{
                        checkNullValue(selectedCompOffData.Compensatory_Date)
                      }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">Reason</div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedCompOffData.Reason) }}
                    </section>
                  </div>
                </v-col>
                <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0">
                  <div class="text-subtitle-1 text-grey-darken-1">Status</div>
                  <div class="text-subtitle-1 font-weight-regular">
                    <section class="text-body-2">
                      {{ checkNullValue(selectedCompOffData.Approval_Status) }}
                    </section>
                  </div>
                </v-col>
                <v-col
                  cols="12"
                  v-if="
                    labelList[429]?.Field_Visiblity?.toLowerCase() === 'yes' &&
                    compOffAttachments.length > 0
                  "
                  class="px-md-6 pb-0"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ labelList[429].Field_Alias }}
                  </p>
                  <p
                    v-for="(doc, index) in compOffAttachments"
                    :key="index"
                    class="text-subtitle-1 font-weight-regular text-blue cursor-pointer mb-2"
                    style="text-decoration: underline"
                    @click="viewEmailAttachments(index)"
                  >
                    {{ formattedFileName(doc.filePath) }}
                  </p>
                </v-col>
              </v-row>
              <v-row class="d-flex justify-center">
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  ></MoreDetails> </v-col
              ></v-row>
              <div class="bottom-space"></div>
            </div> </v-card-text></v-card></template
    ></v-overlay>
    <FilePreviewModal
      v-if="showEmailAttachments"
      :fileName="attachmentFileName"
      :folderName="'Compensatory Off Document Upload'"
      :current="selectedDocumentIndex"
      :length="compOffAttachments.length"
      @prev-document="changeSelectedDocument(-1)"
      @next-document="changeSelectedDocument(1)"
      @close-preview-modal="showEmailAttachments = false"
    ></FilePreviewModal>
  </div>
</template>

<script>
import { checkNullValue, convertUTCToLocal } from "@/helper";
import { defineAsyncComponent } from "vue";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
export default {
  name: "ViewCompOff",
  emits: ["close-view-details", "edit-comp-off-record"],
  props: {
    selectedCompOffData: {
      type: Object,
      required: true,
    },
    enableView: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      showViewForm: false,
      moreDetailsList: [],
      openMoreDetails: true,
      compOffAttachments: [],
      attachmentFileName: "",
      showEmailAttachments: false,
      selectedDocumentIndex: null,
    };
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeName() {
      return this.$store.state.orgDetails.userDetails.employeeFullName;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    formAccess() {
      let formAccess = this.accessRights("334");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formattedFileName() {
      return (fileName) => {
        if (fileName) {
          return fileName.split("-")[3];
        }
      };
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {},
  watch: {
    enableView(val) {
      this.showViewForm = val;
      let parsedCompOffAttachments = this.selectedCompOffData.Document_Names
        ? JSON.parse(this.selectedCompOffData.Document_Names)
        : null;
      this.compOffAttachments = parsedCompOffAttachments
        ? parsedCompOffAttachments
        : [];
      this.compOffAttachments = this.compOffAttachments.map((file) => ({
        filePath: file,
      }));
      this.prefillMoreDetails();
    },
  },
  components: {
    MoreDetails,
    FilePreviewModal,
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    onCloseView() {
      this.showViewForm = false;
      this.$emit("close-view-details");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      const addedOn = this.selectedCompOffData?.Added_On
          ? this.selectedCompOffData.Added_On
          : "",
        addedByName = this.selectedCompOffData.Added_By_Name,
        updatedByName = this.selectedCompOffData.Updated_By_Name,
        updatedOn = this.selectedCompOffData?.Updated_On
          ? this.selectedCompOffData.Updated_On
          : "";
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    onEditPosition() {
      this.showViewForm = false;
      this.$emit("edit-comp-off-record");
    },
    viewEmailAttachments(index) {
      this.selectedDocumentIndex = index;
      this.attachmentFileName = this.compOffAttachments[index].filePath;
      this.showEmailAttachments = true;
    },
    changeSelectedDocument(step) {
      this.selectedDocumentIndex += step;
      if (
        this.selectedDocumentIndex >= 0 &&
        this.selectedDocumentIndex < this.compOffAttachments.length
      ) {
        this.attachmentFileName =
          this.compOffAttachments[this.selectedDocumentIndex].filePath;
      }
    },
  },
};
</script>
<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
:deep(.ql-toolbar.ql-snow) {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
:deep(.ql-editor) {
  font-family: Roboto, sans-serif !important;
}
.bottom-space {
  margin-top: 200px;
}
</style>
