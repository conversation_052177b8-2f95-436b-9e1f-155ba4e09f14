<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col
            v-if="presentOnlyForMyTeam"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedEmployee"
              density="compact"
              :items="employeesList"
              item-title="employee"
              item-value="employeeValue"
              label="Employee"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedLeaveName"
              density="compact"
              :items="leaveNameList"
              item-title="leaveType"
              item-value="leaveType"
              label="Leave Name"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedLateArrival"
              density="compact"
              :items="['Yes', 'No']"
              label="Late Arrival"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedEarlyCheckout"
              density="compact"
              :items="['Yes', 'No']"
              label="Early Checkout"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="presentOnlyForMyTeam"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedAttendanceShortage"
              density="compact"
              :items="['Yes', 'No']"
              label="Attendance Shortage"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="presentOnlyForMyTeam"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedAttendanceFinalization"
              density="compact"
              :items="['Yes', 'No']"
              label="Attendance Finalization"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col class="py-2" :cols="windowWidth > 600 ? 6 : 12">
            <v-autocomplete
              v-model="selectedStatus"
              density="compact"
              :items="statusList"
              item-title="status"
              item-value="status"
              variant="solo"
              label="Status"
              single-line
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="presentOnlyForMyTeam"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedDesignation"
              density="compact"
              :items="designationList"
              item-title="designation"
              item-value="designation"
              label="Designation"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="presentOnlyForMyTeam"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedDepartment"
              density="compact"
              :items="departmentList"
              label="Department"
              item-title="departments"
              item-value="departments"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="presentOnlyForMyTeam"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLocation"
              density="compact"
              :items="locationList"
              label="Location"
              itemValue="Location_Id"
              itemTitle="Location_Name"
              :isLoading="dropdownListFetching"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="presentOnlyForMyTeam"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedWorkSchedule"
              density="compact"
              :items="workScheduleList"
              label="Work Schedule"
              itemValue="WorkSchedule_Id"
              itemTitle="Title"
              :isLoading="dropdownListFetching"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="presentOnlyForMyTeam"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedemployeeType"
              density="compact"
              :items="employeeTypeList"
              label="Employee Type"
              itemValue="EmpType_Id"
              itemTitle="Employee_Type"
              :isLoading="dropdownListFetching"
              single-line
              variant="solo"
              multiple
              chips
              closable-chips
            />
          </v-col>
          <v-col
            v-if="fieldForce && presentOnlyForMyTeam"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedServiceProvider"
              density="compact"
              variant="solo"
              :items="serviceProviderList"
              item-value="Service_Provider_Id"
              item-title="Service_Provider_Name"
              :isLoading="dropdownListFetching"
              :label="labelList[115]?.Field_Alias"
              single-line
              multiple
              chips
              closable-chips
            />
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="fnApplyFilter()"
        >
          <span>Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span>Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
export default {
  name: "MyTeamLeaveFilter",
  data: () => ({
    openFormFilter: false,
    dropdownListFetching: false,
    // Date Picker
    selectedEmployee: null,
    selectedLeaveName: null,
    selectedLocation: null,
    selectedemployeeType: null,
    selectedDepartment: null,
    selectedDesignation: null,
    selectedWorkSchedule: null,
    selectedLateArrival: null,
    selectedEarlyCheckout: null,
    selectedAttendanceShortage: null,
    selectedAttendanceFinalization: null,
    selectedServiceProvider: null,
    selectedStatus: null,
    // Lists
    leaveNameList: [],
    employeesList: [],
    locationList: [],
    employeeTypeList: [],
    departmentList: [],
    designationList: [],
    workScheduleList: [],
    serviceProviderList: [],
    statusList: [],
  }),
  props: {
    itemList: {
      type: Array,
      default: () => [],
    },
    callingFrom: {
      type: String,
      required: true,
    },
    formId: {
      type: Number,
      required: true,
    },
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    presentOnlyForMyTeam() {
      return this.callingFrom?.toLowerCase() === "myteam";
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  watch: {
    itemList() {
      this.formFilterData();
    },
  },
  mounted() {
    this.formFilterData();
    if (this.presentOnlyForMyTeam) this.retrieveDropdownDetails();
  },
  methods: {
    formFilterData() {
      this.employeesList = [];
      this.leaveNameList = [];
      this.departmentList = [];
      this.designationList = [];
      this.statusList = [];
      for (let item of this.itemList) {
        if (item && item.Employee_Name) {
          this.employeesList.push({
            employee: item.User_Defined_EmpId
              ? `${item.Employee_Name} - ${item.User_Defined_EmpId}`
              : item.Employee_Name,
            employeeValue: item.Employee_Name,
          });
        }
        if (item && item.Leave_Name) {
          this.leaveNameList.push({
            leaveType: item.Leave_Name,
          });
        }
        if (item && item.Department_Name) {
          this.departmentList.push({
            departments: item.Department_Name,
          });
        }
        if (item && item.Designation_Name) {
          this.designationList.push({
            designation: item.Designation_Name,
          });
        }
        if (item && item.Approval_Status) {
          this.statusList.push({
            status: item.Approval_Status,
          });
        }
      }
      this.employeesList = this.removeDuplicatesFromArrayOfObject(
        this.employeesList,
        "employee"
      );
      this.leaveNameList = this.removeDuplicatesFromArrayOfObject(
        this.leaveNameList,
        "leaveType"
      );
      this.departmentList = this.removeDuplicatesFromArrayOfObject(
        this.departmentList,
        "departments"
      );
      this.designationList = this.removeDuplicatesFromArrayOfObject(
        this.designationList,
        "designation"
      );
      this.statusList = this.removeDuplicatesFromArrayOfObject(
        this.statusList,
        "status"
      );
    },
    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
    // apply filter
    fnApplyFilter(updatedList = []) {
      this.openFormFilter = false;
      let filterObj = {
        employee: this.selectedEmployee,
        leaveType: this.selectedLeaveName,
        location: this.selectedLocation,
        employeeType: this.selectedemployeeType,
        department: this.selectedDepartment,
        designation: this.selectedDesignation,
        workSchedule: this.selectedWorkSchedule,
        lateArrival: this.selectedLateArrival?.map((item) =>
          item === "Yes" ? 1 : 0
        ),
        earlyCheckout: this.selectedEarlyCheckout,
        attendanceShortage: this.selectedAttendanceShortage?.map((item) =>
          item === "Yes" ? 1 : 0
        ),
        attendanceFinalization: this.selectedAttendanceFinalization?.map(
          (item) => (item === "Yes" ? "Auto Lop" : "")
        ),
        serviceProvide: this.selectedServiceProvider,
        status: this.selectedStatus,
      };
      this.applyFilter(filterObj, updatedList);
    },
    // reset filter
    resetFilterValues() {
      this.openFormFilter = false;
      this.resetAllModelValues();
      this.$emit("reset-filter");
    },
    applyFilter(filter, updatedList) {
      let filteredList = updatedList?.length ? updatedList : this.itemList;
      if (filter.employee && filter.employee?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.employee.includes(item.Employee_Name);
        });
      }
      if (filter.leaveType && filter.leaveType?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.leaveType.includes(item.Leave_Name);
        });
      }
      if (filter.location && filter.location?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.location.includes(item.Location_Id);
        });
      }
      if (filter.employeeType && filter.employeeType?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.employeeType.includes(item.EmpType_Id);
        });
      }
      if (filter.department && filter.department?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.department.includes(item.Department_Name);
        });
      }
      if (filter.designation && filter.designation?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.designation.includes(item.Designation_Name);
        });
      }
      if (filter.workSchedule && filter.workSchedule?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.workSchedule.includes(item.Work_Schedule);
        });
      }
      if (filter.lateArrival && filter.lateArrival?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.lateArrival.includes(item.Late_Attendance);
        });
      }
      if (filter.earlyCheckout && filter.earlyCheckout?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.earlyCheckout.includes(item.Early_Checkout);
        });
      }
      if (filter.attendanceShortage && filter.attendanceShortage?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.attendanceShortage.includes(item.Attendance_Shortage);
        });
      }
      if (
        filter.attendanceFinalization &&
        filter.attendanceFinalization.length > 0
      ) {
        const lowerCaseReasons = filter.attendanceFinalization.map(
          (r) => r?.toLowerCase() || ""
        );
        const isIncludeMode = lowerCaseReasons.includes("auto lop");
        filteredList = filteredList.filter((item) => {
          const reason = (item.Reason || "")?.toLowerCase();
          return isIncludeMode
            ? lowerCaseReasons.includes(reason)
            : !lowerCaseReasons.includes(reason);
        });
      }
      if (filter.serviceProvide && filter.serviceProvide?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.serviceProvide.includes(item.Service_Provider_Id);
        });
      }
      if (filter.status && filter.status?.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.status.includes(item.Approval_Status);
        });
      }
      this.$emit("apply-filter", filteredList);
    },
    resetAllModelValues() {
      this.selectedEmployee = null;
      this.selectedLeaveName = null;
      this.selectedLocation = null;
      this.selectedemployeeType = null;
      this.selectedDepartment = null;
      this.selectedDesignation = null;
      this.selectedWorkSchedule = null;
      this.selectedLateArrival = null;
      this.selectedEarlyCheckout = null;
      this.selectedAttendanceShortage = null;
      this.selectedAttendanceFinalization = null;
      this.selectedServiceProvider = null;
      this.selectedStatus = null;
    },
    retrieveDropdownDetails() {
      this.dropdownListFetching = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: parseInt(this.formId) })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { locations, employeeType, workSchedules, serviceProvider } =
              res.data.getDropDownBoxDetails;
            this.locationList = locations;
            this.employeeTypeList = employeeType;
            this.workScheduleList = workSchedules;
            this.serviceProviderList = serviceProvider;
          }
          this.dropdownListFetching = false;
        })
        .catch(() => {
          this.dropdownListFetching = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
