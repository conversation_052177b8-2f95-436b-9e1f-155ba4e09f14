<template>
  <div>
    <v-container fluid v-if="formAccess">
      <div v-if="leaveListLoading" class="mt-3">
        <v-skeleton-loader
          ref="skeleton1"
          type="table-heading"
          class="mx-auto"
        ></v-skeleton-loader>
        <div v-for="i in 4" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>
      <AppFetchErrorScreen
        v-else-if="isErrorInList"
        :content="errorContent"
        key="error-screen"
        icon-name="fas fa-redo-alt"
        image-name="common/human-error-image"
        button-text="Retry"
        @button-click="refetchList()"
      ></AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="originalList?.length === 0"
        key="no-data-screen"
        :main-title="emptyScenarioMsg"
      >
        <template #contentSlot>
          <div :style="isMobileView ? 'max-width: 95%' : 'max-width: 80%'">
            <v-row class="rounded-lg mb-4" style="background: white">
              <v-col cols="12">
                <NotesCard
                  v-if="callingFrom?.toLowerCase() === `myteam`"
                  notes="Leave request feature allows managers to efficiently handle employee leave applications while ensuring compliance with company policies. Employees can apply for leave, track their leave balances, and receive real-time updates on their requests. The system ensures accuracy by validating leave requests against available balances before submission, preventing excess leave applications. Once a request is submitted, managers or assigned approvers receive notifications for approval, with the ability to approve, reject, or request modifications."
                  backgroundColor="transparent"
                  class="mb-4"
                />
                <NotesCard
                  v-if="callingFrom?.toLowerCase() === `myteam`"
                  notes="This streamlined workflow enhances transparency and minimizes administrative effort by automatically updating leave balances upon approval. Managers can review employee leave history and maintain workforce planning without manual tracking. The feature promotes efficient leave management, reduces processing time, and ensures that business operations are not disrupted due to unplanned absences."
                  backgroundColor="transparent"
                  class="mb-4"
                />
                <NotesCard
                  v-if="callingFrom?.toLowerCase() === `selfservice`"
                  notes="The Leave Request feature empowers employees to submit leave applications independently while ensuring compliance with company policies. Employees can select leave types, specify the duration, and provide a reason for the request. The system restricts applications beyond available leave balances, ensuring fair allocation. Before submission, the request is validated against eligibility criteria, reducing errors and improving efficiency."
                  backgroundColor="transparent"
                  class="mb-4"
                />
                <NotesCard
                  v-if="callingFrom?.toLowerCase() === `selfservice`"
                  notes="Once submitted, leave requests are automatically routed to the designated manager for review. Managers can approve, reject, or request modifications with comments for better communication. Upon approval, the employee’s leave balance is updated in real time, ensuring transparency. This feature enhances employee autonomy, simplifies leave tracking, and minimizes the risk of disputes regarding leave eligibility and approvals."
                  backgroundColor="transparent"
                  class="mb-4"
                />
              </v-col>
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <div
                  class="d-flex align-center flex-wrap"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <CustomSelect
                    v-model="selectedPeriod"
                    :items="periodList"
                    :itemSelected="selectedPeriod"
                    :isAutoComplete="true"
                    variant="solo"
                    class="mt-3"
                    label="Period"
                    placeholder="Select Period"
                    density="compact"
                    min-width="150px"
                    :max-width="isMobileView ? `200px` : `300px`"
                    @selected-item="onChangePeriod($event)"
                  />
                  <v-btn
                    class="bg-white mb-3 mx-4"
                    :style="'width: max-content'"
                    :size="isMobileView ? 'small' : 'default'"
                    rounded="lg"
                    @click="$refs.datePicker.fp.open(), resetDateFilters()"
                  >
                    <v-icon color="primary" size="14"
                      >fas fa-calendar-alt</v-icon
                    >
                    <span v-if="!isMobileView" class="text-caption px-1 pt-1"
                      >Date:</span
                    >
                    <flat-pickr
                      ref="datePicker"
                      v-model="selectedMonthYear"
                      :config="flatPickerOptions"
                      placeholder="Select Date Range"
                      class="ml-2 mt-1 date-range-picker-custom-bg"
                      style="outline: 0px; color: var(--v-primary-base)"
                      :style="`width: ${isMobileView ? `160px` : `170px`}`"
                      @onChange="onChangeDateRange"
                    />
                  </v-btn>
                  <v-btn
                    v-if="formAccess?.add"
                    variant="elevated"
                    rounded="lg"
                    prepend-icon="fas fa-plus"
                    class="primary"
                    @click="openAddEditForm()"
                  >
                    <template v-slot:prepend>
                      <v-icon size="15" class="pr-1 primary"></v-icon>
                    </template>
                    <span class="primary">Apply</span>
                  </v-btn>
                  <v-btn
                    color="transparent"
                    variant="flat"
                    class="ml-2 mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="itemList?.length == 0 || emptySearchValue"
        key="no-results-screen"
        main-title="There are no leaves matched for the selected filters/searches."
        image-name="common/no-records"
      >
        <template #contentSlot>
          <div style="max-width: 80%">
            <v-row class="rounded-lg pa-5 mb-4">
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <v-btn
                  variant="elevated"
                  color="primary"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="windowWidth <= 960 ? 'small' : 'default'"
                  @click="resetFilter()"
                >
                  <span class="primary">Reset Filter/Search </span>
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <div v-else>
        <div
          v-if="originalList?.length > 0"
          class="d-flex flex-wrap align-center"
          :class="isMobileView ? 'flex-column' : ''"
          style="justify-content: space-between"
        >
          <div
            class="d-flex align-center flex-wrap"
            :class="isMobileView ? 'justify-center' : ''"
          >
            <CustomSelect
              v-model="selectedPeriod"
              :items="periodList"
              :itemSelected="selectedPeriod"
              :isAutoComplete="true"
              variant="solo"
              class="mt-3"
              label="Period"
              placeholder="Select Period"
              density="compact"
              min-width="150px"
              max-width="500px"
              @selected-item="onChangePeriod($event)"
            />
            <v-btn
              class="bg-white mb-3 ml-2"
              :style="'width: max-content'"
              :size="isMobileView ? 'small' : 'default'"
              rounded="lg"
              @click="$refs.datePicker.fp.open(), resetDateFilters()"
            >
              <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
              <span class="text-caption px-1 pt-1">Date:</span>
              <flat-pickr
                ref="datePicker"
                v-model="selectedMonthYear"
                :config="flatPickerOptions"
                placeholder="Select Date Range"
                class="ml-2 mt-1 date-range-picker-custom-bg"
                style="outline: 0px; color: var(--v-primary-base); width: 170px"
                @onChange="onChangeDateRange"
              />
            </v-btn>
          </div>

          <div
            class="d-flex align-center"
            :class="isMobileView ? 'justify-center' : 'justify-end'"
          >
            <v-btn
              v-if="formAccess.add"
              prepend-icon="fas fa-plus"
              color="primary"
              variant="elevated"
              rounded="lg"
              :size="isMobileView ? 'small' : 'default'"
              @click="openAddEditForm()"
            >
              <template v-slot:prepend>
                <v-icon></v-icon>
              </template>
              Apply
            </v-btn>
            <v-btn
              rounded="lg"
              color="transparent"
              variant="flat"
              class="mt-1"
              :size="isMobileView ? 'small' : 'default'"
              @click="refetchList()"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu class="mb-1" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                  <v-icon>fas fa-ellipsis-v</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'bg-hover': isHovering,
                        }"
                        >{{ action.key }}</v-list-item-title
                      >
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
        <v-row>
          <v-col cols="12">
            <v-data-table
              :headers="tableHeadersWithStatusBar"
              :items="itemList"
              :items-per-page="50"
              fixed-header
              :show-select="
                callingFrom?.toLowerCase() === 'myteam' &&
                Boolean(formAccess?.delete)
              "
              :height="
                itemList?.length > 11 ? $store.getters.getTableHeight(270) : ''
              "
              item-value="Leave_Id"
              class="elevation-1"
              style="box-shadow: none !important"
              :search="searchValue"
              @update:currentItems="updateCurrentItems($event)"
            >
              <template v-slot:[`header.data-table-select`]>
                <v-checkbox-btn
                  v-model="selectAllBox"
                  color="primary"
                  false-icon="far fa-circle"
                  true-icon="fas fa-check-circle"
                  indeterminate-icon="fas fa-minus-circle"
                  class="mt-1"
                  @change="toggleSelectAll(selectAllBox)"
                />
              </template>
              <template v-slot:item="{ item }">
                <tr
                  style="z-index: 200"
                  class="data-table-tr bg-white cursor-pointer"
                  @click="openViewForm(item)"
                  :class="[
                    isMobileView
                      ? ' v-data-table__mobile-table-row ma-0 mt-2'
                      : '',
                  ]"
                >
                  <td
                    v-if="!isMobileView"
                    class="pa-0"
                    style="width: 8px; position: relative"
                  >
                    <div
                      :class="[
                        'left-status-bar',
                        leftStatusBarClass(item.Approval_Status),
                      ]"
                      style="position: absolute; top: 0; bottom: 0; left: 0"
                    />
                  </td>
                  <td
                    v-if="
                      callingFrom?.toLowerCase() === 'myteam' &&
                      Boolean(formAccess?.delete)
                    "
                    @click.stop="
                      {
                      }
                    "
                  >
                    <v-checkbox-btn
                      v-model="item.isSelected"
                      color="primary"
                      false-icon="far fa-circle"
                      true-icon="fas fa-check-circle"
                      class="mt-n2 ml-n2"
                      :class="[
                        enableToltipForDelete(item)
                          ? 'dimmed-checkbox cursor-not-allowed'
                          : '',
                      ]"
                      @click.stop="checkAllSelected()"
                      :disabled="enableToltipForDelete(item)"
                    />
                  </td>
                  <td
                    v-if="callingFrom?.toLowerCase() === `myteam`"
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : ''
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Employee
                    </div>
                    <section class="d-flex align-center">
                      <v-tooltip
                        :text="item.Employee_Name"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                            v-bind="props"
                          >
                            {{ checkNullValue(item.Employee_Name) }}
                            <div
                              v-if="item?.User_Defined_EmpId"
                              class="text-grey"
                            >
                              {{ checkNullValue(item.User_Defined_EmpId) }}
                            </div>
                          </div>
                        </template>
                      </v-tooltip>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : ''
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Leave
                    </div>
                    <section
                      class="text-body-2 text-truncate d-flex align-center d-flex align-center"
                      style="max-width: 250px"
                    >
                      <span class="text-body-2 font-weight-regular">
                        <v-tooltip
                          :text="item.Leave_Name"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                              v-bind="item.Leave_Name?.length > 25 ? props : {}"
                            >
                              {{ checkNullValue(item.Leave_Name) }}
                            </div>
                          </template>
                        </v-tooltip>
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : ''
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Start Date
                    </div>
                    <section
                      class="text-body-2 text-truncate"
                      style="max-width: 150px"
                    >
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(formatDate(item.Start_Date)) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : ''
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      End Date
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(formatDate(item.End_Date)) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? ' d-flex justify-space-between align-center'
                        : ' '
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Total Days
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(item.Total_Days) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : ''
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? 'font-weight-bold d-flex align-center'
                          : 'font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Status
                    </div>
                    <section class="d-flex align-center justify-space-between">
                      <div class="d-flex align-center justify-space-around">
                        <span
                          id="w-80"
                          :class="statusColor(item.Approval_Status)"
                          class="text-body-2 font-weight-regular d-flex justify-center align-center text-center"
                          >{{ checkNullValue(item.Approval_Status) }}</span
                        >
                      </div>
                    </section>
                  </td>

                  <td
                    :class="
                      isMobileView ? 'd-flex justify-center align-center' : ''
                    "
                  >
                    <div
                      v-if="isMobileView"
                      class="font-weight-bold d-flex justify-center align-center"
                      style="width: 100%"
                    >
                      Actions
                    </div>
                    <section
                      class="d-flex justify-center align-center"
                      style="width: 100%"
                    >
                      <ActionMenu
                        v-if="itemActions(item)?.length > 0"
                        @selected-action="onActions($event, item)"
                        :accessRights="checkAccess"
                        :actions="itemActions(item)"
                        iconColor="grey"
                        :disableActionButtons="getDisabledButtons(item)"
                        :tooltipActionButtons="getDisabledButtons(item)"
                        :tooltipMessage="getTooltipMessage(item)"
                      />
                      <div v-else>
                        <p>-</p>
                      </div>
                    </section>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </div>
    </v-container>
    <AppAccessDenied v-else />
    <AppLoading v-if="listLoading" />
    <ViewLeaveRequest
      v-if="showViewForm"
      :selected-item="selectedItem"
      :calling-from="callingFrom"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      @open-edit-form="openAddEditForm(true)"
      @close-view-form="closeAllForms()"
    />
    <AddEditLeaveRequest
      v-if="showEditForm"
      :selected-item="selectedItem"
      :calling-from="callingFrom"
      :is-edit="isEdit"
      :form-id="formId"
      :leave-settings="leaveSettings"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      :reason-type-list="reasonTypeList"
      @edit-updated="refetchList('updated')"
      @close-form="closeAllForms()"
    />
    <AppWarningModal
      v-if="deleteModel"
      :open-modal="deleteModel"
      confirmation-heading="Are you sure to delete the selected record?"
      icon-name="fas fa-trash"
      icon-Size="75"
      @close-warning-modal="closeAllForms()"
      @accept-modal="onDeleteLeave()"
    />
    <AppWarningModal
      v-if="multiDeleteModel"
      :open-modal="multiDeleteModel"
      confirmation-heading="Are you sure to delete the selected records?"
      confirmationSubText="Leave Records which are Applied, Cancelled, and Rejected can be deleted."
      icon-name="fas fa-trash"
      icon-Size="75"
      @close-warning-modal="closeAllForms()"
      @accept-modal="onDeleteLeave(true)"
    >
      <template v-slot:warningModalContent>
        <div class="text-body-1 text-center text-grey mx-4">
          Note: Leave records with the status Approved, Cancel Applied, or those
          automatically applied due to Attendance Shortage, Late Arrival, or
          Early Check-Out cannot be selected & deleted.
        </div>
      </template>
    </AppWarningModal>
    <ApprovalFlowModal
      v-if="openApprovalModal"
      :task-id="selectedItem.Workflow_Instance_Id"
      @close-modal="closeAllForms()"
    />
    <AppWarningModal
      v-if="cancelModel"
      :open-modal="cancelModel"
      confirmation-heading="Are you sure to cancel this leave request?"
      icon-name="far fa-times-circle"
      icon-Size="75"
      @close-warning-modal="closeAllForms()"
      @accept-modal="validateCancelLeave()"
    >
      <template v-slot:warningModalContent>
        <v-form ref="cancelForm">
          <div class="mt-2">
            <CustomSelect
              v-model="selectedCancelStatus"
              :items="cancelStatusList"
              :itemSelected="selectedCancelStatus"
              item-title="text"
              item-value="value"
              :rules="[required('Status', selectedCancelStatus)]"
              :is-required="true"
              label="Status"
              variant="solo"
              min-width="350px"
            />
            <v-textarea
              ref="Comment"
              v-model="selectedCancelComment"
              :rules="[
                cancelCommentMandatory
                  ? required('Comment', selectedCancelComment)
                  : true,
                validateWithRulesAndReturnMessages(
                  selectedCancelComment,
                  'departmentDescription',
                  'Comment'
                ),
              ]"
              rows="3"
              :clearable="true"
              auto-grow
              variant="solo"
              min-width="350px"
              ><template v-slot:label>
                Comment
                <span v-if="cancelCommentMandatory" style="color: red">*</span>
              </template></v-textarea
            >
          </div>
        </v-form>
      </template>
    </AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
// Async Components
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const ViewLeaveRequest = defineAsyncComponent(() =>
  import("./ViewLeaveRequest.vue")
);
const AddEditLeaveRequest = defineAsyncComponent(() =>
  import("./AddEditLeaveRequest.vue")
);
import ApprovalFlowModal from "@/components/custom-components/ApprovalFlowModal.vue";

import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import { GET_LIST_LEAVES } from "@/graphql/my-team/leaves.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "LeaveRequestList",
  components: {
    NotesCard,
    ActionMenu,
    flatPickr,
    CustomSelect,
    ViewLeaveRequest,
    AddEditLeaveRequest,
    ApprovalFlowModal,
  },
  props: {
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    formId: {
      type: Number,
      required: true,
    },
    callingFrom: {
      type: String,
      required: true,
    },
    leaveSettings: {
      type: Object,
      default: () => {},
    },
    filteredList: {
      type: Array,
      default: () => [],
    },
    landedFormName: {
      type: String,
      required: true,
    },
  },
  mixins: [FileExportMixin, validationRules],
  emits: ["send-list-data", "opened-forms"],
  data: () => ({
    isLoading: false,
    leavesLimitToCallAPI: 2500,
    totalApiCount: 0,
    apiCallCount: 0,
    reasonTypeList: [],
    originalList: [],
    itemList: [],
    startDate: null,
    endDate: null,
    selectedPeriod: null,
    selectedMonthYear: null,
    isEdit: false,
    selectedItem: null,
    showViewForm: false,
    showEditForm: false,
    deleteModel: false,
    multiDeleteModel: false,
    selectAllBox: false,
    openApprovalModal: false,
    // Cancel Model
    selectedCancelStatus: null,
    cancelStatusList: [],
    cancelModel: false,
    selectedCancelComment: null,
    cancelCommentMandatory: true,
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
    leaveListLoading: false,
    emptySearchValue: false,
  }),
  computed: {
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
      };
    },
    periodList() {
      let list = [
        "Last 7 Days",
        "This Month",
        "Last Month",
        "Next 90 Days",
        "Custom",
      ];
      if (this.callingFrom?.toLowerCase() === "myteam")
        list.unshift("Today", "Yesterday");
      return list;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      // return "https://capricetest.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
        },
      ];
      if (this.callingFrom?.toLowerCase() === "myteam") {
        actions.push({
          key: "Export Leave Balance based on Period",
        });
        if (this.selectedItems.length > 0) {
          actions.splice(0, 0, {
            key: "Delete",
          });
        }
      }
      return actions;
    },
    selectedItems() {
      return this.itemList.filter((item) => item.isSelected === true);
    },
    tableHeadersWithStatusBar() {
      // Only add the status bar column if not in mobile view
      let headers = [];
      if (!this.isMobileView) {
        headers.push({
          title: "",
          key: "status-bar",
          sortable: false,
          width: "8px",
          align: "start",
        });
      }
      if (
        this.callingFrom?.toLowerCase() === "myteam" &&
        Boolean(this.formAccess?.delete) &&
        !this.isMobileView
      ) {
        headers.push({
          title: "",
          key: "data-table-select",
          sortable: false,
          width: "36px",
        });
      }
      return [...headers, ...this.tableHeaders];
    },
    tableHeaders() {
      let headers = [];
      if (this.callingFrom?.toLowerCase() === "myteam")
        headers.push(
          {
            title: "Employee",
            key: "Employee_Name",
            value: (item) => `${item.Employee_Name} ${item.User_Defined_EmpId}`,
          },
          { title: "Leave", key: "Leave_Name" }
        );
      else
        headers.push({
          title: "Leave",
          key: "Leave_Name",
        });
      headers.push(
        { title: "Start Date", key: "Start_Date" },
        { title: "End Date", key: "End_Date" },
        { title: "Total Days", key: "Total_Days" },
        { title: "Status", key: "Approval_Status" },
        { title: "Actions", key: "action", align: "center", sortable: false }
      );
      return headers;
    },

    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList?.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isManager() {
      return this.$store.state.isManager;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    checkAccess() {
      let havingAccess = {};
      havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      havingAccess["cancel"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? 1 : 0;
      havingAccess["approval workflow"] = this.formAccess?.view ? 1 : 0;
      return havingAccess;
    },
  },

  watch: {
    showViewForm(val) {
      this.$emit("opened-forms", val || this.showEditForm);
    },
    showEditForm(val) {
      this.$emit("opened-forms", val || this.showViewForm);
    },
    filteredList: {
      handler(val) {
        if (val && val.length) this.itemList = val;
        else this.itemList = [];
      },
      deep: true,
    },
    selectedCancelStatus: {
      handler(val) {
        if (val?.toLowerCase() === "approved") {
          this.cancelCommentMandatory = false;
          this.selectedCancelComment = "";
        } else {
          this.cancelCommentMandatory = true;
          this.selectedCancelComment = null;
        }
      },
      immediate: true,
    },
    searchValue(val) {
      if (!val) this.resetFilter();
    },
  },

  mounted() {
    this.getCurrentDateRange();
    this.getDropdownDetails();
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    updateCurrentItems(val) {
      if (val.length === 0) {
        this.emptySearchValue = true;
        this.$emit("send-list-data", { list: [] });
      } else this.emptySearchValue = false;
    },
    enableToltipForDelete(item) {
      return (
        item.Approval_Status === "Approved" ||
        item.Approval_Status === "Cancel Applied" ||
        item.Late_Attendance == 1 ||
        item.Attendance_Shortage == 1 ||
        item.Early_Checkout == `Yes`
      );
    },
    getDisabledButtons(item) {
      let disabledButtons = [];

      // Add Delete button if it should be disabled
      if (this.enableToltipForDelete(item)) {
        disabledButtons.push("Delete");
      }

      // Add Cancel and Edit buttons if payslip is generated
      if (item.isPayslipGenerated) {
        disabledButtons.push("Cancel", "Edit");
        if (!disabledButtons?.includes("Delete"))
          disabledButtons.push("Delete");
      }

      return disabledButtons;
    },
    getTooltipMessage(item) {
      // If payslip is generated, show that message
      if (item.isPayslipGenerated) {
        return "Actions are disabled as the payslip has been generated";
      }

      // Otherwise show the default delete tooltip if needed
      if (this.enableToltipForDelete(item)) {
        return "Approved,Cancel Applied & auto-applied leave records cannot be deleted";
      }

      return "";
    },
    toggleSelectAll(value) {
      const shouldSelect = (item) =>
        !(
          item.Approval_Status === "Approved" ||
          item.Approval_Status === "Cancel Applied" ||
          item.Late_Attendance == 1 ||
          item.Attendance_Shortage == 1 ||
          item.Early_Checkout == `Yes`
        );
      if (value) {
        this.itemList.forEach((item) => {
          item.isSelected = shouldSelect(item);
        });
        this.selectAllBox = this.itemList?.some(shouldSelect) || false;
      } else {
        this.itemList.forEach((item) => {
          item.isSelected = false;
        });
      }
    },

    checkAllSelected() {
      const selectedItems = this.itemList.filter((el) => el.isSelected);
      const selectableItems = this.itemList.filter(
        (el) => !this.enableToltipForDelete(el)
      );
      this.selectAllBox =
        selectedItems.length === selectableItems.length &&
        selectableItems.length > 0;
    },
    getCurrentDateRange() {
      // Leave Date From = Current Month
      const leaveDateFrom = moment().startOf("month").format("YYYY-MM-DD");
      const leaveDateTo = moment().endOf("month").format("YYYY-MM-DD");
      this.selectedPeriod = "This Month";
      // Set the Date Array instead of String
      this.selectedMonthYear = [
        this.formatDate(leaveDateFrom),
        this.formatDate(leaveDateTo),
      ];
      this.startDate = leaveDateFrom;
      this.endDate = leaveDateTo;

      this.fetchList();
    },
    fetchList(type = "") {
      let vm = this;

      // Directly use the array
      let leaveStartDate = this.startDate;
      let leaveEndDate = this.endDate;

      if (
        !moment(leaveStartDate).isValid() ||
        !moment(leaveEndDate).isValid() ||
        !this.selectedMonthYear?.length ||
        !this.selectedPeriod
      ) {
        vm.showAlert({
          isOpen: true,
          message: "Please select a valid Date Range / Period.",
          type: "warning",
        });
        return;
      }

      vm.leaveListLoading = true;
      vm.$apollo
        .query({
          query: GET_LIST_LEAVES,
          client: "apolloClientAC",
          variables: {
            formId: parseInt(vm.formId),
            leaveStartDate: leaveStartDate,
            leaveEndDate: leaveEndDate,
            limit: vm.leavesLimitToCallAPI,
            offset: 0,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listLeaves &&
            response.data.listLeaves.leaveDetails &&
            !response.data.listLeaves.errorCode
          ) {
            let tempData =
              JSON.parse(response.data.listLeaves.leaveDetails) || [];
            tempData?.forEach((item) => {
              item.isSelected = false;
            }) || [];
            vm.selectAllBox = false;
            vm.originalList = tempData;
            vm.itemList = tempData;

            let { totalCount } = response.data.listLeaves;
            if (totalCount > 0) {
              totalCount = parseInt(totalCount);
              vm.apiCallCount = 1;
              vm.totalApiCount = Math.ceil(
                totalCount / vm.leavesLimitToCallAPI
              );
              for (let i = 1; i < vm.totalApiCount; i++) {
                vm.updateListLeaves(i, type);
              }
            }
            vm.$emit("send-list-data", { list: tempData, type });
          } else {
            vm.handleLeavesError(response.data.listLeaves?.errorCode || "");
          }
          vm.leaveListLoading = false;
        })
        .catch((err) => {
          vm.leaveListLoading = false;
          vm.handleLeavesError(err);
        });
    },
    updateListLeaves(index = 1, type = "") {
      let vm = this;
      vm.listLoading = true;
      let apiOffset = parseInt(index) * vm.leavesLimitToCallAPI;
      apiOffset = parseInt(apiOffset);
      vm.$apollo
        .query({
          query: GET_LIST_LEAVES,
          client: "apolloClientAC",
          variables: {
            formId: parseInt(vm.formId),
            leaveStartDate: vm.startDate,
            leaveEndDate: vm.endDate,
            limit: vm.leavesLimitToCallAPI,
            offset: apiOffset,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listLeaves &&
            response.data.listLeaves.leaveDetails &&
            !response.data.listLeaves.errorCode
          ) {
            const tempData =
              JSON.parse(response.data.listLeaves.leaveDetails) || [];
            tempData?.forEach((item) => {
              item.isSelected = false;
            }) || [];
            vm.originalList = [...vm.originalList, ...tempData];
            vm.itemList = [...vm.itemList, ...tempData];

            vm.apiCallCount = vm.apiCallCount + 1;
            if (vm.totalApiCount === vm.apiCallCount) {
              vm.$emit("send-list-data", { list: vm.originalList, type });
              vm.listLoading = false;
            }
          } else {
            vm.handleLeavesError(response.data.listLeaves?.errorCode || "");
          }
        })
        .catch((err) => {
          vm.handleLeavesError(err);
        });
    },
    itemActions(selectedItem = {}) {
      let items = [];
      // Condition for List the Cancel in ActionMenu
      let cancelApprovalStatus = ["Applied", "Approved"];
      let editableStatus = ["Applied", "Approved", "Returned"];
      let tzDate = new Date(); // Assuming tzDate() gives current timezone date
      let isManager = parseInt(this.formAccess.isManager); // Setting isManager based on Form
      let Role = this.formAccess.admin; // Setting Role based on Form

      // Extract values from selectedItem
      let {
        Late_Attendance,
        Approval_Status,
        Approver_Id,
        Attendance_Shortage,
        Early_Checkout,
        Restrict_Employee_To_Apply,
        Enable_Leave_Exception,
        Added_By,
        Employee_Id,
        Curr_Year,
        CO_Year,
        M,
        H,
      } = selectedItem;

      // Check if payslip is generated
      const isPayslipGenerated = M == 1 || H == 1;

      // ✅ 1. Condition for Showing **Cancel Button**
      let showCancel =
        (Late_Attendance == 0 ||
          (Late_Attendance == 1 &&
            Approval_Status == "Approved" &&
            ((isManager == 1 && Approver_Id == this.loginEmployeeId) ||
              Role == "admin"))) &&
        (Attendance_Shortage == 0 ||
          (Attendance_Shortage == 1 &&
            Approval_Status == "Approved" &&
            ((isManager == 1 && Approver_Id == this.loginEmployeeId) ||
              Role == "admin"))) &&
        (Early_Checkout == "No" ||
          (Early_Checkout == "Yes" &&
            Approval_Status == "Approved" &&
            ((isManager == 1 && Approver_Id == this.loginEmployeeId) ||
              Role == "admin"))) &&
        ((Restrict_Employee_To_Apply === 1 && Role == "admin") ||
          (Restrict_Employee_To_Apply === 0 &&
            ([Added_By, Employee_Id, Approver_Id].includes(
              this.loginEmployeeId
            ) ||
              Role == "admin"))) &&
        cancelApprovalStatus.includes(Approval_Status) &&
        (Curr_Year == "Current" || CO_Year < tzDate.getFullYear());

      // ✅ 2. Condition for Showing **Edit Button**
      let showEdit =
        (Role == "admin" ||
          (Restrict_Employee_To_Apply === 0 &&
            ([Added_By, Employee_Id].includes(this.loginEmployeeId) ||
              Approver_Id == this.loginEmployeeId))) &&
        editableStatus.includes(Approval_Status) &&
        Curr_Year == "Current" &&
        Late_Attendance == 0;

      // Exception handling for leave where exceptions are enabled
      if (Enable_Leave_Exception == "Yes" && showEdit) {
        showEdit = false;
      }

      // ✅ 3. Condition for Showing **Delete Button**
      let showDelete =
        Role == "admin" &&
        Approval_Status != "Approved" &&
        Approval_Status != "Cancel Applied" &&
        Late_Attendance != 1 &&
        Attendance_Shortage != 1;

      // ✅ Items actions based on conditions
      if (this.formAccess?.update && showCancel) items.push("Cancel");
      if (this.formAccess?.delete && showDelete) items.push("Delete");
      if (this.formAccess?.update && showEdit) items.push("Edit");
      if (selectedItem?.Workflow_Instance_Id) {
        items.push("Approval Workflow");
      }

      // Store payslip status in the item for tooltip display
      selectedItem.isPayslipGenerated = isPayslipGenerated;

      return items;
    },

    handleLeavesError(err = "") {
      this.listLoading = false;
      this.originalList = [];
      this.itemList = [];
      this.$emit("send-list-data", { list: [] });
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    async validateCancelLeave() {
      const { valid } = await this.$refs.cancelForm.validate();
      if (valid) this.onCancelLeave();
    },
    onCancelLeave() {
      let vm = this;
      vm.listLoading = true;

      // Get current year
      const currentYear = new Date().getFullYear();
      const leaveRecord = vm.selectedItem;
      const selectedApprovalStatus = vm.selectedCancelStatus;
      let isStatusAction = "";
      let Role = this.formAccess.admin; // Setting Role based on Form
      const isAuthorized =
        leaveRecord.Added_By === this.loginEmployeeId ||
        leaveRecord.Employee_Id === this.loginEmployeeId ||
        leaveRecord.Approver_Id === this.loginEmployeeId ||
        Role === "admin";
      const isCurrentYear =
        leaveRecord.Curr_Year === "Current" ||
        leaveRecord.CO_Year < currentYear;
      const isPayslipGenerated = leaveRecord.M === 1 || leaveRecord.H === 1;

      if (
        (leaveRecord.Approver_Id == this.loginEmployeeId || Role === "admin") &&
        leaveRecord.Approval_Status === "Applied" &&
        selectedApprovalStatus !== "Cancelled"
      ) {
        isStatusAction = "Approve";
      } else if (
        isAuthorized &&
        leaveRecord.Approval_Status === "Applied" &&
        isCurrentYear &&
        !isPayslipGenerated
      ) {
        isStatusAction = "CancelAppliedLeave";
      } else if (
        (leaveRecord.Approver_Id === this.loginEmployeeId ||
          Role === "admin") &&
        leaveRecord.Approval_Status === "Cancel Applied" &&
        isCurrentYear
      ) {
        isStatusAction = "CancelApprove";
      } else if (
        isAuthorized &&
        leaveRecord.Approval_Status === "Approved" &&
        isCurrentYear &&
        !isPayslipGenerated
      ) {
        isStatusAction = "CancelApply";
      }

      // Prepare API request object
      const apiObj = {
        url: vm.baseUrl + "employees/leaves/leave-status",
        type: "POST",
        dataType: "json",
        data: {
          isAction: isStatusAction,
          Approver_Id: parseInt(leaveRecord.Approver_Id),
          Employee_Id: parseInt(leaveRecord.Employee_Id),
          Added_By: parseInt(leaveRecord.Added_By),
          Status: selectedApprovalStatus,
          Comments: vm.selectedCancelComment,
          ESIC_Reason: leaveRecord.ESIC_Reason ? leaveRecord.ESIC_Reason : "",
          Leave_Id: parseInt(leaveRecord.Leave_Id),
          documentList: leaveRecord.Document_File_Path?.length
            ? leaveRecord.Document_File_Path
            : [],
        },
      };
      // Call the API using Vuex Action
      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.success) {
            vm.showAlert({
              isOpen: true,
              type: "success",
              message: res.msg ? res.msg : "Leave record updated successfully.",
            });
            vm.refetchList("updated");
            vm.closeAllForms();
          } else {
            vm.showAlert({
              isOpen: true,
              type: res?.type || "warning",
              message:
                res?.msg && res?.msg?.length
                  ? res?.msg
                  : "Something went wrong while updating the leave record.",
            });
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          this.$store.dispatch("handleApiErrors", {
            error: err,
            action: "cancel",
            form: this.landedFormName,
            isListError: false,
          });
        });
    },
    onDeleteLeave(isMultiDelete = false) {
      let vm = this;
      vm.listLoading = true;
      const apiObj = {
        url: vm.baseUrl + "employees/leaves/delete-leave",
        type: "POST",
        async: false,
        dataType: "json",
        data: {
          leaveId: isMultiDelete
            ? this.selectedItems.map((item) => item.Leave_Id)
            : [vm.selectedItem.Leave_Id],
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.success) {
            vm.showAlert({
              isOpen: true,
              type: "success",
              message: res.msg
                ? res.msg
                : "Leave record(s) deleted successfully.",
            });
            vm.refetchList("updated");
            vm.closeAllForms();
          } else
            vm.showAlert({
              isOpen: true,
              type: res?.type || "warning",
              message:
                res?.msg && res?.msg?.length
                  ? res?.msg
                  : "Something went wrong while deleting the leave record(s). Please try after some time.",
            });

          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          this.$store.dispatch("handleApiErrors", {
            error: err,
            action: "delete",
            form: this.landedFormName,
            isListError: false,
          });
        });
    },

    openAddEditForm(openEdit = false) {
      if (openEdit) this.isEdit = true;
      this.showViewForm = false;
      this.showEditForm = true;
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showEditForm = false;
    },

    closeAllForms() {
      this.showEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.deleteModel = false;
      this.cancelModel = false;
      this.selectedCancelStatus = null;
      this.selectedCancelComment = null;
      this.openApprovalModal = false;
      this.multiDeleteModel = false;
      this.selectAllBox = false;
      this.originalList?.map((item) => {
        item.isSelected = false;
      });
      this.itemList?.map((item) => {
        item.isSelected = false;
      });
    },

    refetchList(type = "") {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList(type);
      this.resetFilter(type);
    },
    resetFilter(type = "") {
      this.emptySearchValue = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$emit("send-list-data", { list: this.originalList, type });
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      } else if (actionType === "Export Leave Balance based on Period") {
        let url = this.baseUrl + "employees/leaves/export-leave-history/";
        window.open(url, "_blank");
      } else if (actionType === "Delete" && this.selectedItems.length > 0) {
        this.multiDeleteModel = true;
      }
      this.openMoreMenu = false;
    },
    onActions(action, item) {
      this.selectedItem = item;

      // Check if the action is disabled due to payslip generation
      if (
        item.isPayslipGenerated &&
        (action === "Cancel" || action === "Edit" || action === "Delete")
      )
        return;

      if (action === "Cancel") {
        this.prepareCancelStatus(item);
        this.cancelModel = true;
      } else if (action === "Delete" && !this.enableToltipForDelete(item))
        this.deleteModel = true;
      else if (action === "Edit") this.openAddEditForm(true);
      else if (action?.toLowerCase() === "approval workflow")
        this.openApprovalModal = true;
    },

    prepareCancelStatus(item) {
      this.cancelStatusList = [];
      let Role = this.formAccess?.admin || "";

      // Approval Status
      let recordApprovalStatus = item.Approval_Status;
      let cancelApprovalStatus = ["Applied", "Approved"];

      // Check if payslip is generated
      if (item.M == 1 || item.H == 1) {
        this.closeAllForms();
        return;
      }

      // Allow only if condition matches
      if (
        (item.Late_Attendance == 0 ||
          (item.Late_Attendance == 1 && recordApprovalStatus == "Approved")) &&
        ([item.Added_By, item.Employee_Id, item.Approver_Id].includes(
          this.loginEmployeeId
        ) ||
          Role === "admin") &&
        cancelApprovalStatus.includes(recordApprovalStatus) &&
        (item.Curr_Year == "Current" || item.CO_Year < new Date().getFullYear())
      ) {
        if (recordApprovalStatus === "Approved") {
          this.cancelStatusList.push({
            text: "Cancel Leave",
            value: "Cancel Applied",
          });
          this.selectedCancelStatus = "Cancel Applied";
        } else if (recordApprovalStatus === "Applied") {
          this.cancelStatusList.push({
            text: "Cancel Leave",
            value: "Cancelled",
          });
          this.selectedCancelStatus = "Cancelled";
        }
      } else {
        // If user does not have permission, show alert and close modal
        this.showAlert({
          isOpen: true,
          msg: "Sorry, Access Denied",
          type: "warning",
        });
        this.closeAllForms();
      }
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        User_Defined_EmpId: String(el.User_Defined_EmpId ?? ""),
        Start_Date: el.Start_Date ? this.formatDate(el.Start_Date) : "",
        End_Date: el.End_Date ? this.formatDate(el.End_Date) : "",
        Late_Attendance: el.Late_Attendance ? "Yes" : "No",
        // Added_On: el.Added_On ? this.convertUTCToLocal(el.Added_On) : "",
        // Updated_On: el.Updated_On ? this.convertUTCToLocal(el.Updated_On) : "",
      }));
      let headers = [];
      if (this.callingFrom?.toLowerCase() === "myteam")
        headers.push(
          {
            header: "Employee Id",
            key: "User_Defined_EmpId",
          },
          {
            header: "Employee",
            key: "Employee_Name",
          },
          {
            header: "Department",
            key: "Department_Name",
          },
          {
            header: "Designation",
            key: "Designation_Name",
          }
        );

      headers.push(
        {
          header: "Leave",
          key: "Leave_Name",
        },
        {
          header: "Reason",
          key: "Reason",
        },
        {
          header: "Duration",
          key: "Duration",
        },
        {
          header: "Leave Period",
          key: "Leave_Period",
        },
        {
          header: "Start Date",
          key: "Start_Date",
        },
        {
          header: "End Date",
          key: "End_Date",
        },
        {
          header: "Total Days",
          key: "Total_Days",
        },
        {
          header: "Hours",
          key: "Hours",
        }
      );
      if (this.callingFrom?.toLowerCase() === "myteam")
        headers.push({
          header: "Contact Details",
          key: "Contact_Details",
        });
      headers.push(
        {
          header: "Alternate Person",
          key: "Alternate_Person_Name",
        },
        {
          header: "Late Arrival",
          key: "Late_Attendance",
        },
        {
          header: "Late Arrival Hours",
          key: "Late_Attendance_Hours",
        },
        {
          header: "Early Checkout",
          key: "Early_Checkout",
        },
        {
          header: "Early Checkout Hours",
          key: "Early_Checkout_Hours",
        },
        {
          header: "Status",
          key: "Approval_Status",
        },
        { header: "Added On", key: "Added_On" },
        { header: "Added By", key: "Added_By_Name" },
        { header: "Updated On", key: "Updated_On" },
        { header: "Updated By", key: "Updated_By_Name" }
      );
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: headers,
      };
      this.exportExcelFile(exportOptions);
    },
    leftStatusBarClass(status) {
      const s = (status || "").toLowerCase();
      if (s === "approved" || s === "cancelled" || s === "cancel approved") {
        return "left-status-bar-green";
      } else if (s === "rejected") {
        return "left-status-bar-red";
      } else if (s === "applied" || s === "cancel applied") {
        return "left-status-bar-amber";
      }
      return "";
    },
    statusColor(status) {
      switch (status) {
        case "Applied":
          return "text-primary";
        case "Approved":
          return "text-green";
        case "Rejected":
          return "text-red";
        case "Returned":
          return "text-amber";
        case "Cancelled":
          return "text-amber";
        default:
          return "";
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    resetDateFilters() {
      this.selectedPeriod = "Custom";
      this.startDate = null;
      this.endDate = null;
      this.selectedMonthYear = null;
    },
    onChangePeriod(period) {
      if (period === "Custom") {
        this.selectedMonthYear = null;
      } else {
        let startDate, endDate;

        switch (period) {
          case "Today":
            startDate = endDate = moment().format("YYYY-MM-DD");
            break;
          case "Yesterday":
            startDate = endDate = moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD");
            break;
          case "Last 7 Days":
            startDate = moment().subtract(6, "days").format("YYYY-MM-DD");
            endDate = moment().format("YYYY-MM-DD");
            break;
          case "This Month":
            startDate = moment().startOf("month").format("YYYY-MM-DD");
            endDate = moment().endOf("month").format("YYYY-MM-DD");
            break;
          case "Last Month":
            startDate = moment()
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD");
            endDate = moment()
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD");
            break;
          case "Next 90 Days":
            startDate = moment().format("YYYY-MM-DD");
            endDate = moment().add(90, "days").format("YYYY-MM-DD");
            break;
        }

        if (startDate && endDate) {
          this.selectedMonthYear = [
            this.formatDate(startDate),
            this.formatDate(endDate),
          ];
        }
      }
    },

    onChangeDateRange(selectedDates) {
      if (selectedDates?.length > 1) {
        // Format the start and end dates
        let leaveStartDate = moment(selectedDates[0]).format("YYYY-MM-DD");
        let leaveEndDate = moment(selectedDates[1]).format("YYYY-MM-DD");

        // Calculate the difference in days
        let dateDifference = moment(leaveEndDate).diff(
          moment(leaveStartDate),
          "days"
        );
        const differenceAllowed =
          this.callingFrom?.toLowerCase() === "myteam" ? 90 : 365;
        // Prevent if the range is more than difference allowed days
        if (dateDifference > differenceAllowed) {
          this.selectedMonthYear = [
            this.formatDate(this.startDate),
            this.formatDate(this.endDate),
          ];
          this.showAlert({
            isOpen: true,
            message: `The selected date range cannot exceed ${differenceAllowed} days. Please select a valid date range.`,
            type: "warning",
          });
          return;
        }
        if (
          moment(selectedDates[0]).format("YYYY-MM-DD") != this.startDate ||
          moment(selectedDates[1]).format("YYYY-MM-DD") != this.endDate
        ) {
          // Set the dates and fetch the list
          this.startDate = leaveStartDate;
          this.endDate = leaveEndDate;
          this.fetchList();
        }
      }
    },
    getDropdownDetails() {
      let vm = this;
      vm.isLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;
      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: vm.formId,
            key: ["esic_reason"],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "esic_reason")
                vm.reasonTypeList = item.data.filter(
                  (dataItem) => dataItem.Form_Id === 31
                );
            });
          } else {
            let err = res.data.retrieveDropdownDetails?.errorCode || "";
            vm.handleGetDropdownDetails(err);
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "dropdown details",
        isListError: false,
      });
    },
  },
};
</script>

<style scoped>
.left-status-bar {
  width: 7px;
  height: 100%;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  display: block;
}
.left-status-bar-green {
  background-color: #2ecc40;
}
.left-status-bar-red {
  background-color: #ff4d4f;
}
.left-status-bar-amber {
  background-color: #ffc107;
}
.row-bg-green,
.row-bg-red,
.row-bg-amber {
  background: none !important;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__value) {
  min-width: 160px;
  display: flex;
  align-items: center;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__calendar) {
  right: 1px;
}
/* Custom styling for dimmed checkboxes */
:deep(.dimmed-checkbox) {
  opacity: 0.6;
}

:deep(.dimmed-checkbox .v-selection-control__input) {
  cursor: not-allowed;
}

:deep(.dimmed-checkbox .v-selection-control__input i) {
  color: rgba(0, 0, 0, 0.38) !important;
}
</style>
