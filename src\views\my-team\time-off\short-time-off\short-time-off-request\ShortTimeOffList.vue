<template>
  <div>
    <v-container fluid class="shorttimeoff">
      <div v-if="listLoading" class="mt-3">
        <v-skeleton-loader
          ref="skeleton1"
          type="table-heading"
          class="mx-auto"
        ></v-skeleton-loader>
        <div v-for="i in 4" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>
      <AppFetchErrorScreen
        v-else-if="isErrorInList"
        :content="errorContent"
        key="error-screen"
        icon-name="fas fa-redo-alt"
        image-name="common/human-error-image"
        button-text="Retry"
        @button-click="refetchList('updated')"
      ></AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="originalList?.length === 0"
        key="no-data-screen"
        :main-title="emptyScenarioMsg"
        :isSmallImage="!isFilter"
        :image-name="!isFilter ? '' : 'common/no-records'"
      >
        <template #contentSlot>
          <div
            :style="
              isMobileView
                ? 'max-width: 95%; margin: 0 auto;'
                : 'max-width: 80%; margin: 0 auto;'
            "
            :class="isMobileView ? 'px-2' : ''"
          >
            <v-row class="rounded-lg mb-4" style="background: white">
              <v-col cols="12"
                ><NotesCard
                  v-if="showTeam"
                  notes="Short Time-off feature enables employees to request a limited duration of time away from work during their scheduled working hours, without the need to apply for a full-day or half-day leave. It is typically used for brief personal needs such as errands, medical appointments, or other short-duration commitments."
                  backgroundColor="transparent"
                  class="mb-4"
                />
                <NotesCard
                  v-if="showTeam"
                  notes="Organizations can define the maximum duration allowed per request and set a limit on the number of short time off permissions. Each request follows an approval workflow, where it is sent to the employee's reporting manager or designated approver to ensure accountability and proper tracking."
                  backgroundColor="transparent"
                  class="mb-4"
                />
                <NotesCard
                  v-if="!showTeam"
                  notes="This module enables employees to request a brief period away from work during their scheduled working hours without applying for a full-day or half-day leave. This is particularly useful for personal tasks such as medical appointments, bank visits, or other short-duration needs that do not require a full day's absence."
                  backgroundColor="transparent"
                  class="mb-4"
                />
                <NotesCard
                  v-if="!showTeam"
                  notes="Employees can initiate permission requests directly through the ESS portal by specifying the reason, date, and the start and end times of the requested time off. Once submitted, the request follows an approval workflow where it is routed to the employee's reporting manager or designated approver for review and action."
                  backgroundColor="transparent"
                  class="mb-4"
                />
              </v-col>
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <div
                  class="d-flex align-center flex-wrap"
                  :class="isMobileView ? 'justify-center flex-column' : ''"
                  style="gap: 8px"
                >
                  <CustomSelect
                    v-model="selectedPeriod"
                    :items="periodList"
                    :itemSelected="selectedPeriod"
                    :isAutoComplete="true"
                    variant="solo"
                    class="mt-3"
                    label="Period"
                    placeholder="Select Period"
                    @click="resetSelection"
                    density="compact"
                    min-width="150px"
                    :max-width="isMobileView ? `200px` : `300px`"
                    @selected-item="onChangePeriod($event)"
                  />
                  <v-btn
                    class="bg-white mb-3 mx-4"
                    :style="'width: max-content'"
                    :size="isMobileView ? 'small' : 'default'"
                    rounded="lg"
                    @click="$refs.datePicker.fp.open(), resetDateFilters()"
                  >
                    <v-icon color="primary" size="14"
                      >fas fa-calendar-alt</v-icon
                    >
                    <span v-if="!isMobileView" class="text-caption px-1 pt-1"
                      >Date:</span
                    >
                    <flat-pickr
                      ref="datePicker"
                      v-model="selectedMonthYear"
                      :config="flatPickerOptions"
                      placeholder="Select Date Range"
                      class="ml-2 mt-1 date-range-picker-custom-bg"
                      style="outline: 0px; color: var(--v-primary-base)"
                      :style="`width: ${isMobileView ? `160px` : `170px`}`"
                      @onChange="onChangeDateRange"
                    />
                  </v-btn>
                  <v-btn
                    v-if="formAccess?.add"
                    variant="elevated"
                    rounded="lg"
                    prepend-icon="fas fa-plus"
                    class="primary"
                    @click="openAddEditForm()"
                  >
                    <template v-slot:prepend>
                      <v-icon size="15" class="pr-1 primary"></v-icon>
                    </template>
                    <span class="primary">Apply</span>
                  </v-btn>
                  <v-btn
                    color="transparent"
                    variant="flat"
                    class="ml-2 mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList('updated')"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <AppFetchErrorScreen
        v-else-if="itemList?.length === 0 || emptySearchValue"
        key="no-results-screen"
        main-title="There are no short time off matched for the selected filters/searches."
        image-name="common/no-records"
      >
        <template #contentSlot>
          <div style="max-width: 80%">
            <v-row class="rounded-lg pa-5 mb-4">
              <v-col cols="12" class="d-flex align-center justify-center mb-4">
                <v-btn
                  variant="elevated"
                  color="primary"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="windowWidth <= 960 ? 'small' : 'default'"
                  @click="resetFilter('grid')"
                >
                  <span class="primary">Reset Filter/Search </span>
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </template>
      </AppFetchErrorScreen>
      <div v-else>
        <div
          v-if="originalList?.length > 0"
          class="d-flex flex-wrap align-center"
          :class="isMobileView ? 'flex-column' : ''"
          style="justify-content: space-between"
        >
          <div
            class="d-flex align-center flex-wrap"
            :class="isMobileView ? 'justify-center' : ''"
          >
            <CustomSelect
              v-model="selectedPeriod"
              :items="periodList"
              :itemSelected="selectedPeriod"
              :isAutoComplete="true"
              variant="solo"
              class="mt-3"
              label="Period"
              placeholder="Select Period"
              @click="resetSelection"
              density="compact"
              min-width="180px"
              max-width="500px"
              @selected-item="onChangePeriod($event)"
            />
            <v-btn
              class="bg-white mb-3 ml-2"
              :style="'width: max-content'"
              :size="isMobileView ? 'small' : 'default'"
              rounded="lg"
              @click="$refs.datePicker.fp.open(), resetDateFilters()"
            >
              <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
              <span class="text-caption px-1 pt-1">Date:</span>
              <flat-pickr
                ref="datePicker"
                v-model="selectedMonthYear"
                :config="flatPickerOptions"
                placeholder="Select Date Range"
                class="ml-2 mt-1 date-range-picker-custom-bg"
                style="outline: 0px; color: var(--v-primary-base); width: 170px"
                @onChange="onChangeDateRange"
              />
            </v-btn>
          </div>

          <div
            class="d-flex align-center"
            :class="isMobileView ? 'justify-center' : 'justify-end'"
          >
            <v-btn
              v-if="formAccess?.add"
              prepend-icon="fas fa-plus"
              color="primary"
              variant="elevated"
              rounded="lg"
              :size="isMobileView ? 'small' : 'default'"
              @click="openAddEditForm()"
            >
              <template v-slot:prepend>
                <v-icon></v-icon>
              </template>
              Apply
            </v-btn>
            <v-btn
              rounded="lg"
              color="transparent"
              variant="flat"
              class="mt-1"
              :size="isMobileView ? 'small' : 'default'"
              @click="refetchList('updated')"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>

            <v-menu class="mb-1" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                  <v-icon>fas fa-ellipsis-v</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'bg-hover': isHovering,
                        }"
                        >{{ action.key }}</v-list-item-title
                      >
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
        <v-row>
          <v-col cols="12">
            <v-data-table
              v-model="selectedShortRecords"
              :headers="tableHeadersWithStatusBar"
              :items="itemList"
              :show-select="showTeam && formAccess?.delete && !isMobileView"
              :items-per-page="50"
              fixed-header
              :height="
                $store.getters.getTableHeightBasedOnScreenSize(
                  250,
                  itemList,
                  true
                )
              "
              item-value="Short_Time_Off_Id"
              class="elevation-1"
              style="box-shadow: none !important"
              :sort-by="[{ key: 'Short_Time_Off_Date', order: 'desc' }]"
              :search="searchValue"
              @update:currentItems="updateCurrentItems($event)"
            >
              <template v-slot:[`header.data-table-select`]>
                <v-checkbox-btn
                  v-model="selectAllBox"
                  color="primary"
                  false-icon="far fa-circle"
                  true-icon="fas fa-check-circle"
                  indeterminate-icon="fas fa-minus-circle"
                  class="mt-1"
                  @change="toggleSelectAll(selectAllBox)"
                ></v-checkbox-btn>
              </template>
              <template v-slot:item="{ item }">
                <tr
                  style="z-index: 200"
                  class="data-table-tr bg-white cursor-pointer"
                  @click="openViewForm(item)"
                  :class="[
                    isMobileView
                      ? ' v-data-table__mobile-table-row ma-0 mt-2'
                      : '',
                  ]"
                >
                  <td
                    v-if="!isMobileView"
                    class="pa-0"
                    style="width: 8px; position: relative"
                  >
                    <div
                      :class="[
                        'left-status-bar',
                        leftStatusBarClass(item.Approval_Status),
                      ]"
                      style="position: absolute; top: 0; bottom: 0; left: 0"
                    ></div>
                  </td>
                  <td v-if="showTeam && formAccess?.delete && !isMobileView">
                    <div class="d-flex justify-center align-center">
                      <v-checkbox-btn
                        v-model="item.isSelected"
                        color="primary"
                        false-icon="far fa-circle"
                        true-icon="fas fa-check-circle"
                        class="mt-n2 ml-n2"
                        @click.stop="checkAllSelected()"
                        :disabled="enableTooltipForDelete(item)"
                      ></v-checkbox-btn>
                    </div>
                  </td>
                  <td
                    v-if="showTeam"
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-small'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Employee
                    </div>
                    <section class="d-flex align-center">
                      <v-tooltip
                        :text="item.Employee_Name"
                        location="bottom"
                        max-width="400"
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                            v-bind="props"
                          >
                            {{ checkNullValue(item.Employee_Name) }}
                            <div
                              v-if="item?.User_Defined_EmpId"
                              class="text-grey"
                            >
                              {{ checkNullValue(item.User_Defined_EmpId) }}
                            </div>
                          </div>
                        </template>
                      </v-tooltip>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-medium'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Request For
                    </div>
                    <section
                      class="text-body-2 text-truncate"
                      style="max-width: 150px"
                    >
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(item.Request_For) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Start Date & Time
                    </div>
                    <section
                      class="text-body-2 text-truncate"
                      style="max-width: 150px"
                    >
                      <span class="text-body-2 font-weight-regular">
                        {{ formatDateTime(item.View_Start_Date_Time) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      End Date & Time
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ formatDateTime(item.View_End_Date_Time) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? 'font-weight-bold d-flex align-center'
                          : 'font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Status
                    </div>
                    <section class="d-flex align-center justify-space-between">
                      <div class="d-flex align-center justify-space-around">
                        <span
                          id="w-80"
                          :class="statusColor(item.Approval_Status)"
                          class="text-body-2 font-weight-regular d-flex justify-center align-center text-center"
                          >{{ checkNullValue(item.Approval_Status) }}</span
                        >
                      </div>
                    </section>
                  </td>

                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-center align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      class="font-weight-bold d-flex justify-center align-center"
                      style="width: 100%"
                    >
                      Actions
                    </div>
                    <section
                      class="d-flex justify-center align-center"
                      style="width: 100%"
                    >
                      <ActionMenu
                        v-if="getAvailableActions(item)?.length > 0"
                        @selected-action="onActions($event, item)"
                        :accessRights="checkAccess"
                        :actions="getAvailableActions(item)"
                        iconColor="grey"
                        :disableActionButtons="
                          enableTooltipForDelete(item) ? [`Delete`] : []
                        "
                        :tooltipActionButtons="
                          enableTooltipForDelete(item) ? [`Delete`] : []
                        "
                        :tooltipMessage="
                          enableTooltipForDelete(item)
                            ? `Approved, Cancel Applied & auto-applied leave records cannot be deleted`
                            : ``
                        "
                      ></ActionMenu>
                      <div v-else>
                        <p>-</p>
                      </div>
                    </section>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </div>
    </v-container>
    <AddEditShortTimeOff
      v-if="showAddEditForm"
      :selected-item="isEdit ? selectedItem : {}"
      :is-edit="isEdit"
      :callingFrom="callingFrom"
      :form-id="formId"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      @edit-updated="refetchList('updated')"
      @close-form="closeAddEditForm"
    />
    <ViewShortTimeOff
      v-if="showViewForm"
      :callingFrom="callingFrom"
      :selected-item="selectedItem"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      @open-edit-form="openAddEditForm(true, $event)"
      @close-view-form="closeAllForms()"
    />
    <AppWarningModal
      v-if="deleteModel"
      :open-modal="deleteModel"
      confirmation-heading="Are you sure to delete the selected record?"
      icon-name="fas fa-trash"
      icon-Size="75"
      @close-warning-modal="closeAllForms()"
      @accept-modal="onDeleteShortTimeOff()"
    />
    <AppWarningModal
      v-if="multiDeleteModel"
      :open-modal="multiDeleteModel"
      confirmation-heading="Are you sure to delete the selected records?"
      confirmationSubText="Short Time Off Records which are Applied, Cancelled, and Rejected can be deleted."
      icon-name="fas fa-trash"
      icon-Size="75"
      @close-warning-modal="closeAllForms()"
      @accept-modal="onDeleteShortTimeOff()"
    >
      <template v-slot:warningModalContent>
        <div class="text-body-1 text-center text-grey mx-4">
          Note: Short Time Off records with the status Approved, Cancel Applied,
          or those automatically applied due to Late Arrival, or Early Check-Out
          cannot be selected & deleted.
        </div>
      </template>
    </AppWarningModal>
    <AppWarningModal
      v-if="cancelModel"
      :open-modal="cancelModel"
      confirmation-heading="Are you sure to cancel the selected record?"
      icon-name="far fa-times-circle"
      icon-Size="75"
      :acceptButtonDisable="!selectedCancelComment"
      @close-warning-modal="closeAllForms()"
      @accept-modal="validateCancelShortTimeOff()"
    >
      <template v-slot:warningModalContent>
        <v-form ref="cancelForm">
          <div class="mt-2">
            <CustomSelect
              v-model="selectedCancelStatus"
              :items="cancelStatusList"
              :itemSelected="selectedCancelStatus"
              item-title="text"
              item-value="value"
              :rules="[required('Status', selectedCancelStatus)]"
              :is-required="true"
              label="Status"
              variant="solo"
              min-width="350px"
            />
            <v-textarea
              ref="Comment"
              v-model="selectedCancelComment"
              :rules="[
                required('Comment', selectedCancelComment),
                validateWithRulesAndReturnMessages(
                  selectedCancelComment,
                  'departmentDescription',
                  'Comment'
                ),
              ]"
              rows="3"
              :clearable="true"
              auto-grow
              variant="solo"
              min-width="350px"
              ><template v-slot:label>
                Comment
                <span style="color: red">*</span>
              </template></v-textarea
            >
          </div>
        </v-form>
      </template>
    </AppWarningModal>
    <ApprovalFlowModal
      v-if="openApprovalModal"
      :task-id="selectedItem?.Process_Instance_Id"
      @close-modal="closeAllForms()"
    />
    <AppLoading v-if="isLoading" />
  </div>
</template>

<script>
import Config from "@/config.js";
import { defineAsyncComponent } from "vue";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
// Async Components
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const AddEditShortTimeOff = defineAsyncComponent(() =>
  import("../AddEditShortTimeOff.vue")
);
const ViewShortTimeOff = defineAsyncComponent(() =>
  import("./ViewShortTimeOff.vue")
);
const ApprovalFlowModal = defineAsyncComponent(() =>
  import("@/components/custom-components/ApprovalFlowModal.vue")
);
import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";
import validationRules from "@/mixins/validationRules";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { LIST_SHORT_TIME_OFF } from "@/graphql/my-team/shortTimeOff.js";
import mixpanel from "mixpanel-browser";

export default {
  name: "ShortTimeOffList",
  components: {
    NotesCard,
    ActionMenu,
    flatPickr,
    CustomSelect,
    AddEditShortTimeOff,
    ViewShortTimeOff,
    ApprovalFlowModal,
  },
  props: {
    formId: {
      type: Number,
      required: true,
    },
    callingFrom: {
      type: String,
      required: true,
    },
    filteredList: {
      type: Array,
      default: () => [],
    },
  },

  mixins: [FileExportMixin, validationRules],
  emits: ["send-list-data"],
  data: () => ({
    selectedShortTimeOffRecords: [],
    selectAllBox: false,
    originalList: [],
    itemList: [],
    isFilter: false,
    startDate: null,
    endDate: null,
    selectedPeriod: "This Month",
    selectedMonthYear: null,
    isEdit: false,
    selectedItem: null,
    showViewForm: false,
    showAddEditForm: false,
    deleteModel: false,
    multiDeleteModel: false,
    openApprovalModal: false,
    // Cancel Model
    selectedCancelStatus: null,
    cancelStatusList: [
      { text: "Cancel Short Time Off", value: "Cancel Short Time Off" },
    ],
    cancelModel: false,
    selectedCancelComment: null,
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
    isLoading: false,
    selectedShortRecords: [],
    currentTabItem: "",
    emptySearchValue: false,
  }),
  computed: {
    landedFormName() {
      return "Short Time Off";
    },
    formAccess() {
      let formAccess =
        this.callingFrom === "myTeam"
          ? this.accessRights("352")
          : this.accessRights("353");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
      };
    },
    periodList() {
      let list = [
        "Last 7 Days",
        "This Month",
        "Last Month",
        "Next 90 Days",
        "Custom",
      ];

      if (this.showTeam) list.unshift("Today", "Yesterday");
      return list;
    },
    presentPendingApprovalButton() {
      return (
        this.itemList?.some(
          (el) =>
            !el.Process_Instance_Id &&
            (el.Approval_Status?.toLowerCase() === "applied" ||
              el.Approval_Status?.toLowerCase() === "cancel applied")
        ) || false
      );
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    myTeamTimeOffFormAccess() {
      return this.$store.getters.myTeamTimeOffFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.myTeamTimeOffFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        if (this.approvalManagementFormAccess) {
          const shortTimeOffIndex = formAccessArray.indexOf("Short Time Off");
          const reportsLabel =
            this.accessRights(348)?.customFormName || "Reports";
          const reportsIndex = formAccessArray.indexOf(reportsLabel);

          // Remove Approvals first if already present
          formAccessArray = formAccessArray.filter(
            (item) => item !== "Approvals"
          );

          if (
            shortTimeOffIndex !== -1 &&
            reportsIndex !== -1 &&
            shortTimeOffIndex < reportsIndex
          ) {
            formAccessArray.splice(reportsIndex, 0, "Approvals");
          } else {
            formAccessArray.push("Approvals");
          }
        }
        return formAccessArray;
      }
      return [];
    },
    approvalManagementFormAccess() {
      let formAccessRights = this.accessRights(184);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [];

      // Only add Delete action if user has delete access
      if (this.showTeam && this.formAccess && this.formAccess.delete) {
        actions.push({
          key: "Delete",
        });
      }

      // Always add Export action
      actions.push({
        key: "Export",
      });

      return actions;
    },
    tableHeaders() {
      let headers = [];

      if (this.showTeam) {
        headers.push({
          title: "Employee",
          key: "Employee_Name",
          value: (item) => `${item.Employee_Name} ${item.User_Defined_EmpId}`,
          align: "start",
        });
      }

      headers.push(
        { title: "Request For", key: "Request_For", align: "start" },
        { title: "Start Date & Time", key: "Start_Date_Time" },
        { title: "End Date & Time", key: "End_Date_Time" },
        { title: "Status", key: "Approval_Status" },
        { title: "Actions", key: "action", align: "center", sortable: false }
      );

      return headers;
    },
    showTeam() {
      return this.callingFrom === "myTeam";
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList?.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    formatDate() {
      return (date, withTime = false) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    formatDateTime() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat =
            this.$store.state.orgDetails.orgDateFormat + " HH:mm";
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isManager() {
      return this.$store.state.isManager;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    checkAccess() {
      let havingAccess = {};
      havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      havingAccess["cancel"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? 1 : 0;
      havingAccess["approval workflow"] = this.formAccess?.view ? 1 : 0;
      return havingAccess;
    },
    tableHeadersWithStatusBar() {
      // Only add the status bar column if not in mobile view
      let headers = [];
      if (!this.isMobileView) {
        headers.push({
          title: "",
          key: "status-bar",
          sortable: false,
          width: "8px",
        });
      }
      if (this.showTeam && this.formAccess?.delete && !this.isMobileView) {
        headers.push({
          title: "",
          key: "data-table-select",
          sortable: false,
          width: "36px",
        });
      }
      return [...headers, ...this.tableHeaders];
    },
  },

  watch: {
    searchValue(val) {
      if (!val) this.resetFilter();
    },
    filteredList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.itemList = [...newVal];
        } else this.itemList = [];
      },
      deep: true,
    },
    selectedShortRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.Short_Time_Off_Id)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through itemList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);

    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    // If we have filtered list from parent, use it, otherwise fetch data
    if (this.filteredList && this.filteredList.length > 0) {
      this.itemList = this.filteredList;
      this.originalList = [...this.filteredList];
      // Make sure period is set to This Month by default
      if (!this.selectedMonthYear) {
        this.onChangePeriod("This Month");
      }
    } else {
      this.getCurrentDateRange();
    }
  },

  methods: {
    checkNullValue,
    // Keep convertUTCToLocal for export functionality
    convertUTCToLocal,
    updateCurrentItems(val) {
      if (val.length === 0) {
        this.emptySearchValue = true;
        this.$emit("send-list-data", []);
      } else this.emptySearchValue = false;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.myTeamTimeOffFormAccess;
        let clickedForm = formAccess[tab];
        if (tab === "Approvals") {
          window.location.href =
            this.baseUrl + "v3/approvals/approval-management?form_id=352";
        } else if (clickedForm.isVue3) {
          window.location.href = this.baseUrl + "my-team/" + clickedForm.url;
        } else {
          window.location.href = this.baseUrl + "in/my-team/" + clickedForm.url;
        }
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
        if (this.itemList.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    getCurrentDateRange() {
      // Leave Date From = Current Month
      const leaveDateFrom = moment().startOf("month").format("YYYY-MM-DD");
      const leaveDateTo = moment().endOf("month").format("YYYY-MM-DD");
      this.selectedPeriod = "This Month";
      // Set the Date Array instead of String
      this.selectedMonthYear = [
        this.formatDate(leaveDateFrom),
        this.formatDate(leaveDateTo),
      ];
      this.startDate = leaveDateFrom;
      this.endDate = leaveDateTo;

      this.fetchList();
      this.$emit("send-list-data", this.itemList);
    },

    async fetchList(type = "") {
      const vm = this;
      const formId = vm.formId;

      // Store the current filtered state before fetching
      const isFiltered = vm.itemList.length !== vm.originalList.length;
      const currentSearchValue = vm.$store.state.empSearchValue;

      // Remember the IDs of the current filtered items to maintain filter state
      const filteredIds = isFiltered
        ? vm.itemList.map((item) => item.Short_Time_Off_Id)
        : [];

      vm.listLoading = true;

      try {
        // Determine employee selection logic based on callingFrom prop
        let employeeId = null;
        if (vm.callingFrom === "employee-self-service") {
          // Self-service case - only show current employee's data
          employeeId = vm.loginEmployeeId;
        } else {
          // My-team case - can be null to show all employees
          employeeId = null;
        }

        // Validate date range
        const stStartDate = vm.startDate;
        const stEndDate = vm.endDate;

        if (!moment(stStartDate).isValid() || !moment(stEndDate).isValid()) {
          vm.showAlert({
            isOpen: true,
            message: "Please select a valid Short Time Off Date Range.",
            type: "warning",
          });
          return;
        }

        // Make API call
        const response = await vm.$apollo.query({
          query: LIST_SHORT_TIME_OFF,
          client: "apolloClientAC",
          variables: {
            employeeId: employeeId,
            formId: formId,
            startDate: stStartDate,
            endDate: stEndDate,
          },
          fetchPolicy: "no-cache",
        });

        // Handle response
        if (
          response.data?.listShortTimeOff?.shortTimeOffDetails &&
          !response.data.listShortTimeOff.errorCode
        ) {
          // Get the data directly without parsing
          let tempData =
            response.data.listShortTimeOff.shortTimeOffDetails || [];

          // Initialize isSelected property for each item
          tempData.forEach((item) => {
            item.isSelected = false;
          });

          // Update the component data
          vm.originalList = [...tempData];

          // If this is an update after an action and we had a filtered list
          if (type === "updated" && isFiltered) {
            // If we have a search value, apply it
            if (currentSearchValue) {
              let searchValue = currentSearchValue.toString().toLowerCase();
              vm.itemList = tempData.filter((item) => {
                return Object.values(item).some(
                  (v) => v && v.toString().toLowerCase().includes(searchValue)
                );
              });
            }
            // Otherwise, try to maintain the same filtered items by ID
            else if (filteredIds.length > 0) {
              vm.itemList = tempData.filter((item) =>
                filteredIds.includes(item.Short_Time_Off_Id)
              );
            }
            // If no search and no filtered IDs, use the full list
            else {
              vm.itemList = [...tempData];
            }

            // Emit the filtered data to parent component
            vm.$emit("send-list-data", vm.itemList);
          } else {
            vm.itemList = tempData;
            vm.$emit("send-list-data", tempData);
          }

          // Track analytics
          try {
            if (typeof mixpanel !== "undefined") {
              mixpanel.track("Short Time Off data retrieved", {
                count: tempData.length,
                period: `${stStartDate} to ${stEndDate}`,
              });
            }
          } catch (error) {
            // Error tracking mixpanel event
          }

          // Reset loading state
          vm.listLoading = false;
        } else {
          vm.handleShortTimeOffError(
            response.data.listShortTimeOff?.errorCode || ""
          );
        }
      } catch (err) {
        vm.handleShortTimeOffError(err);
      }
    },

    handleShortTimeOffError(err = "") {
      this.listLoading = false;
      this.$emit("send-list-data", []);
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    openAddEditForm(openEdit = false, item = null) {
      if (openEdit) {
        this.isEdit = true;
        // If item is provided, use it; otherwise, keep the current selectedItem
        if (item) {
          // Make sure we're using the combined date-time fields
          this.selectedItem = item;

          // Ensure Start_Date_Time and End_Date_Time are available
          if (
            !this.selectedItem.Start_Date_Time &&
            this.selectedItem.View_Start_Date_Time
          ) {
            this.selectedItem.Start_Date_Time =
              this.selectedItem.View_Start_Date_Time;
          }

          if (
            !this.selectedItem.End_Date_Time &&
            this.selectedItem.View_End_Date_Time
          ) {
            this.selectedItem.End_Date_Time =
              this.selectedItem.View_End_Date_Time;
          }
        }
      } else {
        this.isEdit = false;
        this.selectedItem = {};
      }
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    closeAddEditForm() {
      this.closeAllForms();
    },

    closeAllForms() {
      this.showViewForm = false;
      this.showAddEditForm = false;
      this.isEdit = false;
      this.selectedItem = null;
      this.deleteModel = false;
      this.multiDeleteModel = false;
      this.cancelModel = false;
      this.selectedCancelComment = null;
      this.openApprovalModal = false;
    },

    refetchList(type = "") {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.selectAllBox = false;
      this.selectedShortRecords = [];

      // The filtering logic is now handled in fetchList
      this.fetchList(type);

      // Only reset filter if not updating after an action
      if (type !== "updated") {
        this.resetFilter();
      }
    },
    resetFilter() {
      this.emptySearchValue = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      // Emit the original list data to the parent component
      this.$emit("send-list-data", this.originalList);

      // Emit a reset-filter event to trigger the parent's resetFilter method
      this.$emit("reset-filter");
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      } else if (actionType === "Delete") {
        // Check if any records are selected
        const selectedItems = this.itemList.filter((item) => item.isSelected);
        if (selectedItems.length > 0) {
          this.selectedItem = selectedItems;
          this.multiDeleteModel = true;
        } else {
          this.showAlert({
            isOpen: true,
            type: "warning",
            message: "Please select at least one record to delete.",
          });
        }
      }
      this.openMoreMenu = false;
    },
    enableTooltipForDelete(item) {
      return (
        item.Approval_Status.toLowerCase() === "approved" ||
        item.Approval_Status.toLowerCase() === "cancel applied" ||
        item.Late_Attendance == 1 ||
        item.Early_Checkout.toLowerCase() == `yes`
      );
    },
    toggleSelectAll(value) {
      const shouldSelect = (item) =>
        !(
          item.Approval_Status.toLowerCase() === "approved" ||
          item.Approval_Status.toLowerCase() === "cancel applied" ||
          item.Late_Attendance == 1 ||
          item.Early_Checkout.toLowerCase() == `yes`
        );
      if (value) {
        this.itemList.forEach((item) => {
          item.isSelected = shouldSelect(item);
        });
        this.selectAllBox = this.itemList?.some(shouldSelect) || false;
      } else {
        this.itemList.forEach((item) => {
          item.isSelected = false;
        });
      }
    },

    checkAllSelected() {
      const selectedItems = this.itemList.filter((el) => el.isSelected);
      const selectableItems = this.itemList.filter(
        (el) => !this.enableTooltipForDelete(el)
      );
      this.selectAllBox =
        selectedItems.length === selectableItems.length &&
        selectableItems.length > 0;
    },
    getAvailableActions(item) {
      const actions = [];
      const access = this.checkAccess;

      // Only show Cancel action for items with status 'Approved' or 'Applied' and if user has cancel access
      if (
        access["cancel"] &&
        item.Approval_Status?.toLowerCase() === "approved"
      ) {
        actions.push("Cancel");
      }

      // Only show Delete action for items with status 'Applied', 'Cancelled', or 'Rejected' and if user has delete access
      if (
        access["delete"] &&
        (item.Approval_Status?.toLowerCase() === "applied" ||
          item.Approval_Status?.toLowerCase() === "cancelled" ||
          item.Approval_Status?.toLowerCase() === "rejected")
      ) {
        actions.push("Delete");
      }

      // Only show Edit action for items with status 'Applied' and if user has update access
      if (
        access["update"] &&
        item.Approval_Status?.toLowerCase() === "applied"
      ) {
        actions.push("Edit");
      }

      // Add Approval Workflow action if workflow instance ID exists and if user has view access
      if (access["approval workflow"] && item.Process_Instance_Id) {
        actions.push("Approval Workflow");
      }

      return actions;
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action === "Cancel") {
        // Set the appropriate cancel status based on the current approval status
        if (item.Approval_Status.toLowerCase() === "approved") {
          this.selectedCancelStatus = "Cancel Short Time Off";
        } else if (item.Approval_Status.toLowerCase() === "applied") {
          this.selectedCancelStatus = "Cancel Short Time Off";
        }
        this.cancelModel = true;
      } else if (action === "Delete" && !this.enableTooltipForDelete(item))
        this.deleteModel = true;
      else if (action === "Edit") this.openAddEditForm(true, item);
      else if (action?.toLowerCase() === "approval workflow")
        this.openApprovalModal = true;
    },

    onDeleteShortTimeOff() {
      let vm = this;

      // Check if all selected items have valid status for deletion
      const allowedStatuses = ["applied", "cancelled", "rejected"];
      const selectedItems = Array.isArray(vm.selectedItem)
        ? vm.selectedItem
        : [vm.selectedItem];

      // Check if any selected item has a status that's not allowed for deletion
      const invalidItems = selectedItems.filter((item) => {
        const status = item.Approval_Status?.toLowerCase();
        return !allowedStatuses.includes(status);
      });

      // If there are invalid items, show error and return
      if (invalidItems.length > 0) {
        vm.showAlert({
          isOpen: true,
          type: "warning",
          message:
            "Only Short time off requests with status 'Applied', 'Cancelled', or 'Rejected' can be deleted.",
        });
        vm.closeAllForms();
        return;
      }

      vm.isLoading = true;

      // Always create an array of IDs - handles single or multiple selections
      let shortTimeOffIdArr = vm.selectedItem
        ? Array.isArray(vm.selectedItem)
          ? vm.selectedItem.map((item) => item.Short_Time_Off_Id)
          : [vm.selectedItem.Short_Time_Off_Id]
        : [];

      const apiObj = {
        url: vm.baseUrl + "employees/short-time-off/delete-short-time-off/",
        type: "POST",
        async: false,
        dataType: "json",
        data: {
          shortTimeOffIdArr: shortTimeOffIdArr,
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          if (res && res.success) {
            vm.isLoading = false;
            vm.showAlert({
              isOpen: true,
              type: "success",
              message: res.msg || "Short time off record deleted successfully.",
            });
            vm.closeAllForms();
            // Delay the list refresh to ensure the modal is closed first
            setTimeout(() => {
              vm.refetchList("updated");
            }, 300);
          } else {
            vm.isLoading = false;
            vm.openWarningModal = false;
            vm.showAlert({
              isOpen: true,
              type: "warning",
              message:
                res?.msg ||
                "Failed to delete short time off record. Please try again.",
            });
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleDeleteError(err);
        });
    },
    handleDeleteError(err = "") {
      this.openWarningModal = false;

      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "delete",
        form: this.landedFormName,
        isListError: false,
      });
    },
    async validateCancelShortTimeOff() {
      const { valid } = await this.$refs.cancelForm.validate();
      if (valid) this.onCancelShortTimeOff();
    },

    onCancelShortTimeOff() {
      const vm = this;
      vm.isLoading = true;

      const apiObj = {
        url: `${vm.baseUrl}employees/short-time-off/status-update-short-time-off`,
        type: "POST",
        dataType: "json",
        data: {
          shortTimeOffIdArr: [vm.selectedItem.Short_Time_Off_Id],
          status: "Cancel Applied",
          comments: vm.selectedCancelComment || "",
          isAction: "StatusUpdate",
        },
      };

      vm.$store
        .dispatch("triggerControllerFunction", apiObj)
        .then((res) => {
          vm.isLoading = false;
          if (res?.success) {
            vm.showAlert({
              isOpen: true,
              type: "success",
              message: res.msg || "Short time off cancelled successfully.",
            });
            vm.closeAllForms();
            // Delay the list refresh to ensure the modal is closed first
            setTimeout(() => {
              vm.refetchList("updated");
            }, 300);
          } else {
            vm.showAlert({
              isOpen: true,
              type: "warning",
              message: res?.msg || "Failed to cancel short time off.",
            });
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "cancel",
            form: vm.landedFormName,
            isListError: false,
          });
        });
    },

    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));
      exportData = exportData.map((el) => ({
        ...el,
        User_Defined_EmpId: el.User_Defined_EmpId
          ? String(el.User_Defined_EmpId)
          : "",
        Start_Date_Time: el.Start_Date_Time
          ? this.formatDate(el.View_Start_Date_Time, true)
          : "",
        End_Date_Time: el.End_Date_Time
          ? this.formatDate(el.View_End_Date_Time, true)
          : "",
        Approved_On: el.Approved_On
          ? this.formatDate(el.Approved_On, true)
          : "",
        Added_On: el.Added_On ? this.formatDate(el.Added_On, true) : "",
        Updated_On: el.Updated_On ? this.formatDate(el.Updated_On, true) : "",
        Late_Attendance: el.Late_Attendance,
        Early_Checkout: el.Early_Checkout,
      }));

      const headers = [
        { header: "Employee Id", key: "User_Defined_EmpId" },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Request For", key: "Request_For" },
        { header: "Reason", key: "Reason" },
        { header: "Start Date Time", key: "Start_Date_Time" },
        { header: "End Date Time", key: "End_Date_Time" },
        { header: "Total Hours", key: "Total_Hours" },
        { header: "Alternate Person", key: "AlternatePersonName" },
        { header: "Late Attendance", key: "Late_Attendance" },
        { header: "Early Checkout", key: "Early_Checkout" },
        { header: "Early Checkout Hours", key: "Early_Checkout_Hours" },
        { header: "Status", key: "Approval_Status" },
        { header: "Designation", key: "Designation_Name" },
        { header: "Department", key: "Department_Name" },
        { header: "Location", key: "Location_Name" },
        { header: "Employee Type", key: "Employee_Type" },
        { header: "Business Unit", key: "Business_Unit" },
        { header: "Added By", key: "Added_By_Name" },
        { header: "Added On", key: "Added_On" },
        { header: "Updated By", key: "Updated_By_Name" },
        { header: "Updated On", key: "Updated_On" },
      ];

      const exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: headers,
      };

      this.exportExcelFile(exportOptions);
    },

    statusColor(status) {
      switch (status) {
        case "Applied":
          return "text-primary";
        case "Approved":
          return "text-green";
        case "Rejected":
          return "text-red";
        case "Returned":
          return "text-amber";
        case "Cancelled":
          return "text-amber";
        default:
          return "";
      }
    },
    leftStatusBarClass(status) {
      const s = (status || "").toLowerCase();
      if (s === "approved" || s === "cancelled" || s === "cancel approved") {
        return "left-status-bar-green";
      } else if (s === "rejected") {
        return "left-status-bar-red";
      } else if (s === "applied" || s === "cancel applied") {
        return "left-status-bar-amber";
      }
      return "";
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onChangePeriod(period) {
      if (period === "Custom") {
        this.selectedMonthYear = null;
      } else {
        let startDate, endDate;

        switch (period) {
          case "Today":
            startDate = endDate = moment().format("YYYY-MM-DD");
            break;
          case "Yesterday":
            startDate = endDate = moment()
              .subtract(1, "days")
              .format("YYYY-MM-DD");
            break;
          case "Last 7 Days":
            startDate = moment().subtract(6, "days").format("YYYY-MM-DD");
            endDate = moment().format("YYYY-MM-DD");
            break;
          case "This Month":
            startDate = moment().startOf("month").format("YYYY-MM-DD");
            endDate = moment().endOf("month").format("YYYY-MM-DD");
            break;
          case "Last Month":
            startDate = moment()
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD");
            endDate = moment()
              .subtract(1, "month")
              .endOf("month")
              .format("YYYY-MM-DD");
            break;
          case "Next 90 Days":
            startDate = moment().format("YYYY-MM-DD");
            endDate = moment().add(90, "days").format("YYYY-MM-DD");
            break;
        }

        if (startDate && endDate) {
          this.selectedMonthYear = [
            this.formatDate(startDate),
            this.formatDate(endDate),
          ];
        }
      }
    },
    resetDateFilters() {
      this.selectedPeriod = "Custom";
      this.startDate = null;
      this.endDate = null;
      this.selectedMonthYear = null;
    },

    onChangeDateRange(selectedDates) {
      if (selectedDates?.length > 1) {
        // Format the start and end dates
        let shortTimeOffStartDate = moment(selectedDates[0]).format(
          "YYYY-MM-DD"
        );
        let shortTimeOffEndDate = moment(selectedDates[1]).format("YYYY-MM-DD");

        // Calculate the difference in days
        let dateDifference = moment(shortTimeOffEndDate).diff(
          moment(shortTimeOffStartDate),
          "days"
        );
        const differenceAllowed = !this.showTeam ? 365 : 90;
        // Prevent if the range is more than difference allowed days
        if (dateDifference > differenceAllowed) {
          this.selectedMonthYear = [
            this.formatDate(this.startDate),
            this.formatDate(this.endDate),
          ];
          this.showAlert({
            isOpen: true,
            message: `The selected date range cannot exceed ${differenceAllowed} days. Please select a valid date range.`,
            type: "warning",
          });
          return;
        }
        if (
          moment(selectedDates[0]).format("YYYY-MM-DD") != this.startDate ||
          moment(selectedDates[1]).format("YYYY-MM-DD") != this.endDate
        ) {
          // Set the dates and fetch the list
          this.startDate = shortTimeOffStartDate;
          this.endDate = shortTimeOffEndDate;
          this.fetchList();
        }
      }
    },
    redirectToOldShortTimeOff() {
      window.open(this.baseUrl + "employees/short-time-off", "_blank");
    },
    resetSelection() {
      this.selectAllBox = false;
      // Reset selection for all items
      if (this.itemList && this.itemList.length > 0) {
        this.itemList.forEach((item) => {
          item.isSelected = false;
        });
      }
      this.selectedShortRecords = [];
    },
  },
};
</script>

<style scoped>
.shorttimeoff {
  padding: 5em 2em 0em 3em;
}
/* Adjust padding for employee self-service view */
.shorttimeoff[callingFrom="selfService"] {
  padding: 0em 2em 0em 3em;
}

/* Mobile responsive adjustments */
@media screen and (max-width: 805px) {
  .shorttimeoff {
    padding: 4em 1em 0em 1em;
  }
  .shorttimeoff[callingFrom="selfService"] {
    padding: 0em 1em 0em 1em;
  }
}
.left-status-bar {
  width: 7px;
  height: 100%;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  display: block;
}
.left-status-bar-green {
  background-color: #2ecc40;
}
.left-status-bar-red {
  background-color: #ff4d4f;
}
.left-status-bar-amber {
  background-color: #ffc107;
}
.row-bg-green,
.row-bg-red,
.row-bg-amber {
  background: none !important;
}
</style>

<style>
/* Global styles for datepicker components */
.datepicker-employee_attendance .vuejs3-datepicker__value {
  min-width: 160px;
  display: flex;
  align-items: center;
}
.datepicker-employee_attendance .vuejs3-datepicker__calendar {
  right: 1px;
}
</style>
