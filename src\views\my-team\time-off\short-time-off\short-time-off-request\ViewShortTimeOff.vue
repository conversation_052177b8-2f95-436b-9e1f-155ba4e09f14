<template>
  <v-overlay
    :model-value="showViewForm"
    @click:outside="onCloseOverlay()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? 'width:100vw; height: 100vh'
            : 'width:40vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center fixed-title"
        >
          <div class="text-h6 text-medium ps-2">View Short Time Off</div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <div class="d-flex justify-end align-center">
            <v-btn
              v-if="presentEditButton"
              @click="onOpenEditForm()"
              class="mr-3 mt-3 text-primary"
              variant="text"
              rounded="lg"
            >
              <v-icon class="mr-1" size="15">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
          <div class="px-6 py-2">
            <v-row>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Employee Id
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.User_Defined_EmpId) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Employee Name
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Employee_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Request For
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Request_For) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Reason</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Reason) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Start Date & Time
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ formatDate(selectedItem?.View_Start_Date_Time) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  End Date & Time
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ formatDate(selectedItem?.View_End_Date_Time) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Total Hours
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ formattedTotalHoursText }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Late Arrival
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Late_Arrival) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Early Checkout
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Early_Checkout) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Early Checkout Hours
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Early_Checkout_Hours) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Alternate Person
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.AlternatePersonName) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Status</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem?.Approval_Status) }}
                  </section>
                </div>
              </v-col>
            </v-row>
            <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails> </v-col
            ></v-row>
            <div class="bottom-space"></div>
          </div> </v-card-text></v-card></template
  ></v-overlay>
</template>

<script>
import { checkNullValue } from "@/helper";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import moment from "moment";

export default {
  name: "ViewShortTimeOff",
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      showViewForm: true,
    };
  },
  props: {
    selectedItem: {
      type: Object,
      default: () => {},
    },
    callingFrom: {
      type: String,
      required: true,
    },
    formAccess: {
      type: Object,
      required: true,
    },
  },
  emits: ["close-view-form", "open-edit-form"],
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    presentEditButton() {
      let { Approval_Status, Approver_Id, Added_By, Employee_Id } =
        this.selectedItem;
      let editableStatus = ["Applied"];
      let showEdit =
        (this.formAccess.admin == "admin" ||
          [Added_By, Employee_Id].includes(this.loginEmployeeId) ||
          Approver_Id == this.loginEmployeeId) &&
        editableStatus.includes(Approval_Status) &&
        this.formAccess?.update;
      return showEdit;
    },
    formattedTotalHoursText() {
      if (!this.selectedItem?.Total_Hours) {
        return "00 Hrs 00 Mins";
      }

      // Parse the total hours value (could be decimal or HH:MM format)
      let hours = 0;
      let minutes = 0;

      const totalHoursValue = this.selectedItem.Total_Hours;

      // Check if it's in HH:MM format
      if (
        typeof totalHoursValue === "string" &&
        totalHoursValue.includes(":")
      ) {
        [hours, minutes] = totalHoursValue.split(":").map(Number);
      } else {
        // Assume it's a decimal value
        const totalHoursDecimal = parseFloat(totalHoursValue) || 0;
        hours = Math.floor(totalHoursDecimal);
        minutes = Math.round((totalHoursDecimal - hours) * 60);
      }

      // Format as "XX Hours YY Minutes"
      return `${hours.toString().padStart(2, "0")} Hrs ${minutes
        .toString()
        .padStart(2, "0")} Mins`;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
  },
  components: {
    MoreDetails,
  },
  mounted() {
    this.prefillMoreDetails();
  },
  methods: {
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    checkNullValue,
    onOpenEditForm() {
      this.showViewForm = false;
      this.$emit("open-edit-form");
    },
    onCloseOverlay() {
      this.showViewForm = false;
      this.$emit("close-view-form");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];

      // Handle Added information
      if (this.selectedItem.Added_On) {
        const addedEntry = {
          actionDate: this.formatDate(this.selectedItem.Added_On),
          displayText: this.selectedItem?.Added_By_Name
            ? `${this.formatDate(this.selectedItem.Added_On)}  ${
                this.selectedItem.Added_By_Name
              }`
            : this.formatDate(this.selectedItem.Added_On),
        };

        if (this.selectedItem?.Added_By_Name) {
          // If Added_By_Name exists, show "Added by [name]"
          addedEntry.actionBy = this.selectedItem.Added_By_Name;
          addedEntry.text = "Added";
        } else {
          // If Added_By_Name doesn't exist, set text to null to prevent the dash
          addedEntry.text = null; // Setting text to null will prevent the dash from being displayed
        }

        this.moreDetailsList.push(addedEntry);
      }

      // Handle Updated information
      if (this.selectedItem.Updated_On) {
        const updatedEntry = {
          actionDate: this.formatDate(this.selectedItem.Updated_On),
          displayText: this.selectedItem?.Updated_By_Name
            ? `${this.formatDate(this.selectedItem.Updated_On)}  ${
                this.selectedItem.Updated_By_Name
              }`
            : this.formatDate(this.selectedItem.Updated_On),
        };

        if (this.selectedItem?.Updated_By_Name) {
          // If Updated_By_Name exists, show "Updated by [name]"
          updatedEntry.actionBy = this.selectedItem.Updated_By_Name;
          updatedEntry.text = "Updated";
        } else {
          // If Updated_By_Name doesn't exist, set text to null to prevent the dash
          updatedEntry.text = null; // Setting text to null will prevent the dash from being displayed
        }

        this.moreDetailsList.push(updatedEntry);
      }
    },
  },
};
</script>

<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
.fixed-title {
  position: sticky;
  top: 0;
  z-index: 10;
}
.bottom-space {
  margin-top: 200px;
}
</style>
