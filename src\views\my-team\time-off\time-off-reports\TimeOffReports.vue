<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
                :is-search="true"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            button-text="Retry"
            @button-click="fetchList()"
          />

          <AppFetchErrorScreen
            v-else-if="originalList?.length === 0"
            key="no-data-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      color="transparent"
                      rounded="lg"
                      variant="flat"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="fetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="itemList?.length == 0"
            key="no-results-screen"
            :main-title="`There are no ${landedFormName} matched for the selected filters/searches.`"
            image-name="common/no-records"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div v-if="reportCategories?.length > 0">
              <template
                v-for="(reportList, index) in reportCategories"
                :key="index"
              >
                <v-row v-if="reportList.reports?.length" class="mx-12 my-4">
                  <v-col cols="12" class="body-1 text-primary font-weight-bold">
                    {{ checkNullValue(reportList.header) }}
                  </v-col>
                  <v-col
                    v-for="(report, i) in reportList.reports"
                    :key="i + report.Report_Title"
                    xl="3"
                    lg="3"
                    md="4"
                    sm="6"
                    cols="12"
                    class="pa-2"
                  >
                    <v-hover>
                      <template #default="{ hover }">
                        <v-card
                          class="imgHover pa-2 rounded-lg"
                          height="100%"
                          @click="actionOnReport(report)"
                        >
                          <v-card-title
                            :class="{ 'common-box-shadow': hover }"
                            class="d-flex align-center"
                            min-height="65"
                          >
                            <v-tooltip
                              :text="report?.Report_Description"
                              location="top"
                              max-width="500"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  v-bind="props"
                                >
                                  {{ checkNullValue(report.Report_Title) }}
                                </div>
                              </template>
                            </v-tooltip>
                            <v-spacer />
                            <v-icon
                              class="fas fa-file-export"
                              size="17"
                              color="primary"
                            >
                              fas fa-file-export
                            </v-icon>
                          </v-card-title>
                        </v-card>
                      </template>
                    </v-hover>
                  </v-col>
                </v-row>
              </template>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <TimeOffReportsModel
      v-if="showReportsInfo"
      :report-data="selectedItem"
      :common-filter-details="commonFilterDetails"
      :dropdown-data="dropdownData"
      @close-model="closeAllForms()"
    />
  </div>
</template>
<script>
const { defineAsyncComponent } = require("vue");
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const TimeOffReportsModel = defineAsyncComponent(() =>
  import("./TimeOffReportsModel.vue")
);
import { LIST_TIME_OFF_REPORTS } from "@/graphql/my-team/leaves.js";
import {
  RETRIEVE_DROPDOWN_DATA,
  LIST_BUSINESS_UNIT,
} from "@/graphql/dropDownQueries.js";
import { RETRIEVE_ORGANISATION_GROUP_LIST } from "@/graphql/corehr/organisationGroupQueries";
import { checkNullValue } from "@/helper";
export default {
  name: "TimeOffReports",
  components: {
    EmployeeDefaultFilterMenu,
    TimeOffReportsModel,
  },
  data() {
    return {
      originalList: [],
      itemList: [],
      selectedItem: null,
      showReportsInfo: false,
      // error/loading
      errorContent: "",
      isErrorInList: false,
      listLoading: false,
      // Leaves
      currentTabItem: "",
      commonFilterDetails: [],
      // Dropdown data
      dropdownData: {
        department: [],
        location: [],
        workschedule: [],
        role: [],
        employeetype: [],
        serviceprovider: [],
        state: [],
        manager: [],
        skill: [],
        client: [],
        currency: [],
        fieldforce: 0,
        organizationgroup: [],
        businessunit: [],
      },
      dropdownLoading: false,
      dropdownError: false,
    };
  },
  computed: {
    landedFormName() {
      return this.accessRights(348)?.customFormName || "Reports";
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    leavesList() {
      return this.itemList?.filter((el) => el.Form_Id == 332) || [];
    },
    compoffList() {
      return this.itemList?.filter((el) => el.Form_Id == 334) || [];
    },
    shortTimeOffList() {
      return this.itemList?.filter((el) => el.Form_Id == 352) || [];
    },
    reportCategories() {
      const categories = [];

      // Group leaves by Report_Group_Name
      if (this.leavesList && this.leavesList.length > 0) {
        const groupedLeaves = this.groupByReportHeader(
          this.leavesList,
          "Leaves"
        );
        categories.push(...groupedLeaves);
      }

      // Group compensatory off by Report_Group_Name
      if (this.compoffList && this.compoffList.length > 0) {
        const groupedCompoff = this.groupByReportHeader(
          this.compoffList,
          "Compensatory Off"
        );
        categories.push(...groupedCompoff);
      }

      // Group short time off by Report_Group_Name
      if (this.shortTimeOffList && this.shortTimeOffList.length > 0) {
        const groupedShortTimeOff = this.groupByReportHeader(
          this.shortTimeOffList,
          "Short Time Off"
        );
        categories.push(...groupedShortTimeOff);
      }

      return categories;
    },
    leaveRequestFormAccess() {
      let formAccessRights = this.accessRights(332);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    compOffRequestFormAccess() {
      let formAccessRights = this.accessRights(334);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    shortTimeOffFormAccess() {
      let formAccessRights = this.accessRights(352);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    myTeamTimeOffFormAccess() {
      return this.$store.getters.myTeamTimeOffFormAccess;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(348);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"] &&
        (this.shortTimeOffFormAccess ||
          this.compOffRequestFormAccess ||
          this.leaveRequestFormAccess)
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.myTeamTimeOffFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
    this.fetchDropdownData();
    this.getEmpList();
    this.fetchOrganizationGroups();
    this.fetchBusinessUnit();
  },
  methods: {
    checkNullValue,
    groupByReportHeader(items, defaultHeader) {
      // Group items by Report_Group_Name
      const groupedByHeader = {};

      // If items array is empty, return an empty array
      if (!items || items.length === 0) {
        return [];
      }

      // Group items by their Report_Group_Name
      items.forEach((item) => {
        const header = item.Report_Group_Name || defaultHeader;
        if (!groupedByHeader[header]) {
          groupedByHeader[header] = [];
        }
        groupedByHeader[header].push(item);
      });

      // Convert the grouped object to an array of category objects
      return Object.entries(groupedByHeader).map(([header, reports]) => ({
        header,
        reports,
      }));
    },
    actionOnReport(report = "") {
      this.selectedItem = report;
      this.showReportsInfo = true;
    },
    closeAllForms() {
      this.showReportsInfo = false;
      this.selectedItem = null;
    },
    fetchList() {
      let vm = this;
      vm.isErrorInList = false;
      vm.errorContent = "";
      let ids = [];
      if (this.leaveRequestFormAccess) ids.push(332);
      if (this.compOffRequestFormAccess) ids.push(334);
      if (this.shortTimeOffFormAccess) ids.push(352);
      if (!ids?.length) return;
      vm.listLoading = true;

      vm.$apollo
        .query({
          query: LIST_TIME_OFF_REPORTS,
          client: "apolloClientAC",
          variables: {
            formId: ids,
            calledFrom: 348,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listTimeOffReports &&
            !response.data.listTimeOffReports.errorCode
          ) {
            const teampData = JSON.parse(
              response.data.listTimeOffReports.reportDetails
            );
            vm.itemList = teampData;
            vm.originalList = teampData;
            if (response.data.listTimeOffReports.commonFilterDetails) {
              let commonFilters = JSON.parse(
                response.data.listTimeOffReports.commonFilterDetails
              );
              vm.commonFilterDetails = Object.keys(commonFilters).map((key) => {
                return {
                  label: key,
                  type: commonFilters[key],
                };
              });
            }
          } else {
            vm.handleListError(
              response.data.listTimeOffReports?.errorCode || ""
            );
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.itemList = [];
      this.originalList = [];
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Time Off Reports",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    async fetchDropdownData() {
      this.dropdownLoading = true;
      this.dropdownError = false;

      try {
        const response = await this.$apollo.query({
          query: RETRIEVE_DROPDOWN_DATA,
          client: "apolloClientA",
          variables: {
            status: "Active",
            formId: 348,
          },
          fetchPolicy: "no-cache",
        });

        if (
          response &&
          response.data &&
          response.data.getDropDownBoxDetails &&
          !response.data.getDropDownBoxDetails.errorCode
        ) {
          let dropdownData = response.data.getDropDownBoxDetails;
          this.dropdownData.department =
            dropdownData.departments.map((item) => ({
              id: item.Department_Id,
              value: item.Department_Name,
            })) || [];
          this.dropdownData.location =
            dropdownData.locations.map((item) => ({
              id: item.Location_Id,
              value: item.Location_Name,
            })) || [];
          this.dropdownData.workschedule =
            dropdownData.workSchedules.map((item) => ({
              id: item.WorkSchedule_Id,
              value: item.Title,
            })) || [];
          this.dropdownData.role =
            dropdownData.roles.map((item) => ({
              id: item.Roles_Id,
              value: item.Roles_Name,
            })) || [];
          this.dropdownData.employeetype =
            dropdownData.employeeType.map((item) => ({
              id: item.EmpType_Id,
              value: item.Employee_Type,
            })) || [];
          this.dropdownData.serviceprovider =
            dropdownData.serviceProvider.map((item) => ({
              id: item.Service_Provider_Id,
              value: item.Service_Provider_Name,
            })) || [];
          this.dropdownData.state =
            dropdownData.state.map((item) => ({
              id: item.State_Id,
              value: item.State_Name,
            })) || [];
          this.dropdownData.managername =
            dropdownData.managers.map((item) => ({
              id: item.Manager_Id,
              value: item.Manager_Name,
            })) || [];
          this.dropdownData.skill =
            dropdownData.skills.map((item) => ({
              id: item.SkillDefinition_Id,
              value: item.Title,
            })) || [];
          this.dropdownData.client =
            dropdownData.clients.map((item) => ({
              id: item.Client_Id,
              value: item.Company_Name,
            })) || [];
          this.dropdownData.currency =
            dropdownData.currency.map((item) => ({
              id: item.Currency_Id,
              value: item.Currency_Name,
            })) || [];
          this.dropdownData.fieldforce = dropdownData.fieldForce;
        }
      } catch (error) {
        this.dropdownError = true;
      } finally {
        this.dropdownLoading = false;
      }
    },
    fetchOrganizationGroups() {
      let vm = this;
      vm.$apollo
        .query({
          query: RETRIEVE_ORGANISATION_GROUP_LIST,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: { formId: 348 },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listOrganizationGroup &&
            !response.data.listOrganizationGroup.errorCode
          ) {
            const { organizationGroupObject } =
              response.data.listOrganizationGroup;
            if (organizationGroupObject) {
              let orgList = organizationGroupObject.organizationGroupList
                ? organizationGroupObject.organizationGroupList
                : [];
              orgList = orgList.map((item) => ({
                id: item.organizationGroupId,
                value: item.organizationGroup,
                status: item.status,
              }));
              vm.dropdownData.organizationgroup = orgList.filter(
                (group) => group.status === "Active"
              );
            } else {
              vm.dropdownData.organizationgroup = [];
            }
          } else {
            vm.dropdownData.organizationgroup = [];
          }
        })
        .catch(() => {
          vm.dropdownData.organizationgroup = [];
        });
    },
    fetchBusinessUnit() {
      let vm = this;
      vm.$apollo
        .query({
          query: LIST_BUSINESS_UNIT,
          client: "apolloClientI",
          variables: {
            action: "active",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listBusinessUnitInDropdown &&
            !response.data.listBusinessUnitInDropdown.errorCode &&
            response.data.listBusinessUnitInDropdown.settings &&
            response.data.listBusinessUnitInDropdown.settings.length > 0
          ) {
            let businessUnitList =
              response.data.listBusinessUnitInDropdown.settings;
            businessUnitList = businessUnitList.map((item) => ({
              id: item.businessUnitId,
              value: item.businessUnit,
            }));
            vm.dropdownData.businessunit = businessUnitList;
          } else {
            vm.dropdownData.businessunit = [];
          }
        })
        .catch(() => {
          vm.dropdownData.businessunit = [];
        });
    },
    async getEmpList() {
      this.isFetchingEmployees = true;
      try {
        const empData = await this.$store.dispatch("getEmployeesList", {
          formName: "Reports",
          formId: 348,
        });

        if (!empData || empData.length === 0) {
          this.dropdownData.employeename = [];
          return;
        }

        this.dropdownData.employeename = empData.map((item) => ({
          value: `${item.employeeName} - ${item.userDefinedEmpId}`,
          id: item.employeeId,
        }));
      } catch (err) {
        const snackbarData = {
          isOpen: true,
          message:
            err === "error"
              ? "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator."
              : err,
          type: "warning",
        };
        this.showAlert(snackbarData);
      } finally {
        this.isFetchingEmployees = false;
      }
    },
    showAlert(data) {
      this.$store.dispatch("showSnackbar", data);
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.listLoading = true;
        const { formAccess } = this.myTeamTimeOffFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/my-team/" + clickedForm.url);
        } else {
          window.location.href = this.baseUrl + "in/my-team/" + clickedForm.url;
        }
      }
    },
  },
};
</script>
<style scoped>
.container {
  padding: 3.7em 0em 0em 0em;
}
.leaves-card {
  padding: 0em 0em 0em 0em;
}
/* Zoom In #1 */
.imgHover img {
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
}
.imgHover:hover img {
  -webkit-transform: scale(1.3);
  transform: scale(1.3);
}
@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
  .leaves-card {
    padding: 0em 0em 0em 0em;
  }
}
</style>
