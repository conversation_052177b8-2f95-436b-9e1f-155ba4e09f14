<template>
  <div>
    <v-dialog
      v-model="showModel"
      :width="isMobileView ? '90%' : '60%'"
      persistent
      scrollable
      style="z-index: 1000"
    >
      <v-card class="rounded-lg" :min-height="windowWidth > 700 ? 400 : ''">
        <div class="d-flex justify-end">
          <v-icon color="primary" class="pr-4 pt-4" @click="closeAllForms()"
            >fas fa-times</v-icon
          >
        </div>
        <v-card-text>
          <div
            class="d-flex align-center flex-column font-weight-bold modal-heading text-primary"
          >
            {{ checkNullValue(reportData?.Report_Title) }}
          </div>
          <v-form ref="reportForm">
            <!-- Common Filters -->

            <v-row class="pt-2">
              <v-col
                cols="12"
                xs="12"
                md="4"
                v-for="(item, index) in commonFilters"
                :key="index"
              >
                <CustomSelect
                  v-if="
                    item.type?.toLowerCase() === 'dropdown' &&
                    item.label.toLowerCase() !== 'designation'
                  "
                  v-model="item.selectedValue"
                  :items="getCommonFilterItems(item)"
                  item-title="value"
                  item-value="id"
                  :isAutoComplete="true"
                  :isRequired="item.isRequired"
                  :rules="[
                    item.isRequired
                      ? required(item.label, item.selectedValue?.length)
                      : true,
                  ]"
                  :selectProperties="getSelectProperties(item)"
                  :itemSelected="item.selectedValue"
                  :label="item.label"
                  variant="solo"
                  density="comfortable"
                />
                <CustomSelect
                  v-else-if="
                    item.type?.toLowerCase() === 'dropdown' &&
                    item.label.toLowerCase() === 'designation'
                  "
                  @selected-item="onDesignationSelected($event, item)"
                  :items="getDesignationListWithSelected(item)"
                  item-title="Designation_Name"
                  item-value="Designation_Id"
                  :isLoading="designationListLoading"
                  placeholder="Type minimum 3 characters to list"
                  :no-data-text="noDataText"
                  :isAutoComplete="true"
                  :isRequired="item.isRequired"
                  :rules="[
                    item.isRequired
                      ? required(item.label, item.selectedValue?.length)
                      : true,
                  ]"
                  :selectProperties="getSelectProperties(item)"
                  :itemSelected="item.selectedValue"
                  :label="item.label"
                  variant="solo"
                  density="comfortable"
                  @update-search-value="callDesignationList($event, item)"
                />
              </v-col>

              <!-- Report Filters -->
              <v-col
                cols="12"
                xs="12"
                md="4"
                v-for="(item, index) in reportFilters"
                :key="index"
              >
                <!-- Date Range -->
                <div v-if="item.fieldType?.toLowerCase() == `dpicker`">
                  <p class="text-caption mt-n2">
                    {{ item.labels }}
                    <span v-if="item.isRequired" style="color: red">*</span>
                  </p>
                  <v-btn
                    class="bg-white date-btn"
                    :class="{ 'mobile-date-btn': isMobileView }"
                    :style="isMobileView ? 'width: 100%' : 'width: max-content'"
                    rounded="lg"
                    @click="$refs['datePicker' + index].fp?.open()"
                  >
                    <v-icon color="primary" size="14"
                      >fas fa-calendar-alt</v-icon
                    >
                    <span class="text-caption px-1 pt-1">Date:</span>
                    <flat-pickr
                      :ref="'datePicker' + index"
                      v-model="item.selectedValue"
                      :config="flatPickerOptions"
                      placeholder="Select Date Range"
                      clearable
                      class="ml-2 mt-1 date-range-picker-custom-bg"
                      :class="{ 'mobile-date-picker': isMobileView }"
                      :style="{
                        outline: '0px',
                        color: 'var(--v-primary-base)',
                        width: isMobileView ? '100%' : '190px',
                        maxWidth: isMobileView ? '150px' : '190px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }"
                    />
                  </v-btn>
                </div>
                <!-- Month Picker -->
                <v-menu
                  v-else-if="item.fieldType?.toLowerCase() == `mpicker`"
                  v-model="item.dateMenu"
                  location="top"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  min-width="auto"
                >
                  <template v-slot:activator="{ props }">
                    <v-text-field
                      v-model="item.selectedformattedValue"
                      v-bind="props"
                      :rules="[
                        item.isRequired
                          ? required(item.labels, item.selectedformattedValue)
                          : true,
                      ]"
                      variant="solo"
                      density="comfortable"
                      clearable
                    >
                      <template v-slot:label>
                        {{ item.labels }}
                        <span v-if="item.isRequired" style="color: red">*</span>
                      </template>
                    </v-text-field>
                  </template>
                  <datepicker
                    :ref="'monthPicker' + index"
                    :format="'MMMM, yyyy'"
                    :placeholder="item.labels"
                    v-model="item.selectedValue"
                    :inline="true"
                    clearable
                    maximum-view="year"
                    minimum-view="month"
                    :class="isMobileView ? 'mb-2' : ''"
                    :style="{
                      width: '100%',
                      height: '30px',
                    }"
                    @update:modelValue="closeDateMenu(item)"
                  />
                </v-menu>
                <!-- text Fileds -->
                <v-text-field
                  v-else-if="
                    item.labels?.toLowerCase() == `employee name` ||
                    item.labels?.toLowerCase() == `manager name` ||
                    item.labels?.toLowerCase() == `name`
                  "
                  ref="textFields"
                  v-model="item.selectedValue"
                  :rules="[
                    item.isRequired
                      ? required(item.labels, item.selectedValue)
                      : true,
                  ]"
                  variant="solo"
                  density="comfortable"
                  clearable
                  ><template v-slot:label>
                    {{ item.labels }}
                    <span v-if="item.isRequired" style="color: red">*</span>
                  </template>
                </v-text-field>
                <!-- Number Fileds -->
                <v-text-field
                  v-else-if="item.fieldType?.toLowerCase() == `amount`"
                  ref="numberFields"
                  v-model="item.selectedValue"
                  variant="solo"
                  :rules="[
                    item.isRequired
                      ? required(item.labels, item.selectedValue)
                      : true,
                  ]"
                  density="comfortable"
                  type="number"
                  clearable
                  ><template v-slot:label>
                    {{ item.labels }}
                    <span v-if="item.isRequired" style="color: red">*</span>
                  </template>
                </v-text-field>
                <!-- All Dropdowns with special handling for departments and multi-select -->
                <CustomSelect
                  v-else
                  v-model="item.selectedValue"
                  :items="
                    item.fieldType?.toLowerCase().includes('department')
                      ? formatDepartmentItems(item.listItems)
                      : item.listItems
                  "
                  item-title="Value"
                  item-value="Id"
                  :isAutoComplete="true"
                  :itemSelected="item.selectedValue"
                  :label="item.labels"
                  variant="solo"
                  density="comfortable"
                  :isRequired="item.isRequired"
                  :selectProperties="getSelectProperties(item)"
                  :rules="[
                    item.isRequired
                      ? required(
                          item.labels,
                          item.selectionMode === 'multi-select'
                            ? item.selectedValue?.length
                            : item.selectedValue
                        )
                      : true,
                  ]"
                />
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <div class="text-center pb-10">
          <v-btn
            rounded="lg"
            class="primary"
            variant="elevated"
            @click="validateReport()"
          >
            <v-icon size="15" class="pr-2 fas fa-file-export" /> Export
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
    <app-loading v-if="isLoading" />
  </div>
</template>
<script>
import { checkNullValue } from "@/helper";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import Datepicker from "vuejs3-datepicker";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  GET_REPORT_FILTER_FIELDS,
  GET_REPORT_DETAILS,
} from "@/graphql/my-team/leaves.js";
export default {
  name: "TimeOffReportsModel",
  data() {
    return {
      showModel: true,
      isLoading: false,
      dropdownLoading: false,
      reportFilters: [],
      commonFilters: [],
      selectedFilter: null,
      selectedItem: {},
      designationList: [],
      designationListLoading: false,
      searchString: "",
      designationSearchTimeout: null,
      selectedDesignations: {}, // Store selected designations by filter item
    };
  },
  components: {
    CustomSelect,
    flatPickr,
    Datepicker,
  },
  mixins: [validationRules],
  props: {
    reportData: {
      type: Object,
      required: true,
    },
    commonFilterDetails: {
      type: Array,
      default: () => [],
    },
    dropdownData: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
      };
    },
    getCommonFilterItems() {
      return (item) => {
        if (
          item.label.toLowerCase() ===
          this.labelList[115]?.Field_Alias?.toLowerCase()
        ) {
          return this.dropdownData["serviceprovider"] || [];
        }
        let label = item.label?.toLowerCase().replace(/\s+/g, "");
        return this.dropdownData[label] || [];
      };
    },
    noDataText() {
      if (this.designationListLoading) {
        return "Loading...";
      } else if (
        !this.designationListLoading &&
        this.designationList.length == 0 &&
        this.searchString.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  mounted() {
    this.commonFilters = JSON.parse(JSON.stringify(this.commonFilterDetails));
    if (this.dropdownData.organizationgroup.length <= 0) {
      this.commonFilters = this.commonFilters.filter((item) => {
        return item.label.toLowerCase() !== "organization group";
      });
    }
    if (this.dropdownData.businessunit.length <= 0) {
      this.commonFilters = this.commonFilters.filter((item) => {
        return item.label.toLowerCase() !== "business unit";
      });
    }

    if (!this.dropdownData.fieldforce) {
      this.commonFilters = this.commonFilters.filter((item) => {
        return item.label.toLowerCase() !== "service provider";
      });
    }
    this.prefillFormDetails();
  },
  methods: {
    /**
     * Parse Filter_Fields with comprehensive support for multiple data structures
     * Legacy format: [{"Leave Type": "Dropdown"}, {"Start Date": "DPicker"}]
     * Enhanced format: [{"Field_Key": "", "Mandatory": "no", "Leave Type": "Dropdown", "Show_On_Clone": "no", "Selection_Mode": "multi-select"}]
     * New format: [{"Name": "Leave Type", "Type": "Dropdown", "Field_Key": "", "Mandatory": "no", "Show_On_Clone": "no", "Selection_Mode": "multi-select"}]
     */
    parseFilterFields(filterFields) {
      return filterFields.map((item) => {
        let fieldName,
          fieldType,
          isMandatory = false,
          selectionMode = "single-select",
          fieldKey = "";

        // Check for new format with explicit Name and Type properties
        if (item.Name !== undefined && item.Type !== undefined) {
          // New format with explicit Name and Type properties
          fieldName = item.Name;
          fieldType = item.Type;
          isMandatory = item.Mandatory?.toLowerCase() === "yes";
          selectionMode =
            item.Selection_Mode === "multi-select"
              ? "multi-select"
              : "single-select";
          fieldKey = item.Field_Key || "";
        } else if (
          item.Mandatory !== undefined ||
          item.Selection_Mode !== undefined
        ) {
          // Enhanced format - find the field name (exclude metadata fields)
          const metadataFields = [
            "Field_Key",
            "Mandatory",
            "Show_On_Clone",
            "Selection_Mode",
            "Type",
          ];
          fieldName = Object.keys(item).find(
            (key) => !metadataFields.includes(key)
          );
          fieldType = item[fieldName];
          isMandatory = item.Mandatory?.toLowerCase() === "yes";
          selectionMode =
            item.Selection_Mode === "multi-select"
              ? "multi-select"
              : "single-select";
          fieldKey = item.Field_Key || "";
        } else {
          // Legacy format - use first key as field name
          fieldName = Object.keys(item)[0];
          fieldType = item[fieldName];
          // Apply legacy mandatory logic
          if (
            fieldName.toLowerCase() === "employee name" &&
            this.reportData?.Report_Title === "Employee Step Increment"
          ) {
            isMandatory = true;
          }
          selectionMode = "single-select";
          fieldKey = "";
        }

        // Apply field alias for service provider
        let displayLabel = fieldName;
        if (fieldName.toLowerCase() === "service provider") {
          displayLabel = this.labelList[115]?.Field_Alias || "Service Provider";
        }

        return {
          labels: displayLabel,
          fieldType: fieldType,
          listItems: [],
          selectedValue: null,
          isRequired: isMandatory,
          selectionMode: selectionMode,
          originalFieldName: fieldName,
          fieldKey: fieldKey,
        };
      });
    },

    prefillFormDetails() {
      if (this.reportData && this.reportData.Filter_Fields) {
        let filterFields = JSON.parse(this.reportData.Filter_Fields);
        this.reportFilters = this.parseFilterFields(filterFields);
        this.getReportFilterFields(this.reportData?.Report_Id);
      }
    },
    closeDateMenu(item) {
      item.dateMenu = false;
      item.selectedformattedValue = moment(item.selectedValue).format(
        "MMMM, yyyy"
      );
    },
    getReportFilterFields(reportId) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_REPORT_FILTER_FIELDS,
          client: "apolloClientAC",
          variables: {
            reportId: reportId,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getReportFilterFields &&
            !response.data.getReportFilterFields.errorCode
          ) {
            const filterDetails = JSON.parse(
              response.data.getReportFilterFields.filterDetails
            );
            const filterFieldDefaults = JSON.parse(
              response.data.getReportFilterFields.filterFieldDefaults
            );
            filterDetails.map((item, index) => {
              vm.reportFilters[index].listItems = item;
            });
            if (Object.keys(filterFieldDefaults).length) {
              Object.keys(filterFieldDefaults).map((key) => {
                let filterIndex = vm.reportFilters.findIndex(
                  (item) => item.labels.toLowerCase() === key.toLowerCase()
                );
                if (filterIndex !== -1) {
                  if (
                    vm.reportFilters[filterIndex].fieldType.toLowerCase() ===
                      "dpicker" &&
                    filterFieldDefaults[key] &&
                    filterFieldDefaults[key].fromDate &&
                    filterFieldDefaults[key].toDate
                  ) {
                    let orgDateFormat =
                      this.$store.state.orgDetails.orgDateFormat;

                    // Format dates according to org format
                    const fromDate = moment(
                      filterFieldDefaults[key].fromDate
                    ).format(orgDateFormat);
                    const toDate = moment(
                      filterFieldDefaults[key].toDate
                    ).format(orgDateFormat);
                    vm.reportFilters[
                      filterIndex
                    ].selectedValue = `${fromDate} to ${toDate}`;
                  } else {
                    vm.reportFilters[filterIndex].selectedValue =
                      filterFieldDefaults[key];
                  }
                }
              });
            }
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleRetrieveError(err);
        });
    },
    handleListError(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: "report filter fields",
        isListError: false,
      });
      this.closeAllForms();
    },
    // Build hierarchical structure for departments
    buildHierarchy(departments) {
      if (!departments || !Array.isArray(departments)) {
        return [];
      }

      const departmentMap = {};
      const hierarchy = [];

      // Convert items to department structure if needed
      const deptItems = [];

      // First pass: Create department objects
      departments.forEach((item) => {
        if (!item) return;

        // Handle case where item.value is an object with Department structure
        if (
          item.value &&
          typeof item.value === "object" &&
          item.value.Department_Id
        ) {
          // This is a department with nested structure
          const dept = {
            Department_Id: item.value.Department_Id,
            Department_Name: item.value.Department_Name || "",
            Parent_Type_Id: 0, // Default as root
            originalItem: item,
            hasChildren:
              item.value.Child &&
              Array.isArray(item.value.Child) &&
              item.value.Child.length > 0,
          };

          deptItems.push(dept);

          // Process children if they exist
          if (dept.hasChildren && item.value.Child) {
            item.value.Child.forEach((childItem) => {
              if (childItem && childItem.Department_Id) {
                deptItems.push({
                  Department_Id: childItem.Department_Id,
                  Department_Name: childItem.Department_Name || "",
                  Parent_Type_Id: dept.Department_Id, // Set parent ID to connect to parent
                  originalItem: {
                    id: childItem.Department_Id,
                    value: childItem,
                  },
                  hasChildren:
                    childItem.Child &&
                    Array.isArray(childItem.Child) &&
                    childItem.Child.length > 0,
                });

                // Process second level children if they exist
                if (
                  childItem.Child &&
                  Array.isArray(childItem.Child) &&
                  childItem.Child.length > 0
                ) {
                  childItem.Child.forEach((grandChild) => {
                    if (grandChild && grandChild.Department_Id) {
                      deptItems.push({
                        Department_Id: grandChild.Department_Id,
                        Department_Name: grandChild.Department_Name || "",
                        Parent_Type_Id: childItem.Department_Id,
                        originalItem: {
                          id: grandChild.Department_Id,
                          value: grandChild,
                        },
                        hasChildren: false,
                      });
                    }
                  });
                }
              }
            });
          }
        } else {
          // Default case - simple string value
          deptItems.push({
            Department_Id: item.id || "",
            Department_Name: typeof item.value === "string" ? item.value : "",
            Parent_Type_Id: 0,
            originalItem: item,
            hasChildren: false,
          });
        }
      });

      // Map all departments by their Department_Id
      deptItems.forEach((dept) => {
        if (dept && dept.Department_Id) {
          departmentMap[dept.Department_Id] = { ...dept, children: [] };
        }
      });

      // Organize departments into a hierarchical structure
      deptItems.forEach((dept) => {
        if (!dept || !dept.Department_Id) return;

        if (!dept.Parent_Type_Id || dept.Parent_Type_Id === 0) {
          // If it's a root department, add it to the hierarchy
          hierarchy.push(departmentMap[dept.Department_Id]);
        } else if (departmentMap[dept.Parent_Type_Id]) {
          // Else, add it to its parent's children array if parent exists
          departmentMap[dept.Parent_Type_Id].children.push(
            departmentMap[dept.Department_Id]
          );
        } else {
          // If parent doesn't exist, add as root
          hierarchy.push(departmentMap[dept.Department_Id]);
        }
      });

      return hierarchy;
    },
    callDesignationList(searchString) {
      // Clear existing timeout
      if (this.designationSearchTimeout) {
        clearTimeout(this.designationSearchTimeout);
      }

      // Set new timeout for debouncing
      this.designationSearchTimeout = setTimeout(() => {
        if (searchString.length >= 3) {
          this.getDesignationList(searchString);
        }
      }, 300); // 300ms delay
    },
    async getDesignationList(searchString) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designationList = designationResult;
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.designationList = [];
        });
    },
    onDesignationSelected(selectedId, item) {
      // Store the selected designation
      item.selectedValue = selectedId;

      // Find and store the selected designation object for this filter item
      const selectedDesignation = this.designationList.find(
        (designation) => designation.Designation_Id === selectedId
      );

      if (selectedDesignation) {
        // Use a unique key for each filter item (could be index or label)
        const filterKey = item.label || "designation";
        this.selectedDesignations[filterKey] = selectedDesignation;
      }
    },
    getDesignationListWithSelected(item) {
      // Get the current designation list
      let combinedList = [...this.designationList];

      // If there's a selected designation for this filter item, ensure it's in the list
      const filterKey = item.label || "designation";
      const selectedDesignation = this.selectedDesignations[filterKey];

      if (selectedDesignation && item.selectedValue) {
        // Check if the selected designation is already in the current list
        const existsInList = combinedList.some(
          (designation) =>
            designation.Designation_Id === selectedDesignation.Designation_Id
        );

        // If not in the list, add it to the beginning
        if (!existsInList) {
          combinedList.unshift(selectedDesignation);
        }
      }

      return combinedList;
    },

    // Flatten hierarchical structure with indentation levels
    flattenTree(items, level = 0) {
      if (!items || !Array.isArray(items)) {
        return [];
      }

      let flattened = [];
      items.forEach((item) => {
        if (!item) return;

        // Add indentation based on the level of hierarchy
        const originalItem = item.originalItem || {};

        // For department items, we need to format them differently
        if (item.Department_Name) {
          // Create a formatted display name with indentation
          const displayText = "  ".repeat(level) + (item.Department_Name || "");

          // If this is from a nested structure with Department_Id
          if (item.originalItem?.value?.Department_Id) {
            flattened.push({
              id: item.Department_Id || "",
              // Use the Department_Name as the value for display
              value: displayText,
              level: level,
              originalItem: originalItem,
            });
          } else {
            // For regular items
            flattened.push({
              id: item.Department_Id || "",
              value: displayText,
              level: level,
              originalItem: originalItem,
            });
          }
        } else {
          // Default case for non-department items
          flattened.push({
            id: item.Department_Id || "",
            value: item.Department_Name || "",
            level: level,
            originalItem: originalItem,
          });
        }

        // Recursively add children with increased indentation
        if (item.children && item.children.length > 0) {
          flattened = flattened.concat(
            this.flattenTree(item.children, level + 1)
          );
        }
      });
      return flattened;
    },

    // Format department items for the dropdown
    formatDepartmentItems(items) {
      // Return empty array if items is null or undefined
      if (!items || !Array.isArray(items)) {
        return [];
      }

      // Build hierarchy and flatten it for display
      const hierarchy = this.buildHierarchy(items);
      return this.flattenTree(hierarchy);
    },
    getSelectProperties(item) {
      const isMultiSelect = item.selectionMode === "multi-select";

      return {
        multiple: isMultiSelect,
        chips: isMultiSelect,
        closableChips: isMultiSelect,
        clearable: true,
      };
    },
    getListItemsFromFilterData(index) {
      const filterData = this.selectedItem?.Filter_Data;
      if (
        filterData &&
        filterData[index] &&
        Object.keys(filterData[index] || {}).length
      ) {
        return Object.entries(filterData[index] || {}).map(([key, value]) => ({
          id: key || "",
          value: value || "",
        }));
      }
      return [];
    },
    async validateReport() {
      const { valid } = await this.$refs.reportForm.validate();
      if (valid) {
        this.exportReports();
      }
    },
    exportReports() {
      let commonFilterArray =
        this.commonFilterDetails?.map((el) => {
          let filterValue = this.commonFilters.find((item) => {
            return item.label === el.label;
          });
          return filterValue?.selectedValue || "";
        }) || [];
      let filterArray = [];
      this.reportFilters?.forEach((el) => {
        if (el && el.fieldType?.toLowerCase() === "dpicker") {
          if (el.selectedValue) {
            let dateParts = el.selectedValue.split(" to ");
            if (dateParts.length === 2) {
              // Get the organization date format
              let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;

              // Parse dates using moment with the correct format
              let startDate = moment(dateParts[0], orgDateFormat).format(
                "YYYY-MM-DD"
              );
              let endDate = moment(dateParts[1], orgDateFormat).format(
                "YYYY-MM-DD"
              );
              filterArray.push(startDate, endDate);
            } else if (dateParts.length === 1) {
              let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
              let startDate = moment(dateParts[0], orgDateFormat).format(
                "YYYY-MM-DD"
              );
              filterArray.push(startDate, startDate);
            } else {
              filterArray.push("", "");
            }
          } else {
            filterArray.push("", "");
          }
        } else if (el && el.fieldType?.toLowerCase() === "mpicker") {
          filterArray.push(
            el.selectedValue ? moment(el.selectedValue).format("YYYY-MM") : ""
          );
        } else {
          // Handle both single-select and multi-select values
          if (
            el.selectionMode === "multi-select" &&
            Array.isArray(el.selectedValue)
          ) {
            filterArray.push(JSON.stringify(el.selectedValue || ""));
          } else {
            // For single-select or non-array values, use as is
            filterArray.push(el.selectedValue || "");
          }
        }
      });

      // Convert to a JSON string
      let filterString =
        commonFilterArray.concat(filterArray).map(String) || "";
      this.getReportDetails(this.reportData?.Report_Id, filterString);
    },
    getReportDetails(selectedReportId, filterString) {
      let vm = this;
      this.isLoading = true;
      vm.$apollo
        .query({
          query: GET_REPORT_DETAILS,
          client: "apolloClientAC",
          variables: {
            reportId: selectedReportId,
            filterValues: JSON.stringify(filterString),
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.generateReportDetails &&
            !response.data.generateReportDetails.errorCode
          ) {
            const { s3URL } = response.data.generateReportDetails;
            if (s3URL) {
              window.open(s3URL, "_blank");
            } else {
              let snackbarData = {
                isOpen: true,
                message: "No data found for the selected filters",
                type: "warning",
              };
              this.showAlert(snackbarData);
            }
          } else {
            this.handleRetrieveError(
              response.data.getReportDetails?.errorCode || ""
            );
          }
          this.isLoading = false;
        })
        .catch((err) => {
          this.handleRetrieveError(err);
        });
    },
    handleRetrieveError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "Report",
        isListError: false,
      });
    },
    closeAllForms() {
      this.commonFilters = [];
      this.showModel = false;
      this.$emit("close-model");
    },
    checkNullValue,
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style scoped>
.modal-heading {
  font-size: 1.3em;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__value) {
  min-width: 160px;
  display: flex;
  align-items: center;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__calendar) {
  right: 1px;
}
/* Responsive flat-pickr styling */
.date-btn {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  transition: all 0.3s ease;
  max-width: 100%;
  overflow: hidden;
}

.mobile-date-btn {
  padding: 6px 8px;
  font-size: 0.9rem;
  width: 100% !important;
}

/* Mobile specific styles for flat-pickr */
.mobile-date-picker {
  width: 150px !important;
  font-size: 0.9rem !important;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Media queries for responsive design */
@media (max-width: 600px) {
  .date-btn {
    width: 100% !important;
    justify-content: flex-start;
  }
}
</style>
