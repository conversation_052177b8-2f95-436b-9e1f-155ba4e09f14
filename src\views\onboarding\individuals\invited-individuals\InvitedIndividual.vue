<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      :showBottomSheet="!showAddForm"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row justify="center">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isFilter="true"
              :isApplyFilter="true"
              :list-items="originalData"
              :reset-filter-count="resetFilterCount"
              :appliedFilterCount="appliedFilterCount"
              ref="formFilterRef"
              @reset-emp-filter="resetFilter()"
              @applied-filter="onApplyFilter($event)"
            >
              <template #bottom-filter-menu>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedStatus"
                    color="primary"
                    :items="filteredStatusItems"
                    label="Status"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                  >
                  </v-select>
                </v-col>
              </template>
            </EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container v-if="formAccess" fluid class="invited-container">
      <div v-if="showAddForm">
        <InviteIndividualsModal
          :locationAutoPrefill="locationAutoPrefill"
          @submit-job-post-form="handleInviteSuccess"
          @close-add-invite-form="closeAddForm"
        />
      </div>
      <div v-else>
        <div v-if="listLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          key="error-screen"
          icon-name="fas fa-redo-alt"
          image-name="common/human-error-image"
          button-text="Retry"
          @button-click="refetchList()"
        ></AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="originalData.length === 0"
          key="no-data-screen"
          :main-title="emptyScenarioMsg"
          :isSmallImage="!isFilter"
          :image-name="!isFilter ? '' : 'common/no-records'"
        >
          <template v-if="!isFilter" #contentSlot>
            <div style="max-width: 80%">
              <v-row
                class="rounded-lg pa-5 mb-4"
                :style="!isFilter ? 'background: white' : ''"
              >
                <v-col cols="12">
                  <NotesCard
                    notes="In the onboarding invite module, ensure that all the necessary candidate details are accurately entered. This includes the candidate's location, department, designation, employee type, and work schedule. Double-check the information to avoid any errors."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    notes="Set Expiry Date: Set an expiry date for the self onboarding URL by choosing the URL validity. This will determine how long the URL remains valid for the candidate to access and complete their onboarding. Choose an appropriate expiry period based on the onboarding timeline and any specific requirements."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    notes="Generate Unique Self Onboarding URL: Once the candidate details are entered, generate a unique self onboarding URL for the candidate. This URL will serve as the access point for the candidate to complete their onboarding process. Make sure the URL is generated correctly and is associated with the specific candidate."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    notes="Verify Invitation: Before sending the onboarding invite, double-check all the details and ensure that the self onboarding URL is working correctly. Test the URL to ensure that it redirects the candidate to the designated onboarding platform or module."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                </v-col>
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    v-if="formAccess && formAccess.add"
                    variant="elevated"
                    rounded="lg"
                    prepend-icon="fas fa-plus"
                    class="primary"
                    @click="onOpenAddForm"
                  >
                    <template v-slot:prepend>
                      <v-icon size="15" class="pr-1 primary"></v-icon>
                    </template>
                    <span class="primary">Invite Individuals</span>
                  </v-btn>
                  <v-btn
                    color="transparent"
                    variant="flat"
                    class="ml-2 mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="itemList.length == 0"
          key="no-results-screen"
          main-title="There are no candidates matched for the selected filters/searches."
          image-name="common/no-records"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4">
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    variant="elevated"
                    color="primary"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    @click="resetFilter('grid')"
                  >
                    <span class="primary">Reset Filter/Search </span>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <div v-else>
          <div
            v-if="!showViewForm"
            class="d-flex align-center"
            :class="
              isMobileView
                ? 'd-flex flex-wrap align-center my-6 justify-center flex-column'
                : 'd-flex flex-wrap align-center my-4 justify-end'
            "
          >
            <v-btn
              v-if="formAccess && formAccess.add"
              variant="elevated"
              rounded="lg"
              prepend-icon="fas fa-plus"
              class="primary"
              @click="onOpenAddForm"
            >
              <template v-slot:prepend>
                <v-icon size="15" class="pr-1 primary"></v-icon>
              </template>
              <span class="primary">Invite Individuals</span>
            </v-btn>
            <v-btn color="transparent" variant="flat" @click="refetchList()">
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu class="mb-1" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                  <v-icon>fas fa-ellipsis-v</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="onMoreAction(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'pink-lighten-5': isHovering,
                        }"
                        ><v-icon size="15" class="pr-2">{{
                          action.icon
                        }}</v-icon
                        >{{ action.key }}</v-list-item-title
                      >
                    </template>
                  </v-hover>
                </v-list-item>
                <v-list-item
                  :disabled="!canResendInvites"
                  @click="resendInvites"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{ 'pink-lighten-5': isHovering }"
                      >
                        <v-icon size="15" class="pr-2"
                          >fas fa-paper-plane</v-icon
                        >Resend Invite
                      </v-list-item-title>
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
          <v-row>
            <v-col :cols="showViewForm && windowWidth >= 1264 ? 5 : 12">
              <v-data-table
                v-model="selectedCandidateRecords"
                :headers="headers"
                :items="itemList"
                :show-select="!isMobileView && !showViewForm"
                :items-per-page="50"
                fixed-header
                :height="
                  itemList.length > 11 ? $store.getters.getTableHeight(270) : ''
                "
                item-value="Candidate_Id"
                class="elevation-1"
                :sort-by="[{ key: 'Expire_Time', order: 'desc' }]"
                style="box-shadow: none !important"
              >
                <template v-slot:[`header.data-table-select`]>
                  <v-checkbox-btn
                    v-model="selectAllBox"
                    color="primary"
                    false-icon="far fa-circle"
                    true-icon="fas fa-check-circle"
                    indeterminate-icon="fas fa-minus-circle"
                    class="mt-1"
                    @change="toggleSelectAll(selectAllBox)"
                  ></v-checkbox-btn>
                </template>
                <template v-slot:item="{ item }">
                  <tr
                    style="z-index: 200"
                    class="data-table-tr bg-white cursor-pointer"
                    @click="onOpenViewForm(item)"
                    :class="[
                      isMobileView
                        ? ' v-data-table__mobile-table-row ma-0 mt-2'
                        : '',
                    ]"
                  >
                    <td
                      v-if="!showViewForm"
                      :class="isMobileView ? 'mt-3 mb-n5' : ''"
                      @click.stop="
                        {
                        }
                      "
                    >
                      <v-checkbox-btn
                        v-model="item.isSelected"
                        color="primary"
                        false-icon="far fa-circle"
                        true-icon="fas fa-check-circle"
                        class="mt-n2 ml-n2"
                        @click.stop="checkAllSelected()"
                        :disabled="item.Status === 'Self Onboarding Completed'"
                      ></v-checkbox-btn>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-small'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Candidate
                      </div>
                      <section class="d-flex align-center">
                        <div
                          v-if="
                            showViewForm &&
                            !isMobileView &&
                            selectedItem &&
                            selectedItem.Candidate_Id === item.Candidate_Id
                          "
                          class="data-table-side-border d-flex"
                        ></div>
                        <v-tooltip
                          :text="item.Candidate_Name"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div
                              class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                              :style="
                                !isMobileView
                                  ? 'max-width: 300px; '
                                  : 'max-width: 200px; '
                              "
                              v-bind="props"
                            >
                              {{ checkNullValue(item.Candidate_Name) }}
                              <div
                                v-if="item?.Candidate_Email"
                                class="text-grey"
                                :style="
                                  isMobileView
                                    ? 'white-space: normal; word-break: break-word'
                                    : ''
                                "
                              >
                                {{ checkNullValue(item.Candidate_Email) }}
                              </div>
                            </div>
                          </template>
                        </v-tooltip>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5 font-weight-medium'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Designation
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{
                            this.isServiceProvider
                              ? item.Service_Provider_Name
                              : item.Designation_Name
                          }}
                        </span>
                      </section>
                    </td>
                    <td
                      v-if="!showViewForm"
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Department
                      </div>
                      <section
                        class="text-body-2 text-truncate"
                        style="max-width: 150px"
                      >
                        <span class="text-body-2 font-weight-regular">
                          {{ checkNullValue(item.Department_Name) }}
                        </span>
                      </section>
                    </td>
                    <td
                      v-if="!showViewForm"
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Date of Join
                      </div>
                      <section>
                        <span class="text-body-2 font-weight-regular">
                          {{ formatDate(item.Date_Of_Join, true) }}
                        </span>
                      </section>
                    </td>
                    <td
                      v-if="!showViewForm"
                      :class="
                        isMobileView
                          ? ' d-flex justify-space-between align-center'
                          : ' pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? ' font-weight-bold d-flex align-center'
                            : ' font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        URL Validity
                      </div>
                      <section class="text-truncate">
                        <span :class="checkUrlValidity(item.Expire_Time)">
                          {{ convertUTCToLocal(item.Expire_Time) }}
                        </span>
                      </section>
                    </td>
                    <td
                      v-if="!showViewForm"
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        :class="
                          isMobileView
                            ? 'font-weight-bold d-flex align-center'
                            : 'font-weight-bold mt-2 d-flex align-center'
                        "
                      >
                        Status
                      </div>
                      <section
                        class="d-flex align-center justify-space-between"
                      >
                        <div class="d-flex align-center justify-space-around">
                          <span
                            id="w-80"
                            :class="statusColor(item.Status)"
                            class="text-body-2 font-weight-regular d-flex justify-center align-center text-center"
                            >{{ checkNullValue(item.Status) }}</span
                          >
                        </div>
                      </section>
                    </td>

                    <td
                      v-if="!showViewForm"
                      :class="
                        isMobileView
                          ? 'd-flex justify-space-between align-center'
                          : 'pa-2 pl-5'
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="font-weight-bold d-flex justify-space-between align-center"
                        style="width: 100%"
                      >
                        Actions
                      </div>
                      <section
                        class="d-flex justify-center align-center"
                        style="width: 100%"
                      >
                        <ActionMenu
                          v-if="itemActions(item).length > 0"
                          @selected-action="onActions($event, item)"
                          :accessRights="checkAccess()"
                          :actions="itemActions(item)"
                          iconColor="grey"
                        ></ActionMenu>
                        <div v-else>
                          <p>-</p>
                        </div>
                      </section>
                    </td>
                  </tr>
                </template>
              </v-data-table>
            </v-col>
            <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
              <ViewInvitedIndividuals
                @close-split-view="closeViewForm()"
                :selectedItem="selectedItem"
                :urlExpiryColorClass="urlExpiryColorClass"
              />
            </v-col>
          </v-row>
        </div>
      </div>
    </v-container>
    <AppAccessDenied v-else></AppAccessDenied>
  </div>
  <v-dialog
    class="pl-4"
    v-model="showViewForm"
    v-if="showViewForm && windowWidth < 1264"
    width="900"
    @click:outside="closeViewForm()"
  >
    <ViewInvitedIndividuals
      @close-split-view="closeViewForm()"
      :selectedItem="selectedItem"
      :urlExpired="urlExpired"
    />
  </v-dialog>
  <AppLoading v-if="isLoading"></AppLoading>

  <AppWarningModal
    v-if="openStatusConfirmationModal"
    :open-modal="openStatusConfirmationModal"
    :confirmation-heading="`Are you sure want to change the candidate status?`"
    @close-warning-modal="openStatusConfirmationModal = false"
    @accept-modal="updateCandidateStatus()"
  ></AppWarningModal>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import moment from "moment";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import FileExportMixin from "@/mixins/FileExportMixin";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import {
  LIST_INVITED_INDIVIDUALS,
  RESEND_CANDIDATE_INVITE,
} from "@/graphql/onboarding/individualQueries.js";
import { GET_SUN_FISH_DETAILS } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const InviteIndividualsModal = defineAsyncComponent(() =>
  import("./InviteIndividualsModal.vue")
);
const ViewInvitedIndividuals = defineAsyncComponent(() =>
  import("./ViewInvitedIndividuals.vue")
);
import { getCustomFieldName } from "@/helper";

export default {
  name: "InvitedIndividual",
  components: {
    EmployeeDefaultFilterMenu,
    InviteIndividualsModal,
    ViewInvitedIndividuals,
    NotesCard,
    ActionMenu,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // filter
    selectedStatus: [],
    resetFilterCount: 0,
    appliedFilterCount: 0,
    // add,edit,view
    showAddForm: false,
    showViewForm: false,
    // tab
    currentTabItem: "tab-0",
    mainTabList: ["Invited Individuals", "Onboarded Individuals"],
    // table
    isServiceProvider: false,
    selectedCandidateRecords: [],
    urlExpired: false,
    originalData: [],
    itemList: [],
    selectAllBox: false,
    selectedItem: null,
    isFilter: false,
    urlExpiryColorClass: "",
    templateData: {},
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
    isLoading: false,
    sunfishDetailsLoader: false,
    openStatusConfirmationModal: false,
    // export
    openMoreMenu: false,
    havingAccess: {},
    locationAutoPrefill: "No",
    showValidationAlert: false,
  }),
  mounted() {
    this.fetchUrls();
    this.getSunfishDetails();
  },
  computed: {
    filteredStatusItems() {
      if (this.fieldForce) {
        return [
          "Onboarding Inprogress",
          "Self Onboarding Completed",
          "Yet to be invited",
          "Returned",
        ];
      } else {
        return [
          "Onboarding Inprogress",
          "Self Onboarding Completed",
          "Returned",
        ];
      }
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("178");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    canResendInvites() {
      return this.selectedItems.some((item) => item.isSelected);
    },
    headers() {
      if (this.showViewForm) {
        return [
          {
            title: "Candidate",
            align: "start",
            key: "name",
          },
          //conditionally rendering Organization Unit and designation based on isServiceProvider data field
          {
            title: this.isServiceProvider
              ? getCustomFieldName(115, "Service Provider")
              : "Designation",
            key: this.isServiceProvider ? "serviceProvider" : "Designation",
          },
        ];
      } else {
        return [
          {
            title: "Candidate",
            align: "start",
            key: "Candidate_Name",
          },
          //conditionally rendering Organization Unit and designation based on isServiceProvider data field
          {
            title: this.isServiceProvider
              ? getCustomFieldName(115, "Service Provider")
              : "Designation",
            key: this.isServiceProvider
              ? "Service_Provider_Name"
              : "Designation_Name",
          },
          { title: "Department", key: "Department_Name" },
          { title: "Date of Join", key: "Date_Of_Join" },
          { title: "URL Validity", key: "Expire_Time" },
          { title: "Status", key: "Status" },
          { title: "Actions", key: "action", align: "center", sortable: false },
        ];
      }
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.isFilter) {
        msgText =
          "There are no invited individuals for the selected filters/searches";
      }
      return msgText;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    selectedItems() {
      let selected = this.itemList.filter((el) => el.isSelected === true);
      return selected && selected.length > 0 ? selected : [];
    },
  },
  watch: {
    selectedCandidateRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemList
        for (const item of this.itemList) {
          // Check if Candidate_Id is present in selRecords
          if (selRecords.includes(item.Candidate_Id)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through itemList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
      }
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    checkAccess() {
      let checkformAccess =
        this.formAccess && (this.formAccess.update || this.formAccess.add);
      this.havingAccess["resend invite"] = checkformAccess ? 1 : 0;
      this.havingAccess["invite"] = checkformAccess ? 1 : 0;
      return this.havingAccess;
    },
    changeStatusValue(status) {
      switch (status) {
        case "Draft":
          return "Onboarding Inprogress";
        case "Finished":
          return "Self Onboarding Completed";
        default:
          return status;
      }
    },
    statusColor(status) {
      switch (status) {
        case "Self Onboarding Completed":
          return "text-green";
        case "Onboarding Inprogress":
          return "text-primary";
        case "Supplier to be verified":
          return "text-amber";
        case "Yet to be invited":
          return "text-blue";
        default:
          return "";
      }
    },
    itemActions(item) {
      switch (item.Status) {
        case "Onboarding Inprogress":
          return ["Resend Invite"];
        case "Yet to be invited":
          return ["Resend Invite"];
        case "Returned":
          return ["Resend Invite"];
        default:
          return [];
      }
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    onApplyFilter(filteredArray) {
      let filteredList = filteredArray;
      if (this.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedStatus.includes(item.Status);
        });
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = filteredList;
    },
    // reset filter
    resetFilter(calledFrom) {
      this.itemList = this.originalData;
      this.isFilter = false;
      if (calledFrom === "grid") {
        this.resetFilterCount += 1;
      }
      this.selectedStatus = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
    },
    //function handling tab change
    onTabChange(tabName) {
      if (tabName !== "Invited Individuals") {
        this.$router.push("/onboarding/onboarded-individuals");
      }
    },
    toggleSelectAll(value) {
      if (value) {
        this.itemList.forEach((item) => {
          item.isSelected = item.Status !== "Self Onboarding Completed";
        });
      } else {
        this.itemList.forEach((item) => {
          item.isSelected = false;
        });
      }
    },
    checkAllSelected() {
      const selectedItems = this.itemList.filter((el) => el.isSelected);
      const selectableItems = this.itemList.filter(
        (el) => el.Status !== "Self Onboarding Completed"
      );
      this.selectAllBox = selectedItems.length === selectableItems.length;
    },
    formatDate(date, withoutTime) {
      let orgDateFormat = withoutTime
        ? this.$store.state.orgDetails.orgDateFormat
        : this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
      return date ? moment(date).format(orgDateFormat) : "-";
    },
    resendInvites() {
      let candidateIds = [];
      for (let candidate of this.selectedItems) {
        candidateIds.push(candidate.Candidate_Id);
      }
      if (candidateIds.length > 0) {
        this.triggerCandidateInvites(candidateIds);
      } else {
        let snackbarData = {
          isOpen: true,
          message: "No eligible candidates to resend invites.",
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    triggerCandidateInvites(candidates) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RESEND_CANDIDATE_INVITE,
          variables: {
            candidates: candidates,
            formId: 178,
          },
          client: "apolloClientV",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res.data &&
            res.data.triggerInviteCandidate &&
            !res.data.triggerInviteCandidate.errorCode
          ) {
            let snackbarData = {
              isOpen: true,
              message: "Invitation resend successfully",
              type: "success",
            };
            this.showAlert(snackbarData);
            vm.selectAllBox = false;
            vm.itemList.forEach((item) => {
              item.isSelected = false;
            });
            vm.refetchList();
          } else {
            vm.handleTriggerError();
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleTriggerError(err);
        });
    },
    handleTriggerError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "resending",
        form: "invite",
        isListError: false,
      });
    },
    // Retrieving the list of Invited Individuals
    fetchUrls() {
      let vm = this;
      vm.listLoading = true;
      vm.isErrorInList = false;
      vm.$apollo
        .query({
          query: LIST_INVITED_INDIVIDUALS,
          client: "apolloClientV",
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          vm.listLoading = false;
          if (data && data.listInvitedIndividuals) {
            const response = data.listInvitedIndividuals;
            if (response.errorCode === "" && response.listIndividuals) {
              const itemList = JSON.parse(response.listIndividuals);
              // Convert status values
              itemList.forEach((item) => {
                item.Status = vm.changeStatusValue(item.Status);
              });
              vm.itemList = itemList;
              vm.originalData = itemList;
            } else {
              vm.handleListError(response.message);
            }
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "invited individuals",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.fetchUrls();
      this.resetFilter();
    },
    checkUrlValidity(dateString) {
      const currentDate = new Date();
      if (!dateString) return;

      // Extract the date and time components from the date string
      const [datePart, timePart] = dateString.split(" ");

      // Extract the day, month, and year from the date component
      const [day, month, year] = datePart.split("/");

      // Extract the hours, minutes, and seconds from the time component
      const [hours, minutes, seconds] = timePart.split(":");

      // Create a new Date object using the extracted components (note that month is zero-based)
      const givenDate = new Date(year, month - 1, day, hours, minutes, seconds);

      // Compare the given date with the current date
      if (givenDate < currentDate) {
        this.urlExpired = true;
        return "text-red body-2 font-weight-regular";
      } else {
        this.urlExpired = false;
        return "body-2 font-weight-regular";
      }
    },
    onOpenAddForm() {
      this.showAddForm = true;
    },
    closeAddForm() {
      this.showAddForm = false;
    },
    handleInviteSuccess() {
      this.showAddForm = false;
      this.fetchUrls();
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action === "Invite") {
        this.onOpenAddForm();
      } else if (action === "Resend Invite") {
        this.triggerCandidateInvites([item.Candidate_Id]);
      }
    },

    onOpenViewForm(item) {
      this.selectedItem = item;
      this.urlExpiryColorClass = this.checkUrlValidity(item.Expire_Time);
      this.showViewForm = true;
    },
    closeViewForm() {
      this.selectedItem = null;
      this.showViewForm = false;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.itemList));
      if (this.selectedItems.length > 0) {
        exportData = this.selectedItems;
      }
      exportData = exportData.map((el) => ({
        ...el,
        Added_On: el.Created_At ? this.formatDate(el.Created_At) : "",
        Updated_On: el.Updated_At ? this.formatDate(el.Updated_At) : "",
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: "Individual Candidates",
        sheetName: "Individual Candidates",
        header: [
          { key: "Candidate_Name", header: "Candidate" },
          { key: "Candidate_Email", header: "Email Address" },
          { key: "Designation_Name", header: "Designation" },
          { key: "Department_Name", header: "Department" },
          { key: "Location_Name", header: "Location" },
          { key: "Employee_Type", header: "Employee Type" },
          { key: "WorkSchedule_Name", header: "Work Schedule" },
          { key: "Manager_Name", header: "Manager" },
          { key: "Service_Provider_Name", header: "Service Provider" },
          { key: "Status", header: "Status" },
          { key: "Added_On", header: "Created On" },
          { key: "Created_By_Name", header: "Created By" },
          { key: "Updated_On", header: "Updated On" },
          { key: "Updated_By_Name", header: "Updated By" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
    getSunfishDetails() {
      let vm = this;
      vm.sunfishDetailsLoader = true;
      vm.$apollo
        .query({
          query: GET_SUN_FISH_DETAILS,
          client: "apolloClientAS",
          variables: {
            integrationType: "",
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.retrieveOnboardingSettings) {
            const result = res.data.retrieveOnboardingSettings;
            vm.locationAutoPrefill = result.Enable_Location_Auto_Prefill;
          } else {
            vm.locationAutoPrefill = "No";
          }
          vm.sunfishDetailsLoader = false;
        })
        .catch(() => {
          vm.locationAutoPrefill = "No";
          vm.sunfishDetailsLoader = false;
        });
    },
  },
};
</script>
<style scoped>
.invited-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
@media screen and (max-width: 805px) {
  .invited-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
