<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
      :show-bottom-sheet="!listLoading && !showViewEditForm"
    >
      <template #topBarContent>
        <v-row v-show="!listLoading && !showViewEditForm" justify="center">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="justify-end mr-8"
              :isFilter="true"
              :isApplyFilter="true"
              :list-items="originalList"
              :reset-filter-count="resetFilterCount"
              :appliedFilterCount="appliedFilterCount"
              ref="formFilterRef"
              departmentIdKey="departmentId"
              designationIdKey="designationId"
              locationIdKey="locationId"
              empTypeIdKey="empTypeId"
              workScheduleIdKey="workScheduleId"
              @reset-emp-filter="resetFilter()"
              @applied-filter="applyFilter($event)"
            >
              <template #bottom-filter-menu>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedStatus"
                    color="primary"
                    :items="[
                      'Unverified',
                      'Verified',
                      'Returned',
                      'Onboarded',
                      'Rejected',
                      'Withdrawn',
                    ]"
                    label="Status"
                    multiple
                    closable-chips
                    chips
                    density="compact"
                    single-line
                  >
                  </v-select>
                </v-col>
              </template>
            </EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>

    <v-container fluid class="onboarded-container">
      <div v-if="formAccess">
        <div v-if="enableSkeletonLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          key="error-screen"
          icon-name="fas fa-redo-alt"
          image-name="common/human-error-image"
          button-text="Retry"
          @button-click="refetchList()"
        ></AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="originalList.length == 0"
          key="no-data-screen"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4" style="background: white">
                <v-col cols="12">
                  <NotesCard
                    notes="This category comprises individuals who have either self-onboarded or transitioned from job candidates within the system."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    notes="It provides comprehensive information about each individual, including personal details (e.g., name, contact information) and job-related specifics (e.g., position applied for, experience, skills)."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    notes="Users with appropriate permissions can access and modify candidate details. Additionally, this system facilitates the conversion of candidates into employees, streamlining the transition process within the organization."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                </v-col>
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    color="transparent"
                    variant="flat"
                    @click="refetchList()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="itemList.length == 0"
          key="no-results-screen"
          main-title="There are no candidates matched for the selected filters/searches."
          image-name="common/no-records"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row class="rounded-lg pa-5 mb-4">
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    color="primary"
                    variant="elevated"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="windowWidth <= 960 ? 'small' : 'default'"
                    @click="resetFilter('grid')"
                  >
                    Reset Filter/Search
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <div v-else>
          <v-row v-if="showViewEditForm"
            ><v-col cols="12">
              <ProfileTopCard
                :selectedCandidateId="selectedCandidateId"
                :formAccess="
                  formAccess &&
                  selectedCandidateStatus &&
                  selectedCandidateStatus.toLowerCase() === 'unverified'
                    ? formAccess
                    : false
                "
                :candidateList="itemList"
                :updateCount="updateCount"
                :selectedCandidateStatus="selectedCandidateStatus"
                :selectedCandidateDetails="selectedCandidateDetails"
                @close-profile="closeViewEditForm()"
                @on-change-candidate="onChangeCandidate($event)"
                @status-updated="onStatusUpdate($event)"
              ></ProfileTopCard>
              <AllProfileDetails
                :selectedCandidateId="selectedCandidateId"
                :selectedCandidateDOB="selectedCandidateDOB"
                :formAccess="
                  formAccess &&
                  selectedCandidateStatus &&
                  selectedCandidateStatus.toLowerCase() === 'unverified'
                    ? formAccess
                    : false
                "
                :selectedCandidateDetails="selectedCandidateDetails"
                :candidateList="originalList"
                :candidateChangeCount="candidateChangeCount"
                @close-add-form="closeViewEditForm()"
                @candidate-id-retrieved="selectedCandidateId = $event"
                @details-updated="updateCount += 1"
                @candidate-details-updated="onEmpDetailsUpdated($event)"
                @close-profile="closeViewEditForm()"
              /> </v-col
          ></v-row>
          <div v-else>
            <div
              class="d-flex align-center"
              :class="
                isMobileView
                  ? 'd-flex flex-wrap align-center my-6 justify-center flex-column'
                  : 'd-flex flex-wrap align-center my-4 justify-end'
              "
            >
              <v-btn
                color="transparent"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu
                class="mb-1"
                v-model="openMoreMenu"
                transition="scale-transition"
              >
                <template v-slot:activator="{ props }">
                  <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                    <v-icon>fas fa-ellipsis-v</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action.key"
                    @click="onMoreAction(action.key)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'pink-lighten-5': isHovering,
                          }"
                          ><v-icon
                            size="15"
                            class="mr-2"
                            :color="action.color"
                            >{{ action.icon }}</v-icon
                          >{{ action.key }}</v-list-item-title
                        >
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
            <v-row>
              <v-col cols="12" v-if="!enableSunfishLoader">
                <v-data-table
                  :headers="headers"
                  :items="itemList"
                  :items-per-page="50"
                  class="elevation-1"
                  color="secondary"
                  :search="searchValue"
                  style="box-shadow: none !important"
                  :height="
                    itemList.length > 11
                      ? $store.getters.getTableHeight(270)
                      : ''
                  "
                  fixed-header
                  :sort-by="[{ key: 'candidateStatus', order: 'desc' }]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="openViewForm(item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView
                          ? 'v-data-table__mobile-table-row ma-0 mt-2'
                          : ''
                      "
                    >
                      <td
                        id="mobile-view-td"
                        class="bg-white"
                        style="max-width: 200px; position: sticky; left: 0"
                      >
                        <div v-if="isMobileView" class="font-weight-bold mt-2">
                          Candidate Name
                        </div>
                        <section
                          class="text-primary text-body-2 font-weight-regular"
                        >
                          {{ checkNullValue(item.candidateName) }}
                          <div v-if="item.empEmail" class="text-grey">
                            {{ item.empEmail }}
                          </div>
                        </section>
                      </td>
                      <td id="mobile-view-td" style="max-width: 200px">
                        <div v-if="isMobileView" class="font-weight-bold mt-2">
                          Designation
                        </div>
                        <v-tooltip :text="item.designationName">
                          <template v-slot:activator="{ props }">
                            <section
                              :class="isMobileView ? '' : 'text-truncate'"
                              class="text-body-2 font-weight-regular"
                              v-bind="
                                item.designationName &&
                                item.designationName.length > 25
                                  ? props
                                  : ''
                              "
                            >
                              {{ checkNullValue(item.designationName) }}
                            </section>
                          </template>
                        </v-tooltip>
                      </td>
                      <td id="mobile-view-td" style="max-width: 200px">
                        <div v-if="isMobileView" class="font-weight-bold mt-2">
                          Department
                        </div>
                        <v-tooltip :text="item.departmentName">
                          <template v-slot:activator="{ props }">
                            <section
                              :class="isMobileView ? '' : 'text-truncate'"
                              class="text-body-2 font-weight-regular"
                              v-bind="props"
                            >
                              {{ checkNullValue(item.departmentName) }}
                            </section>
                          </template>
                        </v-tooltip>
                      </td>
                      <td id="mobile-view-td">
                        <div v-if="isMobileView" class="font-weight-bold mt-2">
                          Date of Join
                        </div>
                        <section class="text-body-2 font-weight-regular">
                          {{ formatDate(item.doj) }}
                        </section>
                      </td>
                      <td id="mobile-view-td">
                        <div v-if="isMobileView" class="font-weight-bold mt-2">
                          Location
                        </div>
                        <v-tooltip :text="item.locationName">
                          <template v-slot:activator="{ props }">
                            <section
                              style="max-width: 150px"
                              :class="isMobileView ? '' : 'text-truncate'"
                              class="text-body-2 font-weight-regular"
                              v-bind="props"
                            >
                              {{ checkNullValue(item.locationName) }}
                            </section>
                          </template>
                        </v-tooltip>
                      </td>
                      <td id="mobile-view-td">
                        <div v-if="isMobileView" class="font-weight-bold mt-2">
                          Status
                        </div>
                        <section
                          class="text-body-2 font-weight-medium"
                          :class="
                            item.candidateStatus === 'Verified'
                              ? 'text-blue'
                              : item.candidateStatus === 'Onboarded'
                              ? 'text-green'
                              : item.candidateStatus === 'Unverified'
                              ? 'text-amber'
                              : 'text-red'
                          "
                          style="max-width: 200px"
                        >
                          {{ item.candidateStatus }}
                        </section>
                      </td>
                      <td
                        v-if="
                          sunFishVisible &&
                          sunFishVisible !== '' &&
                          sunFishVisible == 'Active'
                        "
                        id="mobile-view-td"
                        class="text-center"
                        @click.stop="
                          {
                          }
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold">
                          Deployment Notification
                        </div>
                        <div
                          class="d-flex justify-start align-center"
                          v-if="
                            item.emailStatus &&
                            formAccess.update &&
                            item.sfOverAllStatus &&
                            ((sunFishAllowFailureNotification &&
                              item?.sfOverAllStatus?.toLowerCase() ===
                                'failed') ||
                              item?.sfOverAllStatus?.toLowerCase() ===
                                'success')
                          "
                        >
                          <v-menu
                            class="mb-1"
                            v-model="item.notificationMenu"
                            transition="scale-transition"
                          >
                            <template v-slot:activator="{ props }">
                              <v-btn
                                v-bind="
                                  deploymentType?.toLowerCase() === 'sunfish'
                                    ? props
                                    : ''
                                "
                                color="white"
                                density="compact"
                                style="width: fit-content"
                                :class="{
                                  'disabled-icon':
                                    !item.personalEmail ||
                                    item.personalEmail.trim() === '',
                                }"
                                @click="
                                  deploymentType?.toLowerCase() === 'sunfish'
                                    ? {}
                                    : onClickNotify(item)
                                "
                              >
                                Notify
                                <template v-slot:append>
                                  <v-icon
                                    :color="
                                      item.emailStatus === 'Open' ||
                                      item.hiringManagerEmailStatus === 'Open'
                                        ? 'grey-darken-1'
                                        : item.emailStatus === 'Success' &&
                                          item.hiringManagerEmailStatus ===
                                            'Success'
                                        ? 'green'
                                        : 'red'
                                    "
                                    size="15"
                                    class=""
                                  >
                                    far fa-paper-plane
                                  </v-icon>
                                </template>
                              </v-btn>
                            </template>
                            <v-list>
                              <v-list-item
                                v-for="action in notificationActions"
                                :key="action.key"
                                @click="onNotificationAction(action.key, item)"
                              >
                                <v-hover>
                                  <template
                                    v-slot:default="{ isHovering, props }"
                                  >
                                    <v-list-item-title
                                      v-bind="props"
                                      class="pa-3"
                                      :class="{
                                        'pink-lighten-5': isHovering,
                                      }"
                                    >
                                      {{ action.key }}
                                      <v-icon
                                        size="15"
                                        class="mr-2"
                                        :color="actionColor(item, action.key)"
                                        >{{ action.icon }}</v-icon
                                      >
                                    </v-list-item-title>
                                  </template>
                                </v-hover>
                              </v-list-item>
                            </v-list>
                          </v-menu>
                        </div>
                        <div
                          v-else
                          class="d-flex justify-start align-center ml-3"
                        >
                          -
                        </div>
                      </td>
                      <td
                        v-if="
                          sunFishVisible &&
                          sunFishVisible !== '' &&
                          sunFishVisible == 'Active'
                        "
                        id="mobile-view-td"
                        @click.stop="
                          {
                          }
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold mt-2">
                          Data Sync Status
                        </div>
                        <section
                          v-if="
                            item.sfOverAllStatus &&
                            item.sfOverAllStatus === 'Failed'
                          "
                          class="text-body-2 font-weight-bold text-red"
                          @click="onChangeSunfish(item)"
                        >
                          {{ checkNullValue(item.sfOverAllStatus) }}
                        </section>
                        <section
                          v-else-if="
                            item.sfOverAllStatus &&
                            item.sfOverAllStatus === 'Open'
                          "
                          class="text-body-2 font-weight-bold text-orange"
                          @click="onChangeSunfish(item)"
                        >
                          {{ checkNullValue(item.sfOverAllStatus) }}
                        </section>
                        <section
                          v-else-if="
                            item.sfOverAllStatus &&
                            item.sfOverAllStatus === 'Success'
                          "
                          class="text-body-2 font-weight-bold text-green"
                        >
                          {{ checkNullValue(item.sfOverAllStatus) }}
                        </section>
                        <section v-else class="text-body-2 font-weight-bold">
                          {{ checkNullValue(item.sfOverAllStatus) }}
                        </section>
                      </td>
                      <td
                        id="mobile-view-td"
                        class="text-center"
                        @click.stop="
                          {
                          }
                        "
                      >
                        <div v-if="isMobileView" class="font-weight-bold mt-2">
                          Actions
                        </div>
                        <ActionMenu
                          v-if="listActions(item.candidateStatus).length > 0"
                          :actions="listActions(item.candidateStatus)"
                          :access-rights="havingAccess"
                          :disableActionButtons="
                            item.personalEmail && item.personalEmail !== ''
                              ? []
                              : [
                                  'Verify',
                                  'Reject',
                                  'Return',
                                  'Convert to Employee',
                                ]
                          "
                          :tooltipActionButtons="
                            item.personalEmail && item.personalEmail !== ''
                              ? []
                              : [
                                  'Verify',
                                  'Reject',
                                  'Return',
                                  'Convert to Employee',
                                ]
                          "
                          @selected-action="
                            item.personalEmail && item.personalEmail !== ''
                              ? onActions($event, item)
                              : {}
                          "
                          iconColor="grey"
                          :tooltipMessage="
                            item.personalEmail && item.personalEmail !== ''
                              ? ``
                              : `Candidate requires personal email address for you to perform this action`
                          "
                        ></ActionMenu>
                        <section class="text-body-2 font-weight-medium" v-else>
                          -
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </div>
        </div>
      </div>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      @close-warning-modal="openWarningModal = false"
      @accept-modal="onDelete()"
    ></AppWarningModal>
    <AppWarningModal
      v-if="openStatusConfirmationModal"
      :open-modal="openStatusConfirmationModal"
      :confirmation-heading="modalText"
      @close-warning-modal="openStatusConfirmationModal = false"
      @accept-modal="validateEmpMail()"
    ></AppWarningModal>
    <AppWarningModal
      v-if="openConversionConfirmModal"
      :open-modal="openConversionConfirmModal"
      confirmation-heading="Are you sure to convert the candidate as employee?"
      @close-warning-modal="openConversionConfirmModal = false"
      @accept-modal="getCoverageSetting()"
    ></AppWarningModal>
    <v-dialog
      v-if="openConversionModal"
      v-model="openConversionModal"
      @click:outside="closeConversionModal()"
      width="600"
    >
      <v-card class="rounded-lg pa-4">
        <v-card-title class="d-flex">
          <span class="text-h6 text-primary font-weight-bold mb-4"
            >Candidate to Employee conversion</span
          >
          <v-spacer></v-spacer>
          <v-icon color="primary" size="25" @click="closeConversionModal()"
            >fas fa-times</v-icon
          >
        </v-card-title>
        <v-card-text>
          <div
            v-if="
              labelList['326'] &&
              labelList['326'].Field_Visiblity?.toLowerCase() === 'yes'
            "
            class="d-flex justify-start align-center item-center mt-n9 pl-1"
          >
            <span class="text-gray justify-start">
              {{ labelList["326"].Field_Alias }}
            </span>
            <v-switch
              color="primary"
              class="ml-4 mt-4"
              v-model="enableEmployeeId"
              :true-value="true"
              :false-value="false"
            />
          </div>
          <NotesCard
            v-if="idGenerationconfig && enableEmployeeId"
            imageName=""
            notes="Please note that the Employee Number Series setting is enabled; if you override the employee ID, ensure it is less than or equal to the next number in the series, and if the employee is not a rehire and you do not wish to retain the same employee ID, then do not override the employee ID."
            class="mb-4"
          ></NotesCard>
          <v-form ref="migrationForm">
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model.trim="migrationDetails.User_Defined_EmpId"
                  :rules="[
                    alreadyExistErrMsg['User_Defined_EmpId'],
                    required(
                      'Employee Id',
                      migrationDetails.User_Defined_EmpId
                    ),
                    validateWithRulesAndReturnMessages(
                      migrationDetails.User_Defined_EmpId,
                      'userDefinedEmpId',
                      'Employee Id'
                    ),
                  ]"
                  :disabled="!enableEmployeeId"
                  variant="solo"
                  :loading="fetchingMaxEmpId"
                  ref="employeeId"
                  @update:model-value="onChangeFields('User_Defined_EmpId')"
                  @change="
                    validateFieldAlreadyExist(
                      'User_Defined_EmpId',
                      'Employee Id'
                    )
                  "
                >
                  <template v-slot:label>
                    Employee Id<span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <!-- Work Email -->
              <v-col
                v-if="entomoIntegrationEnabled && isEntomoSyncTypePush"
                cols="12"
              >
                <v-text-field
                  v-model.trim="migrationDetails.workEmail"
                  variant="solo"
                  :rules="[
                    required(
                      labelList[459]?.Field_Alias || 'Work Email',
                      migrationDetails.workEmail
                    ),
                    checkFieldAvailability(migrationDetails.workEmail)
                      ? validateWithRulesAndReturnMessages(
                          migrationDetails.workEmail,
                          'personalEmail',
                          labelList[459]?.Field_Alias || 'Work Email'
                        )
                      : true,
                  ]"
                  ref="workEmail"
                  @update:model-value="onChangeFields('workEmail')"
                >
                  <template v-slot:label>
                    {{ labelList[459]?.Field_Alias || "Work Email"
                    }}<span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                v-else-if="
                  labelList[459]?.Field_Visiblity?.toLowerCase() === 'yes'
                "
              >
                <v-text-field
                  v-model="migrationDetails.workEmail"
                  :rules="[
                    labelList[459].Mandatory_Field?.toLowerCase() === 'yes'
                      ? required(
                          labelList[459].Field_Alias || 'Work Email',
                          migrationDetails.workEmail
                        )
                      : true,
                    checkFieldAvailability(migrationDetails.workEmail)
                      ? validateWithRulesAndReturnMessages(
                          migrationDetails.workEmail,
                          'personalEmail',
                          labelList[459].Field_Alias || 'Work Email'
                        )
                      : true,
                  ]"
                  variant="solo"
                  ref="workEmail"
                  @update:model-value="onChangeFields('workEmail')"
                >
                  <template v-slot:label>
                    {{ labelList[459].Field_Alias || "Work Email" }}
                    <span
                      style="color: red"
                      v-if="
                        labelList[459].Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" v-if="labelList[312].Field_Visiblity == 'Yes'">
                <CustomSelect
                  :items="roles"
                  v-model="selectedRole"
                  :itemSelected="selectedRole"
                  itemValue="Roles_Id"
                  itemTitle="Roles_Name"
                  :isAutoComplete="true"
                  :isLoading="dropdownListFetching"
                  :noDataText="
                    dropdownListFetching ? 'Loading...' : 'No data available'
                  "
                  :label="labelList[312].Field_Alias"
                  :isRequired="
                    labelList[312].Mandatory_Field == 'Yes' ? true : false
                  "
                  :rules="[
                    labelList[312].Mandatory_Field == 'Yes'
                      ? required(`${labelList[312].Field_Alias}`, selectedRole)
                      : true,
                  ]"
                  @selected-item="(e) => {}"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" v-if="labelList[187].Field_Visiblity == 'Yes'">
                <CustomSelect
                  :items="careerPICList"
                  v-model="selectedCareerPIC"
                  :itemSelected="selectedCareerPIC"
                  itemValue="Career_Id"
                  itemTitle="Career_Name"
                  :isLoading="careerListLoader"
                  :label="labelList[187].Field_Alias"
                  :isRequired="
                    labelList[187].Mandatory_Field == 'Yes' ? true : false
                  "
                  :rules="[
                    labelList[187].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[187].Field_Alias}`,
                          selectedCareerPIC
                        )
                      : true,
                  ]"
                  @selected-item="(e) => {}"
                ></CustomSelect>
              </v-col>
              <v-col cols="12" v-if="labelList[186].Field_Visiblity == 'Yes'">
                <CustomSelect
                  :items="timePICList"
                  :itemSelected="selectedTimePIC"
                  itemValue="Timekeeping_Id"
                  itemTitle="Timekeeping_Name"
                  :isLoading="careerListLoader"
                  :isAutoComplete="true"
                  :label="labelList[186].Field_Alias"
                  v-model="selectedTimePIC"
                  :isRequired="
                    labelList[186].Mandatory_Field == 'Yes' ? true : false
                  "
                  :rules="[
                    labelList[186].Mandatory_Field == 'Yes'
                      ? required(
                          `${labelList[186].Field_Alias}`,
                          selectedTimePIC
                        )
                      : true,
                  ]"
                  @selected-item="(e) => {}"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                v-if="labelList[475]?.Field_Visiblity?.toLowerCase() === 'yes'"
              >
                <v-text-field
                  v-model.trim="migrationDetails.External_EmpId"
                  ref="externalEmpId"
                  :rules="[
                    () => {
                      if (
                        labelList[475]?.Mandatory_Field?.toLowerCase() === 'yes'
                      ) {
                        return required(
                          labelList[475].Field_Alias ||
                            'Biometric Integration Id',
                          migrationDetails.External_EmpId
                        );
                      }
                      return true;
                    },
                    () => {
                      if (
                        checkFieldAvailability(migrationDetails.External_EmpId)
                      ) {
                        return alreadyExistErrMsg['External_EmpId'];
                      }
                      return true;
                    },
                    () => {
                      if (
                        checkFieldAvailability(migrationDetails.External_EmpId)
                      ) {
                        return validateWithRulesAndReturnMessages(
                          migrationDetails.External_EmpId,
                          'biometricIntegraionId',
                          labelList[475]?.Field_Alias ||
                            'Biometric Integration Id'
                        );
                      }
                      return true;
                    },
                  ]"
                  variant="solo"
                  @update:model-value="onChangeFields('External_EmpId')"
                  @change="
                    validateFieldAlreadyExist(
                      'External_EmpId',
                      labelList[475].Field_Alias || 'Biometric Integration Id'
                    )
                  "
                >
                  <template v-slot:label>
                    {{
                      labelList[475].Field_Alias || "Biometric Integration Id"
                    }}
                    <span
                      style="color: red"
                      v-if="
                        labelList[475].Mandatory_Field?.toLowerCase() === 'yes'
                      "
                      >*</span
                    >
                  </template>
                </v-text-field>
                <div
                  v-if="isValidExternalEmpId"
                  class="text-caption ml-2"
                  style="color: #f3012d; line-height: 1.2"
                  :class="
                    labelList[475]?.Mandatory_Field?.toLowerCase() === 'yes' &&
                    !migrationDetails.External_EmpId
                      ? ''
                      : 'mt-n4'
                  "
                >
                  Please enter the
                  {{ labelList[475].Field_Alias || "biometric integration id" }}
                  for biometric data processing.
                </div>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions class="d-flex justify-center">
          <v-btn
            rounded="lg"
            variant="outlined"
            class="primary"
            @click="closeConversionModal()"
            ><span class="primary">Cancel</span></v-btn
          >
          <v-btn
            rounded="lg"
            class="mr-1 secondary"
            variant="elevated"
            :disabled="isValidatingAlreadyExist"
            @click="validateEditForm()"
            ><span class="primary">Save</span>
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
  <v-dialog
    v-if="showIntegratedPopup"
    v-model="showIntegratedPopup"
    persistent
    width="400"
  >
    <v-card class="rounded-lg pa-4">
      <div>
        <v-row class="d-flex py-1 mr-3 pb-2" justify="space-between">
          <v-col cols="9">
            <div class="font-weight-bold ml-2 py-1">API Status</div>
          </v-col>
          <v-col cols="2">
            <v-tooltip text="Sync Again" location="top">
              <template v-slot:activator="{ props }">
                <v-btn
                  :disabled="syncLoader"
                  variant="outlined"
                  v-bind="props"
                  @click="triggerSync(false)"
                  class="rounded-pill w-auto pa-2 py-2 item-center d-flex justify-center primary"
                >
                  <v-icon color="primary" size="16">fas fa-sync-alt</v-icon>
                </v-btn>
              </template>
            </v-tooltip>
          </v-col>
        </v-row>
        <v-divider></v-divider>
        <v-row class="pt-2 pb-1">
          <v-col cols="12" v-for="api of apiStatus" :key="api.id">
            <span
              v-if="Object.keys(api || {}).length"
              class="d-flex justify-space-between"
            >
              <v-card variant="flat" class="d-flex" :disabled="syncLoader">
                <div
                  v-if="api?.status === 'Success'"
                  class="rounded-pill pa-1 bg-green-lighten-5 d-flex justify-center"
                  style="width: 30px"
                >
                  <v-icon color="green" size="25"> fas fa-check-circle </v-icon>
                </div>
                <div
                  v-else
                  class="rounded-pill pa-1 bg-red-lighten-5 d-flex justify-center"
                >
                  <v-icon color="red" size="25"> fas fa-times-circle </v-icon>
                </div>
                <v-tooltip v-if="api?.reason" :text="api.reason" location="top">
                  <template v-slot:activator="{ props }">
                    <div
                      v-bind="props"
                      class="font-weight-normal ml-2 text-subtitle-1"
                    >
                      {{ api.name }}
                    </div>
                  </template>
                </v-tooltip>
                <div v-else class="font-weight-normal ml-2 text-subtitle-1">
                  {{ api.name }}
                </div>
              </v-card>
              <div v-if="syncLoader">
                <v-progress-circular
                  color="blue-grey-darken-1"
                  indeterminate
                  width="2"
                  size="25"
                ></v-progress-circular>
              </div>
            </span>
          </v-col>
        </v-row>
        <v-divider></v-divider>
        <v-row class="d-flex mt-2 pb-1" justify="end">
          <v-btn
            class="text-none"
            text="Cancel"
            @click="showIntegratedPopup = false"
            rounded="lg"
            variant="outlined"
            color="primary"
          ></v-btn>
        </v-row>
      </div>
    </v-card>
  </v-dialog>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="3000"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <ExportEmployeeDataDialog
    :open-dialog="openExportModal"
    screen="onboarded"
    :sunfish="sunFishVisible"
    :deployment-type="deploymentType"
    :formId="formId"
    @close-modal="openExportModal = false"
  ></ExportEmployeeDataDialog>
  <EmailTemplateOverlay
    v-if="showCustomEmail"
    ref="customEmail"
    :emailRecievers="emailReciever"
    :ccEmailRecievers="ccEmailReciever"
    :candidateId="selectedCandidateId"
    :toCCExchange="toCCExchange"
    :typeOfTemplate="templateType"
    :typeOfSchedule="typeOfSchedule"
    :templateEmail="templateEmail"
    :templateData="templateData"
    :actionName="actionName"
    @custom-email-sent="onSendCustomEmail($event)"
    @close-email-template-window="onCloseEmail()"
  ></EmailTemplateOverlay>
</template>
<script>
import { defineAsyncComponent } from "vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const ProfileTopCard = defineAsyncComponent(() =>
  import("./profile-details/ProfileTopCard.vue")
);
const AllProfileDetails = defineAsyncComponent(() =>
  import("./profile-details/AllProfileDetails.vue")
);
import {
  convertUTCToLocal,
  getErrorCodesAndMessagesWithValidation,
} from "@/helper";
// Queries
import {
  LIST_ONBOARDED_CANDIDATES,
  UPDATE_CANDIDATE_STATUS,
  CONVERT_CANDIDATE_TO_EMPLOYEE,
  GET_SUN_FISH_DETAILS,
  TRIGGER_SYNC_DETAILS,
  RETRIEVE_HIRING_MANAGER,
} from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import {
  RETRIEVE_MAX_EMP_ID,
  VALIDATE_FIELD_AVAILABILITY,
} from "@/graphql/employee-profile/profileQueries.js";
import { checkNullValue } from "@/helper.js";
import moment from "moment";
import validationRules from "@/mixins/validationRules";
import EmailTemplateOverlay from "./EmailTemplateOverlay.vue";
import { TAX_CODE_LIST } from "@/graphql/dropDownQueries.js";
import {
  RETRIEVE_EMPLOYEE_NUMBER_SERIES,
  GET_MAX_EMPLOYEE_ID,
} from "@/graphql/settings/core-hr/employeeNumberSeries.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import ExportEmployeeDataDialog from "@/views/my-team/team-summary/employee-data-import/ExportEmployeeDataDialog.vue";

export default {
  components: {
    ProfileTopCard,
    AllProfileDetails,
    EmployeeDefaultFilterMenu,
    ActionMenu,
    NotesCard,
    EmailTemplateOverlay,
    CustomSelect,
    ExportEmployeeDataDialog,
  },
  name: "OnboardedIndividual",
  mixins: [FileExportMixin, validationRules],
  data: () => ({
    // tab
    currentTabItem: "tab-1",
    mainTabList: ["Invited Individuals", "Onboarded Individuals"],
    // filter
    selectedStatus: [],
    resetFilterCount: 0,
    appliedFilterCount: 0,
    // table
    originalList: [],
    itemList: [],
    //export
    openMoreMenu: false,
    // error/loading
    errorContent: "",
    isErrorInList: false,
    listLoading: false,
    isLoading: false,
    validationMessages: [],
    havingAccess: {},
    showValidationAlert: false,
    // warning model
    openWarningModal: false,
    openStatusConfirmationModal: false,
    openConversionConfirmModal: false,
    openConversionModal: false,
    // action
    selectedAction: "",
    selectedActionItem: null,
    migrationDetails: {
      User_Defined_EmpId: "",
      External_EmpId: "",
      workEmail: "",
    },
    alreadyExistErrMsg: {
      External_EmpId: true,
      User_Defined_EmpId: true,
    },
    fetchingMaxEmpId: false,
    isValidatingAlreadyExist: false,
    showCustomEmail: false,
    templateData: {},
    // view
    showViewEditForm: false,
    selectedCandidateId: 0,
    selectedCandidateStatus: "Unverified",
    selectedCandidateDOB: "",
    selectedCandidateDetails: {},
    updateCount: 0,
    candidateChangeCount: 0,
    showIntegratedPopup: false,
    toCCExchange: false,
    apiStatus: {
      sfPreviousRehireData: { id: 10, name: "Rehire Data" },
      sfEmployeeInfoStatus: { id: 1, name: "Employee Data" },
      sfPersonalInfoStatus: { id: 2, name: "Personal Data" },
      sfAdditionalInfoStatus: {
        id: 3,
        name: "Additional Data",
      },
      sfEducationInfoStatus: {
        id: 4,
        name: "Education Data",
      },
      sfAttachmentInfoStatus: { id: 5, name: "Attachment Data" },
      sfAccreditationInfoStatus: { id: 6, name: "Accreditations Data" },
      sfCertificateInfoStatus: { id: 7, name: "Certificate Data" },
      sfLanguageInfoStatus: { id: 8, name: "Language Data" },
      sfPreviousEmployementData: { id: 9, name: "Previous Employement Data" },
    },
    selectedIntegratedStatus: "",
    syncLoader: false,
    sunFishVisible: "",
    sunFishAllowFailureNotification: false,
    selectedCandidate: { candidateId: 0 },
    selectedTimePIC: null,
    selectedCareerPIC: null,
    timePICList: [],
    careerPICList: [],
    careerListLoader: false,
    templateType: "",
    enableEmployeeId: false,
    enableSunfishDeployment: false,
    typeOfSchedule: "nocalendar",
    enableSunfishLoader: false,
    formId: 178,
    dropdownListFetching: false,
    roles: [],
    selectedRole: null,
    openNotificationMenu: false,
    hiringManagerDetails: {},
    recruitersEmailList: [],
    emailReciever: [],
    ccEmailReciever: [],
    adminEmails: [],
    openExportModal: false,
    actionName: "",
    deploymentType: "",
    idGenerationconfig: false,
    idGenerationCoverage: "",
    settingsArray: [],
  }),
  computed: {
    enableSkeletonLoading() {
      return this.enableSunfishLoader || this.listLoading;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let individualFormAccess = this.accessRights("178");
      if (
        individualFormAccess &&
        individualFormAccess.accessRights &&
        individualFormAccess.accessRights["view"]
      ) {
        return individualFormAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForCandidateVerification() {
      let formAccess = this.accessRights("274");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForCandidateRejection() {
      let formAccess = this.accessRights("279");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForCandidateWithdraw() {
      let formAccess = this.accessRights("302");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
          color: "primary",
        },
        {
          key: "Export with filters",
          icon: "fas fa-file-export",
          color: "primary",
        },
      ];
      return actions;
    },
    notificationActions() {
      let actions = [
        {
          key: "Notify Candidate",
          icon: "fas fa-paper-plane",
        },
        {
          key: "Notify Hiring Manager",
          icon: "fas fa-paper-plane",
        },
      ];
      return actions;
    },
    actionColor() {
      return (item, action) => {
        if (action == "Notify Candidate") {
          if (item.emailStatus.toLowerCase() == "open") {
            return "grey-darken-1";
          } else if (item.emailStatus.toLowerCase() == "success") {
            return "green";
          } else {
            return "red";
          }
        } else if (action == "Notify Hiring Manager") {
          if (item.hiringManagerEmailStatus.toLowerCase() == "open") {
            return "grey-darken-1";
          } else if (item.emailStatus.toLowerCase() == "success") {
            return "green";
          } else {
            return "red";
          }
        }
      };
    },
    addressLine1() {
      let organization = {};
      if (this.fieldForce) {
        organization = this.$store.state.orgDetails.serviceProvider;
      } else {
        organization = this.$store.state.orgDetails.organization;
      }
      let line1 = [];
      if (organization.street1) {
        line1.push(organization.street1);
      }
      if (organization.street2) {
        line1.push(organization.street2);
      }
      return line1.length > 0 ? line1.join(",") : "";
    },
    addressLine2() {
      const { organization } = this.$store.state.orgDetails;
      let line2 = [];
      if (organization.city) {
        line2.push(organization.city);
      }
      if (organization.state) {
        line2.push(organization.state);
      }
      if (organization.country) {
        line2.push(organization.country);
      }
      if (organization.pincode) {
        line2.push(organization.pincode);
      }
      return line2.length > 0 ? line2.join(",") : "";
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    headers() {
      let headers = [
        {
          title: "Candidate Name",
          align: "start",
          key: "candidateName",
          fixed: true,
        },
        {
          title: "Designation",
          key: "designationName",
        },
        { title: "Department", key: "departmentName" },
        { title: "Date of Join", key: "doj" },
        { title: "Location", key: "locationName" },
        { title: "Status", key: "candidateStatus" },
      ];
      if (
        this.sunFishVisible &&
        this.sunFishVisible !== "" &&
        this.sunFishVisible == "Active"
      ) {
        headers.push({
          title: "Deployment Notification",
          key: "emailStatus",
        });
        headers.push({ title: "Data Sync Status", key: "sfOverAllStatus" });
      }
      headers.push({
        title: "Actions",
        key: "actions",
        align: "center",
        sortable: false,
      });
      return headers;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isValidExternalEmpId() {
      if (this.migrationDetails.External_EmpId) {
        let field = this.$refs.externalEmpId;
        if (field && field.rules && field.rules.length > 0) {
          return field.rules.every((value) => value === true);
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
    loginEmployeeUser() {
      return (
        this.$store.state.userDetails.employeeFirstName +
        " " +
        this.$store.state.userDetails.employeeLastName
      );
    },
    modalText() {
      if (this.selectedAction === "Return") {
        return "Returning the candidate form will automatically extend the expiry of the self-onboarding link, allowing the candidate to update the required details. In the next steps, you can add your remarks to an email and preview the message before sending it to the candidate.";
      }
      return `Are you sure want to change the candidate status to ${this.selectedAction}? Once changed, candidate details cannot be updated`;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    templateEmail() {
      let emailList = [];
      if (this.selectedActionItem) {
        if (
          this.selectedActionItem.personalEmail &&
          this.selectedActionItem.personalEmail.length
        ) {
          emailList.push(this.selectedActionItem.personalEmail);
        }
        if (
          this.selectedAction === "Return" ||
          this.selectedAction === "Reject"
        ) {
          return emailList;
        }
        return emailList;
      } else if (this.templateType === "onboardingHiringTeamNotification") {
        return this.recruitersEmailList;
      } else {
        return emailList;
      }
    },
    orgDetails() {
      return this.$store.state.orgDetails;
    },
    loginEmployeeDetails() {
      return this.$store.state.orgDetails.userDetails;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    enableSunfishDeployment(val) {
      if (val) {
        this.typeOfSchedule = "sunfishDeploymentEmail";
      }
    },
  },

  mounted() {
    this.fetchList();
    this.getSunfishDetails();
    this.retrieveTaxCodeList();
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    retrieveDropdownDetails() {
      this.dropdownListFetching = true;
      this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { roles } = res.data.getDropDownBoxDetails;
            this.selectedRole = this.selectedActionItem.rolesId;
            this.roles = roles;
            this.dropdownListFetching = false;
          }
        })
        .catch(() => {
          this.dropdownListFetching = false;
        });
    },
    closeConversionModal() {
      this.openConversionModal = null;
      this.selectedRole = null;
    },
    onTabChange(tabName) {
      if (tabName !== "Onboarded Individuals") {
        this.$router.push("/onboarding/individuals");
      }
    },

    retrieveTaxCodeList() {
      let vm = this;
      vm.careerListLoader = true;
      vm.$apollo
        .query({
          query: TAX_CODE_LIST,
          client: "apolloClientAS",
        })
        .then((res) => {
          if (
            res.data &&
            res.data.listTimekeepingCareerDetail &&
            res.data.listTimekeepingCareerDetail
          ) {
            this.careerPICList = [];
            this.timePICList = [];
            const listData = res.data.listTimekeepingCareerDetail;
            if (listData.career && listData.career.length) {
              this.careerPICList = listData.career;
            }
            if (listData.timeKeeping && listData.timeKeeping.length) {
              this.timePICList = listData.timeKeeping;
            }
          }
          vm.careerListLoader = false;
        })
        .catch(() => {
          vm.careerListLoader = false;
        });
    },
    listActions(status) {
      if (this.formAccess) {
        if (status === "Unverified") {
          if (
            this.formAccessForCandidateVerification &&
            this.formAccessForCandidateVerification.update
          ) {
            this.havingAccess.verify =
              this.formAccessForCandidateVerification.update;
            this.havingAccess.return =
              this.formAccessForCandidateVerification.update;
          }
          if (
            this.formAccessForCandidateRejection &&
            this.formAccessForCandidateRejection.update
          ) {
            this.havingAccess.reject =
              this.formAccessForCandidateRejection.update;
          }
          if (
            this.formAccessForCandidateWithdraw &&
            this.formAccessForCandidateWithdraw.update
          ) {
            this.havingAccess["candidate withdrawn"] =
              this.formAccessForCandidateWithdraw.update;
          }
          if (
            this.formAccessForCandidateWithdraw &&
            this.formAccessForCandidateWithdraw.view
          ) {
            return ["Verify", "Reject", "Return", "Candidate Withdrawn"];
          } else return ["Verify", "Reject", "Return"];
        } else if (status === "Verified") {
          this.havingAccess["convert to employee"] =
            this.formAccess && (this.formAccess.add || this.formAccess.update);
          if (
            this.formAccessForCandidateWithdraw &&
            this.formAccessForCandidateWithdraw.update
          ) {
            this.havingAccess["candidate withdrawn"] =
              this.formAccessForCandidateWithdraw.update;
          }
          if (
            this.formAccessForCandidateWithdraw &&
            this.formAccessForCandidateWithdraw.view
          ) {
            return ["Convert to Employee", "Candidate Withdrawn"];
          } else return ["Convert to Employee"];
        } else if (status == "Withdrawn") {
          if (
            this.formAccessForCandidateVerification &&
            this.formAccessForCandidateVerification.update
          ) {
            this.havingAccess.return =
              this.formAccessForCandidateVerification.update;
            this.havingAccess.unverify =
              this.formAccessForCandidateVerification.update;
          }
          if (
            this.formAccessForCandidateRejection &&
            this.formAccessForCandidateRejection.update
          ) {
            this.havingAccess.reject =
              this.formAccessForCandidateRejection.update;
          }
          return ["Reject", "Return", "Unverify"];
        } else return [];
      } else return [];
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      if (actionType === "Export with filters") {
        this.openExportModal = true;
      }
      this.openMoreMenu = false;
    },
    openViewForm(item) {
      this.selectedCandidateDetails = item;
      this.selectedCandidateId = item.Candidate_Id;
      this.selectedCandidateStatus = item.candidateStatus;
      this.selectedCandidateDOB = item.dob;
      this.showViewEditForm = true;
    },
    onChangeCandidate(item) {
      this.selectedCandidateDetails = item;
      this.selectedCandidateId = item.Candidate_Id;
      this.selectedCandidateStatus = item.candidateStatus;
      this.selectedCandidateDOB = item.dob;
      this.candidateChangeCount += 1;
    },
    onStatusUpdate(status) {
      this.selectedCandidateStatus = status;
      this.updateCount += 1;
    },
    onEmpDetailsUpdated(details) {
      const combinedObj = { ...this.selectedCandidateDetails, ...details };
      this.selectedCandidateDetails = combinedObj;
    },
    closeViewEditForm() {
      this.$store.commit("onboarding/UPDATE_EDIT_FORM_OPENED_COUNT", "0-false");
      if (this.updateCount > 0) {
        this.refetchList();
      } else {
        this.showViewEditForm = false;
        this.selectedCandidateId = 0;
        this.selectedCandidateDetails = {};
      }
    },
    onClickNotify(item) {
      this.selectedCandidateId = item.Candidate_Id;
      this.selectedActionItem = item;
      this.actionName = "Deployment Notification";
      this.enableSunfishDeployment = true;
      this.showCustomEmail = true;
    },
    onChangeStatus(item) {
      this.enableSunfishDeployment = true;
      this.selectedAction = "";
      let templateData = {
        Candidate_Name: item.candidateName,
        Recruiter_Name: item.candidateFirstName,
        Location: item.locationName,
        Start_Time: this.formatDate(item.doj),
        Company_Name: item.createdBy,
        Specialist_Email: item.createdByEmail,
        emailTemplateType: "Sunfish Deployment",
      };
      this.templateData = templateData;
      this.selectedActionItem = item;
      this.selectedCandidateId = item.Candidate_Id;
      let emailReciever = [];
      this.ccEmailReciever = [];
      this.emailReciever = [];
      if (this.adminEmails && this.adminEmails.length > 0) {
        emailReciever = this.adminEmails;
      }
      if (
        this.selectedActionItem.createdByEmail &&
        this.selectedActionItem.createdByEmail.length
      ) {
        emailReciever.push(this.selectedActionItem.createdByEmail);
      }
      if (this.enableSunfishDeployment) {
        if (
          this.selectedActionItem.recruiterEmail &&
          this.selectedActionItem.recruiterEmail.length
        ) {
          emailReciever.concat([this.selectedActionItem.recruiterEmail]);
        }
      }
      this.ccEmailReciever = emailReciever;
      this.templateType = "sunfishDeploymentEmail";
      this.showCustomEmail = true;
      this.toCCExchange = true;
    },
    onCloseEmail() {
      this.showCustomEmail = false;
      this.toCCExchange = false;
      this.enableSunfishDeployment = false;
      this.recruitersEmailList = [];
      this.emailReciever = [];
      this.ccEmailReciever = [];
      this.selectedActionItem = null;
    },
    onActions(action, item) {
      this.selectedActionItem = item;
      this.selectedAction = action;
      this.selectedCandidateId = item.Candidate_Id;
      this.actionName =
        action?.toLowerCase() === "candidate withdrawn"
          ? action
          : action + " Candidate";
      if (action === "Delete") {
        this.openWarningModal = true;
      } else if (action === "Verify" || action === "Unverify") {
        this.openStatusConfirmationModal = true;
      } else if (action === "Convert to Employee") {
        this.migrationDetails["User_Defined_EmpId"] = "";
        this.migrationDetails["External_EmpId"] = "";
        this.migrationDetails["workEmail"] = item.empEmail ? item.empEmail : "";
        this.selectedCandidate = {
          candidateId: item.Candidate_Id,
          serviceProviderId: item.Service_Provider_Id,
        };
        this.openConversionConfirmModal = true;
      } else if (action === "Return") {
        let templateData = {
          Recruiter_Name: this.loginEmployeeUser,
          Candidate_Name: item.candidateName,
          Url: item.url,
          Pin: item.pin,
        };
        this.formId = 274;
        this.templateData = templateData;
        this.templateType = "candidateFinishToDraft";
        this.openStatusConfirmationModal = true;
      } else if (action === "Reject") {
        this.formId = 279;
        let templateData = {
          Candidate_Name: item.candidateName,
          Company_Name: this.companyName,
          Recruiter_Name: this.loginEmployeeUser,
          Company_Address_1: this.addressLine1,
          Company_Address_2: this.addressLine2,
          Url: item.url,
          Pin: item.pin,
        };
        this.templateData = templateData;
        this.templateType = "rejectedCandidateOnboarded";
        this.openStatusConfirmationModal = true;
      } else if (action === "Candidate Withdrawn") {
        this.formId = 302;
        let templateData = {
          Candidate_Name: item.candidateName,
          Company_Name: this.companyName,
          Recruiter_Name: this.loginEmployeeUser,
          Job_Post_Name: item.designationName,
        };
        this.templateData = templateData;
        this.templateType = "candidateWithdrawn";
        this.openStatusConfirmationModal = true;
      }
    },
    onNotificationAction(action, item) {
      this.actionName = action;
      if (action === "Notify Candidate") {
        if (!item.personalEmail || item.personalEmail.trim() === "") {
          let snackbarData = {
            isOpen: true,
            message: "Candidate does not have a personal email",
            type: "warning",
          };
          this.showAlert(snackbarData);
        } else {
          this.onChangeStatus(item);
        }
      } else if (action === "Notify Hiring Manager") {
        this.selectedCandidate = item;
        this.selectedCandidateId = item.Candidate_Id;
        this.getHiringManager(item);
      }
    },
    onDelete() {
      this.openWarningModal = false;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    resetFilter(calledFrom) {
      if (calledFrom === "grid") {
        this.resetFilterCount += 1;
      }
      this.selectedStatus = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = this.originalList;
    },
    applyFilter(filteredArray) {
      let filteredList = filteredArray;
      if (this.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedStatus.includes(item.candidateStatus);
        });
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.itemList = filteredList;
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_ONBOARDED_CANDIDATES,
          client: "apolloClientV",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listCandidateDetails &&
            !response.data.listCandidateDetails.errorCode
          ) {
            let candidateList =
              response.data.listCandidateDetails.listCandidates;
            candidateList = candidateList ? JSON.parse(candidateList) : [];
            candidateList.forEach((candidate) => {
              if (candidate.Data_Sync_Result) {
                candidate.Data_Sync_Result = JSON.parse(
                  candidate.Data_Sync_Result
                );
              }
              if (candidate.candidateStatus === "Migrated") {
                candidate.candidateStatus = "Onboarded";
              }
            });
            vm.originalList = candidateList;
            vm.itemList = candidateList;
            this.adminEmails = response.data.listCandidateDetails.adminEmail;
            vm.onApplySearch();
            vm.listLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    getHiringManager(item) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_HIRING_MANAGER,
          variables: { candidateId: item.Candidate_Id },
          client: "apolloClientV",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getNewHireEmployeeDetails &&
            response.data.getNewHireEmployeeDetails.hiringManagerDetails
          ) {
            let { hiringManagerDetails } =
              response.data.getNewHireEmployeeDetails;
            hiringManagerDetails = JSON.parse(hiringManagerDetails);
            let doj = moment.utc(item.doj).toDate();
            doj = moment(doj).format("MMMM DD,YYYY");
            let employee =
              this.loginEmployeeDetails.employeeLastName +
              ", " +
              this.loginEmployeeDetails.employeeFirstName;
            let candidateArray = [];
            if (hiringManagerDetails.Emp_Last_Name) {
              candidateArray.push(hiringManagerDetails.Emp_Last_Name);
            }
            if (hiringManagerDetails.Emp_First_Name) {
              candidateArray.push(hiringManagerDetails.Emp_First_Name);
            }
            let candidateName = candidateArray.join(", ");
            candidateName = hiringManagerDetails.Emp_Middle_Name
              ? candidateName + " " + hiringManagerDetails.Emp_Middle_Name
              : candidateName;
            let templateData = {
              emailTemplateType: "Sunfish Deployment",
              Applicant_Id: hiringManagerDetails.userDefinedEmpId,
              Candidate_Name: candidateName,
              Date_Of_Join: doj,
              Job_Post_Name: item.designationName,
              Recruiter_Name: employee,
              Group_Name: hiringManagerDetails.groupName
                ? hiringManagerDetails.groupName
                : "",
              Recruiter_Id: this.loginEmployeeDetails.employeeId,
              Org_Code: hiringManagerDetails.Organization_Group_Code
                ? hiringManagerDetails.Organization_Group_Code
                : "",
              Org_Name: hiringManagerDetails.Organization_Unit_Name
                ? hiringManagerDetails.Organization_Unit_Name
                : "",
              Division_Name: hiringManagerDetails.divisionName
                ? hiringManagerDetails.divisionName
                : "",
              First_Line_Manager: hiringManagerDetails.Manager_Name,
              Second_Line_Manager:
                hiringManagerDetails.directManagerDetails &&
                hiringManagerDetails.directManagerDetails.Manager_Name
                  ? hiringManagerDetails.directManagerDetails.Manager_Name
                  : "",
              Department_Name: hiringManagerDetails.deptName
                ? hiringManagerDetails.deptName
                : "",
              Branch_Name: hiringManagerDetails.sectionName
                ? hiringManagerDetails.sectionName
                : "",
              Branch_Code: hiringManagerDetails.sectionCode
                ? hiringManagerDetails.sectionCode
                : "",
            };
            vm.recruitersEmailList = [];
            if (item.createdByEmail) {
              vm.ccEmailReciever.push(item.createdByEmail);
            }
            if (item.recruiterEmail) {
              let emails = item.recruiterEmail.split(",");
              vm.ccEmailReciever = vm.ccEmailReciever.concat(emails);
            }
            if (hiringManagerDetails.Manager_Email) {
              vm.recruitersEmailList.push(hiringManagerDetails.Manager_Email);
            }
            if (
              hiringManagerDetails.directManagerDetails &&
              hiringManagerDetails.directManagerDetails.Manager_Email
            ) {
              vm.ccEmailReciever.push(
                hiringManagerDetails.directManagerDetails.Manager_Email
              );
            }
            if (vm.adminEmails && vm.adminEmails.length > 0) {
              vm.ccEmailReciever = [...vm.ccEmailReciever, ...vm.adminEmails];
            }
            this.templateData = templateData;
            this.templateType = "onboardingHiringTeamNotification";
            this.typeOfSchedule = "newHireNotification";
            this.toCCExchange = true;
            this.showCustomEmail = true;
          } else {
            vm.hiringManagerDetails = {};
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleStatusUpdateError(err);
        });
    },

    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "onboarded individuals",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.updateCount = 0;
      this.showViewEditForm = false;
      this.selectedCandidateId = 0;
      this.selectedCandidateDetails = {};
      this.originalList = [];
      this.itemList = [];
      this.fetchList();
    },
    checkFieldAvailability(value) {
      if (value) {
        let strValue = value.toString();
        return strValue.trim().length > 0;
      } else return false;
    },
    onChangeFields(field = "") {
      this.isFormDirty = true;
      this.alreadyExistErrMsg[field] = true;
    },
    getCoverageSetting() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMPLOYEE_NUMBER_SERIES,
          variables: { formId: 178 },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listEmployeeIdPrefixSettings) {
            vm.idGenerationconfig =
              response.data.listEmployeeIdPrefixSettings.config?.isEnabled ||
              false;
            vm.idGenerationCoverage =
              response.data.listEmployeeIdPrefixSettings.config?.configLevel ||
              "";
            vm.settingsArray =
              response.data.listEmployeeIdPrefixSettings.employeeIdPrefixSettings;
          } else {
            vm.idGenerationconfig = false;
            vm.idGenerationCoverage = "";
            vm.settingsArray = [];
          }
          vm.isLoading = false;
          this.onConfirmMigrate();
        })
        .catch(() => {
          vm.idGenerationconfig = false;
          vm.idGenerationCoverage = "";
          vm.settingsArray = [];
          vm.isLoading = false;
          this.onConfirmMigrate();
        });
    },
    onConfirmMigrate() {
      this.openConversionConfirmModal = false;
      this.openConversionModal = true;
      this.retrieveDropdownDetails();
      if (this.idGenerationconfig) {
        let serviceProviderId = null;
        if (this.idGenerationCoverage?.toLowerCase() === "serviceprovider") {
          serviceProviderId = this.selectedCandidate.serviceProviderId;
        }
        this.getEmployeeId(serviceProviderId);
      } else {
        this.retrieveEmpMaxId();
      }
      this.selectedCareerPIC = null;
      this.selectedTimePIC = null;
      this.enableEmployeeId = false;
    },
    getEmployeeId(serviceProviderId = null) {
      if (
        this.idGenerationCoverage?.toLowerCase() === "serviceprovider" &&
        !serviceProviderId
      ) {
        this.showAlert({
          isOpen: true,
          message: "The candidate is not associated with any service provider.",
          type: "warning",
        });
        return;
      }
      let vm = this;
      vm.fetchingMaxEmpId = true;
      vm.$apollo
        .query({
          query: GET_MAX_EMPLOYEE_ID,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            serviceProviderId: serviceProviderId,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveMaxEmployeeId &&
            !response.data.retrieveMaxEmployeeId.errorCode
          ) {
            const { maxEmployeeId } = response.data.retrieveMaxEmployeeId;
            vm.migrationDetails["User_Defined_EmpId"] = maxEmployeeId
              ? maxEmployeeId.toString()
              : "";
            vm.validateFieldAlreadyExist("User_Defined_EmpId", "Employee Id");
          } else {
            vm.migrationDetails["User_Defined_EmpId"] = "";
            vm.showAlert({
              isOpen: true,
              message:
                response.data?.retrieveMaxEmployeeId?.message ||
                "Something went wrong while retrieving the employee id. Please try after some time.",
              type: "warning",
            });
          }
          vm.fetchingMaxEmpId = false;
        })
        .catch((err) => {
          vm.fetchingMaxEmpId = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "employee id",
            isListError: false,
          });
        });
    },
    validateEmpMail() {
      this.openStatusConfirmationModal = false;
      if (
        (this.selectedAction === "Return" ||
          this.selectedAction === "Reject" ||
          this.selectedAction === "Candidate Withdrawn") &&
        this.selectedActionItem &&
        this.selectedActionItem.personalEmail
      ) {
        this.showCustomEmail = true;
        this.toCCExchange = false;
      } else {
        this.updateCandidateStatus();
      }
    },
    onSendCustomEmail(templateId) {
      this.showCustomEmail = false;
      this.emailReciever = [];
      this.ccEmailReciever = [];
      if (
        !this.enableSunfishDeployment &&
        this.templateType !== "onboardingHiringTeamNotification"
      ) {
        this.updateCandidateStatus(templateId);
      } else if (this.templateType === "onboardingHiringTeamNotification") {
        this.recruitersEmailList = [];
        var snackbarData = {
          isOpen: true,
          type: "success",
          message: "Notification sent successfully",
        };
        this.showAlert(snackbarData);
        this.selectedActionItem = null;
      } else {
        this.enableSunfishDeployment = false;
        this.refetchList();
        snackbarData = {
          isOpen: true,
          type: "success",
          message:
            "The deployment notification email has been sent successfully.",
        };
        this.showAlert(snackbarData);
        this.selectedActionItem = null;
      }
    },
    updateCandidateStatus(templateId = null) {
      let vm = this;
      vm.openStatusConfirmationModal = false;
      vm.isLoading = true;
      let formId;
      if (
        vm.selectedAction.toLowerCase() === "verify" ||
        vm.selectedAction.toLowerCase() === "return" ||
        vm.selectedAction.toLowerCase() === "unverify"
      ) {
        formId = 274;
      } else if (vm.selectedAction.toLowerCase() === "reject") {
        formId = 279;
      } else if (vm.selectedAction.toLowerCase() === "candidate withdrawn") {
        formId = 302;
      } else {
        formId = 178;
      }
      let status = "";
      if (vm.selectedAction === "Reject") {
        status = "Rejected";
      } else if (vm.selectedAction === "Return") {
        status = "Returned";
      } else if (vm.selectedAction == "Candidate Withdrawn") {
        status = "Withdrawn";
      } else if (vm.selectedAction == "Unverify") {
        status = "Unverified";
      } else {
        status = "Verified";
      }
      vm.$apollo
        .mutate({
          mutation: UPDATE_CANDIDATE_STATUS,
          variables: {
            candidateId: vm.selectedActionItem.Candidate_Id,
            formId: formId,
            status: status,
            templateId:
              status?.toLowerCase() !== "verified" ? templateId : null,
          },
          client: "apolloClientW",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Candidate status updated successfully",
          };
          vm.showAlert(snackbarData);
          // if (
          //   vm.selectedAction === "Return" &&
          //   vm.selectedActionItem.empEmail
          // ) {
          //   vm.showCustomEmail = true;
          // }
          vm.refetchList();
        })
        .catch((err) => {
          vm.handleStatusUpdateError(err);
        });
    },

    handleStatusUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "candidate status",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    async validateEditForm() {
      let isFormValid = await this.$refs.migrationForm.validate();
      if (isFormValid && isFormValid.valid) {
        this.migrateToEmployee();
      }
    },
    migrateToEmployee() {
      let vm = this;
      vm.openConversionModal = false;
      vm.isLoading = true;
      let settingId = null;
      if (this.idGenerationconfig) {
        if (this.idGenerationCoverage?.toLowerCase() === "serviceprovider") {
          settingId = this.settingsArray.find((item) => {
            return (
              item.serviceProviderId ===
              this.selectedCandidate.serviceProviderId
            );
          })?.empPrefixSettingId;
        } else {
          settingId = this.settingsArray.find((item) => {
            return item.serviceProviderId === null;
          })?.empPrefixSettingId;
        }
      }
      vm.$apollo
        .mutate({
          mutation: CONVERT_CANDIDATE_TO_EMPLOYEE,
          variables: {
            Candidate_Id: vm.selectedActionItem.Candidate_Id,
            User_Defined_EmpId: vm.migrationDetails.User_Defined_EmpId,
            External_EmpId: vm.migrationDetails.External_EmpId,
            Career_Id: vm.selectedCareerPIC ? vm.selectedCareerPIC : 0,
            Timekeeping_Id: vm.selectedTimePIC ? vm.selectedTimePIC : 0,
            Roles_Id: vm.selectedRole ? vm.selectedRole : null,
            empPrefixSettingId: settingId,
            Emp_Email: vm.migrationDetails.workEmail,
          },
          client: "apolloClientW",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Candidate onboarded successfully",
          };
          if (
            this.sunFishVisible &&
            this.sunFishVisible !== "" &&
            this.sunFishVisible === "Active"
          ) {
            vm.triggerSync(true);
          }
          vm.showAlert(snackbarData);
          vm.refetchList();
        })
        .catch((err) => {
          vm.handleMigrationError(err);
        });
    },
    handleMigrationError(err = "") {
      let errorCode = getErrorCodesAndMessagesWithValidation(err);
      if (errorCode && errorCode.length) {
        errorCode = errorCode[0];
      }
      this.isLoading = false;
      this.refetchList();
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "candidate status",
          isListError: false,
        })
        .then((validationErrors) => {
          if (errorCode === "IO0135" && validationErrors.length) {
            //Form the messages
            let backupValidations = validationErrors;
            validationErrors = {};
            for (let error of backupValidations) {
              if (error === "ESS0138") {
                validationErrors["ESS0138"] =
                  "ESS0138 - Employee ID is already assigned to an active employee. Please confirm and update the active employee's Employee ID to convert the candidate to an employee.";
              } else if (error === "ESS0140") {
                validationErrors["ESS0140"] =
                  "ESS0140 - Employee email is already registered for an active employee. Please confirm and update the active employee's email to convert the candidate to an employee.";
              } else if (error === "ESS0141") {
                validationErrors["ESS0141"] =
                  "ESS0141 - Biometric Integration ID is already assigned to an active employee. Please confirm and update the active employee's Biometric Integration ID to convert the candidate to an employee.";
              } else if (error === "ESS0136") {
                validationErrors["ESS0136"] =
                  "ESS0136 - Bank account number is already linked to an active employee. Please confirm and update the active employee's account number and convert the candidate to an employee.";
              } else if (error === "ESS0139") {
                validationErrors["ESS0139"] =
                  "ESS0139 - Mobile number is already linked to an active employee. Please confirm and update the active employee's mobile number to convert the candidate to an employee.";
              } else if (error === "ESS0146") {
                validationErrors["ESS0146"] =
                  "ESS0146 - UAN is already linked to an active employee. Please confirm and update the active employee's UAN to convert the candidate to an employee.";
              } else if (error === "ESS0142") {
                validationErrors["ESS0142"] =
                  "ESS0142 - PAN is already linked to an active employee. Please confirm and update the active employee's PAN to convert the candidate to an employee.";
              } else if (error === "ESS0143") {
                validationErrors["ESS0143"] =
                  "ESS0143 - National Identity Number (Aadhar/Social Security) is already linked to an active employee. Please confirm and update the active employee's National Identity Number (Aadhar/Social Security) to convert the candidate to an employee.";
              } else if (error === "ESS0144") {
                validationErrors["ESS0144"] =
                  "ESS0144 - PF Number is already linked to an active employee. Please confirm and update the active employee's PF Number to convert the candidate to an employee.";
              }
            }
          }
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          if (this.validationMessages && this.validationMessages.length) {
            this.showValidationAlert = true;
          }
        });
    },
    retrieveEmpMaxId() {
      let vm = this;
      if (!vm.migrationDetails["User_Defined_EmpId"]) {
        vm.fetchingMaxEmpId = true;
        vm.$apollo
          .query({
            query: RETRIEVE_MAX_EMP_ID,
            client: "apolloClientAC",
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveMaxEmployeeId &&
              !response.data.retrieveMaxEmployeeId.errorCode
            ) {
              const { maxEmployeeId } = response.data.retrieveMaxEmployeeId;
              vm.migrationDetails["User_Defined_EmpId"] = maxEmployeeId
                ? (parseInt(maxEmployeeId) + 1).toString()
                : "";
              vm.validateFieldAlreadyExist("User_Defined_EmpId", "Employee Id");
            }
            vm.fetchingMaxEmpId = false;
          })
          .catch(() => {
            vm.fetchingMaxEmpId = false;
          });
      }
    },
    validateFieldAlreadyExist(field, label, table = "emp_job") {
      let vm = this;
      if (vm.migrationDetails[field]) {
        vm.isValidatingAlreadyExist = true;
        vm.$apollo
          .query({
            query: VALIDATE_FIELD_AVAILABILITY,
            client: "apolloClientAC",
            variables: {
              employeeId: vm.migrationDetails.Employee_Id
                ? vm.migrationDetails.Employee_Id
                : 0,
              columnValue: vm.migrationDetails[field],
              columnName: field,
              tableName: table,
            },
            fetchPolicy: "no-cache",
          })
          .then((response) => {
            if (
              response.data &&
              response.data.validateCommonAvailability &&
              !response.data.validateCommonAvailability.errorCode
            ) {
              const { isAvailable } = response.data.validateCommonAvailability;
              if (!isAvailable) {
                vm.alreadyExistErrMsg[field] = label + " already exist";
                vm.$refs.migrationForm.validate();
              } else {
                vm.alreadyExistErrMsg[field] = true;
              }
            }
            vm.isValidatingAlreadyExist = false;
          })
          .catch((err) => {
            vm.alreadyExistErrMsg[field] = true;
            vm.$refs.migrationForm.validate();
            vm.isValidatingAlreadyExist = false;
            let fieldLabel = label.toLowerCase();
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "validating",
              form: fieldLabel,
              isListError: false,
            });
          });
      }
    },
    onChangeSunfish(item) {
      this.showIntegratedPopup = true;
      this.selectedCandidate = {
        candidateId: item.Candidate_Id,
      };
      let preparedData = {};
      const tempAPI = this.apiStatus;
      if (this.deploymentType?.toLowerCase() === "sunfish") {
        Object.keys(this.apiStatus).forEach((list) => {
          switch (list) {
            case "sfEmployeeInfoStatus":
              preparedData["sfEmployeeInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Employee Data",
                reason: item.sfEmployeeInfoFailureReason,
                status: item.sfEmployeeInfoStatus,
              };
              break;
            case "sfPersonalInfoStatus":
              preparedData["sfPersonalInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Personal Data",
                reason: item.sfPersonalInfoFailureReason,
                status: item.sfPersonalInfoStatus,
              };
              break;
            case "sfAdditionalInfoStatus":
              preparedData["sfAdditionalInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Additional Data",
                reason: item.sfAdditionalInfoFailureReason,
                status: item.sfAdditionalInfoStatus,
              };
              break;
            case "sfEducationInfoStatus":
              preparedData["sfEducationInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Education Data",
                reason: item.sfEducationInfoFailureReason,
                status: item.sfEducationInfoStatus,
              };
              break;
            case "sfAttachmentInfoStatus":
              preparedData["sfAttachmentInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Attachment Data",
                reason: item.sfAttachmentInfoFailureReason,
                status: item.sfAttachmentInfoStatus,
              };
              break;
            case "sfPreviousRehireData":
              preparedData["sfPreviousRehireData"] = item.Data_Sync_Result
                ?.IsRehire
                ? {
                    id: tempAPI[list]?.id,
                    name: "Rehire Data",
                    reason:
                      item.Data_Sync_Result?.Rehire?.Sync_Status?.toLowerCase() ===
                      "failed"
                        ? item.Data_Sync_Result?.Rehire?.Failure_Reason || ""
                        : "",
                    status: item.Data_Sync_Result?.Rehire?.Sync_Status || null,
                  }
                : {};
              break;
            default:
              break;
          }
        });
      } else if (this.deploymentType?.toLowerCase() === "pagt nexus hrms") {
        Object.keys(this.apiStatus).forEach((list) => {
          switch (list) {
            case "sfPersonalInfoStatus":
              preparedData["sfPersonalInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Personal Data",
                reason: item.Data_Sync_Result?.Personal?.Failure_Reason || "",
                status: item.Data_Sync_Result?.Personal?.Sync_Status || null,
              };
              break;
            case "sfAdditionalInfoStatus":
              preparedData["sfAdditionalInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Additional Data",
                reason: item.Data_Sync_Result?.Additional?.Failure_Reason || "",
                status: item.Data_Sync_Result?.Additional?.Sync_Status || null,
              };
              break;
            case "sfEducationInfoStatus":
              preparedData["sfEducationInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Education Data",
                reason: item.Data_Sync_Result?.Education?.Failure_Reason || "",
                status: item.Data_Sync_Result?.Education?.Sync_Status || null,
              };
              break;
            case "sfAttachmentInfoStatus":
              preparedData["sfAttachmentInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Attachment Data",
                reason: item.Data_Sync_Result?.Attachment?.Failure_Reason || "",
                status: item.Data_Sync_Result?.Attachment?.Sync_Status || null,
              };
              break;
            case "sfAccreditationInfoStatus":
              preparedData["sfAccreditationInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Accreditations Data",
                reason:
                  item.Data_Sync_Result?.Accreditations?.Failure_Reason || "",
                status:
                  item.Data_Sync_Result?.Accreditations?.Sync_Status || null,
              };
              break;
            case "sfCertificateInfoStatus":
              preparedData["sfCertificateInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Certificate Data",
                reason:
                  item.Data_Sync_Result?.Certificate?.Failure_Reason || "",
                status: item.Data_Sync_Result?.Certificate?.Sync_Status || null,
              };
              break;
            case "sfLanguageInfoStatus":
              preparedData["sfLanguageInfoStatus"] = {
                id: tempAPI[list]?.id,
                name: "Language Data",
                reason: item.Data_Sync_Result?.Language?.Failure_Reason || "",
                status: item.Data_Sync_Result?.Language?.Sync_Status || null,
              };
              break;
            case "sfPreviousEmployementData":
              preparedData["sfPreviousEmployementData"] = {
                id: tempAPI[list]?.id,
                name: "Previous Employement Data",
                reason:
                  item.Data_Sync_Result?.PreviousEmployment?.Failure_Reason ||
                  "",
                status:
                  item.Data_Sync_Result?.PreviousEmployment?.Sync_Status ||
                  null,
              };
              break;
            default:
              break;
          }
        });
      }
      this.apiStatus = preparedData;
    },
    getSunfishDetails() {
      let vm = this;
      vm.enableSunfishLoader = true;
      vm.$apollo
        .query({
          query: GET_SUN_FISH_DETAILS,
          client: "apolloClientAS",
          variables: {
            integrationType: "",
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res && res.data && res.data.retrieveOnboardingSettings) {
            const result = res.data.retrieveOnboardingSettings;
            vm.enableSunfishLoader = false;
            vm.deploymentType = result.Integration_Type;
            if (result.status) {
              vm.sunFishVisible = result.status;
            }
            if (result.Allow_Deployment_Notification_OnFailure) {
              vm.sunFishAllowFailureNotification =
                result.Allow_Deployment_Notification_OnFailure.toLowerCase() ===
                "yes"
                  ? true
                  : false;
            }
          } else {
            vm.enableSunfishLoader = false;
            this.sunFishVisible = "";
            vm.sunFishAllowFailureNotification = false;
          }
        })
        .catch((err) => {
          vm.enableSunfishLoader = false;
          vm.sunFishAllowFailureNotification = false;
          vm.handleStatusUpdateError(err);
        });
    },
    triggerSync(syncFlag) {
      this.syncLoader = true;
      let vm = this;
      vm.$apollo
        .query({
          query: TRIGGER_SYNC_DETAILS,
          client: "apolloClientAH",
          variables: {
            candidateId: this.selectedCandidate.candidateId,
            integrationType: this.deploymentType,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.syncLoader = false;
          if (res && res.data) {
            this.fetchList();
            this.showIntegratedPopup = false;
          } else if (syncFlag) {
            this.fetchList();
          }
        })
        .catch(() => {
          if (syncFlag) {
            this.fetchList();
          }
          this.syncLoader = false;
        });
    },

    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.itemList));
      exportData = exportData.map((el) => ({
        ...el,
        dobFormat: el.dob ? this.formatDate(el.dob) : "",
        dojFormat: el.doj ? this.formatDate(el.doj) : "",
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: "Onboarding Individual",
        sheetName: "Onboarding Individual",
        header: [
          { key: "candidateName", header: "Candidate Name" },
          { key: "dobFormat", header: "Date of Birth" },
          { key: "dojFormat", header: "Date of Join" },
          { key: "designationName", header: "Designation" },
          { key: "departmentName", header: "Department" },
          { key: "locationName", header: "Location" },
          { key: "employeeType", header: "Employee Type" },
          { key: "workSchedule", header: "Work Schedule" },
          { key: "isManager", header: "Manager" },
          { key: "managerName", header: "Manager Name" },
          { key: "candidateStatus", header: "Status" },
        ],
      };
      this.exportExcelFile(exportOptions);
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.onboarded-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .onboarded-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
