<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >{{ labelList[230]?.Field_Alias || "Accreditation" }} Details</span
        >
        <v-spacer></v-spacer>
        <v-icon
          color="primary"
          size="25"
          @click="$emit('close-accreditations-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="primary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditAccreditationsForm">
          <v-row>
            <v-col
              cols="12"
              md="6"
              v-if="labelList[230].Field_Visiblity === 'Yes'"
            >
              <CustomSelect
                :items="accreditationCategoryAndTypeList"
                :label="labelList[230].Field_Alias"
                :itemSelected="
                  editedAccreditationDetails.Accreditation_Category_And_Type_Id
                "
                :rules="[
                  labelList[230].Mandatory_Field === 'Yes'
                    ? required(
                        labelList[230].Field_Alias,
                        editedAccreditationDetails.Accreditation_Category_And_Type_Id
                      )
                    : true,
                ]"
                itemValue="Accreditation_Category_And_Type_Id"
                itemTitle="Accreditation_Category"
                subText="Accreditation_Type"
                :isRequired="labelList[230].Mandatory_Field === 'Yes'"
                :isAutoComplete="true"
                :isLoading="accreditationCategoryAndTypeListLoading"
                :noDataText="
                  accreditationCategoryAndTypeListLoading
                    ? 'Loading...'
                    : 'No data available'
                "
                @selected-item="
                  onChangeCustomSelectField(
                    $event,
                    'Accreditation_Category_And_Type_Id'
                  )
                "
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[386]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="6"
            >
              <v-text-field
                v-model="editedAccreditationDetails.Identifier"
                :rules="[
                  labelList[386].Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(
                        labelList[386].Field_Alias,
                        editedAccreditationDetails.Identifier
                      )
                    : true,
                  ,
                  validateWithRulesAndReturnMessages(
                    editedAccreditationDetails.Identifier,
                    'identifier',
                    labelList[386].Field_Alias
                  ),
                ]"
                clearable
                variant="solo"
                @update:model-value="onChangeFields()"
              >
                <template v-slot:label>
                  {{ labelList[386].Field_Alias
                  }}<span
                    v-if="
                      labelList[386].Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <v-col
              v-if="labelList[246]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="6"
            >
              <v-menu
                v-model="receivedDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="receivedDate"
                    v-model="formattedReceivedDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[
                      labelList[246].Mandatory_Field?.toLowerCase() === 'yes'
                        ? required(
                            labelList[246].Field_Alias,
                            formattedReceivedDate
                          )
                        : true,
                    ]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      {{ labelList[246].Field_Alias }}
                      <span
                        v-if="
                          labelList[246].Mandatory_Field?.toLowerCase() ===
                          'yes'
                        "
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="editedAccreditationDetails.Received_Date"
                  :min="selectedEmpDobDate"
                  :max="currentDate"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="6">
              <v-menu
                v-model="expiryDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="expiryDate"
                    v-model="formattedExpiryDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Expiry Date', formattedExpiryDate)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Expiry Date<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="editedAccreditationDetails.Expiry_Date"
                  :min="accreditationExpiryDateMin"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col
              v-if="labelList[387]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="6"
            >
              <v-text-field
                v-model="editedAccreditationDetails.Exam_Rating"
                :rules="[
                  labelList[387].Mandatory_Field?.toLowerCase() === 'yes' &&
                  editedAccreditationDetails.Exam_Rating !== 0
                    ? required(
                        labelList[387].Field_Alias,
                        editedAccreditationDetails.Exam_Rating
                      )
                    : true,
                  minMaxNumberValidation(
                    labelList[387].Field_Alias,
                    editedAccreditationDetails.Exam_Rating,
                    0,
                    100
                  ),
                ]"
                min="0"
                ref="examRating"
                @update:modelValue="onChangeFields()"
                variant="solo"
                type="number"
                ><template v-slot:label>
                  <span>{{ labelList[387].Field_Alias }}</span>
                  <span
                    v-if="
                      labelList[387].Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col
              v-if="labelList[389]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                v-model="editedAccreditationDetails.Exam_Date_Month"
                :items="monthsList"
                :label="labelList[389].Field_Alias"
                :itemSelected="editedAccreditationDetails.Exam_Date_Month"
                :rules="[
                  labelList[389].Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(
                        `${labelList[389].Field_Alias}`,
                        editedAccreditationDetails.Exam_Date_Month
                      )
                    : true,
                ]"
                clearable
                ref="examDateMonth"
                :isRequired="
                  labelList[389].Mandatory_Field?.toLowerCase() === 'yes'
                "
                @selected-item="onChangeFields($event, 'Exam_Date_Month')"
              />
            </v-col>
            <v-col
              v-if="labelList[388]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="yearList"
                v-model="editedAccreditationDetails.Exam_Date_Year"
                :label="labelList[388].Field_Alias"
                :itemSelected="editedAccreditationDetails.Exam_Date_Year"
                :rules="[
                  labelList[388].Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(
                        `${labelList[388].Field_Alias}`,
                        editedAccreditationDetails.Exam_Date_Year
                      )
                    : true,
                ]"
                clearable
                ref="examDateYear"
                :isRequired="
                  labelList[388].Mandatory_Field?.toLowerCase() === 'yes'
                "
                @selected-item="onChangeFields($event, 'Exam_Date_Year')"
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-file-input
                prepend-icon=""
                clearable
                :model-value="fileContent"
                append-inner-icon="fas fa-paperclip"
                variant="solo"
                :rules="[required('Document', fileContentRuleValue)]"
                accept="image/png, image/jpeg, image/jpg, application/pdf"
                @update:modelValue="onChangeFiles"
                @click:clear="removeFiles"
              >
                <template v-slot:label>
                  Document<span style="color: red">*</span>
                </template>
              </v-file-input>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="$emit('close-accreditations-form')"
                  class="ma-2 pa-2"
                  color="primary"
                  rounded="lg"
                  variant="text"
                  elevation="4"
                  >Cancel</v-btn
                >
                <v-btn
                  color="primary"
                  rounded="lg"
                  class="ma-2 pa-2"
                  :disabled="!isFormDirty"
                  @click="validateAccreditationsDetails"
                  >Save</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import validationRules from "@/mixins/validationRules.js";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { ADD_UPDATE_ACCREDITATION_DETAILS } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import { LIST_ACCREDITATIONS_AND_TYPES } from "@/graphql/dropDownQueries";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AccreditationForm",
  mixins: [validationRules],
  components: {
    CustomSelect,
  },
  props: {
    selectedAccreditationDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
  },
  emits: ["refetch-doc-accreditation-details", "close-accreditations-form"],
  data: () => ({
    editedAccreditationDetails: {
      Accreditation_Category_And_Type_Id: "",
      Identifier: "",
      File_Name: null,
      Received_Date: null,
      Expiry_Date: null,
    },
    accreditationCategoryAndTypeList: [],
    accreditationCategoryAndTypeListLoading: false,
    //Date-picker
    formattedReceivedDate: "",
    formattedExpiryDate: "",
    receivedDateMenu: false,
    expiryDateMenu: false,
    // edit
    isFileChanged: false,
    isFormDirty: false,
    isLoading: false,
    validationMessages: [],
    showValidationAlert: false,
    formType: "",
    // file
    fileContent: null,
  }),

  computed: {
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    monthsList() {
      return moment.months();
    },
    yearList() {
      let empDob = this.selectedCandidateDOB;
      let year = 0;
      if (empDob) {
        year = new Date().getFullYear() - new Date(empDob).getFullYear();
      } else {
        year = 80;
      }
      const now = new Date().getUTCFullYear();
      const years = Array(now - (now - year))
        .fill("")
        .map((v, idx) => now - idx);
      return years;
    },
    accreditationExpiryDateMin() {
      if (
        this.editedAccreditationDetails.Received_Date &&
        this.editedAccreditationDetails.Received_Date !== "0000-00-00"
      ) {
        return moment(this.editedAccreditationDetails.Received_Date).format(
          "YYYY-MM-DD"
        );
      }
      return null;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    domainName() {
      return this.$store.getters.domain;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    fileContentRuleValue() {
      return this.fileContent && this.fileContent.name
        ? this.fileContent.name
        : null;
    },
    selectedEmpDobDate() {
      if (
        this.selectedCandidateDOB &&
        this.selectedCandidateDOB !== "0000-00-00"
      ) {
        return moment(this.selectedCandidateDOB).format("YYYY-MM-DD");
      } else return null;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedAccreditationDetails &&
      Object.keys(this.selectedAccreditationDetails).length > 0
    ) {
      this.editedAccreditationDetails = JSON.parse(
        JSON.stringify(this.selectedAccreditationDetails)
      );
      if (this.editedAccreditationDetails.Received_Date) {
        this.formattedReceivedDate = this.formatDate(
          this.editedAccreditationDetails?.Received_Date
        );
        this.editedAccreditationDetails.Received_Date = this
          .editedAccreditationDetails.Received_Date
          ? new Date(this.editedAccreditationDetails.Received_Date)
          : null;
      }
      if (this.editedAccreditationDetails.Expiry_Date) {
        this.formattedExpiryDate = this.formatDate(
          this.editedAccreditationDetails?.Expiry_Date
        );
        this.editedAccreditationDetails.Expiry_Date = this
          .editedAccreditationDetails.Expiry_Date
          ? new Date(this.editedAccreditationDetails.Expiry_Date)
          : null;
      }
      if (this.editedAccreditationDetails["File_Name"]) {
        this.fileContent = {
          name: this.formattedFileName(
            this.editedAccreditationDetails["File_Name"]
          ),
        };
      } else {
        this.fileContent = null;
      }

      this.formType = "edit";
    } else {
      this.formType = "add";
    }
    this.retrieveAccreditationCategoryAndType();
  },

  watch: {
    "editedAccreditationDetails.Received_Date": function (val) {
      if (val) {
        this.receivedDateMenu = false;
        this.formattedReceivedDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "editedAccreditationDetails.Expiry_Date": function (val) {
      if (val) {
        this.expiryDateMenu = false;
        this.formattedExpiryDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },

  methods: {
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File Name";
      }
      return "";
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.editedAccreditationDetails[field] = value;
    },
    onChangeFiles(value) {
      this.fileContent = value;
      if (this.fileContent && this.fileContent.name) {
        mixpanel.track("Onboarded-candidate-accreditation-file-changed");
        this.editedAccreditationDetails["File_Name"] =
          this.selectedCandidateId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent.name;
        this.isFileChanged = true;
        this.onChangeFields();
      }
    },
    removeFiles() {
      mixpanel.track("Onboarded-candidate-accreditation-file-removed");
      this.editedAccreditationDetails["File_Name"] = "";
      this.editedAccreditationDetails["File_Size"] = "";
      this.fileContent = null;
      this.onChangeFields();
    },
    async validateAccreditationsDetails() {
      const { valid } = await this.$refs.addEditAccreditationsForm.validate();
      mixpanel.track("Onboarded-candidate-accreditation-submit-clicked");
      if (valid) {
        this.validateDocuments();
      }
    },

    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        if (this.fileContent && this.fileContent.size && this.isFileChanged) {
          await this.uploadFileContents(this.fileContent);
        }
        this.updateAccreditationDetails();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },
    updateAccreditationDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_ACCREDITATION_DETAILS,
          variables: {
            candidateId: vm.selectedCandidateId,
            accreditationDetailId:
              vm.editedAccreditationDetails.Accreditation_Detail_Id,
            accreditationCategoryAndType:
              vm.editedAccreditationDetails.Accreditation_Category_And_Type_Id,
            receivedDate: moment(
              vm.editedAccreditationDetails.Received_Date
            ).isValid()
              ? moment(vm.editedAccreditationDetails.Received_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            expiryDate: moment(
              vm.editedAccreditationDetails.Expiry_Date
            ).isValid()
              ? moment(vm.editedAccreditationDetails.Expiry_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            identifier: vm.editedAccreditationDetails.Identifier,
            fileName: vm.editedAccreditationDetails["File_Name"],
            examRating: parseInt(vm.editedAccreditationDetails.Exam_Rating),
            examDateYear: parseInt(
              vm.editedAccreditationDetails.Exam_Date_Year
            ),
            examDateMonth: vm.editedAccreditationDetails.Exam_Date_Month,
            dependentId: vm.selectedAccreditationDetails.Dependent_Id,
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track("Onboarded-candidate-accreditation-update-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Accreditation details updated successfully"
                : "Accreditation details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-doc-accreditation-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-accreditation-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "accreditation details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    async uploadFileContents() {
      mixpanel.track("Onboarded-candidate-accreditation-file-upload-start");
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employee Accreditation/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName:
            fileUploadUrl + this.editedAccreditationDetails["File_Name"],
          action: "upload",
          type: "accreditation",
          fileContent: vm.fileContent,
        })
        .catch((error) => {
          throw error;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveAccreditationCategoryAndType() {
      let vm = this;
      vm.accreditationCategoryAndTypeListLoading = true;
      vm.$apollo
        .query({
          query: LIST_ACCREDITATIONS_AND_TYPES,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveAccreditationCategoryAndType &&
            !response.data.retrieveAccreditationCategoryAndType.errorCode
          ) {
            const { accreditationCategoryAndType } =
              response.data.retrieveAccreditationCategoryAndType;
            vm.accreditationCategoryAndTypeList =
              accreditationCategoryAndType &&
              accreditationCategoryAndType.length > 0
                ? accreditationCategoryAndType
                : [];
          }
          vm.accreditationCategoryAndTypeListLoading = false;
        })
        .catch(() => {
          vm.accreditationCategoryAndTypeListLoading = false;
        });
    },
  },
};
</script>

<style scoped>
.custom-label-color {
  color: #2c2c2c !important;
}
</style>
