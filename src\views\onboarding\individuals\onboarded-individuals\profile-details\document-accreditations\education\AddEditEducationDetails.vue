<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Education Details</span
        >
        <v-spacer></v-spacer>
        <v-icon color="primary" size="25" @click="$emit('close-education-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="primary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditEducationForm">
          <v-row>
            <!-- School/Diploma/Degree - 128 -->
            <v-col
              v-if="labelList[128]?.Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="courseList"
                :label="labelList[128].Field_Alias || 'School/Diploma/Degree'"
                :itemSelected="
                  educationFormData.Education_Type
                    ? parseInt(educationFormData.Education_Type)
                    : null
                "
                :rules="[
                  labelList[128].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[128].Field_Alias || 'School/Diploma/Degree',
                        educationFormData.Education_Type
                      )
                    : true,
                ]"
                itemValue="Course_Id"
                itemTitle="Course_Name"
                :isRequired="
                  labelList[128].Mandatory_Field?.toLowerCase() == 'yes'
                "
                @selected-item="
                  onChangeCustomSelectField($event, 'Education_Type')
                "
                :isAutoComplete="true"
                :isLoading="courseListFetching"
                :noDataText="
                  courseListFetching ? 'Loading...' : 'No data available'
                "
              ></CustomSelect>
            </v-col>
            <!-- Specialization - 129 -->
            <v-col
              v-if="labelList[129]?.Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                v-if="labelList[129].Predefined?.toLowerCase() == 'yes'"
                :items="specializationList"
                :label="labelList[129].Field_Alias"
                :itemSelected="
                  educationFormData.Specialization_Id
                    ? parseInt(educationFormData.Specialization_Id)
                    : null
                "
                itemValue="Specialization_Id"
                itemTitle="Specialization"
                @selected-item="
                  onChangeCustomSelectField($event, 'Specialization_Id')
                "
                :isAutoComplete="true"
                :isLoading="splInstituteLoading"
                :noDataText="
                  splInstituteLoading ? 'Loading...' : 'No data available'
                "
                :rules="[
                  labelList[129].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[129].Field_Alias,
                        educationFormData.Specialization_Id
                      )
                    : true,
                ]"
                :isRequired="
                  labelList[129].Mandatory_Field?.toLowerCase() == 'yes'
                "
              ></CustomSelect>
              <v-text-field
                v-else
                v-model="educationFormData.Specialisation"
                :rules="[
                  educationFormData.Specialisation
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.Specialisation,
                        'specialisation',
                        labelList[129].Field_Alias || 'Specialization'
                      )
                    : true,
                  labelList[129].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[129].Field_Alias,
                        educationFormData.Specialisation
                      )
                    : true,
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[129].Field_Alias }}
                  <span
                    v-if="
                      labelList[129].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <!-- Institute - 130 -->
            <v-col
              v-if="labelList[130]?.Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                v-if="labelList[130].Predefined?.toLowerCase() == 'yes'"
                :items="instituteList"
                :label="labelList[130].Field_Alias"
                :itemSelected="
                  educationFormData.Institution_Id
                    ? parseInt(educationFormData.Institution_Id)
                    : null
                "
                itemValue="Institution_Id"
                itemTitle="Institution"
                @selected-item="
                  onChangeCustomSelectField($event, 'Institution_Id')
                "
                :isAutoComplete="true"
                :isLoading="splInstituteLoading"
                :noDataText="
                  splInstituteLoading ? 'Loading...' : 'No data available'
                "
                :rules="[
                  labelList[130].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[130].Field_Alias,
                        educationFormData.Institution_Id
                      )
                    : true,
                ]"
                :isRequired="
                  labelList[130].Mandatory_Field?.toLowerCase() == 'yes'
                "
              ></CustomSelect>
              <v-text-field
                v-else
                v-model="educationFormData.Institute_Name"
                :rules="[
                  educationFormData.Institute_Name
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.Institute_Name,
                        'instituteName',
                        labelList[130].Field_Alias || 'Institute'
                      )
                    : true,
                  labelList[130].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[130].Field_Alias,
                        educationFormData.Institute_Name
                      )
                    : true,
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[130].Field_Alias }}
                  <span
                    v-if="
                      labelList[130].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <!-- School/University -131 -->
            <v-col
              v-if="labelList[131]?.Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="6"
            >
              <v-text-field
                v-model="educationFormData.University"
                :rules="[
                  labelList[131].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[131].Field_Alias,
                        educationFormData.University
                      )
                    : true,
                  educationFormData.University
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.University,
                        'university',
                        labelList[131].Field_Alias || 'School/University'
                      )
                    : true,
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[131].Field_Alias }}
                  <span
                    v-if="
                      labelList[131].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <!-- Year of Start - 292 -->
            <v-col
              cols="12"
              md="6"
              v-if="labelList[292]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <CustomSelect
                :items="yearList"
                :label="labelList[292].Field_Alias"
                :itemSelected="educationFormData.Year_Of_Start"
                @selected-item="
                  onChangeCustomSelectField($event, 'Year_Of_Start')
                "
                :rules="[
                  labelList[292].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[292].Field_Alias,
                        educationFormData.Year_Of_Start
                      )
                    : true,
                ]"
                :isRequired="
                  labelList[292].Mandatory_Field?.toLowerCase() == 'yes'
                "
              ></CustomSelect>
            </v-col>
            <!-- Year of Passing - 134 -->
            <v-col
              cols="12"
              md="6"
              v-if="labelList[134].Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <CustomSelect
                :items="yearList"
                :label="labelList[134].Field_Alias"
                :itemSelected="educationFormData.Year_Of_Passing"
                :rules="[
                  labelList[134].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[134].Field_Alias,
                        educationFormData.Year_Of_Passing
                      )
                    : true,
                ]"
                :isRequired="
                  labelList[134].Mandatory_Field?.toLowerCase() == 'yes'
                "
                @selected-item="
                  onChangeCustomSelectField($event, 'Year_Of_Passing')
                "
              ></CustomSelect>
            </v-col>
            <!-- Start Date - 156 -->
            <v-col
              v-if="labelList[156]?.Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="6"
            >
              <v-menu
                v-model="startDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="Start Date"
                    v-model="formattedStartDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="
                      labelList[156].Mandatory_Field?.toLowerCase() == 'yes'
                        ? [
                            required(
                              labelList[156].Field_Alias,
                              formattedStartDate
                            ),
                          ]
                        : [true]
                    "
                    variant="solo"
                    readonly
                    v-bind="props"
                  >
                    <template v-slot:label>
                      {{ labelList[156].Field_Alias }}
                      <span
                        v-if="
                          labelList[156].Mandatory_Field?.toLowerCase() == 'yes'
                        "
                        style="color: red"
                        >*</span
                      >
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="educationFormData.Start_Date"
                  :min="new Date(selectedStartDate)"
                  :max="new Date(educationStartMax)"
                  @update:model-value="onChangeFields"
                />
              </v-menu>
            </v-col>
            <!-- End Date - 157 -->
            <v-col
              v-if="labelList[157]?.Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="6"
            >
              <v-menu
                v-model="endDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="End Date"
                    v-model="formattedEndDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="
                      labelList[157].Mandatory_Field?.toLowerCase() == 'yes'
                        ? [
                            required(
                              labelList[157].Field_Alias,
                              formattedEndDate
                            ),
                          ]
                        : [true]
                    "
                    variant="solo"
                    readonly
                    v-bind="props"
                  >
                    <template v-slot:label>
                      {{ labelList[157].Field_Alias }}
                      <span
                        v-if="
                          labelList[157].Mandatory_Field?.toLowerCase() == 'yes'
                        "
                        style="color: red"
                        >*</span
                      >
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="educationFormData.End_Date"
                  :min="new Date(educationEndDateMin)"
                  @update:model-value="onChangeFields"
                />
              </v-menu>
            </v-col>
            <!-- City - 158 -->
            <v-col
              v-if="labelList[158]?.Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="cityList"
                :label="labelList[158].Field_Alias"
                item-value="City_Name"
                item-title="cityStateDetails"
                :isLoading="isCityListLoading"
                :ref="'City'"
                :isAutoComplete="true"
                :isRequired="
                  labelList[158].Mandatory_Field?.toLowerCase() == 'yes'
                "
                :rules="[
                  labelList[158].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        `${labelList[158].Field_Alias}`,
                        educationFormData.City
                      )
                    : true,
                ]"
                :noDataText="
                  isCityListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="educationFormData.City"
                @selected-item="onChangeCustomSelectField($event, 'City')"
              />
            </v-col>
            <!-- State - 159 -->
            <v-col
              v-if="labelList[159]?.Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="6"
            >
              <formTextFeild
                :ref="'State'"
                v-model="educationFormData.State"
                :rules="[
                  labelList[159].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        `${labelList[159].Field_Alias}`,
                        educationFormData.State
                      )
                    : true,
                  educationFormData.State
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.State,
                        'state',
                        `${labelList[159].Field_Alias}`
                      )
                    : true,
                ]"
              >
                <template v-slot:label>
                  <span>{{ labelList[159].Field_Alias }}</span>
                  <span
                    v-if="
                      labelList[159].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template></formTextFeild
              >
            </v-col>
            <!-- Country - 160 -->
            <v-col
              v-if="labelList[160]?.Field_Visiblity?.toLowerCase() == 'yes'"
              cols="12"
              md="6"
            >
              <CustomSelect
                v-model="educationFormData.Country"
                :items="countryList"
                :label="labelList[160].Field_Alias"
                :isAutoComplete="true"
                :isLoading="countryListLoading"
                :noDataText="
                  countryListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="
                  educationFormData.Country ? educationFormData.Country : ''
                "
                itemValue="Country_Code"
                itemTitle="Country_Name"
                :isRequired="
                  labelList[160].Mandatory_Field?.toLowerCase() == 'yes'
                "
                :rules="[
                  labelList[160].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        `${labelList[160].Field_Alias}`,
                        educationFormData.Country
                      )
                    : true,
                ]"
                ref="Country"
                @selected-item="onChangeCustomSelectField($event, 'Country')"
              />
            </v-col>
            <!-- Percentage - 132 -->
            <v-col
              cols="12"
              md="6"
              v-if="labelList[132]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <v-text-field
                v-model="educationFormData.Percentage"
                :rules="[
                  educationFormData.Percentage
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.Percentage,
                        'percentage',
                        labelList[132].Field_Alias
                      )
                    : true,
                  labelList[132].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[132].Field_Alias,
                        educationFormData.Percentage
                      )
                    : true,
                ]"
                variant="solo"
                type="number"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[132].Field_Alias }}
                  <span
                    v-if="
                      labelList[132].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <!-- Grade - 133 -->
            <v-col
              cols="12"
              md="6"
              v-if="labelList[133]?.Field_Visiblity?.toLowerCase() == 'yes'"
            >
              <v-text-field
                v-model="educationFormData.Grade"
                :rules="[
                  educationFormData.Grade
                    ? validateWithRulesAndReturnMessages(
                        educationFormData.Grade,
                        'grade',
                        labelList[133].Field_Alias
                      )
                    : true,
                  labelList[133].Mandatory_Field?.toLowerCase() == 'yes'
                    ? required(
                        labelList[133].Field_Alias,
                        educationFormData.Grade
                      )
                    : true,
                ]"
                @update:model-value="formatInput"
                variant="solo"
              >
                <template v-slot:label>
                  {{ labelList[133].Field_Alias }}
                  <span
                    v-if="
                      labelList[133].Mandatory_Field?.toLowerCase() == 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <!-- Document -->
            <v-col cols="12" md="6">
              <v-file-input
                prepend-icon=""
                :model-value="fileContent"
                append-inner-icon="fas fa-paperclip"
                variant="solo"
                label="Document"
                accept="image/png, image/jpeg, image/jpg, application/pdf"
                :rules="[required('Document', fileContentRuleValue)]"
                @update:model-value="onChangeFiles"
                @click:clear="removeFiles"
              >
                <template v-slot:label>
                  Document
                  <span style="color: red">*</span>
                </template>
              </v-file-input>
            </v-col>
            <!-- Document Sub Type -->
            <v-col
              v-if="
                fileContent &&
                fileContent.name &&
                presentDocumentSubtype === null
              "
              cols="12"
              md="6"
            >
              <CustomSelect
                :items="documentSubTypeList"
                label="Document Sub Type"
                :itemSelected="educationFormData.Sub_Type_Id"
                :rules="[
                  required('Document Sub Type', educationFormData.Sub_Type_Id),
                ]"
                itemValue="Document_Sub_Type_Id"
                itemTitle="Document_Sub_Type"
                :isRequired="true"
                @selected-item="
                  onChangeCustomSelectField($event, 'Sub_Type_Id')
                "
                :isAutoComplete="true"
                :isLoading="documentSubTypeListLoading"
                :noDataText="
                  documentSubTypeListLoading
                    ? 'Loading...'
                    : 'No data available'
                "
              ></CustomSelect>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="this.$emit('close-education-form')"
                  class="ma-2 pa-2"
                  color="primary"
                  rounded="lg"
                  variant="text"
                  elevation="4"
                  >Cancel</v-btn
                >
                <v-btn
                  color="primary"
                  rounded="lg"
                  class="ma-2 pa-2"
                  :disabled="!isFormDirty"
                  @click="validateEducationDetails"
                  >Save</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { ADD_UPDATE_EDUCATION_DETAILS } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import { LIST_CITIES } from "@/graphql/dropDownQueries.js";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import moment from "moment";
import {
  LIST_COURSE,
  LIST_SUB_DOC_TYPE,
  LIST_SPECIALIZATION_INSTITUTE,
} from "@/graphql/dropDownQueries";

export default {
  name: "AddEditEducationDetails",
  mixins: [validationRules],
  props: {
    selectedEducationDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
  },
  components: {
    CustomSelect,
  },
  emits: ["refetch-career-details", "close-education-form"],
  data() {
    return {
      educationFormData: {
        Education_Type: null,
        Specialisation: "",
        Specialization_Id: null,
        Institute_Name: "",
        Institution_Id: null,
        University: "",
        Year_Of_Start: null,
        Year_Of_Passing: null,
        Percentage: null,
        Grade: "",
        Sub_Type_Id: null,
      },
      // list
      courseListFetching: false,
      courseList: [],
      documentSubTypeList: [],
      documentSubTypeListLoading: false,
      specializationList: [],
      instituteList: [],
      splInstituteLoading: false,
      // edit
      isFormDirty: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      splInstituteIsDropdown: {},
      // file
      fileContent: null,
      isFileChanged: false,
      //Date-picker
      formattedStartDate: "",
      formattedEndDate: "",
      startDateMenu: false,
      endDateMenu: false,
      cityList: [],
      isCityListLoading: false,
      countryListLoading: false,
      countryList: [],
    };
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedEducationDetails &&
      Object.keys(this.selectedEducationDetails).length > 0
    ) {
      this.educationFormData = JSON.parse(
        JSON.stringify(this.selectedEducationDetails)
      );
      this.fileContent = {
        name: this.formattedFileName(this.educationFormData["File_Name"]),
        size: 100,
      };
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
    this.retrieveEducationCourse();
    this.retrieveDocumentSubType();
    this.retrieveSplInstitute();
    this.listCity();
    this.retrieveCountries();
  },

  // function for getting the available years
  computed: {
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    yearList() {
      let empDob = this.selectedCandidateDOB;
      let year = 0;
      if (empDob) {
        year = new Date().getFullYear() - new Date(empDob).getFullYear();
      } else {
        year = 80;
      }
      const now = new Date().getUTCFullYear();
      const years = Array(now - (now - year))
        .fill("")
        .map((v, idx) => now - idx);
      return years;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    fileContentRuleValue() {
      return this.fileContent && this.fileContent.name
        ? this.fileContent.name
        : null;
    },
    presentDocumentSubtype() {
      let document = this.courseList.find(
        (course) => course.Course_Id === this.educationFormData.Education_Type
      );
      return document?.Document_Sub_Type_Id
        ? document.Document_Sub_Type_Id
        : null;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    currentDate() {
      const today = new Date();
      return today;
    },
    selectedStartDate() {
      if (
        this.selectedCandidateDOB &&
        moment(this.selectedCandidateDOB).isValid()
      ) {
        return new Date(this.selectedCandidateDOB);
      } else return null;
    },
    educationStartMax() {
      if (
        this.educationFormData.End_Date &&
        moment(this.educationFormData.End_Date).isValid()
      ) {
        const issueDateMs = new Date(this.educationFormData.End_Date);
        return issueDateMs;
      }
      return this.currentDate;
    },
    educationEndDateMin() {
      if (
        this.educationFormData.Start_Date &&
        moment(this.educationFormData.Start_Date).isValid()
      ) {
        const issueDateMs = new Date(this.educationFormData.Start_Date);
        return issueDateMs;
      }
      return this.selectedStartDate;
    },
  },
  watch: {
    "educationFormData.Start_Date": function (val) {
      if (val) {
        this.startDateMenu = false;
        this.formattedStartDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "educationFormData.End_Date": function (val) {
      if (val) {
        this.endDateMenu = false;
        this.formattedEndDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "educationFormData.City": function (val) {
      for (let item of this.cityList) {
        if (item && item.cityStateDetails && item.City_Name == val) {
          let details = item.cityStateDetails.split(", ");
          this.educationFormData["State"] = details[1];
          this.educationFormData["Country"] = item.Country_Code;
        }
      }
    },
  },

  methods: {
    // method to transform the text into upperCase
    formatInput() {
      this.educationFormData.Grade = this.educationFormData.Grade.toUpperCase();
      this.onChangeFields();
    },

    onChangeFields() {
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.educationFormData[field] = value;
      if (field === "Institution_Id") {
        this.educationFormData["Institute_Name"] = this.instituteList.find(
          (el) => el.Institution_Id === value
        )?.Institution;
      }
    },

    async validateEducationDetails() {
      const { valid } = await this.$refs.addEditEducationForm.validate();
      mixpanel.track("Onboarded-candidate-career-edu-submit-click");
      if (valid) {
        this.validateDocuments();
      }
    },

    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        if (this.fileContent && this.fileContent.size && this.isFileChanged) {
          await this.uploadFileContents(this.fileContent);
        }
        this.updateEducationDetails();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },

    updateEducationDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_EDUCATION_DETAILS,
          variables: {
            candidateId: vm.selectedCandidateId,
            educationId: vm.educationFormData.Education_Id,
            educationType: parseInt(vm.educationFormData.Education_Type),
            specialisation: vm.educationFormData.Specialisation,
            instituteName: vm.educationFormData.Institute_Name,
            university: vm.educationFormData.University,
            yearOfPassing: parseInt(vm.educationFormData.Year_Of_Passing),
            yearOfStart: parseInt(vm.educationFormData.Year_Of_Start),
            percentage: parseFloat(vm.educationFormData.Percentage),
            startDate: moment(vm.educationFormData.Start_Date).isValid()
              ? moment(vm.educationFormData.Start_Date).format("YYYY-MM-DD")
              : null,
            endDate: moment(vm.educationFormData.End_Date).isValid()
              ? moment(vm.educationFormData.End_Date).format("YYYY-MM-DD")
              : null,
            grade: vm.educationFormData.Grade,
            fileName: vm.educationFormData["File_Name"],
            documentSubTypeId: vm.presentDocumentSubtype
              ? vm.presentDocumentSubtype
              : vm.educationFormData.Sub_Type_Id,
            specializationId: vm.educationFormData.Specialization_Id
              ? vm.educationFormData.Specialization_Id
              : 0,
            institutionId: vm.educationFormData.Institution_Id
              ? vm.educationFormData.Institution_Id
              : 0,
            city: vm.educationFormData.City,
            state: vm.educationFormData.State,
            country: vm.educationFormData.Country,
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track("Onboarded-candidate-career-edu-update-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Education details updated successfully"
                : "Education details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-career-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-career-edu-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "education details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveEducationCourse() {
      let vm = this;
      vm.courseListFetching = true;
      vm.$apollo
        .query({
          query: LIST_COURSE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listCourseDetails &&
            !response.data.listCourseDetails.errorCode
          ) {
            const { courseDetails } = response.data.listCourseDetails;
            vm.courseList =
              courseDetails && courseDetails.length > 0 ? courseDetails : [];
          }
          vm.courseListFetching = false;
        })
        .catch(() => {
          vm.courseListFetching = false;
        });
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File Name";
      }
      return "";
    },
    onChangeFiles(value) {
      this.fileContent = value;
      mixpanel.track("Onboarded-candidate-doc-file-changed");
      if (this.fileContent && this.fileContent.name) {
        this.educationFormData["File_Name"] =
          this.selectedCandidateId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent.name;
        this.isFileChanged = true;
      }
      this.onChangeFields();
    },
    removeFiles() {
      mixpanel.track("Onboarded-candidate-doc-file-removed");
      this.educationFormData["File_Name"] = "";
      this.fileContent = null;
      this.onChangeFields();
    },
    retrieveDocumentSubType() {
      let vm = this;
      vm.documentSubTypeListLoading = true;
      vm.$apollo
        .query({
          query: LIST_SUB_DOC_TYPE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentSubType &&
            !response.data.listDocumentSubType.errorCode
          ) {
            const { documentSubType } = response.data.listDocumentSubType;
            vm.documentSubTypeList =
              documentSubType && documentSubType.length > 0
                ? documentSubType
                : [];
            vm.documentSubTypeList = vm.documentSubTypeList.filter(
              (el) => el.Document_Type_Id == 3
            );
          }
          vm.documentSubTypeListLoading = false;
        })
        .catch(() => {
          vm.documentSubTypeListLoading = false;
        });
    },
    async uploadFileContents() {
      mixpanel.track("Onboarded-candidate-doc-file-upload-start");
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employees Document Upload/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.educationFormData["File_Name"],
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .catch((error) => {
          throw error;
        });
    },
    retrieveSplInstitute() {
      let vm = this;
      vm.splInstituteLoading = true;
      vm.$apollo
        .query({
          query: LIST_SPECIALIZATION_INSTITUTE,
          client: "apolloClientV",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveListEduInstitutionAndSpecialization &&
            !response.data.retrieveListEduInstitutionAndSpecialization.errorCode
          ) {
            const { institution, specialization } =
              response.data.retrieveListEduInstitutionAndSpecialization;
            vm.specializationList = specialization;
            vm.instituteList = institution;
          }
          vm.splInstituteLoading = false;
        })
        .catch(() => {
          vm.splInstituteLoading = false;
        });
    },
    listCity() {
      let vm = this;
      vm.isCityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES,
          client: "apolloClientAC",
          variables: {
            Form_Id: 178,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCityListWithState &&
            !response.data.getCityListWithState.errorCode
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            vm.cityList = cityDetails;
          }
          vm.isCityListLoading = false;
        })
        .catch(() => {
          vm.isCityListLoading = false;
        });
    },
    async retrieveCountries() {
      this.countryListLoading = true;
      this.countryList = [];
      await this.$store
        .dispatch("listCountries")
        .then((langList) => {
          this.countryList = langList;
          this.countryListLoading = false;
        })
        .catch(() => {
          this.countryListLoading = false;
        });
    },
  },
};
</script>
