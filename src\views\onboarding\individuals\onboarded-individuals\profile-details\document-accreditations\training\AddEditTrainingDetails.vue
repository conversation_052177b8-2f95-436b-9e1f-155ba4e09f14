<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Training Details</span
        >
        <v-spacer></v-spacer>
        <v-icon color="primary" size="25" @click="$emit('close-training-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addTrainingForm">
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="trainingFormData.Training_Name"
                :rules="[
                  required('Training', trainingFormData.Training_Name),
                  validateWithRulesAndReturnMessages(
                    trainingFormData.Training_Name,
                    'trainingName',
                    'Training'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Training<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="trainingFormData.Trainer"
                :rules="[
                  required('Trainer ', trainingFormData.Trainer),
                  validateWithRulesAndReturnMessages(
                    trainingFormData.Trainer,
                    'trainer',
                    'Trainer '
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Trainer<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-menu
                v-model="startDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="Start Date"
                    v-model="formattedStartDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('Start Date', formattedStartDate)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      Start Date<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="trainingFormData.Training_Start_Date"
                  :min="selectedEmpDobDate"
                  :max="currentDate"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="6">
              <v-menu
                v-model="endDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="End Date"
                    v-model="formattedEndDate"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('End Date', formattedEndDate)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      End Date<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="trainingFormData.Training_End_Date"
                  :min="trainingEndDateMin"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="6">
              <p class="text-subtitle-1 text-grey-darken-1">Duration</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{
                  trainingFormData.Training_Duration
                    ? convertMonthToYearMonthsDays(
                        trainingFormData.Training_Duration
                      )
                    : "-"
                }}
              </p>
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="trainingFormData.Center"
                :rules="[
                  required('Center', trainingFormData.Center),
                  validateWithRulesAndReturnMessages(
                    trainingFormData.Center,
                    'center',
                    'Center'
                  ),
                ]"
                variant="solo"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Center<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6">
              <v-file-input
                prepend-icon=""
                :model-value="fileContent"
                append-inner-icon="fas fa-paperclip"
                variant="solo"
                label="Document"
                accept="image/png, image/jpeg, image/jpg, application/pdf"
                @update:modelValue="onChangeFiles"
                @click:clear="removeFiles"
              >
              </v-file-input>
            </v-col>
            <v-col v-if="fileContent" cols="12" md="6">
              <CustomSelect
                :items="documentSubTypeList"
                label="Document Sub Type"
                :itemSelected="trainingFormData.Sub_Type_Id"
                :rules="[
                  required('Document Sub Type', trainingFormData.Sub_Type_Id),
                ]"
                itemValue="Document_Sub_Type_Id"
                itemTitle="Document_Sub_Type"
                :isRequired="true"
                @selected-item="
                  onChangeCustomSelectField($event, 'Sub_Type_Id')
                "
                :isAutoComplete="true"
                :isLoading="documentSubTypeListLoading"
                :noDataText="
                  documentSubTypeListLoading
                    ? 'Loading...'
                    : 'No data available'
                "
              ></CustomSelect>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="this.$emit('close-training-form')"
                  class="ma-2 pa-2"
                  color="primary"
                  elevation="4"
                  rounded="lg"
                  variant="text"
                  >Cancel</v-btn
                >
                <v-btn
                  class="ma-2 pa-2"
                  color="primary"
                  rounded="lg"
                  @click="validateTrainingDetails"
                  :disabled="!isFormDirty"
                  >Save</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules.js";
import moment from "moment";
import { ADD_UPDATE_TRAINING_DETAILS } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import { convertMonthToYearMonthsDays, getDaysDifference } from "@/helper";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import { LIST_SUB_DOC_TYPE } from "@/graphql/dropDownQueries";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "AddEditTrainingDetails",
  components: {
    CustomSelect,
  },
  props: {
    selectedTrainingDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
  },
  mixins: [validationRules],
  emits: ["refetch-career-details", "close-training-form"],
  data() {
    return {
      trainingFormData: {
        Training_Name: "",
        Training_Start_Date: null,
        Training_End_Date: null,
        Training_Duration: "",
        Trainer: "",
        Center: "",
      },
      isFormDirty: false,
      // edit
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      // file
      fileContent: null,
      isFileChanged: false,
      documentSubTypeList: [],
      documentSubTypeListLoading: false,
      //Date-picker
      formattedStartDate: "",
      formattedEndDate: "",
      startDateMenu: false,
      endDateMenu: false,
    };
  },

  // using this to make all the future dates disabled
  computed: {
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    selectedEmpDobDate() {
      if (
        this.selectedCandidateDOB &&
        this.selectedCandidateDOB !== "0000-00-00"
      ) {
        return moment(this.selectedCandidateDOB).format("YYYY-MM-DD");
      } else return null;
    },
    trainingStartMax() {
      if (
        this.trainingFormData.Training_End_Date &&
        this.trainingFormData.Training_End_Date !== "0000-00-00"
      ) {
        return moment(this.trainingFormData.Training_End_Date).format(
          "YYYY-MM-DD"
        );
      }
      return this.currentDate;
    },
    trainingEndDateMin() {
      if (
        this.trainingFormData.Training_Start_Date &&
        this.trainingFormData.Training_Start_Date !== "0000-00-00"
      ) {
        return moment(this.trainingFormData.Training_Start_Date).format(
          "YYYY-MM-DD"
        );
      }
      return this.selectedEmpDobDate;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    fileContentRuleValue() {
      return this.fileContent && this.fileContent.name
        ? this.fileContent.name
        : null;
    },
  },

  watch: {
    "trainingFormData.Training_Start_Date": function (val) {
      // Do something with the new value and/or old value here
      this.calculateDuration();
      if (val) {
        this.startDateMenu = false;
        this.formattedStartDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "trainingFormData.Training_End_Date": function (val) {
      this.calculateDuration();
      if (val) {
        this.endDateMenu = false;
        this.formattedEndDate = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedTrainingDetails &&
      Object.keys(this.selectedTrainingDetails).length > 0
    ) {
      this.trainingFormData = JSON.parse(
        JSON.stringify(this.selectedTrainingDetails)
      );
      if (this.trainingFormData.Training_Start_Date) {
        this.formattedStartDate = this.formatDate(
          this.trainingFormData?.Training_Start_Date
        );
        this.trainingFormData.Training_Start_Date = this.trainingFormData
          .Training_Start_Date
          ? new Date(this.trainingFormData.Training_Start_Date)
          : null;
      }
      if (this.trainingFormData.Training_End_Date) {
        this.formattedEndDate = this.formatDate(
          this.trainingFormData?.Training_End_Date
        );
        this.trainingFormData.Training_End_Date = this.trainingFormData
          .Training_End_Date
          ? new Date(this.trainingFormData.Training_End_Date)
          : null;
      }
      if (this.trainingFormData["File_Name"]) {
        this.fileContent = {
          name: this.formattedFileName(this.trainingFormData["File_Name"]),
          size: 100,
        };
      }
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
    this.retrieveDocumentSubType();
  },

  methods: {
    convertMonthToYearMonthsDays,
    calculateDuration() {
      let dayDifference = getDaysDifference(
        this.trainingFormData.Training_Start_Date,
        this.trainingFormData.Training_End_Date
      );
      this.trainingFormData["Training_Duration"] = (dayDifference / 30).toFixed(
        2
      );
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.trainingFormData[field] = value;
    },

    async validateTrainingDetails() {
      const { valid } = await this.$refs.addTrainingForm.validate();
      mixpanel.track("Onboarded-candidate-career-training-submit-click");
      if (valid) {
        this.validateDocuments();
      }
    },

    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        if (this.fileContent && this.fileContent.size && this.isFileChanged) {
          await this.uploadFileContents(this.fileContent);
        }
        this.updateTrainingDetails();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },

    updateTrainingDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_TRAINING_DETAILS,
          variables: {
            candidateId: vm.selectedCandidateId,
            trainingId: vm.trainingFormData.Training_Id,
            trainingName: vm.trainingFormData.Training_Name,
            trainingStartDate: moment(
              vm.trainingFormData.Training_Start_Date
            ).isValid()
              ? moment(vm.trainingFormData.Training_Start_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            trainingEndDate: moment(
              vm.trainingFormData.Training_End_Date
            ).isValid()
              ? moment(vm.trainingFormData.Training_End_Date).format(
                  "YYYY-MM-DD"
                )
              : null,
            trainingDuration: vm.trainingFormData.Training_Duration,
            trainer: vm.trainingFormData.Trainer,
            center: vm.trainingFormData.Center,
            fileName: vm.trainingFormData["File_Name"],
            documentSubTypeId: vm.trainingFormData.Sub_Type_Id,
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track("Onboarded-candidate-career-training-update-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Training details updated successfully"
                : "Training details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-career-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-career-training-update-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "training details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File Name";
      }
      return "";
    },
    onChangeFiles(value) {
      this.fileContent = value;
      if (this.fileContent && this.fileContent.name) {
        mixpanel.track("Onboarded-candidate-doc-file-changed");
        this.trainingFormData["File_Name"] =
          this.selectedCandidateId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent?.name;
        this.isFileChanged = true;
      }
      this.onChangeFields();
    },
    removeFiles() {
      mixpanel.track("Onboarded-candidate-doc-file-removed");
      this.fileContent = null;
      this.trainingFormData["File_Name"] = "";
      this.onChangeFields();
    },
    retrieveDocumentSubType() {
      let vm = this;
      vm.documentSubTypeListLoading = true;
      vm.$apollo
        .query({
          query: LIST_SUB_DOC_TYPE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listDocumentSubType &&
            !response.data.listDocumentSubType.errorCode
          ) {
            const { documentSubType } = response.data.listDocumentSubType;
            vm.documentSubTypeList =
              documentSubType && documentSubType.length > 0
                ? documentSubType
                : [];
            vm.documentSubTypeList = vm.documentSubTypeList.filter(
              (el) => el.Document_Type_Id == 6
            );
          }
          vm.documentSubTypeListLoading = false;
        })
        .catch(() => {
          vm.documentSubTypeListLoading = false;
        });
    },
    async uploadFileContents() {
      mixpanel.track("Onboarded-candidate-doc-file-upload-start");
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employees Document Upload/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.trainingFormData["File_Name"],
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .catch((error) => {
          throw error;
        });
    },
  },
};
</script>
