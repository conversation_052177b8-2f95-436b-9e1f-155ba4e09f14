<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Experience Details</span
        >
        <v-spacer></v-spacer>
        <v-icon
          color="primary"
          size="25"
          @click="$emit('close-experience-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditExperienceForm">
          <v-row>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model="experienceFormData.Prev_Company_Name"
                variant="solo"
                :rules="[
                  required('Company', experienceFormData.Prev_Company_Name),
                  validateWithRulesAndReturnMessages(
                    experienceFormData.Prev_Company_Name,
                    'companyName',
                    'Company'
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Company<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model="experienceFormData.Designation"
                variant="solo"
                :rules="[
                  required('Designation', experienceFormData.Designation),
                  validateWithRulesAndReturnMessages(
                    experienceFormData.Designation,
                    'designation',
                    'Designation'
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Designation<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-text-field
                v-model="experienceFormData.Prev_Company_Location"
                variant="solo"
                :rules="[
                  required(
                    'Location',
                    experienceFormData.Prev_Company_Location
                  ),
                  validateWithRulesAndReturnMessages(
                    experienceFormData.Prev_Company_Location,
                    'companyLocation',
                    'Location'
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  Location<span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-menu
                v-model="fromMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="startDate"
                    v-model="formattedFrom"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('From', formattedFrom)]"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      From<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="experienceFormData.Start_Date"
                  :min="selectedEmpDobDate"
                  :max="startDateMax"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-menu
                v-model="fromTo"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    ref="endDate"
                    v-model="formattedTo"
                    prepend-inner-icon="fas fa-calendar"
                    :rules="[required('To', formattedTo)]"
                    readonly
                    :disabled="!experienceFormData.Start_Date"
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      To<span style="color: red">*</span>
                    </template></v-text-field
                  >
                </template>
                <v-date-picker
                  v-model="experienceFormData.End_Date"
                  :min="endDateMin"
                  :max="selectedEmpDojDate"
                  @update:modelValue="onChangeFields()"
                />
              </v-menu>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Duration</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ durationInDays }}
              </p>
            </v-col>
            <v-col cols="12" md="4" sm="6">
              <v-file-input
                prepend-icon=""
                :model-value="fileContent"
                append-inner-icon="fas fa-paperclip"
                label="Document"
                variant="solo"
                accept="image/png, image/jpeg, image/jpg, application/pdf"
                @update:modelValue="onChangeFiles"
                @click:clear="removeFiles"
              ></v-file-input>
            </v-col>
          </v-row>
          <div
            v-if="
              labelList[366]?.Field_Visiblity?.toLowerCase() === 'yes' ||
              labelList[367]?.Field_Visiblity?.toLowerCase() === 'yes' ||
              labelList[368]?.Field_Visiblity?.toLowerCase() === 'yes'
            "
            class="d-flex justify-space-between"
          >
            <span
              class="text-subtitle-1 text-grey-darken-1 font-weight-bold mb-4"
            >
              Reference(s)
            </span>
            <v-btn
              color="primary"
              class=""
              variant="text"
              @click="addMoreReferences()"
            >
              <v-icon class="mr-1" size="15">fas fa-plus</v-icon>
              Add New
            </v-btn>
          </div>
          <v-form ref="referenceForm">
            <v-row
              v-for="(item, index) in experienceFormData.References"
              :key="index"
            >
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[366]?.Field_Visiblity?.toLowerCase() == 'yes'"
              >
                <v-text-field
                  v-model="item.Reference_Name"
                  variant="solo"
                  :rules="[
                    labelList[366].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          labelList[366].Field_Alias,
                          item.Reference_Name
                        )
                      : true,
                    validateWithRulesAndReturnMessages(
                      item.Reference_Name,
                      'skillName',
                      labelList[366]?.Field_Alias
                    ),
                  ]"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    {{ labelList[366].Field_Alias }}
                    <span
                      v-if="
                        labelList[366].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="4"
                sm="6"
                v-if="labelList[367]?.Field_Visiblity?.toLowerCase() == 'yes'"
              >
                <v-text-field
                  v-model="item.Reference_Email"
                  variant="solo"
                  :rules="[
                    labelList[367].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          labelList[367].Field_Alias,
                          item.Reference_Email
                        )
                      : true,
                    validateWithRulesAndReturnMessages(
                      item.Reference_Email,
                      'empEmail',
                      labelList[367]?.Field_Alias
                    ),
                  ]"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    {{ labelList[367].Field_Alias }}
                    <span
                      v-if="
                        labelList[367].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                md="3"
                sm="5"
                v-if="labelList[368]?.Field_Visiblity?.toLowerCase() == 'yes'"
              >
                <v-text-field
                  v-model="item.Reference_Number"
                  variant="solo"
                  :rules="[
                    labelList[368].Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          labelList[368].Field_Alias,
                          item.Reference_Number
                        )
                      : true,
                    item.Reference_Number
                      ? minLengthValidation(
                          labelList[368]?.Field_Alias,
                          item.Reference_Number,
                          6
                        )
                      : true,
                  ]"
                  :counter="15"
                  :maxlength="15"
                  @update:model-value="onChangeFields"
                >
                  <template v-slot:label>
                    {{ labelList[368].Field_Alias }}
                    <span
                      v-if="
                        labelList[368].Mandatory_Field?.toLowerCase() == 'yes'
                      "
                      style="color: red"
                      >*</span
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col v-if="experienceFormData.References.length > 1">
                <v-btn
                  variant="text"
                  class="mt-5"
                  @click="experienceFormData.References.splice(index, 1)"
                >
                  <v-icon>fas fa-trash</v-icon>
                </v-btn>
              </v-col>
            </v-row>
          </v-form>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  class="ma-2 pa-2"
                  variant="text"
                  color="primary"
                  elevation="2"
                  rounded="lg"
                  @click="$emit('close-experience-form')"
                >
                  Cancel
                </v-btn>
                <v-btn
                  :disabled="!isFormDirty"
                  class="ma-2 pa-1"
                  color="primary"
                  rounded="lg"
                  @click="validateExperienceDetails()"
                >
                  Save
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { ADD_UPDATE_EXPERIENCE_DETAILS } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import {
  convertMonthToYearMonthsDays,
  getDaysDifference,
  getMonthDifference,
  getYearDifference,
} from "@/helper";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";

export default {
  name: "AddEditExperienceDetails",
  mixins: [validationRules],
  props: {
    experienceDetails: {
      type: Object,
      required: false,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
    selectedCandidateDOB: {
      type: [String, Date],
      default: "",
    },
    selectedEmpDoj: {
      type: String,
      default: "",
    },
  },
  emits: ["refetch-experience-details", "close-experience-form"],
  data() {
    return {
      experienceFormData: {
        Prev_Company_Name: "",
        Designation: "",
        Prev_Company_Location: "",
        Start_Date: null,
        End_Date: null,
        Duration: "",
        File_Name: null,
        File_Size: null,
        References: [
          {
            Reference_Name: "",
            Reference_Number: "",
            Reference_Email: "",
          },
        ],
      },
      //Date-picker
      formattedFrom: "",
      fromMenu: false,
      fromTo: false,
      formattedTo: "",
      isFormDirty: false,
      // edit
      isFileChanged: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      // file
      fileContent: null,
    };
  },
  computed: {
    selectedEmpDobDate() {
      if (
        this.selectedCandidateDOB &&
        this.selectedCandidateDOB !== "0000-00-00"
      ) {
        return moment(this.selectedCandidateDOB).format("YYYY-MM-DD");
      } else return null;
    },
    selectedEmpDojDate() {
      if (this.selectedEmpDoj && this.selectedEmpDoj !== "0000-00-00") {
        return moment(this.selectedEmpDoj).format("YYYY-MM-DD");
      } else return null;
    },
    startDateMax() {
      if (
        this.experienceFormData.End_Date &&
        this.experienceFormData.End_Date !== "0000-00-00"
      ) {
        return moment(this.experienceFormData.End_Date).format("YYYY-MM-DD");
      }
      return this.selectedEmpDojDate;
    },
    endDateMin() {
      if (
        this.experienceFormData.Start_Date &&
        this.experienceFormData.Start_Date !== "0000-00-00"
      ) {
        return moment(this.experienceFormData.Start_Date).format("YYYY-MM-DD");
      }
      return this.selectedEmpDobDate;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    domainName() {
      return this.$store.getters.domain;
    },
    currentTimeStamp() {
      return moment().unix();
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    durationInDays() {
      if (
        this.experienceFormData.Start_Date &&
        this.experienceFormData.End_Date
      ) {
        let startDate = moment(this.experienceFormData.Start_Date);
        let endDate = moment(this.experienceFormData.End_Date);
        let totalDays = endDate.diff(startDate, "days");

        // Break down the duration into years, months, and days
        let years = Math.floor(totalDays / 365);
        totalDays %= 365;
        let months = Math.floor(totalDays / 30);
        let days = totalDays % 30;
        let durationString = [];
        if (years > 0) {
          durationString.push(`${years} years`);
        }
        if (months > 0) {
          durationString.push(`${months} months`);
        }
        if (days > 0) {
          durationString.push(`${days} days`);
        }
        return durationString.join(" ");
      } else return "-";
    },
  },
  watch: {
    "experienceFormData.Start_Date": function (val) {
      // Do something with the new value and/or old value here
      this.calculateDuration();
      if (val) {
        this.fromMenu = false;
        this.formattedFrom = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
    "experienceFormData.End_Date": function (val) {
      // Do something with the new value and/or old value here
      this.calculateDuration();
      if (val) {
        this.toMenu = false;
        this.formattedTo = this.formatDate(val);
        this.isFormDirty = true;
      }
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    // Set the form data when component is created
    if (
      this.experienceDetails &&
      Object.keys(this.experienceDetails).length > 0
    ) {
      this.experienceFormData = JSON.parse(
        JSON.stringify(this.experienceDetails)
      );
      if (this.experienceFormData.Start_Date) {
        this.formattedFrom = this.formatDate(
          this.experienceFormData?.Start_Date
        );
        this.experienceFormData.Start_Date = this.experienceFormData.Start_Date
          ? new Date(this.experienceFormData.Start_Date)
          : null;
      }
      if (this.experienceFormData.End_Date) {
        this.formattedTo = this.formatDate(this.experienceFormData?.End_Date);
        this.experienceFormData.End_Date = this.experienceFormData.End_Date
          ? new Date(this.experienceFormData.End_Date)
          : null;
      }
      if (this.experienceFormData["File_Name"]) {
        this.fileContent = {
          name: this.formattedFileName(this.experienceFormData["File_Name"]),
        };
      }
      if (this.experienceFormData["File_Size"]) {
        this.fileContent = {
          ...this.fileContent,
          size: this.experienceFormData["File_Size"],
        };
      }
      if (this.labelList[366]?.Field_Visiblity?.toLowerCase() == "yes") {
        if (this.experienceDetails["Experience_Reference"].length > 0) {
          this.experienceFormData["References"] =
            this.experienceDetails["Experience_Reference"];
        } else {
          this.experienceFormData["References"] = [
            {
              Reference_Name: "",
              Reference_Number: "",
              Reference_Email: "",
            },
          ];
        }
      }
      this.formType = "edit";
    } else {
      this.formType = "add";
    }
  },
  methods: {
    convertMonthToYearMonthsDays,
    calculateDuration() {
      let dayDifference = getDaysDifference(
        this.experienceFormData.Start_Date,
        this.experienceFormData.End_Date
      );
      this.experienceFormData["Duration"] = (dayDifference / 30).toFixed(2);
    },
    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File";
      }
      return "";
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    onChangeFiles(value) {
      this.fileContent = value;
      if (this.fileContent && this.fileContent.name) {
        mixpanel.track("Onboarded-candidate-exp-file-changed");
        this.experienceFormData["File_Name"] =
          this.selectedCandidateId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent?.name;
        this.experienceFormData["File_Size"] = this.fileContent.size.toString();
        this.isFileChanged = true;
      }
      this.onChangeFields();
    },
    removeFiles() {
      mixpanel.track("Onboarded-candidate-exp-file-removed");
      this.fileContent = null;
      this.experienceFormData["File_Name"] = "";
      this.experienceFormData["File_Size"] = "";
      this.onChangeFields();
    },
    async validateExperienceDetails() {
      const { valid } = await this.$refs.addEditExperienceForm.validate();
      let refValid = true;
      if (this.labelList[366]?.Mandatory_Field?.toLowerCase() == "yes") {
        const { valid } = await this.$refs.referenceForm.validate();
        refValid = valid;
      }
      mixpanel.track("Onboarded-candidate-exp-edit-submit-click");
      if (valid && refValid) {
        this.validateDocuments();
      }
    },

    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        if (this.fileContent && this.fileContent.size && this.isFileChanged) {
          await this.uploadFileContents(this.fileContent);
        }
        this.updateExperienceDetails();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },

    updateExperienceDetails() {
      let vm = this;
      vm.isLoading = true;
      let yearDifference = getYearDifference(
        this.experienceFormData.Start_Date,
        this.experienceFormData.End_Date
      );
      let monthDifference = getMonthDifference(
        this.experienceFormData.Start_Date,
        this.experienceFormData.End_Date
      );
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_EXPERIENCE_DETAILS,
          variables: {
            candidateId: vm.selectedCandidateId,
            experienceId: vm.experienceFormData.Experience_Id,
            companyName: vm.experienceFormData.Prev_Company_Name,
            designation: vm.experienceFormData.Designation,
            startDate: moment(vm.experienceFormData.Start_Date).isValid()
              ? moment(vm.experienceFormData.Start_Date).format("YYYY-MM-DD")
              : null,
            endDate: moment(vm.experienceFormData.End_Date).isValid()
              ? moment(vm.experienceFormData.End_Date).format("YYYY-MM-DD")
              : null,
            companyLocation: vm.experienceFormData.Prev_Company_Location,
            duration: vm.experienceFormData.Duration,
            years: yearDifference,
            months: monthDifference,
            fileName: vm.experienceFormData["File_Name"],
            fileSize: vm.experienceFormData["File_Size"],
            referenceDetails: vm.experienceFormData.References,
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track("Onboarded-candidate-exp-edit-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Experience details updated successfully"
                : "Experience details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-experience-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-exp-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "experience details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    async uploadFileContents() {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Employee Experience/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.experienceFormData["File_Name"],
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .catch((error) => {
          throw error;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async addMoreReferences() {
      let { valid } = await this.$refs.referenceForm.validate();
      if (valid) {
        this.experienceFormData.References.push({
          Reference_Name: "",
          Reference_Number: "",
          Reference_Email: "",
        });
      }
    },
  },
};
</script>
