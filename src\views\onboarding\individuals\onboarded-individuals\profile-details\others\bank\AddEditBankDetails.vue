<template>
  <v-card class="rounded-lg pa-4">
    <div>
      <v-card-title class="d-flex">
        <span class="text-h6 text-grey-darken-1 font-weight-bold mb-4"
          >Bank Details</span
        >
        <v-spacer></v-spacer>
        <v-icon color="primary" size="25" @click="$emit('close-bank-form')"
          >fas fa-times</v-icon
        >
      </v-card-title>
      <v-card-text>
        <v-alert v-if="showValidationAlert" prominent type="warning">
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </v-alert>
        <v-form ref="addEditBankForm">
          <v-row>
            <v-col
              v-if="labelList[135]?.Field_Visiblity === 'Yes'"
              cols="12"
              lg="4"
              xl="4"
              md="6"
              sm="12"
              xs="12"
            >
              <v-text-field
                :id="'ifsc'"
                ref="bankIfsc"
                v-model="bankFormData.IFSC_Code"
                variant="solo"
                @change="onChangedIfsc"
                @update:model-value="onChangeFields"
                :rules="[
                  labelList[135].Mandatory_Field === 'Yes'
                    ? required(
                        labelList[135].Field_Alias,
                        bankFormData.IFSC_Code
                      )
                    : true,
                  labelList[135].Field_Alias?.toLowerCase() === 'ifsc code'
                    ? validateWithRulesAndReturnMessages(
                        bankFormData.IFSC_Code,
                        'ifsc',
                        labelList[135].Field_Alias
                      )
                    : true,
                ]"
                :loading="bankNameListLoading"
              >
                <template v-slot:label>
                  {{ labelList[135].Field_Alias
                  }}<span
                    v-if="labelList[135].Mandatory_Field === 'Yes'"
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" lg="4" xl="4" md="6" sm="12" xs="12">
              <CustomSelect
                :items="bankNameList"
                label="Bank"
                :itemSelected="bankFormData.Emp_Bank_Id"
                itemValue="Bank_Id"
                itemTitle="Bank_Name"
                @selected-item="
                  onChangeCustomSelectField($event, 'Emp_Bank_Id')
                "
                :rules="[required('Bank', bankFormData.Emp_Bank_Id)]"
                :isRequired="true"
                :isAutoComplete="true"
                :isLoading="bankNameListLoading"
                :noDataText="
                  bankNameListLoading ? 'Loading...' : 'No data available'
                "
              ></CustomSelect>
            </v-col>
            <v-col
              cols="12"
              lg="4"
              xl="4"
              md="6"
              sm="12"
              xs="12"
              v-if="labelList[442]?.Field_Visiblity === 'Yes'"
            >
              <v-text-field
                variant="solo"
                :id="'branchLocation'"
                v-model="bankFormData.Branch_Name"
                :rules="[
                  labelList[442].Mandatory_Field === 'Yes'
                    ? required(
                        labelList[442].Field_Alias,
                        bankFormData.Branch_Name
                      )
                    : true,
                  validateWithRulesAndReturnMessages(
                    bankFormData.Branch_Name,
                    'branch',
                    labelList[442].Field_Alias
                  ),
                ]"
                :maxlength="50"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[442].Field_Alias }}
                  <span
                    v-if="labelList[442].Mandatory_Field === 'Yes'"
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" lg="4" xl="4" md="6" sm="12" xs="12">
              <v-text-field
                variant="solo"
                :id="'street'"
                v-model="bankFormData.Street"
                :rules="
                  bankFormData.Street
                    ? [
                        validateWithRulesAndReturnMessages(
                          bankFormData.Street,
                          'bankStreet',
                          'Street'
                        ),
                      ]
                    : [true]
                "
                label="Street"
                :maxlength="100"
                @update:model-value="onChangeFields"
              ></v-text-field>
            </v-col>
            <v-col cols="12" lg="4" xl="4" md="6" sm="12" xs="12">
              <v-text-field
                variant="solo"
                :id="'city'"
                v-model="bankFormData.City"
                label="City"
                :rules="
                  bankFormData.City
                    ? [
                        validateWithRulesAndReturnMessages(
                          bankFormData.City,
                          'city',
                          'City'
                        ),
                      ]
                    : [true]
                "
                :maxlength="50"
                @update:model-value="onChangeFields"
              ></v-text-field>
            </v-col>
            <v-col cols="12" lg="4" xl="4" md="6" sm="12" xs="12">
              <v-text-field
                variant="solo"
                :id="'state'"
                v-model="bankFormData.State"
                label="State"
                :rules="
                  bankFormData.State
                    ? [
                        validateWithRulesAndReturnMessages(
                          bankFormData.State,
                          'state',
                          'State'
                        ),
                      ]
                    : [true]
                "
                :maxlength="20"
                @update:model-value="onChangeFields"
              ></v-text-field>
            </v-col>
            <v-col
              cols="12"
              lg="4"
              xl="4"
              md="6"
              sm="12"
              xs="12"
              v-if="labelList[146].Field_Visiblity === 'Yes'"
            >
              <v-text-field
                variant="solo"
                :id="'pin'"
                v-model="bankFormData.Zip"
                :label="labelList[146].Field_Alias"
                :maxlength="8"
                :rules="[
                  labelList[146].Mandatory_Field === 'Yes'
                    ? required(labelList[146].Field_Alias, bankFormData.Zip)
                    : true,
                  bankFormData.Zip
                    ? validateWithRulesAndReturnMessages(
                        bankFormData.Zip,
                        'pinCode',
                        labelList[146].Field_Alias
                      )
                    : true,
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[146].Field_Alias
                  }}<span
                    style="color: red"
                    v-if="labelList[146].Mandatory_Field === 'Yes'"
                    >*</span
                  >
                </template></v-text-field
              >
            </v-col>
            <v-col
              cols="12"
              lg="4"
              xl="4"
              md="6"
              sm="12"
              xs="12"
              v-if="labelList[346]?.Field_Visiblity?.toLowerCase() === 'yes'"
            >
              <v-text-field
                variant="solo"
                :id="'bankAccountName'"
                v-model="bankFormData.Bank_Account_Name"
                :rules="[
                  labelList[346].Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(
                        labelList[346].Field_Alias,
                        bankFormData.Bank_Account_Name
                      )
                    : true,
                  validateWithRulesAndReturnMessages(
                    bankFormData.Bank_Account_Name,
                    'companyName',
                    labelList[346]?.Field_Alias
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[346].Field_Alias }}
                  <span
                    v-if="
                      labelList[346].Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" lg="4" xl="4" md="6" sm="12" xs="12">
              <v-text-field
                variant="solo"
                :id="'accountNumber'"
                v-model="bankFormData.Bank_Account_Number"
                :maxlength="50"
                :rules="[
                  required(
                    labelList[455]?.Field_Alias || 'Account Number',
                    bankFormData.Bank_Account_Number
                  ),
                  validateWithRulesAndReturnMessages(
                    bankFormData.Bank_Account_Number,
                    'accountNumber',
                    labelList[455]?.Field_Alias || 'Account Number'
                  ),
                ]"
                @update:model-value="onChangeFields"
              >
                <template v-slot:label>
                  {{ labelList[455]?.Field_Alias || "Account Number" }}
                  <span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              cols="12"
              lg="4"
              xl="4"
              md="6"
              sm="12"
              xs="12"
              v-if="labelList[443]?.Field_Visiblity?.toLowerCase() === 'yes'"
            >
              <CustomSelect
                :items="accountTypeList"
                :label="labelList[443].Field_Alias"
                :itemSelected="bankFormData.Account_Type_Id"
                itemValue="Account_Type_Id"
                itemTitle="Account_Type"
                @selected-item="
                  onChangeCustomSelectField($event, 'Account_Type_Id')
                "
                :isAutoComplete="true"
                :isLoading="accountTypeListLoading"
                :noDataText="
                  accountTypeListLoading ? 'Loading...' : 'No data available'
                "
                :rules="[
                  labelList[443].Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(
                        labelList[443].Field_Alias,
                        bankFormData.Account_Type_Id
                      )
                    : true,
                ]"
                :isRequired="
                  labelList[443].Mandatory_Field?.toLowerCase() === 'yes'
                "
              ></CustomSelect>
            </v-col>
            <v-col cols="12" lg="4" xl="4" md="6" sm="12" xs="12">
              <CustomSelect
                :items="creditAccountList"
                label="Credit Account"
                :itemSelected="bankFormData.Credit_Account"
                @selected-item="
                  onChangeCustomSelectField($event, 'Credit_Account')
                "
                :rules="[
                  required('Credit Account', bankFormData.Credit_Account),
                ]"
                :isRequired="true"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" lg="4" xl="4" md="6" sm="12" xs="12">
              <v-text-field
                variant="solo"
                :id="'beneficiaryId'"
                v-model="bankFormData.Beneficiary_Id"
                label="Beneficiary Id"
                :maxlength="8"
                :rules="
                  bankFormData.Beneficiary_Id
                    ? [
                        validateWithRulesAndReturnMessages(
                          bankFormData.Beneficiary_Id,
                          'beneficiaryId',
                          'Beneficiary Id'
                        ),
                      ]
                    : [true]
                "
                @update:model-value="onChangeFields"
              ></v-text-field>
            </v-col>
            <v-col cols="12" lg="4" xl="4" md="6" sm="12" xs="12">
              <CustomSelect
                :items="['Active', 'Inactive']"
                label="Status"
                :itemSelected="bankFormData.Status"
                @selected-item="onChangeCustomSelectField($event, 'Status')"
                :rules="[required('Status', bankFormData.Status)]"
                :isRequired="true"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[449]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              lg="4"
              xl="4"
              md="6"
              sm="12"
              xs="12"
            >
              <v-file-input
                prepend-icon=""
                v-model="fileContent"
                class="text-truncate"
                append-inner-icon="fas fa-paperclip"
                variant="solo"
                :label="labelList[449].Field_Alias"
                accept="image/png, image/jpeg, image/jpg, application/pdf"
                :rules="[
                  ...(labelList[449].Mandatory_Field?.toLowerCase() === 'yes'
                    ? [
                        required(
                          labelList[449].Field_Alias,
                          fileContentRuleValue
                        ),
                      ]
                    : []),
                  fileTypeRule,
                ]"
                @update:modelValue="onChangeFiles"
                @click:clear="removeFiles"
              >
                <template v-slot:label>
                  {{ labelList[449].Field_Alias }}
                  <span
                    v-if="
                      labelList[449].Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-file-input>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <div class="d-flex justify-end">
                <v-btn
                  @click="$emit('close-bank-form')"
                  class="ma-2 pa-2"
                  color="primary"
                  rounded="lg"
                  variant="text"
                  elevation="4"
                  >Cancel</v-btn
                >
                <v-btn
                  color="primary"
                  rounded="lg"
                  :disabled="!isFormDirty"
                  class="ma-2 pa-2"
                  @click="validateBankDetails"
                  >Save</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </div>
  </v-card>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import axios from "axios";
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { LIST_BANK, LIST_ACCOUNT_TYPE } from "@/graphql/dropDownQueries";
import { ADD_UPDATE_BANK_DETAILS } from "@/graphql/onboarding/onboardedIndividualsQueries.js";
import mixpanel from "mixpanel-browser";
import moment from "moment";
import Config from "@/config.js";

export default {
  name: "AddEditBankDetails",
  props: {
    selectedBankDetails: {
      type: Object,
      required: true,
    },
    selectedCandidateId: {
      type: Number,
      default: 0,
    },
  },
  components: { CustomSelect },
  mixins: [validationRules],
  emits: ["refetch-other-details", "close-bank-form"],
  data() {
    return {
      bankFormData: {
        Bank_Id: 0,
        Candidate_Id: 0,
        Bank_Account_Number: "",
        Bank_Name: "",
        Emp_Bank_Id: "",
        Branch_Name: "",
        IFSC_Code: "",
        BSB_Code: null,
        Street: "",
        City: "",
        State: "",
        Zip: "",
        Account_Type_Id: "",
        Status: "Active",
        Account_Type: "",
        bankName: "",
        Beneficiary_Id: "",
        Credit_Account: "Salary Account",
        Bank_Account_Name: "",
        File_Name: "",
      },
      fileContent: null,
      isFileChanged: false,
      retrievedFileName: "",
      openModal: false,
      // edit
      isFormDirty: false,
      isLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      formType: "",
      // list
      creditAccountList: ["Salary Account", "Reimbursement Account"],
      accountTypeList: [],
      accountTypeListLoading: false,
      bankNameList: [],
      bankNameListLoading: false,
    };
  },

  computed: {
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    fileContentRuleValue() {
      return this.fileContent && this.fileContent.name
        ? this.fileContent.name
        : null;
    },
    currentTimeStamp() {
      return moment().valueOf();
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    domainName() {
      return this.$store.getters.domain;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    if (
      this.selectedBankDetails &&
      Object.keys(this.selectedBankDetails).length > 0
    ) {
      this.bankFormData = JSON.parse(JSON.stringify(this.selectedBankDetails));
      this.formType = "edit";

      // Handle existing file
      if (this.bankFormData?.File_Name) {
        this.fileContent = {
          name: this.formattedFileName(this.bankFormData.File_Name),
        };
      }
    } else {
      this.formType = "add";
    }
    this.retrieveBankLists();
    this.retrieveAccountTypes();
  },

  methods: {
    async onChangedIfsc() {
      const ifscValid = await this.$refs.bankIfsc.validate();
      if (
        ifscValid.length === 0 &&
        this.labelList[135]?.Field_Alias?.toLowerCase() === "ifsc code"
      ) {
        axios
          .get("https://ifsc.razorpay.com/" + this.bankFormData.IFSC_Code, {
            crossdomain: true,
          })
          .then((res) => {
            mixpanel.track("Onboarded-candidate-other-bank-ifsc-fetch-success");
            if (res && res.data) {
              this.bankFormData["City"] = res.data.CITY;
              this.bankFormData["Street"] = res.data.ADDRESS;
              this.bankFormData["State"] = res.data.STATE;
              this.bankFormData["Branch_Name"] = res.data.BRANCH;
              const selectedBank = this.bankNameList.filter((item) => {
                let bName = item.Bank_Name.toLowerCase();
                let ifscBankName = res.data.BANK.toLowerCase();
                return bName === ifscBankName;
              });
              if (selectedBank && selectedBank.length > 0) {
                this.bankFormData["Emp_Bank_Id"] = selectedBank[0].Bank_Id;
              }
            }
          })
          .catch(() => {
            mixpanel.track("Onboarded-candidate-other-bank-ifsc-fetch-error");
            let snackbarData = {
              isOpen: true,
              message: "Invalid " + this.labelList[135].Field_Alias,
              type: "warning",
            };
            this.showAlert(snackbarData);
          });
      }
      this.onChangeFields();
    },

    onChangeFields() {
      this.isFormDirty = true;
    },

    onChangeCustomSelectField(value, field) {
      this.onChangeFields();
      this.bankFormData[field] = value;
    },

    onChangeFiles() {
      mixpanel.track("Onboarded-candidate-bank-file-changed");
      if (this.fileContent && this.fileContent.name) {
        this.bankFormData.File_Name =
          this.selectedCandidateId +
          "?" +
          this.currentTimeStamp +
          "?1?" +
          this.fileContent?.name;
        this.isFileChanged = true;
        this.onChangeFields();
      }
    },

    removeFiles() {
      mixpanel.track("Onboarded-candidate-bank-file-removed");
      this.fileContent = null;
      this.bankFormData.File_Name = "";
      this.onChangeFields();
    },

    retrieveDocuments(fileName) {
      let vm = this;
      vm.retrievedFileName = fileName;
      vm.openModal = true;
    },

    formattedFileName(fileName) {
      if (fileName) {
        let fileNameChunks = fileName.split("?");
        return fileNameChunks && fileNameChunks.length > 0
          ? fileNameChunks[3]
          : "File Name";
      }
      return "";
    },

    async validateBankDetails() {
      const { valid } = await this.$refs.addEditBankForm.validate();
      mixpanel.track("Onboarded-candidate-other-bank-submit-click");

      if (valid) {
        this.validateDocuments();
      }
    },

    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        if (this.fileContent && this.fileContent.size && this.isFileChanged) {
          await this.uploadFileContents(this.fileContent);
        }
        this.updateBankDetails();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },

    async uploadFileContents() {
      mixpanel.track("Onboarded-candidate-bank-file-upload-start");
      let vm = this;
      let fileUploadUrl = `${this.domainName}/${this.orgCode}/Employee Bank Details/`;
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + this.bankFormData.File_Name,
          action: "upload",
          type: "documents",
          fileContent: vm.fileContent,
        })
        .catch((error) => {
          throw error;
        });
    },

    updateBankDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_BANK_DETAILS,
          variables: {
            candidateId: vm.selectedCandidateId,
            bankId: vm.bankFormData.Bank_Id,
            bankAccountNumber: vm.bankFormData.Bank_Account_Number,
            empBankId: vm.bankFormData.Emp_Bank_Id
              ? vm.bankFormData.Emp_Bank_Id
              : 0,
            branchName: vm.bankFormData.Branch_Name,
            ifsc: vm.bankFormData.IFSC_Code,
            bsb: vm.bankFormData.BSB_Code,
            bank_streetName: vm.bankFormData.Street,
            bank_city: vm.bankFormData.City,
            bank_state: vm.bankFormData.State,
            bank_pinCode: vm.bankFormData.Zip,
            accountType: vm.bankFormData.Account_Type_Id
              ? vm.bankFormData.Account_Type_Id
              : vm.labelList[443].Field_Visiblity === "Yes"
              ? 0
              : 1,
            creditAccount: vm.bankFormData.Credit_Account,
            beneficiaryId: vm.bankFormData.Beneficiary_Id,
            status: vm.bankFormData.Status,
            bankAccountName: vm.bankFormData.Bank_Account_Name,
            fileName: vm.bankFormData.File_Name || "",
          },
          client: "apolloClientW",
        })
        .then(() => {
          mixpanel.track("Onboarded-candidate-other-bank-edit-success");
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message:
              vm.formType === "edit"
                ? "Bank details updated successfully"
                : "Bank details added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-other-details");
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },

    handleUpdateError(err = "") {
      mixpanel.track("Onboarded-candidate-other-bank-edit-error");
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.formType === "edit" ? "updating" : "adding",
          form: "bank details",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    retrieveBankLists() {
      let vm = this;
      vm.bankNameListLoading = true;
      vm.$apollo
        .query({
          query: LIST_BANK,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listBankDetails &&
            !response.data.listBankDetails.errorCode
          ) {
            const { bankDetails } = response.data.listBankDetails;
            vm.bankNameList =
              bankDetails && bankDetails.length > 0 ? bankDetails : [];
          }
          vm.bankNameListLoading = false;
        })
        .catch(() => {
          vm.bankNameListLoading = false;
        });
    },

    retrieveAccountTypes() {
      let vm = this;
      vm.accountTypeListLoading = true;
      vm.$apollo
        .query({
          query: LIST_ACCOUNT_TYPE,
          client: "apolloClientI",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listAccountType &&
            !response.data.listAccountType.errorCode
          ) {
            const { accountType } = response.data.listAccountType;
            vm.accountTypeList =
              accountType && accountType.length > 0 ? accountType : [];
          }
          vm.accountTypeListLoading = false;
        })
        .catch(() => {
          vm.accountTypeListLoading = false;
        });
    },
  },
};
</script>
