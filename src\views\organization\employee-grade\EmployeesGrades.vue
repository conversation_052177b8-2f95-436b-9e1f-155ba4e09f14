<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row
            v-if="originalList.length > 0 && !showAddEditForm"
            justify="center"
          >
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="false"
              />
              <FormFilter
                :originalList="originalList"
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="employeegrades">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList('Grade error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="originalList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="Grade refers to a system of classifying jobs or employees into levels or bands based on factors such as skills, responsibilities, experience, and compensation. This grading system helps organizations standardize their compensation practices, manage career progression, and ensure equitable treatment of employees."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="By providing a structured framework, they help ensure fairness, motivate employees, and support strategic HR initiatives."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="These features enable organizations to manage their workforce effectively, align employee goals with business objectives, and maintain competitive and compliant HR practices."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 secondary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span class="primary">Add Grade</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      rounded="lg"
                      color="transparent"
                      variant="flat"
                      class="mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && originalList.length"
            main-title="There are no grade matched for the selected filters/searches."
            image-name="common/no-records"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div
              v-if="!isSmallTable"
              class="d-flex align-center my-3"
              :class="isMobileView ? 'justify-center ' : 'justify-end'"
            >
              <v-btn
                v-if="formAccess.add"
                prepend-icon="fas fa-plus"
                variant="elevated"
                class="mx-1 secondary"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="openAddForm()"
              >
                <template v-slot:prepend>
                  <v-icon></v-icon>
                </template>
                <span class="primary">Add New</span>
              </v-btn>
              <v-btn
                color="transparent"
                class="mt-1"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList('Refetch List')"
                ><v-icon>fas fa-redo-alt</v-icon></v-btn
              >
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn
                    variant="plain"
                    class="mt-1 ml-n3 mr-n5"
                    v-bind="props"
                  >
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action.key"
                    @click="onMoreAction(action.key)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'pink-lighten-5': isHovering,
                          }"
                          ><v-icon size="15" class="pr-2">{{
                            action.icon
                          }}</v-icon
                          >{{ action.key }}</v-list-item-title
                        >
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>

            <v-row>
              <v-col
                v-if="originalList.length > 0"
                class="mb-12"
                :cols="isSmallTable && windowWidth >= 1264 ? 6 : 12"
              >
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                  :sort-by="[{ key: 'grade', order: 'asc' }]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="openViewForm(item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView
                          ? 'v-data-table__mobile-table-row ma-0 mt-2'
                          : ''
                      "
                    >
                      <td id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold mt-2">
                          Grade
                        </div>
                        <section class="d-flex align-center">
                          <div
                            v-if="
                              isSmallTable &&
                              !isMobileView &&
                              selectedItem &&
                              selectedItem.gradeId === item.gradeId
                            "
                            class="data-table-side-border d-flex"
                          ></div>
                          <div style="max-width: 200px" class="text-truncate">
                            <span
                              class="text-primary text-body-2 font-weight-medium"
                            >
                              <v-tooltip :text="item.grade" location="bottom">
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-bind="
                                      item.grade && item.grade.length > 20
                                        ? props
                                        : ''
                                    "
                                    >{{ item.grade }}</span
                                  >
                                </template>
                              </v-tooltip>
                              <v-tooltip
                                :text="item.gradeCode"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <div
                                    v-if="item.gradeCode"
                                    v-bind="
                                      item.gradeCode &&
                                      item.gradeCode.length > 20
                                        ? props
                                        : ''
                                    "
                                    class="text-grey"
                                  >
                                    {{ checkNullValue(item.gradeCode) }}
                                  </div>
                                </template>
                              </v-tooltip>
                            </span>
                          </div>
                        </section>
                      </td>
                      <td v-if="isSmallTable" id="mobile-view-td">
                        <div
                          id="mobile-header"
                          class="font-weight-bold text-truncate"
                          style="max-width: 200px"
                        >
                          Parent Grade
                        </div>
                        <section
                          class="text-body-2 text-primary text-truncate"
                          style="max-width: 200px"
                        >
                          {{ checkNullValue(item.parentGrade) }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div
                          id="mobile-header"
                          class="font-weight-bold text-truncate"
                          style="max-width: 200px"
                        >
                          Parent Grade
                        </div>
                        <section
                          class="text-body-2 text-primary text-truncate"
                          style="max-width: 200px"
                        >
                          {{ checkNullValue(item.parentGrade) }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Minimum Annual Salary
                        </div>
                        <section class="text-body-2 text-primary">
                          {{
                            item.minAnnualSalary !== null
                              ? item.minAnnualSalary
                              : "-"
                          }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Maximum Annual Salary
                        </div>
                        <section class="text-body-2 text-primary">
                          {{
                            item.maxAnnualSalary !== null
                              ? item.maxAnnualSalary
                              : "-"
                          }}
                        </section>
                      </td>

                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Eligible for Overtime
                        </div>
                        <section class="text-body-2 text-primary">
                          {{ mapEligibleOvertime(item.eligibleOvertime) }}
                        </section>
                      </td>

                      <td
                        v-if="!isSmallTable"
                        class="text-body-2 text-end"
                        style="width: 150px"
                      >
                        <ActionMenu
                          @selected-action="onActions($event, item)"
                          :actions="['Edit', 'Delete']"
                          :access-rights="formAccess"
                        ></ActionMenu>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
              <v-col
                :cols="originalList.length === 0 ? 12 : 6"
                v-if="isSmallTable && windowWidth >= 1264"
              >
                <AddEditEmployeesGrade
                  v-if="showAddEditForm"
                  :isEdit="isEdit"
                  :editFormData="selectedItem"
                  :access-rights="formAccess"
                  @close-form="closeAllForms()"
                  @form-updated="
                    closeEditForm('Grade was added/updated', $event)
                  "
                ></AddEditEmployeesGrade>
                <ViewEmployeeGrade
                  v-else
                  :selectedItem="selectedItem"
                  :access-rights="formAccess"
                  @close-form="closeAllForms()"
                  @open-edit-form="openEditForm()"
                ></ViewEmployeeGrade>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <v-dialog
      v-if="openFormInModal"
      :model-value="openFormInModal"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditEmployeesGrade
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :formAccess="formAccess"
        @close-form="closeAllForms()"
        @form-updated="closeEditForm('Grade was added/updated', $event)"
      ></AddEditEmployeesGrade>
      <ViewEmployeeGrade
        v-else
        :selectedItem="selectedItem"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      ></ViewEmployeeGrade>
    </v-dialog>
    <!-- <div v-else-if="isImportModel">
      <GradesImport
        @close-import-model="closeImportModel()"
        @refetch-data="refetchList()"
        :backupMainList="backupMainList"
      ></GradesImport>
    </div> -->
    <AppLoading v-if="isLoading"></AppLoading>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      iconName="fas fa-trash"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDeleteGrades()"
    ></AppWarningModal>
    <AppWarningModal
      v-if="openRedirectModal"
      :open-modal="openRedirectModal"
      iconName="fas fa-check-circle"
      closeButtonText=""
      acceptButtonText=""
      confirmationHeading="Grade Added Successfully!"
      iconColor="green"
      @close-warning-modal="openRedirectModal = false"
    >
      <template v-slot:warningModalContent>
        <div class="text-center mt-3">
          The new grade must be associated with Timesheet Hours to enable
          employees to mark attendance, apply for leave, and ensure payslip
          generation. Kindly proceed by clicking this
          <a :href="this.baseUrl + 'employees/timesheets'">link</a>
        </div>
      </template>
    </AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";

// Async components
const ViewEmployeeGrade = defineAsyncComponent(() =>
  import("./ViewEmployeesGrades.vue")
);
const AddEditEmployeesGrade = defineAsyncComponent(() =>
  import("./AddEditEmployeesGrades.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
// const GradesImport = defineAsyncComponent(() =>
//   import("./EmployeeGradeImport.vue")
// );

// Synchronous components
const FormFilter = defineAsyncComponent(() =>
  import("./EmployeeGradeFilter.vue")
);
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

// Queries
import {
  LIST_EMPLOYEE_GRADE,
  DELETE_EMPLOYEE_GRADE,
} from "@/graphql/organisation/grades/gradeQueries";

import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import OrgStructureTabTranslationMixin from "@/mixins/OrgStructureTabTranslationMixin";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "EmployeegradeDetails",
  components: {
    FormFilter,
    EmployeeDefaultFilterMenu,
    AddEditEmployeesGrade,
    ViewEmployeeGrade,
    // GradesImport,
    NotesCard,
    ActionMenu,
  },
  mixins: [FileExportMixin, OrgStructureTabTranslationMixin],
  data() {
    return {
      // List data
      listLoading: false,
      itemList: [],
      originalList: [],
      isErrorInList: false,
      errorContent: "",
      openWarningModal: false,
      openMoreMenu: false,
      isLoading: false,
      isEdit: false,
      showAddEditForm: false,
      selectedItem: null,
      showViewForm: false,
      resetFilterCount: 0,
      // Tab data
      // isImportModel: false,
      currentTabItem: "",
      isEmployeeTypeLoading: false,
      editFormData: {},
      openRedirectModal: false,
    };
  },
  computed: {
    landedFormName() {
      return "Grades";
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights("20");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    orgStructureFormAccess() {
      return this.$store.getters.orgStructureFormAccess;
    },
    organizationGroupFormName() {
      let projectForm = this.accessRights("269");
      if (
        projectForm &&
        projectForm.customFormName &&
        projectForm.customFormName !== ""
      ) {
        return projectForm.customFormName;
      } else return "Organization Group";
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.orgStructureFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        if (formAccessArray && formAccessArray.includes("Organization Group")) {
          const index = formAccessArray.indexOf("Organization Group");
          formAccessArray[index] = this.organizationGroupFormName;
        }

        // Translate form names using the mixin
        return this.translateFormAccessArray(formAccessArray);
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
        // {
        //   key: "Import",
        //   icon: "fas fa-file-import",
        // },
      ];
    },
    isSmallTable() {
      return this.showViewForm || this.showAddEditForm;
    },
    tableHeaders() {
      return this.isSmallTable
        ? [
            {
              title: "Grade",
              align: "start",
              key: "grade",
            },
            {
              title: "Parent Grade",
              key: "parentGrade",
            },
          ]
        : [
            {
              title: "Grade",
              align: "start",
              key: "grade",
            },
            {
              title: "Parent Grade",
              key: "parentGrade",
            },
            {
              title: "Minimum Annual Salary",
              key: "minAnnualSalary",
            },
            {
              title: "Maximum Annual Salary",
              key: "maxAnnualSalary",
            },
            {
              title: "Overtime Eligibility",
              key: "eligibleOvertime",
            },
            {
              title: "Actions",
              key: "action",
              align: "end",
              sortable: false,
            },
          ];
    },
    emptyScenarioMsg() {
      return this.originalList.length
        ? "There are no grade for the selected filters/searches."
        : "";
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    openFormInModal() {
      if (this.isSmallTable && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
  },
  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },
  errorCaptured(err, vm, info) {
    const url = window.employeegrade.href;
    let msg =
      "Something went wrong while loading the grade. Please try after some time.";
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = `${err} ${info}`;
    }
    this.showAlert({
      isOpen: false,
      message: msg,
      type: "warning",
    });
    return false;
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.selectedFormData = Object.assign({}, newData);
      },
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_EMPLOYEE_GRADE,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listEmployeeGrade.listEmployeeGradeData
          ) {
            const responseData =
              response.data.listEmployeeGrade.listEmployeeGradeData;
            vm.itemList = responseData;
            vm.originalList = responseData;
            vm.listLoading = false;
            mixpanel.track("Grade list retrieved");
          } else {
            vm.handleGradeListError();
          }
        })
        .catch((err) => {
          vm.handleGradeListError(err);
        });
    },

    mapEligibleOvertime(value) {
      return value === 1 ? "Yes" : "No";
    },

    handleGradeListError(err = "") {
      mixpanel.track("Grade error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Grades",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedItem = null;
    },

    onDeleteGrades() {
      let vm = this;
      vm.isLoading = true;
      const { gradeId } = this.selectedItem;
      vm.$apollo
        .mutate({
          mutation: DELETE_EMPLOYEE_GRADE,
          variables: {
            gradeId: gradeId,
          },
          client: "apolloClientBB",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: "Grade deleted successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.refetchList("Grade deleted");
          vm.openWarningModal = false;
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      mixpanel.track("Grade-delete-error");

      this.isLoading = false;

      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "grades",
        isListError: false,
      });

      this.openWarningModal = false;
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.itemList;
        searchItems = searchItems.filter((item) => {
          let newObj = {
            grade: item.grade,
            gradeCode: item.gradeCode,
            parentGrade: item.parentGrade,
            minAnnualSalary: item.minAnnualSalary,
            maxAnnualSalary: item.maxAnnualSalary,
            eligibleOvertime:
              item.eligibleOvertime !== null &&
              item.eligibleOvertime !== undefined
                ? item.eligibleOvertime === 1
                  ? "yes"
                  : "no"
                : "",
          };
          return Object.keys(newObj).some((k) => {
            if (newObj[k]) {
              let fieldValue = newObj[k].toString().toLowerCase();
              if (k === "eligibleOvertime") {
                fieldValue = item.eligibleOvertime === 1 ? "yes" : "no";
              }
              return fieldValue.includes(searchValue);
            }
            return false;
          });
        });
        this.itemList = searchItems;
      }
    },

    applyFilter(filteredArray) {
      this.itemList = filteredArray;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    resetFilter() {
      this.itemList = this.originalList;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef?.resetAllModelValues();
    },
    openEditForm() {
      mixpanel.track("Grade edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    openViewForm(item) {
      mixpanel.track("Grade form opened");
      this.selectedItem = item;
      this.showViewForm = true;
    },
    openAddForm() {
      mixpanel.track("Grade add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    closeAllForms() {
      mixpanel.track("Grade all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.openWarningModal = false;
    },
    onTabChange(tab) {
      mixpanel.track("Grades form tab changed");
      // Use the mixin method for handling translated tabs
      this.onTabChangeWithTranslation(tab);
    },
    closeEditForm(msg, isUpdate) {
      if (!isUpdate) {
        this.openRedirectModal = true;
      }
      this.refetchList(msg);
    },
    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
      this.resetFilter();
    },
    // openImportModel() {
    //   this.isImportModel = true;
    // },
    // closeImportModel() {
    //   this.isImportModel = false;
    // },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        mixpanel.track("Employee-Grade-export-click");
        this.exportReportFile();
      }
      // else if (actionType === "Import") {
      //   this.openImportModel();
      // }
      this.openMoreMenu = false;
    },
    onActions(type, item) {
      if (type && type.toLowerCase() === "delete") {
        this.onDelete(item);
      } else {
        this.onEdit(item);
      }
    },

    onEdit(item) {
      this.selectedItem = item;
      this.openEditForm();
    },

    onDelete(item) {
      this.selectedItem = item;
      this.openWarningModal = true;
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Grade Code", key: "gradeCode" },
        { header: "Employee Grade", key: "grade" },
        { header: "Parent Grade", key: "parentGrade" },
        { header: "Minimum Annual Salary", key: "minAnnualSalary" },
        { header: "Maximum Annual Salary", key: "maxAnnualSalary" },
        { header: "Minimum Hourly Wage", key: "minHourWages" },
        { header: "Maximum Hourly Wage", key: "maxHourWages" },
        {
          header: "Minimum Overtime Hourly Wage",
          key: "minOvertimeWages",
        },
        {
          header: "Maximum Overtime Hourly Wage",
          key: "maxOvertimeWages",
        },
        { header: "Overtime Eligibility", key: "eligibleOvertime" },
        { header: "Overtime Allocation", key: "overTimeAllocation" },
        { header: "Overtime Fixed Amount", key: "overTimeFixedAmount" },
        { header: "Wage Index", key: "overTimeWageIndex" },
        { header: "Description", key: "description" },
        { header: "Added On", key: "addedOn" },
        { header: "Added By", key: "addedByName" },
        { header: "Updated On", key: "updatedOn" },
        { header: "Updated By", key: "updatedByName" },
      ];
      if (this.entomoIntegrationEnabled && this.isEntomoSyncTypePush) {
        exportHeaders.unshift({
          header: "Level",
          key: "level",
        });
      }

      const exportList = this.itemList.map((item) => ({
        level: item.level,
        gradeCode: item.gradeCode,
        grade: item.grade,
        parentGrade: item.parentGrade,
        minAnnualSalary: item.minAnnualSalary,
        maxAnnualSalary: item.maxAnnualSalary,
        minHourWages: item.minHourWages,
        maxHourWages: item.maxHourWages,
        minOvertimeWages: item.minOvertimeWages,
        maxOvertimeWages: item.maxOvertimeWages,
        eligibleOvertime: item.eligibleOvertime,
        overTimeAllocation: item.overTimeAllocation,
        overTimeFixedAmount: item.overTimeFixedAmount,
        overTimeWageIndex: item.overTimeWageIndex,
        description: item.description,
        addedOn: item.addedOn ? this.convertUTCToLocal(item.addedOn) : "",
        addedByName: item.addedByName,
        updatedOn: item.updatedOn ? this.convertUTCToLocal(item.updatedOn) : "",
        updatedByName: item.updatedByName,
      }));

      const exportOptions = {
        fileExportData: exportList,
        fileName: "Grades",
        sheetName: "Grades",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
      mixpanel.track("Grades-Details-exported");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.employeegrades {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
.notification-bar {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.new-badge {
  background-color: #ff6f61;
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  font-weight: bold;
  margin-right: 10px;
}

@media screen and (max-width: 805px) {
  .employeegrades {
    padding: 4em 1em 0em 1em;
  }
}
</style>
