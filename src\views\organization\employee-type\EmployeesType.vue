<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <!-- Top bar content for filtering -->
        <template v-slot:topBarContent>
          <v-row
            v-if="originalList && originalList.length > 0 && !showAddEditForm"
            justify="center"
          >
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="false"
              />
              <FormFilter
                :originalList="originalList"
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <!-- Main content area -->
    <v-container fluid class="employeetype">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <!-- Loading skeleton or error screens -->
          <div v-if="listLoading" class="mt-3">
            <!-- Skeleton loaders -->
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <!-- Error screens for fetching data -->
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList('Employee Type error refetch')"
          >
          </AppFetchErrorScreen>

          <!-- No results screen -->
          <AppFetchErrorScreen
            v-else-if="originalList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      notes="Employee type typically refers to the classification of employees based on their work arrangement, contract status, or role within the organization. Common employee types include : Full-Time Employees, Part-Time Employees, Consultant, Intern etc."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="This classification helps in organizing, managing, and applying different policies and benefits to various categories of employees."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="It also assists organizations in managing payroll, benefits, compliance, and other HR functions more effectively."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 secondary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span class="primary">Add Employee Type</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      rounded="lg"
                      color="transparent"
                      variant="flat"
                      class="mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <!-- No matching employee Type found -->
          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && originalList.length"
            :main-title="'There are no Employee Type matched for the selected filters/searches.'"
            image-name="common/no-records"
          >
            <template v-slot:contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <!-- Button to reset filter/search -->
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <!-- Content area for employee Type -->
          <div v-else>
            <!-- Buttons for actions like add, refetch, and more actions -->
            <div
              v-if="originalList.length > 0 && !isSmallTable"
              class="d-flex flex-wrap align-center my-2"
              :class="isMobileView ? 'flex-column' : ''"
              style="justify-content: space-between"
            >
              <div
                class="d-flex align-center flex-wrap"
                :class="isMobileView ? 'justify-center' : ''"
              >
                <v-btn
                  rounded="lg"
                  style="pointer-events: none"
                  variant="flat"
                  :size="windowWidth <= 960 ? 'small' : 'default'"
                  >Active:
                  <span class="text-green font-weight-bold">{{
                    activeEmployees
                  }}</span>
                </v-btn>
                <v-btn
                  rounded="lg"
                  style="pointer-events: none"
                  variant="flat"
                  class="ml-2"
                  :size="windowWidth <= 960 ? 'small' : 'default'"
                  >InActive:
                  <span class="text-red font-weight-bold">{{
                    inactiveEmployees
                  }}</span></v-btn
                >
              </div>
              <div
                v-if="!isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  v-if="formAccess.add"
                  prepend-icon="fas fa-plus"
                  variant="elevated"
                  class="mx-1 secondary"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="openAddForm()"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  <span class="primary">Add New</span>
                </v-btn>
                <v-btn
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                  ><v-icon>fas fa-redo-alt</v-icon></v-btn
                >
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>

            <!-- Data table for displaying employee Type -->
            <v-row>
              <v-col
                v-if="originalList.length > 0"
                class="mb-12"
                :cols="isSmallTable && windowWidth >= 1264 ? 6 : 12"
              >
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                  :sort-by="[{ key: 'Employee_Type', order: 'asc' }]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="openViewForm(item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView
                          ? 'v-data-table__mobile-table-row ma-0 mt-2'
                          : ''
                      "
                    >
                      <td id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold mt-2">
                          Employee Type
                        </div>
                        <section class="d-flex align-center">
                          <div
                            v-if="
                              isSmallTable &&
                              !isMobileView &&
                              selectedItem &&
                              selectedItem.EmpType_Id === item.EmpType_Id
                            "
                            class="data-table-side-border d-flex"
                          ></div>

                          <div style="max-width: 200px" class="text-truncate">
                            <span
                              class="text-primary text-body-2 font-weight-medium"
                            >
                              <v-tooltip
                                :text="item.Employee_Type"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <span
                                    v-bind="
                                      item.Employee_Type &&
                                      item.Employee_Type.length > 20
                                        ? props
                                        : ''
                                    "
                                    >{{ item.Employee_Type }}</span
                                  >
                                </template>
                              </v-tooltip>
                              <v-tooltip
                                :text="item.Employee_Type_Code"
                                location="bottom"
                              >
                                <template v-slot:activator="{ props }">
                                  <div
                                    v-if="item.Employee_Type_Code"
                                    v-bind="
                                      item.Employee_Type_Code &&
                                      item.Employee_Type_Code.length > 20
                                        ? props
                                        : ''
                                    "
                                    class="text-grey"
                                  >
                                    {{
                                      checkNullValue(item.Employee_Type_Code)
                                    }}
                                  </div>
                                </template>
                              </v-tooltip>
                            </span>
                          </div>
                        </section>
                      </td>
                      <td v-if="isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Work Schedule
                        </div>
                        <section class="text-body-2 text-primary">
                          {{ checkNullValue(item.Work_Schedule) }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Work Schedule
                        </div>
                        <section class="text-body-2 text-primary">
                          {{ checkNullValue(item.Work_Schedule) }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Eligible for Benefits
                        </div>
                        <section class="text-body-2 text-primary">
                          {{ item.Benefits_Applicable_Value }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Holiday Eligibility
                        </div>
                        <section class="text-body-2 text-primary">
                          {{ item.Holiday_Eligibility_Value }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Salary Calculation Days
                        </div>
                        <section class="text-body-2 text-primary">
                          {{ checkNullValue(item.Salary_Calc_Days_Flag) }}
                        </section>
                      </td>
                      <td v-if="!isSmallTable" id="mobile-view-td">
                        <div id="mobile-header" class="font-weight-bold">
                          Status
                        </div>
                        <section class="text-body-2 text-primary">
                          <!-- Toggle button for Active/Inactive -->
                          <AppToggleButton
                            button-active-text="Active"
                            button-inactive-text="InActive"
                            button-active-color="#7de272"
                            button-inactive-color="red"
                            id-value="gab-analysis-based-on"
                            :current-value="
                              item.EmployeeType_Status === 'Active'
                            "
                            @chosen-value="
                              (EmployeeType_Status) =>
                                updateStatus(EmployeeType_Status, item)
                            "
                            :isDisableToggle="!formAccess.update || isLoading"
                            :tooltipContent="
                              formAccess.update
                                ? ''
                                : `Sorry, you don't have access rights to update the status`
                            "
                          ></AppToggleButton>
                        </section>
                      </td>

                      <td
                        v-if="!isSmallTable"
                        class="text-body-2 text-end"
                        style="width: 150px"
                      >
                        <ActionMenu
                          @selected-action="onActions($event, item)"
                          :actions="['Edit', 'Delete']"
                          :access-rights="formAccess"
                        ></ActionMenu>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
              <v-col
                :cols="originalList.length === 0 ? 12 : 6"
                v-if="isSmallTable && windowWidth >= 1264"
              >
                <AddEditEmployeesType
                  v-if="showAddEditForm"
                  :isEdit="isEdit"
                  :editFormData="selectedItem"
                  :access-rights="formAccess"
                  :landedFormName="landedFormName"
                  @close-form="closeAllForms()"
                  @form-updated="refetchList('Employee Type was added/updated')"
                ></AddEditEmployeesType>
                <ViewEmployeeType
                  v-else
                  :selectedItem="selectedItem"
                  :access-rights="formAccess"
                  @close-form="closeAllForms()"
                  @open-edit-form="openEditForm()"
                ></ViewEmployeeType>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <v-dialog
      v-if="openFormInModal"
      :model-value="openFormInModal"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditEmployeesType
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :formAccess="formAccess"
        @close-form="closeAllForms()"
        @form-updated="refetchList('Employee Type was added/updated')"
      ></AddEditEmployeesType>
      <ViewEmployeeType>
        v-else :selectedItem="selectedItem" :access-rights="formAccess"
        @close-form="closeAllForms()" @open-edit-form="openEditForm()"
        ></ViewEmployeeType
      >
    </v-dialog>
    <!-- <div v-else-if="isImportModel">
      <EmployeesTypeImport
        @close-import-model="closeImportModel()"
        @refetch-data="refetchList()"
        :backupMainList="backupMainList"
      ></EmployeesTypeImport>
    </div> -->
    <AppLoading v-if="isLoading"></AppLoading>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      iconName="fas fa-trash"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onDeleteEmployeeType()"
    ></AppWarningModal>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const ViewEmployeeType = defineAsyncComponent(() =>
  import("./ViewEmployeesType.vue")
);
const AddEditEmployeesType = defineAsyncComponent(() =>
  import("./AddEditEmployeesType.vue")
);
// Async Components
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const FormFilter = defineAsyncComponent(() =>
  import("./EmployeesTypeFilter.vue")
);
// const EmployeesTypeImport = defineAsyncComponent(() =>
//   import("./EmployeesTypeImport.vue")
// );

// GraphQL Queries
import {
  LIST_EMPLOYEE_TYPES,
  ADD_UPDATE_EMPLOYEE_TYPE,
  DELETE_EMPLOYEE_TYPE,
} from "@/graphql/organisation/employeetype/employeeTypeQueries";
import mixpanel from "mixpanel-browser";
import Config from "@/config.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import OrgStructureTabTranslationMixin from "@/mixins/OrgStructureTabTranslationMixin";
import { checkNullValue, convertUTCToLocal } from "@/helper.js";

export default {
  name: "EmployeesTypeDetails",
  components: {
    NotesCard,
    ViewEmployeeType,
    AddEditEmployeesType,
    ActionMenu,
    // EmployeesTypeImport,
    EmployeeDefaultFilterMenu,
    FormFilter,
  },
  mixins: [FileExportMixin, OrgStructureTabTranslationMixin],
  data: () => ({
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    openWarningModal: false,
    openMoreMenu: false,
    isLoading: false,
    isEdit: false,
    showAddEditForm: false,
    selectedItem: null,
    showViewForm: false,
    currentTabItem: "",
    editFormData: {},
    isImportModel: false,
    openMoreDetails: false,
    resetFilterCount: 0,
  }),
  computed: {
    landedFormName() {
      let employeetype = this.accessRights("17");
      if (employeetype && employeetype.customFormName) {
        return employeetype.customFormName;
      } else return "Employee Type";
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights("27");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    orgStructureFormAccess() {
      return this.$store.getters.orgStructureFormAccess;
    },
    organizationGroupFormName() {
      let projectForm = this.accessRights("269");
      if (
        projectForm &&
        projectForm.customFormName &&
        projectForm.customFormName !== ""
      ) {
        return projectForm.customFormName;
      } else return "Organization Group";
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.orgStructureFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        if (formAccessArray && formAccessArray.includes("Organization Group")) {
          const index = formAccessArray.indexOf("Organization Group");
          formAccessArray[index] = this.organizationGroupFormName;
        }

        // Translate form names using the mixin
        return this.translateFormAccessArray(formAccessArray);
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
        // {
        //   key: "Import",
        //   icon: "fas fa-file-import",
        // },
      ];
      return actions;
    },
    isSmallTable() {
      return this.showViewForm || this.showAddEditForm;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Employee Type",
            align: "start",
            key: "Employee_Type",
          },
          {
            title: "Work Schedule",
            key: "Work_Schedule",
          },
        ];
      } else {
        return [
          {
            title: "Employee Type",
            align: "start",
            key: "Employee_Type",
          },
          {
            title: "Work Schedule",
            key: "Work_Schedule",
          },
          {
            title: "Eligible for Benefits",
            key: "Benefits_Applicable",
          },
          {
            title: "Holiday Eligibility",
            key: "Holiday_Eligiblity",
          },
          {
            title: "Salary Calculation Days",
            key: "Salary_Calc_Days_Flag",
          },
          {
            title: "Status",
            key: "EmployeeType_Status",
          },
          {
            title: "Actions",
            key: "action",
            align: "end",
            sortable: false,
          },
        ];
      }
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    isEntomoSyncTypePush() {
      return this.$store.state.isEntomoSyncTypePush;
    },
    emptyScenarioMsg() {
      return this.originalList.length
        ? "There are no Employee Type for the selected filters/searches."
        : "";
    },
    mixPanelId() {
      return this.$store.getters.mixPanelId;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    openFormInModal() {
      if (this.isSmallTable && this.windowWidth < 1264) {
        return true;
      }
      return false;
    },
    activeEmployees() {
      let typeList = this.originalList.filter(
        (el) => el.EmployeeType_Status === "Active"
      );
      return typeList.length;
    },
    inactiveEmployees() {
      let typeList = this.originalList.filter(
        (el) => el.EmployeeType_Status === "InActive"
      );
      return typeList.length;
    },
  },

  mounted() {
    mixpanel.init(Config.mixPanelToken, {
      debug: true,
      track_pageview: true,
      persistence: "localStorage",
    });
    mixpanel.identify(this.mixPanelId);
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },
  // errorCaptured(err, vm, info) {
  //   const url = window.employeetype.href;
  //   let msg =
  //     "Something went wrong while loading the employee type. Please try after some time.";
  //   if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
  //     msg = `${err} ${info}`;
  //   }
  //   this.showAlert({
  //     isOpen: false,
  //     message: msg,
  //     type: "warning",
  //   });
  //   return false;
  // },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    mapBinary(value) {
      return value === 1 ? "Yes" : "No";
    },
    updateStatus(EmployeeType_Status, item) {
      let vm = this;
      // Extract the boolean value from the array
      const newStatus = Array.isArray(EmployeeType_Status)
        ? EmployeeType_Status[1]
        : EmployeeType_Status;

      const currentStatus = item.EmployeeType_Status === "Active";

      if (currentStatus === newStatus) {
        // If the status hasn't changed, do nothing
        return;
      }
      this.isLoading = true;

      this.$apollo
        .mutate({
          mutation: ADD_UPDATE_EMPLOYEE_TYPE,
          variables: {
            Level: item.Level ? parseInt(item.Level) : null,
            EmpType_Id: item.EmpType_Id ? parseInt(item.EmpType_Id) : 0,
            Employee_Type: item.Employee_Type || "",
            Benefits_Applicable:
              item.Benefits_Applicable_Value === "Yes" ? 1 : 0,
            Holiday_Eligiblity:
              item.Holiday_Eligibility_Value === "Yes" ? 1 : 0,
            Salary_Calc_Days: item.Salary_Calc_Days
              ? parseInt(item.Salary_Calc_Days)
              : 0,
            Fixed_Days: item.Fixed_Days ? parseFloat(item.Fixed_Days) : 0,
            Comp_Off_Days: item.Comp_Off_Days
              ? parseInt(item.Comp_Off_Days)
              : 0,
            Comp_Off_Fixed_Days: item.Comp_Off_Fixed_Days
              ? parseInt(item.Comp_Off_Fixed_Days)
              : 0,
            Display_Total_Hours_In_Minutes:
              item.Display_Total_Hours_In_Minutes === "Yes" ? 1 : 0,
            Exclude_Break_Hours: item.Exclude_Break_Hours === "Yes" ? 1 : 0,
            Work_Schedule: item.Work_Schedule || "",
            Enable_Work_Place: item.Enable_Work_Place === "Yes" ? 1 : 0,
            Attendance_Process_Status: item.Attendance_Process_Status || "",
            Approve_Dashboard_Attendance:
              item.Approve_Dashboard_Attendance || "",
            EmployeeType_Status: newStatus ? "Active" : "InActive",
            Description: item.Description || "",
            Employee_Type_Code: item.Employee_Type_Code || "",
            Work_Place_Id: Array.isArray(item.workPlace) ? item.workPlace : [],
          },
          client: "apolloClientBB",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.addUpdateEmployeeType
          ) {
            const { errorCode, validationError } =
              response.data.addUpdateEmployeeType;
            if (!errorCode && !validationError) {
              let snackbarData = {
                isOpen: true,
                type: "success",
                message: vm.landedFormName + " status updated successfully.",
              };
              vm.showAlert(snackbarData);
              this.isLoading = false;
              this.refetchList("status-update-success");
            } else {
              vm.handleAddUpdateError("updating");
            }
          } else {
            vm.handleAddUpdateError("updating");
          }
        })
        .catch((addUpdateError) => {
          vm.handleAddUpdateError(addUpdateError);
        });

      // Update the local state with the new status
      item.EmployeeType_Status = newStatus ? "Active" : "InActive";
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;

      // Check if the error contains associated forms information
      if (
        err &&
        err.graphQLErrors &&
        err.graphQLErrors[0] &&
        err.graphQLErrors[0].extensions
      ) {
        const associatedForms = err.graphQLErrors[0].extensions.associatedForms;

        // Create a message to display the associated forms
        let errorMessage =
          "Employee Type status cannot be updated as the following forms are associated with this employee type:\n\n";
        associatedForms.forEach((form, index) => {
          // Add a comma for all but the last item
          if (index === associatedForms.length - 1) {
            errorMessage += ` ${form}.`; // Add a period for the last item
          } else {
            errorMessage += ` ${form},\n`; // Add a comma and newline for other items
          }
        });
        errorMessage +=
          "\nPlease ensure the forms are reassigned or removed before making this change.";

        // Show alert with the associated forms
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: errorMessage,
        });
      } else {
        // Fallback to general error handling
        this.$store.dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "Employee Type",
          isListError: false,
        });
      }

      // Refetch the list after failure
      this.refetchList("status-update-failed");
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_EMPLOYEE_TYPES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listEmployeeTypes &&
            response.data.listEmployeeTypes.employeeTypes
          ) {
            let responseData = JSON.parse(
              response.data.listEmployeeTypes.employeeTypes
            );
            responseData = responseData.map((el) => {
              return {
                ...el,
                Benefits_Applicable_Value:
                  el.Benefits_Applicable === 1 ? "Yes" : "No",
                Holiday_Eligibility_Value:
                  el.Holiday_Eligiblity === 1 ? "Yes" : "No",
                Display_Duration_In_Hours_And_Minutes:
                  el.Display_Total_Hours_In_Minutes === 1 ? "Yes" : "No",
                Exclude_Break_Hours_Value:
                  el.Exclude_Break_Hours === 1 ? "Yes" : "No",
                Enable_Work_Place_Value:
                  el.Enable_Work_Place === 1 ? "Yes" : "No",
              };
            });

            // Filter responseData to include only active employee types
            vm.itemList = responseData.filter(
              (res) => res.EmployeeType_Status === "Active"
            );

            vm.originalList = responseData;
            vm.onApplySearch();
            vm.listLoading = false;
            mixpanel.track("Employee Types list retrieved");
          } else {
            vm.handleEmployeeTypesError();
          }
        })
        .catch((err) => {
          vm.handleEmployeeTypesError(err);
        });
    },

    handleEmployeeTypesError(err = "") {
      mixpanel.track("Employee Type error in list API");
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Employee Type",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    onDeleteEmployeeType() {
      let vm = this;
      vm.isLoading = true;
      const { EmpType_Id } = this.selectedItem;
      vm.$apollo
        .mutate({
          mutation: DELETE_EMPLOYEE_TYPE,
          variables: {
            EmpType_Id: EmpType_Id,
          },
          client: "apolloClientBB",
        })
        .then(() => {
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.landedFormName + " deleted successfully.",
          };
          vm.showAlert(snackbarData);
          vm.refetchList("Employee Type deleted");
          vm.openWarningModal = false;
        })
        .catch((err) => {
          vm.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      this.openWarningModal = false;
      this.isLoading = false;

      // Check if the error contains associated forms information
      if (
        err &&
        err.graphQLErrors &&
        err.graphQLErrors[0] &&
        err.graphQLErrors[0].extensions
      ) {
        const associatedForms = err.graphQLErrors[0].extensions.associatedForms;

        // Create a message to display the associated forms
        let errorMessage =
          "Employee Type cannot be deleted as the following forms are associated with this employee type:\n\n";
        associatedForms.forEach((form, index) => {
          // Add a comma for all but the last item
          if (index === associatedForms.length - 1) {
            errorMessage += ` ${form}.`; // Add a period for the last item
          } else {
            errorMessage += ` ${form},\n`; // Add a comma and newline for other items
          }
        });
        errorMessage +=
          "\nPlease ensure the forms are reassigned or removed before making this change.";

        // Show alert with the associated forms
        this.showAlert({
          isOpen: true,
          type: "warning",
          message: errorMessage,
        });
      } else {
        // Fallback to general error handling
        this.$store.dispatch("handleApiErrors", {
          error: err,
          action: "delete",
          form: "Employee Type",
          isListError: false,
        });
      }
    },

    onApplySearch(val) {
      if (!val) {
        this.resetFilter();
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          let newObj = {
            Employee_Type: item.Employee_Type,
            Employee_Type_Code: item.Employee_Type_Code,
            Work_Schedule: item.Work_Schedule,
            Eligible_For_Benefits: item.Benefits_Applicable_Value,
            Holiday_Eligibility: item.Holiday_Eligibility_Value,
            Salary_Calculation_Days: item.Salary_Calc_Days_Flag,
            Status: item.EmployeeType_Status,
          };
          return Object.keys(newObj).some((k) => {
            if (
              newObj[k] &&
              newObj[k].toString().toLowerCase().includes(searchValue)
            ) {
              return true;
            } else {
              return false;
            }
          });
        });
        this.itemList = searchItems;
      }
    },
    applyFilter(filteredArray) {
      this.itemList = filteredArray;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    resetFilter() {
      this.itemList = this.originalList.filter(
        (res) => res.EmployeeType_Status === "Active"
      );
      this.isFilter = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
    },

    openEditForm() {
      mixpanel.track("Employee Type edit form opened");
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openViewForm(item) {
      mixpanel.track("Employee Type form opened");
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    openAddForm() {
      mixpanel.track("Employee Type add form opened");
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      mixpanel.track("Employee Type all forms closed");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.openWarningModal = false;
    },

    onTabChange(tab) {
      mixpanel.track("Employee Type form tab changed");
      // Use the mixin method for handling translated tabs
      this.onTabChangeWithTranslation(tab);
    },

    refetchList(msg) {
      mixpanel.track(msg);
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
      this.resetFilter();
    },
    // openImportModel() {
    //   this.isImportModel = true;
    // },
    // closeImportModel() {
    //   this.isImportModel = false;
    // },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        mixpanel.track("Employee-Type-export-click");
        this.exportReportFile();
      }
      // else if (actionType === "Import") {
      //   this.openImportModel();
      // }
      this.openMoreMenu = false;
    },
    onActions(type, item) {
      if (type && type.toLowerCase() === "delete") {
        this.onDelete(item);
      } else {
        this.onEdit(item);
      }
    },

    onEdit(item) {
      this.selectedItem = item;
      this.openEditForm();
    },
    onDelete(item) {
      this.selectedItem = item;
      this.openWarningModal = true;
    },
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.selectedItem = null;
    },
    exportReportFile() {
      const exportHeaders = [
        { header: "Employee Type Code", key: "Employee_Type_Code" },
        { header: "Employee Type", key: "employeeType" },
        { header: "Work Schedule", key: "workSchedule" },
        { header: "Eligible For Benefits", key: "eligibleForBenefits" },
        { header: "Holiday Eligibility", key: "holidayEligibility" },
        { header: "Salary Calculation Days", key: "salaryCalculationDays" },
        {
          header: "Comp Off Calculation Days",
          key: "compOffCalculationDays",
        },
        {
          header: "Display Duration in Hours and Minutes",
          key: "displayDurationInHoursAndMinutes",
        },
        { header: "Exclude Break Hours", key: "excludeBreakHours" },
        {
          header: "Processed Biometric Attendance Record Status",
          key: "processedBiometricAttendanceRecordStatus",
        },
        { header: "Enable Work Place", key: "enableWorkPlace" },
        {
          header: "Approve Dashboard Attendance",
          key: "approveDashboardAttendance",
        },
        {
          header: "Work Place",
          key: "workPlace",
        },
        { header: "Status", key: "Status" },
        { header: "Description", key: "Description" },
        { header: "Added On", key: "addedOn" },
        { header: "Added By", key: "addedByName" },
        { header: "Updated On", key: "updatedOn" },
        { header: "Updated By", key: "updatedByName" },
      ];
      if (this.entomoIntegrationEnabled && this.isEntomoSyncTypePush) {
        exportHeaders.unshift({
          header: "Level",
          key: "Level",
        });
      }

      const exportList = this.itemList.map((item) => ({
        Level: item.Level,
        Employee_Type_Code: item.Employee_Type_Code,
        employeeType: item.Employee_Type,
        workSchedule: item.Work_Schedule,
        eligibleForBenefits: item.Benefits_Applicable_Value,
        holidayEligibility: item.Holiday_Eligibility_Value,
        salaryCalculationDays: item.Salary_Calc_Days_Flag,
        compOffCalculationDays: item.Comp_Off_Days_Flag,
        displayDurationInHoursAndMinutes:
          item.Display_Duration_In_Hours_And_Minutes,
        excludeBreakHours: item.Exclude_Break_Hours_Value,
        processedBiometricAttendanceRecordStatus:
          item.Attendance_Process_Status,
        enableWorkPlace: item.Enable_Work_Place_Value,
        approveDashboardAttendance: item.Approve_Dashboard_Attendance,
        workPlace: item.Work_Place,
        Status: item.EmployeeType_Status,
        Description: item.Description,
        addedOn: item.Added_On ? this.convertUTCToLocal(item.Added_On) : "",
        addedByName: item.Added_By_Name,
        updatedOn: item.Updated_On
          ? this.convertUTCToLocal(item.Updated_On)
          : "",
        updatedByName: item.Updated_By_Name,
      }));

      const exportOptions = {
        fileExportData: exportList,
        fileName: "Employee Type",
        sheetName: "Employee Type",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
      mixpanel.track("Employee Type-Details-exported");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.employeetype {
  padding: 5em 2em 0em 3em;
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

.notification-bar {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  margin: 10px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.new-badge {
  background-color: #ff6f61;
  color: white;
  padding: 5px 10px;
  border-radius: 3px;
  font-weight: bold;
  margin-right: 10px;
}

@media screen and (max-width: 805px) {
  .employeetype {
    padding: 4em 1em 0em 1em;
  }
}
</style>
