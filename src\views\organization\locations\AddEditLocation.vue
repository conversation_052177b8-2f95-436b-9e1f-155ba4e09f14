<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">Location</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mr-1">
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="isFormDirty ? '' : props"
                  rounded="lg"
                  class="mb-2 secondary"
                  variant="elevated"
                  type="submit"
                  @click="isFormDirty ? validateLocationForm() : {}"
                  ><span class="px-2 primary">Save</span></v-btn
                >
              </template>
              <div v-if="!isFormDirty">There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon class="mt-n2" color="primary" @click="closeAddForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card-text style="height: calc(100vh - 300px); overflow: scroll">
        <v-form ref="LocationAddForm" @submit.prevent="[]">
          <v-row class="px-sm-4 px-md-6 pt-sm-4">
            <!-- Location Code -->
            <v-col
              v-if="entomoIntegrationEnabled"
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="LocationCode"
                variant="solo"
                ref="LocationCode"
                :rules="[
                  required(
                    labelList[310]?.Field_Alias || 'Location Code',
                    LocationCode
                  ),
                  LocationCode
                    ? multilingualNameNumericValidation(
                        labelList[310]?.Field_Alias || 'Location Code',
                        LocationCode
                      )
                    : true,
                  minMaxStringValidation(
                    labelList[310]?.Field_Alias || 'Location Code',
                    LocationCode,
                    1,
                    100
                  ),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>{{
                    labelList[310]?.Field_Alias || "Location Code"
                  }}</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col
              v-else-if="
                labelList[310] &&
                labelList[310].Field_Visiblity.toLowerCase() === 'yes'
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="LocationCode"
                variant="solo"
                ref="LocationCode"
                :rules="[
                  labelList[310].Mandatory_Field === 'Yes'
                    ? required(`${labelList[310].Field_Alias}`, LocationCode)
                    : true,
                  LocationCode
                    ? multilingualNameNumericValidation(
                        'Location Code',
                        LocationCode
                      )
                    : true,
                  minMaxStringValidation(
                    `${labelList[310].Field_Alias}`,
                    LocationCode,
                    1,
                    100
                  ),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>{{ labelList[310].Field_Alias }}</span>
                  <span
                    v-if="labelList[310].Mandatory_Field === 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
              ><v-text-field
                v-model="LocationName"
                variant="solo"
                ref="LocationName"
                :rules="[
                  required('Location Name', LocationName),
                  locationFirstCharacterValidation(LocationName),
                  multilingualNameNumericValidation(
                    'Location Name',
                    LocationName
                  ),
                  minMaxStringValidation('Location Name', LocationName, 1, 250),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Location Name</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                :items="locationTypeList"
                v-model="LocationType"
                label="Location Type"
                itemValue="value"
                itemTitle="text"
                :isLoading="isCityListLoading"
                ref="LocationType"
                :isAutoComplete="true"
                :isRequired="true"
                :rules="[required('Location Type', LocationType)]"
                :noDataText="
                  isCityListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="LocationType"
                @selected-item="LocationType = $event"
                @update:model-value="deductFormChange()"
              ></CustomSelect>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="Street1"
                variant="solo"
                ref="Street1"
                :rules="[
                  required('Street 1', Street1),
                  multilingualNameNumericValidation('Street 1', Street1),
                  minMaxStringValidation('Street 1', Street1, 1, 255),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Street 1</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="Street2"
                variant="solo"
                :rules="[
                  Street2
                    ? multilingualNameNumericValidation('Street 2', Street2)
                    : true,
                  Street2
                    ? minMaxStringValidation('Street 2', Street2, 1, 255)
                    : true,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Street 2</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                v-model="CountryCode"
                :items="countryList"
                label="Country"
                itemValue="Country_Code"
                itemTitle="Country_Name"
                :isLoading="isCityListLoading"
                ref="country"
                :isAutoComplete="true"
                :isRequired="true"
                :rules="[required('Country', CountryCode)]"
                :noDataText="
                  isCityListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="CountryCode"
                @selected-item="CountryCode = $event"
                @update:model-value="deductFormChange()"
              ></CustomSelect>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                :items="stateList"
                label="State"
                itemValue="State_Id"
                itemTitle="State_Name"
                :disabled="!CountryCode"
                :isLoading="isCityListLoading"
                ref="state"
                :isAutoComplete="true"
                :isRequired="true"
                :rules="[required('State', StateId)]"
                :noDataText="
                  isCityListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="StateId"
                @selected-item="StateId = $event"
                @update:model-value="deductFormChange()"
              ></CustomSelect>
            </v-col>
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                :items="cityList"
                label="City"
                itemValue="City_Id"
                itemTitle="City_Name"
                :disabled="!StateId"
                :isLoading="isCityListLoading"
                ref="city"
                :isAutoComplete="true"
                :isRequired="true"
                :rules="[required('City', CityId)]"
                :noDataText="
                  isCityListLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="CityId"
                @selected-item="CityId = $event"
                @update:model-value="deductFormChange()"
              ></CustomSelect>
            </v-col>
            <v-col
              v-if="labelList[431]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                v-if="labelList[431].Predefined.toLowerCase() === 'yes'"
                :items="barangayList"
                :label="labelList[431].Field_Alias"
                item-value="Barangay_Id"
                itemTitle="barangayDetails"
                :isLoading="isBarangayLoading"
                ref="barangay"
                :disabled="!CityId"
                :isAutoComplete="true"
                :isRequired="
                  labelList[431].Mandatory_Field.toLowerCase() == 'yes'
                "
                :rules="[
                  labelList[431].Mandatory_Field.toLowerCase() == 'yes'
                    ? required(`${labelList[431].Field_Alias}`, Barangay_Id)
                    : true,
                ]"
                :noDataText="
                  isBarangayLoading ? 'Loading...' : 'No data available'
                "
                :itemSelected="Barangay_Id"
                @selected-item="Barangay_Id = $event"
                @update:model-value="onUpdateBarangay($event)"
              ></CustomSelect>
              <v-text-field
                v-else
                v-model="barangay"
                :rules="[
                  labelList[431]?.Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(labelList[431]?.Field_Alias, barangay)
                    : true,

                  validateWithRulesAndReturnMessages(
                    barangay,
                    'barangay',
                    labelList[431]?.Field_Alias
                  ),
                ]"
                :disabled="!CityId"
                clearable
                variant="solo"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  {{ labelList[431]?.Field_Alias }}
                  <span
                    v-if="
                      labelList[431]?.Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <!-- region -->
            <v-col
              v-if="labelList[432]?.Field_Visiblity?.toLowerCase() === 'yes'"
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="region"
                :rules="[
                  labelList[432]?.Mandatory_Field?.toLowerCase() === 'yes'
                    ? required(labelList[432]?.Field_Alias, region)
                    : true,
                  validateWithRulesAndReturnMessages(
                    region,
                    'region',
                    labelList[432]?.Field_Alias
                  ),
                ]"
                :disabled="!barangay"
                clearable
                variant="solo"
                ref="region"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  {{ labelList[432]?.Field_Alias }}
                  <span
                    v-if="
                      labelList[432]?.Mandatory_Field?.toLowerCase() === 'yes'
                    "
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>
            <v-col
              v-if="
                labelList[309] &&
                labelList[309].Field_Visiblity.toLowerCase() === 'yes'
              "
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="PinCode"
                variant="solo"
                ref="PinCode"
                :rules="[
                  labelList[309].Mandatory_Field === 'Yes'
                    ? required(`${labelList[309].Field_Alias}`, PinCode)
                    : true,
                  PinCode
                    ? multilingualNameNumericValidation('Pin Code', PinCode)
                    : true,
                  minMaxStringValidation(
                    `${labelList[309].Field_Alias}`,
                    PinCode,
                    3,
                    10
                  ),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>{{ labelList[309].Field_Alias }}</span>
                  <span
                    v-if="labelList[309].Mandatory_Field === 'Yes'"
                    class="ml-1"
                    style="color: red"
                    >*</span
                  >
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <CustomSelect
                :items="timeZoneList"
                label="Time Zone"
                item-title="TimeZone_Id"
                item-value="Zone_Id"
                :disabled="!CountryCode"
                :isLoading="listLoading"
                ref="Time zone"
                :isRequired="true"
                :isAutoComplete="true"
                :rules="[required('Time Zone', TimeZoneId)]"
                :noDataText="listLoading ? 'Loading...' : 'No data available'"
                :itemSelected="TimeZoneId"
                @selected-item="TimeZoneId = $event"
                @update:model-value="deductFormChange()"
              ></CustomSelect>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="CurrencySymbol"
                variant="solo"
                :rules="[
                  !CurrencySymbol
                    ? true
                    : maxLengthValidation('Currency Symbol', CurrencySymbol, 9),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Currency Symbol</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="PhoneNumber"
                variant="solo"
                type="number"
                :rules="[
                  minMaxStringValidation('Contact Number', PhoneNumber, 7, 15),
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Contact Number</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="12"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-textarea
                v-model="Description"
                rows="2"
                row-height="10"
                color="primary"
                hide-details="auto"
                variant="solo"
                label="Description"
                counter="600"
                :rules="[
                  Description
                    ? multilingualNameNumericValidation(
                        'Description',
                        Description
                      )
                    : true,
                  Description
                    ? minMaxStringValidation('Description', Description, 5, 600)
                    : true,
                ]"
                @update:model-value="deductFormChange()"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="isLoadingCard"
        scrim="#fff"
      >
        <v-progress-circular color="secondary" indeterminate size="54">
        </v-progress-circular>
      </v-overlay>
    </v-card>
    <AppWarningModal
      v-if="showConfirmation"
      :open-modal="showConfirmation"
      imgUrl="common/exit_form"
      confirmation-heading="Are you sure to exit this form?"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 secondary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import {
  ADD_EDIT_LIST_LOCATION_DETAILS,
  GET_TIME_ZONE_LIST,
} from "@/graphql/organisation/location/OrganisationQueries.js";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  LIST_CITIES_NO_AUTH,
  LIST_BARANGAY,
} from "@/graphql/dropDownQueries.js";

export default {
  name: "AddEditLocations",

  mixins: [validationRules],

  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    CustomSelect,
  },
  emits: ["close-form", "form-updated"],

  data: () => ({
    showConfirmation: false,
    isFormDirty: false,
    Location_Id: 0,
    LocationName: "",
    LocationType: null,
    LocationCode: "",
    Street1: "",
    Street2: "",
    CountryName: null,
    CountryCode: null,
    StateName: null,
    StateId: null,
    CityName: null,
    CityId: null,
    barangayList: [],
    isBarangayLoading: false,
    barangay: null,
    Barangay_Id: null,
    cityList: [],
    uniqueCityList: [],
    isCityListLoading: false,
    listLoading: false,
    stateList: [],
    uniqueStateList: [],
    uniqueTimeZoneList: [],
    countryList: [],
    PinCode: "",
    TimeZone: null,
    TimeZoneId: null,
    TimeZoneName: null,
    timeZoneList: [],
    CurrencySymbol: "",
    currencyListLoading: false,
    currencyList: "",
    description: "",
    LocationStatus: "Active",
    region: null,

    locationTypeList: [
      {
        value: "FieldOffice",
        text: "Field Office",
      },
      {
        value: "MainBranch",
        text: "Main Branch",
      },
      {
        value: "SalesOffice",
        text: "Sales Office",
      },
      {
        value: "SubBranch",
        text: "Sub Branch",
      },
    ],
    CurrencyName: "",
    PhoneNumber: "",
    Description: null,
    initialLoad: true,
    isLoadingCard: false,

    validationMessages: [],
    showValidationAlert: false,
  }),

  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  watch: {
    CountryCode(newCountryCode, oldCountryCode) {
      if (
        !this.initialLoad &&
        newCountryCode &&
        newCountryCode !== oldCountryCode
      ) {
        // Reset state, city, and timezone when country is reset
        this.StateId = null;
        this.CityId = null;
        this.TimeZoneId = null;
        if (newCountryCode) {
          this.findStateBasedOnCountry(newCountryCode);
          this.findTimeZoneBasedOnCountry(newCountryCode);
        }
      }
    },
    StateId(newStateId, oldStateId) {
      if (!this.initialLoad && newStateId && newStateId !== oldStateId) {
        // Reset city when state is reset
        this.CityId = null;
        this.findCityBasedOnState(newStateId);
      }
    },
    CityId(newCityId, oldCityId) {
      if (!this.initialLoad && newCityId && newCityId !== oldCityId) {
        this.retrieveBarangay(newCityId);
      }
    },
  },

  mounted() {
    if (this.isEdit) {
      const {
        Location_Name,
        Location_Code,
        Description,
        Location_Type,
        Street1,
        Street2,
        City_Id,
        City_Name,
        Barangay,
        Barangay_Id,
        Region,
        Location_Id,
        State_Id,
        State_Name,
        Country_Code,
        Country_Name,
        TimeZone_Id,
        TimeZone_Name,
        Pincode,
        Currency_Symbol,
        Currency_Id,
        Phone,
        Location_Status,
      } = this.editFormData;

      this.Location_Id = Location_Id ? parseInt(Location_Id) : 0;
      this.LocationName = Location_Name ? Location_Name : "";
      this.LocationType = Location_Type ? Location_Type : "";
      this.LocationCode = Location_Code ? Location_Code : "";
      this.Street1 = Street1 ? Street1 : 1;
      this.Street2 = Street2 ? Street2 : "";
      this.CountryName = Country_Name ? Country_Name : null;
      this.CountryCode = Country_Code ? Country_Code : null;
      this.StateId = State_Id ? State_Id : "";
      this.StateName = State_Name ? State_Name : "";
      this.CityId = City_Id ? City_Id : null;
      if (this.CityId) {
        this.retrieveBarangay(this.CityId);
      }
      this.barangay = Barangay ? Barangay : "";
      this.Barangay_Id = Barangay_Id ? Barangay_Id : null;
      this.region = Region ? Region : "";
      this.CityName = City_Name ? City_Name : "";
      this.PinCode = Pincode ? Pincode : "";
      this.TimeZoneId = TimeZone_Id ? TimeZone_Id : "";
      this.TimeZone = TimeZone_Name ? TimeZone_Name : "";
      this.CurrencySymbol = Currency_Symbol ? Currency_Symbol : "";
      this.CurrencyName = Currency_Id ? Currency_Id : ";";
      this.PhoneNumber = Phone ? Phone : "";
      this.Description = Description ? Description : "";
      this.LocationStatus = Location_Status ? Location_Status : "Active";
    }
    this.listCity();
    this.fetchTimeZoneList();
  },

  methods: {
    locationFirstCharacterValidation(value) {
      if (value && value.startsWith(" ")) {
        return "The first character of Location Name cannot be a space.";
      }
      return true;
    },
    findStateBasedOnCountry(countryCode) {
      if (countryCode) {
        let selectedStateBasedOnCountry = this.uniqueStateList.filter(
          (element) => element.Country_Code === countryCode
        );
        this.stateList = selectedStateBasedOnCountry;

        let selectedCountry = this.countryList.find(
          (element) => element.Country_Code === this.countryList
        );
        this.CountryName = selectedCountry
          ? selectedCountry.Country_Name
          : null;
      }
    },
    findCityBasedOnState(stateId) {
      if (stateId) {
        let selectedCityBasedOnState = this.uniqueCityList.filter(
          (element) => element.State_Id === stateId
        );
        this.cityList = selectedCityBasedOnState;

        let selectedState = this.stateList.find(
          (element) => element.State_Id === stateId
        );
        this.StateName = selectedState ? selectedState.State_Name : "";
      }
    },
    onChangeBarangay(val) {
      let filterCity = this.barangayList.filter((el) => el.Barangay_Id == val);
      if (filterCity && filterCity[0]) {
        this.barangay = filterCity[0].Barangay_Name;
        this.region = filterCity[0].Region_Name;
      }
    },
    onUpdateBarangay(value) {
      this.deductFormChange();
      this.onChangeBarangay(value);
    },
    findCityNameBasedOnCity(cityId) {
      if (cityId) {
        let selectedCity = this.cityList.find(
          (element) => element.City_Id === cityId
        );
        this.CityName = selectedCity ? selectedCity.City_Name : "";
      }
    },
    findTimeZoneBasedOnCountry(countryCode) {
      if (countryCode) {
        let selectedTimeZoneBasedOnCountry = this.uniqueTimeZoneList.filter(
          (element) => element.Country_Code === countryCode
        );
        this.timeZoneList = selectedTimeZoneBasedOnCountry;
      }
    },
    deductFormChange() {
      this.isFormDirty = true;
    },

    closeAddForm() {
      if (this.isFormDirty) {
        this.showConfirmation = true;
      } else {
        this.closeForm();
      }
    },
    listCity() {
      let vm = this;
      vm.isCityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES_NO_AUTH,
          client: "apolloClientAS",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCityListWithState &&
            response.data.getCityListWithState.cityDetails
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            // Create an array to store unique states
            let uniqueStates = [],
              uniqueCity = [],
              uniqueCountry = [];
            // Iterate through each city object
            cityDetails.forEach((city) => {
              let cityId = city.City_Id;
              let cityName = city.City_Name;
              let stateId = city.State_Id;
              let stateName = city.State_Name;
              let countryCode = city.Country_Code;
              let countryName = city.Country_Name;
              // Check if the city is already in the uniqueStates array
              let cityExists = uniqueCity.some(
                (city) => city.City_Id === cityId
              );
              if (!cityExists) {
                uniqueCity.push({
                  City_Id: cityId,
                  City_Name: cityName,
                  State_Id: stateId,
                  State_Name: stateName,
                });
              }
              // Check if the state is already in the uniqueStates array
              let stateExists = uniqueStates.some(
                (state) => state.State_Id === stateId
              );
              // If not, add it to the array
              if (!stateExists) {
                uniqueStates.push({
                  State_Id: stateId,
                  State_Name: stateName,
                  Country_Code: countryCode,
                  Country_Name: countryName,
                });
              }
              let countryExists = uniqueCountry.some(
                (country) => country.Country_Code === countryCode
              );
              if (!countryExists) {
                uniqueCountry.push({
                  Country_Code: countryCode,
                  Country_Name: countryName,
                });
              }
            });
            vm.uniqueStateList = uniqueStates;
            vm.uniqueCityList = uniqueCity;
            vm.countryList = uniqueCountry;
            this.findStateBasedOnCountry(this.CountryCode);
            this.findCityBasedOnState(this.StateId);
            this.findCityNameBasedOnCity(this.CityId);
            this.initialLoad = false;
          }
          vm.isCityListLoading = false;
        })
        .catch(() => {
          vm.isCityListLoading = false;
        });
    },
    fetchTimeZoneList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: GET_TIME_ZONE_LIST,
          client: "apolloClientAS",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getTimezoneList.timeZoneDetails) {
            let allTimeZones = response.data.getTimezoneList.timeZoneDetails;
            vm.uniqueTimeZoneList = allTimeZones;
            this.findTimeZoneBasedOnCountry(this.CountryCode);
          }
          vm.listLoading = false;
        })
        .catch(() => {
          vm.listLoading = false;
        });
    },
    abortClose() {
      this.showConfirmation = false;
    },

    acceptClose() {
      this.showConfirmation = false;
      this.closeForm();
    },

    closeForm() {
      this.isFormDirty = false;
      this.$emit("close-form");
    },
    async validateLocationForm() {
      // Validate the form fields
      const { valid } = await this.$refs.LocationAddForm.validate();

      if (valid) {
        // Submit the form if all fields are valid
        this.addupdateList();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });

        // Handle and log invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "LocationName",
                "LocationType",
                "Street1",
                "country",
                "state",
                "city",
                "PinCode",
                "Time zone",
              ];

              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect
                  ? fieldRef.onFocusCustomSelect()
                  : fieldRef.focus();
              } else {
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 0.4,
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },
    async addupdateList() {
      let isFormValid = await this.$refs.LocationAddForm.validate();
      let vm = this;
      if (isFormValid && isFormValid.valid) {
        try {
          vm.isLoadingCard = true;
          let isUpdate = !!vm.Location_Id;
          vm.$apollo
            .mutate({
              mutation: ADD_EDIT_LIST_LOCATION_DETAILS,
              client: "apolloClientJ",
              fetchPolicy: "no-cache",
              variables: {
                locationId: vm.Location_Id,
                locationName: vm.LocationName,
                locationCode: vm.LocationCode,
                locationType: vm.LocationType,
                street1: vm.Street1,
                street2: vm.Street2,
                cityName: vm.CityName,
                cityId: vm.CityId ? vm.CityId : null,
                stateName: vm.StateName,
                stateId: vm.StateId,
                countryName: vm.CountryName,
                countryCode: vm.CountryCode,
                pincode: vm.PinCode,
                phone: vm.PhoneNumber,
                currencySymbol: vm.CurrencySymbol,
                description: vm.Description,
                orgId: 0,
                zoneId: vm.TimeZoneId,
                locationStatus: vm.LocationStatus,
                barangay: vm.barangay,
                barangayId: vm.Barangay_Id ? vm.Barangay_Id : null,
                region: vm.region,
              },
            })
            .then((response) => {
              if (
                response &&
                response.data &&
                response.data.addUpdateLocation
              ) {
                const { errorCode, validationError } =
                  response.data.addUpdateLocation;
                if (!errorCode && !validationError) {
                  let snackbarData = {
                    isOpen: true,
                    type: "success",
                    message: isUpdate
                      ? "Location updated successfully."
                      : "Location added successfully.",
                  };
                  vm.showAlert(snackbarData);
                  vm.$emit("form-updated");
                } else
                  vm.handleAddEditError(
                    isUpdate ? "updating" : "adding",
                    errorCode
                  );
              } else
                vm.handleAddEditError(
                  isUpdate ? "updating" : "adding",
                  response.data?.errorCode || ""
                );
            })
            .catch((addEditError) => {
              vm.handleAddEditError(
                isUpdate ? "updating" : "adding",
                addEditError
              );
            });
        } catch (e) {
          vm.handleAddEditError(isUpdate ? "updating" : "adding", e);
        } finally {
          vm.isLoadingCard = false;
        }
      }
    },

    handleAddEditError(action, err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: action,
          form: "location",
          isListError: false,
        })
        .then((validationErrors) => {
          this.validationMessages = validationErrors;
          this.showValidationAlert = true;
        });
    },

    retrieveBarangay(cityId) {
      let vm = this;
      vm.isBarangayLoading = true;
      vm.$apollo
        .query({
          query: LIST_BARANGAY,
          client: "apolloClientAS",
          variables: {
            searchString: "",
            cityId: cityId,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getBarangayListWithCity &&
            !response.data.getBarangayListWithCity.errorCode &&
            response.data.getBarangayListWithCity.barangayDetails?.length
          ) {
            const { barangayDetails } = response.data.getBarangayListWithCity;
            vm.barangayList = barangayDetails;
          }
          vm.isBarangayLoading = false;
        })
        .catch(() => {
          vm.isBarangayLoading = false;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
