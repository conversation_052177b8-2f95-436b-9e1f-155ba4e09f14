<template>
  <div class="d-flex flex-column justify-center align-center error-screen-bg">
    <v-row>
      <v-col
        cols="12"
        class="d-flex flex-column justify-center align-center pa-2"
      >
        <div>
          <img width="160" style="height: auto" :src="logUrl" alt="logo" />
        </div>
        <div
          class="page-not-found-code my-6 font-weight-bold text-h1 text-primary mb-5"
        >
          404
        </div>
        <div class="font-weight-bold text-h5 text-primary mb-5">
          Sorry! The link is broken or the page has been moved.
        </div>
        <span class="mb-5 sub-content text-center text-subtitle-1 text-black"
          >You may have typed the application address incorrectly or may have
          used an outdated link from external references. Please go to
          application homepage and navigate to the desired function. If problem
          persists, please contact our support team by email.</span
        >
        <div class="mb-4">
          <v-btn rounded="lg" color="secondary" @click="redirectToHome()">
            Go to Home Page
          </v-btn>
        </div>
        <div class="mb-5">
          <img
            style="height: auto; width: 100%"
            :src="getImgUrl"
            alt="page_not_found"
          />
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: "PageNotFound",
  props: {},
  computed: {
    //to know whether browser support webp or not
    isBrowserSupportWebp() {
      return this.$store.state.isWebpSupport;
    },
    getImgUrl() {
      if (this.isBrowserSupportWebp)
        return require("@/assets/images/layout/page-not-found.webp");
      else return require("@/assets/images/layout/page-not-found.png");
    },
    logUrl() {
      return require("@/assets/logo.png");
    },
    // returns baseurl of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },
  methods: {
    redirectToHome() {
      const redirectToV3Dashboard = [
        "fieldforce",
        "capricetest",
        "adeera",
        "hmcgroupuat",
        "capricecloud",
      ];
      if (redirectToV3Dashboard.includes(this.orgCode))
        window.location.href = this.baseUrl + "v3/";
      // otherwise, then we have to redirect to dashboard
      else window.location.href = this.baseUrl + "in/";
    },
  },
};
</script>

<style lang="scss" scoped>
.sub-content {
  width: 66%;
}
.error-screen-bg {
  height: 100%;
  background-color: white;
  background-image: url("../../assets/images/layout/cloud-bg.png");
  background-repeat: no-repeat;
  background-size: cover;
}
.page-not-found-code {
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent;
  background: -webkit-linear-gradient(#ed3093 0%, #9c27b0 100%);
}
@media screen and (max-width: 768px) {
  .sub-content {
    width: 100%;
  }
}
</style>
