<template>
  <div v-if="showCareerPageDetails">
    <CareerPageDetails
      :jobDetails="wholeJobPostData"
      :orgName="orgName"
      :careerConfig="careerConfig"
      :companyLogoUrl="companyLogoUrl"
      @close-details-form="closeAllForms($event)"
    />
  </div>
  <div v-else>
    <div>
      <v-row style="margin: 1% 5% 1%" align="center">
        <!-- Company Logo -->
        <v-col cols="auto" v-if="companyLogoUrl">
          <div>
            <img
              :src="companyLogoUrl"
              alt="Company Logo"
              class="rounded"
              style="max-height: 40px; max-width: 120px; object-fit: contain"
            />
          </div>
        </v-col>

        <!-- Organization Name -->
        <v-col v-else>
          <p
            class="text-h6 font-weight-bold ma-0"
            :style="{
              color: careerConfig.primaryColor,
              fontFamily: headlineFontFamily,
            }"
          >
            {{ orgName }}
          </p>
        </v-col>
      </v-row>
      <v-row>
        <v-card class="w-100 position-relative" v-if="careersLoading">
          <v-skeleton-loader type="image, sentences"></v-skeleton-loader>
        </v-card>
        <div
          v-else
          class="w-100 position-relative"
          :style="{
            height: '50vh',
            backgroundImage: `url('${bannerImageUrl}')`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            textAlign: 'center',
          }"
        >
          <!-- Banner Overlay -->
          <div :style="bannerOverlayStyle"></div>

          <!-- Banner Content -->
          <div
            class="h-100 d-flex position-absolute"
            :class="bannerContentStyle"
            :style="bannerContentPositionStyle"
          >
            <div
              :class="bannerTextAlignmentClass"
              class="px-4"
              :style="bannerTextContainerStyle"
            >
              <!-- Dynamic Headline -->
              <h1
                v-if="careerConfig.careerHeadlineText"
                :style="bannerHeadlineStyle"
                class="mb-4"
              >
                {{ careerConfig.careerHeadlineText }}
              </h1>

              <!-- Dynamic Sub-headline -->
              <p
                v-if="careerConfig.careerSubHeadlineText"
                :style="bannerSubTextStyle"
                class="mb-6"
              >
                {{ careerConfig.careerSubHeadlineText }}
              </p>
            </div>
          </div>
        </div>
      </v-row>
      <v-row style="margin-top: 2%" justify-md="center">
        <v-col cols="12" md="3" sm="12" class="sticky-column d-md-block">
          <!-- The Filter section -->
          <v-row
            style="margin: 10px"
            class="d-flex justify-center align-center"
            cols="12"
          >
            <v-card v-if="isLoading" class="filterCard"
              ><v-skeleton-loader
                class="mx-auto border"
                max-width="300"
                type="heading, heading, heading"
              ></v-skeleton-loader
            ></v-card>
            <v-card v-else class="filterCard">
              <v-card-text>
                <v-text-field
                  v-model="searchInput"
                  placeholder="Search"
                  :style="{
                    fontFamily: headlineFontFamily,
                  }"
                  ref="jobSection"
                  density="compact"
                  variant="outlined"
                  prepend-inner-icon="fas fa-search"
                  clearable
                />
                <v-autocomplete
                  v-if="
                    filtersVisible?.serviceProvider &&
                    fieldForce &&
                    labelList['115'] &&
                    labelList['115'].Field_Visiblity === 'Yes'
                  "
                  v-model="selectedServiceProvider"
                  :color="careerConfig.primaryColor"
                  :items="serviceProviderList"
                  :label="
                    labelList['115'] && labelList['115'].Field_Alias
                      ? labelList['115'].Field_Alias
                      : 'Service Provider'
                  "
                  :style="{
                    fontFamily: getFontFamily(
                      careerConfig.careerHeadlineFontFamily
                    ),
                  }"
                  item-title="serviceProviderName"
                  multiple
                  closable-chips
                  chips
                  clearable
                  density="compact"
                  single-line
                  variant="solo"
                  class="autocomplete"
                ></v-autocomplete>
                <v-autocomplete
                  v-if="filtersVisible?.department"
                  v-model="selectedDepartment"
                  :color="careerConfig.primaryColor"
                  :items="departmentList"
                  label="Department"
                  :style="{
                    fontFamily: getFontFamily(
                      careerConfig.careerHeadlineFontFamily
                    ),
                  }"
                  item-title="departmentName"
                  multiple
                  closable-chips
                  chips
                  clearable
                  density="compact"
                  single-line
                  variant="solo"
                  class="autocomplete"
                ></v-autocomplete>
                <v-autocomplete
                  v-if="filtersVisible?.location"
                  v-model="selectedLocation"
                  :color="careerConfig.primaryColor"
                  :items="locationList"
                  label="Location"
                  :style="{
                    fontFamily: getFontFamily(
                      careerConfig.careerHeadlineFontFamily
                    ),
                  }"
                  item-title="locationName"
                  multiple
                  closable-chips
                  chips
                  clearable
                  density="compact"
                  single-line
                  variant="solo"
                  class="autocomplete"
                ></v-autocomplete>
              </v-card-text>
            </v-card>
          </v-row>
        </v-col>
        <v-col cols="12" md="8" sm="12">
          <!-- The List Items -->
          <div v-if="listLoading">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 4" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else>
            <v-row
              v-if="!isJobListEmpty && isFilteredListEmpty"
              class="ma-2 pa-4"
              :style="{ backgroundColor: careerConfig.hoverColor }"
            >
              <div class="pa-5 d-flex justify-center align-center w-100">
                <span
                  class="text-body-1"
                  :style="{
                    fontFamily: getFontFamily(
                      careerConfig.careerHeadlineFontFamily
                    ),
                  }"
                  >No matches found in search results!</span
                >
              </div>
            </v-row>
            <v-row
              v-else-if="isJobListEmpty"
              class="bg-hover"
              style="margin: 2% 0 10px"
            >
              <div
                class="pa-5 d-flex justify-center align-center"
                :style="{
                  fontFamily: getFontFamily(
                    careerConfig.careerHeadlineFontFamily
                  ),
                }"
              >
                No jobs available right now!
              </div>
            </v-row>
            <v-row v-else>
              <v-col
                v-for="(department, index) in filteredDepartments"
                :key="index"
                cols="12"
                sm="10"
                ><div
                  v-if="
                    department?.departmentName && department?.jobs?.length > 0
                  "
                >
                  <div
                    v-if="filtersVisible?.department"
                    class="mb-2 d-flex align-center ml-2"
                  >
                    <span
                      class="pr-2 text-body-1 font-weight-bold"
                      :style="{
                        color: careerConfig.primaryColor,
                        fontFamily: getFontFamily(
                          careerConfig.careerHeadlineFontFamily
                        ),
                      }"
                      >{{ department.departmentName }}</span
                    >
                    -
                    <span
                      class="pa-1 text-body-2 rounded-lg ml-2"
                      :style="{
                        backgroundColor: careerConfig.hoverColor,
                        fontFamily: getFontFamily(
                          careerConfig.careerHeadlineFontFamily
                        ),
                      }"
                      >{{ department?.jobs?.length }} jobs</span
                    >
                  </div>
                  <v-row>
                    <v-col
                      v-for="job in department.jobs"
                      :key="job.Job_Post_Id"
                      cols="12"
                    >
                      <v-card
                        class="cursor-pointer ml-2"
                        :style="jobCardStyle"
                        @click="navigateToCareersPageDetails(job)"
                      >
                        <div>
                          <v-card-title>
                            <v-row>
                              <v-col cols="11">
                                <div
                                  class="d-flex align-center justify-space-between"
                                >
                                  <div class="text-subtitle-2 mb-2 text-wrap">
                                    <strong
                                      class="job-post-title mr-3"
                                      :style="{
                                        color: careerConfig.primaryColor,
                                        fontFamily: getFontFamily(
                                          careerConfig.careerHeadlineFontFamily
                                        ),
                                      }"
                                      >{{ job.Job_Post_Name }}</strong
                                    >
                                    <span
                                      class="text-grey"
                                      variant="pill"
                                      :style="{
                                        fontFamily: getFontFamily(
                                          careerConfig.careerHeadlineFontFamily
                                        ),
                                      }"
                                    >
                                      {{
                                        getPostingDateDifference(
                                          job.Posting_Date
                                        )
                                      }}
                                      days ago
                                    </span>
                                  </div>
                                </div>
                                <div
                                  class="text-grey text-body-2 d-flex align-center"
                                  :style="{
                                    fontFamily: getFontFamily(
                                      careerConfig.careerHeadlineFontFamily
                                    ),
                                  }"
                                >
                                  {{ job.City_Name }}
                                  <div
                                    v-if="job.Experience_Level && job.City_Name"
                                    style="
                                      background: #c4c4c4;
                                      border-radius: 50%;
                                      margin: 0px 5px;
                                      height: 10px;
                                      width: 10px;
                                    "
                                  ></div>
                                  {{ job.Experience_Level }}
                                </div>
                              </v-col>
                              <v-col
                                cols="1"
                                class="d-flex align-center justify-center"
                              >
                                <v-icon
                                  class="fas fa-location-arrow fa-sm hover-icon"
                                  :color="careerConfig.primaryColor"
                                  size="sm"
                                />
                              </v-col>
                            </v-row>
                          </v-card-title>
                        </div>
                      </v-card>
                    </v-col>
                  </v-row>
                </div>
              </v-col>
            </v-row>
          </div>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script>
import { POSTED_JOBS } from "@/graphql/settings/irukka-integration/jobPostFormQueries.js";
import CareerPageDetails from "./CareerPageDetails.vue";
import { CUSTOM_COLOR_PICKER } from "@/graphql/commonQueries.js";
import { GET_JOB_HEADER } from "@/graphql/recruitment/recruitmentQueries.js";
import { RETRIEVE_GENERAL_SETTINGS } from "@/graphql/settings/careerConfigurationQueries.js";
import Config from "@/config.js";

export default {
  name: "CareerPage",
  components: { CareerPageDetails },
  data() {
    return {
      jobPostList: [],
      departmentList: [],
      locationList: [],
      skillsList: [],
      qualificationList: [],
      selectedDepartment: [],
      selectedLocation: [],
      serviceProviderList: [],
      selectedServiceProvider: null,
      searchInput: "",
      orgName: "",
      fieldForce: null,
      wholeJobPostData: null,
      listLoading: false,
      showCareerPageDetails: false,
      settingResult: {},
      isLoading: false,
      careersLoading: false,
      loadedFonts: new Set(), // Track loaded fonts to avoid duplicates
      // Career page designer configuration
      careerConfig: {
        primaryColor: "#1C277D",
        hoverColor: "#0F1B5C",
        careerBannerImage: null,
        careerHeadlineText: "",
        careerSubHeadlineText: "",
        careerTextHorizontalPosition: "center",
        careerTextVerticalPosition: "middle",
        careerBannerOpacity: 30,
        careerHeadlineFontFamily: '"Roboto", sans-serif',
        careerHeadingFontSize: "48px",
        careerHeadlineFontColor: "#FFFFFF",
        careerSubHeadlineFontFamily: '"Roboto", sans-serif',
        careerSubHeadlineFontSize: "18px",
        careerSubHeadlineFontColor: "#FFFFFF",
        companyLogo: null,
        careerLogo: null,
      },
    };
  },
  computed: {
    filtersVisible() {
      let filters = this.settingResult?.Career_Portal_Filters
        ? JSON.parse(this.settingResult.Career_Portal_Filters)
        : {};
      return filters;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    // Dynamic banner styling
    bannerImageUrl() {
      if (this.careerConfig.careerBannerImage) {
        return this.getImageUrl(
          this.careerConfig.careerBannerImage,
          "bannerImage"
        );
      }
      return "https://cdn.kekastatic.net/shared/assets/images/components/careers/banner.jpg";
    },

    // Company logo URL
    companyLogoUrl() {
      if (this.careerConfig.careerLogo) {
        return this.getImageUrl(this.careerConfig.careerLogo, "careerLogo");
      } else if (this.careerConfig.companyLogo) {
        return this.getImageUrl(this.careerConfig.companyLogo, "companyLogo");
      }
      return null;
    },
    bannerOverlayStyle() {
      const opacity = this.careerConfig.careerBannerOpacity / 100;
      return {
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: `rgba(0, 0, 0, ${opacity})`,
        zIndex: 0,
      };
    },
    bannerContentStyle() {
      const horizontal = this.careerConfig.careerTextHorizontalPosition;
      const vertical = this.careerConfig.careerTextVerticalPosition;

      let justifyClass = "justify-center";
      let alignClass = "align-center";

      if (horizontal === "left") justifyClass = "justify-start";
      else if (horizontal === "right") justifyClass = "justify-end";

      if (vertical === "top") alignClass = "align-start";
      else if (vertical === "bottom") alignClass = "align-end";

      return `${justifyClass} ${alignClass}`;
    },

    bannerContentPositionStyle() {
      return {
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 2, // Above the overlay
      };
    },

    bannerTextAlignmentClass() {
      const horizontal = this.careerConfig.careerTextHorizontalPosition;

      if (horizontal === "left") return "text-left";
      if (horizontal === "right") return "text-right";
      return "text-center";
    },

    bannerTextContainerStyle() {
      return {
        maxWidth: "100%",
        maxHeight: "100%",
        overflow: "hidden",
        wordWrap: "break-word",
        wordBreak: "break-word",
        hyphens: "auto",
      };
    },
    bannerHeadlineStyle() {
      return {
        fontFamily: this.careerConfig.careerHeadlineFontFamily || "inherit",
        fontSize: this.careerConfig.careerHeadingFontSize || "3em",
        color: this.careerConfig.careerHeadlineFontColor,
        fontWeight: "bold",
        marginBottom: "16px",
      };
    },
    bannerSubTextStyle() {
      return {
        fontFamily: this.careerConfig.careerSubHeadlineFontFamily || "inherit",
        fontSize: this.careerConfig.careerSubHeadlineFontSize || "2em",
        color: this.careerConfig.careerSubHeadlineFontColor,
        lineHeight: "1.5",
      };
    },
    // Dynamic job card styling
    jobCardStyle() {
      return {
        borderRadius: "8px",
        transition: "border-color 0.3s, color 0.3s",
        border: `1px solid ${this.careerConfig.hoverColor}`,
      };
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    filteredDepartments() {
      let filteredPosts = this.jobPostList;
      if (this.searchInput) {
        filteredPosts = filteredPosts.filter((job) =>
          job.Job_Post_Name.toLowerCase().includes(
            this.searchInput.toLowerCase()
          )
        );
      }
      if (this.selectedDepartment.length > 0) {
        filteredPosts = filteredPosts.filter((job) =>
          this.selectedDepartment.includes(job.Department_Name)
        );
      }
      if (this.selectedLocation.length > 0) {
        filteredPosts = filteredPosts.filter((job) =>
          this.selectedLocation.includes(job.Location)
        );
      }
      if (
        this.selectedServiceProvider &&
        this.selectedServiceProvider.length > 0
      ) {
        filteredPosts = filteredPosts.filter((job) =>
          this.selectedServiceProvider.includes(job.Service_Provider_Name)
        );
      }
      const departments = {};
      filteredPosts.forEach((job) => {
        if (!departments[job.Department_Name]) {
          departments[job.Department_Name] = {
            departmentName: job.Department_Name,
            jobs: [],
          };
        }
        departments[job.Department_Name].jobs.push(job);
      });

      return Object.values(departments);
    },
    isJobListEmpty() {
      return this.jobPostList.length === 0;
    },
    isFilteredListEmpty() {
      return this.filteredDepartments?.length === 0;
    },
  },
  watch: {
    "careerConfig.careerHeadlineFontFamily": {
      handler(newVal) {
        if (newVal) {
          this.loadGoogleFont(newVal);
        }
      },
      immediate: true,
    },
    "careerConfig.careerSubHeadlineFontFamily": {
      handler(newVal) {
        if (newVal) {
          this.loadGoogleFont(newVal);
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.fetchPostedJobs();
    this.customColorPicker();
    this.retrieveCareerDesignerSettings();
    this.retrieveJobHeader();
  },
  methods: {
    navigateToCareersPageDetails(job) {
      this.wholeJobPostData = job;
      this.showCareerPageDetails = true;
    },
    closeAllForms(refetchCount) {
      this.showCareerPageDetails = false;
      if (refetchCount > 0) {
        this.fetchPostedJobs();
      }
    },
    // Custom Color Picker Api
    customColorPicker() {
      let vm = this;

      // First get basic org details from existing API
      vm.$apollo
        .query({
          query: CUSTOM_COLOR_PICKER,
          client: "apolloClientAO",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.customColorPicker &&
            response.data.customColorPicker.colorResult
          ) {
            const colorResult = response.data.customColorPicker.colorResult[0];
            vm.orgName = colorResult.Org_Name;
            vm.fieldForce = colorResult.Field_Force;
          }
        })
        .catch((err) => {
          vm.handleCustomColorPicker(err);
        });
    },

    // Retrieve Career Page Designer Settings
    retrieveCareerDesignerSettings() {
      let vm = this;
      vm.careersLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_GENERAL_SETTINGS,
          client: "apolloClientBA",
          variables: {
            formId: 311,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response?.data?.retrieveGeneralSettings?.generalSettings) {
            const settings =
              response.data.retrieveGeneralSettings.generalSettings;

            // Update career configuration with retrieved settings
            this.careerConfig = {
              primaryColor: settings.Primary_Color || "#1C277D",
              hoverColor: settings.Hover_Color || "#0F1B5C",
              careerBannerImage: settings.Career_Banner_Image,
              careerHeadlineText: settings.Career_Headline_Text,
              careerSubHeadlineText: settings.Career_Sub_Headline_Text,
              careerTextHorizontalPosition:
                settings.Career_Text_Horizontal_Position || "center",
              careerTextVerticalPosition:
                settings.Career_Text_Vertical_Position || "middle",
              careerBannerOpacity: settings.Career_Banner_Opacity || 30,
              careerHeadlineFontFamily:
                settings.Career_Headline_Font_Family || '"Roboto", sans-serif',
              careerHeadingFontSize:
                settings.Career_Heading_Font_Size || "48px",
              careerHeadlineFontColor:
                settings.Career_Headline_Font_Color || "#FFFFFF",
              careerSubHeadlineFontFamily:
                settings.Career_Sub_Headline_Font_Family ||
                '"Roboto", sans-serif',
              careerSubHeadlineFontSize:
                settings.Career_Sub_Headline_Font_Size || "18px",
              careerSubHeadlineFontColor:
                settings.Career_Sub_Headline_Font_Color || "#FFFFFF",
              companyLogo: settings.Company_Logo,
              careerLogo: settings.Career_Logo_Path,
            };
          } else {
            this.careerConfig.careerHeadlineText = "Career Vacancies";
            this.careerConfig.careerSubHeadlineText =
              "Chart your course, explore opportunities and set sail for success with us.";
          }
        })
        .catch((err) => {
          vm.handleCareerConfigError(err);
        })
        .finally(() => {
          vm.careersLoading = false;
        });
    },
    handleCustomColorPicker(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "career details",
        isListError: false,
      });
    },

    handleCareerConfigError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "career details",
        isListError: false,
      });
    },
    retrieveJobHeader() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_JOB_HEADER,
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.recruitmentSetting &&
            res.data.recruitmentSetting.settingResult &&
            res.data.recruitmentSetting.settingResult.length
          ) {
            this.settingResult = res.data.recruitmentSetting.settingResult[0];
          }
          vm.isLoading = false;
        })
        .catch(() => {
          vm.isLoading = false;
        });
    },
    fetchPostedJobs() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: POSTED_JOBS,
          variables: {
            employeeId: vm.loginEmployeeId,
            isDropDownCall: 1,
            searchString: "",
            formId: 16,
            action: "add",
          },
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listJobPost &&
            response.data.listJobPost.JobpostDetails
          ) {
            this.jobPostList = response.data.listJobPost.JobpostDetails.filter(
              (job) => job.Job_Post_Status === "Open"
            ).map((job) => ({
              ...job,
              Department_Name: job.Department_Name,
              Location: job.City_Name,
              Job_Type: job.Job_Type,
              Service_Provider_Name: job.Service_Provider_Name,
            }));

            this.departmentList = [
              ...new Set(
                this.jobPostList
                  .map((job) => job.Department_Name)
                  .filter((department) => department !== null)
              ),
            ];

            this.locationList = [
              ...new Set(
                this.jobPostList
                  .map((job) => job.City_Name)
                  .filter((location) => location !== null)
              ),
            ];

            this.serviceProviderList = [
              ...new Set(
                this.jobPostList
                  .map((job) => job.Service_Provider_Name)
                  .filter((provider) => provider !== null && provider !== 0)
              ),
            ];
            this.getJobpostId();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "career details",
        isListError: false,
      });
    },
    getPostingDateDifference(postingDate) {
      if (postingDate && postingDate !== "0000-00-00") {
        const postingDateObj = new Date(postingDate);
        const currentDate = new Date();
        const differenceInTime =
          currentDate.getTime() - postingDateObj.getTime();
        const differenceInDays = differenceInTime / (1000 * 3600 * 24);
        return Math.floor(differenceInDays);
      } else {
        return "";
      }
    },
    getJobpostId() {
      if (this.$route.query?.jobPostId) {
        const jobPostId = parseInt(this.$route.query.jobPostId);
        let jobPostRecord = this.getJobPostById(this.jobPostList, jobPostId);
        if (jobPostRecord) {
          this.navigateToCareersPageDetails(jobPostRecord);
        }
      }
    },
    getJobPostById(jobPosts, id) {
      return jobPosts.find((post) => post.Job_Post_Id === id);
    },

    // Helper method to get font family CSS with proper defaults
    getFontFamily(fontValue) {
      // If no font value provided, use Roboto as default
      if (!fontValue) {
        return '"Roboto", sans-serif';
      }

      // If it's already in CSS format (contains quotes), return as-is
      if (fontValue.includes('"') || fontValue.includes("'")) {
        return fontValue;
      }

      // If it's a legacy format, convert to CSS format
      // Handle old format "Font Name (Category)"
      const fontMatch = fontValue.match(/^(.+?)\s*\(/);
      if (fontMatch) {
        const fontName = fontMatch[1].trim();
        const category = fontValue.includes("serif") ? "serif" : "sans-serif";
        return `"${fontName}", ${category}`;
      }

      // Handle kebab-case format - e.g., "lilita-one"
      if (fontValue.includes("-")) {
        const properFontName = fontValue
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        return `"${properFontName}", sans-serif`;
      }

      // Fallback: wrap in quotes and add sans-serif
      return `"${fontValue}", sans-serif`;
    },

    // Dynamically load Google Font when font family changes
    loadGoogleFont(cssFont) {
      if (!cssFont) return;

      // Extract font name from CSS format like '"Lilita One", sans-serif'
      const fontMatch = cssFont.match(/^"([^"]+)"/);
      if (!fontMatch) return;

      const fontName = fontMatch[1];

      // Check if font is already loaded
      if (this.loadedFonts.has(fontName)) return;

      // Skip system fonts and local fonts that don't need loading
      const systemFonts = [
        "Arial",
        "Helvetica",
        "Times New Roman",
        "Georgia",
        "Verdana",
        "GT Walsheim", // Local commercial font
      ];
      if (systemFonts.includes(fontName)) return;

      // Create and inject Google Fonts CSS link
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = `https://fonts.googleapis.com/css2?family=${fontName.replace(
        /\s+/g,
        "+"
      )}:wght@100;200;300;400;500;600;700;800;900&display=swap`;
      link.setAttribute("data-font-family", fontName);

      // Add to document head
      document.head.appendChild(link);

      // Track that this font has been loaded
      this.loadedFonts.add(fontName);
    },

    // Helper method to get image URL (fallback method)
    getImageUrl(filename, imageType) {
      if (!filename) return null;

      // Check if it's already a full URL
      if (filename.startsWith("http://") || filename.startsWith("https://")) {
        return filename;
      }
      if (imageType === "careerLogo") {
        return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/careerLogo/${filename}`;
      } else if (imageType === "companyLogo") {
        return `${Config.publicImageS3Path}hrapp_upload/${this.orgCode}_tmp/logos/${filename}`;
      } else if (imageType === "bannerImage") {
        return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/banner/${filename}`;
      }

      return null;
    },
  },
};
</script>

<style scoped>
.filterCard {
  width: 100%;
}

.job-post-title:hover {
  border-color: 5px solid rgb(var(--v-theme-primary));
}

.hover-icon {
  opacity: 0;
  transition: opacity 0.3s;
}

.job-card:hover .hover-icon {
  opacity: 1;
}
.sticky-column {
  position: sticky;
  top: 10px;
  height: 100vh;
  overflow-y: auto;
}
@media screen and (max-width: 1024px) {
  .filterCard {
    border: 2px solid lightgrey;
    min-width: 200px;
  }
}
@media screen and (max-width: 960px) {
  .sticky-column {
    position: relative !important;
    top: auto;
    height: auto;
    overflow-y: visible;
    width: 100%;
  }
}
</style>
