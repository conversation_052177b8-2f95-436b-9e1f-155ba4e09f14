<template>
  <div>
    <v-row class="pa-2" align="center">
      <v-col cols="auto">
        <v-icon
          :color="careerConfig.primaryColor"
          class="fas fa-angle-left ml-7 cursor-pointer"
          size="large"
          @click="navigateToCareersPage"
        ></v-icon>
      </v-col>

      <!-- Company Logo -->
      <v-col cols="auto" v-if="careerConfig.companyLogo && companyLogoUrl">
        <div>
          <img
            :src="companyLogoUrl"
            alt="Company Logo"
            class="rounded"
            style="max-height: 40px; max-width: 120px; object-fit: contain"
          />
        </div>
      </v-col>

      <!-- Organization Name -->
      <v-col v-else>
        <p
          class="headingsText font-weight-bold text-h6"
          :style="{
            color: careerConfig.primaryColor,
            fontFamily: getFontFamily(careerConfig.careerHeadlineFontFamily),
          }"
        >
          {{ orgName }}
        </p>
      </v-col>
    </v-row>
    <v-row
      class="d-flex justify-space-around"
      :style="{ backgroundColor: careerConfig.primaryColor }"
    >
      <v-card :color="careerConfig.primaryColor" class="pa-4 text-center">
        <div
          class="text-h5 text-center"
          :style="{
            fontFamily: getFontFamily(careerConfig.careerHeadlineFontFamily),
          }"
        >
          {{ jobDetails.Job_Post_Name }}
        </div>
        <div
          class="d-flex mt-2 justify-center align-center text-white"
          :style="{
            fontFamily: getFontFamily(careerConfig.careerHeadlineFontFamily),
          }"
        >
          {{ jobLocation }}
          <div
            v-if="jobDetails.Experience_Level"
            style="
              background: white;
              border-radius: 50%;
              margin: 0px 5px;
              height: 10px;
              width: 10px;
            "
          ></div>
          {{ jobDetails.Experience_Level }}
        </div>
      </v-card>
    </v-row>
    <v-row>
      <v-col cols="12" style="padding: 5px 20% 5px 20%">
        <div class="d-flex justify-end align-center">
          <v-btn
            variant="elevated"
            rounded="lg"
            size="large"
            :color="careerConfig.primaryColor"
            class="pa-3 mt-2 text-button"
            :style="{
              fontFamily: getFontFamily(careerConfig.careerHeadlineFontFamily),
            }"
            @click="navigateToUrl(jobDetails.Job_Post_Id)"
          >
            Apply for this job
          </v-btn>
        </div>
        <div style="font-size: 20px" class="mt-10 text-body-1">
          <div
            v-if="!sanitizedJobDescription"
            class="pa-5 d-flex bg-hover justify-center align-center"
            style="margin: 2% 0 10px"
          >
            No job description for this job post
          </div>
          <div
            v-else
            ref="editorView"
            class="quill-editorView"
            :style="{
              fontFamily: getFontFamily(careerConfig.careerHeadlineFontFamily),
            }"
          ></div>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import DOMPurify from "dompurify";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
export default {
  name: "CareerPageDetails",
  emits: ["close-details-form"],
  data() {
    return {
      loadedFonts: new Set(), // Track loaded fonts to avoid duplicates
    };
  },
  computed: {
    sanitizedJobDescription() {
      let jobDescription = this.jobDetails?.Job_Description
        ? this.convertEmojiCodepointsToEmojis(this.jobDetails.Job_Description)
        : "";
      return DOMPurify.sanitize(jobDescription);
    },
    jobLocation() {
      const { City_Name, State_Name, Country_Name } = this.jobDetails || {};

      return [City_Name, State_Name, Country_Name].filter(Boolean).join(", ");
    },
  },
  props: {
    jobDetails: {
      type: Object,
      required: true,
    },
    orgName: {
      type: String,
      required: true,
    },
    careerConfig: {
      type: Object,
      default: () => ({
        primaryColor: "#1C277D",
        secondaryColor: "#F1F5F9",
        hoverColor: "#0F1B5C",
        companyLogo: null,
      }),
    },
    companyLogoUrl: {
      type: String,
      default: null,
    },
  },
  watch: {
    "careerConfig.careerHeadlineFontFamily": {
      handler(newVal) {
        if (newVal) {
          this.loadGoogleFont(newVal);
        }
      },
      immediate: true,
    },
    "careerConfig.careerSubHeadlineFontFamily": {
      handler(newVal) {
        if (newVal) {
          this.loadGoogleFont(newVal);
        }
      },
      immediate: true,
    },
  },
  mounted() {
    window.scrollTo(0, 0);
    this.initQuillEditor();
  },
  methods: {
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editorView, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.quill.root.innerHTML = this.jobDetails?.Job_Description
        ? this.convertEmojiCodepointsToEmojis(this.jobDetails.Job_Description)
        : "";
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
      });
      this.quill.enable(false);
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editorView.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    navigateToUrl(item) {
      window.open(
        this.$store.getters.baseUrl + `v3/job-candidates?jobPostId=${item}`,
        "_blank"
      );
    },
    navigateToCareersPage() {
      this.$emit("close-details-form");
    },
    convertEmojiCodepointsToEmojis(text) {
      return text.replace(/\[EMOJI:([0-9a-f-]+)\]/gi, (match, codePoints) => {
        // Split by dash if there are multiple code points
        const codePointArray = codePoints.split("-");

        // Convert each hex code point back to a character and join them
        const emoji = codePointArray
          .map((hex) => String.fromCodePoint(parseInt(hex, 16)))
          .join("");

        return emoji;
      });
    },

    // Helper method to get font family CSS with proper defaults
    getFontFamily(fontValue) {
      // If no font value provided, use Roboto as default
      if (!fontValue) {
        return '"Roboto", sans-serif';
      }

      // If it's already in CSS format (contains quotes), return as-is
      if (fontValue.includes('"') || fontValue.includes("'")) {
        return fontValue;
      }

      // If it's a legacy format, convert to CSS format
      // Handle old format "Font Name (Category)"
      const fontMatch = fontValue.match(/^(.+?)\s*\(/);
      if (fontMatch) {
        const fontName = fontMatch[1].trim();
        const category = fontValue.includes("serif") ? "serif" : "sans-serif";
        return `"${fontName}", ${category}`;
      }

      // Handle kebab-case format - e.g., "lilita-one"
      if (fontValue.includes("-")) {
        const properFontName = fontValue
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        return `"${properFontName}", sans-serif`;
      }

      // Fallback: wrap in quotes and add sans-serif
      return `"${fontValue}", sans-serif`;
    },

    // Dynamically load Google Font when font family changes
    loadGoogleFont(cssFont) {
      if (!cssFont) return;

      // Extract font name from CSS format like '"Lilita One", sans-serif'
      const fontMatch = cssFont.match(/^"([^"]+)"/);
      if (!fontMatch) return;

      const fontName = fontMatch[1];

      // Check if font is already loaded
      if (this.loadedFonts.has(fontName)) return;

      // Skip system fonts that don't need loading
      const systemFonts = [
        "Arial",
        "Helvetica",
        "Times New Roman",
        "Georgia",
        "Verdana",
        "GT Walsheim", // Local commercial font
      ];
      if (systemFonts.includes(fontName)) return;

      // Create and inject Google Fonts CSS link
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = `https://fonts.googleapis.com/css2?family=${fontName.replace(
        /\s+/g,
        "+"
      )}:wght@100;200;300;400;500;600;700;800;900&display=swap`;
      link.setAttribute("data-font-family", fontName);

      // Add to document head
      document.head.appendChild(link);

      // Track that this font has been loaded
      this.loadedFonts.add(fontName);
    },
  },
};
</script>
<style scoped>
.headingsText {
  margin: 1em;
}
.quill-editorView {
  height: auto;
}
::v-deep .ql-toolbar.ql-snow {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
::v-deep .ql-editor {
  padding: 0px;
}
</style>
