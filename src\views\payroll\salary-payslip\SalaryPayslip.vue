<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <v-row justify="center" v-if="originalList.length > 0">
          <v-col cols="12" md="9" class="d-flex justify-end">
            <EmployeeDefaultFilterMenu
              class="d-flex justify-end"
              :isFilter="true"
              :isDefaultFilter="false"
              @apply-emp-filter="filterAppliedCount += 1"
              @reset-emp-filter="resetFilter()"
            >
              <template v-slot:new-filter>
                <v-row class="mt-5">
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-autocomplete
                      v-model="filterObj.selectedEmployees"
                      label="Employee"
                      variant="solo"
                      density="compact"
                      color="primary"
                      multiple
                      closable-chips
                      chips
                      :items="employeeList"
                    ></v-autocomplete>
                  </v-col>
                  <!-- <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-autocomplete
                      v-model="filterObj.selectedStatus"
                      label="Status"
                      variant="solo"
                      density="compact"
                      color="primary"
                      multiple
                      closable-chips
                      chips
                      :items="['Yet To Finalize', 'Unpaid', 'Paid']"
                    ></v-autocomplete>
                  </v-col> -->
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-autocomplete
                      v-model="filterObj.selectedPayPeriod"
                      label="Pay Period"
                      variant="solo"
                      density="compact"
                      color="primary"
                      multiple
                      closable-chips
                      chips
                      :items="['First Half', 'Second Half']"
                    ></v-autocomplete>
                  </v-col>
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-autocomplete
                      v-model="filterObj.selectedDepartment"
                      label="Department"
                      variant="solo"
                      density="compact"
                      color="primary"
                      multiple
                      closable-chips
                      chips
                      :items="departmentList"
                    ></v-autocomplete>
                  </v-col>
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-autocomplete
                      v-model="filterObj.selectedLoacation"
                      label="Location"
                      variant="solo"
                      density="compact"
                      color="primary"
                      multiple
                      closable-chips
                      chips
                      :items="locationList"
                    ></v-autocomplete>
                  </v-col>
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-autocomplete
                      v-model="filterObj.selectedOrganizationGroup"
                      label="Business Unit"
                      variant="solo"
                      density="compact"
                      color="primary"
                      multiple
                      closable-chips
                      chips
                      :items="organizationGroupList"
                    ></v-autocomplete>
                  </v-col>
                </v-row>
              </template>
            </EmployeeDefaultFilterMenu>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
    <ProfileCard class="sub-tabs" v-if="formAccess?.view">
      <FormTab :model-value="openedSubTab">
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          color="primary"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            <div class="d-flex align-center">
              {{ tab.label }}
            </div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard>
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item
          :value="currentTabItem"
          :class="windowWidth < 1280 ? 'mb-14' : ''"
        >
          <BimonthlyPaySlip
            v-if="openedSubTab === 'bimonthly'"
            :originalList="originalList"
            :listLoading="listLoading"
            :isErrorInList="isErrorInList"
            :errorContent="errorContent"
            :filterAppliedCount="filterAppliedCount"
            :filterObj="filterObj"
            :formAccess="formAccess"
            @refetch-list="fetchPaySlipList()"
            @reset-filter="resetFilter()"
            @change-month="fetchPaySlipList($event)"
          ></BimonthlyPaySlip>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { RETRIEVE_BIMONTHLY_PAYSLIP_LIST } from "@/graphql/payroll/salaryPayslip.js";
import moment from "moment";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu.vue";
const BimonthlyPaySlip = defineAsyncComponent(() =>
  import("./bi-monthly/BimonthlyPaySlip.vue")
);

export default {
  name: "SalaryPayslip",
  components: {
    BimonthlyPaySlip,
    EmployeeDefaultFilterMenu,
  },
  data() {
    return {
      currentTabItem: "",
      openedSubTab: "bimonthly",
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      originalList: [],
      filterObj: {
        selectedEmployees: [],
        selectedStatus: [],
        selectedPayPeriod: [],
        selectedDepartment: [],
        selectedLoacation: [],
        selectedOrganizationGroup: [],
      },
      filterAppliedCount: 0,
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    formAccess() {
      let formAccessRights = this.accessRights(364);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    fullAndFinalAccess() {
      let formAccess = this.accessRights(239);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else return false;
    },
    mainTabs() {
      let tabs = ["Salary Payslip"];
      if (this.fullAndFinalAccess && this.fullAndFinalAccess.view) {
        tabs.push("Full & Final Settlement");
      }
      return tabs;
    },
    subTabItems() {
      let initialTabs = [
        {
          label: this.$t("payroll.monthly"),
          value: "monthly",
          disable: false,
        },
        {
          label: this.$t("payroll.bimonthly"),
          value: "bimonthly",
          disable: false,
        },
      ];
      return initialTabs;
    },
    isActiveSubTab() {
      return (val) => {
        return this.openedSubTab === val;
      };
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    employeeList() {
      let employees = new Set(
        this.originalList.map((item) => item.Employee_Name)
      );
      return Array.from(employees).filter(Boolean);
    },
    departmentList() {
      let departments = new Set(
        this.originalList.map((item) => item.Department_Name)
      );
      return Array.from(departments).filter(Boolean);
    },
    locationList() {
      let locations = new Set(
        this.originalList.map((item) => item.Location_Name)
      );
      return Array.from(locations).filter(Boolean);
    },
    organizationGroupList() {
      let organizationGroups = new Set(
        this.originalList.map((item) => item.Organization_Unit_Name)
      );
      return Array.from(organizationGroups).filter(Boolean);
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf("Salary Payslip");
    this.fetchPaySlipList();
  },
  methods: {
    onTabChange(tabName) {
      if (tabName == "Full & Final Settlement") {
        this.$router.push("/payroll/full-and-final-settlement");
      }
    },
    onChangeSubTabs(tab) {
      if (tab === "monthly") {
        window.location.href = this.baseUrl + "payroll/salary-payslip";
      }
    },
    fetchPaySlipList(date = null) {
      let vm = this;
      vm.listLoading = true;
      vm.isErrorInList = false;
      vm.errorContent = "";
      vm.$apollo
        .query({
          query: RETRIEVE_BIMONTHLY_PAYSLIP_LIST,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: {
            formId: 364,
            year: date
              ? parseInt(moment(date).format("YYYY"))
              : moment().year(),
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listSalaryPayslip &&
            response.data.listSalaryPayslip.salaryPayslips
          ) {
            let tempdata = response.data.listSalaryPayslip.salaryPayslips;
            tempdata.forEach(
              (item) =>
                (item.Total_Salary = (item.Total_Salary ?? 0).toFixed(2))
            );
            tempdata.sort((a, b) => {
              const order = {
                "Second Half": 0, // comes first
                "First Half": 1, // comes later
              };
              return order[a.Pay_Period] - order[b.Pay_Period];
            });
            vm.originalList = tempdata;
          } else {
            vm.originalList = [];
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          vm.handleFetchError(err);
        });
    },
    handleFetchError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "bi-monthly payslip",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    resetFilter() {
      this.filterObj = {
        selectedEmployees: [],
        selectedStatus: [],
        selectedPayPeriod: [],
        selectedDepartment: [],
        selectedLoacation: [],
        selectedOrganizationGroup: [],
      };
      this.filterAppliedCount = 0;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
  },
};
</script>
<style scoped>
.container {
  padding: 118px 0px 0px 0px;
}
.sub-tabs {
  position: fixed;
  top: 118px;
  z-index: 100;
}
</style>
