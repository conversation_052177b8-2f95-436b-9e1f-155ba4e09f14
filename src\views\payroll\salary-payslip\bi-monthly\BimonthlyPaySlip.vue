<template>
  <div class="mt-3 mx-5">
    <div v-if="listLoading" class="mt-5">
      <!-- Skeleton loaders -->
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      :button-text="$t('settings.retry')"
      @button-click="onActions('refetch list')"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="itemList.length === 0"
      key="no-results-screen"
      :main-title="
        originalList.length === 0
          ? ''
          : $t('settings.noRecordsFound', {
              formName: `${$t('payroll.bimonthly')} payslip`,
            })
      "
      :isSmallImage="originalList.length === 0"
      :image-name="originalList.length === 0 ? '' : 'common/no-records'"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row
            :style="originalList.length === 0 ? 'background: white' : ''"
            class="rounded-lg pa-5 mb-4"
          >
            <v-col v-if="originalList.length === 0" cols="12">
              <NotesCard
                :notes="$t('payroll.bimonthlyEmptyText1')"
                backgroundColor="transparent"
                class="mb-4"
              >
              </NotesCard>
              <NotesCard
                :notes="$t('payroll.bimonthlyEmptyText2')"
                backgroundColor="transparent"
                class="mb-4"
              >
              </NotesCard>
            </v-col>
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                v-if="originalList.length > 0"
                class="bg-white my-2 ml-2"
                rounded="lg"
              >
                <template v-slot:prepend>
                  <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
                </template>
                {{ formattedSelectedMonth }}
                <v-menu
                  v-model="dateMenu"
                  activator="parent"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                >
                  <Datepicker
                    v-model="selectedMonthYear"
                    :inline="true"
                    :format="'MMMM, yyyy'"
                    maximum-view="year"
                    minimum-view="month"
                    :open-date="selectedMonthYear"
                    :disabled-dates="disabledDates"
                    @update:modelValue="onChangeMonth($event)"
                  />
                </v-menu>
              </v-btn>
              <v-btn
                v-if="originalList.length > 0"
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="$emit('reset-filter')"
              >
                {{ $t("settings.resetFilterSearch") }}
              </v-btn>
              <v-btn
                v-if="originalList.length === 0"
                rounded="lg"
                class="mt-1"
                color="transparent"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="onActions('refetch list')"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <div
        style="width: 100%"
        class="d-flex align-center py-1 mb-2"
        :class="{
          'justify-end': !isMobileView,
          'justify-center': isMobileView,
        }"
      >
        <div :class="isMobileView ? 'mt-2' : ''">
          <v-btn class="bg-white my-2 ml-2" rounded="lg">
            <template v-slot:prepend>
              <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
            </template>
            {{ formattedSelectedMonth }}
            <v-menu
              v-model="dateMenu"
              activator="parent"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
            >
              <Datepicker
                v-model="selectedMonthYear"
                :inline="true"
                :format="'MMMM, yyyy'"
                maximum-view="year"
                minimum-view="month"
                :open-date="selectedMonthYear"
                :disabled-dates="disabledDates"
                @update:modelValue="onChangeMonth($event)"
              />
            </v-menu>
          </v-btn>
          <v-btn
            rounded="lg"
            color="transparent"
            variant="flat"
            :size="isMobileView ? 'small' : 'default'"
            @click="onActions('refetch list')"
          >
            <v-icon>fas fa-redo-alt</v-icon>
          </v-btn>
          <v-menu v-model="openMoreMenu" transition="scale-transition">
            <template v-slot:activator="{ props }">
              <v-btn variant="plain" v-bind="props">
                <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                <v-icon v-else>fas fa-caret-up</v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="action in moreActions"
                :key="action"
                @click="onActions(action)"
              >
                <v-hover>
                  <template v-slot:default="{ isHovering, props }">
                    <v-list-item-title
                      v-bind="props"
                      class="pa-3"
                      :class="{
                        'bg-hover': isHovering,
                      }"
                      >{{ action }}</v-list-item-title
                    >
                  </template>
                </v-hover>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </div>
      <v-data-table
        :headers="tableHeaders"
        :items="itemList"
        fixed-header
        :height="$store.getters.getTableHeightBasedOnScreenSize(290, itemList)"
        :items-per-page="50"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          {
            value: -1,
            title: '$vuetify.dataFooter.itemsPerPageAll',
          },
        ]"
        :show-select="formAccess?.delete ? true : false"
      >
        <template v-slot:[`header.data-table-select`]>
          <v-checkbox-btn
            v-model="selectAllBox"
            color="primary"
            false-icon="far fa-circle"
            true-icon="fas fa-check-circle"
            indeterminate-icon="fas fa-minus-circle"
            class="mt-1"
            @change="toggleSelectAll(selectAllBox)"
          />
        </template>
        <template v-slot:item="{ item }">
          <tr
            class="data-table-tr bg-white cursor-pointer"
            @click="onActions('view', item)"
            :class="[
              isMobileView ? ' v-data-table__mobile-table-row ma-0 mt-2' : '',
            ]"
          >
            <td
              v-if="formAccess?.delete"
              :class="isMobileView ? 'mt-3 mb-n5' : ''"
              @click.stop="
                {
                }
              "
            >
              <v-checkbox-btn
                v-model="item.isSelected"
                color="primary"
                false-icon="far fa-circle"
                true-icon="fas fa-check-circle"
                class="mt-n2 ml-n2"
                @click.stop="checkAllSelected()"
              />
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Employee</div>
              <section>
                <div style="max-width: 200px" class="text-truncate">
                  <span class="text-primary text-body-2 font-weight-medium">
                    <v-tooltip :text="item.Employee_Name" location="bottom">
                      <template v-slot:activator="{ props }">
                        <span
                          v-bind="
                            item.Employee_Name && item.Employee_Name.length > 20
                              ? props
                              : ''
                          "
                          >{{ checkNullValue(item.Employee_Name) }}</span
                        >
                      </template>
                    </v-tooltip>
                    <v-tooltip
                      :text="item.User_Defined_EmpId?.toString()"
                      location="bottom"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          v-if="item.User_Defined_EmpId"
                          v-bind="
                            item.User_Defined_EmpId &&
                            item.User_Defined_EmpId.length > 20
                              ? props
                              : ''
                          "
                          class="text-grey"
                        >
                          {{ checkNullValue(item.User_Defined_EmpId) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </span>
                </div>
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Salary Month
              </div>
              <section>
                {{ checkNullValue(item.Formatted_Salary_Month) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">Pay Period</div>
              <section>
                {{ checkNullValue(item.Pay_Period) }}
              </section>
            </td>
            <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Net Pay {{ payrollCurrency ? `(in ${payrollCurrency})` : `` }}
              </div>
              <section>
                {{ checkNullValue(item.Total_Salary) }}
              </section>
            </td>
            <!-- <td
              :class="isMobileView ? 'd-flex justify-space-between' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div v-if="isMobileView" class="font-weight-bold">
                Payment Status
              </div>
              <section>
                <span :class="statusColor(item.Payment_Status)">
                  {{ checkNullValue(item.Payment_Status) }}
                </span>
              </section>
            </td> -->
            <td
              :class="isMobileView ? 'd-flex align-center' : ''"
              :style="isMobileView ? `width: ${windowWidth - 40}px` : ''"
            >
              <div
                v-if="isMobileView"
                class="font-weight-bold"
                style="width: 60%"
              ></div>
              <section
                class="d-flex justify-end align-center"
                style="width: 100%"
              >
                <ActionMenu
                  v-if="itemActions(item)?.length"
                  :accessRights="checkAccess"
                  @selected-action="onAction($event, item)"
                  :actions="itemActions(item)"
                  :isPresentTooltip="false"
                  :tooltipMessage="getTooltipMessage(item)"
                  :tooltipActionButtons="getDisabledButtons(item)"
                  :disableActionButtons="getDisabledButtons(item)"
                  iconColor="grey"
                />
                <div v-else>
                  <p>-</p>
                </div>
              </section>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
    <AppLoading v-if="isLoading" />
    <AppWarningModal
      v-if="deleteModel"
      :open-modal="deleteModel"
      confirmation-heading="Are you sure to delete the selected record?"
      icon-name="far fa-times-circle"
      icon-Size="75"
      @close-warning-modal="closeAllPopups()"
      @accept-modal="deletePayslip()"
    />
    <FilePreviewModal
      v-if="openDocumentModal"
      :fileName="selectedItem?.S3_FileName"
      :folder-name="folderName"
      fileRetrieveType="documents"
      @close-preview-modal="closeAllPopups()"
    />
    <AppWarningModal
      v-if="multiDeleteModel"
      :open-modal="multiDeleteModel"
      confirmation-heading="Are you sure to delete the selected records?"
      icon-name="fas fa-trash"
      icon-Size="75"
      @close-warning-modal="closeAllPopups()"
      @accept-modal="deletePayslip(true)"
    />
  </div>
</template>
<script>
import NotesCard from "@/components/helper-components/NotesCard.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import FilePreviewModal from "@/components/custom-components/FilePreviewModal.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
import { checkNullValue } from "@/helper";
import moment from "moment";
import Datepicker from "vuejs3-datepicker";
export default {
  name: "BimonthlyPaySlip",
  components: {
    NotesCard,
    ActionMenu,
    FilePreviewModal,
    Datepicker,
  },
  mixins: [FileExportMixin],
  emits: ["refetch-list", "reset-filter", "change-month"],
  props: {
    originalList: {
      type: Array,
      required: true,
      default: () => [],
    },
    listLoading: {
      type: Boolean,
      default: false,
    },
    isErrorInList: {
      type: Boolean,
      default: false,
    },
    errorContent: {
      type: String,
      default: "",
    },
    filterAppliedCount: {
      type: Number,
      default: 0,
    },
    filterObj: {
      type: Object,
      default: () => ({}),
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
  },
  data() {
    return {
      openMoreMenu: false,
      itemList: [],
      selectedMonthYear: new Date(),
      dateMenu: false,
      isLoading: false,
      selectedItem: null,
      deleteModel: false,
      multiDeleteModel: false,
      openDocumentModal: false,
      folderName: "",
      selectAllBox: false,
    };
  },
  computed: {
    checkAccess() {
      let havingAccess = {};
      havingAccess["view payslip"] =
        this.formAccess && this.formAccess.view ? 1 : 0;
      havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? 1 : 0;
      return havingAccess;
    },
    baseUrl() {
      // return "https://calendarmonth.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    payrollCurrency() {
      return this.$store.state.payrollCurrency;
    },
    moreActions() {
      let list = [this.$t("settings.export")];
      if (this.formAccess?.delete && this.itemList.some((el) => el.isSelected))
        list.push("Delete");
      return list;
    },
    tableHeaders() {
      return [
        {
          title: "Employee",
          key: "Employee_Name",
          fixed: true,
          width: "30%",
        },
        { title: "Salary Month", key: "Salary_Month" },
        { title: "Pay Period", key: "Pay_Period" },
        {
          title: `Net Pay${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          key: "Total_Salary",
        },
        // { title: "Payment Status", key: "Payment_Status" },
        { title: "Actions", key: "", sortable: false, align: "end" },
      ];
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    statusColor() {
      return (status) => {
        if (status?.toLowerCase() === "paid") {
          return "text-green";
        } else if (status?.toLowerCase() === "unpaid") {
          return "text-red";
        }
        return "text-amber";
      };
    },
    formattedSelectedMonth() {
      return moment(this.selectedMonthYear).format("MMMM, YYYY");
    },
    disabledDates() {
      return {
        from: this.maxPayslipMonth,
      };
    },
    maxPayslipMonth() {
      const maxMonth = this.originalList.reduce((max, item) => {
        return moment(item.Salary_Month, "M,YYYY").isAfter(
          moment(max.Salary_Month, "M,YYYY")
        )
          ? item
          : max;
      }).Salary_Month;
      return moment(maxMonth, "M,YYYY").toDate();
    },
    getTooltipMessage() {
      return (item) => {
        if (!item.S3_FileName) {
          return "Payslip is being generated. This may take a moment. If the file name doesn’t appear shortly, please refresh the page and try again.";
        }
      };
    },
    getDisabledButtons() {
      return (item) => {
        let disabledButtons = [];
        if (!item.S3_FileName) {
          disabledButtons.push("View Payslip");
        }
        return disabledButtons;
      };
    },
  },
  watch: {
    selectedMonthYear: {
      handler: function (newVal, oldVal) {
        if (
          newVal &&
          oldVal &&
          moment(newVal).format("YYYY") != moment(oldVal).format("YYYY")
        )
          this.$emit("change-month", newVal);
      },
      deep: true,
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
    originalList(list) {
      this.itemList = list;
      this.selectedMonthYear = this.maxPayslipMonth;
      this.closeAllPopups();
      this.applyFilter();
    },
    filterAppliedCount(val) {
      if (val) {
        this.applyFilter();
      } else {
        this.itemList = this.originalList;
      }
    },
  },
  mounted() {
    this.itemList = this.originalList;
  },
  methods: {
    checkNullValue,
    onAction(action, item) {
      this.selectedItem = item;
      if (action === "View Payslip") {
        if (item.S3_FileName) {
          const year = item.Salary_Month?.split(",")[1];
          this.folderName = `Salary Payslip/${year}/BiMonthly`;
          this.openDocumentModal = true;
        } else {
          let snackbarData = {
            isOpen: true,
            message:
              "Payslip is being generated. This may take a moment. If the file name doesn’t appear shortly, please refresh the page and try again.",
            type: "warning",
          };
          this.showAlert(snackbarData);
        }
      } else if (action === "Delete") this.deleteModel = true;
    },
    closeAllPopups() {
      this.openDocumentModal = false;
      this.deleteModel = false;
      this.selectedItem = null;
      this.folderName = "";
      this.selectAllBox = false;
      this.multiDeleteModel = false;
      this.itemList?.forEach((item) => {
        item.isSelected = false;
      });
      this.selectAllBox = false;
    },
    itemActions() {
      let list = [];
      if (this.formAccess?.view) list.push("View Payslip");
      if (this.formAccess?.delete) list.push("Delete");
      return list;
    },
    onActions(action) {
      if (action === "refetch list") {
        this.$emit("refetch-list");
      } else if (action === this.$t("settings.export")) {
        this.exportReportFile();
      } else if (action === "Delete") {
        this.multiDeleteModel = true;
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: "Employee Id",
          key: "User_Defined_EmpId",
        },
        { header: "Employee Name", key: "Employee_Name" },
        { header: "Salary Month", key: "Formatted_Salary_Month" },
        { header: "Pay Period", key: "Pay_Period" },
        {
          header: `Net Pay${
            this.payrollCurrency ? ` (in ${this.payrollCurrency})` : ""
          }`,
          key: "Total_Salary",
        },
        { header: "Payment Status", key: "Payment_Status" },
        { header: "Generated By", key: "Generated_By" },
        { header: "Department", key: "Department_Name" },
        { header: "Location", key: "Location_Name" },
        { header: "Organization Unit", key: "Organization_Unit_Name" },
      ];
      let exportOptions = {
        fileExportData: this.itemList,
        fileName: "Bi-Monthly Payslip",
        sheetName: "Bi-Monthly Payslip",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    toggleSelectAll(value) {
      if (value) {
        this.itemList.forEach((item) => {
          item.isSelected = true;
        });
      } else {
        this.itemList.forEach((item) => {
          item.isSelected = false;
        });
      }
    },
    checkAllSelected() {
      const selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    onChangeMonth(event) {
      this.selectedMonthYear = event;
      this.dateMenu = false;
      this.applyFilter();
    },
    applyFilter() {
      let filteredList = this.originalList;
      if (this.selectedMonthYear) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Salary_Month == moment(this.selectedMonthYear).format("M,YYYY")
          );
        });
      }
      if (this.filterObj.selectedEmployees.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.filterObj.selectedEmployees.includes(item.Employee_Name);
        });
      }
      if (this.filterObj.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.filterObj.selectedStatus.includes(item.Payment_Status);
        });
      }
      if (this.filterObj.selectedPayPeriod.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.filterObj.selectedPayPeriod.includes(item.Pay_Period);
        });
      }
      if (this.filterObj.selectedDepartment.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.filterObj.selectedDepartment.includes(
            item.Department_Name
          );
        });
      }
      if (this.filterObj.selectedLoacation.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.filterObj.selectedLoacation.includes(item.Location_Name);
        });
      }
      if (this.filterObj.selectedOrganizationGroup.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.filterObj.selectedOrganizationGroup.includes(
            item.Organization_Unit_Name
          );
        });
      }
      this.itemList = filteredList;
    },
    async deletePayslip(isMultiDelete = false) {
      let vm = this;
      vm.isLoading = true;
      try {
        const apiObj = {
          url: vm.baseUrl + "payroll/salary-payslip/delete-bwd-salary-payslip",
          type: "POST",
          dataType: "json",
          data: {
            payslipId: isMultiDelete
              ? vm.itemList
                  .filter((el) => el.isSelected)
                  .map((el) => el.Payslip_Id)
              : [vm.selectedItem?.Payslip_Id],
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response?.success) {
          vm.showAlert({
            isOpen: true,
            type: "success",
            message: response?.msg || "Payslip deleted successfully.",
          });
          vm.$emit("refetch-list");
        } else
          vm.showAlert({
            isOpen: true,
            type: "warning",
            message:
              response?.msg ||
              "Something went wrong while deleting the payslip. Please try after some time.",
          });
      } catch (error) {
        vm.showAlert({
          isOpen: true,
          type: "warning",
          message:
            error ||
            "Something went wrong while deleting the payslip. Please try after some time.",
        });
      } finally {
        vm.isLoading = false;
        vm.closeAllPopups();
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
