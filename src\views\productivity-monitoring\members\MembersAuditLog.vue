<template>
  <div>
    <div v-if="mainTabs?.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="!selectedForm" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else class="mt-5">
            <div
              class="d-flex flex-wrap align-center"
              :class="isMobileView ? 'flex-column' : ''"
              style="justify-content: space-between"
            >
              <div
                class="d-flex align-center flex-wrap"
                :class="isMobileView ? 'justify-center' : ''"
              >
                <CustomSelect
                  v-model="selectedMember"
                  :items="membersList"
                  :itemSelected="selectedMember"
                  :isAutoComplete="true"
                  clearable
                  :is-loading="membersLoading"
                  item-title="empNameId"
                  item-value="employee_id"
                  class="mt-3"
                  label="Member"
                  density="compact"
                  min-width="200px"
                  max-width="300px"
                  @selected-item="refetchList()"
                />
                <v-btn
                  class="bg-white mb-3 ml-2"
                  :style="'width: max-content'"
                  :size="isMobileView ? 'small' : 'default'"
                  rounded="lg"
                  @click="$refs.datePicker.fp.open(), resetDateFilters()"
                >
                  <v-icon color="primary" size="14">fas fa-calendar-alt</v-icon>
                  <span class="text-caption px-1 pt-1">Date:</span>
                  <flat-pickr
                    ref="datePicker"
                    v-model="selectedMonthYear"
                    :config="flatPickerOptions"
                    placeholder="Select Date Range"
                    class="ml-2 mt-1 date-range-picker-custom-bg"
                    style="
                      outline: 0px;
                      color: var(--v-primary-base);
                      width: 170px;
                    "
                    @onChange="onChangeDateRange"
                  />
                </v-btn>
              </div>
              <div
                class="d-flex align-center"
                :class="isMobileView ? 'justify-center' : 'justify-end'"
              >
                <v-btn
                  rounded="lg"
                  color="transparent"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu
                  v-if="activityLogs && activityLogs.length > 0"
                  class="my-1 ml-n1"
                  transition="scale-transition"
                >
                  <template v-slot:activator="{ props }">
                    <v-btn variant="plain" v-bind="props">
                      <v-icon>fas fa-ellipsis-v</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="exportAuditLogs(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              hover: isHovering,
                            }"
                          >
                            <v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon>
                            {{ action.key }}
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>
            <v-card class="pa-4 my-4 rounded-lg overflow-visible">
              <ActivityLog
                :formId="374"
                :logsFormId="[selectedForm]"
                :unique-id="selectedMember ? selectedMember : 0"
                :startDate="startDate"
                :endDate="endDate"
                :callListApi="callListAPi"
                @activity-logs-data="onActivityLogsUpdated($event)"
              />
            </v-card>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
</template>
<script>
import moment from "moment";
import { defineAsyncComponent } from "vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import flatPickr from "vue-flatpickr-component";
import "flatpickr/dist/flatpickr.css";
import { GET_TEAM_MEMBERS } from "@/graphql/data-loss-prevention/keyLoggerQueries";
import FileExportMixin from "@/mixins/FileExportMixin";
import { convertUTCToLocal } from "@/helper";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ActivityLog = defineAsyncComponent(() =>
  import(
    "@/views/recruitment/job-candidates/job-candidates-details/activity-log/ActivityLog.vue"
  )
);
export default {
  name: "MembersAuditLog",
  mixins: [FileExportMixin],
  components: {
    EmployeeDefaultFilterMenu,
    CustomSelect,
    flatPickr,
    ActivityLog,
  },
  data() {
    return {
      currentTabItem: "",
      selectedForm: "",
      selectedMonthYear: null,
      startDate: null,
      endDate: null,
      callListAPi: false,
      selectedMember: null,
      membersLoading: false,
      membersList: [],
      activityLogs: [],
    };
  },
  computed: {
    landedFormName() {
      return this.$t("settings.auditLog");
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    formAccess() {
      let formAccessRights = this.accessRights("374");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    membersFormAccess() {
      let formAccessRights = this.accessRights("200");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    mainTabs() {
      let tabs = [];
      if (this.membersFormAccess) tabs.push(this.$t("settings.members"));
      if (this.formAccess) tabs.push(this.$t("settings.auditLog"));
      return tabs;
    },
    flatPickerOptions() {
      let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
      orgDateFormat = orgDateFormat.replace(/DD/g, "d");
      orgDateFormat = orgDateFormat.replace(/MM/g, "m");
      orgDateFormat = orgDateFormat.replace(/YYYY/g, "Y");
      return {
        mode: "range",
        dateFormat: orgDateFormat,
      };
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formatDate() {
      return (date) => {
        if (moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    moreActions() {
      return [
        {
          key: this.$t("common.export"),
          icon: "fas fa-file-export",
        },
      ];
    },
    formList() {
      let forms = [];
      if (this.checkAccessForListForms(200)) {
        forms.push({
          label:
            this.accessRights("200")?.customFormName ||
            this.$t("settings.members"),
          value: 200,
        });
      }
      return forms;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.getCurrentDateRange();
    this.fetchTeamMembers();
  },
  methods: {
    refetchList() {
      this.callListAPi = true;
      setTimeout(() => {
        this.callListAPi = false;
      }, 1000);
    },
    fetchTeamMembers() {
      let vm = this;
      vm.membersLoading = true;
      vm.$apollo
        .query({
          query: GET_TEAM_MEMBERS,
          client: "apolloClientE",
          variables: {
            formId: 374,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response?.data?.getTeamMembers?.teamMembers) {
            const teamMembers =
              JSON.parse(response.data.getTeamMembers.teamMembers) || [];
            vm.membersList =
              teamMembers?.map((item) => ({
                ...item,
                empNameId: item?.user_defined_employee_id
                  ? item.employee_name + " - " + item.user_defined_employee_id
                  : item.employee_name,
              })) || [];
          } else vm.membersList = [];

          vm.membersLoading = false;
        })
        .catch(() => {
          vm.membersList = [];
          vm.membersLoading = false;
        });
    },
    checkAccessForListForms(formId) {
      let formAccessRights = this.accessRights(formId);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    getCurrentDateRange() {
      // Leave Date From = Current Month
      const leaveDateFrom = moment().startOf("month").format("YYYY-MM-DD");
      const leaveDateTo = moment().endOf("month").format("YYYY-MM-DD");
      // Set the Date Array instead of String
      this.selectedMonthYear = [
        this.formatDate(leaveDateFrom),
        this.formatDate(leaveDateTo),
      ];
      this.startDate = leaveDateFrom;
      this.endDate = leaveDateTo;
      this.selectedForm = 200;
      this.refetchList();
    },
    onChangeDateRange(selectedDates) {
      if (selectedDates?.length > 1) {
        // Format the start and end dates
        let startDate = moment(selectedDates[0]).format("YYYY-MM-DD");
        let endDate = moment(selectedDates[1]).format("YYYY-MM-DD");

        // Calculate the difference in days
        let dateDifference = moment(endDate).diff(moment(startDate), "days");
        const differenceAllowed = 365;
        // Prevent if the range is more than difference allowed days
        if (dateDifference > differenceAllowed) {
          this.selectedMonthYear = [
            this.formatDate(this.startDate),
            this.formatDate(this.endDate),
          ];
          this.showAlert({
            isOpen: true,
            message: `The selected date range cannot exceed ${differenceAllowed} days. Please select a valid date range.`,
            type: "warning",
          });
          return;
        }
        if (
          moment(selectedDates[0]).format("YYYY-MM-DD") != this.startDate ||
          moment(selectedDates[1]).format("YYYY-MM-DD") != this.endDate
        ) {
          // Set the dates and fetch the list
          this.startDate = startDate;
          this.endDate = endDate;
          this.refetchList();
        }
      }
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        window.location.href =
          this.baseUrl + "in/productivity-monitoring/members";
      }
    },
    resetDateFilters() {
      this.startDate = null;
      this.endDate = null;
      this.selectedMonthYear = null;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onActivityLogsUpdated(logs) {
      this.activityLogs = logs || [];
    },
    exportAuditLogs() {
      // Format data for export
      const exportData = this.activityLogs.map((log) => ({
        employeeName: log.employeeName || "",
        userAction: log.userAction || "",
        description: log.description || "",
        formName: "Members",
        logTimestamp: log.logTimestamp
          ? convertUTCToLocal(log.logTimestamp)
          : "",
      }));

      const exportHeaders = [
        { key: "formName", header: "Form Name" },
        { key: "userAction", header: "Action" },
        { key: "employeeName", header: "Action performed by" },
        { key: "logTimestamp", header: "Action Date Time" },
        { key: "description", header: "Description" },
      ];

      const selectedMemberName = this.selectedMember
        ? this.membersList.find((m) => m.employee_id === this.selectedMember)
            ?.employee_name || ""
        : "";

      const dateRange = this.selectedMonthYear
        ? ` (${this.selectedMonthYear[0]} - ${this.selectedMonthYear[1]})`
        : "";

      const fileName =
        "Members - " +
        `${this.$t("settings.auditLog")}${
          selectedMemberName ? ` - ${selectedMemberName}` : ""
        }${dateRange}`;

      const exportOptions = {
        fileExportData: exportData,
        fileName: fileName,
        sheetName: this.$t("settings.auditLog"),
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}

:deep(.datepicker-employee_attendance .vuejs3-datepicker__value) {
  min-width: 160px;
  display: flex;
  align-items: center;
}
:deep(.datepicker-employee_attendance .vuejs3-datepicker__calendar) {
  right: 1px;
}
@media screen and (max-width: 1252px) {
  .container {
    padding: 6em 1em 0em 1em;
  }
}
</style>
