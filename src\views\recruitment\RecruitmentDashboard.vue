<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      ></AppTopBarTab>
    </div>
    <v-container fluid class="recruitment-dashboard-container">
      <v-card
        width="100%"
        :height="windowWidth < 550 ? '' : 100"
        class="pl-5 pr-9 align-center d-flex rounded-lg"
        :class="windowWidth < 550 ? 'py-2' : ''"
      >
        <v-skeleton-loader
          type="list-item-two-line"
          v-if="hiringHealthLoading"
          width="100%"
        ></v-skeleton-loader>
        <v-row
          v-else
          class="align-center justify-space-between h-100"
          :class="windowWidth < 550 ? 'flex-column' : ''"
        >
          <v-col>
            <div>
              <p class="text-h6">
                <b>Hiring Health</b>
              </p>
              <p class="text-subtitle-1">
                <span class="text-blue">
                  {{
                    totalVacancies - candidatesHired > 0
                      ? totalVacancies - candidatesHired
                      : 0
                  }}
                  positions
                </span>
                to be hired
              </p>
            </div>
          </v-col>
          <v-col
            class="d-flex flex-column"
            :class="windowWidth < 550 ? '' : 'align-end'"
          >
            <div style="width: max-content" class="text-right">
              <v-progress-linear
                :model-value="hiringProgress"
                :color="hiringProgress == 100 ? 'green' : 'red-darken-1'"
                class="rounded-lg"
                :height="8"
              ></v-progress-linear>
              <p class="text-subtitle-1">
                {{ candidatesHired }} of {{ totalVacancies }} hired in last 6
                months
              </p>
            </div>
          </v-col>
        </v-row>
        <v-btn
          color="transparent"
          class="ma-3 topPosition pa-0"
          variant="flat"
          size="small"
          min-width="15px"
          density="compact"
          @click="refetchRecruiterInfo('getHiringHealth', 'getHiredCandidates')"
        >
          <v-icon color="grey">fas fa-redo-alt</v-icon>
        </v-btn>
      </v-card>
      <v-row class="mt-3">
        <v-col :cols="windowWidth < 500 ? 12 : 6" sm="6" md="3">
          <v-card height="90" class="rounded-lg pa-3">
            <div class="d-flex align-start">
              <p class="mr-3 text-subtitle-1">
                Offer acceptance rate
                <span v-if="windowWidth > 1624">- last 6 months</span>
              </p>
              <v-tooltip
                max-width="300"
                text="The offer acceptance rate over the last 6 months is a metric that measures the percentage of job offers extended by your organization that were accepted by candidates within the specified time frame. This metric is crucial for evaluating the effectiveness of your hiring process and candidate engagement strategies."
              >
                <template v-slot:activator="{ props }">
                  <v-icon v-bind="props" size="small" color="blue"
                    >fas fa-info-circle</v-icon
                  >
                </template>
              </v-tooltip>
            </div>
            <v-skeleton-loader
              type="heading"
              v-if="acceptanceRateLoading"
              width="100%"
            ></v-skeleton-loader>
            <span v-else class="text-h6 mt-1 text-primary font-weight-bold"
              >{{ acceptanceRate }}%</span
            >
            <v-btn
              color="transparent"
              class="ma-3 topPosition pa-0"
              variant="flat"
              size="small"
              min-width="15px"
              density="compact"
              @click="refetchRecruiterInfo('getAcceptanceRate')"
            >
              <v-icon color="grey">fas fa-redo-alt</v-icon>
            </v-btn>
          </v-card>
        </v-col>
        <v-col :cols="windowWidth < 500 ? 12 : 6" sm="6" md="3">
          <v-card height="90" class="rounded-lg pa-3">
            <div class="d-flex align-start">
              <p class="text-subtitle-1 mr-3">Positions Overdue</p>
              <v-tooltip
                max-width="300"
                text="'Positions Overdue' refers to job openings that have exceeded their expected or target fill date without being successfully filled. This metric is essential for tracking the timeliness of your recruitment process and identifying potential bottlenecks or issues in your hiring pipeline."
              >
                <template v-slot:activator="{ props }">
                  <v-icon v-bind="props" size="small" color="blue"
                    >fas fa-info-circle</v-icon
                  >
                </template>
              </v-tooltip>
            </div>
            <v-skeleton-loader
              type="heading"
              v-if="positionOverdueLoading"
              width="100%"
            ></v-skeleton-loader>
            <p v-else class="text-h6 mt-1 text-primary font-weight-bold">
              {{ positionsOverdue }}
            </p>
            <v-btn
              color="transparent"
              class="ma-3 topPosition pa-0"
              variant="flat"
              size="small"
              min-width="15px"
              density="compact"
              @click="refetchRecruiterInfo('getPositionOverdue')"
            >
              <v-icon color="grey">fas fa-redo-alt</v-icon>
            </v-btn>
          </v-card>
        </v-col>
        <v-col :cols="windowWidth < 500 ? 12 : 6" sm="6" md="3">
          <v-card height="90" class="rounded-lg pa-3">
            <div class="d-flex align-start">
              <p class="text-subtitle-1 mr-3">
                Source to hire %
                <span v-if="windowWidth > 1624">- last 6 months</span>
              </p>
              <v-tooltip
                max-width="300"
                text="The Source to Hire % over the last 6 months is a metric that measures the quality of candidates received from various sources in delivering successful hires within your organization. Its calculated based on number of hires out of the total number of profiles received"
              >
                <template v-slot:activator="{ props }">
                  <v-icon v-bind="props" size="small" color="blue"
                    >fas fa-info-circle</v-icon
                  >
                </template>
              </v-tooltip>
            </div>
            <v-skeleton-loader
              type="heading"
              v-if="sourceRatioLoading"
              width="100%"
            ></v-skeleton-loader>
            <p v-else class="text-h6 mt-1 text-primary font-weight-bold">
              {{ sourceToHire }}%
            </p>
            <v-btn
              color="transparent"
              class="ma-3 topPosition pa-0"
              variant="flat"
              size="small"
              min-width="15px"
              density="compact"
              @click="refetchRecruiterInfo('getSourceToHire')"
            >
              <v-icon color="grey">fas fa-redo-alt</v-icon>
            </v-btn>
          </v-card>
        </v-col>
        <v-col :cols="windowWidth < 500 ? 12 : 6" sm="6" md="3">
          <v-card height="90" class="rounded-lg pa-3">
            <div class="d-flex align-start">
              <p class="text-subtitle-1 mr-3">
                Time to hire
                <span v-if="windowWidth > 1624">- last 6 months</span>
              </p>
              <v-tooltip
                max-width="300"
                text="'Time to Hire' over the last 6 months is a crucial metric that measures the average duration taken to fill job vacancies from the moment they are applied until the new employee confirmed for their role. This metric is essential for evaluating the efficiency and effectiveness of your organization's recruitment process."
              >
                <template v-slot:activator="{ props }">
                  <v-icon v-bind="props" size="small" color="blue"
                    >fas fa-info-circle</v-icon
                  >
                </template>
              </v-tooltip>
            </div>
            <v-skeleton-loader
              type="heading"
              v-if="timeTohireLoading"
              width="100%"
            ></v-skeleton-loader>
            <p v-else class="text-h6 mt-1 text-primary font-weight-bold">
              {{ timeToHire }} Days
            </p>
            <v-btn
              color="transparent"
              class="ma-3 topPosition pa-0"
              variant="flat"
              size="small"
              min-width="15px"
              density="compact"
              @click="refetchRecruiterInfo('getTimeToHire')"
            >
              <v-icon color="grey">fas fa-redo-alt</v-icon>
            </v-btn>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col :cols="12" md="6">
          <v-card height="400" class="rounded-lg pa-3">
            <v-skeleton-loader
              type="table-heading, table-tbody"
              v-if="departmentsLoading"
              width="100%"
            ></v-skeleton-loader>
            <div v-else-if="showUnits">
              <v-card-title>
                <p class="text-h6 font-weight-bold">Organization Units</p>
              </v-card-title>

              <div
                v-if="organizationUnitsList.length <= 0"
                style="height: 320px"
                class="d-flex justify-center align-center text-h6"
              >
                No units available
              </div>
              <v-data-table
                v-else
                :items="organizationUnitsList"
                :headers="departmentTableHeaders"
                height="320"
                fixed-header
              >
                <template v-slot:item="{ item }">
                  <tr
                    v-if="item.name != ''"
                    @click="getOrganizationWiseDepartmentList(item.id)"
                    style="cursor: pointer"
                    class="text-subtitle-1 data-table-tr"
                  >
                    <td class="truncate">{{ item.name }}</td>
                    <td>{{ item.jobs }}</td>
                    <td>
                      <div
                        class="d-flex"
                        :class="
                          windowWidth < 1500
                            ? 'flex-column justify-end align-start'
                            : 'justify-space-between align-center'
                        "
                      >
                        <div
                          :style="
                            windowWidth < 1500 ? 'width: 100%' : 'width: 70%'
                          "
                        >
                          <v-progress-linear
                            :max="item.totalPositions ? item.totalPositions : 0"
                            :model-value="
                              item.hiredPositions ? item.hiredPositions : 0
                            "
                            :color="
                              item.hiredPositions == item.totalPositions
                                ? 'green'
                                : 'red-darken-1'
                            "
                            class="rounded-lg"
                            :height="8"
                          ></v-progress-linear>
                        </div>
                        <div style="width: max-content">
                          {{ item.hiredPositions }} of {{ item.totalPositions }}
                        </div>
                      </div>
                    </td>
                  </tr>
                </template>
                <template v-slot:bottom></template>
              </v-data-table>
            </div>
            <div v-if="showDepartments">
              <v-card-title class="d-flex align-center">
                <v-icon
                  size="x-small"
                  class="mr-4"
                  v-if="fieldForce"
                  @click="hideDepartments()"
                  >fas fa-arrow-left</v-icon
                >
                <p class="text-h6 font-weight-bold">Departments</p>
              </v-card-title>
              <div
                v-if="departmentsList.length <= 0"
                style="height: 320px"
                class="d-flex justify-center align-center text-h6"
              >
                No departments available
              </div>
              <v-data-table
                v-else
                :items="departmentsList"
                :headers="departmentTableHeaders"
                height="320"
                fixed-header
              >
                <template v-slot:item="{ item }">
                  <tr v-if="item.name != ''">
                    <td>{{ item.name }}</td>
                    <td>{{ item.jobs }}</td>
                    <td
                      class="d-flex justify-space-between align-center"
                      :class="windowWidth < 1500 ? 'flex-column' : ''"
                    >
                      <div style="width: 70%">
                        <v-progress-linear
                          :max="item.totalPositions"
                          :model-value="item.hiredPositions"
                          :color="
                            item.hiredPositions == item.totalPositions
                              ? 'green'
                              : 'red-darken-1'
                          "
                          class="rounded-lg"
                          :height="8"
                        ></v-progress-linear>
                      </div>
                      <div style="width: max-content">
                        {{ item.hiredPositions }} of {{ item.totalPositions }}
                      </div>
                    </td>
                  </tr>
                </template>
                <template v-slot:bottom></template>
              </v-data-table>
            </div>
            <v-btn
              color="transparent"
              class="ma-3 topPosition pa-0"
              variant="flat"
              size="small"
              min-width="15px"
              density="compact"
              @click="refetchRecruiterInfo('getDepartmentWiseJobDetails')"
            >
              <v-icon color="grey">fas fa-redo-alt</v-icon>
            </v-btn>
          </v-card>
        </v-col>
        <v-col cols="12" md="6">
          <v-card height="400" class="rounded-lg pa-3">
            <v-tabs v-model="currentStatusTab" color="primary">
              <v-tab :value="1" class="text-subtitle-1">Pending Offers</v-tab>
              <v-tab :value="2" class="text-subtitle-1">Accepted Offers</v-tab>
              <v-tab :value="3" class="text-subtitle-1">Rejected Offers</v-tab>
              <v-tab :value="4" class="text-subtitle-1">New Hires</v-tab>
            </v-tabs>

            <v-window v-model="currentStatusTab" class="overflow-y-auto">
              <v-window-item v-for="n in 4" :key="n" :value="n">
                <v-skeleton-loader
                  type="table-tbody"
                  v-if="candidatesLoading"
                  width="100%"
                ></v-skeleton-loader>
                <div
                  v-else
                  class="mt-3"
                  style="height: 300px; overflow-y: scroll"
                >
                  <div
                    v-if="displayCandidateList.length <= 0"
                    style="height: 100%"
                    class="d-flex justify-center align-center text-h6 text-center"
                  >
                    No candidates with {{ currentStatusItem }} status
                  </div>
                  <v-row
                    v-else
                    v-for="(candidate, index) in displayCandidateList"
                    :key="index"
                  >
                    <v-col class="px-6 py-0 mt-3">
                      <v-card class="rounded-lg" elevation="3">
                        <div
                          class="d-flex justify-space-between pa-3"
                          :class="windowWidth < 550 ? 'flex-column' : ''"
                        >
                          <div>
                            <p class="text-subtitle-1 font-weight-medium">
                              {{
                                candidate.First_Name + " " + candidate.Last_Name
                              }}
                            </p>
                            <p>{{ candidate.Job_Post_Name }}</p>
                          </div>
                          <div class="text-right">
                            <p>{{ getCandidateDate(candidate) }}</p>
                            <p class="text-red">
                              {{ getCandidateOverdue(candidate) }}
                            </p>
                          </div>
                        </div>
                      </v-card>
                    </v-col>
                  </v-row>
                </div>
              </v-window-item>
            </v-window>
            <v-btn
              color="transparent"
              class="mx-3 topPosition pa-0 my-2"
              variant="flat"
              size="small"
              min-width="15px"
              density="compact"
              @click="refetchRecruiterInfo('fetchList')"
            >
              <v-icon color="grey">fas fa-redo-alt</v-icon>
            </v-btn>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="selectedView !== 'My Dashboard'">
          <v-card height="450" class="rounded-lg pa-3">
            <div
              class="d-flex justify-space-between"
              :class="windowWidth < 550 ? 'flex-column' : ''"
            >
              <div class="d-flex align-center">
                <p class="text-h6 font-weight-bold mr-5">
                  Recruiter Performance
                </p>
                <v-tooltip
                  max-width="300"
                  text="'Recruiter Performance' measures the effectiveness and efficiency of individual recruiters or recruitment teams in sourcing, engaging, and successfully hiring candidates. This metric is essential for assessing the contribution of recruiters to the overall recruitment process and organizational goals."
                >
                  <template v-slot:activator="{ props }">
                    <v-icon v-bind="props" size="small" color="blue"
                      >fas fa-info-circle</v-icon
                    >
                  </template>
                </v-tooltip>
              </div>

              <div :style="'width: 200px'" class="mr-8">
                <CustomSelect
                  :items="timePeriodList"
                  itemValue="value"
                  itemTitle="title"
                  :item-selected="timePeriodRecruiter"
                  density="compact"
                  @selected-item="onChangePeriod($event, 'recruiter')"
                ></CustomSelect>
              </div>
            </div>
            <v-skeleton-loader
              v-if="recruitersLoading"
              type="table-heading, table-tbody"
            ></v-skeleton-loader>
            <v-data-table
              v-else
              :items="recruiterList"
              :headers="headers"
              height="350"
              fixed-header
              scroll
            >
              <template v-slot:bottom></template>
            </v-data-table>
            <v-btn
              color="transparent"
              class="ma-3 topPosition pa-0"
              variant="flat"
              size="small"
              min-width="15px"
              density="compact"
              @click="refetchRecruiterInfo('getRecruitersPerformanceDetails')"
            >
              <v-icon color="grey">fas fa-redo-alt</v-icon>
            </v-btn>
          </v-card>
        </v-col>
        <v-col>
          <v-card height="700" class="pa-3 rounded-lg">
            <div
              class="d-flex justify-space-between"
              :class="windowWidth < 550 ? 'flex-column' : ''"
            >
              <div class="d-flex align-center">
                <p class="text-h6 font-weight-bold mr-5">Time to hire</p>
                <v-tooltip
                  max-width="300"
                  text="'Time to Hire' is a crucial metric that measures the average duration taken to fill job vacancies from the moment they are applied until the new employee confirmed for their role. This metric is essential for evaluating the efficiency and effectiveness of your organization's recruitment process."
                >
                  <template v-slot:activator="{ props }">
                    <v-icon v-bind="props" size="small" color="blue"
                      >fas fa-info-circle</v-icon
                    >
                  </template>
                </v-tooltip>
              </div>

              <div style="width: 200px" class="mr-8">
                <CustomSelect
                  :items="timePeriodForTimeToHire"
                  itemValue="value"
                  itemTitle="title"
                  :item-selected="timePeriodTimeToHire"
                  density="compact"
                  @selected-item="onChangePeriod($event, 'time to hire')"
                ></CustomSelect>
              </div>
            </div>
            <p
              style="color: gray"
              :class="windowWidth < 550 ? 'text-center' : ''"
            >
              This shows time to hire over a period of time
            </p>
            <v-skeleton-loader
              type="table-tbody, table-tbody"
              v-if="chartDetailsLoading"
              width="100%"
            ></v-skeleton-loader>
            <VueApexCharts
              v-else
              width="100%"
              height="80%"
              :options="options"
              type="line"
              :series="series"
            ></VueApexCharts>
            <v-btn
              color="transparent"
              class="ma-3 topPosition pa-0"
              variant="flat"
              size="small"
              min-width="15px"
              density="compact"
              @click="refetchRecruiterInfo('getTimeToHireOverPeriod')"
            >
              <v-icon color="grey">fas fa-redo-alt</v-icon>
            </v-btn>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import VueApexCharts from "vue3-apexcharts";
import moment from "moment";
import { convertUTCToLocal } from "@/helper";

import {
  GET_HIRING_HEALTH,
  GET_HIRED_CANDIDATES,
  GET_ACCEPTENCE_RATE,
  GET_POSITIONS_OVERDUE,
  GET_SOURCE_TO_HIRE,
  GET_TIME_TO_HIRE,
  GET_ORG_UNIT_LIST,
  GET_RECRUITER_PERFORMANCE_DETAILS,
  GET_ORGANIZATION_WISE_DEPARTMENT_LIST,
  GET_TIME_TO_HIRE_OVER_PERIOD,
} from "@/graphql/recruitment/dashboardQueries";

import { LIST_JOB_CANDIDATES } from "@/graphql/recruitment/recruitmentQueries";

export default {
  name: "RecruitmentDashboard",
  components: { CustomSelect, VueApexCharts },
  data: () => ({
    currentTabItem: "tab-0",
    currentStatusTab: 1,
    selectedView: "My Dashboard",
    totalVacancies: 0,
    candidatesHired: 0,
    positionsOverdue: 0,
    timeToHire: 0,
    acceptanceRate: 0,
    sourceToHire: 0,
    timePeriodRecruiter: 1,
    timePeriodTimeToHire: 3,
    recruitersLoading: false,
    isLoading: false,
    hiringHealthLoading: false,
    acceptanceRateLoading: false,
    positionOverdueLoading: false,
    sourceRatioLoading: false,
    timeTohireLoading: false,
    departmentsLoading: false,
    candidatesLoading: false,
    hiredCandidatesLoading: false,
    chartDetailsLoading: false,
    showDepartments: false,
    showUnits: false,

    candidatesList: [],
    organizationUnitsList: [],
    timePeriodList: [
      { title: "Last month", value: 1 },
      { title: "Last 3 months", value: 3 },
      { title: "Last 6 months", value: 6 },
      { title: "Last 12 months", value: 12 },
    ],
    timePeriodForTimeToHire: [
      { title: "Last 3 months", value: 3 },
      { title: "Last 6 months", value: 6 },
      { title: "Last 9 month", value: 9 },
      { title: "Last 12 months", value: 12 },
    ],
    recruiterList: [],
    headers: [
      { title: "Name", key: "name", width: "40%" },
      { title: "Positions hired", key: "positionHired" },
      { title: "Time to hire", key: "timeToHire" },
      { title: "Jobs", key: "jobs" },
    ],
    departmentTableHeaders: [
      { title: "Name", key: "name", width: "30%" },
      { title: "Jobs", key: "jobs", width: "20%" },
      { title: "Positions hired", key: "totalPositions" },
    ],
    departmentsList: [],
    options: {
      chart: {
        type: "line",
        zoom: { enabled: false },
      },
      noData: {
        text: "There are no recruitments in selected time period",
        style: {
          fontSize: "16px",
        },
      },
      yaxis: {
        show: true,
        showForNullSeries: false,
        seriesName: "Series 1",
        forceNiceScale: true,
        labels: {
          formatter: (val) => {
            return Number.isInteger(val)
              ? val + " days"
              : val.toFixed(2) + " days";
          },
        },
      },
    },
    series: [
      {
        name: "Series 1",
        data: [],
      },
    ],
  }),
  mounted() {
    this.getHiringHealth(this.loginEmployeeId);
    this.getHiredCandidates(this.loginEmployeeId);
    this.getAcceptanceRate(this.loginEmployeeId);
    this.getPositionOverdue(this.loginEmployeeId);
    this.getSourceToHire(this.loginEmployeeId);
    this.getTimeToHire(this.loginEmployeeId);
    this.getDepartmentWiseJobDetails(this.loginEmployeeId);
    this.getTimeToHireOverPeriod(3, this.loginEmployeeId);
    this.fetchList(this.loginEmployeeId);
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    isManager() {
      return this.$store.state.isManager;
    },
    mainTabs() {
      if (this.isAdmin) {
        return ["My Dashboard", "Organization Dashboard"];
      } else if (this.isManager) {
        return ["My Dashboard", "Team Dashboard"];
      } else {
        return ["My Dashboard"];
      }
    },
    currentStatusItem() {
      if (this.currentStatusTab == 1) {
        return "Pending";
      } else if (this.currentStatusTab == 2) {
        return "Accepted";
      } else if (this.currentStatusTab === 3) {
        return "Rejected";
      } else if (this.currentStatusTab == 4) {
        return "Hired";
      } else return "";
    },
    hiringProgress() {
      return (this.candidatesHired / this.totalVacancies) * 100;
    },
    displayCandidateList() {
      if (this.candidatesList && this.candidatesList.length > 0) {
        if (this.currentStatusTab == 1) {
          return this.candidatesList.filter(
            (el) =>
              el.Candidate_Status == "Offer Letter Rolled Out" &&
              el.Offer_Letter_Rolled_Out_Date &&
              el.Offer_Letter_Rolled_Out_Date != "0000-00-00 00:00:00"
          );
        } else if (this.currentStatusTab == 2) {
          return this.candidatesList.filter(
            (el) =>
              (el.Candidate_Status == "Offer letter Accepted" ||
                el.Candidate_Status == "Onboarded" ||
                el.Candidate_Status == "Onboarding Inprogress" ||
                el.Candidate_Status == "Self Onboarding Completed") &&
              el.Offer_Letter_Response_Date &&
              el.Offer_Letter_Response_Date != "0000-00-00 00:00:00"
          );
        } else if (this.currentStatusTab == 3) {
          return this.candidatesList.filter(
            (el) =>
              el.Candidate_Status == "Offer letter Declined" &&
              el.Offer_Letter_Response_Date &&
              el.Offer_Letter_Response_Date != "0000-00-00 00:00:00"
          );
        } else if (this.currentStatusTab == 4) {
          return this.candidatesList.filter(
            (el) =>
              el.Candidate_Status == "Hired" &&
              el.Hiring_Date &&
              el.Hiring_Date != "0000-00-00 00:00:00"
          );
        } else return [];
      } else {
        return [];
      }
    },
  },

  methods: {
    convertUTCToLocal,
    getCandidateDate(candidate) {
      if (candidate.Candidate_Status == "Offer Letter Rolled Out") {
        let date = this.convertUTCToLocal(
          candidate.Offer_Letter_Rolled_Out_Date
        );
        date = moment(date).format("D MMMM, YYYY");
        return date && date != "Invalid date" ? `released on ${date}` : "";
      } else if (
        candidate.Candidate_Status == "Offer letter Accepted" ||
        candidate.Candidate_Status == "Onboarded" ||
        candidate.Candidate_Status == "Onboarding Inprogress" ||
        candidate.Candidate_Status == "Self Onboarding Completed"
      ) {
        let date = moment(candidate.Offer_Letter_Response_Date).format(
          "D MMMM, YYYY"
        );
        return date && date != "Invalid date" ? `accepted on ${date}` : "";
      } else if (candidate.Candidate_Status == "Offer letter Declined") {
        let date = moment(candidate.Offer_Letter_Response_Date).format(
          "D MMMM, YYYY"
        );
        return date && date != "Invalid date" ? `rejected on ${date}` : "";
      } else if (candidate.Candidate_Status == "Hired") {
        let date = moment(candidate.Hiring_Date).format("D MMMM, YYYY");
        return date ? `hired on ${date}` : "";
      }
    },
    getCandidateOverdue(candidate) {
      if (candidate.Candidate_Status == "Offer Letter Rolled Out") {
        let date = moment();
        let diff = moment(candidate.Offer_Letter_Rolled_Out_Date).diff(
          date,
          "days"
        );
        return Math.abs(diff) ? Math.abs(diff) + " days overdue" : "";
      } else return "";
    },
    getHiringHealth(empId = null) {
      let vm = this;
      vm.hiringHealthLoading = true;
      vm.$apollo
        .query({
          query: GET_HIRING_HEALTH,
          variables: {
            employeeIds: empId ? [empId] : [],
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.retrieveHiringHealth &&
            data.retrieveHiringHealth.positionsToBeHired
          ) {
            if (data.retrieveHiringHealth.positionsToBeHired <= 0) {
              this.totalVacancies = 0;
            } else {
              this.totalVacancies =
                data.retrieveHiringHealth.positionsToBeHired;
            }
          } else {
            this.totalVacancies = 0;
          }
          vm.hiringHealthLoading = false;
        })
        .catch(() => {
          vm.hiringHealthLoading = false;
        });
    },
    getHiredCandidates(empId = null) {
      let vm = this;
      vm.$apollo
        .query({
          query: GET_HIRED_CANDIDATES,
          variables: {
            employeeIds: empId ? [empId] : [],
            endDate: moment().format("YYYY-MM-DD"),
            startDate: moment().subtract(6, "months").format("YYYY-MM-DD"),
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.hiredCandidateCount &&
            data.hiredCandidateCount.totalHiredCandidates
          ) {
            if (data.hiredCandidateCount.totalHiredCandidates <= 0) {
              this.candidatesHired = 0;
            } else {
              this.candidatesHired =
                data.hiredCandidateCount.totalHiredCandidates;
            }
          } else {
            this.candidatesHired = 0;
          }
          vm.hiredCandidatesLoading = false;
        })
        .catch(() => {
          vm.hiredCandidatesLoading = false;
        });
    },
    getAcceptanceRate(empId = null) {
      let vm = this;
      vm.acceptanceRateLoading = true;
      vm.$apollo
        .query({
          query: GET_ACCEPTENCE_RATE,
          variables: {
            employeeIds: empId ? [empId] : [],
            endDate: moment().format("YYYY-MM-DD"),
            startDate: moment().subtract(6, "months").format("YYYY-MM-DD"),
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.getOfferAcceptanceRate &&
            data.getOfferAcceptanceRate.offerAcceptanceRate
          ) {
            if (data.getOfferAcceptanceRate.offerAcceptanceRate <= 0) {
              this.acceptanceRate = 0;
            } else if (data.getOfferAcceptanceRate.offerAcceptanceRate > 0) {
              this.acceptanceRate =
                data.getOfferAcceptanceRate.offerAcceptanceRate.toFixed(2);
            }
          } else {
            this.acceptanceRate = 0;
          }
          vm.acceptanceRateLoading = false;
        })
        .catch(() => {
          vm.acceptanceRateLoading = false;
        });
    },
    getPositionOverdue(empId = null) {
      let vm = this;
      vm.positionOverdueLoading = true;
      vm.$apollo
        .query({
          query: GET_POSITIONS_OVERDUE,
          variables: {
            employeeIds: empId ? [empId] : [],
            endDate: moment().format("YYYY-MM-DD"),
            startDate: moment().subtract(6, "months").format("YYYY-MM-DD"),
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.getPositionsOverdue &&
            data.getPositionsOverdue.noOfPositionsOverdue
          ) {
            if (data.getPositionsOverdue.noOfPositionsOverdue <= 0) {
              this.positionsOverdue = 0;
            } else {
              this.positionsOverdue =
                data.getPositionsOverdue.noOfPositionsOverdue;
            }
          } else {
            this.positionsOverdue = 0;
          }
          vm.positionOverdueLoading = false;
        })
        .catch(() => {
          vm.positionOverdueLoading = false;
        });
    },
    getSourceToHire(empId = null) {
      let vm = this;
      vm.sourceRatioLoading = true;
      vm.$apollo
        .query({
          query: GET_SOURCE_TO_HIRE,
          variables: {
            employeeIds: empId ? [empId] : [],
            endDate: moment().format("YYYY-MM-DD"),
            startDate: moment().subtract(6, "months").format("YYYY-MM-DD"),
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.retrieveSourceToHireRatio &&
            data.retrieveSourceToHireRatio.sourceToHireRatio
          ) {
            if (data.retrieveSourceToHireRatio.sourceToHireRatio <= 0) {
              this.sourceToHire = 0;
            } else {
              this.sourceToHire = (
                data.retrieveSourceToHireRatio.sourceToHireRatio * 100
              ).toFixed(2);
            }
          } else {
            this.sourceToHire = 0;
          }
          vm.sourceRatioLoading = false;
        })
        .catch(() => {
          vm.sourceRatioLoading = false;
        });
    },
    getTimeToHire(empId = null) {
      let vm = this;
      vm.timeTohireLoading = true;
      vm.$apollo
        .query({
          query: GET_TIME_TO_HIRE,
          variables: {
            employeeIds: empId ? [empId] : [],
            endDate: moment().format("YYYY-MM-DD"),
            startDate: moment().subtract(6, "months").format("YYYY-MM-DD"),
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.retrieveTimeToHire &&
            data.retrieveTimeToHire.timeToHire
          ) {
            if (data.retrieveTimeToHire.timeToHire <= 0) {
              this.timeToHire = 0;
            } else {
              this.timeToHire = Math.round(data.retrieveTimeToHire.timeToHire);
            }
          } else {
            this.timeToHire = 0;
          }
          vm.timeTohireLoading = false;
        })
        .catch(() => {
          vm.timeTohireLoading = false;
        });
    },
    getDepartmentWiseJobDetails(empId = null) {
      let vm = this;
      vm.departmentsLoading = true;
      vm.$apollo
        .query({
          query: GET_ORG_UNIT_LIST,
          variables: {
            employeeIds: empId ? [empId] : [],
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (data && data.getOrgUnitList) {
            this.organizationUnitsList =
              data.getOrgUnitList.serviceProviderWiseJobpostDetails;
            this.departmentsList =
              data.getOrgUnitList.departmentWiseJobpostDetails;
            if (this.fieldForce) {
              this.showUnits = true;
              this.showDepartments = false;
            } else {
              this.showDepartments = true;
              this.showUnits = false;
            }
          }
          vm.departmentsLoading = false;
        })
        .catch(() => {
          vm.departmentsLoading = false;
        });
    },
    getRecruitersPerformanceDetails(value) {
      let vm = this;
      vm.recruitersLoading = true;
      let endDate = moment().format("YYYY-MM-DD");
      let startDate = moment()
        .subtract(value - 1, "months")
        .startOf("month")
        .format("YYYY-MM-DD");
      vm.$apollo
        .query({
          query: GET_RECRUITER_PERFORMANCE_DETAILS,
          variables: {
            startDate: startDate,
            endDate: endDate,
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.retrieveRecuiterPerformanceDetails &&
            data.retrieveRecuiterPerformanceDetails
              .retrieveRecuiterPerformanceDetails
          ) {
            this.recruiterList =
              data.retrieveRecuiterPerformanceDetails.retrieveRecuiterPerformanceDetails;
          }
          vm.recruitersLoading = false;
        })
        .catch(() => {
          vm.recruitersLoading = false;
        });
    },
    getTimeToHireOverPeriod(value, empId = null) {
      let vm = this;
      this.chartDetailsLoading = true;
      vm.$apollo
        .query({
          query: GET_TIME_TO_HIRE_OVER_PERIOD,
          client: "apolloClientAN",
          variables: {
            startDate: moment()
              .subtract(value - 1, "months")
              .startOf("month")
              .format("YYYY-MM-DD"),
            endDate: moment().format("YYYY-MM-DD"),
            employeeIds: empId ? [empId] : [],
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.retrieveTimeToHireOverAPeriod &&
            data.retrieveTimeToHireOverAPeriod.timeToHireDetails
          ) {
            let co_ordinates = JSON.parse(
              data.retrieveTimeToHireOverAPeriod.timeToHireDetails
            );
            let series_data = [];
            for (let key in co_ordinates) {
              if (co_ordinates[key]) {
                series_data.push({
                  x: key,
                  y: co_ordinates[key].toFixed(2),
                });
              } else {
                series_data.push({
                  x: key,
                  y: 0,
                });
              }
            }
            this.series = [{ data: series_data }];
          }
          this.chartDetailsLoading = false;
        })
        .catch(() => {
          this.chartDetailsLoading = false;
        });
    },

    fetchList(empId = null) {
      let vm = this;
      vm.candidatesLoading = true;
      let action =
        this.currentTabItem === "tab-0" ? "mydashboard" : "dashboard";
      vm.$apollo
        .query({
          query: LIST_JOB_CANDIDATES,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            searchString: "",
            isDropDownCall: 0,
            jobPostId: [],
            preferredLocation: [],
            status: [],
            employeeId: [empId],
            action: action,
          },
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.listJobCandidates &&
            data.listJobCandidates.jobCandidates
          ) {
            this.candidatesList = data.listJobCandidates.jobCandidates;
          }
          this.candidatesLoading = false;
        })
        .catch(() => {
          this.candidatesLoading = false;
        });
    },
    getOrganizationWiseDepartmentList(id) {
      let vm = this;
      vm.departmentsLoading = true;
      vm.$apollo
        .query({
          query: GET_ORGANIZATION_WISE_DEPARTMENT_LIST,
          variables: {
            orgUnitId: id,
          },
          fetchPolicy: "no-cache",
          client: "apolloClientAN",
        })
        .then((res) => {
          let { data } = res;

          if (
            data &&
            data.retrieveDepartmentWiseJobPostDetails &&
            data.retrieveDepartmentWiseJobPostDetails
              .departmentWiseJobpostDetails
          ) {
            let orgDepartmentList = [];
            for (let item of data.retrieveDepartmentWiseJobPostDetails
              .departmentWiseJobpostDetails) {
              orgDepartmentList.push({
                name: item.departmentName,
                jobs: item.jobs,
                totalPositions: item.totalPositions,
                hiredPositions: item.hiredPositions,
              });
            }
            vm.departmentsList = orgDepartmentList;
          }
          vm.showDepartments = true;
          vm.showUnits = false;
          vm.departmentsLoading = false;
        })
        .catch(() => {
          vm.departmentsLoading = false;
        });
    },
    onTabChange(tabName) {
      this.selectedView = tabName;
      if (tabName == "My Dashboard") {
        this.currentTabItem = "tab-0";
        this.getHiringHealth(this.loginEmployeeId);
        this.getHiredCandidates(this.loginEmployeeId);
        this.getAcceptanceRate(this.loginEmployeeId);
        this.getPositionOverdue(this.loginEmployeeId);
        this.getSourceToHire(this.loginEmployeeId);
        this.getTimeToHire(this.loginEmployeeId);
        this.getDepartmentWiseJobDetails(this.loginEmployeeId);
        this.timePeriodTimeToHire = 3;
        this.getTimeToHireOverPeriod(3, this.loginEmployeeId);
        this.fetchList(this.loginEmployeeId);
      } else {
        this.currentTabItem = "tab-1";
        this.getHiringHealth();
        this.getHiredCandidates();
        this.getAcceptanceRate();
        this.getPositionOverdue();
        this.getSourceToHire();
        this.getTimeToHire();
        this.getDepartmentWiseJobDetails();
        this.getRecruitersPerformanceDetails(1);
        this.timePeriodTimeToHire = 3;
        this.getTimeToHireOverPeriod(3);
        this.fetchList();
      }
    },
    calculatePercentage(total, hired) {
      return (hired / total) * 100;
    },
    onChangePeriod(value, cardName) {
      if (cardName == "recruiter") {
        this.timePeriodRecruiter = value;
        if (this.currentTabItem == "tab-0") {
          this.getRecruitersPerformanceDetails(value, this.loginEmployeeId);
        } else {
          this.getRecruitersPerformanceDetails(value);
        }
      } else {
        this.timePeriodTimeToHire = value;
        if (this.currentTabItem == "tab-0") {
          this.getTimeToHireOverPeriod(value, this.loginEmployeeId);
        } else {
          this.getTimeToHireOverPeriod(value);
        }
      }
    },
    hideDepartments() {
      this.showUnits = true;
      this.showDepartments = false;
    },
    refetchRecruiterInfo() {
      for (let item of arguments) {
        if (this.currentTabItem == "tab-0") {
          if (item == "getTimeToHireOverPeriod") {
            this[item](this.timePeriodTimeToHire, this.loginEmployeeId);
          } else if (item == "getRecruitersPerformanceDetails") {
            this[item](this.timePeriodRecruiter, this.loginEmployeeId);
          } else {
            this[item](this.loginEmployeeId);
          }
        } else {
          if (item == "getTimeToHireOverPeriod") {
            this[item](this.timePeriodTimeToHire);
          } else if (item == "getRecruitersPerformanceDetails") {
            this[item](this.timePeriodRecruiter);
          } else {
            this[item]();
          }
        }
      }
    },
  },
};
</script>
<style scoped>
.recruitment-dashboard-container {
  padding: 5em 2em 0em 3em;
}
@media only screen and (min-width: 1200px) {
  .recruitment-dashboard-container {
    padding: 5em 2em 0em 3em;
  }
}
@media only screen and (min-width: 992px) {
  .recruitment-dashboard-container {
    padding: 6em 2em 0em 3em;
  }
}

@media screen and (max-width: 992px) {
  .recruitment-dashboard-container {
    padding: 6em 1em 0em 1em;
  }
}

@media screen and (max-width: 600px) {
  thead {
    display: table-header-group !important;
  }
  :deep(.apexcharts-text tspan) {
    font-size: 11px;
  }
}

.v-data-table thead {
  display: table-header-group !important;
}

.truncate {
  max-width: 20%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.topPosition {
  position: absolute;
  top: 0;
  right: 0;
}
</style>
