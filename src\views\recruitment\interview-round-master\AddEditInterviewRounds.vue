<template>
  <div>
    <v-card class="rounded-lg mb-2">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">Interview Round</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mr-1">
            <v-tooltip location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="isFormDirty ? '' : props"
                  rounded="lg"
                  class="mb-2 secondary"
                  variant="elevated"
                  type="submit"
                  @click="isFormDirty ? validateAddUpdateForm() : {}"
                  ><span class="px-2 primary">Save</span></v-btn
                >
              </template>
              <div v-if="!isFormDirty">There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon class="mt-n2" color="primary" @click="closeAddForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card-text style="height: calc(100vh - 300px); overflow: scroll">
        <v-form
          ref="interviewRoundForm"
          @submit.prevent="
            {
            }
          "
        >
          <v-row class="px-sm-4 px-md-6 pt-sm-4">
            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="roundName"
                style="max-width: 80%"
                ref="roundName"
                id="roundName"
                maxlength="50"
                variant="solo"
                :rules="[
                  required('Round Name', roundName?.trim()),
                  alphaNumSpaceNewLineWithElevenSymbolValidation(roundName),
                  roundName
                    ? minMaxStringValidation('Round Name', roundName, 3, 50)
                    : true,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Round Name</span>
                  <span style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="maxScorePerSkill"
                ref="maxScorePerSkill"
                id="maxScorePerSkill"
                label="Max Score Per Skill"
                style="max-width: 80%"
                type="number"
                variant="solo"
                :rules="[
                  numericRequiredValidation(
                    'Max Score Per Skill',
                    maxScorePerSkill
                  ),
                  numericValidation('Max Score Per Skill', maxScorePerSkill),
                  minMaxNumberValidation(
                    'Max Score Per Skill',
                    parseInt(maxScorePerSkill),
                    1,
                    10
                  ),
                  ,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Max Score Per Skill</span>
                  <span style="color: red">*</span>
                </template></v-text-field
              >
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="passingScore"
                ref="passingScore"
                id="passingScore"
                label="Passing Score"
                style="max-width: 80%"
                type="number"
                variant="solo"
                :rules="[
                  numericRequiredValidation('Passing Score', passingScore),
                  numericValidation('Passing Score', passingScore),
                  minMaxNumberValidation(
                    'Passing Score',
                    parseInt(passingScore),
                    1,
                    totalScore ? totalScore : 100,
                    '',
                    totalScore ? 'Total Score' : ''
                  ),
                  ,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Passing Score</span>
                  <span style="color: red">*</span>
                </template></v-text-field
              >
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <p class="text-body-2 text-grey-darken-1">
                Total Score
                <v-tooltip location="bottom">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      class="pl-1"
                      v-bind="props"
                      size="small"
                      color="info"
                    >
                      fas fa-info-circle
                    </v-icon>
                  </template>
                  <div style="width: 200px !important">
                    The total score is calculated by multiplying the maximum
                    score achievable for each skill by the number of skills
                    added.
                  </div>
                </v-tooltip>
              </p>
              <p class="text-bode-2 pt-2 font-weight-regular">
                {{ totalScore ? totalScore : 0 }}
              </p>
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="minimumPanelMembers"
                ref="minimumPanelMembers"
                id="minimumPanelMembers"
                label="Minimum Panel Members"
                style="max-width: 80%"
                type="number"
                variant="solo"
                :rules="[
                  numericRequiredValidation(
                    'Minimum Panel Members',
                    minimumPanelMembers
                  ),
                  numericValidation(
                    'Minimum Panel Members',
                    minimumPanelMembers
                  ),
                  minMaxNumberValidation(
                    'Minimum Panel Members',
                    parseInt(minimumPanelMembers),
                    1,
                    maximumPanelMembers ? maximumPanelMembers : 100,
                    '',
                    maximumPanelMembers ? 'Maximum Panel Members' : ''
                  ),
                  ,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Minimum Panel Members</span>
                  <span style="color: red">*</span>
                </template></v-text-field
              >
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="maximumPanelMembers"
                ref="maximumPanelMembers"
                id="maximumPanelMembers"
                label="Maximum Panel Members"
                style="max-width: 80%"
                type="number"
                variant="solo"
                :rules="[
                  numericRequiredValidation(
                    'Maximum Panel Members',
                    maximumPanelMembers
                  ),
                  numericValidation(
                    'Maximum Panel Members',
                    maximumPanelMembers
                  ),
                  minMaxNumberValidation(
                    'Maximum Panel Members',
                    parseInt(maximumPanelMembers),
                    minimumPanelMembers ? minimumPanelMembers : 1,
                    100,
                    minimumPanelMembers ? 'Minimum Panel Members' : '',
                    ''
                  ),
                  ,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Maximum Panel Members</span>
                  <span style="color: red">*</span>
                </template></v-text-field
              >
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-text-field
                v-model="maximumCandidates"
                ref="maximumCandidates"
                id="maximumCandidates"
                label="Maximum Candidates"
                style="max-width: 80%"
                type="number"
                variant="solo"
                :rules="[
                  numericRequiredValidation(
                    'Maximum Candidates',
                    maximumCandidates
                  ),
                  numericValidation('Maximum Candidates', maximumCandidates),
                  minMaxNumberValidation(
                    'Maximum Candidates',
                    parseInt(maximumCandidates),
                    1,
                    500
                  ),
                  ,
                ]"
                @update:model-value="deductFormChange()"
              >
                <template v-slot:label>
                  <span>Maximum Candidates</span>
                  <span style="color: red">*</span>
                </template></v-text-field
              >
            </v-col>

            <v-col
              cols="12"
              sm="12"
              lg="6"
              md="6"
              xl="6"
              class="pl-sm-4 pl-md-6"
              style="height: 100px"
            >
              <v-textarea
                v-model="description"
                ref="description"
                id="description"
                rows="2"
                row-height="8"
                color="primary"
                maxlength="500"
                hide-details="auto"
                variant="solo"
                label="Description"
                style="max-width: 80%"
                counter="500"
                :rules="
                  description
                    ? [
                        minLengthValidation('Description', description, 5),
                        maxLengthValidation('Description', description, 500),
                      ]
                    : [true]
                "
                @update:model-value="deductFormChange()"
              />
            </v-col>
          </v-row>
        </v-form>
        <div>
          <v-row class="px-sm-4 px-md-6 pt-sm-4">
            <v-col cols="12" sm="12" md="6" class="pl-sm-4 pl-md-6">
              <div class="text-subtitle-1 font-weight-bold">
                Skill Categories
              </div>
            </v-col>
          </v-row>

          <v-form
            v-for="(category, categoryIndex) in skillCategory"
            :key="categoryIndex"
            :ref="'skillCategoryForm' + categoryIndex"
            class="rounded-lg my-6"
            style="box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1)"
          >
            <!-- Skill Category Name -->
            <v-row class="px-sm-4 px-md-6 pt-sm-4">
              <v-col cols="10" sm="10" md="6">
                <v-text-field
                  variant="solo"
                  :ref="'skillCategoryName' + categoryIndex"
                  :id="'skillCategoryName' + categoryIndex"
                  class="pl-4"
                  v-model="category.skillCategory"
                  style="max-width: 80%"
                  :rules="[
                    required('Skill Category', category.skillCategory),
                    validateWithRulesAndReturnMessages(
                      category.skillCategory,
                      'skillCategory',
                      'Skill Category'
                    ),
                  ]"
                  @update:model-value="deductFormChange()"
                >
                  <template v-slot:label>
                    <span>Skill Category</span>
                    <span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                v-if="skillCategory?.length > 1"
                cols="2"
                sm="2"
                md="6"
                class="d-flex justify-end pl-n8"
              >
                <v-icon
                  size="15"
                  class="fas fa-times"
                  color="primary"
                  @click="removeSkillCategory(categoryIndex)"
                />
              </v-col>
            </v-row>

            <!-- Skills -->
            <v-form
              v-for="(skill, skillIndex) in category.skills"
              :key="categoryIndex + skillIndex"
              :ref="'skillsForm' + categoryIndex + skillIndex"
            >
              <v-row align="center">
                <v-col cols="11">
                  <v-row class="px-sm-4 px-md-6 pt-sm-4">
                    <v-col cols="12" class="pl-sm-4 pl-md-6">
                      <v-textarea
                        variant="solo"
                        :ref="`skillName` + categoryIndex + skillIndex"
                        :id="`skillName` + categoryIndex + skillIndex"
                        rows="2"
                        row-height="8"
                        color="primary"
                        hide-details="auto"
                        v-model="skill.skill"
                        :rules="[
                          required('Skill', skill.skill),
                          validateWithRulesAndReturnMessages(
                            skill.skill,
                            'skillName',
                            'Skill'
                          ),
                        ]"
                        @update:model-value="deductFormChange()"
                      >
                        <template v-slot:label>
                          <span>Skill</span>
                          <span style="color: red">*</span>
                        </template>
                      </v-textarea>
                    </v-col>
                    <v-col cols="12" class="pl-sm-4 pl-md-6">
                      <v-textarea
                        v-model="skill.questions"
                        :ref="`questions` + categoryIndex + skillIndex"
                        :id="`questions` + categoryIndex + skillIndex"
                        rows="2"
                        row-height="8"
                        color="primary"
                        hide-details="auto"
                        variant="solo"
                        label="Instructions to Interviewer"
                        :rules="
                          skill.questions
                            ? [
                                maxLengthValidation(
                                  'Instructions to Interviewer',
                                  skill.questions,
                                  5000
                                ),
                                validateWithRulesAndReturnMessages(
                                  skill.questions,
                                  'instructionsInterviewer',
                                  'Instructions to Interviewer'
                                ),
                              ]
                            : [true]
                        "
                        @update:model-value="deductFormChange()"
                      />
                    </v-col>
                  </v-row>
                </v-col>
                <v-col
                  v-if="category.skills?.length > 1"
                  cols="1"
                  class="d-flex justify-center pl-n8"
                >
                  <v-icon
                    size="15"
                    class="fas fa-trash"
                    color="primary"
                    @click="removeSkillPair(categoryIndex, skillIndex)"
                  />
                </v-col>
                <v-divider
                  style="max-width: 90%"
                  class="border-opacity-25 ml-6"
                  v-if="category.skills?.length - 1 !== skillIndex"
                />
              </v-row>

              <!-- Add Skill Button -->
              <v-row
                v-if="category.skills?.length - 1 === skillIndex"
                class="px-sm-4 px-md-6 pt-sm-4"
              >
                <v-col cols="12" class="d-flex justify-center">
                  <v-btn
                    class="primary"
                    variant="text"
                    size="small"
                    @click="addSkill(categoryIndex)"
                  >
                    <v-icon class="mr-1" size="14">fas fa-plus</v-icon>Add
                    Skill/Question
                  </v-btn>
                </v-col>
              </v-row>
            </v-form>
          </v-form>

          <!-- Add Skill Category Button -->
          <v-row class="px-sm-4 px-md-6 pt-sm-4 mb-2">
            <v-col cols="12" class="d-flex justify-center">
              <v-btn
                class="primary"
                variant="text"
                size="small"
                @click="addSkillCategory()"
              >
                <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add Skill
                Category
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </v-card-text>
      <v-overlay
        contained
        class="align-center justify-center"
        :model-value="isLoadingCard"
        scrim="#fff"
      >
        <v-progress-circular color="secondary" indeterminate size="54">
        </v-progress-circular>
      </v-overlay>
    </v-card>
    <AppWarningModal
      v-if="showConfirmation"
      :open-modal="showConfirmation"
      imgUrl="common/exit_form"
      confirmation-heading="Are you sure to exit this form?"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            class="mt-n5 secondary"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
  </div>
</template>

<script>
import {
  UPDATE_INTERVIEW_ROUNDS,
  ADD_INTERVIEW_ROUNDS,
} from "@/graphql/recruitment/interviewRoundMasterQueries.js";
import validationRules from "@/mixins/validationRules";
export default {
  name: "AddEditInterviewRounds",

  mixins: [validationRules],

  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  emits: ["close-form", "form-updated"],

  data: () => ({
    // others
    showConfirmation: false,
    isFormDirty: false,

    // data
    roundId: 0,
    roundName: "",
    maxScorePerSkill: 1,
    passingScore: 1,
    totalScore: 0,
    minimumPanelMembers: 1,
    maximumPanelMembers: 1,
    maximumCandidates: 1,
    description: "",

    // loading
    isLoadingCard: false,

    // errors
    validationMessages: [],
    showValidationAlert: false,
    // skill section
    skillCategory: [
      {
        skillCategoryId: 0,
        skillCategory: "",
        skills: [{ skillId: 0, skill: "", questions: "" }],
      },
    ],
  }),

  computed: {
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  watch: {
    skillCategory: {
      handler(newValue) {
        this.deductFormChange(newValue);
      },
      deep: true,
    },
  },

  mounted() {
    if (this.isEdit) {
      const {
        Round_Name,
        Max_Score_Per_Skill,
        Description,
        Passing_Score,
        Total_Score,
        Minimum_Panel_Members,
        Round_Id,
        Maximum_Panel_members,
        SkillsCategory,
        Maximum_Candidates,
      } = this.editFormData;
      this.roundId = Round_Id ? parseInt(Round_Id) : 0;
      this.roundName = Round_Name ? Round_Name : "";
      this.maxScorePerSkill = Max_Score_Per_Skill ? Max_Score_Per_Skill : 1;
      this.passingScore = Passing_Score ? Passing_Score : 1;
      this.totalScore = Total_Score ? Total_Score : 0;
      this.minimumPanelMembers = Minimum_Panel_Members
        ? Minimum_Panel_Members
        : 1;
      this.maximumPanelMembers = Maximum_Panel_members
        ? Maximum_Panel_members
        : 1;
      this.maximumCandidates = Maximum_Candidates ? Maximum_Candidates : 1;
      this.description = Description ? Description : "";
      if (SkillsCategory && SkillsCategory.length) {
        this.skillCategory = [];
        const parsedSkillsCategory = JSON.parse(SkillsCategory);
        this.skillCategory = parsedSkillsCategory?.map((category) => ({
          skillCategoryId: category.Skill_Category_Id || 0,
          skillCategory: category.Skill_Category || "",
          skills: category.Skills?.map((skill) => ({
            skillId: skill.Skill_Id || 0,
            skill: skill.Skill_Name || "",
            questions: skill.Questions || "",
          })),
        }));
      }
    }
  },

  methods: {
    async addSkillCategory(justValidate = false) {
      let isFormValid = true; // Flag for overall form validity
      const invalidFields = []; // To collect invalid fields

      // Validate all skill category forms
      for (
        let categoryIndex = 0;
        categoryIndex < this.skillCategory.length;
        categoryIndex++
      ) {
        const categoryFormRef =
          this.$refs["skillCategoryForm" + categoryIndex][0];

        // Validate skill category name form
        const categoryValidationResponse = await categoryFormRef.validate();
        if (!categoryValidationResponse.valid) {
          invalidFields.push(categoryFormRef);
          isFormValid = false;
        }

        // Validate all skills within the category
        for (
          let skillIndex = 0;
          skillIndex < this.skillCategory[categoryIndex].skills.length;
          skillIndex++
        ) {
          const skillFormRef =
            this.$refs["skillsForm" + categoryIndex + skillIndex][0]; // Update to skillsForm
          const skillValidationResponse = await skillFormRef.validate();
          if (!skillValidationResponse.valid) {
            invalidFields.push(skillFormRef);
            isFormValid = false;
          }
        }
      }

      // Scroll to the first invalid field if any validation errors exist
      if (!isFormValid && invalidFields.length > 0) {
        const firstInvalidField = invalidFields[0];
        firstInvalidField.$el.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
        return true; // Stop execution if there are invalid fields
      } else if (!justValidate) {
        // Add a new skill category if all validations pass
        this.skillCategory.push({
          skillCategoryId: 0,
          skillCategory: "", // Update to match the new key
          skills: [
            {
              skillId: 0,
              skill: "",
              questions: "",
            },
          ],
        });
        return false;
      }
    },
    async addSkill(categoryIndex) {
      let isFormValid = true; // Flag to track overall form validity
      const invalidFields = []; // To collect invalid field references

      // Iterate through all skills in the category
      for (
        let skillIndex = 0;
        skillIndex < this.skillCategory[categoryIndex].skills.length;
        skillIndex++
      ) {
        const formRef =
          this.$refs["skillsForm" + categoryIndex + skillIndex][0];

        // Validate each skill form
        const validationResponse = await formRef.validate();
        if (!validationResponse.valid) {
          invalidFields.push(formRef); // Track invalid forms
          isFormValid = false;
        }
      }

      // If there are invalid fields, scroll to the first one
      if (!isFormValid && invalidFields.length > 0) {
        const firstInvalidField = invalidFields[0];
        firstInvalidField.$el.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
        return; // Stop further execution if the form is invalid
      } else {
        // If all forms are valid, add a new skill
        this.skillCategory[categoryIndex].skills.push({
          skillId: 0,
          skill: "",
          questions: "",
        });
      }
    },
    removeSkillPair(categoryIndex, pairIndex) {
      this.skillCategory[categoryIndex].skills.splice(pairIndex, 1);
    },
    removeSkillCategory(categoryIndex) {
      if (this.skillCategory.length > 1) {
        this.skillCategory.splice(categoryIndex, 1);
      }
    },
    deductFormChange() {
      this.totalScore =
        this.skillCategory.reduce(
          (total, category) => total + category.skills.length,
          0
        ) * parseInt(this.maxScorePerSkill);
      this.$refs.interviewRoundForm.validate();
      this.isFormDirty = true;
    },

    // before closing the form we have ask close confirmation from user
    closeAddForm() {
      if (this.isFormDirty) {
        this.showConfirmation = true;
      } else {
        this.closeForm();
      }
    },

    // when aborting close in confirmation screen we have to keep the user in the same page
    abortClose() {
      this.showConfirmation = false;
    },

    // when accepting close in confirmation screen, we have to close the form
    acceptClose() {
      this.showConfirmation = false;
      this.closeForm();
    },

    closeForm() {
      this.isFormDirty = false;
      this.$emit("close-form");
    },

    async validateAddUpdateForm() {
      let isForm1Valid = await this.$refs.interviewRoundForm.validate();
      let isForm2Valid = null;

      // Validate all skill categories
      for (
        let categoryIndex = 0;
        categoryIndex < this.skillCategory.length;
        categoryIndex++
      ) {
        const skills = this.skillCategory[categoryIndex].skills;

        // Check if any skill key in the skills array is an empty string
        const hasEmptySkill = skills.some((skill) => skill.skill === "");

        // If any skill key is empty, pass categoryIndex to add Skill
        if (hasEmptySkill) {
          isForm2Valid = hasEmptySkill;
        }
      }

      // Validate addSkillCategory (entire category form)
      let isForm3Valid = await this.addSkillCategory(true);
      // Proceed if all forms are valid
      if (
        isForm1Valid &&
        isForm1Valid.valid &&
        !isForm2Valid &&
        !isForm3Valid
      ) {
        if (this.isEdit) {
          this.updateRounds();
        } else {
          this.addRounds();
        }
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              if (fieldRef.$el) {
                fieldRef.$el.scrollIntoView({
                  behavior: "smooth",
                  block: "start",
                });
              }
            }
          });
        }
      }
    },
    addRounds() {
      let vm = this;
      vm.isLoadingCard = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_INTERVIEW_ROUNDS,
            variables: {
              roundName: vm.roundName,
              description: vm.description,
              passingScore: vm.passingScore ? parseInt(vm.passingScore) : 0,
              totalScore: vm.totalScore ? parseFloat(vm.totalScore) : 0,
              minPanelMembers: vm.minimumPanelMembers
                ? parseInt(vm.minimumPanelMembers)
                : 0,
              maxPanelMembers: vm.maximumPanelMembers
                ? parseInt(vm.maximumPanelMembers)
                : 0,
              maxCandidates: vm.maximumCandidates
                ? parseInt(vm.maximumCandidates)
                : 0,
              maximumScorePerSkill: vm.maxScorePerSkill
                ? parseInt(vm.maxScorePerSkill)
                : 0,
              skillCategory: vm.skillCategory,
              addedBy: vm.loginEmployeeId,
            },
            client: "apolloClientA",
          })
          .then((response) => {
            if (response && response.data && response.data.addRounds) {
              const { errorCode, validationError } = response.data.addRounds;
              if (!errorCode && !validationError) {
                var snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Interview rounds added successfully.",
                };
                vm.showAlert(snackbarData);
                vm.$emit("form-updated");
                vm.isLoadingCard = false;
              } else {
                vm.handleAddEditError("adding");
              }
            } else {
              vm.handleAddEditError("adding");
            }
          })
          .catch((addEditError) => {
            vm.handleAddEditError("adding", addEditError);
          });
      } catch {
        vm.handleAddEditError("adding");
      }
    },

    updateRounds() {
      let vm = this;
      vm.isLoadingCard = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_INTERVIEW_ROUNDS,
            variables: {
              roundId: vm.editFormData.Round_Id,
              roundName: vm.roundName,
              description: vm.description,
              passingScore: vm.passingScore ? parseInt(vm.passingScore) : 0,
              totalScore: vm.totalScore ? parseFloat(vm.totalScore) : 0,
              minPanelMembers: vm.minimumPanelMembers
                ? parseInt(vm.minimumPanelMembers)
                : 0,
              maxPanelMembers: vm.maximumPanelMembers
                ? parseInt(vm.maximumPanelMembers)
                : 0,
              maxCandidates: vm.maximumCandidates
                ? parseInt(vm.maximumCandidates)
                : 0,
              maximumScorePerSkill: vm.maxScorePerSkill
                ? parseInt(vm.maxScorePerSkill)
                : 0,
              skillCategory: vm.skillCategory,
              updatedBy: vm.loginEmployeeId,
            },
            client: "apolloClientA",
          })
          .then((response) => {
            if (response && response.data && response.data.editRounds) {
              const { errorCode, validationError } = response.data.editRounds;
              if (!errorCode && !validationError) {
                var snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Interview rounds updated successfully.",
                };
                vm.showAlert(snackbarData);
                vm.$emit("form-updated");
                vm.isLoadingCard = false;
              } else {
                vm.handleAddEditError("updating");
              }
            } else {
              vm.handleAddEditError("updating");
            }
          })
          .catch((addEditError) => {
            vm.handleAddEditError("updating", addEditError);
          });
      } catch {
        vm.handleAddEditError("updating");
      }
    },

    handleAddEditError(action, err = "") {
      this.isLoadingCard = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: action,
          form: "interview rounds",
          isListError: false,
        })
        .then((validationErrors) => {
          this.validationMessages = validationErrors;
          this.showValidationAlert = true;
        });
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
