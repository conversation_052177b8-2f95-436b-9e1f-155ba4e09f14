<template>
  <div class="text-center">
    <v-overlay
      v-model="overlay"
      class="d-flex justify-end overlay-content-parent"
      @click:outside="onClickClose()"
      persistent
      style="z-index: 1000"
    >
      <template v-slot:default>
        <div
          class="overlay-card"
          :style="{
            height: windowHeight + 'px',
            width: windowWidth >= 770 ? '35vw' : '100vw',
          }"
        >
          <div
            :class="
              windowWidth < 770
                ? ' d-flex bg-white justify-end align-center'
                : 'd-flex align-center text-h6 text-medium-emphasis pa-2 bg-primary'
            "
            style="width: 100%"
          >
            <v-icon
              v-if="displayCustomEmail"
              @click="
                displayCustomEmail
                  ? (displayCustomEmail = false)
                  : $emit('close-overlay')
              "
              size="17"
              class="mx-2"
              >fas fa-chevron-left</v-icon
            >
            <span v-if="windowWidth >= 770" class="ml-2">
              Move to Archive
            </span>
            <v-card v-else class="d-flex justify-end pa-2 fixed-title">
              <v-btn
                class="mr-5"
                variant="outlined"
                @click="
                  displayCustomEmail
                    ? (this.displayCustomEmail = false)
                    : onClickClose()
                "
                rounded="lg"
                >Cancel</v-btn
              >
              <v-btn
                color="primary"
                variant="elevated"
                rounded="lg"
                @click="validateArchiveCandidateForm()"
              >
                {{
                  displayCustomEmail
                    ? notificationTime &&
                      notificationTime.toLowerCase() === "now"
                      ? "Send Email"
                      : "Submit"
                    : notifyCandidate
                    ? "Preview Email"
                    : "Submit"
                }}
              </v-btn>
            </v-card>
            <v-spacer></v-spacer>
            <v-btn
              v-if="windowWidth >= 770"
              icon="fas fa-times"
              variant="text"
              @click="onClickClose()"
            ></v-btn>
          </div>
          <div class="overlay-body">
            <v-form v-if="!displayCustomEmail" ref="archiveForm">
              <v-row class="px-sm-4 px-md-6 pt-sm-4">
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <CustomSelect
                    :items="archiveReasonList"
                    v-model="archiveReason"
                    label="Reason for Archiving"
                    itemValue="Reason_Id"
                    itemTitle="Reason"
                    ref="archiveReason"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :is-loading="isArchiveReasonLoading"
                    :rules="[required('Reason for Archiving', archiveReason)]"
                    :itemSelected="archiveReason"
                    @selected-item="archiveReason = $event"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                >
                  <v-switch
                    v-model="notifyCandidate"
                    label="Notify Candidate"
                    :false-value="false"
                    :true-value="true"
                    color="primary"
                    hide-details
                  ></v-switch>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="6"
                  md="6"
                  xl="6"
                  class="pl-sm-4 pl-md-6"
                  style="height: 100px"
                  v-if="notifyCandidate"
                >
                  <CustomSelect
                    :items="notificationTimeList"
                    v-model="notificationTime"
                    label="Time"
                    itemValue="notificationTime"
                    itemTitle="notificationTime"
                    ref="notificationTime"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :rules="[required('Notification Time', notificationTime)]"
                    :itemSelected="notificationTime"
                    @selected-item="notificationTime = $event"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                  v-if="notifyCandidate && emailTemplateList?.length"
                >
                  <CustomSelect
                    :items="emailTemplateList"
                    v-model="selectedEmailTemplate"
                    label="Email Template"
                    itemValue="Template_Id"
                    itemTitle="Template_Name"
                    ref="selectedEmailTemplate"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :rules="[required('Email Template', selectedEmailTemplate)]"
                    :itemSelected="selectedEmailTemplate"
                    @selected-item="selectedEmailTemplate = $event"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                >
                  <v-textarea
                    v-model="archiveComment"
                    rows="2"
                    row-height="10"
                    color="primary"
                    hide-details="auto"
                    variant="solo"
                    label="Comment"
                    counter="500"
                    :rules="[
                      archiveComment
                        ? validateWithRulesAndReturnMessages(
                            archiveComment,
                            'archiveComment',
                            'Archive Comment'
                          )
                        : true,
                    ]"
                    @update:model-value="deductFormChange()"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
            <CustomEmail
              v-else
              ref="customEmail"
              :formId="16"
              :templateEmail="[candidateDetails.Personal_Email]"
              :templateData="templateData"
              typeOfSchedule="noncalendar"
              :typeOfTemplate="typeOfTemplate"
              :emailTemplateList="emailTemplateList"
              :selectedEmailTemplate="selectedEmailTemplate"
              :selectedCandidateId="selectedCandidateId"
              :noCustomTemplate="noCustomTemplate"
              :notificationTimeNow="
                notificationTime && notificationTime.toLowerCase() === 'now'
                  ? true
                  : false
              "
              :submitText="
                notificationTime && notificationTime.toLowerCase() === 'now'
                  ? 'Send Email'
                  : 'Submit'
              "
              @custom-email-cancel="closeCustomEmail()"
            ></CustomEmail>
          </div>
          <v-card
            v-if="windowWidth >= 770"
            class="overlay-footer pa-2"
            elevation="16"
          >
            <v-btn
              class="mr-5"
              variant="outlined"
              @click="
                displayCustomEmail
                  ? (this.displayCustomEmail = false)
                  : onClickClose()
              "
              rounded="lg"
              >Cancel</v-btn
            >
            <v-btn
              color="primary"
              variant="elevated"
              rounded="lg"
              @click="validateArchiveCandidateForm()"
            >
              {{
                displayCustomEmail
                  ? notificationTime && notificationTime.toLowerCase() === "now"
                    ? "Send Email"
                    : "Submit"
                  : notifyCandidate
                  ? "Preview Email"
                  : "Submit"
              }}
            </v-btn>
          </v-card>
        </div>
        <AppLoading v-if="isLoading"></AppLoading>
      </template>
    </v-overlay>
  </div>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="onConfirmCloseEditFrom()"
  ></AppWarningModal>
</template>

<script>
import { checkNullValue } from "@/helper";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import {
  ARCHIVE_CANDIDATE_DETAILS,
  GET_ARCHIVE_REASONS_LIST,
} from "@/graphql/recruitment/recruitmentQueries.js";
import CustomEmail from "@/views/common/customEmail/CustomEmailComponent.vue";
import {
  recruitmentEmailTemplates,
  replacementTags,
} from "@/views/common/customEmail/recruitmentEmailTemplates";
import { LIST_CUSTOM_EMAIL_TEMPLATES } from "@/graphql/settings/email-template/emailTemplateQueries.js";
import moment from "moment";

export default {
  name: "ArchiveCandidateOverlayForm",
  components: {
    CustomSelect,
    CustomEmail,
  },
  props: {
    candidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    candidateId: {
      type: Number,
      required: true,
    },
    jobTitle: {
      default: "",
      type: String,
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    originalStatusList: {
      type: Array,
      required: true,
    },
  },
  mixins: [validationRules],
  emits: ["close-archive-candidates-window", "refetch-data"],

  data: () => ({
    isFormDirty: false,
    showConfirmation: false,
    overlay: true,
    isLoading: false,
    notificationTime: null,
    notifyCandidate: false,
    archiveReason: null,
    archiveComment: "",
    openCustomEmail: false,
    itemTalentList: [],
    selectedTalentPoolId: null,
    archiveStatusId: null,
    buttonText: "Submit",
    notificationTimeList: [
      "Now",
      "After 2 Hours",
      "After 8 Hours",
      "After 24 Hours",
      "After 48 Hours",
    ],
    isArchiveReasonLoading: false,
    archiveReasonList: [],
    typeOfTemplate: "ArchiveApplication",
    htmlContent: null,
    emailTemplateList: [],
    selectedEmailTemplate: null,
    selectedCandidateId: null,
    noCustomTemplate: false,
    displayCustomEmail: false,
    openConfirmationPopup: false,
    archiveReasonValue: "",
  }),

  mounted() {
    this.getArchivedSatusId();
    this.fetchArchiveReasonList();
    this.selectedCandidateId = this.candidateId;
  },

  watch: {
    notifyCandidate(val) {
      if (val) {
        this.buttonText = "Next";
        this.fetchEmailTemplates();
      } else {
        this.buttonText = "Submit";
      }
    },
    archiveReason(val) {
      this.archiveReasonValue = this.getReasonById(this.archiveReasonList, val);
    },
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgDetails() {
      return this.$store.state.orgDetails;
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    loginEmployeeDetails() {
      return this.$store.state.orgDetails.userDetails;
    },
  },
  methods: {
    checkNullValue,
    deductFormChange() {
      this.isFormDirty = true;
    },
    onClickClose() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.closeWindow();
      }
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.closeWindow();
    },
    async validateArchiveCandidateForm() {
      this.htmlContent = null;
      if (!this.displayCustomEmail) {
        let { valid } = await this.$refs.archiveForm.validate();
        if (valid) {
          if (this.notifyCandidate) {
            this.templateData = {
              Company_Name: this.companyName,
              Candidate_Name: [
                this.candidateDetails.First_Name,
                this.candidateDetails.Middle_Name,
                this.candidateDetails.Last_Name,
              ]
                .filter((name) => name)
                .join(" "),
              Recruiter_Name: this.loginEmployeeDetails.employeeFullName,
              Designation: this.loginEmployeeDetails.designationName,
              Archive_Reason: this.archiveReasonValue,
              Job_Post_Name: this.candidateDetails.Job_Post_Name,
            };
            if (recruitmentEmailTemplates[this.typeOfTemplate]) {
              this.htmlContent = this.replaceTags(
                recruitmentEmailTemplates[this.typeOfTemplate].body,
                replacementTags,
                this.templateData
              );
            }
            this.displayCustomEmail = true;
          } else {
            this.archiveCandidate();
          }
        }
      } else {
        let customEmailRef = this.$refs.customEmail
          ? this.$refs.customEmail
          : null;
        if (customEmailRef) {
          this.htmlContent = customEmailRef.htmlContent;
          let noPlaceholderFound = customEmailRef.noPlaceholderFound;
          if (noPlaceholderFound) {
            let snackbarData = {
              isOpen: true,
              message:
                "Some placeholders are not replaced, kindly replace or remove them before proceeding.",
              type: "warning",
            };
            this.showAlert(snackbarData);
            customEmailRef.noPlaceholderFound = false;
          } else {
            const { valid } =
              await customEmailRef.$refs.customEmailForm.validate();
            if (valid && customEmailRef.isContentPresent) {
              this.archiveCandidate(customEmailRef);
            }
          }
        }
      }
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeWindow() {
      this.$emit("close-archive-candidates-window");
      this.overlay = true;
    },
    closeCustomEmail() {
      this.templateData = "";
      this.templateType = "";
      this.comment = "";
      this.selectedItem = null;
      this.openCustomEmail = false;
      this.closeWindow();
    },

    async archiveCandidate(customEmailRef = null) {
      let vm = this;
      vm.isLoading = true;
      const formmatedNotificationTime = this.getFormattedDate(
        vm.notificationTime
      );
      try {
        const response = await vm.$apollo.mutate({
          mutation: ARCHIVE_CANDIDATE_DETAILS,
          client: "apolloClientBF",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: parseInt(vm.candidateId),
            archiveReasonId: vm.archiveReason,
            notificationTime: formmatedNotificationTime,
            archiveComment: vm.archiveComment,
            archiveStatusId: this.archiveStatusId,
            mailContent:
              vm.notifyCandidate &&
              vm.notificationTime &&
              vm.notificationTime.toLowerCase() === "now"
                ? null
                : vm.htmlContent,
            action: "candidate",
          },
        });

        if (
          response &&
          response.data &&
          response.data.archiveCandidateDetails
        ) {
          const { errorCode, validationError } =
            response.data.archiveCandidateDetails;
          if (!errorCode && !validationError) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate archived successfully.",
            };
            // sending the custom email
            if (customEmailRef) {
              try {
                await customEmailRef.validateCustomEmailForm();
              } catch (error) {
                vm.handleArchiveCandidateError(error);
              }
            }
            vm.showAlert(snackbarData);
            vm.closeWindow();
            vm.$emit("refetch-data");
          } else {
            vm.handleArchiveCandidateError();
          }
        } else {
          vm.handleArchiveCandidateError();
        }
      } catch (err) {
        vm.handleArchiveCandidateError(err);
      } finally {
        vm.isLoading = false;
        this.overlay = true;
      }
    },
    handleArchiveCandidateError(err = "") {
      this.isLoading = false;
      this.trackingStatusLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "archiving",
        form: "candidate details",
        isListError: false,
      });
    },
    getArchivedSatusId() {
      let archivedItem = this.originalStatusList.find(
        (item) => item.Status?.toLowerCase() === "archived"
      );
      this.archiveStatusId = archivedItem ? archivedItem.Id : null;
    },
    getFormattedDate(selectedItem) {
      if (!selectedItem) {
        return null;
      }
      let date = moment().utc();

      switch (selectedItem) {
        case "Now":
          date = null;
          break;
        case "After 2 Hours":
          date = date.add(2, "hours");
          break;
        case "After 8 Hours":
          date = date.add(8, "hours");
          break;
        case "After 24 Hours":
          date = date.add(24, "hours");
          break;
        case "After 48 Hours":
          date = date.add(48, "hours");
          break;
        default:
          return "Invalid selection";
      }
      return date ? date.format("YYYY-MM-DD HH:mm:ss") : null;
    },
    fetchArchiveReasonList() {
      let vm = this;
      vm.isArchiveReasonLoading = true;
      vm.$apollo
        .query({
          query: GET_ARCHIVE_REASONS_LIST,
          client: "apolloClientAY",
          variables: {
            formId: 16,
            stageId: this.candidateDetails?.Hiring_Stage_Id,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getArchiveReasonsList &&
            response.data.getArchiveReasonsList.archiveReasonList &&
            !response.data.getArchiveReasonsList.archiveReasonList.errorCode
          ) {
            vm.archiveReasonList =
              response.data.getArchiveReasonsList.archiveReasonList;
            vm.isArchiveReasonLoading = false;
          } else {
            vm.archiveReasonList = [];
            vm.isArchiveReasonLoading = false;
          }
        })
        .catch((err) => {
          vm.isArchiveReasonLoading = false;
          vm.handleFetchArchiveReasonListError(err);
        });
    },
    handleFetchArchiveReasonListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "archive reason list",
        isListError: false,
      });
    },
    replaceTags(template, replacementTags, templateData) {
      for (const [tag, replacement] of Object.entries(replacementTags)) {
        if (
          template.includes(tag) &&
          templateData.hasOwnProperty(replacement)
        ) {
          // Check if tag exists in template and replacement data exists
          template = template.replace(
            new RegExp("\\" + tag, "g"),
            templateData[replacement]
          );
        }
      }
      return template;
    },
    fetchEmailTemplates() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: LIST_CUSTOM_EMAIL_TEMPLATES,
          variables: {
            formId: 16,
            categoryId: 7,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listCustomEmailTemplates &&
            response.data.listCustomEmailTemplates.emailTemplates
          ) {
            this.emailTemplateList =
              response.data.listCustomEmailTemplates.emailTemplates;
            this.noCustomTemplate =
              this.emailTemplateList?.length === 0 ? true : false;
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.isLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "email templates",
        isListError: false,
      });
    },
    getReasonById(reasons, reasonId) {
      const result = reasons.find((item) => item.Reason_Id === reasonId);
      return result ? result.Reason : "";
    },
  },
};
</script>

<style scoped>
.overlay-card {
  background: white;
}

.overlay-content-parent {
  z-index: 1000 !important;
}

.overlay-body {
  padding: 15px;
  height: calc(100vh - 130px);
  overflow-y: scroll !important;
  overflow: hidden;
}

.overlay-footer {
  height: auto;
  position: fixed;
  bottom: 0;
  width: 100%;
}
.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.fixed-title {
  width: 100%;
}
</style>
