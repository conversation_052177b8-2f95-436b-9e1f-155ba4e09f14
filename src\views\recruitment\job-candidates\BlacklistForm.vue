<template>
  <div>
    <v-overlay
      v-model="showBlacklistForm"
      @click:outside="closeForm()"
      class="d-flex justify-end"
      @update:model-value="$emit('close-form')"
    >
      <template v-slot:default>
        <v-card
          :style="{
            height: windowHeight + 'px',
            width: windowWidth <= 770 ? '100vw' : '30vw',
          }"
        >
          <div class="d-flex align-centerpa-2 bg-primary w-100">
            <div class="text-h6 s pa-2" style="color: white">
              Blacklist Candidate
            </div>
            <v-spacer></v-spacer>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="closeForm()"
            ></v-btn>
          </div>
          <v-form ref="blacklistForm" class="pa-5">
            <custom-select
              label="Reason to blacklist"
              :items="reasonList"
              :loading="reasonListLoading"
              :item-selected="reason"
              :is-auto-complete="true"
              :is-required="true"
              item-title="Blacklisted_Reason"
              item-value="Blacklisted_Reason_Id"
              :rules="[required('Reason', reason)]"
              @selected-item="reason = $event"
            ></custom-select>
            <v-textarea
              v-model="comment"
              variant="solo"
              :rules="[
                required('Comment', comment),
                validateWithRulesAndReturnMessages(
                  comment,
                  'blackedComment',
                  'Comment'
                ),
              ]"
            >
              <template v-slot:label>
                <span>Comment</span>
                <span class="text-red">*</span>
              </template>
            </v-textarea>
            <div class="d-flex justify-end mb-2">
              <v-btn
                prepend-icon="fas fa-plus"
                variant="text"
                color="primary"
                @click="addDocument()"
                >Add</v-btn
              >
            </div>
            <div
              v-for="(item, index) in documentList"
              :key="index"
              class="d-flex align-center"
            >
              <v-file-input
                :model-value="item"
                prepend-icon
                append-inner-icon="fas fa-paperclip"
                label="Document"
                variant="solo"
                :rules="[checkSize(index)]"
                accept="image/png, image/jpeg, image/jpg, application/pdf"
                @update:model-value="onChangeFiles(index, $event)"
              >
              </v-file-input>
              <v-icon
                v-if="documentList.length > 1"
                class="ml-2"
                size="15"
                @click="documentList.splice(index, 1)"
              >
                fas fa-trash
              </v-icon>
            </div>
          </v-form>
          <v-card
            style="width: 100%; height: 7%"
            :elevation="10"
            class="position-absolute bottom-0 pa-3 d-flex justify-end"
          >
            <v-btn
              rounded="lg"
              color="primary"
              variant="outlined"
              class="mr-2"
              @click="closeForm()"
            >
              Cancel
            </v-btn>
            <v-btn rounded="lg" color="primary" @click="validateForm()">
              Submit
            </v-btn>
          </v-card>
        </v-card>
      </template>
    </v-overlay>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import { MOVE_CANDIDATE_TO_BLACKLIST } from "@/graphql/recruitment/recruitmentQueries";
export default {
  name: "BlacklistForm",
  components: { CustomSelect },
  emits: ["close-form", "update-form"],
  mixins: [validationRules],
  props: {
    viewForm: {
      type: Boolean,
      default: false,
    },
    selectedCandidateId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      showBlacklistForm: false,
      reason: null,
      comment: "",
      documentList: [null],
      isLoading: false,
    };
  },
  computed: {
    currentTimeStamp() {
      return moment().unix();
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },
  watch: {
    viewForm(val) {
      this.showBlacklistForm = val;
    },
    showBlacklistForm(val) {
      if (!val) this.$emit("close-form");
    },
  },
  mounted() {
    this.getBlacklistReasonList();
  },
  methods: {
    closeForm(type) {
      this.showBlacklistForm = false;
      this.reason = null;
      this.comment = "";
      this.documentList = [null];
      if (type == "update") {
        this.$emit("update-form");
      } else {
        this.$emit("close-form");
      }
    },
    checkSize(index) {
      if (this.documentList[index]) {
        const fileSize = this.documentList[index]?.size;
        if (fileSize > 3000000) {
          return "The file size should be less than 3 MB.";
        }
        return true;
      }
      return true;
    },
    getBlacklistReasonList() {
      let vm = this;
      vm.reasonListLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;

      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            key: "blacklist_reasons",
          },
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.retrieveDropdownDetails &&
            data.retrieveDropdownDetails.dropdownDetails
          ) {
            const tempData = JSON.parse(
              data.retrieveDropdownDetails.dropdownDetails
            );
            vm.reasonList = tempData[0].data;
          }
          vm.reasonListLoading = false;
        })
        .catch((error) => {
          vm.reasonList = [];
          vm.reasonListLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: error,
            action: "retrieving",
            form: "blacklist reason list",
            isListError: false,
          });
        });
    },
    async addDocument() {
      this.documentList.push(null);
    },
    onChangeFiles(index, val) {
      this.documentList[index] = val;
      if (val) {
        this.documentList[index].formatName =
          this.currentTimeStamp + "?blacklistCandidate?1?" + val.name;
      }
    },
    async validateForm() {
      const { valid } = await this.$refs.blacklistForm.validate();
      if (valid) {
        this.validateDocuments();
      }
    },

    async validateDocuments() {
      try {
        // Upload Documents files
        this.isLoading = true;
        this.documentList = this.documentList.filter((item) => item);
        if (this.documentList.length > 0) {
          for (let document of this.documentList) {
            await this.uploadFileContents(document);
          }
        }
        this.moveCandidateToBlacklist();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },
    moveCandidateToBlacklist() {
      let vm = this;
      vm.isLoading = true;
      let documentNameList = this.documentList
        .filter((document) => document)
        .map((document) => {
          return { filePath: document.formatName };
        });
      vm.$apollo
        .mutate({
          mutation: MOVE_CANDIDATE_TO_BLACKLIST,
          client: "apolloClientAH",
          variables: {
            candidateId: this.selectedCandidateId,
            blackedReasonId: this.reason,
            blackedComment: this.comment,
            blackedAttachment: documentNameList,
          },
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.moveCandidateToBlackedList &&
            !data.moveCandidateToBlackedList.errorCode
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate blacklisted successfully",
            };
            vm.isLoading = false;
            this.showAlert(snackbarData);
            this.closeForm("update");
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "blacklisting",
            form: "candidate",
            isListError: false,
          });
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async uploadFileContents(fileContent) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/Blacklist Candidate/";
      await vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + fileContent.formatName,
          action: "upload",
          type: "documents",
          fileContent: fileContent,
        })
        .catch((error) => {
          throw error;
        });
    },
  },
};
</script>
