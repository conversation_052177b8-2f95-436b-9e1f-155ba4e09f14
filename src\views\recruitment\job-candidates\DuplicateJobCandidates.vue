<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
        :show-bottom-sheet="!showJobCandidateDetails"
      >
        <template #topBarContent>
          <v-row justify="center">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                v-show="
                  !showJobCandidateDetails &&
                  (itemList.length || isFilterApplied)
                "
                class="justify-end"
                :isFilter="false"
              ></EmployeeDefaultFilterMenu>
              <FormFilter
                v-if="
                  !dropDownLoading &&
                  !showJobCandidateDetails &&
                  (itemList.length || isFilterApplied)
                "
                ref="formFilterRef"
                :status-list="candidateStatusList"
                :drop-down="dropDownDetails"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilterFromFilterComponent($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container v-if="!isLoading" fluid class="job-candidate-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="enableSkeletonLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList(true)"
          />

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showJobCandidateDetails"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="!isFilterApplied"
            :image-name="!isFilterApplied ? '' : 'common/no-records'"
          >
            <template v-if="!isFilterApplied" #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  class="rounded-lg pa-5 mb-4"
                  :style="!isFilterApplied ? 'background: white' : ''"
                >
                  <v-col v-if="itemList.length === 0" cols="12">
                    <NotesCard
                      notes="Our candidate management module streamlines your recruitment process by providing a centralized platform to effortlessly manage candidate profiles, applications, and interactions. Whether sourcing candidates or onboarding new hires, our intuitive features ensure you stay organized and efficient throughout the hiring journey."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="From tracking applicant profiles to scheduling interviews and managing feedback, our platform simplifies every step of the recruitment process. With seamless integration and customizable workflows, you can elevate your hiring experience and focus on selecting the best candidates for your team."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4 flex-wrap"
                  >
                    <v-btn
                      v-if="formAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 primary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddJobCandidate()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon
                      ><span class="primary">Add Job Candidate</span>
                    </v-btn>
                    <v-btn
                      v-if="itemList.length === 0"
                      color="white"
                      rounded="lg"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
            <template v-else #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4 flex-wrap"
                  >
                    <div
                      v-if="
                        jobTitleFilteredList.length === 0 &&
                        recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
                          'yes'
                      "
                      :style="
                        isMobileView
                          ? 'min-width: 300px; max-width: 400px'
                          : 'min-width: 400px; max-width: 600px'
                      "
                      class="ml-2 mr-3"
                    >
                      <JobCandidateGlobalSearch
                        :formId="16"
                        screenTab="duplicatecandidates"
                        placeholder="Search candidates..."
                        @candidate-selected="handleGlobalCandidateSelection"
                      />
                    </div>
                    <div
                      v-if="jobTitleFilteredList.length === 0"
                      style="min-width: 200px"
                      class="ml-2 mr-3"
                    >
                      <CustomSelect
                        :items="stageList"
                        label="Stages"
                        itemValue="id"
                        itemTitle="value"
                        :isAutoComplete="true"
                        :itemSelected="selectedStage"
                        density="compact"
                        class="mt-5"
                        @selected-item="onSelectStage($event)"
                      />
                    </div>
                    <div
                      v-if="jobTitleFilteredList.length === 0"
                      style="max-width: 300px"
                      class="ml-2 mr-3"
                    >
                      <CustomSelect
                        :items="jobPostList"
                        label=""
                        prefix="Job Title: "
                        :isAutoComplete="true"
                        :is-loading="jobPostLoding"
                        itemValue="Job_Post_Id"
                        itemTitle="Job_Post_Name_With_Count"
                        :itemSelected="selectedJobPostId"
                        density="compact"
                        class="mt-5"
                        @selected-item="onSelectJobPost($event)"
                      />
                    </div>
                    <v-btn
                      v-if="jobTitleFilteredList.length === 0 && formAccess.add"
                      prepend-icon="fas fa-plus"
                      rounded="lg"
                      variant="elevated"
                      class="mx-1 primary"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddJobCandidate"
                    >
                      <template v-slot:prepend>
                        <v-icon class="primary"></v-icon>
                      </template>
                      <span class="primary">Add Job Candidate</span>
                    </v-btn>
                    <v-btn
                      v-if="jobTitleFilteredList.length !== 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter()"
                      >Reset Filter/Search</v-btn
                    >
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div v-if="showJobCandidateDetails">
              <JobCandidatesDetails
                ref="jobCandidateDetails"
                :formAccess="formAccess"
                :selectedJobCandidateId="
                  selectedItem ? selectedItem.Candidate_Id : 0
                "
                :selectedJobPostId="selectedItem ? selectedItem.Job_Post_Id : 0"
                :candidateChangeCount="candidateChangeCount"
                :candidateList="itemList"
                :originalList="originalList"
                :currentSortedItems="currentSortedItems"
                :recruitmentSettings="recruitmentSettings"
                :selectedItem="selectedItem"
                :parentTabName="parentTabName"
                :canScheduleInterview="
                  selectedItem
                    ? !candidateNoActionStatusList.includes(
                        selectedItem.Status_Id
                      ) &&
                      validateUser(selectedItem.Recruiter_Id) &&
                      enableInterview.includes(selectedItem.Hiring_Stage) &&
                      !!selectedItem.Job_Post_Id
                    : false
                "
                :candidateNoActionStatusList="candidateNoActionStatusList"
                :isRecruiter="isRecruiter"
                @retrieve-error="closeAllForms($event)"
                @close-view-form="closeAllForms($event)"
                @on-change-candidate="onChangeCandidate($event)"
                @check-candidate-actions="handleCandidateBlacklistCheck($event)"
              />
            </div>
            <div v-else>
              <div
                class="d-flex flex-wrap align-center my-3"
                :class="isMobileView ? 'flex-column' : ''"
                style="justify-content: space-between"
              >
                <div
                  class="d-flex align-center flex-wrap"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <!-- Global Candidate Search Component -->
                  <div
                    :style="
                      isMobileView
                        ? 'min-width: 300px; max-width: 400px'
                        : 'min-width: 400px; max-width: 600px'
                    "
                    class="mr-3"
                  >
                    <JobCandidateGlobalSearch
                      v-if="
                        recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
                        'yes'
                      "
                      :formId="16"
                      screenTab="duplicatecandidates"
                      placeholder="Search candidates..."
                      @candidate-selected="handleGlobalCandidateSelection"
                    />
                  </div>
                </div>
                <div
                  class="d-flex align-center my-3 flex-wrap"
                  :class="isMobileView ? 'justify-center ' : 'justify-end'"
                >
                  <div style="min-width: 200px" class="ml-2 mr-3">
                    <CustomSelect
                      :items="stageList"
                      label="Stages"
                      itemValue="id"
                      itemTitle="value"
                      :isAutoComplete="true"
                      :itemSelected="selectedStage"
                      density="compact"
                      class="mt-5"
                      @selected-item="onSelectStage($event)"
                    />
                  </div>
                  <div
                    style="max-width: 300px; min-width: 200px"
                    class="ml-2 mr-3"
                  >
                    <CustomSelect
                      :items="jobPostList"
                      label=""
                      prefix="Job Title: "
                      :isAutoComplete="true"
                      :is-loading="jobPostLoding"
                      itemValue="Job_Post_Id"
                      itemTitle="Job_Post_Name_With_Count"
                      :itemSelected="selectedJobPostId"
                      density="compact"
                      class="mt-5"
                      @selected-item="onSelectJobPost($event)"
                    />
                  </div>
                  <v-btn
                    v-if="formAccess.add"
                    prepend-icon="fas fa-plus"
                    variant="elevated"
                    rounded="lg"
                    class="mx-1 primary"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="onAddJobCandidate"
                  >
                    <template v-slot:prepend>
                      <v-icon></v-icon>
                    </template>
                    <span>Add Job Candidate</span>
                  </v-btn>
                  <v-btn
                    color="transparent"
                    class="ml-1 mt-1"
                    variant="flat"
                    @click="refetchList"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                  <v-menu v-model="openMoreMenu" transition="scale-transition">
                    <template v-slot:activator="{ props }">
                      <v-btn variant="transparent" class="mt-1" v-bind="props">
                        <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                        <v-icon v-else>fas fa-caret-up</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="action in moreActions"
                        :key="action"
                        @click="onMoreAction(action)"
                      >
                        <v-hover v-bind="props">
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                            >
                              <v-tooltip :text="action.message">
                                <template v-slot:activator="{ props }">
                                  <div v-bind="action.message ? props : ''">
                                    {{ action.key }}
                                  </div>
                                </template>
                              </v-tooltip>
                            </v-list-item-title>
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                  <!-- Dynamic Columns Menu -->
                  <v-menu
                    v-model="openDynamicColumnsMenu"
                    transition="scale-transition"
                    :close-on-content-click="false"
                    location="bottom end"
                    max-width="350"
                    offset="10"
                    @update:model-value="onDynamicColumnsMenuToggle"
                  >
                    <template v-slot:activator="{ props }">
                      <v-btn
                        variant="transparent"
                        class="mt-1"
                        v-bind="props"
                        v-show="openDynamicColumnsMenu"
                      >
                        <v-icon size="x-small">fas fa-arrow-left</v-icon>
                      </v-btn>
                    </template>
                    <v-card>
                      <v-card-title class="text-h6 pa-4 d-flex align-center">
                        <v-btn
                          variant="text"
                          size="small"
                          icon
                          class="mr-2"
                          @click="closeDynamicColumnsMenu"
                        >
                          <v-icon size="small">fas fa-arrow-left</v-icon>
                        </v-btn>
                        Edit Columns
                      </v-card-title>
                      <v-divider></v-divider>
                      <v-card-text class="pa-0">
                        <v-list density="compact">
                          <!-- Select All Option -->
                          <v-list-item class="px-4 py-2 bg-grey-lighten-5">
                            <template v-slot:prepend>
                              <v-checkbox-btn
                                v-model="selectAllColumns"
                                color="primary"
                                false-icon="far fa-square"
                                true-icon="fas fa-check-square"
                                class="mr-3"
                                @change="toggleSelectAllColumns"
                              ></v-checkbox-btn>
                            </template>
                            <v-list-item-title class="font-weight-medium">
                              Select All
                            </v-list-item-title>
                          </v-list-item>
                          <v-divider></v-divider>

                          <!-- Individual Column Options -->
                          <v-list-item
                            v-for="column in editableColumns"
                            :key="column.key"
                            class="px-4 py-2"
                          >
                            <template v-slot:prepend>
                              <v-checkbox-btn
                                v-model="column.selected"
                                color="primary"
                                false-icon="far fa-square"
                                true-icon="fas fa-check-square"
                                class="mr-3"
                              ></v-checkbox-btn>
                            </template>
                            <v-list-item-title>
                              {{ column.label }}
                            </v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-card-text>
                      <v-divider></v-divider>
                      <v-card-actions class="pa-4">
                        <v-spacer></v-spacer>
                        <v-btn
                          variant="outlined"
                          color="primary"
                          rounded="lg"
                          size="small"
                          @click="closeDynamicColumnsMenu"
                        >
                          Cancel
                        </v-btn>
                        <v-btn
                          color="primary"
                          variant="elevated"
                          rounded="lg"
                          size="small"
                          @click="saveDynamicColumns"
                        >
                          Save
                        </v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-menu>
                </div>
              </div>
              <!-- Mobile Column Edit Button -->
              <div
                v-if="
                  isMobileView &&
                  itemList.length > 0 &&
                  recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
                    'yes'
                "
                class="d-flex justify-end mb-3"
              >
                <v-btn
                  variant="outlined"
                  color="primary"
                  size="small"
                  rounded="lg"
                  @click="openEditColumnsMenu"
                  class="mr-2"
                >
                  <v-icon size="16" class="mr-1">fas fa-ellipsis-v</v-icon>
                  Edit Columns
                </v-btn>
              </div>
              <v-row>
                <v-col cols="12" class="mb-12">
                  <!-- Mobile Card View -->
                  <div v-if="isMobileView" class="mobile-cards-container">
                    <v-card
                      v-for="item in itemList"
                      :key="item.Candidate_Id"
                      class="candidate-card mb-4 pa-4"
                      elevation="2"
                      @click="openViewForm(item)"
                    >
                      <!-- Top Row with Checkbox and Action Menu -->
                      <div class="d-flex">
                        <v-checkbox-btn
                          v-model="item.isSelected"
                          color="primary"
                          false-icon="far fa-circle"
                          true-icon="fas fa-check-circle"
                          class="mt-n2"
                          @click.stop=""
                          @change="checkAllSelected()"
                        ></v-checkbox-btn>
                        <ActionMenu
                          v-if="
                            (validateUser(item.Recruiter_Id) ||
                              formAccess.delete) &&
                            !candidateNoActionStatusList.includes(
                              item.Status_Id
                            )
                          "
                          @selected-action="
                            checkCandidateBlacklist($event, item)
                          "
                          :actions="item.options"
                          :access-rights="item.havingAccess"
                          iconColor="grey"
                          @click.stop=""
                        ></ActionMenu>
                      </div>

                      <!-- Main Content Row -->
                      <v-row no-gutters>
                        <v-col cols="12" class="pl-2">
                          <!-- Candidate Name and Contact -->
                          <div class="candidate-header mb-3">
                            <div class="d-flex align-center mb-1">
                              <h3
                                class="text-primary text-h7 font-weight-medium"
                                :style="{
                                  width: windowWidth <= 425 ? '200px' : '',
                                }"
                              >
                                {{
                                  item.First_Name ||
                                  item.Middle_Name ||
                                  item.Last_Name
                                    ? [
                                        item.First_Name,
                                        item.Middle_Name,
                                        item.Last_Name,
                                      ]
                                        .filter(Boolean)
                                        .join(" ")
                                    : "-"
                                }}
                              </h3>
                              <v-tooltip
                                v-if="item.Blacklisted?.toLowerCase() == 'yes'"
                                text="This candidate has been blacklisted"
                              >
                                <template v-slot:activator="{ props }">
                                  <v-icon
                                    size="16"
                                    class="ml-2"
                                    color="primary"
                                    v-bind="props"
                                  >
                                    fas fa-user-slash
                                  </v-icon>
                                </template>
                              </v-tooltip>
                              <v-badge
                                v-if="
                                  item.Duplicate_Count &&
                                  item.Duplicate_Count > 1
                                "
                                color="hover"
                                text-color="white"
                                :content="item.Duplicate_Count"
                                class="ml-4"
                              ></v-badge>
                            </div>
                            <div class="text-body-2 text-grey-darken-1">
                              {{
                                item.Mobile_No_Country_Code
                                  ? item.Mobile_No_Country_Code +
                                    " " +
                                    checkNullValue(item.Mobile_No)
                                  : checkNullValue(item.Mobile_No)
                              }}
                            </div>
                            <div
                              v-if="item.Personal_Email"
                              class="text-body-2 text-grey-darken-1 text-truncate"
                            >
                              {{ checkNullValue(item.Personal_Email) }}
                            </div>
                          </div>

                          <!-- Dynamic Mobile Fields Row 1 -->
                          <v-row
                            no-gutters
                            class="mb-2"
                            v-if="getMobileRowFields(1).length > 0"
                          >
                            <v-col
                              v-for="field in getMobileRowFields(1)"
                              :key="field.key"
                              :cols="
                                getMobileRowFields(1).length === 1 ? 12 : 6
                              "
                            >
                              <div
                                class="text-caption text-grey-darken-2 font-weight-bold"
                              >
                                {{ field.label }}
                              </div>
                              <div
                                v-if="field.key === 'Candidate_Status'"
                                class="text-body-2"
                              >
                                <v-chip
                                  :color="getStatusColor(item.Candidate_Status)"
                                  size="small"
                                  variant="flat"
                                >
                                  {{ checkNullValue(item.Candidate_Status) }}
                                </v-chip>
                                <div
                                  v-if="item?.Archive_Reason"
                                  class="text-caption text-grey mt-1"
                                >
                                  {{ item.Archive_Reason }}
                                </div>
                              </div>
                              <div
                                v-else-if="field.key === 'Applicant_Ranking'"
                                class="d-flex align-center mt-1"
                              >
                                <v-progress-linear
                                  :model-value="item.Applicant_Ranking"
                                  height="12"
                                  width="80"
                                  :color="
                                    item.Applicant_Ranking <= 30
                                      ? 'red-accent-1'
                                      : item.Applicant_Ranking <= 60
                                      ? 'amber-lighten-2'
                                      : 'green-lighten-3'
                                  "
                                  rounded="lg"
                                  class="mr-2"
                                ></v-progress-linear>
                                <span class="text-caption"
                                  >{{ item.Applicant_Ranking }}%</span
                                >
                              </div>
                              <div
                                v-else-if="field.key === 'Resume'"
                                class="text-body-2"
                              >
                                <v-btn
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                  @click.stop="
                                    retrieveResumeDetails(item.Resume)
                                  "
                                >
                                  <v-icon size="14" class="mr-1"
                                    >fas fa-file-pdf</v-icon
                                  >
                                  View Resume
                                </v-btn>
                              </div>
                              <div v-else class="text-body-2">
                                {{ getMobileFieldValue(item, field.key) }}
                              </div>
                            </v-col>
                          </v-row>
                          <!-- Dynamic Mobile Fields Row 2 -->
                          <v-row
                            no-gutters
                            class="mb-2"
                            v-if="getMobileRowFields(2).length > 0"
                          >
                            <v-col
                              v-for="field in getMobileRowFields(2)"
                              :key="field.key"
                              :cols="
                                getMobileRowFields(2).length === 1 ? 12 : 6
                              "
                            >
                              <div
                                class="text-caption text-grey-darken-2 font-weight-bold"
                              >
                                {{ field.label }}
                              </div>
                              <div
                                v-if="field.key === 'Candidate_Status'"
                                class="text-body-2"
                              >
                                <v-chip
                                  :color="getStatusColor(item.Candidate_Status)"
                                  size="small"
                                  variant="flat"
                                >
                                  {{ checkNullValue(item.Candidate_Status) }}
                                </v-chip>
                                <div
                                  v-if="item?.Archive_Reason"
                                  class="text-caption text-grey mt-1"
                                >
                                  {{ item.Archive_Reason }}
                                </div>
                              </div>
                              <div
                                v-else-if="field.key === 'Applicant_Ranking'"
                                class="d-flex align-center mt-1"
                              >
                                <v-progress-linear
                                  :model-value="item.Applicant_Ranking"
                                  height="12"
                                  width="80"
                                  :color="
                                    item.Applicant_Ranking <= 30
                                      ? 'red-accent-1'
                                      : item.Applicant_Ranking <= 60
                                      ? 'amber-lighten-2'
                                      : 'green-lighten-3'
                                  "
                                  rounded="lg"
                                  class="mr-2"
                                ></v-progress-linear>
                                <span class="text-caption"
                                  >{{ item.Applicant_Ranking }}%</span
                                >
                              </div>
                              <div
                                v-else-if="field.key === 'Resume'"
                                class="text-body-2"
                              >
                                <v-btn
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                  @click.stop="
                                    retrieveResumeDetails(item.Resume)
                                  "
                                >
                                  <v-icon size="14" class="mr-1"
                                    >fas fa-file-pdf</v-icon
                                  >
                                  View Resume
                                </v-btn>
                              </div>
                              <div v-else class="text-body-2">
                                {{ getMobileFieldValue(item, field.key) }}
                              </div>
                            </v-col>
                          </v-row>

                          <!-- Dynamic Mobile Fields Row 3 -->
                          <v-row
                            no-gutters
                            class="mb-2"
                            v-if="getMobileRowFields(3).length > 0"
                          >
                            <v-col
                              v-for="field in getMobileRowFields(3)"
                              :key="field.key"
                              :cols="
                                getMobileRowFields(3).length === 1 ? 12 : 6
                              "
                            >
                              <div
                                class="text-caption text-grey-darken-2 font-weight-bold"
                              >
                                {{ field.label }}
                              </div>
                              <div
                                v-if="field.key === 'Candidate_Status'"
                                class="text-body-2"
                              >
                                <v-chip
                                  :color="getStatusColor(item.Candidate_Status)"
                                  size="small"
                                  variant="flat"
                                >
                                  {{ checkNullValue(item.Candidate_Status) }}
                                </v-chip>
                                <div
                                  v-if="item?.Archive_Reason"
                                  class="text-caption text-grey mt-1"
                                >
                                  {{ item.Archive_Reason }}
                                </div>
                              </div>
                              <div
                                v-else-if="field.key === 'Applicant_Ranking'"
                                class="d-flex align-center mt-1"
                              >
                                <v-progress-linear
                                  :model-value="item.Applicant_Ranking"
                                  height="12"
                                  width="80"
                                  :color="
                                    item.Applicant_Ranking <= 30
                                      ? 'red-accent-1'
                                      : item.Applicant_Ranking <= 60
                                      ? 'amber-lighten-2'
                                      : 'green-lighten-3'
                                  "
                                  rounded="lg"
                                  class="mr-2"
                                ></v-progress-linear>
                                <span class="text-caption"
                                  >{{ item.Applicant_Ranking }}%</span
                                >
                              </div>
                              <div
                                v-else-if="field.key === 'Resume'"
                                class="text-body-2"
                              >
                                <v-btn
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                  @click.stop="
                                    retrieveResumeDetails(item.Resume)
                                  "
                                >
                                  <v-icon size="14" class="mr-1"
                                    >fas fa-file-pdf</v-icon
                                  >
                                  View Resume
                                </v-btn>
                              </div>
                              <div v-else class="text-body-2">
                                {{ getMobileFieldValue(item, field.key) }}
                              </div>
                            </v-col>
                          </v-row>

                          <!-- Dynamic Mobile Fields Row 4 -->
                          <v-row
                            no-gutters
                            class="mb-3"
                            v-if="getMobileRowFields(4).length > 0"
                          >
                            <v-col
                              v-for="field in getMobileRowFields(4)"
                              :key="field.key"
                              :cols="
                                getMobileRowFields(4).length === 1 ? 12 : 6
                              "
                            >
                              <div
                                class="text-caption text-grey-darken-2 font-weight-bold"
                              >
                                {{ field.label }}
                              </div>
                              <div
                                v-if="field.key === 'Resume'"
                                class="text-body-2"
                              >
                                <v-btn
                                  size="small"
                                  variant="outlined"
                                  color="primary"
                                  @click.stop="
                                    retrieveResumeDetails(item.Resume)
                                  "
                                >
                                  <v-icon size="14" class="mr-1"
                                    >fas fa-file-pdf</v-icon
                                  >
                                  View Resume
                                </v-btn>
                              </div>
                              <div v-else class="text-body-2">
                                {{ getMobileFieldValue(item, field.key) }}
                              </div>
                            </v-col>
                          </v-row>

                          <!-- Dynamic Fields from API (not in static mobile mapping) -->
                          <div
                            v-if="getMobileDynamicColumns().length > 0"
                            class="mt-2"
                          >
                            <v-row
                              v-for="(
                                rowFields, rowIndex
                              ) in getMobileDynamicColumnsInRows()"
                              :key="'dynamic-row-' + rowIndex"
                              no-gutters
                              class="mb-2"
                            >
                              <v-col
                                v-for="column in rowFields"
                                :key="column.key"
                                :cols="rowFields.length === 1 ? 12 : 6"
                              >
                                <div
                                  class="text-caption text-grey-darken-2 font-weight-bold"
                                >
                                  {{ column.label }}
                                </div>
                                <div class="text-body-2">
                                  {{
                                    checkNullValue(
                                      isDateField(column.key)
                                        ? formatOnlyDate(item[column.key])
                                        : item[column.key]
                                    )
                                  }}
                                </div>
                              </v-col>
                            </v-row>
                          </div>
                        </v-col>
                      </v-row>
                    </v-card>
                  </div>

                  <!-- Desktop Table View -->
                  <v-data-table
                    v-else
                    v-model="selectedCandidateRecords"
                    :headers="tableHeaders"
                    :items="itemList"
                    :show-select="!isMobileView"
                    fixed-header
                    :sort-by="[{ key: 'Added_On', order: 'desc' }]"
                    item-value="Candidate_Id"
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                    @update:currentItems="updateCurrentSortedItems($event)"
                    style="box-shadow: none !important"
                    class="elevation-1 custom-scroll-table"
                  >
                    <template
                      v-slot:[`header.data-table-select`]="{ selectAll }"
                    >
                      <v-checkbox-btn
                        v-model="selectAllBox"
                        color="primary"
                        false-icon="far fa-circle"
                        true-icon="fas fa-check-circle"
                        indeterminate-icon="fas fa-minus-circle"
                        class="mt-1"
                        @change="selectAll(selectAllBox)"
                      ></v-checkbox-btn>
                    </template>

                    <!-- Edit Columns Header -->
                    <template v-slot:[`header.Action`]>
                      <div class="d-flex align-center justify-center">
                        <span class="mr-2">Action</span>
                        <v-btn
                          v-if="
                            recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
                            'yes'
                          "
                          icon="fas fa-ellipsis-v"
                          variant="text"
                          color="grey"
                          size="small"
                          @click="openEditColumnsMenu"
                          class="ml-2"
                        >
                          <v-icon size="16" color="grey"
                            >fas fa-ellipsis-v</v-icon
                          >
                          <v-tooltip activator="parent" location="top">
                            Edit Columns
                          </v-tooltip>
                        </v-btn>
                      </div>
                    </template>
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                      >
                        <td>
                          <v-checkbox-btn
                            v-model="item.isSelected"
                            color="primary"
                            false-icon="far fa-circle"
                            true-icon="fas fa-check-circle"
                            class="mt-n2 ml-n2"
                            @click.stop=""
                            @change="checkAllSelected()"
                          ></v-checkbox-btn>
                        </td>
                        <!-- Unified Column Rendering - matches header order exactly -->
                        <td
                          v-for="column in getVisibleDataColumns()"
                          :key="column.key"
                          :style="getColumnStyle(column.key)"
                          :class="getColumnClass(column.key)"
                        >
                          <!-- First_Name Column (Candidate) -->
                          <section
                            v-if="column.key === 'First_Name'"
                            class="text-body-2"
                            style="max-width: 250px"
                          >
                            <div class="text-primary d-flex align-center">
                              <div
                                :style="{
                                  maxWidth:
                                    item.Duplicate_Count &&
                                    item.Duplicate_Count > 1
                                      ? '220px'
                                      : '250px',
                                }"
                              >
                                <span class="d-flex text-truncate">
                                  {{
                                    item.First_Name ||
                                    item.Middle_Name ||
                                    item.Last_Name
                                      ? [
                                          item.First_Name,
                                          item.Middle_Name,
                                          item.Last_Name,
                                        ]
                                          .filter(Boolean)
                                          .join(" ")
                                      : "-"
                                  }}
                                </span>
                              </div>
                              <v-tooltip
                                text="This candidate has been blacklisted"
                              >
                                <template v-slot:activator="{ props }">
                                  <v-icon
                                    v-if="
                                      item.Blacklisted?.toLowerCase() == 'yes'
                                    "
                                    size="13"
                                    class="ml-2"
                                    style="border-radius: 50%"
                                    v-bind="props"
                                  >
                                    fas fa-user-slash
                                  </v-icon>
                                </template>
                              </v-tooltip>
                              <v-badge
                                v-if="
                                  item.Duplicate_Count &&
                                  item.Duplicate_Count > 1
                                "
                                color="hover"
                                text-color="primary"
                                :content="item.Duplicate_Count"
                                overlap
                                style="border-radius: 50%"
                                class="ml-5 mt-2"
                              ></v-badge>
                            </div>
                            <div>
                              {{
                                item.Mobile_No_Country_Code
                                  ? item.Mobile_No_Country_Code +
                                    " " +
                                    checkNullValue(item.Mobile_No)
                                  : checkNullValue(item.Mobile_No)
                              }}
                              <div
                                v-if="item.Personal_Email"
                                class="text-grey text-truncate"
                              >
                                {{ checkNullValue(item.Personal_Email) }}
                              </div>
                            </div>
                          </section>

                          <!-- Job_Post_Name Column -->
                          <section
                            v-else-if="column.key === 'Job_Post_Name'"
                            class="text-body-2 font-weight-regular text-truncate"
                            style="max-width: 200px"
                          >
                            {{ checkNullValue(item.Job_Post_Name) }}
                          </section>

                          <!-- Candidate_Status Column -->
                          <section
                            v-else-if="column.key === 'Candidate_Status'"
                            class="text-body-2 font-weight-medium text-truncate"
                            :class="getStatusClass(item.Candidate_Status)"
                          >
                            {{ checkNullValue(item.Candidate_Status) }}
                            <div v-if="item?.Archive_Reason" class="text-grey">
                              {{ item.Archive_Reason }}
                            </div>
                          </section>

                          <!-- Source Column -->
                          <section
                            v-else-if="column.key === 'Source'"
                            class="text-body-2 font-weight-regular text-truncate"
                            style="max-width: 200px"
                          >
                            {{ checkNullValue(item.Source) }}
                          </section>

                          <!-- Applicant_Ranking Column -->
                          <section
                            v-else-if="column.key === 'Applicant_Ranking'"
                            style="max-width: 100px"
                          >
                            <v-progress-linear
                              :model-value="item.Applicant_Ranking"
                              height="15"
                              width="100"
                              :color="
                                item.Applicant_Ranking <= 30
                                  ? 'red-accent-1'
                                  : item.Applicant_Ranking <= 60
                                  ? 'amber-lighten-2'
                                  : 'green-lighten-3'
                              "
                              rounded="lg"
                              ><span class="text-body-2 font-weight-regular">{{
                                item.Applicant_Ranking
                              }}</span></v-progress-linear
                            >
                          </section>

                          <!-- Expected_CTC Column -->
                          <section
                            v-else-if="column.key === 'Expected_CTC'"
                            class="text-body-2 font-weight-regular text-truncate"
                            style="max-width: 200px"
                          >
                            {{ item.Expected_CTC ? item.Expected_CTC : 0 }}
                          </section>

                          <!-- First_Interviewer Column -->
                          <section
                            v-else-if="column.key === 'First_Interviewer'"
                            class="text-body-2 font-weight-regular text-truncate"
                            style="max-width: 200px"
                          >
                            {{ checkNullValue(item.First_Interviewer) }}
                          </section>

                          <!-- Resume Column -->
                          <section
                            v-else-if="column.key === 'Resume'"
                            class="text-body-2 font-weight-medium text-truncate"
                            @click.stop="retrieveResumeDetails(item.Resume)"
                            style="cursor: pointer"
                          >
                            <a class="text-decoration-none"> View Resume </a>
                          </section>

                          <!-- Dynamic columns from API -->
                          <section
                            v-else
                            class="text-body-2 font-weight-regular text-truncate"
                            style="max-width: 200px"
                          >
                            {{
                              isDateField(column.key)
                                ? checkNullValue(
                                    formatOnlyDate(item[column.key])
                                  )
                                : checkNullValue(item[column.key])
                            }}
                          </section>
                        </td>

                        <!-- Action Column (always last) -->
                        <td
                          v-if="isActionColumnVisible()"
                          class="text-body-2 text-center"
                          style="width: 150px"
                          @click.stop=""
                        >
                          <ActionMenu
                            v-if="
                              (validateUser(item.Recruiter_Id) ||
                                formAccess.delete) &&
                              !candidateNoActionStatusList.includes(
                                item.Status_Id
                              )
                            "
                            @selected-action="
                              checkCandidateBlacklist($event, item)
                            "
                            :actions="item.options"
                            :access-rights="item.havingAccess"
                            iconColor="grey"
                          ></ActionMenu>
                          <div v-else>-</div>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
      <SelectInterviewType
        v-if="scheduleInterviewOverlay"
        :candidate-details="candidateDetails"
        :candidateId="candidateDetails.Candidate_Id"
        :candidate-email="selectedEmails"
        :candidateName="candidateDetails.First_Name"
        :jobPostId="candidateDetails.Job_Post_Id"
        :statusId="candidateDetails.Status_Id"
        :jobTitle="candidateDetails.Job_Post_Name"
        @close-interview-schedule-window="handleInterviewSuccess($event)"
      ></SelectInterviewType>
      <InterviewSchedules
        v-if="assessmentLinkOverlay"
        :candidateId="candidateList"
        :candidate-details="candidateDetails"
        :candidateName="candidateDetails.First_Name"
        :candidate-email="selectedEmails"
        :jobPostId="candidateDetails.Job_Post_Id"
        :statusId="candidateDetails.Status_Id"
        :jobTitle="candidateDetails.Job_Post_Name"
        :candidateWillSelect="true"
        interviewType="Assessment Link"
        @close-interview-schedule-window="handleInterviewSuccess($event)"
      ></InterviewSchedules>
      <SelectInterviewType
        v-if="bulkScheduleOverlay"
        :isBulk="true"
        :candidate-details="candidateDetails"
        :candidate-email="selectedEmails"
        :candidateId="shortlistCandidateList"
        :candidateName="candidateDetails.First_Name"
        :jobPostId="selectedJobPostId"
        :statusId="13"
        :jobTitle="selectedJobPostName"
        @close-interview-schedule-window="handleInterviewSuccess($event)"
      ></SelectInterviewType>
      <InterviewSchedules
        v-if="bulkAssessmentOverlay"
        :isBulk="true"
        :candidate-details="candidateDetails"
        :candidate-email="selectedEmails"
        :candidateId="shortlistCandidateList"
        :jobPostId="selectedJobPostId"
        :statusId="13"
        :candidateName="candidateDetails.First_Name"
        :jobTitle="selectedJobPostName"
        :candidateWillSelect="true"
        interviewType="Assessment Link"
        @close-interview-schedule-window="handleInterviewSuccess($event)"
      ></InterviewSchedules>

      <!-- <DuplicateCandidates
        v-if="duplicateCandidates"
        :candidateDetails="candidateDetails"
        :candidateId="candidateIdSelected"
        :candidateName="candidateDetails.First_Name"
        :jobPostId="candidateDetails.Job_Post_Id"
        :statusId="candidateDetails.Status_Id"
        :jobTitle="candidateDetails.Job_Post_Name"
        @close-duplicate-candidates-window="duplicateCandidates = false"
        @on-display-change-button="onChangeCandidate()"
      /> -->
      <AppWarningModal
        v-if="openWarningModal"
        :open-modal="openWarningModal"
        confirmation-heading="Are you sure you want to delete this record ?"
        icon-name="fas fa-trash-alt"
        @close-warning-modal="closeWarningModal"
        @accept-modal="deleteJobCandidate()"
      ></AppWarningModal>
      <AppWarningModal
        v-if="openShortlistCandidateModal"
        :open-modal="openShortlistCandidateModal"
        :acceptButtonDisable="shortlistCandidateList.length > 0 ? false : true"
        :confirmation-heading="
          shortlistCandidateList.length > 0
            ? confirmationMessages[0]
            : confirmationMessages[1]
        "
        @close-warning-modal="
          openShortlistCandidateModal = false;
          acceptCandidatePortal = false;
          shortlistCandidateList = [];
        "
        @accept-modal="shortlistCandidates()"
      />
      <AppWarningModal
        v-if="openRemoveBlacklistModal"
        :open-modal="openRemoveBlacklistModal"
        confirmation-heading="Are you sure you want to remove this candidate from blacklist ?"
        icon-name=""
        @close-warning-modal="openRemoveBlacklistModal = false"
        @accept-modal="removeCandidateFromBlacklist()"
      ></AppWarningModal>
      <AppWarningModal
        v-if="openDisablePortalAccessModal"
        :open-modal="openDisablePortalAccessModal"
        confirmation-heading="Are you sure you want to disable portal access for this candidate ?"
        icon-name=""
        @close-warning-modal="openDisablePortalAccessModal = false"
        @accept-modal="updatePortalAccess(selectedCandidateId)"
      ></AppWarningModal>
      <AppWarningModal
        v-if="blacklistCandidateWarning"
        :open-modal="blacklistCandidateWarning"
        confirmation-heading="This candidate has been blacklisted. Are you sure you want to proceed ?"
        icon-name=""
        @close-warning-modal="cancelBlacklistAction()"
        @accept-modal="onActions(this.selectedType, this.candidateDetails)"
      />
      <BlacklistForm
        :view-form="openBlacklistForm"
        :selectedCandidateId="selectedCandidateId"
        @close-form="openBlacklistForm = false"
        @update-form="(openBlacklistForm = false), refetchList()"
      />
      <ShortlistCandidateOverlayForm
        v-if="openShortlistOverlayForm"
        :emailTemplateList="emailTemplateList"
        :candidateId="candidateDetails.Candidate_Id"
        :noCustomTemplate="noCustomTemplate"
        :candidateEmail="candidateEmail"
        :templateData="shortlistTemplateData"
        @close-shortlist-candidate-window="openShortlistOverlayForm = false"
        @refetch-data="refetchList()"
      />
      <EnablePortalAccessOverlayForm
        v-if="openEnablePortalAccessForm"
        :emailTemplateList="emailTemplateList"
        :accept-candidate-portal="
          portalAccessType?.toLowerCase() === 'enable portal access'
            ? 'Yes'
            : 'No'
        "
        :noCustomTemplate="noCustomTemplate"
        :candidateEmail="candidateEmail"
        :templateData="enablePortalAccessTemplateData"
        :candidateId="candidateDetails.Candidate_Id"
        @close-candidate-portal-form="openEnablePortalAccessForm = false"
        @refetch-data="refetchList()"
      />
      <FilePreviewModal
        v-if="openModal"
        :fileName="retrievedFileName"
        folderName="resume"
        :get-cloudfront-url="true"
        :appendUnderScoreInDomain="true"
        @close-preview-modal="openModal = false"
      />
      <UpdateCandidateStatusOverlayForm
        v-if="openCustomEmail"
        ref="customEmail"
        :candidateId="candidateDetails.Candidate_Id"
        :typeOfTemplate="templateType"
        :typeOfSchedule="
          templateType == 'InitiateBackgroundInvestigation' ? '' : 'noncalendar'
        "
        :templateData="templateData"
        :emailRecievers="templateEmail"
        :templateEmail="[candidateDetails.Personal_Email]"
        :selectedStatus="71"
        :selectedStage="'background investigation'"
        @custom-email-sent="onClickSend()"
        @custom-email-cancel="onClickClose()"
      />
      <AppWarningModal
        v-if="tabChangeWarning"
        :open-modal="tabChangeWarning"
        confirmation-heading="You have filled the job candidate form. Are you sure to change tab?"
        icon-name="far fa-times-circle"
        @close-warning-modal="onDeclineTabChange()"
        @accept-modal="onAcceptTabChange()"
      />
    </v-container>
    <AppLoading v-if="isLoading || dynamicColumnsSaving" />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import moment from "moment";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
import FormFilter from "./FormFilter";
const JobCandidatesDetails = defineAsyncComponent(() =>
  import("./job-candidates-details/JobCandidatesDetails.vue")
);
const EnablePortalAccessOverlayForm = defineAsyncComponent(() =>
  import("./EnablePortalAccessOverlayForm.vue")
);
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import SelectInterviewType from "./SelectInterviewType.vue";
import JobCandidateGlobalSearch from "@/views/recruitment/job-candidates/global-search-component/JobCandidateGlobalSearch.vue";
import { checkNullValue } from "@/helper.js";
import FileExportMixin from "@/mixins/FileExportMixin";
import InterviewSchedules from "./InterviewSchedules.vue";
import UpdateCandidateStatusOverlayForm from "../job-candidates/job-candidates-details/UpdateCandidateStatusOverlayForm.vue";
const ShortlistCandidateOverlayForm = defineAsyncComponent(() =>
  import("./ShortlistCandidateOverlayForm.vue")
);
// Queries
import {
  LIST_JOB_CANDIDATES,
  LIST_JOB_POSTS,
  GET_STATUS_LIST,
  CANDIDATES_DROP_DOWN,
  DELETE_CANDIDATE_JOB,
  UPDATE_CANDIDATE_STATUS,
  REMOVE_CANDIDATE_FROM_BLACKLIST,
  GET_JOB_HEADER,
  UPDATE_CANDIDATE_PORTAL_ACCESS,
  GET_DATA_TABLE_DEFAULT_HEADERS,
  ADD_UPDATE_EMPLOYEE_CUSTOM_TABLE_HEADER,
} from "@/graphql/recruitment/recruitmentQueries.js";
import { LIST_CUSTOM_EMAIL_TEMPLATES } from "@/graphql/settings/email-template/emailTemplateQueries.js";
const BlacklistForm = defineAsyncComponent(() => import("./BlacklistForm.vue"));
import Config from "@/config.js";

export default {
  name: "DuplicateCandidates",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    FormFilter,
    JobCandidatesDetails,
    ActionMenu,
    SelectInterviewType,
    InterviewSchedules,
    FilePreviewModal,
    CustomSelect,
    UpdateCandidateStatusOverlayForm,
    EnablePortalAccessOverlayForm,
    BlacklistForm,
    ShortlistCandidateOverlayForm,
    JobCandidateGlobalSearch,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // list
    listLoading: false,
    dropDownLoading: false,
    recruitmentSettingsLoading: false,
    recruitmentSettings: {},
    isFilterApplied: false,
    itemList: [],
    originalList: [],
    currentSortedItems: [], // Track the current sorted order from v-data-table
    backupFilteredList: [],
    jobTitleFilteredList: [],
    openWarningModal: false,
    jobPostList: [],
    jobPostLoding: false,
    selectedDeleteItem: null,
    dropDownDetails: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    openMoreMenu: false,
    jobPostMenu: false,
    selectedJobPostName: "",
    selectedJobPostId: null,
    retrievedFileName: "",
    openModal: false,
    // add/update
    isLoading: false,
    // view
    selectedItem: null,
    showJobCandidateDetails: false,
    scheduleInterviewOverlay: false,
    assessmentLinkOverlay: false,
    candidateDetails: {},
    selectAllBox: false,
    selectedCandidateRecords: [],
    candidateList: [],
    bulkScheduleOverlay: false,
    bulkAssessmentOverlay: false,
    stageList: [],
    stageLoader: false,
    selectedStage: 1,
    enableInterview: ["Screening", "Interview"],
    // rejected, hired, not hired, offer accepted, offer declined, onboarding, onboarded, offer rolled out
    candidateNoActionStatusList: [3, 20, 21, 22, 23, 24, 25],
    openShortlistCandidateModal: false,
    shortlistCandidateList: [],
    candidateChangeCount: 0,
    isRecruiter: "",
    flowList: [],
    candidateStatusList: [],
    duplicateCandidates: false,
    openCustomEmail: false,
    templateType: "",
    templateEmail: [],
    templateData: {},
    emailRecievers: [],
    selectedCandidateId: null,
    parentTabName: "Duplicate Candidates",
    openBlacklistForm: false,
    openRemoveBlacklistModal: false,
    blacklistCandidateWarning: false,
    selectedType: "",
    // Job Canididate Portal Access
    portalAccessEnabeld: false,
    bulkAcceptCandidatePortal: false,
    currentTabItem: "",
    tabName: "",
    tabChangeWarning: false,
    emailTemplateList: [],
    isEmailTemplateListLoading: false,
    openShortlistOverlayForm: false,
    openDisablePortalAccessModal: false,
    openEnablePortalAccessForm: false,
    portalAccessType: "",
    candidateEmail: "",
    enablePortalAccessTemplateData: {},
    totalCandidateCount: 0,
    // Dynamic Columns functionality
    openDynamicColumnsMenu: false,
    dynamicColumnsSaving: false,
    tempAvailableColumns: [], // Temporary state for working changes (preview)
    savedAvailableColumns: [], // Saved state from API (persistent)
    dynamicColumnsRetriveLoading: false, // Track loading state for dynamic columns API
    availableColumns: [], // Current displayed columns (used for rendering)
  }),
  computed: {
    enableSkeletonLoading() {
      return (
        this.listLoading ||
        this.jobPostLoding ||
        this.isEmailTemplateListLoading ||
        this.dynamicColumnsRetriveLoading ||
        this.recruitmentSettingsLoading
      );
    },
    mainTabs() {
      let tab = [];
      const addTalentPool = this.talentFormAccess; // Check for talentFormAccess

      if (
        this.formAccess &&
        this.formAccess.view &&
        this.onboardingFormAccess
      ) {
        tab.push("Job Candidates", "Upcoming Interviews", "Pending Feedback");
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Archived");
        tab.push("Duplicate Candidates", "Onboarding");
      } else if (
        this.formAccess &&
        this.formAccess.view &&
        !this.onboardingFormAccess
      ) {
        tab.push("Job Candidates", "Upcoming Interviews", "Pending Feedback");
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Archived");
        tab.push("Duplicate Candidates");
      } else if (this.onboardingFormAccess) {
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Duplicate Candidates", "Onboarding");
      } else {
        if (addTalentPool) {
          tab.push("Talent Pool");
        }
        tab.push("Duplicate Candidates");
      }

      return tab;
    },

    talentFormAccess() {
      let formAccess = this.accessRights("297");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        formAccess.accessRights["archive"] = formAccess.accessRights["update"];
        formAccess.accessRights["move candidate to job"] =
          formAccess.accessRights["update"];
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    selectedEmails() {
      if (this.selectedItems && this.selectedItems.length > 0) {
        return this.selectedItems.map((el) => el.Personal_Email);
      }
      return [this.candidateDetails.Personal_Email];
    },
    selectedItems() {
      let selected = this.itemList.filter((el) => {
        if (
          el.isSelected === true &&
          this.enableInterview.includes(el.Hiring_Stage)
        ) {
          return el.Candidate_Id;
        }
      });
      return selected && selected.length > 0 ? selected : [];
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    loginEmployeeEmail() {
      return this.$store.state.orgDetails.userDetails;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        return date ? moment(date).format(orgDateFormat + " HH:mm:ss") : "-";
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("16");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    onboardingFormAccess() {
      let formAccess = this.accessRights("178");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return true;
      } else {
        return false;
      }
    },
    formAccessForSendAssessment() {
      let formAccess = this.accessRights("275");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForScheduleInterview() {
      let formAccess = this.accessRights("272");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      // Filter available columns to show only selected ones and convert to v-data-table format
      // Add additional validation to ensure only visible columns are included
      const filteredHeaders = this.availableColumns
        .filter((column) => {
          // Ensure column exists, has a key, and is selected (visible)
          return column && column.key && column.selected === true;
        })
        .map((column) => {
          const header = {
            title: column.label || column.key, // Fallback to key if label is missing
            key: column.key,
          };

          // Add specific properties for certain columns
          if (column.key === "First_Name") {
            header.align = "start";
            header.fixed = true;
          }
          if (column.key === "Action") {
            header.align = "center";
            header.sortable = false;
          }

          return header;
        });

      return filteredHeaders;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.isFilterApplied) {
        msgText =
          this.jobTitleFilteredList.length === 0
            ? "There are no candidates for the selected stage and job title"
            : "There are no candidates for the selected filters/searches.";
      }
      return msgText;
    },
    moreActions() {
      if (this.selectedJobPostId != 0) {
        return [
          {
            key: "Send Assessment",
            message:
              "Only candidates in the screening or interview stages will be sent with assessment communication",
          },
          {
            key: "Schedule Interview",
            message:
              "Only candidates in the screening or interview stages will be scheduled with the interview",
          },
          {
            key: "Shortlist",
            message: "Only candidates in the sourced stage will be shortlisted",
          },
          {
            key: "Export",
          },
        ];
      } else {
        return [
          {
            key: "Shortlist",
            message: "Only candidates in the sourced stage will be shortlisted",
          },
          {
            key: "Export",
          },
        ];
      }
    },
    confirmationMessages() {
      return [
        `There are '${this.shortlistCandidateList.length}' candidates in sourced stage who will be shortlist. Are you sure to shortlist?`,
        "There are no candidates in sourced stage for shortlisting. Please refine the filter to select the sourced stage candidates.",
      ];
    },
    userIsRecruiter() {
      return this.$store.state.isRecruiter;
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    formatOnlyDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    // Computed property to get non-fixed columns for the Dynamic Columns menu
    editableColumns() {
      return this.tempAvailableColumns.filter((column) => !column.fixed);
    },
    // Computed properties for Select All functionality
    selectAllColumns: {
      get() {
        const editableColumns = this.editableColumns;
        if (editableColumns.length === 0) return false;
        return editableColumns.every((column) => column.selected);
      },
      set() {
        // This setter is handled by the toggleSelectAllColumns method
        // to provide better control over the selection logic
      },
    },
    // Computed property to get dynamic columns that are not in the static header mapping
    dynamicColumns() {
      const staticKeys = [
        "First_Name",
        "Job_Post_Name",
        "Candidate_Status",
        "Source",
        "Applicant_Ranking",
        "Expected_CTC",
        "First_Interviewer",
        "Resume",
        "Action",
      ];
      return this.availableColumns.filter(
        (column) => column.selected && !staticKeys.includes(column.key)
      );
    },
  },

  mounted() {
    this.retrieveJobPosts();
    this.retrieveDropDownDetails();
    this.getStageList();
    this.retrieveRecruitmentSetting();
    this.currentTabItem =
      "tab-" + this.mainTabs.indexOf("Duplicate Candidates");
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
      if (val && val.length) {
        this.isFilterApplied = true;
      }
    },
    selectedCandidateRecords(selRecords) {
      if (this.selectAllBox) {
        // Iterate through itemList
        for (const item of this.itemList) {
          // Check if employeeId is present in selRecords
          if (selRecords.includes(item.Candidate_Id)) {
            // Set to true if there's a match
            item.isSelected = true;
          }
        }
      } else {
        // Iterate through itemList
        for (const item of this.itemList) {
          item.isSelected = false;
        }
      }
    },
    // Watch for changes in temporary columns to provide preview functionality
    tempAvailableColumns: {
      handler(newTempColumns) {
        // Only update displayed columns if the menu is open (preview mode)
        if (this.openDynamicColumnsMenu && newTempColumns.length > 0) {
          this.availableColumns = JSON.parse(JSON.stringify(newTempColumns));
        }
      },
      deep: true,
    },
  },

  methods: {
    // Helper methods for column visibility
    getVisibleDataColumns() {
      return this.availableColumns.filter((col) => {
        // Ensure column exists, has a key, is selected, and is not the Action column
        return (
          col &&
          col.key &&
          col.selected === true &&
          col.key?.toLowerCase() !== "action"
        );
      });
    },

    isActionColumnVisible() {
      const actionColumn = this.availableColumns.find(
        (col) => col && col.key === "Action"
      );
      return actionColumn && actionColumn.selected === true;
    },
    onChangeCandidate(item) {
      this.selectedItem = item;
      this.candidateChangeCount += 1;
    },
    updateCurrentSortedItems(sortedItems) {
      // Store the current sorted order from v-data-table
      this.currentSortedItems = sortedItems?.map((item) => item?.raw) || [];
    },
    handleInterviewSuccess(isSuccess) {
      this.isLoading = false;
      this.scheduleInterviewOverlay = false;
      this.assessmentLinkOverlay = false;
      this.bulkAssessmentOverlay = false;
      this.bulkScheduleOverlay = false;
      this.selectAllBox = false;
      if (isSuccess) {
        this.refetchList();
      }
    },
    onAcceptTabChange() {
      this.tabChangeWarning = false;
      this.$refs.jobCandidateDetails.$refs.editJobCandidateDetails.isFormDirty = false;
      this.onTabChange(this.tabName);
    },
    onDeclineTabChange() {
      this.tabChangeWarning = false;
      this.currentTabItem =
        "tab-" + this.mainTabs.indexOf("Duplicate Candidates");
    },
    //function handling tab change
    onTabChange(tabName) {
      this.tabName = tabName;
      this.currentTabItem = "tab-" + this.mainTabs.indexOf(tabName);
      if (
        this.$refs.jobCandidateDetails?.$refs?.editJobCandidateDetails
          ?.isFormDirty
      ) {
        this.tabChangeWarning = true;
      } else {
        if (tabName === "Onboarding") {
          this.$router.push("/recruitment/job-candidates/onboarding");
        } else if (tabName === "Upcoming Interviews") {
          this.$router.push("/recruitment/job-candidates/upcoming-interviews");
        } else if (tabName === "Pending Feedback") {
          this.$router.push("/recruitment/job-candidates/pending-feedback");
        } else if (tabName === "Job Candidates") {
          this.$router.push("/recruitment/job-candidates");
        } else if (tabName === "Talent Pool") {
          this.$router.push("/recruitment/job-candidates/talent-pool");
        } else if (tabName === "Archived") {
          this.$router.push("/recruitment/job-candidates/archived-candidates");
        }
      }
    },
    onSelectJobPost(jobPost) {
      this.selectAllBox = false;
      this.jobPostMenu = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.selectedJobPostId = jobPost;
      let selectedJobPost = this.jobPostList.filter(
        (el) => el.Job_Post_Id == jobPost
      );
      this.selectedJobPostName = selectedJobPost[0].Job_Post_Name;
      this.fetchList();
    },
    checkAllSelected() {
      let selectedItems = this.itemList.filter((el) => el.isSelected);
      this.selectAllBox = selectedItems.length === this.itemList.length;
    },
    sendBulkInvites() {
      let selected = this.shortlistCandidateList;
      if (selected && selected.length > 0) {
        for (const ele of selected) {
          if (!this.candidateList.includes(ele.Candidate_Id)) {
            this.candidateList.push(ele.Candidate_Id);
          }
        }
        return true;
      } else {
        let snackbarData = {
          isOpen: true,
          message: "Please select at least one record to proceed",
          type: "warning",
        };
        this.showAlert(snackbarData);
        return false;
      }
    },
    validateUser(recruiters, item = null) {
      let isFormAdmin = this.formAccess.admin === "admin";
      if (isFormAdmin) {
        return true;
      } else if (
        item &&
        item.Job_Post_Creator == this.loginEmployeeId &&
        this.isRecruiter.toLowerCase() === "yes"
      ) {
        return true;
      } else {
        let employeeId = this.loginEmployeeId;
        if (recruiters && recruiters.includes(employeeId)) {
          return true;
        }
        return false;
      }
    },
    checkNullValue,
    getStatusClass(status) {
      if (status === "Shortlisted") {
        return "text-green";
      } else if (status === "Scheduled For Interview") {
        return "text-amber";
      } else if (status === "Rejected") {
        return "text-red";
      } else {
        return "text-blue";
      }
    },
    getStatusColor(status) {
      if (status === "Shortlisted") {
        return "green";
      } else if (status === "Scheduled For Interview") {
        return "amber";
      } else if (status === "Rejected") {
        return "red";
      } else if (status === "Applied") {
        return "blue";
      } else {
        return "grey";
      }
    },
    handleCandidateBlacklistCheck(eventData) {
      // Handle the event emitted from JobCandidateTopCard via JobCandidatesDetails
      const { type, item } = eventData;
      this.checkCandidateBlacklist(type, item);
    },
    checkCandidateBlacklist(type, item) {
      this.candidateDetails = item;
      this.selectedType = type;
      if (
        item &&
        item.Blacklisted?.toLowerCase() == "yes" &&
        type.toLowerCase() !== "remove from blacklist"
      ) {
        this.blacklistCandidateWarning = true;
      } else {
        this.onActions(type, item);
      }
    },
    onActions(type, item) {
      this.assessmentLinkOverlay = false;
      this.scheduleInterviewOverlay = false;
      this.blacklistCandidateWarning = false;
      this.candidateDetails = item;
      if (type && type.toLowerCase() === "delete") {
        this.onDelete(item);
      }
      if (type && type.toLowerCase() === "schedule interview") {
        this.scheduleInterviewOverlay = true;
        this.candidateList.push(item.Candidate_Id);
      }
      if (type && type.toLowerCase() === "send assessment") {
        this.assessmentLinkOverlay = true;
        this.candidateList.push(item.Candidate_Id);
      }
      if (type && type.toLowerCase() === "shortlist") {
        this.checkEmailTemplate(item.Candidate_Id, "shortlist");
      }
      if (type && type.toLowerCase() === "initiate background investigation") {
        this.selectedCandidateId = item.Candidate_Id;
        this.templateType = "intiateBackgroundInvestigation";
        this.emailRecievers = [item.Personal_Email];
        this.templateEmail = [this.loginEmployeeEmail.employeeEmail];
        let templateData = {
          Candidate_Name:
            item.Last_Name + ", " + item.First_Name + " " + item.Middle_Name,
          Recruiter_Name: this.loginEmployeeEmail.employeeFullName,
        };
        this.templateType = "InitiateBackgroundInvestigation";
        this.templateData = templateData;
        this.openCustomEmail = true;
      }
      if (type && type.toLowerCase() === "blacklist candidate") {
        this.selectedCandidateId = item.Candidate_Id;
        this.openBlacklistForm = true;
      }
      if (type && type.toLowerCase() === "remove from blacklist") {
        this.selectedCandidateId = item.Candidate_Id;
        this.openRemoveBlacklistModal = true;
      }
      if (
        type?.toLowerCase() === "enable portal access" ||
        type?.toLowerCase() === "disable portal access"
      ) {
        this.checkEmailTemplate(item.Candidate_Id, type);
      }
    },
    onClickClose() {
      this.openCustomEmail = false;
    },
    onClickSend() {
      this.openCustomEmail = false;
      this.updateCanidateStatus(71);
    },
    updateCanidateStatus(status) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_CANDIDATE_STATUS,
          client: "apolloClientAM",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: [vm.selectedCandidateId],
            candidateStatus: status,
          },
        })
        .then(() => {
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Candidate status updated successfully",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.refetchList();
        })
        .catch((err) => {
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "updating",
            form: "candidate status",
            isListError: false,
          });
          vm.isLoading = false;
        });
    },
    onDelete(item) {
      this.selectedDeleteItem = item;
      this.openWarningModal = true;
    },
    closeWarningModal() {
      this.openWarningModal = false;
    },
    retrieveDropDownDetails() {
      let vm = this;
      vm.dropDownLoading = true;
      vm.$apollo
        .query({
          query: CANDIDATES_DROP_DOWN,
          client: "apolloClientA",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getDropDownBoxDetails &&
            !response.data.getDropDownBoxDetails.errorCode.length
          )
            vm.dropDownDetails = response.data.getDropDownBoxDetails;
          vm.dropDownLoading = false;
        })
        .catch(() => {
          vm.dropDownLoading = false;
        });
    },
    retrieveJobPosts(jobPostId = null) {
      let vm = this;
      vm.jobPostLoding = true;
      vm.$apollo
        .query({
          query: LIST_JOB_POSTS,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            searchString: "",
            designation: null,
            functionalArea: null,
            jobType: null,
            closingDate: "",
            status: null,
            location: [],
            isDropDownCall: 1,
            skills: [],
            qualification: [],
            action: "duplicate",
            formId: 16,
            stageId: vm.selectedStage,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listJobPost &&
            !response.data.listJobPost.errorCode.length
          ) {
            vm.jobPostList = response.data.listJobPost.JobpostDetails;
            let allJobPost = [
              {
                Job_Post_Name: "All",
                Job_Post_Id: 0,
              },
            ];
            vm.jobPostList = allJobPost.concat(vm.jobPostList);
            let total = vm.jobPostList.reduce((acc, jobPost) => {
              return acc + (jobPost.Candidate_Count || 0);
            }, 0);
            vm.jobPostList = vm.jobPostList.map((jobPost) => {
              const Candidate_Count =
                jobPost.Job_Post_Id === 0 ? total : jobPost.Candidate_Count;

              return {
                ...jobPost,
                Job_Post_Name_With_Count: `${jobPost.Job_Post_Name} (${Candidate_Count})`,
              };
            }); // Check for jobPostId in route query parameters (handle both camelCase and lowercase)
            let routeJobPostId =
              vm.$route?.query?.jobPostId || vm.$route?.query?.jobpostId;

            if (jobPostId && !routeJobPostId) {
              routeJobPostId = jobPostId;
            }
            if (routeJobPostId) {
              const jobPostIdFromRoute = parseInt(routeJobPostId);
              // Check if the jobPostId from route exists in the job post list
              const jobPostExists = vm.jobPostList.find(
                (jobPost) => jobPost.Job_Post_Id === jobPostIdFromRoute
              );
              if (jobPostExists) {
                vm.selectedJobPostId = jobPostIdFromRoute;
              } else {
                // Fallback to default selection if route jobPostId doesn't exist
                const jobPostWithApplicants = vm.jobPostList.filter(
                  (jobPost) => {
                    return (
                      jobPost.Job_Post_Id !== 0 && jobPost.Number_Of_Applicants
                    );
                  }
                );
                if (jobPostWithApplicants.length > 0) {
                  vm.selectedJobPostId = jobPostWithApplicants[0].Job_Post_Id;
                } else {
                  vm.selectedJobPostId = 0;
                }
              }
            } else {
              const jobPostWithApplicants = vm.jobPostList.filter((jobPost) => {
                return (
                  jobPost.Job_Post_Id !== 0 && jobPost.Number_Of_Applicants
                );
              });
              if (jobPostWithApplicants.length > 0) {
                vm.selectedJobPostId = jobPostWithApplicants[0].Job_Post_Id;
              } else {
                vm.selectedJobPostId = 0;
              }
            }
          }
          vm.handleSuccessErrorOfJobPostListApi();
        })
        .catch(() => {
          vm.handleSuccessErrorOfJobPostListApi();
        });
    },

    handleSuccessErrorOfJobPostListApi() {
      this.jobPostLoding = false;
      // If a candidateId exists in the route params, open the form
      let decodedCandidateId = null;
      if (this.$route?.query?.candidateId) {
        decodedCandidateId = parseInt(this.$route.query.candidateId);
      }
      this.fetchList(decodedCandidateId);
    },

    applyFilterFromFilterComponent(filter) {
      let filterObj = {
        jobPosts: this.selectedJobPostId,
        status: filter.status,
        preferredLocation: filter.preferredLocation,
        currentLocation: filter.currentLocation,
        source: filter.source,
        skills: filter.skills,
        gender: filter.gender,
        experience: filter.experience,
        currentCTC: filter.currentCTC,
        expectedCTC: filter.expectedCTC,
        applicantRanking: filter.applicantRanking,
        daysToJoin: filter.daysToJoin,
      };
      this.applyFilter(filterObj);
    },

    applyFilter(filter) {
      let filteredList = this.originalList;
      if (filter.jobPosts) {
        filteredList = filteredList.filter((item) => {
          return filter.jobPosts == item.Job_Post_Id;
        });
        this.jobTitleFilteredList = filteredList;
      } else {
        this.jobTitleFilteredList = filteredList;
      }
      if (filter.status && filter.status.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.status.includes(item.Status_Id);
        });
      }
      if (filter.preferredLocation && filter.preferredLocation.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.preferredLocation.includes(item.Preferred_Location);
        });
      }
      if (filter.currentLocation && filter.currentLocation.length > 0) {
        filteredList = filteredList.filter((item) => {
          return item.Current_Location
            ? item.Current_Location.toString()
                .toLowerCase()
                .includes(filter.currentLocation)
            : true;
        });
      }
      if (filter.skills && filter.skills != 0) {
        filteredList = filteredList.filter((item) => {
          if (item.Skill_Set) {
            let skillsList = item.Skill_Set || [];
            return skillsList.includes(filter.skills);
          }
        });
      }
      if (filter.source && filter.source.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.source.includes(item.Source);
        });
      }
      if (filter.gender && filter.gender.length > 0) {
        filteredList = filteredList.filter((item) => {
          return filter.gender.includes(item.Gender);
        });
      }
      if (filter.experienceInYears && filter.experienceInYears.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Total_Experience_In_Years >= filter.experienceInYears[0] &&
            item.Total_Experience_In_Years <= filter.experienceInYears[1]
          );
        });
      }
      if (filter.experienceInMonths && filter.experienceInMonths.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Total_Experience_In_Months >= filter.experienceInMonths[0] &&
            item.Total_Experience_In_Months <= filter.experienceInMonths[1]
          );
        });
      }
      if (filter.currentCTC && filter.currentCTC.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Current_CTC >= filter.currentCTC[0] &&
            item.Current_CTC <= filter.currentCTC[1]
          );
        });
      }
      if (filter.expectedCTC && filter.expectedCTC.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Expected_CTC >= filter.expectedCTC[0] &&
            item.Expected_CTC <= filter.expectedCTC[1]
          );
        });
      }
      if (filter.applicantRanking && filter.applicantRanking.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Applicant_Ranking >= filter.applicantRanking[0] &&
            item.Applicant_Ranking <= filter.applicantRanking[1]
          );
        });
      }
      if (filter.daysToJoin && filter.daysToJoin.length > 0) {
        filteredList = filteredList.filter((item) => {
          return (
            item.Notice_Period >= filter.daysToJoin[0] &&
            item.Notice_Period <= filter.daysToJoin[1]
          );
        });
      }
      this.isFilterApplied = true;
      this.backupFilteredList = filteredList;
      this.itemList = filteredList;
      this.onApplySearch(this.searchValue);
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },

    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.$store.state.empSearchValue = "";
      this.itemList = this.originalList;
      this.backupFilteredList = this.originalList;
      let filterObj = {
        jobPosts: this.selectedJobPostId,
      };
      this.applyFilter(filterObj);
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.showJobCandidateDetails = true;
    },

    onAddJobCandidate() {
      this.selectedItem = null;
      this.showJobCandidateDetails = true;
    },

    closeAllForms(eventData) {
      // Handle both old format (number) and new format (object)
      let shouldRefetch = false;

      if (typeof eventData === "number") {
        // Old format: just refetchCount
        shouldRefetch = eventData > 0;
      } else if (eventData && typeof eventData === "object") {
        // New format: object with action data
        const { refetchCount, candidate } = eventData;
        if (refetchCount > 0) {
          const selectedStageId = this.stageList?.find(
            (stage) => stage.value === candidate?.Hiring_Stage
          )?.id;
          if (selectedStageId) this.selectedStage = selectedStageId;
          shouldRefetch = refetchCount > 0;
        }
      }

      if (shouldRefetch) {
        this.retrieveJobPosts(eventData?.candidate?.Job_Post_Id);
        this.refetchList();
      }
      this.showJobCandidateDetails = false;
      this.selectedItem = null;
      this.$router.replace({ path: this.$route.path });
    },

    retrieveRecruitmentSetting() {
      let vm = this;
      vm.recruitmentSettingsLoading = true;
      vm.$apollo
        .query({
          query: GET_JOB_HEADER,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            formId: 16,
          },
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.recruitmentSetting &&
            res.data.recruitmentSetting.settingResult &&
            res.data.recruitmentSetting.settingResult[0]
          ) {
            vm.recruitmentSettings =
              res.data.recruitmentSetting.settingResult[0];
            this.portalAccessEnabeld =
              res.data.recruitmentSetting.settingResult[0]?.Candidate_Portal_Login_Access?.toLowerCase() ===
              "yes";
            if (
              this.recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
              "yes"
            )
              this.retrieveDynamicColumns();
            else this.fallbackToStaticColumns();
          } else {
            vm.handleRecruitmentErrors(
              res.data.recruitmentSetting?.errorCode || ""
            );
          }
          vm.recruitmentSettingsLoading = false;
        })
        .catch((err) => {
          vm.recruitmentSettingsLoading = false;
          vm.handleRecruitmentErrors(err);
        });
    },
    handleRecruitmentErrors(error) {
      this.fallbackToStaticColumns();
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "retrieving",
        form: "candidate job details",
        isListError: false,
      });
    },
    // Dynamic Columns Methods
    async retrieveDynamicColumns() {
      try {
        this.dynamicColumnsRetriveLoading = true;
        const response = await this.$apollo.query({
          query: GET_DATA_TABLE_DEFAULT_HEADERS,
          variables: {
            formId: 16,
            screenType: "DuplicateCandidates",
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        });

        if (
          response.data &&
          response.data.getDataTableDefaultHeaders &&
          !response.data.getDataTableDefaultHeaders.errorCode &&
          response.data.getDataTableDefaultHeaders.dataTableDefaultHeaders
        ) {
          // Successfully retrieved API data
          const apiColumns =
            response.data.getDataTableDefaultHeaders.dataTableDefaultHeaders;

          // Start with static columns to maintain proper order
          const staticColumns = this.getStaticColumns();
          const mergedColumns = [];

          // Process static columns first to maintain order
          staticColumns.forEach((staticColumn) => {
            const apiColumn = apiColumns.find(
              (col) => col.key === staticColumn.key
            );
            if (apiColumn) {
              // Update visibility from API but keep static column structure
              mergedColumns.push({
                ...staticColumn,
                selected: apiColumn.visible?.toLowerCase() === "yes",
              });
            } else {
              // Keep static column as is
              mergedColumns.push(staticColumn);
            }
          });

          // Add any additional API columns that don't exist in static columns
          // Insert them before the Action column to maintain Action as last
          const actionIndex = mergedColumns.findIndex(
            (col) => col.key?.toLowerCase() === "action"
          );
          apiColumns.forEach((apiColumn) => {
            const existingColumn = mergedColumns.find(
              (col) => col.key === apiColumn.key
            );
            if (!existingColumn) {
              const newColumn = {
                key: apiColumn.key,
                label: apiColumn.label,
                selected: apiColumn.visible?.toLowerCase() === "yes",
              };
              // Insert before Action column
              if (actionIndex !== -1) {
                mergedColumns.splice(actionIndex, 0, newColumn);
              } else {
                mergedColumns.push(newColumn);
              }
            }
          });

          // Set both current displayed columns and saved state
          this.availableColumns = mergedColumns;
          this.savedAvailableColumns = JSON.parse(
            JSON.stringify(mergedColumns)
          );
        } else {
          // API returned error or no data, fall back to static columns
          this.fallbackToStaticColumns();
        }
      } catch {
        // Fall back to static columns on API failure
        this.fallbackToStaticColumns();
      } finally {
        this.dynamicColumnsRetriveLoading = false;
      }
    },
    fetchList(candidateId = null, offset = 0) {
      let vm = this;
      vm.listLoading = true;
      if (offset == 0) {
        vm.originalList = [];
        vm.itemList = [];
      }
      vm.$apollo
        .query({
          query: LIST_JOB_CANDIDATES,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            searchString: "",
            action: "Duplicate",
            isDropDownCall: 0,
            jobPostId: candidateId
              ? null
              : vm.selectedJobPostId === 0
              ? []
              : [vm.selectedJobPostId],
            preferredLocation: [],
            status: [],
            stageId: candidateId ? null : vm.selectedStage,
            candidateId: candidateId,
            offset: offset,
            limit: 1500,
          },
        })
        .then((response) => {
          const { listJobCandidates } = response.data || {};
          if (listJobCandidates && !listJobCandidates.errorCode.length) {
            let jCandidates = this.processCandidates(
              listJobCandidates.jobCandidates
            );
            vm.isRecruiter = listJobCandidates.isRecruiter;
            vm.totalCandidateCount = listJobCandidates.candidateTotalCount;

            jCandidates = jCandidates.map((item) => ({
              ...item,
              Applicant_Ranking: item.Applicant_Ranking
                ? (parseFloat(item.Applicant_Ranking) * 100).toFixed(2)
                : 0,
            }));

            vm.originalList = [...vm.originalList, ...jCandidates];
            vm.itemList = vm.originalList;

            // If a candidateId exists in the route params, open the form
            if (candidateId) {
              const candidateItem = vm.itemList.find(
                (data) => data.Candidate_Id === candidateId
              );

              if (candidateItem) {
                vm.openViewForm(candidateItem);
              }
            }
            if (vm.originalList.length >= vm.totalCandidateCount) {
              vm.listLoading = false;

              // Reset filter model values
              if (vm.$refs.formFilterRef) {
                vm.$refs.formFilterRef.resetAllModelValues();
              }
            } else {
              vm.fetchList(null, vm.originalList.length);
            }
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "candidate job details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList(isError = false) {
      this.isErrorInList = false;
      this.selectAllBox = false;
      this.errorContent = "";
      this.closeAllForms();
      if (isError) {
        this.resetFilter();
      }
      this.fetchList();
    },

    deleteJobCandidate() {
      let vm = this;
      vm.closeWarningModal();
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_CANDIDATE_JOB,
            variables: {
              candidateId: vm.selectedDeleteItem.Candidate_Id,
              employeeId: vm.$store.state.orgDetails.employeeId,
            },
            client: "apolloClientA",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate details deleted successfully",
            };
            vm.showAlert(snackbarData);
            vm.refetchList();
          })
          .catch((deleteErr) => {
            vm.handleDeleteError(deleteErr);
          });
      } catch {
        vm.handleDeleteError();
      }
    },

    handleDeleteError(err = "") {
      this.isLoading = false;
      this.refetchList();
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "candidate details",
        isListError: false,
      });
    },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType.key === "Send Assessment") {
        this.itemList.map((ele) => {
          if (
            this.enableInterview.includes(ele.Hiring_Stage) &&
            ele.isSelected
          ) {
            this.shortlistCandidateList.push(ele.Candidate_Id);
          }
        });
        if (this.shortlistCandidateList.length > 0) {
          this.bulkAssessmentOverlay = true;
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message:
              "Unable to initiate the bulk assessment process as there are no candidates in 'Screening' or 'Interview' stage was selected",
          };
          this.showAlert(snackbarData);
        }
      }
      if (actionType.key === "Schedule Interview") {
        this.itemList.map((ele) => {
          if (
            this.enableInterview.includes(ele.Hiring_Stage) &&
            ele.isSelected
          ) {
            this.shortlistCandidateList.push(ele.Candidate_Id);
          }
        });
        if (this.shortlistCandidateList.length > 0) {
          this.bulkScheduleOverlay = true;
        } else {
          let snackbarData = {
            isOpen: true,
            type: "warning",
            message:
              "Unable to schedule the bulk interview process as there are no candidates in 'Screening' or 'Interview' stage was selected",
          };
          this.showAlert(snackbarData);
        }
      }
      if (actionType.key === "Shortlist") {
        this.itemList.map((ele) => {
          if (ele.Hiring_Stage === "Sourced" && ele.isSelected) {
            this.shortlistCandidateList.push(ele.Candidate_Id);
          }
        });
        this.openShortlistCandidateModal = true;
      }
      if (actionType.key === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: "First Name",
          key: "First_Name",
        },
        {
          header: "Last Name",
          key: "Last_Name",
        },
        {
          header: "Mobile Number",
          key: "Mobile_No",
        },
        {
          header: "Personal Email",
          key: "Personal_Email",
        },
        {
          header: "Gender",
          key: "Gender",
        },
        {
          header: "Skill Set",
          key: "Skill_Set",
        },
        {
          header: "Source of Application",
          key: "Source",
        },
        {
          header: "Job Title",
          key: "Job_Post_Name",
        },
        {
          header: "Preferred Location",
          key: "Preferred_Location",
        },
        {
          header: "Expected Basic Salary",
          key: "Expected_CTC",
        },
        {
          header: "First Interviewer",
          key: "First_Interviewer",
        },
        {
          header: "Current Basic Salary",
          key: "Current_CTC",
        },
        {
          header: "Total Experience(In Months)",
          key: "Total_Experience_In_Months",
        },
        {
          header: "Total Experience(In Years)",
          key: "Total_Experience_In_Years",
        },
        {
          header: "Availability to Join(In Days)",
          key: "Notice_Period",
        },
        {
          header: "Job Match Score",
          key: "applicantRanking",
        },
        {
          header: "Last Stage",
          key: "Last_Stage",
        },
        {
          header: "Status",
          key: "Candidate_Status",
        },
      ];
      let jobCandidates = this.itemList;
      jobCandidates = jobCandidates.map((item) => ({
        ...item,
        applicantRanking: item.Applicant_Ranking
          ? parseInt(item.Applicant_Ranking) * 100
          : 0,
        Skill_Set: item?.Skill_Set?.join(", ") || "",
      }));
      let exportOptions = {
        fileExportData: jobCandidates,
        fileName: "Duplicate Candidates",
        sheetName: "Duplicate Candidates",
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    shortlistCandidates(candidateId = null) {
      this.isLoading = true;
      let vm = this;
      vm.$apollo
        .mutate({
          mutation: UPDATE_CANDIDATE_STATUS,
          variables: {
            candidateId: candidateId
              ? candidateId
              : this.shortlistCandidateList,
            candidateStatus: 12,
          },
          client: "apolloClientAM",
        })
        .then(() => {
          this.isLoading = false;
          this.selectAllBox = false;
          this.openShortlistCandidateModal = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Candidates have been shortlisted successfully",
          };
          vm.bulkPortalAccessPopup = false;
          vm.bulkAcceptCandidatePortal = false;
          vm.shortlistCandidateList = [];
          vm.showAlert(snackbarData);
          vm.refetchList();
        })
        .catch((err) => {
          vm.bulkPortalAccessPopup = false;
          vm.bulkAcceptCandidatePortal = false;
          vm.shortlistCandidateList = [];
          this.isLoading = false;
          this.openShortlistCandidateModal = false;
          this.handleShorlistError(err);
        });
    },
    handleShorlistError(err = "") {
      this.isLoading = false;
      this.refetchList();
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "candidate status",
        isListError: false,
      });
    },
    processCandidates(candidates) {
      return candidates.map((item) => {
        let options = [];
        let havingAccess = {};

        if (this.validateUser(item.Recruiter_Id, item)) {
          if (
            (item.Hiring_Stage == "Screening" ||
              item.Hiring_Stage == "Interview") &&
            item.Job_Post_Id
          ) {
            options.push("Send Assessment");
            if (this.formAccessForSendAssessment && this.userIsRecruiter) {
              havingAccess["send assessment"] =
                this.formAccessForSendAssessment.add;
            }
            options.push("Schedule Interview");
            if (this.formAccessForScheduleInterview && this.userIsRecruiter) {
              havingAccess["schedule interview"] =
                this.formAccessForScheduleInterview.add;
            }
            if (item.Hiring_Stage == "Screening") {
              havingAccess["delete"] =
                this.formAccess && this.formAccess.delete
                  ? this.formAccess.delete
                  : 0;
              options.push("Delete");
            }
          } else if (item.Hiring_Stage == "Sourced") {
            havingAccess["shortlist"] =
              this.formAccess && this.formAccess.update
                ? this.formAccess.update
                : 0;
            havingAccess["delete"] =
              this.formAccess && this.formAccess.delete
                ? this.formAccess.delete
                : 0;
            options = ["Shortlist", "Delete"];
          } else if (item.Hiring_Stage == "Hired") {
            havingAccess["initiate background investigation"] = 1;
            options = ["Initiate Background Investigation"];
          }
        } else {
          if (item.Hiring_Stage == "Screening" && item.Job_Post_Id) {
            options.push("Send Assessment");
            options.push("Schedule Interview");
            havingAccess["delete"] =
              this.formAccess && this.formAccess.delete
                ? this.formAccess.delete
                : 0;
            options.push("Delete");
          } else if (item.Hiring_Stage == "Sourced") {
            havingAccess["delete"] =
              this.formAccess && this.formAccess.delete
                ? this.formAccess.delete
                : 0;
            havingAccess["shortlist"] =
              this.formAccess && this.formAccess.update
                ? this.formAccess.update
                : 0;
            options = ["Shortlist", "Delete"];
          } else if (item.Hiring_Stage == "Hired") {
            havingAccess["initiate background investigation"] = 1;
            options = ["Initiate Background Investigation"];
          }
        }
        havingAccess["move candidate to talent pool"] =
          this.userIsRecruiter && this.talentFormAccess.update ? 1 : 0;
        havingAccess["move candidate to archive"] =
          this.userIsRecruiter &&
          this.formAccess.update &&
          this.validateUser(item.Recruiter_Id, item)
            ? 1
            : 0;
        // if (
        //   this.selectedStage !== 5 &&
        //   item.Candidate_Status?.toLowerCase() !== "rejected"
        // ) {
        //   options.push("Move Candidate To Talent Pool");
        //   options.push("Move Candidate To Archive");
        // }
        if (
          this.portalAccessEnabeld &&
          item.Blacklisted?.toLowerCase() === "no"
        ) {
          if (item?.Portal_Access_Enabeld?.toLowerCase() === "no") {
            options.push("Enable Portal Access");
          } else {
            options.push("Disable Portal Access");
          }
        }
        if (item?.Hiring_Stage?.toLowerCase() === "preboarding") {
          // rejected, not hired, onboarded
          this.candidateNoActionStatusList = [3, 20, 24];
        }
        if (item?.Hiring_Stage?.toLowerCase() === "archived") {
          this.candidateNoActionStatusList = [20, 21, 22, 23, 24, 25];
        }
        if (
          item.Candidate_Status?.toLowerCase() !== "onboarded" &&
          item.Blacklisted?.toLowerCase() !== "yes"
        ) {
          options.push("Blacklist Candidate");
          havingAccess["blacklist candidate"] = this.formAccess.update ? 1 : 0;
        }
        if (item.Blacklisted?.toLowerCase() === "yes") {
          options.push("Remove from Blacklist");
          havingAccess["remove from blacklist"] = this.formAccess.update
            ? 1
            : 0;
        }
        havingAccess["enable portal access"] =
          this.userIsRecruiter &&
          this.formAccess.update &&
          this.validateUser(item.Recruiter_Id, item)
            ? 1
            : 0;
        havingAccess["disable portal access"] =
          this.userIsRecruiter &&
          this.formAccess.update &&
          this.validateUser(item.Recruiter_Id, item)
            ? 1
            : 0;

        return {
          ...item,
          havingAccess,
          options,
        };
      });
    },
    retrieveResumeDetails(filepath) {
      this.retrievedFileName = filepath;
      this.openModal = true;
    },
    getStageList() {
      this.stageLoader = true;
      this.$apollo
        .query({
          query: GET_STATUS_LIST,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            formId: 16,
            conditions: [
              {
                key: "Form_Id",
                value: ["16"],
              },
            ],
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getAtsStatusList &&
            response.data.getAtsStatusList.statusList
          ) {
            let tempData = response.data.getAtsStatusList.statusList;
            let groupedByStage = tempData.reduce((acc, item) => {
              // If the stage doesn't exist in the accumulator, create a new array for it
              if (!acc[item.Stage]) {
                acc[item.Stage] = [];
                this.stageList[item.Stage_Id - 1] = {
                  id: item.Stage_Id,
                  value: item.Stage,
                };
              }
              // Push the current item into the appropriate group
              acc[item.Stage].push(item);
              return acc;
            }, {});
            this.flowList = groupedByStage;
            this.candidateStatusList = groupedByStage["Sourced"];
          } else {
            this.stageList = [];
          }
          this.stageLoader = false;
        })
        .catch((err) => {
          this.stageLoader = false;
          this.handleRetrieveHiringFlow(err);
        });
    },
    handleRetrieveHiringFlow(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "stages list",
        isListError: false,
      });
    },
    onSelectStage(stage) {
      this.selectedStage = stage;
      this.candidateStatusList = this.flowList[this.stageList[stage - 1].value];
      this.retrieveJobPosts();
    },
    removeCandidateFromBlacklist() {
      let vm = this;
      vm.openRemoveBlacklistModal = false;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: REMOVE_CANDIDATE_FROM_BLACKLIST,
          variables: {
            candidateId: this.selectedCandidateId,
          },
          client: "apolloClientAH",
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            type: "success",
            message: "Candidate removed from blacklist successfully",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.refetchList();
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "removing",
            form: "candidate from blacklist",
            isListError: false,
          });
        });
    },
    cancelBlacklistAction() {
      this.selectedType = "";
      this.candidateDetails = {};
      this.blacklistCandidateWarning = false;
    },
    updatePortalAccess(candidateId = null) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_CANDIDATE_PORTAL_ACCESS,
          variables: {
            candidateId: candidateId,
            portalAccessEnabeld:
              vm.portalAccessType?.toLowerCase() === "enable portal access"
                ? "Yes"
                : "No",
          },
          client: "apolloClientAM",
        })
        .then(async () => {
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Candidate portal access updated successfully",
          };
          vm.openDisablePortalAccessModal = false;
          vm.showAlert(snackbarData);
          vm.refetchList();
          vm.openEnablePortalAccessForm = false;
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          this.handleUpdatePortalAccessError(err);
        });
    },
    handleUpdatePortalAccessError(err = "") {
      this.refetchList();
      this.openEnablePortalAccessForm = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "portal access",
        isListError: false,
      });
    },
    checkEmailTemplate(candidateId, type = null) {
      this.fetchEmailTemplates(candidateId, type);
    },
    fetchEmailTemplates(candidateId, type = null) {
      let vm = this;
      vm.isEmailTemplateListLoading = true;
      this.portalAccessType = "";
      vm.$apollo
        .query({
          query: LIST_CUSTOM_EMAIL_TEMPLATES,
          variables: {
            formId: type === "shortlist" ? 16 : 311,
            categoryId: type === "shortlist" ? 3 : 18,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listCustomEmailTemplates &&
            response.data.listCustomEmailTemplates.emailTemplates
          ) {
            this.emailTemplateList =
              response.data.listCustomEmailTemplates.emailTemplates;
            this.noCustomTemplate =
              this.emailTemplateList?.length === 0 ? true : false;
            let candidateName = [
              this.candidateDetails?.First_Name,
              this.candidateDetails?.Middle_Name,
              this.candidateDetails?.Last_Name,
            ]
              .filter((name) => name)
              .join(" ");
            this.candidateEmail = this.candidateDetails?.Personal_Email;
            if (type === "shortlist") {
              this.shortlistTemplateData = {
                Company_Name: this.companyName,
                Candidate_Name: candidateName,
                Recruiter_Name: this.loginEmployeeDetails?.employeeFullName,
                Designation: this.loginEmployeeDetails?.designationName,
                Job_Post_Name: this.candidateDetails?.Job_Post_Name,
              };
              this.openShortlistOverlayForm = true;
            } else {
              this.portalAccessType = type;
              if (
                this.portalAccessType?.toLowerCase() === "enable portal access"
              ) {
                this.enablePortalAccessTemplateData = {
                  Company_Name: this.companyName,
                  Candidate_Name: candidateName,
                  Recruiter_Name: this.loginEmployeeDetails?.employeeFullName,
                  Job_Post_Name: this.candidateDetails?.Job_Post_Name,
                  Candidate_Portal_URL: `<a href="${`https://${this.orgCode}.${Config.domain}/v3/candidate-portal`}">https://${
                    this.orgCode
                  }.${Config.domain}/v3/candidate-portal</a>`,
                  Designation: this.loginEmployeeDetails?.designationName,
                  Specialist_Email: this.loginEmployeeDetails?.employeeEmail,
                };
                this.openEnablePortalAccessForm = true;
              } else {
                this.selectedCandidateId = candidateId;
                this.openDisablePortalAccessModal = true;
              }
            }
          }
          vm.isEmailTemplateListLoading = false;
        })
        .catch((err) => {
          vm.handleFetchEmailTemplatesError(err);
          vm.isEmailTemplateListLoading = false;
        });
    },
    handleFetchEmailTemplatesError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "email templates",
        isListError: false,
      });
    },
    // Global Search Event Handlers
    handleGlobalCandidateSelection(selectedCandidate) {
      if (selectedCandidate) {
        window.open(
          this.$store.getters.baseUrl +
            `v3/recruitment/job-candidates/duplicate-candidates?candidateId=${selectedCandidate.Candidate_Id}&jobpostId=${selectedCandidate?.Job_Post_Id}`,
          "_blank"
        );
      }
    },

    getStaticColumns() {
      // Return all columns that have explicit <td> elements in the template
      // This ensures proper header-to-column alignment
      return [
        {
          key: "First_Name",
          label: "Candidate",
          selected: true,
          fixed: true, // Cannot be deselected
        },
        {
          key: "Job_Post_Name",
          label: "Job Title",
          selected: true,
        },
        {
          key: "Candidate_Status",
          label: "Status",
          selected: true,
        },
        {
          key: "Source",
          label: "Source of Application",
          selected: true,
        },
        {
          key: "Applicant_Ranking",
          label: "Job Match Score",
          selected: true,
        },
        {
          key: "Expected_CTC",
          label: "Expected Salary",
          selected: true,
        },
        {
          key: "First_Interviewer",
          label: "First Interviewer",
          selected: true,
        },
        {
          key: "Resume",
          label: "Resume",
          selected: true,
          sortable: false,
        },
        {
          key: "Action",
          label: "Action",
          selected: true,
          fixed: true, // Cannot be deselected, always positioned last
          sortable: false,
        },
      ];
    },

    fallbackToStaticColumns() {
      const staticColumns = this.getStaticColumns();
      this.availableColumns = staticColumns;
      this.savedAvailableColumns = JSON.parse(JSON.stringify(staticColumns));
    },

    closeDynamicColumnsMenu() {
      // Revert displayed columns to saved state (cancel preview)
      this.availableColumns = JSON.parse(
        JSON.stringify(this.savedAvailableColumns)
      );
      // Discard temporary changes
      this.tempAvailableColumns = [];
      this.openDynamicColumnsMenu = false;
    },

    // Handle menu toggle to revert changes when closed externally (clicking outside)
    onDynamicColumnsMenuToggle(isOpen) {
      if (!isOpen && this.tempAvailableColumns.length > 0) {
        // Menu was closed externally, revert changes
        this.closeDynamicColumnsMenu();
      }
    },

    openEditColumnsMenu() {
      // Initialize temporary state with saved column selections (not current displayed)
      this.tempAvailableColumns = JSON.parse(
        JSON.stringify(this.savedAvailableColumns)
      );
      this.openDynamicColumnsMenu = true;
      this.openMoreMenu = false; // Close main menu if open
    },

    revertToSavedState() {
      // Revert displayed columns to saved state
      this.availableColumns = JSON.parse(
        JSON.stringify(this.savedAvailableColumns)
      );
      // Close the menu and clear temporary state
      this.openDynamicColumnsMenu = false;
      this.tempAvailableColumns = [];
    },

    toggleSelectAllColumns() {
      const editableColumns = this.tempAvailableColumns.filter(
        (column) => !column.fixed
      );
      const allSelected = editableColumns.every((column) => column.selected);

      // Toggle all editable columns
      this.tempAvailableColumns = this.tempAvailableColumns.map((column) => {
        if (!column.fixed) {
          return { ...column, selected: !allSelected };
        }
        return column;
      });
    },

    async saveDynamicColumns() {
      try {
        this.dynamicColumnsSaving = true;

        // Prepare the selected columns for API call - exclude fixed columns
        const selectedColumnKeys = this.tempAvailableColumns
          .filter((column) => column.selected && !column.fixed)
          .map((column) => column.key);

        // Call the API to save column preferences (if needed for persistence)
        const response = await this.$apollo.mutate({
          mutation: ADD_UPDATE_EMPLOYEE_CUSTOM_TABLE_HEADER,
          variables: {
            formId: 16,
            employeeCustomHeader: selectedColumnKeys,
            screenType: "DuplicateCandidates",
          },
          client: "apolloClientJ",
        });

        if (
          response.data &&
          response.data.addUpdateEmployeeCustomTableHeader &&
          !response.data.addUpdateEmployeeCustomTableHeader.errorCode
        ) {
          // API call successful, commit temporary state to saved state
          this.savedAvailableColumns = JSON.parse(
            JSON.stringify(this.tempAvailableColumns)
          );
          this.availableColumns = JSON.parse(
            JSON.stringify(this.tempAvailableColumns)
          );
          this.tempAvailableColumns = [];
          this.openDynamicColumnsMenu = false;

          // Show success message
          const snackbarData = {
            isOpen: true,
            type: "success",
            message:
              response.data.addUpdateEmployeeCustomTableHeader.message ||
              "Column preferences saved successfully",
          };
          this.showAlert(snackbarData);
        } else {
          // API call failed, revert to saved state and show error
          this.revertToSavedState();
          this.handleDynamicColumnsError(
            response.data?.addUpdateEmployeeCustomTableHeader?.message ||
              "Failed to save column preferences"
          );
        }
      } catch (error) {
        // API call failed, revert to saved state and show error
        this.revertToSavedState();
        this.handleDynamicColumnsError(error);
      } finally {
        this.dynamicColumnsSaving = false;
      }
    },

    handleDynamicColumnsError(err = "") {
      // Also dispatch to store for logging
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "Duplicate Candidates",
        isListError: false,
      });
    },

    // Mobile view helper methods for dynamic columns
    getMobileRowFields(rowNumber) {
      // Define mobile field mapping - which fields go in which row
      const mobileFieldMapping = {
        1: ["Job_Post_Name", "Candidate_Status"],
        2: ["Source", "Applicant_Ranking"],
        3: ["Expected_CTC", "First_Interviewer"],
        4: ["Resume"], // Resume gets its own row since it's a button
      };

      const fieldsForRow = mobileFieldMapping[rowNumber] || [];

      // Filter available columns to get only selected fields for this row
      // Add additional validation to ensure proper visibility filtering
      return this.availableColumns.filter((column) => {
        return (
          column &&
          column.key &&
          column.selected === true &&
          fieldsForRow.includes(column.key) &&
          column.key?.toLowerCase() !== "action" // Exclude Action column from mobile field rows
        );
      });
    },

    getMobileFieldValue(item, fieldKey) {
      // Handle special field formatting for mobile view
      switch (fieldKey) {
        case "Job_Post_Name":
          return this.checkNullValue(item.Job_Post_Name);
        case "Source":
          return this.checkNullValue(item.Source);
        case "Expected_CTC":
          return item.Expected_CTC ? item.Expected_CTC : 0;
        case "First_Interviewer":
          return this.checkNullValue(item.First_Interviewer);
        case "Resume":
          return "View Resume";
        default:
          // Handle dynamic columns from API
          if (this.isDateField(fieldKey)) {
            return this.checkNullValue(this.formatOnlyDate(item[fieldKey]));
          }
          return this.checkNullValue(item[fieldKey]);
      }
    },

    getMobileFieldLabel(fieldKey) {
      // Get the label for mobile field display
      const column = this.availableColumns.find((col) => col.key === fieldKey);
      return column ? column.label : fieldKey;
    },

    // Get mobile dynamic columns (same as desktop dynamicColumns but for clarity)
    getMobileDynamicColumns() {
      // This ensures consistency with desktop view and makes the code more readable
      // Add additional validation for mobile view
      return this.dynamicColumns.filter((column) => {
        return column && column.key && column.selected === true;
      });
    },

    // Organize dynamic columns into rows of 2 columns each for mobile display
    getMobileDynamicColumnsInRows() {
      const dynamicColumns = this.getMobileDynamicColumns();
      const rows = [];

      // Group columns into rows of 2
      for (let i = 0; i < dynamicColumns.length; i += 2) {
        const row = dynamicColumns.slice(i, i + 2);
        rows.push(row);
      }

      return rows;
    },

    // Helper method to check if a field is a date field
    isDateField(columnKey) {
      // Exact date field keys from listJobCandidates API response and related queries
      const exactDateFields = [
        "Last_Interview_Date",
        "First_Interview_Date",
        "Added_On",
        "Hiring_Date",
        "Shortlisting_Date",
        "Offer_Letter_Response_Date",
        "Offer_Letter_Rolled_Out_Date",
        "DOB",
        "Blacklisted_On", // From detailed candidate query
        "Created_On", // Common in dynamic columns
        "Updated_On", // Common in dynamic columns
        "Modified_On", // Common in dynamic columns
      ];

      // Check for exact matches first (most reliable)
      if (exactDateFields.includes(columnKey)) {
        return true;
      }

      // Fallback patterns for potential future date fields or dynamic columns
      const datePatterns = [
        /_Date$/i, // ends with _Date
        /_On$/i, // ends with _On
        /^Date_/i, // starts with Date_
        /DOB/i, // Date of Birth variations
      ];

      return datePatterns.some((pattern) => pattern.test(columnKey));
    },

    // Helper method to get column style for dynamic columns
    getColumnStyle(columnKey) {
      const baseStyle = { width: "150px" };

      // Special styling for First_Name column (sticky)
      if (columnKey === "First_Name") {
        return {
          ...baseStyle,
          position: "sticky",
          left: "0",
          backgroundColor: "white",
        };
      }

      return baseStyle;
    },

    getColumnClass(columnKey) {
      // Special classes for specific columns
      if (columnKey === "First_Name") {
        return "position-sticky left-0 bg-white";
      }

      if (columnKey === "Action") {
        return "text-body-2 text-center";
      }

      return "";
    },
  },
};
</script>

<style scoped>
.job-candidate-container {
  padding: 5em 2em 0em 3em;
}

/* Mobile Card Styles */
.mobile-cards-container {
  padding: 0 16px;
}

.candidate-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.candidate-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.candidate-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

@media (max-width: 768px) {
  .job-candidate-container {
    padding: 1em 0.5em 0em 0.5em;
  }

  .mobile-cards-container {
    padding: 0 8px;
  }
}

@media screen and (max-width: 805px) {
  .job-candidate-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
