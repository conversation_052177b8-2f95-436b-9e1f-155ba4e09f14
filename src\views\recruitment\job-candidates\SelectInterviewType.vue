<template>
  <div class="text-center">
    <v-overlay
      v-if="!showInterviewSchedule"
      v-model="overlay"
      class="d-flex justify-end overlay"
      @click:outside="clearFields()"
    >
      <template v-slot:default>
        <v-card
          rounded="lg"
          :style="{
            height: windowHeight + 'px',
            width: isMobileView ? '100vw' : '35vw',
          }"
        >
          <v-card-title
            class="d-flex justify-space-between align-center bg-primary"
          >
            <div class="text-h6">Select Interview Type</div>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="clearFields()"
            ></v-btn>
          </v-card-title>
          <v-divider class="mb-2"></v-divider>
          <v-card-text>
            <v-radio-group
              v-model="interviewType"
              :inline="isMobileView ? false : true"
            >
              <v-radio
                color="primary"
                label="Online Interview"
                value="Online"
              ></v-radio>
              <v-radio
                :style="isMobileView ? '' : 'margin-left: 20px'"
                color="primary"
                label="Face To Face Interview"
                value="Face To Face"
              ></v-radio>
            </v-radio-group>
          </v-card-text>

          <v-card-text v-if="showChoice">
            <p class="custom-label mb-5 ml-2">
              Would you like the candidate to schedule the interview session ?
            </p>
            <v-radio-group v-model="candidateWillSelect" inline>
              <v-radio color="primary" label="Yes" :value="true"></v-radio>
              <v-radio
                style="margin-left: 110px"
                color="primary"
                label="No"
                :value="false"
              ></v-radio>
            </v-radio-group>
          </v-card-text>
          <v-card class="overlay-footer w-100 pa-2">
            <v-btn
              class="text-none mr-3"
              elevation="2"
              rounded="lg"
              variant="outlined"
              @click="clearFields()"
              >Cancel</v-btn
            >
            <v-btn
              color="primary"
              variant="flat"
              elevation="2"
              rounded="lg"
              @click="validateChoicesForm()"
            >
              Next
            </v-btn>
          </v-card>
        </v-card>
      </template>
    </v-overlay>
    <InterviewSchedules
      v-else
      :candidateId="candidateId"
      :candidateDetails="candidateDetails"
      :candidateEmail="candidateEmail"
      :is-bulk="isBulk"
      :candidateName="candidateName"
      :statusId="statusId"
      :jobTitle="jobTitle"
      :jobPostId="jobPostId"
      :interviewType="interviewType"
      :candidateWillSelect="candidateWillSelect"
      :calendar-items="calendarItems"
      :meeting-items="meetingItems"
      :showConfigArray="showConfigArray"
      @close-interview-schedule-window="closeInterviewScheduleWindow($event)"
      @close-overlay="showInterviewSchedule = false"
    ></InterviewSchedules>
  </div>
</template>
<script>
import InterviewSchedules from "./InterviewSchedules.vue";
export default {
  name: "SelectInterviewType",
  components: {
    InterviewSchedules,
  },
  props: {
    candidateDetails: {
      type: Object,
      default: () => {},
    },
    statusId: {
      default: null,
      type: Number,
    },
    jobTitle: {
      default: "",
      type: String,
    },
    jobPostId: {
      default: null,
      type: Number,
    },
    candidateName: {
      default: "",
      type: String,
      required: true,
    },
    candidateEmail: {
      type: Array,
      required: true,
    },
    candidateId: {
      default: null,
      required: true,
    },
    isBulk: {
      type: Boolean,
      default: false,
    },
    calendarItems: {
      type: Array,
      default: () => [],
    },
    meetingItems: {
      type: Array,
      default: () => [],
    },
    showConfigArray: {
      type: Array,
      default: () => [],
    },
  },
  emits: ["close-interview-schedule-window"],
  data: () => ({
    overlay: true,
    showInterviewSchedule: false,
    interviewType: "",
    candidateWillSelect: true,
    showChoice: false,
  }),
  watch: {
    interviewType() {
      this.showChoice = true;
      this.candidateWillSelect = true;
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
  },
  methods: {
    validateChoicesForm() {
      this.showInterviewSchedule = true;
    },
    clearFields() {
      this.$emit("close-interview-schedule-window", false);
    },
    closeInterviewScheduleWindow(isSuccess) {
      this.$emit("close-interview-schedule-window", isSuccess);
    },
  },
};
</script>
<style scoped>
.overlay {
  height: 100% !important;
}
.overlay-footer {
  height: auto;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
  position: absolute;
  bottom: 0;
}
</style>
