<template>
  <div class="d-flex align-center" v-click-outside="closeDropdown">
    <div class="position-relative" style="width: 100%">
      <!-- Text Field for Search Input -->
      <v-text-field
        v-model="searchString"
        :placeholder="placeholder"
        prepend-inner-icon="fas fa-search"
        :variant="variant"
        :density="density"
        clearable
        hide-details
        :class="isMobileView ? 'mt-3' : ''"
        :loading="isLoading"
        @input="onSearchInput"
        @click:clear="clearSearch"
        @focus="debouncedFocus"
        @blur="onFieldBlur"
      />

      <!-- Dropdown Menu for Results -->
      <v-menu
        v-model="showDropdown"
        :activator="menuActivator"
        :close-on-content-click="false"
        :close-on-back="false"
        location="bottom start"
        offset="4"
        :max-height="windowHeight * 0.6 + `px`"
        :min-width="isMobileView ? `300px` : `400px`"
        transition="slide-y-transition"
        :z-index="9999"
      >
        <v-card
          class="rounded-lg elevation-4"
          :max-height="windowHeight * 0.6 + `px`"
          style="overflow-y: auto"
        >
          <!-- Show results or appropriate message -->
          <div v-if="candidateList.length > 0">
            <div
              v-for="(candidate, index) in candidateList"
              :key="`candidate-${candidate.Candidate_Id}-${index}`"
              class="pa-2"
            >
              <v-card
                class="rounded-lg elevation-1 pa-2 mb-1 cursor-pointer"
                @click="onCandidateSelected(candidate)"
                @mouseenter="hoveredIndex = index"
                @mouseleave="hoveredIndex = -1"
                :class="hoveredIndex === index ? 'bg-grey-lighten-4' : ''"
              >
                <v-row no-gutters align="center">
                  <!-- Avatar/Icon Section -->
                  <v-col cols="auto" class="mr-3">
                    <v-avatar
                      :style="{
                        backgroundColor: getCandidateAvatarColor(
                          candidate.Candidate_Id
                        ),
                      }"
                      size="40"
                    >
                      <span class="text-caption font-weight-medium text-white">
                        {{ getInitials(candidate.Candidate_Name) }}
                      </span>
                    </v-avatar>
                  </v-col>

                  <!-- Candidate Information Section -->
                  <v-col class="flex-grow-1">
                    <!-- Candidate Name - Full Width -->
                    <div
                      class="text-subtitle-1 font-weight-medium text-high-emphasis mb-2"
                    >
                      {{ truncateText(candidate.Candidate_Name, 30) }}
                    </div>

                    <!-- Email - Full Width -->
                    <div
                      v-if="candidate.Personal_Email"
                      class="d-flex align-center mb-2"
                    >
                      <v-icon size="12" class="mr-2 text-medium-emphasis">
                        fas fa-envelope
                      </v-icon>
                      <span class="text-caption text-medium-emphasis">
                        {{ truncateText(candidate.Personal_Email, 25) }}
                      </span>
                    </div>

                    <!-- 2-Column Grid for Other Fields -->
                    <v-row no-gutters class="mb-1">
                      <!-- Column 1 -->
                      <v-col cols="6" class="pr-2">
                        <!-- Mobile Number -->
                        <div
                          v-if="candidate.Mobile_No"
                          class="d-flex align-center mb-1"
                        >
                          <v-icon
                            size="12"
                            class="mr-1 text-medium-emphasis flex-shrink-0"
                          >
                            fas fa-phone
                          </v-icon>
                          <span
                            class="text-caption text-medium-emphasis text-truncate"
                          >
                            {{ truncateText(candidate.Mobile_No, 15) }}
                          </span>
                        </div>

                        <!-- Candidate Stage -->
                        <div
                          v-if="candidate.Candidate_Stage"
                          class="d-flex align-center"
                        >
                          <v-icon
                            size="12"
                            class="mr-1 text-medium-emphasis flex-shrink-0"
                          >
                            fas fa-layer-group
                          </v-icon>
                          <span
                            class="text-caption text-medium-emphasis text-truncate"
                          >
                            {{ truncateText(candidate.Candidate_Stage, 15) }}
                          </span>
                        </div>
                      </v-col>

                      <!-- Column 2 -->
                      <v-col cols="6" class="pl-2">
                        <!-- Job Post Name -->
                        <div
                          v-if="candidate.Job_Post_Name"
                          class="d-flex align-center mb-1"
                        >
                          <v-icon
                            size="12"
                            class="mr-1 text-medium-emphasis flex-shrink-0"
                          >
                            fas fa-briefcase
                          </v-icon>
                          <span
                            class="text-caption text-medium-emphasis text-truncate"
                          >
                            {{ truncateText(candidate.Job_Post_Name, 15) }}
                          </span>
                        </div>
                        <!-- Candidate Status -->
                        <div
                          v-if="candidate.Candidate_Status"
                          class="d-flex align-center"
                        >
                          <v-icon
                            size="12"
                            class="mr-1 text-medium-emphasis flex-shrink-0"
                          >
                            fas fa-user-check
                          </v-icon>
                          <span
                            class="text-caption text-medium-emphasis text-truncate"
                          >
                            {{ truncateText(candidate.Candidate_Status, 15) }}
                          </span>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>

                  <!-- Action Arrow -->
                  <v-col cols="auto">
                    <v-icon
                      color="primary"
                      size="16"
                      class="text-medium-emphasis mx-4"
                    >
                      fas fa-location-arrow
                    </v-icon>
                  </v-col>
                </v-row>
              </v-card>
            </div>
          </div>

          <!-- No Data / Loading / Message State -->
          <div v-else class="py-1 text-center" style="min-width: 300px">
            <div class="text-caption text-medium">
              {{ noDataText }}
            </div>
          </div>
        </v-card>
      </v-menu>
    </div>
    <v-tooltip
      v-if="enableTootltipMessage"
      :text="tooltipMessage"
      location="top"
      max-width="300px"
      class="ml-2"
    >
      <template v-slot:activator="{ props }">
        <v-icon v-bind="props" size="x-small" class="ml-1" color="blue"
          >fas fa-info-circle</v-icon
        >
      </template>
    </v-tooltip>
  </div>
</template>

<script>
import { GET_CANDIDATE_GLOBAL_SEARCH_LIST } from "@/graphql/recruitment/recruitmentQueries";
import { checkNullValue, colorCode } from "@/helper";
import debounce from "lodash.debounce";
export default {
  name: "JobCandidateGlobalSearch",

  directives: {
    "click-outside": {
      beforeMount(el, binding) {
        el.clickOutsideEvent = function (event) {
          if (!(el === event.target || el.contains(event.target))) {
            binding.value(event);
          }
        };
        document.addEventListener("click", el.clickOutsideEvent);
      },
      unmounted(el) {
        document.removeEventListener("click", el.clickOutsideEvent);
      },
    },
  },

  props: {
    formId: {
      type: Number,
      required: true,
    },
    screenTab: {
      type: String,
      default: "",
    },
    placeholder: {
      type: String,
      default: "",
    },
    variant: {
      type: String,
      default: "solo",
    },
    density: {
      type: String,
      default: "compact",
    },
    enableTootltipMessage: {
      type: Boolean,
      default: true,
    },
    tooltipMessage: {
      type: String,
      default:
        "Search for candidates by name, email, mobile number or job title.",
    },
    limit: {
      type: Number,
      default: 500,
    },
  },

  emits: ["candidate-selected"],

  data() {
    return {
      searchString: "",
      candidateList: [],
      isLoading: false,
      // Dropdown control properties
      showDropdown: false,
      menuActivator: null,
      hoveredIndex: -1,
      isFieldFocused: false,
      // Pagination properties
      totalApiCount: 0,
      apiCallCount: 0,
      totalRecords: 0,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    noDataText() {
      if (this.isLoading) {
        return "Loading...";
      } else if (
        !this.isLoading &&
        this.candidateList.length === 0 &&
        this.searchString.length >= 3
      ) {
        return "No candidates found";
      } else {
        return "Type minimum 3 characters to search";
      }
    },
  },

  created() {
    // Initialize debounced search function
    this.debouncedSearch = debounce(this.performSearch, 300);
    // Initialize debounced focus handler to prevent rapid multiple focus events
    this.debouncedFocus = debounce(this.handleFieldFocus, 300);
  },

  mounted() {
    // Set the menu activator to the text field
    this.$nextTick(() => {
      const textField = this.$el.querySelector(".v-text-field");
      if (textField) {
        this.menuActivator = textField;
      }
    });
  },

  methods: {
    checkNullValue,

    // Text truncation method
    truncateText(text, maxLength) {
      if (!text) return "";
      return text.length > maxLength
        ? text.substring(0, maxLength) + "..."
        : text;
    },

    onSearchInput(event) {
      // Handle the input event from v-text-field
      const value = event.target ? event.target.value : event;
      this.searchString = value || "";
      this.debouncedSearch();
    },

    handleFieldFocus() {
      // Set focus state and show dropdown if conditions are met
      this.isFieldFocused = true;

      // Only show dropdown if it's not already shown and we have valid conditions
      if (!this.showDropdown) {
        if (
          this.candidateList.length > 0 ||
          (this.searchString.length >= 3 && !this.isLoading)
        ) {
          this.showDropdown = true;
        }
      }
    },

    onFieldBlur() {
      // Set focus state to false with a small delay to allow for dropdown interactions
      setTimeout(() => {
        this.isFieldFocused = false;
      }, 150);
    },

    closeDropdown() {
      this.showDropdown = false;
      this.hoveredIndex = -1;
      this.isFieldFocused = false;
    },
    performSearch() {
      if (this.searchString.length < 3) {
        this.candidateList = [];
        this.resetPagination();
        this.showDropdown = false;
        return;
      }

      this.isLoading = true;
      this.resetPagination();

      // Only show dropdown if field is focused or we already have it open
      if (this.isFieldFocused || this.showDropdown) {
        this.showDropdown = true;
      }

      this.$apollo
        .query({
          query: GET_CANDIDATE_GLOBAL_SEARCH_LIST,
          client: "apolloClientAN",
          variables: {
            searchValue: this.searchString,
            formId: this.formId,
            screenTab: this.screenTab,
            offset: 0,
            limit: this.limit,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCandidateGlobalSearchList &&
            !response.data.getCandidateGlobalSearchList.errorCode.length
          ) {
            const responseData = response.data.getCandidateGlobalSearchList;
            this.candidateList = responseData.candidates || [];

            // Handle pagination if totalCount is available
            if (responseData.totalRecords) {
              this.totalRecords = parseInt(responseData.totalRecords);
              this.apiCallCount = 1;
              this.totalApiCount = Math.ceil(this.totalRecords / this.limit);

              // Load additional pages if needed
              for (let i = 1; i < this.totalApiCount; i++) {
                this.loadMoreCandidates(i);
              }
            }
          } else {
            this.candidateList = [];
            this.handleSearchError(
              response.data.getCandidateGlobalSearchList?.errorCode ||
                "No data returned from API"
            );
          }
        })
        .catch((error) => {
          this.candidateList = [];
          this.handleSearchError(error);
        })
        .finally(() => {
          // Only set loading to false if this is the only API call
          if (this.totalApiCount <= 1) {
            this.isLoading = false;
          }
        });
    },

    /**
     * Load additional candidates for pagination
     * @param {Number} pageIndex - Page index for pagination
     */
    loadMoreCandidates(pageIndex = 1) {
      const apiOffset = parseInt(pageIndex) * this.limit;

      this.$apollo
        .query({
          query: GET_CANDIDATE_GLOBAL_SEARCH_LIST,
          client: "apolloClientAN",
          variables: {
            searchValue: this.searchString,
            formId: this.formId,
            screenTab: this.screenTab,
            offset: apiOffset,
            limit: this.limit,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCandidateGlobalSearchList &&
            !response.data.getCandidateGlobalSearchList.errorCode.length
          ) {
            const additionalCandidates =
              response.data.getCandidateGlobalSearchList.candidates || [];

            // Append new candidates to existing list
            this.candidateList = [
              ...this.candidateList,
              ...additionalCandidates,
            ];

            this.apiCallCount = this.apiCallCount + 1;

            // Set loading to false when all API calls are complete
            if (this.totalApiCount === this.apiCallCount) {
              this.isLoading = false;
            }
          } else {
            this.handleSearchError(
              response.data.getCandidateGlobalSearchList?.errorCode ||
                "Error loading additional candidates"
            );
          }
        })
        .catch((error) => {
          this.handleSearchError(error);
        });
    },
    resetPagination() {
      this.totalApiCount = 0;
      this.apiCallCount = 0;
      this.totalRecords = 0;
    },

    onCandidateSelected(candidate) {
      if (candidate) {
        this.$emit("candidate-selected", candidate);

        // Keep the dropdown open for additional selections
        // Only reset hover state
        this.hoveredIndex = -1;
      }
    },
    handleSearchError(error = "") {
      this.$store.dispatch("handleApiErrors", {
        error: error,
        action: "searching",
        form: "Global Candidate Search",
        isListError: true,
      });
    },
    clearSearch() {
      this.searchString = "";
      this.candidateList = [];
      this.resetPagination();
      this.showDropdown = false;
      this.hoveredIndex = -1;
      this.isFieldFocused = false;
    },

    /**
     * Generate initials from candidate name
     * @param {String} name - Candidate name
     * @returns {String} - Initials (max 2 characters)
     */
    getInitials(name) {
      if (!name) return "?";

      const words = name.trim().split(" ");
      if (words.length === 1) {
        return words[0].charAt(0).toUpperCase();
      }

      return (
        words[0].charAt(0) + words[words.length - 1].charAt(0)
      ).toUpperCase();
    },

    /**
     * Get consistent avatar color for candidate based on ID
     * @param {String|Number} candidateId - Candidate ID
     * @returns {String} - Hex color code
     */
    getCandidateAvatarColor(candidateId) {
      // Use candidate ID to consistently assign same color (1-10 range for colorCode function)
      const colorIndex = candidateId ? (Math.abs(candidateId) % 10) + 1 : 1;
      return colorCode(colorIndex);
    },
  },
};
</script>

<style scoped>
/* Minimal custom styles - using Vuetify classes for most styling */
.v-card {
  transition: all 0.2s ease-in-out;
}

/* Ensure dropdown appears above other elements */
:deep(.v-overlay__content) {
  z-index: 9999 !important;
}

/* Smooth transitions for hover effects */
.v-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
}
</style>
