<template>
  <div>
    <ActionMenu
      v-if="shouldShowActions"
      @selected-action="handleActionSelection"
      :actions="candidateActions.options"
      :access-rights="candidateActions.havingAccess"
      iconColor="grey"
    />

    <!-- Confirmation Modals -->
    <AppWarningModal
      v-if="showDeleteConfirmation"
      :open-modal="showDeleteConfirmation"
      confirmation-heading="Are you sure you want to delete this record ?"
      icon-name="fas fa-trash-alt"
      @close-warning-modal="showDeleteConfirmation = false"
      @accept-modal="executeDeleteAction"
    />

    <AppWarningModal
      v-if="showRemoveBlacklistModal"
      :open-modal="showRemoveBlacklistModal"
      confirmation-heading="Are you sure you want to remove this candidate from blacklist ?"
      icon-name=""
      @close-warning-modal="showRemoveBlacklistModal = false"
      @accept-modal="executeRemoveFromBlacklistAction"
    />

    <AppWarningModal
      v-if="showDisablePortalAccessModal"
      :open-modal="showDisablePortalAccessModal"
      confirmation-heading="Are you sure you want to disable portal access for this candidate ?"
      icon-name=""
      @close-warning-modal="showDisablePortalAccessModal = false"
      @accept-modal="executePortalAccessAction"
    />

    <AppWarningModal
      v-if="showBlacklistWarning"
      :open-modal="showBlacklistWarning"
      confirmation-heading="This candidate has been blacklisted. Are you sure you want to proceed ?"
      icon-name=""
      @close-warning-modal="cancelBlacklistAction"
      @accept-modal="proceedWithBlacklistedAction"
    />

    <!-- Action-specific Forms -->
    <TalentPoolOverlayForm
      v-if="showTalentPoolForm"
      :candidateDetails="candidate"
      :candidateId="candidateIdSelected"
      :originalStatusList="originalStatusList"
      :candidateName="candidate.First_Name"
      :isCandidateDeleted="false"
      @close-talent-pool-overlay-form="showTalentPoolForm = false"
      @refetch-data="handleActionSuccess('Move Candidate To Talent Pool')"
    />

    <ArchiveCandidateOverlayForm
      v-if="showArchiveForm"
      :candidateDetails="candidate"
      :candidateId="candidateIdSelected"
      :originalStatusList="originalStatusList"
      :candidateName="candidate.First_Name"
      :isCandidateDeleted="false"
      @close-archive-candidates-window="showArchiveForm = false"
      @refetch-data="handleActionSuccess('Move Candidate To Archive')"
    />

    <BlacklistForm
      :view-form="showBlacklistForm"
      :selectedCandidateId="candidateIdSelected"
      :selectedCandidateStatus="candidate.Status_Id"
      @close-form="showBlacklistForm = false"
      @update-form="handleBlacklistSuccess"
    />

    <ShortlistCandidateOverlayForm
      v-if="showShortlistForm"
      :emailTemplateList="emailTemplateList"
      :candidateId="candidateIdSelected"
      :noCustomTemplate="noCustomTemplate"
      :candidateEmail="candidateEmail"
      :templateData="shortlistTemplateData"
      @close-shortlist-candidate-window="showShortlistForm = false"
      @refetch-data="handleActionSuccess('Shortlist')"
    />

    <EnablePortalAccessOverlayForm
      v-if="showPortalAccessForm"
      :emailTemplateList="emailTemplateList"
      :accept-candidate-portal="portalAccessType === 'enable' ? 'Yes' : 'No'"
      :noCustomTemplate="noCustomTemplate"
      :candidateEmail="candidateEmail"
      :templateData="enablePortalAccessTemplateData"
      :candidateId="candidateIdSelected"
      @close-candidate-portal-form="showPortalAccessForm = false"
      @refetch-data="handleActionSuccess(currentAction)"
    />

    <!-- Interview and Assessment Components -->
    <SelectInterviewType
      v-if="showScheduleInterviewForm"
      :candidate-details="candidate"
      :candidateId="candidateIdSelected"
      :candidate-email="
        candidate.Personal_Email ? [candidate.Personal_Email] : []
      "
      :candidateName="candidate.First_Name || ''"
      :jobPostId="candidate.Job_Post_Id"
      :statusId="candidate.Status_Id"
      :jobTitle="candidate.Job_Post_Name || ''"
      :calendar-items="calendarItems"
      :meeting-items="meetingItems"
      :showConfigArray="showConfigArray"
      :isBulk="false"
      @close-interview-schedule-window="handleInterviewSuccess($event)"
    />

    <InterviewSchedules
      v-if="showAssessmentForm"
      :candidateId="[candidateIdSelected]"
      :candidate-details="candidate"
      :candidateName="candidate.First_Name || ''"
      :candidate-email="
        candidate.Personal_Email ? [candidate.Personal_Email] : []
      "
      :jobPostId="candidate.Job_Post_Id"
      :statusId="candidate.Status_Id"
      :jobTitle="candidate.Job_Post_Name || ''"
      :candidateWillSelect="true"
      interviewType="Assessment Link"
      :isBulk="false"
      @close-interview-schedule-window="handleInterviewSuccess($event)"
    />

    <!-- Background Investigation Email Form -->
    <UpdateCandidateStatusOverlayForm
      v-if="showBackgroundInvestigationForm"
      ref="backgroundInvestigationForm"
      :candidateId="candidateIdSelected"
      :typeOfTemplate="'InitiateBackgroundInvestigation'"
      :typeOfSchedule="''"
      :templateData="backgroundInvestigationTemplateData"
      :emailRecievers="[loginEmployeeEmail]"
      :templateEmail="[candidate.Personal_Email]"
      :selectedStatus="71"
      :selected-stage="'background investigation'"
      @custom-email-sent="handleBackgroundInvestigationSuccess"
      @custom-email-cancel="handleBackgroundInvestigationCancel"
      @close-overlay="handleBackgroundInvestigationCancel"
    />

    <AppLoading v-if="isLoading" />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

// Import all the required components
const TalentPoolOverlayForm = defineAsyncComponent(() =>
  import("@/views/recruitment/job-candidates/TalentPoolOverlayForm.vue")
);
const ArchiveCandidateOverlayForm = defineAsyncComponent(() =>
  import("@/views/recruitment/job-candidates/ArchiveCandidateOverlayForm.vue")
);
const ShortlistCandidateOverlayForm = defineAsyncComponent(() =>
  import("@/views/recruitment/job-candidates/ShortlistCandidateOverlayForm.vue")
);
const EnablePortalAccessOverlayForm = defineAsyncComponent(() =>
  import("@/views/recruitment/job-candidates/EnablePortalAccessOverlayForm.vue")
);
const BlacklistForm = defineAsyncComponent(() =>
  import("@/views/recruitment/job-candidates/BlacklistForm.vue")
);
import SelectInterviewType from "@/views/recruitment/job-candidates/SelectInterviewType.vue";
import InterviewSchedules from "@/views/recruitment/job-candidates/InterviewSchedules.vue";
import UpdateCandidateStatusOverlayForm from "@/views/recruitment/job-candidates/job-candidates-details/UpdateCandidateStatusOverlayForm.vue";

// Import GraphQL mutations and queries
import {
  DELETE_CANDIDATE_JOB,
  REMOVE_CANDIDATE_FROM_BLACKLIST,
  UPDATE_CANDIDATE_PORTAL_ACCESS,
  UPDATE_CANDIDATE_STATUS,
  GET_STATUS_LIST,
  MOVE_ARCHIVE_TO_CANDIDATE,
} from "@/graphql/recruitment/recruitmentQueries.js";
import { RETRIEVE_INTERVIEW_ROUNDS_LIST } from "@/graphql/recruitment/interviewScheduleQueries.js";
import { LIST_CUSTOM_EMAIL_TEMPLATES } from "@/graphql/settings/email-template/emailTemplateQueries.js";
import { GET_MY_INTEGRATION } from "@/graphql/recruitment/myIntegrationQueries.js";
import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";

export default {
  name: "CandidateActionMenu",
  components: {
    ActionMenu,
    TalentPoolOverlayForm,
    ArchiveCandidateOverlayForm,
    ShortlistCandidateOverlayForm,
    EnablePortalAccessOverlayForm,
    BlacklistForm,
    SelectInterviewType,
    InterviewSchedules,
    UpdateCandidateStatusOverlayForm,
  },
  data() {
    return {
      candidateNoActionStatusList: [3, 20, 21, 22, 23, 24, 25],
      isLoading: false,

      // Modal states
      showDeleteConfirmation: false,
      showRemoveBlacklistModal: false,
      showDisablePortalAccessModal: false,
      showBlacklistWarning: false,

      // Form states
      showTalentPoolForm: false,
      showArchiveForm: false,
      showBlacklistForm: false,
      showShortlistForm: false,
      showPortalAccessForm: false,
      showScheduleInterviewForm: false,
      showAssessmentForm: false,
      showBackgroundInvestigationForm: false,

      // Action tracking
      currentAction: "",
      pendingAction: "",
      pendingCandidate: null,

      // Email template data
      emailTemplateList: [],
      noCustomTemplate: false,
      candidateEmail: "",
      portalAccessType: "",

      // Template data objects
      shortlistTemplateData: {},
      enablePortalAccessTemplateData: {},
      backgroundInvestigationTemplateData: {},

      // Interview/Assessment data
      calendarItems: [],
      meetingItems: [],
      showConfigArray: [],
      roundsOptionList: [],

      // Additional data for interview components
      roundListLoading: false,

      // Status list for forms
      originalStatusList: [],

      // Store computed actions as reactive data
      computedActions: { options: [], havingAccess: {} },
    };
  },
  props: {
    candidate: {
      type: Object,
      required: true,
    },
    parentTabName: {
      type: String,
      default: "Job Candidates",
    },
    formAccess: {
      type: Object,
      required: true,
    },
    isRecruiter: {
      type: String,
      default: "No",
    },
    recruitmentSettings: {
      type: Object,
      required: true,
    },
    candidateIdSelected: {
      type: Number,
      required: true,
    },
  },
  emits: ["candidate-data-refresh", "candidate-view-close"],
  mounted() {
    // Initialize data properties on component mount
    this.initializeDataProperties();
    // Initialize actions
    this.updateActions();
  },
  watch: {
    // Watch for changes in candidate data to update actions
    candidate: {
      handler(newCandidate, oldCandidate) {
        // Only update if candidate data actually changed
        if (
          newCandidate &&
          (!oldCandidate ||
            newCandidate.Status_Id !== oldCandidate.Status_Id ||
            newCandidate.Hiring_Stage !== oldCandidate.Hiring_Stage ||
            newCandidate.Blacklisted !== oldCandidate.Blacklisted ||
            newCandidate.Portal_Access_Enabeld !==
              oldCandidate.Portal_Access_Enabeld)
        ) {
          this.updateActions();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    userIsRecruiter() {
      return this.$store.state.isRecruiter;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    talentFormAccess() {
      let formAccess = this.accessRights("297");

      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        formAccess.accessRights["archive"] = formAccess.accessRights["update"];
        formAccess.accessRights["move candidate to job"] =
          formAccess.accessRights["update"];

        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForScheduleInterview() {
      let formAccess = this.accessRights("272");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForSendAssessment() {
      let formAccess = this.accessRights("275");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    candidateActions() {
      // Return the reactive data property instead of computing each time
      return this.computedActions;
    },
    shouldShowActions() {
      return (
        this.candidate &&
        (this.validateUser(this.candidate?.Recruiter_Id) ||
          this.formAccess?.delete) &&
        this.candidateActions?.options?.length > 0 &&
        !this.candidateNoActionStatusList?.includes(this.candidate?.Status_Id)
      );
    },
    loginEmployeeEmail() {
      return this.$store.getters.loginEmployeeEmail || "";
    },
    actionsToCloseView() {
      return [
        "delete",
        "move candidate to talent pool",
        "move candidate to archive",
        "rollback",
      ];
    },
  },

  methods: {
    /**
     * Initialize data properties with proper default values
     */
    initializeDataProperties() {
      // Initialize arrays to ensure they are reactive
      this.calendarItems = [];
      this.meetingItems = [];
      this.showConfigArray = [];
      this.emailTemplateList = [];
      this.originalStatusList = [];
      this.roundsOptionList = [];

      // Initialize template data objects
      this.shortlistTemplateData = {};
      this.enablePortalAccessTemplateData = {};
      this.backgroundInvestigationTemplateData = {};

      // Initialize other properties
      this.noCustomTemplate = false;
      this.candidateEmail = "";
      this.portalAccessType = "";
      this.roundListLoading = false;
    },

    /**
     * Reset data properties when needed
     */
    resetDataProperties() {
      this.calendarItems = [];
      this.meetingItems = [];
      this.showConfigArray = [];
      this.emailTemplateList = [];
      this.noCustomTemplate = false;
      this.candidateEmail = "";
      this.roundListLoading = false;
    },

    /**
     * Refresh actions by updating the reactive data
     */
    refreshActions() {
      this.updateActions();
    },

    /**
     * Update actions by processing candidate data
     */
    updateActions() {
      // Process the candidate actions and update reactive data
      const newActions = this.processCandidateActions(this.candidate);

      // Update the reactive data property
      this.computedActions = {
        options: [...newActions.options],
        havingAccess: { ...newActions.havingAccess },
      };
    },

    /**
     * Method to be called by parent components when candidate data is refreshed externally
     */
    onCandidateDataRefreshed() {
      this.updateActions();
    },

    /**
     * Public method to manually refresh actions (for testing and debugging)
     */
    refreshActionMenu() {
      this.updateActions();
    },

    /**
     * Get current available actions (for testing and debugging)
     */
    getCurrentActions() {
      return {
        options: [...this.candidateActions.options],
        havingAccess: { ...this.candidateActions.havingAccess },
      };
    },
    /**
     * Main action handler - centralized entry point for all candidate actions
     */
    handleActionSelection(action) {
      let vm = this;
      vm.currentAction = action;
      vm.isLoading = true;

      // Check if candidate is blacklisted and action requires warning
      if (vm.shouldShowBlacklistWarning(action)) {
        vm.pendingAction = action;
        vm.showBlacklistWarning = true;
        vm.isLoading = false;
        return;
      }

      // Execute the action directly
      vm.executeAction(action)
        .then(() => {
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.handleApiErrors(error);
          vm.isLoading = false;
        });
    },

    /**
     * Check if blacklist warning should be shown
     */
    shouldShowBlacklistWarning(action) {
      return (
        this.candidate?.Blacklisted?.toLowerCase() === "yes" &&
        action.toLowerCase() !== "remove from blacklist"
      );
    },

    /**
     * Execute the specific action based on action type
     */
    executeAction(action) {
      const actionLower = action.toLowerCase();

      switch (actionLower) {
        case "delete":
          this.showDeleteConfirmation = true;
          return Promise.resolve();
        case "shortlist":
          return this.handleShortlistAction();
        case "move candidate to talent pool":
          return this.handleMoveToTalentPoolAction();
        case "move candidate to archive":
          return this.handleMoveToArchiveAction();
        case "blacklist candidate":
          this.showBlacklistForm = true;
          return Promise.resolve();
        case "remove from blacklist":
          this.showRemoveBlacklistModal = true;
          return Promise.resolve();
        case "enable portal access":
          return this.handleEnablePortalAccessAction();
        case "disable portal access":
          this.showDisablePortalAccessModal = true;
          return Promise.resolve();
        case "send assessment":
          return this.handleSendAssessmentAction();
        case "schedule interview":
          return this.handleScheduleInterviewAction();
        case "initiate background investigation":
          return this.handleBackgroundInvestigationAction();
        case "rollback":
          return this.handleRollbackAction();
        default:
          return Promise.resolve();
      }
    },

    processCandidateActions(candidate) {
      if (!candidate) return { options: [], havingAccess: {} };

      let options = [];
      let havingAccess = {};

      // Process actions based on parent tab context
      if (this.parentTabName === "Job Candidates") {
        return this.processJobCandidateActions(
          candidate,
          options,
          havingAccess
        );
      } else if (this.parentTabName === "Archived") {
        return this.processArchivedCandidateActions(
          candidate,
          options,
          havingAccess
        );
      } else if (this.parentTabName === "Duplicate Candidates") {
        return this.processDuplicateCandidateActions(
          candidate,
          options,
          havingAccess
        );
      } else if (this.parentTabName === "Job Candidate Onboarding") {
        return this.processOnboardingCandidateActions(
          candidate,
          options,
          havingAccess
        );
      }

      return { options, havingAccess };
    },
    processJobCandidateActions(
      candidate = {},
      options = [],
      havingAccess = {}
    ) {
      if (this.validateUser(candidate.Recruiter_Id, candidate)) {
        if (
          (candidate.Hiring_Stage?.toLowerCase() == "screening" ||
            candidate.Hiring_Stage?.toLowerCase() == "interview") &&
          candidate.Job_Post_Id
        ) {
          options.push("Send Assessment");
          if (this.formAccessForSendAssessment && this.userIsRecruiter) {
            havingAccess["send assessment"] =
              this.formAccessForSendAssessment.add;
          }
          options.push("Schedule Interview");
          if (this.formAccessForScheduleInterview && this.userIsRecruiter) {
            havingAccess["schedule interview"] =
              this.formAccessForScheduleInterview.add;
          }
          if (candidate.Hiring_Stage?.toLowerCase() == "screening") {
            havingAccess["delete"] =
              this.formAccess && this.formAccess.delete
                ? this.formAccess.delete
                : 0;
            options.push("Delete");
          }
        } else if (candidate.Hiring_Stage?.toLowerCase() == "sourced") {
          havingAccess["shortlist"] =
            this.formAccess && this.formAccess.update
              ? this.formAccess.update
              : 0;
          havingAccess["delete"] =
            this.formAccess && this.formAccess.delete
              ? this.formAccess.delete
              : 0;
          options = ["Shortlist", "Delete"];
        } else if (candidate.Hiring_Stage?.toLowerCase() == "hired") {
          havingAccess["initiate background investigation"] = 1;
          options = ["Initiate Background Investigation"];
        }
      } else {
        if (
          candidate.Hiring_Stage?.toLowerCase() == "screening" &&
          candidate.Job_Post_Id
        ) {
          options.push("Send Assessment");
          options.push("Schedule Interview");
          havingAccess["delete"] =
            this.formAccess && this.formAccess.delete
              ? this.formAccess.delete
              : 0;
          options.push("Delete");
        } else if (candidate.Hiring_Stage?.toLowerCase() == "sourced") {
          havingAccess["delete"] =
            this.formAccess && this.formAccess.delete
              ? this.formAccess.delete
              : 0;
          havingAccess["shortlist"] =
            this.formAccess && this.formAccess.update
              ? this.formAccess.update
              : 0;
          options = ["Shortlist", "Delete"];
        } else if (candidate.Hiring_Stage?.toLowerCase() == "hired") {
          havingAccess["initiate background investigation"] = 1;
          options = ["Initiate Background Investigation"];
        }
      }

      // Common actions for all job candidates
      this.addCommonCandidateActions(candidate, options, havingAccess);

      return { options, havingAccess };
    },
    processArchivedCandidateActions(candidate, options, havingAccess) {
      // Archived candidates have specific actions based on validation and stage
      if (this.validateUser(candidate.Recruiter_Id, candidate)) {
        if (
          (candidate.Hiring_Stage?.toLowerCase() == "screening" ||
            candidate.Hiring_Stage?.toLowerCase() == "interview") &&
          candidate.Job_Post_Id
        ) {
          if (candidate.Hiring_Stage?.toLowerCase() == "screening") {
            havingAccess["delete"] =
              this.formAccess && this.formAccess.delete
                ? this.formAccess.delete
                : 0;
            options.push("Delete");
          }
        } else if (candidate.Hiring_Stage?.toLowerCase() == "sourced") {
          havingAccess["delete"] =
            this.formAccess && this.formAccess.delete
              ? this.formAccess.delete
              : 0;
          options.push("Delete");
        }
      } else {
        if (
          candidate.Hiring_Stage?.toLowerCase() == "screening" &&
          candidate.Job_Post_Id
        ) {
          havingAccess["delete"] =
            this.formAccess && this.formAccess.delete
              ? this.formAccess.delete
              : 0;
          options.push("Delete");
        } else if (candidate.Hiring_Stage?.toLowerCase() == "sourced") {
          havingAccess["delete"] =
            this.formAccess && this.formAccess.delete
              ? this.formAccess.delete
              : 0;
          options.push("Delete");
        }
      }

      // Rollback action for archived candidates
      havingAccess["rollback"] = this.userIsRecruiter ? 1 : 0;
      options.push("Rollback");

      // Portal access actions for archived candidates
      if (
        this.recruitmentSettings?.Candidate_Portal_Login_Access?.toLowerCase() ===
          "yes" &&
        candidate.Blacklisted?.toLowerCase() === "no"
      ) {
        if (candidate?.Portal_Access_Enabeld?.toLowerCase() === "no") {
          options.push("Enable Portal Access");
        } else {
          options.push("Disable Portal Access");
        }
      }

      // Set candidate no action status list for preboarding stage
      if (candidate?.Hiring_Stage?.toLowerCase() === "preboarding") {
        // rejected, not hired, onboarded
        this.candidateNoActionStatusList = [3, 20, 24];
      } else this.candidateNoActionStatusList = [3, 20, 21, 22, 23, 24, 25];

      // Blacklist actions for archived candidates
      if (
        candidate.Candidate_Status?.toLowerCase() !== "onboarded" &&
        candidate.Blacklisted?.toLowerCase() !== "yes"
      ) {
        options.push("Blacklist Candidate");
        havingAccess["blacklist candidate"] = this.formAccess.update ? 1 : 0;
      }

      if (candidate.Blacklisted?.toLowerCase() === "yes") {
        options.push("Remove from Blacklist");
        havingAccess["remove from blacklist"] = this.formAccess.update ? 1 : 0;
      }

      // Portal access permissions for archived candidates
      havingAccess["enable portal access"] =
        this.userIsRecruiter &&
        this.formAccess.update &&
        this.validateUser(candidate.Recruiter_Id, candidate)
          ? 1
          : 0;
      havingAccess["disable portal access"] =
        this.userIsRecruiter &&
        this.formAccess.update &&
        this.validateUser(candidate.Recruiter_Id, candidate)
          ? 1
          : 0;

      return { options, havingAccess };
    },
    processDuplicateCandidateActions(candidate, options, havingAccess) {
      // Duplicate candidates have similar actions to job candidates with some restrictions
      if (this.validateUser(candidate.Recruiter_Id, candidate)) {
        if (
          (candidate.Hiring_Stage?.toLowerCase() == "screening" ||
            candidate.Hiring_Stage?.toLowerCase() == "interview") &&
          candidate.Job_Post_Id
        ) {
          options.push("Send Assessment");
          if (this.formAccessForSendAssessment && this.userIsRecruiter) {
            havingAccess["send assessment"] =
              this.formAccessForSendAssessment.add;
          }
          options.push("Schedule Interview");
          if (this.formAccessForScheduleInterview && this.userIsRecruiter) {
            havingAccess["schedule interview"] =
              this.formAccessForScheduleInterview.add;
          }
          if (candidate.Hiring_Stage?.toLowerCase() == "screening") {
            havingAccess["delete"] =
              this.formAccess && this.formAccess.delete
                ? this.formAccess.delete
                : 0;
            options.push("Delete");
          }
        } else if (candidate.Hiring_Stage?.toLowerCase() == "sourced") {
          havingAccess["shortlist"] =
            this.formAccess && this.formAccess.update
              ? this.formAccess.update
              : 0;
          havingAccess["delete"] =
            this.formAccess && this.formAccess.delete
              ? this.formAccess.delete
              : 0;
          options = ["Shortlist", "Delete"];
        } else if (candidate.Hiring_Stage?.toLowerCase() == "hired") {
          havingAccess["initiate background investigation"] = 1;
          options = ["Initiate Background Investigation"];
        }
      } else {
        if (
          candidate.Hiring_Stage?.toLowerCase() == "screening" &&
          candidate.Job_Post_Id
        ) {
          options.push("Send Assessment");
          options.push("Schedule Interview");
          havingAccess["delete"] =
            this.formAccess && this.formAccess.delete
              ? this.formAccess.delete
              : 0;
          options.push("Delete");
        } else if (candidate.Hiring_Stage?.toLowerCase() == "sourced") {
          havingAccess["delete"] =
            this.formAccess && this.formAccess.delete
              ? this.formAccess.delete
              : 0;
          havingAccess["shortlist"] =
            this.formAccess && this.formAccess.update
              ? this.formAccess.update
              : 0;
          options = ["Shortlist", "Delete"];
        } else if (candidate.Hiring_Stage?.toLowerCase() == "hired") {
          havingAccess["initiate background investigation"] = 1;
          options = ["Initiate Background Investigation"];
        }
      }

      // Set access rights for talent pool and archive actions
      havingAccess["move candidate to talent pool"] =
        this.userIsRecruiter && this.talentFormAccess?.update ? 1 : 0;
      havingAccess["move candidate to archive"] =
        this.userIsRecruiter &&
        this.formAccess.update &&
        this.validateUser(candidate.Recruiter_Id, candidate)
          ? 1
          : 0;

      // Note: Move to talent pool and archive actions are commented out in DuplicateJobCandidates
      // but access rights are still set for potential future use

      // Portal access actions for duplicate candidates
      if (
        this.recruitmentSettings?.Candidate_Portal_Login_Access?.toLowerCase() ===
          "yes" &&
        candidate.Blacklisted?.toLowerCase() === "no"
      ) {
        if (candidate?.Portal_Access_Enabeld?.toLowerCase() === "no") {
          options.push("Enable Portal Access");
        } else {
          options.push("Disable Portal Access");
        }
      }

      // Set candidate no action status list based on hiring stage
      if (candidate?.Hiring_Stage?.toLowerCase() === "preboarding") {
        // rejected, not hired, onboarded
        this.candidateNoActionStatusList = [3, 20, 24];
      }
      if (candidate?.Hiring_Stage?.toLowerCase() === "archived") {
        this.candidateNoActionStatusList = [20, 21, 22, 23, 24, 25];
      }
      if (
        candidate?.Hiring_Stage?.toLowerCase() !== "preboarding" &&
        candidate?.Hiring_Stage?.toLowerCase() !== "archived"
      )
        this.candidateNoActionStatusList = [3, 20, 21, 22, 23, 24, 25];

      // Blacklist actions for duplicate candidates
      if (
        candidate.Candidate_Status?.toLowerCase() !== "onboarded" &&
        candidate.Blacklisted?.toLowerCase() !== "yes"
      ) {
        options.push("Blacklist Candidate");
        havingAccess["blacklist candidate"] = this.formAccess.update ? 1 : 0;
      }

      if (candidate.Blacklisted?.toLowerCase() === "yes") {
        options.push("Remove from Blacklist");
        havingAccess["remove from blacklist"] = this.formAccess.update ? 1 : 0;
      }

      // Portal access permissions for duplicate candidates
      havingAccess["enable portal access"] =
        this.userIsRecruiter &&
        this.formAccess.update &&
        this.validateUser(candidate.Recruiter_Id, candidate)
          ? 1
          : 0;
      havingAccess["disable portal access"] =
        this.userIsRecruiter &&
        this.formAccess.update &&
        this.validateUser(candidate.Recruiter_Id, candidate)
          ? 1
          : 0;

      return { options, havingAccess };
    },
    processOnboardingCandidateActions(candidate, options, havingAccess) {
      // Onboarding specific actions
      switch (candidate.Candidate_Status) {
        case "Offer letter Accepted":
          options = ["Send Invite"];
          havingAccess["send invite"] = 1;
          break;
        case "Onboarding Inprogress":
          options = ["Resend Invite"];
          havingAccess["resend invite"] = 1;
          break;
        default:
          options = [];
      }

      return { options, havingAccess };
    },
    addCommonCandidateActions(candidate, options, havingAccess) {
      havingAccess["move candidate to talent pool"] =
        this.userIsRecruiter && this.talentFormAccess?.update ? 1 : 0;
      havingAccess["move candidate to archive"] =
        this.userIsRecruiter &&
        this.formAccess.update &&
        this.validateUser(candidate.Recruiter_Id, candidate)
          ? 1
          : 0;
      havingAccess["enable portal access"] =
        this.userIsRecruiter &&
        this.formAccess.update &&
        this.validateUser(candidate.Recruiter_Id, candidate)
          ? 1
          : 0;
      havingAccess["disable portal access"] =
        this.userIsRecruiter &&
        this.formAccess.update &&
        this.validateUser(candidate.Recruiter_Id, candidate)
          ? 1
          : 0;

      if (
        candidate?.Hiring_Stage?.toLowerCase() !== "archived" &&
        candidate.Candidate_Status?.toLowerCase() !== "rejected"
      ) {
        options.push("Move Candidate To Talent Pool");
        options.push("Move Candidate To Archive");
      }

      if (
        this.recruitmentSettings?.Candidate_Portal_Login_Access?.toLowerCase() ===
          "yes" &&
        candidate.Blacklisted?.toLowerCase() === "no"
      ) {
        if (candidate?.Portal_Access_Enabeld?.toLowerCase() === "no") {
          options.push("Enable Portal Access");
        } else {
          options.push("Disable Portal Access");
        }
      }
      if (candidate?.Hiring_Stage?.toLowerCase() === "preboarding")
        // rejected, not hired, onboarded
        this.candidateNoActionStatusList = [3, 20, 24];
      if (candidate?.Hiring_Stage?.toLowerCase() === "archived")
        this.candidateNoActionStatusList = [20, 21, 22, 23, 24, 25];

      if (
        candidate?.Hiring_Stage?.toLowerCase() !== "preboarding" &&
        candidate?.Hiring_Stage?.toLowerCase() !== "archived"
      )
        this.candidateNoActionStatusList = [3, 20, 21, 22, 23, 24, 25];

      if (
        candidate.Candidate_Status?.toLowerCase() !== "onboarded" &&
        candidate.Blacklisted?.toLowerCase() !== "yes"
      ) {
        options.push("Blacklist Candidate");
        havingAccess["blacklist candidate"] = this.formAccess.update ? 1 : 0;
      }

      if (candidate.Blacklisted?.toLowerCase() === "yes") {
        options.push("Remove from Blacklist");
        havingAccess["remove from blacklist"] = this.formAccess.update ? 1 : 0;
      }
    },
    validateUser(recruiters, candidate = null) {
      let isFormAdmin = this.formAccess.admin === "admin";
      if (isFormAdmin) {
        return true;
      } else if (
        candidate &&
        candidate.Job_Post_Creator == this.loginEmployeeId &&
        this.isRecruiter.toLowerCase() === "yes"
      ) {
        return true;
      } else {
        let employeeId = this.loginEmployeeId;
        if (recruiters && recruiters.includes(employeeId)) {
          return true;
        }
        return false;
      }
    },
    /**
     * Blacklist warning handlers
     */
    cancelBlacklistAction() {
      this.showBlacklistWarning = false;
      this.pendingAction = "";
      this.isLoading = false;
    },

    proceedWithBlacklistedAction() {
      let vm = this;
      vm.showBlacklistWarning = false;
      vm.executeAction(vm.pendingAction)
        .then(() => {
          vm.pendingAction = "";
        })
        .catch((error) => {
          vm.handleApiErrors(error);
          vm.pendingAction = "";
        });
    },

    /**
     * Delete action handlers
     */
    executeDeleteAction() {
      let vm = this;
      vm.showDeleteConfirmation = false;
      vm.isLoading = true;

      vm.$apollo
        .mutate({
          mutation: DELETE_CANDIDATE_JOB,
          variables: {
            candidateId: vm.candidateIdSelected,
          },
          client: "apolloClientA",
        })
        .then((result) => {
          if (!result.data?.deleteCandidateJob?.errorCode) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate details deleted successfully",
            };
            vm.showAlert(snackbarData);
            vm.handleActionSuccess("Delete", true);
          } else {
            vm.handleUpdateError(
              result.data?.deleteCandidateJob?.errorCode || ""
            );
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.handleUpdateError(error);
          vm.isLoading = false;
        });
    },
    handleUpdateError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "candidate details",
        isListError: false,
      });
    },

    /**
     * Handle general API errors
     */
    handleApiErrors(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "processing",
        form: "candidate action",
        isListError: false,
      });
    },

    /**
     * Remove from blacklist handlers
     */
    executeRemoveFromBlacklistAction() {
      let vm = this;
      vm.showRemoveBlacklistModal = false;
      vm.isLoading = true;

      vm.$apollo
        .mutate({
          mutation: REMOVE_CANDIDATE_FROM_BLACKLIST,
          variables: {
            candidateId: vm.candidateIdSelected,
          },
          client: "apolloClientAH",
        })
        .then((result) => {
          if (!result.data?.removeCandidateFromBlacklist?.errorCode) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate removed from blacklist successfully",
            };
            vm.showAlert(snackbarData);
            vm.handleActionSuccess("Remove from Blacklist");
          } else {
            vm.handleUpdateError(
              result.data?.removeCandidateFromBlacklist?.errorCode || ""
            );
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.handleUpdateError(error);
          vm.isLoading = false;
        });
    },

    /**
     * Portal access handlers
     */
    executePortalAccessAction() {
      let vm = this;
      vm.showDisablePortalAccessModal = false;
      vm.isLoading = true;

      vm.$apollo
        .mutate({
          mutation: UPDATE_CANDIDATE_PORTAL_ACCESS,
          variables: {
            candidateId: [vm.candidateIdSelected],
            portalAccessEnabeld: "No",
          },
          client: "apolloClientAM",
        })
        .then((result) => {
          if (!result.data?.updateCandidatePortalAccess?.errorCode) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Portal access disabled successfully",
            };
            vm.showAlert(snackbarData);
            vm.handleActionSuccess("Disable Portal Access");
          } else {
            vm.handleUpdateError(
              result.data?.updateCandidatePortalAccess?.errorCode || ""
            );
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.handleUpdateError(error);
          vm.isLoading = false;
        });
    },

    /**
     * Shortlist action handler
     */
    handleShortlistAction() {
      let vm = this;
      return vm
        .loadEmailTemplates()
        .then(() => {
          // Prepare template data for shortlist
          vm.shortlistTemplateData = {
            Company_Name: vm.$store.getters.organizationName || "",
            Candidate_Name: vm.getCandidateFullName(),
            Recruiter_Name: vm.$store.getters.loginEmployeeFullName || "",
            Job_Post_Name: vm.candidate?.Job_Post_Name || "",
          };
          vm.candidateEmail = vm.candidate.Personal_Email || "";
          vm.showShortlistForm = true;
        })
        .catch((error) => {
          vm.handleApiErrors(error);
          vm.showShortlistForm = false;
        });
    },

    /**
     * Move to talent pool action handler
     */
    handleMoveToTalentPoolAction() {
      let vm = this;
      return vm
        .loadStatusList()
        .then(() => {
          vm.showTalentPoolForm = true;
        })
        .catch((error) => {
          vm.handleApiErrors(error);
          vm.showTalentPoolForm = false;
        });
    },

    /**
     * Move to archive action handler
     */
    handleMoveToArchiveAction() {
      let vm = this;
      return vm
        .loadStatusList()
        .then(() => {
          vm.showArchiveForm = true;
        })
        .catch((error) => {
          vm.handleApiErrors(error);
          vm.showArchiveForm = false;
        });
    },

    /**
     * Enable portal access action handler
     */
    handleEnablePortalAccessAction() {
      let vm = this;
      return vm
        .loadEmailTemplates()
        .then(() => {
          // Prepare template data for portal access
          vm.enablePortalAccessTemplateData = {
            Company_Name: vm.$store.getters.organizationName || "",
            Candidate_Name: vm.getCandidateFullName(),
            Recruiter_Name: vm.$store.getters.loginEmployeeFullName || "",
            Job_Post_Name: vm.candidate?.Job_Post_Name || "",
          };
          vm.candidateEmail = vm.candidate.Personal_Email || "";
          vm.portalAccessType = "enable";
          vm.showPortalAccessForm = true;
        })
        .catch((error) => {
          vm.handleApiErrors(error);
          vm.showPortalAccessForm = false;
        });
    },

    /**
     * Send assessment action handler
     */
    handleSendAssessmentAction() {
      let vm = this;
      return vm
        .loadInterviewData()
        .then(() => {
          vm.isLoading = false;
          vm.showAssessmentForm = true;
          return Promise.resolve();
        })
        .catch((error) => {
          vm.handleApiErrors(error);
          vm.showAssessmentForm = false;
          // Ensure loading state is reset
          vm.isLoading = false;
          throw error; // Re-throw to ensure promise chain handles it
        });
    },

    /**
     * Schedule interview action handler
     */
    handleScheduleInterviewAction() {
      let vm = this;
      return vm
        .loadInterviewData()
        .then(() => {
          vm.isLoading = false;
          vm.showScheduleInterviewForm = true;
          return Promise.resolve();
        })
        .catch((error) => {
          vm.handleApiErrors(error);
          vm.showScheduleInterviewForm = false;
          // Ensure loading state is reset
          vm.isLoading = false;
          throw error; // Re-throw to ensure promise chain handles it
        });
    },

    /**
     * Background investigation action handler
     */
    handleBackgroundInvestigationAction() {
      let vm = this;
      return vm
        .loadEmailTemplates()
        .then(() => {
          // Prepare template data for background investigation
          vm.backgroundInvestigationTemplateData = {
            Candidate_Name: vm.getCandidateFullName(),
            Recruiter_Name: vm.$store.getters.loginEmployeeFullName || "",
            Designation: vm.$store.getters.loginEmployeeDesignation || "",
            Org_Name: vm.$store.getters.organizationName || "",
            emailTemplateType: "Background Investigation",
          };
          vm.showBackgroundInvestigationForm = true;
        })
        .catch((error) => {
          vm.handleApiErrors(error);
          vm.showBackgroundInvestigationForm = false;
        });
    },

    /**
     * Rollback action handler - moves archived candidate back to job candidates
     */
    handleRollbackAction() {
      let vm = this;
      vm.isLoading = true;

      return vm.$apollo
        .mutate({
          mutation: MOVE_ARCHIVE_TO_CANDIDATE,
          client: "apolloClientAV",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: vm.candidateIdSelected,
            formId: 16,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.moveArchiveToCandidate
          ) {
            const { errorCode, validationError } =
              response.data.moveArchiveToCandidate;
            if (!errorCode && !validationError) {
              let snackbarData = {
                isOpen: true,
                type: "success",
                message: "Candidate rolled back successfully.",
              };
              vm.showAlert(snackbarData);
              vm.handleActionSuccess("Rollback", true);
            } else {
              vm.handleRollbackError(
                response.data.moveArchiveToCandidate.message ||
                  "Failed to rollback candidate"
              );
            }
          } else {
            vm.handleRollbackError("Invalid response from server");
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          vm.handleRollbackError(error);
          vm.isLoading = false;
        });
    },

    /**
     * Handle rollback errors
     */
    handleRollbackError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "rolling back",
        form: "job candidate",
        isListError: false,
      });
    },

    /**
     * Load email templates for actions that require them
     */
    loadEmailTemplates() {
      let vm = this;
      vm.portalAccessType = "";
      return vm.$apollo
        .query({
          query: LIST_CUSTOM_EMAIL_TEMPLATES,
          variables: {
            formId: 16,
            categoryId: 1,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((result) => {
          // Handle different response structures based on API version
          if (result.data?.listCustomEmailTemplates?.emailTemplates) {
            vm.emailTemplateList =
              result.data.listCustomEmailTemplates.emailTemplates;
          } else if (result.data?.listCustomEmailTemplates) {
            vm.emailTemplateList = result.data.listCustomEmailTemplates;
          } else {
            vm.emailTemplateList = [];
          }

          vm.noCustomTemplate = vm.emailTemplateList.length === 0;
          return vm.emailTemplateList;
        })
        .catch((error) => {
          vm.handleEmailTemplateError(error);
          vm.emailTemplateList = [];
          vm.noCustomTemplate = true;
          return vm.emailTemplateList;
        });
    },

    /**
     * Load status list for forms that require it
     */
    loadStatusList() {
      let vm = this;
      return vm.$apollo
        .query({
          query: GET_STATUS_LIST,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            formId: 16,
            conditions: [
              {
                key: "Form_Id",
                value: ["16"],
              },
            ],
          },
        })
        .then((result) => {
          // Handle different response structures
          if (result.data?.getAtsStatusList?.statusList) {
            vm.originalStatusList = result.data.getAtsStatusList.statusList;
          } else {
            vm.originalStatusList = [];
          }
          return vm.originalStatusList;
        })
        .catch((error) => {
          vm.handleRetrieveHiringFlow(error);
          vm.originalStatusList = [];
          return vm.originalStatusList;
        });
    },
    handleRetrieveHiringFlow(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "stages list",
        isListError: false,
      });
    },

    /**
     * Handle integration status retrieval errors
     */
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "integration status",
        isListError: false,
      });
    },

    /**
     * Handle email template loading errors
     */
    handleEmailTemplateError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "email templates",
        isListError: false,
      });
    },

    /**
     * Show success alert
     */
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    /**
     * Load interview data for interview/assessment actions
     */
    loadInterviewData() {
      let vm = this;
      return vm.$apollo
        .query({
          query: RETRIEVE_INTERVIEW_ROUNDS_LIST,
          variables: {
            jobpostId: vm.candidate.Job_Post_Id,
            searchString: "",
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((roundsResult) => {
          // Handle the response structure properly
          if (roundsResult.data?.listJobPostRounds?.errorCode) {
            throw new Error(
              roundsResult.data.listJobPostRounds.message ||
                "Failed to load interview rounds"
            );
          }

          vm.roundsOptionList =
            roundsResult.data?.listJobPostRounds?.jobpostRounds || [];

          // Load integration status for calendar and meeting items
          return vm.loadIntegrationStatus();
        })
        .then(() => {
          return vm.loadMicrosoftIntegrationStatus();
        })
        .then(() => {
          return {
            roundsOptionList: vm.roundsOptionList,
            calendarItems: vm.calendarItems,
            meetingItems: vm.meetingItems,
            showConfigArray: vm.showConfigArray,
          };
        })
        .catch((error) => {
          vm.handleRetrieveHiringFlow(error);
          vm.roundsOptionList = [];
          vm.calendarItems = [];
          vm.meetingItems = [];
          vm.showConfigArray = [];
          return {
            roundsOptionList: vm.roundsOptionList,
            calendarItems: vm.calendarItems,
            meetingItems: vm.meetingItems,
            showConfigArray: vm.showConfigArray,
          };
        });
    },

    /**
     * Load integration status for calendar and meeting items
     */
    loadIntegrationStatus() {
      let vm = this;
      return vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 16, // Form ID for job candidates
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((result) => {
          // Reset arrays
          vm.calendarItems = [];
          vm.meetingItems = [];

          if (result.data?.jobBoardIntegrationStatus?.getStatus?.length > 0) {
            const { getStatus } = result.data.jobBoardIntegrationStatus;

            for (let iStatus of getStatus) {
              if (
                iStatus.Integration_Type === "Microsoft" &&
                iStatus.Integration_Status?.toLowerCase() === "active"
              ) {
                vm.calendarItems.push(iStatus.Integration_Type);
              } else if (
                iStatus.Integration_Type === "Teams Meeting" &&
                iStatus.Integration_Status?.toLowerCase() === "active"
              ) {
                vm.meetingItems.push(iStatus.Integration_Type);
              }
            }
          }
          return {
            calendarItems: vm.calendarItems,
            meetingItems: vm.meetingItems,
          };
        })
        .catch((error) => {
          vm.handleRetrieveIntegrationStatusError(error);
          vm.calendarItems = [];
          vm.meetingItems = [];
          return {
            calendarItems: vm.calendarItems,
            meetingItems: vm.meetingItems,
          };
        });
    },

    /**
     * Load Microsoft integration status for showConfigArray
     */
    loadMicrosoftIntegrationStatus() {
      let vm = this;
      return vm.$apollo
        .query({
          query: GET_MY_INTEGRATION,
          variables: {
            formId: 16, // Form ID for job candidates
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((result) => {
          // Reset showConfigArray
          vm.showConfigArray = [];

          if (result.data?.retrieveMicrosoftIntegration) {
            const integration = result.data.retrieveMicrosoftIntegration;

            // Add items to showConfigArray if they are not active
            if (integration.calendarStatus?.toLowerCase() !== "active") {
              vm.showConfigArray.push("Microsoft");
            }
            if (integration.teamsStatus?.toLowerCase() !== "active") {
              vm.showConfigArray.push("Teams Meeting");
            }
          }
          return vm.showConfigArray;
        })
        .catch((error) => {
          vm.handleRetrieveIntegrationStatusError(error);
          vm.showConfigArray = [];
          return vm.showConfigArray;
        });
    },

    /**
     * Handle successful action completion
     */
    handleActionSuccess(actionName, shouldClose = false) {
      // Check if this action should close the view
      const shouldCloseView =
        shouldClose ||
        this.actionsToCloseView.some(
          (closeAction) => actionName.toLowerCase() === closeAction
        );

      if (shouldCloseView) {
        // For actions that close the view, emit close event immediately
        // The parent will handle list refresh
        this.$emit("candidate-view-close", {
          action: actionName,
          candidate: this.candidate,
        });
      } else {
        // For actions that keep the view open, refresh candidate details
        // and update action menu based on new candidate state
        this.$emit("candidate-data-refresh");

        // Wait for candidate data to be refreshed, then update actions
        this.$nextTick(() => {
          // Add a small delay to ensure candidate data has been updated
          setTimeout(() => {
            this.updateActions();
          }, 100);
        });
      }
    },

    /**
     * Handle blacklist form success
     */
    handleBlacklistSuccess() {
      this.showBlacklistForm = false;
      let snackbarData = {
        isOpen: true,
        type: "success",
        message: "Candidate blacklisted successfully",
      };
      this.showAlert(snackbarData);
      this.handleActionSuccess("Blacklist Candidate");
    },

    /**
     * Handle interview success (both assessment and interview)
     */
    handleInterviewSuccess(isSuccess = false) {
      // Determine which action was completed
      const wasAssessment = this.showAssessmentForm;
      const wasInterview = this.showScheduleInterviewForm;

      // Close the appropriate form
      this.showScheduleInterviewForm = false;
      this.showAssessmentForm = false;
      this.isLoading = false;
      if (isSuccess) {
        // Show appropriate success message
        let message = "Interview/Assessment scheduled successfully";
        let actionName = "Schedule Interview";

        if (wasAssessment) {
          message = "Assessment scheduled successfully";
          actionName = "Send Assessment";
        } else if (wasInterview) {
          message = "Interview scheduled successfully";
          actionName = "Schedule Interview";
        }

        let snackbarData = {
          isOpen: true,
          type: "success",
          message: message,
        };
        this.showAlert(snackbarData);
        this.handleActionSuccess(actionName);
      }
    },

    /**
     * Handle background investigation success
     */
    handleBackgroundInvestigationSuccess() {
      this.showBackgroundInvestigationForm = false;
      // Update candidate status to "Background Investigation Initiated"
      let statusId = this.getCandidateStatusId();
      this.updateCandidateStatus(
        statusId,
        "Background investigation initiated successfully",
        "Initiate Background Investigation"
      );
    },

    /**
     * Handle background investigation cancel/close
     */
    handleBackgroundInvestigationCancel() {
      this.showBackgroundInvestigationForm = false;
      this.isLoading = false;
    },

    /**
     * Get candidate full name for template data
     */
    getCandidateFullName() {
      if (!this.candidate) return "";

      let nameParts = [];
      if (this.candidate.Last_Name) {
        nameParts.push(this.candidate.Last_Name);
      }
      if (this.candidate.First_Name) {
        nameParts.push(this.candidate.First_Name);
      }
      if (this.candidate.Middle_Name) {
        nameParts.push(this.candidate.Middle_Name);
      }

      return nameParts.join(" ");
    },

    /**
     * Get candidate status ID for background investigation
     */
    getCandidateStatusId() {
      let statusList = this.originalStatusList || [];
      let statusObj = statusList.find(
        (status) =>
          status.Status.toLowerCase() === "background investigation initiated"
      );

      return statusObj ? statusObj.Id : 71; // Default to 71 if not found
    },

    /**
     * Update candidate status after email actions
     * @param {number} statusId - The status ID to update to
     * @param {string} successMessage - Success message to display
     */
    updateCandidateStatus(
      statusId,
      successMessage = "Candidate status updated successfully",
      actionName = null
    ) {
      let vm = this;
      vm.isLoading = true;

      vm.$apollo
        .mutate({
          mutation: UPDATE_CANDIDATE_STATUS,
          client: "apolloClientAM",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: [vm.candidateIdSelected],
            candidateStatus: statusId,
          },
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            type: "success",
            message: successMessage,
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;

          // Trigger action success with the specific action name
          if (actionName) {
            vm.handleActionSuccess(actionName);
          }
        })
        .catch((err) => {
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "updating",
            form: "candidate status",
            isListError: false,
          });
          vm.isLoading = false;
        });
    },
  },
};
</script>
