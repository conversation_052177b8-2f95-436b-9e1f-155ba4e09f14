<template>
  <ProfileCard>
    <div v-if="listLoading" class="pa-4">
      <div v-for="i in 2" :key="i" class="mt-2">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <v-row v-else class="ma-0">
      <!-- First column: Back button and contact details -->
      <v-col
        cols="12"
        :sm="isMobileView ? 12 : 6"
        :md="isMobileView ? 12 : 4"
        :class="isMobileView ? 'pa-3' : 'pl-3'"
      >
        <v-card
          v-if="isMobileView"
          class="pa-2 my-3"
          elevation="2"
          rounded="lg"
        >
          <v-row>
            <v-col cols="8" class="pr-0">
              <div class="d-flex align-center mb-3">
                <v-btn
                  @click="goBackToList()"
                  icon
                  variant="text"
                  color="primary"
                  size="small"
                  class="mr-1"
                >
                  <v-icon>fas fa-arrow-left</v-icon>
                </v-btn>

                <div class="flex-grow-1 min-width-0">
                  <h2
                    class="text-h6 text-primary font-weight-bold mb-1"
                    style="white-space: normal; word-break: break-word"
                  >
                    {{ candidateName }}
                  </h2>

                  <div class="d-flex align-center mb-1 flex-wrap">
                    <v-icon size="14" color="grey-darken-1" class="mr-2"
                      >fas fa-envelope</v-icon
                    >
                    <span
                      class="text-body-2 text-grey-darken-1"
                      style="white-space: normal; word-break: break-word"
                    >
                      {{ checkNullValue(candidateDetails.Personal_Email) }}
                    </span>
                  </div>

                  <div class="d-flex align-center flex-wrap">
                    <v-icon size="14" color="grey-darken-1" class="mr-2"
                      >fas fa-phone</v-icon
                    >
                    <span
                      v-if="candidateDetails.Mobile_No_Country_Code"
                      class="text-body-2 text-grey-darken-1 pr-1"
                      style="white-space: normal"
                    >
                      {{ candidateDetails.Mobile_No_Country_Code }}
                    </span>
                    <span
                      class="text-body-2 text-grey-darken-1"
                      style="white-space: normal"
                    >
                      {{ checkNullValue(candidateDetails.Mobile_No) }}
                    </span>
                  </div>
                </div>
              </div>
            </v-col>
            <v-col cols="4" class="pl-0">
              <v-btn
                variant="outlined"
                color="primary"
                size="small"
                @click="onChangeCandidate()"
                class="mx-2 mt-2"
              >
                <v-icon size="14" class="mr-1">fas fa-exchange-alt</v-icon>
                Change
              </v-btn></v-col
            >
          </v-row>
        </v-card>

        <!-- Desktop layout -->
        <div v-else class="d-flex align-center">
          <v-icon
            @click="goBackToList()"
            color="primary"
            size="x-large"
            class="mr-2"
          >
            fas fa-angle-left fa-lg
          </v-icon>
          <ContactDetails
            :employeeName="candidateName"
            :empEmailAddress="checkNullValue(candidateDetails.Personal_Email)"
            :empMobileNo="checkNullValue(candidateDetails.Mobile_No)"
            :empMobileNoCode="candidateDetails.Mobile_No_Country_Code"
            :address="formAddress(candidateDetails)"
            :callingFromRecruitment="true"
            :isEdit="false"
            @on-display-change-button="onChangeCandidate()"
            :displayChangeButton="true"
          ></ContactDetails>
        </div>
      </v-col>

      <!-- Second column: Blacklist info and action links -->
      <v-col
        cols="12"
        :sm="isMobileView ? 12 : 6"
        :md="isMobileView ? 12 : 4"
        :class="
          isMobileView
            ? 'pa-3'
            : 'pl-3 d-flex flex-column align-center justify-center'
        "
      >
        <!-- Mobile Action Cards -->
        <div v-if="isMobileView" class="w-100">
          <!-- Blacklist Warning Card -->
          <v-card
            v-if="candidateDetails.Blacklisted?.toLowerCase() === 'yes'"
            color="hover"
            class="pa-2 mb-3 text-center"
          >
            This Candidate has been blacklisted.
            <div
              style="text-decoration: underline"
              @click="blacklistViewForm = true"
              class="text-blue cursor-pointer"
            >
              View More
            </div>
          </v-card>

          <!-- Action Buttons Card -->
          <v-card class="pa-4 mb-3" elevation="2" rounded="lg">
            <div
              class="text-subtitle-2 font-weight-bold mb-3 text-grey-darken-1"
            >
              Quick Actions
            </div>
            <div class="d-flex flex-column gap-2">
              <v-btn
                v-if="
                  recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
                  `no`
                "
                variant="outlined"
                color="success"
                @click="retrieveResumeDetails()"
                size="default"
                block
                rounded="lg"
              >
                <v-icon size="16" class="mr-2">fas fa-file-pdf</v-icon>
                <span class="text-wrap">
                  {{
                    candidateDetails?.Resume
                      ? "View Resume & Attachments"
                      : "Upload Resume"
                  }}
                </span>
              </v-btn>

              <v-btn
                v-if="selectedItem?.Workflow_Task_Id"
                variant="outlined"
                color="primary"
                class="mt-2"
                @click="showWorkFlowModel = true"
                size="default"
                block
                rounded="lg"
              >
                <v-icon size="16" class="mr-2">fas fa-clipboard-list</v-icon>
                <span class="text-wrap"> View Pre-Screening Response</span>
              </v-btn>
            </div>
          </v-card>
        </div>

        <!-- Desktop Layout -->
        <div v-else class="d-flex flex-column align-center">
          <v-card
            v-if="candidateDetails.Blacklisted?.toLowerCase() === 'yes'"
            color="hover"
            class="pa-2 my-3 text-center"
          >
            This Candidate has been blacklisted.
            <div
              style="text-decoration: underline"
              @click="blacklistViewForm = true"
              class="text-blue cursor-pointer"
            >
              View More
            </div>
          </v-card>

          <div class="d-flex flex-column align-center gap-2">
            <v-btn
              v-if="
                recruitmentSettings?.Enable_New_Features?.toLowerCase() === `no`
              "
              variant="text"
              color="green"
              @click="retrieveResumeDetails()"
              class="text-decoration-underline"
            >
              <span class="text-wrap">
                {{
                  candidateDetails?.Resume
                    ? "View Resume & other attachments"
                    : "Upload Resume"
                }}</span
              >
            </v-btn>

            <v-btn
              v-if="selectedItem?.Workflow_Task_Id"
              variant="text"
              color="primary"
              @click="showWorkFlowModel = true"
              class="text-decoration-underline"
            >
              <span class="text-wrap"> View Pre-Screening Response</span>
            </v-btn>
          </div>
        </div>
      </v-col>

      <!-- Third column: Status and stages -->
      <v-col
        cols="12"
        :sm="isMobileView ? 12 : 12"
        :md="isMobileView ? 12 : 4"
        :class="isMobileView ? 'pa-3' : 'd-flex align-center justify-end pr-6'"
      >
        <div :class="isMobileView ? 'w-100' : ''">
          <!-- Mobile Status Management Card -->
          <div v-if="isMobileView" class="w-100">
            <!-- Job Post Name -->
            <div
              v-if="
                selectedItem.Job_Post_Name &&
                recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
                  `yes`
              "
              class="d-flex align-center justify-center my-2"
            >
              <span
                class="text-body-1 text-primary text-decoration-underline cursor-pointer"
                style="white-space: normal; word-break: break-word"
                @click="redirectToJobPost(selectedItem.Job_Post_Id)"
              >
                {{ selectedItem.Job_Post_Name }}
              </span>
            </div>
            <!-- Navigation buttons for mobile -->
            <div v-if="showNavigationButtons" class="d-flex align-center ml-2">
              <v-btn
                @click="navigateToPrevious()"
                :disabled="!canNavigatePrevious"
                icon
                variant="text"
                color="primary"
                size="small"
                class="mr-1"
                :style="!canNavigatePrevious ? 'opacity: 0.5' : ''"
              >
                <v-icon>fas fa-chevron-left</v-icon>
              </v-btn>
              <div class="d-flex flex-column align-center mx-2 text-subtitle-1">
                {{ currentCandidateIndex >= 0 ? currentCandidateIndex + 1 : 0 }}
                of
                {{ sortedCandidateList ? sortedCandidateList?.length : 0 }}
                candidates
              </div>
              <v-btn
                @click="navigateToNext()"
                :disabled="!canNavigateNext"
                icon
                variant="text"
                color="primary"
                size="small"
                :style="!canNavigateNext ? 'opacity: 0.5' : ''"
              >
                <v-icon>fas fa-chevron-right</v-icon>
              </v-btn>
            </div>
            <!-- Duplicate candidate info -->
            <v-alert
              v-if="
                this.selectedItem?.Duplicate_Count &&
                this.selectedItem.Duplicate_Count > 1
              "
              type="info"
              variant="tonal"
              class="mb-3"
              rounded="lg"
            >
              <div class="d-flex align-center">
                <div class="flex-grow-1">
                  <div class="text-subtitle-2 font-weight-bold">
                    Duplicate Found
                  </div>
                  <div class="text-body-2">
                    Found in {{ this.selectedItem.Duplicate_Count }} jobs or
                    talent pools
                  </div>
                </div>
                <v-btn
                  variant="text"
                  color="info"
                  size="small"
                  @click="duplicateCandidates = true"
                >
                  View Details
                </v-btn>
              </div>
            </v-alert>

            <!-- Status Management Card -->
            <div class="d-flex align-center">
              <v-card
                v-if="
                  !candidateDetails?.Talent_Pool_Id &&
                  candidateDetails?.Archived?.toLowerCase() === 'no'
                "
                class="pa-4"
                elevation="2"
                rounded="lg"
                width="100%"
              >
                <div class="d-flex flex-column gap-3">
                  <div
                    v-if="
                      recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
                      `no`
                    "
                  >
                    <CustomSelect
                      :items="stageList"
                      label="Hiring Stage"
                      item-value="value"
                      item-title="value"
                      style="width: 100%"
                      :isAutoComplete="true"
                      :itemSelected="selectedStage"
                      density="comfortable"
                      :disabled="
                        !highRole || this.selectedStage == 'Preboarding'
                      "
                      :disabledValue="disableStageList"
                      @selected-item="onSelectStage($event)"
                    />

                    <CustomSelect
                      ref="candidateStatusRef"
                      :items="candidateStatusList"
                      itemValue="Id"
                      itemTitle="Status"
                      label="Candidate Status"
                      density="comfortable"
                      style="width: 100%"
                      :isAutoComplete="true"
                      :isLoading="statusAccessLoading"
                      :disabled="
                        !highRole || this.selectedStage == 'Preboarding'
                      "
                      :disabled-value="disabledStatusList"
                      :itemSelected="candidateStatus"
                      @selected-item="checkBlacklist($event)"
                    />
                  </div>
                  <div v-else class="d-flex align-center">
                    <CustomSelect
                      ref="unifiedStatusRefDesktop"
                      :items="unifiedStageStatusList"
                      itemValue="Id"
                      itemTitle="Status"
                      label="Candidate Status"
                      density="comfortable"
                      style="width: 100%"
                      :isAutoComplete="true"
                      :isLoading="statusAccessLoading"
                      :disabled="
                        !highRole || this.selectedStage == 'Preboarding'
                      "
                      :disabled-value="unifiedDisabledValues"
                      :itemSelected="selectedUnifiedValue"
                      subText="Stage"
                      subTextTitle="Stage"
                      class="pa-2"
                      @selected-item="onSelectUnifiedStatus($event)"
                    />
                  </div>
                </div>
              </v-card>
              <div class="d-flex align-center mb-4">
                <!-- New Candidate ActionMenu component usage -->
                <CandidateActionMenu
                  v-if="presentActions"
                  :candidate="candidateDetails"
                  :candidate-id-selected="candidateIdSelected"
                  :parent-tab-name="parentTabName"
                  :form-access="formAccess"
                  :is-recruiter="isRecruiter"
                  :selected-stage="selectedStage"
                  :recruitment-settings="recruitmentSettings"
                  :candidate-no-action-status-list="candidateNoActionStatusList"
                  @candidate-data-refresh="$emit(`edit-updated`)"
                  @candidate-view-close="handleCandidateViewClose"
                />
              </div>
            </div>
          </div>

          <!-- Desktop Layout -->
          <div v-else>
            <!-- Job Post Name -->
            <div
              v-if="
                selectedItem.Job_Post_Name &&
                recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
                  `yes`
              "
              class="d-flex align-center justify-end pr-4"
            >
              <span
                class="text-body-1 text-primary text-decoration-underline cursor-pointer"
                style="white-space: normal; word-break: break-word"
                @click="redirectToJobPost(selectedItem.Job_Post_Id)"
              >
                {{ selectedItem.Job_Post_Name }}
              </span>
            </div>
            <!-- Navigation buttons for desktop -->
            <div
              v-if="showNavigationButtons"
              class="d-flex align-center justify-end"
            >
              <v-btn
                @click="navigateToPrevious()"
                :disabled="!canNavigatePrevious"
                icon
                variant="text"
                color="primary"
                size="small"
                class="mr-1"
                :style="!canNavigatePrevious ? 'opacity: 0.5' : ''"
              >
                <v-icon>fas fa-chevron-left</v-icon>
              </v-btn>
              <div class="d-flex flex-column align-center mx-2 text-subtitle-1">
                {{ currentCandidateIndex >= 0 ? currentCandidateIndex + 1 : 0 }}
                of
                {{ sortedCandidateList ? sortedCandidateList?.length : 0 }}
                candidates
              </div>
              <v-btn
                @click="navigateToNext()"
                :disabled="!canNavigateNext"
                icon
                variant="text"
                color="primary"
                size="small"
                class="ml-1"
                :style="!canNavigateNext ? 'opacity: 0.5' : ''"
              >
                <v-icon>fas fa-chevron-right</v-icon>
              </v-btn>
            </div>
            <!-- Duplicate candidate info -->
            <div class="d-flex justify-end pt-5">
              <v-card
                v-if="
                  this.selectedItem?.Duplicate_Count &&
                  this.selectedItem.Duplicate_Count > 1
                "
                class="pa-2 mb-5"
                color="hover"
              >
                <v-icon color="primary" class="mr-1">fas fa-info-circle</v-icon>
                <span>
                  Candidate found in
                  {{ this.selectedItem.Duplicate_Count }} jobs or talent pools.
                </span>
                <span
                  @click="duplicateCandidates = true"
                  class="text-primary cursor-pointer mb-2"
                >
                  View Details
                </span>
              </v-card>
            </div>

            <!-- Status and stage selectors -->
            <div class="d-flex align-center justify-end">
              <div
                class="d-flex justify-end flex-wrap"
                v-if="
                  !candidateDetails?.Talent_Pool_Id &&
                  candidateDetails?.Archived?.toLowerCase() === 'no'
                "
              >
                <div
                  v-if="
                    recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
                    `no`
                  "
                  class="d-flex flex-wrap"
                >
                  <CustomSelect
                    :items="stageList"
                    label="Stages"
                    item-value="value"
                    item-title="value"
                    style="width: 200px"
                    :isAutoComplete="true"
                    :itemSelected="selectedStage"
                    density="comfortable"
                    :disabled="!highRole || this.selectedStage == 'Preboarding'"
                    :disabledValue="disableStageList"
                    class="pa-2"
                    @selected-item="onSelectStage($event)"
                  />
                  <CustomSelect
                    ref="candidateStatusRef"
                    :items="candidateStatusList"
                    itemValue="Id"
                    itemTitle="Status"
                    label="Candidate Status"
                    density="comfortable"
                    style="width: 200px"
                    :isAutoComplete="true"
                    :isLoading="statusAccessLoading"
                    :disabled="!highRole || this.selectedStage == 'Preboarding'"
                    :disabled-value="disabledStatusList"
                    :itemSelected="candidateStatus"
                    class="pa-2"
                    @selected-item="checkBlacklist($event)"
                  />
                </div>
                <div v-else class="d-flex align-center">
                  <CustomSelect
                    class="mt-2"
                    ref="unifiedStatusRefDesktop"
                    :items="unifiedStageStatusList"
                    itemValue="Id"
                    itemTitle="Status"
                    label="Candidate Status"
                    density="comfortable"
                    style="width: 100%"
                    min-width="250px"
                    :isAutoComplete="true"
                    :isLoading="statusAccessLoading"
                    :disabled="!highRole || this.selectedStage == 'Preboarding'"
                    :disabled-value="unifiedDisabledValues"
                    :itemSelected="selectedUnifiedValue"
                    subText="Stage"
                    subTextTitle="Stage"
                    @selected-item="onSelectUnifiedStatus($event)"
                  />
                </div>
              </div>
              <div class="d-flex align-center mb-4">
                <!-- New Candidate ActionMenu component usage -->
                <CandidateActionMenu
                  v-if="presentActions"
                  :candidate="candidateDetails"
                  :candidate-id-selected="candidateIdSelected"
                  :parent-tab-name="parentTabName"
                  :form-access="formAccess"
                  :is-recruiter="isRecruiter"
                  :selected-stage="selectedStage"
                  :recruitment-settings="recruitmentSettings"
                  :candidate-no-action-status-list="candidateNoActionStatusList"
                  @candidate-data-refresh="$emit(`edit-updated`)"
                  @candidate-view-close="handleCandidateViewClose"
                />
              </div>
            </div>
          </div>
        </div>
      </v-col>

      <UpdateCandidateStatusOverlayForm
        v-if="showCustomEmail"
        ref="customEmail"
        :candidateId="candidateIdSelected"
        :typeOfTemplate="templateType"
        :typeOfSchedule="
          templateType == 'InitiateBackgroundInvestigation' ? '' : 'noncalendar'
        "
        :templateData="templateData"
        :emailRecievers="templateEmail"
        :templateEmail="[candidateDetails.Personal_Email]"
        :selectedStatus="selectedStatus"
        :selectedStage="selectedStage"
        @custom-email-sent="customEmailSent"
        @custom-email-cancel="onCustomEmailCancel"
      ></UpdateCandidateStatusOverlayForm>
    </v-row>
  </ProfileCard>
  <AppLoading v-if="isLoading"></AppLoading>
  <FilePreviewAndEdit
    v-if="openModal"
    :fileName="candidateDetails.Resume"
    :otherAttachment="candidateDetails.Other_Attachments"
    :heading="candidateDetails.Resume ? 'Attachment' : 'Resume Upload'"
    :isUpload="candidateDetails.Resume ? false : true"
    folderName="resume"
    :get-cloudfront-url="true"
    :fileSize="candidateDetails.Resume_File_Size"
    @close-preview-modal="openModal = false"
    @update-resume-details="uploadFileContents"
  ></FilePreviewAndEdit>

  <DuplicateCandidates
    v-if="duplicateCandidates"
    :candidateDetails="candidateDetails"
    :candidateId="candidateIdSelected"
    :candidateName="candidateDetails.First_Name"
    :jobPostId="candidateDetails.Job_Post_Id"
    :statusId="candidateDetails.Status_Id"
    :jobTitle="candidateDetails.Job_Post_Name"
    :parentTabName="parentTabName"
    @close-duplicate-candidates-window="duplicateCandidates = false"
    @on-display-change-button="onChangeCandidate()"
  />

  <v-dialog
    :model-value="openFormInModal"
    :class="isMobileView ? '' : 'pl-4'"
    :width="isMobileView ? '95vw' : '1000'"
    :max-width="isMobileView ? '95vw' : '1000px'"
    :fullscreen="isMobileView"
    @click:outside="showWorkFlowModel = false"
  >
    <FormRender
      :form-data="formJsonData"
      :formResponseId="formResponseId"
      :conversationalId="conversationalId"
      :pdfName="
        candidateDetails?.Candidate_First_Name +
        ' ' +
        (candidateDetails?.Candidate_Middle_Name
          ? candidateDetails?.Candidate_Middle_Name + ' '
          : '') +
        candidateDetails?.Candidate_Last_Name +
        '-' +
        candidateIdSelected
      "
      :fromJobRecritment="true"
      :show-approval="false"
      @close-form-render="showWorkFlowModel = false"
    />
  </v-dialog>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="You are updating the overall status of the candidate and the decision is final. Are you sure to update the status of the candidate?"
    @close-warning-modal="cancelUpdate()"
    @accept-modal="confirmUpdate()"
  ></AppWarningModal>
  <AppWarningModal
    v-if="blacklistWarningModal"
    :open-modal="blacklistWarningModal"
    confirmation-heading="This candidate has been blacklisted. Are you sure you want to proceed ?"
    icon-name=""
    @close-warning-modal="blacklistWarningModal = false"
    @accept-modal="onChangeRoundStatus(changedStatus)"
  ></AppWarningModal>
  <EmployeesListModal
    v-if="showEmpListModal"
    :show-modal="showEmpListModal"
    :employeesList="candidateList"
    :showFilterSearch="true"
    :showFilter="false"
    modalTitle="Job Candidate(s)"
    selectStrategy="single"
    employeeIdKey="Candidate_Id"
    userDefinedEmpIdKey=""
    employeeNameLabel="Candidate Name"
    employeeNameKey="First_Name"
    deptNameLabel="Status"
    deptNameKey="Candidate_Status"
    designationNameLabel="Job Title"
    designationKey="Job_Post_Name"
    :isApplyFilter="false"
    @on-select-employee="onSelectCandidate($event)"
    @close-modal="showEmpListModal = false"
  ></EmployeesListModal>
  <file-preview-modal
    v-if="showBlacklistDocuments"
    :fileName="blacklistFileName"
    :folderName="'Blacklist Candidate'"
    :current="selectedDocumentIndex"
    :length="blacklistDocument.length"
    @prev-document="changeSelectedDocument(-1)"
    @next-document="changeSelectedDocument(1)"
    @close-preview-modal="showBlacklistDocuments = false"
  ></file-preview-modal>
  <v-overlay v-model="blacklistViewForm" class="d-flex justify-end">
    <v-card height="100vh" :width="isMobileView ? '100vw' : '35vw'">
      <div
        style="height: 60px; width: 100%"
        class="bg-primary pa-3 d-flex align-center"
      >
        <span class="text-h6 text-white font-weight-bold">
          {{ candidateDetails?.Candidate_First_Name }}
          {{ candidateDetails?.Candidate_Last_Name }}
        </span>
        <v-spacer></v-spacer>
        <v-icon color="white" size="25" @click="blacklistViewForm = false">
          fas fa-times
        </v-icon>
      </div>
      <v-row class="pa-4">
        <v-col cols="12" md="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Reason for Blacklisting
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(candidateDetails.Blacklisted_Reason) }}
          </p>
        </v-col>
        <v-col cols="12" md="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            Comment by Blacklister
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(candidateDetails.Blacklisted_Comments) }}
          </p>
        </v-col>
        <v-col cols="12" md="6">
          <p class="text-subtitle-1 text-grey-darken-1">Blacklisted By</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ checkNullValue(candidateDetails.Blacklisted_By_Name) }}
          </p>
        </v-col>
        <v-col cols="12" md="6">
          <p class="text-subtitle-1 text-grey-darken-1">Blacklisted On</p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ convertUTCToLocal(candidateDetails.Blacklisted_On) }}
          </p>
        </v-col>
        <v-col cols="12" v-if="blacklistDocument.length > 0">
          <p class="text-subtitle-1 text-grey-darken-1">Document(s)</p>
          <p
            v-for="(doc, index) in blacklistDocument"
            :key="index"
            class="text-subtitle-1 font-weight-regular text-blue cursor-pointer mb-2"
            style="text-decoration: underline"
            @click="viewBlacklistedDocuments(index)"
          >
            {{ formattedFileName(doc.filePath) }}
          </p>
        </v-col>
      </v-row>
    </v-card>
  </v-overlay>
</template>
<script>
import ContactDetails from "@/components/custom-components/ContactDetails.vue";
import moment from "moment";
import { defineAsyncComponent } from "vue";
const FilePreviewAndEdit = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewAndEdit.vue")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import DuplicateCandidates from "./DuplicateCandidates.vue";
import { GET_DYNAMIC_FORM_DETAILS } from "@/graphql/workflow/approvalManagementQueries.js";
import {
  GET_STATUS_LIST,
  UPDATE_JOB_CANDIDATE_DETAILS,
  UPDATE_CANDIDATE_STATUS,
  GET_ROLE_BASED_JOB_POST_MEMBER,
} from "@/graphql/recruitment/recruitmentQueries.js";
const EmployeesListModal = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeesListModal.vue")
);
const FormRender = defineAsyncComponent(() =>
  import("@/views/workflow/approval-management/FormRender.vue")
);
import CandidateActionMenu from "@/views/recruitment/job-candidates/job-candidate-actions/CandidateActionMenu.vue";

import UpdateCandidateStatusOverlayForm from "./UpdateCandidateStatusOverlayForm.vue";

export default {
  name: "JobCandidateTopCard",
  components: {
    ContactDetails,
    FilePreviewAndEdit,
    CustomSelect,
    DuplicateCandidates,
    // CustomEmail,
    EmployeesListModal,
    FormRender,
    FilePreviewModal,
    CandidateActionMenu,
    UpdateCandidateStatusOverlayForm,
  },
  props: {
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    duplicateCandidateDetails: {
      type: Array,
      required: true,
    },
    candidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    recruitmentSettings: {
      type: Object,
      default: function () {
        return {};
      },
    },
    selectedItem: {
      type: Object,
      default: function () {
        return {};
      },
    },
    listLoading: {
      type: Boolean,
      default: false,
    },
    candidateList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    originalList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    currentSortedItems: {
      type: Array,
      default: function () {
        return [];
      },
    },
    candidateIdSelected: {
      type: Number,
      required: true,
    },
    candidateJobPostIdSelected: {
      type: Number,
      default: 0,
    },
    isRecruiter: {
      type: String,
      default: "No",
    },
    parentTabName: {
      type: String,
      default: "",
    },
    candidateNoActionStatusList: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  emits: [
    "close-view-form",
    "edit-updated",
    "status-updated",
    "on-change-candidate",
    "check-candidate-actions",
  ],
  data() {
    return {
      retrievedFileName: "",
      duplicateCandidates: false,
      openModal: false,
      isLoading: false,
      selectedEmployeeResume: {
        File_Name: "",
      },
      candidateStatusList: [],
      stageList: [],
      stageLoader: false,
      selectedStage: "",
      flowList: [],
      openWarningModal: false,
      changedStatus: "",
      candidateStatus: "",
      showCustomEmail: false,
      selectedStatus: 0,
      showEmpListModal: false,
      templateType: "",
      loginEmpRole: "",
      statusAccessLoading: false,
      // form presentation
      showWorkFlowModel: false,
      formResponseId: null,
      conversationalId: 0,
      formJsonData: {},
      templateEmail: [],
      emailTemplateType: "",
      showBlacklistDocuments: false,
      blacklistFileName: "",
      blacklistViewForm: false,
      selectedDocumentIndex: null,
      blacklistDocument: [],
      blacklistWarningModal: false,
    };
  },
  computed: {
    presentActions() {
      return (
        this.recruitmentSettings?.Enable_New_Features?.toLowerCase() === `yes`
      );
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    highRole() {
      return (
        (this.loginEmpRole === "Admin" ||
          this.loginEmpRole === "Recruiter" ||
          this.selectedItem?.Job_Post_Creator == this.loginEmployeeId ||
          this.loginEmpRole === "Hiring Manager") &&
        this.isRecruiter.toLowerCase() === "yes"
      );
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    addressLine1() {
      let organization = {};
      if (this.fieldForce) {
        organization = this.$store.state.orgDetails.serviceProvider;
      } else {
        organization = this.$store.state.orgDetails.organization;
      }
      let line1 = [];
      if (organization.street1) {
        line1.push(organization.street1);
      }
      if (organization.street2) {
        line1.push(organization.street2);
      }
      return line1.length > 0 ? line1.join(",") : "";
    },
    addressLine2() {
      const { organization } = this.$store.state.orgDetails;
      let line2 = [];
      if (organization.city) {
        line2.push(organization.city);
      }
      if (organization.state) {
        line2.push(organization.state);
      }
      if (organization.country) {
        line2.push(organization.country);
      }
      if (organization.pincode) {
        line2.push(organization.pincode);
      }
      return line2.length > 0 ? line2.join(",") : "";
    },
    loginEmployeeName() {
      return (
        this.$store.state.userDetails.employeeFirstName +
        " " +
        this.$store.state.userDetails.employeeLastName
      );
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    loginEmpEmail() {
      return this.$store.state.userDetails.employeeEmail;
    },
    loginEmpDesignation() {
      return this.$store.state.userDetails.designationName;
    },
    templateData() {
      let templateData = this.candidateDetails;
      //Include other data
      let candidateDetails = [];
      if (this.candidateDetails.Candidate_Last_Name) {
        candidateDetails.push(this.candidateDetails.Candidate_Last_Name);
        candidateDetails.push(" ");
      }
      if (this.candidateDetails.Candidate_First_Name) {
        candidateDetails.push(this.candidateDetails.Candidate_First_Name);
      }
      if (this.candidateDetails.Candidate_Middle_Name) {
        candidateDetails.push(
          " " + this.candidateDetails.Candidate_Middle_Name
        );
      }
      templateData.Candidate_Name = candidateDetails.join("");
      templateData.Company_Name = this.companyName;
      templateData.Recruiter_Name = this.loginEmployeeName;
      templateData.Job_Post_Name = this.candidateDetails.Job_Post_Name;
      templateData.Company_Address_1 = this.addressLine1;
      templateData.Company_Address_2 = this.addressLine2;
      templateData.emailTemplateType = this.emailTemplateType;
      templateData.Designation = this.loginEmpDesignation;
      templateData.Org_Name = this.companyName;
      return templateData;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    disableStageList() {
      // applied - can shortlist or reject
      if (this.candidateDetails.Hiring_Stage == "Sourced") {
        return [
          "Preboarding",
          "Hired",
          "Interview",
          "Background Investigation",
        ];
      } else if (this.candidateDetails.Hiring_Stage == "Screening") {
        return [
          "Preboarding",
          "Hired",
          "Sourced",
          "Interview",
          "Background Investigation",
        ];
      } else if (this.candidateDetails.Hiring_Stage == "Interview") {
        return [
          "Preboarding",
          "Screening",
          "Sourced",
          "Background Investigation",
        ];
      } else if (this.candidateDetails.Hiring_Stage == "Hired") {
        return ["Preboarding", "Screening", "Sourced", "Interview"];
      } else if (this.candidateDetails.Hiring_Stage == "Archived") {
        return [
          "Preboarding",
          "Screening",
          "Sourced",
          "Interview",
          "Hired",
          "Background Investigation",
        ];
      } else if (
        this.candidateDetails.Hiring_Stage == "Background Investigation"
      ) {
        return ["Preboarding", "Screening", "Sourced", "Interview", "Hired"];
      } else {
        return [
          "Screening",
          "Sourced",
          "Interview",
          "Hired",
          "Background Investigation",
        ];
      }
    },
    disabledStatusList() {
      let disabledStatusList = [];
      if (this.candidateDetails.Hiring_Stage == "Interview") {
        if (
          this.candidateDetails?.MPP_Position_Type?.toLowerCase() ===
            "new position" &&
          this.candidateDetails?.MPP_Status?.toLowerCase() !==
            "to changes approved"
        ) {
          disabledStatusList.push("Scheduled For Interview", "Hired");
        } else {
          disabledStatusList.push("Scheduled For Interview");
        }
      }
      if (this.candidateDetails.Hiring_Stage == "Background Investigation") {
        disabledStatusList.push("Background Investigation Initiated");
      } else {
        disabledStatusList.push(
          "Background Investigation Success",
          "Background Investigation Failed"
        );
      }
      if (
        this.candidateDetails?.MPP_Position_Type?.toLowerCase() ===
          "new position" &&
        this.candidateDetails?.MPP_Status?.toLowerCase() !==
          "to changes approved"
      ) {
        disabledStatusList.push("Hired");
      }
      return disabledStatusList;
    },
    unifiedStageStatusList() {
      if (!this.flowList || !this.stageList || this.stageList.length === 0) {
        return [];
      }

      // Get disabled stages and statuses for filtering
      const disabledStages = this.disableStageList;
      const disabledStatuses = this.disabledStatusList;

      // Define the stage order to maintain consistency
      const stageOrder = this.stageList.map((stage) => stage.value);

      let unifiedList = [];

      // Process stages in the defined order
      stageOrder.forEach((stageName) => {
        // Skip disabled stages entirely
        if (disabledStages.includes(stageName)) {
          return;
        }

        if (this.flowList[stageName] && this.flowList[stageName].length > 0) {
          // Add statuses for this stage
          this.flowList[stageName].forEach((status) => {
            // Include the current candidate's status even if it's normally disabled
            const isCurrentStatus = status.Id === this.candidateStatus;

            // Skip individually disabled statuses unless it's the current status
            if (disabledStatuses.includes(status.Status) && !isCurrentStatus) {
              return;
            }

            unifiedList.push({
              Id: status.Id,
              Status: status.Status,
              Stage: status.Stage,
              Stage_Id: status.Stage_Id,
              // For the unified dropdown display
              title: status.Status,
              value: status.Id,
              raw: {
                stageName: status.Stage,
                statusId: status.Id,
                stageId: status.Stage_Id,
              },
            });
          });
        }
      });

      return unifiedList;
    },
    unifiedDisabledValues() {
      // Return status IDs that should be disabled but visible in the dropdown
      const disabledStatuses = this.disabledStatusList;
      let disabledIds = [];

      // Find status IDs that match disabled status names and are included in the unified list
      this.unifiedStageStatusList.forEach((item) => {
        if (disabledStatuses.includes(item.Status)) {
          disabledIds.push(item.Id);
        }
      });

      return disabledIds;
    },
    selectedUnifiedValue() {
      // Return the current candidate status ID for pre-selection
      return this.candidateStatus;
    },
    openFormInModal() {
      if (this.showWorkFlowModel) {
        return true;
      }
      return false;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formattedFileName() {
      return (fileName) => {
        if (fileName) {
          return fileName.split("?")[3];
        }
      };
    },
    shouldShowCustomEmail() {
      return (
        (this.selectedStage?.toLowerCase() == "archived" ||
          this.selectedStage?.toLowerCase() == "hired" ||
          this.selectedStage?.toLowerCase() == "background investigation" ||
          this.changedStatusObj?.Status.toLowerCase() ==
            "background investigation failed" ||
          this.changedStatusObj?.Status.toLowerCase() ==
            "background investigation initiated") &&
        this.candidateDetails.Personal_Email &&
        this.candidateDetails.Personal_Email.length
      );
    },
    candidateName() {
      let candidateName =
        this.candidateDetails.Candidate_First_Name ||
        this.candidateDetails.Candidate_Middle_Name ||
        this.candidateDetails.Candidate_Last_Name
          ? [
              this.candidateDetails.Candidate_First_Name,
              this.candidateDetails.Candidate_Middle_Name,
              this.candidateDetails.Candidate_Last_Name,
            ]
              .filter(Boolean)
              .join(" ")
          : "-";
      return candidateName;
    },
    sortedCandidateList() {
      return this.currentSortedItems.length > 0
        ? this.currentSortedItems
        : this.originalList;
    },
    // Navigation computed properties
    currentCandidateIndex() {
      if (
        !this.sortedCandidateList ||
        this.sortedCandidateList.length === 0 ||
        !this.candidateIdSelected
      ) {
        return -1;
      }
      try {
        const index = this.sortedCandidateList.findIndex(
          (candidate) =>
            candidate && candidate.Candidate_Id === this.candidateIdSelected
        );
        return index;
      } catch {
        return -1;
      }
    },
    canNavigatePrevious() {
      return (
        this.currentCandidateIndex > 0 &&
        this.sortedCandidateList &&
        this.sortedCandidateList.length > 1
      );
    },
    canNavigateNext() {
      return (
        this.currentCandidateIndex >= 0 &&
        this.currentCandidateIndex < this.sortedCandidateList.length - 1 &&
        this.sortedCandidateList &&
        this.sortedCandidateList.length > 1
      );
    },
    showNavigationButtons() {
      return (
        this.recruitmentSettings?.Enable_New_Features?.toLowerCase() ===
          "yes" &&
        this.sortedCandidateList &&
        Array.isArray(this.sortedCandidateList) &&
        this.sortedCandidateList.length > 1 &&
        this.currentCandidateIndex >= 0
      );
    },
  },
  watch: {
    candidateDetails(val) {
      this.selectedStage = val.Hiring_Stage;
      this.candidateStatusList = this.flowList[val.Hiring_Stage];
      if (val) {
        this.blacklistDocument = val.Blacklisted_Attachment_File_Name
          ? JSON.parse(val.Blacklisted_Attachment_File_Name)
          : [];
      }
    },
    listLoading(val) {
      if (!val) {
        this.candidateStatus = this.candidateDetails.Status_Id;

        // Initialize dropdown refs for both old and new functionality
        this.$nextTick(() => {
          // Update unified dropdown refs if they exist
          if (this.$refs.unifiedStatusRef) {
            this.$refs.unifiedStatusRef.selectedItem =
              this.candidateDetails.Status_Id;
          }
          if (this.$refs.unifiedStatusRefDesktop) {
            this.$refs.unifiedStatusRefDesktop.selectedItem =
              this.candidateDetails.Status_Id;
          }

          // Update separate dropdown ref for old functionality
          if (this.$refs.candidateStatusRef) {
            this.$refs.candidateStatusRef.selectedItem =
              this.candidateDetails.Status_Id;
          }
        });
      }
    },
    candidateJobPostIdSelected(val) {
      if (val && this.loginEmpRole != "Admin") {
        this.getRoleBasedJobPostMember();
      }
    },
  },
  mounted() {
    let isFormAdmin = this.formAccess.admin === "admin";
    if (isFormAdmin) {
      this.loginEmpRole = "Admin";
    } else if (this.candidateJobPostIdSelected) {
      this.getRoleBasedJobPostMember();
    }
    this.getStageList();
    if (
      this.selectedItem?.Dynamic_Form_Id &&
      this.selectedItem?.Workflow_Task_Id
    )
      this.retriveDynamicForm();
    this.duplicateCandidateDetails;
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    getRoleBasedJobPostMember() {
      let vm = this;
      vm.loginEmpRole = "";
      vm.statusAccessLoading = true;
      vm.$apollo
        .query({
          query: GET_ROLE_BASED_JOB_POST_MEMBER,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            jobPostId: parseInt(vm.candidateJobPostIdSelected),
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getRoleBasedJobPostMember &&
            !response.data.getRoleBasedJobPostMember.errorCode.length
          ) {
            // Recruiters
            let recruiters = response.data.getRoleBasedJobPostMember.recruiters;
            let filteredRecruiter = recruiters.filter(
              (el) => el.Employee_Id == vm.loginEmployeeId
            );
            if (filteredRecruiter && filteredRecruiter.length > 0) {
              vm.loginEmpRole = "Recruiter";
            }
            // Hiring Managers
            if (!vm.loginEmpRole) {
              let hiringManagers =
                response.data.getRoleBasedJobPostMember.hiringManager;
              let filteredHiringManager = hiringManagers.filter(
                (el) => el.Employee_Id == vm.loginEmployeeId
              );
              if (filteredHiringManager && filteredHiringManager.length > 0) {
                vm.loginEmpRole = "Hiring Manager";
              }
            }
            vm.statusAccessLoading = false;
          } else {
            vm.loginEmpRole = "";
            vm.statusAccessLoading = false;
          }
        })
        .catch(() => {
          vm.loginEmpRole = "";
          vm.statusAccessLoading = false;
        });
    },
    goBackToList() {
      this.$emit("close-view-form");
    },
    onSelectCandidate(candidate) {
      this.$emit("on-change-candidate", candidate);
      this.showEmpListModal = false;
    },
    onChangeCandidate() {
      this.showEmpListModal = true;
    },
    // Navigation methods
    navigateToPrevious() {
      // Validate preconditions
      if (
        !this.canNavigatePrevious ||
        !this.sortedCandidateList ||
        !Array.isArray(this.sortedCandidateList)
      )
        return;

      const previousIndex = this.currentCandidateIndex - 1;

      if (previousIndex < 0) return;

      const previousCandidate = this.sortedCandidateList[previousIndex];

      if (!previousCandidate || !previousCandidate.Candidate_Id) return;

      this.$emit("on-change-candidate", previousCandidate);
    },
    navigateToNext() {
      // Validate preconditions
      if (
        !this.canNavigateNext ||
        !this.sortedCandidateList ||
        !Array.isArray(this.sortedCandidateList)
      )
        return;

      const nextIndex = this.currentCandidateIndex + 1;

      if (nextIndex >= this.sortedCandidateList.length) return;

      const nextCandidate = this.sortedCandidateList[nextIndex];

      if (!nextCandidate || !nextCandidate.Candidate_Id) return;

      this.$emit("on-change-candidate", nextCandidate);
    },
    retriveDynamicForm() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_DYNAMIC_FORM_DETAILS,
          variables: {
            envelope: {
              orgCode: vm.orgCode,
              loggedInUserId: vm.loginEmployeeId,
            },
            dynamicFormId: parseInt(vm.selectedItem?.Dynamic_Form_Id),
            workflowTaskId: vm.selectedItem?.Workflow_Task_Id,
          },
          client: "apolloClientZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getWorkflowTaskDynamicFormDetails &&
            response.data.getWorkflowTaskDynamicFormDetails.result
          ) {
            const { dynamicFormTemplates, dynamicFormResponse } =
              response.data.getWorkflowTaskDynamicFormDetails.result;
            let formJson = "";
            if (dynamicFormResponse) {
              // response of the form -- empty if it is not submitted before, or the previous response is returned
              formJson = dynamicFormResponse.formResponse;
            } else {
              // form template for new data
              formJson = dynamicFormTemplates.template;
            }
            vm.formJsonData = JSON.parse(formJson);
            vm.conversationalId = dynamicFormTemplates.conversational;
            vm.formResponseId = dynamicFormResponse
              ? parseInt(dynamicFormResponse.formResponseId)
              : null;
            vm.isLoading = false;
          } else {
            vm.handleFormRetrieveError();
          }
        })
        .catch(() => {
          vm.handleFormRetrieveError();
        });
    },
    handleFormRetrieveError() {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message:
          "Something went wrong while retrieving the form details. Please try after some time.",
      };
      this.showAlert(snackbarData);
    },
    formAddress(details) {
      let fullAddress = [];
      if (details.pApartment_Name) {
        fullAddress.push(details.pApartment_Name);
      }
      if (details.pStreet_Name) {
        fullAddress.push(details.pStreet_Name);
      }
      if (details.pCity) {
        fullAddress.push(details.pCity);
      }
      if (details.pState) {
        fullAddress.push(details.pState);
      }
      if (details.Country_Name) {
        fullAddress.push(details.Country_Name);
      }
      if (details.pPincode) {
        fullAddress.push(details.pPincode);
      }
      return fullAddress.join(", ");
    },
    retrieveResumeDetails() {
      let vm = this;
      vm.openModal = true;
    },
    async updateCandidateDetails(fileProperties) {
      let vm = this;
      vm.isLoading = true;
      if (fileProperties && fileProperties.length) {
        let preLocations = [];
        if (
          this.candidateDetails.Preferred_Location &&
          this.candidateDetails.Preferred_Location.length > 0
        ) {
          for (
            var l = 0;
            l < this.candidateDetails.Preferred_Location.length;
            l++
          ) {
            preLocations.push(
              this.candidateDetails.Preferred_Location[l].Location_Id
            );
          }
        }
        let langKnown = [];
        if (
          this.candidateDetails.Lang_Known &&
          this.candidateDetails.Lang_Known.length > 0
        ) {
          langKnown = this.candidateDetails.Lang_Known.map((lang) => ({
            langKnown: lang.Lang_Id,
            langSpoken: lang.Lang_Spoken || null,
            langReadWrite: lang.Lang_Read_Write || null,
            langProficiency: lang.Lang_Proficiency || null,
          }));
        }
        let educationArray = [];
        let editedEducationArray = vm.candidateDetails.Candidate_Education;
        if (editedEducationArray && editedEducationArray.length > 0) {
          for (let education of editedEducationArray) {
            let eObj = {
              educationType: education.Education_Type_Id,
              institute: education.Institute_Name,
              speacialisation: education.Specialisation,
              yearOfPassing: education.Year_Of_Passing
                ? parseInt(education.Year_Of_Passing)
                : null,
              percentage: education.Percentage
                ? parseFloat(education.Percentage)
                : 0,
              institutionId: education.Institution_Id
                ? education.Institution_Id
                : 0,
              specializationId: education.Specialization_Id
                ? education.Specialization_Id
                : 0,
              startDate: education.Start_Date,
              endDate: education.End_Date,
              city: education.City,
              state: education.State,
              country: education.Country,
            };
            educationArray.push(eObj);
          }
        }
        let certificateArray = [];
        let editedCertificationArray =
          vm.candidateDetails.Candidate_Certifications;
        if (editedCertificationArray && editedCertificationArray.length > 0) {
          for (let certificate of editedCertificationArray) {
            let cObj = {
              certificationName: certificate.Certification_Name,
              receivedDate: certificate.Received_Date,
              certificateReceivedFrom: certificate.Certificate_Received_From,
              certificationFileName: "",
              ranking: certificate.ranking || null,
            };
            certificateArray.push(cObj);
          }
        }
        let experienceArray = [];
        let editedExpArray = vm.candidateDetails.Candidate_Experience;
        if (editedExpArray && editedExpArray.length > 0) {
          for (let experience of editedExpArray) {
            let exObj = {
              companyName: experience.Prev_Company_Name,
              designation: experience.Designation,
              startDate: moment(experience.Start_Date, "YYYY/MM/DD").isValid()
                ? moment(experience.Start_Date, "YYYY/MM/DD").format(
                    "DD/MM/YYYY"
                  )
                : null,
              endDate: moment(experience.End_Date, "YYYY/MM/DD").isValid()
                ? moment(experience.End_Date, "YYYY/MM/DD").format("DD/MM/YYYY")
                : null,
              companyLocation: experience.Prev_Company_Location || null,
              referenceDetails: experience.References,
            };
            experienceArray.push(exObj);
          }
        }
        let dependent = [];
        if (vm.candidateDetails.Father_Name) {
          dependent.push(vm.candidateDetails.Father_Name);
        }
        if (vm.candidateDetails.Mother_Name) {
          dependent.push(vm.candidateDetails.Mother_Name);
        }
        await vm.$apollo
          .mutate({
            mutation: UPDATE_JOB_CANDIDATE_DETAILS,
            variables: {
              candidateId: vm.candidateIdSelected,
              salutation: vm.candidateDetails.Salutation,
              firstName: vm.candidateDetails.Candidate_First_Name,
              lastName: vm.candidateDetails.Candidate_Last_Name,
              gender: vm.candidateDetails.Gender,
              genderId: vm.candidateDetails.Gender_Id,
              dob: vm.candidateDetails.DOB
                ? moment(vm.candidateDetails.DOB).format("YYYY-MM-DD")
                : "",
              bloodGroup: vm.candidateDetails.Blood_Group,
              maritalStatus: vm.candidateDetails.Marital_Status,
              nationality: vm.candidateDetails.Nationality,
              languagesKnown: langKnown || [],
              candidateDependent: dependent,
              candidateWorkPermit: vm.candidateDetails.Work_Permit_Ids,
              candidateOtherWorkPermit: vm.candidateDetails.Other_Work_Permit,
              nationalIdentificationNumber:
                vm.candidateDetails.National_Identification_Number,
              otherWorkAuthorization: vm.candidateDetails.Other_Work_Permit,
              emailId: vm.candidateDetails.Personal_Email,
              mobileNo: vm.candidateDetails.Mobile_No,
              apartmentName: vm.candidateDetails.pApartment_Name,
              street: vm.candidateDetails.pStreet_Name,
              city: vm.candidateDetails.pCity,
              state: vm.candidateDetails.pState,
              country: vm.candidateDetails.pCountry,
              pincode: vm.candidateDetails.pPincode,
              preferredLocation: preLocations,
              candidateEducation: educationArray,
              candidateExperience: experienceArray,
              passportNo: vm.candidateDetails.Passport_No,
              candidateCertification: certificateArray,
              candidateSkills: [],
              verifierName: vm.candidateDetails.Verifier_Name,
              verifierPhoneNo: vm.candidateDetails.Verifier_Phone_Number,
              verifierEmailId: vm.candidateDetails.Verifier_Email_Id,
              jobPost: vm.candidateDetails.Job_Post_Id,
              currentEmployer: vm.candidateDetails.Current_Employer,
              noticePeriod: vm.candidateDetails.Notice_Period
                ? parseInt(vm.candidateDetails.Notice_Period)
                : null,
              currentCTC: vm.candidateDetails.Current_CTC
                ? parseInt(vm.candidateDetails.Current_CTC)
                : null,
              expectedCTC: vm.candidateDetails.Expected_CTC
                ? parseInt(vm.candidateDetails.Expected_CTC)
                : null,
              resume: fileProperties[0],
              resumeFileSize: fileProperties[1],
              currency: vm.candidateDetails.Currency,
              status: vm.candidateDetails.Status_Id
                ? vm.candidateDetails.Status_Id
                : 10,
              totalExperienceInYears:
                vm.candidateDetails.Total_Experience_In_Years,
              totalExperienceInMonths:
                vm.candidateDetails.Total_Experience_In_Months,
              source: vm.candidateDetails.Source,
              candidateProfilePicture: "",
            },
            client: "apolloClientA",
          })
          .then((res) => {
            if (res && res.data && res.data.updateJobCandidates) {
              const { errorCode } = res.data.updateJobCandidates;
              if (!errorCode) {
                let snackbarData = {
                  isOpen: true,
                  message: "Resume uploaded successfully",
                  type: "success",
                };
                vm.showAlert(snackbarData);
                vm.isLoading = false;
                vm.$emit("edit-updated");
              } else {
                vm.handleUpdateError();
              }
            } else {
              vm.handleUpdateError();
            }
          })
          .catch((err) => {
            vm.handleUpdateError(err);
          });
      }
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "uploading",
        form: "resume",
        isListError: false,
      });
    },
    async deleteResumeFromS3(fileName) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "_" + "/" + this.orgCode + "/" + "resume" + "/";
      await vm.$store.dispatch("deletes3File", {
        fileName: fileUploadUrl + fileName,
        type: "documents",
      });
    },
    async uploadFileContents(fileProperties) {
      let vm = this;
      vm.isLoading = true;

      try {
        // Upload new resume first
        let fileUploadUrl =
          this.domainName + "_" + "/" + this.orgCode + "/" + "resume" + "/";
        await vm.$store.dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + fileProperties[0],
          action: "upload",
          type: "documents",
          fileContent: fileProperties[2],
        });

        vm.updateCandidateDetails(fileProperties);
        // Delete old resume if it exists (after successful upload)
        if (vm.candidateDetails?.Resume) {
          await vm.deleteResumeFromS3(vm.candidateDetails.Resume);
        }
        vm.openModal = false;
      } catch (err) {
        vm.isLoading = false;
        vm.handleUpdateError(err);
      }
    },
    validateUser(recruiters, item = null) {
      let isFormAdmin = this.formAccess.admin === "admin";
      if (isFormAdmin) {
        return true;
      } else if (
        item &&
        item.Job_Post_Creator == this.loginEmployeeId &&
        this.isRecruiter.toLowerCase() === "yes"
      ) {
        return true;
      } else {
        let employeeId = this.loginEmployeeId;
        if (recruiters && recruiters.includes(employeeId)) {
          return true;
        }
        return false;
      }
    },
    redirectToJobPost(jobPostId) {
      window.open(
        this.$store.getters.baseUrl +
          `v3/recruitment/job-posts?jobPostId=${jobPostId}`,
        "_blank"
      );
    },
    onCustomEmailCancel() {
      this.showCustomEmail = false;
      this.selectedStage = this.candidateDetails.Hiring_Stage;
      this.candidateStatusList = this.flowList[this.selectedStage];
      this.selectedStatus = 0;
      this.templateEmail = [];
      this.candidateStatus = this.candidateDetails.Status_Id;

      // Update unified dropdown refs if they exist
      if (this.$refs.unifiedStatusRef) {
        this.$refs.unifiedStatusRef.selectedItem =
          this.candidateDetails.Status_Id;
      }
      if (this.$refs.unifiedStatusRefDesktop) {
        this.$refs.unifiedStatusRefDesktop.selectedItem =
          this.candidateDetails.Status_Id;
      }

      // Update separate dropdown ref for old functionality
      if (this.$refs.candidateStatusRef) {
        this.$refs.candidateStatusRef.selectedItem =
          this.candidateDetails.Status_Id;
      }
    },

    checkBlacklist(value) {
      this.changedStatus = value;
      if (this.candidateDetails.Blacklisted?.toLowerCase() === "yes") {
        this.blacklistWarningModal = true;
      } else {
        if (this.shouldShowCustomEmail) {
          this.confirmUpdate();
        } else {
          this.onChangeRoundStatus(value);
        }
      }
    },

    onChangeRoundStatus(value) {
      this.blacklistWarningModal = false;
      let status = this.candidateStatusList.filter((obj) => {
        return obj.Id == value;
      });
      this.changedStatusObj = status && status.length > 0 ? status[0] : null;
      this.openWarningModal = true;
    },

    customEmailSent() {
      this.showCustomEmail = false;
      this.updateStatus(this.selectedStatus, true);
      this.selectedStatus = 0;
      this.templateEmail = [];
    },

    confirmUpdate() {
      this.openWarningModal = false;
      this.updateStatus(this.changedStatus);
    },
    checkCandidateActions(type, item) {
      // Emit event to parent component instead of handling logic directly
      this.$emit("check-candidate-actions", { type, item });
    },

    /**
     * Handle candidate view close after specific actions
     */
    handleCandidateViewClose(eventData) {
      const { action, candidate } = eventData;

      // Emit the close-view-form event to parent with proper structure
      this.$emit("close-view-form", {
        refetchCount: 1, // Always refetch for action-completed closes
        action,
        candidate,
        reason: "action-completed",
      });
    },

    onCandidateActionSelected(eventData) {
      // Handle the new Candidate ActionMenu event format
      const { action, candidate } = eventData;

      // First, process the action through the existing handler
      this.checkCandidateActions(action, candidate);
      const actionsToCloseView = [
        "delete",
        "move candidate to talent pool",
        "move candidate to archive",
        "rollback",
      ];

      // Check if the current action should trigger view closure
      const shouldCloseView = actionsToCloseView.some(
        (closeAction) => action.toLowerCase() === closeAction
      );

      if (shouldCloseView) {
        // Use nextTick and setTimeout to ensure:
        // 1. Action processing completes successfully
        // 2. Any success/error messages are shown to user
        // 3. API calls have time to complete before navigation
        this.$nextTick(() => {
          setTimeout(() => {
            this.$emit("close-view-form");
          }, 500); // 500ms delay for smooth UX and action completion
        });
      }
    },

    async updateStatus(value, customEmailUpdated = false) {
      //Send email if status is Rejected / Not Hired
      if (this.shouldShowCustomEmail && !customEmailUpdated) {
        if (this.selectedStage == "Archived") {
          this.templateType = "rejectedNotHiredCandidate";
        } else if (this.selectedStage == "Hired") {
          this.templateType = "hiredCandidate";
        } else if (
          this.changedStatusObj?.Status == "Background Investigation Failed"
        ) {
          this.templateType = "BIFailed";
        } else if (
          this.changedStatusObj?.Status == "Background Investigation Initiated"
        ) {
          this.templateType = "InitiateBackgroundInvestigation";
          this.templateEmail = [this.loginEmpEmail];
          this.emailTemplateType = "Background Investigation";
        }
        this.selectedStatus = value;
        this.showCustomEmail = true;
      } else {
        let vm = this;
        vm.isLoading = true;
        vm.candidateStatus = value;
        vm.$apollo
          .mutate({
            mutation: UPDATE_CANDIDATE_STATUS,
            client: "apolloClientAM",
            fetchPolicy: "no-cache",
            variables: {
              candidateId: [vm.candidateIdSelected],
              candidateStatus: value,
            },
          })
          .then(() => {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate status updated successfully",
            };
            vm.showAlert(snackbarData);
            vm.isLoading = false;
            vm.$emit("status-updated");
          })
          .catch((err) => {
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "updating",
              form: "candidate status",
              isListError: false,
            });
            vm.isLoading = false;
          });
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    getStageList() {
      this.statusAccessLoading = true;
      this.$apollo
        .query({
          query: GET_STATUS_LIST,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            formId: 16,
            conditions: [
              {
                key: "Form_Id",
                value: ["16"],
              },
            ],
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getAtsStatusList &&
            response.data.getAtsStatusList.statusList
          ) {
            let tempData = response.data.getAtsStatusList.statusList;
            const groupedByStage = tempData.reduce((acc, item) => {
              // If the stage doesn't exist in the accumulator, create a new array for it
              if (!acc[item.Stage]) {
                acc[item.Stage] = [];
                this.stageList[item.Stage_Id - 1] = {
                  id: item.Stage_Id,
                  value: item.Stage,
                };
              }
              // Push the current item into the appropriate group
              acc[item.Stage].push(item);
              return acc;
            }, {});
            this.flowList = groupedByStage;
            this.candidateStatusList = groupedByStage[this.selectedStage];
          } else {
            this.stageList = [];
          }
          this.statusAccessLoading = false;
        })
        .catch((err) => {
          this.statusAccessLoading = false;
          this.handleRetrieveHiringFlow(err);
        });
    },
    handleRetrieveHiringFlow(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "stages list",
        isListError: false,
      });
    },
    onSelectUnifiedStatus(statusId) {
      // Find the selected status in the unified list
      const selectedStatus = this.unifiedStageStatusList.find(
        (item) => item.value === statusId
      );

      if (selectedStatus) {
        // Extract stage and status information
        const newStage = selectedStatus?.raw?.stageName;
        const newStatusId = selectedStatus?.raw?.statusId;

        // Update the stage if it changed
        if (this.selectedStage !== newStage) {
          this.selectedStage = newStage;
          this.candidateStatusList = this.flowList[newStage];
        }

        // Update the candidate status
        this.candidateStatus = newStatusId;

        // Call the existing blacklist check logic
        this.checkBlacklist(newStatusId);
      }
    },
    onSelectStage(stage) {
      // Reset both unified and separate dropdown selections
      if (this.$refs.unifiedStatusRef) {
        this.$refs.unifiedStatusRef.selectedItem = null;
      }
      if (this.$refs.unifiedStatusRefDesktop) {
        this.$refs.unifiedStatusRefDesktop.selectedItem = null;
      }
      if (this.$refs.candidateStatusRef) {
        this.$refs.candidateStatusRef.selectedItem = null;
      }
      this.selectedStage = stage;
      this.candidateStatusList = this.flowList[stage];
      this.selectedStatus = 0;
      this.templateEmail = [];
      this.candidateStatus = this.candidateDetails.Status_Id;
    },
    cancelUpdate() {
      this.openWarningModal = false;
      this.selectedStage = this.candidateDetails.Hiring_Stage;
      this.candidateStatusList = this.flowList[this.selectedStage];
      this.candidateStatus = this.candidateDetails.Status_Id;

      // Update unified dropdown refs if they exist
      if (this.$refs.unifiedStatusRef) {
        this.$refs.unifiedStatusRef.selectedItem =
          this.candidateDetails.Status_Id;
      }
      if (this.$refs.unifiedStatusRefDesktop) {
        this.$refs.unifiedStatusRefDesktop.selectedItem =
          this.candidateDetails.Status_Id;
      }

      // Update separate dropdown ref for old functionality
      if (this.$refs.candidateStatusRef) {
        this.$refs.candidateStatusRef.selectedItem =
          this.candidateDetails.Status_Id;
      }
    },
    viewBlacklistedDocuments(index) {
      this.selectedDocumentIndex = index;
      this.blacklistFileName = this.blacklistDocument[index].filePath;
      this.showBlacklistDocuments = true;
    },
    changeSelectedDocument(step) {
      this.selectedDocumentIndex += step;
      if (
        this.selectedDocumentIndex >= 0 &&
        this.selectedDocumentIndex < this.blacklistDocument.length
      ) {
        this.blacklistFileName =
          this.blacklistDocument[this.selectedDocumentIndex].filePath;
      }
    },
  },
};
</script>
