<template>
  <div>
    <JobCandidateTopCard
      v-if="candidateIdSelected"
      ref="jobCandidateTopCard"
      :listLoading="isLoading"
      :selectedItem="selectedItem"
      :candidateDetails="candidateDetails"
      :candidateIdSelected="candidateIdSelected"
      :form-access="formAccess"
      :recruitmentSettings="recruitmentSettings"
      :candidateList="candidateList"
      :originalList="originalList"
      :currentSortedItems="currentSortedItems"
      :candidateJobPostIdSelected="candidateJobPostIdSelected"
      :isRecruiter="isRecruiter"
      @close-view-form="closeViewForm($event)"
      @edit-updated="retrieveJobCandidateDetails()"
      :parentTabName="parentTabName"
      :candidateNoActionStatusList="candidateNoActionStatusList"
      @status-updated="retrieveJobCandidateDetails('', 'edit')"
      @on-change-candidate="$emit('on-change-candidate', $event)"
      @check-candidate-actions="$emit('check-candidate-actions', $event)"
    />
    <ProfileCard class="mt-3 mb-2">
      <FormTab :model-value="activeTab" :hide-slider="true">
        <v-tab
          v-for="tab in viewFormTabs"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          @click="onChangeViewFormTabs(tab.value)"
        >
          <div
            :class="[
              isActiveTab(tab)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            {{ tab.label }}
            <div
              v-if="isActiveTab(tab)"
              class="mt-3 mb-n4"
              style="border-bottom: 4px solid"
            ></div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard>

    <ProfileCard class="my-5" ref="viewFormContent">
      <div v-if="isLoading" class="pa-4">
        <div v-for="i in 5" :key="i" class="mt-2">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </div>
      <div v-else>
        <div v-if="activeTab === 'Resume'" class="pa-4">
          <FilePreviewAndEdit
            :showDialog="false"
            :fileName="candidateDetails.Resume"
            :otherAttachment="candidateDetails.Other_Attachments || []"
            :heading="candidateDetails.Resume ? 'Attachment' : 'Resume Upload'"
            folderName="resume"
            :isUpload="candidateDetails.Resume ? false : true"
            :fileSize="candidateDetails.Resume_File_Size"
            fileRetrieveType="documents"
            :getCloudfrontUrl="true"
            fileAction="view"
            @close-preview-modal="() => {}"
            @update-resume-details="uploadFileContents"
          />
        </div>
        <div v-if="activeTab === 'Profile'">
          <div
            v-if="
              !showEditForm &&
              parentTabName &&
              parentTabName.toLowerCase() !== 'archived'
            "
            class="d-flex justify-end mt-2 mb-n6"
          >
            <v-tooltip
              :text="
                candidateDetails?.Hiring_Stage?.toLowerCase() === 'preboarding'
                  ? `Candidate details cannot be modified once preboarding has started`
                  : ''
              "
            >
              <template v-slot:activator="{ props }">
                <v-btn
                  v-if="
                    formAccess &&
                    formAccess.update &&
                    candidateDetails.Status_Id != 3
                  "
                  v-bind="
                    candidateDetails?.Hiring_Stage?.toLowerCase() ===
                    'preboarding'
                      ? props
                      : {}
                  "
                  @click="
                    candidateDetails?.Hiring_Stage?.toLowerCase() ===
                    'preboarding'
                      ? {}
                      : openEditForm()
                  "
                  class="primary"
                  variant="text"
                >
                  <v-icon class="mr-1" size="14">fas fa-edit</v-icon>Edit
                </v-btn>
              </template></v-tooltip
            >
            <v-icon
              size="14"
              class="mt-3 mr-4"
              color="grey"
              @click="retrieveJobCandidateDetails()"
              >fas fa-redo-alt</v-icon
            >
          </div>
          <v-card-text v-if="showEditForm" class="edit-form-section-height">
            <EditJobCandidateDetails
              ref="editJobCandidateDetails"
              :candidateDetails="candidateDetails"
              :candidateIdSelected="candidateIdSelected"
              @close-edit-form="closeEditForm()"
              @edit-updated="retrieveJobCandidateDetails($event, 'edit')"
              :showJobHeader="false"
              @close-candidate-details="closeViewForm()"
            ></EditJobCandidateDetails>
          </v-card-text>
          <v-card-text v-else class="view-form-section-height pa-4">
            <PersonalInfo :candidateDetails="candidateDetails" />
            <JobInfo :candidateDetails="candidateDetails" />
            <ExperienceDetails
              :candidateDetails="candidateDetails"
              :isEdit="false"
            ></ExperienceDetails>
            <ContactInfo :candidateDetails="candidateDetails" />
            <CareerInfo :isEdit="false" :candidateDetails="candidateDetails" />
            <JobCandidatesCustomFields
              :external-form="jobPostId ? true : false"
              :custom-form-name="formName"
              :form-id="jobPostId ? 311 : 16"
              :primary-id="candidateIdSelected ? candidateIdSelected : 0"
              :show-view-form="true"
              :show-edit-form="false"
            />
          </v-card-text>
        </div>
        <v-card-text
          v-if="activeTab === 'Feedback'"
          class="edit-form-section-height pa-4"
        >
          <Feedback
            :candidateIdSelected="candidateIdSelected"
            :candidateJobPostIdSelected="candidateJobPostIdSelected"
            :formAccess="formAccess"
            :candidateDetails="candidateDetails"
            :canScheduleInterview="canScheduleInterview"
            :isRecruiter="isRecruiter"
            :parentTabName="parentTabName"
          />
        </v-card-text>
        <v-card-text
          v-if="activeTab === 'Activity Log'"
          class="edit-form-section-height pa-4"
          ><div class="d-flex flex-wrap align-center justify-end">
            <v-btn
              rounded="lg"
              color="transparent"
              variant="flat"
              :size="isMobileView ? 'small' : 'default'"
              @click="refetchList()"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu
              v-if="activityLogs && activityLogs.length > 0"
              class="my-1 ml-n1"
              transition="scale-transition"
            >
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" v-bind="props">
                  <v-icon>fas fa-ellipsis-v</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in moreActions"
                  :key="action.key"
                  @click="exportAuditLogs(action.key)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          hover: isHovering,
                        }"
                      >
                        <v-icon size="15" class="pr-2">{{
                          action.icon
                        }}</v-icon>
                        {{ action.key }}
                      </v-list-item-title>
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
          <ActivityLog
            :formId="16"
            :uniqueId="candidateIdSelected"
            :logsFormId="[16, 134, 178, 272, 273, 274, 275, 297, 302]"
            :callListApi="activityRefresh"
            @activity-logs-data="onActivityLogsUpdated($event)"
          />
        </v-card-text>
      </div>
    </ProfileCard>
  </div>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to discard the changes?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="changeTab()"
  ></AppWarningModal>
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import PersonalInfo from "./PersonalInfo.vue";
import JobInfo from "./JobInfo.vue";
import ExperienceDetails from "./experience-details/ExperienceDetails.vue";
import ContactInfo from "./ContactInfo.vue";
import CareerInfo from "./career/CareerInfo.vue";
import JobCandidateTopCard from "./JobCandidateTopCard.vue";
import Feedback from "./feedback/Feedback.vue";
import ActivityLog from "./activity-log/ActivityLog.vue";
import { RETRIEVE_JOB_CANDIDATE_DETAILS } from "@/graphql/recruitment/recruitmentQueries.js";
const EditJobCandidateDetails = defineAsyncComponent(() =>
  import("./EditJobCandidateDetails.vue")
);
const JobCandidatesCustomFields = defineAsyncComponent(() =>
  import("./custom-fields/JobCandidatesCustomFields.vue")
);
import FilePreviewAndEdit from "@/components/custom-components/FilePreviewAndEdit.vue";
import { convertUTCToLocal } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";

export default defineComponent({
  name: "JobCandidateDetails",
  mixins: [FileExportMixin],
  components: {
    PersonalInfo,
    JobInfo,
    ContactInfo,
    CareerInfo,
    JobCandidatesCustomFields,
    Feedback,
    JobCandidateTopCard,
    ExperienceDetails,
    EditJobCandidateDetails,
    ActivityLog,
    FilePreviewAndEdit,
  },
  props: {
    recruitmentSettings: {
      type: Object,
      default: function () {
        return {};
      },
    },
    selectedJobCandidateId: {
      type: Number,
      default: 0,
    },
    selectedJobPostId: {
      type: Number,
      default: 0,
    },
    selectedItem: {
      type: Object,
      default: function () {
        return {};
      },
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    originalList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    currentSortedItems: {
      type: Array,
      default: function () {
        return [];
      },
    },
    candidateList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    candidateChangeCount: {
      type: Number,
      default: 0,
    },
    canScheduleInterview: {
      type: Boolean,
      default: true,
    },
    isRecruiter: {
      type: String,
      default: "No",
    },
    parentTabName: {
      type: String,
      default: "",
    },
    candidateNoActionStatusList: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  emits: [
    "retrieve-error",
    "close-view-form",
    "on-change-candidate",
    "check-candidate-actions",
  ],
  data() {
    return {
      activeTab: "Profile",
      candidateIdSelected: 0,
      candidateJobPostIdSelected: 0,
      changedTab: "",
      candidateDetails: {},
      showScrollButton: false,
      isLoading: false,
      showEditForm: false,
      refetchCount: 0,
      openWarningModal: false,
      activityLogs: [],
      activityRefresh: false,
    };
  },
  computed: {
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    formName() {
      let formName = this.jobPostId
        ? this.accessIdRights("311")
        : this.accessIdRights("16");
      if (formName?.customFormName && formName.customFormName !== "") {
        return [formName.customFormName];
      } else return ["Job Candidates"];
    },
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    viewFormTabs() {
      let tabs = [
        {
          label: "Profile",
          value: "Profile",
          disable: false,
        },
        {
          label: "Feedback",
          value: "Feedback",
          disable: false,
        },
        {
          label: "Activity Log",
          value: "Activity Log",
          disable: false,
        },
      ];
      if (this.recruitmentNewFeatureEnabled && this.candidateIdSelected)
        tabs.unshift({
          label: "Resume",
          value: "Resume",
          disable: false,
        });

      return tabs;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    domainName() {
      let domain = this.$store.getters.domain;
      if (this.appendUnderScoreInDomain) {
        domain = domain + "_";
      }
      return domain;
    },

    recruitmentNewFeatureEnabled() {
      return (
        this.recruitmentSettings?.Enable_New_Features?.toLowerCase() === "yes"
      );
    },
  },
  watch: {
    candidateChangeCount(count) {
      if (count > 0) {
        this.candidateIdSelected = this.selectedJobCandidateId;
        this.candidateJobPostIdSelected = this.selectedJobPostId;
        if (this.candidateIdSelected) {
          this.retrieveJobCandidateDetails();
          this.enableAllTabs();
        }
      }
    },
  },
  mounted() {
    this.candidateIdSelected = this.selectedJobCandidateId;
    this.candidateJobPostIdSelected = this.selectedJobPostId;
    if (this.candidateIdSelected) {
      this.retrieveJobCandidateDetails();
      this.enableAllTabs();
      if (this.recruitmentNewFeatureEnabled) {
        this.activeTab = "Resume";
      } else {
        this.activeTab = "Profile";
      }
    } else {
      this.activeTab = "Profile";
      this.showEditForm = true;
    }
  },
  methods: {
    closeViewForm(eventData = null) {
      if (this.$route.query?.candidateId) {
        this.refetchCount = 1;
      }

      // If eventData is passed from action completion, include it in the emit
      if (eventData && eventData.action) {
        this.$emit("close-view-form", {
          refetchCount: 1,
          action: eventData.action,
          candidate: eventData.candidate,
          reason: eventData.reason || "action-completed",
        });
      } else {
        this.$emit("close-view-form", {
          refetchCount: this.refetchCount,
          candidate: this.candidateDetails,
        });
      }
    },
    isActiveTab(tab) {
      return this.activeTab === tab.value;
    },
    enableAllTabs() {
      this.viewFormTabs = this.viewFormTabs.map((item) => {
        item["disable"] = false;
        return item;
      });
    },
    onChangeViewFormTabs(val) {
      if (this.showEditForm) {
        this.changedTab = val;
        this.openWarningModal = true;
      } else {
        this.activeTab = val;
      }
    },
    onCloseWarningModal() {
      this.changedTab = "";
      this.openWarningModal = false;
    },
    changeTab() {
      this.activeTab = this.changedTab;
      this.showEditForm = false;
      this.openWarningModal = false;
    },
    openEditForm() {
      this.showEditForm = true;
    },
    closeEditForm() {
      if (!this.candidateIdSelected) {
        this.$emit("close-view-form", this.refetchCount);
      } else {
        this.showEditForm = false;
      }
    },

    retrieveJobCandidateDetails(candidateId = "", type) {
      if (candidateId) {
        this.candidateIdSelected = candidateId;
        this.enableAllTabs();
      }
      if (type == "edit") {
        this.refetchCount += 1;
      }
      let vm = this;
      vm.isLoading = true;
      vm.showEditForm = false;
      vm.$apollo
        .query({
          query: RETRIEVE_JOB_CANDIDATE_DETAILS,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            employeeId: parseInt(vm.loginEmployeeId),
            candidateId: parseInt(vm.candidateIdSelected),
            action: "view",
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveJobCandidates &&
            !response.data.retrieveJobCandidates.errorCode
          ) {
            vm.candidateDetails =
              response.data.retrieveJobCandidates.jobCandidateDetails;
            if (!vm.candidateIdSelected && vm.candidateDetails) {
              vm.candidateIdSelected = vm.candidateDetails.Candidate_Id
                ? vm.candidateDetails.Candidate_Id
                : 0;
            }
            if (!vm.candidateJobPostIdSelected && vm.candidateDetails) {
              vm.candidateJobPostIdSelected = vm.candidateDetails.Job_Post_Id
                ? vm.candidateDetails.Job_Post_Id
                : 0;
            }
            vm.isLoading = false;
          } else {
            vm.handleRetrieveError();
          }
        })
        .catch((err) => {
          vm.handleRetrieveError(err);
        });
    },

    handleRetrieveError() {
      this.isLoading = false;
      var snackbarData = {
        isOpen: true,
        type: "warning",
        message: "Something went wrong while retrieving job candidate details",
      };
      this.showAlert(snackbarData);
      this.$emit("retrieve-error", this.refetchCount);
    },
    async deleteResumeFromS3(fileName) {
      let vm = this;
      let fileUploadUrl =
        this.domainName + "_" + "/" + this.orgCode + "/" + "resume" + "/";

      await vm.$store.dispatch("deletes3File", {
        fileName: fileUploadUrl + fileName,
        type: "documents",
      });
    },
    async uploadFileContents(fileProperties) {
      let vm = this;
      vm.isLoading = true;

      try {
        // Upload new resume first
        let fileUploadUrl =
          this.domainName + "_" + "/" + this.orgCode + "/" + "resume" + "/";
        await vm.$store.dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + fileProperties[0],
          action: "upload",
          type: "documents",
          fileContent: fileProperties[2],
        });

        await this.$refs.jobCandidateTopCard.updateCandidateDetails(
          fileProperties
        );
        // Delete old resume if it exists (after successful upload)
        if (vm.candidateDetails?.Resume) {
          await vm.deleteResumeFromS3(vm.candidateDetails.Resume);
        }
        // after updating the candidate details, update the resume name before passing it to the resume parser
        this.candidateDetails.Resume = fileProperties?.length
          ? fileProperties[0]
          : "";
        // vm.passResumeUpdate = fileProperties;

        vm.isLoading = false;
      } catch (err) {
        vm.isLoading = false;
        vm.handleUpdateError(err);
      }
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "uploading",
        form: "resume",
        isListError: false,
      });
    },
    refetchList() {
      this.activityRefresh = true;
      setTimeout(() => {
        this.activityRefresh = false;
      }, 1000);
    },
    onActivityLogsUpdated(logs) {
      this.activityLogs = logs || [];
    },
    exportAuditLogs() {
      // Format data for export
      const exportData = this.activityLogs.map((log) => ({
        employeeName: log.employeeName || "",
        userAction: log.userAction || "",
        description: log.description || "",
        formName: this.parentTabName,
        logTimestamp: log.logTimestamp
          ? convertUTCToLocal(log.logTimestamp)
          : "",
      }));

      const exportHeaders = [
        { key: "formName", header: "Form Name" },
        { key: "userAction", header: "Action" },
        { key: "employeeName", header: "Action performed by" },
        { key: "logTimestamp", header: "Action Date Time" },
        { key: "description", header: "Description" },
      ];
      const exportOptions = {
        fileExportData: exportData,
        fileName:
          this.parentTabName ||
          "Activity Logs" +
            " - " +
            (this.candidateDetails?.Candidate_First_Name || "") +
            " " +
            (this.candidateDetails?.Candidate_Last_Name || ""),
        sheetName: this.parentTabName || "Activity Logs",
        header: exportHeaders,
      };

      this.exportExcelFile(exportOptions);
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style scoped>
.scroll-to-bottom-button {
  position: fixed;
  bottom: 5%;
  right: 15%;
  cursor: pointer;
}
.view-form-section-height {
  height: calc(100vh - 230px);
  overflow: hidden;
  overflow-y: scroll;
}

.edit-form-section-height {
  height: calc(100vh - 210px);
  overflow: hidden;
  overflow-y: scroll;
}
</style>
