<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <AppFetchErrorScreen
    v-else-if="isErrorInList || !activityLogs.length"
    :image-name="
      errorContent
        ? 'common/common-error-image'
        : 'common/initial-fetch-error-image'
    "
    :content="
      errorContent
        ? errorContent
        : 'No activity logs found for the records yet.'
    "
    :icon-name="'fas fa-redo-alt'"
    :button-text="'Retry'"
    @button-click="getActivityLogs()"
  >
  </AppFetchErrorScreen>
  <div v-else>
    <div style="max-height: 600px" class="overflow-y-auto">
      <v-timeline
        justify="auto"
        side="end"
        :align="'center'"
        style="justify-content: start"
      >
        <v-timeline-item v-for="log in activityLogs" :key="log.id" size="15">
          <template v-slot:opposite>
            <div class="d-flex flex-column align-end">
              <span class="text-body-1">{{ log.userAction }}</span>
              <span class="text-body-2">{{
                formatDateTime(log.logTimestamp)
              }}</span>
            </div>
          </template>
          <v-card class="pa-2 d-flex align-center">
            <v-avatar
              size="45"
              class="text-center justify-center font-weight-bold"
              :style="{ backgroundColor: log.color, color: 'white' }"
            >
              {{ getFirstLetters(log.employeeName) }}
            </v-avatar>
            <div class="pa-1 ml-1">
              <span class="text-body-2 font-weight-bold">{{
                log.employeeName
              }}</span>
              <span class="text-body-2 ml-3">{{ log.description }}</span>
            </div>
          </v-card>
        </v-timeline-item>
      </v-timeline>
    </div>
  </div>
</template>

<script>
import { GET_SYSTEM_ACTIVITY_LOGS } from "@/graphql/recruitment/recruitmentQueries.js";
import moment from "moment";
export default {
  name: "ActivityLog",
  props: {
    uniqueId: {
      type: Number,
      default: 0,
    },
    formId: {
      type: Number,
      required: true,
    },
    logsFormId: {
      type: Array,
      default: () => [],
    },
    callListApi: {
      type: Boolean,
      default: false,
    },
    startDate: {
      type: String,
      default: "",
    },
    endDate: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      listLoading: false,
      activityLogs: [],
      backupActivityLogs: [],
      errorContent: "",
      isErrorInList: false,
      totalApiCount: 0,
      apiCallCount: 0,
      activityLogsLimitToCallAPI: 500,
    };
  },
  emits: ["activity-logs-data"],
  computed: {
    getFirstLetters() {
      return (str) => {
        return str
          .split(" ")
          .map((word) => word[0]?.toUpperCase() || "")
          .join("");
      };
    },
    formatDateTime() {
      return (dateTime) => {
        if (dateTime && moment(dateTime).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(dateTime, "YYYY-MM-DD HH:mm:ss").format(
            orgDateFormat + "  HH:mm"
          );
        }
        return "";
      };
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
  },
  watch: {
    callListApi: {
      handler(newval, oldval) {
        if (newval && newval !== oldval) this.getActivityLogs();
      },
      immediate: true,
    },
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.getActivityLogs();
  },
  methods: {
    getRandomColor() {
      const letters = "0123456789ABCDEF";
      let color = "#";
      for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
      }
      return color;
    },
    onApplySearch(val) {
      if (!val) {
        this.activityLogs = this.backupActivityLogs;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupActivityLogs;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.activityLogs = searchItems;
      }
    },
    getActivityLogs() {
      let vm = this;
      vm.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      vm.listLoading = true;
      vm.isErrorInList = false;
      vm.errorContent = "";

      vm.$apollo
        .query({
          query: GET_SYSTEM_ACTIVITY_LOGS,
          client: "apolloClientAN",
          variables: {
            formId: parseInt(vm.formId),
            uniqueId: parseInt(vm.uniqueId),
            logsFormId: vm.logsFormId || [],
            fromDate: vm.startDate
              ? moment(vm.startDate).format("YYYY-MM-DD 00:00:00")
              : null,
            toDate: vm.endDate
              ? moment(vm.endDate).format("YYYY-MM-DD 23:59:59")
              : null,
            limit: vm.activityLogsLimitToCallAPI,
            offset: 0,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.getSystemActivityLogs &&
            !res.data.getSystemActivityLogs.errorCode
          ) {
            let { activityLogs, totalRecords } = res.data.getSystemActivityLogs;
            let tempData =
              activityLogs?.map((log) => {
                return {
                  ...log,
                  logTimestamp: new Date(log.logTimestamp + ".000Z"),
                  color: vm.getRandomColor(),
                };
              }) || [];

            vm.activityLogs = tempData;
            vm.backupActivityLogs = tempData;
            vm.$emit("activity-logs-data", tempData);

            // Handle pagination if there are more records
            if (totalRecords > 0) {
              totalRecords = parseInt(totalRecords);
              vm.apiCallCount = 1;
              vm.totalApiCount = Math.ceil(
                totalRecords / vm.activityLogsLimitToCallAPI
              );

              // Fetch remaining pages if needed
              for (let i = 1; i < vm.totalApiCount; i++) {
                vm.updateActivityLogs(i);
              }
            }
          } else
            vm.handleError(res.data.getSystemActivityLogs?.errorCode || "");

          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleError(err);
        });
    },
    updateActivityLogs(index = 1) {
      let vm = this;
      let apiOffset = parseInt(index) * vm.activityLogsLimitToCallAPI;
      apiOffset = parseInt(apiOffset);

      vm.$apollo
        .query({
          query: GET_SYSTEM_ACTIVITY_LOGS,
          client: "apolloClientAN",
          variables: {
            formId: parseInt(vm.formId),
            uniqueId: parseInt(vm.uniqueId),
            logsFormId: vm.logsFormId || [],
            fromDate: vm.startDate
              ? moment(vm.startDate).format("YYYY-MM-DD 00:00:00")
              : null,
            toDate: vm.endDate
              ? moment(vm.endDate).format("YYYY-MM-DD 23:59:59")
              : null,
            limit: vm.activityLogsLimitToCallAPI,
            offset: apiOffset,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (
            res &&
            res.data &&
            res.data.getSystemActivityLogs &&
            !res.data.getSystemActivityLogs.errorCode
          ) {
            let { activityLogs } = res.data.getSystemActivityLogs;
            const tempData =
              activityLogs?.map((log) => {
                return {
                  ...log,
                  logTimestamp: new Date(log.logTimestamp + ".000Z"),
                  color: vm.getRandomColor(),
                };
              }) || [];

            vm.activityLogs = [...vm.activityLogs, ...tempData];
            vm.backupActivityLogs = [...vm.backupActivityLogs, ...tempData];

            vm.apiCallCount = vm.apiCallCount + 1;
            if (vm.totalApiCount === vm.apiCallCount) {
              vm.listLoading = false;
              vm.$emit("activity-logs-data", vm.backupActivityLogs);
            }
          } else {
            vm.handleError(res.data.getSystemActivityLogs?.errorCode || "");
          }
        })
        .catch((err) => {
          vm.handleError(err);
        });
    },
    handleError(err) {
      this.activityLogs = [];
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Activity Log",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
  },
};
</script>
<style scoped>
/* Custom scrollbar styling for configuration panel */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
