<template>
  <div>
    <div>
      <div id="educationDiv">
        <div class="d-flex">
          <div class="d-flex align-center">
            <v-progress-circular
              v-if="!isEdit"
              model-value="100"
              color="blue-grey"
              :size="18"
              class="mr-1"
            ></v-progress-circular>
            <span
              class="d-flex text-subtitle-1 text-grey-darken-1 font-weight-bold"
            >
              Education Details
            </span>
          </div>
        </div>
        <div v-if="showAddEditEducationForm">
          <AddEditEducationDetails
            ref="educationAddEdit"
            :educationDetails="educationDetails"
            :isUserLogedIn="isUserLogedIn"
            :selectedCandidateDOB="selectedCandidateDOB"
            :jobPostId="jobPostId"
            @update-education-details="updateCareerInfoDetails"
          >
          </AddEditEducationDetails>
        </div>
        <div v-else>
          <div v-if="!isMobileView" class="d-flex">
            <v-slide-group
              class="mt-4"
              selected-class="bg-secondary"
              prev-icon="fas fa-chevron-circle-left"
              next-icon="fas fa-chevron-circle-right"
              show-arrows
            >
              <v-slide-group-item>
                <ViewEducationDetails :educationDetails="educationDetails" />
              </v-slide-group-item>
            </v-slide-group>
          </div>
          <div v-else>
            <div class="card-container">
              <ViewEducationDetails :educationDetails="educationDetails" />
            </div>
          </div>
        </div>
      </div>
      <div id="certificationDiv" class="mt-6">
        <div class="d-flex">
          <div class="d-flex align-center">
            <v-progress-circular
              v-if="!isEdit"
              model-value="100"
              color="brown"
              :size="18"
              class="mr-1"
            ></v-progress-circular>
            <span
              class="d-flex text-subtitle-1 text-grey-darken-1 font-weight-bold"
              >Certification Details</span
            >
          </div>
        </div>
        <div v-if="showAddEditCertificationForm">
          <AddEditCertificationDetails
            ref="certificationAddEdit"
            :certificationDetails="certificationDetails"
            :selectedCandidateDOB="selectedCandidateDOB"
            :dateFormat="dateFormat"
            :jobPostId="jobPostId"
            :isUserLogedIn="isUserLogedIn"
            @update-certification-details="updateCareerInfoDetails"
          >
          </AddEditCertificationDetails>
        </div>
        <div v-else>
          <div v-if="!isMobileView" class="d-flex">
            <v-slide-group
              class="mt-4"
              selected-class="bg-secondary"
              prev-icon="fas fa-chevron-circle-left"
              next-icon="fas fa-chevron-circle-right"
              show-arrows
            >
              <v-slide-group-item>
                <ViewCertificationDetails
                  :certificationDetails="certificationDetails"
                />
              </v-slide-group-item>
            </v-slide-group>
          </div>
          <div v-else>
            <div class="card-container">
              <ViewCertificationDetails
                :certificationDetails="certificationDetails"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import ViewEducationDetails from "./education/ViewEducationDetails";
import ViewCertificationDetails from "./certification/ViewCertificationDetails";
import moment from "moment";
// components
const AddEditEducationDetails = defineAsyncComponent(() =>
  import("./education/AddEditEducationDetails.vue")
);
const AddEditCertificationDetails = defineAsyncComponent(() =>
  import("./certification/AddEditCertificationDetails.vue")
);

export default {
  name: "CareerInfo",
  components: {
    ViewEducationDetails,
    ViewCertificationDetails,
    AddEditEducationDetails,
    AddEditCertificationDetails,
  },
  emits: ["update-career-info-details"],
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    candidateDetails: {
      type: Object,
      required: true,
    },
    jobPostId: {
      type: [String, Number],
      default: 0,
    },
    isUserLogedIn: {
      type: Boolean,
      default: false,
    },
    dateFormat: {
      type: String,
      default: "",
    },
  },
  data: () => ({
    showAddEditEducationForm: false,
    showAddEditCertificationForm: false,
    educationDetails: [],
    certificationDetails: [],
    careerInfoData: {},
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    selectedCandidateDOB() {
      if (
        this.candidateDetails &&
        this.candidateDetails.DOB &&
        moment(this.candidateDetails.DOB).isValid()
      ) {
        return this.candidateDetails.DOB;
      }
      return null;
    },
  },
  mounted() {
    if (this.candidateDetails) {
      const { Candidate_Education, Candidate_Certifications } =
        this.candidateDetails;
      this.educationDetails = Candidate_Education ? Candidate_Education : [];
      this.certificationDetails = Candidate_Certifications
        ? Candidate_Certifications
        : [];
    }
    if (this.isEdit) {
      this.showAddEditEducationForm = true;
      this.showAddEditCertificationForm = true;
    }
  },
  watch: {
    candidateDetails(val) {
      if (val) {
        const { Candidate_Education, Candidate_Certifications } = val;
        this.educationDetails = Candidate_Education ? Candidate_Education : [];
        this.certificationDetails = Candidate_Certifications
          ? Candidate_Certifications
          : [];
      }
    },
  },
  methods: {
    async validateEducationAddEditForm() {
      if (this.$refs.educationAddEdit) {
        let addEditRes =
          await this.$refs.educationAddEdit.validateEducationDetails();
        return addEditRes;
      } else {
        return true;
      }
    },
    async validateCertificationAddEditForm() {
      if (this.$refs.certificationAddEdit) {
        let addEditRes =
          await this.$refs.certificationAddEdit.validateCertificateDetails();
        return addEditRes;
      } else {
        return true;
      }
    },
    updateCareerInfoDetails(data, from) {
      let updatedData = { ...this.careerInfoData };
      if (from?.toLowerCase() === "certificate") {
        updatedData["Candidate_Certifications"] = [...data];
      }
      if (from?.toLowerCase() === "education") {
        updatedData["Candidate_Education"] = [...data];
      }
      this.careerInfoData = updatedData;
      this.$nextTick(() => {
        this.$emit(
          "update-career-info-details",
          JSON.parse(JSON.stringify(updatedData)),
          "CareerInfo"
        );
      });
    },
  },
};
</script>
<style scoped>
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  /* The grid-gap property adds a 10-pixel gap between the cards. */
  grid-gap: 10px;
}
@media (max-width: 600px) {
  .card-container {
    grid-template-columns: 1fr;
  }
}
</style>
