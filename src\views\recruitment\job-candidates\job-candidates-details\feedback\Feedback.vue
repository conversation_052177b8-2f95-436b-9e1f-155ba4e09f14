<template>
  <div v-if="listLoading">
    <div v-for="i in 5" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <div v-else-if="isErrorInList || !interviewDetails.length">
    <AppFetchErrorScreen
      :image-name="
        errorContent.length
          ? 'common/common-error-image'
          : 'common/initial-fetch-error-image'
      "
      :content="
        errorContent.length
          ? errorContent
          : 'No interviews scheduled for the candidate yet.'
      "
      :icon-name="
        errorContent.length
          ? 'fas fa-redo-alt'
          : canScheduleInterview
          ? 'fas fa-plus'
          : ''
      "
      :button-text="
        errorContent.length
          ? 'Retry'
          : canScheduleInterview &&
            formAccessForScheduleInterview &&
            formAccessForScheduleInterview.add
          ? 'Schedule Interview'
          : ''
      "
      @button-click="
        errorContent.length
          ? retrieveCandidateInterviews()
          : redirectToInterviewCandidates()
      "
    >
    </AppFetchErrorScreen>
  </div>
  <div v-else>
    <div class="d-flex justify-end">
      <v-icon size="14" color="grey" @click="retrieveCandidateInterviews()"
        >fas fa-redo-alt</v-icon
      >
    </div>
    <div>
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="lime-darken-2"
          :size="18"
          class="mr-1"
        ></v-progress-circular>
        <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
          >Upcoming Interviews</span
        >
      </div>
      <div
        v-if="upcomingInterviews && upcomingInterviews.length === 0"
        class="d-flex align-center pl-7 py-3 text-subtitle-1 text-grey"
      >
        No upcoming interviews scheduled
      </div>
      <v-card
        v-else
        v-for="(data, index) in upcomingInterviews"
        :key="data.Interview_Id + '-upcoming-' + index"
        class="my-3 rounded-lg"
      >
        <v-row class="pa-4" align="center">
          <v-col
            cols="12"
            md="6"
            lg="5"
            class="text-body-2 d-flex align-center"
          >
            <v-card width="70" v-if="data.Round_Start_Date_Time">
              <v-card-title
                class="bg-red-lighten-1 text-center"
                style="font-size: 1rem; line-height: 1rem"
                >{{
                  formatDateAndTime(data.Round_Start_Date_Time, "month")
                }}</v-card-title
              >
              <v-card-text class="text-center">
                <div class="font-weight-medium mt-1">
                  {{ formatDateAndTime(data.Round_Start_Date_Time, "date") }}
                </div>
                <div class="text-grey mt-1 mb-n1">
                  {{ formatDateAndTime(data.Round_Start_Date_Time, "day") }}
                </div>
              </v-card-text>
            </v-card>
            <div class="pl-4 pr-8" style="line-height: 2rem">
              <div class="text-blue font-weight-medium">
                {{ data.Interview_Name }}
                <span v-if="data.Round_Name">- {{ data.Round_Name }}</span>
              </div>
              <div class="d-flex align-center flex-wrap">
                <span
                  v-if="data.Round_Start_Date_Time"
                  class="text-primary font-weight-medium pr-2"
                >
                  {{ formatDateAndTime(data.Round_Start_Date_Time, "time") }}
                  -
                  {{ formatDateAndTime(data.Round_End_Date_Time, "time") }}
                </span>
                <div class="d-flex align-center">
                  <div
                    style="
                      background: #c4c4c4;
                      border-radius: 50%;
                      height: 10px;
                      width: 10px;
                    "
                  ></div>
                  <span class="pl-2"
                    ><span class="text-grey">Job Title: </span
                    >{{ data.Job_Post_Name }}</span
                  >
                </div>
              </div>
              <div>{{ data.Venue }}</div>
            </div>
          </v-col>
          <v-col cols="12" sm="10" md="4" lg="5" class="text-body-2">
            <v-row style="line-height: 2rem">
              <v-col cols="6" md="4" class="py-0">
                <span class="text-grey">Interviewer</span>
              </v-col>
              <v-col cols="6" md="8" class="py-0">
                {{ formInterviewerNames(data.panelDetails) }}
              </v-col>
              <v-col cols="6" md="4" class="py-0">
                <span class="text-grey">Scheduled By</span>
              </v-col>
              <v-col cols="6" md="8" class="py-0">{{
                checkNullValue(data.Added_By)
              }}</v-col>
            </v-row>
          </v-col>
          <v-col
            v-if="highRole"
            cols="12"
            sm="2"
            md="2"
            lg="2"
            class="d-flex justify-end text-end"
          >
            <CustomSelect
              :items="roundStatusList"
              itemValue="value"
              itemTitle="title"
              label="Round Status"
              density="comfortable"
              :itemSelected="data.Round_result"
              @selected-item="onChangeRoundStatus($event, index, 'upcoming')"
              :disabled="
                candidateStatusList.includes(candidateDetails.Status_Id) ||
                roundStatusIdList.includes(data.Round_result)
              "
            ></CustomSelect>
            <ActionMenu
              v-if="showActionMenuForUpcomingInterviews(data)"
              :actions="
                upcomingInterviewActions(data.Interview_Name, data.Round_result)
              "
              :access-rights="upcomingInterviewActionsAccess"
              @selected-action="
                handleActions($event, 'upcoming', data, null, index)
              "
            ></ActionMenu>
          </v-col>
        </v-row>
      </v-card>
    </div>
    <div class="mt-4">
      <div class="d-flex align-center">
        <v-progress-circular
          model-value="100"
          color="yellow-darken-1"
          :size="18"
          class="mr-1"
        ></v-progress-circular>
        <span class="text-subtitle-1 text-grey-darken-1 font-weight-bold"
          >Feedback</span
        >
      </div>
      <div
        v-if="pastInterviews && pastInterviews.length === 0"
        class="d-flex align-center pl-7 py-3 text-subtitle-1 text-grey"
      >
        No interviews scheduled
      </div>
      <v-card v-else class="ma-3 rounded-lg">
        <div v-if="fetchingFeedbackDetails">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
        <v-row v-else-if="feedbackList.length > 0" class="pa-4" align="center">
          <!-- <v-col cols="12" sm="1">
            <v-progress-circular
              :size="60"
              :width="5"
              color="teal"
              model-value="70"
            >
              <template v-slot:default>
                <v-icon>far fa-star</v-icon>
              </template>
            </v-progress-circular>
          </v-col> -->
          <!-- <v-col cols="12" sm="2" class="text-body-2">
            <div style="line-height: 2rem">
              <div class="font-weight-medium">4.6/5</div>
              <div class="text-grey">Overall Rating</div>
            </div>
          </v-col> -->
          <v-col cols="12" sm="6" md="6" class="text-body-2">
            <!-- <v-progress-linear
              model-value="60"
              height="15"
              rounded="lg"
              color="amber"
              bg-color="red"
            ></v-progress-linear> -->
            <div class="d-flex flex-wrap">
              <div
                v-for="(statusCount, key) in feedbackStatusCount"
                :key="statusCount + '-' + key"
                class="px-2 mb-1"
              >
                <span class="font-weight-medium"
                  >{{ statusCount }}/{{ feedbackList.length }}
                </span>
                -
                <span class="text-grey"> {{ key }}</span>
              </div>
            </div>
          </v-col>
          <v-col
            cols="12"
            sm="6"
            md="6"
            class="text-body-2"
            :class="isMobileView ? 'text-start' : 'text-end'"
          >
            <div>Feedback By</div>
            <div class="mt-2" :class="isMobileView ? '' : 'float-right'">
              <AvatarOrderedList
                :ordered-list="feedbackEmpList"
              ></AvatarOrderedList>
            </div>
          </v-col>
        </v-row>
        <v-divider></v-divider>
        <v-expansion-panels v-model="defaultExpansionPanel">
          <v-expansion-panel
            v-for="(data, index) in pastInterviews"
            :key="data.Interview_Id + '-past-' + index"
            :value="index"
            tile
          >
            <v-expansion-panel-title>
              <div class="text-body-2 text-blue font-weight-medium">
                {{ data.Interview_Name }} - {{ data.Round_Name }}
              </div>
              <template v-slot:actions>
                <v-icon color="blue" size="12">fas fa-chevron-down </v-icon>
              </template>
            </v-expansion-panel-title>
            <v-expansion-panel-text>
              <v-row justify="space-around" no-gutters>
                <v-col
                  cols="12"
                  class="mt-n4 d-flex align-center"
                  :class="isMobileView ? 'flex-column' : ''"
                >
                  {{ formatDateAndTime(data.Round_Start_Date_Time, "all") }}
                  <span class="pl-6 text-grey"
                    >{{ formatDateAndTime(data.Round_Start_Date_Time, "time") }}
                    -
                    {{
                      formatDateAndTime(data.Round_End_Date_Time, "time")
                    }}</span
                  >
                  <span class="pl-6 text-grey"
                    >Job Title: {{ data.Job_Post_Name }}</span
                  >
                  <v-spacer></v-spacer>
                  <div class="d-flex align-center">
                    <CustomSelect
                      :items="roundStatusList"
                      itemValue="value"
                      itemTitle="title"
                      label="Round Status"
                      style="max-width: 200px"
                      density="comfortable"
                      :itemSelected="data.Round_result"
                      :disabled="
                        !highRole ||
                        roundStatusIdList.includes(data.Round_result) ||
                        candidateStatusList.includes(candidateDetails.Status_Id)
                      "
                      class="mt-4"
                      @selected-item="
                        onChangeRoundStatus($event, index, 'past')
                      "
                    ></CustomSelect>
                    <ActionMenu
                      v-if="shouldShowActionMenu(data)"
                      :actions="
                        pastInterviewRoundActions(
                          data.panelDetails,
                          data.Round_result,
                          data.Interview_Name
                        )
                      "
                      :access-rights="formAccessForRound"
                      @selected-action="
                        handleActions($event, 'past', data, null, index)
                      "
                    ></ActionMenu>
                  </div>
                </v-col>
                <v-col cols="12" class="pa-0 mb-4">
                  <div
                    v-if="data.panelDetails.length > 0"
                    class="table-responsive"
                  >
                    <table style="width: 100%; min-width: 600px">
                      <thead>
                        <tr>
                          <th
                            class="text-black font-weight-regular"
                            style="
                              background: #e1e1e1;
                              padding: 8px;
                              text-align: left;
                              white-space: nowrap;
                            "
                            v-for="header in tableHeaders"
                            :key="header"
                          >
                            {{ header }}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="(item, index) in data.panelDetails"
                          :key="index + 'tax-relief-declarations'"
                          class="text-body-2"
                        >
                          <td class="d-flex align-center">
                            <v-avatar color="purple-lighten-3" size="30">
                              {{
                                item.Panel_Member_Name
                                  ? item.Panel_Member_Name.charAt(
                                      0
                                    ).toUpperCase()
                                  : ""
                              }} </v-avatar
                            ><span class="pl-2">{{
                              item.Panel_Member_Name
                            }}</span>
                          </td>
                          <td>
                            <v-skeleton-loader
                              v-if="scoreLoading"
                              ref="skeleton2"
                              type="list-item"
                              class="mx-auto"
                            ></v-skeleton-loader>
                            <div v-else>
                              {{
                                item.Rating
                                  ? item.Rating
                                  : "Feedback not submitted"
                              }}
                            </div>
                          </td>
                          <td>
                            <v-skeleton-loader
                              v-if="scoreLoading"
                              ref="skeleton2"
                              type="list-item"
                              class="mx-auto"
                            ></v-skeleton-loader>
                            <div v-else-if="item.Rating">
                              {{
                                (
                                  parseFloat(item.Candidate_Obtained_Score) /
                                  item.SkillsCategory.reduce(
                                    (acc, category) =>
                                      acc + category.Skills.length,
                                    0
                                  )
                                ).toFixed(2)
                              }}/{{ item.Max_Score_Per_Skill }}
                            </div>
                          </td>
                          <td class="d-flex align-center">
                            <v-skeleton-loader
                              v-if="scoreLoading"
                              ref="skeleton2"
                              type="list-item"
                              class="mx-auto"
                            ></v-skeleton-loader>
                            <div
                              v-else-if="
                                data.Interview_Name == 'Assessment Link'
                                  ? formAccessForAssessmentFeedback &&
                                    formAccessForAssessmentFeedback.update
                                    ? true
                                    : false
                                  : formAccessForInterviewFeedback &&
                                    formAccessForInterviewFeedback.update
                                  ? true
                                  : false
                              "
                            >
                              <v-btn
                                v-if="
                                  !item.Rating &&
                                  canUpdateFeedback(item.Panel_Member_Id) &&
                                  !roundStatusIdList.includes(
                                    data.Round_result
                                  ) &&
                                  !candidateStatusList.includes(
                                    candidateDetails.Status_Id
                                  )
                                "
                                color="blue"
                                variant="text"
                                size="small"
                                density="comfortable"
                                @click="
                                  openFeedbackForm(
                                    item,
                                    data.Interview_Id,
                                    data.Round_Sequence,
                                    'add'
                                  )
                                "
                              >
                                <v-icon size="13" class="pr-1"
                                  >fas fa-plus </v-icon
                                >Add Feedback
                              </v-btn>
                              <div v-else>
                                {{ item.Note ? item.Note : "-" }}
                              </div>
                            </div>
                            <div v-else>
                              {{ item.Note ? item.Note : "-" }}
                            </div>

                            <v-spacer></v-spacer>
                            <ActionMenu
                              v-if="
                                item.Rating ||
                                (item.Rating !== 'Cancelled' &&
                                  canUpdateFeedback(item.Panel_Member_Id) &&
                                  !roundStatusIdList.includes(
                                    data.Round_result
                                  ) &&
                                  !candidateStatusList.includes(
                                    candidateDetails.Status_Id
                                  ))
                              "
                              :actions="
                                item.Rating !== 'Cancelled' &&
                                canUpdateFeedback(item.Panel_Member_Id) &&
                                !roundStatusIdList.includes(
                                  data.Round_result
                                ) &&
                                !candidateStatusList.includes(
                                  candidateDetails.Status_Id
                                )
                                  ? item.Rating || item.Rating === 'Declined'
                                    ? [
                                        'Edit Feedback',
                                        'View Feedback',
                                        'Cancel',
                                      ]
                                    : ['Cancel', 'Decline Feedback']
                                  : ['View Feedback']
                              "
                              :disableActionButtons="
                                getdisabledFields(item, data)
                              "
                              :accessRights="havingAccess"
                              :tooltipActionButtons="['Cancel']"
                              tooltipMessage="Cancel action removes only the panel member from the interview schedule"
                              @selected-action="
                                handleActions($event, 'past', data, item)
                              "
                            ></ActionMenu>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div v-else>No panel members are associated</div>
                </v-col>
              </v-row>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-card>
    </div>
  </div>
  <v-overlay
    v-model="showFeedbackForm"
    class="d-flex justify-end"
    id="integration-form"
    persistent
  >
    <div
      id="overlay-card"
      :style="{
        height: windowHeight + 'px',
        width: windowWidth >= 1264 ? '35vw' : '100vw',
      }"
    >
      <div id="overlay-head" class="bg-primary">
        Feedback
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="showFeedbackForm = false"
        />
      </div>
      <div id="overlay-body">
        <div class="text-body-1 font-weight-medium mb-3">Recommendation</div>
        <v-btn-toggle
          v-model="selectedPanelDetails.Rating"
          color="primary"
          mandatory
          variant="outlined"
          divided
          :disabled="feedbackFormType == 'view'"
        >
          <v-btn value="Not Hire"
            ><span class="text-body-2">Not Hire</span></v-btn
          >
          <v-btn value="Not Sure"
            ><span class="text-body-2">Not Sure</span></v-btn
          >
          <v-btn value="Average"
            ><span class="text-body-2">Average</span></v-btn
          >
          <v-btn value="Hire"><span class="text-body-2">Hire</span></v-btn>
          <v-btn value="Must Hire"
            ><span class="text-body-2">Must Hire</span></v-btn
          >
        </v-btn-toggle>
        <div
          v-if="showRecommendationErrMsg && !selectedPanelDetails.Rating"
          class="text-caption"
          style="color: #b00020"
        >
          Recommendation is required
        </div>
        <div class="text-body-1 font-weight-medium my-3">Feedback</div>
        <v-textarea
          v-model="selectedPanelDetails.Note"
          variant="solo"
          auto-grow
          rows="2"
          :disabled="feedbackFormType == 'view'"
        />
        <div class="text-body-1 font-weight-medium my-3">Score Card</div>
        <v-expansion-panels v-if="selectedPanelDetails">
          <v-expansion-panel
            v-for="(
              skillCategory, categoryIndex
            ) in selectedPanelDetails.SkillsCategory"
            :key="`category-${categoryIndex}`"
          >
            <v-expansion-panel-title>
              {{ checkNullValue(skillCategory.Skill_Category) }}
            </v-expansion-panel-title>
            <v-expansion-panel-text>
              <div
                v-for="(skill, skillIndex) in skillCategory.Skills"
                :key="`skill-${categoryIndex}-${skillIndex}`"
              >
                <div class="d-flex align-center mb-2">
                  <div
                    style="
                      background: #c4c4c4;
                      border-radius: 50%;
                      height: 12px;
                      width: 12px;
                    "
                    class="mr-1"
                  ></div>
                  <span class="font-weight-medium">{{
                    checkNullValue(skill.Skill_Name)
                  }}</span>
                  <v-spacer />
                  <span
                    v-if="skill.Skill_Score"
                    :class="
                      skill.Skill_Score > selectedPanelDetails.Passing_Score
                        ? 'text-green'
                        : 'text-yellow-darken-2'
                    "
                    class="text-h6 font-weight-medium"
                    >{{ checkNullValue(skill.Skill_Score) }}</span
                  >
                </div>

                <v-rating
                  v-model="skill.Skill_Score"
                  :hover="feedbackFormType !== 'view'"
                  half-increments
                  :color="
                    skill.Skill_Score > selectedPanelDetails.Passing_Score
                      ? 'green'
                      : 'yellow-darken-2'
                  "
                  :length="selectedPanelDetails.Max_Score_Per_Skill"
                  :disabled="feedbackFormType === 'view'"
                />
                <div>
                  <p class="text-subtitle-1">Instructions to Interviewer</p>
                  <p class="text-body-1 text-grey-darken-1">
                    {{
                      expandedIndices.includes(skillIndex)
                        ? skill.Questions
                        : truncatedText(skill)
                    }}
                    <v-btn
                      v-if="isTruncatable(skill)"
                      color="primary"
                      variant="text"
                      @click="toggleContent(skillIndex)"
                    >
                      {{
                        expandedIndices.includes(skillIndex)
                          ? "Show Less"
                          : "Show More"
                      }}
                    </v-btn>
                  </p>
                </div>

                <v-textarea
                  v-model="skill.Skill_Comment"
                  variant="solo"
                  auto-grow
                  rows="1"
                  label="Comment"
                  :disabled="feedbackFormType == 'view'"
                  class="mt-2"
                />
                <v-divider class="my-2" />
              </div>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
        <div v-else>-</div>
      </div>
      <v-card class="mx-auto my-0 pa-2 overlay-footer">
        <v-btn
          class="mr-5"
          variant="outlined"
          @click="showFeedbackForm = false"
          rounded="lg"
        >
          Cancel
        </v-btn>
        <v-btn
          v-if="feedbackFormType != 'view'"
          color="primary"
          variant="elevated"
          @click="updateScoreDetails()"
          rounded="lg"
        >
          Submit
        </v-btn>
      </v-card>
    </div>
    <AppLoading v-if="isUpdatingScore"></AppLoading>
  </v-overlay>
  <SelectInterviewType
    v-if="scheduleInterviewOverlay"
    :candidate-details="candidateDetails"
    :candidate-email="[candidateDetails.Personal_Email]"
    :candidateId="candidateIdSelected"
    :candidateName="candidateDetails.Candidate_First_Name"
    :jobPostId="candidateDetails.Job_Post_Id"
    :statusId="candidateDetails.Status_Id"
    :jobTitle="candidateDetails.Job_Post_Name"
    :overlayWindow="scheduleInterviewOverlay"
    @close-interview-schedule-window="handleInterviewSuccess($event)"
  ></SelectInterviewType>
  <InterviewSchedules
    v-if="assessmentLinkOverlay"
    :candidate-details="candidateDetails"
    :candidate-email="[candidateDetails.Personal_Email]"
    :candidateId="candidateIdSelected"
    :candidateName="candidateDetails.Candidate_First_Name"
    :jobPostId="candidateDetails.Job_Post_Id"
    :statusId="candidateDetails.Status_Id"
    :jobTitle="candidateDetails.Job_Post_Name"
    :candidateWillSelect="
      editedInterviewDetails && editedInterviewDetails.Calendar_Link
        ? true
        : false
    "
    :interviewType="selectedInterviewType"
    :actionType="actionType"
    :overlayWindow="assessmentLinkOverlay"
    :editedInterviewDetails="editedInterviewDetails"
    :interviewVenue="venue"
    :editForm="true"
    @close-interview-schedule-window="handleInterviewSuccess($event)"
  ></InterviewSchedules>
  <CancelInterviewOverlayForm
    v-if="showCustomEmail"
    ref="customEmail"
    :candidateId="candidateIdSelected"
    :typeOfTemplate="typeOfTemplate"
    :templateData="templateData"
    :templateEmail="[candidateDetails.Personal_Email]"
    :notificationTimeNow="notificationCandidate"
    @custom-email-sent="customEmailSent"
    @close-cancel-interview-window="onCustomEmailCancel"
  ></CancelInterviewOverlayForm>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import {
  RETRIEVE_CANDIDATE_INTERVIEW,
  RETRIEVE_CANDIDATE_ROUNDS,
  RETRIEVE_CANDIDATE_SCORE_DETAILS,
  UPDATE_CANDIDATE_SCORE,
  GET_ROLE_BASED_JOB_POST_MEMBER,
  UPDATE_INTERVIEW_ROUND_STATUS,
  RETRIEVE_OVERALL_CANDIDATE_INTERVIEW_DETAILS,
  CANCEL_DECLINE_ROUNDS,
} from "@/graphql/recruitment/recruitmentQueries.js";
import {
  RETRIEVE_INTERVIEW_DETAILS,
  // UPDATE_INTERVIEW_DETAILS,
} from "@/graphql/recruitment/interviewScheduleQueries.js";
import { GET_MICROSOFT_CREDENTIALS } from "@/graphql/settings/Integration/jobPostIntegrationQueries";
import { generateRandomColor, checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import moment from "moment";
import SelectInterviewType from "../../SelectInterviewType.vue";
import InterviewSchedules from "../../InterviewSchedules.vue";
import CancelInterviewOverlayForm from "../../CancelInterviewOverlayForm.vue";
import Config from "../../../../../config.js";
import { PublicClientApplication } from "@azure/msal-browser";

export default {
  name: "FeedbackForm",
  components: {
    ActionMenu,
    AvatarOrderedList,
    CustomSelect,
    SelectInterviewType,
    InterviewSchedules,
    CancelInterviewOverlayForm,
  },
  props: {
    candidateIdSelected: {
      type: Number,
      default: 0,
    },
    candidateJobPostIdSelected: {
      type: Number,
      default: 0,
    },
    formAccess: {
      type: [Boolean, Object],
      default: false,
    },
    candidateDetails: {
      type: Object,
      required: true,
    },
    canScheduleInterview: {
      type: Boolean,
      default: true,
    },
    isRecruiter: {
      type: String,
      default: "No",
    },
    parentTabName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      interviewDetails: [],
      upcomingInterviews: [],
      pastInterviews: [],
      listLoading: false,
      errorContent: "",
      isErrorInList: false,
      isLoading: false,
      candidateInterviewWithRounds: [],
      apiCallCount: 0,
      fetchingFeedbackDetails: false,
      feedbackEmpList: [],
      feedbackList: [],
      feedbackStatusCount: {},
      havingAccess: {},
      formAccessForRound: {},
      upcomingInterviewActionsAccess: {},
      defaultExpansionPanel: 0,
      candidateSkills: {},
      openedPanelMemberCount: 0,
      scoreApiCallCount: 0,
      scoreLoading: false,
      showFeedbackForm: false,
      feedbackFormType: "",
      selectedPanelDetails: {},
      isUpdatingScore: false,
      loginEmpRole: "",
      venue: "",
      showRecommendationErrMsg: false,
      clientId: "",
      roundStatusIdList: ["14", "15", "16", "17", "18"],
      roundStatusList: [
        {
          value: "14",
          title: "Not Hire",
        },
        {
          value: "15",
          title: "Not Sure",
        },
        {
          value: "16",
          title: "Average",
        },
        {
          value: "17",
          title: "Hire",
        },
        {
          value: "18",
          title: "Must Hire",
        },
        {
          value: "27",
          title: "No Show",
        },
      ],
      // rejected, hired, not hired, offer accepted, offer declined, onboarding, onboarded, offer rolled out
      candidateStatusList: [3, 19, 20, 21, 22, 23, 24, 25],
      scheduleInterviewOverlay: false,
      assessmentLinkOverlay: false,
      selectedInterviewType: "",
      editedInterviewDetails: {},
      //Custom Email
      showCustomEmail: false,
      selectedIndex: null,
      selectedType: null,
      templateData: {},
      expandedIndices: [],
      typeOfTemplate: "roundCancelledCandidate",
      notificationCandidate: true,
      actionType: "",
    };
  },
  computed: {
    redirectUri() {
      return `${this.baseUrl}v3/recruitment/my-integration`;
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    addressLine1() {
      let organization = {};
      if (this.fieldForce) {
        organization = this.$store.state.orgDetails.serviceProvider;
      } else {
        organization = this.$store.state.orgDetails.organization;
      }
      let line1 = [];
      if (organization.street1) {
        line1.push(organization.street1);
      }
      if (organization.street2) {
        line1.push(organization.street2);
      }
      return line1.length > 0 ? line1.join(",") : "";
    },
    addressLine2() {
      const { organization } = this.$store.state.orgDetails;
      let line2 = [];
      if (organization.city) {
        line2.push(organization.city);
      }
      if (organization.state) {
        line2.push(organization.state);
      }
      if (organization.country) {
        line2.push(organization.country);
      }
      if (organization.pincode) {
        line2.push(organization.pincode);
      }
      return line2.length > 0 ? line2.join(",") : "";
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    loginEmployeeName() {
      return this.$store.state.userDetails.employeeFirstName;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    tableHeaders() {
      return [
        "Panel Member",
        "Overall Decision",
        "Scorecard Average",
        "Feedback",
      ];
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    highRole() {
      return (
        this.loginEmpRole === "Admin" ||
        this.loginEmpRole === "Recruiter" ||
        this.loginEmpRole === "Hiring Manager" ||
        this.isRecruiter.toLowerCase() === "yes"
      );
    },
    canUpdateFeedback() {
      return (panelMemberId) => {
        if (this.highRole) {
          return true;
        } else if (this.loginEmpRole === "Panel Member") {
          return panelMemberId == this.loginEmployeeId;
        } else {
          return false;
        }
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccessForAssessmentFeedback() {
      let formAccess = this.accessRights("271");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForInterviewFeedback() {
      let formAccess = this.accessRights("273");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForScheduleInterview() {
      let formAccess = this.accessRights("272");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    formAccessForSendAssessment() {
      let formAccess = this.accessRights("275");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
  },
  watch: {
    apiCallCount(count) {
      if (count > 0 && count === this.interviewDetails.length) {
        const beforeCurrentDate = [];
        const onOrAfterCurrentDate = [];
        const currentDateTime = moment();

        this.candidateInterviewWithRounds.forEach((interview) => {
          const startDateTime = moment(interview.Round_Start_Date_Time);
          let formattedStartDateTime = startDateTime.format(
            "YYYY-MM-DD HH:mm:ss"
          );
          let formattedCurrentDateTime = currentDateTime.format(
            "YYYY-MM-DD HH:mm:ss"
          );
          if (
            moment(formattedStartDateTime).isBefore(formattedCurrentDateTime) ||
            this.roundStatusIdList.includes(interview.Round_result)
          ) {
            beforeCurrentDate.push(interview);
          } else {
            onOrAfterCurrentDate.push(interview);
          }
        });
        // Sort interviews by 'Round_Start_Date_Time' in descending order (most recent first)
        onOrAfterCurrentDate.sort((a, b) => {
          const dateA = new Date(a.Round_Start_Date_Time);
          const dateB = new Date(b.Round_Start_Date_Time);
          return dateB - dateA; // Sort descending
        });
        beforeCurrentDate.sort((a, b) => {
          const dateA = new Date(a.Round_Start_Date_Time);
          const dateB = new Date(b.Round_Start_Date_Time);
          return dateB - dateA; // Sort descending
        });
        this.upcomingInterviews = onOrAfterCurrentDate;
        this.pastInterviews = beforeCurrentDate;
        this.listLoading = false;
        this.getRoundBasedPanelMembers(this.defaultExpansionPanel);
      }
    },
    scoreApiCallCount(count) {
      if (count > 0 && count === this.openedPanelMemberCount) {
        this.scoreLoading = false;
      }
    },
    defaultExpansionPanel(val) {
      this.getRoundBasedPanelMembers(val);
    },
  },
  mounted() {
    let isFormAdmin = this.formAccess.admin === "admin";
    if (isFormAdmin) {
      this.loginEmpRole = "Admin";
    } else if (this.candidateJobPostIdSelected) {
      this.getRoleBasedJobPostMember();
    }
    this.retrieveCandidateInterviews();
  },
  methods: {
    generateRandomColor,
    checkNullValue,
    isTruncatable(skill) {
      return skill?.Questions && skill.Questions.length > 200;
    },
    truncatedText(skill) {
      const text = skill?.Questions || "";
      return text.length > 200 ? `${text.slice(0, 200)}...` : text;
    },
    toggleContent(skillIndex) {
      const index = this.expandedIndices.indexOf(skillIndex);
      if (index !== -1) {
        this.expandedIndices.splice(index, 1); // Remove index if already expanded
      } else {
        this.expandedIndices.push(skillIndex); // Add index if not expanded
      }
    },
    onCustomEmailCancel() {
      this.selectedIndex = null;
      this.selectedType = null;
      this.showCustomEmail = false;
      this.notificationCandidate = true;
    },
    upcomingInterviewActions(interviewType, status) {
      let options = [];
      if (status && status.toLowerCase() !== "cancelled") {
        options.push("Edit/Reschedule", "Cancel");
      }
      this.upcomingInterviewActionsAccess["cancel"] = this
        .formAccessForSendAssessment.update
        ? this.formAccessForSendAssessment.update
        : 0;
      if (interviewType === "Assessment Link") {
        if (this.formAccessForSendAssessment.add) {
          this.upcomingInterviewActionsAccess["edit/reschedule"] = this
            .formAccessForSendAssessment.add
            ? this.formAccessForSendAssessment.add
            : 0;
        }
      } else {
        if (this.formAccessForScheduleInterview.add) {
          this.upcomingInterviewActionsAccess["edit/reschedule"] = this
            .formAccessForScheduleInterview.add
            ? this.formAccessForScheduleInterview.add
            : 0;
        }
      }
      return options;
    },
    async customEmailSent() {
      if (this.typeOfTemplate == "candidateNoShow") {
        this.showCustomEmail = false;
        this.updateRoundStatus(27, this.selectedIndex, this.selectedType);
        this.selectedIndex = null;
        this.selectedType = null;
        this.notificationCandidate = true;
      } else {
        let panelMemberData = this.formPanelMemberEmail();
        let cancelInterviewRef = this.$refs.customEmail
          ? this.$refs.customEmail
          : null;
        let customEmailRef = cancelInterviewRef
          ? cancelInterviewRef.getCustomEmailRef()
          : null;
        if (panelMemberData && customEmailRef && this.notificationCandidate) {
          //Send Custom Email for Panel Members
          await customEmailRef.sendCustomEmail(panelMemberData);
        }
        this.showCustomEmail = false;
        this.notificationCandidate = true;
        this.onCancelRound(this.selectedIndex, this.selectedType);
        this.selectedIndex = null;
        this.selectedType = null;
      }
    },
    formPanelMemberEmail() {
      let panelMemberEmail = this.candidateInterviewWithRounds[
        this.selectedIndex
      ].panelDetails
        .filter((el) => el.Panel_Member_Email && el.Panel_Member_Email.length)
        .map((el) => el.Panel_Member_Email);
      if (panelMemberEmail && panelMemberEmail.length) {
        let templateData = this.formTemplateDetails();
        let panelMemberData = {
          formId: 16,
          typeOfInterview: "onlineinterview",
          typeOfTemplate: "roundCancelledPanelMember",
          typeOfSchedule: "noncalendar",
          bccEmails: panelMemberEmail,
          templateData: templateData,
          location: null,
          description: null,
        };
        return panelMemberData;
      }
      return null;
    },
    formTemplateDetails() {
      let templateData = { ...this.candidateDetails };
      //Include other data
      templateData.Candidate_Name = this.candidateDetails.Candidate_First_Name;
      templateData.Company_Name = this.companyName;
      templateData.Recruiter_Name = this.loginEmployeeName;
      templateData.Company_Address_1 = this.addressLine1;
      templateData.Company_Address_2 = this.addressLine2;
      templateData.Round_Name =
        this.candidateInterviewWithRounds[this.selectedIndex].Interview_Name +
        " - " +
        this.candidateInterviewWithRounds[this.selectedIndex].Round_Name
          ? this.candidateInterviewWithRounds[this.selectedIndex].Round_Name
          : "Round";
      return templateData;
    },
    handleActions(selectedAction, type, data, selectedItem, index) {
      if (selectedAction === "Edit Feedback") {
        this.openFeedbackForm(
          selectedItem,
          data.Interview_Id,
          data.Round_Sequence,
          "edit"
        );
      } else if (selectedAction === "View Feedback") {
        this.openFeedbackForm(
          selectedItem,
          data.Interview_Id,
          data.Round_Sequence,
          "view"
        );
      } else if (selectedAction === "Decline Feedback") {
        this.onDeclineFeedback(data.Interview_Id, selectedItem, type);
      } else if (selectedAction === "Cancel" && type == "upcoming") {
        this.selectedIndex = index;
        this.selectedType = type;
        this.templateData = this.formTemplateDetails();
        this.showCustomEmail = true;
      } else if (selectedAction === "Cancel") {
        this.onCancelRound(
          null,
          type,
          data.Interview_Id,
          selectedItem,
          "cancelled"
        );
      } else if (selectedAction === "Edit/Reschedule") {
        this.retrieveInterviewDetails(data);
      } else if (selectedAction === "Cancel Round") {
        if (data.PanelMember_Calendar_Event_Id) {
          this.notificationCandidate = false;
        }
        this.selectedIndex = index;
        this.selectedType = type;
        this.templateData = this.formTemplateDetails();
        this.showCustomEmail = true;
      }
    },
    getRoundBasedPanelMembers(index) {
      if (this.pastInterviews[index]) {
        this.scoreLoading = true;
        this.scoreApiCallCount = 0;
        this.openedPanelMemberCount =
          this.pastInterviews[index].panelDetails.length;
        if (this.openedPanelMemberCount) {
          let panelDetails = this.pastInterviews[index].panelDetails;
          for (let panel of panelDetails) {
            this.retrieveCandidateScores(
              this.pastInterviews[index].Interview_Id,
              this.pastInterviews[index].Round_Id,
              panel.Panel_Member_Id
            );
          }
        } else {
          this.scoreLoading = false;
        }
      }
    },
    redirectToInterviewCandidates() {
      this.scheduleInterviewOverlay = true;
    },
    handleInterviewSuccess(isSuccess) {
      this.isLoading = false;
      this.scheduleInterviewOverlay = false;
      this.assessmentLinkOverlay = false;
      if (isSuccess) {
        this.retrieveCandidateInterviews();
      }
    },
    retrieveCandidateInterviews() {
      let vm = this;
      vm.retrieveCandidateOverallScores();
      vm.listLoading = true;
      vm.errorContent = "";
      vm.isErrorInList = false;
      vm.candidateInterviewWithRounds = [];
      vm.apiCallCount = 0;
      vm.$apollo
        .query({
          query: RETRIEVE_CANDIDATE_INTERVIEW,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: vm.candidateIdSelected,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveCandidatesInterview &&
            !response.data.retrieveCandidatesInterview.errorCode
          ) {
            const { candidateInterview } =
              response.data.retrieveCandidatesInterview;
            vm.interviewDetails = candidateInterview;
            if (candidateInterview && candidateInterview.length > 0) {
              for (let interview of vm.interviewDetails) {
                vm.retrieveCandidateRoundsBasedOnInterview(interview);
              }
            } else {
              vm.listLoading = false;
            }
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    retrieveCandidateRoundsBasedOnInterview(interview) {
      let vm = this;
      vm.$apollo
        .query({
          query: RETRIEVE_CANDIDATE_ROUNDS,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: vm.candidateIdSelected,
            interviewId: interview.Interview_Id,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveCandidatesRounds &&
            !response.data.retrieveCandidatesRounds.errorCode
          ) {
            const { candidateRounds } = response.data.retrieveCandidatesRounds;
            if (candidateRounds && candidateRounds.length > 0) {
              vm.candidateInterviewWithRounds =
                vm.candidateInterviewWithRounds.concat(candidateRounds);
            }
            // Find objects in candidateInterviewWithRounds that have the same Interview_Id as interview
            for (let obj of vm.candidateInterviewWithRounds) {
              if (obj.Interview_Id === interview.Interview_Id) {
                // Merge interview properties into the current object (obj) from candidateInterviewWithRounds
                for (let key in interview) {
                  // Check if the key doesn't already exist in the current object
                  if (!(key in obj)) {
                    // Add the key-value pair from interview to the current object
                    obj[key] = interview[key];
                  }
                }
              }
            }
            vm.apiCallCount += 1;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "status of the candidate details",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    formatDateAndTime(inputDate, type) {
      let date = moment(inputDate + ".000Z");
      if (!date.isValid()) {
        return "";
      }
      if (type === "date") {
        // Format to get the date (day of the month)
        return date.format("DD");
      } else if (type === "day") {
        // Format to get the day of the week in uppercase
        return date.format("ddd").toUpperCase();
      } else if (type === "month") {
        // Format to get the month in uppercase
        return date.format("MMM").toUpperCase();
      } else if (type === "time") {
        // Format to get the time
        return date.format("HH:mm");
      } else if (type === "all") {
        // Format to get the time
        return date.format("MMM DD YYYY dddd");
      } else {
        // Handle invalid type
        return ""; // or throw an error
      }
    },
    formInterviewerNames(panelMembers) {
      if (panelMembers && panelMembers.length > 0) {
        // Extract Panel_Member_Name values and join them with commas
        const names = panelMembers
          .map((member) => member.Panel_Member_Name.trim())
          .join(", ");
        return names;
      } else return "-";
    },
    openFeedbackForm(item, interviewId, roundSeq, action) {
      let interviewObj = {
        Interview_Id: interviewId,
        Round_Sequence: roundSeq,
      };
      this.selectedPanelDetails = { ...item, ...interviewObj };
      this.feedbackFormType = action;
      this.showFeedbackForm = true;
    },
    retrieveCandidateScores(interviewId, roundId, panelMemberId) {
      let vm = this;
      if (
        parseInt(interviewId) &&
        parseInt(roundId) &&
        parseInt(panelMemberId)
      ) {
        vm.$apollo
          .query({
            query: RETRIEVE_CANDIDATE_SCORE_DETAILS,
            client: "apolloClientA",
            fetchPolicy: "no-cache",
            variables: {
              candidateId: vm.candidateIdSelected,
              interviewId: parseInt(interviewId),
              roundId: parseInt(roundId),
              panelMemberId: parseInt(panelMemberId),
            },
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveCandidateSkillsScore &&
              !response.data.retrieveCandidateSkillsScore.errorCode
            ) {
              const { candidateSkills } =
                response.data.retrieveCandidateSkillsScore;
              // Find the index in vm.pastInterviews based on Interview_Id and Round_Id
              let matchedIndex = vm.pastInterviews.findIndex(
                (item) =>
                  item.Interview_Id === interviewId && item.Round_Id === roundId
              );

              if (matchedIndex !== -1) {
                // Get the panelDetails array from the matched element
                let panelDetailsArray =
                  vm.pastInterviews[matchedIndex].panelDetails;

                // Find the index of the panelDetails object with matching Panel_Member_Id
                let panelIndex = panelDetailsArray.findIndex(
                  (panel) => panel.Panel_Member_Id === panelMemberId
                );

                if (panelIndex !== -1) {
                  vm.pastInterviews[matchedIndex].panelDetails[panelIndex] = {
                    ...vm.pastInterviews[matchedIndex].panelDetails[panelIndex],
                    ...candidateSkills,
                  };
                }
              }
              vm.candidateSkills = candidateSkills;
              vm.scoreApiCallCount += 1;
            } else {
              vm.scoreApiCallCount += 1;
            }
          })
          .catch(() => {
            vm.scoreApiCallCount += 1;
          });
      } else {
        vm.scoreApiCallCount += 1;
      }
    },
    updateScoreDetails() {
      let vm = this;
      if (vm.selectedPanelDetails.Rating) {
        this.showRecommendationErrMsg = false;
        vm.isUpdatingScore = true;
        let skillScore = 0,
          skillArray = [];
        // Check if SkillsCategory exists and process its data
        if (
          vm.selectedPanelDetails.SkillsCategory &&
          vm.selectedPanelDetails.SkillsCategory.length > 0
        ) {
          for (let skillCategory of vm.selectedPanelDetails.SkillsCategory) {
            if (skillCategory.Skills && skillCategory.Skills.length > 0) {
              for (let skill of skillCategory.Skills) {
                skillScore += skill.Skill_Score || 0; // Ensure score defaults to 0 if null
                skillArray.push({
                  skillId: skill.Skill_Id,
                  skillScore: skill.Skill_Score || 0,
                  skillComment: skill.Skill_Comment || "",
                });
              }
            }
          }
        }

        vm.$apollo
          .mutate({
            mutation: UPDATE_CANDIDATE_SCORE,
            variables: {
              interviewId: vm.selectedPanelDetails.Interview_Id,
              formId:
                this.selectedInterviewType == "Assessment Link" ? 271 : 273,
              roundId: vm.selectedPanelDetails.Round_Id,
              panelMemberId: parseInt(vm.selectedPanelDetails.Panel_Member_Id),
              candidateId: vm.candidateIdSelected,
              note: vm.selectedPanelDetails.Note,
              rating: vm.selectedPanelDetails.Rating,
              candidateScore: skillScore,
              passingScore: vm.selectedPanelDetails.Passing_Score
                ? vm.selectedPanelDetails.Passing_Score
                : 0,
              noOfPanelMembers: skillArray.length,
              roundSequence: vm.selectedPanelDetails.Round_Sequence,
              skills: skillArray,
            },
            client: "apolloClientA",
          })
          .then(() => {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate score updated successfully",
            };
            vm.showAlert(snackbarData);
            vm.isUpdatingScore = false;
            vm.showFeedbackForm = false;
            vm.retrieveCandidateInterviews();
          })
          .catch((err) => {
            vm.handleUpdateError(err);
          });
      } else {
        this.showRecommendationErrMsg = true;
      }
    },
    handleUpdateError(err = "") {
      this.isUpdatingScore = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "submitting",
        form: "candidate score details",
        isListError: false,
      });
    },
    getRoleBasedJobPostMember() {
      let vm = this;
      vm.loginEmpRole = "";
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_ROLE_BASED_JOB_POST_MEMBER,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            jobPostId: parseInt(vm.candidateJobPostIdSelected),
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getRoleBasedJobPostMember &&
            !response.data.getRoleBasedJobPostMember.errorCode.length
          ) {
            // Recruiters
            let recruiters = response.data.getRoleBasedJobPostMember.recruiters;
            let filteredRecruiter = recruiters.filter(
              (el) => el.Employee_Id == vm.loginEmployeeId
            );
            if (filteredRecruiter && filteredRecruiter.length > 0) {
              vm.loginEmpRole = "Recruiter";
            }
            // Hiring Managers
            if (!vm.loginEmpRole) {
              let hiringManagers =
                response.data.getRoleBasedJobPostMember.hiringManager;
              let filteredHiringManager = hiringManagers.filter(
                (el) => el.Employee_Id == vm.loginEmployeeId
              );
              if (filteredHiringManager && filteredHiringManager.length > 0) {
                vm.loginEmpRole = "Hiring Manager";
              }
            }
            // Panel Members
            if (!vm.loginEmpRole) {
              let panelMembers =
                response.data.getRoleBasedJobPostMember.panelMembers;
              let filteredPanelMember = panelMembers.filter(
                (el) => el.Employee_Id == vm.loginEmployeeId
              );
              if (filteredPanelMember && filteredPanelMember.length > 0) {
                vm.loginEmpRole = "Panel Member";
              }
            }
            vm.isLoading = false;
          } else {
            vm.loginEmpRole = "";
            vm.isLoading = false;
          }
        })
        .catch(() => {
          vm.loginEmpRole = "";
          vm.isLoading = false;
        });
    },
    onChangeRoundStatus(value, index, type) {
      let vm = this;
      if (value == 27) {
        vm.selectedIndex = index;
        vm.selectedType = type;
        vm.templateData = this.formTemplateDetails();
        vm.typeOfTemplate = "candidateNoShow";
        vm.showCustomEmail = true;
      } else {
        vm.updateRoundStatus(value, index, type);
      }
    },

    updateRoundStatus(value, index, type) {
      let vm = this;
      vm.isLoading = true;
      if (type === "upcoming") {
        vm.upcomingInterviews[index]["Round_result"] = value;
      } else {
        vm.pastInterviews[index]["Round_result"] = value;
      }
      vm.$apollo
        .mutate({
          mutation: UPDATE_INTERVIEW_ROUND_STATUS,
          client: "apolloClientAM",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: vm.candidateIdSelected,
            interviewId:
              type === "upcoming"
                ? parseInt(vm.upcomingInterviews[index].Interview_Id)
                : parseInt(vm.pastInterviews[index].Interview_Id),
            roundId:
              type === "upcoming"
                ? parseInt(vm.upcomingInterviews[index].Round_Id)
                : parseInt(vm.pastInterviews[index].Round_Id),
            roundStatus: parseInt(value),
          },
        })
        .then(() => {
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Round status updated successfully",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.retrieveCandidateInterviews();
        })
        .catch((err) => {
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "updating",
            form: "round status",
            isListError: true,
          });
          vm.isLoading = false;
        });
    },
    pastInterviewRoundActions(item, roundStatusId, Interview_Name) {
      let actionList = [],
        isFeedbackGive = "",
        cancelFeedbackCount = 0;
      for (let panel of item) {
        if (panel.Rating) {
          isFeedbackGive = true;
          if (panel.Rating == "Cancelled") {
            cancelFeedbackCount += 1;
          }
        }
      }
      if (!isFeedbackGive && roundStatusId != "27") {
        if (Interview_Name == "Assessment Link") {
          if (this.formAccessForSendAssessment.add) {
            this.formAccessForRound["edit/reschedule"] =
              this.formAccessForSendAssessment.add;
            this.formAccessForRound["cancel round"] =
              this.formAccessForSendAssessment.add;
          } else {
            this.formAccessForRound["edit/reschedule"] = 0;
            this.formAccessForRound["cancel round"] = 0;
          }
        } else {
          if (this.formAccessForScheduleInterview.add) {
            this.formAccessForRound["edit/reschedule"] =
              this.formAccessForScheduleInterview.add;
            this.formAccessForRound["cancel round"] =
              this.formAccessForScheduleInterview.add;
          } else {
            this.formAccessForRound["edit/reschedule"] = 0;
            this.formAccessForRound["cancel round"] = 0;
          }
        }
        actionList.push("Edit/Reschedule");
      }
      if (cancelFeedbackCount != item.length) {
        actionList.push("Cancel Round");
      }
      return actionList;
    },
    onCancelRound(
      index,
      type,
      interviewId,
      selectedItem,
      cancelType = "round cancelled"
    ) {
      let cancelItem = {};
      if (type === "upcoming") {
        cancelItem = this.upcomingInterviews[index];
      } else {
        cancelItem = this.pastInterviews[index];
      }
      let cancelPanelItem = [];
      if (index != null) {
        for (let cancel of cancelItem.panelDetails) {
          cancelPanelItem.push({
            interviewId: cancelItem.Interview_Id,
            roundId: cancelItem.Round_Id,
            panelMemberId: cancel.Panel_Member_Id,
            candidateId: this.candidateIdSelected,
            note: "Cancelled",
          });
        }
      } else {
        cancelPanelItem = [
          {
            interviewId: interviewId,
            roundId: selectedItem.Round_Id,
            panelMemberId: selectedItem.Panel_Member_Id,
            candidateId: this.candidateIdSelected,
            note: "Cancelled",
          },
        ];
      }
      this.cancelDeclineRounds("Cancelled", cancelPanelItem, cancelType);
    },
    onDeclineFeedback(interviewId, selectedItem) {
      let declinedItem = [
        {
          interviewId: interviewId,
          roundId: selectedItem.Round_Id,
          panelMemberId: selectedItem.Panel_Member_Id,
          candidateId: this.candidateIdSelected,
          note: "Declined",
        },
      ];
      this.cancelDeclineRounds("Declined", declinedItem);
    },
    cancelDeclineRounds(status, statusItem, cancelType = "") {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: CANCEL_DECLINE_ROUNDS,
          variables: {
            status: status,
            rating: status,
            candidateRounds: statusItem,
            action: cancelType,
          },
          client: "apolloClientBE",
        })
        .then(() => {
          var snackbarData = {
            isOpen: true,
            type: "success",
            message:
              status === "Cancelled"
                ? "Candidate round cancelled successfully"
                : "Candidate feedback declined successfully",
          };
          vm.showAlert(snackbarData);
          vm.isLoading = false;
          vm.retrieveCandidateInterviews();
        })
        .catch((err) => {
          if (
            err &&
            err.graphQLErrors &&
            err.graphQLErrors[0] &&
            err.graphQLErrors[0].message
          ) {
            let { extensions } = err.graphQLErrors[0];
            if (extensions && extensions.code == "INVALID_MIS_TOKEN") {
              this.getAccessTokenFromRefreshToken(
                "cancelDeclineRounds",
                status,
                statusItem,
                cancelType
              );
            } else if (extensions && extensions.code == "MIS0103")
              this.fetchAuthToken(
                "cancelDeclineRounds",
                status,
                statusItem,
                cancelType
              );
            else {
              vm.$store.dispatch("handleApiErrors", {
                error: err,
                action: "retrieving",
                form: "schedule",
                isListError: false,
              });
              vm.isLoading = false;
            }
          } else {
            vm.$store.dispatch("handleApiErrors", {
              error: err,
              action: "retrieving",
              form: "schedule",
              isListError: false,
            });
          }
        });
    },
    async getAccessTokenFromRefreshToken(
      functionName,
      status,
      statusItem,
      cancelType = ""
    ) {
      const msalConfig = {
        auth: {
          clientId: this.clientId,
          authority: Config.microsoftLogin,
          redirectUri: this.redirectUri,
        },
        cache: {
          cacheLocation: "localStorage",
          storeAuthStateInCookie: true,
        },
      };
      const msalInstance = new PublicClientApplication(msalConfig);
      try {
        await msalInstance.initialize();
        const tokenRequest = {
          scopes: [
            "User.Read",
            "Calendars.ReadWrite",
            "OnlineMeetings.ReadWrite",
          ],
          account: msalInstance.getAllAccounts()[0],
        };
        const response = await msalInstance.acquireTokenSilent(tokenRequest);
        localStorage.setItem("outlookAccess", response.accessToken);
        this[functionName](status, statusItem, cancelType);
      } catch (error) {
        let msg = error.response?.data?.error_description?.split(".")[0];
        let snackbarData = {
          isOpen: true,
          message: msg,
          type: "warning",
        };
        this.showAlert(snackbarData);
        this.fetchAuthToken(functionName);
      }
    },
    getMicrosoftCredentials() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_MICROSOFT_CREDENTIALS,
          variables: {
            Type: "microsoft calendar",
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getAWSCognitoIdentities &&
            response.data.getAWSCognitoIdentities.data &&
            response.data.getAWSCognitoIdentities.data.workwiselymsapplicationID
          ) {
            let { workwiselymsapplicationID } =
              response.data.getAWSCognitoIdentities.data;
            vm.clientId = workwiselymsapplicationID;
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleGetMicrosoftCredentialsError(err);
        });
    },
    handleGetMicrosoftCredentialsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "microsoft credentials",
        isListError: false,
      });
    },
    async fetchAuthToken(functionName) {
      const msalConfig = {
        auth: {
          clientId: this.clientId,
          authority: Config.microsoftLogin,
          redirectUri: this.redirectUri,
        },
        cache: {
          cacheLocation: "localStorage",
          storeAuthStateInCookie: true,
        },
      };
      let scopes = [
        "User.Read",
        "Calendars.ReadWrite",
        "OnlineMeetings.ReadWrite",
      ];
      const msalInstance = new PublicClientApplication(msalConfig);
      try {
        await msalInstance.initialize();
        let response = await msalInstance.loginPopup({
          scopes: scopes,
          prompt: "consent",
        });
        localStorage.setItem("outlookAccess", response.accessToken);
        this[functionName]();
      } catch (err) {
        let snackbarData = {
          isOpen: true,
          message: err,
          type: "warning",
        };
        this.showAlert(snackbarData);
      }
    },
    retrieveCandidateOverallScores() {
      let vm = this;
      vm.fetchingFeedbackDetails = true;
      vm.$apollo
        .query({
          query: RETRIEVE_OVERALL_CANDIDATE_INTERVIEW_DETAILS,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: vm.candidateIdSelected,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveCandidatesInterviewRounds &&
            !response.data.retrieveCandidatesInterviewRounds.errorCode
          ) {
            const { interviewRoundList } =
              response.data.retrieveCandidatesInterviewRounds;
            vm.feedbackEmpList = [];
            vm.feedbackList = interviewRoundList.filter(
              (el) => el.Rating !== "Declined"
            );
            const ratingList = [
              "Not Hire",
              "Not Sure",
              "Average",
              "Hire",
              "Must Hire",
            ];

            // Initialize object to store counts
            const statusCount = {};

            // Initialize counts to zero for each status in ratingList
            ratingList.forEach((status) => {
              statusCount[status] = 0;
            });
            for (let roundList of vm.feedbackList) {
              vm.feedbackEmpList.push({
                employeeName: roundList.Panel_Member_Name,
              });
              if (statusCount.hasOwnProperty(roundList.Rating)) {
                statusCount[roundList.Rating]++;
              }
            }
            vm.feedbackEmpList = vm.removeDuplicates(
              vm.feedbackEmpList,
              "employeeName"
            );
            vm.feedbackStatusCount = statusCount;
          }
          vm.fetchingFeedbackDetails = false;
        })
        .catch(() => {
          vm.fetchingFeedbackDetails = false;
        });
    },
    // Function to remove duplicates based on employeeName
    removeDuplicates(array, key) {
      const seen = new Set();
      return array.filter((item) => {
        const value = item[key];
        if (!seen.has(value)) {
          seen.add(value);
          return true;
        }
        return false;
      });
    },
    retrieveInterviewDetails(data) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_INTERVIEW_DETAILS,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            interviewId: parseInt(data.Interview_Id),
            employeeId: vm.loginEmployeeId,
            formName: "Job Candidates",
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveInterview &&
            !response.data.retrieveInterview.errorCode
          ) {
            const { interviewInfo } = response.data.retrieveInterview;
            this.venue = interviewInfo.Venue;
            vm.selectedInterviewType = data.Interview_Name;
            vm.editedInterviewDetails = interviewInfo;
            vm.assessmentLinkOverlay = true;
            vm.actionType = "edit/reschedule";
            vm.isLoading = false;
          } else {
            vm.handleInterviewRetrieveError();
          }
        })
        .catch((err) => {
          vm.handleInterviewRetrieveError(err);
        });
    },
    handleInterviewRetrieveError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "interview details",
        isListError: true,
      });
    },
    getdisabledFields(item, data) {
      let actions = [];
      if (item.Rating) {
        this.havingAccess["view feedback"] = true;
      }
      if (item.Rating || item.Rating === "Declined") {
        if (data.Interview_Name === "Assessment Link") {
          if (this.formAccessForAssessmentFeedback.update) {
            this.havingAccess["edit feedback"] =
              this.formAccessForAssessmentFeedback.update;
            this.havingAccess["cancel"] =
              this.formAccessForAssessmentFeedback.update;
            actions = [];
          } else {
            this.havingAccess["edit feedback"] = 0;
            this.havingAccess["cancel"] = 0;
            actions = ["Edit Feedback", "Cancel"];
          }
        } else {
          if (this.formAccessForInterviewFeedback.update) {
            this.havingAccess["edit feedback"] =
              this.formAccessForInterviewFeedback.update;
            this.havingAccess["cancel"] =
              this.formAccessForInterviewFeedback.update;
            actions = [];
          } else {
            this.havingAccess["edit feedback"] = 0;
            this.havingAccess["cancel"] = 0;
            actions = ["Edit Feedback", "Cancel"];
          }
        }
      } else {
        if (data.Interview_Name === "Assessment Link") {
          if (this.formAccessForAssessmentFeedback.update) {
            this.havingAccess["decline feedback"] =
              this.formAccessForAssessmentFeedback.update;
            this.havingAccess["cancel"] =
              this.formAccessForAssessmentFeedback.update;
            actions = [];
          } else {
            this.havingAccess["decline feedback"] = 0;
            this.havingAccess["cancel"] = 0;
            actions = ["Cancel", "Decline Feedback"];
          }
        } else {
          if (this.formAccessForInterviewFeedback.update) {
            this.havingAccess["decline feedback"] =
              this.formAccessForInterviewFeedback.update;
            this.havingAccess["cancel"] =
              this.formAccessForInterviewFeedback.update;
            actions = [];
          } else {
            this.havingAccess["decline feedback"] = 0;
            this.havingAccess["cancel"] = 0;
            actions = ["Cancel", "Decline Feedback"];
          }
        }
      }

      return actions;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    shouldShowActionMenu(data) {
      return (
        this.highRole &&
        !this.roundStatusIdList.includes(data.Round_result) &&
        !this.candidateStatusList.includes(this.candidateDetails.Status_Id) &&
        this.pastInterviewRoundActions(data.panelDetails, data.Round_result)
          .length > 0 &&
        this.parentTabName?.toLowerCase() !== "archived"
      );
    },
    showActionMenuForUpcomingInterviews(data) {
      return (
        this.highRole &&
        !this.roundStatusIdList.includes(data.Round_result) &&
        !this.candidateStatusList.includes(this.candidateDetails.Status_Id) &&
        data.Round_result != "27" &&
        this.parentTabName?.toLowerCase() !== "archived" &&
        data.Round_result?.toLowerCase() !== "cancelled"
      );
    },
  },
};
</script>

<style scoped>
table {
  border-collapse: collapse;
  width: 100%;
}

th {
  text-align: left;
  padding: 8px;
}

td {
  text-align: left;
  padding: 8px;
  background-color: #ffffff;
}

th:first-child {
  position: sticky;
  left: 0;
  border: 0px;
}

th:last-child {
  border: 0px;
}

thead th {
  position: sticky;
  top: 0;
  z-index: 2000;
}

@media screen and (max-width: 600px) {
  thead {
    display: contents !important;
  }
  thead th {
    position: relative;
  }
  th:first-child {
    position: relative;
  }
}
#overlay-card {
  background: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
#overlay-head {
  background: rgb(238, 236, 236);
  height: 7%;
  width: 100%;
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.4em;
  font-weight: 500;
}
#overlay-body {
  padding: 15px;
  height: calc(100vh - 90px);
  overflow: hidden;
  overflow-y: scroll !important;
}
.overlay-footer {
  height: auto;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive table {
  border-collapse: collapse;
}
</style>
