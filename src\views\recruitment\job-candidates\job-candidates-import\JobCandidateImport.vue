<template>
  <v-container class="job-candidate-container" fluid>
    <div v-if="isListLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 4" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <div v-else>
      <v-row justify="center">
        <v-col cols="12" lg="11" md="12" sm="12">
          <v-card min-height="560" class="rounded-lg">
            <v-card-text>
              <div class="text-center mb-6">
                <span v-for="i in 3" :key="i">
                  <v-icon color="primary" size="18" class="ml-1">{{
                    currentStep >= i ? "fas fa-circle" : "far fa-circle"
                  }}</v-icon>
                </span>
              </div>
              <BulkImportStep1
                class="mb-10"
                v-show="currentStep === 1"
                ref="bulkStep1"
                :step1-text="step1Text"
                @file-upload-success="uploadFile($event)"
                @file-upload-error="fileRemoveOrError()"
                @generate-excel="onGenerateExcel()"
                :showDownload="true"
              >
              </BulkImportStep1>
              <BulkImportStep2
                class="mb-10 pb-5"
                v-if="fileContent.length > 0 && currentStep === 2"
                ref="bulkStep2"
                :file-params="fileContent"
                :headers-selected="selectedHeaders"
                @column-mapped="
                  matchedCount = $event[0];
                  mappedFileHeader = $event[1];
                "
              ></BulkImportStep2>
              <BulkImportStep3
                class="mb-10"
                ref="bulkImportStep3"
                v-if="checkMatchedFields && currentStep === 3"
                :fields="generateFields"
                :json-data="excelEditorData"
                type-of-import="job-candidates"
                :extend-validation="getValidDynamicListData"
              ></BulkImportStep3>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
      <v-bottom-navigation v-model="openBottomSheet" style="height: 70px">
        <v-sheet
          class="align-center text-center"
          :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
        >
          <v-row class="px-3 mb-5" justify="center">
            <v-col
              v-if="!isLoadUploadButton"
              cols="6"
              class="pa-0 d-flex justify-start align-center pl-2"
            >
              <v-btn
                v-if="currentStep > 1"
                id="back_to_step"
                style="height: 40px; margin-top: 10px"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                color="primary"
                @click="backToStep()"
              >
                <span><i class="fa fa-chevron-left pr-2"></i> Back</span></v-btn
              >
              <v-btn
                id="cancel_step"
                style="height: 40px; margin-top: 10px"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                class="ml-2"
                color="primary"
                @click="closeForm()"
                >Cancel</v-btn
              >
            </v-col>
            <v-col
              :cols="isLoadUploadButton ? '12' : '6'"
              class="pa-0 d-flex justify-center align-center pr-4"
              :style="windowWidth >= 1264 ? 'margin-left: -106px' : ''"
            >
              <div v-if="windowWidth > 768" class="text-end mr-2">
                <div class="mr-1 text-grey text-caption" style="width: 400px">
                  {{ nextBtnHelpContent }}
                </div>
              </div>
              <v-btn
                id="next_step"
                rounded="lg"
                variant="elevated"
                style="height: 40px; margin-top: 10px"
                color="primary"
                class="mr-10"
                :disabled="!enableNextButton"
                :loading="isLoadUploadButton"
                :size="isMobileView ? 'small' : 'default'"
                :dense="isMobileView"
                @click="nextStep()"
              >
                <span>
                  {{ currentStep === 3 ? "Submit" : "Next" }}
                  <v-icon v-if="currentStep !== 3" class="pl-1" size="15"
                    >fa fa-chevron-right</v-icon
                  >
                </span>
              </v-btn>
            </v-col>
            <v-col
              cols="12"
              v-if="windowWidth <= 768 && nextBtnHelpContent"
              class="pa-1 pr-4 d-flex align-center justify-end"
            >
              <div class="mr-1 text-grey mb-0" style="font-size: 10px">
                {{ nextBtnHelpContent }}
              </div>
            </v-col>
          </v-row>
        </v-sheet>
      </v-bottom-navigation>
    </div>
    <v-dialog v-model="importConfirmation" width="50%">
      <v-card>
        <v-row>
          <v-col v-if="invalidData && invalidData.length" cols="12">
            <v-alert prominent type="warning">
              <v-row align="center">
                <v-col v-if="invalidData" class="grow"
                  ><span>{{ invalidCandidates.length }}</span>
                  out of
                  {{ excelEditorData.length }} do not have valid records.This
                  may result in omittion of those records.
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertCandidateData(finalUpdateData)"
                    >Add anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
          <v-col v-else cols="12" class="pa-3">
            <v-alert prominent type="success">
              <v-row align="center">
                <v-col class="grow">
                  Everything looks <strong>good</strong>.
                  <div class="pt-1">
                    Are you
                    <strong>sure</strong> you want to import the candidate
                    details?
                  </div>
                </v-col>
                <v-col class="shrink">
                  <v-btn @click="insertCandidateData(finalUpdateData)"
                    >Add anyway</v-btn
                  >
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
        </v-row>
        <v-overlay
          class="align-center justify-center"
          contained
          :model-value="isLoading"
          scrim="#fff"
        >
          <v-progress-circular color="primary" indeterminate size="64">
          </v-progress-circular>
        </v-overlay>
      </v-card>
    </v-dialog>
  </v-container>
  <AppLoading
    v-if="
      isListLoading ||
      genderListLoading ||
      cityListLoading ||
      courseListLoading ||
      nationalityListLoading ||
      specializationListLoading ||
      maritalListLoading ||
      jobPostListLoading ||
      sourceApplicationListLoading
    "
  ></AppLoading>
</template>
<script>
import BulkImportStep1 from "@/views/common/bulkImport/BulkImportStep1.vue";
import BulkImportStep2 from "@/views/common/bulkImport/BulkImportStep2.vue";
import BulkImportStep3 from "@/views/common/bulkImport/BulkImportStep3.vue";
import FileExportMixin from "@/mixins/FileExportMixin";
import {
  GENDER_LIST,
  LIST_CITIES,
  LIST_COURSE,
  LIST_NATIONALITIES,
  LIST_SPECIALIZATION_INSTITUTE,
  MARITAL_STATUS_LIST,
} from "@/graphql/dropDownQueries";
import {
  CANDIDATES_DROP_DOWN,
  LIST_JOB_LOCATIONS,
  LIST_JOB_POSTS,
  LIST_SOURCE_OF_APPLICATION,
  UPLOAD_BULK_CANDIDATE_DETAILS,
} from "@/graphql/recruitment/recruitmentQueries";
// import { convertMonthToYearMonthsDays, getDaysDifference } from "@/helper";
import moment from "moment";
export default {
  name: "JobCandidateImport",
  components: {
    BulkImportStep1,
    BulkImportStep2,
    BulkImportStep3,
  },
  mixins: [FileExportMixin],
  props: {
    backupMainList: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data: () => ({
    currentStep: 1,
    fileContent: [],
    errorsCountInExcel: 0,
    matchedCount: 0,
    openBottomSheet: true,
    isLoadUploadButton: false,
    mappedFileHeader: [],
    allowanceRestrictBonus: "No",
    step1Text: {
      typeofData: "candidate sheet",
      text: "You have the option of using our predefined template or bring in your own candidate sheet with the headers for import",
      heading: "Download the excel template with predefined headers",
    },
    selectedImportType: 1,
    fields: [],
    optionValues: {},
    excelEditorData: [],
    importConfirmation: false,
    finalExcelData: [],
    finalUpdateData: [],
    isLoading: false,
    step2HeadersData: [],
    genderList: [],
    originalGenderList: [],
    languageList: [],
    languageProficiencyList: [],
    originalLanguageList: [],
    maritalListLoading: false,
    maritalStatusList: [],
    originalMaritalStatusList: [],
    nationalityListLoading: false,
    nationalityList: [],
    originalNationalityList: [],
    currencyList: [],
    originalCurrencyList: [],
    workPermitList: [],
    originalWorkPermitList: [],
    sourceApplicationListLoading: false,
    sourceApplicationList: [],
    jobPostListLoading: false,
    jobPostList: [],
    originalJobPostList: [],
    cityListLoading: false,
    cityList: [],
    originalCityList: [],
    countryList: [],
    originalCountryList: [],
    courseListLoading: false,
    courseList: [],
    originalCourseList: [],
    specializationListLoading: false,
    specializationList: [],
    originalSpecializationList: [],
    instituteList: [],
    originalInstituteList: [],
    isListLoading: false,
    genderListLoading: false,
    genderOrientationList: [],
    bloodGroupList: [],
    toggleButtonList: ["Yes", "No"],
    genderIdentityList: [],
    originalGenderIdentityList: [],
    genderExpressionList: [],
    originalGenderExpressionList: [],
    pronounList: [],
    certificationList: [],
    originalCertificationList: [],
  }),

  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    formatDate() {
      return (date) => {
        return moment(date, "YYYY-MM-DD", true).isValid()
          ? moment(date).format("YYYY-MM-DD")
          : null;
      };
    },
    // preparing the sheet headers
    fieldsMapping() {
      let fieldMapping = [
        {
          key: "firstName",
          label: "First Name",
          required: true,
          fieldName: "First_Name",
        },
        { key: "middleName", label: "Middle Name", fieldName: "Middle_Name" },
        {
          key: "lastName",
          label: "Last Name",
          required: true,
          fieldName: "Last_Name",
        },
        {
          key: "knownAs",
          fieldIndex: 328,
          fieldName: "Emp_Pref_First_Name",
        },
        { key: "suffix", fieldIndex: 268, fieldName: "Suffix" },
        {
          key: "dateOfBirth",
          label: "Date of Birth",
          required: true,
          fieldName: "Date_of_Birth",
          format: "(YYYY-MM-DD)",
        },
        { key: "sex", fieldIndex: 267, fieldName: "Gender" },
        {
          key: "genderOrientation",
          fieldIndex: 210,
          options: this.genderOrientationList,
          fieldName: "Gender_Orientation",
        },
        {
          key: "genderIdentity",
          fieldIndex: 329,
          options: this.genderIdentityList,
          fieldName: "Gender_Identity_Id",
        },
        {
          key: "genderExpression",
          fieldIndex: 330,
          options: this.genderExpressionList,
          fieldName: "Gender_Expression_Id",
        },
        {
          key: "genderPronoun",
          fieldIndex: 209,
          options: this.pronounList,
          fieldName: "gender_pronoun",
        },
        {
          key: "languages.langknown",
          fieldIndex: 325,
          fieldName: "Languages_Known",
        },
        {
          key: "languages.langknown",
          fieldIndex: 383,
          fieldName: "Lang_Id",
        },
        {
          key: "languages.langSpoken",
          fieldIndex: 356,
          fieldName: "langSpoken",
          options: this.toggleButtonList,
        },
        {
          key: "languages.langReadWrite",
          fieldIndex: 357,
          fieldName: "langReadWrite",
          options: this.toggleButtonList,
        },
        {
          key: "languages.langProficiency",
          fieldIndex: 358,
          fieldName: "langProficiency",
          options: this.languageProficiencyList,
        },
        {
          key: "bloodGroup",
          fieldIndex: 262,
          fieldName: "Blood_Group",
          options: this.bloodGroupList,
        },
        { key: "maritalStatus", fieldIndex: 270, fieldName: "Marital_Status" },
        { key: "nationality", fieldIndex: 271, fieldName: "Nationality" },
        { key: "fathersName", fieldIndex: 264, fieldName: "Father_s_Name" },
        { key: "mothersName", fieldIndex: 263, fieldName: "Mother_s_Name" },
        {
          key: "nationalIdentityNumber",
          fieldIndex: 220,
          fieldName: "National_Identity_Number_Aadhar_Social_Security",
        },
        { key: "workPermit", fieldIndex: 265, fieldName: "Work_Permit" },
        {
          key: "otherWorkPermit",
          fieldIndex: 266,
          fieldName: "Other_Work_Permit",
        },
        {
          key: "sourceApplication",
          fieldIndex: 287,
          fieldName: "Source_of_Application",
        },
        {
          key: "physicallyChallenged",
          fieldIndex: 331,
          fieldName: "Physically_Challenged",
          options: this.toggleButtonList,
        },
        {
          key: "jobTitle",
          label: "Job Title",
          required: true,
          fieldName: "Job_Title",
          options: this.jobPostList,
        },
        {
          key: "preferedLocation",
          fieldIndex: 290,
          fieldName: "Preferred_Location",
        },
        {
          key: "keySkills",
          label: "Keys Skills",
          required: true,
          fieldName: "Keys_Skills",
        },
        {
          key: "currentEmployer",
          label: "Current Employer",
          fieldName: "Current_Employer",
        },
        {
          key: "noticePeriod",
          fieldIndex: 227,
          fieldName: "Availablity_To_Join",
          format: "(in Days)",
        },
        {
          key: "totalExperienceYears",
          label: "Total Experience (In years)",
          fieldName: "Total_Experience_In_years",
          required: true,
        },
        {
          key: "totalExperienceMonths",
          label: "Total Experience (In months)",
          fieldName: "Total_Experience_In_months",
          required: true,
        },
        {
          key: "currentBasicSalary",
          label: "Current Basic Salary",
          fieldName: "Current_Basic_Salary",
          required: true,
        },
        {
          key: "expectedSalary",
          label: "Expected Basic Salary",
          fieldName: "Expected_Basic_Salary",
          required: true,
        },
        {
          key: "currency",
          fieldIndex: 289,
          fieldName: "Currency",
        },
        {
          key: "passportNumber",
          fieldIndex: 269,
          fieldName: "Passport_Number",
        },
        {
          key: "numberOfCharactersReference",
          fieldIndex: 322,
          fieldName: "Name_Of_Character_Reference",
        },
        {
          key: "referenceMobileNumber",
          fieldIndex: 323,
          fieldName: "Character_Reference_Mobile_Number",
        },
        {
          key: "referenceEmail",
          fieldIndex: 324,
          fieldName: "Character_Reference_Email",
        },
        {
          key: "street1",
          label: "Street 1",
          required: true,
          fieldName: "Street_One",
        },
        {
          key: "street2",
          label: "Street 2",
          fieldName: "Street",
        },
        {
          key: "city",
          fieldIndex: 143,
          fieldName: "City_Municipality",
        },
        {
          key: "barangay",
          fieldIndex: 332,
          fieldName: "pBarangay",
        },
        {
          key: "region",
          fieldIndex: 333,
          fieldName: "pRegion",
        },
        {
          key: "state",
          label: "State/Province",
          required: true,
          fieldName: "State_Province",
        },
        {
          key: "country",
          label: "Country",
          options: this.countryList,
          fieldName: "Country",
          required: true,
        },
        {
          key: "pinCode",
          fieldIndex: 150,
          fieldName: "Pincode",
        },
        {
          key: "mobileNumberCountryCode",
          label: "Mobile Number Country Code",
          fieldName: "Mobile_Number_Country_Code",
          required: true,
        },
        {
          key: "mobileNumber",
          label: "Mobile Number",
          fieldName: "Mobile_Number",
          required: true,
        },
        {
          key: "personalEmail",
          label: "Personal Email",
          fieldName: "Personal_Email",
          required: true,
        },
        {
          key: "emergencyContactName",
          fieldIndex: 334,
          fieldName: "Emergency_Contact_Name",
        },
        {
          key: "emergencyContactNumber",
          fieldIndex: 335,
          fieldName: "Fax_No",
        },
        {
          key: "emergencyContactRelation",
          fieldIndex: 359,
          fieldName: "Emergency_Contact_Relation",
        },
        {
          key: "experience.company",
          subMenu: "Experience - ",
          fieldName: "Experience_Company",
          fieldIndex: 279,
        },
        {
          key: "experience.companyLocation",
          subMenu: "Experience - ",
          fieldName: "Prev_Company_Location",
          fieldIndex: 336,
        },
        {
          key: "experience.designation",
          subMenu: "Experience - ",
          fieldName: "Experience_Designation",
          fieldIndex: 280,
        },
        {
          key: "experience.from",
          subMenu: "Experience - ",
          fieldName: "Experience_From",
          fieldIndex: 281,
          format: "(YYYY-MM-DD)",
        },
        {
          key: "experience.to",
          subMenu: "Experience - ",
          fieldName: "Experience_To",
          fieldIndex: 282,
          format: "(YYYY-MM-DD)",
        },
        {
          key: "experience.referenceDetails[0].refName",
          subMenu: "Experience - ",
          fieldName: "Reference_Name",
          fieldIndex: 360,
        },
        {
          key: "experience.referenceDetails[0].refEmail",
          subMenu: "Experience - ",
          fieldName: "Reference_Email",
          fieldIndex: 361,
        },
        {
          key: "experience.referenceDetails[0].refNumber",
          subMenu: "Experience - ",
          fieldName: "Reference_Number",
          fieldIndex: 362,
        },
        {
          key: "education.attainment",
          subMenu: "Education - ",
          fieldName: "Education_Educational_Attainment",
          fieldIndex: 128,
        },
        {
          key: "education.instituteName",
          subMenu: "Education - ",
          fieldName: "Education_Institute_Name",
          fieldIndex: 175,
        },
        {
          key: "education.specialization",
          subMenu: "Education - ",
          fieldName: "Education_Field_of_Study",
          fieldIndex: 174,
        },
        {
          key: "education.yearOfStart",
          label: "Year of Start",
          subMenu: "Education - ",
          fieldName: "Education_Year_of_Start",
          fieldIndex: 294,
          options: this.yearList,
        },
        {
          key: "education.yearOfPassing",
          subMenu: "Education - ",
          fieldName: "Education_Year_of_Passing",
          fieldIndex: 179,
          options: this.yearList,
        },
        {
          key: "education.percentage",
          subMenu: "Education - ",
          fieldName: "Education_Percentage",
          fieldIndex: 177,
        },
        {
          key: "certificate.certificateName",
          subMenu: "Certification - ",
          fieldName: "Certification_Certificate",
          fieldIndex: 284,
          options: this.certificationList,
        },
        {
          key: "certificate.receivedOn",
          subMenu: "Certification - ",
          fieldName: "Certification_Received_On",
          fieldIndex: 285,
          format: "(YYYY-MM-DD)",
        },
        {
          key: "certificate.receivedFrom",
          subMenu: "Certification - ",
          fieldName: "Certification_Received_From",
          fieldIndex: 286,
        },
        {
          key: "certificate.ranking",
          subMenu: "Certification - ",
          fieldName: "ranking",
          fieldIndex: 337,
        },
      ];
      return fieldMapping;
    },
    getColumnHighlightProps() {
      let fieldsList = {
        type: "Job Candidate Import",
      };
      return { ...fieldsList, ...this.getValidDynamicListData };
    },
    getValidDynamicListData() {
      let fieldsList = {
        yearOfStart: this.yearList || [],
        jobPostList: this.jobPostList || [],
        countryList: this.countryList || [],
      };
      const visibleFields = {
        262: "bloodGroupList",
        267: "genderList",
        325: "languageList",
        270: "maritalStatusList",
        271: "nationalityList",
        265: "workPermitList",
        289: "currencyList",
        287: "sourceApplicationList",
        143: "cityList",
        128: "courseList",
        175: "instituteList",
        294: "yearList",
        174: "specializationList",
        329: "genderIdentityList",
        330: "genderExpressionList",
        209: "pronounList",
        284: "certificationList",
        383: "languageList",
        358: "languageProficiencyList",
        210: "genderOrientationList",
        331: "toggleButtonList",
        356: "toggleButtonList",
        357: "toggleButtonList",
      };

      Object.keys(visibleFields).forEach((field) => {
        if (
          this.labelList[field] &&
          this.labelList[field].Field_Visiblity &&
          this.labelList[field].Field_Visiblity.toLowerCase() === "yes"
        ) {
          fieldsList[visibleFields[field]] = this[visibleFields[field]] || [];
        }
      });
      return fieldsList;
    },
    yearList() {
      // let empDob = this.selectedCandidateDOB; // after getting the candidate DOB we need to enable this commend line
      let empDob = "";
      let year = 0;
      if (empDob) {
        year = new Date().getFullYear() - new Date(empDob).getFullYear();
      } else {
        year = 80;
      }
      const now = new Date().getUTCFullYear();
      const years = Array(now - (now - year))
        .fill("")
        .map((v, idx) => now - idx);
      return years;
    },
    //refactoring the custom fields
    getCandidateHeaders() {
      const visibleFields = {
        262: "bloodGroupList",
        267: "genderList",
        325: "languageList",
        270: "maritalStatusList",
        271: "nationalityList",
        265: "workPermitList",
        289: "currencyList",
        287: "sourceApplicationList",
        143: "cityList",
        128: "courseList",
        175: "instituteList",
        294: "yearList",
        174: "specializationList",
        329: "genderIdentityList",
        330: "genderExpressionList",
        209: "pronounList",
        383: "languageList",
        358: "languageProficiencyList",
        210: "genderOrientationList",
        284: "certificationList",
        331: "toggleButtonList",
        356: "toggleButtonList",
        357: "toggleButtonList",
      };
      const tempHeader = this.fieldsMapping
        .filter(
          (field) =>
            field?.label ||
            this.labelList[field.fieldIndex]?.Field_Visiblity === "Yes" ||
            field?.subMenu
        )
        .map((field) => ({
          key: field.key,
          fieldName: field.fieldName,
          header: `${field?.subMenu ? field.subMenu : ""}${
            field.label || this.labelList[field.fieldIndex]?.Field_Alias
          }${field?.format ? field.format : ""}`,
          required:
            field.required ||
            this.labelList[field.fieldIndex]?.Mandatory_Field === "Yes" ||
            false,
          options: field.options
            ? field.options || []
            : this[visibleFields[field.fieldIndex]] || null,
        }));
      return tempHeader;
    },
    selectedHeaders() {
      //Form the selectedHeaders with fields and fieldOptions
      let output = this.getCandidateHeaders;
      output = output.map((el) => {
        return {
          title: el.header,
          value: el.header,
          props: { disabled: false },
        };
      });
      return output;
    },

    invalidData() {
      return this.$refs.bulkImportStep3.invalidData;
    },
    invalidCandidates() {
      let invalidData = this.$refs.bulkImportStep3.invalidData;
      let candidateFail = Array.from(new Set(invalidData));
      return candidateFail;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    // enable next button based on current step and scenarios
    enableNextButton() {
      if (this.currentStep === 1 && this.fileContent.length > 0) {
        return true;
      } else if (this.currentStep === 2 && this.checkMatchedFields) {
        return true;
      } else if (this.currentStep === 3) {
        this.formattedFileContent();
        return true;
      } else {
        return false;
      }
    },

    // next button help content based on current step and scenarios
    nextBtnHelpContent() {
      if (this.currentStep === 1) {
        if (this.fileContent.length === 0)
          return "Please import the data with supported file types (CSV, XLSX and XLS) to continue with the next step.";
        else return "";
      } else if (this.currentStep === 2) {
        return "The unmatched optional column(s) will not be processed in the next step.";
      } else if (this.currentStep === 3) {
        if (this.formattedFileContent.length === 0) {
          return "";
        } else if (this.errorsCountInExcel !== 0) {
          return "There seems to be some validation error(s) in your file. Please amend it before uploading.";
        } else {
          return "By clicking the 'Submit' button, you can import employee data.";
        }
      } else {
        return "";
      }
    },
    mandatoryHeader() {
      let fields = this.getCandidateHeaders.map((el) => el.header);

      return fields;
    },
    // check mandatory fields all are matched
    checkMatchedFields() {
      let mandatoryHeader = this.mandatoryHeader;
      if (this.matchedCount === this.mandatoryHeader.length) {
        let mandatoryMatchedCount = 0;
        for (var i in this.mappedFileHeader) {
          if (mandatoryHeader.includes(this.mappedFileHeader[i].hrapp_header))
            mandatoryMatchedCount++;
        }
        this.addHeaders();
        //  if all the mandatory field are matched then return true else false
        return mandatoryMatchedCount === this.mandatoryHeader.length
          ? true
          : false;
      } else return false;
    },

    // get the data from excel file without empty values
    excelFileData() {
      return this.fileContent.filter(
        (content) => content.filter(Boolean).length > 0
      );
    },
    generateFields() {
      let excelHeaderData = this.getCandidateHeaders;
      excelHeaderData = excelHeaderData.map((el) => {
        return {
          field: el.fieldName,
          label: el.header,
          type: el.options ? "select" : "string",
          readonly: false,
          width: "200px",
          options: el.options,
        };
      });
      return excelHeaderData;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
  },
  mounted() {
    this.retrieveJobPosts();
    this.retrieveCountries();
    if (this.labelList) {
      const visibleFields = ["267", "325", "270", "271", "287", "143", "128"];
      // && this line is for the predefined fields
      //     this.labelList[field].Predefined &&
      //     this.labelList[field].Predefined.toLowerCase() === "yes"
      visibleFields.forEach((field) => {
        if (
          this.labelList[field].Field_Visiblity &&
          this.labelList[field].Field_Visiblity.toLowerCase() === "yes"
        ) {
          switch (field) {
            case "267":
              this.retrieveGender();
              break;
            case "270":
              this.retrieveMaritalStatusList();
              break;
            case "271":
              this.retrieveNationalityList();
              break;
            case "287":
              this.retrieveSource();
              break;
            case "143":
              this.retrieveCities();
              break;
            case "128":
              this.retrieveEducationCourse();
              break;
          }
        }
      });
      // && this line is for the predefined fields
      //     this.labelList[field].Predefined &&
      //     this.labelList[field].Predefined.toLowerCase() === "yes"
      if (
        (this.labelList["265"].Field_Visiblity &&
          this.labelList["265"].Field_Visiblity.toLowerCase() === "yes") ||
        (this.labelList["289"].Field_Visiblity &&
          this.labelList["289"].Field_Visiblity.toLowerCase() === "yes")
      ) {
        this.retrieveDropDownDetails();
      }
      // && this line is for the predefined fields
      //     this.labelList[field].Predefined &&
      //     this.labelList[field].Predefined.toLowerCase() === "yes"
      if (
        (this.labelList["174"].Field_Visiblity &&
          this.labelList["174"].Field_Visiblity.toLowerCase() === "yes") ||
        (this.labelList["175"].Field_Visiblity &&
          this.labelList["175"].Field_Visiblity.toLowerCase() === "yes")
      ) {
        this.retrieveSplInstitute();
      }
      if (
        this.labelList[325]?.Field_Visiblity?.toLowerCase() == "yes" ||
        this.labelList[383]?.Field_Visiblity?.toLowerCase() == "yes"
      )
        this.retrieveLanguages();
      this.getDropdownDetails();
    }
  },
  methods: {
    retrieveGender() {
      let vm = this;
      vm.genderListLoading = true;
      vm.$apollo
        .query({
          query: GENDER_LIST,
          client: "apolloClientX",
          fetchPolicy: "no-cache",
          variables: {
            Org_Code: this.orgCode,
          },
        })
        .then(({ data }) => {
          vm.genderListLoading = false;
          if (
            data &&
            data.retrieveGenderList &&
            data.retrieveGenderList.genderData
          ) {
            vm.genderList = data.retrieveGenderList.genderData.map(
              (item) => item.gender
            );
            vm.originalGenderList = data.retrieveGenderList.genderData;
          }
        })
        .catch(() => {
          vm.originalGenderList = [];
          vm.genderList = [];
          vm.genderListLoading = false;
        });
    },
    retrieveLanguages() {
      this.languageList = [];
      this.isListLoading = true;
      this.$store
        .dispatch("listLanguages", {
          client: "apolloClientAP",
        })
        .then((langList) => {
          this.isListLoading = false;
          if (langList && langList.length > 0) {
            this.languageList = langList.map((item) => item?.Language_Name);
            this.originalLanguageList = langList;
          }
        })
        .catch(() => {
          this.isListLoading = false;
          this.originalLanguageList = [];
          this.languageList = [];
        });
    },
    retrieveMaritalStatusList() {
      let vm = this;
      vm.maritalListLoading = true;
      vm.$apollo
        .query({
          query: MARITAL_STATUS_LIST,
          client: "apolloClientX",
          fetchPolicy: "no-cache",
          variables: {
            Org_Code: this.orgCode,
            Url_Hash: "",
          },
        })
        .then(({ data }) => {
          vm.maritalListLoading = false;
          if (
            data &&
            data.listMartialStatus &&
            data.listMartialStatus.marital_status
          ) {
            vm.maritalStatusList = data.listMartialStatus.marital_status.map(
              (item) => item.Marital_Status
            );
            vm.originalMaritalStatusList =
              data.listMartialStatus.marital_status;
          }
        })
        .catch(() => {
          vm.maritalListLoading = false;
          vm.maritalStatusList = [];
          vm.originalMaritalStatusList = [];
        });
    },
    retrieveDropDownDetails() {
      let vm = this;
      vm.isListLoading = true;
      vm.$apollo
        .query({
          query: CANDIDATES_DROP_DOWN,
          client: "apolloClientAO",
        })
        .then((response) => {
          vm.isListLoading = false;
          if (
            response.data &&
            response.data.getDropDownBoxDetails &&
            !response.data.getDropDownBoxDetails.errorCode.length
          ) {
            const { workAuthorizations, currency } =
              response.data.getDropDownBoxDetails;
            vm.workPermitList =
              workAuthorizations && workAuthorizations.length > 0
                ? workAuthorizations.map((item) => item.Work_Authorization_Name)
                : [];
            vm.originalWorkPermitList =
              workAuthorizations && workAuthorizations.length > 0
                ? workAuthorizations
                : [];
            vm.currencyList =
              currency && currency.length > 0
                ? currency.map((item) => item.Currency_Name)
                : [];
            vm.originalCurrencyList =
              currency && currency.length > 0 ? currency : [];
          }
        })
        .catch(() => {
          vm.currencyList = [];
          vm.workPermitList = [];
          vm.isListLoading = false;
        });
    },
    retrieveNationalityList() {
      let vm = this;
      vm.nationalityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_NATIONALITIES,
          variables: { Org_Code: this.$store.getters.orgCode },
          client: "apolloClientX",
        })
        .then(({ data }) => {
          vm.nationalityListLoading = false;
          if (
            data &&
            data.retrieveNationalityList &&
            data.retrieveNationalityList.nationalityData
          ) {
            this.nationalityList =
              data.retrieveNationalityList.nationalityData.map(
                (item) => item.nationality
              );
            this.originalNationalityList =
              data.retrieveNationalityList.nationalityData;
          }
        })
        .catch(() => {
          vm.nationalityListLoading = false;
          vm.originalNationalityList = [];
          vm.nationalityList = [];
        });
    },
    retrieveSource() {
      let vm = this;
      vm.sourceApplicationListLoading = true;
      vm.$apollo
        .query({
          query: LIST_SOURCE_OF_APPLICATION,
          client: "apolloClientAO",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          vm.sourceApplicationListLoading = false;
          if (
            response.data &&
            response.data.retrieveSourceMasterList &&
            !response.data.retrieveSourceMasterList.errorCode
          ) {
            const { source } = response.data.retrieveSourceMasterList;
            vm.sourceApplicationList = source.map((item) => item?.Source_Title);
          }
        })
        .catch(() => {
          vm.sourceApplicationListLoading = false;
          vm.sourceApplicationList = [];
        });
    },
    retrieveJobPosts() {
      let vm = this;
      vm.jobPostListLoading = true;
      vm.$apollo
        .query({
          query: LIST_JOB_POSTS,
          client: "apolloClientAO",
          variables: {
            searchString: "",
            designation: null,
            functionalArea: null,
            jobType: null,
            closingDate: "",
            status: null,
            location: [],
            isDropDownCall: 1,
            skills: [],
            qualification: [],
            action: "add",
            formId: 16,
          },
        })
        .then((response) => {
          vm.jobPostListLoading = false;
          if (
            response.data &&
            response.data.listJobPost &&
            response.data.listJobPost.JobpostDetails
          ) {
            vm.jobPostList = response.data.listJobPost.JobpostDetails.map(
              (item) => item.Job_Post_Name
            );
            vm.originalJobPostList = response.data.listJobPost.JobpostDetails;
          }
        })
        .catch(() => {
          vm.jobPostListLoading = false;
          vm.jobPostList = [];
          vm.originalJobPostList = [];
        });
    },
    //preferred location need to be added
    retrieveJobPostLocation(inputJobPostId) {
      let vm = this;
      let jobPostIdToSendBackend = inputJobPostId
        ? inputJobPostId
        : vm.editedDetails.Job_Post_Id;
      if (jobPostIdToSendBackend) {
        vm.jobPostLocationLoading = true;
        vm.$apollo
          .query({
            query: LIST_JOB_LOCATIONS,
            client: "apolloClientAO",
            fetchPolicy: "no-cache",
            variables: {
              jobpostId: jobPostIdToSendBackend
                ? jobPostIdToSendBackend
                : vm.jobPostId,
            },
          })
          .then((response) => {
            if (
              response.data &&
              response.data.listJobPostLocations &&
              !response.data.listJobPostLocations.errorCode.length
            ) {
              vm.jobpostLocations =
                response.data.listJobPostLocations.jobpostLocations;
            }
            vm.jobPostLocationLoading = false;
          })
          .catch(() => {
            vm.jobPostLocationLoading = false;
            vm.jobpostLocations = [];
          });
      }
    },
    getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;

      vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: 16,
            key: [
              "Gender_Identity_Id",
              "Gender_Expression_Id",
              "Privacy_Statement_Id",
              "gender_orientations",
              "gender_pronoun",
              "Required_Certification",
              "language_proficiency",
              "blood_group",
            ],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            tempData.forEach((item) => {
              if (item.tableKey?.toLowerCase() === "gender_identity_id") {
                vm.genderIdentityList = item.data.map(
                  (dataItem) => dataItem.Gender_Identity
                );
                vm.originalGenderIdentityList = item.data;
              } else if (item.tableKey?.toLowerCase() === "blood_group") {
                vm.bloodGroupList =
                  item.data?.map((dataItem) => dataItem.Blood_Group_Name) || [];
              } else if (
                item.tableKey?.toLowerCase() === "gender_expression_id"
              ) {
                vm.genderExpressionList = item.data.map(
                  (dataItem) => dataItem.Gender_Expression
                );
                vm.originalGenderExpressionList = item.data;
              } else if (item.tableKey?.toLowerCase() === "gender_pronoun") {
                vm.pronounList = item.data.map(
                  (dataItem) => dataItem.Gender_Pronoun_Name
                );
              } else if (
                item.tableKey?.toLowerCase() === "gender_orientations"
              ) {
                vm.genderOrientationList = item.data.map(
                  (dataItem) => dataItem.Gender_Orientations_Name
                );
              } else if (
                item.tableKey?.toLowerCase() === "required_certification"
              ) {
                vm.certificationList = item.data.map(
                  (dataItem) => dataItem.Required_Certification
                );
                vm.originalCertificationList = item.data;
              } else if (
                item.tableKey?.toLowerCase() === "language_proficiency"
              ) {
                vm.languageProficiencyList = item.data.map(
                  (dataItem) => dataItem.Language_Proficiency
                );
              }
            });
          }
          vm.isListLoading = false;
        })
        .catch(() => {
          vm.isListLoading = false;
          vm.genderIdentityList = [];
          vm.genderExpressionList = [];
          vm.bloodGroupList = [];
          vm.pronounList = [];
          vm.genderOrientationList = [];
          vm.certificationList = [];
          vm.languageProficiencyList = [];
          vm.originalGenderIdentityList = [];
          vm.originalGenderExpressionList = [];
          vm.originalCertificationList = [];
        });
    },
    retrieveCities() {
      let vm = this;
      vm.cityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES,
          client: "apolloClientAC",
          variables: {
            Form_Id: 16,
          },
        })
        .then((response) => {
          vm.cityListLoading = false;
          if (
            response.data &&
            response.data.getCityListWithState &&
            !response.data.getCityListWithState.errorCode
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            vm.cityList = cityDetails.map((item) => item?.City_Name);
            vm.originalCityList = cityDetails;
          }
        })
        .catch(() => {
          vm.cityList = [];
          vm.originalCityList = [];
          vm.cityListLoading = false;
        });
    },
    retrieveCountries() {
      this.isListLoading = true;
      this.countryList = [];
      this.$store
        .dispatch("listCountries", {
          client: "apolloClientAP",
        })
        .then((langList) => {
          this.isListLoading = false;
          this.countryList = langList.map((item) => item?.Country_Name);
          this.originalCountryList = langList;
        })
        .catch(() => {
          this.countryList = [];
          this.originalCountryList = [];
          this.isListLoading = false;
        });
    },
    retrieveEducationCourse() {
      let vm = this;
      vm.courseListLoading = true;
      vm.$apollo
        .query({
          query: LIST_COURSE,
          client: "apolloClientAP",
        })
        .then((response) => {
          vm.courseListLoading = false;
          if (
            response.data &&
            response.data.listCourseDetails &&
            !response.data.listCourseDetails.errorCode
          ) {
            const { courseDetails } = response.data.listCourseDetails;
            vm.courseList =
              courseDetails && courseDetails.length > 0
                ? courseDetails.map((item) => item?.Course_Name)
                : [];
            vm.originalCourseList =
              courseDetails && courseDetails.length > 0 ? courseDetails : [];
          }
        })
        .catch(() => {
          vm.courseList = [];
          vm.originalCourseList = [];
          vm.courseListLoading = false;
        });
    },
    retrieveSplInstitute() {
      let vm = this;
      vm.specializationListLoading = true;
      vm.$apollo
        .query({
          query: LIST_SPECIALIZATION_INSTITUTE,
          client: "apolloClientAS",
        })
        .then((response) => {
          vm.specializationListLoading = false;
          if (
            response.data &&
            response.data.retrieveListEduInstitutionAndSpecialization &&
            !response.data.retrieveListEduInstitutionAndSpecialization.errorCode
          ) {
            const { institution, specialization } =
              response.data.retrieveListEduInstitutionAndSpecialization;
            vm.specializationList = specialization.map(
              (item) => item?.Specialization
            );
            vm.originalSpecializationList = specialization;
            vm.instituteList = institution.map((item) => item?.Institution);
            vm.originalInstituteList = institution;
          }
        })
        .catch(() => {
          vm.specializationList = [];
          vm.instituteList = [];
          vm.specializationListLoading = false;
        });
    },
    onGenerateExcel() {
      // we need to have drop-down values in excel, for that we are copying the same object 100 times for now
      const jobCandidatesObject = {
        firstName: null,
        middleName: null,
        lastName: null,
        knownAs: null,
        suffix: null,
        dateOfBirth: null,
        sex: null,
        genderOrientation: null,
        genderIdentity: null,
        genderExpression: null,
        genderPronoun: null,
        languages: {
          langKnown: null,
          langSpoken: null,
          langReadWrite: null,
          langProficiency: null,
        },
        bloodGroup: null,
        maritalStatus: null,
        nationality: null,
        fathersName: null,
        mothersName: null,
        nationalIdentityNumber: null,
        workPermit: null,
        otherWorkPermit: null,
        sourceApplication: null,
        physicallyChallenged: null,
        jobTitle: null,
        preferedLocation: null,
        keySkills: null,
        currentEmployer: null,
        noticePeriod: null,
        totalExperience: null,
        currentBasicSalary: null,
        expectedSalary: null,
        currency: null,
        passportNumber: null,
        numberOfCharactersReference: null,
        referenceMobileNumber: null,
        referenceEmail: null,
        street1: null,
        street2: null,
        city: null,
        barangay: null,
        region: null,
        state: null,
        country: null,
        pinCode: null,
        mobileNumberCountryCode: null,
        mobileNumber: null,
        personalEmail: null,
        emergencyContactName: null,
        emergencyContactNumber: null,
        emergencyContactRelationship: null,
        experience: {
          company: null,
          companyLocation: null,
          designation: null,
          from: null,
          to: null,
          referenceDetails: [
            {
              refName: null,
              refEmail: null,
              refNumber: null,
            },
          ],
        },
        education: {
          educationAttainment: null,
          instituteName: null,
          fieldOfStudy: null,
          yearOfStart: null,
          yearOfPassing: null,
          percentage: null,
        },
        certificate: {
          certificateName: null,
          receivedOn: null,
          receivedFrom: null,
          ranking: null,
        },
      };
      let jobCandidateHeaders = this.getCandidateHeaders;
      let requiredHeaders = jobCandidateHeaders
        .filter((header) => header.required)
        .map((header) => header.header);
      // Create an array with 100 copies of the jobCandidatesObject
      const exportData = Array.from({ length: 100 }, () => ({
        ...jobCandidatesObject,
      }));
      let headers = jobCandidateHeaders;

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Job Candidate template.xlsx",
        sheetName: "Job Candidate sheet",
        header: headers,
        requiredHeaders: requiredHeaders,
        columnHighlightProps: this.getColumnHighlightProps,
      };
      this.exportExcelFile(exportOptions);
    },
    formattedFileContent() {
      //With Fields form the headers
      let generatedData = this.formExcelData();
      this.excelEditorData = generatedData;
    },
    formExcelData() {
      let fields = this.generateFields;
      let data = JSON.parse(JSON.stringify(this.excelFileData));
      let headersAssigned = this.step2HeadersData;
      //Getting the field of the array of objects
      let excelData = [];
      let idCounter = 1;
      // Iterate through each row of data
      for (let i = 1; i < data.length; i++) {
        let rowData = data[i];
        let rowObj = { $id: "000000" + idCounter++ };

        // Iterate through each field definition and populate the row object
        for (let j = 0; j < fields.length; j++) {
          let fieldDef = fields[j];
          let fieldName = fieldDef.field;
          // Find the index of the field in the header mappings array
          let headerIndex = headersAssigned.findIndex(
            (header) => header.hrapp_header === fieldDef.label
          );
          // If the field is present in the header mappings array, use the corresponding value from the input data
          if (headerIndex >= 0) {
            let dataValue = rowData[headerIndex];
            if (dataValue !== null && dataValue !== undefined) {
              rowObj[fieldName] = dataValue;
            } else {
              rowObj[fieldName] = null;
            }
          } else {
            // If the field is not present in the header mappings array, use the default value for the field type
            switch (fieldDef.type) {
              case "string":
                rowObj[fieldName] = "";
                break;
              case "number":
                rowObj[fieldName] = 0;
                break;
              case "boolean":
                rowObj[fieldName] = false;
                break;
              default:
                rowObj[fieldName] = null;
                break;
            }
          }
        }
        excelData.push(rowObj);
      }
      return excelData;
    },
    // called cancel is clicked to close form
    closeForm() {
      this.$emit("close-import-model");
    },
    // back button clicks, to subtract 1 from current step
    backToStep() {
      this.currentStep = this.currentStep - 1;
      this.allRecordsFail = false;
    },

    // next button clicks, to add 1 from current step
    nextStep() {
      if (this.currentStep === 3) {
        this.formBulkData(this.$refs.bulkImportStep3.filteredData);
      } else {
        this.currentStep += 1;
      }
    },

    addHeaders() {
      if (this.$refs.bulkStep2 && this.$refs.bulkStep2.tableItems) {
        this.step2HeadersData = this.$refs.bulkStep2.tableItems;
      }
    },
    getListObject(listObject, targetKey, targetValue) {
      let result = null;
      if (listObject && listObject.length > 0 && targetValue) {
        listObject.forEach((item) => {
          if (
            item &&
            item[targetKey] &&
            item[targetKey].toLowerCase() === targetValue.toLowerCase()
          ) {
            result = item;
          }
        });
      }
      return result;
    },
    formBulkData(data) {
      const filteredData = data;
      this.finalExcelData = data;
      this.finalUpdateData = filteredData.map((item) => {
        // const duration = getDaysDifference(
        //   item["Experience_From"],
        //   item["Experience_To"]
        // );
        // const totalExperienceInMonths = convertMonthToYearMonthsDays(
        //   (duration / 30).toFixed(2)
        // )?.split(" ");
        const jobTitleValue = this.getListObject(
          this.originalJobPostList,
          "Job_Post_Name",
          item["Job_Title"]
        );
        const genderIdValue = this.getListObject(
          this.originalGenderList,
          "gender",
          item["Gender"]
        );
        const genderIdentityIdValue = this.getListObject(
          this.originalGenderIdentityList,
          "Gender_Identity",
          item["Gender_Identity_Id"]
        );
        const genderExpressionValue = this.getListObject(
          this.originalGenderExpressionList,
          "Gender_Expression",
          item["Gender_Expression_Id"]
        );
        const maritalValue = this.getListObject(
          this.originalMaritalStatusList,
          "Marital_Status",
          item["Marital_Status"]
        );
        const nationalityValue = this.getListObject(
          this.originalNationalityList,
          "nationality",
          item["Nationality"]
        );
        const countryValue = this.getListObject(
          this.originalCountryList,
          "Country_Name",
          item["Country"]
        );
        const currencyValue = this.getListObject(
          this.originalCurrencyList,
          "Currency_Name",
          item["Currency"]
        );
        const educationTypeValue = this.getListObject(
          this.originalCourseList,
          "Course_Name",
          item["Education_Educational_Attainment"]
        );
        const specializationValue = this.getListObject(
          this.originalSpecializationList,
          "Specialization",
          item["Education_Field_of_Study"]
        );
        const instituteValue = this.getListObject(
          this.originalInstituteList,
          "Institution",
          item["Education_Institute_Name"]
        );
        const workPermit = this.getListObject(
          this.originalWorkPermitList,
          "Work_Authorization_Name",
          item["Work_Permit"]
        );
        // Languages handling
        let languageValue = {};
        if (this.labelList[325]?.Field_Visiblity?.toLowerCase() === "yes") {
          let language = this.getListObject(
            this.originalLanguageList,
            "Language_Name",
            item["Languages_Known"]
          );
          languageValue = [
            {
              langKnown: language?.Lang_Id,
              langSpoken: null,
              langReadWrite: null,
              langProficiency: null,
            },
          ];
        }
        if (this.labelList[383]?.Field_Visiblity?.toLowerCase() === "yes") {
          let language = this.getListObject(
            this.originalLanguageList,
            "Language_Name",
            item["Lang_Id"]
          );
          languageValue = [
            {
              langKnown: language?.Lang_Id,
              langSpoken: item["langSpoken"] === "Yes" ? 1 : 0,
              langReadWrite: item["langReadWrite"] === "Yes" ? 1 : 0,
              langProficiency: item["langProficiency"] || "",
            },
          ];
        }
        const mobileCountryCode = item[
          "Mobile_Number_Country_Code"
        ]?.replaceAll("+", "");
        let newItem = {
          salutation: "",
          firstName: item["First_Name"] || "",
          lastName: item["Last_Name"] || "",
          knownAs: item["Emp_Pref_First_Name"] || "",
          pronoun: item["gender_pronoun"] || "",
          middleName: item["Middle_Name"] || "",
          Suffix: item["Suffix"] || "",
          genderId: genderIdValue?.genderId || null,
          gender: genderIdValue?.gender || "",
          dob: this.formatDate(item["Date_of_Birth"]) || null,
          bloodGroup: item["Blood_Group"] || "",
          maritalStatus: maritalValue?.Marital_Status_Id,
          nationality: nationalityValue?.nationality || "",
          nationalityId: nationalityValue?.nationalityId || null,
          languagesKnown: languageValue || [],
          emailId: item["Personal_Email"] || "",
          emergencyContactName: item["Emergency_Contact_Name"] || "",
          emergencyContactRelation: item["Emergency_Contact_Relation"] || "",
          emergencyContactNo: item["Fax_No"] || "",
          mobileNo: item["Mobile_Number"] || "",
          mobileNoCountryCode: "+" + mobileCountryCode || "",
          apartmentName: item["Street_One"] || "",
          street: item["Street"] || "",
          city: item["City_Municipality"] || "",
          barangay: item["pBarangay"] || "",
          region: item["pRegion"] || "",
          state: item["State_Province"] || "",
          country: countryValue?.Country_Code || null,
          pincode: item["Pincode"] || "",
          jobPost: jobTitleValue?.Job_Post_Id || null,
          // preferredLocation: [item["Preferred_Location"]] || [],
          noticePeriod: parseInt(item["Availablity_To_Join"]) || null,
          currentCTC: parseFloat(item["Current_Basic_Salary"]) || null,
          expectedCTC: parseFloat(item["Expected_Basic_Salary"]) || null,
          passportNo: item["Passport_Number"] || null,
          resume: "",
          resumeFileSize: "",
          currency: currencyValue?.Currency_Id
            ? currencyValue?.Currency_Id
            : mobileCountryCode == "91"
            ? 72
            : null,
          status: 10,
          totalExperienceInYears:
            parseInt(item["Total_Experience_In_years"]) || null,
          totalExperienceInMonths:
            parseInt(item["Total_Experience_In_months"]) || null,
          candidateProfilePicture: "",
          genderOrientations: item["Gender_Orientation"] || "",
          skillSet: item["Keys_Skills"]?.split(",") || [],
          genderIdentityId: genderIdentityIdValue?.Gender_Identity_Id || null,
          genderExpressionId:
            genderExpressionValue?.Gender_Expression_Id || null,
          currentEmployer: item["Current_Employer"] || null,
          source: item["Source_of_Application"] || null,
          candidateDependent:
            [item["Father_s_Name"], item["Mother_s_Name"]] || [],
          nationalIdentificationNumber:
            item["National_Identity_Number_Aadhar_Social_Security"] || null,
          candidateWorkPermit: [workPermit?.Work_Authorization_Id] || [],
          candidateOtherWorkPermit: item["Other_Work_Permit"] || null,
          physicallyChallenged:
            item["Physically_Challenged"] === "Yes" ? 1 : 0 || null,
          verifierName: item["Name_Of_Character_Reference"],
          verifierPhoneNo: item["Character_Reference_Mobile_Number"],
          verifierEmailId: item["Character_Reference_Email"],
          candidateExperience: [{}],
          candidateEducation: [{}],
          candidateCertification: [{}],
        };
        if (educationTypeValue && educationTypeValue.Course_Id) {
          newItem["candidateEducation"][0]["educationType"] =
            educationTypeValue.Course_Id || null;
        }
        if (specializationValue && specializationValue.Specialization) {
          newItem["candidateEducation"][0]["speacialisation"] =
            specializationValue.Specialization || "";
        }
        if (specializationValue && specializationValue.Specialization_Id) {
          newItem["candidateEducation"][0]["specializationId"] =
            specializationValue.Specialization_Id || null;
        }
        if (instituteValue && instituteValue.Institution) {
          newItem["candidateEducation"][0]["institute"] =
            instituteValue.Institution || "";
        }
        if (instituteValue && instituteValue.Institution_Id) {
          newItem["candidateEducation"][0]["institutionId"] =
            instituteValue.Institution_Id || null;
        }
        if (item["Education_Year_of_Start"]) {
          newItem["candidateEducation"][0]["yearOfStart"] =
            parseInt(item["Education_Year_of_Start"]) || null;
        }
        if (item["Education_Year_of_Passing"]) {
          newItem["candidateEducation"][0]["yearOfPassing"] =
            parseInt(item["Education_Year_of_Passing"]) || null;
        }
        if (item["Education_Percentage"]) {
          newItem["candidateEducation"][0]["percentage"] =
            parseInt(item["Education_Percentage"]) || null;
        }
        if (item["Experience_Company"]) {
          newItem.candidateExperience[0]["companyName"] =
            item["Experience_Company"] || "";
        }
        if (item["Experience_Designation"]) {
          newItem.candidateExperience[0]["designation"] =
            item["Experience_Designation"];
        }
        if (item["Experience_From"]) {
          newItem.candidateExperience[0]["startDate"] = this.formatDate(
            item["Experience_From"]
          );
        }
        if (item["Experience_To"]) {
          newItem.candidateExperience[0]["endDate"] = this.formatDate(
            item["Experience_To"]
          );
        }
        if (item["Experience_To"]) {
          newItem.candidateExperience[0]["companyLocation"] =
            item["Prev_Company_Location"];
        }

        if (item["Reference_Name"]) {
          newItem.candidateExperience[0]["referenceDetails"] = [
            {
              Reference_Name: item["Reference_Name"] || null,
              Reference_Email: item["Reference_Email"] || null,
              Reference_Number: item["Reference_Number"] || null,
            },
          ];
        }
        // if (totalExperienceInMonths[0]) {
        //   newItem.candidateExperience[0]["years"] =
        //     parseInt(totalExperienceInMonths[0]) || null;
        // }
        // if (totalExperienceInMonths[2]) {
        //   newItem.candidateExperience[0]["months"] =
        //     parseInt(totalExperienceInMonths[2]) || null;
        // }
        if (item["Certification_Certificate"]) {
          newItem.candidateCertification[0]["certificationName"] =
            item["Certification_Certificate"];
        }
        if (item["Certification_Received_On"]) {
          newItem.candidateCertification[0]["receivedDate"] = this.formatDate(
            item["Certification_Received_On"]
          );
        }
        if (item["Certification_Received_From"]) {
          newItem.candidateCertification[0]["certificateReceivedFrom"] =
            item["Certification_Received_From"];
        }
        if (item["ranking"]) {
          newItem.candidateCertification[0]["ranking"] = item["ranking"];
        }
        if (Object.values(newItem.candidateCertification[0]).length === 0) {
          newItem.candidateCertification = [];
        }
        if (Object.values(newItem.candidateEducation[0]).length === 0) {
          newItem.candidateEducation = [];
        }
        if (Object.values(newItem.candidateExperience[0]).length === 0) {
          newItem.candidateExperience = [];
        }
        return newItem;
      });
      this.importConfirmation = true;
    },

    // called when file uploaded in step 1
    uploadFile(event) {
      this.fileContent = event;
    },
    // called from step 1 when error while uploading or removing the file
    fileRemoveOrError() {
      this.fileContent = [];
      this.matchedCount = 0;
      this.errorsCountInExcel = 0;
    },
    insertCandidateData(data) {
      if (data.length) {
        let vm = this;
        vm.isLoading = true;
        vm.$apollo
          .mutate({
            mutation: UPLOAD_BULK_CANDIDATE_DETAILS,
            client: "apolloClientAH",
            variables: {
              candidateList: data,
            },
          })
          .then((response) => {
            if (
              response &&
              response.data &&
              response.data.uploadBulkCandidateDetails
            ) {
              let { validationError } =
                response.data.uploadBulkCandidateDetails;
              validationError = JSON.parse(validationError);
              let excelInvalidData = this.$refs.bulkImportStep3.invalidData;
              let remainingData = [];
              let inputData = this.$refs.bulkImportStep3.editorData;
              for (let i = 0; i < inputData.length; i++) {
                if (excelInvalidData.includes(inputData[i].$id)) {
                  //Check if data is not already there in remaining data
                  if (!remainingData.includes(inputData[i])) {
                    remainingData.push(inputData[i]);
                  }
                }
              }
              let backendErrorsWithMesages = [];
              // Validation Backend Error Exists
              if (validationError && validationError.length) {
                for (let i = 0; i < validationError.length; i++) {
                  for (
                    let j = 0;
                    j < validationError[i].failedArrays.length;
                    j++
                  ) {
                    for (let k = 0; k < inputData.length; k++) {
                      if (!remainingData.includes(inputData[k])) {
                        remainingData.push(inputData[k]);
                      }
                      let error = JSON.parse(JSON.stringify(inputData[k]));
                      error.Message = validationError[i].Message;
                      backendErrorsWithMesages.push(error);
                    }
                  }
                }
              }
              this.excelEditorData = remainingData;
              this.$refs.bulkImportStep3.editorData = remainingData;
              //Set Field Error
              if (backendErrorsWithMesages && backendErrorsWithMesages.length) {
                for (let i = 0; i < backendErrorsWithMesages.length; i++) {
                  let message = backendErrorsWithMesages[i].Message;
                  let data = backendErrorsWithMesages[i];
                  let field = {};
                  if (field && message && message !== undefined) {
                    field.name = message.includes("First Name")
                      ? "First Name"
                      : message.includes("Last Name")
                      ? "Last Name"
                      : "Job Candidate";
                  }
                  this.$refs.bulkImportStep3.setFieldError(
                    message,
                    data,
                    field
                  );
                }
              }

              vm.importConfirmation = false;
              vm.isLoading = false;
              if (
                !excelInvalidData.length &&
                !backendErrorsWithMesages.length
              ) {
                let snackbarData = {
                  isOpen: true,
                  type: "success",
                  message: "Job Candidates imported successfully.",
                };
                vm.showAlert(snackbarData);
                vm.closeForm();
                this.$emit("refetch-data");
              }
            } else {
              vm.handleImportError();
              vm.importConfirmation = false;
              vm.closeForm();
            }
          })
          .catch((err) => {
            vm.handleImportError(err);
            vm.importConfirmation = false;
            vm.closeForm();
          });
      } else {
        this.importConfirmation = false;
        this.closeForm();
      }
    },
    handleImportError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "importing",
        form: "Job candidates",
        isListError: false,
      });
    },
    //Function to show error or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
<style>
.job-candidate-container {
  padding: 5em 2em 0em 3em;
}
.v-bottom-navigation__content {
  justify-content: space-around;
  flex-direction: column;
}
.dp__button_bottom {
  display: none;
}
</style>
