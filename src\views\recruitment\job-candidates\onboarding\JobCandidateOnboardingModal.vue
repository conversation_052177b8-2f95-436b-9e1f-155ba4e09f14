<template>
  <v-card class="rounded-lg mb-6 pa-2">
    <v-card-title class="d-flex align-center">
      <span class="text-h6 font-weight-medium text-grey-darken-1">
        Candidate Onboarding
      </span>
      <v-spacer> </v-spacer>
      <v-btn
        icon="fas fa-times"
        variant="text"
        color="primary"
        @click="onClickClose()"
      ></v-btn>
    </v-card-title>
    <v-card-text
      style="height: calc(100vh - 270px); overflow-y: scroll !important"
    >
      <v-form ref="inviteForm">
        <v-row>
          <v-col cols="12" md="6" lg="4">
            <v-text-field
              ref="name"
              v-model="name"
              :counter="50"
              :rules="[
                required('Name', name),
                multilingualNameValidation('Name', name),
                minLengthValidation('Name', name, 1),
                maxLengthValidation('Name', name, 50),
              ]"
              :disabled="true"
              variant="solo"
              @update:model-value="onChangeFields()"
            >
              <template v-slot:label>
                <span>Name</span>
                <span style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" lg="4">
            <v-text-field
              v-model="email"
              ref="email"
              :counter="50"
              :rules="[
                required('Email Address', email),
                emailValidation('Email Address', email),
              ]"
              variant="solo"
              @update:model-value="onChangeFields()"
              ><template v-slot:label>
                Email Address
                <span style="color: red">*</span>
              </template>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" lg="4">
            <CustomSelect
              v-model="selectedDesignation"
              ref="selectedDesignation"
              :isLoading="designationListLoading"
              :rules="[required('Designation', selectedDesignation)]"
              :items="designationList"
              item-title="Designation_Name"
              item-value="Designation_Id"
              label="Designation"
              :noDataText="noDataText"
              :isRequired="true"
              variant="solo"
              placeholder="Type minimum 3 characters to list"
              :isAutoComplete="true"
              :itemSelected="selectedDesignation"
              @selected-item="onDesignationSelect"
              @update-search-value="callDesignationList($event)"
            />
            <v-btn
              class="mt-n4 ml-n2"
              color="primary"
              variant="text"
              size="small"
              @click="openRedirectingForm('in/core-hr/designations')"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
              Designation</v-btn
            >
          </v-col>
          <v-col
            v-if="
              labelList['151'] && labelList['151'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="6"
            lg="4"
          >
            <v-row>
              <v-col cols="10">
                <CustomSelect
                  v-model="selectedOrganizationGroup"
                  ref="selectedOrganizationGroup"
                  :items="orgGroupList"
                  :isLoading="orgGroupListLoading"
                  item-title="organizationGroupFullName"
                  item-value="organizationGroupId"
                  :label="labelList['151'].Field_Alias"
                  :isAutoComplete="true"
                  :isRequired="
                    labelList['151'].Mandatory_Field === 'Yes' ? true : false
                  "
                  :rules="
                    labelList['151'].Mandatory_Field === 'Yes'
                      ? [
                          required(
                            labelList['151'].Field_Alias,
                            selectedOrganizationGroup
                          ),
                        ]
                      : [true]
                  "
                  variant="solo"
                  :itemSelected="selectedOrganizationGroup"
                  @selected-item="
                    (selectedOrganizationGroup = $event), onChangeFields()
                  "
                />
              </v-col>
              <v-col cols="2">
                <v-btn
                  color="transparent"
                  variant="flat"
                  class="ml-n5 mt-3"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="fetchOrganizationGroups()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-btn
              class="mt-n4 ml-n2"
              color="primary"
              variant="text"
              size="small"
              @click="openRedirectingForm('v3/core-hr/organization-group')"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
              {{ labelList["151"]?.Field_Alias }}</v-btn
            >
          </v-col>
          <v-col
            v-if="
              labelList['115'] &&
              labelList['115'].Field_Visiblity === 'Yes' &&
              fieldForce
            "
            cols="12"
            md="6"
            lg="4"
          >
            <CustomSelect
              v-model="selectedServiceProvider"
              ref="selectedServiceProvider"
              :items="serviceProviderList"
              :isLoading="dropdownListLoading"
              item-title="Service_Provider_Name"
              item-value="Service_Provider_Id"
              :label="labelList['115'].Field_Alias"
              :isAutoComplete="true"
              :isRequired="
                labelList['115'].Mandatory_Field === 'Yes' ? true : false
              "
              :rules="
                labelList['115'].Mandatory_Field === 'Yes'
                  ? [
                      required(
                        labelList['115'].Field_Alias,
                        selectedServiceProvider
                      ),
                    ]
                  : [true]
              "
              variant="solo"
              :itemSelected="selectedServiceProvider"
              @selected-item="
                (selectedServiceProvider = $event), onChangeFields()
              "
            ></CustomSelect
          ></v-col>
          <v-col
            v-if="labelList[384]?.Field_Visiblity?.toLowerCase() === 'yes'"
            cols="12"
            md="6"
            lg="4"
          >
            <v-row>
              <v-col cols="10"
                ><CustomSelect
                  v-model="selectedBusinessUnit"
                  ref="selectedBusinessUnit"
                  :items="businessUnitList"
                  item-title="businessUnit"
                  item-value="businessUnitId"
                  :label="labelList[384]?.Field_Alias"
                  :isRequired="
                    labelList[384]?.Mandatory_Field?.toLowerCase() === 'yes'
                  "
                  :rules="
                    labelList['384']?.Mandatory_Field?.toLowerCase() === 'yes'
                      ? [
                          required(
                            labelList['384']?.Field_Alias,
                            selectedBusinessUnit
                          ),
                        ]
                      : [true]
                  "
                  variant="solo"
                  :isAutoComplete="true"
                  :isLoading="businessUnitListLoading"
                  :itemSelected="selectedBusinessUnit"
                  @selected-item="
                    (selectedBusinessUnit = $event), onChangeFields()
                  "
                />
              </v-col>
              <v-col cols="2">
                <v-btn
                  color="transparent"
                  variant="flat"
                  class="ml-n5 mt-3"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="fetchBusinessUnit()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-btn
              class="mt-n4 ml-n2"
              color="primary"
              variant="text"
              size="small"
              @click="openRedirectingForm('v3/core-hr/business-unit')"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
              {{ labelList[384]?.Field_Alias }}</v-btn
            >
          </v-col>
          <v-col cols="12" md="6" lg="4">
            <v-row>
              <v-col cols="10">
                <CustomSelect
                  v-model="selectedDepartment"
                  ref="selectedDepartment"
                  :isLoading="dropdownListLoading"
                  :rules="[required('Department', selectedDepartment)]"
                  :items="departmentList"
                  item-title="Department_Name"
                  item-value="Department_Id"
                  label="Department"
                  :isRequired="true"
                  variant="solo"
                  :isAutoComplete="true"
                  :itemSelected="selectedDepartment"
                  @selected-item="
                    (selectedDepartment = $event), onChangeFields()
                  "
                />
              </v-col>
              <v-col cols="2">
                <v-btn
                  color="transparent"
                  variant="flat"
                  class="ml-n5 mt-3"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="fetchDropdownData()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-btn
              class="mt-n4 ml-n2"
              color="primary"
              variant="text"
              size="small"
              @click="openRedirectingForm('v3/core-hr/department-hierarchy')"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
              Department</v-btn
            >
          </v-col>
          <v-col cols="12" md="6" lg="4">
            <v-row>
              <v-col cols="10">
                <CustomSelect
                  v-model="selectedLocation"
                  ref="selectedLocation"
                  :items="locationList"
                  :isLoading="dropdownListLoading"
                  item-title="Location_Name"
                  item-value="Location_Id"
                  :rules="[required('Location', selectedLocation)]"
                  label="Location"
                  variant="solo"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :itemSelected="selectedLocation"
                  @selected-item="(selectedLocation = $event), onChangeFields()"
                />
              </v-col>
              <v-col cols="2">
                <v-btn
                  color="transparent"
                  variant="flat"
                  class="ml-n5 mt-3"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="fetchDropdownData()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-btn
              class="mt-n4 ml-n2"
              color="primary"
              variant="text"
              size="small"
              @click="openRedirectingForm('v3/core-hr/locations')"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
              Location</v-btn
            >
          </v-col>
          <v-col
            v-if="labelList[425]?.Field_Visiblity.toLowerCase() === 'yes'"
            cols="12"
            md="6"
            lg="4"
          >
            <CustomSelect
              ref="selectedServiceProvider"
              v-model="selectedJobRoles"
              :items="jobRolesList"
              :isLoading="jobRolesListLoading"
              item-title="Job_Role"
              item-value="Job_Role_Id"
              :label="labelList[425].Field_Alias"
              :isAutoComplete="true"
              :isRequired="
                labelList[425].Mandatory_Field.toLowerCase() === 'yes'
                  ? true
                  : false
              "
              :rules="[
                labelList[425].Mandatory_Field.toLowerCase() == 'yes'
                  ? selectedJobRoles
                    ? required(
                        `${labelList[425].Field_Alias}`,
                        selectedJobRoles[0]
                      )
                    : required(
                        `${labelList[425].Field_Alias}`,
                        selectedJobRoles
                      )
                  : true,
              ]"
              variant="solo"
              :selectProperties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              :itemSelected="selectedJobRoles"
              @selected-item="(selectedJobRoles = $event), onChangeFields()"
            ></CustomSelect>
          </v-col>
          <v-col cols="12" md="6" lg="4">
            <v-row>
              <v-col cols="10">
                <CustomSelect
                  v-model="selectedSchedule"
                  ref="selectedSchedule"
                  :isLoading="dropdownListLoading"
                  :rules="[required('Work Schedule', selectedSchedule)]"
                  :items="workScheduleList"
                  item-title="Title"
                  item-value="WorkSchedule_Id"
                  label="Work Schedule"
                  :isRequired="true"
                  variant="solo"
                  :isAutoComplete="true"
                  :itemSelected="selectedSchedule"
                  @selected-item="(selectedSchedule = $event), onChangeFields()"
                />
              </v-col>
              <v-col cols="2">
                <v-btn
                  color="transparent"
                  variant="flat"
                  class="ml-n5 mt-3"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="fetchDropdownData()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-btn
              class="mt-n4 ml-n2"
              color="primary"
              variant="text"
              size="small"
              @click="openRedirectingForm('in/core-hr/work-schedule')"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add Work
              Schedule</v-btn
            >
          </v-col>
          <v-col cols="12" md="6" lg="4">
            <v-row>
              <v-col cols="10">
                <CustomSelect
                  v-model="selectedEmploymentType"
                  ref="selectedEmploymentType"
                  :isLoading="dropdownListLoading"
                  :rules="[required('Employment Type', selectedEmploymentType)]"
                  :items="employeeTypeList"
                  item-title="Employee_Type"
                  item-value="EmpType_Id"
                  label="Employment Type"
                  :isRequired="true"
                  variant="solo"
                  :isAutoComplete="true"
                  :itemSelected="selectedEmploymentType"
                  @selected-item="
                    (selectedEmploymentType = $event), onChangeFields()
                  "
                />
              </v-col>
              <v-col cols="2">
                <v-btn
                  color="transparent"
                  variant="flat"
                  class="ml-n5 mt-3"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="fetchDropdownData()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-btn
              class="mt-n4 ml-n2"
              color="primary"
              variant="text"
              size="small"
              @click="openRedirectingForm('v3/core-hr/employee-type')"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add Employment
              Type</v-btn
            >
          </v-col>
          <v-col cols="12" md="6" lg="4">
            <CustomSelect
              v-model="selectedManager"
              ref="selectedManager"
              :isLoading="dropdownListLoading"
              :rules="[required('Manager', selectedManager)]"
              :items="managerList"
              item-title="Manager_Name"
              item-value="Manager_Id"
              label="Manager"
              variant="solo"
              :isAutoComplete="true"
              :isRequired="true"
              :itemSelected="selectedManager"
              @selected-item="(selectedManager = $event), onChangeFields()"
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            md="6"
            lg="4"
            v-if="labelList[274]?.Field_Visiblity == 'Yes' && selectedManager"
            ><p class="text-subtitle-1 text-grey-darken-1">
              <strong>{{ labelList[274].Field_Alias }}</strong>
            </p>

            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(secondLineManagerDetails) }}
            </p></v-col
          >
          <v-col cols="12" md="6" lg="4">
            <v-text-field v-model="jobCode" label="Job Code" variant="solo">
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" lg="4">
            <v-menu
              v-model="dojMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="formattedDateOfJoin"
                  v-model="formattedDateOfJoin"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="[required('Date of Joining', formattedDateOfJoin)]"
                  readonly
                  v-bind="props"
                  variant="solo"
                >
                  <template v-slot:label>
                    Date of Joining
                    <span style="color: red">*</span>
                  </template></v-text-field
                >
              </template>
              <v-date-picker v-model="selectedJoiningDate"></v-date-picker>
            </v-menu>
          </v-col>
          <v-col
            v-if="
              labelList['153'] && labelList['153'].Field_Visiblity === 'Yes'
            "
            cols="12"
            md="6"
            lg="4"
          >
            <v-menu
              v-model="probationMenu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  ref="formattedProbationDate"
                  v-model="formattedProbationDate"
                  prepend-inner-icon="fas fa-calendar"
                  :rules="
                    labelList['153'].Mandatory_Field === 'Yes'
                      ? [
                          required(
                            labelList['153'].Field_Alias,
                            formattedProbationDate
                          ),
                        ]
                      : [true]
                  "
                  variant="solo"
                  readonly
                  v-bind="props"
                  :disabled="!selectedJoiningDate || !selectedDesignation"
                  @update:model-value="onChangeFields()"
                >
                  <template v-slot:label>
                    {{ labelList["153"].Field_Alias }}
                    <span
                      v-if="labelList['153'].Mandatory_Field === 'Yes'"
                      style="color: red"
                      >*</span
                    >
                  </template></v-text-field
                >
              </template>
              <v-date-picker
                v-model="selectedProbationDate"
                :min="probationMinDate"
              ></v-date-picker> </v-menu
          ></v-col>
          <v-col cols="12" md="6" lg="4" class="d-flex align-center">
            <v-text-field
              v-model="urlValidity"
              ref="urlValidity"
              type="number"
              :rules="[
                required('URL Validity ', urlValidity),
                numericValidation('URL Validity ', urlValidity),
                maxLengthForDigitsValidation('URL Validity', urlValidity),
              ]"
              required
              variant="solo"
              @update:model-value="onChangeFields()"
              ><template v-slot:label>
                <span>URL Validity</span>
                <span style="color: red">*</span>
              </template>
            </v-text-field>
            <CustomSelect
              v-model="selectedPeriod"
              ref="CustomSelect"
              :rules="[required('Duration', selectedPeriod)]"
              :items="dropdownPeriod"
              variant="solo"
              :isAutoComplete="true"
              :isRequired="true"
              class="ml-2"
              :itemSelected="selectedPeriod"
              @selected-item="(selectedPeriod = $event), onChangeFields()"
            ></CustomSelect>
          </v-col>
          <v-col
            cols="12"
            md="6"
            lg="4"
            v-if="labelList[303]?.Field_Visiblity == 'Yes'"
          >
            <v-row>
              <v-col cols="10">
                <CustomSelect
                  ref="MandatoryDocuments"
                  :label="labelList[303].Field_Alias"
                  :rules="[
                    labelList[303].Mandatory_Field == 'Yes'
                      ? required(labelList[303].Field_Alias, mandatoryDocuments)
                      : true,
                  ]"
                  :items="mandatoryDocumentsList"
                  variant="solo"
                  :isAutoComplete="true"
                  itemTitle="Group_Name"
                  itemValue="Group_Id"
                  :isRequired="
                    labelList[303].Mandatory_Field == 'Yes' ? true : false
                  "
                  :select-properties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                  :isLoading="documentListLoading"
                  :item-selected="mandatoryDocuments"
                  @selected-item="
                    (mandatoryDocuments = $event), onChangeFields()
                  "
                />
              </v-col>
              <v-col cols="2">
                <v-btn
                  color="transparent"
                  variant="flat"
                  class="ml-n5 mt-3"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="retrieveDocumentTagList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-btn
              class="mt-n4 ml-n2"
              color="primary"
              variant="text"
              size="small"
              @click="openRedirectingForm('v3/core-hr/document-subtype')"
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon>Add
              {{ labelList[303]?.Field_Alias }}</v-btn
            >
          </v-col>
          <v-col
            cols="12"
            md="6"
            lg="4"
            v-if="labelList[460]?.Field_Visiblity?.toLowerCase() == 'yes'"
          >
            <v-row>
              <v-col cols="10">
                <CustomSelect
                  ref="AccreditationGroups"
                  :label="
                    labelList[460]?.Field_Alias ||
                    'Accreditation Enforcement Groups'
                  "
                  :isRequired="
                    labelList[460]?.Mandatory_Field?.toLowerCase() == 'yes'
                      ? true
                      : false
                  "
                  :rules="[
                    labelList[460]?.Mandatory_Field?.toLowerCase() == 'yes'
                      ? required(
                          labelList[460].Field_Alias ||
                            'Accreditation Enforcement Groups',
                          accreditationGroups
                        )
                      : true,
                  ]"
                  :items="accreditationGroupsList"
                  variant="solo"
                  :isAutoComplete="true"
                  itemTitle="Group_Name"
                  itemValue="Group_Id"
                  :select-properties="{
                    multiple: true,
                    chips: true,
                    clearable: true,
                    closableChips: true,
                  }"
                  :isLoading="accreditationListLoading"
                  :item-selected="accreditationGroups"
                  @selected-item="accreditationGroups = $event"
                />
              </v-col>
              <v-col cols="2">
                <v-btn
                  color="transparent"
                  variant="flat"
                  class="ml-n5 mt-3"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="retrieveAccreditationGroupsList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </v-col>
            </v-row>
            <v-btn
              class="mt-n4 ml-n2"
              color="primary"
              variant="text"
              size="small"
              @click="
                openRedirectingForm(
                  'v3/core-hr/accreditation-category-and-type'
                )
              "
            >
              <v-icon class="mr-1" size="14">fas fa-plus</v-icon>Add
              {{
                labelList[460]?.Field_Alias ||
                "Accreditation Enforcement Groups"
              }}</v-btn
            >
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
  </v-card>
  <v-bottom-navigation v-if="openBottomSheet">
    <v-sheet
      class="align-center text-center"
      :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
      style="width: 100%"
    >
      <v-row justify="center">
        <v-col cols="6" class="d-flex justify-start pl-2">
          <v-btn
            rounded="lg"
            variant="outlined"
            size="small"
            class="primary"
            style="height: 40px"
            @click="onClickClose()"
            ><span class="primary">Cancel</span></v-btn
          >
        </v-col>
        <v-col cols="6" class="d-flex justify-end pr-4">
          <v-btn
            rounded="lg"
            size="small"
            class="mr-1 secondary"
            variant="elevated"
            style="height: 40px"
            @click="validateInviteForm()"
          >
            <span class="primary">Send Invite</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-sheet>
  </v-bottom-navigation>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="confirmClose()"
  >
  </AppWarningModal>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { checkNullValue } from "@/helper";
// Queries
import {
  RETRIEVE_SECONDLINE_MANAGER,
  RETRIEVE_DOCUMENTS_TAG,
  ADD_CANDIDATE_INVITE_ONBOARDING,
  LIST_JOB_ROLES,
  RETRIEVE_LOCATION_BASED_ON_DESIGNATION,
} from "@/graphql/onboarding/individualQueries.js";
import { RETRIEVE_ORGANISATION_GROUP_LIST } from "@/graphql/corehr/organisationGroupQueries";
import { RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION } from "@/graphql/employee-profile/profileQueries.js";
import { LIST_BUSINESS_UNIT } from "@/graphql/dropDownQueries.js";
import { LIST_ACCREDITATION_ENFORCEMENT_GROUPS } from "@/graphql/corehr/accreditationCategoryAndTypeQueries.js";
export default {
  name: "JobCandidateOnboardingModal",
  components: {
    CustomSelect,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    locationAutoPrefill: {
      type: String,
      default: "No",
    },
  },
  mixins: [validationRules],
  emits: ["submit-job-post-form", "close-add-invite-form"],
  data() {
    return {
      fieldForce: 0,
      openBottomSheet: true,
      openWarningModal: false,
      isLoading: false,
      // Form data
      name: "",
      jobCode: "",
      email: "",
      urlValidity: "",
      selectedLocation: null,
      selectedDepartment: null,
      selectedDesignation: null,
      selectedSchedule: null,
      selectedServiceProvider: null,
      selectedPeriod: "Days",
      selectedEmploymentType: null,
      selectedManager: null,
      selectedOrganizationGroup: null,
      selectedBusinessUnit: null,
      selectedJoiningDate: null,
      selectedProbationDate: null,
      // dropdowns
      dropdownPeriod: ["Minute", "Hours", "Days"],
      locationList: [],
      departmentList: [],
      designationList: [],
      workScheduleList: [],
      serviceProviderList: [],
      employeeTypeList: [],
      managerList: [],
      dropdownListLoading: false,
      orgGroupList: [],
      orgGroupListLoading: false,
      businessUnitList: [],
      businessUnitListLoading: false,
      secondLineManagerDetails: null,
      isErrorInSeconLineManager: false,
      designationListLoading: false,
      //Date-picker
      formattedDateOfJoin: "",
      formattedProbationDate: "",
      dojMenu: false,
      probationMenu: false,
      documentListLoading: false,
      mandatoryDocuments: null,
      mandatoryDocumentsList: [],
      searchString: "",
      jobRolesListLoading: false,
      jobRolesList: [],
      selectedJobRoles: [],
      isFormDirty: false,
      accreditationGroupsList: [],
      accreditationGroups: null,
      accreditationListLoading: false,
    };
  },
  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00" && date != "Invalid Date") {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
    noDataText() {
      if (this.designationListLoading) {
        return "Loading...";
      } else if (
        !this.designationListLoading &&
        this.designationList.length == 0 &&
        this.searchString.length >= 3
      ) {
        return "no data found";
      } else {
        return "Type minimum 3 characters to list";
      }
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    probationMinDate() {
      if (
        this.selectedJoiningDate &&
        this.selectedJoiningDate !== "0000-00-00"
      ) {
        return moment(this.selectedJoiningDate).format("YYYY-MM-DD");
      } else return null;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    if (
      this.selectedItem.Designation_Id &&
      this.selectedItem.Designation_Name
    ) {
      this.designationList = [
        {
          Designation_Id: this.selectedItem.Designation_Id,
          Designation_Name: this.selectedItem.Designation_Name,
        },
      ];
    }
    this.name = this.selectedItem.Candidate_Name;
    this.email = this.selectedItem.Candidate_Email;
    this.selectedJoiningDate = this.selectedItem.Date_Of_Join;
    this.candidateId = this.selectedItem.Candidate_Id;
    this.retrieveProbationDate();
    this.fetchDropdownData();
    this.fetchOrganizationGroups();
    this.fetchBusinessUnit();
    this.retrieveDocumentTagList();
    this.retrieveAccreditationGroupsList();
  },
  watch: {
    selectedManager() {
      if (
        this.selectedManager &&
        this.labelList[274]?.Field_Visiblity === "Yes"
      ) {
        this.fetchSecondlineManagerDetails(this.selectedManager);
      }
    },
    selectedProbationDate(val) {
      if (val) {
        this.probationMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedProbationDate = dateValue;
      }
    },
    selectedJoiningDate(val) {
      if (val) {
        this.retrieveProbationDate();
        this.dojMenu = false;
        let dateValue = this.formatDate(val);
        this.formattedDateOfJoin = dateValue;
      }
    },
    selectedDesignation(val) {
      if (val) {
        this.fetchJobRoles(val);
      }
    },
  },
  methods: {
    onClickClose() {
      this.openWarningModal = true;
    },
    onChangeFields() {
      this.isFormDirty = true;
    },
    confirmClose() {
      this.openBottomSheet = false;
      this.openWarningModal = false;
      this.$emit("close-add-invite-form");
    },
    onCloseWarningModal() {
      this.openWarningModal = false;
    },
    onDesignationSelect(designationId) {
      this.onChangeFields();
      this.selectedDesignation = designationId;
      this.retrieveProbationDate();
      if (this.locationAutoPrefill?.toLowerCase() === "yes" && designationId) {
        this.getLocationId();
      }
    },
    checkNullValue,
    // Retrieving the second-line manager details
    fetchSecondlineManagerDetails(employeeId) {
      let vm = this;
      vm.isLoading = true;
      vm.isErrorInSeconLineManager = false;
      vm.$apollo
        .query({
          query: RETRIEVE_SECONDLINE_MANAGER,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: { employeeId: employeeId },
        })
        .then(({ data }) => {
          vm.isLoading = false;
          if (data && data.retrieveManagerDetails) {
            const response = data.retrieveManagerDetails;
            if (response.errorCode === "") {
              vm.secondLineManagerDetails = response.managerName;
            } else {
              vm.handleManagerError(response.message);
            }
          } else {
            vm.handleManagerError();
          }
        })
        .catch((err) => {
          vm.handleManagerError(err);
        });
    },
    handleManagerError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "candidates",
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInSeconLineManager = true;
        });
    },
    addCandidateInviteOnboard() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_CANDIDATE_INVITE_ONBOARDING,
          client: "apolloClientW",
          variables: {
            Expire_Value: parseInt(vm.urlValidity),
            Expire_Type:
              vm.selectedPeriod === "Minute"
                ? 1
                : vm.selectedPeriod === "Hours"
                ? 2
                : 3,
            Department_Id: vm.selectedDepartment,
            Designation_Id: vm.selectedDesignation,
            Location_Id: vm.selectedLocation,
            Date_Of_Join: moment(vm.selectedJoiningDate).isValid()
              ? moment(vm.selectedJoiningDate).format("YYYY-MM-DD")
              : null,
            Job_Code: vm.jobCode,
            Probation_Date: moment(vm.selectedProbationDate).isValid()
              ? moment(vm.selectedProbationDate).format("YYYY-MM-DD")
              : null,
            EmpType_Id: vm.selectedEmploymentType,
            Manager_Id: vm.selectedManager,
            Work_Schedule: vm.selectedSchedule,
            Service_Provider_Id: vm.selectedServiceProvider
              ? vm.selectedServiceProvider
              : 0,
            Business_Unit_Id: vm.selectedBusinessUnit
              ? vm.selectedBusinessUnit
              : 0,
            Name: vm.name,
            MailTo: vm.email,
            organizationGroupId: vm.fieldForce
              ? vm.selectedOrganizationGroup
              : 0,
            candidateId: vm.candidateId,
            groupIds: vm.mandatoryDocuments,
            accreditationGroupIds: vm.accreditationGroups,
            Job_Role_Ids: vm.selectedJobRoles,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.addCandidateInviteOnboard &&
            !response.data.addCandidateInviteOnboard.errorCode
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Invitation sent successfully!",
            };
            vm.showAlert(snackbarData);
            vm.openBottomSheet = false;
            vm.$emit("submit-job-post-form");
            vm.isLoading = false;
          } else {
            vm.handleInviteErrors();
            vm.isLoading = false;
          }
        })
        .catch((error) => {
          this.handleInviteErrors(error);
          this.isLoading = false;
        });
    },
    handleInviteErrors(error) {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "inviting",
        form: "candidates",
        isListError: false,
      });
    },
    retrieveAccreditationGroupsList() {
      let vm = this;
      vm.accreditationListLoading = true;
      vm.$apollo
        .query({
          query: LIST_ACCREDITATION_ENFORCEMENT_GROUPS,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response?.data?.listAccreditationEnforcementGroups?.groups.length
          ) {
            vm.accreditationGroupsList =
              response.data.listAccreditationEnforcementGroups.groups;
          }
          vm.accreditationListLoading = false;
        })
        .catch(() => {
          vm.accreditationListLoading = false;
        });
    },
    //retrieve Organization Group data
    fetchOrganizationGroups() {
      let vm = this;
      vm.orgGroupListLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_ORGANISATION_GROUP_LIST,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: { formId: 179 },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listOrganizationGroup &&
            !response.data.listOrganizationGroup.errorCode
          ) {
            const { organizationGroupObject } =
              response.data.listOrganizationGroup;
            if (organizationGroupObject) {
              const orgList = organizationGroupObject.organizationGroupList
                ? organizationGroupObject.organizationGroupList
                : [];
              vm.orgGroupList = orgList.filter(
                (group) => group.status === "Active"
              );
              this.selectedOrganizationGroup =
                this.selectedItem.Organization_Group_Id;
            } else {
              vm.orgGroupList = [];
            }
          } else {
            vm.orgGroupList = [];
          }
          vm.orgGroupListLoading = false;
        })
        .catch((err) => {
          vm.orgGroupListLoading = false;
          vm.handleOrgGroupError(err);
        });
    },
    handleOrgGroupError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "organization unit configuration",
        isListError: false,
      });
    },
    // fetching the business unit dropdown list
    fetchBusinessUnit() {
      let vm = this;
      vm.businessUnitListLoading = true;
      vm.$apollo
        .query({
          query: LIST_BUSINESS_UNIT,
          client: "apolloClientI",
          variables: {
            action: "active",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listBusinessUnitInDropdown &&
            !response.data.listBusinessUnitInDropdown.errorCode &&
            response.data.listBusinessUnitInDropdown.settings &&
            response.data.listBusinessUnitInDropdown.settings.length > 0
          ) {
            vm.businessUnitList =
              response.data.listBusinessUnitInDropdown.settings;
          } else {
            vm.businessUnitList = [];
          }
          vm.businessUnitListLoading = false;
        })
        .catch((err) => {
          vm.businessUnitListLoading = false;
          vm.handleBusinessUnitError(err);
        });
    },
    handleBusinessUnitError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "business unit",
        isListError: false,
      });
    },
    // Retrieve the probation date based on the latest designation and joining date
    retrieveProbationDate() {
      if (this.selectedJoiningDate && this.selectedDesignation) {
        this.$apollo
          .query({
            query: RETRIEVE_PROBATION_DATE_BASED_ON_DESIGNATION,
            client: "apolloClientAC",
            variables: {
              designationId: this.selectedDesignation,
              dateOfJoin: moment(this.selectedJoiningDate).format("YYYY-MM-DD"),
            },
          })
          .then((response) => {
            if (
              response.data &&
              response.data.retrieveProbationDate &&
              !response.data.retrieveProbationDate.errorCode
            ) {
              this.selectedProbationDate = new Date(
                response.data.retrieveProbationDate.probationDate
              );
            } else {
              this.handleProbationDateError();
            }
          })
          .catch((err) => {
            this.handleProbationDateError(err);
          });
      } else {
        this.selectedProbationDate = null;
      }
    },
    handleProbationDateError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "probation date",
        isListError: false,
      });
    },
    // API for Fetching the dropDown data
    async fetchDropdownData() {
      this.dropdownListLoading = true;
      await this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const {
              departments,
              locations,
              employeeType,
              workSchedules,
              serviceProvider,
              managers,
              fieldForce,
            } = res.data.getDropDownBoxDetails;
            this.departmentList = departments;
            this.locationList = locations;
            this.workScheduleList = workSchedules;
            this.serviceProviderList = serviceProvider;
            this.employeeTypeList = employeeType;
            this.managerList = managers;
            this.fieldForce = fieldForce;
            this.prefillForm();
          }
          this.dropdownListLoading = false;
        })
        .catch(() => {
          this.dropdownListLoading = false;
        });
    },

    // This is the validate function that validates that compulsory fields are filled or not
    async validateInviteForm() {
      // checking whether the fields of form are valid or not
      const { valid } = await this.$refs.inviteForm.validate();
      // submit the form only if all the fields are filled
      if (valid) {
        this.addCandidateInviteOnboard();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "selectedLocation",
                "selectedOrganizationGroup",
                "selectedServiceProvider",
                "selectedBusinessUnit",
                "selectedDepartment",
                "selectedDesignation",
                "selectedSchedule",
                "selectedEmploymentType",
                "selectedManager",
                "selectedPeriod",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 0.4, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },
    prefillForm() {
      this.selectedDepartment = this.selectedItem.Department_Id;
      this.selectedLocation = this.selectedItem.Location_Id;
      this.selectedDesignation = this.selectedItem.Designation_Id;
      this.selectedSchedule = this.selectedItem.WorkSchedule_Id;
      this.selectedServiceProvider = this.selectedItem.Organization_Unit_Id;
      this.selectedEmploymentType = this.selectedItem.EmpType_Id;
      this.selectedManager = this.selectedItem.Manager_Id;
      this.selectedBusinessUnit = this.selectedItem.Business_Unit_Id;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    retrieveDocumentTagList() {
      let vm = this;
      vm.documentListLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_DOCUMENTS_TAG,
          client: "apolloClientV",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.listdocumentEnforcementGroup &&
            data.listdocumentEnforcementGroup.groupIds &&
            data.listdocumentEnforcementGroup.groupIds.length > 0
          ) {
            vm.mandatoryDocumentsList =
              data.listdocumentEnforcementGroup.groupIds;
          }
          vm.documentListLoading = false;
        })
        .catch(() => {
          vm.documentListLoading = false;
        });
    },
    callDesignationList(searchString) {
      this.searchString = searchString;
      if (searchString.length >= 3 && !this.selectedDesignation) {
        this.getDesignationList(searchString);
      }
    },
    openRedirectingForm(path) {
      const redirectionPath = `${this.baseUrl}${path}`;
      window.open(redirectionPath, "_blank");
    },

    async getDesignationList(searchString) {
      this.designationListLoading = true;
      await this.$store
        .dispatch("getDesignationList", {
          status: "",
          searchString: searchString,
        })
        .then((res) => {
          if (
            res.data &&
            res.data.getDesignationDetails &&
            !res.data.getDesignationDetails.errorCode
          ) {
            const { designationResult } = res.data.getDesignationDetails;
            this.designationList = designationResult;
          }
          this.designationListLoading = false;
        })
        .catch(() => {
          this.designationListLoading = false;
          this.dropdownDesignation = [];
        });
    },
    fetchJobRoles(designationId) {
      let vm = this;
      vm.jobRolesListLoading = true;
      vm.isErrorInSeconLineManager = false;
      vm.$apollo
        .query({
          query: LIST_JOB_ROLES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
          variables: { formId: 178, designationId: parseInt(designationId) },
        })
        .then(({ data }) => {
          vm.jobRolesListLoading = false;
          if (data && data.listJobRoles) {
            const response = data.listJobRoles;
            if (!response.errorCode) {
              vm.jobRolesList = JSON.parse(response.jobRoles);
            } else {
              vm.jobRolesList = [];
            }
          } else {
            vm.jobRolesList = [];
          }
        })
        .catch(() => {
          vm.jobRolesListLoading = false;
          vm.jobRolesList = [];
        });
    },
    getLocationId() {
      let vm = this;
      vm.locationIdLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_LOCATION_BASED_ON_DESIGNATION,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
          variables: {
            formId: 178,
            designationId: vm.selectedDesignation,
          },
        })
        .then((res) => {
          vm.locationIdLoading = false;
          if (
            res.data &&
            res.data.getLocationByDesignation &&
            res.data.getLocationByDesignation.locationId
          ) {
            const response = res.data.getLocationByDesignation;
            vm.selectedLocation = response.locationId;
          } else {
            vm.selectedLocation = null;
          }
        })
        .catch(() => {
          vm.locationIdLoading = false;
          vm.selectedLocation = null;
        });
    },
  },
};
</script>
<style>
.position-relative {
  position: relative;
}
</style>
