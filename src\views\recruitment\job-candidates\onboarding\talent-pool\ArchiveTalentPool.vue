<template>
  <div class="text-center">
    <v-overlay
      v-if="overlay"
      v-model="overlay"
      class="d-flex justify-end overlay"
      persistent
      @click:outside="closeWindow()"
    >
      <template v-slot:default>
        <v-card
          rounded="lg"
          :style="{
            height: windowHeight + 'px',
            width: isMobileView ? '100vw' : '35vw',
          }"
        >
          <v-card-title
            class="d-flex justify-space-between align-center bg-primary"
          >
            <v-tooltip text="Archive - Talent Pool Review" location="bottom">
              <template v-slot:activator="{ props }">
                <div v-bind="props" class="text-h6 text-truncate">
                  Archive - Talent Pool Review
                </div>
              </template>
            </v-tooltip>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="closeWindow(true)"
              color="white"
            ></v-btn>
          </v-card-title>

          <v-card-text v-if="isLoading" class="d-flex justify-center">
            <AppLoading />
          </v-card-text>

          <v-card-text v-else class="overflow-y-auto" style="max-height: 85vh">
            <v-form ref="ArchiveForm" @submit.prevent="">
              <v-row class="px-sm-4 px-md-6 pt-4">
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                >
                  <!-- Reason for Archiving Dropdown -->
                  <CustomSelect
                    :items="archiveReasonList"
                    v-model="archiveReason"
                    label="Reason for Archiving"
                    itemValue="Reason_Id"
                    itemTitle="Reason"
                    ref="archiveReason"
                    :isAutoComplete="true"
                    :isRequired="true"
                    :is-loading="isArchiveReasonLoading"
                    :rules="[required('Reason for Archiving', archiveReason)]"
                    :itemSelected="archiveReason"
                    @selected-item="archiveReason = $event"
                    @update:model-value="deductFormChange()"
                  ></CustomSelect>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  lg="12"
                  md="12"
                  xl="12"
                  class="pl-sm-4 pl-md-6"
                >
                  <!-- Archive Comment -->
                  <v-textarea
                    v-model="archiveComment"
                    rows="2"
                    row-height="10"
                    color="primary"
                    hide-details="auto"
                    variant="solo"
                    label="Comment"
                    counter="500"
                    :rules="[
                      archiveComment
                        ? validateWithRulesAndReturnMessages(
                            archiveComment,
                            'archiveComment',
                            'Archive Comment'
                          )
                        : true,
                    ]"
                    @update:model-value="deductFormChange()"
                  ></v-textarea>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>

          <!-- Cancel and Submit buttons -->
          <v-col cols="12" class="d-flex justify-end pr-4">
            <div
              class="d-flex justify-end pa-2 position-absolute"
              style="bottom: 0"
            >
              <v-btn
                rounded="lg"
                variant="outlined"
                class="px-8 mr-2"
                @click="closeWindow(true)"
              >
                Cancel
              </v-btn>
              <!-- Conditional Next/Submit button -->
              <div class="mr-1">
                <!-- Display Next button if Time is 'Now' -->
                <v-btn
                  class="px-8"
                  rounded="lg"
                  variant="elevated"
                  color="primary"
                  @click="validateForm()"
                >
                  {{ buttonText }}
                </v-btn>
              </div>
            </div>
          </v-col>
        </v-card>
      </template>
    </v-overlay>
  </div>
</template>

<script>
import {
  ARCHIVE_CANDIDATE_DETAILS,
  GET_ARCHIVE_REASONS_LIST,
} from "@/graphql/recruitment/recruitmentQueries.js";
import { checkNullValue } from "@/helper";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";

export default {
  name: "ArchiveTalentPool",
  components: {
    CustomSelect,
  },
  props: {
    candidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    candidateId: {
      type: Number,
      required: true,
    },
    candidateIdSelected: {
      type: Number,
      required: true,
    },
    jobTitle: {
      default: "",
      type: String,
    },
    selectedCandidateDetails: {
      type: Object,
      default: function () {
        return {};
      },
    },
    currentTalentPoolId: {
      type: Number,
      required: true,
    },
  },
  mixins: [validationRules],
  emits: ["form-updated", "refetch-talent-pool-list"],

  data: () => ({
    //others
    isFormDirty: false,
    showConfirmation: false,
    overlay: true,
    isLoading: false,
    archiveCandidates: [],
    archiveReason: null, // Field for archive reason
    archiveComment: "", // Field for archive comment
    buttonText: "Submit",
    isArchiveReasonLoading: false,
    archiveReasonList: [],
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    orgDetails() {
      return this.$store.state.orgDetails;
    },
    companyName() {
      const { organizationName } = this.$store.state.orgDetails;
      return organizationName;
    },
    loginEmployeeUser() {
      return (
        this.$store.state.userDetails.employeeFirstName +
        " " +
        this.$store.state.userDetails.employeeLastName
      );
    },
    loginEmployeeDetails() {
      return this.$store.state.orgDetails.userDetails;
    },
  },
  mounted() {
    this.fetchArchiveReasonList();
  },
  methods: {
    checkNullValue,
    deductFormChange() {
      this.isFormDirty = true;
    },
    async validateForm() {
      let { valid } = await this.$refs.ArchiveForm.validate();
      if (valid) {
        this.archiveCandidate();
      }
    },

    async archiveCandidate() {
      let vm = this;

      try {
        vm.isLoading = true;

        const response = await vm.$apollo.mutate({
          mutation: ARCHIVE_CANDIDATE_DETAILS,
          client: "apolloClientAV",
          fetchPolicy: "no-cache",
          variables: {
            candidateId: vm.candidateId,
            archiveReasonId: vm.archiveReason,
            archiveComment: vm.archiveComment || null,
            mailContent: null,
            action: "talentpool",
            talentPoolId: vm.currentTalentPoolId,
          },
        });

        if (
          response &&
          response.data &&
          response.data.archiveCandidateDetails
        ) {
          const { errorCode, validationError } =
            response.data.archiveCandidateDetails;
          if (!errorCode && !validationError) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Candidate archived successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-talent-pool-list");
            vm.$emit("form-updated");
            vm.closeWindow(true);
          } else {
            vm.handleArchiveCandidateError("archiving candidate");
          }
        } else {
          vm.handleArchiveCandidateError("archiving candidate");
        }
      } catch (err) {
        vm.handleArchiveCandidateError("archiving candidate", err);
      } finally {
        vm.isLoading = false;
        this.overlay = true;
        this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      }
    },
    handleArchiveCandidateError(action, err = "") {
      this.isLoading = false;
      this.closeWindow();
      this.$emit("refetch-talent-pool-list");
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: action,
          form: "archiveCandidate",
          isListError: false,
        })
        .then((validationErrors) => {
          this.validationMessages = validationErrors;
          this.showValidationAlert = true;
        });
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    closeWindow(isSuccess) {
      this.$emit("close-archive-candidates-window", isSuccess);
      this.overlay = true;
    },
    fetchArchiveReasonList() {
      let vm = this;
      vm.isArchiveReasonLoading = true;
      vm.$apollo
        .query({
          query: GET_ARCHIVE_REASONS_LIST,
          client: "apolloClientAY",
          variables: {
            formId: 297,
            stageId: this.candidateDetails?.Hiring_Stage_Id,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getArchiveReasonsList &&
            response.data.getArchiveReasonsList.archiveReasonList &&
            !response.data.getArchiveReasonsList.archiveReasonList.errorCode
          ) {
            vm.archiveReasonList =
              response.data.getArchiveReasonsList.archiveReasonList;
            vm.isArchiveReasonLoading = false;
          } else {
            vm.archiveReasonList = [];
            vm.isArchiveReasonLoading = false;
          }
        })
        .catch((err) => {
          vm.isArchiveReasonLoading = false;
          vm.handleFetchArchiveReasonListError(err);
        });
    },
    handleFetchArchiveReasonListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "archive reason list",
        isListError: false,
      });
    },
  },
};
</script>

<style scoped>
.overlay {
  height: 100% !important;
}

.status-container {
  padding: 8px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
