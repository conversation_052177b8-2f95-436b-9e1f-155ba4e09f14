<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end"
                :isFilter="false"
              />
              <ShiftRotationFilter
                v-if="!showAddEditForm && (itemList.length || isFilterApplied)"
                ref="formFilterRef"
                :itemList="originalList"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="shift -rotation-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-12">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList('Business unit error refetch')"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="Shift rotation is a critical aspect of workforce management that ensures fair distribution of work hours among employees while optimizing operational efficiency. By effectively managing shift rotations, organizations can enhance productivity and maintain employee satisfaction."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="To create an effective shift rotation, please ensure that all relevant fields are filled out accurately. This includes selecting the type of shifts, specifying start and end dates, and noting the frequency of rotations."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      class="mt-1 primary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="addShiftRotation()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      <span>Add</span>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      Reset Filter/Search
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      class="mt-1"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div class="mt-12">
              <div
                v-if="originalList.length > 0 && !isSmallTable"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center ' : 'justify-end'"
              >
                <v-btn
                  v-if="formAccess.add"
                  prepend-icon="fas fa-plus"
                  variant="elevated"
                  rounded="lg"
                  class="mx-1 primary"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="addShiftRotation()"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  <span>Add</span>
                </v-btn>
                <v-btn
                  color="transparent"
                  class="mt-1"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList('Refetch List')"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu v-model="openMoreMenu" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      variant="plain"
                      class="mt-1 ml-n3 mr-n5"
                      v-bind="props"
                    >
                      <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                      <v-icon v-else>fas fa-caret-up</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action"
                      @click="onMoreAction(action)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'pink-lighten-5': isHovering,
                            }"
                          >
                            <v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}
                          </v-list-item-title>
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>

              <v-row>
                <v-col
                  v-if="originalList.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                  class="mb-12"
                >
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Scheduler Name
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Rotation_Id === item.Rotation_Id
                              "
                              class="data-table-side-border d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Scheduler_Name"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Scheduler_Name.length > 50 ? props : ''
                                  "
                                >
                                  {{ checkNullValue(item.Scheduler_Name) }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Repeat Schedule
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Rotation_Id === item.Rotation_Id
                              "
                              class="d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Repeat_Schedule"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Repeat_Schedule.length > 50
                                      ? props
                                      : ''
                                  "
                                >
                                  {{ checkNullValue(item.Repeat_Schedule) }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Enable Roster Leave
                          </div>
                          <section class="d-flex align-center">
                            <div
                              v-if="
                                isSmallTable &&
                                !isMobileView &&
                                selectedItem &&
                                selectedItem.Rotation_Id === item.Rotation_Id
                              "
                              class="d-flex"
                            ></div>
                            <v-tooltip
                              :text="item.Enable_Roster_Leave"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div
                                  class="text-subtitle-1 font-weight-regular text-truncate"
                                  :style="
                                    !isMobileView
                                      ? 'max-width: 300px; '
                                      : 'max-width: 200px; '
                                  "
                                  v-bind="
                                    item.Enable_Roster_Leave.length > 50
                                      ? props
                                      : ''
                                  "
                                >
                                  {{ checkNullValue(item.Enable_Roster_Leave) }}
                                </div>
                              </template>
                            </v-tooltip>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Actions
                          </div>

                          <div>
                            <section
                              class="d-flex justify-center align-center"
                              style="width: 100%"
                            >
                              <ActionMenu
                                v-if="itemActions(item).length > 0"
                                @selected-action="onActions($event, item)"
                                :accessRights="checkAccess()"
                                :isPresentTooltip="true"
                                :actions="itemActions(item)"
                                iconColor="grey"
                              ></ActionMenu>
                              <div v-else>
                                <p>-</p>
                              </div>
                            </section>
                          </div>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewShiftRotation
                    :selectedItem="selectedItem"
                    :formAccess="formAccess"
                    :access-rights="formAccess"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  />
                </v-col>

                <v-col
                  :cols="originalList.length === 0 ? 12 : 7"
                  v-if="showAddEditForm && windowWidth >= 1264"
                >
                  <AddEditShiftRotation
                    :editFormData="selectedItem"
                    :formAccess="formAccess"
                    @close-form="closeAllForms()"
                    @added-record="
                      refetchList('Shift Rotation updated successfully')
                    "
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditShiftRotation
        v-if="showAddEditForm"
        :editFormData="selectedItem"
        :formAccess="formAccess"
        @close-form="closeAllForms()"
        @added-record="refetchList('Shift Rotation updated successfully')"
      />
      <ViewShiftRotation
        v-if="showViewForm"
        :selectedItem="selectedItem"
        :formAccess="formAccess"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      />
    </v-dialog>
    <AppWarningModal
      v-if="deleteModel"
      :open-modal="deleteModel"
      confirmation-heading="Are you sure to delete the selected record(s)?"
      icon-name="far fa-times-circle"
      icon-Size="75"
      @close-warning-modal="deleteModel = false"
      @accept-modal="deleteShiftRotation()"
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewShiftRotation = defineAsyncComponent(() =>
  import("./ViewShiftRotation.vue")
);
const AddEditShiftRotation = defineAsyncComponent(() =>
  import("./AddEditShiftRotation.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import ActionMenu from "@/components/custom-components/ActionMenu.vue";

import { checkNullValue, convertUTCToLocal } from "@/helper.js";
// Queries
import {
  LIST_SHIFT_ROTATION,
  DELETE_SHIFT_ROTATION,
} from "@/graphql/roster-management/ShiftRotationQueries.js";
import ShiftRotationFilter from "./ShiftRotationFilter.vue";
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "ShiftRotation",
  components: {
    EmployeeDefaultFilterMenu,
    AddEditShiftRotation,
    ViewShiftRotation,
    NotesCard,
    ActionMenu,
    ShiftRotationFilter,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // list
    listLoading: false,
    itemList: [],
    originalList: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    // add/update
    isLoading: false,
    showAddEditForm: false,
    // view
    selectedItem: null,
    showViewForm: false,
    // tab
    currentTabItem: "",
    isFilterApplied: false,
    openMoreMenu: false,
    havingAccess: {},
    deleteModel: false,
  }),
  computed: {
    landedFormName() {
      let formName = this.$store.getters.formAccessRights("shift-rotation");
      return formName?.formName;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    accessIdRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessIdRights("293");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    rosterManagementFormAccess() {
      return this.$store.getters.rosterManagementFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.rosterManagementFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      if (this.isSmallTable) {
        return [
          {
            title: "Scheduler Name",
            align: "start",
            key: "Scheduler_Name",
          },
          {
            title: "Repeat Schedule",
            key: "Repeat_Schedule",
          },
        ];
      } else {
        return [
          {
            title: "Scheduler Name",
            align: "start",
            key: "Scheduler_Name",
          },
          {
            title: "Repeat Schedule",
            key: "Repeat_Schedule",
          },
          {
            title: "Enable Roster Leave",
            key: "Enable_Roster_Leave",
          },
          {
            title: "Actions",
            key: "action",
            align: "center",
            sortable: false,
          },
        ];
      }
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText =
          "There are no Shift Rotation for the selected filters/searches.";
      }
      return msgText;
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  methods: {
    checkNullValue,
    convertUTCToLocal,
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        this.isLoading = true;
        const { formAccess } = this.rosterManagementFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/roster-management/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/roster-management/" + clickedForm.url;
        }
      }
    },

    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },

    resetFilter() {
      this.isFilterApplied = false;
      this.$refs.formFilterRef
        ? this.$refs.formFilterRef.resetAllModelValues()
        : "";
      this.itemList = this.originalList;
      let filterObj = {
        selectedName: null,
        isRepeatSchedule: null,
        isEnableRoster: null,
      };
      this.applyFilter(filterObj);
    },

    applyFilter(filter) {
      let filteredList = this.originalList;
      if (filter.selectedName && filter.selectedName != "") {
        filteredList = filteredList.filter((item) => {
          return filter.selectedName == item.Scheduler_Name;
        });
      }
      if (filter.isRepeatSchedule && filter.isRepeatSchedule != "") {
        filteredList = filteredList.filter((item) => {
          return filter.isRepeatSchedule == item.Repeat_Schedule;
        });
      }
      if (filter.isEnableRoster && filter.isEnableRoster != "") {
        filteredList = filteredList.filter((item) => {
          return filter.isEnableRoster == item.Enable_Roster_Leave;
        });
      }
      this.isFilterApplied = true;
      this.filteredList = filteredList;
      this.itemList = filteredList;
    },

    checkAccess() {
      this.havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? 1 : 0;
      return this.havingAccess;
    },
    onActions(type, item) {
      this.selectedItem = item;
      if (type && type.toLowerCase() === "delete") {
        this.deleteModel = true;
      }
    },
    itemActions() {
      if (this.formAccess && this.formAccess.delete) {
        return ["Delete"];
      } else return [];
    },
    onMoreAction(actionType) {
      if (actionType.key === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.itemList));
      exportData = exportData.map((el) => ({
        ...el,
        Added_On: el.Added_On ? this.convertUTCToLocal(el.Added_On) : "",
        Updated_On: el.Updated_On ? this.convertUTCToLocal(el.Updated_On) : "",
      }));
      let exportOptions = {
        fileExportData: exportData,
        fileName: "Shift Rotation",
        sheetName: "Shift Rotation",
        header: [
          {
            header: "Scheduler Name",
            key: "Scheduler_Name",
          },
          {
            header: "Repeat Schedule",
            key: "Repeat_Schedule",
          },
          {
            header: "Repeat Count",
            key: "Repeat_Count",
          },
          {
            header: "Enable Roster Leave",
            key: "Enable_Roster_Leave",
          },
          {
            header: "Leave Entitlement Per Roster Day",
            key: "Leave_Entitlement_Per_Roster_Day",
          },
          {
            header: "Leave Replenishment Period",
            key: "Leave_Replenishment_Period",
          },
          {
            header: "Leave Type",
            key: "Leave_Name",
          },
          {
            header: "Added On",
            key: "Added_On",
          },
          {
            header: "Added By",
            key: "Added_By_Name",
          },
          {
            header: "Updated On",
            key: "Updated_On",
          },
          {
            header: "Updated By",
            key: "Updated_By_Name",
          },
        ],
      };
      this.exportExcelFile(exportOptions);
    },

    openEditForm() {
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },

    addShiftRotation() {
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.deleteModel = false;
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_SHIFT_ROTATION,
          client: "apolloClientL",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listShiftRotation &&
            response.data.listShiftRotation.shiftRotation &&
            !response.data.listShiftRotation.errorCode
          ) {
            let tempData = JSON.parse(
              response.data.listShiftRotation.shiftRotation
            );
            vm.itemList = tempData;
            vm.originalList = tempData;
            vm.listLoading = false;
            vm.onApplySearch();
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "Shift Rotation configuration",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },
    deleteShiftRotation() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_SHIFT_ROTATION,
          client: "apolloClientM",
          variables: {
            Rotation_Id: parseInt(this.selectedItem?.Rotation_Id),
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.deleteShiftRotation &&
            !response.data.deleteShiftRotation.error
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Shift Rotation record deleted successfully!",
            };
            vm.showAlert(snackbarData);
            this.selectedItem = null;
            vm.refetchList();
            vm.isLoading = false;
          } else {
            vm.handleDeleteErrors();
          }
        })
        .catch((error) => {
          vm.handleDeleteErrors(error);
        });
    },
    handleDeleteErrors(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "delete",
        form: "Shift Rotation",
        isListError: false,
      });
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.shift -rotation-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .shift -rotation-container {
    padding: 10em 1em 0em 1em;
  }
}
</style>
