<template>
  <div>
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
      </AppTopBarTab>
    </div>
    <v-container fluid class="roster-settings-container">
      <v-window v-model="currentTabItem" v-if="rosterFormAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="isLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="showAppFetchScreen"
            image-name="common/human-error-image"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            button-text="Retry"
            @button-click="fetchRosterManagmentDetails()"
          >
          </AppFetchErrorScreen>
          <section v-else :class="isMobileView ? 'mt-8 mx-3' : 'mt-8 mx-7'">
            <v-row justify="center" class="py-10">
              <v-col cols="12" lg="12" class="bg-white rounded-lg">
                <v-row
                  :class="[
                    isMobileView
                      ? 'd-flex justify-center align-center py-4 px-2'
                      : 'd-flex align-center py-6 px-5',
                  ]"
                >
                  <v-col
                    cols="12"
                    md="6"
                    :class="
                      isMobileView
                        ? 'flex-column justify-space-between'
                        : 'd-flex justify-space-between'
                    "
                  >
                    <v-text class="text-subtitle-1 text-grey-darken-1"
                      >Enable dynamic week-off for roster employees</v-text
                    >
                    <AppToggleButton
                      button-active-text="Yes"
                      button-inactive-text="No"
                      button-active-color="rgb(var(--v-theme-primary))"
                      button-inactive-color="rgb(var(--v-theme-primary))"
                      id-value="gab-analysis-based-on"
                      :current-value="status === 'Yes' ? true : false"
                      @chosen-value="onChangeStatus($event)"
                      :class="isMobileView ? 'mt-5' : ''"
                      :isDisableToggle="disableToggleButton"
                    ></AppToggleButton>
                  </v-col>
                </v-row>
                <v-row
                  :class="[
                    isMobileView
                      ? 'd-flex justify-center align-center py-4 px-2'
                      : 'd-flex align-center py-6 px-5',
                  ]"
                >
                  <v-col
                    cols="12"
                    md="6"
                    :class="
                      isMobileView
                        ? 'flex-column justify-space-between'
                        : 'd-flex justify-space-between'
                    "
                  >
                    <v-text class="text-subtitle-1 text-grey-darken-1"
                      >Enable monthly limit for shift swap requests</v-text
                    >
                    <AppToggleButton
                      button-active-text="Yes"
                      button-inactive-text="No"
                      button-active-color="rgb(var(--v-theme-primary))"
                      button-inactive-color="rgb(var(--v-theme-primary))"
                      id-value="gab-analysis-based-on"
                      :current-value="swapStatus === 'Yes' ? true : false"
                      @chosen-value="onChangeSwapStatus($event)"
                      :class="isMobileView ? 'mt-5' : ''"
                      :isDisableToggle="disableToggleButton"
                    ></AppToggleButton>
                  </v-col>
                </v-row>
                <v-row
                  v-if="swapStatus === 'Yes'"
                  :class="[
                    isMobileView
                      ? 'd-flex justify-center align-center py-4 px-2'
                      : 'd-flex align-center py-6 px-5',
                  ]"
                >
                  <v-col
                    cols="12"
                    md="6"
                    :class="
                      isMobileView
                        ? 'flex-column justify-space-between'
                        : 'd-flex justify-space-between'
                    "
                  >
                    <v-text class="text-subtitle-1 text-grey-darken-1"
                      >Number of shift swap requests per month</v-text
                    >
                    <v-text-field
                      v-model="shiftSwapDays"
                      type="number"
                      :min="1"
                      :rules="[
                        minMaxNumberValidation(
                          'No. of shift swap requests per month',
                          shiftSwapDays,
                          1
                        ),
                      ]"
                      max-width="160px"
                      density="comfortable"
                      variant="solo"
                      @update:model-value="isFormDirty = true"
                    ></v-text-field>
                  </v-col>
                </v-row>
                <v-row
                  :class="[
                    isMobileView
                      ? 'd-flex justify-center align-center py-4 px-2'
                      : 'd-flex align-center py-6 px-5',
                  ]"
                >
                  <v-col
                    cols="12"
                    md="6"
                    :class="
                      isMobileView
                        ? 'flex-column justify-space-between'
                        : 'd-flex justify-space-between'
                    "
                  >
                    <v-text class="text-subtitle-1 text-grey-darken-1"
                      >Enable shift swap for past dates</v-text
                    >
                    <AppToggleButton
                      button-active-text="Yes"
                      button-inactive-text="No"
                      button-active-color="rgb(var(--v-theme-primary))"
                      button-inactive-color="rgb(var(--v-theme-primary))"
                      id-value="gab-analysis-based-on"
                      :current-value="allowPastDates === 'Yes' ? true : false"
                      @chosen-value="onChangeAllowedDates($event)"
                      :class="isMobileView ? 'mt-5' : ''"
                      :isDisableToggle="disableToggleButton"
                    ></AppToggleButton>
                  </v-col>
                </v-row>
                <v-row
                  v-if="allowPastDates === 'Yes'"
                  :class="[
                    isMobileView
                      ? 'd-flex justify-center align-center py-4 px-2'
                      : 'd-flex align-center py-6 px-5',
                  ]"
                >
                  <v-col
                    cols="12"
                    md="6"
                    :class="
                      isMobileView
                        ? 'flex-column justify-space-between'
                        : 'd-flex justify-space-between'
                    "
                  >
                    <v-text class="text-subtitle-1 text-grey-darken-1"
                      >Number of days allowed for past dates</v-text
                    >
                    <v-text-field
                      v-model="pastDays"
                      type="number"
                      :min="1"
                      :max="40"
                      :rules="[
                        minMaxNumberValidation(
                          'No. of shift swap requests per month',
                          pastDays,
                          1,
                          40
                        ),
                      ]"
                      max-width="160px"
                      density="comfortable"
                      variant="solo"
                      @update:model-value="pastDaysChanged = true"
                    ></v-text-field>
                  </v-col>
                </v-row>
                <v-col cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  ></MoreDetails>
                </v-col>
              </v-col>
            </v-row>
          </section>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
      <bottom-sheet
        v-if="changes > 0 && !isLoading"
        :open-alert="true"
        :changes-count="changes"
        @cancel="cancelChanges()"
        @save="addUpdateDynamicWeekOff()"
      ></bottom-sheet>
    </v-container>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>
<script>
import {
  LIST_DYNAMIC_WEEKOFF_SETTINGS,
  UPDATE_DYNAMIC_WEEKOFF_SETTINGS,
} from "@/graphql/settings/core-hr/rosterManagmentSettingQueries";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { getErrorCodes } from "@/helper.js";
import BottomSheet from "@/components/custom-components/BottomSheet.vue";
import moment from "moment";
import validationRules from "@/mixins/validationRules";
export default {
  name: "DynamicWeekOff",
  mixins: [validationRules],
  data: () => ({
    currentTabItem: "tab-4",
    status: "",
    backupStatus: "",
    swapStatus: "",
    swapbackupStatus: "",
    shiftSwapDays: null,
    backupShiftSwapDays: null,
    allowPastDates: "",
    backupAllowPastDates: "",
    pastDays: null,
    backupPastDays: null,
    moreDetailsList: [],
    openMoreDetails: true,
    rosterManagmentSetting: {},
    isLoading: false,
    validationMessages: [],
    showValidationAlert: false,
    showAppFetchScreen: false,
    errorContent:
      "Sorry, you don't have access rights to view the roster management settings. Please contact HR administrator.",
    isFormDirty: false,
    pastDaysChanged: false,
  }),
  components: {
    MoreDetails,
    BottomSheet,
  },
  computed: {
    landedFormName() {
      return "Roster";
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    disableToggleButton() {
      if (this.rosterFormAccess.update === 0) {
        return true;
      } else {
        return false;
      }
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    coreHRFormAccess() {
      return this.$store.getters.coreHrSettingsFormAccess;
    },
    rosterFormAccess() {
      let accessFormName = this.landedFormName.replace(/\s/g, "-");
      accessFormName = accessFormName.toLowerCase();
      let formAccess = this.accessRights(accessFormName);
      if (formAccess && formAccess.accessRights["view"]) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.coreHRFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    changes() {
      let changes = 0;
      if (this.status !== this.backupStatus) {
        changes += 1;
      }
      if (this.swapStatus !== this.swapbackupStatus) {
        changes += 1;
      }
      if (this.allowPastDates !== this.backupAllowPastDates) {
        changes += 1;
      }
      if (this.pastDaysChanged) {
        changes += 1;
      }
      if (this.isFormDirty) {
        changes += 1;
      }
      return changes;
    },
  },
  watch: {
    shiftSwapDays(val) {
      if (val == this.backupShiftSwapDays) {
        this.isFormDirty = false;
      }
    },
    pastDays(val) {
      if (val == this.backupPastDays) {
        this.pastDaysChanged = false;
      }
    },
  },
  methods: {
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.coreHRFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/core-hr/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/core-hr/" + clickedForm.url;
        }
      }
    },

    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const updatedByName =
        this.rosterManagmentSetting.rosterManagmentSetting.Updated_By;
      const updatedOn = this.formatDate(
        new Date(
          this.rosterManagmentSetting.rosterManagmentSetting.Updated_On +
            ".000Z"
        )
      );
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    fetchRosterManagmentDetails() {
      let vm = this;
      vm.isLoading = true;
      vm.isFormDirty = false;
      vm.showAppFetchScreen = false;
      vm.$apollo
        .query({
          query: LIST_DYNAMIC_WEEKOFF_SETTINGS,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveRosterManagmentSetting.rosterManagmentSetting
          ) {
            const { rosterManagmentSetting } =
              response.data.retrieveRosterManagmentSetting;
            vm.rosterManagmentSetting =
              response.data.retrieveRosterManagmentSetting;
            vm.status =
              response.data.retrieveRosterManagmentSetting
                .rosterManagmentSetting.Dynamic_Week_Off === 1
                ? "Yes"
                : "No";
            vm.backupStatus = JSON.parse(JSON.stringify(vm.status));
            vm.swapStatus =
              response.data.retrieveRosterManagmentSetting.rosterManagmentSetting.Enable_Shift_Swap_Restriction;
            vm.swapbackupStatus = JSON.parse(JSON.stringify(vm.swapStatus));
            vm.shiftSwapDays =
              response.data.retrieveRosterManagmentSetting.rosterManagmentSetting.Max_Swap_Requests_Per_Month;
            vm.backupShiftSwapDays = JSON.parse(
              JSON.stringify(vm.shiftSwapDays)
            );
            vm.allowPastDates = JSON.parse(
              JSON.stringify(rosterManagmentSetting.Allow_Past_Shift_Swaps)
            );
            vm.backupAllowPastDates = JSON.parse(
              JSON.stringify(vm.allowPastDates)
            );
            vm.pastDays = JSON.parse(
              JSON.stringify(rosterManagmentSetting.Max_Shift_Swap_Days)
            );
            vm.backupPastDays = JSON.parse(JSON.stringify(vm.pastDays));
            vm.prefillMoreDetails();
            vm.isLoading = false;
          } else {
            vm.handleFormRetrieveError();
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.handleFormRetrieveError(err);
          vm.isLoading = false;
        });
    },

    addUpdateDynamicWeekOff() {
      this.showAppFetchScreen = false;
      let dynamicWeekOffIntValue = this.status === "Yes" ? 1 : 0;
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_DYNAMIC_WEEKOFF_SETTINGS,
            variables: {
              dynamicWeekOff: dynamicWeekOffIntValue,
              swapApprovalRestriction: this.swapStatus,
              maxSwapRequestsPerMonth:
                this.swapStatus === "Yes" ? parseInt(this.shiftSwapDays) : null,
              allowPastShiftSwaps: this.allowPastDates,
              maxShiftSwapDays:
                this.allowPastDates === "Yes" ? parseInt(this.pastDays) : null,
            },

            client: "apolloClientJ",
          })
          .then(() => {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Roster management settings updated successfully.",
            };
            this.backupShiftSwapDays = this.shiftSwapDays;
            this.backupStatus = this.status;
            this.swapbackupStatus = this.swapStatus;
            vm.fetchRosterManagmentDetails();
            vm.showAlert(snackbarData);
            // vm.isLoading = false;
          })
          .catch((addEditError) => {
            vm.fetchRosterManagmentDetails();
            vm.handleFormAddUpdateError(addEditError);
          });
      } catch (addEditError) {
        vm.fetchRosterManagmentDetails();
        vm.handleFormAddUpdateError(addEditError);
      }
    },

    handleFormRetrieveError(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        var errorCode = getErrorCodes(err);

        if (errorCode) {
          this.showAppFetchScreen = false;
          switch (errorCode) {
            case "_DB0100":
              this.showAppFetchScreen = true;
              break;
            case "SET0003":
              // technical errors
              snackbarData.message =
                "Oops! Something went wrong while retrieving the roster management settings. Please contact the platform administrator.";
              break;
            case "SET0104": // technical errors
              snackbarData.message =
                "Sorry, an error occurred while retrieving the roster management settings. Please contact the platform administrator.";
              break;

            default:
              snackbarData.message =
                "Something went wrong while retrieving the roster management settings. Please try after some time.";
              break;
          }
        } else {
          snackbarData.message =
            "Something went wrong while retrieving the roster management settings. Please try after some time.";
        }
      } else {
        snackbarData.message =
          "Something went wrong while retrieving the roster management settings. Please try after some time.";
      }
      this.isLoading = false;
      this.showAlert(snackbarData);
    },
    handleFormAddUpdateError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: "roster management settings",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
      // this.isLoading = false;
    },

    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },

    // show success/error message in snackbar
    showAlert(snackbarData) {
      if (!this.showAppFetchScreen) {
        this.$store.commit("OPEN_SNACKBAR", snackbarData);
      }
    },
    onChangeStatus(value) {
      if (
        (this.status === "Yes" && value[1] === true) ||
        (this.status === "No" && value[1] === false)
      ) {
        return;
      }
      this.status = value[1] ? "Yes" : "No";
    },

    onChangeSwapStatus(value) {
      if (
        (this.swapStatus === "Yes" && value[1] === true) ||
        (this.swapStatus === "No" && value[1] === false)
      ) {
        return;
      }
      this.swapStatus = value[1] ? "Yes" : "No";
      if (this.swapStatus === "No") {
        this.shiftSwapDays = null;
      }
    },

    onChangeAllowedDates(value) {
      if (
        (this.allowPastDates === "Yes" && value[1] === true) ||
        (this.allowPastDates === "No" && value[1] === false)
      ) {
        return;
      }
      this.allowPastDates = value[1] ? "Yes" : "No";
      if (this.allowPastDates === "No") {
        this.pastDays = null;
      }
    },
    cancelChanges() {
      this.status = JSON.parse(JSON.stringify(this.backupStatus));
      this.swapStatus = JSON.parse(JSON.stringify(this.swapbackupStatus));
      this.shiftSwapDays = JSON.parse(JSON.stringify(this.backupShiftSwapDays));
      this.allowPastDates = JSON.parse(
        JSON.stringify(this.backupAllowPastDates)
      );
      this.pastDays = JSON.parse(JSON.stringify(this.backupPastDays));
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    if (this.rosterFormAccess.view) {
      this.fetchRosterManagmentDetails();
    }
  },
};
</script>
<style scoped>
.roster-settings-container {
  padding: 4em 2em 0em 3em;
}

.white {
  background-color: #ffffff !important;
  border-color: #ffffff !important;
}

.rounded-lg {
  border-radius: 1.2em !important;
}

@media screen and (max-width: 805px) {
  .roster-settings-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
