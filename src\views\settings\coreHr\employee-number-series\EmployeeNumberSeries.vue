<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <EmployeeDefaultFilterMenu :isFilter="false">
          </EmployeeDefaultFilterMenu>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <div v-else>
            <v-card
              min-height="100"
              width="100%"
              :class="isMobileView ? '' : ''"
              class="pa-3 pb-0"
            >
              <div
                class="d-flex align-center"
                :class="isMobileView ? 'mb-3' : 'mb-2'"
              >
                <v-progress-circular
                  model-value="100"
                  color="green"
                  :size="22"
                  class="mr-1"
                ></v-progress-circular>
                <p class="text-h6 text-grey-darken-1 font-weight-bold pl-2">
                  Employee Number Series
                </p>
              </div>
              <div class="ml-8">
                <p class="text-caption">
                  Enable or disable employee ID customization
                </p>
                <p class="text-caption">
                  <v-progress-circular
                    model-value="100"
                    color="green"
                    :size="5"
                    class="mr-1"
                  ></v-progress-circular>
                  Choose config level: Organization-wide or Service
                  Provider-specific
                </p>
                <p class="text-caption">
                  <v-progress-circular
                    model-value="100"
                    color="green"
                    :size="5"
                    class="mr-1"
                  ></v-progress-circular>
                  Add, update, or deactivate prefix settings
                </p>
                <p class="text-caption">
                  <v-progress-circular
                    model-value="100"
                    color="green"
                    :size="5"
                    class="mr-1"
                  ></v-progress-circular>
                  Customize prefix, suffix, digit length, and next number
                </p>
              </div>
              <div :class="isMobileView ? '' : 'd-flex'">
                <div class="mt-5 ml-8 mr-5">
                  <span class="text-subtitle-1 text-grey-darken-1">
                    Enable Customization
                  </span>
                  <div
                    class="pa-2 d-flex align-center"
                    style="max-width: 500px"
                  >
                    <v-tooltip
                      :text="'You don\'t have access to perform this action'"
                    >
                      <template v-slot:activator="{ props }">
                        <v-switch
                          v-model="globalSetting"
                          rounded="lg"
                          mandatory
                          elevation="2"
                          color="primary"
                          hide-details
                          v-bind="formAccess && formAccess.update ? '' : props"
                          :disabled="
                            formAccess && formAccess.update ? false : true
                          "
                        >
                        </v-switch>
                      </template>
                    </v-tooltip>
                  </div>
                </div>
                <div class="mt-5 ml-8" v-if="globalSetting && fieldforce">
                  <span class="text-subtitle-1 text-grey-darken-1">
                    Customization Type
                  </span>
                  <div
                    class="pa-2 d-flex align-center"
                    style="max-width: 500px"
                  >
                    <v-tooltip
                      :text="'You don\'t have access to perform this action'"
                    >
                      <template v-slot:activator="{ props }">
                        <v-btn-toggle
                          v-model="selectedCoverageType"
                          rounded="lg"
                          mandatory
                          elevation="2"
                          v-bind="formAccess && formAccess.update ? '' : props"
                          :disabled="
                            formAccess && formAccess.update ? false : true
                          "
                        >
                          <v-btn
                            color="primary"
                            style="background-color: white; color: black"
                            >Organization</v-btn
                          >
                          <v-btn
                            color="primary"
                            style="background-color: white; color: black"
                            >Service Provider</v-btn
                          ></v-btn-toggle
                        >
                      </template>
                    </v-tooltip>
                  </div>
                </div>
              </div>
            </v-card>
            <AppFetchErrorScreen
              v-if="itemList.length === 0"
              key="no-results-screen"
              :main-title="emptyScenarioMsg"
              :isSmallImage="originalList.length === 0"
              :image-name="originalList.length === 0 ? '' : 'common/no-records'"
            >
              <template #contentSlot>
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      :notes="'Create employee number series for your organization.'"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      :notes="'You can add, edit, and delete employee number series.'"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="
                        originalList.length === 0 &&
                        formAccess.add &&
                        globalSettingBackup
                      "
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      {{ $t("settings.addNew") }}
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="transparent"
                      class="mt-1"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="$emit('reset-filter')"
                    >
                      {{ $t("settings.resetFilterSearch") }}
                    </v-btn>
                  </v-col>
                </v-row>
              </template>
            </AppFetchErrorScreen>
            <div v-else>
              <div
                class="d-flex align-center flex-wrap align-center"
                :class="
                  isMobileView
                    ? 'my-6 justify-center flex-column'
                    : 'my-4 justify-end'
                "
              >
                <v-btn
                  v-if="formAccess.add && globalSettingBackup"
                  prepend-icon="fas fa-plus"
                  variant="elevated"
                  color="primary rounded-lg"
                  class="mx-1"
                  @click="openAddForm"
                >
                  {{ $t("settings.addNew") }}
                </v-btn>
                <v-btn
                  rounded="lg"
                  color="transparent"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>

                <v-menu
                  class="mb-1"
                  v-model="openMoreMenu"
                  transition="scale-transition"
                >
                  <template v-slot:activator="{ props }">
                    <v-btn variant="plain" class="ml-n3 mr-n5" v-bind="props">
                      <v-icon>fas fa-ellipsis-v</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              'bg-hover': isHovering,
                            }"
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
              <v-data-table
                :headers="headers"
                :items="itemList"
                :items-per-page="50"
                :height="
                  $store.getters.getTableHeightBasedOnScreenSize(250, itemList)
                "
                :items-per-page-options="[
                  { value: 50, title: '50' },
                  { value: 100, title: '100' },
                  { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
                ]"
                fixed-header
                item-value="empPrefixSettingId"
              >
                <template v-slot:item="{ item }">
                  <tr
                    class="data-table-tr bg-white cursor-pointer"
                    :class="
                      isMobileView ? ' v-data-table__mobile-table-row' : ''
                    "
                    @click="openViewForm(item)"
                  >
                    <td
                      v-if="fieldforce"
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="text-subtitle-1 text-grey-darken-1 mt-2"
                      >
                        {{ labelList[115]?.Field_Alias || "Service Provider" }}
                      </div>
                      <section class="d-flex align-center">
                        <div
                          class="text-body-2 font-weight-medium text-primary"
                        >
                          {{ checkNullValue(item.serviceProviderName) }}
                        </div>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="text-subtitle-1 text-grey-darken-1 mt-2"
                      >
                        Prefix
                      </div>
                      <section class="d-flex align-center">
                        <div
                          class="text-body-2 font-weight-medium"
                          :class="fieldforce ? '' : 'text-primary'"
                        >
                          {{ checkNullValue(item.prefix) }}
                        </div>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="text-subtitle-1 text-grey-darken-1 mt-2"
                      >
                        Suffix
                      </div>
                      <section class="d-flex align-center">
                        <div class="text-body-2 font-weight-medium">
                          {{ checkNullValue(item.suffix) }}
                        </div>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="text-subtitle-1 text-grey-darken-1 mt-2"
                      >
                        Number of Digits
                      </div>
                      <section class="d-flex align-center">
                        <div class="text-body-2 font-weight-medium">
                          {{ checkNullValue(item.noOfDigits) }}
                        </div>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="text-subtitle-1 text-grey-darken-1 mt-2"
                      >
                        Next Number
                      </div>
                      <section class="d-flex align-center">
                        <div class="text-body-2 font-weight-medium">
                          {{ checkNullValue(item.nextNumber) }}
                        </div>
                      </section>
                    </td>
                    <td
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="text-subtitle-1 text-grey-darken-1 mt-2"
                      >
                        Status
                      </div>
                      <div
                        @click.stop="
                          {
                          }
                        "
                        style="max-width: 200px"
                      >
                        <AppToggleButton
                          :key="item.empPrefixSettingId"
                          :button-active-text="$t('settings.active')"
                          :button-inactive-text="$t('settings.inactive')"
                          button-active-color="#7de272"
                          button-inactive-color="red"
                          id-value="gab-analysis-based-on"
                          :current-value="
                            item.status === 'Active' ? true : false
                          "
                          :isDisableToggle="!formAccess.update"
                          :tooltipContent="
                            formAccess.update
                              ? ''
                              : `Sorry, you don't have access rights to update the status`
                          "
                          @chosen-value="updateStatus($event, item)"
                        ></AppToggleButton>
                      </div>
                    </td>
                    <td
                      :class="
                        isMobileView ? 'd-flex justify-space-between' : ''
                      "
                      :style="
                        isMobileView ? `width: ${windowWidth - 40}px` : ''
                      "
                    >
                      <div
                        v-if="isMobileView"
                        class="text-subtitle-1 text-grey-darken-1 mt-2"
                      >
                        {{ $t("settings.actions") }}
                      </div>
                      <div class="d-flex align-center justify-end">
                        <ActionMenu
                          @selected-action="onActions($event, item)"
                          :actions="['Edit']"
                          :access-rights="{ update: formAccess.update }"
                          iconColor="grey"
                        ></ActionMenu>
                      </div>
                    </td>
                  </tr>
                </template>
              </v-data-table>
            </div>
          </div>
          <v-bottom-navigation v-if="openBottomSheet">
            <v-sheet
              class="align-center text-center"
              :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
              style="width: 100%"
            >
              <v-row justify="center">
                <v-col cols="6" class="d-flex justify-start pl-2">
                  <v-btn
                    rounded="lg"
                    variant="text"
                    elevation="4"
                    size="small"
                    color="primary"
                    class="mb-2"
                    style="height: 40px; margin-top: 10px"
                    @click="closeEdit()"
                  >
                    <span class="primary">{{ $t("settings.cancel") }}</span>
                  </v-btn>
                </v-col>
                <v-col cols="6" class="d-flex justify-end pr-4">
                  <v-btn
                    rounded="lg"
                    variant="elevated"
                    size="small"
                    color="primary"
                    class="mb-2"
                    style="height: 40px; margin-top: 10px; margin-right: 10px"
                    @click="addUpdateGlobalSetting()"
                  >
                    <span class="primary">{{ $t("settings.save") }}</span>
                  </v-btn>
                </v-col>
              </v-row>
            </v-sheet>
          </v-bottom-navigation>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <AddUpdate
    v-if="showAddEditForm"
    :overlay-model="showAddEditForm"
    :isEdit="isEdit"
    :selectedItem="selectedItem"
    :coverage="selectedCoverageType"
    @close-form="closeAllForms()"
    @edit-updated="refetchList()"
  />
  <ViewForm
    v-if="showViewForm"
    :overlayModel="showViewForm"
    :selectedItem="selectedItem"
    @close-view="closeAllForms()"
    @on-edit="openEditForm(selectedItem)"
  ></ViewForm>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    iconName=""
    confirmation-heading="Are you sure to update this record?"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="addUpdateListRecord()"
  ></AppWarningModal>
</template>
<script>
import moment from "moment";
import EmployeeDefaultFilterMenu from "@/components/custom-components/EmployeeDefaultFilterMenu.vue";
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import AddUpdate from "./AddUpdate.vue";
import ViewForm from "./ViewForm.vue";
import {
  RETRIEVE_EMPLOYEE_NUMBER_SERIES,
  UPDATE_GLOBAL_SETTING,
  ADD_UPDATE_EMPLOYEE_NUMBER_SERIES,
} from "@/graphql/settings/core-hr/employeeNumberSeries.js";
import { checkNullValue, convertUTCToLocal } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "EmployeeNumberSeries",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    AddUpdate,
    ViewForm,
    ActionMenu,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      currentTabItem: "",
      isLoading: false,
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      originalList: [],
      itemList: [],
      selectedCoverageType: 0,
      globalSetting: 0,
      globalSettingBackup: 0,
      selectedCoverageTypeBackup: 0,
      showAddEditForm: false,
      isEdit: false,
      showViewForm: false,
      selectedItem: null,
      openMoreMenu: false,
      openWarningModal: false,
      status: null,
    };
  },
  computed: {
    landedFormName() {
      return "Employee Number Series";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formAccess() {
      let formAccess = this.accessRights(358);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights.view
      ) {
        return formAccess.accessRights;
      }
      return false;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    coreHRFormAccess() {
      return this.$store.getters.coreHrSettingsFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.coreHRFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText =
          "There are no employee number series for the selected filters/searches.";
      }
      return msgText;
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
          color: "primary",
        },
      ];
      return actions;
    },
    headers() {
      let headers = [
        {
          title: this.$t("settings.prefix"),
          align: "start",
          key: "prefix",
        },
        {
          title: this.$t("settings.suffix"),
          key: "suffix",
        },
        { title: this.$t("settings.noOfDigits"), key: "noOfDigits" },
        { title: this.$t("settings.nextNumber"), key: "nextNumber" },
        { title: this.$t("settings.status"), key: "status", sortable: false },
        {
          title: this.$t("settings.actions"),
          key: "actions",
          sortable: false,
          align: "end",
        },
      ];
      if (this.fieldforce) {
        headers.unshift({
          title:
            this.labelList[115]?.Field_Alias ||
            this.$t("settings.serviceProvider"),
          align: "start",
          key: "serviceProviderName",
        });
      }
      return headers;
    },
    fieldforce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    openBottomSheet() {
      return (
        this.globalSetting !== this.globalSettingBackup ||
        this.selectedCoverageType !== this.selectedCoverageTypeBackup
      );
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat + " HH:mm") : "";
        }
        return "";
      };
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    openAddForm() {
      this.showAddEditForm = true;
      this.isEdit = false;
      this.showViewForm = false;
    },
    openEditForm(item) {
      this.selectedItem = item;
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },
    closeAllForms() {
      console.log("close all forms");
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },
    closeEdit() {
      this.globalSetting = this.globalSettingBackup;
      this.selectedCoverageType = this.selectedCoverageTypeBackup;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.coreHRFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/core-hr/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/core-hr/" + clickedForm.url;
        }
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    onActions(actionType, item) {
      if (actionType === "Edit") {
        this.openEditForm(item);
      }
    },
    exportReportFile() {
      let headers = [
        { header: "Service Provider", key: "serviceProviderName" },
        {
          header: "Prefix",
          key: "prefix",
        },
        {
          header: "Suffix",
          key: "suffix",
        },
        { header: "Number of Digits", key: "noOfDigits" },
        { header: "Next Number", key: "nextNumber" },
        { header: "Status", key: "status" },
        { header: "Added By", key: "addedByName" },
        { header: "Added On", key: "addedOn" },
        { header: "Updated By", key: "updatedByName" },
        { header: "Updated On", key: "updatedOn" },
      ];
      this.itemList = this.itemList.map((item) => ({
        ...item,
        addedOn: item.addedOn
          ? this.formatDate(new Date(item.addedOn + ".000Z"))
          : "",
        updatedOn: item.updatedOn
          ? this.formatDate(new Date(item.updatedOn + ".000Z"))
          : "",
      }));
      this.exportExcelFile({
        fileExportData: this.itemList,
        fileName: "Employee Number Series",
        sheetName: "Employee Number Series",
        header: headers,
      });
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMPLOYEE_NUMBER_SERIES,
          client: "apolloClientI",
          variables: {
            formId: 358,
            serviceProviderId: null,
            status: "",
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listEmployeeIdPrefixSettings &&
            response.data.listEmployeeIdPrefixSettings.employeeIdPrefixSettings
          ) {
            vm.itemList =
              response.data.listEmployeeIdPrefixSettings.employeeIdPrefixSettings;
            vm.originalList =
              response.data.listEmployeeIdPrefixSettings.employeeIdPrefixSettings;
          } else {
            vm.originalList = [];
            vm.itemList = [];
          }
          if (
            response.data &&
            response.data.listEmployeeIdPrefixSettings &&
            response.data.listEmployeeIdPrefixSettings.employeeIdPrefixSettings
          ) {
            vm.globalSetting =
              response.data.listEmployeeIdPrefixSettings.config.isEnabled;
            vm.selectedCoverageType =
              response.data.listEmployeeIdPrefixSettings.config.configLevel ===
              "ServiceProvider"
                ? 1
                : 0;
            vm.globalSettingBackup = vm.globalSetting;
            vm.selectedCoverageTypeBackup = vm.selectedCoverageType;
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "employee number series",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    addUpdateGlobalSetting() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_GLOBAL_SETTING,
          variables: {
            empPrefixConfigId: 1,
            isEnabled: vm.globalSetting,
            configLevel:
              vm.selectedCoverageType === 0
                ? "Organization"
                : "ServiceProvider",
            formId: 358,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          vm.isLoading = false;
          vm.globalSettingBackup = vm.globalSetting;
          vm.selectedCoverageTypeBackup = vm.selectedCoverageType;
          let snackbarData = {
            isOpen: true,
            type: "success",
            message: "Employee ID prefix configuration updated successfully.",
          };
          vm.showAlert(snackbarData);
          vm.refetchList();
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "updating",
            form: "employee number series",
            isListError: false,
          });
        });
    },
    updateStatus(status, item) {
      this.selectedItem = item;
      this.status = status;
      this.openWarningModal = true;
    },
    onCloseWarningModal() {
      this.openWarningModal = false;
      this.status = null;
      this.selectedItem = null;
    },
    addUpdateListRecord() {
      let vm = this;
      vm.isLoading = true;
      vm.openWarningModal = false;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_EMPLOYEE_NUMBER_SERIES,
          variables: {
            empPrefixSettingId: vm.selectedItem.empPrefixSettingId,
            serviceProviderId: vm.selectedItem.serviceProviderId,
            prefix: vm.selectedItem.prefix,
            suffix: vm.selectedItem.suffix,
            noOfDigits: vm.selectedItem.noOfDigits,
            nextNumber: vm.selectedItem.nextNumber,
            status: vm.status[1] ? "Active" : "Inactive",
            formId: 358,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          vm.isLoading = false;
          vm.selectedItem = null;
          vm.status = null;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: "Employee ID prefix setting updated successfully.",
          };
          vm.showAlert(snackbarData);
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "employee number series",
        isListError: false,
      });
    },
  },
};
</script>
<style>
.container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .container {
    padding: 5em 1em 0em 1em;
  }
}
</style>
