<template>
  <div>
    <!-- AppTopBar component is reused here which is already present -->
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="lopRecoveryBackup.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <!-- this the component which has search component and based on prop isFilter filter component is also rendered -->

              <EmployeeDefaultFilterMenu
                v-if="lopRecoveryBackup.length > 0"
                class="justify-end"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <FormFilter
                ref="formFilterRef"
                :items="lopRecoveryBackup"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilter($event)"
              >
                <template #bottom-filter-menu>
                  <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                    <v-autocomplete
                      v-model="selectedStatus"
                      color="primary"
                      :items="['Active', 'InActive']"
                      label="Status"
                      multiple
                      closable-chips
                      chips
                      density="compact"
                      single-line
                      variant="solo"
                    >
                    </v-autocomplete>
                  </v-col>
                </template>
              </FormFilter>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <!-- Tab body starts from here -->

    <v-container fluid class="lop-recovery-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-row
          v-if="showCoverageButton"
          :class="windowWidth < 1264 ? 'mx-auto mt-6' : 'ml-3 mb-1'"
        >
          <v-col
            cols="12"
            class="d-flex"
            :class="isMobileView ? 'flex-column' : ''"
          >
            <div
              class="d-flex mt-8"
              :class="isMobileView ? 'flex-column mx-auto' : ''"
            >
              <div
                class="text-subtitle-1 text-grey-darken-1"
                :class="isMobileView ? 'mx-auto' : 'mr-5 mt-1'"
              >
                LOP Recovery Coverage
              </div>
              <div>
                <v-btn-toggle
                  v-model="selectedCoverageType"
                  rounded="lg"
                  mandatory
                  density="comfortable"
                  :disabled="hasActiveStatus"
                  :class="{
                    'cursor-not-allow': hasActiveStatus,
                    'custom-box-shadow': !hasActiveStatus,
                  }"
                  @update:modelValue="onChangeCoverage(selectedCoverageType)"
                >
                  <v-btn
                    class="text-start text-wrap"
                    color="primary"
                    style="background-color: white; color: black"
                    :size="isMobileView ? 'small' : 'default'"
                    >Organization</v-btn
                  >
                  <v-btn
                    class="text-start text-wrap"
                    color="primary"
                    style="background-color: white; color: black"
                    :size="isMobileView ? 'small' : 'default'"
                    >Custom Group</v-btn
                  ></v-btn-toggle
                >
              </div>
            </div>
          </v-col>
        </v-row>
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading || coverageLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="lopRecoveryData.length == 0 && emptyFilterScreen"
            image-name="common/no-records"
            main-title="There are no LOP recovery configuration for the selected filters/searches."
          >
            <template #contentSlot>
              <div class="d-flex mb-2 flex-wrap justify-center">
                <v-btn
                  color="primary"
                  variant="elevated"
                  class="ml-4 mt-1"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click.stop="resetFilter()"
                >
                  Reset Filter/Search
                </v-btn>
              </div>
            </template>
          </AppFetchErrorScreen>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="lopRecoveryBackup.length === 0 && !showAddEditForm"
            key="no-results-screen"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%" class="mx-auto">
                <v-row
                  v-if="!isLoading"
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                  :class="isMobileView ? 'mt-n16' : ''"
                >
                  <v-col cols="12">
                    <NotesCard
                      heading="LOP Recovery Configuration"
                      imageName=""
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="The configuration of Loss of Pay (LOP) Recovery within HRAPP is crucial for effectively managing Loss of Pay scenarios. This feature allows organizations to establish structured procedures for recouping lost pay, thereby optimizing operational efficiency and guaranteeing precise adjustments in compensation."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="By setting up LOP recovery configurations in HRAPP, companies empower their employees to adjust their compensation in response to various LOP situations. This includes automatically generated LOP due to missed attendance or leave, LOP resulting from late arrivals, and LOP incurred due to attendance shortages."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Furthermore, the LOP Recovery process can be tailored to suit the needs of the entire organization or customized for specific employee groups, offering flexibility and control in managing payroll effectively."
                      backgroundColor="transparent"
                      class="mb-2"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="formAccess.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="onAddLopRecovery()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Configure LOP Recovery
                    </v-btn>
                    <v-btn
                      rounded="lg"
                      color="transparent"
                      variant="flat"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <!-- The div below contains all action buttons -->

              <div
                v-if="!isSmallTable"
                class="d-flex flex-wrap align-center"
                :class="isMobileView ? 'flex-column mt-3 mb-n4' : 'my-3'"
                style="justify-content: space-between"
              >
                <div
                  class="d-flex align-center"
                  :class="isMobileView ? 'justify-center' : ''"
                >
                  <div
                    class="text-grey-darken-1"
                    :class="
                      isMobileView
                        ? 'mx-auto mt-2'
                        : 'text-subtitle-1  mr-5 mt-1'
                    "
                  >
                    LOP Recovery Coverage
                  </div>
                  <div>
                    <v-tooltip v-model="showToolTip" location="right">
                      <template v-slot:activator="{ props }">
                        <v-btn-toggle
                          v-model="selectedCoverageType"
                          rounded="lg"
                          mandatory
                          v-bind="!hasActiveStatus ? '' : props"
                          density="compact"
                          :disabled="hasActiveStatus"
                          :class="{
                            'cursor-not-allow': hasActiveStatus,
                            'custom-box-shadow': !hasActiveStatus,
                          }"
                          @update:modelValue="
                            onChangeCoverage(selectedCoverageType)
                          "
                        >
                          <v-btn
                            class="text-start text-wrap"
                            color="primary"
                            style="background-color: white; color: black"
                            :size="isMobileView ? 'small' : 'default'"
                            >Organization</v-btn
                          >
                          <v-btn
                            class="text-start text-wrap"
                            color="primary"
                            style="background-color: white; color: black"
                            :size="isMobileView ? 'small' : 'default'"
                            >Custom Group</v-btn
                          ></v-btn-toggle
                        >
                      </template>
                      <div
                        v-if="hasActiveStatus"
                        style="
                          width: 150px !important;
                          height: 150px !important;
                        "
                      >
                        There are currently active LOP recovery configurations.
                        To modify the LOP recovery coverage, you must first
                        inactivate these configurations.
                      </div>
                    </v-tooltip>
                  </div>
                </div>
                <div
                  v-if="lopRecoveryBackup.length > 0"
                  class="d-flex align-center my-3"
                  :class="isMobileView ? 'justify-center' : 'justify-end'"
                >
                  <v-tooltip v-model="toolTipForConf" location="right">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        v-bind="
                          hasActiveStatus && coverage == 'Organization'
                            ? props
                            : ''
                        "
                        prepend-icon="fas fa-plus"
                        color="primary rounded-lg"
                        :class="
                          hasActiveStatus && coverage == 'Organization'
                            ? 'cursor-not-allow'
                            : ''
                        "
                        @click="
                          hasActiveStatus && coverage == 'Organization'
                            ? ''
                            : onAddLopRecovery()
                        "
                        :size="isMobileView ? 'small' : 'default'"
                        v-if="formAccess.add"
                      >
                        <template v-slot:prepend>
                          <v-icon></v-icon>
                        </template>
                        Add Configuration
                      </v-btn>
                    </template>
                    <div
                      style="width: 180px !important; height: 150px !important"
                    >
                      At the organization level, there is an active LOP recovery
                      configuration in place. You cannot add a new
                      configuration, but you have the option to edit the
                      existing one.
                    </div>
                  </v-tooltip>
                  <v-btn
                    rounded="lg"
                    color="transparent"
                    variant="flat"
                    class="ml-2 mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                  <v-menu v-model="openMoreMenu" transition="scale-transition">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        variant="plain"
                        class="mt-1 ml-n2 mr-n5"
                        :size="isMobileView ? 'small' : 'default'"
                        v-bind="props"
                      >
                        <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                        <v-icon v-else>fas fa-caret-up</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="action in moreActions"
                        :key="action.key"
                        @click="exportReportFile()"
                      >
                        <v-hover>
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                              ><v-icon size="15" class="pr-2">{{
                                action.icon
                              }}</v-icon
                              >{{ action.key }}</v-list-item-title
                            >
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>
              <v-row>
                <v-col
                  v-if="lopRecoveryData.length > 0"
                  :cols="isSmallTable ? 5 : 12"
                >
                  <v-data-table
                    v-model="selectedData"
                    :headers="headers"
                    :items="lopRecoveryData"
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        lopRecoveryData,
                        true
                      )
                    "
                    class="elevation-1"
                    style="box-shadow: none !important"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        style="z-index: 200"
                        class="data-table-tr bg-white cursor-pointer"
                        @click="openViewForm(item)"
                        :class="[
                          isMobileView
                            ? ' v-data-table__mobile-table-row ma-0 mt-2'
                            : '',
                        ]"
                      >
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                          >
                            Auto LOP
                          </div>
                          <section
                            style="height: 3em"
                            class="d-flex align-center"
                          >
                            <div class="d-flex align-center">
                              <div
                                v-if="
                                  isSmallTable &&
                                  !isMobileView &&
                                  selectedItem &&
                                  selectedItem.Lop_Settings_Id ===
                                    item.Lop_Settings_Id
                                "
                                class="data-table-side-border d-flex"
                                style="height: 3em"
                              ></div>
                            </div>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{ checkNullValue(item.Auto_LOP_Applicable) }}
                            </span>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                          >
                            Attendance Shortage
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{
                                checkNullValue(
                                  item.Attendance_Shortage_Applicable
                                )
                              }}
                            </span>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                          >
                            Late Attendance
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{
                                checkNullValue(item.Late_Attendance_Applicable)
                              }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                          >
                            Workflow Name
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{ checkNullValue(item.Workflow_Name) }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Custom Group
                          </div>
                          <section>
                            <span
                              class="text-subtitle-1 font-weight-regular"
                              :class="
                                item.Group_Name == null && !isMobileView
                                  ? 'ml-10'
                                  : ''
                              "
                            >
                              {{ checkNullValue(item.Group_Name) }}
                            </span>
                          </section>
                        </td>

                        <td
                          v-if="!isSmallTable"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Status
                          </div>

                          <div
                            @click.stop="
                              {
                              }
                            "
                          >
                            <AppToggleButton
                              button-active-text="Active"
                              button-inactive-text="InActive"
                              button-active-color="#7de272"
                              button-inactive-color="red"
                              id-value="gab-analysis-based-on"
                              :current-value="
                                item.Configuration_Status === 'Active'
                                  ? true
                                  : false
                              "
                              :isDisableToggle="!formAccess.update"
                              :tooltipContent="
                                formAccess.update
                                  ? ''
                                  : `Sorry, you don't have access rights to update the status`
                              "
                              @chosen-value="updateStatus($event, item)"
                            ></AppToggleButton>
                          </div>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                  <ViewLopRecovery
                    :selectedItem="selectedItem"
                    :coverage="coverage"
                    :access-rights="formAccess"
                    @close-form="closeAllForms()"
                    @open-edit-form="openEditForm()"
                  />
                </v-col>
                <v-col
                  :cols="lopRecoveryData.length === 0 ? 12 : 7"
                  v-if="showAddEditForm && windowWidth >= 1264 && !listLoading"
                >
                  <AddEditLopRecovery
                    :isEdit="isEdit"
                    :editFormData="selectedItem"
                    :coverage="coverage"
                    :isListEmpty="isListEmpty"
                    @close-form="closeAllForms()"
                    @edit-updated="refetchList()"
                  />
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>

      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="primary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditLopRecovery
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :editFormData="selectedItem"
        :coverage="coverage"
        :isListEmpty="isListEmpty"
        @close-form="closeAllForms()"
        @edit-updated="refetchList()"
      />
      <ViewLopRecovery
        v-if="showViewForm"
        :selectedItem="selectedItem"
        :coverage="coverage"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      />
    </v-dialog>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);

const ViewLopRecovery = defineAsyncComponent(() =>
  import("./ViewLopRecovery.vue")
);
const AddEditLopRecovery = defineAsyncComponent(() =>
  import("./AddEditLopRecovery.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import FormFilter from "./FormFilter.vue";
// Queries
import {
  RETRIEVE_LOP_RECOVERY,
  ADD_UPDATE_LOP_RECOVERY,
} from "@/graphql/settings/core-hr/lopRecoveryConfigurationQueries.js";

import FileExportMixin from "@/mixins/FileExportMixin";
import { convertUTCToLocal, checkNullValue } from "@/helper.js";
export default {
  name: "LopRecovery",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    ViewLopRecovery,
    AddEditLopRecovery,
    FormFilter,
  },
  mixins: [FileExportMixin],
  data: () => ({
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    showToolTip: false,

    isLoading: false,
    isEdit: false,
    showViewForm: false,
    showAddEditForm: false,
    showSelect: false,
    selectedData: [],
    currentTabItem: "tab-6",
    showRetryBtn: true,
    lopRecoveryData: [],
    selectedItem: null,
    selectedCoverageType: 0,
    coverage: "Organization",
    validationMessages: [],
    showValidationAlert: false,
    Coverage_Id: null,
    lopRecoveryCoverageItem: [],
    coverageLoading: false,
    lopRecoveryBackup: [],
    selectedStatus: ["Active"],
    backupFilterData: [],
    openMoreMenu: false,
    emptyFilterScreen: false,
    toolTipForConf: false,
  }),
  computed: {
    landedFormName() {
      return "LOP Recovery";
    },
    moreActions() {
      return [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
    },
    showCoverageButton() {
      return (
        (this.lopRecoveryBackup.length === 0 &&
          this.showAddEditForm &&
          !this.isMobileView &&
          !this.listLoading) ||
        (this.isListEmpty && this.windowWidth < 1264 && !this.listLoading)
      );
    },
    hasActiveStatus() {
      // Check if any entry in lopRecoveryData has an active Status
      return this.lopRecoveryBackup.some(
        (entry) => entry.Configuration_Status === "Active"
      );
    },
    isListEmpty() {
      return this.lopRecoveryBackup.length == 0;
    },
    formAccess() {
      let lopRecoveryAccess = this.accessRights("253");
      if (
        lopRecoveryAccess &&
        lopRecoveryAccess.accessRights &&
        lopRecoveryAccess.accessRights["view"]
      ) {
        return lopRecoveryAccess.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    coreHRFormAccess() {
      return this.$store.getters.coreHrSettingsFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.coreHRFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    headers() {
      if (this.isSmallTable) {
        return [
          {
            title: "Auto LOP",
            align: "start",
            key: "Auto_LOP_Applicable",
          },

          {
            title: "Attendance Shortage",
            key: "Attendance_Shortage_Applicable",
          },
          {
            title: "Late Attendance",
            key: "Late_Attendance_Applicable",
          },
        ];
      } else {
        return [
          {
            title: "Auto LOP",
            align: "start",
            key: "Auto_LOP_Applicable",
          },

          {
            title: "Attendance Shortage",
            key: "Attendance_Shortage_Applicable",
          },
          {
            title: "Late Attendance",
            key: "Late_Attendance_Applicable",
          },
          {
            title: "Workflow Name",
            key: "Workflow_Name",
          },
          {
            title: "Custom Group",
            key: "Group_Name",
          },
          {
            title: "Status",
            key: "Configuration_Status",
          },
        ];
      }
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    if (this.formAccess) {
      this.retrieveCoverage();
      this.refetchList();
    }
  },
  methods: {
    convertUTCToLocal,
    checkNullValue,
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    openEditForm() {
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onAddLopRecovery() {
      this.isEdit = false;
      this.showAddEditForm = true;
      this.showViewForm = false;
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.coreHRFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/core-hr/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/core-hr/" + clickedForm.url;
        }
      }
      this.$store.state.empSearchValue = "";
    },
    resetFilter() {
      this.lopRecoveryData = this.lopRecoveryBackup;
      this.emptyFilterScreen = false;
      if (this.$refs.formFilterRef) {
        this.$refs.formFilterRef.resetAllModelValues();
      }
      this.selectedStatus = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.applyFilter(this.lopRecoveryData);
    },
    applyFilter(filteredArray) {
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.Configuration_Status);
        });
      }
      this.lopRecoveryData = filteredArray;
      this.backupFilterData = filteredArray;
      if (this.lopRecoveryData.length == 0) {
        this.emptyFilterScreen = true;
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onApplySearch(val) {
      if (!val) {
        this.lopRecoveryData = this.backupFilterData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupFilterData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.lopRecoveryData = searchItems;
        if (this.lopRecoveryData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    exportReportFile() {
      let lopRecoveryList = this.lopRecoveryData.map((item) => ({
        Auto_LOP_Applicable: item.Auto_LOP_Applicable,
        Attendance_Shortage_Applicable: item.Attendance_Shortage_Applicable,
        Late_Attendance_Applicable: item.Late_Attendance_Applicable,
        Workflow_Name: item.Workflow_Name,
        Group_Name: item.Group_Name,
        Configuration_Status: item.Configuration_Status,
        Added_By: item.Added_By,
        Added_On: this.convertUTCToLocal(item.Added_On),
        Updated_By: item.Updated_By,
        Updated_On: item.Updated_On
          ? this.convertUTCToLocal(item.Updated_On)
          : "",
      }));
      let fileName = "LOP Recovery Settings";
      let exportHeaders = [
        { header: "Auto LOP", key: "Auto_LOP_Applicable" },
        {
          header: "Attendance Shortage",
          key: "Attendance_Shortage_Applicable",
        },
        {
          header: "Late Attendance",
          key: "Late_Attendance_Applicable",
        },
        {
          header: "Workflow Name",
          key: "Workflow_Name",
        },
        { header: "Custom Group", key: "Group_Name" },
        { header: "Status", key: "Configuration_Status" },
        { header: "Added By", key: "Added_By" },
        { header: "Added On", key: "Added_On" },
        { header: "Update By", key: "Updated_By" },
        { header: "Update On", key: "Updated_On" },
      ];
      let exportOptions = {
        fileExportData: lopRecoveryList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_LOP_RECOVERY,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveLopRecoverySettingsDetails
          ) {
            vm.lopRecoveryData =
              response.data.retrieveLopRecoverySettingsDetails.lopRecoverySettingsDetails;
            vm.lopRecoveryBackup = vm.lopRecoveryData;
            if (vm.lopRecoveryData && vm.lopRecoveryData.length) {
              vm.applyFilter(vm.lopRecoveryData);
            }
            vm.listLoading = false;
          } else {
            vm.handleListError((err = ""), "LOP Recovery");
          }
        })
        .catch((err) => {
          vm.handleListError(err, "LOP Recovery");
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form:
            formName === "coverage"
              ? "LOP recovery coverage"
              : "LOP recovery configuration",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    async retrieveCoverage() {
      let vm = this;
      vm.coverageLoading = true;
      if (this.formAccess) {
        await this.$store
          .dispatch("retrieveFormLevelCoverage", {
            formName: "LOP Recovery",
            Form_Id: 253,
          })
          .then((response) => {
            if (response) {
              vm.lopRecoveryCoverageItem = response;
              vm.coverage = vm.lopRecoveryCoverageItem[0].Coverage;
              vm.Coverage_Id = vm.lopRecoveryCoverageItem[0].Coverage_Id;
              if (vm.coverage == "Organization") {
                vm.selectedCoverageType = 0;
              } else {
                vm.selectedCoverageType = 1;
              }
            } else {
              vm.listLoading = false;
            }
            vm.coverageLoading = false;
          })
          .catch((err) => {
            vm.coverageLoading = false;
            vm.listLoading = false;
            vm.handleListError(err, "coverage");
          });
      }
    },
    async onChangeCoverage(value) {
      let vm = this;
      vm.listLoading = true;
      let changedCoverage;
      if (value == 1) {
        changedCoverage = "Custom Group";
      } else {
        changedCoverage = "Organization";
      }
      try {
        await vm.$store
          .dispatch("updateFormLevelCoverage", {
            Coverage: changedCoverage,
            Coverage_Id: vm.Coverage_Id,
            formName: "LOP Recovery",
          })
          .then(() => {
            vm.coverage = changedCoverage;
            vm.listLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "LOP recovery coverage updated successfully.",
            };
            vm.showAlert(snackbarData);
          })
          .catch((err) => {
            if (value == 1) {
              vm.selectedCoverageType = 0;
            } else {
              vm.selectedCoverageType = 1;
            }
            vm.listLoading = false;
            vm.handleUpdateError(err, "LOP recovery coverage");
          });
      } catch {
        vm.handleUpdateError((err = ""), "LOP recovery coverage");
      }
    },
    updateStatus(statusVal, item) {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_LOP_RECOVERY,
            variables: {
              Lop_Settings_Id: item.Lop_Settings_Id
                ? parseInt(item.Lop_Settings_Id)
                : 0,
              Auto_LOP_Applicable: item.Auto_LOP_Applicable
                ? item.Auto_LOP_Applicable
                : "No",
              Attendance_Shortage_Applicable:
                item.Attendance_Shortage_Applicable
                  ? item.Attendance_Shortage_Applicable
                  : "No",
              Late_Attendance_Applicable: item.Late_Attendance_Applicable
                ? item.Late_Attendance_Applicable
                : "No",
              Configuration_Status: statusVal[1] ? "Active" : "InActive",
              Workflow_Id: item.Workflow_Id ? item.Workflow_Id : null,
              CustomGroup_Id: item.Custom_Group_Id
                ? item.Custom_Group_Id
                : null,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "LOP recovery status updated successfully",
            };
            vm.showAlert(snackbarData);
            vm.refetchList();
          })
          .catch((error) => {
            vm.isLoading = false;
            vm.handleUpdateError(error, "LOP recovery status");
            vm.refetchList();
          });
      } catch {
        vm.isLoading = false;
        vm.handleUpdateError((err = ""), "LOP recovery status");
        vm.refetchList();
      }
    },
    handleUpdateError(err = "", form) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: form,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },
  },
};
</script>

<style scoped>
.custom-box-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.lop-recovery-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .lop-recovery-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
