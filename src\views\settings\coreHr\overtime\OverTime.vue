<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent>
        <EmployeeDefaultFilterMenu
          :isFilter="true"
          :isDefaultFilter="false"
          :translate="true"
          @apply-emp-filter="filterAppliedCount += 1"
          @reset-emp-filter="resetFilter()"
        >
          <template v-slot:new-filter>
            <v-row class="mt-3 mr-2">
              <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                <v-autocomplete
                  v-model="filterObj.salaryType"
                  color="primary"
                  :items="['Monthly', 'Hourly']"
                  :label="$t('settings.salaryType')"
                  multiple
                  closable-chips
                  chips
                  density="compact"
                  single-line
                  variant="solo"
                >
                </v-autocomplete>
              </v-col>
              <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                <v-autocomplete
                  v-model="filterObj.specialWorkDays"
                  color="primary"
                  :items="specialWorkDaysList"
                  item-title="text"
                  item-value="value"
                  :label="$t('settings.specialWorkDays')"
                  multiple
                  closable-chips
                  chips
                  density="compact"
                  single-line
                  variant="solo"
                >
                </v-autocomplete>
              </v-col>
              <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                <v-autocomplete
                  v-model="filterObj.overtimeType"
                  color="primary"
                  :items="['Wage Index', 'Fixed Amount']"
                  :label="$t('settings.overtimeType')"
                  multiple
                  closable-chips
                  chips
                  density="compact"
                  single-line
                  variant="solo"
                ></v-autocomplete>
              </v-col>
              <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                <v-autocomplete
                  v-model="filterObj.coverageType"
                  color="primary"
                  :items="['Organization', 'Custom Group']"
                  :label="$t('settings.coverageType')"
                  multiple
                  closable-chips
                  chips
                  density="compact"
                  single-line
                  variant="solo"
                ></v-autocomplete>
              </v-col>
              <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                <v-autocomplete
                  v-model="filterObj.groupName"
                  color="primary"
                  :items="groupNameList"
                  item-title="text"
                  item-value="value"
                  :label="$t('settings.customGroup')"
                  multiple
                  closable-chips
                  chips
                  density="compact"
                  single-line
                  variant="solo"
                >
                </v-autocomplete>
              </v-col>
              <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                <v-autocomplete
                  v-model="filterObj.status"
                  color="primary"
                  :items="['Active', 'Inactive']"
                  :label="$t('settings.status')"
                  multiple
                  closable-chips
                  chips
                  density="compact"
                  single-line
                  variant="solo"
                >
                </v-autocomplete>
              </v-col>
              <v-col :cols="12" class="py-2 px-5">
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("settings.wageIndex") }}
                </p>
                <div
                  :class="{
                    'd-flex align-center': true,
                    'flex-column': windowWidth < 600,
                  }"
                  style="width: 100%"
                >
                  <v-text-field
                    v-model="filterObj.wageIndex[0]"
                    :min="0"
                    :max="10"
                    density="compact"
                    style="min-width: 100px"
                    :class="windowWidth > 600 ? 'mr-2' : ''"
                    type="number"
                    variant="solo"
                    hide-details
                    @update:model-value="updateRangeValues('wageMin', $event)"
                  ></v-text-field>
                  <v-range-slider
                    width="100%"
                    :class="windowWidth < 600 ? 'ml-5' : ''"
                    v-model="wageIndexRange"
                    :max="10"
                    :step="1"
                    :strict="true"
                    thumb-label
                    color="primary"
                    hide-details
                  >
                    <template v-slot:thumb-label="{ modelValue }">
                      <span class="text-white">{{ modelValue }}</span>
                    </template>
                  </v-range-slider>
                  <v-text-field
                    v-model="filterObj.wageIndex[1]"
                    :min="0"
                    :max="10"
                    density="compact"
                    style="min-width: 100px"
                    :class="windowWidth > 600 ? 'ml-2' : ''"
                    type="number"
                    variant="solo"
                    hide-details
                    @update:model-value="updateRangeValues('wageMax', $event)"
                  ></v-text-field>
                </div>
              </v-col>
              <v-col :cols="12" class="py-2 px-5">
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("settings.amount") }}
                </p>
                <div
                  class="mb-4"
                  :class="{
                    'd-flex align-center': true,
                    'flex-column': windowWidth < 600,
                  }"
                  style="width: 100%"
                >
                  <v-text-field
                    v-model="filterObj.amount[0]"
                    :min="0"
                    :max="1000000"
                    density="compact"
                    style="min-width: 100px"
                    :class="windowWidth > 600 ? 'mr-2' : ''"
                    type="number"
                    variant="solo"
                    hide-details
                    @update:model-value="updateRangeValues('amountMin', $event)"
                  ></v-text-field>

                  <v-range-slider
                    width="100%"
                    :class="windowWidth < 600 ? 'ml-5' : ''"
                    v-model="amountRange"
                    :max="1000000"
                    :step="1"
                    :strict="true"
                    thumb-label
                    color="primary"
                    hide-details
                  >
                    <template v-slot:thumb-label="{ modelValue }">
                      <span class="text-white">{{ modelValue }}</span>
                    </template>
                  </v-range-slider>
                  <v-text-field
                    v-model="filterObj.amount[1]"
                    :min="0"
                    :max="1000000"
                    density="compact"
                    style="min-width: 100px"
                    :class="windowWidth > 600 ? 'ml-2' : ''"
                    type="number"
                    variant="solo"
                    hide-details
                    @update:model-value="updateRangeValues('amountMax', $event)"
                  ></v-text-field>
                </div>
              </v-col>
            </v-row>
          </template>
        </EmployeeDefaultFilterMenu>
      </template>
    </AppTopBarTab>
    <!-- <ProfileCard class="sub-tabs" v-if="formAccess?.view">
      <FormTab :model-value="openedSubTab">
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          color="primary"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            <div class="d-flex align-center">
              {{ tab.label }}
            </div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard> -->
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item
          :value="currentTabItem"
          :class="windowWidth < 1280 ? 'mb-14' : ''"
        >
          <OvertimeConfig
            v-if="openedSubTab === 'configuration'"
            :filter-applied-count="filterAppliedCount"
            :filter-obj="filterObj"
            @present-filter="presentFilter = $event"
            @custom-group-list="groupNameList = $event"
            @reset-filter="resetFilter()"
          ></OvertimeConfig>
          <!-- <OvertimeSlabs v-if="openedSubTab === 'slab'"></OvertimeSlabs> -->
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
// const OvertimeSlabs = defineAsyncComponent(() =>
//   import("./overtime-slabs/OvertimeSlabs.vue")
// );
const OvertimeConfig = defineAsyncComponent(() =>
  import("./overtime-config/OvertimeConfig.vue")
);
export default {
  name: "OverTime",
  components: {
    EmployeeDefaultFilterMenu,
    // OvertimeSlabs,
    OvertimeConfig,
  },
  data: () => ({
    currentTabItem: "",
    openedSubTab: "configuration",
    presentFilter: false,
    filterObj: {
      salaryType: [],
      specialWorkDays: [],
      overtimeType: [],
      coverageType: [],
      groupName: [],
      status: [],
      wageIndex: [null, null],
      amount: [null, null],
    },
    wageIndexRange: [0, 0],
    amountRange: [0, 0],
    specialWorkDaysList: [
      {
        value: "Extra Work Hours(Weekday)",
        text: "Workday",
      },
      {
        value: "Holiday",
        text: "Holiday",
      },
      {
        value: "Mandatory",
        text: "Rest/Special non-working day",
      },
      {
        value: "Work Schedule Holiday(Week Off)",
        text: "Week Off",
      },
      {
        value: "Night Work",
        text: "Night Work",
      },
      {
        value: "Week Off And Holiday",
        text: "Rest Day + Holiday",
      },
      {
        value: "Regular Holiday Falling on a scheduled Rest Day",
        text: "Special Holiday Falling on a scheduled Rest Day",
      },
    ],
    filterAppliedCount: 0,
    groupNameList: [],
  }),
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    landedFormName() {
      return this.$t("settings.overtime");
    },
    coreHRFormAccess() {
      return this.$store.getters.coreHrSettingsFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.coreHRFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            this.$t("settings." + formAccess[access].translationKey) ===
              this.landedFormName
          ) {
            formAccessArray.push(
              this.$t("settings." + formAccess[access].translationKey)
            );
          }
        }
        return formAccessArray;
      }
      return [];
    },
    subTabItems() {
      let initialTabs = [
        {
          label: this.$t("settings.configuration"),
          value: "configuration",
          disable: false,
        },
        // {
        //   label: "Slab(s)",
        //   value: "slab",
        //   disable: false,
        // },
      ];
      return initialTabs;
    },
    isActiveSubTab() {
      return (val) => {
        return this.openedSubTab === val;
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formAccess() {
      let formAccessRights = this.accessRights(363);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"] &&
        formAccessRights.accessRights["admin"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
  },
  watch: {
    wageIndexRange(val) {
      if (val && val.length) {
        if (val[0]) {
          this.filterObj.wageIndex[0] = val[0];
        } else if (val[1]) {
          this.filterObj.wageIndex[0] = 0;
        } else {
          this.filterObj.wageIndex[0] = null;
        }
        if (val[1]) {
          this.filterObj.wageIndex[1] = val[1];
        } else {
          this.filterObj.wageIndex[1] = null;
        }
      } else {
        this.filterObj.wageIndex[0] = null;
        this.filterObj.wageIndex[1] = null;
      }
    },
    amountRange(val) {
      if (val && val.length) {
        if (val[0]) {
          this.filterObj.amount[0] = val[0];
        } else if (val[1]) {
          this.filterObj.amount[0] = 0;
        } else {
          this.filterObj.amount[0] = null;
        }
        if (val[1]) {
          this.filterObj.amount[1] = val[1];
        } else {
          this.filterObj.amount[1] = null;
        }
      } else {
        this.filterObj.amount[0] = null;
        this.filterObj.amount[1] = null;
      }
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
  },
  methods: {
    onChangeSubTabs(tab) {
      if (tab !== this.openedSubTab) {
        this.openedSubTab = tab;
      }
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.coreHRFormAccess;
        let clickedForm = Object.keys(formAccess).find((item) => {
          return this.$t("settings." + formAccess[item].translationKey) === tab;
        });
        if (formAccess[clickedForm].isVue3) {
          this.$router.push("/settings/core-hr/" + formAccess[clickedForm].url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/core-hr/" + formAccess[clickedForm].url;
        }
      }
      this.$store.state.empSearchValue = "";
    },
    updateRangeValues(type, value) {
      if (type == "wageMin") {
        if (value > 10) {
          this.filterObj.wageIndex[0] = 10;
        }
        if (this.filterObj.wageIndex[0]) {
          this.wageIndexRange[0] = parseFloat(this.filterObj.wageIndex[0]);
        } else {
          this.wageIndexRange[0] = 0;
        }
      } else if (type == "wageMax") {
        if (value > 10) {
          this.filterObj.wageIndex[1] = 10;
        }
        if (this.filterObj.wageIndex[1]) {
          this.wageIndexRange[1] = parseFloat(this.filterObj.wageIndex[1]);
        } else {
          this.wageIndexRange[1] = 0;
        }
      } else if (type == "amountMin") {
        if (this.filterObj.amount[0]) {
          this.amountRange[0] = parseFloat(this.filterObj.amount[0]);
        } else {
          this.amountRange[0] = 0;
        }
      } else if (type == "amountMax") {
        if (this.filterObj.amount[1]) {
          this.amountRange[1] = parseFloat(this.filterObj.amount[1]);
        } else {
          this.amountRange[1] = 0;
        }
      }
    },
    resetFilter() {
      this.filterObj = {
        salaryType: [],
        specialWorkDays: [],
        overtimeType: [],
        coverageType: [],
        groupName: [],
        status: [],
        wageIndex: [null, null],
        amount: [null, null],
      };
      this.wageIndexRange = [0, 0];
      this.amountRange = [0, 0];
      this.filterAppliedCount = 0;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
  },
};
</script>
<style scoped>
.container {
  padding: 80px 0px 0px 0px;
}
.sub-tabs {
  position: fixed;
  top: 118px;
  z-index: 100;
}
</style>
