<template>
  <v-overlay v-model="showOverlay" class="d-flex justify-end" persistent>
    <v-card
      :style="{
        height: windowHeight + 'px',
        width: windowWidth < 800 ? '100vw' : '50vw',
      }"
    >
      <v-card-title
        v-if="windowWidth >= 770"
        class="d-flex justify-space-between align-center bg-primary"
      >
        <div class="text-h6">
          {{
            isEdit
              ? $t("settings.editOvertimeConfig")
              : $t("settings.addOvertimeConfig")
          }}
        </div>
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="openConfirmationPopup = true"
          color="white"
        ></v-btn>
      </v-card-title>
      <v-card-title v-else class="d-flex justify-end">
        <v-btn
          class="mr-2"
          variant="text"
          color="primary"
          elevation="4"
          rounded="lg"
          @click="openConfirmationPopup = true"
          >{{ $t("settings.cancel") }}
        </v-btn>
        <v-btn
          color="primary"
          rounded="lg"
          :disabled="!isFormDirty"
          @click="validateForm()"
          >{{ $t("settings.save") }}</v-btn
        >
      </v-card-title>
      <v-card-text
        :class="windowWidth < 770 ? 'mobile-content' : 'add-update-content'"
      >
        <div v-if="showEmployeesList" class="mt-3">
          <div class="">
            <v-btn
              variant="text"
              rounded="lg"
              color="primary"
              @click="showEmployeesList = false"
            >
              <v-icon class="mr-1" size="x-small">fas fa-less-than </v-icon>
              {{ $t("settings.back") }}
            </v-btn>
          </div>
          <EmployeeListCard
            :show-modal="showEmployeesList"
            :modal-title="$t('settings.customGroupEmployees')"
            :employeesList="empListForComponent"
            :selectable="false"
            :translate="true"
            :showFilter="false"
            :showFilterSearch="true"
            :isApplyFilter="true"
            @close-modal="showEmployeesList = false"
          ></EmployeeListCard>
        </div>
        <v-form v-else ref="overtimeConfigForm" class="mb-12">
          <v-row class="mt-3">
            <v-col cols="12" v-if="originalListLength === 0">
              <div
                class="d-flex align-center"
                :class="{
                  'flex-column': windowWidth < 420,
                  'justify-center': isMobileView,
                }"
              >
                <div
                  class="text-grey-darken-1 mr-2"
                  :class="isMobileView ? 'mt-2' : 'text-subtitle-1  mr-5 mt-1'"
                >
                  {{ $t("settings.overtimeCoverage") }}
                </div>
                <v-btn-toggle
                  v-model="coverageType"
                  rounded="lg"
                  mandatory
                  elevation="4"
                  density="compact"
                  @update:modelValue="acceptCoverageChange(coverageType)"
                >
                  <v-btn
                    class="text-start text-wrap"
                    color="primary"
                    style="background-color: white; color: black"
                    :size="isMobileView ? 'small' : 'default'"
                    >{{ $t("settings.organization") }}</v-btn
                  >
                  <v-btn
                    class="text-start text-wrap"
                    color="primary"
                    style="background-color: white; color: black"
                    :size="isMobileView ? 'small' : 'default'"
                    >{{ $t("settings.customGroup") }}</v-btn
                  ></v-btn-toggle
                >
              </div>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="['Monthly', 'Hourly']"
                :label="$t('settings.salaryType')"
                :isRequired="true"
                :is-auto-complete="true"
                :itemSelected="salaryType"
                @selected-item="onChangeIsFormDirty($event, 'salaryType')"
                :rules="[required($t('settings.salaryType'), salaryType, true)]"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="specialWorkDaysList"
                item-title="text"
                item-value="value"
                :label="$t('settings.specialWorkDays')"
                :isRequired="true"
                :itemSelected="specialWorkDays"
                @selected-item="onChangeIsFormDirty($event, 'specialWorkDays')"
                :is-auto-complete="true"
                :rules="[
                  required(
                    $t('settings.specialWorkDays'),
                    specialWorkDays,
                    true
                  ),
                ]"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6" v-if="showCustomGroupDropDown">
              <div class="d-flex">
                <CustomSelect
                  :items="customGroupList"
                  :label="$t('settings.customGroup')"
                  :isRequired="true"
                  :itemSelected="customGroupId"
                  @selected-item="onChangeIsFormDirty($event, 'customGroupId')"
                  item-title="Custom_Group_Name"
                  item-value="Custom_Group_Id"
                  :is-auto-complete="true"
                  :is-loading="customGroupLoading"
                  :disabled="customGroupLoading"
                  :rules="[
                    required($t('settings.customGroup'), customGroupId, true),
                  ]"
                >
                </CustomSelect>
                <v-btn
                  variant="text"
                  rounded="lg"
                  class="ml-2 mt-2"
                  color="grey-darken-1"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="retrieveCustomGroups()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </div>
              <div class="mb-1">
                <v-btn
                  color="primary"
                  variant="text"
                  :href="baseUrl + 'in/core-hr/custom-employee-groups'"
                  target="_blank"
                >
                  <v-icon size="14" class="mr-1">fas fa-plus</v-icon>
                  {{ $t("settings.addCustomGroup") }}
                </v-btn>
              </div>
            </v-col>
            <v-col v-if="customGroupId" cols="12" md="6">
              <div v-if="isLoadingCard">
                <v-skeleton-loader
                  type="list-item-two-line"
                  class="ml-n4 mt-n2"
                  width="80%"
                ></v-skeleton-loader>
              </div>
              <div v-else>
                <span class="text-subtitle-1 text-grey-darken-1"
                  >{{ $t("settings.employees") }} -
                  {{ empListInSelectedGroup.length }}</span
                >
                <div
                  v-if="empListInSelectedGroup.length === 0"
                  class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                >
                  <v-icon color="warning" size="25"
                    >fas fa-exclamation-triangle</v-icon
                  >
                  <span
                    v-if="errorInFetchEmployeesList"
                    class="pl-2 text-subtitle-1 font-weight-regular"
                    >{{ $t("settings.errorEmployeeList") }}
                    <a class="text-primary" @click="fetchCustomEmployeesList"
                      >{{ $t("settings.refresh") }}
                    </a>
                  </span>
                  <span
                    v-else-if="isNoEmployees"
                    class="pl-2 text-subtitle-1 font-weight-regular"
                  >
                    {{ $t("settings.noEmployeesFound") }}</span
                  >
                </div>
                <div v-else class="d-flex align-center">
                  <AvatarOrderedList
                    v-if="empListInSelectedGroup.length > 0"
                    class="mt-2"
                    :ordered-list="empListInSelectedGroup"
                  ></AvatarOrderedList>
                  <v-btn
                    rounded="lg"
                    color="primary"
                    size="small"
                    class="mt-2"
                    @click="openCustomGroupEmpList()"
                  >
                    {{ $t("settings.viewAll") }}
                  </v-btn>
                </div>
              </div>
            </v-col>
            <v-col cols="12" md="6">
              <CustomSelect
                :items="['Wage Index', 'Fixed Amount']"
                :label="$t('settings.overtimeType')"
                :isRequired="true"
                :itemSelected="overtimeType"
                @selected-item="onChangeIsFormDirty($event, 'overtimeType')"
                :is-auto-complete="true"
                :rules="[
                  required($t('settings.overtimeType'), overtimeType, true),
                ]"
              ></CustomSelect>
            </v-col>
            <v-col cols="12" md="6" v-if="overtimeType === 'Wage Index'">
              <v-text-field
                v-model="wageFactor"
                type="number"
                :min="0"
                :max="10"
                variant="solo"
                :rules="[
                  numericRequiredValidation(
                    $t('settings.wageIndex'),
                    wageFactor,
                    true
                  ),
                  minMaxNumberValidationWithoutMaxField(
                    $t('settings.wageIndex'),
                    wageFactor,
                    0,
                    10
                  ),
                  twoDecimalPrecisionValidation(wageFactor, true),
                ]"
                @update:model-value="onChangeIsFormDirty($event, 'wageFactor')"
              >
                <template v-slot:label>
                  <span>{{ $t("settings.wageIndex") }}</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6" v-if="overtimeType === 'Fixed Amount'">
              <v-text-field
                v-model="overtimeFixedAmount"
                type="number"
                variant="solo"
                :rules="[
                  numericRequiredValidation(
                    $t('settings.amount'),
                    overtimeFixedAmount,
                    true
                  ),
                  minMaxNumberValidationWithoutMaxField(
                    $t('settings.amount'),
                    overtimeFixedAmount,
                    0,
                    99999999
                  ),
                  twoDecimalPrecisionValidation(overtimeFixedAmount, true),
                ]"
                @update:model-value="
                  onChangeIsFormDirty($event, 'overtimeFixedAmount')
                "
              >
                <template v-slot:label>
                  <span>{{ $t("settings.amount") }}</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6" v-if="isEdit">
              <div class="v-label ml-2 mb-2">{{ $t("settings.status") }}</div>
              <AppToggleButton
                :button-active-text="'Active'"
                :button-inactive-text="'Inactive'"
                button-active-color="#7de272"
                button-inactive-color="red"
                id-value="gab-analysis-based-on"
                :current-value="Status === 'Active' ? true : false"
                @chosen-value="onChangeStatus($event)"
              ></AppToggleButton>
            </v-col>
          </v-row>
        </v-form>
        <div v-if="windowWidth < 770" style="margin-bottom: 200px"></div>
      </v-card-text>
      <v-card
        v-if="!showEmployeesList && windowWidth >= 770"
        width="100%"
        class="overlay-footer"
        elevation="16"
      >
        <v-btn
          class="mr-2"
          variant="text"
          color="primary"
          elevation="4"
          rounded="lg"
          @click="openConfirmationPopup = true"
          >{{ $t("settings.cancel") }}
        </v-btn>
        <v-btn
          color="primary"
          rounded="lg"
          :disabled="!isFormDirty"
          @click="validateForm()"
          >{{ $t("settings.save") }}</v-btn
        >
      </v-card>
      <AppLoading v-if="isLoading"></AppLoading>
    </v-card>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    :confirmation-heading="$t('settings.exitFormWarning')"
    :accept-button-text="$t('settings.yes')"
    :cancel-button-text="$t('settings.no')"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="closeForm()"
  />
</template>
<script>
import { defineAsyncComponent } from "vue";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
const AvatarOrderedList = defineAsyncComponent(() =>
  import("@/components/helper-components/AvatarOrderedList.vue")
);
const EmployeeListCard = defineAsyncComponent(() =>
  import("@/components/helper-components/EmployeeListCard.vue")
);
import validationRules from "@/mixins/validationRules";
import { ADD_UPDATE_OVERTIME_CONFIG } from "@/graphql/settings/core-hr/overtimeQueries";
export default {
  name: "AddEditOvertimeConfig",
  mixins: [validationRules],
  components: {
    CustomSelect,
    AvatarOrderedList,
    EmployeeListCard,
  },
  emits: ["close-form", "add-update-success"],
  props: {
    showForm: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    coverage: {
      type: Number,
      required: 0,
    },
    coverageId: {
      type: Number,
      required: true,
    },
    selectedItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
    originalListLength: {
      type: Number,
      default: 0,
    },
  },
  data: () => ({
    showOverlay: false,
    openConfirmationPopup: false,
    specialWorkDaysList: [
      {
        value: "Extra Work Hours(Weekday)",
        text: "Workday",
      },
      {
        value: "Holiday",
        text: "Holiday",
      },
      {
        value: "Mandatory",
        text: "Rest/Special non-working day",
      },
      {
        value: "Work Schedule Holiday(Week Off)",
        text: "Week Off",
      },
      {
        value: "Night Work",
        text: "Night Work",
      },
      {
        value: "Week Off And Holiday",
        text: "Rest Day + Holiday",
      },
      {
        value: "Regular Holiday Falling on a scheduled Rest Day",
        text: "Special Holiday Falling on a scheduled Rest Day",
      },
    ],
    specialWorkDays: null,
    salaryType: null,
    isFormDirty: false,
    customGroupId: null,
    customGroupList: [],
    customGroupLoading: false,
    empListInSelectedGroup: [],
    isLoadingCard: false,
    errorInFetchEmployeesList: false,
    isNoEmployees: false,
    wageFactor: null,
    overtimeType: null,
    overtimeFixedAmount: null,
    Status: "Active",
    isLoading: false,
    empListForComponent: [],
    showEmployeesList: false,
    coverageType: null,
  }),
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    showCustomGroupDropDown() {
      if (this.isEdit) {
        return this.selectedItem?.Custom_Group_Id !== null ? true : false;
      } else {
        return this.coverageType ? true : false;
      }
    },
  },
  watch: {
    showForm(val) {
      this.showOverlay = val;
    },
    customGroupId(val) {
      if (!val) {
        this.empListInSelectedGroup = [];
      } else {
        this.fetchCustomEmployeesList();
      }
    },
  },
  mounted() {
    this.showOverlay = this.showForm;
    this.coverageType = this.coverage;
    if (this.showCustomGroupDropDown) {
      this.retrieveCustomGroups();
    }
    if (this.isEdit) {
      this.salaryType = this.selectedItem?.Salary_Type;
      this.specialWorkDays = this.selectedItem?.Special_Work_Days;
      this.customGroupId = this.selectedItem?.Custom_Group_Id;
      this.overtimeType = this.selectedItem?.Overtime_Type;
      this.wageFactor = this.selectedItem?.Wage_Factor;
      this.overtimeFixedAmount = this.selectedItem?.Amount;
      this.Status = this.selectedItem?.Status;
    }
  },
  methods: {
    closeForm() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },
    onChangeIsFormDirty(val, field) {
      if (field == "salaryType") {
        this.salaryType = val;
        this.specialWorkDays = null;
      } else if (field == "specialWorkDays") {
        this.specialWorkDays = val;
      } else if (field == "customGroupId") {
        this.customGroupId = val;
      } else if (field == "overtimeType") {
        this.overtimeType = val;
      } else if (field == "wageFactor") {
        this.wageFactor = val;
      } else if (field == "overtimeFixedAmount") {
        this.overtimeFixedAmount = val;
      }
      this.isFormDirty = true;
    },
    onChangeStatus(value) {
      this.Status = value[1] ? "Active" : "Inactive";
      this.isFormDirty = true;
    },
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },
    async retrieveCustomGroups() {
      this.customGroupLoading = true;
      this.customGroupList = [];

      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: "Over Time",
        })
        .then((groupList) => {
          if (groupList && groupList.length > 0) {
            this.customGroupList = groupList;
          }
          this.customGroupLoading = false;
        })
        .catch(() => {
          this.customGroupLoading = false;
        });
    },
    async fetchCustomEmployeesList() {
      if (this.customGroupId) {
        let vm = this;
        vm.isLoadingCard = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(this.customGroupId),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              if (!employeeDetails || employeeDetails.length === 0) {
                vm.isNoEmployees = true;
                vm.empListInSelectedGroup = [];
              } else {
                for (let i = 0; i < employeeDetails.length; i++) {
                  employeeDetails[i].employee_name =
                    employeeDetails[i]["employeeName"];
                  employeeDetails[i].designation_name =
                    employeeDetails[i]["designationName"];
                  employeeDetails[i].department_name =
                    employeeDetails[i]["departmentName"];
                  employeeDetails[i].user_defined_empid =
                    employeeDetails[i]["userDefinedEmpId"];
                  delete employeeDetails[i].key1;
                }
                vm.empListInSelectedGroup = employeeDetails;
              }
              vm.isLoadingCard = false;
              vm.errorInFetchEmployeesList = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.errorInFetchEmployeesList = true;
            vm.empListInSelectedGroup = [];
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },
    async validateForm() {
      let { valid } = await this.$refs.overtimeConfigForm.validate();
      if (valid) {
        this.addUpdateOvertimeConfig();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          const fields = Array.isArray(field) ? field : [field];
          if (fields && fields[0] && fields[0].rules) {
            const allValid = fields[0].rules.every((value) => value === true);
            if (fields[0].rules.length > 0 && !allValid) {
              invalidFields.push(refName);
            }
          }
        });

        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              const fields = Array.isArray(fieldRef) ? fieldRef : [fieldRef];
              if (fields && fields[0] && fields[0].$el) {
                const element = fields[0].$el;
                if (element && element.scrollIntoView) {
                  element.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                } else if (element && element.getBoundingClientRect) {
                  const rect = element.getBoundingClientRect();
                  window.scrollTo({
                    top: window.scrollY + rect.top - 100, // adjust offset if needed
                    behavior: "smooth",
                  });
                }
              }
            }
          });
        }
      }
    },
    addUpdateOvertimeConfig() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_OVERTIME_CONFIG,
          variables: {
            Configuration_Id: vm.selectedItem?.Configuration_Id || 0,
            CustomGroup_Id: vm.customGroupId ? vm.customGroupId : null,
            Wage_Factor:
              vm.wageFactor && vm.overtimeType === "Wage Index"
                ? parseFloat(vm.wageFactor)
                : null,
            Salary_Type: vm.salaryType,
            Special_Work_Days: vm.specialWorkDays,
            Status: vm.isEdit ? vm.Status : "Active",
            Overtime_Type: vm.overtimeType,
            overtimeFixedAmount:
              vm.overtimeFixedAmount && vm.overtimeType === "Fixed Amount"
                ? parseFloat(vm.overtimeFixedAmount)
                : null,
            formId: 363,
          },
          client: "apolloClientJ",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.isEdit
              ? vm.$t("settings.overtimeUpdateSuccess")
              : vm.$t("settings.overtimeAddSuccess"),
          };
          vm.showAlert(snackbarData);
          vm.$emit("add-update-success");
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEdit ? "updating" : "adding",
        form: "overtime configuration",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async acceptCoverageChange() {
      let vm = this;
      vm.isLoading = true;
      let changedCoverage = "";
      if (vm.coverageType == 1) {
        changedCoverage = "Custom Group";
      } else {
        changedCoverage = "Organization";
      }
      try {
        await vm.$store
          .dispatch("updateFormLevelCoverage", {
            Coverage: changedCoverage,
            Coverage_Id: vm.coverageId,
            formName: "Over Time",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Overtime coverage updated successfully.",
            };
            vm.showAlert(snackbarData);
          })
          .catch((err) => {
            if (vm.coverageType == 1) {
              vm.coverageType = 0;
            } else {
              vm.coverageType = 1;
            }
            vm.isLoading = false;
            vm.handleAddUpdateError(err);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
  },
};
</script>
<style scoped>
.add-update-content {
  max-height: calc(100vh - 120px) !important;
  overflow-y: scroll;
}
.mobile-content {
  height: calc(100vh - 60px) !important;
  overflow-y: scroll;
}
.overlay-footer {
  height: 7%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: fixed;
  bottom: 0;
  padding: 8px;
  z-index: 2000;
}
</style>
