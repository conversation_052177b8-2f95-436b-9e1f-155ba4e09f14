<template>
  <div class="mt-3 mx-5">
    <div v-if="listLoading" class="mt-5">
      <!-- Skeleton loaders -->
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      image-name="common/human-error-image"
      :button-text="$t('settings.retry')"
      @button-click="onActions('refetch list')"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="itemList.length === 0"
      key="no-results-screen"
      :main-title="
        originalList.length === 0
          ? ''
          : $t('settings.noRecordsFound', {
              formName: 'overtime configuration',
            })
      "
      :isSmallImage="originalList.length === 0"
      :image-name="originalList.length === 0 ? '' : 'common/no-records'"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row
            :style="originalList.length === 0 ? 'background: white' : ''"
            class="rounded-lg pa-5 mb-4"
          >
            <v-col v-if="originalList.length === 0" cols="12">
              <NotesCard
                :notes="$t('settings.overTimeEmptyText1')"
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
              <NotesCard
                :notes="$t('settings.overTimeEmptyText2')"
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
            </v-col>
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                v-if="originalList.length === 0 && formAccess?.add"
                variant="elevated"
                class="ml-4 mt-1 primary"
                prepend-icon="fas fa-plus"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="onActions('add')"
              >
                <span>{{ $t("settings.addConfiguration") }}</span>
              </v-btn>
              <v-btn
                v-if="originalList.length > 0"
                color="primary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="$emit('reset-filter')"
              >
                {{ $t("settings.resetFilterSearch") }}
              </v-btn>
              <v-btn
                v-if="originalList.length === 0"
                rounded="lg"
                class="mt-1"
                color="transparent"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="onActions('refetch list')"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <div
        style="width: 100%"
        class="d-flex align-center flex-wrap py-1 mb-2"
        :class="{
          'justify-space-between': windowWidth >= 800,
          'justify-center': windowWidth < 800,
        }"
      >
        <div
          class="d-flex align-center"
          :class="{
            'flex-column': windowWidth < 420,
            'justify-center': isMobileView,
          }"
        >
          <div
            class="text-grey-darken-1 mr-2"
            :class="isMobileView ? 'mt-2' : 'text-subtitle-1  mr-5 mt-1'"
          >
            {{ $t("settings.overtimeCoverage") }}
          </div>
          <v-tooltip location="right" height="auto">
            <template v-slot:activator="{ props }">
              <v-btn-toggle
                v-model="selectedCoverageType"
                rounded="lg"
                mandatory
                v-bind="!formAccess?.update || hasActiveStatus ? props : {}"
                density="compact"
                :disabled="!formAccess?.update || hasActiveStatus"
                :class="{
                  'cursor-not-allow': !formAccess?.update || hasActiveStatus,
                  'custom-box-shadow': formAccess?.update && !hasActiveStatus,
                }"
                @update:modelValue="onChangeCoverage(selectedCoverageType)"
              >
                <v-btn
                  class="text-start text-wrap"
                  color="primary"
                  style="background-color: white; color: black"
                  :size="isMobileView ? 'small' : 'default'"
                >
                  {{ $t("settings.organization") }}
                </v-btn>
                <v-btn
                  class="text-start text-wrap"
                  color="primary"
                  style="background-color: white; color: black"
                  :size="isMobileView ? 'small' : 'default'"
                >
                  {{ $t("settings.customGroup") }}
                </v-btn>
              </v-btn-toggle>
            </template>

            <div
              v-if="!formAccess?.update || hasActiveStatus"
              style="max-width: 200px"
            >
              {{
                !formAccess?.update
                  ? $t("settings.noAccessMessage")
                  : $t("settings.activeOvertimeConfigurations")
              }}
            </div>
          </v-tooltip>
        </div>
        <div
          class="d-flex align-center flex-wrap justify-center"
          :class="windowWidth < 800 ? 'mt-3' : ''"
        >
          <v-btn
            v-if="formAccess?.add"
            color="primary"
            variant="elevated"
            class="mr-1"
            rounded="lg"
            prepend-icon="fas fa-plus"
            :size="isMobileView ? 'small' : 'default'"
            @click="onActions('add')"
          >
            {{ $t("settings.addConfiguration") }}
          </v-btn>
          <div :class="isMobileView ? 'mt-2' : ''">
            <v-btn
              rounded="lg"
              color="transparent"
              variant="flat"
              :size="isMobileView ? 'small' : 'default'"
              @click="onActions('refetch list')"
            >
              <v-icon>fas fa-redo-alt</v-icon>
            </v-btn>
            <v-menu v-model="openMoreMenu" transition="scale-transition">
              <template v-slot:activator="{ props }">
                <v-btn variant="plain" v-bind="props">
                  <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                  <v-icon v-else>fas fa-caret-up</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item
                  v-for="action in [$t('settings.export')]"
                  :key="action"
                  @click="onActions(action)"
                >
                  <v-hover>
                    <template v-slot:default="{ isHovering, props }">
                      <v-list-item-title
                        v-bind="props"
                        class="pa-3"
                        :class="{
                          'bg-hover': isHovering,
                        }"
                        >{{ action }}</v-list-item-title
                      >
                    </template>
                  </v-hover>
                </v-list-item>
              </v-list>
            </v-menu>
          </div>
        </div>
      </div>
      <v-data-table
        class="custom-scroll-table"
        :headers="tableHeaders"
        :items="itemList"
        fixed-header
        :height="$store.getters.getTableHeightBasedOnScreenSize(290, itemList)"
        :items-per-page="itemsPerPage"
        :page="currentPage"
        v-model:page="currentPage"
        v-model:items-per-page="itemsPerPage"
        :page-text="
          $t('settings.pageText', {
            start: startItem,
            end: endItem,
            total: itemList.length,
          })
        "
        :items-per-page-text="$t('settings.itemPerPage')"
        :items-per-page-options="[
          { value: 50, title: '50' },
          { value: 100, title: '100' },
          {
            value: -1,
            title: this.$t('settings.all'),
          },
        ]"
      >
        <template v-slot:item="{ item }">
          <tr
            :style="isMobileView ? 'width: calc(100vw - 40px)' : ''"
            class="data-table-tr bg-white cursor-pointer"
            @click="onActions('view', item)"
            :class="[
              isMobileView ? ' v-data-table__mobile-table-row ma-0 mt-2' : '',
            ]"
          >
            <td
              class="bg-white"
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5 font-weight-small'
              "
              style="position: sticky; left: 0"
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center"
                :class="
                  isMobileView
                    ? 'text-subtitle-1 text-grey-darken-1'
                    : 'mt-2 font-weight-bold'
                "
              >
                {{ $t("settings.salaryType") }}
              </div>
              <section style="height: 3em" class="d-flex align-center">
                <span class="text-primary text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Salary_Type) }}
                </span>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5 font-weight-medium'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center"
                :class="
                  isMobileView
                    ? 'text-subtitle-1 text-grey-darken-1'
                    : 'mt-2 font-weight-bold'
                "
              >
                {{ $t("settings.specialWorkDays") }}
              </div>
              <section
                :style="isMobileView ? 'max-width: 50%' : 'max-width: 200px'"
                class="text-truncate"
              >
                <span class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(specialWorkDaysList[item.Special_Work_Days])
                  }}
                </span>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5 font-weight-medium'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center"
                :class="
                  isMobileView
                    ? 'text-subtitle-1 text-grey-darken-1'
                    : 'mt-2 font-weight-bold'
                "
              >
                {{ $t("settings.overtimeType") }}
              </div>
              <section>
                <span class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(item.Overtime_Type) }}
                </span>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center"
                :class="
                  isMobileView
                    ? 'text-subtitle-1 text-grey-darken-1'
                    : 'mt-2 font-weight-bold'
                "
              >
                {{ $t("settings.wageIndex") }}
              </div>
              <section>
                <span class="text-subtitle-1 font-weight-regular">
                  {{
                    item.Wage_Factor || item.Wage_Factor == 0
                      ? checkNullValue(item.Wage_Factor)
                      : "-"
                  }}
                </span>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center"
                :class="
                  isMobileView
                    ? 'text-subtitle-1 text-grey-darken-1'
                    : 'mt-2 font-weight-bold'
                "
              >
                {{ $t("settings.amount") }}
              </div>
              <section>
                <span class="text-subtitle-1 font-weight-regular">
                  {{
                    item.Amount || item.Amount == 0
                      ? checkNullValue(item.Amount)
                      : "-"
                  }}
                </span>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="text-subtitle-1 text-grey-darken-1 mt-2"
              >
                {{ $t("settings.customGroup") }}
              </div>
              <section>
                <span
                  class="text-subtitle-1 font-weight-regular"
                  :class="
                    item.Group_Name == null && !isMobileView ? 'ml-10' : ''
                  "
                >
                  {{ checkNullValue(item.Group_Name) }}
                </span>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center"
                :class="
                  isMobileView
                    ? 'text-subtitle-1 text-grey-darken-1'
                    : 'mt-2 font-weight-bold'
                "
              >
                {{ $t("settings.status") }}
              </div>
              <section class="d-flex align-center justify-space-between">
                <div class="d-flex align-center justify-space-around">
                  <span
                    id="w-80"
                    v-if="item.Status?.toLowerCase() === 'active'"
                    class="text-green text-subtitle-1 font-weight-regular d-flex justify-center align-center"
                    >{{ item.Status }}</span
                  >
                  <span
                    id="w-80"
                    v-else
                    class="text-red text-subtitle-1 font-weight-regular d-flex justify-center align-center text-center"
                    >{{ item.Status }}</span
                  >
                </div>
              </section>
            </td>
            <td
              :class="
                isMobileView
                  ? 'd-flex justify-space-between align-center'
                  : 'pa-2 pl-5'
              "
            >
              <div
                v-if="isMobileView"
                class="d-flex align-center"
                :class="
                  isMobileView
                    ? 'text-subtitle-1 text-grey-darken-1'
                    : 'mt-2 font-weight-bold'
                "
              >
                {{ $t("settings.actions") }}
              </div>
              <section class="d-flex align-center justify-end">
                <ActionMenu
                  @selected-action="onActions($event, item)"
                  :actions="[$t('settings.edit')]"
                  :isPresentTooltip="false"
                  :tooltipMessage="getTooltipMessage(item)"
                  :tooltipActionButtons="getDisabledButtons(item)"
                  :disableActionButtons="getDisabledButtons(item)"
                  iconColor="grey"
                />
              </section>
            </td>
          </tr>
        </template>
      </v-data-table>
    </div>
  </div>
  <AddEditOvertimeConfig
    v-if="showAddEditForm"
    :showForm="showAddEditForm"
    :originalListLength="originalList.length"
    :coverage="selectedCoverageType"
    :coverageId="Coverage_Id"
    :isEdit="isEdit"
    :selectedItem="selectedItem"
    @close-form="closeAllForms()"
    @add-update-success="onActions('refetch list')"
  ></AddEditOvertimeConfig>
  <ViewOvertimeConfig
    v-if="showViewForm"
    :showForm="showViewForm"
    :selectedItem="selectedItem"
    :coverage="selectedCoverageType"
    :formAccess="formAccess"
    @open-edit-form="onActions(this.$t('settings.edit'), $event)"
    @close-form="closeAllForms()"
  ></ViewOvertimeConfig>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    :confirmation-heading="$t('settings.updateOvertimeCoverage')"
    :accept-button-text="$t('settings.yes')"
    :cancel-button-text="$t('settings.no')"
    @close-warning-modal="rejectCoverageChange()"
    @accept-modal="acceptCoverageChange()"
  />
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
import { checkNullValue } from "@/helper.js";
import { RETRIEVE_OVERTIME_CONFIG } from "@/graphql/settings/core-hr/overtimeQueries.js";

import moment from "moment";
import FileExportMixin from "@/mixins/FileExportMixin";

const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const AddEditOvertimeConfig = defineAsyncComponent(() =>
  import("./AddEditOvertimeConfig.vue")
);
const ViewOvertimeConfig = defineAsyncComponent(() =>
  import("./ViewOvertimeConfig.vue")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);
export default {
  name: "OvertimeConfig",
  mixins: [FileExportMixin],
  emits: ["present-filter", "reset-filter", "custom-group-list"],
  components: {
    NotesCard,
    AddEditOvertimeConfig,
    ViewOvertimeConfig,
    ActionMenu,
  },
  props: {
    filterAppliedCount: {
      type: Number,
      default: 0,
    },
    filterObj: {
      type: Object,
      default: () => ({}),
    },
  },
  data: () => ({
    listLoading: false,
    coverageLoading: false,
    isLoading: false,
    isErrorInList: false,
    errorContent: "",
    originalList: [],
    itemList: [],
    openMoreMenu: false,
    showAddEditForm: false,
    showViewForm: false,
    isEdit: false,
    selectedItem: null,
    selectedCoverageType: 0,
    openConfirmationPopup: false,
    Coverage_Id: null,
    currentPage: 1,
    itemsPerPage: 50,
    specialWorkDaysList: {
      "Extra Work Hours(Weekday)": "Workday",
      Holiday: "Holiday",
      Mandatory: "Rest/Special non-working day",
      "Work Schedule Holiday(Week Off)": "Week Off",
      "Night Work": "Night Work",
      "Week Off And Holiday": "Rest Day + Holiday",
      "Regular Holiday Falling on a scheduled Rest Day":
        "Special Holiday Falling on a scheduled Rest Day",
    },
  }),
  computed: {
    startItem() {
      if (this.itemsPerPage === -1) return this.itemList.length > 0 ? 1 : 0;
      return (this.currentPage - 1) * this.itemsPerPage + 1;
    },
    endItem() {
      if (this.itemsPerPage === -1) return this.itemList.length;
      const end = this.currentPage * this.itemsPerPage;
      return end > this.itemList.length ? this.itemList.length : end;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      return [
        {
          title: this.$t("settings.salaryType"),
          key: "Salary_Type",
          fixed: true,
        },
        {
          title: this.$t("settings.specialWorkDays"),
          key: "Special_Work_Days",
        },
        {
          title: this.$t("settings.overtimeType"),
          key: "Overtime_Type",
        },
        {
          title: this.$t("settings.wageIndex"),
          key: "Wage_Factor",
        },
        {
          title: this.$t("settings.amount"),
          key: "Amount",
        },
        {
          title: this.$t("settings.customGroup"),
          key: "Group_Name",
        },
        {
          title: this.$t("settings.status"),
          key: "Status",
        },
        {
          title: this.$t("settings.actions"),
          key: "",
          align: "end",
          sortable: false,
        },
      ];
    },
    hasActiveStatus() {
      // Check if any entry in originalList has an active Status
      return this.originalList.some(
        (entry) => entry.Status?.toLowerCase() === "active"
      );
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat =
            this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
          return date ? moment(date).format(orgDateFormat) : "";
        }
        return "";
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccessRights = this.accessRights(363);
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    isCustomGroupToolTip() {
      return (item) => {
        return (
          (this.selectedCoverageType === 0 && item.Custom_Group_Id !== null) ||
          (this.selectedCoverageType === 1 && item.Custom_Group_Id === null)
        );
      };
    },
    getDisabledButtons() {
      return (item) => {
        let disabledButtons = [];
        if (!this.formAccess?.update) {
          disabledButtons.push(this.$t("settings.edit"));
        } else if (this.isCustomGroupToolTip(item)) {
          disabledButtons.push(this.$t("settings.edit"));
        }
        return disabledButtons;
      };
    },
    getTooltipMessage() {
      return (item) => {
        if (!this.formAccess?.update) {
          return this.$t("settings.noAccessMessage");
        } else if (this.isCustomGroupToolTip(item)) {
          return this.$t("settings.overtimeConfigSetTo", {
            current:
              this.selectedCoverageType === 0
                ? this.$t("settings.customGroup")
                : this.$t("settings.organization"),
            conflicting:
              this.selectedCoverageType === 0
                ? this.$t("settings.organization")
                : this.$t("settings.customGroup"),
            adjust:
              this.selectedCoverageType === 0
                ? this.$t("settings.customGroup")
                : this.$t("settings.organization"),
          });
        }
      };
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    filterAppliedCount(val) {
      if (val) {
        this.applyFilter();
      } else {
        this.itemList = this.originalList;
      }
    },
  },
  mounted() {
    this.fetchOvertimeConfigList();
    this.retrieveSpecialWagesCoverage();
  },
  methods: {
    checkNullValue,
    onActions(type, item) {
      if (type?.toLowerCase() === "add") {
        this.showAddEditForm = true;
        this.isEdit = false;
        this.selectedItem = null;
      } else if (type?.toLowerCase() === "view") {
        this.selectedItem = item;
        this.showViewForm = true;
        this.showAddEditForm = false;
      } else if (type?.toLowerCase() === "refetch list") {
        this.$emit("reset-filter");
        this.closeAllForms();
        this.fetchOvertimeConfigList();
        this.retrieveSpecialWagesCoverage();
      } else if (type === this.$t("settings.edit")) {
        this.selectedItem = item;
        this.isEdit = true;
        this.showAddEditForm = true;
        this.showViewForm = false;
      } else if (type === this.$t("settings.export")) {
        this.exportReportFile();
      }
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
      this.errorContent = "";
      this.isErrorInList = false;
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: this.$t("settings.salaryType"),
          key: "Salary_Type",
        },
        {
          header: this.$t("settings.specialWorkDays"),
          key: "Special_Work_Days",
        },
        {
          header: this.$t("settings.overtimeType"),
          key: "Overtime_Type",
        },
        {
          header: this.$t("settings.wageIndex"),
          key: "Wage_Factor",
        },
        {
          header: this.$t("settings.amount"),
          key: "Amount",
        },
        {
          header: this.$t("settings.customGroup"),
          key: "Group_Name",
        },
        {
          header: this.$t("settings.status"),
          key: "Status",
        },
        {
          header: this.$t("settings.addedBy"),
          key: "Added_By",
        },
        {
          header: this.$t("settings.addedOn"),
          key: "Added_On",
        },
        {
          header: this.$t("settings.updatedBy"),
          key: "Updated_By",
        },
        {
          header: this.$t("settings.updatedOn"),
          key: "Updated_On",
        },
      ];
      let overtimeConfigList = this.itemList.map((item) => ({
        Salary_Type: item.Salary_Type,
        Special_Work_Days:
          item.Special_Work_Days ===
          "Regular Holiday Falling on a scheduled Rest Day"
            ? "Special Holiday Falling on a scheduled Rest Day"
            : item.Special_Work_Days,
        Group_Name: item.Group_Name,
        Overtime_Type: item.Overtime_Type,
        Wage_Factor: item.Wage_Factor,
        Amount: item.Amount,
        Status: item.Status,
        Added_By: item.Added_By,
        Added_On: this.formatDate(new Date(item.Added_On + ".000Z")),
        Updated_By: item.Updated_By,
        Updated_On: item.Updated_On
          ? this.formatDate(new Date(item.Updated_On + ".000Z"))
          : "",
      }));
      let fileName = this.$t("settings.overtimeConfig");
      let exportOptions = {
        fileExportData: overtimeConfigList,
        fileName: fileName,
        sheetName: fileName,
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    applyFilter() {
      if (this.filterAppliedCount) {
        let filteredList = this.originalList;
        if (this.filterObj.salaryType.length > 0) {
          filteredList = filteredList.filter((item) => {
            return this.filterObj.salaryType.includes(item.Salary_Type);
          });
        }
        if (this.filterObj.specialWorkDays.length > 0) {
          filteredList = filteredList.filter((item) => {
            return this.filterObj.specialWorkDays.includes(
              item.Special_Work_Days
            );
          });
        }
        if (this.filterObj.overtimeType.length > 0) {
          filteredList = filteredList.filter((item) => {
            return this.filterObj.overtimeType.includes(item.Overtime_Type);
          });
        }
        if (this.filterObj.coverageType.length > 0) {
          filteredList = filteredList.filter((item) => {
            if (
              this.filterObj.coverageType.includes("Custom Group") &&
              this.filterObj.coverageType.includes("Organization")
            ) {
              return true;
            } else if (this.filterObj.coverageType.includes("Organization")) {
              return item.Custom_Group_Id === null;
            } else {
              return item.Custom_Group_Id !== null;
            }
          });
        }
        if (this.filterObj.groupName.length > 0) {
          filteredList = filteredList.filter((item) => {
            return this.filterObj.groupName.includes(item.Group_Name);
          });
        }
        if (this.filterObj.status.length > 0) {
          filteredList = filteredList.filter((item) => {
            return this.filterObj.status.includes(item.Status);
          });
        }
        if (
          this.filterObj.wageIndex.length &&
          this.filterObj.wageIndex[0] !== null &&
          this.filterObj.wageIndex[0] !== "" &&
          this.filterObj.wageIndex[1] !== null &&
          this.filterObj.wageIndex[1] !== ""
        ) {
          filteredList = filteredList.filter((item) => {
            return (
              item.Wage_Factor >= this.filterObj.wageIndex[0] &&
              item.Wage_Factor <= this.filterObj.wageIndex[1]
            );
          });
        }
        if (
          this.filterObj.amount.length &&
          this.filterObj.amount[0] !== null &&
          this.filterObj.amount[0] !== "" &&
          this.filterObj.amount[1] !== null &&
          this.filterObj.amount[1] !== ""
        ) {
          filteredList = filteredList.filter((item) => {
            return (
              item.Amount >= this.filterObj.amount[0] &&
              item.Amount <= this.filterObj.amount[1]
            );
          });
        }
        this.itemList = filteredList;
      }
    },
    fetchOvertimeConfigList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_OVERTIME_CONFIG,
          client: "apolloClientI",
          variables: {
            formId: 363,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveOvertimeConfiguration &&
            response.data.retrieveOvertimeConfiguration.OvertimeConfiguration &&
            response.data.retrieveOvertimeConfiguration.OvertimeConfiguration
              .length > 0
          ) {
            vm.itemList =
              response.data.retrieveOvertimeConfiguration.OvertimeConfiguration;
            vm.originalList =
              response.data.retrieveOvertimeConfiguration.OvertimeConfiguration;
            let customGroupList = new Set();
            this.originalList.map((emp) => {
              if (customGroupList.has(emp.Group_Name)) return false;
              customGroupList.add(emp.Group_Name);
              return true;
            });
            let customGroupArray = Array.from(customGroupList).filter(Boolean);
            vm.$emit("custom-group-list", customGroupArray);
            if (vm.originalList.length > 0) {
              vm.$emit("present-filter", true);
            }
          } else {
            vm.$emit("custom-group-list", []);
            vm.$emit("present-filter", false);
            vm.originalList = [];
            vm.itemList = [];
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.$emit("custom-group-list", []);
          vm.$emit("present-filter", false);
          vm.listLoading = false;
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "overtime configuration",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    async retrieveSpecialWagesCoverage() {
      let vm = this;
      vm.coverageLoading = true;
      await this.$store
        .dispatch("retrieveFormLevelCoverage", {
          formName: "Over Time",
          Form_Id: 363,
        })
        .then((response) => {
          if (response && response.length > 0) {
            vm.Coverage_Id = response[0].Coverage_Id;
            if (response[0].Coverage == "Organization") {
              vm.selectedCoverageType = 0;
            } else {
              vm.selectedCoverageType = 1;
            }
          } else {
            vm.selectedCoverageType = 0;
          }
          vm.coverageLoading = false;
        })
        .catch(() => {
          vm.coverageLoading = false;
        });
    },
    onChangeCoverage() {
      this.openConfirmationPopup = true;
    },
    rejectCoverageChange() {
      this.openConfirmationPopup = false;
      if (this.selectedCoverageType == 1) {
        this.selectedCoverageType = 0;
      } else {
        this.selectedCoverageType = 1;
      }
    },
    async acceptCoverageChange() {
      let vm = this;
      vm.isLoading = true;
      this.openConfirmationPopup = false;
      let changedCoverage = "";
      if (vm.selectedCoverageType == 1) {
        changedCoverage = "Custom Group";
      } else {
        changedCoverage = "Organization";
      }
      try {
        await vm.$store
          .dispatch("updateFormLevelCoverage", {
            Coverage: changedCoverage,
            Coverage_Id: vm.Coverage_Id,
            formName: "Over Time",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: this.$t("settings.coverageUpdateSuccess"),
            };
            vm.showAlert(snackbarData);
          })
          .catch((err) => {
            if (vm.selectedCoverageType == 1) {
              vm.selectedCoverageType = 0;
            } else {
              vm.selectedCoverageType = 1;
            }
            vm.isLoading = false;
            vm.handleAddUpdateError(err);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.listLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "overtime coverage",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
