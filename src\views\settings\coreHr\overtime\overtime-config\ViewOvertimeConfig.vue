<template>
  <v-overlay
    v-model="showOverlay"
    class="d-flex justify-end"
    @click:outside="$emit('close-form')"
  >
    <v-card height="100vh" :width="componentWidth">
      <v-card-title
        class="d-flex justify-space-between align-center bg-primary"
      >
        <div class="text-h6">
          {{ $t("settings.viewOvertimeConfig") }}
        </div>
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="$emit('close-form')"
          color="white"
        ></v-btn>
      </v-card-title>
      <v-card-text
        v-if="showEmployeesList"
        style="height: calc(100vh - 100px); overflow-y: scroll"
      >
        <div class="">
          <v-btn
            variant="text"
            rounded="lg"
            color="primary"
            @click="showEmployeesList = false"
          >
            <v-icon class="mr-1" size="x-small">fas fa-less-than </v-icon>
            {{ $t("settings.back") }}
          </v-btn>
        </div>
        <EmployeeListCard
          :show-modal="showEmployeesList"
          :modal-title="$t('settings.customGroupEmployees')"
          :employeesList="empListForComponent"
          :selectable="false"
          :translate="true"
          :showFilter="false"
          :showFilterSearch="true"
          :isApplyFilter="true"
          @close-modal="showEmployeesList = false"
        ></EmployeeListCard>
      </v-card-text>
      <v-card-text
        v-else
        style="height: calc(100vh - 100px); overflow-y: scroll"
      >
        <div class="d-flex justify-end mt-3">
          <v-tooltip height="auto">
            <template v-slot:activator="{ props }">
              <v-btn
                v-if="formAccess?.update"
                variant="text"
                color="primary"
                density="compact"
                :class="isCustomGroupToolTip ? 'cursor-not-allow' : ''"
                v-bind="isCustomGroupToolTip ? props : ''"
                @click="
                  isCustomGroupToolTip
                    ? null
                    : $emit('open-edit-form', selectedItem)
                "
              >
                <span class="mr-2">{{ $t("settings.edit") }}</span>
                <v-icon size="15">fas fa-edit</v-icon>
              </v-btn>
            </template>
            <div style="max-width: 200px">
              {{
                $t("settings.overtimeConfigSetTo", {
                  current:
                    coverage === 0
                      ? $t("settings.customGroup")
                      : $t("settings.organization"),
                  conflicting:
                    coverage === 0
                      ? $t("settings.organization")
                      : $t("settings.customGroup"),
                  adjust:
                    coverage === 0
                      ? $t("settings.customGroup")
                      : $t("settings.organization"),
                })
              }}
            </div>
          </v-tooltip>
        </div>
        <v-row class="px-5 py-3">
          <v-col cols="12" sm="6">
            <div class="text-subtitle-1 text-grey-darken-1">
              {{ $t("settings.salaryType") }}
            </div>
            <div class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedItem.Salary_Type) }}
            </div>
          </v-col>
          <v-col cols="12" sm="6">
            <div class="text-subtitle-1 text-grey-darken-1">
              {{ $t("settings.specialWorkDays") }}
            </div>
            <div class="text-subtitle-1 font-weight-regular">
              {{
                checkNullValue(
                  specialWorkDaysList[selectedItem.Special_Work_Days]
                )
              }}
            </div>
          </v-col>
          <v-col cols="12" sm="6" v-if="selectedItem.Custom_Group_Id">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ $t("settings.customGroup") }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedItem.Group_Name) }}
            </p>
          </v-col>
          <v-col v-if="selectedItem.Custom_Group_Id" cols="12" sm="6" lg="6">
            <div v-if="isLoadingCard">
              <v-skeleton-loader
                type="list-item-two-line"
                class="ml-n4 mt-n2"
                width="80%"
              ></v-skeleton-loader>
            </div>
            <div v-else>
              <span class="text-subtitle-1 text-grey-darken-1"
                >{{ $t("settings.employees") }} -
                {{ empListInSelectedGroup.length }}</span
              >
              <div
                v-if="empListInSelectedGroup.length === 0"
                class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
              >
                <v-icon color="warning" size="25"
                  >fas fa-exclamation-triangle</v-icon
                >
                <span
                  v-if="errorInFetchEmployeesList"
                  class="pl-2 text-subtitle-1 font-weight-regular"
                  >{{ $t("settings.errorEmployeeList") }}
                  <a class="text-primary" @click="fetchCustomEmployeesList"
                    >{{ $t("settings.refresh") }}
                  </a>
                </span>
                <span
                  v-else-if="isNoEmployees"
                  class="pl-2 text-subtitle-1 font-weight-regular"
                >
                  {{ $t("settings.noEmployeesFound") }}</span
                >
              </div>
              <div v-else class="d-flex align-center">
                <AvatarOrderedList
                  v-if="empListInSelectedGroup.length > 0"
                  class="mt-1"
                  :ordered-list="empListInSelectedGroup"
                ></AvatarOrderedList>
                <v-btn
                  rounded="lg"
                  color="primary"
                  size="small"
                  class="mt-1"
                  @click="openCustomGroupEmpList()"
                >
                  {{ $t("settings.viewAll") }}
                </v-btn>
              </div>
            </div>
          </v-col>
          <v-col cols="12" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ $t("settings.overtimeType") }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ checkNullValue(selectedItem.Overtime_Type) }}
            </p>
          </v-col>
          <v-col cols="12" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ $t("settings.wageIndex") }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Wage_Factor || selectedItem.Wage_Factor == 0
                  ? selectedItem.Wage_Factor
                  : "-"
              }}
            </p>
          </v-col>
          <v-col cols="12" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ $t("settings.amount") }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                selectedItem.Amount || selectedItem.Amount == 0
                  ? selectedItem.Amount
                  : "-"
              }}
            </p>
          </v-col>
          <v-col cols="12" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ $t("settings.status") }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ selectedItem.Status }}
            </p>
          </v-col>
          <v-col v-if="moreDetailsList.length > 0" cols="12">
            <MoreDetails
              :more-details-list="moreDetailsList"
              :open-close-card="openMoreDetails"
              @on-open-close="openMoreDetails = $event"
            ></MoreDetails>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-overlay>
</template>

<script>
import moment from "moment";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
import EmployeeListCard from "@/components/helper-components/EmployeeListCard.vue";
import { checkNullValue } from "@/helper.js";
export default {
  name: "ViewOvertimeConfig",
  emits: ["close-form", "open-edit-form"],
  components: {
    MoreDetails,
    AvatarOrderedList,
    EmployeeListCard,
  },
  props: {
    showForm: {
      type: Boolean,
      default: false,
    },
    selectedItem: {
      type: Object,
      default: () => ({}),
    },
    coverage: {
      type: Number,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      default: false,
    },
  },
  data: () => ({
    showOverlay: false,
    empListInSelectedGroup: [],
    isLoadingCard: false,
    errorInFetchEmployeesList: false,
    isNoEmployees: false,
    moreDetailsList: [],
    openMoreDetails: false,
    empListForComponent: [],
    showEmployeesList: false,
    specialWorkDaysList: {
      "Extra Work Hours(Weekday)": "Workday",
      Holiday: "Holiday",
      Mandatory: "Rest/Special non-working day",
      "Work Schedule Holiday(Week Off)": "Week Off",
      "Night Work": "Night Work",
      "Week Off And Holiday": "Rest Day + Holiday",
      "Regular Holiday Falling on a scheduled Rest Day":
        "Special Holiday Falling on a scheduled Rest Day",
    },
  }),
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    componentWidth() {
      if (this.windowWidth > 1410) {
        return "40vw";
      } else if (this.windowWidth > 1264 && this.windowWidth < 1410) {
        return "50vw";
      } else if (this.windowWidth < 1264 && this.windowWidth > 810) {
        return "70vw";
      } else {
        return "100vw";
      }
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    isCustomGroupToolTip() {
      return (
        (this.coverage === 0 && this.selectedItem.Custom_Group_Id !== null) ||
        (this.coverage === 1 && this.selectedItem.Custom_Group_Id === null)
      );
    },
  },
  watch: {
    showForm(val) {
      this.showOverlay = val;
    },
    selectedItem: {
      immediate: true,
      handler() {
        this.prefillMoreDetails();
        this.fetchCustomEmployeesList();
        this.showEmployeesList = false;
      },
    },
  },
  mounted() {
    this.showOverlay = this.showForm;
  },
  methods: {
    checkNullValue,
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },
    async fetchCustomEmployeesList() {
      if (this.selectedItem.Custom_Group_Id) {
        let vm = this;
        vm.isLoadingCard = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(vm.selectedItem.Custom_Group_Id),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              if (!employeeDetails || employeeDetails.length === 0) {
                vm.isNoEmployees = true;
                vm.empListInSelectedGroup = [];
              } else {
                for (let i = 0; i < employeeDetails.length; i++) {
                  employeeDetails[i].employee_name =
                    employeeDetails[i]["employeeName"];
                  employeeDetails[i].designation_name =
                    employeeDetails[i]["designationName"];
                  employeeDetails[i].department_name =
                    employeeDetails[i]["departmentName"];
                  employeeDetails[i].user_defined_empid =
                    employeeDetails[i]["userDefinedEmpId"];
                  delete employeeDetails[i].key1;
                }
                vm.empListInSelectedGroup = employeeDetails;
              }
              vm.isLoadingCard = false;
              vm.errorInFetchEmployeesList = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.errorInFetchEmployeesList = true;
            vm.empListInSelectedGroup = [];
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const Added_On = this.formatDate(
          new Date(this.selectedItem.Added_On + ".000Z")
        ),
        Added_By = this.selectedItem.Added_By,
        Updated_By = this.selectedItem.Updated_By,
        Updated_On = this.formatDate(
          new Date(this.selectedItem.Updated_On + ".000Z")
        );
      if (Added_On && Added_By) {
        this.moreDetailsList.push({
          actionDate: Added_On,
          actionBy: Added_By,
          notDisplayby: 1,
          text: this.$t("settings.addedBy"),
        });
      }
      if (Updated_By && Updated_On) {
        this.moreDetailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By,
          notDisplayby: 1,
          text: this.$t("settings.updatedBy"),
        });
      }
    },
  },
};
</script>
