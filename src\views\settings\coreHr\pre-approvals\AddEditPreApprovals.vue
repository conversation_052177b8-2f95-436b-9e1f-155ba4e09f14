<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div
        v-if="!showEmployeesList"
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold"> Pre-approvals</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-tooltip v-model="showToolTip" location="top">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="isFormDirty ? '' : props"
                  rounded="lg"
                  :color="!isFormDirty ? 'primary' : 'primary'"
                  variant="elevated"
                  class="mb-2"
                  type="submit"
                  @click="savePreApprovalDetails"
                  ><span class="px-2">Save</span></v-btn
                >
              </template>
              <div v-if="!isFormDirty">There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" color="primary" class="mr-1">
            fas fa-times
          </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="overflow: scroll"
        :style="
          showEmployeesList
            ? 'height: calc(100vh - 200px)'
            : 'height: calc(100vh - 240px)'
        "
      >
        <v-card-text v-if="!showEmployeesList">
          <NotesCard
            v-if="coverage === 'Custom Group'"
            notes="Please note: If an employee is a member of several custom groups, the system will only utilize the settings from a single group."
            class="mb-4"
          ></NotesCard>
          <NotesCard
            v-if="
              preApprovalType &&
              preApprovalType.toLowerCase() === 'work from home'
            "
            notes="Work from home pre-approval request will be considered only for working days."
            :class="coverage === 'Custom Group' ? 'mb-4 mt-4' : 'mb-4'"
          ></NotesCard>
          <NotesCard
            v-if="
              preApprovalType && preApprovalType.toLowerCase() === 'on duty'
            "
            notes="On duty pre-approval request will be considered based on the type of day selected."
            :class="coverage === 'Custom Group' ? 'mb-4 mt-4' : 'mb-4'"
          ></NotesCard>

          <v-form ref="preApprovalForm">
            <v-row>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="preApprovalTypes"
                  label="Pre-approval Type"
                  :isRequired="true"
                  :is-loading="preApprovalTypeLoading"
                  :is-auto-complete="false"
                  :itemSelected="preApprovalType"
                  @selected-item="onChangePreApprovalType($event)"
                  :rules="[required('Pre-approval Type', preApprovalType)]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>
              <v-col
                v-if="
                  preApprovalType &&
                  (preApprovalType.toLowerCase() === 'on duty' ||
                    preApprovalType.toLowerCase() === 'overtime work')
                "
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="['Business Working Day', 'Week Off', 'Holiday']"
                  label="Type of Day"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :itemSelected="typeOfDay"
                  @selected-item="onChangeIsFormDirty($event, 'typeOfDay')"
                  :is-auto-complete="true"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    closableChips: true,
                    clearable: true,
                  }"
                  :rules="
                    preApprovalType &&
                    preApprovalType.toLowerCase() === 'on duty'
                      ? [requiredArray('Type of Day', typeOfDay)]
                      : [true]
                  "
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="v-label ml-2 mb-1">Coverage</div>

                <div>
                  <v-btn-toggle
                    v-model="selectedCoverageType"
                    rounded="lg"
                    mandatory
                    density="comfortable"
                    class="custom-box-shadow"
                    @update:modelValue="onChangeCoverage(selectedCoverageType)"
                  >
                    <v-btn
                      class="text-start text-wrap"
                      color="primary"
                      style="background-color: white; color: black"
                      >Organization</v-btn
                    >
                    <v-btn
                      class="text-start text-wrap"
                      color="primary"
                      style="background-color: white; color: black"
                      >Custom Group</v-btn
                    ></v-btn-toggle
                  >
                </div>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="coverage == 'Custom Group'"
              >
                <div class="d-flex">
                  <CustomSelect
                    :items="customGroupList"
                    label="Custom Group"
                    :isRequired="true"
                    :itemSelected="customGroupId"
                    @selected-item="
                      onChangeIsFormDirty($event, 'customGroupId')
                    "
                    item-title="Custom_Group_Name"
                    item-value="Custom_Group_Id"
                    :is-auto-complete="true"
                    :is-loading="customGroupLoading"
                    :disabled="!preApprovalType && customGroupList.length == 0"
                    :rules="[required('Custom Group', customGroupId)]"
                    style="max-width: 300px"
                  >
                  </CustomSelect>
                  <v-btn
                    rounded="lg"
                    variant="text"
                    class="ml-2 mt-2 primary"
                    :size="isMobileView ? 'small' : 'default'"
                    :class="!preApprovalType ? 'cursor-not-allow' : ''"
                    @click="
                      !preApprovalType
                        ? null
                        : onChangePreApprovalType(
                            preApprovalType,
                            'customGroup'
                          )
                    "
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
                <div class="mb-1">
                  <v-btn
                    color="primary"
                    variant="text"
                    size="small"
                    :href="baseUrl + 'in/core-hr/custom-employee-groups'"
                    target="_blank"
                  >
                    <v-icon size="14" class="mr-1">fas fa-plus</v-icon> Add
                    Custom Group
                  </v-btn>
                </div>
                <NotesCard
                  v-if="!preApprovalType && coverage === 'Custom Group'"
                  notes="Please choose a pre-approval type to view the relevant custom group list."
                ></NotesCard>
              </v-col>
              <v-col
                v-if="customGroupId"
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div v-if="isLoadingCard">
                  <v-skeleton-loader
                    type="list-item-two-line"
                    class="ml-n4 mt-n2"
                    width="80%"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >Employees - {{ empListInSelectedGroup.length }}</span
                  >
                  <div
                    v-if="empListInSelectedGroup.length === 0"
                    class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                  >
                    <v-icon color="warning" size="25"
                      >fas fa-exclamation-triangle</v-icon
                    >
                    <span
                      v-if="errorInFetchEmployeesList"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                      >Something went wrong while fetching the employees list.
                      Please try again.
                      <a class="text-primary" @click="fetchCustomEmployeesList"
                        >Refresh
                      </a>
                    </span>
                    <span
                      v-else-if="isNoEmployees"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                    >
                      It seems like there are no employees associated with the
                      selected custom group. Please add some employees under the
                      selected group or try choosing an another group.</span
                    >
                  </div>
                  <div v-else class="d-flex align-center">
                    <AvatarOrderedList
                      v-if="empListInSelectedGroup.length > 0"
                      class="mt-2"
                      :ordered-list="empListInSelectedGroup"
                    ></AvatarOrderedList>
                    <v-btn
                      rounded="lg"
                      color="primary"
                      size="small"
                      class="mt-2"
                      @click="openCustomGroupEmpList()"
                    >
                      View All
                    </v-btn>
                  </div>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="[
                    'Weekly',
                    'Monthly',
                    'Quarterly',
                    'Half yearly',
                    'Yearly',
                  ]"
                  label="Period"
                  :isRequired="true"
                  :itemSelected="period"
                  @selected-item="onChangeIsFormDirty($event, 'period')"
                  :is-auto-complete="true"
                  :rules="[required('Period', period)]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model.number="noOfPreApprovalRequest"
                  type="number"
                  :min="1"
                  :max="maxLimit"
                  variant="solo"
                  :rules="[
                    minMaxNumberValidation(
                      'No Of Pre-approvals Requests',
                      parseInt(noOfPreApprovalRequest),
                      1,
                      maxLimit
                    ),
                    numericRequiredValidation(
                      'No of Pre-approvals Requests',
                      noOfPreApprovalRequest
                    ),
                    numericValidation(
                      'No of Pre-approvals Requests',
                      noOfPreApprovalRequest
                    ),
                  ]"
                  :disabled="!period"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    <span>No of Pre-approvals Requests</span>
                    <span class="ml-1" style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model="maxDaysAllowedPerRequest"
                  suffix="day(s)"
                  variant="solo"
                  type="number"
                  :disabled="preApprovalType?.toLowerCase() === 'overtime work'"
                  :rules="[
                    maxDaysAllowedPerRequest
                      ? validateDecimal(maxDaysAllowedPerRequest)
                      : true,
                    minMaxNumberValidation(
                      'Max Days Allowed Per Request',
                      parseFloat(maxDaysAllowedPerRequest),
                      0.5,
                      365
                    ),
                  ]"
                  @update:model-value="isFormDirty = true"
                  style="max-width: 300px"
                  ><template v-slot:label>
                    <span>Max Days Allowed Per Request</span>
                  </template></v-text-field
                >
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div class="d-flex">
                  <CustomSelect
                    :items="workflowApprovalList"
                    label="Workflow Approval"
                    :isRequired="true"
                    item-title="Workflow_Name"
                    item-value="Workflow_Id"
                    :is-loading="workflowApprovalLoading"
                    :itemSelected="workflowName"
                    :is-auto-complete="true"
                    :rules="[required('Workflow Approval', workflowName)]"
                    @selected-item="onChangeIsFormDirty($event, 'workflowName')"
                    style="max-width: 300px"
                    listWidth="max-width: 300px !important"
                  ></CustomSelect>

                  <v-btn
                    rounded="lg"
                    variant="text"
                    class="ml-2 mt-2 primary"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="
                      onChangePreApprovalType(preApprovalType, 'workflow')
                    "
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
                <div class="mb-1">
                  <v-btn
                    class="ml-n2"
                    color="primary"
                    variant="text"
                    size="small"
                    :href="baseUrl + 'workflow/workflow-builder'"
                  >
                    <v-icon class="mr-1" size="14">fas fa-plus</v-icon> Add
                    Workflow</v-btn
                  >
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model.number="advanceNotificationDays"
                  type="number"
                  suffix="day(s)"
                  variant="solo"
                  :min="0"
                  :max="30"
                  :rules="[
                    minMaxNumberValidation(
                      'Advance Notification',
                      parseInt(advanceNotificationDays),
                      0,
                      30
                    ),
                    numericRequiredValidation(
                      'Advance Notification',
                      advanceNotificationDays
                    ),
                    numericValidation(
                      'Advance Notification',
                      advanceNotificationDays
                    ),
                  ]"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    <span>Advance Notification</span>
                    <span class="ml-1" style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="
                  preApprovalType &&
                  preApprovalType.toLowerCase() == 'work from home'
                "
              >
                <div class="d-flex">
                  <span class="v-label pr-3 pb-5">Restrict Sandwich</span>
                  <v-switch
                    color="primary"
                    class="ml-2"
                    v-model="restrictSandwich"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="checkRestrictSandwichChange"
                  ></v-switch>
                </div>
              </v-col>

              <v-col
                v-if="
                  restrictSandwich === 'Yes' &&
                  preApprovalType &&
                  preApprovalType.toLowerCase() == 'work from home'
                "
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="['Week Off', 'Holiday']"
                  label="Restrict Sandwich For"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :itemSelected="restrictSandwichFor"
                  @selected-item="
                    onChangeIsFormDirty($event, 'restrictSandwichFor')
                  "
                  :is-auto-complete="true"
                  :selectProperties="{
                    multiple: true,
                    chips: true,
                    closableChips: true,
                    clearable: true,
                  }"
                  :rules="
                    restrictSandwich == 'Yes'
                      ? [required('Restrict Sandwich For', restrictSandwichFor)]
                      : [true]
                  "
                  style="max-width: 300px"
                ></CustomSelect>
                <div
                  v-if="isEdit && restrictSandwichForErrorMsg"
                  class="text-caption ml-2"
                  style="color: #b00020; margin-top: -20px !important"
                >
                  {{
                    restrictSandwichFor && restrictSandwichFor.length == 0
                      ? restrictSandwichForErrorMsg
                      : ""
                  }}
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="
                  (preApprovalType &&
                    preApprovalType.toLowerCase() == 'work from home') ||
                  preApprovalType?.toLowerCase() === 'on duty'
                "
              >
                <div class="d-flex">
                  <span class="v-label pr-3 pb-5">Document Submission</span>
                  <v-switch
                    color="primary"
                    class="ml-2"
                    v-model="documentUpload"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="onchangeDocumentUpload"
                  ></v-switch>
                </div>
              </v-col>

              <v-col
                v-if="
                  documentUpload === 'Yes' &&
                  (preApprovalType?.toLowerCase() == 'work from home' ||
                    preApprovalType?.toLowerCase() === 'on duty')
                "
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <v-text-field
                  v-model.number="maxDaysForDocumentUpload"
                  type="number"
                  suffix="day(s)"
                  variant="solo"
                  :min="0"
                  :max="30"
                  :rules="[
                    numericRequiredValidation(
                      'Maximum Days For Document Upload',
                      maxDaysForDocumentUpload
                    ),
                    minMaxNumberValidation(
                      'Maximum Days For Document Upload',
                      parseFloat(maxDaysForDocumentUpload),
                      0.5,
                      365
                    ),
                  ]"
                  style="max-width: 300px"
                  @update:model-value="isFormDirty = true"
                >
                  <template v-slot:label>
                    <div class="d-flex align-center">
                      <span class="text-truncate"
                        >Threshold limit for document submission (in days)</span
                      >
                      <span class="ml-1" style="color: red">*</span>
                    </div>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="isEdit"
              >
                <div class="v-label ml-2 mb-2">Status</div>
                <AppToggleButton
                  button-active-text="Active"
                  button-inactive-text="InActive"
                  button-active-color="#7de272"
                  button-inactive-color="red"
                  id-value="gab-analysis-based-on"
                  :current-value="status === 'Active' ? true : false"
                  @chosen-value="onChangeStatus($event)"
                ></AppToggleButton>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <div v-if="showEmployeesList">
          <div class="d-flex" :class="isMobileView ? 'mb-2' : 'mt-n6 mb-2'">
            <v-btn
              rounded="lg"
              color="primary"
              @click="showEmployeesList = false"
            >
              <v-icon class="mr-1" size="x-small">fas fa-less-than </v-icon>
              Back
            </v-btn>
          </div>
          <EmployeeListCard
            :show-modal="showEmployeesList"
            modal-title="Custom Group Employee(s)"
            :employeesList="empListForComponent"
            :selectable="false"
            :showFilter="false"
            :showFilterSearch="true"
            :isApplyFilter="true"
            @close-modal="showEmployeesList = false"
          ></EmployeeListCard>
        </div>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="abortClose()"
      @accept-modal="acceptClose()"
    >
    </AppWarningModal>
    <AppLoading v-if="isLoadingDeatils"></AppLoading>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { getErrorCodesWithValidation } from "@/helper.js";
import { ADD_UPDATE_PREAPPROVAL_SETTINGS } from "@/graphql/settings/core-hr/preApprovalQueries.js";
import { LIST_WORK_PLACES } from "@/graphql/organisation/employeetype/employeeTypeQueries";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
import EmployeeListCard from "@/components/helper-components/EmployeeListCard.vue";
export default {
  name: "AddEditPreApprovals",
  mixins: [validationRules],
  components: {
    CustomSelect,
    NotesCard,
    AvatarOrderedList,
    EmployeeListCard,
  },
  props: {
    selectedItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
    editedPreApprovalDetails: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isListEmpty: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isMounted: false,
      isLoadingDeatils: false,
      customGroupCallCount: 0,
      selectedCoverageType: 0,
      customGroupId: "",
      customGroupRetrieved: "",
      workflowNameRetrieved: "",
      openConfirmationPopup: false,
      advanceNotificationDays: null,
      maxDaysForDocumentUpload: null,
      preApprovalType: "",
      status: "Active",
      coverage: "Organization",
      restrictSandwich: "No",
      period: "",
      noOfPreApprovalRequest: null,
      restrictSandwichFor: null,
      workflowName: "",
      workflowApprovalList: [],
      workflowApprovalLoading: false,
      customGroupList: [],
      customGroupLoading: false,
      isFormDirty: false,
      showToolTip: false,
      restrictSandwichForErrorMsg: "",
      documentUpload: "No",
      empListForComponent: [],
      empListInSelectedGroup: [],
      showEmployeesList: false,
      errorInFetchEmployeesList: false,
      isNoEmployees: false,
      isLoadingCard: false,
      preApprovalTypes: [
        "Work during week off",
        "Work during holiday",
        "Overtime Work",
      ],
      preApprovalTypeLoading: false,
      typeOfDay: null,
      maxDaysAllowedPerRequest: null,
    };
  },
  watch: {
    customGroupId(val) {
      if (!val) {
        this.empListInSelectedGroup = [];
      } else {
        this.fetchCustomEmployeesList();
      }
    },
    // Commenting for future use
    // preApprovalType(val) {
    //   if (val) {
    //     this.updateTypeOfDay();
    //   }
    // }
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    maxLimit() {
      switch (this.period) {
        case "Weekly":
          return 7;
        case "Monthly":
          return 31;
        case "Quarterly":
          return 93;
        case "Half yearly":
          return 186;
        case "Yearly":
          return 365;
        default:
          return 0;
      }
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  mounted() {
    this.retrievePreApprovalTypes();
    if (this.isEdit) {
      const {
        preApprovalConfigurationId,
        workflowId,
        preApprovalType,
        coverage,
        customGroupId,
        advanceNotificationDays,
        maxDaysForDocumentUpload,
        documentUpload,
        restrictSandwich,
        status,
        period,
        noOfPreApprovalRequest,
        maxDaysAllowedPerRequest,
        restrictSandwichFor,
        typeOfDay,
      } = this.editedPreApprovalDetails;
      this.preApprovalConfigurationId = preApprovalConfigurationId
        ? preApprovalConfigurationId
        : 0;
      this.workflowNameRetrieved = workflowId ? workflowId : "";
      this.customGroupRetrieved = customGroupId ? customGroupId : "";
      this.preApprovalType = preApprovalType ? preApprovalType : "";
      if (this.preApprovalType) {
        this.onChangePreApprovalType(this.preApprovalType, "edit");
      }
      this.coverage = coverage ? coverage : "Organization";

      this.status = status ? status : "Active";
      this.advanceNotificationDays = advanceNotificationDays
        ? advanceNotificationDays
        : 0;
      this.maxDaysForDocumentUpload = maxDaysForDocumentUpload
        ? parseFloat(maxDaysForDocumentUpload)
        : null;
      this.documentUpload = documentUpload ? documentUpload : "No";
      this.period = period ? period : "";
      this.restrictSandwich = restrictSandwich ? restrictSandwich : "No";
      this.restrictSandwichFor = restrictSandwichFor
        ? JSON.parse(this.editedPreApprovalDetails.restrictSandwichFor)
        : null;
      this.restrictSandwichFor =
        this.restrictSandwichFor && this.restrictSandwichFor.length > 0
          ? this.restrictSandwichFor
          : null;
      this.noOfPreApprovalRequest = noOfPreApprovalRequest
        ? noOfPreApprovalRequest
        : null;
      this.maxDaysAllowedPerRequest = maxDaysAllowedPerRequest
        ? parseFloat(maxDaysAllowedPerRequest)
        : null;
      this.typeOfDay = typeOfDay ? JSON.parse(typeOfDay) : null;
      if (this.coverage == "Organization") {
        this.selectedCoverageType = 0;
      } else {
        this.selectedCoverageType = 1;
      }
    }
    this.isFormDirty = false;
    this.isMounted = true;
  },
  methods: {
    //Form the type of day in order - [Business Working Day, Holiday, Week Off]
    formTypeOfDay(typeOfDay = []) {
      const preApprovalType = this.preApprovalType?.toLowerCase();

      if (preApprovalType && preApprovalType.length === 0) {
        throw new Error("Pre Approval Type is required");
      }

      if (
        preApprovalType === "on duty" ||
        preApprovalType === "overtime work"
      ) {
        if (!Array.isArray(typeOfDay) || typeOfDay.length === 0) {
          throw new Error("Type of Day is required");
        }
        const order = ["Business Working Day", "Holiday", "Week Off"];
        const typeOfDayArray = order.filter((day) => typeOfDay.includes(day));
        return JSON.stringify(typeOfDayArray);
      } else if (preApprovalType === "work from home") {
        return JSON.stringify(["Business Working Day"]);
      } else if (preApprovalType === "work during holiday") {
        return JSON.stringify(["Holiday"]);
      } else if (preApprovalType === "work during week off") {
        return JSON.stringify(["Week Off"]);
      } else {
        return null;
      }
    },

    // Commenting for future use
    // updateTypeOfDay() {
    //   if(this.preApprovalType && this.preApprovalType.toLowerCase() === 'work from home') {
    //     this.typeOfDay = ["Business Working Day"];
    //   } else if(this.preApprovalType && this.preApprovalType.toLowerCase() === 'work during holiday') {
    //     this.typeOfDay = ["Holiday"];
    //   } else if(this.preApprovalType && this.preApprovalType.toLowerCase() === 'work during week off') {
    //     this.typeOfDay = ["Week Off"];
    //   }else{
    //     this.typeOfDay = [];
    //   }
    // },
    retrievePreApprovalTypes() {
      let vm = this;
      vm.preApprovalTypeLoading = true;
      vm.$apollo
        .query({
          query: LIST_WORK_PLACES,
          client: "apolloClientAZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listWorkPlaces) {
            let responseData = JSON.parse(
              response.data.listWorkPlaces.workPlaces
            );
            if (responseData?.length) {
              //Filter the responseData where Pre_Approval is Yes
              let applicablePreApprovals = responseData
                .filter((item) => {
                  return item.Pre_Approval == "Yes";
                })
                .map((item) => item.Work_Place);
              vm.preApprovalTypes = applicablePreApprovals.length
                ? vm.preApprovalTypes.concat(applicablePreApprovals)
                : vm.preApprovalTypes;
            }
          }
          vm.preApprovalTypeLoading = false;
        })
        .catch(() => {
          vm.preApprovalTypeLoading = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
      this.showEmployeesList = false;
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      if (this.isEdit) {
        this.$emit("close-edit-form");
      } else {
        this.$emit("close-split-view");
      }
    },
    onChangeIsFormDirty(val, field) {
      if (field == "customGroupId") {
        this.customGroupId = val;
      } else if (field == "period") {
        this.period = val;
      } else if (field == "workflowName") {
        this.workflowName = val;
      } else if (field == "restrictSandwichFor") {
        this.restrictSandwichFor = val && val.length > 0 ? val : null;
      } else if (field == "typeOfDay") {
        this.typeOfDay = val;
      }
      this.isFormDirty = true;
    },
    async savePreApprovalDetails() {
      const { valid } = await this.$refs.preApprovalForm.validate();
      if (valid) {
        if (this.isEdit) {
          if (this.restrictSandwich == "Yes") {
            if (this.isFormDirty && !this.restrictSandwichFor) {
              this.restrictSandwichForErrorMsg =
                "Restrict Sandwich For is required";
              this.isFormDirty = false;
            } else if (
              this.restrictSandwichFor &&
              this.restrictSandwichFor.length == 0
            ) {
              this.restrictSandwichForErrorMsg =
                "Restrict Sandwich For is required";
            } else {
              if (this.isFormDirty) {
                this.addUpdatePreApprovals();
                this.isFormDirty = false;
              } else {
                if (this.isEdit) {
                  this.$emit("close-edit-form");
                } else {
                  this.$emit("close-split-view");
                }
              }
            }
          } else {
            if (this.isFormDirty) {
              this.addUpdatePreApprovals();
              this.isFormDirty = false;
            } else {
              if (this.isEdit) {
                this.$emit("close-edit-form");
              } else {
                this.$emit("close-split-view");
              }
            }
          }
        } else if (this.isFormDirty) {
          this.addUpdatePreApprovals();
          this.isFormDirty = false;
        } else {
          if (this.isEdit) {
            this.$emit("close-edit-form");
          } else {
            this.$emit("close-split-view");
          }
        }
      } else {
        this.restrictSandwichForErrorMsg = "Restrict Sandwich For is required";
      }
    },
    // change the mode of performance management
    onChangeStatus(value) {
      this.status = value[1] ? "Active" : "InActive";
      this.isFormDirty = true;
    },
    onChangeCoverage(value) {
      if (value == 1) {
        this.coverage = "Custom Group";
      } else {
        this.coverage = "Organization";
      }
      this.isFormDirty = true;
    },

    checkRestrictSandwichChange() {
      if (this.restrictSandwich == "No") {
        this.restrictSandwichFor = null;
      }
      this.isFormDirty = true;
    },
    onchangeDocumentUpload() {
      if (this.documentUpload == "No") {
        this.maxDaysForDocumentUpload = null;
      }
      this.isFormDirty = true;
    },
    closeForm() {
      if (this.isEdit) {
        this.$emit("close-edit-form");
      } else {
        this.$emit("close-split-view");
      }
    },
    onChangePreApprovalType(type, action) {
      this.preApprovalType = type;
      this.resetModelValues();
      let selectedFormId;
      let selectedFormName;
      if (type != null) {
        if (type.toLowerCase() === "work from home") {
          selectedFormId = 244;
          selectedFormName = "Work from home (pre-approval)";
        } else if (type.toLowerCase() === "work during week off") {
          selectedFormId = 245;
          selectedFormName = "Work during week off (pre-approval)";
        } else if (type.toLowerCase() === "work during holiday") {
          selectedFormId = 246;
          selectedFormName = "Work during holiday (pre-approval)";
        } else if (type.toLowerCase() === "overtime work") {
          selectedFormId = 366;
          selectedFormName = "Overtime Work";
          this.maxDaysAllowedPerRequest = 1;
        } else {
          selectedFormId = 301;
          selectedFormName = "On Duty (pre-approval)";
        }
      }
      if (action == "customGroup") {
        if (selectedFormName) {
          this.retrieveCustomGroups(selectedFormName, action);
        }
      } else if (action == "workflow") {
        if (selectedFormName) {
          this.retrieveWorkflowDetails(selectedFormId, selectedFormName);
        }
      } else {
        if (action != "edit") {
          this.workflowName = "";
          this.customGroupId = "";
        }
        if (selectedFormName) {
          this.retrieveCustomGroups(selectedFormName, action);
        }
        if (selectedFormId) {
          this.retrieveWorkflowDetails(selectedFormId, action);
        }
      }
      if (action !== "edit") {
        this.isFormDirty = true;
      }
    },
    resetModelValues() {
      this.documentUpload = "No";
      this.maxDaysForDocumentUpload = null;
      this.advanceNotificationDays = null;
      this.noOfPreApprovalRequest = null;
      this.period = "";
      this.restrictSandwich = "No";
      this.customGroupId = "";
    },

    // retrieve custom group based on form name
    async retrieveCustomGroups(formName, action) {
      this.customGroupLoading = true;
      this.customGroupList = [];

      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: formName,
          preApprovalId: this.preApprovalConfigurationId
            ? parseInt(this.preApprovalConfigurationId)
            : 0,
        })
        .then((groupList) => {
          if (groupList && groupList.length > 0) {
            this.customGroupList = groupList;
            if (action == "edit") {
              this.customGroupId = this.customGroupRetrieved;
            }
          }
          this.customGroupLoading = false;
        })
        .catch(() => {
          this.customGroupLoading = false;
        });
    },
    async retrieveWorkflowDetails(formId, action) {
      this.workflowApprovalLoading = true;
      this.workflowApprovalList = [];
      await this.$store
        .dispatch("listWorkflowDetailsBasedOnFormName", {
          formName: "Pre Approvals",
          formId: formId,
        })
        .then((workflowList) => {
          if (workflowList && workflowList.length > 0) {
            this.workflowApprovalList = workflowList;
            if (action == "edit") {
              this.workflowName = this.workflowNameRetrieved;
            }
          }
          this.workflowApprovalLoading = false;
        })
        .catch(() => {
          this.workflowApprovalLoading = false;
        });
    },
    addUpdatePreApprovals() {
      let vm = this;
      vm.isLoadingDeatils = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_PREAPPROVAL_SETTINGS,
            variables: {
              preApprovalConfigurationId: vm.preApprovalConfigurationId
                ? parseInt(vm.preApprovalConfigurationId)
                : 0,
              preApprovalType: vm.preApprovalType
                ? vm.preApprovalType === "Work From Home"
                  ? "Work from home"
                  : vm.preApprovalType
                : "",
              period: vm.period ? vm.period : "",
              restrictSandwich: vm.restrictSandwich
                ? vm.restrictSandwich
                : "No",
              restrictSandwichFor: vm.restrictSandwichFor
                ? JSON.stringify(vm.restrictSandwichFor)
                : "",
              coverage: vm.coverage ? vm.coverage : "Organization",
              noOfPreApprovalRequest: vm.noOfPreApprovalRequest
                ? parseInt(vm.noOfPreApprovalRequest)
                : 0,
              documentUpload: vm.documentUpload ? vm.documentUpload : "No",
              advanceNotificationDays: vm.advanceNotificationDays
                ? parseInt(vm.advanceNotificationDays)
                : 0,
              maxDaysForDocumentUpload: vm.maxDaysForDocumentUpload
                ? parseFloat(vm.maxDaysForDocumentUpload)
                : null,
              workflowId: vm.workflowName ? vm.workflowName : 0,
              customGroupId: vm.customGroupId ? vm.customGroupId : 0,
              typeOfDay: vm.formTypeOfDay(vm.typeOfDay),
              status: vm.status === "InActive" ? "InActive" : "Active",
              maxDaysAllowedPerRequest: vm.maxDaysAllowedPerRequest
                ? parseFloat(vm.maxDaysAllowedPerRequest)
                : null,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoadingDeatils = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.isEdit
                ? "Pre-approvals updated successfully."
                : "Pre-approvals added successfully.",
            };
            if (this.isEdit) {
              this.$emit("save-edited-data");
              this.$emit("close-split-view");
            } else {
              this.$emit("add-data");
              this.$emit("close-split-view");
            }

            vm.showAlert(snackbarData);
          })
          .catch((addEditError) => {
            vm.handlePreApproveAddUpdateError(addEditError);
          });
      } catch (err) {
        vm.handlePreApproveAddUpdateError(err);
      }
    },
    // open employees list to view the employees when the coverage is custom-group
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },
    async fetchCustomEmployeesList() {
      if (this.customGroupId) {
        let vm = this;
        vm.isLoadingCard = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(this.customGroupId),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              if (!employeeDetails || employeeDetails.length === 0) {
                vm.isNoEmployees = true;
                vm.empListInSelectedGroup = [];
              } else {
                for (let i = 0; i < employeeDetails.length; i++) {
                  employeeDetails[i].employee_name =
                    employeeDetails[i]["employeeName"];
                  employeeDetails[i].designation_name =
                    employeeDetails[i]["designationName"];
                  employeeDetails[i].department_name =
                    employeeDetails[i]["departmentName"];
                  employeeDetails[i].user_defined_empid =
                    employeeDetails[i]["userDefinedEmpId"];
                  delete employeeDetails[i].key1;
                }
                vm.empListInSelectedGroup = employeeDetails;
              }
              vm.isLoadingCard = false;
              vm.errorInFetchEmployeesList = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.errorInFetchEmployeesList = true;
            vm.empListInSelectedGroup = [];
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },
    validateDecimal(value) {
      if (!value) return true; // Allow empty values if not required
      const regex = /^\d+(\.\d{1,2})?$/; // Allows up to 2 decimal places
      return regex.test(value)
        ? true
        : "Provide Amount up to 2 decimal places.";
    },
    handlePreApproveAddUpdateError(err = "") {
      this.isLoadingDeatils = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture
        var errorCode = getErrorCodesWithValidation(err);
        var validationErrors = errorCode[1];
        var validationMessages = "";
        var errCode = "";
        switch (errorCode[0]) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "CHR0038": // technical errors
            snackbarData.message =
              "There are currently some difficulty with adding/updating the pre-approval settings. Please try again later.";
            break;

          case "CHR0039":
            snackbarData.message =
              "Error while updating the pre-approval settings. Please contact the system admin.";
            break;
          case "CHR0040":
            snackbarData.message =
              "The coverage is already exist for the selected pre-approval type.";
            break;
          case "CHR0041":
            snackbarData.message =
              "Error while getting the outstanding pre-approvals count.";
            break;
          case "CHR0042":
            snackbarData.message =
              "You are not allowed to update the pre-approval settings if there are any pending pre-approval requests awaiting approval.";
            break;
          case "CHR0045":
            snackbarData.message =
              "The organization coverage settings already exists for the selected pre-approvals type";
            break;
          case "CHR0046":
            snackbarData.message =
              "Inactivate the organization coverage settings to add custom group-level settings";
            break;
          case "CHR0047":
            snackbarData.message =
              "Inactivate the custom group coverage settings to add an organization level settings";
            break;
          case "CHR0048":
            snackbarData.message =
              "The settings already added for the selected custom group";
            break;
          case "CHR0049":
            snackbarData.message = "Error while validating the coverage.";
            break;
          case "_DB0102": // update access denied
            snackbarData.message =
              "Sorry, you don't have update access rights. Please contact the HR administrator.";
            break;
          case "_DB0101": // add access denied
            snackbarData.message =
              "Sorry, you don't have add access rights. Please contact the HR administrator.";
            break;
          case "_UH0001": // unhandled error
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0002": // Error while checking the employee access rights
          case "_DB0104": // While check access rights form not found
          case "BAD_USER_INPUT":
            // add all the backend validation error messages as single sentence to present it to the users
            if (validationErrors) {
              for (errCode in validationErrors) {
                // IVE0385 - message: "Please ensure that the threshold limit for document submission falls within the range of 0.5 to 365".
                //
                if (errCode === "IVE0385") {
                  validationMessages =
                    validationMessages + " " + validationErrors[errCode];
                }
              }
            }
            if (validationMessages) {
              snackbarData.message = validationMessages;
            }
            // other validation errors are not handled by users. So as of now it was considers as common error.
            else {
              // IVE0083 - Please provide a valid Employee Id.
              snackbarData.message =
                "Something went wrong while adding/updating pre-approval-request. If you continue to see this issue please contact the platform administrator.";
            }

            break;
          default:
            snackbarData.message =
              "Something went wrong while configuring pre-approvals. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while configuring pre-approvals. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
  },
};
</script>
<style scoped>
.custom-box-shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}
</style>
