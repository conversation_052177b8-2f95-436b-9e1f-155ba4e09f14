<template>
  <div>
    <!-- AppTopBar component is reused here which is already present -->
    <div>
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="preApprovalData.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <!-- this the component which has search component and based on prop isFilter filter component is also rendered -->

              <EmployeeDefaultFilterMenu
                v-if="preApprovalData.length > 0"
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <!-- Tab body starts from here -->

    <v-container fluid class="pre-approvals-container">
      <!-- current-tab-item is a data property which stores which tab to be shown on click here it has two value tab-0 and tab-1 -->

      <v-window v-model="currentTabItem" v-if="preApprovalsFormAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchPreApprovalList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="preApprovalData.length === 0"
            key="no-results-screen"
          >
            <template #contentSlot>
              <div v-if="!AddEditButtonClicked" style="max-width: 80%">
                <v-row
                  v-if="!isLoading"
                  style="background: white"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col cols="12">
                    <NotesCard
                      notes="Pre-approvals configuration empowers employees to request prior approval for working during weekoffs, holidays, and remote work. This functionality ensures compliance with organizational policies and transparency in work arrangements. With configurable business rules, organizations can define eligibility criteria, restrict sandwich pre-approvals, and limit the number of days for which approvals can be requested."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="Employees can submit pre-approval requests, seeking official authorization before engaging in specific activities. application's flexible configuration allows organizations to define which employees are eligible for pre-approvals, ensuring the option is available to the appropriate individuals. By setting restrictions on sandwich pre-approvals and limiting requested days, organizations can maintain a balanced work schedule and align employee requests with operational requirements. Overall, pre-approvals configuration streamlines the process, ensures compliance, and provides transparency for work arrangements within defined parameters."
                      backgroundColor="transparent"
                      class="mb-2"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="preApprovalsFormAccess.add"
                      variant="elevated"
                      class="ml-4 mt-1 primary"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="EmptyPageAddEditForm()"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      Configure Pre-approvals
                    </v-btn>
                    <v-btn
                      rounded="lg"
                      class="ml-2 mt-1 primary"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="fetchPreApprovalsDetails()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div>
              <!-- The div below contains all action buttons -->
              <div
                v-if="!viewButtonClicked"
                class="d-flex align-center my-3"
                :class="isMobileView ? 'justify-center' : ' justify-end'"
              >
                <v-btn
                  prepend-icon="fas fa-plus"
                  color="primary rounded-lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="openAddForm"
                  v-if="preApprovalsFormAccess.add"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  Add Configuration
                </v-btn>
                <v-btn
                  rounded="lg"
                  class="ml-2 mt-1 primary"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="fetchPreApprovalsDetails()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </div>

              <v-row>
                <v-col :cols="viewButtonClicked && !isMobileView ? 5 : 12">
                  <v-data-table
                    v-model="selectedData"
                    :headers="headers"
                    :items="preApprovalData"
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        preApprovalData
                      )
                    "
                    class="elevation-1"
                    :search="searchValue"
                    style="box-shadow: none !important"
                  >
                    <template v-slot:item="{ item, index }">
                      <tr
                        style="z-index: 200"
                        class="data-table-tr bg-white cursor-pointer"
                        @click="openViewForm(item, index)"
                        :class="[
                          isMobileView
                            ? ' v-data-table__mobile-table-row ma-0 mt-2'
                            : '',
                        ]"
                      >
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5 font-weight-small'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Pre-Approval Type
                          </div>
                          <section
                            style="height: 3em"
                            class="d-flex align-center"
                          >
                            <div class="d-flex align-center">
                              <div
                                v-if="
                                  viewButtonClicked &&
                                  !isMobileView &&
                                  selectedItem &&
                                  selectedItem.preApprovalConfigurationId ===
                                    item.preApprovalConfigurationId
                                "
                                class="data-table-side-border d-flex"
                                style="height: 3em"
                              ></div>
                            </div>
                            <span
                              class="text-primary text-subtitle-1 font-weight-regular"
                            >
                              {{ item.preApprovalType }}
                            </span>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5 font-weight-medium'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Coverage
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{ item.coverage }}
                            </span>
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Custom Group
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{
                                item.coverage == "Organization"
                                  ? "-"
                                  : item.customGroupName
                              }}
                            </span>
                          </section>
                        </td>
                        <td
                          v-if="!viewButtonClicked"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Advance Notification
                          </div>
                          <section>
                            <span class="text-subtitle-1 font-weight-regular">
                              {{ item.advanceNotificationDays + " day(s)" }}
                            </span>
                          </section>
                        </td>
                        <!-- <td
                          v-if="!viewButtonClicked"
                          :class="
                            isMobileView
                              ? ' d-flex justify-space-between align-center'
                              : ' pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex align-center"
                            :class="isMobileView ? '' : 'mt-2'"
                          >
                            Restrict Sandwich
                          </div>
                          <section class="d-flex">
                            <AppToggleButton
                              v-if="
                                item.preApprovalType == 'Work from home'
                              "
                              button-active-text="Yes"
                              button-inactive-text="No"
                              button-active-color="rgb(var(--v-theme-primary))"
                              button-inactive-color="#9E9E9E"
                              id-value="gab-analysis-based-on"
                              :current-value="
                                item.restrictSandwich === 'Yes'
                                  ? true
                                  : false
                              "
                              @chosen-value="
                                onChangeRestrictSandwich(
                                  $event,
                                  item,
                                  index
                                )
                              "
                            ></AppToggleButton>
                            <div v-else :class="isMobileView ? '' : 'ml-10'">
                              -
                            </div>
                          </section>
                        </td> -->
                        <td
                          v-if="!viewButtonClicked"
                          :class="
                            isMobileView
                              ? 'd-flex justify-space-between align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Status
                          </div>
                          <section
                            class="d-flex align-center justify-space-between"
                          >
                            <div
                              class="d-flex align-center justify-space-around"
                            >
                              <span
                                id="w-80"
                                v-if="item.status === 'Active'"
                                class="text-green text-subtitle-1 font-weight-regular d-flex justify-center align-center"
                                >{{ item.status }}</span
                              >
                              <span
                                id="w-80"
                                v-else
                                class="text-red text-subtitle-1 font-weight-regular d-flex justify-center align-center text-center"
                                >{{ item.status }}</span
                              >
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>

                <v-col
                  cols="7"
                  v-if="
                    viewButtonClicked && !AddEditButtonClicked && !isMobileView
                  "
                >
                  <ViewPreApproval
                    @close-split-view="closePreApprovalViewTab()"
                    @close-edit-form="closeEditForm"
                    @open-edit-form="openEditForm"
                    @update-edited-data="updateData"
                    :selectedItem="selectedItem"
                    :isEdit="isEdit"
                    :access-rights="preApprovalsFormAccess"
                  />
                </v-col>
                <v-dialog
                  class="pl-4"
                  v-model="preApprovalViem"
                  v-if="
                    isMobileView && viewButtonClicked && !AddEditButtonClicked
                  "
                  width="900"
                  @click:outside="closePreApprovalViewTab()"
                >
                  <ViewPreApproval
                    @close-split-view="closePreApprovalViewTab()"
                    @close-edit-form="closeEditForm"
                    @open-edit-form="openEditForm"
                    @update-edited-data="updateData"
                    :selectedItem="selectedItem"
                    :isEdit="isEdit"
                    :access-rights="preApprovalsFormAccess"
                  />
                </v-dialog>

                <v-col
                  cols="7"
                  v-if="AddEditButtonClicked && windowWidth >= 1300"
                >
                  <AddEditPreApprovals
                    @close-split-view="closePreApprovalViewTab()"
                    :selectedItem="selectedItem"
                    :isEdit="isEdit"
                    @add-data="updateData"
                  />
                </v-col>
                <v-dialog
                  class="pl-4"
                  v-if="windowWidth < 1300"
                  v-model="AddEditButtonClicked"
                  width="900"
                  @click:outside="closePreApprovalViewTab()"
                >
                  <AddEditPreApprovals
                    @close-split-view="closePreApprovalViewTab()"
                    @add-data="updateData"
                    :selectedItem="selectedItem"
                    :isEdit="isEdit"
                  />
                </v-dialog>
              </v-row>

              <div v-if="restrictSandwichDialog">
                <v-dialog
                  v-model="restrictSandwichDialog"
                  persistent
                  width="550px"
                  style="height: 100% !important"
                >
                  <v-card
                    class="rounded-lg"
                    :min-height="windowWidth > 700 ? 350 : ''"
                  >
                    <div class="category-list-header">
                      <div class="d-flex justify-end">
                        <v-icon
                          color="white"
                          :class="
                            isMobileView ? 'mt-2 mb-2 mr-2' : 'rounded-lg ma-2'
                          "
                          size="20"
                          @click="cancelRestrictSandwichForChange"
                          >fas fa-times</v-icon
                        >
                      </div>
                      <div class="mt-n6 d-flex justify-start align-center mb-3">
                        <span
                          class="ml-5 text-white text-wrap font-weight-bold"
                          style="font-size: 0.8em"
                          >Restrict Sandwich Pre-approvals Setting
                        </span>
                      </div>
                    </div>
                    <v-row class="d-flex justify-center">
                      <v-col
                        :class="isMobileView ? 'ml-4' : 'mb-n5 ml-15 mt-4'"
                      >
                        <div class="d-flex">
                          <p clsss="v-label text-wrap font-weight-bold text-h6">
                            Choose one or more sandwich restriction below
                          </p>
                          <v-tooltip v-model="showToolTip" location="bottom">
                            <template v-slot:activator="{ props }">
                              <v-icon
                                :class="
                                  isMobileView ? 'mt-2 ml-1 mr-4' : 'mt-1 ml-2'
                                "
                                v-bind="props"
                                size="x-small"
                                color="info"
                              >
                                fas fa-info-circle
                              </v-icon>
                            </template>
                            <div
                              style="
                                width: 140px !important;
                                height: 140px !important;
                              "
                            >
                              Choosing a restriction for sandwich, system will
                              count the pre-approval days including week off or
                              holiday or leaves
                            </div>
                          </v-tooltip>
                        </div>
                      </v-col>

                      <v-col
                        cols="10"
                        class="mb-n10 ml-2"
                        :class="isMobileView ? 'd-flex flex-column' : 'd-flex'"
                      >
                        <v-checkbox
                          v-model="restrictSandwichFor"
                          label="Week Off"
                          value="Week Off"
                          color="primary"
                          :class="isMobileView ? 'mb-n4' : ''"
                        ></v-checkbox>
                        <v-checkbox
                          v-model="restrictSandwichFor"
                          label="Holiday"
                          value="Holiday"
                          color="primary"
                        ></v-checkbox>
                      </v-col>

                      <v-col
                        cols="10"
                        class="mb-n6 ml-2"
                        :class="isMobileView ? 'd-flex flex-column' : 'd-flex'"
                      >
                        <v-checkbox
                          v-model="restrictSandwichFor"
                          label="Paid Leave"
                          value="Paid Leave"
                          color="primary"
                          :class="isMobileView ? 'mb-n4' : ''"
                        ></v-checkbox>
                        <v-checkbox
                          v-model="restrictSandwichFor"
                          label="Unpaid Leave"
                          value="Unpaid Leave"
                          color="primary"
                          :class="isMobileView ? '' : 'ml-7'"
                        ></v-checkbox>
                      </v-col>

                      <v-col cols="12" class="mb-5">
                        <div class="d-flex justify-center pb-2">
                          <v-btn
                            color="primary"
                            class="mr-2"
                            variant="outlined"
                            rounded="lg"
                            @click="cancelRestrictSandwichForChange"
                            >Cancel</v-btn
                          >
                          <v-btn
                            class="mx-2"
                            color="primary"
                            rounded="lg"
                            @click="saveRestrictSandwichFor"
                            >Confirm</v-btn
                          >
                        </div>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-dialog>
              </div>

              <AppWarningModal
                v-if="openWarningModalForRestrictSandwich"
                :open-modal="openWarningModalForRestrictSandwich"
                :confirmation-heading="warningTextForRestrictSandwich"
                @close-warning-modal="onCloseWarningModalForRstrictSandwich()"
                @accept-modal="openWarningModelForRstrictSandwich()"
              >
              </AppWarningModal>

              <AppWarningModal
                v-if="openWarningModal"
                :open-modal="openWarningModal"
                :confirmation-heading="warningText"
                :icon-name="warningIconClass"
                @close-warning-modal="onCloseWarningModal()"
                @accept-modal="deleteItem(deleteItems)"
              >
              </AppWarningModal>
            </div>
          </div>
          <AddEditPreApprovals
            :class="isMobileView ? 'mx-1' : 'mx-5'"
            :style="
              !isMobileView
                ? 'margin-top: -140px !important;'
                : 'margin-top: -80px !important;'
            "
            v-if="preApprovalData.length == 0 && AddEditButtonClicked"
            @close-split-view="closePreApprovalViewTab()"
            @add-data="updateData"
            :selectedItem="selectedItem"
            :isEdit="isEdit"
            :isListEmpty="isListEmpty"
          />
        </v-window-item>
      </v-window>

      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>

    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const ViewPreApproval = defineAsyncComponent(() =>
  import("./ViewPreApprovals.vue")
);
const AddEditPreApprovals = defineAsyncComponent(() =>
  import("./AddEditPreApprovals.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
import { getErrorCodes } from "@/helper.js";
// Queries
import { LIST_PREAPPROVAL_SETTINGS } from "@/graphql/settings/core-hr/preApprovalQueries.js";
// The code is commented for
import { ADD_UPDATE_PREAPPROVAL_SETTINGS } from "@/graphql/settings/core-hr/preApprovalQueries.js";
export default {
  name: "Pre-approvals",
  components: {
    EmployeeDefaultFilterMenu,
    AddEditPreApprovals,
    ViewPreApproval,
    NotesCard,
  },
  data: () => ({
    listLoading: false,
    isErrorInList: false,
    errorContent: "",
    isListEmpty: true,
    showToolTip: false,
    isLoading: false,
    isLoadingDeatils: false,
    isEdit: false,
    restrictSandwichDialog: false,
    viewButtonClicked: false,
    preApprovalViem: false,
    AddEditButtonClicked: false,
    tab: null,
    showSelect: false,
    confirmDeleteAll: false,
    selectedData: [],
    selectedItemIndex: null,
    restrictSandwichFor: [],
    currentTabItem: "tab-3",
    showRetryBtn: true,
    preApprovalData: [],
    selectedItem: null,
    selectedRestictSandwichItem: [],
    //WARNING MODEL
    openWarningModal: false,
    warningText: "Are you sure to delete this record",
    deleteItems: null,
    warningIconClass: "",
    openMultipleWarningModal: false,

    warningTextForRestrictSandwich:
      "Disabling the restriction for sandwich will allow the user to combine the preapproval requests with week off, holidays and leaves. Are you sure to disable the sandwich restriction?",
    openWarningModalForRestrictSandwich: false,
  }),
  computed: {
    landedFormName() {
      return "Pre Approvals";
    },
    preApprovalsFormAccess() {
      let accessFormName = this.landedFormName.replace(/\s/g, "-");
      accessFormName = accessFormName.toLowerCase();
      let preApprovalAccess = this.accessRights(accessFormName);
      if (
        preApprovalAccess &&
        preApprovalAccess.accessRights &&
        preApprovalAccess.accessRights["view"]
      ) {
        return preApprovalAccess.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    coreHRFormAccess() {
      return this.$store.getters.coreHrSettingsFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.coreHRFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    headers() {
      if (this.viewButtonClicked || this.AddEditButtonClicked) {
        return [
          {
            title: "Pre-approvals Type",
            align: "start",
            key: "preApprovalType",
          },

          {
            title: "Coverage",
            key: "coverage",
          },
          {
            title: "Custom Group",
            key: "customGroupName",
          },
        ];
      } else {
        return [
          {
            title: "Pre-approvals Type",
            align: "start",

            key: "preApprovalType",
          },

          {
            title: "Coverage",
            key: "coverage",
          },
          {
            title: "Custom Group",
            key: "customGroupName",
          },
          {
            title: "Advance Notification",
            key: "advanceNotificationDays",
          },
          // {
          //   title: "Restrict Sandwich",
          //   key: "restrictSandwich",
          // },
          {
            title: "Status",
            key: "status",
          },
        ];
      }
    },
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchPreApprovalsDetails();
  },
  methods: {
    closeEditForm() {
      this.isEdit = false;
    },
    openEditForm() {
      this.isEdit = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    updateData() {
      this.viewButtonClicked = false;
      this.preApprovalViem = false;
      this.AddEditButtonClicked = false;
      this.fetchPreApprovalsDetails();

      let snackbarData = {
        isOpen: true,
        message: this.isEdit
          ? "Pre-approval configuration updated successfully"
          : "Pre-approval configuration added successfully",
        type: "success",
      };
      this.showAlert(snackbarData);
    },
    onChangeRestrictSandwich(value, restrictSandwichRow) {
      this.selectedRestictSandwichItem = JSON.parse(
        JSON.stringify(restrictSandwichRow)
      );
      this.restrictSandwichFor =
        this.selectedRestictSandwichItem.restrictSandwichFor;
      if (this.selectedRestictSandwichItem) {
        this.selectedRestictSandwichItem.restrictSandwich = value[1]
          ? "Yes"
          : "No";
      }
      if (
        this.selectedRestictSandwichItem &&
        this.selectedRestictSandwichItem.restrictSandwich == "Yes"
      ) {
        this.restrictSandwichDialog = true;
      } else {
        this.openWarningModalForRestrictSandwich = true;
      }
    },
    saveRestrictSandwichFor() {
      if (this.selectedRestictSandwichItem) {
        this.selectedRestictSandwichItem.restrictSandwichFor =
          this.restrictSandwichFor;
        this.addUpdatePreApprovals(this.selectedRestictSandwichItem);
        this.restrictSandwichDialog = false;
      } else {
        this.restrictSandwichDialog = false;
      }
    },
    cancelRestrictSandwichForChange() {
      this.selectedRestictSandwichItem.restrictSandwich = "No";
      this.restrictSandwichDialog = false;
    },

    onCloseWarningModalForRstrictSandwich() {
      this.selectedRestictSandwichItem.restrictSandwich = "Yes";
      this.openWarningModalForRestrictSandwich = false;
    },
    openWarningModelForRstrictSandwich() {
      if (this.selectedRestictSandwichItem) {
        this.selectedRestictSandwichItem.restrictSandwichFor = "";
        this.selectedRestictSandwichItem.restrictSandwich = "No";
        this.addUpdatePreApprovals(this.selectedRestictSandwichItem);
        this.openWarningModalForRestrictSandwich = false;
      } else {
        this.openWarningModalForRestrictSandwich = false;
      }
    },

    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.coreHRFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/core-hr/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/core-hr/" + clickedForm.url;
        }
      }
    },

    openWarningPopUp(item, warningicon) {
      if (item === null) {
        this.warningIconClass = warningicon;
        this.openWarningModal = true;
        return;
      }
      this.warningIconClass = warningicon;
      this.openWarningModal = true;
      this.deleteItems = item;
    },

    openViewForm(item, index) {
      this.selectedItemIndex = index;
      this.selectedItem = item;
      this.viewButtonClicked = true;
      this.preApprovalViem = true;
      this.AddEditButtonClicked = false;
      this.isEdit = false;
    },
    openAddForm() {
      this.AddEditButtonClicked = true;
      this.viewButtonClicked = true;
      this.isEdit = false;
    },
    EmptyPageAddEditForm() {
      this.AddEditButtonClicked = true;
      this.isEdit = false;
    },
    closePreApprovalViewTab() {
      this.selectedItemIndex = null;
      this.selectedItem = null;
      this.viewButtonClicked = false;

      this.preApprovalViem = false;
      this.AddEditButtonClicked = false;
    },
    //function to delete single file
    deleteItem(item) {
      const index = this.preApprovalData.findIndex((d) => d === item);
      if (index !== -1) {
        this.preApprovalData.splice(index, 1);
      }
      this.openWarningModal = false;
    },
    // function close the warning modal
    onCloseWarningModal() {
      this.warningIconClass = "";
      this.openWarningModal = false;
      this.deleteItems = null;
    },

    onClosemultipleWarningModal() {
      this.openMultipleWarningModal = false;
    },
    fetchPreApprovalsDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_PREAPPROVAL_SETTINGS,
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.listPreApprovalSettings) {
            vm.preApprovalData =
              response.data.listPreApprovalSettings.preApprovalSettings;
          } else {
            vm.handleFormRetrieveError();
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleFormRetrieveError(err);
          vm.listLoading = false;
        });
    },
    handleFormRetrieveError(err = "") {
      this.listLoading = false;

      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        var errorCode = getErrorCodes(err);

        if (errorCode) {
          switch (errorCode) {
            case "_DB0100":
              this.errorContent =
                "Sorry, you don't have access rights to view the pre-approval details. Please contact HR administrator.";
              break;
            case "_DB0000": // technical errors
              this.errorContent =
                "It’s us! There seem to be some technical difficulties. Please try after some time.";
              break;
            case "_UH0001": // unhandled error
            case "_DB0001": // Error while retrieving the employee access rights
            case "_DB0002": // Error while checking the employee access rights
            case "_DB0104": // While check access rights form not found
            case "CHR0035": // Error while listing pre-approval details.
              this.errorContent =
                "Error while retrieving the pre-approval configuration details. Please contact the system admin.";
              break;
            default:
              this.errorContent =
                "Something went wrong while retrieving the pre-approval details. If you continue to see this issue please contact the platform administrator.";
              break;
          }
        } else {
          this.errorContent =
            "Something went wrong while retrieving the pre-approval details. Please try after some time.";
        }
      } else {
        this.errorContent =
          "Something went wrong while retrieving the pre-approval details. Please try after some time.";
      }
      this.isErrorInList = true;
    },
    addUpdatePreApprovals(item) {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_PREAPPROVAL_SETTINGS,
            variables: {
              preApprovalConfigurationId: item.preApprovalConfigurationId
                ? parseInt(item.preApprovalConfigurationId)
                : 0,
              preApprovalType: item.preApprovalType ? item.preApprovalType : "",
              period: item.period ? item.period : "",
              restrictSandwich: item.restrictSandwich
                ? item.restrictSandwich
                : "",
              restrictSandwichFor: item.restrictSandwichFor
                ? JSON.stringify(item.restrictSandwichFor)
                : "",
              coverage: item.coverage ? item.coverage : "Organization",
              noOfPreApprovalRequest: item.noOfPreApprovalRequest
                ? parseInt(item.noOfPreApprovalRequest)
                : 0,
              advanceNotificationDays: item.advanceNotificationDays
                ? parseInt(item.advanceNotificationDays)
                : 0,
              workflowId: parseInt(item.workflowId),
              customGroupId: parseInt(item.customGroupId),
              status: item.status === "InActive" ? "InActive" : "Active",
            },
            client: "apolloClientJ",
          })
          .then(() => {
            this.fetchPreApprovalsDetails();
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: "Pre-approvals updated successfully.",
            };
            vm.showAlert(snackbarData);
          })
          .catch((addEditError) => {
            vm.handlePreApproveAddUpdateError(addEditError);
          });
      } catch {
        vm.handlePreApproveAddUpdateError();
      }
    },

    handlePreApproveAddUpdateError(err = "") {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      if (err && err.graphQLErrors && err.graphQLErrors.length > 0) {
        // error capture

        var errorCode = getErrorCodes(err);

        switch (errorCode) {
          case "_DB0000": // Technical error
            snackbarData.message =
              "It’s us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "CHR0038": // technical errors
            snackbarData.message =
              "There are some difficulties to add/updating the pre-approval settings. Please try after some time.";
            break;

          case "CHR0039":
            snackbarData.message =
              "Error while updating the pre-approval settings. Please contact the system admin.";
            break;
          case "CHR0040":
            snackbarData.message =
              "The coverage is already exist for the selected pre-approval type.";
            break;
          case "CHR0041":
            snackbarData.message =
              "Error while getting the outstanding pre-approvals count.";
            break;
          case "CHR0042":
            snackbarData.message =
              "You will not be allow to Inactive the pre-approval settings if any of the Pre-approval requests is pending for the approval.";
            break;
          case "CHR0045":
            snackbarData.message =
              "The organization coverage settings already exists for the selected pre-approvals type";
            break;
          case "CHR0046":
            snackbarData.message =
              "Inactivate the organization coverage settings to add custom group-level settings";
            break;
          case "CHR0047":
            snackbarData.message =
              "Inactivate the custom group coverage settings to add an organization level settings";
            break;
          case "CHR0048":
            snackbarData.message =
              "The settings already added for the selected custom group";
            break;
          case "CHR0049":
            snackbarData.message = "Error while validating the coverage.";
            break;
          case "_UH0001": // unhandled error
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0002": // Error while checking the employee access rights
          case "_DB0104": // While check access rights form not found
          default:
            snackbarData.message =
              "Something went wrong while configuring pre-approvals. If you continue to see this issue please contact the platform administrator.";
            break;
        }
      } else {
        snackbarData.message =
          "Something went wrong while configuring pre-approvals. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },
    refetchPreApprovalList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.fetchPreApprovalsDetails();
    },
  },
};
</script>

<style scoped>
.pre-approvals-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .pre-approvals-container {
    padding: 5em 1em 0em 1em;
  }
}

.category-list-header {
  background-color: rgb(var(--v-theme-primary)) !important;
  font-size: 1.5em;
  text-transform: inherit;
  font-weight: 500;
  display: flex;
  flex-direction: column;
}
</style>
