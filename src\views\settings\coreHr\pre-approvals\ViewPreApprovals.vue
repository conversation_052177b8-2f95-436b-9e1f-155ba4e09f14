<template>
  <div>
    <v-card v-if="!isEdit" class="rounded-lg">
      <div
        v-if="!showEmployeesList"
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="35" color="hover" variant="elevated">
            <v-icon class="primary" size="20">fas fa-file-alt</v-icon>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            <div class="text-subtitle-1 font-weight-bold">Pre-approvals</div>
          </div>
        </div>
        <div class="d-flex align-center">
          <v-btn
            @click="$emit('open-edit-form')"
            size="small"
            color="primary"
            variant="elevated"
            rounded="lg"
            v-if="accessRights.update"
            >Edit</v-btn
          >
          <v-icon
            class="mx-1"
            color="primary"
            @click="$emit('close-split-view')"
          >
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card v-if="!showEmployeesList">
        <div
          :style="
            isMobileView ? 'height: calc(100vh - 200px); overflow: scroll' : ''
          "
        >
          <v-card-text>
            <v-row class="px-sm-8 px-md-12 mt-2 mb-2">
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Pre-approvals Type
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedPreApprovalDetails.preApprovalType }}
                </p>
              </v-col>
              <v-col
                v-if="
                  editedPreApprovalDetails?.preApprovalType?.toLowerCase() ==
                    'on duty' ||
                  editedPreApprovalDetails?.preApprovalType?.toLowerCase() ==
                    'overtime work'
                "
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Type of Day</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ typeOfDay }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Coverage</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedPreApprovalDetails.coverage) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                v-if="editedPreApprovalDetails.coverage !== 'Organization'"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Custom Group</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedPreApprovalDetails.customGroupName) }}
                </p>
              </v-col>
              <v-col
                v-if="editedPreApprovalDetails.customGroupId"
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <div v-if="isLoadingCard">
                  <v-skeleton-loader
                    type="list-item-two-line"
                    class="ml-n4 mt-n2"
                    width="80%"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >Employees - {{ empListInSelectedGroup.length }}</span
                  >
                  <div
                    v-if="empListInSelectedGroup.length === 0"
                    class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                  >
                    <v-icon color="warning" size="25"
                      >fas fa-exclamation-triangle</v-icon
                    >
                    <span
                      v-if="errorInFetchEmployeesList"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                      >Something went wrong while fetching the employees list.
                      Please try again.
                      <a class="text-primary" @click="fetchCustomEmployeesList"
                        >Refresh
                      </a>
                    </span>
                    <span
                      v-else-if="isNoEmployees"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                    >
                      It seems like there are no employees associated with the
                      selected custom group. Please add some employees under the
                      selected group or try choosing an another group.</span
                    >
                  </div>
                  <div v-else class="d-flex align-center">
                    <AvatarOrderedList
                      v-if="empListInSelectedGroup.length > 0"
                      class="mt-1"
                      :ordered-list="empListInSelectedGroup"
                    ></AvatarOrderedList>
                    <v-btn
                      rounded="lg"
                      color="primary"
                      size="small"
                      class="mt-1"
                      @click="openCustomGroupEmpList()"
                    >
                      View All
                    </v-btn>
                  </div>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Period</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editedPreApprovalDetails.period) }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  No of Pre-approvals Request
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    editedPreApprovalDetails &&
                    editedPreApprovalDetails.noOfPreApprovalRequest
                      ? editedPreApprovalDetails.noOfPreApprovalRequest
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Max Days Allowed Per Request
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(
                      editedPreApprovalDetails.maxDaysAllowedPerRequest
                    )
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Workflow Approval
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    editedPreApprovalDetails &&
                    editedPreApprovalDetails.workflowName
                      ? editedPreApprovalDetails.workflowName
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedPreApprovalDetails.preApprovalType == 'Work from home'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Restrict Sandwich
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedPreApprovalDetails.restrictSandwich }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                v-if="editedPreApprovalDetails.restrictSandwich == 'Yes'"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Restrict Sandwich For
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    editedPreApprovalDetails &&
                    editedPreApprovalDetails.restrictSandwichFor
                      ? JSON.parse(
                          editedPreApprovalDetails.restrictSandwichFor
                        ).join(",")
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Advance Notification
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    editedPreApprovalDetails.advanceNotificationDays + " day(s)"
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedPreApprovalDetails.preApprovalType == 'Work from home'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Document Submission
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedPreApprovalDetails.documentUpload }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
                v-if="
                  editedPreApprovalDetails.preApprovalType ==
                    'Work from home' &&
                  editedPreApprovalDetails.documentUpload === 'Yes'
                "
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  Threshold limit for document submission (in days)
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    editedPreApprovalDetails.maxDaysForDocumentUpload
                      ? editedPreApprovalDetails.maxDaysForDocumentUpload +
                        " day(s)"
                      : "-"
                  }}
                </p>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                lg="6"
                :class="isMobileView ? ' ml-4' : ''"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Status</p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ editedPreApprovalDetails.status }}
                </p>
              </v-col>
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-card-text>
        </div>
      </v-card>
      <div
        v-if="showEmployeesList"
        style="overflow: scroll"
        :style="
          isMobileView
            ? 'height: calc(100vh - 200px)'
            : 'height: calc(100vh - 200px)'
        "
      >
        <div class="d-flex ma-2">
          <v-btn
            rounded="lg"
            color="primary"
            @click="showEmployeesList = false"
          >
            <v-icon class="mr-1" size="x-small">fas fa-less-than </v-icon>
            Back
          </v-btn>
        </div>
        <EmployeeListCard
          :show-modal="showEmployeesList"
          modal-title="Custom Group Employee(s)"
          :employeesList="empListForComponent"
          :selectable="false"
          :showFilter="false"
          :showFilterSearch="true"
          :isApplyFilter="true"
          @close-modal="showEmployeesList = false"
        ></EmployeeListCard>
      </div>
    </v-card>
    <div v-if="isEdit">
      <AddEditPreApprovals
        :editedPreApprovalDetails="editedPreApprovalDetails"
        @close-edit-form="closeEditForm"
        @save-edited-data="saveEditedPreApprovalDetails"
        v-if="windowWidth > 1300"
        :isEdit="isEdit"
      />
      <v-dialog
        v-else
        class="pl-4"
        v-model="displayEdit"
        width="900"
        @close-edit-form="closeEditForm"
        @save-edited-data="saveEditedPreApprovalDetails"
      >
        <AddEditPreApprovals
          :editedPreApprovalDetails="editedPreApprovalDetails"
          @close-edit-form="closeEditForm"
          @save-edited-data="saveEditedPreApprovalDetails"
          :isEdit="isEdit"
        />
      </v-dialog>
    </div>
  </div>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
import EmployeeListCard from "@/components/helper-components/EmployeeListCard.vue";
import moment from "moment";
import { checkNullValue } from "@/helper";
import { defineAsyncComponent } from "vue";
const AddEditPreApprovals = defineAsyncComponent(() =>
  import("./AddEditPreApprovals.vue")
);
export default {
  name: "ViewPreApprovals",
  components: {
    MoreDetails,
    AddEditPreApprovals,
    AvatarOrderedList,
    EmployeeListCard,
  },
  props: {
    selectedItem: {
      type: Object,
      required: true,
    },
    isEdit: {
      type: Boolean,
      required: true,
    },
    accessRights: {
      type: Object,
      required: true,
    },
  },
  data: () => ({
    moreDetailsList: [],
    openMoreDetails: true,
    editedPreApprovalDetails: {},
    displayEdit: true,
    showEmployeesList: false,
    empListInSelectedGroup: [],
    isNoEmployees: false,
    isLoadingCard: false,
    errorInFetchEmployeesList: false,
  }),
  computed: {
    typeOfDay() {
      if (this.editedPreApprovalDetails?.typeOfDay) {
        let types = JSON.parse(this.editedPreApprovalDetails.typeOfDay);
        return types.join(", ");
      }
      return "-";
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.editedPreApprovalDetails = Object.assign({}, newData);
        this.prefillMoreDetails();
        this.fetchCustomEmployeesList();
        this.showEmployeesList = false;
      },
    },
    isEdit() {
      this.displayEdit = this.isEdit;
    },
  },
  mounted() {
    this.displayEdit = this.isEdit;
  },
  methods: {
    checkNullValue,
    closeEditForm() {
      this.showEmployeesList = false;
      this.$emit("close-edit-form");
    },
    saveEditedPreApprovalDetails() {
      this.$emit("update-edited-data");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.formatDate(
          new Date(this.selectedItem.addedOn + ".000Z")
        ),
        addedByName = this.selectedItem.addedByName,
        updatedByName = this.selectedItem.updatedByName,
        updatedOn = this.formatDate(
          new Date(this.selectedItem.updatedOn + ".000Z")
        );
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },
    async fetchCustomEmployeesList() {
      if (this.editedPreApprovalDetails.customGroupId) {
        let vm = this;
        vm.isLoadingCard = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(vm.editedPreApprovalDetails.customGroupId),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              if (!employeeDetails || employeeDetails.length === 0) {
                vm.isNoEmployees = true;
                vm.empListInSelectedGroup = [];
              } else {
                for (let i = 0; i < employeeDetails.length; i++) {
                  employeeDetails[i].employee_name =
                    employeeDetails[i]["employeeName"];
                  employeeDetails[i].designation_name =
                    employeeDetails[i]["designationName"];
                  employeeDetails[i].department_name =
                    employeeDetails[i]["departmentName"];
                  employeeDetails[i].user_defined_empid =
                    employeeDetails[i]["userDefinedEmpId"];
                  delete employeeDetails[i].key1;
                }
                vm.empListInSelectedGroup = employeeDetails;
              }
              vm.isLoadingCard = false;
              vm.errorInFetchEmployeesList = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.errorInFetchEmployeesList = true;
            vm.empListInSelectedGroup = [];
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },
  },
};
</script>
