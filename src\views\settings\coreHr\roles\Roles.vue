<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        :showBottomSheet="rolesListBackup.length > 0 && !showAddEditViewForm"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="rolesListBackup.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isDefaultFilter="false"
                :reset-filter-count="resetFilterCount"
                @reset-emp-filter="resetFilter('filter')"
                @apply-emp-filter="applyFilter()"
              >
                <template #new-filter>
                  <v-row class="mt-5">
                    <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                      <v-select
                        v-model="selectedStatus"
                        color="secondary"
                        :items="['Active', 'Inactive']"
                        label="Status"
                        multiple
                        closable-chips
                        chips
                        density="compact"
                        single-line
                      >
                      </v-select>
                    </v-col>
                    <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                      <v-autocomplete
                        v-model="selectedAdmin"
                        color="secondary"
                        :items="adminList"
                        label="Admin Privileges"
                        multiple
                        closable-chips
                        chips
                        density="compact"
                        single-line
                      >
                      </v-autocomplete>
                    </v-col>
                  </v-row>
                </template>
              </EmployeeDefaultFilterMenu>
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="roles-configuration-container">
      <v-window v-model="currentTabItem" v-if="isSuperAdmin">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? 'Retry' : ''"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <v-row v-else-if="showAddEditViewForm">
            <v-col cols="12">
              <RoleAccessRights
                :selectedItem="selectedItem"
                :action-type="actionType"
                :rolesList="rolesList"
                @close-form="closeAllForms()"
                @refetch-list="refetchList()"
                @retrieve-error="refetchList()"
              ></RoleAccessRights>
            </v-col>
          </v-row>

          <AppFetchErrorScreen
            v-else-if="rolesListBackup.length === 0"
            key="no-results-screen"
            :isSmallImage="true"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                  <v-col v-if="rolesListBackup.length === 0" cols="12">
                    <NotesCard
                      notes="Roles and access rights to ensure efficient and secure access to various forms and functionalities. This is designed to streamline the distribution of forms to users based on their specific roles and permissions."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="These roles are defined based on the access rights they require to perform their tasks effectively. Super admins have granular control over who gets access to which forms. They can assign roles and access rights at both the individual form level and the user level."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      notes="By implementing this role-based access control, you can efficiently manage and distribute forms within your application while ensuring that users only have access to the forms and functionalities required for their specific roles and responsibilities. This is not only enhances security but also improves user productivity and overall performance."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="rolesListBackup.length === 0"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddForm()"
                    >
                      <v-icon size="15" class="pr-1 primary"
                        >fas fa-plus</v-icon
                      >
                      Configure Roles
                    </v-btn>
                    <v-btn
                      v-if="rolesListBackup.length === 0"
                      color="transparent"
                      variant="flat"
                      rounded="lg"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else>
            <div v-if="rolesList.length > 0">
              <div
                class="d-flex align-center"
                :class="
                  isMobileView
                    ? 'd-flex flex-wrap align-center my-6 justify-center flex-column'
                    : 'd-flex flex-wrap align-center my-4 justify-end'
                "
              >
                <v-btn
                  prepend-icon="fas fa-plus"
                  color="primary rounded-lg"
                  class="mx-1"
                  @click="openAddForm"
                >
                  <template v-slot:prepend>
                    <v-icon></v-icon>
                  </template>
                  Configure Roles
                </v-btn>
                <v-btn
                  rounded="lg"
                  color="transparent"
                  variant="flat"
                  class="mt-1"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </div>

              <v-row
                class="mb-12"
                :style="
                  'overflow: scroll; height: ' +
                  $store.getters.getTableHeight(230)
                "
              >
                <v-col
                  v-for="(data, index) in rolesList"
                  :key="index"
                  xlg="3"
                  lg="4"
                  md="6"
                  sm="6"
                  cols="12"
                >
                  <RolesCard
                    :roleDetails="data"
                    @view-roles="openViewForm(data)"
                    @refetch-list="refetchList()"
                    @on-open-edit="openEditForm(data)"
                    @export-roles="exportRoles(data)"
                  ></RolesCard>
                </v-col>
              </v-row>
            </div>
            <AppFetchErrorScreen
              v-else
              key="no-results-screen"
              main-title="There are no roles matched for the selected filters/searches."
              image-name="common/no-records"
            >
              <template #contentSlot>
                <div style="max-width: 80%">
                  <v-row class="rounded-lg pa-5 mb-4">
                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                    >
                      <v-btn
                        color="primary"
                        variant="elevated"
                        class="ml-4 mt-1"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="resetFilter('grid')"
                      >
                        Reset Filter/Search
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </template>
            </AppFetchErrorScreen>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
  </div>
</template>

<script>
import moment from "moment";
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const RoleAccessRights = defineAsyncComponent(() =>
  import("./RoleAccessRights.vue")
);
import {
  LIST_ROLE_CONFIGURATIONS,
  RETRIEVE_ROLE_ACCESS_RIGHTS,
} from "@/graphql/settings/core-hr/rolesQueries.js";
import RolesCard from "./RolesCard.vue";
import FileExportMixin from "@/mixins/FileExportMixin";

export default {
  name: "RolesConfiguration",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    RolesCard,
    RoleAccessRights,
  },
  mixins: [FileExportMixin],
  data: () => ({
    // list
    listLoading: false,
    rolesList: [],
    rolesListBackup: [],
    isErrorInList: false,
    errorContent: "",
    showRetryBtn: true,
    // add
    isLoading: false,
    // view/add/update
    showAddEditViewForm: false,
    actionType: "view",
    selectedItem: {},
    // tab
    currentTabItem: "",
    // filter
    selectedStatus: ["Active"],
    resetFilterCount: 0,
    adminList: [
      "Admin",
      "Super Admin",
      "Employee Admin",
      "Payroll Admin",
      "Benefits Admin",
      "Roster Admin",
      "Productivity Monitoring Admin",
    ],
    selectedAdmin: [],
  }),
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    displayFormName() {
      return "Roles";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    coreHrSettingsFormAccess() {
      return this.$store.getters.coreHrSettingsFormAccess;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.coreHrSettingsFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.displayFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    formatDate() {
      return (date) => {
        if (date && date !== "0000-00-00") {
          let orgDateFormat =
            this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
  },

  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },

  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.displayFormName);
    this.fetchList();
  },

  errorCaptured(err, vm, info) {
    if (!vm || vm.name != "bottom-navigation") {
      let url = window.location.href;
      console.error("roles error", err, info);
      let msg =
        "Something went wrong while loading the roles configuration. Please try after some time.";
      if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
        msg = err + " " + info;
      }
      let snackbarData = {
        isOpen: true,
        message: msg,
        type: "warning",
      };
      this.showAlert(snackbarData);
    }
    return false;
  },

  methods: {
    onTabChange(tab) {
      if (tab !== this.displayFormName) {
        const { formAccess } = this.coreHrSettingsFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/core-hr/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/core-hr/" + clickedForm.url;
        }
      }
    },

    onApplySearch(val) {
      if (!val) {
        this.rolesList = this.rolesListBackup;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.rolesListBackup;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.rolesList = searchItems;
      }
    },

    resetFilter(calledFrom) {
      if (calledFrom === "grid") {
        this.resetFilterCount += 1;
      }
      this.selectedStatus = ["Active"];
      this.selectedAdmin = [];
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.rolesList = this.rolesListBackup;
    },

    applyFilter() {
      let filteredList = this.rolesListBackup;
      if (this.selectedStatus.length > 0) {
        filteredList = filteredList.filter((item) => {
          return this.selectedStatus.includes(item.Role_Status);
        });
      }
      if (this.selectedAdmin.length > 0) {
        filteredList = filteredList.filter((obj) => {
          return this.selectedAdmin.some((value) =>
            obj.Type_Of_Admin.includes(value)
          );
        });
      }
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.rolesList = filteredList;
    },

    openViewForm(item) {
      this.closeAllForms();
      this.openAddEditViewForm(item, "view");
    },

    openEditForm(item) {
      this.closeAllForms();
      this.openAddEditViewForm(item, "edit");
    },

    openAddForm() {
      this.closeAllForms();
      let rolesAccessData = {
        Roles_Id: 0,
      };
      this.openAddEditViewForm(rolesAccessData, "add");
    },

    exportRoles(item) {
      this.fetchRoleAccessRights(item);
    },

    fetchRoleAccessRights(item) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_ROLE_ACCESS_RIGHTS,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
          variables: {
            Roles_Id: item.Roles_Id,
            Template_Name: "",
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveRolesAccessRights &&
            !response.data.retrieveRolesAccessRights.errorCode
          ) {
            let { rolesData } = response.data.retrieveRolesAccessRights;
            const { Forms, AccessRights, Modules } = rolesData;
            let accessRights = JSON.parse(AccessRights);
            let forms = JSON.parse(Forms);
            let modules = JSON.parse(Modules);
            let expectedArray = [];
            // Iterate through each object in the access rights array
            for (let i = 0; i < accessRights.length; i++) {
              // Find the corresponding form based on Form_Id
              let form = forms.find(
                (form) => form.Form_Id === accessRights[i].Form_Id
              );

              if (form) {
                // Find the corresponding module based on Module_Id in the forms array
                let module = modules.find(
                  (module) => module.Module_Id === form.Module_Id
                );

                if (module) {
                  // Create the merged object with Form_Name and Module_Name & Module_Id
                  let mergedObject = {
                    ...accessRights[i],
                    Form_Name: form.Form_Name,
                    Module_Id: module.Module_Id,
                    Module_Name: module.Module_Name,
                  };

                  expectedArray.push(mergedObject);
                }
              }
            }
            let exportOptions = {
              fileExportData: expectedArray,
              fileName: "Role Access Rights",
              sheetName: "Role Access Rights",
              header: [
                {
                  header: "Form_Name",
                  key: "Form_Name",
                },
                {
                  header: "Module_Name",
                  key: "Module_Name",
                },
                { header: "View", key: "Role_View" },
                { header: "Add", key: "Role_Add" },
                { header: "Update", key: "Role_Update" },
                { header: "Delete", key: "Role_Delete" },
                { header: "Optional Choice", key: "Role_Optional_Choice" },
                { header: "HR Group", key: "Role_Hr_Group" },
                { header: "Payroll Group", key: "Role_Payroll_Group" },
              ],
            };
            this.exportExcelFile(exportOptions);
            vm.isLoading = false;
          } else {
            vm.handleRetrieveRoleAccessRightsError();
          }
        })
        .catch((err) => {
          vm.handleRetrieveRoleAccessRightsError(err);
        });
    },

    handleRetrieveRoleAccessRightsError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "role access rights",
        isListError: false,
      });
      this.$emit("retrieve-error");
    },

    openAddEditViewForm(item, type) {
      this.showAddEditViewForm = true;
      this.actionType = type;
      this.selectedItem = item;
    },

    closeAllForms() {
      this.showAddEditViewForm = false;
      this.selectedItem = {};
    },

    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_ROLE_CONFIGURATIONS,
          client: "apolloClientAC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listRoles &&
            !response.data.listRoles.errorCode
          ) {
            let { listRoles } = response.data.listRoles;
            listRoles = listRoles.map((item) => {
              item["Type_Of_Admin"] =
                item.Type_Of_Admin && item.Type_Of_Admin.length > 0
                  ? item.Type_Of_Admin
                  : [];
              item["Admin_Privileges"] = item.Type_Of_Admin.join(", ");
              item["Added_On"] = item.Added_On
                ? this.formatDate(new Date(item.Added_On + ".000Z"))
                : "-";
              item["Updated_On"] = item.Updated_On
                ? this.formatDate(new Date(item.Updated_On + ".000Z"))
                : "-";
              return item;
            });
            vm.rolesList = listRoles;
            vm.rolesListBackup = listRoles;
            vm.listLoading = false;
            vm.isErrorInList = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },

    handleListError(err = "") {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "roles",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },

    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.fetchList();
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.roles-configuration-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .roles-configuration-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
