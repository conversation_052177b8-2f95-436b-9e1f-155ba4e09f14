<template>
  <div v-if="isMounted">
    <v-card class="rounded-lg">
      <div
        v-if="!showEmployeesList"
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-label">
          <v-chip
            class="mr-2 text-subtitle-1 pa-7 rounded-ts-lg rounded-be-xl bg-primary"
            size="large"
            label
          >
            {{ isEdit ? "Edit" : "New" }}
          </v-chip>
          <span class="pt-1 font-weight-bold">Special Wages</span>
        </div>
        <div class="d-flex align-center pa-1">
          <div class="mt-2 mr-1">
            <v-btn
              v-if="isFormDirty"
              rounded="lg"
              color="primary"
              variant="elevated"
              class="mb-2"
              @click="validatePreApprovalForm"
              >Save</v-btn
            >
            <v-tooltip v-else location="bottom">
              <template v-slot:activator="{ props }">
                <v-btn
                  v-bind="props"
                  rounded="lg"
                  class="cursor-not-allow primary"
                  variant="elevated"
                  >Save</v-btn
                >
              </template>
              <div>There are no changes to be updated</div>
            </v-tooltip>
          </div>
          <v-icon @click="onCloseEditForm" color="primary" class="mr-1">
            fas fa-times
          </v-icon>
        </div>
      </div>

      <div
        :class="isMobileView ? 'pa-2' : 'pa-8'"
        style="overflow: scroll"
        :style="
          showEmployeesList
            ? 'height: calc(100vh - 200px)'
            : 'height: calc(100vh - 260px)'
        "
      >
        <v-card-text>
          <v-form v-if="!showEmployeesList" ref="specialWagesForm">
            <v-row>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="['Monthly', 'Hourly']"
                  :itemSelected="salaryType"
                  label="Salary Type"
                  :is-auto-complete="true"
                  :isRequired="true"
                  @selected-item="onChangeIsFormDirty($event, 'salaryType')"
                  :rules="[required('Salary Type', salaryType)]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>

              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="salaryType == 'Hourly'"
              >
                <div class="d-flex">
                  <span class="v-label pr-3 pb-5">Attendance Required</span>
                  <v-switch
                    color="primary"
                    class="ml-2"
                    v-model="Attendance_Required"
                    :true-value="'Yes'"
                    :false-value="'No'"
                    @update:model-value="isFormDirty = true"
                  ></v-switch>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <CustomSelect
                  :items="[
                    'Extra Work Hours(Weekday)',
                    'Holiday',
                    'Mandatory',
                    'Work Schedule Holiday(Week Off)',
                    'Special Day Falling on a scheduled Rest Day',
                    'Regular Holiday Falling on a scheduled Rest Day',
                    'Night Shift',
                  ]"
                  label="Special Work Days"
                  :isRequired="true"
                  :itemSelected="specialWorkDays"
                  :disabledValue="activeSpecialWorkDaysTypes"
                  @selected-item="
                    onChangeIsFormDirty($event, 'specialWorkDays')
                  "
                  :is-auto-complete="true"
                  :rules="[required('Special Work Days', specialWorkDays)]"
                  style="max-width: 300px"
                ></CustomSelect>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="openCustomGroupDropDown"
              >
                <div class="d-flex">
                  <CustomSelect
                    :items="customGroupList"
                    label="Custom Group"
                    :isRequired="true"
                    :itemSelected="customGroupId"
                    @selected-item="
                      onChangeIsFormDirty($event, 'customGroupId')
                    "
                    item-title="Custom_Group_Name"
                    item-value="Custom_Group_Id"
                    :is-auto-complete="true"
                    :is-loading="customGroupLoading"
                    :disabled="customGroupLoading"
                    :rules="[required('Custom Group', customGroupId)]"
                    style="max-width: 300px"
                  >
                  </CustomSelect>
                  <v-btn
                    rounded="lg"
                    class="ml-2 mt-2 primary"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="retrieveCustomGroups('Special Wages', 85)"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </div>
                <div class="mb-1">
                  <v-btn
                    color="primary"
                    variant="text"
                    :href="baseUrl + 'in/core-hr/custom-employee-groups'"
                    target="_blank"
                  >
                    <v-icon size="14" class="mr-1">fas fa-plus</v-icon> Add
                    Custom Group
                  </v-btn>
                </div>
              </v-col>
              <v-col
                v-if="customGroupId"
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
              >
                <div v-if="isLoadingCard">
                  <v-skeleton-loader
                    type="list-item-two-line"
                    class="ml-n4 mt-n2"
                    width="80%"
                  ></v-skeleton-loader>
                </div>
                <div v-else>
                  <span class="text-subtitle-1 text-grey-darken-1"
                    >Employees - {{ empListInSelectedGroup.length }}</span
                  >
                  <div
                    v-if="empListInSelectedGroup.length === 0"
                    class="bg-yellow-lighten-5 pa-3 rounded-lg d-flex"
                  >
                    <v-icon color="warning" size="25"
                      >fas fa-exclamation-triangle</v-icon
                    >
                    <span
                      v-if="errorInFetchEmployeesList"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                      >Something went wrong while fetching the employees list.
                      Please try again.
                      <a class="text-primary" @click="fetchCustomEmployeesList"
                        >Refresh
                      </a>
                    </span>
                    <span
                      v-else-if="isNoEmployees"
                      class="pl-2 text-subtitle-1 font-weight-regular"
                    >
                      It seems like there are no employees associated with the
                      selected custom group. Please add some employees under the
                      selected group or try choosing an another group.</span
                    >
                  </div>
                  <div v-else class="d-flex align-center">
                    <AvatarOrderedList
                      v-if="empListInSelectedGroup.length > 0"
                      class="mt-2"
                      :ordered-list="empListInSelectedGroup"
                    ></AvatarOrderedList>
                    <v-btn
                      rounded="lg"
                      color="primary"
                      size="small"
                      class="mt-2"
                      @click="openCustomGroupEmpList()"
                    >
                      View All
                    </v-btn>
                  </div>
                </div>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="specialWorkDays != 'Extra Work Hours(Weekday)'"
              >
                <v-text-field
                  v-model="wageFactor"
                  type="number"
                  :min="0"
                  :max="10"
                  variant="solo"
                  :rules="[
                    numericRequiredValidation('Wage Index', wageFactor),
                    twoDecimalPrecisionValidation(wageFactor),
                    minMaxNumberValidation('Wage Index', wageFactor, 0, 10),
                  ]"
                  style="max-width: 300px"
                  @update:model-value="validateInput('wageFactor')"
                >
                  <template v-slot:label>
                    <span>Wage Index</span>
                    <span class="ml-1" style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col
                cols="12"
                sm="6"
                :md="isListEmpty ? 4 : 6"
                class="px-md-6 pb-0 mb-2"
                v-if="isEdit"
              >
                <div class="v-label ml-2 mb-2">Status</div>
                <AppToggleButton
                  button-active-text="Active"
                  button-inactive-text="Inactive"
                  button-active-color="#7de272"
                  button-inactive-color="red"
                  id-value="gab-analysis-based-on"
                  :current-value="Status === 'Active' ? true : false"
                  @chosen-value="onChangeStatus($event)"
                ></AppToggleButton>
              </v-col>
            </v-row>
          </v-form>
          <div v-if="showEmployeesList">
            <div class="d-flex" :class="isMobileView ? 'mb-2' : 'mt-n8 mb-2'">
              <v-btn
                rounded="lg"
                color="primary"
                @click="showEmployeesList = false"
              >
                <v-icon class="mr-1" size="x-small">fas fa-less-than </v-icon>
                Back
              </v-btn>
            </div>
            <EmployeeListCard
              v-if="showEmployeesList"
              :show-modal="showEmployeesList"
              modal-title="Custom Group Employee(s)"
              :employeesList="empListForComponent"
              :selectable="false"
              :showFilter="false"
              :showFilterSearch="true"
              :isApplyFilter="true"
              @close-modal="showEmployeesList = false"
            ></EmployeeListCard>
          </div>
        </v-card-text>
      </div>
    </v-card>
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      confirmation-heading="Are you sure to exit this form?"
      imgUrl="common/exit_form"
      @close-warning-modal="onCloseWarningModal()"
      @accept-modal="onConfirmCloseEditFrom()"
    >
    </AppWarningModal>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="primary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            Close
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppLoading v-if="isLoadingDetails"></AppLoading>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import AvatarOrderedList from "@/components/helper-components/AvatarOrderedList.vue";
import EmployeeListCard from "@/components/helper-components/EmployeeListCard.vue";
// Queries
import { ADD_UPDATE_SPECIAL_WAGES } from "@/graphql/settings/core-hr/specialWagesQueries";

export default {
  name: "AddEditSpecialWages",
  mixins: [validationRules],
  components: {
    CustomSelect,
    AvatarOrderedList,
    EmployeeListCard,
  },
  props: {
    selectedItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isListEmpty: {
      type: Boolean,
      default: false,
    },
    coverage: {
      type: String,
      required: false,
    },
    specialWagesData: {
      type: Array,
      required: false,
    },
  },
  emits: ["close-form", "edit-updated"],
  data() {
    return {
      salaryType: "Monthly",
      specialWorkDays: "",
      wageFactor: null,
      Attendance_Required: "No",
      Status: "Active",
      isMounted: false,
      isLoadingDetails: false,
      openConfirmationPopup: false,
      isFormDirty: false,
      customGroupId: null,
      customGroupRetrieved: null,
      customGroupList: [],
      customGroupLoading: false,
      showValidationAlert: false,
      validationMessages: [],
      Configuration_Id: null,
      empListForComponent: [],
      empListInSelectedGroup: [],
      showEmployeesList: false,
      errorInFetchEmployeesList: false,
      isNoEmployees: false,
      isLoadingCard: false,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    openCustomGroupDropDown() {
      if (this.isEdit) {
        return this.customGroupRetrieved !== null ? true : false;
      } else {
        return this.coverage === "Custom Group" ? true : false;
      }
    },
    activeSpecialWorkDaysTypes() {
      const monthlyActiveSpecialWorkDayTypes = [];
      const hourlyActiveSpecialWorkDayTypes = [];
      if (this.specialWagesData.length) {
        this.specialWagesData.forEach((item) => {
          if (
            item.Custom_Group_Id === null &&
            item.Status === "Active" &&
            item.Salary_Type === "Monthly"
          ) {
            monthlyActiveSpecialWorkDayTypes.push(item.Special_Work_Days);
          } else if (
            item.Custom_Group_Id === null &&
            item.Status === "Active" &&
            item.Salary_Type === "Hourly"
          ) {
            hourlyActiveSpecialWorkDayTypes.push(item.Special_Work_Days);
          }
        });
      }

      if (this.salaryType == "Hourly") {
        return hourlyActiveSpecialWorkDayTypes;
      } else {
        return monthlyActiveSpecialWorkDayTypes;
      }
    },
  },
  watch: {
    customGroupId(val) {
      if (!val) {
        this.empListInSelectedGroup = [];
      } else {
        this.fetchCustomEmployeesList();
      }
    },
  },
  mounted() {
    if (this.isEdit) {
      const {
        Salary_Type,
        Special_Work_Days,
        Status,
        Attendance_Required,
        Wage_Factor,
        Custom_Group_Id,
        Configuration_Id,
      } = this.editFormData;
      this.Configuration_Id = Configuration_Id ? Configuration_Id : 0;
      this.salaryType = Salary_Type ? Salary_Type : "";
      this.specialWorkDays = Special_Work_Days ? Special_Work_Days : "";
      this.Status = Status ? Status : "Active";
      this.Attendance_Required = Attendance_Required
        ? Attendance_Required
        : null;

      this.wageFactor = Wage_Factor;
      this.customGroupRetrieved = Custom_Group_Id ? Custom_Group_Id : null;
      this.retrieveCustomGroups("Special Wages", "edit");
    }
    this.retrieveCustomGroups("Special Wages");
    this.isFormDirty = false;
    this.isMounted = true;
  },
  methods: {
    // open employees list to view the employees when the coverage is custom-group
    openCustomGroupEmpList() {
      this.empListForComponent = this.empListInSelectedGroup;
      this.showEmployeesList = true;
    },
    // on changing the custom group we need to fetch the employees list relevant to the selected group
    async fetchCustomEmployeesList() {
      if (this.customGroupId) {
        let vm = this;
        vm.isLoadingCard = true;
        await this.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(this.customGroupId),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              if (!employeeDetails || employeeDetails.length === 0) {
                vm.isNoEmployees = true;
                vm.empListInSelectedGroup = [];
              } else {
                for (let i = 0; i < employeeDetails.length; i++) {
                  employeeDetails[i].employee_name =
                    employeeDetails[i]["employeeName"];
                  employeeDetails[i].designation_name =
                    employeeDetails[i]["designationName"];
                  employeeDetails[i].department_name =
                    employeeDetails[i]["departmentName"];
                  employeeDetails[i].user_defined_empid =
                    employeeDetails[i]["userDefinedEmpId"];
                  delete employeeDetails[i].key1;
                }
                vm.empListInSelectedGroup = employeeDetails;
              }
              vm.isLoadingCard = false;
              vm.errorInFetchEmployeesList = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.errorInFetchEmployeesList = true;
            vm.empListInSelectedGroup = [];
          });
      } else {
        this.empListInSelectedGroup = [];
      }
    },
    validateInput(fieldName) {
      // Get the value of the specified field
      let value = this[fieldName];

      // Check if the input value is negative
      if (value !== null && value < 0) {
        // Reset the input value to null
        this[fieldName] = null;
      }
      this.isFormDirty = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onCloseWarningModal() {
      this.openConfirmationPopup = false;
    },
    onCloseEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.onConfirmCloseEditFrom();
      this.showEmployeesList = false;
    },
    onConfirmCloseEditFrom() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
    },
    onChangeStatus(value) {
      this.Status = value[1] ? "Active" : "Inactive";
      this.isFormDirty = true;
    },
    onChangeIsFormDirty(val, field) {
      if (field == "salaryType") {
        this.salaryType = val;
        this.specialWorkDays = null;
      } else if (field == "specialWorkDays") {
        this.specialWorkDays = val;
      } else if (field == "customGroupId") {
        this.customGroupId = val;
      }
      this.isFormDirty = true;
    },
    async validatePreApprovalForm() {
      const { valid } = await this.$refs.specialWagesForm.validate();
      if (valid) {
        this.addUpdateSpecialWages();
      }
    },
    addUpdateSpecialWages() {
      let vm = this;
      vm.isLoadingDetails = true;
      try {
        vm.$apollo
          .mutate({
            mutation: ADD_UPDATE_SPECIAL_WAGES,
            variables: {
              Configuration_Id: vm.Configuration_Id
                ? parseInt(vm.Configuration_Id)
                : 0,
              Salary_Type: vm.salaryType ? vm.salaryType : "Monthly",

              Special_Work_Days: vm.specialWorkDays
                ? vm.specialWorkDays
                : "Holiday",
              Attendance_Required:
                vm.salaryType === "Hourly" ? vm.Attendance_Required : "No",
              Wage_Factor: vm.wageFactor ? parseFloat(vm.wageFactor) : null,
              Status: vm.Status === "Inactive" ? "Inactive" : "Active",
              CustomGroup_Id: vm.customGroupId ? vm.customGroupId : null,
            },
            client: "apolloClientJ",
          })
          .then(() => {
            vm.isLoadingDetails = false;
            vm.isLoadingDetails = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: vm.isEdit
                ? "Special wage configuration updated successfully."
                : "Special wage configuration added successfully.",
            };
            vm.showAlert(snackbarData);
            vm.$emit("edit-updated");
          })
          .catch((addEditError) => {
            vm.handleAddUpdateError(addEditError);
          });
      } catch {
        vm.handleAddUpdateError();
      }
    },
    handleAddUpdateError(err = "") {
      this.isLoadingDetails = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: "special wage configuration",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    async retrieveCustomGroups(formName, action) {
      this.customGroupLoading = true;
      this.customGroupList = [];

      await this.$store
        .dispatch("listCustomGroupBasedOnFormName", {
          formName: formName,
        })
        .then((groupList) => {
          if (groupList && groupList.length > 0) {
            this.customGroupList = groupList;
            if (action == "edit") {
              this.customGroupId = this.customGroupRetrieved;
            }
          }
          this.customGroupLoading = false;
        })
        .catch(() => {
          this.customGroupLoading = false;
        });
    },
  },
};
</script>
