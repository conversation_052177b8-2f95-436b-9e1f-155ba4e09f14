<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <!-- <BusinessUnitFilter
                  v-if="!showAddEditForm && (itemList.length || isFilterApplied)"
                  ref="formFilterRef"
                  @reset-filter="resetFilter()"
                  @apply-filter="applyFilterFromFilterComponent($event)"
                /> -->
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="location-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <v-skeleton-loader
            v-if="trackingStatusLoading"
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <v-card
            v-else
            min-height="100"
            width="100%"
            :class="isMobileView ? '' : ''"
            class="pa-5"
          >
            <div
              class="d-flex align-center"
              :class="isMobileView ? 'mb-3' : 'mb-2'"
            >
              <v-progress-circular
                model-value="100"
                color="green"
                :size="22"
                class="mr-1"
              ></v-progress-circular>
              <p class="text-h6 text-grey-darken-1 font-weight-bold pl-2">
                {{ $t("settings.additionalScreenshots") }}
              </p>
            </div>
            <div class="ml-8">
              <p class="text-caption">
                {{ $t("settings.additionalScreenshotsHelp") }}
              </p>
            </div>
            <div :class="isMobileView ? '' : 'd-flex'">
              <div class="mt-5 ml-8 mr-5">
                <span class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("settings.screenshotSchedule") }}
                </span>
                <div class="pa-2 d-flex align-center" style="max-width: 500px">
                  <v-tooltip :text="$t('settings.accessDeniedMessage')">
                    <template v-slot:activator="{ props }">
                      <v-btn-toggle
                        v-model="editedScreenshots.screenshot_frequency_type"
                        rounded="lg"
                        mandatory
                        elevation="2"
                        v-bind="formAccess && formAccess.update ? '' : props"
                        :disabled="
                          formAccess && formAccess.update ? false : true
                        "
                      >
                        <v-btn
                          color="primary"
                          style="background-color: white; color: black"
                          >{{ $t("settings.fixed") }}</v-btn
                        >
                        <v-btn
                          color="primary"
                          style="background-color: white; color: black"
                          >{{ $t("settings.random") }}</v-btn
                        ></v-btn-toggle
                      >
                      <v-btn
                        size="small"
                        rounded="xl"
                        class="ml-5"
                        v-bind="formAccess && formAccess.update ? '' : props"
                        :readonly="
                          formAccess && formAccess.update ? false : true
                        "
                        @click="setAllEmployees('frequency')"
                        >{{ $t("settings.setAll") }}</v-btn
                      >
                    </template>
                  </v-tooltip>
                </div>
              </div>
              <div class="mt-5 ml-8">
                <span class="text-subtitle-1 text-grey-darken-1">
                  {{ $t("settings.additionalScreenshots") }}
                </span>
                <div
                  class="pa-2 d-flex align-center"
                  :style="{
                    width: isMobileView ? '200px' : '300px',
                  }"
                >
                  <v-tooltip :text="$t('settings.accessDeniedMessage')">
                    <template v-slot:activator="{ props }">
                      <div
                        class="pa-2 d-flex align-center"
                        :style="{
                          width: isMobileView ? '200px' : '300px',
                        }"
                        v-bind="formAccess && formAccess.update ? '' : props"
                      >
                        <v-tooltip
                          :text="
                            editedScreenshots.additional_screenshots_per_frequency
                          "
                          location="top"
                        >
                          <template v-slot:activator="{ props }">
                            <v-slider
                              v-model="
                                editedScreenshots.additional_screenshots_per_frequency
                              "
                              :max="7"
                              :step="1"
                              show-ticks="always"
                              v-bind="props"
                              :readonly="
                                formAccess && formAccess.update ? false : true
                              "
                              hide-details
                              color="primary"
                            ></v-slider>
                          </template>
                        </v-tooltip>

                        <v-btn
                          size="small"
                          rounded="xl"
                          class="ml-5"
                          :readonly="
                            formAccess && formAccess.update ? false : true
                          "
                          @click="setAllEmployees('number')"
                          >{{ $t("settings.setAll") }}</v-btn
                        >
                      </div>
                    </template>
                  </v-tooltip>
                </div>
              </div>
            </div>
          </v-card>

          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="$t('settings.retry')"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      :notes="$t('settings.additionalScreenshotsNote1')"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      :notes="$t('settings.additionalScreenshotsNote2')"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      {{ $t("settings.resetFilterSearch") }}
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="white"
                      rounded="lg"
                      class="ml-2 mt-1"
                      variant="text"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else class="mt-5">
            <div
              v-if="originalList.length > 0"
              class="d-flex align-center my-3"
              :class="isMobileView ? 'justify-center ' : 'justify-end'"
            >
              <v-btn
                rounded="lg"
                class="ml-2 mt-1"
                variant="text"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn
                    variant="plain"
                    class="mt-1 ml-n3 mr-n5"
                    v-bind="props"
                  >
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action"
                    @click="onMoreAction(action)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'pink-lighten-5': isHovering,
                          }"
                        >
                          <v-tooltip :text="action.message">
                            <template v-slot:activator="{ props }">
                              <div v-bind="action.message ? props : ''">
                                <v-icon size="15" class="pr-2">
                                  {{ action.icon }}
                                </v-icon>
                                {{ action.key }}
                              </div>
                            </template>
                          </v-tooltip>
                        </v-list-item-title>
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>

            <v-row>
              <v-col v-if="originalList.length > 0" :cols="12" class="mb-12">
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("settings.employeeName") }}
                        </div>
                        <section>
                          <div
                            class="text-body-2 font-weight-medium text-primary text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ item.employee_name }}
                          </div>
                          <div
                            class="text-subtitle-2 font-weight-regular text-grey"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ item.user_defined_empid }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                        @click.stop="
                          {
                          }
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("settings.additionalScreenshots") }}
                        </div>
                        <section
                          class="d-flex align-center"
                          style="max-width: 200px"
                        >
                          <v-tooltip :text="$t('settings.accessDeniedMessage')">
                            <template v-slot:activator="{ props }">
                              <div
                                class="d-flex align-center"
                                style="max-width: 200px; min-width: 200px"
                                v-bind="
                                  formAccess && !formAccess.update ? props : ''
                                "
                              >
                                <v-tooltip
                                  :text="
                                    item.additional_screenshots_per_frequency
                                  "
                                  location="top"
                                >
                                  <template v-slot:activator="{ props }">
                                    <v-slider
                                      v-model="
                                        item.additional_screenshots_per_frequency
                                      "
                                      :max="7"
                                      :step="1"
                                      show-ticks="always"
                                      thumb-label
                                      hide-details
                                      color="primary"
                                      v-bind="props"
                                      :readonly="
                                        formAccess && !formAccess.update
                                      "
                                      @update:model-value="
                                        onChangeEmployee(item)
                                      "
                                    ></v-slider> </template
                                ></v-tooltip>
                              </div>
                            </template>
                          </v-tooltip>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                        @click.stop="
                          {
                          }
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("settings.screenshotSchedule") }}
                        </div>
                        <section
                          class="d-flex align-center"
                          style="max-width: 200px"
                        >
                          <v-tooltip :text="$t('settings.accessDeniedMessage')">
                            <template v-slot:activator="{ props }">
                              <v-btn-toggle
                                v-model="item.screenshot_frequency_type"
                                rounded="lg"
                                density="compact"
                                mandatory
                                elevation="2"
                                v-bind="
                                  formAccess && formAccess.update ? '' : props
                                "
                                :disabled="
                                  formAccess && formAccess.update ? false : true
                                "
                                @update:model-value="onChangeEmployee(item)"
                              >
                                <v-btn
                                  color="primary"
                                  style="background-color: white; color: black"
                                  >{{ $t("settings.fixed") }}</v-btn
                                >
                                <v-btn
                                  color="primary"
                                  style="background-color: white; color: black"
                                  >{{ $t("settings.random") }}</v-btn
                                ></v-btn-toggle
                              >
                            </template>
                          </v-tooltip>
                        </section>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <bottom-sheet
      v-if="changes > 0"
      :open-alert="true"
      :changes-count="changes"
      @cancel="cancelChanges()"
      @save="updateAdditionalScreenshots()"
    ></bottom-sheet>
    <AppLoading v-if="isLoading"></AppLoading>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn class="mt-n5" variant="text" @click="closeValidationAlert()">
            {{ $t("settings.close") }}
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      :confirmation-heading="$t('settings.confirmationHeading')"
      @close-warning-modal="closeWarningModal()"
      @accept-modal="updateAdditionalScreenshots()"
    ></AppWarningModal>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import {
  RETRIEVE_EMP_MONITOR_SCREENSHOT_FREQUENCY_SETTINGS,
  UPDATE_EMP_MONITOR_SCREENSHOT_FREQUENCY_SETTINGS,
} from "@/graphql/settings/data-loss-prevention/additionalScreenshots.js";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const BottomSheet = defineAsyncComponent(() =>
  import("@/components/custom-components/BottomSheet.vue")
);
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "LocationTracing",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    BottomSheet,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      currentTabItem: "",
      isLoading: false,
      originalList: [],
      itemList: [],
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      trackingStatus: "",
      trackingStatusLoading: false,
      validationMessages: [],
      showValidationAlert: false,
      openWarningModal: false,
      editedScreenshots: {},
      additionalScreenshots: {},
      changedEmployees: [],
      openMoreMenu: false,
      count: 0,
      editedFrequency: "",
      frequency: "",
    };
  },
  computed: {
    landedFormName() {
      return this.$t("settings.additionalScreenshots");
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formAccess() {
      let formAccessRights = this.accessRights("300");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"] &&
        formAccessRights.accessRights["admin"] === "admin"
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    dataLossFormAccess() {
      return this.$store.getters.lossPreventionFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.dataLossFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access of formAccess) {
          if (access.havingAccess || access.formId === 300) {
            formAccessArray.push(this.$t(access.displayFormName));
          }
        }
        return formAccessArray;
      }
      return [];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = this.$t("settings.noRecordsFound", {
          formName: this.landedFormName.toLowerCase(),
        });
      }
      return msgText;
    },
    moreActions() {
      return [{ key: this.$t("settings.export"), icon: "fas fa-file-export" }];
    },
    tableHeaders() {
      return [
        {
          title: this.$t("settings.employeeName"),
          align: "start",
          key: "employee_name",
        },
        {
          title: this.$t("settings.additionalScreenshots"),
          key: "additional_screenshots_per_frequency",
        },
        {
          title: this.$t("settings.screenshotSchedule"),
          key: "screenshot_frequency_type",
        },
      ];
    },
    changes() {
      let change = 0;
      if (
        this.editedScreenshots.additional_screenshots_per_frequency !==
        this.additionalScreenshots.additional_screenshots_per_frequency
      ) {
        change += 1;
      }
      if (
        this.editedScreenshots.screenshot_frequency_type !==
        this.additionalScreenshots.screenshot_frequency_type
      ) {
        change += 1;
      }
      change += this.changedEmployees.length;
      return change;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.retrieveSettings();
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
  },
  methods: {
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.dataLossFormAccess;
        let clickedTab = formAccess.find(
          (form) => this.$t(form.displayFormName) === tab
        );
        if (clickedTab.isVue3) {
          this.$router.push("/settings/" + clickedTab.url);
        } else {
          window.location.href = this.baseUrl + "in/settings/" + clickedTab.url;
        }
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = JSON.parse(JSON.stringify(this.originalList));
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = JSON.parse(JSON.stringify(this.originalList));
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    closeWarningModal() {
      this.openWarningModal = false;
      this.editedKeyLoggingSetting = this.keyLoggingSetting;
    },
    onClickGlobalSetting() {
      this.openWarningModal = true;
    },
    updateKeyLoggingSetting() {
      this.openWarningModal = false;
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.retrieveSettings();
    },
    cancelChanges() {
      this.itemList = JSON.parse(JSON.stringify(this.originalList));
      this.editedScreenshots = JSON.parse(
        JSON.stringify(this.additionalScreenshots)
      );
      this.changedEmployees = [];
    },
    onChangeEmployee(item) {
      const index = this.changedEmployees.findIndex(
        (changedEmp) => changedEmp.employee_id === item.employee_id
      );
      if (index !== -1) {
        let idx = this.originalList.findIndex(
          (obj) => obj.employee_id == item.employee_id
        );
        if (
          item.additional_screenshots_per_frequency ==
            this.originalList[idx].additional_screenshots_per_frequency &&
          item.screenshot_frequency_type ==
            this.originalList[idx].screenshot_frequency_type
        ) {
          let array = this.changedEmployees;
          array.splice(index, 1);
          this.changedEmployees = array;
        } else {
          this.changedEmployees[index].additional_screenshots_per_frequency =
            item.additional_screenshots_per_frequency;
          this.changedEmployees[index].screenshot_frequency_type =
            item.screenshot_frequency_type;
        }
      } else {
        this.changedEmployees.push(item);
      }
    },
    setAllEmployees(type) {
      let newArray = this.itemList.map((obj) => {
        if (type == "number") {
          return {
            ...obj,
            additional_screenshots_per_frequency:
              this.editedScreenshots.additional_screenshots_per_frequency,
          };
        } else {
          return {
            ...obj,
            screenshot_frequency_type:
              this.editedScreenshots.screenshot_frequency_type,
          };
        }
      });
      this.itemList = newArray;
      this.changedEmployees = newArray;
    },
    retrieveSettings() {
      let vm = this;
      vm.listLoading = true;
      vm.trackingStatusLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_EMP_MONITOR_SCREENSHOT_FREQUENCY_SETTINGS,
          client: "apolloClientE",
          fetchPolicy: "no-cache",
          variables: {
            formId: 300,
          },
        })
        .then((response) => {
          let { data } = response;
          if (
            data &&
            data.retrieveScreenshotsFrequencySettings &&
            data.retrieveScreenshotsFrequencySettings.screenshotsFrequency
          ) {
            let {
              employeeLevelScreenshotsFrequencySettings,
              organizationLevelScreenshotsFrequencySettings,
            } = data.retrieveScreenshotsFrequencySettings.screenshotsFrequency;
            if (organizationLevelScreenshotsFrequencySettings) {
              this.additionalScreenshots = JSON.parse(
                JSON.stringify(organizationLevelScreenshotsFrequencySettings)
              );
              this.editedScreenshots = JSON.parse(
                JSON.stringify(organizationLevelScreenshotsFrequencySettings)
              );
              if (this.editedScreenshots.screenshot_frequency_type == "Fixed") {
                this.additionalScreenshots.screenshot_frequency_type = 0;
                this.editedScreenshots.screenshot_frequency_type = 0;
              } else {
                this.additionalScreenshots.screenshot_frequency_type = 1;
                this.editedScreenshots.screenshot_frequency_type = 1;
              }
            }
            if (
              employeeLevelScreenshotsFrequencySettings &&
              employeeLevelScreenshotsFrequencySettings.length > 0
            ) {
              let newArray = employeeLevelScreenshotsFrequencySettings.map(
                (obj) => {
                  if (obj.screenshot_frequency_type == "Fixed") {
                    obj.screenshot_frequency_type = 0;
                  } else if (obj.screenshot_frequency_type == "Random") {
                    obj.screenshot_frequency_type = 1;
                  }
                  return obj;
                }
              );
              this.originalList = JSON.parse(JSON.stringify(newArray));
              this.itemList = JSON.parse(JSON.stringify(newArray));
            }
          }
          vm.onApplySearch(this.searchValue);
          vm.listLoading = false;
          vm.trackingStatusLoading = false;
        })
        .catch((error) => {
          this.handleListError(error);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      this.trackingStatusLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "additional screenshots",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    updateAdditionalScreenshots() {
      let vm = this;
      this.isLoading = true;
      let newArray = [];
      if (this.changedEmployees.length > 0) {
        newArray = this.changedEmployees.map((obj) => {
          return {
            employeeId: obj.employee_id,
            captureScreenshot: obj.capture_screenshot,
            screenshotFrequency: obj.screenshot_frequency,
            numberOfScreenshots: obj.capture_screenshot
              ? obj.no_of_screenshots_per_frequency
              : 0,
            numberOfAdditionalScreenshots:
              obj.additional_screenshots_per_frequency,
            screenshotFrequencyType: obj.screenshot_frequency_type
              ? "Random"
              : "Fixed",
          };
        });
      }
      this.changedEmployees = [];
      vm.$apollo
        .mutate({
          mutation: UPDATE_EMP_MONITOR_SCREENSHOT_FREQUENCY_SETTINGS,
          variables: {
            formId: 300,
            organizationSettings: [
              {
                captureScreenshot: vm.editedScreenshots.capture_screenshot,
                screenshotFrequency: vm.editedScreenshots.screenshot_frequency,
                numberOfScreenshots:
                  vm.editedScreenshots.no_of_screenshots_per_frequency,
                numberOfAdditionalScreenshots:
                  vm.editedScreenshots.additional_screenshots_per_frequency,
                screenshotFrequencyType:
                  vm.editedScreenshots.screenshot_frequency_type == 1
                    ? "Random"
                    : "Fixed",
              },
            ],
            employeeSettings: newArray,
          },
          client: "apolloClientE",
        })
        .then((response) => {
          let { updateScreenshotFrequencySettings } = response.data;
          if (
            updateScreenshotFrequencySettings &&
            !updateScreenshotFrequencySettings.errorCode
          ) {
            this.changedEmployees = [];
            let snackbarData = {
              isOpen: true,
              message: this.$t("settings.screenshotsUpdatedSuccess"),
              type: "success",
            };
            vm.showAlert(snackbarData);
            this.refetchList();
          }
          vm.isLoading = false;
        })
        .catch((error) => {
          this.handleUpdateError(error);
        });
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "additional screenshots",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    onMoreAction(action) {
      if (action.key == this.$t("settings.export")) {
        this.exportReportFile();
      }
    },
    exportReportFile() {
      let newArray = this.itemList.map((item) => {
        return {
          ...item,
          screenshot_frequency_type: item.screenshot_frequency_type
            ? "Random"
            : "Fixed",
        };
      });
      let exportHeaders = [
        {
          header: this.$t("settings.employeeName"),
          key: "employee_name",
        },
        {
          header: this.$t("settings.employeeId"),
          key: "user_defined_empid",
        },
        {
          header: this.$t("settings.additionalScreenshotsPerFrequency"),
          key: "additional_screenshots_per_frequency",
        },
        {
          header: this.$t("settings.screenshotSchedule"),
          key: "screenshot_frequency_type",
        },
      ];
      let additionalScreenshots = newArray;
      let exportOptions = {
        fileExportData: additionalScreenshots,
        fileName: this.$t("settings.additionalScreenshotsSettings"),
        sheetName: this.$t("settings.additionalScreenshotsSettings"),
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
<style scoped>
.location-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 1252px) {
  .location-container {
    padding: 6em 1em 0em 1em;
  }
}

.icon-position {
  position: absolute;
  top: 10px;
  right: 10px;
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
