<template>
  <div>
    <div v-if="mainTabs.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row justify="center" v-if="originalList.length > 0">
            <v-col cols="12" md="9" class="d-flex justify-end">
              <EmployeeDefaultFilterMenu
                class="justify-end mr-8"
                :isFilter="false"
              >
              </EmployeeDefaultFilterMenu>
              <!-- <BusinessUnitFilter
                v-if="!showAddEditForm && (itemList.length || isFilterApplied)"
                ref="formFilterRef"
                @reset-filter="resetFilter()"
                @apply-filter="applyFilterFromFilterComponent($event)"
              /> -->
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>
    <v-container fluid class="location-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <v-skeleton-loader
            v-if="trackingStatusLoading"
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <v-card
            v-else
            min-height="100"
            width="100%"
            :class="isMobileView ? '' : ''"
            class="pa-5"
          >
            <div
              class="d-flex align-center"
              :class="isMobileView ? 'mb-3' : 'mb-2'"
            >
              <v-progress-circular
                model-value="100"
                color="green"
                :size="22"
                class="mr-1"
              ></v-progress-circular>
              <p class="text-h6 text-grey-darken-1 font-weight-bold pl-2">
                {{ $t("settings.locationTracking") }}
              </p>
            </div>
            <div class="ml-8">
              <p class="text-caption">
                {{ $t("settings.locationTrackingDescription") }}
              </p>
              <p class="text-caption">
                <v-progress-circular
                  model-value="100"
                  color="green"
                  :size="5"
                  class="mr-1"
                ></v-progress-circular>
                {{ $t("settings.ipAddressTypePublic") }}
              </p>
              <p class="text-caption">
                <v-progress-circular
                  model-value="100"
                  color="green"
                  :size="5"
                  class="mr-1"
                ></v-progress-circular>
                {{ $t("settings.addressFormatIPv4") }}
              </p>
              <p class="text-caption">
                <v-progress-circular
                  model-value="100"
                  color="green"
                  :size="5"
                  class="mr-1"
                ></v-progress-circular>
                {{ $t("settings.ipRangeSpecify") }}
              </p>
              <p class="text-caption">
                {{ $t("settings.ipAddressImportantNote") }}
              </p>
            </div>
            <div class="mt-5 ml-8 d-flex align-center">
              <span class="text-subtitle-1 text-grey-darken-1">{{
                $t("settings.enableLocationTracking")
              }}</span>
              <AppToggleButton
                ref="appToggleButton"
                :button-active-text="
                  trackingStatus == 'Enabled'
                    ? $t('settings.enabled')
                    : $t('settings.enable')
                "
                :button-inactive-text="
                  trackingStatus == 'Enabled'
                    ? $t('settings.disable')
                    : $t('settings.disabled')
                "
                button-active-color="#7de272"
                button-inactive-color="red"
                id-value="gab-analysis-based-on"
                :class="'ml-2'"
                :current-value="trackingStatus === 'Enabled' ? true : false"
                :isDisableToggle="!formAccess.update"
                :tooltipContent="
                  formAccess.update
                    ? ''
                    : $t('settings.accessDeniedUpdateMessage')
                "
                @chosen-value="clickEnableLocation($event)"
              ></AppToggleButton>
            </div>
          </v-card>

          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="$t('settings.retry')"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0 && !showAddEditForm"
            key="no-results-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList.length === 0"
            :image-name="originalList.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div v-if="!isSmallTable" style="max-width: 80%">
                <v-row
                  :style="originalList.length === 0 ? 'background: white' : ''"
                  class="rounded-lg pa-5 mb-4"
                >
                  <v-col v-if="originalList.length === 0" cols="12">
                    <NotesCard
                      :notes="$t('settings.locationTrackingNote1')"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                    <NotesCard
                      :notes="$t('settings.locationTrackingNote2')"
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList.length === 0 && formAccess.add"
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="openAddForm"
                    >
                      <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                      {{ $t("settings.addNew") }}
                    </v-btn>
                    <v-btn
                      v-if="originalList.length > 0"
                      color="primary"
                      variant="elevated"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="resetFilter"
                    >
                      {{ $t("settings.resetFilterSearch") }}
                    </v-btn>
                    <v-btn
                      v-if="originalList.length === 0"
                      color="white"
                      rounded="lg"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <div v-else class="mt-5">
            <div
              v-if="originalList.length > 0 && !isSmallTable"
              class="d-flex align-center my-3"
              :class="isMobileView ? 'justify-center ' : 'justify-end'"
            >
              <v-btn
                v-if="formAccess.add"
                prepend-icon="fas fa-plus"
                color="primary"
                rounded="lg"
                class="mx-1"
                :size="isMobileView ? 'small' : 'default'"
                @click="openAddForm"
              >
                <template v-slot:prepend>
                  <v-icon></v-icon>
                </template>
                {{ $t("settings.addNew") }}
              </v-btn>
              <v-btn
                rounded="lg"
                class="ml-2 mt-1"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn
                    variant="plain"
                    class="mt-1 ml-n3 mr-n5"
                    v-bind="props"
                  >
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in moreActions"
                    :key="action"
                    @click="onMoreAction(action)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'pink-lighten-5': isHovering,
                          }"
                        >
                          <v-tooltip :text="action.message">
                            <template v-slot:activator="{ props }">
                              <div v-bind="action.message ? props : ''">
                                <v-icon size="15" class="pr-2">
                                  {{ action.icon }}
                                </v-icon>
                                {{ action.key }}
                              </div>
                            </template>
                          </v-tooltip>
                        </v-list-item-title>
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>

            <v-row>
              <v-col
                v-if="originalList.length > 0"
                :cols="isSmallTable ? 5 : 12"
                class="mb-12"
              >
                <v-data-table
                  :headers="tableHeaders"
                  :items="itemList"
                  fixed-header
                  :height="
                    $store.getters.getTableHeightBasedOnScreenSize(
                      290,
                      itemList
                    )
                  "
                  :items-per-page="50"
                  :items-per-page-options="[
                    { value: 50, title: '50' },
                    { value: 100, title: '100' },
                    {
                      value: -1,
                      title: '$vuetify.dataFooter.itemsPerPageAll',
                    },
                  ]"
                >
                  <template v-slot:item="{ item }">
                    <tr
                      @click="openViewForm(item)"
                      class="data-table-tr bg-white cursor-pointer"
                      :class="
                        isMobileView ? ' v-data-table__mobile-table-row' : ''
                      "
                    >
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("settings.workLocation") }}
                        </div>
                        <section class="d-flex align-center">
                          <div
                            v-if="
                              isSmallTable &&
                              !isMobileView &&
                              selectedItem &&
                              selectedItem.officeIpAddressId ===
                                item.officeIpAddressId
                            "
                            class="data-table-side-border d-flex"
                          ></div>
                          <div
                            class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ item.workLocationName }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("settings.workLocation") }}
                        </div>
                        <section class="d-flex align-center">
                          <div
                            class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 300px; '
                                : 'max-width: 200px; '
                            "
                          >
                            {{ item.ipRange }}
                          </div>
                        </section>
                      </td>
                      <td
                        :class="
                          isMobileView ? 'd-flex justify-space-between' : ''
                        "
                        :style="
                          isMobileView ? `width: ${windowWidth - 40}px` : ''
                        "
                      >
                        <div
                          v-if="isMobileView"
                          class="text-subtitle-1 text-grey-darken-1 mt-2"
                        >
                          {{ $t("settings.workLocation") }}
                        </div>
                        <div
                          @click.stop="
                            {
                            }
                          "
                        >
                          <AppToggleButton
                            :key="count"
                            :button-active-text="$t('settings.active')"
                            :button-inactive-text="$t('settings.inactive')"
                            button-active-color="#7de272"
                            button-inactive-color="red"
                            id-value="gab-analysis-based-on"
                            :current-value="
                              item.status === 'Active' ? true : false
                            "
                            :isDisableToggle="!formAccess.update"
                            :tooltipContent="
                              formAccess.update
                                ? ''
                                : $t('settings.accessDeniedUpdateMessage')
                            "
                            @chosen-value="updateStatus($event, item)"
                          ></AppToggleButton>
                        </div>
                      </td>
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
              <v-col cols="7" v-if="showViewForm && windowWidth >= 1264">
                <view-location-tracking
                  :selectedItem="selectedItem"
                  :access-rights="formAccess"
                  :landedFormName="landedFormName"
                  @close-form="closeAllForms()"
                  @open-edit-form="openEditForm()"
                />
              </v-col>
              <v-col
                :cols="originalList.length === 0 ? 12 : 7"
                v-if="showAddEditForm && windowWidth >= 1264"
              >
                <AddEditLocationTracking
                  :isEdit="isEdit"
                  :editFormData="selectedItem"
                  :landedFormName="landedFormName"
                  @close-form="closeAllForms()"
                  @edit-updated="refetchList()"
                />
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <AppLoading v-if="isLoading"></AppLoading>
    <v-dialog
      :model-value="openFormInModal"
      class="pl-4"
      width="900"
      @click:outside="closeAllForms()"
    >
      <AddEditLocationTracking
        v-if="showAddEditForm"
        :isEdit="isEdit"
        :landed-form-name="landedFormName"
        :editFormData="selectedItem"
        @close-form="closeAllForms()"
        @edit-updated="refetchList()"
      />
      <ViewLocationTracking
        v-if="showViewForm"
        :landed-form-name="landedFormName"
        :selectedItem="selectedItem"
        :isEdit="isEdit"
        :access-rights="formAccess"
        @close-form="closeAllForms()"
        @open-edit-form="openEditForm()"
      />
    </v-dialog>
    <AppSnackBar
      v-if="showValidationAlert"
      :show-snack-bar="showValidationAlert"
      snack-bar-type="warning"
      timeOut="-1"
      @close-snack-bar="closeValidationAlert"
    >
      <template #custom-alert>
        <div
          v-for="(validationMsg, index) of validationMessages"
          :key="validationMsg + index"
          class="text-subtitle-1"
        >
          {{ validationMsg }}
        </div>
        <div class="d-flex justify-end">
          <v-btn
            color="secondary"
            class="mt-n5"
            variant="text"
            @click="closeValidationAlert()"
          >
            {{ $t("settings.close") }}
          </v-btn>
        </div>
      </template>
    </AppSnackBar>
    <AppWarningModal
      v-if="openWarningModal"
      :open-modal="openWarningModal"
      :confirmation-heading="$t('settings.confirmationHeading')"
      @close-warning-modal="
        (openWarningModal = false), retrieveWorkLocationList()
      "
      @accept-modal="updateLocationTrackingSetting(newStatus)"
    ></AppWarningModal>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const ViewLocationTracking = defineAsyncComponent(() =>
  import("./ViewLocationTracking.vue")
);
const AddEditLocationTracking = defineAsyncComponent(() =>
  import("./AddEditLocationTracking.vue")
);
import {
  RETRIEVE_LOCATION_TRACKING_LIST,
  UPDATE_LOCATION_TRACKING_SETTING,
  ADD_UPDATE_WORK_LOCATION,
} from "@/graphql/settings/data-loss-prevention/locationTrackingSettingQueries";
import FileExportMixin from "@/mixins/FileExportMixin";
export default {
  name: "LocationTracing",
  components: {
    EmployeeDefaultFilterMenu,
    NotesCard,
    ViewLocationTracking,
    AddEditLocationTracking,
  },
  mixins: [FileExportMixin],
  data() {
    return {
      currentTabItem: "",
      isLoading: false,
      originalList: [],
      itemList: [],
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      trackingStatus: "",
      trackingStatusLoading: false,
      showAddEditForm: false,
      showViewForm: false,
      openMoreMenu: false,
      selectedItem: null,
      isEdit: false,
      validationMessages: [],
      showValidationAlert: false,
      openWarningModal: false,
      newStatus: [],
      count: 0,
    };
  },
  computed: {
    landedFormName() {
      return this.$t("settings.locationTracking");
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formAccess() {
      let formAccessRights = this.accessRights("284");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"] &&
        formAccessRights.accessRights["admin"] === "admin"
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    dataLossFormAccess() {
      return this.$store.getters.lossPreventionFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } = this.dataLossFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access of formAccess) {
          if (access.havingAccess || access.formId === 284) {
            formAccessArray.push(this.$t(access.displayFormName));
          }
        }
        return formAccessArray;
      }
      return [];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length > 0) {
        msgText = this.$t("settings.noRecordsFound", {
          formName: this.landedFormName.toLowerCase(),
        });
      }
      return msgText;
    },
    isSmallTable() {
      return (
        !this.openFormInModal && (this.showAddEditForm || this.showViewForm)
      );
    },
    openFormInModal() {
      if (
        (this.showAddEditForm || this.showViewForm) &&
        this.windowWidth < 1264
      ) {
        return true;
      }
      return false;
    },
    moreActions() {
      return [{ key: this.$t("settings.export"), icon: "fas fa-file-export" }];
    },
    tableHeaders() {
      return [
        {
          title: this.$t("settings.workLocation"),
          align: "start",
          key: "workLocationName",
        },
        {
          title: this.$t("settings.ipRange"),
          key: "ipRange",
        },
        {
          title: this.$t("settings.status"),
          key: "status",
          width: "20%",
        },
      ];
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.retrieveWorkLocationList();
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
  },
  methods: {
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.dataLossFormAccess;
        let clickedTab = formAccess.find(
          (form) => this.$t(form.displayFormName) === tab
        );
        if (clickedTab.isVue3) {
          this.$router.push("/settings/" + clickedTab.url);
        } else {
          window.location.href = this.baseUrl + "in/settings/" + clickedTab.url;
        }
      }
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    refetchList() {
      this.isErrorInList = false;
      this.errorContent = "";
      this.closeAllForms();
      this.retrieveWorkLocationList();
    },
    closeAllForms() {
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedItem = null;
      this.isEdit = false;
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
      this.showAddEditForm = false;
    },
    openEditForm() {
      this.isEdit = true;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },
    openAddForm() {
      this.isEdit = false;
      this.showViewForm = false;
      this.showAddEditForm = true;
    },

    retrieveWorkLocationList() {
      let vm = this;
      vm.listLoading = true;
      vm.trackingStatusLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_LOCATION_TRACKING_LIST,
          fetchPolicy: "no-cache",
          client: "apolloClientK",
        })
        .then((res) => {
          let { data } = res;
          if (
            data &&
            data.getofficeIpAddress &&
            data.getofficeIpAddress.officeIpAddressData &&
            data.getofficeIpAddress.officeIpAddressData.length > 0
          ) {
            this.originalList = data.getofficeIpAddress.officeIpAddressData;
            this.itemList = data.getofficeIpAddress.officeIpAddressData;
          }
          if (
            data &&
            data.getofficeIpAddress &&
            data.getofficeIpAddress.orgLevelofficeIpAddressData &&
            data.getofficeIpAddress.orgLevelofficeIpAddressData.length > 0
          ) {
            this.trackingStatus =
              data.getofficeIpAddress.orgLevelofficeIpAddressData[0].status;
          }
          vm.trackingStatusLoading = false;
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.listLoading = false;
      vm.trackingStatusLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "work location",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    updateLocationTrackingSetting(status) {
      let vm = this;
      vm.trackingStatusLoading = true;
      vm.openWarningModal = false;
      vm.$apollo
        .mutate({
          mutation: UPDATE_LOCATION_TRACKING_SETTING,
          client: "apolloClientR",
          variables: {
            orgLocationTrackingId: 1,
            status: status[1] ? "Enabled" : "Disabled",
          },
          fetchPolicy: "no-cache",
        })
        .then(() => {
          vm.trackingStatusLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: this.$t("settings.locationTrackingSettingsUpdatedSuccess"),
          };
          vm.showAlert(snackbarData);
          vm.trackingStatus = status[1] ? "Enabled" : "Disabled";
        })
        .catch((err) => {
          vm.handleLocationSettingError(err);
        });
    },
    handleLocationSettingError(err = "") {
      this.listLoading = false;
      this.trackingStatusLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "location tracking setting",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    updateStatus(status, item) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_WORK_LOCATION,
          variables: {
            officeIpAddressId: item.officeIpAddressId,
            workLocationName: item.workLocationName,
            ipRange: item.ipRange,
            status: status[1] ? "Active" : "Inactive",
          },
          client: "apolloClientR",
        })
        .then(() => {
          vm.isLoading = false;
          var snackbarData = {
            isOpen: true,
            type: "success",
            message: vm.isEdit
              ? this.$t("settings.workLocationAddedSuccess")
              : this.$t("settings.workLocationUpdatedSuccess"),
          };
          vm.showAlert(snackbarData);
        })
        .catch((addEditError) => {
          vm.handleAddUpdateError(addEditError);
        });
    },
    handleAddUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: this.isEdit ? "updating" : "adding",
          form: this.landedFormName.toLowerCase(),
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
      this.refetchList();
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    onMoreAction(action) {
      if (action.key == this.$t("settings.export")) {
        this.exportReportFile();
      }
    },
    clickEnableLocation(action) {
      let newStatus = "";
      action[1] ? (newStatus = "Enabled") : (newStatus = "Disabled");
      if (this.trackingStatus.toLowerCase() !== newStatus.toLowerCase()) {
        this.newStatus = action;
        this.openWarningModal = true;
      }
    },
    exportReportFile() {
      let exportHeaders = [
        {
          header: this.$t("settings.workLocation"),
          key: "workLocationName",
        },
        {
          header: this.$t("settings.ipRange"),
          key: "ipRange",
        },
        {
          header: this.$t("settings.status"),
          key: "status",
        },
        {
          header: this.$t("settings.addedOn"),
          key: "addedOn",
        },
        {
          header: this.$t("settings.addedBy"),
          key: "addedBy",
        },
        {
          header: this.$t("settings.updatedOn"),
          key: "updatedOn",
        },
        {
          header: this.$t("settings.updatedBy"),
          key: "updatedBy",
        },
      ];
      let LocationSettings = this.itemList;
      let exportOptions = {
        fileExportData: LocationSettings,
        fileName:
          this.$t("settings.locationTracking") +
          " - " +
          this.$t("settings.settings"),
        sheetName:
          this.$t("settings.locationTracking") +
          " - " +
          this.$t("settings.settings"),
        header: exportHeaders,
      };
      this.exportExcelFile(exportOptions);
    },
  },
};
</script>
<style scoped>
.location-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 1252px) {
  .location-container {
    padding: 6em 1em 0em 1em;
  }
}

.icon-position {
  position: absolute;
  top: 10px;
  right: 10px;
}

.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}
</style>
