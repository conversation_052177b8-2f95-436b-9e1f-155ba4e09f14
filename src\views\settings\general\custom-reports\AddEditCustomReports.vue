<template>
  <v-overlay
    :model-value="showDialog"
    @click:outside="isFormDirty ? (openConfirmationPopup = true) : closeForm()"
    persistent
    class="d-flex justify-end"
    id="integration-form"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="{
          width: windowWidth >= 770 ? '50vw' : '100vw',
          height: windowHeight + 'px',
        }"
      >
        <v-card-title
          :class="
            windowWidth < 770
              ? ' d-flex bg-white justify-end align-center fixed-title'
              : 'd-flex bg-primary justify-space-between align-center fixed-title'
          "
        >
          <div v-if="windowWidth >= 770" class="text-h6 text-medium ps-2">
            {{ isEdit ? "Edit" : "Clone" }}
            {{ landedFormName }}
          </div>

          <div v-else class="d-flex align-center">
            <v-btn
              rounded="lg"
              class="mr-3"
              variant="outlined"
              @click="
                isFormDirty ? (openConfirmationPopup = true) : closeForm()
              "
            >
              Cancel
            </v-btn>
            <v-btn
              rounded="lg"
              class="primary"
              variant="elevated"
              @click="saveCustomReport()"
            >
              {{ isEdit ? "Update" : "Clone" }}
            </v-btn>
          </div>
          <v-btn
            v-if="windowWidth >= 770"
            icon
            class="clsBtn"
            variant="text"
            @click="isFormDirty ? (openConfirmationPopup = true) : closeForm()"
          >
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text
          class="card mb-3 px-0"
          :style="{
            'overflow-y': 'auto',
            'padding-bottom': '50px',
          }"
        >
          <div class="px-5 py-6">
            <v-form ref="customReportForm">
              <v-row class="mb-2">
                <!-- Report Title -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <v-text-field
                    v-model="formData.Report_Title"
                    label="Report Title"
                    variant="solo"
                    density="comfortable"
                    disabled
                  />
                </v-col>
                <!-- Custom Title -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <v-text-field
                    ref="customTitleField"
                    v-model="formData.Custom_Report_Title"
                    variant="solo"
                    density="comfortable"
                    :rules="[
                      required('Custom Title', formData.Custom_Report_Title),
                      validateWithRulesAndReturnMessages(
                        formData.custom_title,
                        'customReportTitle',
                        'Custom Title'
                      ),
                    ]"
                    @update:modelValue="
                      handleFormFieldChange('Custom_Report_Title')
                    "
                  >
                    <template v-slot:label>
                      Custom Title
                      <span style="color: red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <!-- Module Name -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <v-text-field
                    v-model="formData.Module_Name"
                    label="Module Name"
                    variant="solo"
                    density="comfortable"
                    disabled
                  />
                </v-col>
                <!-- Form Name -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <v-text-field
                    v-model="formData.Form_Name"
                    label="Form Name"
                    variant="solo"
                    density="comfortable"
                    disabled
                  />
                </v-col>
                <!-- Report Category -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <v-text-field
                    v-model="formData.Report_Group_Name"
                    label="Report Category"
                    variant="solo"
                    density="comfortable"
                    disabled
                    @update:model-value="
                      handleFormFieldChange('Report_Group_Name')
                    "
                  />
                </v-col>
                <!-- Visibility -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <div class="mb-3">
                    <v-switch
                      v-model="formData.Report_Visibility"
                      label="Visibility"
                      true-value="Yes"
                      false-value="No"
                      density="compact"
                      color="primary"
                      @change="handleFormFieldChange('Report_Visibility')"
                    />
                  </div>
                </v-col>
                <!-- Default Sorting -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <CustomSelect
                    ref="defaultSortingField"
                    v-model="formData.Default_Sorting"
                    :items="sortingOptions"
                    label="Default Sorting"
                    :isAutoComplete="true"
                    :itemSelected="formData.Default_Sorting"
                    @selected-item="handleFormFieldChange('Default_Sorting')"
                    :is-required="true"
                    :rules="[
                      required('Default Sorting', formData.Default_Sorting),
                    ]"
                    item-title="title"
                    item-value="value"
                    clearable
                  />
                </v-col>
                <!-- Group By -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <CustomSelect
                    ref="groupByField"
                    v-model="formData.Group_By"
                    :items="groupByOptions"
                    label="Group By"
                    :isAutoComplete="true"
                    :itemSelected="formData.Group_By"
                    @selected-item="handleFormFieldChange('Group_By')"
                    item-title="title"
                    item-value="value"
                    clearable
                  />
                </v-col>
                <!-- Report Header Template -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <CustomSelect
                    ref="headerTemplateField"
                    v-model="formData.Report_Header_Id"
                    :items="headerTemplateOptions"
                    :isAutoComplete="true"
                    :itemSelected="formData.Report_Header_Id"
                    label="Report Header Template"
                    item-title="title"
                    item-value="value"
                    clearable
                    @selected-item="handleFormFieldChange('Report_Header_Id')"
                  />
                </v-col>
                <!-- Report Footer Template -->
                <v-col cols="12" sm="6" class="px-md-6">
                  <CustomSelect
                    ref="footerTemplateField"
                    v-model="formData.Report_Footer_Id"
                    :items="footerTemplateOptions"
                    :isAutoComplete="true"
                    :itemSelected="formData.Report_Footer_Id"
                    label="Report Footer Template"
                    item-title="title"
                    item-value="value"
                    clearable
                    @selected-item="handleFormFieldChange('Report_Footer_Id')"
                  />
                </v-col>
                <!-- Headers Configuration -->
                <v-col cols="12" class="px-md-6">
                  <div class="mb-3">
                    <label
                      class="text-subtitle-2 text-grey-darken-1 mb-2 d-block"
                    >
                      Headers Configuration <span class="text-red">*</span>
                    </label>
                    <v-card variant="outlined" class="pa-4">
                      <div class="mb-4">
                        <div class="text-subtitle-2 text-grey-darken-1 mb-3">
                          Manage Headers Order and Visibility
                        </div>

                        <div class="border rounded overflow-hidden">
                          <!-- Table Header -->
                          <v-row
                            class="bg-grey-lighten-4 pa-2 ma-0"
                            style="border-bottom: 1px solid #e0e0e0"
                            no-gutters
                          >
                            <v-col cols="1" class="text-center">
                              <span class="text-caption font-weight-bold"
                                >Order</span
                              >
                            </v-col>
                            <v-col cols="9">
                              <span class="text-caption font-weight-bold"
                                >Header Label</span
                              >
                            </v-col>
                            <v-col cols="2" class="text-center">
                              <span class="text-caption font-weight-bold"
                                >Visible</span
                              >
                            </v-col>
                          </v-row>

                          <!-- Draggable Headers List -->
                          <draggable
                            v-model="unifiedHeaders"
                            item-key="key"
                            handle=".drag-handle"
                            @change="onHeadersReorder"
                            style="min-height: 200px"
                          >
                            <template #item="{ element: item, index }">
                              <v-row
                                class="header-row-item pa-2 ma-0 transition-swing"
                                style="border-bottom: 1px solid #f5f5f5"
                                no-gutters
                                :class="{
                                  'bg-grey-lighten-5': !item.visible,
                                }"
                              >
                                <!-- Drag Handle -->
                                <v-col
                                  cols="1"
                                  class="d-flex align-center justify-center"
                                >
                                  <v-icon
                                    class="drag-handle cursor-move"
                                    color="grey-darken-1"
                                    size="small"
                                    style="
                                      opacity: 0.6;
                                      transition: opacity 0.2s ease;
                                    "
                                  >
                                    fas fa-grip-vertical
                                  </v-icon>
                                </v-col>

                                <!-- Header Label with Inline Editing and Edit Button -->
                                <v-col cols="9" class="d-flex align-center">
                                  <div
                                    v-if="item.isEditing"
                                    class="d-flex align-center gap-2 w-100"
                                  >
                                    <v-text-field
                                      v-model="item.editedLabel"
                                      variant="outlined"
                                      density="compact"
                                      hide-details
                                      :rules="[
                                        required(
                                          'Header Label',
                                          item.editedLabel
                                        ),
                                        minMaxStringValidation(
                                          'Header Label',
                                          item.editedLabel,
                                          1,
                                          100
                                        ),
                                      ]"
                                      @keyup.enter="saveHeaderLabel(index)"
                                      @keyup.escape="cancelHeaderEdit(index)"
                                      @update:modelValue="isFormDirty = true"
                                      autofocus
                                      class="flex-grow-1"
                                    />
                                    <v-btn
                                      icon
                                      size="small"
                                      color="success"
                                      variant="text"
                                      @click="saveHeaderLabel(index)"
                                    >
                                      <v-icon size="small">fas fa-check</v-icon>
                                    </v-btn>
                                    <v-btn
                                      icon
                                      size="small"
                                      color="error"
                                      variant="text"
                                      @click="cancelHeaderEdit(index)"
                                    >
                                      <v-icon size="small">fas fa-times</v-icon>
                                    </v-btn>
                                  </div>
                                  <div
                                    v-else
                                    class="d-flex align-center justify-space-between w-100"
                                  >
                                    <div class="d-flex align-center gap-2">
                                      <v-btn
                                        icon
                                        size="small"
                                        variant="text"
                                        @click="startHeaderEdit(index)"
                                        :disabled="item.isEditing"
                                      >
                                        <v-icon size="small"
                                          >fas fa-edit</v-icon
                                        >
                                      </v-btn>
                                      <span
                                        :class="{
                                          'text-grey': !item.visible,
                                        }"
                                        class="text-body-2 font-weight-medium"
                                      >
                                        {{ item.label }}
                                      </span>
                                    </div>
                                  </div>
                                </v-col>

                                <!-- Visibility Toggle -->
                                <v-col
                                  cols="2"
                                  class="d-flex align-center justify-center"
                                >
                                  <v-switch
                                    v-model="item.visible"
                                    color="primary"
                                    density="compact"
                                    hide-details
                                    @change="updateHeaderVisibility"
                                  />
                                </v-col>
                              </v-row>
                            </template>
                          </draggable>
                        </div>
                      </div>

                      <!-- Note about restrictions -->
                      <v-alert
                        type="info"
                        variant="tonal"
                        density="compact"
                        class="mt-3"
                      >
                        <div class="text-caption">
                          Note: You can reorder headers by dragging, edit labels
                          by clicking the edit icon, and control visibility
                          using the toggle switches. You cannot add or delete
                          headers.
                        </div>
                      </v-alert>
                    </v-card>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </div>
        </v-card-text>
      </v-card>
      <v-card
        v-if="windowWidth >= 770"
        class="overlay-footer bottom-0 position-fixed w-100"
        elevation="16"
      >
        <div class="d-flex justify-end pa-4">
          <v-btn
            rounded="lg"
            class="mr-6"
            variant="outlined"
            @click="isFormDirty ? (openConfirmationPopup = true) : closeForm()"
          >
            Cancel
          </v-btn>
          <v-btn
            rounded="lg"
            class="primary"
            variant="elevated"
            :disabled="
              !isFormDirty && unifiedHeaders.some((item) => item.visible)
            "
            @click="saveCustomReport()"
          >
            {{ isEdit ? "Update" : "Clone" }}
          </v-btn>
        </div>
      </v-card>
    </template>
  </v-overlay>
  <AppLoading v-if="isLoading" />
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="closeForm()"
  />
</template>

<script>
import { defineAsyncComponent } from "vue";
import validationRules from "@/mixins/validationRules";
import { CLONE_AND_UPDATE_REPORT_DEFINITION } from "@/graphql/my-team/leaves.js";
import draggable from "vuedraggable";

const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);

export default {
  name: "AddEditCustomReports",
  components: {
    CustomSelect,
    draggable,
  },
  mixins: [validationRules],
  props: {
    selectedItem: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isClone: {
      type: Boolean,
      default: false,
    },
    landedFormName: {
      type: String,
      default: "Custom Report",
    },
    originalList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showDialog: true,
      isLoading: false,
      newHeader: "",
      isFormDirty: false,
      openConfirmationPopup: false,
      formData: {
        Report_Id: null,
        Report_Title: "",
        Custom_Report_Title: "",
        Module_Name: "",
        Form_Name: "",
        Report_Group_Name: "",
        Report_Visibility: "Yes",
        Default_Sorting: null,
        Group_By: null,
        Default_Headers: {},
        Custom_Headers: {},
        Report_Header_Id: null,
        Report_Footer_Id: null,
        Report_Type: "Custom",
      },
      // Enhanced headers management
      unifiedHeaders: [],
      headerTemplateOptions: [],
      footerTemplateOptions: [],
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    sortingOptions() {
      const defaultHeaders = this.formData.Default_Headers || {};
      const customHeaders = this.formData.Custom_Headers || {};
      const allHeaders = { ...defaultHeaders, ...customHeaders };

      return Object.keys(allHeaders).map((key) => ({
        title: allHeaders[key],
        value: key,
      }));
    },
    groupByOptions() {
      const defaultHeaders = this.formData.Default_Headers || {};
      const customHeaders = this.formData.Custom_Headers || {};
      const allHeaders = { ...defaultHeaders, ...customHeaders };

      return Object.keys(allHeaders).map((key) => ({
        title: allHeaders[key],
        value: key,
      }));
    },
  },
  mounted() {
    this.prefillForm();
  },
  methods: {
    prefillForm() {
      if ((this.isEdit || this.isClone) && this.selectedItem) {
        this.formData = {
          ...this.formData,
          ...this.selectedItem,
          // Parse JSON strings for headers
          Default_Headers: this.parseHeadersJSON(
            this.selectedItem.Default_Headers
          ),
          Custom_Headers: this.parseHeadersJSON(
            this.selectedItem.Custom_Headers
          ),
        };
      } else {
        // Set default values for new report
        this.formData.Report_Title = "New Report Template";
        this.formData.Module_Name = "Core HR";
        this.formData.Form_Name = "Employee Performance";
        this.formData.Report_Group_Name = "Performance";
        // Set sample headers for new reports
        this.formData.Default_Headers = {};
        this.formData.Custom_Headers = {};
      }

      // Initialize unified headers
      this.initializeUnifiedHeaders();
    },
    // Parse JSON strings for headers
    parseHeadersJSON(headersData) {
      if (!headersData) return {};

      // If it's already an object, return as is
      if (typeof headersData === "object" && !Array.isArray(headersData)) {
        return headersData;
      }

      // If it's a string, try to parse it
      if (typeof headersData === "string") {
        try {
          return JSON.parse(headersData);
        } catch {
          return {};
        }
      }

      // If it's an array (legacy format), convert to object
      if (Array.isArray(headersData)) {
        const headerObj = {};
        headersData.forEach((header) => {
          headerObj[header] = header;
        });
        return headerObj;
      }

      return {};
    },

    // Enhanced headers management methods
    initializeUnifiedHeaders() {
      this.unifiedHeaders = [];

      // Process Custom_Headers first (they should appear first and be enabled)
      const customHeaders = this.formData.Custom_Headers || {};
      const defaultHeaders = this.formData.Default_Headers || {};

      // Add custom headers first (these are visible/enabled)
      Object.keys(customHeaders).forEach((key) => {
        this.unifiedHeaders.push({
          key: key,
          originalKey: key,
          label: customHeaders[key],
          defaultLabel: defaultHeaders[key] || key,
          type: "custom",
          visible: true,
          isEditing: false,
          editedLabel: customHeaders[key],
          order: this.unifiedHeaders.length,
        });
      });

      // Add remaining default headers that are not in custom headers (these are disabled/hidden)
      Object.keys(defaultHeaders).forEach((key) => {
        if (!customHeaders.hasOwnProperty(key)) {
          this.unifiedHeaders.push({
            key: key,
            originalKey: key,
            label: defaultHeaders[key],
            defaultLabel: defaultHeaders[key],
            type: "default",
            visible: false,
            isEditing: false,
            editedLabel: defaultHeaders[key],
            order: this.unifiedHeaders.length,
          });
        }
      });
    },
    startHeaderEdit(index) {
      const header = this.unifiedHeaders[index];
      header.isEditing = true;
      header.editedLabel = header.label;
      this.isFormDirty = true;
    },
    saveHeaderLabel(index) {
      const header = this.unifiedHeaders[index];
      if (header.editedLabel && header.editedLabel.trim()) {
        header.label = header.editedLabel.trim();
        header.isEditing = false;
        this.updateOriginalHeaders();
        this.isFormDirty = true;
      }
    },
    cancelHeaderEdit(index) {
      const header = this.unifiedHeaders[index];
      header.editedLabel = header.label;
      header.isEditing = false;
    },
    updateHeaderVisibility() {
      this.isFormDirty = true;
      // Update the original headers objects when visibility changes
      this.updateOriginalHeaders();
    },
    updateOriginalHeaders() {
      // Update the Custom_Headers object based on visibility
      // Custom_Headers should only contain visible fields with their custom labels
      const customHeaders = {};

      this.unifiedHeaders.forEach((header) => {
        if (header.visible) {
          customHeaders[header.key] = header.label;
        }
      });

      this.formData.Custom_Headers = customHeaders;

      // Default_Headers remains unchanged as it contains the original field mappings
      // It's used as reference for available fields
    },
    onHeadersReorder() {
      // Update order property for each header
      this.unifiedHeaders.forEach((header, index) => {
        header.order = index;
      });
      this.isFormDirty = true;
    },
    async saveCustomReport() {
      const { valid } = await this.$refs.customReportForm.validate();

      if (!valid) return;

      // Update original headers before saving
      // this.updateOriginalHeaders();

      this.isLoading = true;

      try {
        const variables = {
          action: this.isEdit ? "update" : "clone",
          reportId: this.formData.Report_Id,
          customReportTitle: this.formData.Custom_Report_Title,
          reportVisibility: this.formData.Report_Visibility,
          // Convert headers objects to JSON strings for API
          customHeaders: JSON.stringify(this.formData.Custom_Headers),
          reportHeaderId: this.formData.Report_Header_Id,
          reportFooterId: this.formData.Report_Footer_Id,
        };

        const response = await this.$apollo.mutate({
          mutation: CLONE_AND_UPDATE_REPORT_DEFINITION,
          client: "apolloClientAD",
          variables,
        });

        if (!response.data?.addUpdateReportDefinition?.errorCode) {
          const snackbarData = {
            isOpen: true,
            type: "success",
            message:
              response.data?.addUpdateReportDefinition?.message ||
              `Custom report ${
                this.isEdit ? "updated" : "cloned"
              } successfully`,
          };
          this.showAlert(snackbarData);
          this.$emit("custom-fields-updated");
          this.closeForm();
        } else
          this.handleAppUpdateErrors(
            response.data?.addUpdateReportDefinition?.errorCode || ""
          );
      } catch (error) {
        this.handleAppUpdateErrors(error);
      } finally {
        this.isLoading = false;
      }
    },
    handleAppUpdateErrors(error = "") {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: this.isEdit ? "updating" : "cloning",
        form: this.landedFormName,
        isListError: false,
      });
    },
    closeForm() {
      this.showDialog = false;
      this.isFormDirty = false;
      this.$emit("close-form");
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    // Generic method to handle form field changes and set dirty state
    handleFormFieldChange() {
      this.isFormDirty = true;
    },
  },
};
</script>

<style scoped>
.overlay-card {
  overflow-y: auto;
  justify-content: flex-start;
}

.fixed-title {
  position: sticky;
  top: 0;
  z-index: 10;
}

.overlay-footer {
  height: 60px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 10px;
  z-index: 2000;
}
#integration-form > .v-overlay__content {
  height: 100%;
  width: 50%;
}
.gap-2 {
  gap: 8px;
}

/* Custom styles for enhanced headers management */
/* Custom hover effects - cannot be achieved with Vuetify utilities alone */
.drag-handle:hover {
  opacity: 1 !important;
}

/* Row hover effect for better UX */
.header-row-item:hover {
  background-color: #fafafa !important;
}

/* Remove border from last item */
.header-row-item:last-child {
  border-bottom: none !important;
}

/* Vuedraggable ghost and chosen states - library-specific classes */
.sortable-ghost {
  opacity: 0.5;
  background-color: #f5f5f5;
}

.sortable-chosen {
  background-color: #e3f2fd;
}
</style>
