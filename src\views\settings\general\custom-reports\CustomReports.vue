<template>
  <div>
    <div v-if="mainTabs?.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      >
        <template #topBarContent>
          <v-row v-if="originalList?.length" justify="center">
            <v-col
              cols="12"
              md="9"
              class="d-flex justify-end"
              v-if="!showAddEditForm && !showViewForm"
            >
              <EmployeeDefaultFilterMenu
                class="d-flex justify-end"
                :isFilter="false"
              />
              <CustomReportsFilter
                ref="formFilterRef"
                :original-list="originalList"
                @filter-applied="onFilterApplied"
                @filter-reset="resetFilter"
              />
            </v-col>
          </v-row>
        </template>
      </AppTopBarTab>
    </div>

    <v-container fluid class="custom-reports-container">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            button-text="Retry"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="originalList?.length === 0"
            key="no-data-screen"
            :main-title="emptyScenarioMsg"
            :isSmallImage="originalList?.length === 0"
            :image-name="originalList?.length === 0 ? '' : 'common/no-records'"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                  <v-col cols="12">
                    <NotesCard
                      notes="The Custom Reports module provides a flexible and structured framework for defining and managing organization-level report templates with enhanced functionality. Custom Reports can be configured with advanced customization options, allowing organizations to create tailored report templates that meet specific business requirements and improve data analysis capabilities."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                    <NotesCard
                      notes="With the ability to create custom fields for each form, organizations can ensure that forms are aligned with specific requirements, whether it’s for onboarding, compliance, or any other operational need. This approach supports organizations in managing forms for different services, and departments, allowing them to easily modify field labels, options, and settings based on local regulations and requirements. The system ensures that only admins have the authority to make changes, providing a controlled environment that safeguards the integrity of the data while allowing for tailored experiences across different contexts."
                      backgroundColor="transparent"
                      class="mb-4"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      v-if="originalList?.length === 0"
                      color="transparent"
                      rounded="lg"
                      variant="flat"
                      class="ml-2 mt-1"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList('Refetch List')"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="itemList.length === 0"
            key="no-results-screen"
            main-title="There are no custom reports matched for the selected filters/searches."
            image-name="common/no-records"
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      variant="elevated"
                      color="primary"
                      class="ml-4 mt-1"
                      rounded="lg"
                      :size="windowWidth <= 960 ? 'small' : 'default'"
                      @click="resetFilter('grid')"
                    >
                      <span class="primary">Reset Filter/Search </span>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <div v-else>
            <div>
              <div
                v-if="originalList?.length"
                class="d-flex flex-wrap align-center my-3"
                :class="isMobileView ? 'flex-column' : ''"
                style="justify-content: space-between"
              >
                <div
                  class="d-flex align-center"
                  :class="isMobileView ? 'justify-center' : ''"
                />

                <div
                  class="d-flex align-center"
                  :class="isMobileView ? 'justify-center' : 'justify-end'"
                >
                  <v-btn
                    rounded="lg"
                    color="transparent"
                    variant="flat"
                    class="mt-1"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList('Refetch List')"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                  <v-menu v-model="openMoreMenu" transition="scale-transition">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        variant="plain"
                        class="mt-1 ml-n1 mr-n5"
                        v-bind="props"
                      >
                        <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                        <v-icon v-else>fas fa-caret-up</v-icon>
                      </v-btn>
                    </template>
                    <v-list>
                      <v-list-item
                        v-for="action in moreActions"
                        :key="action"
                        @click="onMoreAction(action)"
                      >
                        <v-hover>
                          <template v-slot:default="{ isHovering, props }">
                            <v-list-item-title
                              v-bind="props"
                              class="pa-3"
                              :class="{
                                'pink-lighten-5': isHovering,
                              }"
                              ><v-icon size="15" class="pr-2">{{
                                action.icon
                              }}</v-icon
                              >{{ action.key }}</v-list-item-title
                            >
                          </template>
                        </v-hover>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>

              <v-row>
                <v-col v-if="originalList?.length" class="mb-12">
                  <v-data-table
                    :headers="tableHeaders"
                    :items="itemList"
                    fixed-header
                    :height="
                      $store.getters.getTableHeightBasedOnScreenSize(
                        290,
                        itemList
                      )
                    "
                    :items-per-page="50"
                    :items-per-page-options="[
                      { value: 50, title: '50' },
                      { value: 100, title: '100' },
                      {
                        value: -1,
                        title: '$vuetify.dataFooter.itemsPerPageAll',
                      },
                    ]"
                  >
                    <template v-slot:item="{ item }">
                      <tr
                        @click="openViewForm(item)"
                        class="data-table-tr bg-white cursor-pointer"
                        :class="
                          isMobileView ? ' v-data-table__mobile-table-row' : ''
                        "
                      >
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Custom Title
                          </div>
                          <v-tooltip
                            :text="item.Custom_Report_Title"
                            location="bottom"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <section
                                class="text-subtitle-1 font-weight-regular text-truncate"
                                v-bind="
                                  item.Custom_Report_Title?.length > 30
                                    ? props
                                    : ''
                                "
                                :style="
                                  !isMobileView
                                    ? 'max-width: 500px; '
                                    : 'max-width: 200px; '
                                "
                              >
                                {{ checkNullValue(item.Custom_Report_Title) }}
                                <div
                                  v-if="item.Report_Title"
                                  class="text-body-2 text-grey-darken-1 text-truncate"
                                >
                                  {{ item.Report_Title }}
                                </div>
                              </section>
                            </template>
                          </v-tooltip>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Module Name
                          </div>
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 500px;'
                                : 'max-width: 200px;'
                            "
                          >
                            {{ checkNullValue(item.Module_Name) }}
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Form Name
                          </div>
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 500px;'
                                : 'max-width: 200px;'
                            "
                          >
                            {{ checkNullValue(item.Form_Name) }}
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Report Category
                          </div>
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 500px;'
                                : 'max-width: 200px;'
                            "
                          >
                            {{ checkNullValue(item.Report_Group_Name) }}
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView
                              ? `width: ${windowWidth - 40}px`
                              : 'width: max-content'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1"
                          >
                            Visibility
                          </div>
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 500px;'
                                : 'max-width: 200px;'
                            "
                          >
                            {{ checkNullValue(item.Report_Visibility) }}
                          </section>
                        </td>
                        <td
                          :class="
                            isMobileView ? 'd-flex justify-space-between' : ''
                          "
                          :style="
                            isMobileView ? `width: ${windowWidth - 40}px` : ''
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="text-subtitle-1 text-grey-darken-1 mt-2"
                          >
                            Type
                          </div>
                          <section
                            class="text-subtitle-1 font-weight-regular text-truncate"
                            :style="
                              !isMobileView
                                ? 'max-width: 500px;'
                                : 'max-width: 200px;'
                            "
                          >
                            {{ checkNullValue(item.Report_Type) }}
                          </section>
                        </td>

                        <td
                          :class="
                            isMobileView
                              ? 'd-flex justify-center align-center'
                              : 'pa-2 pl-5'
                          "
                        >
                          <div
                            v-if="isMobileView"
                            class="font-weight-bold d-flex justify-center align-center"
                            style="width: 100%"
                          >
                            Actions
                          </div>
                          <section
                            class="d-flex justify-start align-center"
                            style="width: 100%"
                          >
                            <ActionMenu
                              v-if="itemActions(item)?.length > 0"
                              :accessRights="checkAccess"
                              @selected-action="onActions($event, item)"
                              :actions="itemActions(item)"
                              iconColor="grey"
                            ></ActionMenu>
                            <div v-else>
                              <p>-</p>
                            </div>
                          </section>
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </div>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <ViewCustomReports
      v-if="showViewForm"
      :selected-item="selectedItem"
      :form-access="formAccess"
      :landed-form-name="landedFormName"
      @open-edit-form="onOpenEditForm()"
      @close-view-form="onCloseOverlay()"
    />
    <AddEditCustomReports
      v-if="showAddEditForm"
      :selected-item="selectedItem"
      :list-mapping-keys="listMappingKeys"
      :landed-form-name="landedFormName"
      :original-list="originalList"
      :is-edit="isEdit"
      :is-clone="isClone"
      @custom-fields-updated="refetchList()"
      @close-form="onCloseOverlay()"
    />
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
import { checkNullValue, convertUTCToLocal } from "@/helper";
import FileExportMixin from "@/mixins/FileExportMixin";
const AddEditCustomReports = defineAsyncComponent(() =>
  import("./AddEditCustomReports.vue")
);
const ViewCustomReports = defineAsyncComponent(() =>
  import("./ViewCustomReports.vue")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const CustomReportsFilter = defineAsyncComponent(() =>
  import("./CustomReportsFilter.vue")
);
import { GET_REPORT_DEFINITIONS_LIST } from "@/graphql/my-team/leaves.js";
export default {
  name: "CustomReports",
  data() {
    return {
      isEdit: false,
      isClone: false,
      selectedItem: null,
      showViewForm: false,
      showAddEditForm: false,
      listLoading: false,
      dropdownLoading: false,
      itemList: [],
      originalList: [],
      openMoreMenu: false,
      currentTabItem: "",
      isErrorInList: false,
      errorContent: "",
      limitToCallAPI: 2500,
      totalApiCount: 0,
      apiCallCount: 0,
    };
  },
  components: {
    EmployeeDefaultFilterMenu,
    ActionMenu,
    NotesCard,
    ViewCustomReports,
    AddEditCustomReports,
    CustomReportsFilter,
  },
  mixins: [FileExportMixin],
  computed: {
    checkAccess() {
      let havingAccess = {};
      havingAccess["update"] = this.formAccess?.update ? 1 : 0;
      havingAccess["clone"] = this.formAccess?.add ? 1 : 0;
      havingAccess["delete"] = this.formAccess?.delete ? 1 : 0;
      return havingAccess;
    },
    landedFormName() {
      let customFieldForm = this.accessRights("380");
      if (
        customFieldForm?.customFormName &&
        customFieldForm.customFormName !== ""
      ) {
        return customFieldForm.customFormName;
      } else return "Report";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    multipleAccessRights() {
      return this.$store.getters.formIdsBasedAccessRights;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    settingsGeneralFormAccess() {
      return this.$store.getters.settingsGeneralFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.settingsGeneralFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formAccess() {
      let formAccessRights = this.accessRights("380");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    emptyScenarioMsg() {
      let msgText = "";
      if (this.originalList.length) {
        msgText = `There are no ${this.landedFormName.toLowerCase()} for the selected filters/searches.`;
      }
      return msgText;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    moreActions() {
      let moreActions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return moreActions;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    tableHeaders() {
      return [
        {
          title: "Custom Title",
          align: "start",
          key: "Custom_Report_Title",
        },
        {
          title: "Module Name",
          key: "Module_Name",
        },
        {
          title: "Form Name",
          key: "Form_Name",
        },
        {
          title: "Report Category",
          key: "Report_Group_Name",
        },
        {
          title: "Visibility",
          key: "Report_Visibility",
        },
        {
          title: "Type",
          key: "Report_Type",
        },
        {
          title: "Actions",
          key: "actions",
          sortable: false,
        },
      ];
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.fetchList();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    fetchList() {
      let vm = this;
      vm.listLoading = true;
      vm.isErrorInList = false;
      vm.errorContent = "";
      vm.$apollo
        .query({
          query: GET_REPORT_DEFINITIONS_LIST,
          client: "apolloClientAC",
          variables: {
            limit: vm.limitToCallAPI,
            offset: 0,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getReportDefinitionsList &&
            response.data.getReportDefinitionsList.reportDefinitions &&
            !response.data.getReportDefinitionsList.errorCode
          ) {
            let tempData =
              response.data.getReportDefinitionsList.reportDefinitions || [];
            vm.originalList = tempData;
            vm.itemList = tempData;

            let totalCount =
              response.data.getReportDefinitionsList.totalRecords;
            if (totalCount > 0) {
              totalCount = parseInt(totalCount);
              vm.apiCallCount = 1;
              vm.totalApiCount = Math.ceil(totalCount / vm.limitToCallAPI);
              for (let i = 1; i < vm.totalApiCount; i++) {
                vm.updateListReports(i);
              }
            }
            vm.listLoading = false;
          } else {
            vm.handleListError(
              response.data.getReportDefinitionsList?.errorCode || ""
            );
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    updateListReports(index = 1) {
      let vm = this;
      vm.listLoading = true;
      let apiOffset = parseInt(index) * vm.limitToCallAPI;
      apiOffset = parseInt(apiOffset);
      vm.$apollo
        .query({
          query: GET_REPORT_DEFINITIONS_LIST,
          client: "apolloClientAC",
          variables: {
            limit: vm.limitToCallAPI,
            offset: apiOffset,
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getReportDefinitionsList &&
            response.data.getReportDefinitionsList.reportDefinitions &&
            !response.data.getReportDefinitionsList.errorCode
          ) {
            const tempData =
              response.data.getReportDefinitionsList.reportDefinitions || [];
            vm.originalList = [...vm.originalList, ...tempData];
            vm.itemList = [...vm.itemList, ...tempData];

            vm.apiCallCount = vm.apiCallCount + 1;
            if (vm.totalApiCount === vm.apiCallCount) {
              vm.listLoading = false;
            }
          } else {
            vm.handleListError(
              response.data.getReportDefinitionsList?.errorCode || ""
            );
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.itemList = [];
      this.originalList = [];
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    refetchList() {
      this.openMoreMenu = false;
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.selectedForm = null;
      this.selectedItem = null;
      this.isEdit = false;
      this.isClone = false;
      this.resetFilter();
      this.fetchList();
    },
    openViewForm(item) {
      this.selectedItem = item;
      this.showViewForm = true;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onOpenEditForm() {
      this.isEdit = true;
      this.showAddEditForm = true;
    },
    onCloseOverlay() {
      this.selectedItem = null;
      this.showAddEditForm = false;
      this.showViewForm = false;
      this.isEdit = false;
      this.isClone = false;
      this.resetFilter();
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.settingsGeneralFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/general/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/general/" + clickedForm.url;
        }
      }
    },
    onActions(action, item) {
      this.selectedItem = item;
      if (action?.toLowerCase() === "edit") {
        this.isEdit = true;
        this.showAddEditForm = true;
      } else if (action?.toLowerCase() === "clone") {
        this.isClone = true;
        this.showAddEditForm = true;
      } else if (action?.toLowerCase() === "delete") {
        // this.deleteCustomReport(item);
      }
    },
    onMoreAction(actionType) {
      if (actionType.key === "Export") {
        this.exportReportFile(actionType);
      }
      this.openMoreMenu = false;
    },
    onFilterApplied(filteredData) {
      this.itemList = filteredData;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    onApplySearch(val) {
      if (!val) {
        this.itemList = this.originalList;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.originalList;
        searchItems = searchItems.filter((item) => {
          let matches = false;
          matches =
            item.Report_Title?.toLowerCase().includes(searchValue) ||
            item.Custom_Report_Title?.toLowerCase().includes(searchValue) ||
            item.Module_Name?.toLowerCase().includes(searchValue) ||
            item.Form_Name?.toLowerCase().includes(searchValue) ||
            item.Report_Group_Name?.toLowerCase().includes(searchValue) ||
            item.Report_Type?.toLowerCase().includes(searchValue);

          return matches;
        });
        this.itemList = searchItems;
      }
    },
    resetFilter() {
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      // this.$refs.formFilterRef
      //   ? this.$refs.formFilterRef.resetAllModelValues()
      //   : "";
      this.itemList = this.originalList;
    },
    exportReportFile() {
      let exportData = JSON.parse(JSON.stringify(this.originalList));

      exportData = exportData.map((el) => ({
        ...el,
        Report_Visibility: el.Report_Visibility ? "Visible" : "Hidden",
        Default_Headers: Array.isArray(el.Default_Headers)
          ? el.Default_Headers.join(", ")
          : el.Default_Headers,
        Custom_Headers: Array.isArray(el.Custom_Headers)
          ? el.Custom_Headers.join(", ")
          : el.Custom_Headers,
      }));

      // Export options
      let exportOptions = {
        fileExportData: exportData,
        fileName: this.landedFormName,
        sheetName: this.landedFormName,
        header: [
          { header: "Report ID", key: "Report_Id" },
          { header: "Report Title", key: "Report_Title" },
          { header: "Custom Title", key: "Custom_Report_Title" },
          { header: "Module Name", key: "Module_Name" },
          { header: "Form Name", key: "Form_Name" },
          { header: "Report Category", key: "Report_Group_Name" },
          { header: "Visibility", key: "Report_Visibility" },
          { header: "Type", key: "Report_Type" },
          { header: "Report Header ID", key: "Report_Header_Id" },
          { header: "Report Footer ID", key: "Report_Footer_Id" },
          { header: "Default Headers", key: "Default_Headers" },
          { header: "Custom Headers", key: "Custom_Headers" },
        ],
      };

      this.exportExcelFile(exportOptions);
    },
    itemActions(item) {
      let tabs = [];
      if (this.formAccess?.update) tabs.push("Edit");
      if (this.formAccess?.add) tabs.push("Clone");
      if (
        this.formAccess?.delete &&
        item?.Report_Type?.toLowerCase() !== "default"
      )
        tabs.push("Delete");
      return tabs;
    },
  },
};
</script>

<style>
.custom-reports-container {
  padding: 5em 2em 0em 3em;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
  border-left: 7px solid rgb(var(--v-theme-primary)) !important;
}

@media screen and (max-width: 805px) {
  .custom-reports-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
