<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="primary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2 px-2">
          <v-col cols="12" md="6" lg="6">
            <CustomSelect
              v-model="filters.moduleNames"
              :items="moduleNameOptions"
              label="Module Name"
              :select-properties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              item-title="title"
              item-value="value"
              placeholder="Select module names"
              density="compact"
            />
          </v-col>
          <v-col cols="12" md="6" lg="6">
            <CustomSelect
              v-model="filters.formNames"
              :items="formNameOptions"
              label="Form Name"
              :select-properties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              item-title="title"
              item-value="value"
              placeholder="Select form names"
              density="compact"
            />
          </v-col>
          <v-col cols="12" md="6" lg="6">
            <CustomSelect
              v-model="filters.reportCategories"
              :items="reportCategoryOptions"
              label="Report Category"
              :select-properties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              item-title="title"
              item-value="value"
              placeholder="Select report categories"
              density="compact"
            />
          </v-col>
          <v-col cols="12" md="6" lg="6">
            <CustomSelect
              v-model="filters.visibility"
              :items="visibilityOptions"
              label="Visibility"
              :select-properties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              item-title="title"
              item-value="value"
              placeholder="Select visibility"
              density="compact"
            />
          </v-col>
          <v-col cols="12" md="6" lg="6">
            <CustomSelect
              v-model="filters.types"
              :items="typeOptions"
              label="Type"
              :select-properties="{
                multiple: true,
                chips: true,
                closableChips: true,
                clearable: true,
              }"
              item-title="title"
              item-value="value"
              placeholder="Select types"
              density="compact"
            />
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 primary"
          rounded="lg"
          @click.stop="applyFilters()"
        >
          Apply
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilters()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineAsyncComponent } from "vue";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);

export default {
  name: "CustomReportsFilter",
  components: {
    CustomSelect,
  },
  data() {
    return {
      openFormFilter: false,
      filters: {
        moduleNames: [],
        formNames: [],
        reportCategories: [],
        visibility: [],
        types: [],
      },
    };
  },

  props: {
    originalList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    activeFiltersCount() {
      let count = 0;
      Object.values(this.filters).forEach((filter) => {
        if (Array.isArray(filter) && filter.length > 0) {
          count++;
        }
      });
      return count;
    },
    moduleNameOptions() {
      const uniqueModules = [
        ...new Set(this.originalList.map((item) => item.Module_Name)),
      ];
      return uniqueModules.map((module) => ({
        title: module,
        value: module,
      }));
    },
    formNameOptions() {
      const uniqueForms = [
        ...new Set(this.originalList.map((item) => item.Form_Name)),
      ];
      return uniqueForms.map((form) => ({
        title: form,
        value: form,
      }));
    },
    reportCategoryOptions() {
      const uniqueCategories = [
        ...new Set(this.originalList.map((item) => item.Report_Group_Name)),
      ];
      return uniqueCategories.map((category) => ({
        title: category,
        value: category,
      }));
    },
    visibilityOptions() {
      return [
        { title: "Visible", value: true },
        { title: "Hidden", value: false },
      ];
    },
    typeOptions() {
      const uniqueTypes = [
        ...new Set(this.originalList.map((item) => item.Report_Type)),
      ];
      return uniqueTypes.map((type) => ({
        title: type,
        value: type,
      }));
    },
  },
  methods: {
    applyFilters() {
      let filteredData = [...this.originalList];

      // Apply module name filter
      if (this.filters.moduleNames.length > 0) {
        filteredData = filteredData.filter((item) =>
          this.filters.moduleNames.includes(item.Module_Name)
        );
      }

      // Apply form name filter
      if (this.filters.formNames.length > 0) {
        filteredData = filteredData.filter((item) =>
          this.filters.formNames.includes(item.Form_Name)
        );
      }

      // Apply report category filter
      if (this.filters.reportCategories.length > 0) {
        filteredData = filteredData.filter((item) =>
          this.filters.reportCategories.includes(item.Report_Group_Name)
        );
      }

      // Apply visibility filter
      if (this.filters.visibility.length > 0) {
        filteredData = filteredData.filter((item) =>
          this.filters.visibility.includes(item.Report_Visibility)
        );
      }

      // Apply type filter
      if (this.filters.types.length > 0) {
        filteredData = filteredData.filter((item) =>
          this.filters.types.includes(item.Report_Type)
        );
      }

      this.$emit("filter-applied", filteredData);
      this.openFormFilter = false;
    },
    resetFilters() {
      this.filters = {
        moduleNames: [],
        formNames: [],
        reportCategories: [],
        visibility: [],
        types: [],
      };
      this.$emit("filter-reset");
      this.openFormFilter = false;
    },
    resetAllModelValues() {
      this.resetFilters();
    },
  },
};
</script>

<style scoped>
.filter-card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.gap-2 {
  gap: 8px;
}
</style>
