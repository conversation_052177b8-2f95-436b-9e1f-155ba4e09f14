<template>
  <v-overlay
    :model-value="showDialog"
    @click:outside="onCloseOverlay()"
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card position-relative"
        :style="
          windowWidth <= 1264
            ? 'width:100vw; height: 100vh'
            : 'width:50vw; height: 100vh'
        "
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center fixed-title"
        >
          <div class="text-h6 text-medium ps-2">View {{ landedFormName }}</div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="card mb-3 px-0" style="overflow-y: auto">
          <div class="d-flex justify-end align-center">
            <v-btn
              v-if="formAccess?.update"
              @click="openEditForm()"
              class="mr-3 mt-3 text-primary"
              variant="text"
              rounded="lg"
            >
              <v-icon class="mr-1" size="15">fas fa-edit</v-icon>Edit
            </v-btn>
          </div>
          <div class="px-6 py-2">
            <v-row>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Report Title
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Report_Title) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Custom Title
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Custom_Report_Title) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Module Name
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Module_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Form Name</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Form_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Report Category
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Report_Group_Name) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Type</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Report_Type) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Visibility</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Report_Visibility) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Report Header(s)
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(visibleHeadersText) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Default Sorting
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Default_Sorting) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">Group By</div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Group_By) }}
                  </section>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 pb-0 my-3">
                <div class="text-subtitle-1 text-grey-darken-1">
                  Report Footer
                </div>
                <div class="text-subtitle-1 font-weight-regular">
                  <section class="text-body-2">
                    {{ checkNullValue(selectedItem.Report_Footer_Id) }}
                  </section>
                </div>
              </v-col>
            </v-row>
            <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                />
              </v-col>
            </v-row>
          </div>
        </v-card-text>
      </v-card>
    </template>
  </v-overlay>
</template>

<script>
import { checkNullValue, convertUTCToLocal } from "@/helper.js";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";

export default {
  name: "ViewCustomReports",
  components: {
    MoreDetails,
  },
  props: {
    selectedItem: {
      type: Object,
      default: () => ({}),
    },
    formAccess: {
      type: Object,
      default: () => ({}),
    },
    landedFormName: {
      type: String,
      default: "Custom Report",
    },
  },
  data() {
    return {
      showDialog: true,
      moreDetailsList: [],
      openMoreDetails: true,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    visibleHeadersList() {
      // Parse Custom_Headers to get visible fields with their labels
      const customHeaders = this.parseHeadersJSON(
        this.selectedItem.Custom_Headers
      );
      const defaultHeaders = this.parseHeadersJSON(
        this.selectedItem.Default_Headers
      );

      // Create list of visible headers with labels and keys
      const visibleHeaders = [];

      // Custom_Headers contains only visible fields, so iterate through them
      Object.keys(customHeaders).forEach((key) => {
        visibleHeaders.push({
          key: key,
          label: customHeaders[key],
          defaultLabel: defaultHeaders[key] || key,
        });
      });

      return visibleHeaders;
    },
    visibleHeadersText() {
      // Return simple comma-separated list of visible header labels
      if (this.visibleHeadersList.length === 0) {
        return this.checkNullValue("");
      }

      const labels = this.visibleHeadersList.map((header) => header.label);
      return labels.join(", ");
    },
  },
  mounted() {
    this.prefillMoreDetails();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    // Parse JSON strings for headers (same logic as AddEditCustomReports)
    parseHeadersJSON(headersData) {
      if (!headersData) return {};

      // If it's already an object, return as is
      if (typeof headersData === "object" && !Array.isArray(headersData)) {
        return headersData;
      }

      // If it's a string, try to parse it
      if (typeof headersData === "string") {
        try {
          return JSON.parse(headersData);
        } catch {
          return {};
        }
      }

      // If it's an array (legacy format), convert to object
      if (Array.isArray(headersData)) {
        const headerObj = {};
        headersData.forEach((header) => {
          headerObj[header] = header;
        });
        return headerObj;
      }

      return {};
    },
    onCloseOverlay() {
      this.showDialog = false;
      this.$emit("close-view-form");
    },
    openEditForm() {
      this.$emit("open-edit-form");
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.selectedItem.Added_On,
        addedByName = this.selectedItem.Added_By_Name,
        updatedByName = this.selectedItem.Updated_By_Name,
        updatedOn = this.selectedItem.Updated_On;

      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>

<style scoped>
.overlay-card {
  height: 100vh;
  overflow-y: auto;
  justify-content: flex-start;
}
.fixed-title {
  position: sticky;
  top: 0;
  z-index: 10;
}
.bottom-space {
  margin-top: 200px;
}

@media screen and (max-width: 960px) {
  .v-card-text {
    padding: 16px !important;
  }
}
</style>
