<template>
  <v-overlay
    v-if="overlay"
    v-model="overlay"
    :persistent="true"
    @click:outside="onCloseOverlay()"
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card"
        :style="{
          height: windowHeight + 'px',
          width: windowWidth >= 1264 ? '50vw' : '100vw',
        }"
      >
        <v-card-title
          class="position-fixed w-100"
          style="z-index: 2000"
          :class="
            windowWidth < 770
              ? ' d-flex bg-white justify-end align-center'
              : 'd-flex bg-primary justify-space-between align-center'
          "
        >
          <div v-if="windowWidth >= 770" class="text-h6 text-medium ps-2">
            {{ isEditForm ? "Edit" : isCloneForm ? "Clone" : "Add" }}
            Email Template
          </div>
          <div v-else class="d-flex align-center">
            <v-btn
              rounded="lg"
              class="mr-6 primary"
              @click="onCloseOverlay()"
              variant="outlined"
            >
              Cancel
            </v-btn>
            <v-btn
              rounded="lg"
              class="mr-1 primary"
              @click="submitEmailTemplateForm()"
              variant="elevated"
              :disabled="!isFormDirty"
            >
              Submit
            </v-btn>
          </div>
          <v-btn
            v-if="windowWidth >= 770"
            icon
            class="clsBtn"
            variant="text"
            @click="onCloseOverlay()"
          >
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text
          class="card mb-3 px-0"
          style="overflow-y: auto; margin-top: 50px"
        >
          <v-form ref="refEmailTemplateForm">
            <v-row class="px-6 pt-6">
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="templateName"
                  :counter="150"
                  :rules="[
                    required('Template Name', templateName),
                    validateWithRulesAndReturnMessages(
                      templateName,
                      'templateName',
                      'Template Name'
                    ),
                  ]"
                  variant="solo"
                  @input="isFormDirty = true"
                >
                  <template v-slot:label>
                    {{ "Template Name " }}<span style="color: red">*</span>
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <CustomSelect
                  label="Form"
                  :item-selected="selectedForm"
                  v-model="selectedForm"
                  item-title="formName"
                  item-value="formId"
                  :items="formList"
                  :isRequired="true"
                  :isAutoComplete="true"
                  clearable
                  :rules="[required('Form', selectedForm)]"
                  @selected-item="isFormDirty = true"
                  @update:modelValue="updateDropDown"
                ></CustomSelect>
              </v-col>
              <v-col
                v-if="selectedForm && categoryList?.length"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 my-2"
              >
                <CustomSelect
                  v-model="selectedCategory"
                  :items="categoryList"
                  item-title="Category_Name"
                  item-value="Category_Id"
                  label="Category"
                  :isRequired="true"
                  :is-loading="dropdownLoading"
                  :isAutoComplete="true"
                  :clearable="true"
                  :rules="[required('Category', selectedCategory)]"
                  :itemSelected="selectedCategory"
                  @selected-item="isFormDirty = true"
                  @update:modelValue="updateCategoryTypeDropDown"
                />
              </v-col>
              <v-col
                v-if="selectedCategory && categoryTypeList?.length"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 my-2"
              >
                <CustomSelect
                  v-model="selectedCategoryType"
                  :items="categoryTypeList"
                  item-title="Category_Type_Name"
                  item-value="Category_Type_Id"
                  label="Category Type"
                  :isRequired="true"
                  :is-loading="dropdownLoading"
                  :isAutoComplete="true"
                  :clearable="true"
                  :rules="[required('Category Type', selectedCategoryType)]"
                  :itemSelected="selectedCategoryType"
                  @selected-item="isFormDirty = true"
                />
              </v-col>
              <!-- For future use -->
              <!-- <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <CustomSelect
                  v-model="selectedVisibility"
                  :items="visibilityList"
                  label="Visibility"
                  item-title="Visibility"
                  :isRequired="true"
                  :isAutoComplete="true"
                  :clearable="true"
                  :rules="[required('Visibility', selectedVisibility)]"
                  :itemSelected="selectedVisibility"
                  item-value="Visibility"
                  @selected-item="isFormDirty = true"
                />
              </v-col> -->
              <v-col cols="12" sm="6" md="6" class="px-md-6 mt-2">
                <div class="d-flex">
                  <span class="v-label pr-3 pb-5"
                    >Make this as default template?</span
                  >
                  <v-switch
                    v-model="isdefaultTemplate"
                    color="primary"
                    true-value="Yes"
                    false-value="No"
                  />
                </div>
              </v-col>
              <v-col
                v-if="selectedForm && emailsToSendList?.length"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 my-2"
              >
                <v-autocomplete
                  v-model="selectedEmailsToSend"
                  color="primary"
                  :items="emailsToSendList"
                  label="To"
                  multiple
                  variant="solo"
                  chips
                  closable-chips="true"
                >
                  <template #append-inner>
                    <span
                      v-if="
                        !selectedCcEmails?.length > 0 &&
                        !showCcField &&
                        !showBccField
                      "
                      @mousedown.stop.prevent="showHideCcField"
                      style="cursor: pointer"
                    >
                      Cc
                    </span>
                    <span
                      v-if="
                        !selectedBccEmails?.length > 0 &&
                        !showBccField &&
                        !showCcField
                      "
                      @mousedown.stop.prevent="showHideBCcField"
                      class="ml-1"
                      style="cursor: pointer"
                    >
                      Bcc
                    </span>
                  </template></v-autocomplete
                >
              </v-col>
              <v-col
                v-if="
                  selectedCcEmails?.length ||
                  (showCcField && selectedForm && ccEmailList?.length)
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 my-2"
              >
                <v-autocomplete
                  v-model="selectedCcEmails"
                  color="primary"
                  :items="ccEmailList"
                  label="Cc"
                  multiple
                  variant="solo"
                  chips
                  closable-chips="true"
                  ><template #append-inner>
                    <span
                      v-if="!selectedBccEmails?.length > 0 && !showBccField"
                      @mousedown.stop.prevent="showHideBCcField"
                      class="ml-1"
                      style="cursor: pointer"
                    >
                      Bcc
                    </span>
                  </template></v-autocomplete
                >
              </v-col>
              <v-col
                v-if="
                  selectedBccEmails?.length ||
                  (showBccField && selectedForm && bccEmailList?.length)
                "
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 my-2"
              >
                <v-autocomplete
                  v-model="selectedBccEmails"
                  color="primary"
                  :items="bccEmailList"
                  label="Bcc"
                  multiple
                  variant="solo"
                  chips
                  closable-chips="true"
                  ><template #append-inner>
                    <span
                      v-if="!selectedCcEmails?.length > 0 && !showCcField"
                      @mousedown.stop.prevent="showHideCcField"
                      style="cursor: pointer"
                    >
                      Cc
                    </span>
                  </template></v-autocomplete
                >
              </v-col>
              <v-col
                v-if="selectedForm && emailsToSendList?.length"
                cols="12"
                sm="6"
                md="6"
                class="px-md-6 my-2"
              >
                <div class="text-subtitle-1 text-grey-darken-4 mt-n6">
                  <span class="v-label text-wrap"
                    >Do you want to include additional email addresses in
                    Cc?</span
                  >
                  <v-autocomplete
                    v-model="selectedAdditionalEmail"
                    color="primary"
                    :items="customEmailList"
                    item-value="Employee_Id"
                    item-title="Emp_Detail"
                    label="Additional email address"
                    multiple
                    variant="solo"
                    chips
                    closable-chips="true"
                  >
                  </v-autocomplete>
                </div>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <div class="d-flex mt-n6">
                  <p>
                    <span class="v-label"> Add additional external emails</span>
                  </p>
                </div>
                <v-text-field
                  ref="additionalExternalEmails"
                  v-model="input"
                  @update:modelValue="showAddIcon"
                  variant="solo"
                  :rules="[
                    input
                      ? validateWithRulesAndReturnMessages(
                          input,
                          'empEmail',
                          'Additional External Emails'
                        )
                      : true,
                  ]"
                  @keydown.enter.prevent="addChip"
                >
                  <template v-slot:default>
                    <v-icon v-if="showIcon" @click="addChip" size="x-small"
                      >fas fa-plus</v-icon
                    >
                    <v-chip
                      v-for="(chip, index) in additionalExternalEmails"
                      append-icon="fas fa-times-circle"
                      :key="index"
                      class="ma-1"
                      @click="removeChip(index)"
                      >{{ chip }}</v-chip
                    >
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" sm="6" md="6" class="px-md-6 my-2">
                <v-text-field
                  v-model="senderName"
                  counter="100"
                  variant="solo"
                  :rules="[
                    required('Sender Name', senderName),
                    validateWithRulesAndReturnMessages(
                      senderName,
                      'senderName',
                      'Sender Name'
                    ),
                  ]"
                  ><template v-slot:label
                    ><span>Sender Name</span>
                    <span class="ml-1" style="color: red">*</span></template
                  ></v-text-field
                >
              </v-col>
            </v-row>
            <div
              class="d-flex justify-space-between ml-4 mr-1"
              :style="
                selectedForm && subjectPlaceholderList?.length
                  ? 'margin-bottom: 0px'
                  : 'margin-bottom: 17px'
              "
            >
              <p class="text-subtitle-1 text-grey-darken-1 pl-6 mt-5">
                Subject
                <span style="color: red">*</span>
              </p>

              <v-col
                v-if="selectedForm && subjectPlaceholderList?.length"
                cols="3"
                class="px-md-5 pb-0 mb-0"
              >
                <CustomSelect
                  label="Placeholders"
                  v-model="dropdownValue"
                  :itemSelected="dropdownValue"
                  :items="subjectPlaceholderList"
                  item-value="PlaceHolder_Key"
                  item-title="PlaceHolder_Name"
                  :isAutoComplete="true"
                  @update:modelValue="insertValueInSubject"
                  variantType="underlined"
                  class="mr-4"
                  density="compact"
                ></CustomSelect>
              </v-col>
            </div>

            <v-row class="d-flex justify-end px-6">
              <v-col cols="12" sm="12" md="12" class="px-md-6 mt-n2 pt-0 mt-0">
                <v-text-field
                  v-model="subject"
                  :rules="[
                    required('Subject', subject),
                    validateWithRulesAndReturnMessages(
                      subject,
                      'subjectContent',
                      'Subject'
                    ),
                  ]"
                  :counter="300"
                  placeholder="Subject"
                  @input="isFormDirty = true"
                  @focus="trackCursorPosition"
                  @click="trackCursorPosition"
                  @keydown="trackCursorPosition"
                  ref="subject"
                  variant="solo"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row
              v-if="selectedForm && emailTemplatePlaceholderList?.length"
              class="d-flex justify-end px-6"
            >
              <v-col cols="3" class="px-md-6 pt-0 mb-n5">
                <CustomSelect
                  label="Placeholders"
                  v-model="selectedEmailTemplatePlaceholders"
                  :itemSelected="selectedEmailTemplatePlaceholders"
                  :items="emailTemplatePlaceholderList"
                  item-value="PlaceHolder_Key"
                  item-title="PlaceHolder_Name"
                  :isAutoComplete="true"
                  variantType="underlined"
                  @update:modelValue="insertDropdownValue"
                  density="compact"
                ></CustomSelect>
              </v-col>
            </v-row>
            <v-row class="px-6 pt-0 mt-0">
              <v-col cols="12" sm="12" md="12" class="px-md-6 pt-0 mt-0">
                <div style="padding-bottom: 60px">
                  <div ref="editor" class="quill-editor" id="formEditor"></div>
                  <div
                    v-if="editorErrorMessage"
                    class="text-caption ml-3"
                    style="color: #b00020"
                  >
                    {{ editorErrorMessage }}
                  </div>
                </div>
              </v-col>
            </v-row>
            <v-row class="px-6 pt-0 mb-6">
              <v-col cols="12" sm="12" md="12" class="px-md-6 pt-0 mt-0">
                <div class="d-flex justify-end mb-2">
                  <v-btn
                    prepend-icon="fas fa-plus"
                    variant="text"
                    color="primary"
                    @click="addDocument()"
                    >Add</v-btn
                  >
                </div>
                <v-form ref="documentForm">
                  <div v-for="(item, index) in documentList" :key="index">
                    <div class="d-flex align-center">
                      <v-file-input
                        :model-value="item"
                        prepend-icon
                        append-inner-icon="fas fa-paperclip"
                        variant="solo"
                        :hint="
                          !attachmentValidationMessage[index]
                            ? 'You can upload .pdf, .png, .docx, .doc upto 3mb'
                            : ''
                        "
                        :persistent-hint="!attachmentValidationMessage[index]"
                        accept=".pdf, .png, .doc, .docx,"
                        @update:model-value="onChangeFiles(index, $event)"
                        @click:clear="removeFiles(index)"
                      >
                        <template v-slot:label>
                          <span>Attach Files</span>
                        </template>
                      </v-file-input>
                      <v-icon
                        v-if="documentList.length > 1"
                        class="ml-2"
                        size="15"
                        @click="removeFiles(index)"
                      >
                        fas fa-trash
                      </v-icon>
                    </div>
                    <div
                      v-if="attachmentValidationMessage[index]"
                      class="text-caption ml-4 mt-n4"
                      style="color: #b00020"
                    >
                      {{ attachmentValidationMessage[index] }}
                    </div>
                  </div>
                </v-form>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <div v-if="windowWidth >= 770" class="card-actions-div">
          <v-card-actions class="d-flex align-end">
            <v-sheet class="align-center text-center" style="width: 100%">
              <v-row justify="center">
                <v-col cols="12" class="d-flex justify-end pr-6">
                  <v-btn
                    rounded="lg"
                    class="mr-6 primary"
                    @click="onCloseOverlay()"
                    variant="outlined"
                  >
                    Cancel
                  </v-btn>
                  <v-btn
                    rounded="lg"
                    class="mr-1 primary"
                    @click="submitEmailTemplateForm()"
                    variant="elevated"
                    :disabled="!isFormDirty"
                  >
                    Submit
                  </v-btn>
                </v-col>
              </v-row>
            </v-sheet>
          </v-card-actions>
        </div>
      </v-card>
      <AppLoading v-if="listLoading"></AppLoading>
    </template>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onClosePopup()"
  >
  </AppWarningModal>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
// import { defineAsyncComponent } from "vue";
import validationRules from "@/mixins/validationRules";
import { ADD_UPDATE_CUSTOM_EMAIL_TEMPLATE } from "@/graphql/settings/email-template/emailTemplateQueries.js";
import { RETRIEVE_ROLES_EMAIL_LIST } from "@/graphql/mpp/manPowerPlanningQueries";
import { GET_CLOUD_FRONT_URL } from "@/graphql/settings/email-template/emailTemplateQueries.js";
import moment from "moment";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
export default {
  name: "AddEditRecruitmentRequestForm",
  emits: ["on-close-add-form", "open-workflow-model", "refetch-list"],
  mixins: [validationRules],
  props: {
    selectedEmailTemplateData: {
      type: Object,
      default: () => {},
    },
    isEditForm: {
      type: Boolean,
      default: false,
    },
    isCloneForm: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      listLoading: false,
      overlay: true,
      formList: [],
      categoryList: [],
      categoryTypeList: [],
      selectedCategory: null,
      selectedCategoryType: null,
      selectedForm: null,
      templateName: null,
      openConfirmationPopup: false,
      isFormDirty: false,
      visibilityList: ["Everyone", "Only me"],
      selectedVisibility: null,
      dropdownValue: [],
      subjectPlaceholderList: [],
      subject: "",
      htmlContent: "",
      editorOptions: {
        theme: "snow",
      },
      dropdownLoading: false,
      templateId: 0,
      cursorPosition: 0,
      emailsToSendList: [],
      selectedEmailsToSend: [],
      bccEmailList: [],
      selectedBccEmails: [],
      ccEmailList: [],
      selectedCcEmails: [],
      showCcField: false,
      showBccField: false,
      selectedAdditionalEmail: [],
      emailTemplatePlaceholderList: [],
      selectedEmailTemplatePlaceholders: [],
      editorErrorMessage: "",
      hasContent: false,
      contentLength: 0,
      customEmailList: [],
      documentList: [null],
      apiCount: 0,
      isdefaultTemplate: "No",
      attachmentValidationMessage: [],
      senderName: null,
      input: "",
      additionalExternalEmails: [],
      showIcon: false,
      downloadLink: "",
      imageUploadErrorMessage:
        "Images cannot be pasted directly into the text editor. Please use the image upload option available in the toolbar.",
      imageBlockingObserver: null,
      isToolbarImageUpload: false,
    };
  },
  components: {
    CustomSelect,
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    isInputValid() {
      const rules = [
        this.validateWithRulesAndReturnMessages(
          this.input,
          "empEmail",
          "Additional External Emails"
        ),
      ];
      return rules[0] === true ? true : false;
    },
    multipleAccessRights() {
      return this.$store.getters.formIdsBasedAccessRights;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    currentTimeStamp() {
      return moment().unix();
    },
  },
  mounted() {
    this.preFillEmailTemplateForm();
  },
  beforeUnmount() {
    // Clean up the mutation observer
    if (this.imageBlockingObserver) {
      this.imageBlockingObserver.disconnect();
      this.imageBlockingObserver = null;
    }
  },
  methods: {
    onCloseOverlay() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else {
        this.onClosePopup();
        this.isFormDirty = false;
      }
    },
    onClosePopup() {
      this.openConfirmationPopup = false;
      this.documentList = [null];
      this.$emit("on-close-add-form");
      this.isFormDirty = false;
    },
    onChange() {
      this.isFormDirty = true;
    },
    async submitEmailTemplateForm() {
      if (
        this.selectedBccEmails.length === 0 &&
        this.selectedCcEmails.length === 0 &&
        this.selectedEmailsToSend.length === 0 &&
        this.selectedAdditionalEmail.length === 0
      ) {
        let snackbarData = {
          isOpen: true,
          message: "No recipients selected. Add an email address to proceed.",
          type: "warning",
        };
        this.showAlert(snackbarData);
        return;
      }
      const { valid } = await this.$refs.refEmailTemplateForm.validate();
      this.hasContent = !!this.quill.getText().trim();
      let overSize = this.attachmentValidationMessage.some(
        (item) => typeof item === "string" && item.trim() !== ""
      );
      if (valid && !overSize && this.hasContent) {
        if (this.contentLength <= 1) {
          this.editorErrorMessage = "Email template content is required";
        } else {
          this.hasContent = false;
          this.uploadDocuments();
        }
      } else {
        if (this.contentLength <= 1) {
          this.editorErrorMessage = "Email template content is required";
        }
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    showImagePasteWarning() {
      // Reusable method to show image paste warning
      let snackbarData = {
        isOpen: true,
        message: this.imageUploadErrorMessage,
        type: "warning",
      };
      this.showAlert(snackbarData);
    },
    preventImagePaste(event) {
      // Reusable method to prevent image paste and show warning
      event.preventDefault();
      event.stopPropagation();
      this.showImagePasteWarning();
      return false;
    },
    isImageType(type) {
      // Helper method to check if a type is an image
      return type.indexOf("image") !== -1;
    },
    showAddIcon() {
      this.showIcon = !!this.input.trim();
    },
    addChip() {
      if (this.isInputValid && this.input.trim()) {
        this.additionalExternalEmails.push(this.input.trim());
        this.input = "";
        this.showIcon = false;
      }
    },
    removeChip(index) {
      this.additionalExternalEmails.splice(index, 1);
    },
    async getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      vm.listLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;

      await vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: 310,
            key: [
              "custom_template_category",
              "custom_template_category_type",
              "custom_template_forms",
              "email_template_placeholders",
              "email_notification_setting",
            ],
            conditionDetails: [
              {
                key: "custom_template_category",
                condition: "Form_Id",
                value: vm.selectedForm ? [vm.selectedForm.toString()] : [],
              },
              {
                key: "custom_template_category_type",
                condition: "Category_Id",
                value: vm.selectedCategory
                  ? [vm.selectedCategory.toString()]
                  : [],
              },
              {
                key: "email_template_placeholders",
                condition: "Form_Id",
                value: vm.selectedForm ? [vm.selectedForm.toString()] : [],
              },
            ],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            const dataArrays = this.extractArraysByTableKey(tempData);
            let formData = dataArrays?.custom_template_forms;
            let senderName = dataArrays?.email_notification_setting;
            let placeholders = dataArrays?.email_template_placeholders;
            vm.categoryList = dataArrays?.custom_template_category;
            vm.categoryTypeList = dataArrays?.custom_template_category_type;
            if (vm.categoryTypeList?.length === 0) {
              vm.selectedCategoryType = null;
            }
            vm.emailTemplatePlaceholderList = placeholders;
            vm.subjectPlaceholderList = placeholders;
            vm.senderName = vm.selectedEmailTemplateData?.Sender_Name || null;
            let senderNameArray = senderName;
            let emailSenderName = senderNameArray?.length
              ? senderNameArray[0].Sender_Name
              : "HRAPP Notfication";
            vm.senderName = vm.senderName || emailSenderName;
            const tempFormIds = formData.map((item) => item.Form_Id);
            vm.formList = vm.multipleAccessRights(tempFormIds) || [];
            if (vm.selectedForm) {
              const form = formData.find(
                (item) => item.Form_Id === vm.selectedForm
              );

              let emailReceivers = form.Email_Receiver
                ? JSON.parse(form.Email_Receiver)
                : [];
              vm.emailsToSendList = emailReceivers;
              vm.bccEmailList = emailReceivers;
              vm.ccEmailList = emailReceivers;
            }
            vm.formList = vm.multipleAccessRights(tempFormIds) || [];
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.listLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.listLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "dropdown details",
        isListError: false,
      });
    },
    removeFiles(index) {
      this.attachmentValidationMessage[index] = "";
      this.documentList.splice(index, 1);
    },
    preFillEmailTemplateForm() {
      this.selectedVisibility = this.visibilityList[0];
      if (this.isEditForm || this.isCloneForm) {
        this.selectedForm = this.selectedEmailTemplateData.Form_Id;
        this.selectedVisibility = this.selectedEmailTemplateData.Visibility;
        this.templateName = this.selectedEmailTemplateData.Template_Name;
        this.selectedCategory = this.selectedEmailTemplateData.Category_Id;
        this.subject = this.selectedEmailTemplateData?.Subject_Content
          ? this.selectedEmailTemplateData?.Subject_Content
          : "";
        this.selectedAdditionalEmail = this.selectedEmailTemplateData
          ?.Additional_Emails
          ? JSON.parse(this.selectedEmailTemplateData.Additional_Emails).map(
              (item) => item.id
            )
          : [];
        this.selectedEmailsToSend = this.selectedEmailTemplateData?.To_Emails
          ? JSON.parse(this.selectedEmailTemplateData.To_Emails)
          : [];
        this.selectedCcEmails = this.selectedEmailTemplateData?.CC_Emails
          ? JSON.parse(this.selectedEmailTemplateData.CC_Emails)
          : [];
        this.selectedBccEmails = this.selectedEmailTemplateData?.Bcc_Emails
          ? JSON.parse(this.selectedEmailTemplateData.Bcc_Emails)
          : [];
        this.selectedCategoryType =
          this.selectedEmailTemplateData.Category_Type_Id;
        this.templateId = this.isEditForm
          ? this.selectedEmailTemplateData.Template_Id
          : 0;
        this.isdefaultTemplate =
          this.selectedEmailTemplateData.Default_Template;
        let mailAttachments = JSON.parse(
          this.selectedEmailTemplateData?.AttachmentsFileNames
        );
        this.additionalExternalEmails = this.selectedEmailTemplateData
          .External_Emails?.length
          ? JSON.parse(this.selectedEmailTemplateData.External_Emails)
          : [];
        this.documentList =
          mailAttachments && mailAttachments.length > 0
            ? mailAttachments?.map((file) => ({
                name: file?.split("?")[3],
                formatName: file,
              })) || [null]
            : [null];
      }
      this.retrieveRolesEmailList();
      this.getDropdownDetails();
      setTimeout(() => {
        this.initQuillEditor();
      }, 0);
    },
    insertDropdownValue() {
      let placeholder = this.selectedEmailTemplatePlaceholders;
      // Ensure placeholder is valid and not empty
      if (!placeholder || placeholder.trim() === "") return;
      placeholder = "{{" + placeholder + "}}";
      const editor = this.quill; // Access the Quill instance
      if (!this.quill.hasFocus()) {
        this.quill.focus();
      }
      if (placeholder) {
        const range = editor.getSelection(); // Get the current cursor position

        if (range) {
          editor.insertText(range.index, placeholder); // Insert placeholder at cursor
          editor.setSelection(range.index + placeholder.length); // Move cursor to after the inserted text
          this.editorErrorMessage = "";
          this.selectedEmailTemplatePlaceholders = [];
        } else {
          this.editorErrorMessage = "Please place the cursor in the editor.";
        }
      }
    },
    updateDropDown() {
      this.selectedCategory = null;
      this.selectedCategoryType = null;
      this.selectedEmailsToSend = [];
      this.selectedBccEmails = [];
      this.selectedCcEmails = [];
      this.getDropdownDetails();
    },
    async uploadDocuments() {
      try {
        this.documentList = this.documentList.filter((item) => item);
        if (this.documentList.length > 0) {
          for (let document of this.documentList) {
            if (document?.size) {
              await this.uploadFileContents(
                document,
                "Email Template Document Upload"
              );
            }
          }
        }
        this.addUpdateEmailTemplate();
      } catch (error) {
        this.listLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "document",
          isListError: false,
        });
      }
    },
    addUpdateEmailTemplate() {
      let vm = this;
      const formattedContent = vm.getQuillContentWithInlineStyles();
      vm.listLoading = true;
      let documentNameList = vm.documentList
        .filter((document) => document)
        .map((document) => {
          return document.formatName;
        });
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_CUSTOM_EMAIL_TEMPLATE,
          variables: {
            templateId: vm.templateId,
            templateName: vm.cleanString(vm.templateName),
            templateContent: formattedContent,
            templateFields: [],
            categoryId: vm.selectedCategory,
            categoryTypeId: vm.selectedCategoryType,
            formId: vm.selectedForm,
            visibility: vm.selectedVisibility,
            toEmails: vm.selectedEmailsToSend,
            ccEmails: vm.selectedCcEmails,
            bccEmails: vm.selectedBccEmails,
            additionalEmails: vm.selectedAdditionalEmail,
            subjectContent: vm.subject,
            defaultTemplate: vm.isdefaultTemplate,
            attachedFiles: documentNameList,
            senderName: vm.cleanString(vm.senderName),
            externalEmails: vm.additionalExternalEmails,
          },
          client: "apolloClientJ",
        })
        .then((res) => {
          if (res && res.data && res.data.addUpdateCustomEmailTemplate) {
            const { errorCode } = res.data.addUpdateCustomEmailTemplate;
            if (!errorCode) {
              let snackbarData = {
                isOpen: true,
                message: `Email template ${
                  vm.isEditForm ? "updated" : "added"
                } successfully`,
                type: "success",
              };
              vm.showAlert(snackbarData);
              vm.listLoading = false;
              vm.$emit("refetch-list");
            } else {
              vm.handleUpdateError();
            }
          } else {
            vm.handleUpdateError();
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },
    handleUpdateError(err = "") {
      this.listLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.isEditForm ? "updating" : "adding",
        form: "email template",
        isListError: false,
      });
    },
    showHideCcField() {
      this.showCcField = !this.showCcField;
    },
    showHideBCcField() {
      this.showBccField = !this.showBccField;
    },
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editor, {
        theme: "snow",
        modules: {
          toolbar: {
            container: [
              [{ header: [1, 2, false] }],
              ["bold", "italic", "underline"], // Text styles
              [{ list: "ordered" }, { list: "bullet" }], // Lists
              [{ table: "better-table" }], // Table button
              ["link", "image"], // Attach link and upload image
              [{ align: [] }], // Alignment
            ],
            handlers: {
              image: () => {
                const input = document.createElement("input");
                input.setAttribute("type", "file");
                input.setAttribute("accept", "image/*");
                input.click();

                input.onchange = async () => {
                  const file = input.files[0];
                  if (file) {
                    const formData = new FormData();
                    formData.append("image", file);

                    try {
                      let fileImage;
                      for (let value of formData.values()) {
                        value.formatName =
                          moment().unix() + "?emailTemplate?1?" + value.name;
                        fileImage = value;
                      }
                      await this.uploadFileContents(
                        fileImage,
                        "Email Template Image Upload",
                        "tableImage"
                      );
                    } catch (error) {
                      let snackbarData = {
                        isOpen: true,
                        message: "Error while uploading the image.",
                        type: "warning",
                      };
                      this.showAlert(snackbarData);
                    }
                  }
                };
              },
              link: this.linkHandler,
              table: function () {
                this.quill.getModule("better-table").insertTable(3, 3);
              },
            },
          },
          table: false,
          "better-table": {
            operationMenu: {
              color: {
                colors: ["green", "red", "yellow", "blue", "white"],
                text: "Background Colors:",
              },
            },
          },
        },
      });
      if (this.isEditForm || this.isCloneForm) {
        this.quill.root.innerHTML =
          this.selectedEmailTemplateData?.Template_Content;
      }
      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.isFormDirty = true;
        this.hasContent = !!this.quill.getText().trim();
        this.contentLength = this.quill.getLength();
        if (!this.hasContent || this.contentLength <= 1) {
          this.editorErrorMessage = "Email template content is required";
        } else {
          this.editorErrorMessage = "";
        }
      });

      // Listen for paste events to detect and prevent image pasting
      this.quill.root.addEventListener("paste", (event) => {
        const clipboardData = event.clipboardData;

        if (clipboardData) {
          // Check for image data in clipboard items
          const items = clipboardData.items;
          if (items) {
            for (let i = 0; i < items.length; i++) {
              const item = items[i];
              if (this.isImageType(item.type)) {
                return this.preventImagePaste(event);
              }
            }
          }

          // Check for files that might be images
          const files = clipboardData.files;
          if (files && files.length > 0) {
            for (let i = 0; i < files.length; i++) {
              const file = files[i];
              if (this.isImageType(file.type)) {
                return this.preventImagePaste(event);
              }
            }
          }
        }
      });

      // Add mutation observer to catch any images that might slip through
      this.setupImageBlockingObserver();
    },
    setupImageBlockingObserver() {
      // Create a mutation observer to watch for image insertions
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === "childList") {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Check if the added node is an image
                if (node.tagName === "IMG") {
                  // Only block if it's not a toolbar upload
                  if (!this.isToolbarImageUpload) {
                    // Remove the image and show warning
                    node.remove();
                    this.showImagePasteWarning();
                  }
                }
                // Check if the added node contains images
                const images = node.querySelectorAll("img");
                if (images.length > 0 && !this.isToolbarImageUpload) {
                  images.forEach((img) => {
                    img.remove();
                  });
                  this.showImagePasteWarning();
                }
              }
            });
          }
        });
      });

      // Start observing the Quill editor
      if (this.quill && this.quill.root) {
        observer.observe(this.quill.root, {
          childList: true,
          subtree: true,
        });

        // Store observer reference for cleanup
        this.imageBlockingObserver = observer;
      }
    },
    trackCursorPosition() {
      const input = this.$refs.subject.$el.querySelector("input");
      this.cursorPosition = input.selectionStart || 0; // Capture cursor position
    },
    insertValueInSubject() {
      let subDropdownValue = this.dropdownValue;
      // Ensure dropdownValue is valid and not empty
      if (!subDropdownValue || subDropdownValue.trim() === "") return;
      let value = "{{" + this.dropdownValue + "}}";
      const input = this.$refs.subject.$el.querySelector("input");
      const textBefore = this.subject.slice(0, this.cursorPosition);
      const textAfter = this.subject.slice(this.cursorPosition);
      this.subject = `${textBefore}${value}${textAfter}`; // Insert at cursor
      this.showDropdown = false; // Hide dropdown after selection
      this.$nextTick(() => {
        input.focus(); // Refocus the text field
        input.setSelectionRange(
          this.cursorPosition + value.length,
          this.cursorPosition + value.length
        ); // Move cursor after inserted text
        this.dropdownValue = [];
      });
    },
    retrieveRolesEmailList() {
      this.isLoading = true;
      this.$apollo
        .query({
          query: RETRIEVE_ROLES_EMAIL_LIST,
          client: "apolloClientAG",
          variables: {
            roleIds: [],
            formId: 310,
          },
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.isLoading = false;
          if (res && res.data && res.data.retrieveEmployeeRoleEmail) {
            const tempEmailList = res.data.retrieveEmployeeRoleEmail;
            this.showCustomEmail = true;
            if (
              tempEmailList.nonRoleEmailList &&
              tempEmailList.nonRoleEmailList.length
            ) {
              this.customEmailList = tempEmailList.nonRoleEmailList.map(
                (item) => {
                  return {
                    ...item,
                    Emp_Detail: `${item?.Employee_Name} ${
                      item?.User_Defined_EmpId
                        ? " - " + item.User_Defined_EmpId
                        : ""
                    }`,
                  };
                }
              );
            }
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.customEmailList = [];
          this.handleRetrieveError(err);
        });
    },
    handleRetrieveError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "additional email list",
        isListError: false,
      });
    },
    async uploadFileContents(fileContent, folderName, source = "") {
      let vm = this;
      vm.isLoading = true;
      let fileUploadUrl =
        this.domainName + "/" + this.orgCode + "/" + folderName + "/";
      vm.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl + fileContent.formatName,
          action: "upload",
          type: "documents",
          fileContent: fileContent,
        })
        .then(async () => {
          this.apiCount += 1;
          if (this.apiCount === this.documentList.length) {
            this.isLoading = false;
            this.apiCount = 0;
          }
          if (source?.toLowerCase() === "tableimage") {
            await this.retrieveFileContents(fileContent.formatName);
            const range = this.quill.getSelection();

            // Set flag before inserting image to allow it through
            this.isToolbarImageUpload = true;

            this.quill.insertEmbed(range.index, "image", this.downloadLink);

            // Reset flag after a short delay
            setTimeout(() => {
              this.isToolbarImageUpload = false;
            }, 500);
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          throw err;
        });
    },
    async addDocument() {
      const { valid } = await this.$refs.documentForm.validate();
      if (valid) {
        this.documentList.push(null);
      }
    },
    onChangeFiles(index, val) {
      this.documentList[index] = val;
      if (val) {
        this.isFormDirty = true;
        this.documentList[index].formatName =
          this.currentTimeStamp + "?emailTemplate?1?" + val.name;
        if (this.documentList?.length) {
          const fileSize = this.documentList[index]?.size;
          if (fileSize > 3000000) {
            this.attachmentValidationMessage[index] =
              "The file size should be less than 3 MB.";
          } else {
            const allowedExtensions = ["pdf", "png", "doc", "docx"];
            const fileExtension = this.documentList[index].name
              .split(".")
              .pop()
              .toLowerCase();

            if (!allowedExtensions.includes(fileExtension)) {
              this.attachmentValidationMessage[index] =
                "Invalid file type. Only pdf, png, docx, and doc are allowed.";
            } else {
              this.attachmentValidationMessage[index] = "";
            }
          }
        }
      }
    },
    cleanString(inputString) {
      return inputString?.trim().replace(/\s+/g, " ");
    },
    updateCategoryTypeDropDown() {
      this.selectedCategoryType = null;
      this.getDropdownDetails();
    },
    extractArraysByTableKey(data) {
      return data.reduce((result, item) => {
        if (item.data && item.data.length > 0) {
          result[item.tableKey] = item.data;
        }
        return result;
      }, {});
    },
    async retrieveFileContents(fileName) {
      let vm = this;
      vm.listLoading = true;
      await vm.$apollo
        .query({
          query: GET_CLOUD_FRONT_URL,
          variables: {
            fileName: fileName,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then(async (response) => {
          if (response?.data?.getCloudFrontUrl?.url) {
            vm.downloadLink = response.data.getCloudFrontUrl.url;
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.listLoading = false;
          this.handleRetrieveFileContentsError(err);
        });
    },
    handleRetrieveFileContentsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "image url",
        isListError: false,
      });
    },
    getQuillContentWithInlineStyles() {
      let quillContent = this.quill.root.innerHTML;

      // Ensuring all <td> and <th> elements have inline styles for borders
      quillContent = quillContent.replace(
        /<td(.*?)>/g,
        '<td$1 style="border: 1px solid black;">'
      );
      quillContent = quillContent.replace(
        /<th(.*?)>/g,
        '<th$1 style="border: 1px solid black;">'
      );
      quillContent = quillContent.replace(
        /<table(.*?)>/g,
        '<table$1 style="border: 1px solid black;">'
      );

      return quillContent;
    },
  },
};
</script>
<style scoped>
.overlay-card {
  overflow-y: auto;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.quill-editor {
  height: 200px;
}
:deep(.ql-snow .ql-tooltip) {
  left: 0px !important;
}
</style>
