<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
      <template v-if="!isErrorInList" #topBarContent>
        <v-row
          v-if="backUpEmailTemplateData && backUpEmailTemplateData.length > 0"
          justify="center"
        >
          <v-col cols="12" md="9" class="d-flex justify-end">
            <!-- this the component which has search component and based on prop isFilter filter component is also rendered -->
            <EmployeeDefaultFilterMenu class="justify-end" :isFilter="false" />
            <FormFilter
              ref="formFilterRef"
              :items="filteredList"
              @reset-filter="resetFilter()"
              @apply-filter="applyFilter($event)"
            ></FormFilter>
          </v-col>
        </v-row>
      </template>
    </AppTopBarTab>
  </div>
  <v-container fluid class="job-post-container">
    <v-window v-model="currentTabItem" v-if="formAccess">
      <v-window-item :value="currentTabItem">
        <div>
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <div v-else-if="!isErrorInList">
            <div
              v-if="backUpEmailTemplateData.length > 0"
              class="d-flex flex-wrap align-center my-3"
              :class="isMobileView ? 'flex-column' : ''"
              style="justify-content: space-between"
            >
              <div
                class="d-flex align-center"
                :class="isMobileView ? 'justify-center' : ''"
              >
                <CustomSelect
                  v-model="selectedForm"
                  :items="formList"
                  label="Forms"
                  :is-loading="dropdownLoading"
                  :isAutoComplete="true"
                  clearable
                  item-title="customFormName"
                  item-value="formId"
                  min-width="200px"
                  density="comfortable"
                  :itemSelected="selectedForm"
                  @selected-item="selectedForm = $event"
                  @update:modelValue="filteredItemList()"
                />
              </div>
              <div
                v-if="formAccess && formAccess.add"
                class="d-flex align-center"
                :class="isMobileView ? 'justify-center' : 'justify-end'"
              >
                <v-btn
                  @click="addButtonClicked()"
                  class="primary"
                  variant="elevated"
                  :size="isMobileView ? 'small' : 'default'"
                  v-bind="!isRecruiter ? props : {}"
                >
                  <v-icon size="15" class="pr-1 primary">fas fa-plus</v-icon>
                  <span class="primary">Add</span></v-btn
                >
                <v-btn
                  color="transparent"
                  variant="flat"
                  rounded="lg"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchEmailTemplateList()"
                >
                  <v-icon class="pr-1 grey">fas fa-redo-alt</v-icon>
                </v-btn>
                <v-menu class="mb-1 mt-1" transition="scale-transition">
                  <template v-slot:activator="{ props }">
                    <v-btn variant="plain" v-bind="props">
                      <v-icon>fas fa-ellipsis-v</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="action in moreActions"
                      :key="action.key"
                      @click="onMoreAction(action.key)"
                    >
                      <v-hover>
                        <template v-slot:default="{ isHovering, props }">
                          <v-list-item-title
                            v-bind="props"
                            class="pa-3"
                            :class="{
                              hover: isHovering,
                            }"
                            ><v-icon size="15" class="pr-2">{{
                              action.icon
                            }}</v-icon
                            >{{ action.key }}</v-list-item-title
                          >
                        </template>
                      </v-hover>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </div>
            <v-data-table
              v-if="emailTemplateData.length > 0"
              :headers="headers"
              :items="emailTemplateData"
              :items-per-page="50"
              :items-per-page-options="[
                { value: 50, title: '50' },
                { value: 100, title: '100' },
                { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
              ]"
              fixed-header
              :height="
                $store.getters.getTableHeightBasedOnScreenSize(
                  290,
                  emailTemplateData
                )
              "
              :sort-by="[{ key: 'Posting_Date', order: 'desc' }]"
              class="elevation-1"
              style="box-shadow: none !important"
            >
              <template v-slot:item="{ item, index }">
                <tr
                  style="z-index: 200; cursor: pointer"
                  class="data-table-tr bg-white cursor-pointer"
                  @click="viewEmailTemplateDetails(item, index)"
                  :class="[
                    isMobileView
                      ? ' v-data-table__mobile-table-row ma-0 mt-2'
                      : '',
                  ]"
                >
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-small'
                    "
                    :style="isMobileView ? '' : 'max-width: 100px'"
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Template Name
                    </div>
                    <v-tooltip :text="item.Template_Name" location="top">
                      <template v-slot:activator="{ props }">
                        <section :class="isMobileView ? '' : 'text-truncate'">
                          <span
                            class="text-primary text-body-2 font-weight-regular"
                            v-bind="props"
                          >
                            {{ checkNullValue(item.Template_Name) }}
                          </span>
                        </section>
                      </template>
                    </v-tooltip>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Form Name
                    </div>
                    <section>
                      <span class="text-body-2 font-weight-regular">
                        {{ checkNullValue(item.Form_Name) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-small'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Category
                    </div>
                    <section>
                      <span
                        class="text-primary text-body-2 font-weight-regular"
                      >
                        {{ checkNullValue(item.Category_Name) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5 font-weight-small'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Category Type
                    </div>
                    <section>
                      <span
                        class="text-primary text-body-2 font-weight-regular"
                      >
                        {{ checkNullValue(item.Category_Type_Name) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                    :style="isMobileView ? '' : 'max-width: 100px'"
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Created By
                    </div>
                    <v-tooltip :text="item.Added_By_Name" location="top">
                      <template v-slot:activator="{ props }">
                        <section :class="isMobileView ? '' : 'text-truncate'">
                          <span
                            class="text-body-2 font-weight-regular"
                            v-bind="props"
                          >
                            {{ checkNullValue(item.Added_By_Name) }}
                          </span>
                        </section>
                      </template>
                    </v-tooltip>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2'
                    "
                    @click.stop=""
                  >
                    <div
                      v-if="isMobileView"
                      :class="
                        isMobileView
                          ? ' font-weight-bold d-flex align-center'
                          : ' font-weight-bold mt-2 d-flex align-center'
                      "
                    >
                      Actions
                    </div>
                    <section>
                      <div class="d-flex justify-start">
                        <ActionMenu
                          v-if="getActions(item)"
                          @selected-action="onActions($event, item)"
                          :actions="getActions(item)"
                          :access-rights="checkAccess()"
                          :isPresentTooltip="true"
                          iconColor="grey"
                        ></ActionMenu>
                        <section class="text-body-2 font-weight-medium" v-else>
                          -
                        </section>
                      </div>
                    </section>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </div>
          <div v-if="!listLoading && backUpEmailTemplateData.length == 0">
            <AppFetchErrorScreen key="no-results-screen">
              <template #contentSlot>
                <div v-if="!isSmallTable" style="max-width: 80%">
                  <v-row
                    v-if="!isLoading"
                    style="background: white"
                    class="rounded-lg pa-5 mb-4"
                  >
                    <v-col cols="12">
                      <NotesCard
                        notes="Welcome to the Email Template page. Here, you can create, customize, and organize email templates tailored for various scenarios."
                        backgroundColor="transparent"
                        class="mb-2"
                      ></NotesCard>
                      <NotesCard
                        notes="Design personalized email templates with dynamic fields that adapt to your needs."
                        backgroundColor="transparent"
                        class="mb-4"
                      ></NotesCard>
                      <NotesCard
                        notes="Use placeholders to automate content customization."
                        backgroundColor="transparent"
                        class="mb-4"
                      ></NotesCard>
                      <NotesCard
                        notes="Effortlessly save time and maintain consistent communication by leveraging reusable email templates."
                        backgroundColor="transparent"
                        class="mb-4"
                      ></NotesCard>
                    </v-col>

                    <v-col
                      cols="12"
                      class="d-flex align-center justify-center mb-4"
                    >
                      <div v-if="formAccess && formAccess.add">
                        <v-btn
                          @click="addButtonClicked()"
                          class="px-6 seconary mr-2"
                          v-bind="!isRecruiter ? props : {}"
                          variant="elevated"
                        >
                          <v-icon size="15" class="pr-1">fas fa-plus</v-icon
                          ><span class="primary">Add Email Template</span>
                        </v-btn>
                      </div>
                      <v-btn
                        color="transparent"
                        variant="flat"
                        rounded="lg"
                        :size="isMobileView ? 'small' : 'default'"
                        @click="refetchEmailTemplateList()"
                      >
                        <v-icon class="pr-1" color="grey"
                          >fas fa-redo-alt</v-icon
                        >
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </template>
            </AppFetchErrorScreen>
          </div>
          <div
            v-if="
              emailTemplateData?.length == 0 &&
              !listLoading &&
              emptyFilterScreen
            "
          >
            <AppFetchErrorScreen
              image-name="common/no-records"
              main-title="There are no email templates for the selected filters/searches."
            >
              <template #contentSlot>
                <div class="d-flex mb-2 flex-wrap justify-center">
                  <v-btn
                    color="primary"
                    variant="elevated"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click.stop="resetFilter()"
                  >
                    Reset Filter/Search
                  </v-btn>
                </div>
              </template>
            </AppFetchErrorScreen>
          </div>
          <div v-else-if="isErrorInList && backUpEmailTemplateData.length != 0">
            <AppFetchErrorScreen
              :content="errorContent"
              icon-name="fas fa-redo-alt"
              :button-text="showRetryBtn ? 'Retry' : ''"
              @button-click="refetchEmailTemplateList()"
            >
            </AppFetchErrorScreen>
          </div>
        </div>
      </v-window-item>
    </v-window>
    <AppAccessDenied v-else></AppAccessDenied>
  </v-container>
  <AppLoading v-if="emailTemplateDetailsLoading"></AppLoading>
  <EmailTemplateForm
    v-if="openEmailTemplateForm"
    :selectedEmailTemplateData="selectedEmailTemplateDetails"
    :isEditForm="isEditForm"
    :isCloneForm="isCloneForm"
    @on-close-add-form="closeEmailTemplateForm()"
    @open-workflow-model="openWorkflowConfirm($event)"
    @refetch-list="refetchEmailList($event)"
  />
  <EmailTemplateView
    :selectedEmailTemplateData="selectedEmailTemplateDetails"
    :enable-view="showViewForm"
    @close-view-details="closeView()"
    @edit-email-template="editEmailTemplate()"
  />
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          class="mt-n5 primary"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    :confirmation-heading="warningText"
    :icon-name="warningIconClass"
    @close-warning-modal="onCloseWarningModal()"
    @accept-modal="deleteThisEmailTemplate(deleteItem)"
  >
    <template>
      <v-form>
        <v-textarea
          variant="outlined"
          auto-grow
          rows="1"
          class="mt-4"
          :style="isMobileView ? '' : 'min-width: 300px'"
        ></v-textarea>
      </v-form>
    </template>
  </AppWarningModal>
</template>
<script>
import validationRules from "../../../../mixins/validationRules";
import moment from "moment";
import { defineAsyncComponent } from "vue";
import FileExportMixin from "@/mixins/FileExportMixin";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const EmailTemplateForm = defineAsyncComponent(() =>
  import("./AddEmailTemplateForm.vue")
);
const FormFilter = defineAsyncComponent(() => import("./FormFilter.vue"));
import { LIST_CUSTOM_EMAIL_TEMPLATES } from "@/graphql/settings/email-template/emailTemplateQueries.js";

import { checkNullValue } from "@/helper";
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
import { convertUTCToLocal } from "@/helper.js";
const EmailTemplateView = defineAsyncComponent(() =>
  import("./EmailTemplateView.vue")
);
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
export default {
  name: "EmailTemplate",
  components: {
    EmailTemplateForm,
    EmployeeDefaultFilterMenu,
    EmailTemplateView,
    FormFilter,
    NotesCard,
    ActionMenu,
    CustomSelect,
  },
  mixins: [validationRules, FileExportMixin],
  data() {
    return {
      currentTabItem: "",
      selectedWorkflow: null,
      dropdownWorkflow: [],
      headers: [
        {
          title: "Template Name",
          align: "start",
          key: "Template_Name",
        },
        { title: "Form Name", key: "Form_Name" },
        {
          title: "Category",
          key: "Category_Name",
        },
        {
          title: "Category Type",
          key: "Category_Type_Name",
        },
        { title: "Created By", key: "Added_By_Name" },
        { title: "Actions", key: "actions", sortable: false },
      ],
      emailTemplateData: [],
      listLoading: true,
      selectedData: [],
      backUpEmailTemplateData: [],
      showViewForm: false,
      validationMessages: [],
      showValidationAlert: false,
      emailTemplateDetailsLoading: false,
      emptyFilterScreen: false,
      errorContent: "",
      showRetryBtn: true,
      isErrorInList: false,
      backupFilterData: [],
      openMoreMenu: false,
      havingAccess: {},
      openEmailTemplateForm: false,
      selectedEmailTemplateDetails: {},
      isEditForm: false,
      isCloneForm: false,
      openWarningModal: false,
      warningText: "Are you sure you want to delete this email template?",
      formList: [],
      selectedForm: null,
      dropdownLoading: false,
      filteredList: [],
      senderNameArray: [],
    };
  },
  computed: {
    currentDate() {
      return moment().format("YYYY-MM-DD");
    },
    moreActions() {
      let actions = [
        {
          key: "Export",
          icon: "fas fa-file-export",
        },
      ];
      return actions;
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    formatDate() {
      return (date) => {
        if (date && date != "0000-00-00") {
          let formattedDate = this.convertUTCToLocal(date);
          return formattedDate.split(" ")[0];
        } else return "-";
      };
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    //the value searched in searchbox will be stored in vuex store
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("310");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    isRecruiter() {
      return this.$store.state.isRecruiter;
    },
    landedFormName() {
      let customFieldForm = this.accessRights("310");
      if (
        customFieldForm?.customFormName &&
        customFieldForm.customFormName !== ""
      ) {
        return customFieldForm.customFormName;
      } else return "Email Templates";
    },
    settingsGeneralFormAccess() {
      return this.$store.getters.settingsGeneralFormAccess;
    },
    mainTabList() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.settingsGeneralFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    multipleAccessRights() {
      return this.$store.getters.formIdsBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  watch: {
    searchValue(val) {
      this.onApplySearch(val);
    },
    selectedForm(val) {
      if (!val) {
        this.refetchEmailTemplateList();
      }
    },
  },
  mounted() {
    this.currentTabItem =
      "tab-" + this.mainTabList.indexOf(this.landedFormName);
    this.fetchEmailTemplates();
    this.orgCode = this.$store.getters.orgCode;
    this.getDropdownDetails();
  },
  methods: {
    convertUTCToLocal,
    checkNullValue,
    checkAccess() {
      this.havingAccess["clone"] =
        this.formAccess && this.formAccess.add ? 1 : 0;
      this.havingAccess["update"] =
        this.formAccess && this.formAccess.update ? 1 : 0;
      this.havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? 1 : 0;
      return this.havingAccess;
    },
    getActions() {
      let list = ["Clone", "Edit"];
      return list;
    },
    addButtonClicked() {
      this.isEditForm = false;
      this.isCloneForm = false;
      this.openEmailTemplateForm = true;
      this.selectedEmailTemplateDetails = {};
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.settingsGeneralFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/general/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/general/" + clickedForm.url;
        }
      }
    },
    onMoreAction(actionType) {
      if (actionType === "Export") {
        this.exportReportFile();
      }
      this.openMoreMenu = false;
    },
    exportReportFile() {
      const convertToScript = (html) => {
        const tmp = document.createElement("div");
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || "";
      };
      let exportData = JSON.parse(JSON.stringify(this.emailTemplateData));
      exportData = exportData.map((el) => ({
        ...el,
        Posting_Date: el.Posting_Date ? this.formatDate(el.Posting_Date) : "",
        Closing_Date: el.Closing_Date ? this.formatDate(el.Closing_Date) : "",
        Job_Description: el.Job_Description
          ? convertToScript(el.Job_Description)
          : "",
      }));

      let exportOptions = {
        fileExportData: exportData,
        fileName: "Email Templates",
        sheetName: "Email Templates",
        header: [
          { key: "Template_Name", header: "Template Name" },
          { key: "Form_Name", header: "Form Name" },
          { key: "Category_Name", header: "Category" },
          { key: "Category_Type_Name", header: "Category Type" },
          { key: "Added_On", header: "Added On" },
          { key: "Added_By_Name", header: "Added By" },
          { key: "Updated_On", header: "Updated On" },
          { key: "Updated_By_Name", header: "Updated By" },
        ],
      };

      this.exportExcelFile(exportOptions);
    },
    onApplySearch(val) {
      if (!val) {
        this.emailTemplateData = this.backupFilterData;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.backupFilterData;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.emailTemplateData = searchItems;
        if (this.emailTemplateData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    resetFilter() {
      this.emailTemplateData = this.backUpEmailTemplateData;
      this.backupFilterData = this.backUpEmailTemplateData;
      this.emptyFilterScreen = false;
      this.selectedForm = null;
      this.$refs.formFilterRef.resetAllModelValues();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    applyFilter(filterParams) {
      this.emailTemplateData = filterParams;
      this.backupFilterData = filterParams;
      this.onApplySearch(this.searchValue);
      if (this.emailTemplateData.length == 0) {
        this.emptyFilterScreen = true;
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    fetchEmailTemplates() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: LIST_CUSTOM_EMAIL_TEMPLATES,
          variables: {
            formId: null,
          },
          client: "apolloClientI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listCustomEmailTemplates &&
            response.data.listCustomEmailTemplates.emailTemplates
          ) {
            this.emailTemplateData = [];
            this.backUpEmailTemplateData =
              response.data.listCustomEmailTemplates.emailTemplates;
            this.emailTemplateData = this.backUpEmailTemplateData;
            this.backupFilterData = this.backUpEmailTemplateData;
            this.filteredList = this.backUpEmailTemplateData;
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          vm.handleListError(err);
          vm.listLoading = false;
        });
    },
    handleListError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "email templates",
        isListError: false,
      });
    },
    refetchEmailTemplateList() {
      this.isErrorInList = false;
      this.fetchEmailTemplates();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.selectedForm = null;
    },
    viewEmailTemplateDetails(item) {
      this.showViewForm = true;
      this.selectedEmailTemplateDetails = item;
      let emailSenderName = this.senderNameArray?.length
        ? this.senderNameArray[0].Sender_Name
        : "HRAPP Notfication";
      this.selectedEmailTemplateDetails.Sender_Name =
        this.selectedEmailTemplateDetails?.Sender_Name || emailSenderName;
    },
    onActions(type, item) {
      if (type && type.toLowerCase() === "clone") {
        this.cloneEmailTemplate(item);
      } else if (type && type.toLowerCase() === "edit") {
        this.editEmailTemplate(item);
      } else if (type && type.toLowerCase() === "delete") {
        this.validateDeletionOfEmailTemplate(item, "fas fa-trash");
      }
    },
    closeEmailTemplateForm() {
      this.openEmailTemplateForm = false;
    },
    closeView() {
      this.showViewForm = false;
    },
    editEmailTemplate(item = {}) {
      this.openEmailTemplateForm = true;
      this.isEditForm = true;
      this.showViewForm = false;
      if (Object.keys(item).length) {
        this.selectedEmailTemplateDetails = item;
      }
    },
    validateDeletionOfEmailTemplate(item, warningicon) {
      this.openWarningPopUp(item, warningicon);
    },
    onCloseWarningModal() {
      this.warningIconClass = "";
      this.openWarningModal = false;
      this.deleteItem = null;
    },
    openWarningPopUp(item, warningicon) {
      if (item === null) {
        this.warningIconClass = warningicon;
        this.openWarningModal = true;
        return;
      } else {
        this.warningIconClass = warningicon;
        this.openWarningModal = true;
      }
    },
    async getDropdownDetails() {
      let vm = this;
      vm.dropdownLoading = true;
      const {
        clients: { apolloClientA },
      } = vm.$apolloProvider;

      await vm.$store
        .dispatch("getDropdownDetails", {
          apolloClient: apolloClientA,
          payload: {
            formId: 310,
            key: [
              "custom_template_category",
              "custom_template_category_type",
              "custom_template_forms",
              "email_notification_setting",
            ],
          },
        })
        .then((res) => {
          if (
            res.data &&
            res.data.retrieveDropdownDetails &&
            res.data.retrieveDropdownDetails.dropdownDetails &&
            !res.data.retrieveDropdownDetails.errorCode
          ) {
            const tempData = JSON.parse(
              res.data.retrieveDropdownDetails.dropdownDetails
            );
            let formListIds = tempData[2]?.data || [];
            let tempFormIds = formListIds.map((item) => item.Form_Id);
            vm.senderNameArray = tempData[3]?.data || [];
            vm.formList = vm.multipleAccessRights(tempFormIds) || [];
          } else {
            let err = res.data.retrieveDropdownDetails.errorCode;
            vm.handleGetDropdownDetails(err);
          }
          vm.dropdownLoading = false;
        })
        .catch((err) => {
          vm.dropdownLoading = false;
          vm.handleGetDropdownDetails(err);
        });
    },
    handleGetDropdownDetails(err) {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "dropdown details",
        isListError: false,
      });
    },
    filteredItemList() {
      this.onApplySearch();
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
      this.emailTemplateData = this.backUpEmailTemplateData;
      this.backupFilterData = this.backUpEmailTemplateData;
      this.emptyFilterScreen = false;
      this.$refs.formFilterRef.resetAllModelValues();
      if (this.selectedForm) {
        let searchList = this.backUpEmailTemplateData;
        searchList = searchList.filter(
          (item) => item.Form_Id === this.selectedForm
        );
        this.emailTemplateData = searchList;
        this.backupFilterData = searchList;
        this.filteredList = searchList;
        if (this.emailTemplateData.length == 0) {
          this.emptyFilterScreen = true;
        }
      }
    },
    cloneEmailTemplate(item) {
      this.openEmailTemplateForm = true;
      this.isCloneForm = true;
      this.isEditForm = false;
      if (Object.keys(item).length) {
        this.selectedEmailTemplateDetails = item;
      }
    },
    refetchEmailList() {
      this.openEmailTemplateForm = false;
      this.refetchEmailTemplateList();
    },
  },
};
</script>
<style scoped>
.job-post-container {
  padding: 5em 2em 0em 3em;
}

table {
  border-collapse: collapse;
  width: 100%;
}

th {
  text-align: left;
  padding: 8px;
}

td {
  text-align: left;
  padding: 8px;
  background-color: #ffffff;
}

th:first-child {
  position: sticky;
  left: 0;
  border: 0px;
}

th:last-child {
  border: 0px;
}

thead th {
  position: sticky;
  top: 0;
  z-index: 2000;
}

@media screen and (max-width: 600px) {
  thead {
    display: contents !important;
  }
  thead th {
    position: relative;
  }
  th:first-child {
    position: relative;
  }
}

@media screen and (max-width: 805px) {
  .job-post-container {
    padding: 4em 1em 0em 1em;
  }
}

.selected-row {
  background-color: rgb(var(--v-theme-hover));
}

::v-deep.v-dialog > .v-overlay__content > .v-card {
  align-self: center !important;
}
</style>
