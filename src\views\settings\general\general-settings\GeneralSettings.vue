<template>
  <div>
    <!-- Top Bar with Tabs -->
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    />
    <!-- Main Content -->
    <v-container fluid class="general-settings-container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <!-- Loading State -->
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <!-- Error State -->
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="showRetryBtn ? this.$t('common.retry') : ''"
            @button-click="fetchSettings()"
          />

          <!-- Main Settings Form -->
          <div v-else>
            <v-card class="pa-4 my-4 rounded-lg overflow-visible">
              <v-row v-if="!isEditMode" justify="end">
                <v-col cols="auto">
                  <!-- Edit Button - i18n: common.edit -->
                  <v-btn
                    v-if="formAccess?.update"
                    color="primary"
                    variant="elevated"
                    rounded="lg"
                    size="default"
                    @click="enableEditMode"
                  >
                    <v-icon class="mr-1" size="small">fas fa-edit</v-icon>
                    {{ this.$t("common.edit") }}
                  </v-btn>
                </v-col>
              </v-row>
              <v-row v-else justify="end">
                <v-col cols="auto">
                  <!-- Cancel Button - i18n: common.cancel -->
                  <v-btn
                    variant="outlined"
                    rounded="lg"
                    size="default"
                    class="mr-2"
                    @click="cancelEdit"
                  >
                    {{ this.$t("common.cancel") }}
                  </v-btn>
                  <!-- Save Button - i18n: common.save -->
                  <v-btn
                    v-if="isFormDirty"
                    rounded="lg"
                    variant="elevated"
                    class="primary"
                    @click="validateDocuments()"
                    >{{ this.$t("common.save") }}</v-btn
                  >
                  <!-- Save Button Tooltip - i18n: settings.general.noChangesToUpdate -->
                  <v-tooltip
                    v-else
                    :text="this.$t('settings.general.noChangesToUpdate')"
                    location="top"
                  >
                    <template v-slot:activator="{ props }">
                      <v-btn
                        v-bind="props"
                        color="primary"
                        variant="elevated"
                        rounded="lg"
                        class="cursor-not-allow primary"
                      >
                        {{ this.$t("common.save") }}
                      </v-btn>
                    </template>
                  </v-tooltip>
                </v-col>
              </v-row>
              <!-- Edit Mode Form -->
              <v-form
                v-if="isEditMode"
                ref="generalSettingsForm"
                @submit.prevent="saveSettings"
              >
                <v-row no-gutters>
                  <!-- Left Panel - Brand Assets -->
                  <v-col
                    cols="12"
                    md="6"
                    :class="windowWidth > 768 ? 'border-e-md' : ''"
                  >
                    <div class="pa-3 pa-md-4">
                      <div class="d-flex align-center mb-3">
                        <v-progress-circular
                          model-value="100"
                          color="blue"
                          :size="22"
                          class="mr-2"
                        />
                        <!-- Brand Assets Section Header - i18n: settings.general.brandAssets -->
                        <div class="text-h6 font-weight-bold text-primary">
                          {{ this.$t("settings.general.brandAssets") }}
                        </div>
                      </div>

                      <!-- Company Logo Section -->
                      <div class="mb-4">
                        <v-row align="center">
                          <v-col cols="12" sm="4" class="d-flex align-center">
                            <!-- Company Logo Label - i18n: settings.general.companyLogo -->
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.companyLogo") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" class="d-flex align-center">
                            <div class="d-flex align-center mb-2">
                              <div
                                class="mr-4 position-relative"
                                style="width: 80px; height: 40px"
                              >
                                <v-card
                                  class="d-flex align-center justify-center"
                                  style="width: 80px; height: 40px"
                                  variant="outlined"
                                  color="grey-lighten-4"
                                >
                                  <span v-if="companyLogoPreview">
                                    <img
                                      v-if="companyLogoPreview"
                                      :src="companyLogoPreview"
                                      :alt="
                                        this.$t(
                                          'settings.general.companyLogoPreview'
                                        )
                                      "
                                      class="w-100 h-100 rounded"
                                      style="
                                        object-fit: cover;
                                        object-position: center;
                                      "
                                      loading="eager"
                                    />
                                  </span>
                                  <div
                                    v-else
                                    class="d-flex flex-column align-center justify-center h-100"
                                  >
                                    <v-icon color="grey-lighten-1" size="24"
                                      >fas fa-image</v-icon
                                    >
                                    <span
                                      class="text-caption text-grey-lighten-1 mt-1"
                                      >200×80</span
                                    >
                                  </div>
                                </v-card>
                              </div>
                            </div>
                          </v-col>
                          <v-col cols="12" sm="6" class="d-flex align-center">
                            <v-file-input
                              ref="companyLogoFileInput"
                              v-model="formData.companyLogoFile"
                              prepend-icon=""
                              clearable
                              append-inner-icon="fas fa-paperclip"
                              :label="this.$t('settings.general.companyLogo')"
                              accept="image/png,image/jpeg,image/jpg,image/gif"
                              variant="solo"
                              density="compact"
                              hide-details="auto"
                              :rules="companyLogoValidationRules"
                              :loading="isLoading"
                              :hint="
                                this.$t('settings.general.companyLogoHint')
                              "
                              persistent-hint
                              @update:model-value="handleCompanyLogoUpload"
                            >
                              <template v-slot:selection="{ fileNames }">
                                <span class="text-truncate">
                                  {{
                                    fileNames?.length > 0
                                      ? fileNames[0]?.length > 25
                                        ? fileNames[0].slice(0, 22) + "..."
                                        : fileNames[0]
                                      : ""
                                  }}
                                </span>
                              </template>
                            </v-file-input>
                          </v-col>
                        </v-row>
                      </div>

                      <!-- Favicon Section -->
                      <div class="mb-4">
                        <v-row align="center">
                          <v-col cols="12" sm="4" class="d-flex align-center">
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.favicon") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="2" class="d-flex align-center">
                            <div class="d-flex align-center mb-2">
                              <div
                                class="mr-4 position-relative"
                                style="width: 40px; height: 40px"
                              >
                                <v-card
                                  class="d-flex align-center justify-center"
                                  style="width: 40px; height: 40px"
                                  variant="outlined"
                                  color="grey-lighten-4"
                                >
                                  <span v-if="faviconPreview">
                                    <img
                                      v-if="faviconPreview"
                                      :src="faviconPreview"
                                      :alt="
                                        this.$t(
                                          'settings.general.faviconPreview'
                                        )
                                      "
                                      class="w-100 h-100 rounded"
                                      style="
                                        object-fit: cover;
                                        object-position: center;
                                      "
                                      loading="eager"
                                    />
                                  </span>
                                  <div
                                    v-else
                                    class="d-flex flex-column align-center justify-center h-100"
                                  >
                                    <v-icon color="grey-lighten-1" size="16"
                                      >fas fa-star</v-icon
                                    >
                                    <span
                                      class="text-caption text-grey-lighten-1 mt-1"
                                      >32×32</span
                                    >
                                  </div>
                                </v-card>
                              </div>
                            </div>
                          </v-col>
                          <v-col cols="12" sm="6" class="d-flex align-center">
                            <v-file-input
                              ref="faviconFileInput"
                              v-model="formData.faviconFile"
                              prepend-icon=""
                              clearable
                              append-inner-icon="fas fa-paperclip"
                              :label="this.$t('settings.general.favicon')"
                              accept="image/png,image/jpeg,image/jpg,image/gif,image/ico"
                              variant="solo"
                              density="compact"
                              hide-details="auto"
                              :rules="faviconValidationRules"
                              :loading="isLoading"
                              :hint="this.$t('settings.general.faviconHint')"
                              persistent-hint
                              @update:model-value="handleFaviconUpload"
                            >
                              <template v-slot:selection="{ fileNames }">
                                <span class="text-truncate">
                                  {{
                                    fileNames?.length > 0
                                      ? fileNames[0]?.length > 25
                                        ? fileNames[0].slice(0, 22) + "..."
                                        : fileNames[0]
                                      : ""
                                  }}
                                </span>
                              </template>
                            </v-file-input>
                          </v-col>
                        </v-row>
                      </div>

                      <!-- Company Logo Toggle -->
                      <div class="mb-4">
                        <v-row align="center">
                          <v-col cols="12" sm="4" class="d-flex align-center">
                            <!-- Use Company Logo As Product Logo Label - i18n: settings.general.useCompanyLogoAsProductLogo -->
                            <div class="text-body-2 font-weight-medium">
                              {{
                                this.$t(
                                  "settings.general.useCompanyLogoAsProductLogo"
                                )
                              }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <v-switch
                                ref="useCompanyLogoAsProductLogoSwitch"
                                v-model="formData.useCompanyLogoAsProductLogo"
                                true-value="Yes"
                                false-value="No"
                                :disabled="!isEditMode"
                                density="compact"
                                color="primary"
                                hide-details
                                @update:model-value="
                                  onChangeFields(
                                    $event,
                                    'useCompanyLogoAsProductLogo'
                                  )
                                "
                              />
                            </div>
                          </v-col>
                        </v-row>
                      </div>
                    </div>
                  </v-col>

                  <!-- Right Panel - Layout and Styling -->
                  <v-col cols="12" md="6">
                    <div class="pa-3 pa-md-4">
                      <div class="d-flex align-center mb-3">
                        <v-progress-circular
                          model-value="100"
                          color="red"
                          :size="22"
                          class="mr-2"
                        />
                        <!-- Layout and Styling Section Header - i18n: settings.general.layoutAndStyling -->
                        <div class="text-h6 font-weight-bold text-primary">
                          {{ this.$t("settings.general.layoutAndStyling") }}
                        </div>
                      </div>

                      <!-- Color Controls -->
                      <div class="mb-3">
                        <!-- Color Controls Subsection Header - i18n: settings.general.colorControls -->
                        <div class="text-subtitle-1 font-weight-medium mb-2">
                          {{ this.$t("settings.general.colorControls") }}
                        </div>

                        <!-- Primary Color -->
                        <v-row align="center">
                          <v-col cols="12" sm="4">
                            <!-- Primary Color Label - i18n: settings.general.primaryColor -->
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.primaryColor") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor: formData.primaryColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                  cursor: isEditMode ? 'pointer' : 'default',
                                  transition: 'all 0.2s ease',
                                }"
                                @click="
                                  isEditMode && openColorPicker('primary')
                                "
                              ></div>
                              <v-text-field
                                ref="primaryColorField"
                                v-model="formData.primaryColor"
                                variant="solo"
                                density="compact"
                                class="mt-2"
                                :rules="[
                                  required(
                                    this.$t('settings.general.primaryColor'),
                                    formData.primaryColor
                                  ),
                                  isValidHexColor(formData.primaryColor),
                                ]"
                                :readonly="!isEditMode"
                                :disabled="isLoading"
                                style="max-width: 120px"
                                placeholder="#1C277D"
                                @update:model-value="
                                  onChangeFields($event, 'primaryColor')
                                "
                              />
                            </div>
                          </v-col>
                        </v-row>

                        <!-- Secondary Color -->
                        <v-row align="center">
                          <v-col cols="12" sm="4">
                            <!-- Secondary Color Label - i18n: settings.general.secondaryColor -->
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.secondaryColor") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor: formData.secondaryColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                  cursor: isEditMode ? 'pointer' : 'default',
                                  transition: 'all 0.2s ease',
                                }"
                                @click="
                                  isEditMode && openColorPicker('secondary')
                                "
                              ></div>
                              <v-text-field
                                ref="secondaryColorField"
                                v-model="formData.secondaryColor"
                                variant="solo"
                                density="compact"
                                class="mt-2"
                                :rules="[
                                  required(
                                    this.$t('settings.general.secondaryColor'),
                                    formData.secondaryColor
                                  ),
                                  isValidHexColor(formData.secondaryColor),
                                ]"
                                :readonly="!isEditMode"
                                :disabled="isLoading"
                                style="max-width: 120px"
                                placeholder="#F1F5F9"
                                @update:model-value="
                                  onChangeFields($event, 'secondaryColor')
                                "
                              />
                            </div>
                          </v-col>
                        </v-row>

                        <!-- Hover Color -->
                        <v-row align="center">
                          <v-col cols="12" sm="4">
                            <!-- Hover Color Label - i18n: settings.general.hoverColor -->
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.hoverColor") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor: formData.hoverColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                  cursor: isEditMode ? 'pointer' : 'default',
                                  transition: 'all 0.2s ease',
                                }"
                                @click="isEditMode && openColorPicker('hover')"
                              ></div>
                              <v-text-field
                                ref="hoverColorField"
                                v-model="formData.hoverColor"
                                variant="solo"
                                density="compact"
                                class="mt-2"
                                :rules="[
                                  required(
                                    this.$t('settings.general.hoverColor'),
                                    formData.hoverColor
                                  ),
                                  isValidHexColor(formData.hoverColor),
                                ]"
                                :readonly="!isEditMode"
                                :disabled="isLoading"
                                style="max-width: 120px"
                                placeholder="#0F1B5C"
                                @update:model-value="
                                  onChangeFields($event, 'hoverColor')
                                "
                              />
                            </div>
                          </v-col>
                        </v-row>

                        <!-- Data Table Header Panel Color -->
                        <v-row align="center">
                          <v-col cols="12" sm="4">
                            <!-- Data Table Header Panel Color Label - i18n: settings.general.dataTableHeaderPanel -->
                            <div class="text-body-2 font-weight-medium">
                              {{
                                this.$t("settings.general.dataTableHeaderPanel")
                              }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor: formData.tableHeaderColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                  cursor: isEditMode ? 'pointer' : 'default',
                                  transition: 'all 0.2s ease',
                                }"
                                @click="
                                  isEditMode && openColorPicker('tableHeader')
                                "
                              ></div>
                              <v-text-field
                                ref="tableHeaderColorField"
                                v-model="formData.tableHeaderColor"
                                variant="solo"
                                density="compact"
                                class="mt-2"
                                :rules="[
                                  required(
                                    this.$t(
                                      'settings.general.dataTableHeaderPanel'
                                    ),
                                    formData.tableHeaderColor
                                  ),
                                  isValidHexColor(formData.tableHeaderColor),
                                ]"
                                :readonly="!isEditMode"
                                :disabled="isLoading"
                                style="max-width: 120px"
                                placeholder="#FFFFFF"
                                @update:model-value="
                                  onChangeFields($event, 'tableHeaderColor')
                                "
                              />
                            </div>
                          </v-col>
                        </v-row>

                        <!-- Data Table Header Text Color -->
                        <v-row align="center">
                          <v-col cols="12" sm="4">
                            <!-- Data Table Header Text Color Label - i18n: settings.general.dataTableHeaderText -->
                            <div class="text-body-2 font-weight-medium">
                              {{
                                this.$t("settings.general.dataTableHeaderText")
                              }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor:
                                    formData.tableHeaderTextColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                  cursor: isEditMode ? 'pointer' : 'default',
                                  transition: 'all 0.2s ease',
                                }"
                                @click="
                                  isEditMode &&
                                    openColorPicker('tableHeaderText')
                                "
                              ></div>
                              <v-text-field
                                ref="tableHeaderTextColorField"
                                v-model="formData.tableHeaderTextColor"
                                variant="solo"
                                density="compact"
                                class="mt-3"
                                :rules="[
                                  required(
                                    this.$t(
                                      'settings.general.dataTableHeaderText'
                                    ),
                                    formData.tableHeaderTextColor
                                  ),
                                  isValidHexColor(
                                    formData.tableHeaderTextColor
                                  ),
                                ]"
                                :readonly="!isEditMode"
                                :disabled="isLoading"
                                style="max-width: 120px"
                                placeholder="#000000"
                                @update:model-value="
                                  onChangeFields($event, 'tableHeaderTextColor')
                                "
                              />
                            </div>
                          </v-col>
                        </v-row>
                      </div>
                    </div>
                  </v-col>
                </v-row>
              </v-form>

              <!-- View Mode -->
              <div v-else>
                <v-row no-gutters>
                  <!-- Left Panel - Brand Assets (View Mode) -->
                  <v-col
                    cols="12"
                    md="6"
                    :class="windowWidth > 768 ? 'border-e-md' : ''"
                  >
                    <div class="pa-4 pa-md-6">
                      <div class="d-flex align-center mb-4">
                        <v-progress-circular
                          model-value="100"
                          color="blue"
                          :size="22"
                          class="mr-2"
                        />
                        <!-- Brand Assets Section Header (View Mode) - i18n: settings.general.brandAssets -->
                        <div class="text-h6 font-weight-bold text-primary">
                          {{ this.$t("settings.general.brandAssets") }}
                        </div>
                      </div>

                      <!-- Company Logo Section (View Mode) -->
                      <div class="mb-4">
                        <v-row align="center">
                          <v-col cols="12" sm="4" class="d-flex align-center">
                            <!-- Company Logo Label (View Mode) - i18n: settings.general.companyLogo -->
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.companyLogo") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <v-card
                                class="mr-4 d-flex align-center justify-center"
                                style="width: 200px; height: 80px"
                                variant="outlined"
                                color="grey-lighten-4"
                              >
                                <span v-if="companyLogoImageSrc">
                                  <img
                                    :src="companyLogoImageSrc"
                                    :alt="
                                      this.$t('settings.general.companyLogo')
                                    "
                                    class="w-100 h-100 rounded"
                                    style="
                                      object-fit: cover;
                                      object-position: center;
                                    "
                                    loading="eager"
                                  />
                                </span>
                                <div
                                  v-else
                                  class="d-flex flex-column align-center justify-center h-100"
                                >
                                  <v-icon color="grey-lighten-1" size="24"
                                    >fas fa-image</v-icon
                                  >
                                  <span
                                    class="text-caption text-grey-lighten-1 mt-1"
                                    >200×80</span
                                  >
                                </div>
                              </v-card>
                            </div>
                          </v-col>
                        </v-row>
                      </div>

                      <!-- Favicon Section (View Mode) -->
                      <div class="mb-4">
                        <v-row align="center">
                          <v-col cols="12" sm="4" class="d-flex align-center">
                            <!-- Favicon Label (View Mode) - i18n: settings.general.favicon -->
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.favicon") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <v-card
                                class="d-flex align-center justify-center"
                                style="width: 32px; height: 32px"
                                variant="outlined"
                                color="grey-lighten-4"
                              >
                                <span v-if="faviconImageSrc">
                                  <img
                                    :src="faviconImageSrc"
                                    :alt="this.$t('settings.general.favicon')"
                                    class="w-100 h-100 rounded"
                                    style="
                                      object-fit: cover;
                                      object-position: center;
                                    "
                                    loading="eager"
                                  />
                                </span>
                                <div
                                  v-else
                                  class="d-flex flex-column align-center justify-center h-100"
                                >
                                  <v-icon color="grey-lighten-1" size="16"
                                    >fas fa-star</v-icon
                                  >
                                  <span
                                    class="text-caption text-grey-lighten-1 mt-1"
                                    >32×32</span
                                  >
                                </div>
                              </v-card>
                            </div>
                          </v-col>
                        </v-row>
                      </div>

                      <!-- Company Logo Toggle (View Mode) -->
                      <div class="mb-4">
                        <v-row align="center">
                          <v-col cols="12" sm="4" class="d-flex align-center">
                            <!-- Use Company Logo As Product Logo Label (View Mode) - i18n: settings.general.useCompanyLogoAsProductLogo -->
                            <div class="text-body-2 font-weight-medium">
                              {{
                                this.$t(
                                  "settings.general.useCompanyLogoAsProductLogo"
                                )
                              }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="text-subtitle-1 font-weight-regular">
                              <section class="text-body-2">
                                {{
                                  itemData.useCompanyLogoAsProductLogo || "No"
                                }}
                              </section>
                            </div>
                          </v-col>
                        </v-row>
                      </div>
                    </div>
                  </v-col>

                  <!-- Right Panel - Layout and Styling (View Mode) -->
                  <v-col cols="12" md="6">
                    <div class="pa-4 pa-md-6">
                      <div class="d-flex align-center mb-4">
                        <v-progress-circular
                          model-value="100"
                          color="red"
                          :size="22"
                          class="mr-2"
                        />
                        <div class="text-h6 font-weight-bold text-primary">
                          {{ this.$t("settings.general.layoutAndStyling") }}
                        </div>
                      </div>

                      <!-- Color Controls (View Mode) -->
                      <div class="mb-4">
                        <div class="text-subtitle-1 font-weight-medium mb-3">
                          {{ this.$t("settings.general.colorControls") }}
                        </div>

                        <!-- Primary Color (View Mode) -->
                        <v-row align="center" class="mb-3">
                          <v-col cols="12" sm="4">
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.primaryColor") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor: itemData.primaryColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                }"
                              ></div>
                              <div class="text-body-2 text-grey-darken-1">
                                {{ itemData.primaryColor }}
                              </div>
                            </div>
                          </v-col>
                        </v-row>

                        <!-- Secondary Color (View Mode) -->
                        <v-row align="center" class="mb-3">
                          <v-col cols="12" sm="4">
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.secondaryColor") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor: itemData.secondaryColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                }"
                              ></div>
                              <div class="text-body-2 text-grey-darken-1">
                                {{ itemData.secondaryColor }}
                              </div>
                            </div>
                          </v-col>
                        </v-row>

                        <!-- Hover Color (View Mode) -->
                        <v-row align="center" class="mb-3">
                          <v-col cols="12" sm="4">
                            <div class="text-body-2 font-weight-medium">
                              {{ this.$t("settings.general.hoverColor") }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor: itemData.hoverColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                }"
                              ></div>
                              <div class="text-body-2 text-grey-darken-1">
                                {{ itemData.hoverColor }}
                              </div>
                            </div>
                          </v-col>
                        </v-row>

                        <!-- Data Table Header Panel Color (View Mode) -->
                        <v-row align="center">
                          <v-col cols="12" sm="4">
                            <div class="text-body-2 font-weight-medium">
                              {{
                                this.$t("settings.general.dataTableHeaderPanel")
                              }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor: itemData.tableHeaderColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                }"
                              ></div>
                              <div class="text-body-2 text-grey-darken-1">
                                {{ itemData.tableHeaderColor }}
                              </div>
                            </div>
                          </v-col>
                        </v-row>

                        <!-- Data Table Header Text Color (View Mode) -->
                        <v-row align="center">
                          <v-col cols="12" sm="4">
                            <div class="text-body-2 font-weight-medium">
                              {{
                                this.$t("settings.general.dataTableHeaderText")
                              }}
                            </div>
                          </v-col>
                          <v-col cols="12" sm="8">
                            <div class="d-flex align-center">
                              <div
                                class="color-preview rounded mr-3"
                                :style="{
                                  backgroundColor:
                                    itemData.tableHeaderTextColor,
                                  width: '32px',
                                  height: '32px',
                                  border: '1px solid #e0e0e0',
                                }"
                              ></div>
                              <div class="text-body-2 text-grey-darken-1">
                                {{ itemData.tableHeaderTextColor }}
                              </div>
                            </div>
                          </v-col>
                        </v-row>
                      </div>
                    </div>
                  </v-col>
                </v-row>
                <v-row class="px-sm-8 px-md-10 my-4 d-flex justify-center">
                  <v-col v-if="moreDetailsList.length > 0" cols="12">
                    <MoreDetails
                      :more-details-list="moreDetailsList"
                      :open-close-card="openMoreDetails"
                      @on-open-close="openMoreDetails = $event"
                    /> </v-col
                ></v-row>
              </div>
            </v-card>
          </div>
        </v-window-item>
      </v-window>

      <!-- Access Denied -->
      <AppAccessDenied v-else />
    </v-container>

    <!-- Loading Overlay -->
    <AppLoading v-if="isLoading" />

    <!-- Color Picker Dialog -->
    <v-dialog v-model="showColorPicker" max-width="400">
      <v-card>
        <!-- Color Picker Dialog Title - i18n: settings.general.chooseColor -->
        <v-card-title>
          {{
            this.$t("settings.general.chooseColor", {
              colorType: currentColorType,
            })
          }}
        </v-card-title>
        <v-card-text>
          <div class="text-center">
            <input
              ref="colorPicker"
              type="color"
              :value="getCurrentColor()"
              @input="updateColor"
              style="
                width: 100px;
                height: 100px;
                border: none;
                border-radius: 8px;
                cursor: pointer;
              "
            />
            <div class="mt-3">
              <!-- Preset Colors Label - i18n: settings.general.presetColors -->
              <div class="text-body-2 mb-2">
                {{ this.$t("settings.general.presetColors") }}
              </div>
              <div class="d-flex flex-wrap ga-2">
                <v-card
                  v-for="color in presetColors"
                  :key="color"
                  class="rounded cursor-pointer"
                  :style="{ backgroundColor: color }"
                  width="32"
                  height="32"
                  variant="outlined"
                  @click="selectPresetColor(color)"
                ></v-card>
              </div>
            </div>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <!-- Color Picker Cancel Button - i18n: common.cancel -->
          <v-btn
            variant="outlined"
            rounded="lg"
            @click="showColorPicker = false"
          >
            {{ this.$t("common.cancel") }}
          </v-btn>
          <!-- Color Picker Apply Button - i18n: common.apply -->
          <v-btn
            variant="elevated"
            rounded="lg"
            color="primary"
            @click="applyColor"
          >
            {{ this.$t("common.apply") }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { generateRandomColor, convertUTCToLocal } from "@/helper";
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import Config from "@/config";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";

import {
  RETRIEVE_GENERAL_SETTINGS,
  ADD_UPDATE_GENERAL_SETTINGS,
} from "@/graphql/settings/careerConfigurationQueries.js";

export default {
  name: "GeneralSettings",
  mixins: [validationRules],
  components: {
    MoreDetails,
  },
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      isFormDirty: false,
      isEditMode: false,
      listLoading: false,
      isLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      currentTabItem: "",
      companyLogoPreview: null,
      faviconPreview: null,
      formData: {},
      itemData: {
        generalSettingId: null,
        faviconFilename: null,
        companyLogo: null,
        careerLogoPath: null,
        useCompanyLogoAsProductLogo: "No",
        primaryColor: "#00263E",
        secondaryColor: "#FFFFFF",
        hoverColor: "#e5e9eb",
        tableHeaderColor: "#FFFFFF",
        tableHeaderTextColor: "#000000",
        // Career page related fields
        careerBannerImage: null,
        careerHeadlineText: null,
        careerSubHeadlineText: null,
        careerTextHorizontalPosition: null,
        careerTextVerticalPosition: null,
        careerBannerOpacity: null,
        careerHeadlineFontFamily: null,
        careerHeadingFontSize: null,
        careerHeadlineFontColor: null,
        careerSubHeadlineFontFamily: null,
        careerSubHeadlineFontSize: null,
        careerSubHeadlineFontColor: null,
        // Legacy fields for backward compatibility
        favicon: null,
        // File upload fields
        companyLogoFile: null,
        faviconFile: null,
      },
      originalData: {},
      showColorPicker: false,
      currentColorType: "",
      tempColor: "",
      presetColors: [],
      companyLogoValidationRules: [
        (files) => {
          // Allow empty/null files (for edit mode without changes)
          if (!files || files?.length === 0) return true;

          // Handle both File objects and existing file data
          const file = Array.isArray(files) ? files[0] : files;

          // If it's not a File object (existing data), skip validation
          if (typeof File === "undefined" || !(file instanceof File))
            return true;

          const maxSize = 2 * 1024 * 1024; // 2MB
          return (
            file.size <= maxSize ||
            this.$t("settings.general.fileSizeMustBeLessThan2MB")
          );
        },
      ],
      faviconValidationRules: [
        (files) => {
          // Allow empty/null files (for edit mode without changes)
          if (!files || files?.length === 0) return true;

          // Handle both File objects and existing file data
          const file = Array.isArray(files) ? files[0] : files;

          // If it's not a File object (existing data), skip validation
          if (typeof File === "undefined" || !(file instanceof File))
            return true;

          const maxSize = 1 * 1024 * 1024; // 1MB
          return (
            file.size <= maxSize ||
            this.$t("settings.general.fileSizeMustBeLessThan1MB")
          );
        },
      ],
    };
  },
  computed: {
    landedFormName() {
      let generalForm = this.accessRights("201");
      if (generalForm?.customFormName && generalForm.customFormName !== "") {
        return generalForm.customFormName;
      } else return this.$t("settings.general.general");
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    formAccess() {
      let formAccessRights = this.accessRights("201");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"] &&
        formAccessRights.accessRights["admin"] === "admin"
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    settingsGeneralFormAccess() {
      return this.$store.getters.settingsGeneralFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.settingsGeneralFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // Computed properties for image URLs using get ImageUrl method
    companyLogoImageSrc() {
      return this.getImageUrl(this.itemData.companyLogo, "companyLogo");
    },
    faviconImageSrc() {
      return this.itemData.faviconFilename
        ? this.getImageUrl("favicon.ico", "favicon")
        : null;
    },
    bannerImageSrc() {
      return this.getImageUrl(this.itemData.bannerImage, "bannerImage");
    },
  },
  mounted() {
    if (this.formAccess?.view) {
      this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
      this.fetchSettings();
    }
  },
  methods: {
    convertUTCToLocal,
    fetchSettings() {
      this.listLoading = true;
      this.errorContent = "";
      this.isErrorInList = false;

      this.$apollo
        .query({
          query: RETRIEVE_GENERAL_SETTINGS,
          client: "apolloClientI",
          variables: {
            formId: 201, // General Settings form ID
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data?.retrieveGeneralSettings?.generalSettings) {
            const settings =
              response.data.retrieveGeneralSettings.generalSettings;

            if (settings) {
              // Map API response to form data - include ALL fields from RETRIEVE_GENERAL_SETTINGS
              this.itemData = {
                generalSettingId: settings.General_Setting_Id,
                faviconFilename: settings.Favicon_Filename,
                companyLogo: this.normalizeFilename(
                  settings.Company_Logo,
                  "companyLogo"
                ),
                useCompanyLogoAsProductLogo:
                  settings.Use_Company_Logo_As_Product_Logo,
                careerLogoPath: settings.Career_Logo_Path,
                primaryColor: settings.Primary_Color || "#00263E",
                secondaryColor: settings.Secondary_Color || "#FFFFFF",
                hoverColor: settings.Hover_Color || "#e5e9eb",
                tableHeaderColor: settings.Table_Header_Color || "#FFFFFF",
                tableHeaderTextColor:
                  settings.Table_Header_Text_Color || "#000000",
                // Career page related fields
                careerBannerImage: settings.Career_Banner_Image,
                careerHeadlineText: settings.Career_Headline_Text,
                careerSubHeadlineText: settings.Career_Sub_Headline_Text,
                careerTextHorizontalPosition:
                  settings.Career_Text_Horizontal_Position,
                careerTextVerticalPosition:
                  settings.Career_Text_Vertical_Position,
                careerBannerOpacity: settings.Career_Banner_Opacity,
                careerHeadlineFontFamily: settings.Career_Headline_Font_Family,
                careerHeadingFontSize: settings.Career_Heading_Font_Size,
                careerHeadlineFontColor: settings.Career_Headline_Font_Color,
                careerSubHeadlineFontFamily:
                  settings.Career_Sub_Headline_Font_Family,
                careerSubHeadlineFontSize:
                  settings.Career_Sub_Headline_Font_Size,
                careerSubHeadlineFontColor:
                  settings.Career_Sub_Headline_Font_Color,
                Added_On: settings.Added_On,
                Added_By: settings.Added_By,
                Updated_On: settings.Updated_On,
                Updated_By: settings.Updated_By,
              };
            } else {
              // No existing settings, use defaults
              this.itemData = { ...this.itemData };
            }
          }
          this.originalData = { ...this.itemData };
          this.prefillMoreDetails();
          this.listLoading = false;
        })
        .catch((error) => {
          this.listLoading = false;
          this.handleListError(error);
        });
    },
    handleListError(error) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: error,
          action: "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    // Helper method to ensure backward compatibility with existing filenames
    normalizeFilename(filename, type) {
      if (!filename) return null;

      // If it's already a standardized filename, return as is
      if (type === "companyLogo" && filename.startsWith("company.")) {
        return filename;
      }
      // For legacy filenames, return as is for backward compatibility
      // The system will gradually migrate to new patterns as files are re-uploaded
      return filename;
    },
    enableEditMode() {
      this.isEditMode = true;
      this.formData = {
        ...this.itemData,
        companyLogoFile: { name: this.originalData.companyLogo },
        faviconFile: { name: this.originalData.faviconFilename },
      };
      this.companyLogoPreview = this.companyLogoImageSrc;
      this.faviconPreview = this.faviconImageSrc;
    },
    cancelEdit() {
      this.isFormDirty = false;
      this.isEditMode = false;
      this.formData = null;
      this.companyLogoPreview = null;
      this.faviconPreview = null;
    },
    async saveSettings() {
      this.validateDocuments();
    },
    handleUpdateErrors(error) {
      this.$store.dispatch("handleApiErrors", {
        error,
        action: "updating",
        form: this.landedFormName,
        isListError: false,
      });
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.settingsGeneralFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/general/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/general/" + clickedForm.url;
        }
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    openColorPicker(type) {
      this.currentColorType = type;
      this.tempColor = this.getCurrentColor();
      // Generate 25 fresh random colors each time the dialog opens
      this.presetColors = Array.from({ length: 25 }, () =>
        generateRandomColor()
      );
      this.showColorPicker = true;
    },

    getCurrentColor() {
      const colorMap = {
        primary: this.formData.primaryColor,
        secondary: this.formData.secondaryColor,
        hover: this.formData.hoverColor,
        tableHeader: this.formData.tableHeaderColor,
        tableHeaderText: this.formData.tableHeaderTextColor,
      };
      return colorMap[this.currentColorType] || "#000000";
    },

    updateColor(event) {
      this.tempColor = event.target.value;
    },

    selectPresetColor(color) {
      this.tempColor = color;
      this.$refs.colorPicker.value = color;
    },
    onChangeFields() {
      this.isFormDirty = true;
    },

    applyColor() {
      this.isFormDirty = true;
      const colorMap = {
        primary: "primaryColor",
        secondary: "secondaryColor",
        hover: "hoverColor",
        tableHeader: "tableHeaderColor",
        tableHeaderText: "tableHeaderTextColor",
      };

      this.formData[colorMap[this.currentColorType]] = this.tempColor;
      this.showColorPicker = false;
    },

    // File upload handlers
    handleCompanyLogoUpload(files) {
      this.isFormDirty = true;
      if (!files || files?.length === 0) {
        this.formData.companyLogoFile = null;
        this.companyLogoPreview = null;
        return;
      }

      const file = Array.isArray(files) ? files[0] : files;
      this.formData.companyLogoFile = file;

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        this.companyLogoPreview = e.target.result;
      };
      reader.readAsDataURL(file);
    },

    handleFaviconUpload(files) {
      this.isFormDirty = true;
      if (!files || files?.length === 0) {
        this.formData.faviconFile = null;
        this.faviconPreview = null;
        return;
      }

      const file = Array.isArray(files) ? files[0] : files;
      this.formData.faviconFile = file;

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        this.faviconPreview = e.target.result;
      };
      reader.readAsDataURL(file);
    },

    // Synchronous file upload validation and processing
    async validateDocuments() {
      try {
        // Validate form before submitting
        const { valid } = await this.$refs.generalSettingsForm.validate();
        if (!valid) {
          return;
        }

        this.isLoading = true;

        // Handle company logo upload
        if (
          this.formData.companyLogoFile &&
          this.originalData?.companyLogo !== this.formData?.companyLogoFile.name
        ) {
          if (this.originalData.companyLogo)
            // Delete existing company logo if it's a file
            await this.deleteExistingFile(this.originalData.companyLogo);

          // Upload new company logo

          const logoFileName = await this.uploadFile(
            this.formData.companyLogoFile,
            "companyLogo"
          );
          this.formData.companyLogo = logoFileName;
        }

        // Handle favicon upload
        if (
          this.formData.faviconFile &&
          this.originalData?.faviconFilename !== this.formData?.faviconFile.name
        ) {
          // Upload new favicon
          const faviconFileName = await this.uploadFile(
            this.formData.faviconFile,
            "favicon"
          );
          this.formData.faviconFilename = faviconFileName;
        }

        this.performSave();
      } catch (error) {
        this.isLoading = false;
        this.$store.dispatch("handleApiErrors", {
          error: error,
          action: "uploading",
          form: "logo",
          isListError: false,
        });
      }
    },

    performSave() {
      this.isLoading = true;
      const faviconFile = this.formData.faviconFile ? "favicon.ico" : null;
      let companyLogo;
      if (this.formData.companyLogoFile?.name) {
        const fileExtension = this.formData.companyLogoFile.name
          .split(".")
          .pop();
        if (this.formData.companyLogoFile.name.includes(this.orgCode))
          companyLogo = this.formData.companyLogoFile.name;
        else companyLogo = `company.${fileExtension}`;
      } else companyLogo = null;
      this.$apollo
        .mutate({
          mutation: ADD_UPDATE_GENERAL_SETTINGS,
          variables: {
            generalSettingId: this.formData.generalSettingId,
            faviconFilename: faviconFile,
            companyLogo: companyLogo,
            useCompanyLogoAsProductLogo:
              this.formData.useCompanyLogoAsProductLogo,
            careerLogoPath: this.formData.careerLogoPath,
            primaryColor: this.formData.primaryColor,
            secondaryColor: this.formData.secondaryColor,
            hoverColor: this.formData.hoverColor,
            tableHeaderColor: this.formData.tableHeaderColor,
            tableHeaderTextColor: this.formData.tableHeaderTextColor,
            // Career page related fields - pass through existing values
            careerBannerImage: this.formData.careerBannerImage,
            careerHeadlineText: this.formData.careerHeadlineText,
            careerSubHeadlineText: this.formData.careerSubHeadlineText,
            careerTextHorizontalPosition:
              this.formData.careerTextHorizontalPosition,
            careerTextVerticalPosition:
              this.formData.careerTextVerticalPosition,
            careerBannerOpacity: this.formData.careerBannerOpacity,
            careerHeadlineFontFamily: this.formData.careerHeadlineFontFamily,
            careerHeadingFontSize: this.formData.careerHeadingFontSize,
            careerHeadlineFontColor: this.formData.careerHeadlineFontColor,
            careerSubHeadlineFontFamily:
              this.formData.careerSubHeadlineFontFamily,
            careerSubHeadlineFontSize: this.formData.careerSubHeadlineFontSize,
            careerSubHeadlineFontColor:
              this.formData.careerSubHeadlineFontColor,
            formId: 201, // General Settings form ID
          },
          client: "apolloClientJ",
        })
        .then((response) => {
          if (response?.data?.addUpdateGeneralSettings) {
            this.isEditMode = false;
            this.isFormDirty = false;

            this.showAlert({
              isOpen: true,
              type: "success",
              message: this.$t("settings.general.updatedSuccessfully"),
            });
            const colors = {
              Primary_Color: this.formData.primaryColor,
              Secondary_Color: this.formData.secondaryColor,
              Hover_Color: this.formData.hoverColor,
              Table_Header_Color: this.formData.tableHeaderColor,
              Table_Header_Text_Color: this.formData.tableHeaderTextColor,
            };
            localStorage.setItem("brand_color", JSON.stringify(colors));
            localStorage.setItem(
              "faviconFilename",
              this.formData.faviconFile ? "favicon.ico" : ""
            );
            window.location.reload();
          }
          this.isLoading = false;
        })
        .catch((error) => {
          this.handleUpdateErrors(error);
          this.isLoading = false;
        });
    },

    async uploadFile(file, type) {
      const timestamp = moment().unix();
      let standardizedFileName, fileUploadUrl;

      // Get file extension
      const fileExtension = file.name.split(".").pop().toLowerCase();

      if (type === "companyLogo") {
        standardizedFileName = `company.${fileExtension}`;
        fileUploadUrl = `hrapp_upload/${this.orgCode}_tmp/logos/${standardizedFileName}`;
      } else if (type === "favicon") {
        standardizedFileName = "favicon.ico";
        fileUploadUrl = `${this.domainName}/${this.orgCode}/favicon/${standardizedFileName}`;
      } else {
        // Fallback for other file types
        standardizedFileName = `${timestamp}/${type}/${file.name}`;
        fileUploadUrl = `${this.domainName}/${this.orgCode}/General Settings/${standardizedFileName}`;
      }

      await this.$store
        .dispatch("s3FileUploadRetrieveAction", {
          fileName: fileUploadUrl,
          action: "upload",
          type: "logo",
          fileContent: file,
        })
        .catch((error) => {
          throw error;
        });

      return standardizedFileName;
    },

    // Helper method to get image URL (replaces retrieveImageFromS3 for better performance)
    getImageUrl(filename, imageType) {
      if (!filename) return null;

      // Check if it's already a full URL
      if (filename.startsWith("http://") || filename.startsWith("https://")) {
        return filename;
      }

      if (imageType === "companyLogo") {
        return `${Config.publicImageS3Path}hrapp_upload/${this.orgCode}_tmp/logos/${filename}`;
      } else if (imageType === "favicon") {
        return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/favicon/${filename}`;
      } else if (imageType === "bannerImage") {
        return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/banner/${filename}`;
      }

      return null;
    },

    async deleteExistingFile(fileName) {
      const fileUploadUrl = `hrapp_upload/${this.orgCode}_tmp/logos/${fileName}`;

      await this.$store
        .dispatch("deletes3File", {
          fileName: fileUploadUrl,
          type: "logo",
        })
        .catch((error) => {
          throw error;
        });
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const addedOn = this.convertUTCToLocal(this.originalData.Added_On),
        addedByName = this.originalData.Added_By,
        updatedByName = this.originalData.Updated_By,
        updatedOn = this.convertUTCToLocal(this.originalData.Updated_On);

      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>

<style scoped>
.general-settings-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .general-settings-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
