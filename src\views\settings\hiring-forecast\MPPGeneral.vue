<template>
  <div>
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabList"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>
  </div>
  <v-container fluid class="mpp-general-container">
    <v-window v-model="currentTabItem" v-if="formAccess">
      <v-window-item :value="currentTabItem">
        <div v-if="listLoading" class="mt-5">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <v-row v-else class="d-flex justify-center pa-5">
          <v-col cols="12" xs="12" sm="12" md="8" class="d-flex align-center">
            <div class="rounded-lg bg-white pa-6">
              <v-row>
                <v-col cols="12" class="d-flex align-center mb-4">
                  <div class="pl-3 text-h6 text-grey-darken-1 font-weight-bold">
                    This section allows you to manage workflow approval for
                    approved & forecasted positions. Adjust the settings as
                    needed.
                  </div>
                </v-col>
                <v-col
                  cols="12"
                  class="d-flex align-center py-6 flex-wrap d-flex"
                >
                  <div class="ma-4">
                    <v-switch
                      v-model="workflowApprovalEnabled"
                      label="Enable Workflow For Approved & Forecasted Position"
                      :false-value="false"
                      :true-value="true"
                      color="primary"
                      hide-details
                      @update:modelValue="onChangeFields"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>

              <v-col
                v-if="isFormDirty"
                cols="12"
                class="bg-white d-flex justify-end"
              >
                <v-btn
                  class="mt-2 mx-4"
                  color="primary"
                  variant="text"
                  elevation="4"
                  rounded="md"
                  @click="onCancel"
                  >Cancel</v-btn
                >
                <v-btn
                  class="mt-2 px-4"
                  color="primary"
                  rounded="md"
                  @click="updateWorkflow"
                  >Submit</v-btn
                >
              </v-col>
            </div>
          </v-col>
        </v-row>
      </v-window-item>
    </v-window>
    <AppAccessDenied v-else></AppAccessDenied>
  </v-container>
  <AppLoading v-if="isLoading"></AppLoading>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          class="mt-n5 primary"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>
<script>
import {
  UPDATE_WORKFLOW_APPROVAL,
  GET_WORKFLOW_APPROVAL_ENABLED,
} from "@/graphql/mpp/manPowerPlanningQueries";
export default {
  name: "MPPGeneral",
  components: {},
  data() {
    return {
      workflowApprovalEnabled: true,
      showValidationAlert: false,
      validationMessages: [],
      isLoading: false,
      isWorkflowEnabled: null,
      listLoading: false,
      isFormDirty: false,
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("349");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    hiringForecastFormAccess() {
      let individualFormAccess = this.accessRights("285");
      if (
        individualFormAccess &&
        individualFormAccess.accessRights &&
        individualFormAccess.accessRights["view"]
      ) {
        return individualFormAccess.accessRights;
      } else {
        return false;
      }
    },
    mainTabList() {
      return this.hiringForecastFormAccess && this.hiringForecastFormAccess.view
        ? ["Hiring Forecast", "General"]
        : ["General"];
    },
    currentTabItem() {
      let index = this.mainTabList.indexOf("General");
      return "tab-" + index;
    },
  },
  watch: {},
  mounted() {
    this.getWorkflowApproval();
  },
  methods: {
    onChangeFields() {
      this.isFormDirty = true;
    },
    onTabChange(tabName) {
      if (tabName.toLowerCase() === "hiring forecast") {
        this.$router.push("/man-power-planning/settings");
      }
    },
    onCancel() {
      this.isFormDirty = false;
      this.workflowApprovalEnabled =
        this.isWorkflowEnabled?.toLowerCase() === "yes" ? true : false;
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async getWorkflowApproval() {
      let vm = this;
      vm.listLoading = true;
      await vm.$apollo
        .query({
          query: GET_WORKFLOW_APPROVAL_ENABLED,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            formId: 349,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveWorkflowApprovalSetting &&
            response.data.retrieveWorkflowApprovalSetting.enableWorkflowApproval
          ) {
            this.isWorkflowEnabled =
              response.data.retrieveWorkflowApprovalSetting.enableWorkflowApproval;
            this.workflowApprovalEnabled =
              this.isWorkflowEnabled.toLowerCase() === "yes" ? true : false;
          }
          vm.listLoading = false;
        })
        .catch(() => {
          vm.handleGetWorkflowApprovalError(err);
        });
    },
    handleGetWorkflowApprovalError(err = "") {
      this.listLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "workflow approval enable details",
        isListError: false,
      });
    },
    updateWorkflow() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_WORKFLOW_APPROVAL,
          variables: {
            enableWorkflowApproval: this.workflowApprovalEnabled ? "Yes" : "No",
            formId: 349,
          },
          client: "apolloClientAH",
        })
        .then((res) => {
          if (res && res.data && res.data.updateWorkflowApprovalSetting) {
            const { errorCode } = res.data.updateWorkflowApprovalSetting;
            if (!errorCode) {
              let snackbarData = {
                isOpen: true,
                message: `Workflow approval ${
                  vm.workflowApprovalEnabled ? "enabled" : "disabled"
                } successfully`,
                type: "success",
              };
              vm.showAlert(snackbarData);
              vm.isLoading = false;
              vm.$emit("refetch-list");
            } else {
              vm.handleUpdateError();
            }
            vm.isFormDirty = false;
          } else {
            vm.handleUpdateError();
          }
        })
        .catch((err) => {
          vm.handleUpdateError(err);
        });
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.isFormDirty = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: this.workflowApprovalEnabled ? "enabling" : "disabling",
        form: "workflow approval",
        isListError: false,
      });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
  },
};
</script>
<style scoped>
.mpp-general-container {
  padding: 5em 2em 0em 3em;
}

@media screen and (max-width: 805px) {
  .mpp-general-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
