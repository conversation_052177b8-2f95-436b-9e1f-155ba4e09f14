<template>
  <div>
    <v-card class="ma-5 pa-4">
      <v-icon
        color="primary"
        class="font-weight-bold ml-auto mb-4"
        :size="20"
        @click="this.$emit('close-view-job-post-details', 'back')"
        >fas fa-arrow-left
      </v-icon>
      <v-skeleton-loader
        v-if="integrationStatusLoading"
        ref="skeleton1"
        type="heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <v-tabs v-else v-model="tab" bg-color="white">
        <v-tab value="one">Jobpost Details</v-tab>
        <v-tab v-if="isRecruiter" value="two">Hiring Team</v-tab>
        <v-tab v-if="showJobRoundsTabVisibility" value="three">Rounds</v-tab>
        <v-tab v-if="publishAccess" value="four">Publish</v-tab>
      </v-tabs>

      <v-card-text>
        <v-window v-model="tab">
          <v-window-item value="one">
            <v-div v-if="isJobPostDetailsLoading" class="mt-3">
              <div v-for="i in 4" :key="i" class="mt-4">
                <v-skeleton-loader
                  ref="skeleton2"
                  type="list-item"
                  class="mx-auto"
                ></v-skeleton-loader>
              </div>
            </v-div>
            <v-row v-else class="mt-1">
              <!-- header 1 Whats the job you are hiring for? -->
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[155].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.jobTitle) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[228].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[228].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.priority) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[202].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[202].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.experienceLevel) }}
                </p>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Status</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.jobpostStatus) }}
                </p>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Posting Date</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(formatDate(jobPostDetails.postingDate)) }}
                </p>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Closing Date</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(formatDate(jobPostDetails.closingDate)) }}
                </p>
              </v-col>
              <v-col
                v-if="
                  labelList[211].Field_Visiblity == 'Yes' &&
                  jobPostDetails.expectedJoiningDate != '1970/01/01'
                "
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[211].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(
                      formatDate(jobPostDetails.expectedJoiningDate)
                    )
                  }}
                </p>
              </v-col>
              <v-col v-if="fieldForce" cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ getCustomFieldName(115, "Service Provider") }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.serviceProviderName) }}
                </p>
              </v-col>
              <v-col v-if="fieldForce" cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[154].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.organizationGroup) }}
                </p>
              </v-col>
              <v-col
                v-if="jobPostDetails.customGroupName"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Custom Group</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.customGroupName) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[254].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[254].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.groupName) }}
                </p>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Division/Department/Section
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.functionalArea) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[260].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[260].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.designation) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[261].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[261].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.paymentType) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[257].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[257].Field_Alias }}
                </p>
                <span class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.locations) }}
                </span>
              </v-col>
              <v-col
                v-if="labelList[253].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[253].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.cityName) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[252].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[252].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.stateName) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[204].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[204].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.jobLocationType) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[203].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[203].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.workPlaceType) }}
                </p>
              </v-col>
              <!-- <v-col v-if="jobPostDetails.address" cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Company Address</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(jobPostDetails.address) }}
              </p>
            </v-col> -->
              <v-col
                v-if="
                  labelList[258] && labelList[258].Field_Visiblity === 'Yes'
                "
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[258].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.jobType) }}
                </p>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">
                  No of Vacancies
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.noOfVacancies) }}
                </p>
              </v-col>
              <v-col
                v-if="jobPostDetails.noOfMaleVacancies"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  No of Male Vacancies
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.noOfMaleVacancies) }}
                </p>
              </v-col>
              <v-col
                v-if="jobPostDetails.noOfFemaleVacancies"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  No of Female Vacancies
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.noOfFemaleVacancies) }}
                </p>
              </v-col>
              <v-col cols="12">
                <p class="text-subtitle-1 text-grey-darken-1 pb-2">
                  Job Description
                </p>
                <div>
                  <div ref="editorView" class="quill-editorView"></div>
                </div>
              </v-col>

              <!-- header 2 - Job Requirements -->
              <v-col cols="12">
                <div
                  class="d-flex align-center text-body-1 text-grey-darken-1 font-weight-bold"
                >
                  <v-progress-circular
                    model-value="100"
                    color="primary"
                    :size="16"
                    class="mr-1"
                  ></v-progress-circular>
                  Job Requirements
                </div>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Key Skills</p>
                <span class="text-subtitle-1 font-weight-regular">
                  {{ splitSkills(jobPostDetails?.keySkillSet) }}
                </span>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">
                  Experience Range
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ jobPostDetails.minExperienceRange }} -
                  {{ jobPostDetails.maxExperience }} years(s)
                </p>
              </v-col>
              <v-col
                v-if="labelList[288].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[288].Field_Alias }}
                </p>
                <p
                  v-if="jobPostDetails.expectedQualification"
                  class="text-subtitle-1 font-weight-regular"
                >
                  <span class="text-subtitle-1 font-weight-regular">
                    {{ jobPostDetails.expectedQualification }}
                  </span>
                </p>
                <p v-else class="text-subtitle-1 font-weight-regular">
                  <span class="text-subtitle-1 font-weight-regular"> - </span>
                </p>
              </v-col>
              <!-- <v-col cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">
                Expected Work Permits
              </p>
              <span class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(jobPostDetails.workPermits) }}
              </span>
            </v-col> -->
              <!-- <v-col
              v-if="jobPostDetails.otherWorkPermits"
              cols="12"
              md="4"
              sm="6"
            >
              <p class="text-subtitle-1 text-grey-darken-1">
                Other Work Permits
              </p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(jobPostDetails.otherWorkPermits) }}
              </p>
            </v-col> -->
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Rounds</p>
                <span class="text-subtitle-1 font-weight-regular">
                  {{ jobPostDetails.rounds }}
                </span>
              </v-col>
              <v-col
                v-if="
                  labelList[327] &&
                  labelList[327].Field_Visiblity?.toLowerCase() === 'yes'
                "
                cols="12"
                sm="6"
                md="4"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList["327"].Field_Alias }}
                </p>

                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.requiredCertification) }}
                </p>
              </v-col>

              <!-- header 3 -  Benefits & Experience -->
              <v-col cols="12">
                <div
                  class="d-flex align-center text-body-1 text-grey-darken-1 font-weight-bold"
                >
                  <v-progress-circular
                    model-value="100"
                    color="light-blue-darken-1"
                    :size="16"
                    class="mr-1"
                  ></v-progress-circular>
                  Benefits & Experience
                </div></v-col
              >
              <v-col
                v-if="labelList[289].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[289].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.currency) }}
                </p>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Salary Range</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ jobPostDetails.minSalary }} -
                  {{ jobPostDetails.maxSalary }}
                </p>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Pay Type</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.payType) }}
                </p>
              </v-col>

              <!-- header 4 - Replacement Details -->
              <v-col cols="12">
                <div
                  class="d-flex align-center text-body-1 text-grey-darken-1 font-weight-bold"
                >
                  <v-progress-circular
                    model-value="100"
                    color="green-darken-2"
                    :size="16"
                    class="mr-1"
                  ></v-progress-circular>
                  Replacement Details
                </div></v-col
              >
              <v-col
                v-if="jobPostDetails.coolingPeriod"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">Cooling Period</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.coolingPeriod) }}
                  month(s)
                </p>
              </v-col>
              <!-- <v-col v-if="jobPostDetails.agencyInvolved" cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Agency Involved</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(jobPostDetails.agencyInvolved) }}
              </p>
            </v-col> -->
              <!-- <v-col v-if="jobPostDetails.jobDuration" cols="12" md="4" sm="6">
              <p class="text-subtitle-1 text-grey-darken-1">Job Duration</p>
              <p class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(jobPostDetails.jobDuration) }}
                months
              </p>
            </v-col> -->
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Field Work</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ jobPostDetails.isTravelRequired == 1 ? "Yes" : "No" }}
                </p>
              </v-col>
              <v-col
                v-if="
                  jobPostDetails.client &&
                  labelList[278].Field_Visiblity == 'Yes'
                "
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[278].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.client) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[291].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[291].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.hiringManager) }}
                </p>
              </v-col>

              <v-col
                v-if="labelList[256].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[256].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.replacementFor) }}
                </p>
              </v-col>
              <v-col v-if="jobPostDetails.industry" cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Industry</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.industry) }}
                </p>
              </v-col>
              <v-col v-if="jobPostDetails.category" cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Category</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.category) }}
                </p>
              </v-col>
              <v-col v-if="jobPostDetails.subcategory" cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Subcategory</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.subcategory) }}
                </p>
              </v-col>
              <v-col
                v-if="labelList[259].Field_Visiblity == 'Yes'"
                cols="12"
                md="4"
                sm="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ labelList[259].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.reasonForOpening) }}
                </p>
              </v-col>

              <!-- header 5 - others -->
              <v-col cols="12">
                <div
                  class="d-flex align-center text-body-1 text-grey-darken-1 font-weight-bold"
                >
                  <v-progress-circular
                    model-value="100"
                    color="blue-darken-2"
                    :size="16"
                    class="mr-1"
                  ></v-progress-circular>
                  Others
                </div>
              </v-col>
              <v-col cols="12" md="4" sm="6">
                <p class="text-subtitle-1 text-grey-darken-1">Workflow</p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(jobPostDetails.workflow) }}
                </p>
              </v-col>
            </v-row>
            <!-- header 6 - Custom Fields -->
            <div class="pr-2">
              <JobCandidatesCustomFields
                custom-form-name="Job Posts"
                :form-id="15"
                :primary-id="jobPostId || 0"
                :show-view-form="true"
                :show-edit-form="false"
                :pass-preview-value="jobPostData?.customFieldInputs || ''"
              />
            </div>
            <v-row
              v-if="moreDetailsList.length > 0"
              class="px-sm-8 px-md-10 mt-2 mb-2"
            >
              <v-col cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                /> </v-col
            ></v-row>
          </v-window-item>
          <v-window-item value="two">
            <HiringTeam
              :jobPostId="jobPostId"
              :serviceProviderId="serviceProviderId"
              :customGroupId="customGroupId"
            ></HiringTeam
          ></v-window-item>
          <v-window-item value="three">
            <ViewRounds
              :jobPostId="jobPostId"
              :jobPostData="jobPostData"
              :hasUpdateAccess="hasUpdateAccess"
              :showJobRoundsTab="showJobRoundsTab"
            ></ViewRounds>
          </v-window-item>
          <v-window-item value="four">
            <publishJobPostCards
              :jobPostId="jobPostId"
              :jobPostData="jobPostData"
              :cityList="cityList"
              :cityListLoading="cityListLoading"
              :stateList="stateList"
            ></publishJobPostCards>
          </v-window-item>
        </v-window>
      </v-card-text>
    </v-card>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
</template>

<script>
//components
import moment from "moment";
import {
  checkNullValue,
  getCustomFieldName,
  convertUTCToLocal,
} from "@/helper";
import { defineAsyncComponent } from "vue";
// import DOMPurify from "dompurify";
const publishJobPostCards = defineAsyncComponent(() =>
  import("./PublishJobPost.vue")
);
import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
const HiringTeam = defineAsyncComponent(() =>
  import("./recruitment/hiring-team/HiringTeam.vue")
);
import { LIST_CITIES_NO_AUTH } from "@/graphql/dropDownQueries.js";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
const ViewRounds = defineAsyncComponent(() =>
  import("./recruitment/view-rounds/ViewRounds.vue")
);
import JobCandidatesCustomFields from "@/views/recruitment/job-candidates/job-candidates-details/custom-fields/JobCandidatesCustomFields.vue";

export default {
  name: "JobPostViewPage",
  components: {
    MoreDetails,
    publishJobPostCards,
    HiringTeam,
    ViewRounds,
    JobCandidatesCustomFields,
  },
  data() {
    return {
      givenJobPostIndex: null,
      isJobPostDetailsLoading: true,
      wholeJobPostData: null,
      mytoken: "",
      showValidationAlert: false,
      validationMessages: [],
      getIntegrationStatus: [],
      managerList: [],
      linkedInStatus: null,
      indeedStatus: null,
      jobStreetStatus: null,
      irukkaStatus: null,
      publishAccess: false,
      integrationStatusLoading: false,
      jobPostDetails: {
        jobTitle: "",
        paymentType: "",
        serviceProviderName: "",
        customGroupName: "",
        experienceLevel: "",
        organizationGroup: "",
        workPlaceType: "",
        functionalArea: "",
        currency: "",
        designation: "",
        minSalary: "",
        maxSalary: "",
        jobType: "",
        expectedQualification: null,
        jobDuration: "",
        keySkillSet: "",
        minExperienceRange: "",
        maxExperience: "",
        rounds: "",
        expectedJoiningDate: null,
        locations: "",
        address: "",
        coolingPeriod: "",
        isTravelRequired: null,
        noOfVacancies: null,
        noOfMaleVacancies: null,
        noOfFemaleVacancies: null,
        priority: "",
        agencyInvolved: "",
        workPermits: "",
        postingDate: null,
        jobpostStatus: "",
        otherWorkPermits: "",
        closingDate: null,
        jobDescription: "",
        category: "",
        subcategory: "",
        industry: "",
        reasonForOpening: "",
        workflow: "",
        client: "",
        hiringManager: "",
        hiringManagerId: "",
        replacementFor: null,
        serviceProviderId: null,
        customGroupId: null,
        addedBy: "",
        addedOn: "",
        updatedBy: "",
        payType: "",
      },
      tab: "one",
      moreDetailsList: [],
      cityListLoading: false,
      cityList: [],
      stateList: [],
    };
  },
  props: {
    jobPostDetailsLoading: {
      type: Boolean,
      default: false,
    },
    jobPostId: {
      type: Number,
      required: true,
    },
    jobPostIdQueryParam: {
      type: Number,
      required: false,
    },
    jobPostData: {
      type: Object,
      required: true,
    },
    showJobRoundsTab: {
      type: String,
      default: "Yes",
    },
  },
  computed: {
    fieldForce() {
      const { fieldForce } = this.$store.state.orgDetails;
      return fieldForce;
    },
    formatDate() {
      return (date) => {
        if (date) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    // to check the device is mobile based on window size
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    formAccess() {
      let formAccess = this.accessRights("15");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    isRecruiter() {
      return this.$store.state.isRecruiter;
    },
    showJobRoundsTabVisibility() {
      return this.showJobRoundsTab?.toLowerCase() === "yes";
    },
    hasUpdateAccess() {
      return this.isRecruiter && this.formAccess?.update;
    },
  },
  watch: {
    jobPostData(newVal) {
      if (newVal) {
        this.prefillJobPostDetails();
        this.fetchIntegrationStatus();
      }
    },
    jobPostDetailsLoading(newVal) {
      this.isJobPostDetailsLoading = newVal;
      this.$nextTick(() => {
        this.initQuillEditor();
      });
    },
  },

  mounted() {
    this.integrationStatusLoading = true;
    this.fetchDropdownData();
    this.loadAuthToken();
    this.retrieveCities();
    if (this.jobPostId) {
      this.givenJobPostIndex = this.jobPostId;
    }
  },

  methods: {
    checkNullValue,
    getCustomFieldName,
    convertUTCToLocal,
    prefillMoreDetails() {
      // to form more details array based on this values
      let addedDateLocal = convertUTCToLocal(this.wholeJobPostData.Added_On);
      let updateDateLocal = convertUTCToLocal(this.wholeJobPostData.Updated_On);
      const addedOn = addedDateLocal,
        addedByName = this.wholeJobPostData.addedByName,
        updatedByName = this.wholeJobPostData.updatedByName,
        updatedOn = updateDateLocal;
      if (addedOn && addedByName) {
        this.moreDetailsList.push({
          actionDate: addedOn,
          actionBy: addedByName,
          text: "Added",
        });
      }
      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
    prefillJobPostDetails() {
      if (this.jobPostData) {
        this.wholeJobPostData = this.jobPostData;
        this.serviceProviderId = this.wholeJobPostData.Service_Provider_Id;
        this.customGroupId = this.wholeJobPostData.customGroupId;
        this.jobPostDetails.jobTitle = this.wholeJobPostData.Job_Post_Name;
        this.jobPostDetails.jobTitle = this.wholeJobPostData.Job_Post_Name;
        this.jobPostDetails.paymentType = this.wholeJobPostData.Payment_Type;
        this.jobPostDetails.functionalArea =
          this.wholeJobPostData.Functional_Area;
        this.jobPostDetails.serviceProviderName =
          this.wholeJobPostData.Service_Provider_Name;
        this.jobPostDetails.customGroupName =
          this.wholeJobPostData.CustomGroupName;
        this.jobPostDetails.experienceLevel =
          this.wholeJobPostData.Experience_Level;
        this.jobPostDetails.organizationGroup =
          this.wholeJobPostData.Organization_Group;
        this.jobPostDetails.workPlaceType = this.wholeJobPostData.workPlaceType;
        this.jobPostDetails.currency = this.wholeJobPostData.Currency;
        this.jobPostDetails.designation = this.wholeJobPostData.Designation;
        this.jobPostDetails.minSalary =
          this.wholeJobPostData.Min_Payment_Frequency;
        this.jobPostDetails.maxSalary =
          this.wholeJobPostData.Max_Payment_Frequency;
        this.jobPostDetails.jobType = this.wholeJobPostData.Job_Type;
        this.jobPostDetails.expectedQualification =
          this.wholeJobPostData.Qualification.map((item) => {
            return item.Qualification;
          });
        this.jobPostDetails.jobLocationType =
          this.wholeJobPostData?.jobLocationType;
        this.jobPostDetails.requiredCertification =
          this.wholeJobPostData?.Required_Certification;
        this.jobPostDetails.expectedQualification =
          this.jobPostDetails.expectedQualification.join(", ");
        this.jobPostDetails.jobDuration = this.wholeJobPostData.Job_Duration;
        this.jobPostDetails.keySkillSet = this.wholeJobPostData.Skill_Set;
        this.jobPostDetails.minExperienceRange =
          this.wholeJobPostData.Min_Work_Experience;
        this.jobPostDetails.maxExperience =
          this.wholeJobPostData.Max_Work_Experience;
        this.jobPostDetails.rounds = this.wholeJobPostData.Rounds.map(
          (item) => {
            return item.Round_Name;
          }
        );
        this.jobPostDetails.rounds = this.jobPostDetails.rounds.join(", ");
        this.jobPostDetails.expectedJoiningDate =
          this.wholeJobPostData.Expected_Joining_Date;
        this.jobPostDetails.locations = this.wholeJobPostData.JobLocations.map(
          (item) => {
            return item.Location_Name;
          }
        );
        this.jobPostDetails.locations =
          this.jobPostDetails.locations.join(", ");
        this.jobPostDetails.address = this.wholeJobPostData.Address;
        this.jobPostDetails.coolingPeriod =
          this.wholeJobPostData.Cooling_Period;
        this.jobPostDetails.isTravelRequired =
          this.wholeJobPostData.Travel_Required;
        this.jobPostDetails.noOfVacancies =
          this.wholeJobPostData.No_Of_Vacancies;
        this.jobPostDetails.noOfMaleVacancies =
          this.wholeJobPostData.No_Of_Male_Vacancies;
        this.jobPostDetails.noOfFemaleVacancies =
          this.wholeJobPostData.No_Of_Female_Vacancies;
        this.jobPostDetails.priority = this.wholeJobPostData.Priority;
        this.jobPostDetails.agencyInvolved =
          this.wholeJobPostData.Agency_Involved;
        this.jobPostDetails.workPermits =
          this.wholeJobPostData.WorkAuthorization.map((item) => {
            return item.Work_Authorization_Name;
          });
        this.jobPostDetails.workPermits =
          this.jobPostDetails.workPermits.join(", ");
        this.jobPostDetails.postingDate = this.wholeJobPostData.Posting_Date;
        this.jobPostDetails.jobpostStatus =
          this.wholeJobPostData.Job_Post_Status;
        this.jobPostDetails.otherWorkPermits =
          this.wholeJobPostData.OtherWorkAuthorization;
        this.jobPostDetails.closingDate = this.wholeJobPostData.Closing_Date;
        this.jobPostDetails.category = this.wholeJobPostData.Category_Name;
        this.jobPostDetails.subcategory =
          this.wholeJobPostData.Subcategory_Name;
        this.jobPostDetails.industry = this.wholeJobPostData.Industry_Name;
        this.jobPostDetails.reasonForOpening =
          this.wholeJobPostData.Reason_For_Opening == "Others"
            ? this.wholeJobPostData.Other_Reason_For_Opening
            : this.wholeJobPostData.Reason_For_Opening;
        this.jobPostDetails.workflow = this.wholeJobPostData.Workflow_Name;
        this.jobPostDetails.client = this.wholeJobPostData.Client_Name;
        this.jobPostDetails.addedOn = this.wholeJobPostData.Added_On;
        this.jobPostDetails.addedBy = this.wholeJobPostData.addedByName;
        this.jobPostDetails.updatedBy = this.wholeJobPostData.updatedByName;
        this.jobPostDetails.cityName = this.wholeJobPostData.City_Name;
        this.jobPostDetails.stateName = this.wholeJobPostData.State_Name;
        this.jobPostDetails.groupName = this.wholeJobPostData.Pos_Name;
        this.jobPostDetails.payType = this.wholeJobPostData.Pay_Type;
        this.jobPostDetails.replacementFor =
          this.wholeJobPostData.ReplacementFor.map((item) => {
            return item.Employee_Name;
          });
        this.jobPostDetails.replacementFor =
          this.jobPostDetails.replacementFor.join(", ");
        this.jobPostDetails.hiringManagerId =
          this.wholeJobPostData.Hiring_Manager_Id;
        if (this.jobPostDetails.hiringManagerId) {
          this.jobPostDetails.hiringManager = this.managerList.filter(
            (item) => {
              if (item.Manager_Id == this.jobPostDetails.hiringManagerId) {
                return item.Manager_Name;
              }
            }
          );
        } else {
          this.jobPostDetails.hiringManager =
            this.wholeJobPostData.Hiring_Manager_Name;
        }
        this.prefillMoreDetails();
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    loadAuthToken() {
      this.authCode = this.$route.query.code;
      if (this.jobPostIdQueryParam && this.authCode) {
        this.tab = "four";
      }
      this.mytoken = window.$cookies.get("irukkaAuthToken");
    },
    formLanguagesNames(languages) {
      let langNames = [];
      for (var i = 0; i < languages.length; i++) {
        langNames.push(languages[i].Language_Name);
      }
      return langNames.join(", ");
    },
    async fetchDropdownData() {
      this.dropdownListLoading = true;
      await this.$store
        .dispatch("getDefaultDropdownList", { formId: 15 })
        .then((res) => {
          if (
            res.data &&
            res.data.getDropDownBoxDetails &&
            !res.data.getDropDownBoxDetails.errorCode
          ) {
            const { managers } = res.data.getDropDownBoxDetails;
            this.managerList = managers;
          }
          this.dropdownListLoading = false;
        })
        .catch(() => {
          this.dropdownListLoading = false;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    async fetchIntegrationStatus() {
      let vm = this;
      vm.integrationStatusLoading = true;
      await vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 15,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length > 0
          ) {
            this.getIntegrationStatus =
              response.data.jobBoardIntegrationStatus.getStatus;
            this.getAllIntegrationStatus();
            this.publishAccess = this.shouldShowPublishTab();
          }
          this.integrationStatusLoading = false;
        })
        .catch((err) => {
          this.integrationStatusLoading = false;
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "integration status",
        isListError: false,
      });
    },
    getStatusByType(integrationType) {
      // Find the object with the specified Integration_Type
      const integration = this.getIntegrationStatus.find(
        (item) =>
          item.Integration_Type.toLowerCase() === integrationType.toLowerCase()
      );

      // If the integration is found, return its Integration_Status
      if (integration) {
        return integration.Integration_Status;
      } else {
        return null;
      }
    },
    getAllIntegrationStatus() {
      this.linkedInStatus = this.getStatusByType("linkedin");
      this.jobStreetStatus = this.getStatusByType("seek");
      this.indeedStatus = this.getStatusByType("indeed");
      this.irukkaStatus = this.getStatusByType("irukka");
    },
    shouldShowPublishTab() {
      const { getIntegrationStatus, jobPostData } = this;
      return (
        getIntegrationStatus.length > 0 &&
        (this.linkedInStatus == "Active" ||
          this.indeedStatus == "Active" ||
          this.jobStreetStatus == "Active") &&
        ![1, 6].includes(jobPostData?.Status_Id) &&
        (this.formAccess.update || this.formAccess.add) &&
        this.isRecruiter
      );
    },
    splitSkills(skills) {
      if (skills?.length) return skills.join(", ");
      else return "-";
    },
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editorView, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.quill.root.innerHTML = this.wholeJobPostData.Job_Description
        ? this.convertEmojiCodepointsToEmojis(
            this.wholeJobPostData.Job_Description
          )
        : "";
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
      });
      this.quill.enable(false);
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editorView.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    convertEmojiCodepointsToEmojis(text) {
      return text.replace(/\[EMOJI:([0-9a-f-]+)\]/gi, (match, codePoints) => {
        // Split by dash if there are multiple code points
        const codePointArray = codePoints.split("-");

        // Convert each hex code point back to a character and join them
        const emoji = codePointArray
          .map((hex) => String.fromCodePoint(parseInt(hex, 16)))
          .join("");

        return emoji;
      });
    },
    retrieveCities() {
      let vm = this;
      vm.cityListLoading = true;
      vm.$apollo
        .query({
          query: LIST_CITIES_NO_AUTH,
          client: "apolloClientAS",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCityListWithState &&
            !response.data.getCityListWithState.errorCode
          ) {
            const { cityDetails } = response.data.getCityListWithState;
            vm.cityList = cityDetails;
            // Create an array to store unique states
            let uniqueStates = [];

            // Iterate through each city object
            cityDetails.forEach((city) => {
              let stateId = city.State_Id;
              let stateName = city.State_Name;

              // Check if the state is already in the uniqueStates array
              let exists = uniqueStates.some(
                (state) => state.State_Id === stateId
              );

              // If not, add it to the array
              if (!exists) {
                uniqueStates.push({
                  State_Id: stateId,
                  State_Name: stateName,
                });
              }
            });
            vm.stateList = uniqueStates;
          }
          vm.cityListLoading = false;
        })
        .catch(() => {
          vm.cityListLoading = false;
        });
    },
  },
};
</script>
<style scoped>
.job-description-content {
  font-size: 16px; /* Adjust the font size as needed */
}
/* Ensure list styles are visible */
.job-description-content ol,
.job-description-content ul {
  list-style: initial !important ; /* Reset list styles to default */
}

/* Ensure list items are properly displayed */
.job-description-content li {
  margin-bottom: 5px; /* Add some spacing between list items */
}
.quill-editorView {
  height: auto;
}
::v-deep .ql-toolbar.ql-snow {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
::v-deep .ql-editor {
  padding: 0px;
}
</style>
