<template>
  <div class="text-center">
    <v-overlay
      :model-value="overlay"
      @click:outside="onCloseOverlay()"
      persistent
      class="d-flex justify-end"
    >
      <template v-slot:default="{}">
        <v-card
          class="overlay-card"
          :style="{
            height: windowHeight + 'px',
            width: windowWidth <= 770 ? '100vw' : '50vw',
          }"
        >
          <v-card-title
            class="d-flex bg-primary justify-space-between align-center overlay-head"
          >
            <div class="text-h6 text-medium ps-2 truncate">
              Publish Job Title: <span>{{ selectedPosition }}</span>
            </div>
            <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
              <v-icon>fas fa-times</v-icon>
            </v-btn>
          </v-card-title>

          <v-card-text
            class="card mb-3 px-0"
            style="overflow-y: auto; padding-top: 7vh"
          >
            <v-tabs class="text-bold" v-model="tab">
              <v-tab
                v-for="(tabItem, index) in profileTabs"
                :key="tabItem.value"
                :value="index"
                @click="onChangeProfileTabs(index)"
                :disabled="isActiveTab(tabItem) ? false : true"
              >
                <div
                  :class="[
                    isActiveTab(tabItem)
                      ? 'text-primary font-weight-bold'
                      : 'text-grey font-weight-bold',
                  ]"
                >
                  {{ tabItem.label }}
                  <div v-if="isActiveTab(tabItem)" class="mt-3 mb-n4"></div>
                </div>
              </v-tab>
            </v-tabs>

            <v-tabs-items v-model="tab">
              <!-- Settings Tab -->
              <v-tab-item v-if="activeTab === 'Job_Info'">
                <v-card flat>
                  <div>
                    <v-form
                      ref="jobInfoRefForm"
                      :style="`max-height: calc(100vh - 150px);
                        overflow-y: ${selectedHirer ? 'scroll' : 'hidden'}`"
                    >
                      <v-row class="mx-2 pt-5">
                        <v-col cols="12" md="6" sm="12">
                          <CustomSelect
                            v-model="selectedHirer"
                            :isLoading="hirerListLoading"
                            :items="hirerList"
                            ref="selectedHirerName"
                            label="Hirer Name"
                            item-title="Company_Name"
                            item-value="Hirer_ID"
                            :isAutoComplete="true"
                            :rules="[required('Hirer Name', selectedHirer)]"
                            clearable
                            :disabled="this.editJobData.length ? true : false"
                            variant="solo"
                            :isRequired="true"
                            :itemSelected="selectedHirer"
                            @selected-item="onChangeHirer($event)"
                          ></CustomSelect>
                        </v-col>
                        <v-col cols="12" md="6" sm="12" v-if="selectedHirer">
                          <CustomSelect
                            v-model="selectedRecruiterName"
                            :isLoading="recruiterListLoading"
                            :items="recruiterNameList"
                            ref="selectedRecruiterName"
                            label="Recruiter Name"
                            item-title="Emp_Name"
                            item-value="Emp_Name"
                            :isAutoComplete="true"
                            clearable
                            variant="solo"
                            :rules="
                              recruiterNameList && recruiterNameList.length
                                ? [
                                    required(
                                      'Recruiter Name',
                                      selectedRecruiterName
                                    ),
                                    validateWithRulesAndReturnMessages(
                                      selectedRecruiterName,
                                      'recruiterName',
                                      'Recruiter Name'
                                    ),
                                  ]
                                : [true]
                            "
                            :isRequired="
                              recruiterNameList && recruiterNameList.length
                                ? true
                                : false
                            "
                            :itemSelected="selectedRecruiterName"
                            @selected-item="selectedRecruiterName = $event"
                          ></CustomSelect>
                        </v-col>
                        <v-col
                          cols="12"
                          md="6"
                          sm="12"
                          v-if="selectedHirer && isValidField"
                        >
                          <div
                            class="mt-n4"
                            :class="
                              mobileNumberValidation ? '' : 'custom-label'
                            "
                            :style="
                              mobileNumberValidation
                                ? 'color: rgb(var(--v-theme-error)); font-size: 12px'
                                : ''
                            "
                          >
                            Phone Number
                          </div>
                          <VueTelInput
                            class="pa-2"
                            v-model="selectedPhoneNo"
                            :error="!mobileNumberValidation"
                            error-color="#E53935"
                            valid-color="#9E9E9E"
                            :defaultCountry="mobileNoCountryCode"
                            :autoDefaultCountry="false"
                            mode="national"
                            @country-changed="getCountryCode($event)"
                            @validate="validateMobileNumber"
                            :valid-characters-only="true"
                            ref="mobileNo"
                          ></VueTelInput>
                          <span
                            :class="
                              mobileNumberValidation
                                ? 'text-red caption mt-1'
                                : 'text-green caption mt-1'
                            "
                            >{{ mobileNumberValidation }}</span
                          >
                          <!-- <v-text-field
                            v-model="selectedPhoneNo"
                            ref="selectedPhoneNo"
                            :max="15"
                            :rules="[
                              required('Phone Number', selectedPhoneNo),
                              numericValidation(
                                'Phone Number',
                                selectedPhoneNo
                              ),
                              minLengthValidation(
                                'Phone Number',
                                selectedPhoneNo,
                                7
                              ),
                              maxLengthValidation(
                                'Phone Number',
                                selectedPhoneNo,
                                15
                              ),
                            ]"
                            variant="solo"
                          >
                            <template v-slot:label>
                              Phone Number
                              <span style="color: red">*</span>
                            </template>
                          </v-text-field> -->
                        </v-col>
                        <v-col cols="12" md="6" sm="12" v-if="selectedHirer">
                          <v-text-field
                            v-model="selectedEmail"
                            ref="selectedEmail"
                            :rules="[
                              required('Email', selectedEmail),
                              emailValidation('Email', selectedEmail),
                            ]"
                            :isRequired="true"
                            variant="solo"
                            ><template v-slot:label>
                              Email
                              <span style="color: red">*</span>
                            </template>
                          </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" sm="12" v-if="selectedHirer">
                          <v-text-field
                            v-model="selectedPosition"
                            ref="positionTitle"
                            :rules="[
                              required('Position Title', selectedPosition),
                              validateWithRulesAndReturnMessages(
                                selectedPosition,
                                'jobTitle',
                                'Position Title'
                              ),
                            ]"
                            :isRequired="true"
                            variant="solo"
                            ><template v-slot:label>
                              Position Title
                              <span style="color: red">*</span>
                            </template>
                          </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" sm="12" v-if="selectedHirer">
                          <CustomSelect
                            v-model="selectedRole"
                            ref="selectedRole"
                            :items="roleList"
                            :isAutoComplete="true"
                            clearable
                            variant="solo"
                            item-title="name"
                            item-value="name"
                            label="Role"
                            :rules="[required('Role', selectedRole)]"
                            :isRequired="true"
                            :itemSelected="selectedRole"
                            @selected-item="selectedRole = $event"
                          ></CustomSelect>
                        </v-col>
                        <v-col cols="12" md="6" sm="12" v-if="selectedHirer">
                          <CustomSelect
                            :items="locationList"
                            label="Location"
                            :isRequired="true"
                            :rules="[required('Location', selectedLocation)]"
                            :isLoading="isLocationLoading"
                            :noDataText="noDataText"
                            :itemSelected="selectedLocation"
                            itemValue="location_Id"
                            itemTitle="location_Name"
                            placeholder="Start typing the location for listing"
                            :isAutoComplete="true"
                            :clearable="true"
                            ref="Location"
                            @selected-item="updateLocation($event)"
                            @update-search-value="debouncedSearch"
                          ></CustomSelect>
                        </v-col>
                        <v-col cols="12" md="6" sm="12" v-if="selectedHirer">
                          <CustomSelect
                            v-model="selectedCategories"
                            :items="categoriesList"
                            item-title="cat_Name"
                            item-value="cat_Id"
                            ref="selectedCategories"
                            label="Category"
                            :isLoading="isCategoriesLoading"
                            :isAutoComplete="true"
                            :rules="[
                              selectedLocation
                                ? required('Category', selectedCategories)
                                : true,
                            ]"
                            clearable
                            variant="solo"
                            :itemSelected="selectedCategories"
                            :disabled="!selectedLocation"
                            :isRequired="selectedLocation ? true : false"
                            @selected-item="onCategorySelected($event)"
                          >
                          </CustomSelect>
                        </v-col>
                        <v-col cols="12" md="6" sm="12" v-if="selectedHirer">
                          <CustomSelect
                            v-model="selectedSubCategories"
                            :items="subCategoriesList"
                            item-title="subcat_Name"
                            item-value="subcat_Id"
                            ref="selectedSubCategories"
                            label="Sub Category"
                            :disabled="!selectedCategories || !selectedLocation"
                            :isLoading="isCategoriesLoading"
                            :isAutoComplete="true"
                            :isRequired="
                              selectedCategories || selectedLocation
                                ? true
                                : false
                            "
                            :rules="[
                              selectedCategories || selectedLocation
                                ? required(
                                    'Sub Category',
                                    selectedSubCategories
                                  )
                                : true,
                            ]"
                            clearable
                            variant="solo"
                            :itemSelected="selectedSubCategories"
                            @selected-item="selectedSubCategories = $event"
                          ></CustomSelect>
                        </v-col>
                        <v-col cols="12" md="6" sm="12" v-if="selectedHirer">
                          <CustomSelect
                            :items="dropdownSeekWorkArrangementCodes"
                            :isAutoComplete="true"
                            variant="solo"
                            label="Seek Work Arrangement Codes"
                            :disabled="
                              this.jobPostData?.workPlaceType ? true : false
                            "
                            :itemSelected="selectedSeekWorkArrangementCodes"
                            @selected-item="
                              selectedSeekWorkArrangementCodes = $event
                            "
                          ></CustomSelect>
                        </v-col>
                        <v-col cols="12" v-if="selectedHirer">
                          <v-textarea
                            v-model="selectedSearchSummary"
                            variant="solo"
                            ref="selectedSearchSummary"
                            auto-grow
                            :rules="[
                              required('Job Summary', selectedSearchSummary),
                              validateWithRulesAndReturnMessages(
                                selectedSearchSummary,
                                'searchSummarydescription',
                                'Job Summary'
                              ),
                            ]"
                          >
                            <template v-slot:label>
                              Job Summary : (Short description to be shown in
                              search results)
                              <span style="color: red">*</span>
                            </template>
                          </v-textarea>
                          <text
                            style="display: block"
                            class="d-flex justify-end"
                            >{{ selectedSearchSummary.length }}/150</text
                          >
                        </v-col>
                      </v-row>
                    </v-form>
                  </div>
                </v-card>
              </v-tab-item>

              <!-- Salary Information Tab -->
              <v-tab-item v-else-if="activeTab === 'Salary_Info'">
                <v-card flat>
                  <v-form ref="salaryInfoRefForm" v-if="selectedHirer">
                    <v-row class="mx-2 pt-5">
                      <v-col cols="12" md="6" sm="12">
                        <CustomSelect
                          v-model="selectedWorkType"
                          :items="workList"
                          ref="workType"
                          label="Work Type"
                          :isAutoComplete="true"
                          clearable
                          item-title="name"
                          item-value="name"
                          variant="solo"
                          :rules="[required('Work Type', selectedWorkType)]"
                          :isRequired="true"
                          :itemSelected="selectedWorkType"
                          @selected-item="selectedWorkType = $event"
                        ></CustomSelect>
                      </v-col>
                      <v-col cols="12" md="6" sm="12">
                        <CustomSelect
                          v-model="selectedPayType"
                          :items="PayTypeList"
                          ref="selectedPayType"
                          label="Pay Details"
                          :isAutoComplete="true"
                          clearable
                          item-title="PayType_Name"
                          item-value="PayType_Name"
                          variant="solo"
                          :disabled="selectedPayType ? true : false"
                          :rules="[required('Pay Details', selectedPayType)]"
                          :isRequired="true"
                          :itemSelected="selectedPayType"
                          @selected-item="selectedPayType = $event"
                        ></CustomSelect>
                      </v-col>
                      <v-col cols="12" md="6" sm="12" class="pt-10">
                        <CustomSelect
                          v-model="selectedCurrency"
                          :items="currencyList"
                          ref="selectedCurrency"
                          label="Currency"
                          :isAutoComplete="true"
                          clearable
                          item-title="code"
                          item-value="code"
                          variant="solo"
                          :isLoading="currencyLoader"
                          :rules="[required('Currency', selectedCurrency)]"
                          :isRequired="true"
                          :itemSelected="selectedCurrency"
                          @selected-item="selectedCurrency = $event"
                        ></CustomSelect>
                      </v-col>
                      <v-col cols="12" md="6" sm="12">
                        <p class="text-subtitle-1 text-grey-darken-1 ml-3">
                          Salary Range<span style="color: red">*</span>
                        </p>
                        <v-range-slider
                          v-model="range"
                          :max="99999999"
                          :min="1"
                          :step="1"
                          strict
                          thumb-size="15"
                          track-size="2"
                          color="orange"
                          hide-details
                          class="align-center"
                        >
                          <template v-slot:prepend>
                            <v-text-field
                              ref="salaryStart"
                              v-model="range[0]"
                              :model-value="findMinRangeSalary()"
                              hide-details
                              single-line
                              variant="solo"
                              style="width: 90px"
                              type="number"
                            ></v-text-field>
                          </template>
                          <template v-slot:append>
                            <v-text-field
                              ref="salaryEnd"
                              v-model="range[1]"
                              :model-value="findMaxRangeSalary()"
                              hide-details
                              single-line
                              variant="solo"
                              type="number"
                              style="width: 90px"
                            ></v-text-field>
                          </template>
                        </v-range-slider>
                        <div
                          v-if="salaryRangeErrorMessage"
                          class="text-caption ml-4"
                          style="color: #b00020"
                        >
                          {{ salaryRangeErrorMessage }}
                        </div>
                      </v-col>
                      <v-col cols="12" class="pt-6">
                        <v-text-field
                          v-model="payDescription"
                          ref="payDescription"
                          variant="solo"
                          :rules="
                            payDescription && payDescription.length
                              ? [
                                  maxLengthValidation(
                                    'Pay shown on your ad',
                                    payDescription,
                                    50
                                  ),
                                  validateWithRulesAndReturnMessages(
                                    payDescription,
                                    'payDescription',
                                    'Pay shown on your ad'
                                  ),
                                ]
                              : []
                          "
                          label="Pay shown on your ad"
                          :isRequired="true"
                        >
                        </v-text-field>
                        <p
                          style="display: block"
                          class="d-flex justify-space-between text-grey"
                        >
                          <span class="pl-2">E.g. $50,000 + annual bonus</span>
                          <span>{{ payDescription?.length }}/50</span>
                        </p>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-card>
              </v-tab-item>

              <!-- Ad Type Tab -->
              <v-tab-item v-else-if="activeTab === 'Ad_Type'">
                <v-card flat>
                  <v-form
                    ref="adInfoRefForm"
                    v-if="selectedHirer"
                    style="max-height: calc(100vh - 170px); overflow-y: scroll"
                  >
                    <v-row class="mx-2 py-5">
                      <v-col cols="12" v-if="products && products.length">
                        <div>Ad Type<span style="color: red">*</span></div>
                        <div>
                          <perfect-scrollbar class="adsCard-container">
                            <div
                              v-for="product in products"
                              :key="product.id.value"
                              :class="[
                                'adsCard',
                                { selected: selectedCard === product.id.value },
                              ]"
                              class="rounded-lg"
                              @click="selectCard(product)"
                            >
                              <div class="d-flex justify-center">
                                <v-icon
                                  v-if="selectedCard === product.id.value"
                                  class="far fa-dot-circle"
                                  color="primary"
                                ></v-icon>
                                <v-icon
                                  v-else
                                  class="far fa-dot-circle"
                                  color="grey"
                                ></v-icon>
                              </div>
                              <p
                                class="font-weight-bold pt-3"
                                :class="{
                                  'text-primary':
                                    selectedCard === product.id.value,
                                }"
                              >
                                {{ product.label }}
                              </p>
                              <p
                                class="font-weight-bold pt-3"
                                :class="{
                                  'text-primary':
                                    selectedCard === product?.id?.value,
                                }"
                              >
                                {{ product?.price?.summary }}
                              </p>
                              <p class="pt-3">
                                {{ product.description }}
                              </p>
                              <v-row class="pl-3 pt-2">
                                <v-col>
                                  <ul>
                                    <div
                                      v-for="(
                                        sellingPoint, indexKey
                                      ) in product.sellingPoints"
                                      :key="indexKey"
                                    >
                                      <li class="py-1">
                                        {{ sellingPoint.text }}
                                      </li>
                                    </div>
                                  </ul>
                                </v-col>
                              </v-row>
                            </div>
                          </perfect-scrollbar>
                        </div>
                        <p>
                          {{ adsInformation }}
                        </p>
                        <p
                          v-if="selectedProductPaymentSummary"
                          class="pa-3 mt-2 bg-hover"
                          style="
                            border-left: 3px solid rgb(var(--v-theme-primary));
                          "
                          v-html="selectedProductPaymentSummary"
                        ></p>
                      </v-col>

                      <v-col
                        cols="12"
                        v-for="indexKey in searchBulletPointsLimit"
                        :key="indexKey"
                      >
                        <p
                          class="text-subtitle-1 text-grey-darken-1"
                          v-if="indexKey === 1"
                        >
                          Key selling points
                        </p>
                        <p
                          class="text-subtitle-1 text-grey-darken-1"
                          v-if="indexKey === 1"
                        >
                          {{
                            `Enter ${searchBulletPointsLimit} key selling points to attract candidates to view your role`
                          }}
                        </p>
                        <v-text-field
                          v-model="searchBulletPoints[indexKey - 1]"
                          :ref="indexKey"
                          variant="solo"
                          :max="80"
                          :rules="[
                            maxLengthValidation(
                              'key selling points',
                              searchBulletPoints[indexKey - 1],
                              80
                            ),
                          ]"
                        >
                        </v-text-field>
                        <text
                          style="display: block"
                          class="d-flex justify-space-between align-center"
                        >
                          <div class="text-subtitle-1 text-grey-darken-1">
                            <span v-if="indexKey === 1"
                              >E.g Flexible working hours</span
                            >
                          </div>
                          <div class="text-subtitle-1 text-grey-darken-1">
                            {{
                              searchBulletPoints[indexKey - 1]?.length
                                ? searchBulletPoints[indexKey - 1]?.length
                                : 0
                            }}/80
                          </div></text
                        >
                      </v-col>
                      <v-col
                        cols="12"
                        v-if="selectedProduct?.features?.branding"
                      >
                        <v-autocomplete
                          v-model="selectedPreview"
                          :items="previewList"
                          item-title="node.name"
                          item-value="node.id.value"
                          ref="selectedSeekBranding"
                          label="Seek Branding"
                          :isAutoComplete="true"
                          :isLoading="previewLoading"
                          clearable
                          variant="solo"
                          :rules="[required('Seek Branding', selectedPreview)]"
                          :isRequired="true"
                          :itemSelected="selectedPreview"
                          :listWidth="100"
                          auto-select-first
                          item-props
                        >
                          <template v-slot:label>
                            Seek Branding
                            <span style="color: red">*</span>
                          </template>
                          <template v-slot:item="{ item, props }">
                            <div class="py-1 px-2" v-bind="props">
                              <v-hover>
                                <template
                                  v-slot:default="{ isHovering, props }"
                                >
                                  <div
                                    v-bind="props"
                                    class="rounded-lg text-truncate cursor-pointer"
                                    :class="
                                      isHovering
                                        ? 'bg-hover'
                                        : 'bg-grey-lighten-4'
                                    "
                                    :style="listWidth"
                                  >
                                    <v-list-item>
                                      <div
                                        class="d-flex justify-space-between align-center"
                                      >
                                        <div>{{ item.title }}</div>
                                        <div
                                          @click="onchangeAdPreview(item)"
                                          class="pa-3 text-blue"
                                          style="text-decoration: underline"
                                        >
                                          Preview
                                        </div>
                                      </div>
                                    </v-list-item>
                                  </div>
                                </template>
                              </v-hover>
                            </div>
                          </template>
                        </v-autocomplete>
                        <p>
                          To preview or manage your branding
                          <a
                            href="https://talent.seek.com.au/account/select?returnUrl=%2Faccount%2Fbranding"
                            target="_blank"
                            >Head over to seek</a
                          >
                        </p>
                      </v-col>
                      <v-col cols="12" md="6" sm="12">
                        <v-text-field
                          v-model="selectedVideoURL"
                          ref="selectedVideoURL"
                          variant="solo"
                          label="Video URL"
                          :rules="[
                            selectedVideoURL && selectedVideoURL.length
                              ? validateWithRulesAndReturnMessages(
                                  selectedVideoURL,
                                  'videoUrl',
                                  'Video URL'
                                )
                              : true,
                          ]"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12" md="6" sm="12">
                        <CustomSelect
                          v-model="selectedVideoPosition"
                          :items="videoPositionList"
                          ref="selectedVideoPosition"
                          item-title="name"
                          :disabled="!selectedVideoURL ? true : false"
                          item-value="name"
                          label="Video Position"
                          :isAutoComplete="true"
                          :rules="[
                            selectedVideoURL
                              ? required(
                                  'Video Position',
                                  selectedVideoPosition
                                )
                              : true,
                          ]"
                          :isRequired="selectedVideoURL ? true : false"
                          clearable
                          variant="solo"
                          :itemSelected="selectedVideoPosition"
                          @selected-item="selectedVideoPosition = $event"
                        ></CustomSelect>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          v-model="selectedHirerReference"
                          ref="selectedHirerReference"
                          variant="solo"
                          label="Internal Hirer Reference"
                        ></v-text-field>
                      </v-col>
                      <v-col cols="12">
                        <v-text-field
                          v-model="selectedBillingReference"
                          ref="selectedBillingReference"
                          variant="solo"
                          label="Billing Reference"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                  </v-form>
                </v-card>
              </v-tab-item>
              <!-- </v-form> -->
            </v-tabs-items>
          </v-card-text>

          <!-- Card actions at the bottom -->
          <div class="card-actions-div" v-if="selectedHirer">
            <v-card-actions class="d-flex align-end justify-space-between">
              <v-sheet class="align-center text-center" style="width: 100%">
                <v-row justify="center">
                  <v-col cols="6" class="d-flex justify-start pl-4">
                    <v-btn
                      v-if="tab !== 0"
                      rounded="lg"
                      class="primary"
                      variant="outlined"
                      @click="previousTab"
                    >
                      Previous
                    </v-btn>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-end pr-4">
                    <v-btn
                      v-if="tab !== 2"
                      rounded="lg"
                      class="mr-1 primary"
                      @click="nextTab"
                      variant="elevated"
                    >
                      Next
                    </v-btn>
                    <v-btn
                      v-if="tab === 2"
                      rounded="lg"
                      class="primary mr-3"
                      variant="outlined"
                      @click="onchangePreview()"
                    >
                      Preview
                    </v-btn>
                  </v-col>
                </v-row>
              </v-sheet>
            </v-card-actions>
          </div>
          <AppLoading
            v-if="dropdownListLoading || previewLoading || seekTokenLoading"
          ></AppLoading>
        </v-card>
      </template>
    </v-overlay>
  </div>
  <v-dialog
    v-model="showPreviewModel"
    :width="windowWidth > 700 ? windowWidth - 100 : windowWidth - 10"
    style="height: 100%"
    @click:outside="showPreviewModel = false"
  >
    <v-card
      class="rounded-lg"
      :min-height="windowWidth > 700 ? 800 : ''"
      style="position: relative"
    >
      <div class="d-flex align-end justify-space-between px-2">
        <div class="py-2">Preview</div>
        <v-icon size="20" color="primary" @click="showPreviewModel = false"
          >fas fa-times</v-icon
        >
      </div>
      <iframe
        @load="onIframeLoad"
        :src="previewUrl"
        :width="windowWidth > 700 ? windowWidth - 100 : windowWidth - 10"
        :height="windowWidth > 700 ? 800 : 500"
      ></iframe>
      <AppLoading v-if="frameLoader || isLoading"></AppLoading>

      <!-- Fixed Publish Button at Bottom -->
      <div
        style="
          position: fixed;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 1000;
        "
      >
        <v-btn
          rounded="lg"
          class="primary"
          :disabled="seekTokenLoading"
          @click="validatePublishForm"
          variant="elevated"
          size="large"
        >
          Publish
        </v-btn>
      </div>
    </v-card>
  </v-dialog>
  <v-dialog
    v-model="showAdModel"
    width="700px"
    min-height="600px"
    style="height: 100%"
    @click:outside="showAdModel = false"
  >
    <v-card class="rounded-lg">
      <div class="d-flex align-end justify-space-between px-2">
        <div>
          On clicking the preview link on selected branding user will able to
          his brand logo and image
        </div>
        <v-icon size="20" color="primary" @click="showAdModel = false"
          >fas fa-times</v-icon
        >
      </div>
    </v-card>
    <v-card class="rounded-lg">
      <img
        v-if="selectedImage.images[1]?.url"
        :src="selectedImage.images[1]?.url"
        alt="image source"
        style="width: 100%; height: 400px"
      />
      <div v-else style="width: 100%; height: 400px">
        <div class="text-subtitle-1 py-2 px-3">No cover image</div>
      </div>
      <img
        :src="selectedImage.images[0]?.url"
        alt="image source"
        style="width: 150px; height: 80px"
      />
      <div class="text-subtitle-1 py-2 px-3">{{ selectedImage?.name }}</div>
    </v-card>
  </v-dialog>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to exit job street form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onClosePopup()"
  >
  </AppWarningModal>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import validationRules from "@/mixins/validationRules";
import { VueTelInput } from "vue-tel-input";
import {
  JOB_CATEGORIES,
  LOCATION_SUGGESTIONS,
  ADVERTISEMENT_PRODUCTS,
  ADVERTISEMENT_SEEK_BRANDINGS,
  PREVIEW_LOGO_SEEK,
  GET_HIRER_LIST,
  GET_SEEK_TOKEN,
  ADD_SEEK_INTEGRATION,
  GET_RECRUITER_LIST,
  GET_PREVIEW_LIST,
  GET_LOCATION_NAME,
  UPDATE_SEEK_INTEGRATION,
  GET_CURRENCY_LIST,
} from "@/graphql/recruitment/job-seek/jobStreetQueries.js";
import debounce from "lodash.debounce";
import { getErrorCodes } from "@/helper";

export default {
  name: "JobStreet",
  emits: ["on-close", "refresh-job-details"],
  components: {
    CustomSelect,
    VueTelInput,
  },
  props: {
    jobPostData: {
      type: Object,
      required: true,
      default: () => {},
    },
    jobPostId: {
      type: Number || String,
      required: true,
    },
    jobStreetModel: {
      type: Boolean,
      required: true,
    },
    editJobData: {
      type: Array,
      required: true,
    },
  },
  mixins: [validationRules],
  data() {
    return {
      overlay: this.jobStreetModel,
      // Tabs
      tab: 0,
      activeTab: "Job_Info",
      profileTabs: [
        { label: "Job Info", value: "Job_Info" },
        { label: "Salary Info", value: "Salary_Info" },
        { label: "Ad Type", value: "Ad_Type" },
      ],
      // Ads Cards
      selectedCard: 0,
      selectedProductPaymentSummary: "",
      adsInformation: "",
      products: [],
      // Form data
      selectedHirer: null,
      hirerListLoading: false,
      hirerList: [],
      selectedRecruiterName: "",
      recruiterListLoading: false,
      recruiterNameList: [],
      selectedEmail: "",
      selectedPosition: "",
      selectedPhoneNo: "",
      selectedRole: null,
      selectedCategories: null,
      selectedSubCategories: null,
      dropdownSeekWorkArrangementCodes: ["Hybrid", "OnSite", "Remote"],
      selectedSeekWorkArrangementCodes: null,
      selectedWorkType: null,
      selectedLocation: null,
      selectedSearchSummary: "",
      selectedCurrency: "",
      tempCurrencyCode: "",
      selectedPayType: null,
      payDescription: "",
      selectedSellingPoints: [],
      currencyList: [],
      currencyLoader: false,
      selectedProduct: null,
      selectedSeekBranding: null,
      selectedVideoURL: "",
      selectedVideoPosition: null,
      selectedHirerReference: "",
      selectedBillingReference: "",
      // dropdowns
      roleList: [
        { id: 1, name: "Recruiter" },
        { id: 2, name: "HiringManager" },
      ],
      categoriesList: [],
      subCategoriesList: [],
      workTypeList: [],
      locationList: [],
      PayTypeList: [
        {
          PayType_Id: 1,
          PayType_Name: "Hourly rate",
          basisCode: "Hourly",
          intervalCode: "Hour",
        },
        {
          PayType_Id: 2,
          PayType_Name: "Monthly salary",
          basisCode: "Salaried",
          intervalCode: "Month",
        },
        {
          PayType_Id: 3,
          PayType_Name: "Annual salary",
          basisCode: "Salaried",
          intervalCode: "Year",
        },
        {
          PayType_Id: 4,
          PayType_Name: "Annual plus commission",
          basisCode: "SalariedPlusCommission",
          intervalCode: "Year",
        },
      ],
      workList: [
        { id: 1, name: "Casual" },
        { id: 2, name: "ContractTemp" },
        { id: 3, name: "FullTime" },
        { id: 4, name: "PartTime" },
      ],
      seekBrandingList: [],
      videoPositionList: [
        { id: 1, name: "Above" },
        { id: 2, name: "Below" },
      ],
      previewList: [],
      selectedPreview: "",
      previewLoading: false,
      selectedPreviews: {},
      range: [0, 99999999],
      salaryRangeErrorMessage: "",
      dropdownListLoading: false,
      isCategoriesLoading: false,
      isLocationLoading: false,
      isSeekBranding: false,
      showPreviewModel: false,
      isListError: false,
      keyOne: "",
      keyTwo: "",
      keyThree: "",
      showAdModel: false,
      selectedImage: {},
      editFlag: false,
      editLoading: false,
      previewUrl: "",
      searchBulletPointsLimit: null,
      searchBulletPoints: [],
      frameLoader: false,
      isLoading: false,
      openConfirmationPopup: false,
      seekTokenLoading: false,
      mobileNoCountryCode: 91,
      isValidField: false,
      searchString: "",
    };
  },
  computed: {
    mobileNumberValidation() {
      if (!this.selectedPhoneNo) {
        return "";
      } else if (!this.isValidMobileNumber) {
        return "Please provide a valid mobile number";
      } else {
        return "";
      }
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    noDataText() {
      if (this.isLocationLoading) {
        return "Loading...";
      } else if (
        !this.isLocationLoading &&
        this.locationList.length == 0 &&
        this.searchString.length >= 1
      ) {
        return "No data found";
      } else {
        return "Start typing the location for listing";
      }
    },
  },

  watch: {
    overlay(val) {
      if (val === false) {
        this.$emit("on-close");
      }
    },
    editJobData: {
      immediate: true,
      handler(val) {
        if (val && val.length) {
          this.retrieveJobDetails(val);
        } else {
          this.isValidField = true;
        }
      },
    },
  },
  created() {
    this.debouncedSearch = debounce(this.onLocationInput, 800);
  },
  mounted() {
    this.getHirerList();
    this.retrieveRecruiterDetails();
    if (this.jobPostData) {
      this.selectedPayType = this.jobPostData?.Pay_Type;
      this.range[0] = this.jobPostData?.Min_Payment_Frequency;
      this.range[1] = this.jobPostData?.Max_Payment_Frequency;
      this.selectedPosition = this.editJobData.length
        ? ""
        : this.jobPostData?.Job_Post_Name;
      this.selectedHirerReference = this.orgCode + "-" + this.jobPostId;
    }
    if (this.editJobData?.length === 0) {
      if (this.jobPostData?.workPlaceType?.toLowerCase() === "remote") {
        this.selectedSeekWorkArrangementCodes = "Remote";
      } else if (this.jobPostData?.workPlaceType?.toLowerCase() === "on-site") {
        this.selectedSeekWorkArrangementCodes = "OnSite";
      } else if (this.jobPostData?.workPlaceType?.toLowerCase() === "hybrid") {
        this.selectedSeekWorkArrangementCodes = "Hybrid";
      }
    }
  },

  methods: {
    onCloseOverlay() {
      this.openConfirmationPopup = true;
    },
    onClosePopup() {
      this.overlay = false;
      this.openConfirmationPopup = false;
    },
    onIframeLoad() {
      this.frameLoader = false;
    },
    onChangeHirer(value) {
      if (value) {
        this.getSeekIntegration(value);
      }
    },
    getSeekIntegration(id) {
      this.getSeekToken(id, false);
    },
    findMinRangeSalary() {
      if (this.range[0] < 0) {
        this.range[0] = 0;
        return this.range[0];
      }
      if (this.range[1] < 0) {
        this.range[1] = 0;
        return this.range;
      } else {
        return this.range[0];
      }
    },
    findMaxRangeSalary() {
      let minSal = this.range[0].toString();
      let maxSal = this.range[1].toString();
      if (!minSal || !maxSal) {
        this.salaryRangeErrorMessage = "Salary is required";
      } else {
        let res = parseInt(maxSal) - parseInt(minSal);
        if (res < 0) {
          this.salaryRangeErrorMessage =
            "Minimum salary should not be greater than maximum salary.";
          if (this.range[0] <= 99999999) {
            return this.range[1];
          }
          this.range[0] = 99999999;
          return this.range[0];
        }
        if (this.range[1] > 99999999) {
          this.range[1] = 99999999;
          return this.range[1];
        } else {
          this.salaryRangeErrorMessage = "";
          return this.range[1];
        }
      }
    },
    async getSeekToken(id, updateFlag) {
      let vm = this;
      vm.seekTokenLoading = true;
      vm.isLoading = true;
      await vm.$apollo
        .query({
          query: GET_SEEK_TOKEN,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            hirer_Id: id,
            form_Id: 15,
            isPublish: 1,
          },
        })
        .then(async (response) => {
          vm.seekTokenLoading = false;
          vm.isLoading = false;
          if (
            response &&
            response.data &&
            response.data.getAuthTokenJobStreet &&
            response.data.getAuthTokenJobStreet.getData &&
            response.data.getAuthTokenJobStreet.getData.accessToken
          ) {
            const token = response.data.getAuthTokenJobStreet.getData;
            if (token && token.accessToken && token.browserToken) {
              const browserToken = JSON.parse(token.browserToken);
              const accessToken = JSON.parse(token.accessToken);
              await window.$cookies.set(
                "jobStreet_bowser_token",
                browserToken.access_token,
                browserToken.expires_in
              );
              await window.$cookies.set(
                "jobStreet_access_token",
                accessToken.access_token,
                accessToken.expires_in
              );
            }
            if (
              window.$cookies.get("jobStreet_bowser_token") &&
              this.selectedHirer
            ) {
              // if (this.selectedSubCategories) {
              //   this.fetchAdvertisementProducts();
              // }
              if (updateFlag) {
                this.dropdownListLoading = true;
                if (this.editJobData.length) {
                  this.updateJobSeek();
                } else {
                  this.onSubmitSeek();
                }
              } else {
                this.fetchPreviews();
                this.getCurrencyList();
              }
            }
          }
        })
        .catch((err) => {
          vm.seekTokenLoading = false;
          vm.isLoading = false;
          vm.handleRetrieveSeekBranding(err);
        });
    },
    getCurrencyList() {
      this.currencyLoader = true;
      this.$apollo
        .query({
          query: GET_CURRENCY_LIST,
          client: "apolloClientAW",
          fetchPolicy: "no-cache",
          variables: {
            usageTypeCode: "All",
          },
        })
        .then((res) => {
          this.currencyLoader = false;
          if (
            res &&
            res.data &&
            res.data.currencies &&
            res.data.currencies.length
          ) {
            this.currencyList = res.data.currencies;
            if (this.jobPostData && this.jobPostData.Currency_Code) {
              let filterData = this.editJobData.length
                ? this.editJobData[0]?.currencyCode
                : this.jobPostData?.Currency_Code;
              const tempCurrency = res.data.currencies.find(
                (item) => item?.code === filterData
              );
              this.tempCurrencyCode = tempCurrency?.code
                ? tempCurrency?.code
                : "";
              this.selectedCurrency = tempCurrency?.code
                ? tempCurrency?.code
                : "";
              if (this.editJobData.length) {
                this.range = [
                  this.editJobData[0]?.minimumAmount,
                  this.editJobData[0]?.maximumAmount,
                ];
              }
            } else {
              this.range = [1, 99999999];
            }
          } else {
            this.currencyList = [];
          }
        })
        .catch((err) => {
          this.currencyLoader = false;
          this.handleRetrieveLocationsError(err);
        });
    },
    retrieveRecruiterDetails() {
      let vm = this;
      vm.recruiterListLoading = true;
      vm.$apollo
        .query({
          query: GET_RECRUITER_LIST,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            jobPostId: parseInt(this.jobPostId),
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getRoleBasedJobPostMember &&
            response.data.getRoleBasedJobPostMember.recruiters &&
            response.data.getRoleBasedJobPostMember.recruiters.length
          ) {
            this.recruiterNameList =
              response.data.getRoleBasedJobPostMember.recruiters;
          } else {
            this.recruiterNameList = [
              { Emp_Name: this.jobPostData?.addedByName, Emp_id: 0 },
            ];
          }
          vm.recruiterListLoading = false;
        })
        .catch(() => {
          this.recruiterNameList = [
            { Emp_Name: this.jobPostData?.addedByName, Emp_id: 0 },
          ];
          vm.recruiterListLoading = false;
        });
    },
    async retrieveJobDetails(editJobPostData) {
      this.editLoading = true;
      if (editJobPostData.length) {
        this.editFlag = true;
        const editData = editJobPostData[0];
        const suggestions = JSON.parse(editData.searchBulletPointsArray);
        if (editData && editData.hirerId) {
          this.selectedHirer = editData.hirerId;
          await this.getSeekToken(editData.hirerId, false);
          this.payDescription = editData.payDescription
            ? editData.payDescription
            : "";
          this.selectedRole = editData.roleCode;
          this.selectedEmail = editData.recruiterEmailId;
          let seekWorkArrangementCodes = editData.seekWorkArrangementCodes
            ? JSON.parse(editData.seekWorkArrangementCodes)
            : null;
          this.selectedSeekWorkArrangementCodes =
            seekWorkArrangementCodes?.length
              ? seekWorkArrangementCodes[0]
              : null;
          this.selectedRecruiterName = editData.recruiterName;
          this.selectedVideoPosition = editData.videoPositionCode;
          this.selectedVideoURL = editData.videoUrl;
          this.selectedLocation = editData.positionLocationId;
          this.selectedPhoneNo = editData.phoneNo;
          this.searchBulletPoints = suggestions;
          this.selectedSearchSummary = editData.jobSummary;
          this.selectedWorkType = editData.seekWorkTypeCode;
          this.selectedPosition = editData.jobTitle;
          this.selectedBillingReference = editData.seekBillingReference;
          this.selectedPreview = editData.advertisementBranding;
          this.selectedSubCategories = editData.categoryId;
          this.selectedCard = editData.seekAdvertisementProductId;
          this.mobileNoCountryCode = editData.recruiterNoCountryCode
            ? parseInt(editData.recruiterNoCountryCode)
            : 91;

          this.products.forEach((product) => {
            product.selected =
              product.id.value === editData.seekAdvertisementProductId;
            if (product.id.value === editData.seekAdvertisementProductId) {
              this.selectedProduct = product;
              let initializeSellingPoints =
                product?.features?.searchBulletPoints?.limit;
              this.selectedSellingPoints = Array(initializeSellingPoints).fill(
                ""
              );
              this.selectedProductPaymentSummary = product.payment.summaryHtml;
            }
          });
          this.getLocation(editData.positionLocationId);
          this.isValidField = true;
          this.isValidMobileNumber = true;
        }
        this.fetchJobCategories();
      } else {
        this.editFlag = false;
      }
      this.editLoading = false;
    },
    validateMobileNumber(param) {
      this.isValidMobileNumber = param.valid;
    },
    getCountryCode(countryCode) {
      if (countryCode) {
        this.mobileNoCountryCode = countryCode.dialCode;
      } else {
        this.mobileNoCountryCode = "";
      }
    },
    getLocation(locationId) {
      let vm = this;
      vm.isLocationLoading = true;
      vm.$apollo
        .query({
          query: GET_LOCATION_NAME,
          client: "apolloClientAW",
          fetchPolicy: "no-cache",
          variables: {
            id: locationId,
          },
        })
        .then((response) => {
          this.locationList = [];
          if (
            response &&
            response.data &&
            response.data.location &&
            response.data.location.contextualName
          ) {
            this.locationList = [
              {
                location_Name: response.data.location.contextualName,
                location_Id: locationId,
              },
            ];
            vm.isLocationLoading = false;
          } else {
            this.locationList = [];
            vm.isLocationLoading = false;
            vm.handleRetrieveLocationsError();
          }
        })
        .catch((err) => {
          vm.isLocationLoading = false;
          this.locationList = [];
          if (err && err.graphQLErrors) {
            let errorCode = getErrorCodes(err);
            if (
              errorCode &&
              errorCode.toLowerCase().trim() === "unauthenticated"
            ) {
              this.handleUnAuthenticatedErrors();
            } else {
              vm.handleRetrieveLocationsError(err);
            }
          }
        });
    },
    async nextTab() {
      if (this.tab === 0) {
        const { valid } = await this.$refs.jobInfoRefForm.validate();
        if (valid) {
          if (this.tab < this.profileTabs.length - 1) {
            this.tab++;
            this.activeTab = this.profileTabs[this.tab].value;
          }
        }
      } else if (this.tab === 1) {
        const { valid } = await this.$refs.salaryInfoRefForm.validate();
        if (valid && this.salaryRangeErrorMessage === "") {
          this.fetchAdvertisementProducts();
        }
      }
    },
    onchangeAdPreview(val) {
      this.showAdModel = true;
      this.selectedImage = val?.props?.node;
    },
    async onchangePreview() {
      const { valid } = await this.$refs.adInfoRefForm.validate();
      if (valid) {
        this.fetchPreview();
      }
    },
    onChangeProfileTabs(index) {
      this.tab = index;
      this.activeTab = this.profileTabs[index].value;
    },
    previousTab() {
      if (this.tab > 0) {
        this.tab--;
        this.activeTab = this.profileTabs[this.tab].value;
      }
    },
    isActiveTab(tab) {
      return tab.value === this.activeTab;
    },
    selectCard(value) {
      const tempLimit = value.features.searchBulletPoints?.limit;
      this.searchBulletPointsLimit = tempLimit;
      if (tempLimit && tempLimit > 0) {
        const arr = new Array(tempLimit).fill(0);
        const incrementedArr = arr.map((_, index) =>
          this.searchBulletPoints[index] ? this.searchBulletPoints[index] : ""
        );
        this.searchBulletPoints = incrementedArr;
      } else {
        this.searchBulletPoints = [];
      }

      this.selectedCard = value.id.value;
      this.products.forEach((product) => {
        product.selected = product.id.value === value.id.value;
        if (product.id.value === value.id.value) {
          this.selectedProduct = product;
          let initializeSellingPoints =
            product?.features?.searchBulletPoints?.limit;
          this.selectedSellingPoints = Array(initializeSellingPoints).fill("");
          this.selectedProductPaymentSummary = product?.payment?.summaryHtml;
        }
      });
    },

    // GET_PREVIEW_LIST
    fetchPreview() {
      let vm = this;
      const selectedPay = this.PayTypeList.find(
        (item) => item.PayType_Name === this.selectedPayType
      );
      let positionCard = {
        jobCategories: this.selectedSubCategories,
        positionLocation: this.selectedLocation,
        positionOrganizations: this.selectedHirer,
        positionTitle: this.selectedPosition,
        offeredRemunerationPackage: {
          basisCode: selectedPay.basisCode,
          descriptions:
            this.payDescription && this.payDescription.length
              ? [this.payDescription]
              : [],
          ranges: [
            {
              intervalCode: selectedPay.intervalCode,
              minimumAmount: {
                currency: this.selectedCurrency,
                value: this.jobPostData.Min_Payment_Frequency,
              },
              maximumAmount: {
                currency: this.selectedCurrency,
                value: this.jobPostData.Max_Payment_Frequency,
              },
            },
          ],
        },
        seekAnzWorkTypeCode: this.selectedWorkType,
        postingInstructions: [
          {
            seekAdvertisementProductId: this.selectedProduct?.id?.value,
            brandingId: this.selectedPreview ? this.selectedPreview : null,
          },
        ],
        seekVideo: null,
      };
      let sellingPoints = [
        {
          descriptionId: "AdvertisementDetails",
          content: this.jobPostData.Job_Description,
        },
        {
          descriptionId: "SearchSummary",
          content: this.selectedSearchSummary,
        },
      ];
      if (this.searchBulletPoints && this.searchBulletPoints.length) {
        this.searchBulletPoints.forEach((listData) => {
          if (listData && listData.length) {
            sellingPoints.push({
              descriptionId: "SearchBulletPoint",
              content: listData,
            });
          }
        });
      }
      positionCard["positionFormattedDescriptions"] = sellingPoints;
      if (this.selectedVideoURL) {
        positionCard["seekVideo"] = {
          url: this.selectedVideoURL,
          seekAnzPositionCode: this.selectedVideoPosition,
        };
      }
      if (this.editJobData.length) {
        positionCard["profileId"] = this.editJobData[0].profileId;
      }
      this.frameLoader = true;
      vm.$apollo
        .query({
          query: GET_PREVIEW_LIST,
          variables: {
            positionProfile: positionCard,
          },
          client: "apolloClientAW",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          this.frameLoader = false;
          this.showPreviewModel = true;
          this.previewUrl = "";
          if (
            response &&
            response.data &&
            response.data.postedPositionProfilePreview &&
            response.data.postedPositionProfilePreview.previewUri &&
            response.data.postedPositionProfilePreview.previewUri.url
          ) {
            this.previewUrl =
              response.data.postedPositionProfilePreview.previewUri.url;
          }
        })
        .catch((err) => {
          this.previewUrl = "";
          this.frameLoader = false;
          if (err && err.graphQLErrors) {
            let errorCode = getErrorCodes(err);
            if (errorCode.toLowerCase().trim() === "unauthenticated") {
              this.handleUnAuthenticatedErrors();
            } else {
              if (
                errorCode?.toLowerCase().trim() === "bad_user_input" &&
                JSON.stringify(err).includes("Cannot exceed 15000")
              ) {
                var snackbarData = {
                  isOpen: true,
                  type: "error",
                  message:
                    "The Job Description is too long. Please keep it within 15,000 characters.",
                };
                this.showAlert(snackbarData);
              } else {
                vm.handleRetrieveAdvertisementProductsError(err);
              }
            }
          }
        });
    },
    // Retrving Ad Product Details

    fetchAdvertisementProducts() {
      let vm = this;
      vm.dropdownListLoading = true;
      const selectedPay = this.PayTypeList.find(
        (item) => item.PayType_Name === this.selectedPayType
      );
      let positionCard = {
        positionProfile: {
          jobCategories: this.selectedSubCategories,
          positionLocation: this.selectedLocation,
          positionOrganizations: this.selectedHirer,
          positionTitle: this.selectedPosition,
          offeredRemunerationPackage: {
            basisCode: selectedPay.basisCode,
            descriptions:
              this.payDescription && this.payDescription.length
                ? [this.payDescription]
                : [],
            ranges: [
              {
                intervalCode: selectedPay.intervalCode,
                minimumAmount: {
                  currency: this.selectedCurrency,
                  value: this.jobPostData.Min_Payment_Frequency,
                },
                maximumAmount: {
                  currency: this.selectedCurrency,
                  value: this.jobPostData.Max_Payment_Frequency,
                },
              },
            ],
          },
          seekAnzWorkTypeCode: this.selectedWorkType,
        },
      };
      if (this.editJobData.length) {
        positionCard["positionProfile"]["profileId"] =
          this.editJobData[0].profileId;
        positionCard["selectedAdvertisementProductId"] =
          this.editJobData[0].seekAdvertisementProductId;
      }
      vm.$apollo
        .query({
          query: ADVERTISEMENT_PRODUCTS,
          variables: positionCard,
          client: "apolloClientAW",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.advertisementProducts
          ) {
            if (this.tab < this.profileTabs.length - 1) {
              this.tab++;
              this.activeTab = this.profileTabs[this.tab].value;
            }
            this.adsInformation =
              response.data.advertisementProducts.information;
            this.products = response.data.advertisementProducts.products;
            if (this.products.length > 0) {
              if (this.editJobData.length) {
                let res = this.products.find((item) => item.selected);
                this.selectCard(res);
              } else {
                this.selectCard(this.products[0]);
              }
            }
            vm.dropdownListLoading = false;
          } else {
            vm.dropdownListLoading = false;
          }
        })
        .catch((err) => {
          vm.dropdownListLoading = false;
          if (err && err.graphQLErrors) {
            let errorCode = getErrorCodes(err);
            if (errorCode.toLowerCase().trim() === "unauthenticated") {
              this.handleUnAuthenticatedErrors();
            } else {
              vm.handleRetrieveAdvertisementProductsError(err);
            }
          }
        });
    },
    handleRetrieveAdvertisementProductsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "Job Street",
        isListError: false,
      });
    },

    // Retriving The Categories & Sub-Categories
    fetchJobCategories() {
      let vm = this;
      vm.isCategoriesLoading = true;
      vm.$apollo
        .query({
          query: JOB_CATEGORIES,
          variables: {
            schemeId: "seekAnz",
            positionProfile: {
              positionLocation: this.selectedLocation,
            },
          },
          client: "apolloClientAW",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          vm.isCategoriesLoading = false;
          if (response && response.data && response.data.jobCategories) {
            let categories = response.data.jobCategories.map((cat) => ({
              cat_Name: cat.name,
              cat_Id: cat.id.value,
              children: cat.children,
            }));
            categories.push({ cat_Id: 0, cat_Name: "Other", children: [] });
            this.categoriesList = categories;
            if (this.selectedSubCategories) {
              this.categoriesList.forEach((item) => {
                if (item.children && item.children.length) {
                  if (
                    item.children.find(
                      (list) => list.id.value === this.selectedSubCategories
                    )
                  ) {
                    this.selectedCategories = item.cat_Id;
                    this.subCategoriesList = item.children.map((child) => ({
                      subcat_Name: child.name,
                      subcat_Id: child.id.value,
                    }));
                    // if (this.selectedSubCategories) {
                    //   this.fetchAdvertisementProducts();
                    // }
                  }
                }
              });
            }
          } else {
            vm.handleRetrieveJobCategoriesError();
          }
        })
        .catch((err) => {
          if (err && err.graphQLErrors) {
            let errorCode = getErrorCodes(err);
            if (errorCode.toLowerCase().trim() === "unauthenticated") {
              this.handleUnAuthenticatedErrors();
            } else {
              vm.handleRetrieveJobCategoriesError(err);
            }
          }
        });
    },
    handleRetrieveJobCategoriesError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job street",
        isListError: false,
      });
    },
    onCategorySelected(categoryId) {
      this.selectedSubCategories = null;
      const selectedCategories = this.categoriesList.find(
        (cat) => cat.cat_Id === categoryId
      );
      if (selectedCategories) {
        this.subCategoriesList = selectedCategories.children.map((child) => ({
          subcat_Name: child.name,
          subcat_Id: child.id.value,
        }));
      } else {
        this.subCategoriesList = [];
      }
    },

    async onLocationInput(val) {
      const searchText = val;
      this.searchString = searchText;
      if (
        searchText &&
        searchText.length &&
        window.$cookies.get("jobStreet_bowser_token") &&
        this.selectedHirer
      ) {
        this.fetchLocations(searchText);
      } else {
        // this.locationList = [];
      }
    },
    updateLocation(val) {
      this.selectedLocation = val;
      if (val) {
        this.fetchJobCategories();
      }
    },
    // Retriving The locations
    fetchLocations(searchText) {
      let vm = this;
      vm.isLocationLoading = true;
      vm.$apollo
        .query({
          query: LOCATION_SUGGESTIONS,
          variables: {
            first: 5,
            hirerId: vm.selectedHirer,
            schemeId: "seekAnz",
            text: searchText,
            usageTypeCode: "PositionPosting",
          },
          client: "apolloClientAW",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          this.locationList = [];
          if (response && response.data && response.data.locationSuggestions) {
            this.locationList = response.data.locationSuggestions.map(
              (loc) => ({
                location_Name: loc.location.contextualName,
                location_Id: loc.location.id.value,
                currency_Code: loc.location?.currencies[0]?.code,
              })
            );
            vm.isLocationLoading = false;
          } else {
            this.locationList = [];
            vm.isLocationLoading = false;
            vm.handleRetrieveLocationsError();
          }
        })
        .catch((err) => {
          vm.isLocationLoading = false;
          if (err && err.graphQLErrors) {
            let errorCode = getErrorCodes(err);
            if (errorCode.toLowerCase().trim() === "unauthenticated") {
              this.handleUnAuthenticatedErrors();
            } else {
              vm.handleRetrieveLocationsError(err);
            }
          }
        });
    },
    handleUnAuthenticatedErrors() {
      var snackbarData = {
        isOpen: true,
        type: "warning",
        message: "You've been inactive for a while; please try again",
      };
      this.showAlert(snackbarData);
      this.getSeekToken(this.selectedHirer, false);
    },
    fetchPreviews() {
      let vm = this;
      vm.previewLoading = true;
      vm.$apollo
        .query({
          query: PREVIEW_LOGO_SEEK,
          variables: {
            after: null,
            first: 10,
            hirerId: vm.selectedHirer,
          },
          client: "apolloClientAW",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          vm.previewLoading = false;
          if (
            response &&
            response.data &&
            response.data.advertisementBrandings &&
            response.data.advertisementBrandings.edges
          ) {
            this.previewList = response.data.advertisementBrandings.edges;
          } else {
            vm.handleRetrieveLocationsError();
          }
        })
        .catch((err) => {
          vm.previewLoading = false;
          if (err && err.graphQLErrors) {
            let errorCode = getErrorCodes(err);
            if (errorCode.toLowerCase().trim() === "unauthenticated") {
              this.handleUnAuthenticatedErrors();
            } else {
              vm.handleRetrieveLocationsError(err);
            }
          }
        });
    },
    handleRetrieveLocationsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "Job Street",
        isListError: false,
      });
    },
    async getHirerList() {
      let vm = this;
      vm.hirerListLoading = true;
      await vm.$apollo
        .query({
          query: GET_HIRER_LIST,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getJobStreetHirerList &&
            response.data.getJobStreetHirerList.hirerList &&
            response.data.getJobStreetHirerList.hirerList.length > 0
          ) {
            this.hirerList = response.data.getJobStreetHirerList.hirerList;
          } else {
            this.hirerList = [];
          }
          vm.hirerListLoading = false;
        })
        .catch((err) => {
          vm.hirerListLoading = false;
          vm.handleRetrieveSeekBranding(err);
        });
    },

    //Retreive Seek Branding dropdown
    fetchSeekBrandings() {
      let vm = this;
      vm.isSeekBranding = true;
      vm.$apollo
        .query({
          query: ADVERTISEMENT_SEEK_BRANDINGS,
          variables: {
            after: "",
            first: 10, // Number of branding items to fetch initially
            hirerId: vm.selectedHirer,
          },
          client: "apolloClientAW",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.advertisementBranding
          ) {
            vm.seekBrandingList =
              response.data.advertisementBrandings.edges.map((edge) => ({
                seek_Id: edge.node.id.value,
                seek_Name: edge.node.name,
              }));
            vm.isSeekBranding = false;
          } else {
            vm.isSeekBranding = true;
            vm.handleRetrieveSeekBranding();
          }
        })
        .catch((err) => {
          vm.handleRetrieveSeekBranding(err);
        });
    },
    handleRetrieveSeekBranding(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job street",
        isListError: false,
      });
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    // This is the validate function that validates that compulsory fields are filled or not
    async validatePublishForm() {
      // Check if Job Description exceeds 15,000 characters
      if (this.jobPostData?.Job_Description?.length > 15000) {
        const snackbarData = {
          isOpen: true,
          type: "error",
          message:
            "Job Description cannot exceed 15,000 characters. Current length: " +
            this.jobPostData.Job_Description.length +
            " characters.",
        };
        this.showAlert(snackbarData);
        return;
      }

      const { valid } = await this.$refs.adInfoRefForm.validate();
      if (valid) {
        this.getSeekToken(this.selectedHirer, true);
      }
    },
    updateJobSeek() {
      let vm = this;
      vm.dropdownListLoading = true;
      this.isLoading = true;
      vm.$apollo
        .query({
          query: UPDATE_SEEK_INTEGRATION,
          variables: {
            jobStreetId: this.editJobData[0]?.jobStreetId,
            roleCode: this.selectedRole,
            email: this.selectedEmail,
            recruiterName: this.selectedRecruiterName,
            phoneNo: this.selectedPhoneNo,
            recruiterNoCountryCode: this.mobileNoCountryCode.toString(),
          },
          client: "apolloClientAX",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          vm.isLoading = false;
          if (response && response.data) {
            this.dropdownListLoading = true;
            this.onSubmitSeek();
          } else {
            vm.dropdownListLoading = false;
            vm.handleRetrieveLocationsError();
          }
        })
        .catch((err) => {
          vm.dropdownListLoading = false;
          vm.isLoading = false;
          vm.handleUpdateJobDetailsError(err);
        });
    },
    handleUpdateJobDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "job details",
        isListError: false,
      });
    },
    async onSubmitSeek() {
      let vm = this;
      this.isLoading = true;
      let requestPayload = {
        jobStreetId: this.editJobData.length
          ? this.editJobData[0]?.jobStreetId
          : 0,
        jobId: this.jobPostId,
        payDescription: this.payDescription,
        roleCode: this.selectedRole,
        email: this.selectedEmail,
        recruiterName: this.selectedRecruiterName
          ? this.selectedRecruiterName
          : "manager",
        // documentId: "someDocumentId",
        videoPositionCode: this.selectedVideoPosition,
        videoUrl: this.selectedVideoURL,
        positionLocationId: this.selectedLocation,
        hirerId: this.selectedHirer,
        phoneNo: this.selectedPhoneNo,
        seekAdvertisementProductId: this.selectedProduct?.id?.value,
        categoryId: this.selectedSubCategories,
        seekWorkTypeCode: this.selectedWorkType,
        advertisementBranding: this.selectedPreview,
        seekBillingReference: this.selectedBillingReference,
        appliedStatus: "Active",
        searchSummaryDescription: this.selectedSearchSummary,
        jobTitle: this.selectedPosition,
        jobSummary: this.jobPostData.Job_Description,
        searchBulletPointsArray: this.searchBulletPoints,
        recruiterNoCountryCode: this.mobileNoCountryCode.toString(),
        minimumAmount: this.range[0].toString(),
        maximumAmount: this.range[1].toString(),
        currency: this.selectedCurrency,
        seekWorkArrangementCodes: this.selectedSeekWorkArrangementCodes
          ? [this.selectedSeekWorkArrangementCodes]
          : [],
      };
      if (
        this.selectedProduct &&
        this.selectedProduct?.features &&
        this.selectedProduct?.features?.branding
      ) {
        requestPayload["advertisementBranding"] = this.selectedPreview;
      } else {
        requestPayload["advertisementBranding"] = null;
      }
      if (this.editJobData.length) {
        requestPayload["profileId"] = this.editJobData[0]?.profileId;
      }
      vm.$apollo
        .query({
          variables: requestPayload,
          query: ADD_SEEK_INTEGRATION,
          client: "apolloClientAX",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          this.dropdownListLoading = false;
          if (res && res.data) {
            //close the model here
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: `Job details ${
                this.editJobData && this.editJobData.length
                  ? "updated"
                  : "added"
              } successfully.`,
            };
            this.showAlert(snackbarData);
            this.$emit("refresh-job-details");
          } else {
            vm.handleUpdateJobDetailsError();
          }
          this.isLoading = false;
        })
        .catch((err) => {
          vm.dropdownListLoading = false;
          this.isLoading = false;
          vm.handleUpdateJobDetailsError(err);
        });
    },
  },
};
</script>

<style scoped>
.overlay-card {
  overflow-y: auto;
  border-radius: 0px !important;
}
.card-actions-div {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.adsCard-container {
  display: flex;
  margin: 10px 0px;
  padding-bottom: 15px;
  overflow-x: scroll;
}
.adsCard {
  min-width: 200px;
  max-width: 200px;
  height: auto;
  border: 1px solid #ccc;
  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: border-color 0.3s;
  margin: 0px 10px;
  padding: 10px;
}
.adsCard.selected {
  border: 2px solid rgb(var(--v-theme-primary));
}
.overlay-head {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
}
.truncate {
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
