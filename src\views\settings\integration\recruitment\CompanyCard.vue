<template>
  <v-card
    :style="isMobileView ? '' : 'max-width:350px; height: 200px'"
    class="rounded-lg py-3"
  >
    <div class="ml-3 mr-7 d-flex justify-space-between" style="height: 30px">
      <div class="d-flex align-center" style="width: 200px">
        <img
          :src="logo"
          style="width: 30px; height: auto"
          class=""
          :alt="CompanyName"
        />
        <p class="ml-2 font-weight-bold" style="height: max-content">
          {{ CompanyName }}
        </p>
      </div>
      <div class="d-flex justify-end align-center">
        <v-tooltip text="You don't have access to perform this action">
          <template v-slot:activator="{ props }">
            <v-switch
              density="compact"
              v-bind="formAccess.add && isAdmin ? '' : props"
              :readonly="formAccess.add && isAdmin ? false : true"
              color="primary"
              hide-details
              v-model="configureSwitch"
              @click="handleSwitchClick($event)"
            ></v-switch>
          </template>
        </v-tooltip>
      </div>
    </div>
    <div style="background-color: rgb(***********)" class="pa-3 my-2">
      <div bg-color="grey-lighten-2" style="font-size: small">
        {{ description }}
      </div>
    </div>
    <div class="d-flex justify-end">
      <v-btn
        v-if="showEditButton"
        variant="text"
        color="blue"
        density="compact"
        @click="openOverlay()"
      >
        Edit configuration
      </v-btn>
    </div>
    <div v-if="overlay" class="text-center">
      <v-overlay
        v-model="overlay"
        class="d-flex justify-end"
        id="integration-form"
        :class="
          CompanyName && CompanyName.toLowerCase() === 'jobstreet'
            ? 'job-street-form-overlay'
            : ''
        "
      >
        <SignInForm
          v-if="CompanyName == 'Irukka'"
          @close="closeButtonClicked()"
          @change-status="changeStatus"
        ></SignInForm>
        <div
          v-else
          id="overlay-card"
          class="rounded-lg"
          :style="
            CompanyName && CompanyName.toLowerCase() === 'jobstreet'
              ? 'overflow: scroll'
              : ''
          "
        >
          <div
            id="overlay-head"
            class="text-h6 text-medium-emphasis bg-primary"
          >
            <p>{{ CompanyName }} Integration Settings</p>
            <v-btn
              icon="fas fa-times"
              variant="text"
              @click="closeButtonClicked()"
            ></v-btn>
          </div>
          <div
            id="overlay-body"
            :style="{ height: `calc(${windowHeight}px - 130px)` }"
          >
            <img
              :src="logo"
              style="width: 50px; height: auto"
              class="mb-5"
              :alt="CompanyName"
            />
            <v-form @submit.prevent ref="integrationForm">
              <div class="d-flex justify-end link-tab">
                <a :href="hyperLink" target="__blank">How to get this?</a>
              </div>
              <v-text-field
                variant="solo"
                class="mb-3"
                v-model="companyId"
                v-if="CompanyName.toLowerCase() !== 'indeed'"
                :placeholder="
                  CompanyName === 'JobStreet' ? 'Example: 62337863' : ''
                "
                :rules="[
                  required(
                    CompanyName === 'JobStreet'
                      ? 'Seek Company Id'
                      : 'Company Id',
                    companyId
                  ),
                  minMaxStringValidation(
                    CompanyName === 'JobStreet'
                      ? 'Seek Company Id'
                      : 'Company Id',
                    companyId,
                    1,
                    50
                  ),
                  multilingualNameNumericValidation(
                    CompanyName === 'JobStreet'
                      ? 'Seek Company Id'
                      : 'Company Id',
                    companyId
                  ),
                ]"
              >
                <template v-slot:label>
                  <span>{{
                    CompanyName == "JobStreet"
                      ? "Seek Company Id"
                      : "Company Id"
                  }}</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
              <v-text-field
                variant="solo"
                class="mb-3"
                :counter="100"
                v-if="CompanyName.toLowerCase() === 'indeed'"
                v-model="sourceName"
                :rules="[
                  required('Source Name', sourceName),
                  minMaxStringValidation('Source Name', sourceName, 1, 100),
                  multilingualNameNumericValidation('Source Name', sourceName),
                ]"
              >
                <template v-slot:label>
                  <span>{{ "Source Name" }}</span>
                  <span class="ml-1" style="color: red">*</span>
                </template>
              </v-text-field>
              <span></span>
              <div class="my-4 d-flex justify-center">
                <v-btn
                  class="px-7 primary"
                  variant="elevated"
                  type="submit"
                  @click="submitForm()"
                  >{{ CompanyName == "JobStreet" ? "Add" : "Update" }}</v-btn
                >
              </div>
            </v-form>
            <div class="mt-5">
              <v-row>
                <v-col cols="12">
                  <v-card
                    class="rounded-lg py-4"
                    v-if="
                      formAccess &&
                      formAccess.view &&
                      CompanyName &&
                      CompanyName.toLowerCase() === 'jobstreet'
                    "
                  >
                    <div v-if="hirerListLoading" class="mt-3">
                      <v-skeleton-loader
                        ref="skeleton1"
                        type="table-heading"
                        class="mx-auto"
                      ></v-skeleton-loader>
                      <div v-for="i in 3" :key="i" class="mt-2">
                        <v-skeleton-loader
                          ref="skeleton2"
                          type="list-item-avatar"
                          class="mx-auto"
                        ></v-skeleton-loader>
                      </div>
                    </div>
                    <div v-else>
                      <v-data-table
                        v-if="!isLoading"
                        :headers="jobStreetHeaders"
                        :items="hirerList"
                        fixed-header
                        :hide-default-footer="true"
                        style="box-shadow: none !important"
                        class="elevation-1"
                      >
                        <template v-slot:item="{ item }">
                          <tr
                            class="data-table-tr bg-white"
                            :class="
                              isMobileView
                                ? ' v-data-table__mobile-table-row'
                                : ''
                            "
                          >
                            <td
                              :class="
                                isMobileView
                                  ? 'd-flex justify-space-between align-center'
                                  : ''
                              "
                              :style="
                                isMobileView
                                  ? `width: ${windowWidth - 40}px; height: auto`
                                  : ''
                              "
                            >
                              <div
                                v-if="isMobileView"
                                class="text-subtitle-1 text-grey-darken-1"
                              >
                                Seek Hiring Account
                              </div>
                              <div class="flex-column">
                                <v-tooltip
                                  :text="item?.Company_Name"
                                  location="bottom"
                                  max-width="300"
                                >
                                  <template v-slot:activator="{ props }">
                                    <section
                                      v-bind="item?.Company_Name ? props : {}"
                                      class="d-flex align-center text-body-2 text-truncate text-start text-primary"
                                      style="
                                        max-width: 250px;
                                        white-space: normal !important;
                                        word-break: break-word;
                                      "
                                    >
                                      {{ checkNullValue(item.Company_Name) }}
                                    </section>
                                  </template>
                                </v-tooltip>
                                <div
                                  class="text-subtitle-1 font-weight-regular text-grey-darken-1 text-truncate"
                                  :style="'max-width: 250px;'"
                                >
                                  {{ checkNullValue(item.Company_Id) }}
                                </div>
                              </div>
                            </td>
                            <td
                              :class="
                                isMobileView
                                  ? 'd-flex justify-space-between align-center'
                                  : ''
                              "
                              :style="
                                isMobileView
                                  ? `width: ${windowWidth - 40}px`
                                  : ''
                              "
                            >
                              <div
                                v-if="isMobileView"
                                class="text-subtitle-1 text-grey-darken-1"
                              >
                                Status
                              </div>
                              <section class="text-body-2 text-primary">
                                <AppToggleButton
                                  button-active-text="Active"
                                  button-inactive-text="InActive"
                                  button-active-color="#7de272"
                                  button-inactive-color="red"
                                  id-value="gab-analysis-based-on"
                                  :current-value="item.Status === 'Active'"
                                  @chosen-value="
                                    (hirer_Status) =>
                                      updateStatus(hirer_Status, item)
                                  "
                                  :tooltipContent="
                                    formAccess.update
                                      ? ''
                                      : `Sorry, you don't have access rights to update the status`
                                  "
                                  :isDisableToggle="!formAccess.update"
                                ></AppToggleButton>
                              </section>
                            </td>
                            <td
                              :class="
                                isMobileView
                                  ? 'd-flex justify-space-between align-center'
                                  : ''
                              "
                              :style="
                                isMobileView
                                  ? `width: ${windowWidth - 40}px`
                                  : ''
                              "
                            >
                              <div
                                v-if="isMobileView"
                                class="text-subtitle-1 text-grey-darken-1"
                              >
                                Action
                              </div>
                              <section>
                                <ActionMenu
                                  v-if="formAccess.delete"
                                  @selected-action="onActions(item)"
                                  :actions="['Delete']"
                                  :access-rights="formAccess"
                                  iconColor="grey"
                                ></ActionMenu>
                                <div v-else>-</div>
                              </section>
                            </td>
                          </tr>
                        </template>
                      </v-data-table>
                    </div>
                  </v-card>
                </v-col>
              </v-row>
            </div>
          </div>
        </div>
      </v-overlay>
    </div>
  </v-card>
  <AppWarningModal
    v-if="openWarningModal"
    :open-modal="openWarningModal"
    confirmation-heading="Are you sure you want to delete this record ?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openWarningModal = false"
    @accept-modal="onDeleteSeekAccount()"
  ></AppWarningModal>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import { ADD_UPDATE_RECRUITMENT_STATUS } from "@/graphql/settings/Integration/jobPostIntegrationQueries";
import SignInForm from "../SignInForm.vue";
import {
  GET_HIRER_LIST,
  GET_SEEK_TOKEN,
  UPDATE_SEEK_STATUS,
  VERIFY_JOB_STREET,
} from "@/graphql/recruitment/job-seek/jobStreetQueries";
import { checkNullValue } from "@/helper";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import { DELETE_SEEK_ACCOUNT } from "@/graphql/mpp/manPowerPlanningQueries";

export default {
  name: "CompanyCard",
  components: {
    SignInForm,
    ActionMenu,
  },
  mixins: [validationRules],
  emits: ["refetch"],
  props: {
    logo: {
      type: String,
      default: "",
    },
    CompanyName: {
      type: String,
      default: "",
    },
    description: {
      type: String,
      default: "",
    },
    hyperLink: {
      type: String,
      default: "",
    },
    statusArray: {
      type: Array,
      default: () => [],
    },
  },
  data: () => {
    return {
      changeStatus: "",
      configureSwitch: false,
      overlay: false,
      companyId: "",
      showValidationAlert: false,
      invalid: false,
      validationMessages: [],
      companyObject: null,
      getIntegrationStatus: [],
      sourceName: "",
      hirerList: [],
      originalHirerList: [],
      hirerListLoading: false,
      openWarningModal: false,
      selectedHirer: null,
      isLoading: false,
    };
  },
  computed: {
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    formAccess() {
      let formAccess = this.accessRights("242");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["add"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    jobStreetHeaders() {
      let headers = [
        {
          title: "Seek Hiring Account",
          align: "start",
          key: "Company_Name",
        },
        {
          title: "Status",
          align: "start",
          key: "Status",
        },
        {
          title: "Action",
          align: "start",
          sortable: false,
          key: "",
        },
      ];
      return headers;
    },
    showEditButton() {
      if (
        this.companyObject &&
        this.companyObject.Integration_Status == "Active" &&
        this.formAccess &&
        this.formAccess.update &&
        this.isAdmin
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
  watch: {
    overlay() {
      if (!this.overlay) {
        this.isLoading = false;
      }
    },
    statusArray: {
      handler() {
        this.fetchIntegrationStatus();
      },
      deep: true,
    },
  },
  mounted() {
    this.fetchIntegrationStatus();
  },
  methods: {
    checkNullValue,
    closeButtonClicked() {
      this.overlay = false;
      this.companyId =
        this.CompanyName == "JobStreet"
          ? this.companyObject.Hirer_ID
          : this.companyObject.Company_Id;
      if (this.companyObject.Integration_Status == "Active") {
        this.configureSwitch = true;
      } else {
        this.configureSwitch = false;
      }
    },
    handleSwitchClick() {
      if (!this.configureSwitch) {
        if (this.CompanyName == "Indeed") {
          this.companyId = "";
          this.overlay = true;
        } else if (this.CompanyName == "JobStreet") {
          this.companyId = "";
          this.addUpdateRecruitmentStatus("Active");
        } else {
          this.overlay = true;
        }
      } else {
        if (this.companyObject.Integration_Status == "Active") {
          if (this.CompanyName == "Indeed") {
            this.companyId = "";
          }
          this.addUpdateRecruitmentStatus("Inactive");
        }
      }
    },
    openOverlay() {
      this.overlay = true;
      if (this.CompanyName && this.CompanyName.toLowerCase() === "jobstreet") {
        this.companyId = "";
        this.getHirerList();
      }
    },
    onDeleteSeekAccount() {
      this.openWarningModal = false;
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_SEEK_ACCOUNT,
          variables: {
            seekHirerId: parseInt(this.selectedHirer?.Hirer_List_Id) || 0,
            formId: 285,
          },
          client: "apolloClientAX",
        })
        .then((res) => {
          vm.isLoading = false;
          if (
            res &&
            res.data &&
            res.data.deleteJobStreetAccount &&
            res.data.deleteJobStreetAccount.message
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: res.data.deleteJobStreetAccount.message,
            };
            vm.showAlert(snackbarData);
            this.getHirerList();
          }
        })
        .catch((error) => {
          vm.isLoading = false;
          vm.handleAddUpdateIntegrationStatusError(error);
        });
    },
    onActions(item) {
      this.selectedHirer = item;
      this.openWarningModal = true;
    },
    updateStatus(status, item) {
      this.hirerList = [];
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: UPDATE_SEEK_STATUS,
          variables: {
            status: item.Status === "InActive" ? "Active" : "InActive",
            seekHirerId: item?.Hirer_List_Id || "",
            formId: 285,
          },
          client: "apolloClientAX",
        })
        .then((res) => {
          vm.isLoading = false;
          if (
            res &&
            res.data &&
            res.data.jobStreetCompanyIdStatusUpdate &&
            res.data.jobStreetCompanyIdStatusUpdate.message
          ) {
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: res.data.jobStreetCompanyIdStatusUpdate.message,
            };
            this.getHirerList();
            vm.showAlert(snackbarData);
          } else {
            this.hirerList = vm.originalHirerList;
          }
        })
        .catch((addEditError) => {
          vm.isLoading = false;
          this.hirerList = vm.originalHirerList;
          vm.handleAddUpdateIntegrationStatusError(addEditError);
        });
    },
    async getHirerList() {
      let vm = this;
      vm.hirerListLoading = true;
      await vm.$apollo
        .query({
          query: GET_HIRER_LIST,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            status: "All",
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getJobStreetHirerList &&
            response.data.getJobStreetHirerList.hirerList &&
            response.data.getJobStreetHirerList.hirerList.length > 0
          ) {
            this.hirerList = response.data.getJobStreetHirerList.hirerList;
            this.originalHirerList =
              response.data.getJobStreetHirerList.hirerList;
          } else {
            this.hirerList = [];
            this.originalHirerList = [];
          }
          vm.hirerListLoading = false;
        })
        .catch((err) => {
          this.hirerList = [];
          this.originalHirerList = [];
          vm.hirerListLoading = false;
          vm.handleAddUpdateIntegrationStatusError(err);
        });
    },

    async submitForm() {
      let { valid } = await this.$refs.integrationForm.validate();
      if (valid) {
        if (
          this.CompanyName &&
          this.CompanyName.toLowerCase() === "jobstreet" &&
          this.overlay
        ) {
          this.getSeekToken();
        } else {
          this.addUpdateRecruitmentStatus("Active");
        }
      }
    },
    addUpdateRecruitmentStatus(status) {
      this.isLoading = true;
      let integrationId = 0;
      if (this.companyObject && this.companyObject.Integration_Id) {
        integrationId = this.companyObject.Integration_Id;
      }
      let vm = this;
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_RECRUITMENT_STATUS,
          variables: {
            Integration_Id: integrationId,
            Integration_Type:
              this.CompanyName.toLowerCase() === "jobstreet"
                ? "seek"
                : this.CompanyName,
            Integration_Status: status,
            Company_Id:
              this.CompanyName.toLowerCase() === "jobstreet" ||
              this.CompanyName.toLowerCase() === "indeed"
                ? ""
                : this.companyId,
            Indeed_Source_Name:
              this.CompanyName.toLowerCase() === "indeed"
                ? this.sourceName
                : "",
          },
          client: "apolloClientAH",
        })
        .then((res) => {
          if (res) {
            let snackbarData = {
              isOpen: true,
              message: "Integration status updated successfully.",
              type: "success",
            };
            this.showAlert(snackbarData);
          }
          this.isLoading = false;
          this.overlay = false;
          this.$emit("refetch");
          if (
            this.CompanyName.toLowerCase() === "jobstreet" &&
            status?.toLowerCase() === "active"
          ) {
            this.openOverlay();
          }
        })
        .catch((err) => {
          this.isLoading = false;
          vm.handleAddUpdateIntegrationStatusError(err);
        });
    },
    getSeekToken() {
      this.isLoading = true;
      this.$apollo
        .query({
          query: GET_SEEK_TOKEN,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            form_Id: 15,
            isPublish: 1,
          },
        })
        .then(async (response) => {
          this.isLoading = false;
          if (
            response &&
            response.data &&
            response.data.getAuthTokenJobStreet &&
            response.data.getAuthTokenJobStreet.getData &&
            response.data.getAuthTokenJobStreet.getData.accessToken
          ) {
            const token = response.data.getAuthTokenJobStreet.getData;
            if (token && token.accessToken) {
              const accessToken = JSON.parse(token.accessToken);
              await window.$cookies.set(
                "jobStreet_access_token",
                accessToken.access_token,
                accessToken.expires_in
              );
              if (window.$cookies.get("jobStreet_access_token")) {
                this.verifySeekIntegration();
              }
            }
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.handleAddUpdateIntegrationStatusError(err);
        });
    },
    verifySeekIntegration() {
      this.isLoading = true;
      let vm = this;
      vm.$apollo
        .mutate({
          mutation: VERIFY_JOB_STREET,
          variables: {
            companyId: parseInt(this.companyId),
            form_Id: 15,
          },
          client: "apolloClientAY",
        })
        .then((res) => {
          if (res && res.data) {
            this.overlay = false;
            this.isLoading = false;
            let snackbarData = {
              isOpen: true,
              message: `Job Details verified successfully.`,
              type: "success",
            };
            this.showAlert(snackbarData);
          }
        })
        .catch((err) => {
          this.isLoading = false;
          vm.handleUpdateJobDetailsError(err);
        });
    },

    handleUpdateJobDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "job details",
        isListError: false,
      });
    },

    handleAddUpdateIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "integration status",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    fetchIntegrationStatus() {
      this.sourceName = this.getStatusByType("indeed")?.Indeed_Source_Name;
      for (const integration of this.statusArray) {
        if (
          integration.Integration_Type === this.CompanyName.toLowerCase() ||
          (integration.Integration_Type === "seek" &&
            this.CompanyName === "JobStreet")
        ) {
          this.companyObject = integration;
          this.companyId = integration.Company_Id;
          this.sourceName = integration.Indeed_Source_Name;
          this.configureSwitch = integration.Integration_Status === "Active";
          break;
        }
        if (
          integration.Integration_Type === "seek" &&
          this.CompanyName === "JobStreet"
        ) {
          this.companyObject = integration;
          this.companyId = null;
          this.sourceName = "";
          this.configureSwitch = integration.Integration_Status === "Active";
          break;
        }
      }
      if (!this.companyObject) {
        this.companyObject = {
          Integration_Id: 0,
        };
      }
    },
    getStatusByType(integrationType) {
      // Find the object with the specified Integration_Type
      const integration = this.getIntegrationStatus.find(
        (item) =>
          item.Integration_Type.toLowerCase() === integrationType.toLowerCase()
      );
      // If the integration is found, return its Integration_Status
      if (integration) {
        return integration;
      }
    },
  },
};
</script>

<style lang="scss">
#overlay-card {
  height: 100%;
  width: 100%;
  background: white;
}
#integration-form > .v-overlay__content {
  height: 100%;
  width: 500px;
}
@media screen and (max-width: 960px) {
  #integration-form > .v-overlay__content {
    width: 100% !important;
  }
}
@media screen and (max-width: 600px) {
  .job-street-form-overlay > .v-overlay__content {
    width: 100% !important;
  }
}
@media screen and (min-width: 601px) {
  .job-street-form-overlay > .v-overlay__content {
    width: 600px !important;
  }
}
#overlay-head {
  min-height: 7%;
  height: auto;
  width: 100%;
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
#overlay-body {
  padding: 15px;
  overflow-y: scroll !important;
}
.link-tab > a {
  text-decoration: none;
  color: blue;
}
.same-as-vue-tel > input:focus {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
}
</style>
