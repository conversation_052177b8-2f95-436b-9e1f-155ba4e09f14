<template>
  <v-overlay
    :model-value="showModal"
    class="d-flex justify-center align-center"
    persistent
  >
    <v-card class="preview-card rounded-lg" max-width="800" width="100%">
      <v-card-title
        class="d-flex bg-primary justify-space-between align-center overlay-head"
      >
        <div class="text-h6 text-medium ps-2 truncate">Job Post Preview</div>
        <v-btn icon class="clsBtn" variant="text" @click="closeModal">
          <v-icon>fas fa-times</v-icon>
        </v-btn>
      </v-card-title>

      <v-card-text class="pa-0">
        <div class="preview-content">
          <!-- Job Title and Company -->
          <div class="job-header pa-4 border-b">
            <h2 class="text-h5 font-weight-bold mb-2">
              {{ jobData.Job_Post_Name || "Job Title" }}
            </h2>
            <div class="company-info d-flex align-center mb-2">
              <v-icon class="mr-2" color="primary">fas fa-building</v-icon>
              <span class="text-subtitle-1">{{
                jobData.Service_Provider_Name
              }}</span>
            </div>
            <div class="location-info d-flex align-center mb-2">
              <v-icon class="mr-2" color="primary"
                >fas fa-map-marker-alt</v-icon
              >
              <span class="text-subtitle-1">{{ jobLocation }}</span>
            </div>
            <div class="salary-info d-flex align-center">
              <v-icon class="mr-2" color="primary">fas fa-dollar-sign</v-icon>
              <span class="text-subtitle-1">{{ formattedSalary }}</span>
            </div>
          </div>

          <!-- Profile Insights -->
          <div class="profile-insights pa-4 border-b">
            <h3 class="text-h6 mb-3">Profile insights</h3>
            <!-- Skills Section -->
            <div class="skills-section mb-4" v-if="jobData?.Skill_Set?.length">
              <div class="d-flex align-center mb-2">
                <v-icon class="mr-2" color="primary" size="x-small"
                  >fas fa-cogs</v-icon
                >
                <span class="font-weight-medium">Skills</span>
              </div>
              <div class="skills-chips">
                <v-chip
                  v-for="skill in jobData.Skill_Set"
                  :key="skill"
                  class="ma-1"
                  color="primary"
                  variant="outlined"
                  size="small"
                >
                  {{ skill }}
                </v-chip>
              </div>
            </div>

            <!-- Education Section -->
            <div
              class="education-section mb-4"
              v-if="jobData.Qualification && jobData.Qualification.length"
            >
              <div class="d-flex align-center mb-2">
                <v-icon class="mr-2" color="primary"
                  >fas fa-graduation-cap</v-icon
                >
                <span class="font-weight-medium">Education</span>
              </div>
              <div class="education-chips">
                <v-chip
                  v-for="qual in jobData.Qualification"
                  :key="qual.Qualification_Id"
                  class="ma-1"
                  color="success"
                  variant="outlined"
                  size="small"
                >
                  {{ qual.Qualification }}
                </v-chip>
              </div>
            </div>
          </div>

          <!-- Job Details -->
          <div class="job-details pa-4 border-b">
            <h3 class="text-h6 mb-3">Job details</h3>

            <!-- Job type -->
            <div class="detail-item mb-3" v-if="jobData.Job_Type">
              <div class="d-flex align-center mb-1">
                <v-icon class="mr-2" color="primary">fas fa-briefcase</v-icon>
                <span class="font-weight-medium">Job type</span>
              </div>
              <v-chip color="primary" variant="outlined" size="small">
                {{ jobData.Job_Type }}
              </v-chip>
            </div>
            <div class="detail-item mb-3">
              <div class="d-flex align-center mb-1">
                <span class="font-weight-medium">Number of Vacancies</span>
              </div>
              <span class="pl-2"> {{ jobData.No_Of_Vacancies }}</span>
            </div>
            <div v-if="jobData?.Closing_Date" class="detail-item mb-3">
              <div class="d-flex align-center mb-1">
                <span class="font-weight-medium">Closing Date</span>
              </div>
              <span> {{ formatDate(jobData.Closing_Date) }}</span>
            </div>
          </div>

          <!-- Platform Specific Details -->
          <div
            class="platform-details pa-4 border-b"
            v-if="hasPlatformSpecificData"
          >
            <h3 class="text-h6 mb-3">{{ platformName }} Specific Details</h3>

            <!-- Indeed Specific Fields -->
            <div v-if="isIndeed" class="indeed-details">
              <div class="detail-row mb-3" v-if="platformData.contactType">
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary">fas fa-user-tie</v-icon>
                  <span class="font-weight-medium">Contact Type</span>
                </div>
                <v-chip color="info" variant="outlined" size="small">
                  {{ platformData.contactType }}
                </v-chip>
              </div>
              <div
                class="detail-row mb-3"
                v-if="platformData.cityName && platformData.stateName"
              >
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary"
                    >fas fa-map-marker-alt</v-icon
                  >
                  <span class="font-weight-medium">Location</span>
                </div>
                <span class="text-body-2">
                  {{ platformData.cityName }},
                  {{ platformData.stateName }}
                </span>
              </div>

              <div class="detail-row mb-3" v-if="platformData.emailAddress">
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary">fas fa-envelope</v-icon>
                  <span class="font-weight-medium">Contact Email</span>
                </div>
                <span class="text-body-2">{{ platformData.emailAddress }}</span>
              </div>

              <div class="detail-row mb-3" v-if="platformData.contactName">
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary">fas fa-user</v-icon>
                  <span class="font-weight-medium">Contact Name</span>
                </div>
                <span class="text-body-2">{{ platformData.contactName }}</span>
              </div>

              <div
                class="detail-row mb-3"
                v-if="platformData.selectedWorkPlace"
              >
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary">fas fa-home</v-icon>
                  <span class="font-weight-medium">Remote Type</span>
                </div>
                <v-chip color="success" variant="outlined" size="small">
                  {{ platformData.selectedWorkPlace }}
                </v-chip>
              </div>

              <div class="detail-row mb-3" v-if="platformData.benefits">
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary">fas fa-gift</v-icon>
                  <span class="font-weight-medium">Benefits</span>
                </div>
                <p class="text-body-2 mt-1">{{ platformData.benefits }}</p>
              </div>
            </div>

            <!-- LinkedIn Specific Fields -->
            <div v-if="isLinkedIn" class="linkedin-details">
              <div class="detail-item mb-3">
                <div class="d-flex align-center mb-1">
                  <span class="font-weight-medium">Apply Url</span>
                </div>
                <a :href="applyUrl" target="_blank">{{ applyUrl }}</a>
              </div>
              <div
                v-if="platformData.linkedInCompanyId"
                class="detail-item mb-3"
              >
                <div class="d-flex align-center mb-1">
                  <span class="font-weight-medium">Company Id</span>
                </div>
                <span class="pl-2"> {{ platformData.linkedInCompanyId }}</span>
              </div>
              <div class="detail-row mb-3" v-if="platformData.experienceLevel">
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary"
                    >fas fa-chart-line</v-icon
                  >
                  <span class="font-weight-medium">Experience Level</span>
                </div>
                <v-chip color="primary" variant="outlined" size="small">
                  {{
                    getExperienceLevelDescription(platformData.experienceLevel)
                  }}
                </v-chip>
              </div>

              <div class="detail-row mb-3" v-if="platformData.jobCode">
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary">fas fa-code</v-icon>
                  <span class="font-weight-medium">Job Code</span>
                </div>
                <v-chip color="info" variant="outlined" size="small">
                  {{ getJobCodeDescription(platformData.jobCode) }}
                </v-chip>
              </div>

              <div
                class="detail-row mb-3"
                v-if="platformData.industryCodeCategory"
              >
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary">fas fa-industry</v-icon>
                  <span class="font-weight-medium">Industry Code Category</span>
                </div>
                <v-chip color="success" variant="outlined" size="small">
                  {{
                    getIndustryCodeCategoryDescription(
                      platformData.industryCodeCategory
                    )
                  }}
                </v-chip>
              </div>

              <div class="detail-row mb-3" v-if="platformData.industryCode">
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary">fas fa-building</v-icon>
                  <span class="font-weight-medium">Industry Code</span>
                </div>
                <v-chip color="warning" variant="outlined" size="small">
                  {{ getIndustryCodeDescription(platformData.industryCode) }}
                </v-chip>
              </div>

              <div class="detail-row mb-3" v-if="platformData.workPlace">
                <div class="d-flex align-center mb-1">
                  <v-icon class="mr-2" color="primary"
                    >fas fa-map-marker-alt</v-icon
                  >
                  <span class="font-weight-medium">Workplace Type</span>
                </div>
                <v-chip color="purple" variant="outlined" size="small">
                  {{ getWorkPlaceDescription(platformData.workPlace) }}
                </v-chip>
              </div>
            </div>
          </div>

          <!-- Full Job Description -->
          <div class="job-description pa-4">
            <h3 class="text-h6 mb-3">Full job description</h3>
            <div>
              <div ref="editorView" class="quill-editorView"></div>
            </div>
          </div>
        </div>
      </v-card-text>

      <!-- Fixed Update Button -->
      <div
        style="
          position: fixed;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 1000;
        "
      >
        <v-btn
          color="primary"
          variant="elevated"
          size="large"
          block
          @click="handleUpdate"
          :loading="loading"
        >
          Publish
        </v-btn>
      </div>
    </v-card>
  </v-overlay>
</template>

<script>
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
import moment from "moment";
export default {
  name: "JobPostPreviewModal",
  emits: ["close-modal", "update-job"],
  props: {
    showModal: {
      type: Boolean,
      default: false,
    },
    applyUrl: {
      type: String,
      required: true,
    },
    jobData: {
      type: Object,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    platformName: {
      type: String,
      required: true,
    },
    platformData: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    jobLocation() {
      if (this.jobData?.JobLocations[0]?.Location_Name) {
        return this.jobData.JobLocations[0]?.Location_Name;
      } else {
        const { City_Name, State_Name, Country_Name } = this.jobData || {};
        let jobLocation = [City_Name, State_Name, Country_Name]
          .filter(Boolean)
          .join(", ");
        return jobLocation || "Location not specified";
      }
    },
    formattedSalary() {
      const currency = this.jobData.Currency || "";
      const minSalary = this.jobData.Min_Payment_Frequency;
      const maxSalary = this.jobData.Max_Payment_Frequency;
      const payType = this.jobData.Pay_Type || "month";

      if (minSalary && maxSalary) {
        return `${currency} ${minSalary} - ${maxSalary} / ${payType}`;
      } else if (minSalary) {
        return `From ${currency} ${minSalary} / ${payType}`;
      } else if (maxSalary) {
        return `Up to ${currency} ${maxSalary} / ${payType}`;
      }
      return "Salary not specified";
    },
    formatDate() {
      return (date) => {
        if (date) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return moment(date).format(orgDateFormat);
        } else return "-";
      };
    },
    isIndeed() {
      return this.platformName?.toLowerCase() === "indeed";
    },
    isLinkedIn() {
      return this.platformName?.toLowerCase() === "linkedin";
    },
    hasPlatformSpecificData() {
      return (
        (this.isIndeed &&
          (this.platformData.contactType ||
            this.platformData.emailAddress ||
            this.platformData.contactName ||
            this.platformData.selectedWorkPlace ||
            this.platformData.screeningForm ||
            this.platformData.benefits ||
            this.platformData.updateLocation)) ||
        (this.isLinkedIn &&
          (this.platformData.experienceLevel ||
            this.platformData.jobCode ||
            this.platformData.industryCodeCategory ||
            this.platformData.industryCode ||
            this.platformData.workPlace))
      );
    },
  },
  mounted() {
    this.initQuillEditor();
  },
  watch: {
    showModal(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.initQuillEditor();
        });
      }
    },
  },
  methods: {
    closeModal() {
      this.$emit("close-modal");
    },
    handleUpdate() {
      this.$emit("update-job");
    },
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editorView, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.quill.root.innerHTML = this.jobData.Job_Description
        ? this.convertEmojiCodepointsToEmojis(this.jobData.Job_Description)
        : "";
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
      });
      this.quill.enable(false);
    },
    convertEmojiCodepointsToEmojis(text) {
      return text.replace(/\[EMOJI:([0-9a-f-]+)\]/gi, (match, codePoints) => {
        // Split by dash if there are multiple code points
        const codePointArray = codePoints.split("-");

        // Convert each hex code point back to a character and join them
        const emoji = codePointArray
          .map((hex) => String.fromCodePoint(parseInt(hex, 16)))
          .join("");

        return emoji;
      });
    },
    setEditorFontSize(fontSize) {
      if (this.$refs.editorView) {
        const editorElement = this.$refs.editorView.querySelector(".ql-editor");
        if (editorElement) {
          editorElement.style.fontSize = fontSize;
        }
      }
    },
    getExperienceLevelDescription(code) {
      // This would typically come from the parent component's options
      // For now, return the code itself
      return code || "Not specified";
    },
    getJobCodeDescription(code) {
      return code || "Not specified";
    },
    getIndustryCodeCategoryDescription(code) {
      return code || "Not specified";
    },
    getIndustryCodeDescription(code) {
      return code || "Not specified";
    },
    getWorkPlaceDescription(code) {
      return code || "Not specified";
    },
  },
};
</script>

<style scoped>
:deep(.v-overlay__content) {
  display: flex;
  justify-content: center;
  width: 100%;
}
.preview-card {
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-content {
  overflow-y: auto;
  max-height: calc(90vh - 120px);
  padding-bottom: 80px;
}

.border-b {
  border-bottom: 1px solid #e0e0e0;
}

.job-header {
  background-color: #f8f9fa;
}

.skills-chips,
.education-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.quill-editorView {
  height: auto;
}
::v-deep .ql-toolbar.ql-snow {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
::v-deep .ql-editor {
  padding: 0px;
}
</style>
