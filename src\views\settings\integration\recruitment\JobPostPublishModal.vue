<template>
  <div>
    <v-skeleton-loader v-if="loading"></v-skeleton-loader>
    <div v-else>
      <v-tooltip
        text="You do not have access to perform this action"
        location="top"
      >
        <template v-slot:activator="{ props }">
          <v-card variant="plain" v-bind="!formAccess.add ? props : {}">
            <v-switch
              v-if="
                name?.toLowerCase() === 'linkedin' &&
                (linkedInStatus === null ||
                  linkedInStatus?.toLowerCase() === 'deleted')
              "
              color="primary"
              class="ml-2"
              v-model="configureSwitch"
              :disabled="!formAccess.add"
              @click="
                overlay = !configureSwitch;
                overlayFunction();
              "
              :readonly="
                formAccess && (formAccess.add || formAccess.update)
                  ? false
                  : true
              "
            ></v-switch>
          </v-card>
        </template>
      </v-tooltip>
      <v-tooltip
        text="You do not have access to perform this action"
        location="top"
      >
        <template v-slot:activator="{ props }">
          <v-card variant="plain" v-bind="!formAccess.add ? props : {}">
            <v-switch
              v-if="
                name?.toLowerCase() === 'indeed' &&
                (indeedStatus === null ||
                  indeedStatus?.toLowerCase() === 'deleted')
              "
              color="primary"
              :disabled="!formAccess.add"
              @click="checkAuth"
              v-model="configureSwitch"
              class="ml-2"
              :readonly="
                formAccess && (formAccess.add || formAccess.update)
                  ? false
                  : true
              "
            ></v-switch>
          </v-card>
        </template>
      </v-tooltip>
      <span
        v-if="
          name?.toLowerCase() === 'jobstreet' &&
          !jobPostLoading &&
          !jobStreetModel
        "
      >
        <v-tooltip
          text="You do not have access to perform this action"
          location="top"
        >
          <template v-slot:activator="{ props }">
            <v-card variant="plain" v-bind="!formAccess.add ? props : {}">
              <v-switch
                v-if="
                  (editJobData && editJobData.length === 0) ||
                  (jobStreetStatus && jobStreetStatus === 'closed')
                "
                color="primary"
                @click="enableJobStreet()"
                class="ml-2"
                :disabled="!formAccess.add"
                v-model="jobStreetModel"
                :readonly="
                  formAccess && (formAccess.add || formAccess.update)
                    ? false
                    : true
                "
              ></v-switch>
            </v-card>
          </template>
        </v-tooltip>
      </span>
      <ActionMenu
        v-if="
          name?.toLowerCase() === 'linkedin' &&
          (linkedInStatus?.toLowerCase() === 'published' ||
            linkedInStatus?.toLowerCase() === 'yet to publish')
        "
        :actions="moreActions"
        :accessRights="checkAccess()"
        @selected-action="onMoreAction"
      >
      </ActionMenu>
      <ActionMenu
        v-if="
          name?.toLowerCase() === 'indeed' &&
          indeedStatus?.toLowerCase() == 'published'
        "
        :actions="moreActions"
        :accessRights="checkAccess()"
        @selected-action="onMoreAction"
      >
      </ActionMenu>
      <ActionMenu
        v-if="
          name?.toLowerCase() === 'jobstreet' &&
          editJobData &&
          editJobData.length &&
          !jobStreetModel &&
          !jobPostLoading &&
          jobStreetStatus !== 'closed'
        "
        :actions="moreActions"
        :accessRights="checkAccess()"
        @selected-action="onMoreAction"
      >
      </ActionMenu>
    </div>
    <template>
      <div class="text-center">
        <v-form ref="jobPostForm">
          <v-overlay
            v-model="overlay"
            class="d-flex justify-end"
            id="job-form"
            @click:outside="openConfirmationModel = true"
            persistent
          >
            <div class="overlay-card">
              <div
                :class="
                  windowWidth < 770
                    ? ' d-flex bg-white justify-end align-center'
                    : 'overlay-head text-white pa-2'
                "
              >
                <span v-if="windowWidth >= 770">
                  Publish Job Title: {{ jobName }} to {{ name }}</span
                >
                <v-card v-else class="d-flex justify-end pa-2 fixed-title">
                  <div>
                    <v-btn
                      class="px-8 rounded-lg mr-2"
                      @click="openConfirmationModel = true"
                      variant="outlined"
                      color="primary"
                      elevation="4"
                    >
                      Cancel
                    </v-btn>
                    <v-btn
                      @click="openPreviewModal()"
                      class="px-8"
                      variant="elevated"
                      rounded="lg"
                      color="primary"
                      >Preview</v-btn
                    >
                  </div>
                </v-card>
                <v-btn
                  v-if="windowWidth >= 770"
                  icon="fas fa-times"
                  variant="text"
                  @click="openConfirmationModel = true"
                  color="white"
                ></v-btn>
              </div>
              <div class="overlay-body">
                <v-card
                  v-if="name?.toLowerCase() === 'linkedin'"
                  color="blue-lighten-3 text-blue-darken-4 d-flex align-center px-5 mb-5"
                  style="height: 40px"
                  ><v-icon>fas fa-info-circle</v-icon> &ensp; This job will be
                  published on {{ name }} in
                  {{ publisAfterHours }} hours</v-card
                >
                <span class="text-h6"
                  >These are the additional fields required for job posting on
                  {{ name }}.</span
                >
                <div
                  id="job-options-card"
                  class="d-flex flex-wrap align-start mt-5"
                >
                  <v-row>
                    <v-col
                      v-if="name?.toLowerCase() === 'linkedin'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        label="Workplace Type"
                        :items="workPlaceOptions"
                        item-title="Description"
                        item-value="Code"
                        :itemSelected="workPlace"
                        @selected-item="changeFieldValue($event, 'workPlace')"
                        :rules="[required('Work Place', workPlace)]"
                        :isRequired="true"
                        :loading="isDropDownLoading"
                      ></CustomSelect>
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'indeed'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        itemValue="key"
                        itemTitle="value"
                        label="Contact Type"
                        :items="dropdownContactType"
                        :isAutoComplete="true"
                        variant="solo"
                        clearable
                        :itemSelected="selectedContactType"
                        @selected-item="selectedContactType = $event"
                      ></CustomSelect>
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'indeed'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <v-text-field
                        v-model="emailAddress"
                        variant="solo"
                        :rules="[
                          required('Email Address', emailAddress),
                          emailValidation('Email Address', emailAddress),
                        ]"
                      >
                        <template v-slot:label>
                          Email Address<span style="color: red">*</span>
                        </template></v-text-field
                      >
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'indeed'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <v-text-field
                        v-model="contactName"
                        :counter="50"
                        :rules="[
                          required('Contact Name', contactName),
                          validateWithRulesAndReturnMessages(
                            contactName,
                            'empFirstName',
                            'Contact Name'
                          ),
                        ]"
                        variant="solo"
                      >
                        <template v-slot:label>
                          Contact Name<span style="color: red">*</span>
                        </template></v-text-field
                      >
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'indeed'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        itemValue="templateId"
                        itemTitle="templateName"
                        label="Screening Questions Form"
                        :items="formGetList"
                        :isAutoComplete="true"
                        variant="solo"
                        :selectProperties="{
                          clearable: true,
                        }"
                        :isLoading="formGetLoader"
                        :itemSelected="selectedIndeedTemplate"
                        @selected-item="selectedIndeedTemplate = $event"
                      ></CustomSelect>
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'indeed'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        label="Remote Type"
                        :items="remoteTypeList"
                        :itemSelected="selectedWorkPlace"
                        :selectProperties="{
                          clearable: true,
                        }"
                        @selected-item="selectedWorkPlace = $event"
                      ></CustomSelect>
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'indeed'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        :items="cityList"
                        :isAutoComplete="true"
                        label="City"
                        :is-required="
                          selectedWorkPlace?.toLowerCase() == 'fully remote'
                            ? false
                            : true
                        "
                        :itemSelected="selectedCityId"
                        :isLoading="cityListLoading"
                        itemValue="City_Id"
                        itemTitle="City_Name"
                        variant="solo"
                        :rules="[
                          selectedWorkPlace?.toLowerCase() == 'fully remote'
                            ? true
                            : required('City', selectedCityId),
                        ]"
                        ref="city"
                        :disabled="isLocationPresent"
                        @selected-item="selectedCityId = $event"
                      ></CustomSelect>
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'indeed'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        :items="stateList"
                        :isAutoComplete="true"
                        label="State"
                        :is-required="
                          selectedWorkPlace?.toLowerCase() == 'fully remote'
                            ? false
                            : true
                        "
                        :itemSelected="selectedStateId"
                        itemValue="State_Id"
                        itemTitle="State_Name"
                        :rules="[
                          selectedWorkPlace?.toLowerCase() == 'fully remote'
                            ? true
                            : required('State', selectedStateId),
                        ]"
                        variant="solo"
                        ref="state"
                        :disabled="isLocationPresent"
                        @selected-item="selectedStateId = $event"
                      ></CustomSelect>
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'indeed'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <div class="d-flex">
                        <span class="v-label pr-3 pb-5"
                          >Update location to Jobpost also?</span
                        >
                        <v-switch
                          v-model="updateLocation"
                          color="primary"
                          true-value="Yes"
                          false-value="No"
                        />
                      </div>
                    </v-col>
                    <v-col v-if="name?.toLowerCase() === 'indeed'" cols="12">
                      <v-textarea
                        v-model="benefits"
                        rows="1"
                        variant="solo"
                        :counter="500"
                        :rules="[
                          required('Benefits', benefits),
                          alphaNumSpaceNewLineWithElevenSymbolValidation(
                            benefits
                          ),
                          minLengthValidation('Benefits', benefits, 10),
                          maxLengthValidation('Benefits', benefits, 500),
                        ]"
                        ><template v-slot:label>
                          Benefits<span style="color: red">*</span>
                        </template>
                      </v-textarea>
                    </v-col>
                    <!-- For future use -->
                    <!-- <v-col
                      v-if="name?.toLowerCase() === 'indeed'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                      style="visibility: hidden"
                    >
                      <VueTelInput
                        class="pa-2"
                        v-model="mobileNumber"
                        :preferred-countries="['IN', 'PH', 'US', 'AU']"
                        :error="!mobileNumberValidation"
                        error-color="#E53935"
                        valid-color="#9E9E9E"
                        :defaultCountry="mobileNoCountryCode"
                        :autoDefaultCountry="true"
                        mode="national"
                        @validate="validateMobileNumber"
                        @country-changed="getCountryCode($event)"
                        :valid-characters-only="true"
                      ></VueTelInput>
                      <span
                        style="
                          color: rgb(var(--v-theme-error));
                          font-size: 12px;
                          padding-left: 14px;
                        "
                        >{{ mobileNumberValidation }}</span
                      >
                    </v-col> -->
                    <v-col
                      v-if="name?.toLowerCase() === 'linkedin'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        label="Experience Level"
                        :items="experienceLevelOptions"
                        item-title="Description"
                        item-value="Code"
                        :itemSelected="experienceLevel"
                        @selected-item="
                          changeFieldValue($event, 'experienceLevel')
                        "
                        :rules="[required('Experience Level', experienceLevel)]"
                        :isRequired="true"
                        :loading="isDropDownLoading"
                      ></CustomSelect>
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'linkedin'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        label="Job Codes"
                        :items="jobCodesOptions"
                        item-title="Description"
                        item-value="Code"
                        :itemSelected="jobCode"
                        @selected-item="changeFieldValue($event, 'jobCode')"
                        :rules="[required('Job Code', jobCode)]"
                        :isRequired="true"
                        :loading="isDropDownLoading"
                      ></CustomSelect>
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'linkedin'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        label="Industry Code Category"
                        :items="industryCodeCategoryOptions"
                        item-title="Description"
                        item-value="Industry_Id"
                        :itemSelected="industryCodeCategory"
                        @selected-item="
                          changeFieldValue($event, 'industryCodeCategory')
                        "
                        :rules="[
                          required(
                            'Industry Code Category',
                            industryCodeCategory
                          ),
                        ]"
                        :isRequired="true"
                        :loading="isDropDownLoading"
                      ></CustomSelect>
                    </v-col>
                    <v-col
                      v-if="name?.toLowerCase() === 'linkedin'"
                      xl="6"
                      lg="6"
                      md="6"
                      sm="12"
                      cols="12"
                    >
                      <CustomSelect
                        label="Industry Codes"
                        :items="industryCodeOptions"
                        item-title="Description"
                        item-value="Industry_Id"
                        :itemSelected="industryCode"
                        @selected-item="
                          changeFieldValue($event, 'industryCode')
                        "
                        :loading="isDropDownLoading"
                      ></CustomSelect>
                    </v-col>
                  </v-row>
                </div>
                <div v-if="name?.toLowerCase() === 'linkedin'">
                  <h3 class="mb-3">Note:</h3>
                  <p class="mb-3">
                    These job posts are published from our side to Linkedin.
                    Through XML feed documentation.
                  </p>
                  <p class="mb-3">
                    All jobs posted through Linkedin XML feed are not guaranted
                    to be posted in Linkedin as Linkedin has control over it.
                  </p>
                  <p class="mb-3">
                    If your company has turned on the integration to push this
                    job to Linked in but you have already posted the same job
                    through Linkedin's Limited or Premium flow, the job will be
                    de-duped by title, location, company name and job posting
                    ID.
                  </p>
                  <p class="mb-3">
                    Job posting coming through XML feed will have a lower
                    priority than Premium(paid) jobs if de-duped to a Premium
                    posting, and thus the Premium posting will remain even
                    through Limited(free) job was de-duped.
                  </p>
                  <p>
                    Learn more about XML feed
                    <a
                      href="https://learn.microsoft.com/en-us/linkedin/talent/job-postings/xml-feeds"
                      target="__blank"
                      class="text-decoration-none"
                      >here</a
                    >.
                  </p>
                </div>
              </div>
              <v-card
                v-if="windowWidth >= 770"
                class="overlay-footer pa-2"
                elevation="16"
              >
                <div>
                  <v-btn
                    class="px-8 rounded-lg mr-2"
                    @click="openConfirmationModel = true"
                    variant="outlined"
                    color="primary"
                    elevation="4"
                  >
                    Cancel
                  </v-btn>
                  <v-btn
                    @click="openPreviewModal()"
                    class="px-8"
                    variant="elevated"
                    rounded="lg"
                    color="primary"
                    >Preview</v-btn
                  >
                </div>
              </v-card>
            </div>

            <AppLoading v-if="loading || toggleLoading"></AppLoading>
          </v-overlay>
        </v-form>
      </div>
    </template>
    <!-- </v-btn> -->
    <AppSnackBar
      v-if="showError"
      :show-snack-bar="showError"
      snack-bar-type="warning"
      timeOut="2"
      @close-snack-bar="closeValidationAlert"
    ></AppSnackBar>
    <AppWarningModal
      v-if="openCloseJobWarningModal"
      :open-modal="openCloseJobWarningModal"
      confirmation-heading="Are you sure to close the jobpost?"
      icon-name="far fa-times-circle"
      @close-warning-modal="openCloseJobWarningModal = false"
      @accept-modal="validateCloseJobToIndeed()"
    ></AppWarningModal>
  </div>
  <AppLoading v-if="isLoading || toggleLoading || jobPostLoading"></AppLoading>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    confirmation-heading="Are you sure to close this job?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="getSeekTokenAndCloseJobStreet('close')"
  >
  </AppWarningModal>
  <AppWarningModal
    v-if="openConfirmationModel"
    :open-modal="openConfirmationModel"
    :confirmation-heading="`Are you sure to exit ${name} form?`"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationModel = false"
    @accept-modal="closeIntegrationForm()"
  >
  </AppWarningModal>
  <JobStreet
    v-if="jobStreetModel"
    :editJobData="jobStreetStatus === 'closed' ? [] : editJobData"
    :jobStreetModel="jobStreetModel"
    @refresh-job-details="refreshList()"
    @on-close="jobStreetModel = false"
    :jobPostId="jobPostId"
    :jobPostData="jobPostData"
  />
  <JobPostPreviewModal
    v-if="showPreviewModal"
    :showModal="showPreviewModal"
    :jobData="jobPostData"
    :loading="loading"
    :platformName="name"
    :platformData="platformSpecificData"
    :applyUrl="applyUrl"
    @close-modal="closePreviewModal"
    @update-job="handlePreviewUpdate"
  />
</template>
<script>
import validationRules from "@/mixins/validationRules";
import moment from "moment";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import Config from "@/config.js";
// import { VueTelInput } from "vue-tel-input";
import { GET_FORM_BUILDER } from "@/graphql/commonQueries";
import {
  GET_LINKEDIN_JOB_OPTIONS,
  RETRIVE_LINKEDIN_JOB_DETAILS,
  ADD_UPDATE_LINKED_IN_JOB_DETAILS,
  DELETE_LINKEDIN_JOB_POST,
} from "@/graphql/settings/Integration/publishJobToLinkedin";
import {
  GET_INDEED_AUTH_CREDENTIALS,
  GET_INDEED_AUTH_TOKEN,
  RETRIEVE_INDEED_JOBPOST_DETAILS,
  PUBLISH_JOBPOST_TO_INDEED,
  CLOSE_JOBPOST_TO_INDEED,
} from "@/graphql/recruitment/job-post/indeedIntegrationQueries.js";
import { GET_INTEGRATION_STATUS } from "@/graphql/settings/irukka-integration/irukkaRegistrationQueries.js";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import JobStreet from "../JobStreet.vue";
import JobPostPreviewModal from "./JobPostPreviewModal.vue";
import {
  CLOSE_SEEK_INTEGRATION,
  GET_PROFILE_POSITION,
  GET_SEEK_TOKEN,
  JOB_STREET_JOB_DETAILS,
} from "@/graphql/recruitment/job-seek/jobStreetQueries";

export default {
  name: "JobPostPublishModal",
  mixins: [validationRules],
  components: {
    CustomSelect,
    JobStreet,
    ActionMenu,
    JobPostPreviewModal,
    // VueTelInput,
  },
  data: () => {
    return {
      overlay: false,
      showError: false,
      loading: false,
      isDropDownLoading: false,
      jobName: "",
      publisAfterHours: 6,
      intergrationId: 0,
      workPlace: null,
      experienceLevel: null,
      jobCode: null,
      industryCode: null,
      industryCodeCategory: null,
      industryOptions: [],
      industryCodeCategoryOptions: [],
      industryCodeOptions: [],
      workPlaceOptions: [],
      experienceLevelOptions: [],
      jobCodesOptions: [],
      errorMessages: [],
      configureSwitch: false,
      emailAddress: null,
      selectedContactType: "",
      contactName: "",
      selectedIndeedTemplate: null,
      benefits: "",
      countryName: "",
      cityRegionPostal: "",
      mobileNumber: null,
      // mobileNoCountryCode: 0,
      mobileNumberCountrycode: null,
      toggleLoading: false,
      openMoreMenu: false,
      indeedStatus: null,
      linkedInStatus: null,
      jobStreetStatus: null,
      indeedIntegraionId: 0,
      indeedSourceId: "",
      orgCode: "",
      industryCodeCategoryName: "",
      industryCodeName: "",
      jobCodeName: "",
      experienceLevelName: "",
      dropdownContactType: [
        { key: "recruiter", value: "Recruiter" },
        { key: "hiring manager", value: "Hiring Manager" },
        { key: "user", value: "User" },
      ],
      formGetList: [],
      formGetLoader: false,
      clientId: "",
      secretKey: "",
      sourceName: "",
      isValidMobileNumber: true,
      closeFlag: false,
      isLoading: false,
      openCloseJobWarningModal: false,
      getIntegrationStatus: [],
      indeedApplyToken: null,
      jobStreetModel: false,
      editJobData: [],
      jobPostLoading: false,
      openConfirmationPopup: false,
      openConfirmationModel: false,
      havingAccess: {},
      educationLevel: "",
      functionalArea: [],
      selectedCityId: null,
      selectedStateId: null,
      selectedWorkPlace: null,
      cityName: "",
      stateName: "",
      remoteTypeList: ["Fully Remote", "Hybrid Remote"],
      pinCode: "",
      isLocationPresent: false,
      updateLocation: "No",
      selectedCountryCode: null,
      showPreviewModal: false,
    };
  },
  props: {
    jobPostId: {
      type: Number,
      required: true,
    },
    linkedInCompanyId: {
      type: Number,
      required: false,
    },
    name: {
      type: String,
      default: "",
    },
    jobPostData: {
      type: Object,
      required: true,
    },
    cityList: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
    cityListLoading: {
      type: Boolean,
      default: false,
    },
    stateList: {
      type: Array,
      required: false,
      default: () => {
        return [];
      },
    },
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    applyUrl() {
      return `https://${this.orgCode}.${Config.jobPostUrlForIndeed}/v3/job-candidates?jobPostId=${this.jobPostId}`;
    },
    formAccess() {
      let formAccess = this.accessRights("15");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights["view"]
      ) {
        return formAccess.accessRights;
      } else {
        return false;
      }
    },
    // mobileNumberValidation() {
    //   if (!this.mobileNumber || !this.mobileNoCountryCode) {
    //     return "Mobile number is required";
    //   } else if (this.mobileNumber && this.mobileNumberProps) {
    //     return this.mobileNumberProps.valid ||
    //       this.mobileNumberProps.valid === undefined
    //       ? ""
    //       : "Please provide a valid mobile number";
    //   }
    //   return "";
    // },
    moreActions() {
      let actions = ["Edit"];
      if (this.name?.toLowerCase() === "indeed") {
        actions.push("Close");
      } else if (this.name?.toLowerCase() === "jobstreet") {
        actions.push("Link to live job", "Close");
      } else {
        actions.push("Delete");
      }
      return actions;
    },
    platformSpecificData() {
      if (this.name?.toLowerCase() === "indeed") {
        return {
          contactType: this.selectedContactType,
          emailAddress: this.emailAddress,
          contactName: this.contactName,
          selectedWorkPlace: this.selectedWorkPlace,
          screeningForm: this.getScreeningFormName(),
          benefits: this.benefits,
          updateLocation: this.updateLocation,
          cityName: this.cityName,
          stateName: this.stateName,
        };
      } else if (this.name?.toLowerCase() === "linkedin") {
        return {
          experienceLevel: this.experienceLevelName,
          jobCode: this.jobCodeName,
          industryCodeCategory: this.industryCodeCategoryName,
          industryCode: this.industryCodeName,
          workPlace: this.workPlace,
          linkedInCompanyId: this.linkedInCompanyId,
        };
      }
      return {};
    },
    getUserDetails() {
      return this.$store.state.userDetails;
    },
  },
  watch: {
    overlay(val) {
      if (val == false) {
        this.configureSwitch = val;
      }
    },
    industryCodeCategory(val) {
      let code = val?.Industry_Id
        ? val.Industry_Id
        : val?.Code
        ? val.Code
        : val;
      let selectedIndustry = this.industryCodeCategoryOptions?.find(
        (cat) => cat.Industry_Id === val
      );
      if (selectedIndustry) {
        this.industryCodeCategoryName = selectedIndustry.Description;
      }
      this.industryCodeOptions = this.industryOptions.filter((ele) => {
        if (ele.Parent_Industry_Id === code) {
          return ele;
        }
      });
      if (this.industryCode?.Industry_Id) {
        this.getIndustryCodeName(this.industryCode.Industry_Id);
      }
    },
    industryCode(val) {
      let selectedIndustryCode = this.industryCodeOptions?.find(
        (cat) => cat.Industry_Id === val
      );
      if (selectedIndustryCode) {
        this.industryCodeName = selectedIndustryCode.Description;
      }
    },
    jobCode(val) {
      let selectedJobCode = this.jobCodesOptions?.find(
        (cat) => cat.Code === val
      );
      if (selectedJobCode) {
        this.jobCodeName = selectedJobCode.Description;
      }
    },
    selectedCityId(val) {
      let filterCity = this.cityList?.filter((el) => el.City_Id == val);
      if (filterCity && filterCity[0]) {
        this.cityName = filterCity[0].City_Name;
        this.selectedCountryCode = filterCity[0].Country_Code;
        if (filterCity[0].State_Id) {
          this.selectedStateId = filterCity[0].State_Id;
          this.stateName = filterCity[0].State_Name;
        }
        // Build cityRegionPostal dynamically based on available values
        const locationParts = [this.cityName, this.stateName].filter(Boolean);
        if (this.pinCode) locationParts.push(this.pinCode);
        this.cityRegionPostal = locationParts.join(", ");
      }
    },
  },
  mounted() {
    this.getEncryptionKey();
    this.retrieveLinkedInJobDetails();
    this.emailAddress = this.getUserDetails.employeeEmail;
    this.contactName = this.getUserDetails.employeeFullName;
    this.orgCode = this.$store.getters.orgCode;
    this.retrieveIndeedJobPostDetails();
    this.fetchGetForm();
    this.setJobPostDetails();
    this.retrieveJobDetails();
    if (this.jobPostData) {
      // this.experienceLevel = this.jobPostData.Experience_Level;
      this.isLocationPresent =
        this.jobPostData.JobLocations?.length || this.jobPostData.City_Id
          ? true
          : false;
      this.workPlace = this.jobPostData.workPlaceType;
      this.selectedCityId = this.jobPostData.JobLocations?.length
        ? parseInt(this.jobPostData.JobLocations[0].City_Id)
        : this.jobPostData.City_Id || null;
      this.selectedStateId = this.jobPostData.JobLocations?.length
        ? parseInt(this.jobPostData.JobLocations[0].State_Id)
        : this.jobPostData.State_Id || null;
      this.pinCode = this.jobPostData.Pincode;
    }
  },
  methods: {
    refreshList() {
      this.jobStreetModel = false;
      this.retrieveJobDetails();
    },
    enableJobStreet() {
      this.jobStreetModel = true;
    },
    checkAccess() {
      this.havingAccess["update"] = this.formAccess.update;
      this.havingAccess["delete"] = this.formAccess.update;
      this.havingAccess["close"] = this.formAccess.update;
      this.havingAccess["link to live job"] = this.formAccess.view;
      return this.havingAccess;
    },
    getExperienceLevelName(val) {
      let selectedExperienceLevel = this.experienceLevelOptions?.find(
        (cat) => cat.Code === val
      );
      if (selectedExperienceLevel) {
        this.experienceLevelName = selectedExperienceLevel.Description;
      }
    },
    getIndustryCodeCategoryName(val) {
      let selectedIndustry = this.industryCodeCategoryOptions?.find(
        (cat) => cat.Industry_Id === val
      );
      if (selectedIndustry) {
        this.industryCodeCategoryName = selectedIndustry.Description;
      }
    },
    getIndustryCodeName(val) {
      let selectedIndustryCode = this.industryCodeOptions?.find(
        (cat) => cat.Industry_Id === val
      );
      if (selectedIndustryCode) {
        this.industryCodeName = selectedIndustryCode.Description;
      }
    },
    getJobCodeName(val) {
      let selectedJobCode = this.jobCodesOptions?.find(
        (cat) => cat.Code === val
      );
      if (selectedJobCode) {
        this.jobCodeName = selectedJobCode.Description;
      }
    },
    getSeekTokenAndCloseJobStreet(closeFlag) {
      this.isLoading = true;
      this.openConfirmationPopup = false;
      this.$apollo
        .query({
          query: GET_SEEK_TOKEN,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            hirer_Id: this.editJobData[0]?.hirerId,
            form_Id: 15,
            isPublish: 1,
          },
        })
        .then(async (response) => {
          if (
            response &&
            response.data &&
            response.data.getAuthTokenJobStreet &&
            response.data.getAuthTokenJobStreet.getData &&
            response.data.getAuthTokenJobStreet.getData.accessToken
          ) {
            const token = response.data.getAuthTokenJobStreet.getData;
            if (token && token.accessToken && token.browserToken) {
              const browserToken = JSON.parse(token.browserToken);
              const accessToken = JSON.parse(token.accessToken);
              await window.$cookies.set(
                "jobStreet_bowser_token",
                browserToken.access_token,
                browserToken.expires_in
              );
              await window.$cookies.set(
                "jobStreet_access_token",
                accessToken.access_token,
                accessToken.expires_in
              );
            }
            if (window.$cookies.get("jobStreet_bowser_token")) {
              if (closeFlag === "close") {
                this.isLoading = false;
                this.onCloseJobStreet();
              } else {
                this.isLoading = false;
                this.getPreviewList();
              }
            } else {
              this.isLoading = false;
            }
          }
        })
        .catch((err) => {
          this.isLoading = false;
          this.handleUpdateJobDetailsError(err);
        });
    },
    onCloseJobStreet() {
      let vm = this;
      vm.jobPostLoading = true;
      vm.$apollo
        .query({
          variables: {
            jobStreetId: parseInt(this.editJobData[0]?.jobStreetId),
          },
          query: CLOSE_SEEK_INTEGRATION,
          client: "apolloClientAX",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          vm.jobPostLoading = false;
          if (res && res.data) {
            let snackbarData = {
              isOpen: true,
              message: `Job Details have been closed successfully.`,
              type: "success",
            };
            vm.showAlert(snackbarData);
            this.retrieveJobDetails();
          }
        })
        .catch(() => {
          vm.jobPostLoading = false;
          this.handleUpdateJobDetailsError();
        });
    },
    retrieveJobDetails() {
      let vm = this;
      vm.jobPostLoading = true;
      vm.$apollo
        .query({
          query: JOB_STREET_JOB_DETAILS,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
          variables: {
            jobPostId: parseInt(this.jobPostId),
          },
        })
        .then((response) => {
          vm.jobPostLoading = false;
          if (
            response.data &&
            response.data.retrieveJobStreetJobPostDetails &&
            response.data.retrieveJobStreetJobPostDetails.data &&
            response.data.retrieveJobStreetJobPostDetails.data.length
          ) {
            vm.editJobData = response.data.retrieveJobStreetJobPostDetails.data;
            vm.jobStreetStatus =
              response.data.retrieveJobStreetJobPostDetails.data[0].appliedStatus.toLowerCase();
          } else {
            vm.editJobData = [];
            vm.jobStreetStatus = null;
          }
        })
        .catch(() => {
          vm.jobPostLoading = false;
          vm.jobStreetStatus = null;
          vm.editJobData = [];
          vm.handleFetchJobOptionsError();
        });
    },
    async checkAuth() {
      if (!this.configureSwitch && this.name?.toLowerCase() === "indeed") {
        this.accessToken = window.$cookies.get("indeedAccessToken");
        if (this.accessToken) {
          this.overlay = !this.configureSwitch;
          this.overlayFunction();
        } else {
          this.fetchIntegrationStatus();
        }
      }
    },
    // validateMobileNumber(param) {
    //   this.isValidMobileNumber = param.valid;
    //   // this.onChangeFields();
    // },
    async getEncryptionKey() {
      let vm = this;
      vm.loading = true;
      await vm.$apollo
        .query({
          query: GET_INDEED_AUTH_CREDENTIALS,
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getIndeedAuthCredentials &&
            response.data.getIndeedAuthCredentials.data &&
            response.data.getIndeedAuthCredentials.data.indeedApplyToken
          ) {
            this.indeedApplyToken =
              response.data.getIndeedAuthCredentials.data.indeedApplyToken;
            vm.loading = false;
          }
        })
        .catch((err) => {
          vm.loading = false;
          vm.handleGetEncryptionKeyError(err);
        });
    },
    handleGetEncryptionKeyError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job posts",
        isListError: false,
      });
    },
    // updateMobileNumber(payload, payload2) {
    //   this.mobileNumberProps = payload2;
    //   if (payload2 && Object.keys(payload2).length > 0) {
    //     this.mobileNumber =
    //       payload.length >= 3 ? payload2.nationalNumber : payload;
    //     this.mobileNoCountryCode = "+" + payload2.countryCallingCode;
    //   }
    // },
    // getCountryCode(mobileNoCountryCode) {
    //   if (mobileNoCountryCode) {
    //     this.mobileNoCountryCode = parseInt(mobileNoCountryCode.dialCode);
    //     this.countryName = mobileNoCountryCode.iso2;
    //     return this.mobileNoCountryCode;
    //   } else {
    //     return "";
    //   }
    // },
    async getIndeedAuthTokens() {
      let vm = this;
      vm.toggleLoading = true;
      await vm.$apollo
        .query({
          query: GET_INDEED_AUTH_TOKEN,
          variables: {
            scope: "employer_access",
            grantType: "client_credentials",
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getIndeedAuthToken &&
            response.data.getIndeedAuthToken.data &&
            Object.keys(JSON.parse(response.data.getIndeedAuthToken.data))
              .length > 0
          ) {
            let authData = JSON.parse(response.data.getIndeedAuthToken.data);
            let accessToken = authData.access_token;
            this.sourceName = response.data.getIndeedAuthToken.sourceName;
            window.$cookies.set("sourceName", this.sourceName, "60MIN");
            window.$cookies.set("indeedAccessToken", accessToken, "60MIN");
            vm.toggleLoading = false;
            if (!this.closeFlag) {
              vm.overlay = this.name?.toLowerCase() === "indeed" ? true : false;
            }
          }
        })
        .catch((err) => {
          vm.toggleLoading = false;
          vm.handleGetIndeedAuthTokensError(err);
        });
    },
    handleGetIndeedAuthTokensError(err = "") {
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "fetching",
          form: "job post",
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showError = false;
      errorMessages = [];
    },
    fetchGetForm() {
      let vm = this;
      this.formGetLoader = true;
      vm.$apollo
        .query({
          query: GET_FORM_BUILDER,
          client: "apolloClientAL",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getAllMinimalDynamicFormTemplate &&
            response.data.getAllMinimalDynamicFormTemplate.result &&
            !response.data.getAllMinimalDynamicFormTemplate.error
          ) {
            vm.formGetList =
              response.data.getAllMinimalDynamicFormTemplate.result;
          } else {
            vm.handleGetFormError();
          }
          vm.formGetLoader = false;
        })
        .catch((err) => {
          this.formGetLoader = false;
          vm.handleGetFormError(err);
        });
    },
    handleGetFormError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job details",
        isListError: false,
      });
    },
    fetchLinkedinOptions() {
      let vm = this;
      vm.isDropDownLoading = true;
      vm.$apollo
        .query({
          query: GET_LINKEDIN_JOB_OPTIONS,
          variables: {
            industryCode: null,
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getDropDownForLinkedIn
          ) {
            let options = response.data.getDropDownForLinkedIn;
            if (options.experienceLevel && options.experienceLevel.length > 0) {
              this.experienceLevelOptions = [...options.experienceLevel];
              if (this.experienceLevel) {
                this.getExperienceLevelName(this.experienceLevel);
              }
            }
            if (options.workPlaceType && options.workPlaceType.length > 0) {
              this.workPlaceOptions = [...options.workPlaceType];
            }
            if (options.jobFunction && options.jobFunction.length > 0) {
              this.jobCodesOptions = [...options.jobFunction];
              if (this.jobCode) {
                this.getJobCodeName(this.jobCode);
              }
            }
            if (options.industryCode && options.industryCode.length > 0) {
              this.industryOptions = [...options.industryCode];
            }
          }
          vm.setIndustryCodeCategory();
          vm.retrieveLinkedInJobDetails();
          vm.isDropDownLoading = false;
        })
        .catch(() => {
          vm.isDropDownLoading = false;
          vm.handleFetchJobOptionsError();
        });
    },
    retrieveLinkedInJobDetails() {
      let vm = this;
      vm.isDropDownLoading = true;
      vm.$apollo
        .query({
          query: RETRIVE_LINKEDIN_JOB_DETAILS,
          variables: {
            jobPostId: parseInt(this.jobPostId),
          },
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveLinkedInJobPostPublishDetails
          ) {
            this.linkedInStatus =
              response.data.retrieveLinkedInJobPostPublishDetails.Status;
            if (
              this.linkedInStatus !== null &&
              this.linkedInStatus.toLowerCase() !== "deleted"
            ) {
              let options = response.data.retrieveLinkedInJobPostPublishDetails;
              if (options.integrationId) {
                this.intergrationId = options.integrationId;
              }
              if (options.workPlaceType && !this.workPlaceType) {
                this.workPlace = options.workPlaceType.Code;
              }
              if (options.jobFunction) {
                this.jobCode = options.jobFunction.Code;
              }
              if (options.industryCategory) {
                this.industryCodeCategory = options.industryCategory;
              }
              if (options.industrySubCategory) {
                this.industryCode = options.industrySubCategory;
              }
              if (options.experienceLevel && !this.experienceLevel) {
                this.experienceLevel = options.experienceLevel.Code;
              }
            }
          }
          vm.isDropDownLoading = false;
        })
        .catch(() => {
          vm.handleRetrieveJobDetailsError();
          vm.isDropDownLoading = false;
        });
    },
    updateLinkedInJobDetails(status = null) {
      let vm = this;
      vm.loading = true;
      vm.$apollo
        .query({
          query: ADD_UPDATE_LINKED_IN_JOB_DETAILS,
          variables: {
            integrationId:
              this.linkedInStatus?.toLowerCase() !== "deleted"
                ? this.intergrationId
                : 0,
            jobPostId: parseInt(this.jobPostId),
            workplaceTypeCode: this.workPlace,
            experienceLevelCode: this.experienceLevel,
            jobFunctionCode: this.jobCode,
            status: status ? status : null,
            industryCategoryCode: this.industryCodeCategory.Industry_Id
              ? this.industryCodeCategory.Industry_Id
              : this.industryCodeCategory,
            industrySubCategoryCode:
              this.industryCode && this.industryCode.Industry_Id
                ? this.industryCode.Industry_Id
                : this.industryCode,
          },
          client: "apolloClientAM",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res.data.addUpdateLinkedInJobPostPublishDetails.errorCode == "") {
            let snackbarData = {
              isOpen: true,
              message: `Job Details ${
                status ? status : "posted"
              } successfully.`,
              type: "success",
            };
            this.showAlert(snackbarData);
            this.overlay = false;
          }
          vm.retrieveLinkedInJobDetails();
          vm.loading = false;
        })
        .catch(() => {
          vm.loading = false;
          vm.handleUpdateJobDetailsError();
        });
    },
    changeFieldValue(value, field) {
      if (field?.toLowerCase() === "industrycodecategory") {
        this.industryCode = null;
      }
      this[field] = value;
      this.getExperienceLevelName(value);
    },
    setIndustryCodeCategory() {
      this.industryCodeCategoryOptions = this.industryOptions.filter((ele) => {
        if (ele.Parent_Industry_Id == null) {
          return ele;
        }
      });
      if (this.industryCodeCategory?.Industry_Id) {
        this.getIndustryCodeCategoryName(this.industryCodeCategory.Industry_Id);
      }
    },
    handleFetchJobOptionsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job options",
        isListError: false,
      });
    },
    handleRetrieveJobDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "job details",
        isListError: false,
      });
    },
    handleUpdateJobDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "job details",
        isListError: false,
      });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    onMoreAction(actionType) {
      this.openMoreMenu = false;
      if (actionType?.toLowerCase() === "edit") {
        this.checkAuth();
        if (this.name?.toLowerCase() === "linkedin") {
          this.fetchLinkedinOptions();
          this.overlay = true;
        } else if (this.name?.toLowerCase() === "jobstreet") {
          this.enableJobStreet();
        }
      } else if (actionType?.toLowerCase() === "close") {
        if (this.name?.toLowerCase() === "indeed") {
          this.closeFlag = true;
          this.openCloseJobWarningModal = true;
        } else if (this.name?.toLowerCase() === "jobstreet") {
          this.openConfirmationPopup = true;
        }
      } else if (actionType?.toLowerCase() === "delete") {
        if (this.name?.toLowerCase() === "linkedin") {
          this.deleteLinkedinJobPost();
        }
      } else if (actionType?.toLowerCase() === "link to live job") {
        if (this.name?.toLowerCase() === "jobstreet") {
          this.getSeekTokenAndCloseJobStreet("link to live job");
        }
      }
    },
    getPreviewList() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_PROFILE_POSITION,
          variables: {
            id:
              this.editJobData && this.editJobData.length
                ? this.editJobData[0]?.profileId
                : null,
          },
          client: "apolloClientAW",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.positionProfile &&
            response.data.positionProfile.positionUri
          ) {
            vm.isLoading = false;
            window.open(response.data.positionProfile.positionUri, "_blank");
          } else {
            vm.isLoading = false;
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.handleRetrieveJobDetailsError(err);
        });
    },
    async validateCloseJobToIndeed() {
      this.openCloseJobWarningModal = false;
      this.accessToken = window.$cookies.get("indeedAccessToken");
      if (this.accessToken) {
        this.closeJobpostToIndeed(this.indeedSourceId, this.indeedIntegraionId);
      } else {
        await this.getIndeedAuthTokens(this.clientId, this.secretKey);
        this.closeJobpostToIndeed(this.indeedSourceId, this.indeedIntegraionId);
      }
    },
    overlayFunction() {
      if (!this.configureSwitch) {
        this.fetchLinkedinOptions();
      }
    },
    async retrieveIndeedJobPostDetails() {
      let vm = this;
      vm.loading = true;
      await vm.$apollo
        .query({
          query: RETRIEVE_INDEED_JOBPOST_DETAILS,
          variables: {
            jobPostId: parseInt(this.jobPostId),
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.retrieveIndeedJobPostDetails &&
            response.data.retrieveIndeedJobPostDetails.data &&
            response.data.retrieveIndeedJobPostDetails.data.length > 0
          ) {
            let indeedData = response.data.retrieveIndeedJobPostDetails.data[0];
            this.indeedStatus = indeedData.status;
            this.indeedIntegraionId = indeedData.integrationId;
            this.indeedSourceId = indeedData.employerJobId;
            let parsedArray = JSON.parse(indeedData.benefits);
            // Extract the text from the array
            let textWithNewline = parsedArray[0];
            // Replace the newline character with a space (or any other separator)
            this.benefits = textWithNewline.replace("\\n", " ");
            this.emailAddress = indeedData.contactEmail;
            this.contactName = indeedData.contactName;
            this.selectedIndeedTemplate = indeedData?.dynamicFormId;
            this.mobileNumber = indeedData.contactPhone;
            this.selectedWorkPlace = indeedData.workplaceType || null;
            this.selectedStateId = indeedData.stateId || this.selectedStateId;
            this.stateName = indeedData.stateName || this.stateName;
            this.selectedCityId = indeedData.cityId || this.selectedCityId;
            this.cityName = indeedData.cityName || this.cityName;
            let contactType = indeedData.contactType.split(", ")[1];
            this.selectedContactType = contactType ? contactType : "";
          } else {
            this.indeedStatus = null;
          }
          vm.loading = false;
        })
        .catch((err) => {
          vm.loading = false;
          vm.handleRetrieveJobDetailsError(err);
        });
    },
    setJobPostDetails() {
      let functionalAreaData = this.jobPostData?.Functional_Area;
      this.functionalArea = functionalAreaData ? [functionalAreaData] : [];
      let qualificationValues = this.jobPostData?.Qualification;
      this.educationLevel =
        qualificationValues.length > 0
          ? qualificationValues.map((q) => q.Qualification).join(", ")
          : "";
      this.jobName = this.jobPostData.Job_Post_Name;
    },
    addUpdateJobPostToIndeed(status = null) {
      let vm = this;
      const postUrl = Config.hrappWebhookApiUrlForIndeed;
      vm.loading = true;
      vm.sourceName = window.$cookies.get("sourceName");
      if (vm.sourceName) {
        let contactType = ["contact"];
        if (this.selectedContactType) {
          contactType.push(this.selectedContactType.toLowerCase());
        }
        vm.$apollo
          .query({
            query: PUBLISH_JOBPOST_TO_INDEED,
            variables: {
              jobPostId: parseInt(this.jobPostId),
              integrationId: this.indeedIntegraionId
                ? this.indeedIntegraionId
                : 0,
              dynamicFormId: this.selectedIndeedTemplate
                ? parseInt(this.selectedIndeedTemplate)
                : null,
              status: status,
              cityId: this.selectedCityId,
              cityName: this.cityName,
              stateId: this.selectedStateId,
              countryCode: this.selectedCountryCode,
              stateName: this.stateName,
              workplaceType: this.selectedWorkPlace,
              updateLocation: this.updateLocation,
              input: {
                jobPostings: [
                  {
                    body: {
                      title: this.jobName,
                      description: this.jobPostData.Job_Description,
                      descriptionFormatting: "RICH_FORMATTING",
                      location: {
                        cityRegionPostal: this.cityRegionPostal,
                      },
                      benefits: this.benefits,
                    },
                    metadata: {
                      jobSource: {
                        sourceType: "Employer",
                        sourceName: String(this.sourceName).trim(),
                      },
                      jobPostingId: this.orgCode + "-" + this.jobPostId,
                      taxonomyClassification: {
                        categories: this.functionalArea,
                        remoteType: this.selectedWorkPlace || null,
                        education: this.educationLevel,
                      },
                      datePublished: moment(this.jobPostData.Added_On).isValid()
                        ? moment
                            .utc(
                              this.jobPostData.Added_On,
                              "YYYY-MM-DD HH:mm:ss"
                            )
                            .format("YYYY-MM-DDTHH:mm:ss[Z]")
                        : null,
                      url: `https://${this.orgCode}.${Config.jobPostUrlForIndeed}/v3/careers?jobPostId=${this.jobPostId}`,
                      contacts: [
                        {
                          contactType: contactType,
                          contactInfo: {
                            contactEmail: this.emailAddress,
                            contactName: this.contactName,
                          },
                        },
                      ],
                    },
                    applyMethod: {
                      indeedApply: {
                        apiToken: this.indeedApplyToken,
                        postUrl: postUrl,
                        applyQuestions: this.selectedIndeedTemplate
                          ? Config.indeedScreeningQuestions +
                            `/?jobpostId=` +
                            this.orgCode +
                            "-" +
                            this.jobPostId
                          : null,
                        resumeRequired: "YES",
                        phoneRequired: "YES",
                      },
                    },
                  },
                ],
              },
            },
            client: "apolloClientAV",
            fetchPolicy: "no-cache",
          })
          .then((res) => {
            if (res?.data?.publishJobPostToIndeed?.results) {
              let response = JSON.parse(
                res.data.publishJobPostToIndeed.results
              );
              if (!response.errors) {
                let snackbarData = {
                  isOpen: true,
                  message: `Job Details have been ${
                    status ? status : "posted"
                  } successfully.`,
                  type: "success",
                };
                this.showAlert(snackbarData);
                this.overlay = false;
                this.retrieveIndeedJobPostDetails();
              } else {
                let snackbarData = {
                  isOpen: true,
                  message:
                    "An error occurred while attempting to post the jobpost to indeed. Please contact the platform administrator for assistance.",
                  type: "warning",
                };
                this.showAlert(snackbarData);
                vm.loading = false;
              }
            }
          })
          .catch((error) => {
            vm.loading = false;
            vm.handleUpdateJobDetailsError(error);
          });
      } else {
        let snackbarData = {
          isOpen: true,
          message:
            "Indeed source name was not found. Please contact the platform administrator for assistance.",
          type: "warning",
        };
        this.showAlert(snackbarData);
        vm.loading = false;
      }
    },
    closeJobpostToIndeed(indeedJobId, indeedIntegraionId) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: CLOSE_JOBPOST_TO_INDEED,
          variables: {
            sourcedPostingId: indeedJobId,
            integrationId: indeedIntegraionId,
          },
          client: "apolloClientAV",
          fetchPolicy: "no-cache",
        })
        .then((res) => {
          if (res?.data?.closeJobpostToIndeed?.results) {
            let response = JSON.parse(res.data.closeJobpostToIndeed.results);
            if (!response.errors) {
              this.closeFlag = false;
              let snackbarData = {
                isOpen: true,
                message: "Jobpost has been closed successfully.",
                type: "success",
              };
              this.showAlert(snackbarData);
              this.retrieveIndeedJobPostDetails();
            } else {
              let snackbarData = {
                isOpen: true,
                message:
                  "An error occurred while attempting to close the jobpost to indeed. Please contact the platform administrator for assistance.",
                type: "warning",
              };
              this.showAlert(snackbarData);
            }
            vm.isLoading = false;
          }
        })
        .catch(() => {
          vm.isLoading = false;
          vm.handleCloseJobDetailsError();
        });
    },
    handleCloseJobDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "job details",
        isListError: false,
      });
    },
    async submitForm() {
      const { valid } = await this.$refs.jobPostForm.validate();
      if (valid) {
        if (this.name?.toLowerCase() === "indeed") {
          this.addUpdateJobPostToIndeed("Published");
        } else if (this.name?.toLowerCase() === "linkedin")
          this.updateLinkedInJobDetails();
      }
    },
    deleteLinkedinJobPost() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_LINKEDIN_JOB_POST,
          client: "apolloClientAM",
          variables: {
            integrationId: this.intergrationId,
            jobPostId: parseInt(this.jobPostId),
          },
        })
        .then(() => {
          vm.intergrationId = 0;
          if (this.jobPostData) {
            this.experienceLevel = this.jobPostData.Experience_Level;
            this.workPlace = this.jobPostData.workPlaceType;
          } else {
            vm.experienceLevel = null;
            vm.workPlace = null;
          }
          vm.jobCode = null;
          vm.industryCodeCategory = null;
          vm.industryCode = null;
          vm.linkedInStatus = null;
          vm.isLoading = false;
          let snackbarData = {
            isOpen: true,
            message: `Job Details deleted successfully.`,
            type: "success",
          };
          vm.showAlert(snackbarData);
        })
        .catch((err) => {
          vm.handleDeleteJobDetailsError(err);
          vm.isLoading = false;
        });
    },
    handleDeleteJobDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "job details",
        isListError: false,
      });
    },
    async fetchIntegrationStatus() {
      let vm = this;
      vm.integrationStatusLoading = true;
      await vm.$apollo
        .query({
          query: GET_INTEGRATION_STATUS,
          variables: {
            form_Id: 15,
          },
          client: "apolloClientAG",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.jobBoardIntegrationStatus &&
            response.data.jobBoardIntegrationStatus.getStatus &&
            response.data.jobBoardIntegrationStatus.getStatus.length > 0
          ) {
            this.getIntegrationStatus =
              response.data.jobBoardIntegrationStatus.getStatus;
            this.getIndeedAuthTokens();
          }
        })
        .catch((err) => {
          this.integrationStatusLoading = false;
          vm.handleRetrieveIntegrationStatusError(err);
        });
    },
    handleRetrieveIntegrationStatusError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "integration status",
        isListError: false,
      });
    },
    getStatusByType(integrationType) {
      // Find the object with the specified Integration_Type
      const integration = this.getIntegrationStatus.find(
        (item) =>
          item.Integration_Type.toLowerCase() === integrationType.toLowerCase()
      );

      // If the integration is found, return its Integration_Status
      if (integration) {
        return integration;
      }
    },
    closeIntegrationForm() {
      this.openConfirmationModel = false;
      this.overlay = false;
    },
    async openPreviewModal() {
      const { valid } = await this.$refs.jobPostForm.validate();
      if (valid) {
        this.showPreviewModal = true;
      }
    },
    closePreviewModal() {
      this.showPreviewModal = false;
    },
    async handlePreviewUpdate() {
      // Close preview modal first
      this.showPreviewModal = false;

      // Validate the form and submit
      const { valid } = await this.$refs.jobPostForm.validate();
      if (valid) {
        if (this.name?.toLowerCase() === "indeed") {
          this.addUpdateJobPostToIndeed("Published");
        } else if (this.name?.toLowerCase() === "linkedin") {
          this.updateLinkedInJobDetails();
        }
      }
    },
    getScreeningFormName() {
      if (this.selectedIndeedTemplate && this.formGetList.length > 0) {
        const form = this.formGetList.find(
          (f) => f.templateId === this.selectedIndeedTemplate
        );
        return form ? form.templateName : "Not specified";
      }
      return "Not specified";
    },
  },
};
</script>
<style scoped>
.overlay-card {
  height: 100%;
  width: 100%;
  display: block;
  background: white;
}
:deep(.v-overlay__content) {
  height: 100%;
  width: 40%;
}

@media only screen and (max-width: 770px) {
  :deep(.v-overlay__content) {
    width: 100%;
  }
}
.overlay-head {
  background: rgb(var(--v-theme-primary));
  height: auto;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: x-large;
  font-weight: 500;
}
.overlay-body {
  padding: 15px;
  height: calc(100vh - 130px);
  overflow: scroll;
}
#job-options-card {
  min-height: 30%;
}
.overlay-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: auto;
  position: fixed;
  bottom: 0;
  width: 100%;
}
.fixed-title {
  width: 100%;
}
</style>
