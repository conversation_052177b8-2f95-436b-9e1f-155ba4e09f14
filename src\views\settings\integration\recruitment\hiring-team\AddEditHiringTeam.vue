<template>
  <v-container fluid :class="isMobileView ? 'pa-2' : ''">
    <h3>{{ headingText }}</h3>

    <!-- Mobile Layout -->
    <div v-if="isMobileView">
      <v-row style="padding-top: 25px">
        <v-col cols="12" class="pa-1">
          <div class="dropDown">
            <CustomSelect
              :itemSelected="selectedValues"
              :items="availableEmployees"
              :label="selectLabel"
              itemValue="employeeId"
              itemTitle="employeeName"
              :isAutoComplete="true"
              @selected-item="updateSelectValue($event, true)"
              :multiple="true"
              clearable
              :isLoading="employeeListLoading"
              style="max-width: 100%"
            ></CustomSelect>
          </div>
        </v-col>
        <v-col cols="12" class="pa-1 mt-n4">
          <div
            v-for="employee in selectedEmployeesList"
            :key="employee.employeeId"
            class="cardDiv mobile-card mb-2"
          >
            <v-card class="rounded-lg" elevation="2" style="padding: 12px">
              <v-row class="ma-0" no-gutters>
                <v-col cols="2" class="pa-1 d-flex align-center justify-center">
                  <v-avatar
                    :color="employeeColorMap[employee.employeeId]"
                    size="32"
                  >
                    <span class="text-caption">
                      {{ getInitials(employee.employeeName) }}
                    </span>
                  </v-avatar>
                </v-col>
                <v-col cols="8" class="pa-1">
                  <div class="d-flex align-center h-100">
                    <span
                      class="text-body-2 font-weight-medium"
                      style="word-break: break-word; line-height: 1.2"
                    >
                      {{ employee.employeeName }}
                    </span>
                  </div>
                </v-col>
                <v-col cols="2" class="pa-1 d-flex align-center justify-center">
                  <v-btn
                    icon
                    variant="text"
                    size="small"
                    @click="removeEmployee(employee.employeeId)"
                    color="grey-lighten-1"
                  >
                    <v-icon size="16">fas fa-trash</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </v-card>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Desktop Layout -->
    <div v-else>
      <v-row style="padding-top: 25px">
        <v-col cols="3">
          <div class="dropDown">
            <CustomSelect
              :itemSelected="selectedValues"
              :items="availableEmployees"
              :label="selectLabel"
              itemValue="employeeId"
              itemTitle="employeeName"
              :isAutoComplete="true"
              @selected-item="updateSelectValue($event, true)"
              :multiple="true"
              clearable
              :isLoading="employeeListLoading"
              style="max-width: 100%"
            ></CustomSelect>
          </div>
        </v-col>
        <v-col cols="9">
          <div
            v-for="employee in selectedEmployeesList"
            :key="employee.employeeId"
            class="cardDiv"
          >
            <v-card class="rounded-lg" elevation="2" style="padding: 3%">
              <v-row>
                <v-col
                  cols="3"
                  style="padding-left: 10%"
                  class="d-flex align-center justify-center"
                >
                  <v-avatar
                    :color="employeeColorMap[employee.employeeId]"
                    size="30"
                  >
                    {{ getInitials(employee.employeeName) }}
                  </v-avatar>
                </v-col>
                <v-col cols="7">
                  <h4>{{ employee.employeeName }}</h4>
                </v-col>
                <v-col
                  cols="2"
                  style="padding-right: 10%"
                  class="d-flex align-center justify-center"
                >
                  <i
                    class="fas fa-trash fa-sm deleteIcon"
                    style="color: lightgrey"
                    @click="removeEmployee(employee.employeeId)"
                  ></i>
                </v-col>
              </v-row>
            </v-card>
          </div>
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script defer>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";

export default {
  name: "AddEditHiringTeam",
  components: {
    CustomSelect,
  },
  data() {
    return {
      employees: [],
      selectedValue: [],
      selectedValues: [],
      employeeColorMap: {},
      employeeIds: [],
    };
  },
  props: {
    headingText: {
      type: String,
      default: "Hiring Employees",
    },
    selectLabel: {
      type: String,
      default: "Add Employees",
    },
    allEmployeesList: {
      type: Array,
      required: true,
    },
    initialSelectedEmployeeIds: {
      type: Array,
      default: () => [],
    },
    employeeListLoading: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    selectedEmployeesList() {
      return this.selectedValue
        .map((selectedId) => {
          const employee = this.employees.find(
            (employee) => employee.employeeId === selectedId
          );
          if (employee) {
            return {
              employeeId: employee.employeeId,
              employeeName: employee.employeeName,
              userDefinedEmpId: employee.userDefinedEmpId,
            };
          }
          return null;
        })
        .filter(Boolean);
    },
    availableEmployees() {
      const filteredEmployees = this.employees.filter(
        (emp) => !this.selectedValue.includes(emp.employeeId)
      );
      // Add "Select All" option at the beginning of the list
      return [
        {
          employeeId: "select-all",
          employeeName: "Select All",
          userDefinedEmpId: "",
        },
        ...filteredEmployees,
      ];
    },
  },
  mounted() {
    this.employees = this.allEmployeesList;
    this.selectedValue = this.initialSelectedEmployeeIds;
    this.updateSelectValue();
  },
  methods: {
    removeEmployee(empId) {
      this.selectedValue = this.selectedValue.filter((id) => id !== empId);
      this.$emit("selected", [this.selectedValue, true]);
    },
    updateSelectValue(value = "") {
      if (value && value.length > 0) {
        // Check if select-all was selected
        if (value.includes("select-all")) {
          // Select all available employees
          this.selectedValue = this.employees.map((emp) => emp.employeeId);
        } else {
          // Normal selection behavior
          let mergedArray = [...this.selectedValue, ...value];
          this.selectedValue = mergedArray;
        }
        this.$emit("selected", [this.selectedValue, true]);
        this.selectedValues = [];
      } else {
        this.$emit("selected", [this.selectedValue, false]);
      }
    },
    getInitials(name) {
      if (!name) return "";
      const names = name.split(" ");
      const firstLetter = names[0].charAt(0);
      const lastLetter =
        names.length > 1 ? names[names.length - 1].charAt(0) : "";
      return (firstLetter + lastLetter).toUpperCase();
    },
  },
  watch: {
    selectedValue(newValue) {
      newValue.forEach((empId) => {
        if (!this.employeeColorMap[empId]) {
          // Generating each RGB component with a minimum value to ensure lightness
          const r = Math.floor(Math.random() * (255 - 128) + 128);
          const g = Math.floor(Math.random() * (255 - 128) + 128);
          const b = Math.floor(Math.random() * (255 - 128) + 128);
          // Converting the RGB components to a hex color string
          const color = `#${r.toString(16)}${g.toString(16)}${b.toString(16)}`;
          this.employeeColorMap[empId] = color;
        }
      });
    },
    employeeListLoading(value) {
      if (!value) {
        this.employees = this.allEmployeesList;
        this.updateSelectValue();
      }
    },
  },
};
</script>

<style scoped>
.dropDown {
  min-width: 100%;
  display: inline-block;
}
.cardDiv {
  margin: 0px 10px 15px;
  min-width: 30%;
  display: inline-block;
}
.logo {
  background-color: yellowgreen;
  border-radius: 50%;
}
.cardDiv .deleteIcon {
  display: none;
}
.cardDiv:hover .deleteIcon {
  display: inline-block;
}
.deleteIcon:hover {
  color: red !important;
}

/* Mobile responsiveness */
.mobile-card {
  min-width: 100% !important;
  margin: 0px 0px 8px !important;
  display: block !important;
}

@media screen and (max-width: 768px) {
  .cardDiv {
    min-width: 100%;
    margin: 0px 0px 8px;
    display: block;
  }

  .dropDown {
    margin-bottom: 16px;
  }
}

@media screen and (width <= 728px) {
  .cardDiv {
    min-width: 20%;
  }
}
</style>
