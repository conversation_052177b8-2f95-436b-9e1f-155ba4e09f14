<template>
  <div>
    <div v-if="jobPostMemberLoading">
      <div v-for="i in 5" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <div v-else-if="isErrorInList">
      <AppFetchErrorScreen
        image-name="common/common-error-image"
        :content="errorContent"
        icon-name="fas fa-redo-alt"
        button-text="Retry"
        :isSmallImage="true"
        @button-click="getRoleBasedJobPostMember"
      >
      </AppFetchErrorScreen>
    </div>
    <div v-else class="content">
      <!-- Hiring Recruiters section  -->
      <AddEditHiringTeam
        :allEmployeesList="recruiterList"
        :employeeListLoading="recruiterListLoading"
        :initialSelectedEmployeeIds="recruitersEmployeeIds"
        headingText="Hiring Recruiters"
        selectLabel="Add Recruiters"
        @selected="updateSelectedRecruiters"
      />
      <!-- Hiring Managers section  -->
      <AddEditHiringTeam
        :allEmployeesList="allEmployeesList"
        :employeeListLoading="employeeListLoading"
        :initialSelectedEmployeeIds="hiringManagerEmployeeIds"
        headingText="Hiring Managers"
        selectLabel="Add Managers"
        @selected="updateSelectedManagers"
      />
      <!-- Panel Members section  -->
      <AddEditHiringTeam
        :allEmployeesList="allEmployeesList"
        :employeeListLoading="employeeListLoading"
        :initialSelectedEmployeeIds="panelEmployeeIds"
        headingText="Interview Panel Members"
        selectLabel="Add Panel Members"
        @selected="updateSelectedPanelMembers"
      />
      <!-- Onboarding Specialist section  -->
      <AddEditHiringTeam
        :allEmployeesList="onboardSpecialistList"
        :employeeListLoading="specialistListLoading"
        :initialSelectedEmployeeIds="onboardingSpecialistEmployeeIds"
        headingText="Onboarding Specialist"
        selectLabel="Add Onboarding Specialist"
        @selected="updateSelectedOnboardingSpecialist"
      />
    </div>
    <div>
      <v-bottom-navigation :disabled="!hasChanges">
        <v-sheet
          class="align-center text-center"
          :class="windowWidth > 768 ? 'd-flex pa-3' : 'pa-2'"
          style="width: 100%"
        >
          <v-row justify="center">
            <v-col cols="6" class="d-flex justify-start pl-2">
              <v-btn
                rounded="lg"
                variant="outlined"
                size="small"
                class="primary"
                density="compact"
                @click="closeEditForm()"
                ><span class="primary">Cancel</span></v-btn
              >
            </v-col>
            <v-col cols="6" class="d-flex justify-end pr-4">
              <v-btn
                rounded="lg"
                size="small"
                density="compact"
                class="mr-1 secondary"
                variant="elevated"
                :dense="isMobileView"
                @click="updateJobPostMembersBasedOnRole()"
              >
                <span class="primary">Save</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-sheet>
      </v-bottom-navigation>
      <AppSnackBar
        v-if="showValidationAlert"
        :show-snack-bar="showValidationAlert"
        snack-bar-type="warning"
        timeOut="-1"
        @close-snack-bar="closeValidationAlert"
      >
        <template #custom-alert>
          <div
            v-for="(validationMsg, index) of validationMessages"
            :key="validationMsg + index"
            class="text-subtitle-1"
          >
            {{ validationMsg }}
          </div>
          <div class="d-flex justify-end">
            <v-btn
              color="secondary"
              class="mt-n5"
              variant="text"
              @click="closeValidationAlert()"
            >
              Close
            </v-btn>
          </div>
        </template>
      </AppSnackBar>
    </div>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>

<script>
import AddEditHiringTeam from "./AddEditHiringTeam.vue";
import {
  UPDATE_ROLE_BASED_JOB_POST_MEMBERS,
  GET_ROLE_BASED_JOB_POST_MEMBER,
} from "@/graphql/recruitment/recruitmentQueries.js";
import { GET_JOB_HEADER } from "@/graphql/recruitment/recruitmentQueries.js";

export default {
  name: "HiringTeam",
  components: {
    AddEditHiringTeam,
  },
  props: {
    jobPostId: {
      type: Number,
      default: 0,
    },
    serviceProviderId: {
      type: Number,
      default: null,
    },
    customGroupId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      allEmployeesList: [],
      recruiterList: [],
      onboardSpecialistList: [],
      isLoading: false,
      selectedRecruiters: [],
      selectedPanelMembers: [],
      selectedHiringManager: [],
      selectedOnboardingSpecialist: [],
      validationMessages: [],
      showValidationAlert: false,
      isErrorInList: false,
      panelEmployeeIds: [],
      recruitersEmployeeIds: [],
      hiringManagerEmployeeIds: [],
      currentHiringManagerEmployeeIds: [],
      currentPanelEmployeeIds: [],
      currentRecruitersEmployeeIds: [],
      currentOnboardingSpecialistEmployeeIds: [],
      onboardingSpecialistEmployeeIds: [],
      jobPostMemberLoading: true,
      errorContent: "",
      employeeListLoading: false,
      recruiterListLoading: false,
      hasChanges: false,
      specialistListLoading: false,
      isCentralisedRecruitment: "No",
    };
  },
  mounted() {
    this.fetchEmployeesList();
    this.checkCentralisedRecruitment();
    this.getRoleBasedJobPostMember();
  },
  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
  },
  methods: {
    async fetchEmployeesList() {
      if (this.allEmployeesList.length === 0) {
        this.employeeListLoading = true;
        await this.$store
          .dispatch("getEmployeesList", {
            formName: "Job Posts",
            formId: 15,
            serviceProviderId: this.serviceProviderId,
          })
          .then((empData) => {
            if (empData.length > 0) {
              let employees = empData.map((emp) => ({
                ...emp,
                employeeName:
                  emp.employeeName && emp.userDefinedEmpId
                    ? `${emp.employeeName} - ${emp.userDefinedEmpId}`
                    : emp.employeeName || "", // Keep original or default to empty string
              }));
              this.allEmployeesList = employees.filter(
                (el) => el.empStatus?.toLowerCase() === "active"
              );
            }
            this.showEmpListModal = true;
            this.employeeListLoading = false;
          })
          .catch((err) => {
            let snackbarData = {
              isOpen: true,
              message: "",
              type: "warning",
            };
            if (err === "error") {
              snackbarData.message =
                "Something went wrong while fetching the employees. If you continue to see this issue, please contact the platform administrator.";
            } else {
              snackbarData.message = err;
            }
            this.showAlert(snackbarData);
            this.employeeListLoading = false;
          });
      } else {
        this.showEmpListModal = true;
      }
    },
    async fetchRecruiterList() {
      this.recruiterListLoading = true;
      await this.$store
        .dispatch("getEmployeesList", {
          formName: "Job Posts",
          formId: 15,
          serviceProviderId:
            this.fieldForce &&
            this.isCentralisedRecruitment?.toLowerCase() === "no"
              ? this.serviceProviderId
              : null,
          customGroupId: this.customGroupId,
        })
        .then((empData) => {
          if (empData.length > 0) {
            let allEmployeesList = empData.map((emp) => ({
              ...emp,
              employeeName:
                emp.employeeName && emp.userDefinedEmpId
                  ? `${emp.employeeName} - ${emp.userDefinedEmpId}`
                  : emp.employeeName || "",
            }));
            this.recruiterList = allEmployeesList.filter(
              (employee) => employee.isRecruiter?.toLowerCase() === "yes"
            );
          }
          this.recruiterListLoading = false;
        })
        .catch((err) => {
          let snackbarData = {
            isOpen: true,
            message: "",
            type: "warning",
          };
          if (err === "error") {
            snackbarData.message =
              "Something went wrong while fetching the recruiters. If you continue to see this issue, please contact the platform administrator.";
          } else {
            snackbarData.message = err;
          }
          this.showAlert(snackbarData);
          this.recruiterListLoading = false;
        });
    },
    async fetchOnboardingSpecialistList() {
      this.specialistListLoading = true;
      await this.$store
        .dispatch("getEmployeesList", {
          formName: "Job Posts",
          formId: 15,
          serviceProviderId:
            this.fieldForce &&
            this.isCentralisedRecruitment?.toLowerCase() === "no"
              ? this.serviceProviderId
              : null,
        })
        .then((empData) => {
          if (empData.length > 0) {
            let allEmployeesList = empData.map((emp) => ({
              ...emp,
              employeeName:
                emp.employeeName && emp.userDefinedEmpId
                  ? `${emp.employeeName} - ${emp.userDefinedEmpId}`
                  : emp.employeeName || "",
            }));
            this.onboardSpecialistList = allEmployeesList.filter(
              (el) => el.empStatus?.toLowerCase() === "active"
            );
          }
          this.showEmpListModal = true;
          this.specialistListLoading = false;
        })
        .catch((err) => {
          let snackbarData = {
            isOpen: true,
            message: "",
            type: "warning",
          };
          if (err === "error") {
            snackbarData.message =
              "Something went wrong while fetching the onboarding specialists. If you continue to see this issue, please contact the platform administrator.";
          } else {
            snackbarData.message = err;
          }
          this.showAlert(snackbarData);
          this.specialistListLoading = false;
        });
    },
    updateSelectedRecruiters(ids) {
      this.hasChanges = !this.arraysAreEqual(
        this.currentRecruitersEmployeeIds,
        ids[0]
      );
      this.selectedRecruiters = [...ids[0]];
      this.recruitersEmployeeIds = [...ids[0]];
    },
    updateSelectedManagers(ids) {
      this.hasChanges = !this.arraysAreEqual(
        this.currentHiringManagerEmployeeIds,
        ids[0]
      );
      this.selectedHiringManager = [...ids[0]];
      this.hiringManagerEmployeeIds = [...ids[0]];
    },
    updateSelectedPanelMembers(ids) {
      this.hasChanges = !this.arraysAreEqual(
        this.currentPanelEmployeeIds,
        ids[0]
      );
      this.selectedPanelMembers = [...ids[0]];
      this.panelEmployeeIds = [...ids[0]];
    },
    updateSelectedOnboardingSpecialist(ids) {
      this.hasChanges = !this.arraysAreEqual(
        this.currentOnboardingSpecialistEmployeeIds,
        ids[0]
      );
      this.selectedOnboardingSpecialist = [...ids[0]];
      this.onboardingSpecialistEmployeeIds = [...ids[0]];
    },
    arraysAreEqual(arr1, arr2) {
      if (arr1.length !== arr2.length) return false;
      const sortedArr1 = [...arr1].sort();
      const sortedArr2 = [...arr2].sort();
      return sortedArr1.every((value, index) => value === sortedArr2[index]);
    },
    async updateJobPostMembersBasedOnRole() {
      if (!this.hasChanges) {
        let snackbarData = {
          isOpen: true,
          message: "No changes detected, no update performed.",
          type: "info",
        };
        this.showAlert(snackbarData);
        return;
      }

      let vm = this;
      vm.isLoading = true;
      try {
        await vm.$apollo.mutate({
          mutation: UPDATE_ROLE_BASED_JOB_POST_MEMBERS,
          variables: {
            jobPostId: parseInt(this.jobPostId),
            panelMembers: this.selectedPanelMembers,
            recruiters: this.selectedRecruiters,
            hiringManager: this.selectedHiringManager,
            onboardSpecialist: this.selectedOnboardingSpecialist,
          },
          client: "apolloClientAM",
        });

        vm.isLoading = false;
        let snackbarData = {
          isOpen: true,
          message: "Job post members updated successfully",
          type: "success",
        };
        vm.showAlert(snackbarData);
        vm.closeEditForm();
        vm.getRoleBasedJobPostMember();
      } catch (err) {
        vm.handleUpdateError(err);
      }
    },
    getRoleBasedJobPostMember() {
      let vm = this;
      vm.isErrorInList = false;
      vm.jobPostMemberLoading = true;
      vm.$apollo
        .query({
          query: GET_ROLE_BASED_JOB_POST_MEMBER,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            jobPostId: parseInt(vm.jobPostId),
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getRoleBasedJobPostMember &&
            !response.data.getRoleBasedJobPostMember.errorCode.length
          ) {
            // Process panel members
            let panelMembers =
              response.data.getRoleBasedJobPostMember.panelMembers;
            vm.panelEmployeeIds = panelMembers.map((item) => item.Employee_Id);
            // Process recruiters
            let recruiters = response.data.getRoleBasedJobPostMember.recruiters;
            vm.recruitersEmployeeIds = recruiters.map(
              (item) => item.Employee_Id
            );
            // Process hiring managers
            let hiringManagers =
              response.data.getRoleBasedJobPostMember.hiringManager;
            vm.hiringManagerEmployeeIds = hiringManagers.map(
              (item) => item.Employee_Id
            );
            // Process onboarding specialists
            let onboardingSpecialists =
              response.data.getRoleBasedJobPostMember.onboardSpecialList;
            vm.onboardingSpecialistEmployeeIds = onboardingSpecialists?.map(
              (item) => item.Employee_Id
            );
            this.currentRecruitersEmployeeIds = this.recruitersEmployeeIds;
            this.currentHiringManagerEmployeeIds =
              this.hiringManagerEmployeeIds;
            this.currentPanelEmployeeIds = this.panelEmployeeIds;
            this.currentOnboardingSpecialistEmployeeIds =
              this.onboardingSpecialistEmployeeIds;
            vm.jobPostMemberLoading = false;
          } else {
            vm.handleListError();
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.jobPostMemberLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "job post roles",
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "job post roles",
        isListError: false,
      });
    },
    closeEditForm() {
      // Reset the selected IDs to their initial values fetched from the server
      this.selectedRecruiters = [...this.recruitersEmployeeIds];
      this.selectedPanelMembers = [...this.panelEmployeeIds];
      this.selectedOnboardingSpecialist = [
        ...this.onboardingSpecialistEmployeeIds,
      ];
      this.selectedHiringManager = [...this.hiringManagerEmployeeIds];
      // Optionally, re-fetch data to ensure UI is synced with the server
      this.getRoleBasedJobPostMember();
      // Close any open forms or modals, if applicable
      this.$emit("edit-closed");
      // Reset change flag
      this.hasChanges = false;
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async getCustomGroupCoverage() {
      let vm = this;
      await vm.$apollo
        .query({
          query: GET_JOB_HEADER,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
          variables: {
            formId: 15,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.recruitmentSetting &&
            response.data.recruitmentSetting.settingResult &&
            response.data.recruitmentSetting.settingResult.length
          ) {
            this.isCentralisedRecruitment =
              response.data.recruitmentSetting.settingResult[0].Centralised_Recruitment;
          }
        })
        .catch((err) => {
          vm.handleGetCustomGroupCoverageError(err);
        });
    },
    handleGetCustomGroupCoverageError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "custom group settings",
        isListError: false,
      });
    },
    async checkCentralisedRecruitment() {
      await this.getCustomGroupCoverage();
      this.fetchRecruiterList();
      this.fetchOnboardingSpecialistList();
    },
  },
};
</script>

<style scoped>
.content {
  margin-bottom: 5%;
}
</style>
