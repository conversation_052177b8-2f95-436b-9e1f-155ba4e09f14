<template>
  <div v-if="isMounted">
    <section>
      <div>
        <v-card
          class="py-9 rounded-lg"
          :class="isMobileView ? '' : 'px-5'"
          elevation="5"
        >
          <v-card-text>
            <v-form ref="payrollGeneralSettings">
              <v-row class="d-flex justify-space-between mb-4">
                <div class="d-flex align-center">
                  <v-progress-circular
                    model-value="100"
                    color="primary"
                    :size="22"
                    class="mr-1"
                  ></v-progress-circular>
                  <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                    accessFormName
                  }}</span>
                </div>
                <div
                  class="d-flex align-center pa-1"
                  :class="isMobileView ? 'ml-auto' : ''"
                >
                  <v-btn
                    rounded="lg"
                    variant="outlined"
                    color="primary"
                    class="mr-2"
                    @click="closeEditForm()"
                  >
                    Cancel
                  </v-btn>
                  <div class="mt-2 mr-1">
                    <v-btn
                      v-if="isFormDirty"
                      rounded="lg"
                      variant="elevated"
                      class="mb-2 primary"
                      @click="validatePayrollGeneralSettingsForm()"
                      >Save</v-btn
                    >
                    <v-tooltip v-else location="bottom">
                      <template v-slot:activator="{ props }">
                        <v-btn
                          v-bind="props"
                          rounded="lg"
                          class="cursor-not-allow mb-2 primary"
                          variant="elevated"
                          >Save</v-btn
                        >
                      </template>
                      <div>There are no changes to be updated</div>
                    </v-tooltip>
                  </div>
                </div>
              </v-row>
              <v-row>
                <v-col
                  v-if="getFieldAlias[78].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                  class="d-flex"
                >
                  <CustomSelect
                    :items="payrollCountryList"
                    :itemSelected="payrollCountryId"
                    item-title="payrollCountryName"
                    item-value="payrollCountryId"
                    :label="getFieldAlias[78].Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias[78].Mandatory_Field == 'Yes' ? true : false
                    "
                    :is-loading="isListLoading"
                    :disabled="
                      isListLoading || syncTaxStatutoryCompliance === 'Yes'
                    "
                    @selected-item="
                      onChangeIsFormDirty($event, 'payrollCountryId')
                    "
                    :rules="[
                      getFieldAlias[78].Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias[78].Field_Alias}`,
                            payrollCountryId
                          )
                        : true,
                    ]"
                    listWidth="max-width: 300px !important"
                    style="max-width: 300px"
                  ></CustomSelect>
                </v-col>
                <v-col
                  cols="12"
                  lg="6"
                  md="6"
                  v-if="
                    getFieldAlias[79].Field_Visiblity == 'Yes' &&
                    payrollCountryId === 'PH'
                  "
                >
                  <div class="d-flex">
                    <span class="v-label pr-3 pb-5">{{
                      getFieldAlias[79].Field_Alias
                    }}</span>
                    <v-switch
                      color="primary"
                      class="ml-2"
                      v-model="slabWisePF"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias[80].Field_Visiblity == 'Yes' &&
                    payrollCountryId === 'PH'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex">
                    <span class="v-label pr-3 pb-5">{{
                      getFieldAlias[80].Field_Alias
                    }}</span>
                    <v-switch
                      color="primary"
                      class="ml-2"
                      v-model="slabWiseNPS"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col
                  v-if="getFieldAlias[81].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex">
                    <span class="v-label pr-3 pb-5">{{
                      getFieldAlias[81].Field_Alias
                    }}</span>
                    <v-switch
                      color="primary"
                      class="ml-2"
                      v-model="includeCurrencyFormatBasedOnCountry"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" lg="6" md="6">
                  <div class="d-flex">
                    <span class="v-label pr-3 pb-5"
                      >Enable Salary Template</span
                    >
                    <v-switch
                      color="primary"
                      class="ml-2"
                      v-model="enableSalaryTemplate"
                      :disabled="syncTaxStatutoryCompliance === 'Yes'"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </div>
      <AppWarningModal
        v-if="openConfirmationPopup"
        :open-modal="openConfirmationPopup"
        confirmation-heading="Are you sure to exit this form?"
        imgUrl="common/exit_form"
        @close-warning-modal="abortClose()"
        @accept-modal="acceptClose()"
      >
      </AppWarningModal>
    </section>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { UPDATE_PAYROLL_GENERAL_SETTINGS } from "@/graphql/tax-and-statutory-compliance/payrollGeneralSettings";

export default {
  name: "EditpayrollGeneralSettings",
  mixins: [validationRules],
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    accessFormName: {
      type: String,
      required: true,
    },
    getFieldAlias: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {
    CustomSelect,
  },
  data() {
    return {
      isLoading: false,
      isMounted: false,
      isFormDirty: false,
      openConfirmationPopup: false,
      slabWiseNPS: null,
      slabWisePF: null,
      payrollCountryId: null,
      payrollCountryName: null,
      includeCurrencyFormatBasedOnCountry: null,
      showValidationAlert: false,
      validationMessages: [],
      payrollCountryList: [
        { payrollCountryName: "India", payrollCountryId: "IN" },
        { payrollCountryName: "Philippines", payrollCountryId: "PH" },
        { payrollCountryName: "Ghana", payrollCountryId: "GH" },
      ],
      isListLoading: false,
      settingsId: null,
      syncTaxStatutoryCompliance: null,
      enableSalaryTemplate: null,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    const {
      Settings_Id,
      Payroll_Country,
      Slab_Wise_PF,
      Payroll_Country_Name,
      Slab_Wise_NPS,
      Include_Currency_Format_Based_On_Country,
      Sync_Tax_Statutory_Compliance,
      Enable_Salary_Template,
    } = this.editFormData;
    this.settingsId = Settings_Id ? Settings_Id : 1;
    this.syncTaxStatutoryCompliance = Sync_Tax_Statutory_Compliance;
    this.slabWiseNPS =
      this.getFieldAlias[80].Field_Visiblity == "Yes" && Slab_Wise_NPS
        ? Slab_Wise_NPS
        : "No";
    this.slabWisePF =
      this.getFieldAlias[79].Field_Visiblity == "Yes" && Slab_Wise_PF
        ? Slab_Wise_PF
        : "No";

    this.payrollCountryId =
      this.getFieldAlias[78].Field_Visiblity == "Yes" && Payroll_Country
        ? Payroll_Country
        : null;
    this.payrollCountryName =
      this.getFieldAlias[78].Field_Visiblity == "Yes" && Payroll_Country_Name
        ? Payroll_Country_Name
        : null;
    this.includeCurrencyFormatBasedOnCountry =
      this.getFieldAlias[81].Field_Visiblity == "Yes" &&
      Include_Currency_Format_Based_On_Country
        ? Include_Currency_Format_Based_On_Country
        : "No";
    this.enableSalaryTemplate = Enable_Salary_Template == 1 ? "Yes" : "No";
    this.isMounted = true;
  },
  methods: {
    onChangeIsFormDirty(val, field) {
      if (field == "payrollCountryId") {
        this.payrollCountryId = val;
      }
      this.isFormDirty = true;
    },
    async validatePayrollGeneralSettingsForm() {
      const { valid } = await this.$refs.payrollGeneralSettings.validate();
      if (valid) {
        this.updatepayrollGeneralSettings();
      }
    },
    updatepayrollGeneralSettings() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_PAYROLL_GENERAL_SETTINGS,
            variables: {
              settingsId: vm.settingsId ? vm.settingsId : 1,
              slabWiseNPS: vm.slabWiseNPS ? vm.slabWiseNPS : "No",
              slabWisePF: vm.slabWisePF ? vm.slabWisePF : "No",
              includeCurrencyFormatBasedOnCountry:
                vm.includeCurrencyFormatBasedOnCountry
                  ? vm.includeCurrencyFormatBasedOnCountry
                  : "No",
              payrollCountry: vm.payrollCountryId ? vm.payrollCountryId : null,
              enableSalaryTemplate: vm.enableSalaryTemplate
                ? vm.enableSalaryTemplate
                : "No",
            },
            client: "apolloClientAK",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: `${this.accessFormName} updated successfully.`,
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((error) => {
            vm.handleUpdateError(error);
          });
      } catch {
        vm.handleUpdateError();
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: this.accessFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    closeEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
      this.isFormDirty = false;
    },
  },
};
</script>
