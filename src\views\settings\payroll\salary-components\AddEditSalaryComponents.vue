<template>
  <v-overlay
    :model-value="props.showForm"
    class="d-flex justify-end"
    :persistent="true"
    @click:outside="onCloseOverlay()"
  >
    <v-card :height="windowInnerHeight + 'px'" :width="componentWidth">
      <v-card-title
        class="d-flex align-center"
        :class="
          windowWidth < 700
            ? 'justify-end pa-0'
            : 'justify-space-between bg-primary'
        "
      >
        <div v-if="windowWidth >= 700" class="text-h6">
          {{ componentHeader }}
        </div>
        <v-btn
          v-if="windowWidth >= 700"
          icon="fas fa-times"
          variant="text"
          @click="onCloseOverlay()"
          color="white"
        ></v-btn>
        <v-card v-else class="d-flex justify-end fixed-title pa-2" width="100%">
          <v-btn
            class="mr-2"
            variant="text"
            elevation="4"
            rounded="lg"
            color="primary"
            @click="onCloseOverlay()"
            >{{ t("common.cancel") }}</v-btn
          >
          <v-btn
            color="primary"
            rounded="lg"
            :disabled="!isFormDirty"
            @click="validateForm()"
            >{{ t("common.save") }}</v-btn
          >
        </v-card>
      </v-card-title>
      <v-card-text
        class="pt-2 overflow-y-auto"
        :style="{
          height:
            windowWidth >= 700 ? 'calc(100% - 115px)' : 'calc(100% - 50px)',
        }"
      >
        <v-form ref="form">
          <v-row>
            <v-col
              cols="12"
              :sm="currentTab === t('settings.bonus') ? '12' : '7'"
            >
              <v-row>
                <v-col
                  v-if="currentTab === t('settings.reimbursements')"
                  cols="12"
                  sm="6"
                >
                  <CustomSelect
                    variant="solo"
                    density="comfortable"
                    :label="t('settings.reimbursementType')"
                    :items="[
                      'Fuel Reimbursement',
                      'Driver Reimbursement',
                      'Telephone Reimbursement',
                      'Leave Travel Allowance',
                      'Vehicle Maintenance Reimbursement',
                    ]"
                    :rules="[
                      required(
                        t('settings.reimbursementType'),
                        reimbursementType
                      ),
                    ]"
                    :isRequired="true"
                    :is-auto-complete="true"
                    :item-selected="reimbursementType"
                    @selected-item="
                      onChangeCustomSelect($event, 'reimbursementType')
                    "
                  >
                  </CustomSelect>
                </v-col>
                <v-col cols="12" sm="6">
                  <v-text-field
                    v-model="allowanceName"
                    variant="solo"
                    density="comfortable"
                    :rules="[
                      required(allowanceNameLabel, allowanceName),
                      validateWithRulesAndReturnMessages(
                        allowanceName,
                        'allowanceName',
                        allowanceNameLabel
                      ),
                    ]"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      <span>{{ allowanceNameLabel }}</span>
                      <span class="text-red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col
                  v-if="currentTab !== t('settings.bonus')"
                  cols="12"
                  sm="6"
                >
                  <span class="text-subtitle-1 text-grey-darken-1 ml-2">
                    {{ t("settings.fbpComponent") }}
                  </span>
                  <v-tooltip
                    text="Basic Allowance and Formula Based Allowance cannot be a FBP Component"
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <div
                        v-bind="
                          selectedItem?.Is_Basic_Pay?.toLowerCase() === 'yes' ||
                          selectedItem?.Formula_Based?.toLowerCase() === 'yes'
                            ? props
                            : ''
                        "
                      >
                        <v-switch
                          color="primary"
                          v-model="isFBPComponent"
                          density="comfortable"
                          :disabled="
                            selectedItem?.Is_Basic_Pay?.toLowerCase() ===
                              'yes' ||
                            selectedItem?.Formula_Based?.toLowerCase() === 'yes'
                          "
                          hide-details
                          :true-value="'Yes'"
                          :false-value="'No'"
                          class="ml-3"
                          @update:model-value="onChangeFbpFlag($event)"
                        ></v-switch>
                      </div>
                    </template>
                  </v-tooltip>
                </v-col>
                <v-col v-if="isFBPComponent === 'No'" cols="12" sm="6">
                  <CustomSelect
                    variant="solo"
                    density="comfortable"
                    :label="t('settings.calculationType')"
                    :items="['Amount', 'Percentage']"
                    :rules="[
                      required(t('settings.calculationType'), calculationType),
                    ]"
                    :isRequired="true"
                    :item-selected="calculationType"
                    @selected-item="
                      onChangeCustomSelect($event, 'calculationType')
                    "
                  >
                  </CustomSelect>
                </v-col>
                <v-col
                  v-if="calculationType === 'Amount' && isFBPComponent === 'No'"
                  cols="12"
                  sm="6"
                >
                  <v-text-field
                    v-model="amount"
                    variant="solo"
                    density="comfortable"
                    :rules="[
                      required(t('settings.amount'), amount),
                      minMaxNumberValidation(
                        t('settings.amount'),
                        amount,
                        0,
                        99999999
                      ),
                      twoDecimalPrecisionValidation(amount),
                    ]"
                    type="number"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      {{ t("settings.amount") }}
                      <span class="text-red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col
                  v-if="
                    calculationType === 'Percentage' && isFBPComponent === 'No'
                  "
                  cols="12"
                  sm="6"
                >
                  <v-text-field
                    v-model="percentage"
                    variant="solo"
                    density="comfortable"
                    :rules="[
                      required(t('settings.percentage'), percentage),
                      minMaxNumberValidation(
                        t('settings.percentage'),
                        percentage,
                        0,
                        100
                      ),
                      twoDecimalPrecisionValidation(percentage),
                    ]"
                    type="number"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      {{ t("settings.percentage") }}
                      <span class="text-red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col v-if="isFBPComponent === 'Yes'" cols="12" sm="6">
                  <v-text-field
                    v-model="fbpMaxAmount"
                    variant="solo"
                    density="comfortable"
                    :rules="[
                      required(t('settings.maxLimit'), fbpMaxAmount),
                      minMaxNumberValidation(
                        t('settings.maxLimit'),
                        fbpMaxAmount,
                        0,
                        99999999
                      ),
                      twoDecimalPrecisionValidation(fbpMaxAmount),
                    ]"
                    type="number"
                    @update:model-value="isFormDirty = true"
                  >
                    <template v-slot:label>
                      {{ t("settings.maxLimit") }}
                      <span class="text-red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col
                  v-if="
                    currentTab !== t('settings.bonus') &&
                    isFBPComponent?.toLowerCase() === 'yes'
                  "
                  cols="12"
                  sm="6"
                >
                  <div class="text-subtitle-1 text-grey-darken-1 ml-2">
                    {{ t("settings.restrictEmployeeFromOverridingFBPAmount") }}
                  </div>
                  <v-switch
                    color="primary"
                    v-model="restrictEmployeeFbpOverride"
                    density="comfortable"
                    hide-details
                    :true-value="'Yes'"
                    :false-value="'No'"
                    class="ml-3"
                    @update:model-value="isFormDirty = true"
                  ></v-switch>
                </v-col>
                <v-col
                  v-if="currentTab !== t('settings.reimbursements')"
                  cols="12"
                  sm="6"
                  class="d-flex"
                >
                  <CustomSelect
                    variant="solo"
                    density="comfortable"
                    :label="t('settings.taxInclusion')"
                    :items="['Taxable', 'Non Taxable']"
                    :rules="[
                      required(t('settings.taxInclusion'), taxInclusion),
                    ]"
                    :isRequired="true"
                    :item-selected="taxInclusion"
                    @selected-item="
                      onChangeCustomSelect($event, 'taxInclusion')
                    "
                  >
                  </CustomSelect>
                  <v-tooltip
                    text="The income tax amount will be divided equally and deducted every month across the financial year."
                    location="bottom"
                  >
                    <template v-slot:activator="{ props }">
                      <v-icon
                        v-bind="props"
                        size="small"
                        color="blue"
                        class="ml-2 mt-4"
                      >
                        fas fa-info-circle
                      </v-icon>
                    </template>
                  </v-tooltip>
                </v-col>
                <v-col
                  v-if="currentTab === t('settings.reimbursements')"
                  cols="12"
                  sm="6"
                >
                  <CustomSelect
                    variant="solo"
                    density="comfortable"
                    :label="t('settings.workFlow')"
                    :items="workflowList"
                    :loading="workflowListLoading"
                    :item-title="'Workflow_Name'"
                    item-value="Workflow_Id"
                    :is-auto-complete="true"
                    :item-selected="workflow"
                    @selected-item="onChangeCustomSelect($event, 'workflow')"
                  >
                  </CustomSelect>
                </v-col>
                <v-col
                  v-if="currentTab !== t('settings.bonus')"
                  cols="12"
                  sm="6"
                >
                  <CustomSelect
                    variant="solo"
                    density="comfortable"
                    :label="t('settings.perks')"
                    item-title="label"
                    item-value="value"
                    :items="perksList"
                    :is-auto-complete="true"
                    :item-selected="perks"
                    :loading="perksListLoading"
                    @selected-item="onChangeCustomSelect($event, 'perks')"
                  >
                  </CustomSelect>
                </v-col>
                <v-col cols="12" sm="6">
                  <span class="text-subtitle-1 text-grey-darken-1 ml-2">
                    {{ t("settings.asIsPayment") }}
                  </span>
                  <v-switch
                    color="primary"
                    v-model="asIsPayment"
                    density="comfortable"
                    hide-details
                    :true-value="'Yes'"
                    :false-value="'No'"
                    class="ml-3"
                    @update:model-value="isFormDirty = true"
                  ></v-switch>
                </v-col>
                <v-col cols="12" sm="6">
                  <CustomSelect
                    variant="solo"
                    density="comfortable"
                    :label="t('settings.period')"
                    :items="periodList"
                    item-title="text"
                    item-value="value"
                    :rules="[required(t('settings.period'), period)]"
                    :is-auto-complete="true"
                    :disabled="
                      currentTab === t('settings.earnings') ||
                      currentTab === t('settings.reimbursements')
                    "
                    :isRequired="true"
                    :item-selected="period"
                    @selected-item="onChangeCustomSelect($event, 'period')"
                  >
                  </CustomSelect>
                </v-col>
                <v-col cols="12" sm="6">
                  <div class="v-label ml-2 mb-2">
                    {{ t("settings.status") }}
                  </div>
                  <v-tooltip
                    :text="
                      props.selectedItem?.Associated_Templates?.length
                        ? 'You cannot change the status of a component that is already associated with a template.'
                        : 'Basic Allowance and Formula Based Allowance cannot be Inactive'
                    "
                    location="bottom"
                  >
                    <template v-slot:activator="{ props: tooltipProps }">
                      <AppToggleButton
                        v-bind="
                          selectedItem?.Is_Basic_Pay?.toLowerCase() === 'yes' ||
                          selectedItem?.Formula_Based?.toLowerCase() ===
                            'yes' ||
                          props.selectedItem?.Associated_Templates?.length
                            ? tooltipProps
                            : ''
                        "
                        :button-active-text="t('common.active')"
                        :button-inactive-text="t('common.inactive')"
                        :isDisableToggle="
                          selectedItem?.Is_Basic_Pay?.toLowerCase() === 'yes' ||
                          selectedItem?.Formula_Based?.toLowerCase() ===
                            'yes' ||
                          props.selectedItem?.Associated_Templates?.length
                        "
                        button-active-color="#7de272"
                        button-inactive-color="red"
                        id-value="gab-analysis-based-on"
                        :current-value="status === 'Active' ? true : false"
                        @chosen-value="onChangeStatus($event)"
                      >
                      </AppToggleButton>
                    </template>
                  </v-tooltip>
                </v-col>
                <v-col cols="12">
                  <v-textarea
                    v-model="description"
                    variant="solo"
                    density="comfortable"
                    :label="t('settings.description')"
                    auto-grow
                    rows="3"
                    :rules="[
                      description
                        ? validateWithRulesAndReturnMessages(
                            description,
                            'description',
                            t('settings.description')
                          )
                        : true,
                    ]"
                    @update:model-value="isFormDirty = true"
                  ></v-textarea>
                </v-col>
                <v-col
                  v-if="props.selectedItem?.Associated_Templates?.length"
                  cols="12"
                >
                  <NotesCard
                    notes="Note: As you've already associated this component with one or more employees, you can only edit the Name and Amount/Percentage. The changes made to Amount/Percentage will apply only to new employees."
                    imageName=""
                    class="mt-n5 mb-1"
                  ></NotesCard>
                </v-col>
              </v-row>
            </v-col>
            <v-col
              v-if="currentTab !== t('settings.bonus')"
              :class="isMobileView ? '' : 'd-flex'"
              cols="12"
              sm="5"
            >
              <v-divider
                style="height: 100%"
                :thickness="3"
                :vertical="!isMobileView"
              ></v-divider>
              <div class="ml-3">
                <span class="text-h5 font-weight-medium text-grey-darken-1">{{
                  t("settings.benefitsAssociation")
                }}</span>
                <div v-if="benefitsAssociationListLoading" class="mt-3 w-100">
                  <v-skeleton-loader
                    ref="skeleton1"
                    type="table-heading"
                    class="mx-auto"
                  ></v-skeleton-loader>
                  <div v-for="i in 3" :key="i" class="mt-4">
                    <v-skeleton-loader
                      ref="skeleton2"
                      type="list-item-avatar"
                      class="mx-auto"
                    ></v-skeleton-loader>
                  </div>
                </div>
                <div v-else>
                  <div
                    v-for="item of benefitsAssociationList"
                    :key="item.value"
                    cols="12"
                  >
                    <div class="d-flex align-center">
                      <v-tooltip
                        text="You cannot change the benefits that is already associated with a template."
                      >
                        <template v-slot:activator="{ props }">
                          <div
                            v-bind="disabledBenefits(item.value) ? props : ''"
                          >
                            <v-checkbox
                              v-model="benefitsAssociation[item.value]"
                              color="primary"
                              density="compact"
                              hide-details
                              :disabled="disabledBenefits(item.value)"
                              @update:model-value="isFormDirty = true"
                            ></v-checkbox>
                          </div>
                        </template>
                      </v-tooltip>
                      <span class="ml-2 text-subtitle-1">
                        {{ `Consider for ${item.label}` }}
                      </span>
                    </div>
                    <v-radio-group
                      v-if="item.value === '52' && showEPfField"
                      v-model="considerForEPF"
                      :disabled="disabledBenefits(item.value)"
                      class="ml-6 custom-radio-group"
                      density="comfortable"
                      color="primary"
                      mandatory
                      hide-details
                      @update:model-value="isFormDirty = true"
                    >
                      <v-radio label="Always" value="Always"></v-radio>
                      <v-radio
                        label="Only when PF Wage is less than ₹15,000"
                        value="Only when PF Wage is less than ₹15,000"
                      >
                        <template v-slot:label>
                          <span>Only when PF Wage is less than ₹15,000</span>
                          <v-tooltip
                            text="If the sum of PF Wage exceeds ₹ 15,000 when this component is included, the EPF will be calculated only for a maximum amount of ₹ 15,000."
                            location="bottom"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="small"
                                color="blue"
                                class="ml-2"
                              >
                                fas fa-info-circle
                              </v-icon>
                            </template>
                          </v-tooltip>
                        </template>
                      </v-radio>
                    </v-radio-group>
                  </div>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      <v-card
        v-if="windowWidth >= 700"
        class="pa-2 d-flex justify-end"
        elevation="16"
        height="50px"
      >
        <v-btn
          class="mr-2"
          variant="text"
          elevation="4"
          rounded="lg"
          color="primary"
          @click="onCloseOverlay()"
          >{{ t("common.cancel") }}</v-btn
        >
        <v-btn color="primary" rounded="lg" @click="validateForm()">
          {{ t("common.save") }}
        </v-btn>
      </v-card>

      <AppLoading v-if="isLoading" />
    </v-card>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationPopup"
    :open-modal="openConfirmationPopup"
    :confirmation-heading="t('common.formExitWarning')"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationPopup = false"
    @accept-modal="onAcceptModal()"
  />
</template>
<script setup>
import {
  computed,
  defineProps,
  defineEmits,
  ref,
  defineAsyncComponent,
  onMounted,
  watch,
  getCurrentInstance,
} from "vue";
import { useValidation } from "@/composables/validationComposables";
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);

// Composables
const store = useStore();
const { t } = useI18n();
const instance = getCurrentInstance();
const {
  required,
  // minMaxStringValidation,
  twoDecimalPrecisionValidation,
  minMaxNumberValidation,
  validateWithRulesAndReturnMessages,
} = useValidation();
const props = defineProps({
  showForm: {
    type: Boolean,
    default: false,
  },
  selectedItem: {
    type: Object,
    default: () => ({}),
  },
  currentTab: {
    type: String,
    required: true,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  formId: {
    type: Number,
    default: 0,
  },
});
const componentHeader = computed(() => {
  if (props.currentTab === t("settings.earnings")) {
    if (props.selectedItem) {
      return t("settings.editEarnings");
    } else {
      return t("settings.addEarnings");
    }
  } else if (props.currentTab === t("settings.reimbursements")) {
    if (props.selectedItem) {
      return t("settings.editReimbursements");
    } else {
      return t("settings.addReimbursements");
    }
  } else {
    if (props.selectedItem) {
      return t("settings.editBonus");
    } else {
      return t("settings.addBonus");
    }
  }
});
const emit = defineEmits(["close-form", "add-edit-success"]);
const onCloseOverlay = () => {
  if (isFormDirty.value) {
    openConfirmationPopup.value = true;
  } else {
    emit("close-form");
  }
};
const onAcceptModal = () => {
  emit("close-form");
  isFormDirty.value = false;
  openConfirmationPopup.value = false;
  form.value.reset();
};
const openConfirmationPopup = ref(false);
const isLoading = ref(false);
const windowInnerHeight = computed(() => {
  return store.state.windowInnerHeight;
});
const windowWidth = computed(() => {
  return store.state.windowWidth;
});
const isMobileView = computed(() => {
  return store.state.isMobileWindowSize;
});
const componentWidth = computed(() => {
  if (props.currentTab === t("settings.bonus")) {
    if (windowWidth.value > 1410) {
      return "40vw";
    } else if (windowWidth.value > 1264 && windowWidth.value < 1410) {
      return "50vw";
    } else if (windowWidth.value < 1264 && windowWidth.value > 810) {
      return "70vw";
    } else {
      return "100vw";
    }
  }
  if (windowWidth.value > 1410) {
    return "60vw";
  } else if (windowWidth.value > 1264 && windowWidth.value < 1410) {
    return "70vw";
  } else if (windowWidth.value < 1264 && windowWidth.value > 810) {
    return "90vw";
  } else {
    return "100vw";
  }
});

onMounted(() => {
  if (props.isEdit) {
    setValues();
  } else {
    if (
      props.currentTab === t("settings.earnings") ||
      props.currentTab === t("settings.reimbursements")
    ) {
      period.value = "Monthly";
    }
    taxInclusion.value = "Taxable";
  }
  if (props.formId === 382) {
    getWorkflowList();
  }
  if (props.formId === 381 || props.formId === 382) {
    getBenefitsAssociationList();
    getPerksList();
  }
});
watch(
  () => props.selectedItem,
  (newVal) => {
    if (newVal) {
      setValues();
    } else {
      // Reset benefits association when adding new item
      benefitsAssociation.value = {};
    }
  },
  { deep: true }
);
const setValues = () => {
  allowanceName.value = props.selectedItem.Allowance_Name;
  taxInclusion.value = props.selectedItem.Tax_Inclusion;
  asIsPayment.value = props.selectedItem.Allowance_As_Is_Payment;
  isFBPComponent.value = props.selectedItem.Is_Flexi_Benefit_Plan;
  period.value = props.selectedItem.Period;
  reimbursementType.value = props.selectedItem.Reimbursement_Type;
  calculationType.value = props.selectedItem.Calculation_Type;
  amount.value = props.selectedItem.Allowance_Amount?.toString();
  percentage.value = props.selectedItem.Allowance_Percentage;
  fbpMaxAmount.value = props.selectedItem.FBP_Max_Declaration_Amount;
  considerForEPF.value = props.selectedItem.Consider_For_EPF_Contribution;
  restrictEmployeeFbpOverride.value =
    props.selectedItem.Restrict_Employee_FBP_Override;
  workflow.value = props.selectedItem.Workflow_Id;
  status.value = props.selectedItem.AllowanceType_Status;
  description.value = props.selectedItem.Description;
  perks.value = props.selectedItem.Perquisites_Id;

  // Initialize benefits association for editing
  if (
    props.selectedItem.Benefits_Association &&
    Array.isArray(props.selectedItem.Benefits_Association)
  ) {
    const benefitsObj = {};
    props.selectedItem.Benefits_Association.forEach((formId) => {
      benefitsObj[formId] = true;
    });
    benefitsAssociation.value = benefitsObj;
  } else {
    benefitsAssociation.value = {};
  }

  isFormDirty.value = false;
};

// Field Data
const allowanceNameLabel = computed(() => {
  return props.currentTab === t("settings.earnings")
    ? t("settings.earningName")
    : props.currentTab === t("settings.reimbursements")
    ? t("settings.reimbursementName")
    : t("settings.bonusName");
});
const isFormDirty = ref(false);
const allowanceName = ref(null);
const taxInclusion = ref(null);
const asIsPayment = ref("No");
const isFBPComponent = ref("No");
const perks = ref([]);
const period = ref(null);
const periodList = ref([
  {
    text: "Monthly",
    value: "Monthly",
  },
  {
    text: "Quarterly",
    value: "Quarterly",
  },
  {
    text: "Half Yearly",
    value: "HalfYearly",
  },
  {
    text: "Annually",
    value: "Annually",
  },
]);
const reimbursementType = ref(null);
const calculationType = ref(null);
const amount = ref(null);
const percentage = ref(null);
const fbpMaxAmount = ref(null);
const considerForEPF = ref(null);
const benefitsAssociation = ref({});
const workflow = ref(null);
const status = ref("Active");
const description = ref(null);
const restrictEmployeeFbpOverride = ref("No");
const onChangeCustomSelect = (value, field) => {
  isFormDirty.value = true;
  if (field === "taxInclusion") {
    taxInclusion.value = value;
  } else if (field === "period") {
    period.value = value;
  } else if (field === "calculationType") {
    calculationType.value = value;
    percentage.value = "0";
    amount.value = "0";
  } else if (field === "perks") {
    perks.value = value;
  } else if (field === "reimbursementType") {
    reimbursementType.value = value;
    allowanceName.value = value;
  } else if (field === "workflow") {
    workflow.value = value;
  }
};

watch(
  () => benefitsAssociation.value["52"],
  (newVal) => {
    if (newVal && !considerForEPF.value) {
      considerForEPF.value = "Always";
    }
  }
);
const onChangeStatus = (value) => {
  isFormDirty.value = true;
  status.value = value[1] ? "Active" : "Inactive";
};
const onChangeFbpFlag = (value) => {
  if (value === "Yes") {
    calculationType.value = "Amount";
    amount.value = "0";
  } else {
    calculationType.value = null;
    fbpMaxAmount.value = null;
    amount.value = null;
    percentage.value = null;
  }
  isFormDirty.value = true;
};
const showEPfField = computed(() => {
  return benefitsAssociation.value["52"];
});
const disabledBenefits = (benefitId) => {
  return (
    props.selectedItem?.Associated_Templates?.length > 0 &&
    (benefitId == "52" ||
      benefitId == "58" ||
      benefitId == "126" ||
      benefitId == "110" ||
      benefitId == "46")
  );
};

// Fetching Dropdowns
const perksList = ref([]);
const perksListLoading = ref(false);
const benefitsAssociationList = ref([]);
const benefitsAssociationListLoading = ref(false);
const workflowList = ref([]);
const workflowListLoading = ref(false);
import { RETRIEVE_PERKS_LIST } from "@/graphql/settings/salaryComponents.js";
const getPerksList = () => {
  perksListLoading.value = true;
  instance.proxy.$apollo
    .query({
      query: RETRIEVE_PERKS_LIST,
      client: "apolloClientF",
      variables: {
        formId: props.formId,
      },
      fetchPolicy: "no-cache",
    })
    .then(({ data }) => {
      if (data?.getPerquisites?.perquisites) {
        let perksListData = JSON.parse(data.getPerquisites.perquisites);
        if (Object.keys(perksListData).length) {
          perksListData = Object.keys(perksListData).map((item) => {
            return {
              label: perksListData[item],
              value: parseInt(item),
            };
          });
        }
        perksList.value = perksListData;
      } else {
        perksList.value = [];
      }
      perksListLoading.value = false;
    })
    .catch(() => {
      perksListLoading.value = false;
      perksList.value = [];
    });
};
import { RETRIEVE_BENEFITS_ASSOCIATION_LIST } from "@/graphql/settings/salaryComponents.js";
const getBenefitsAssociationList = () => {
  benefitsAssociationListLoading.value = true;
  instance.proxy.$apollo
    .query({
      query: RETRIEVE_BENEFITS_ASSOCIATION_LIST,
      client: "apolloClientF",
      variables: {
        formId: props.formId,
        all: true,
      },
      fetchPolicy: "no-cache",
    })
    .then(({ data }) => {
      if (data?.getBenefitForms?.benefitForms) {
        let benefitsAssociationListData = JSON.parse(
          JSON.parse(data.getBenefitForms.benefitForms)
        );
        let list = benefitsAssociationListData.map((item) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
        benefitsAssociationList.value = list;
      } else {
        benefitsAssociationList.value = [];
      }
      benefitsAssociationListLoading.value = false;
    })
    .catch(() => {
      benefitsAssociationListLoading.value = false;
      benefitsAssociationList.value = [];
    });
};
const getWorkflowList = () => {
  workflowListLoading.value = true;
  store
    .dispatch("listWorkflowDetailsBasedOnFormName", {
      formName: "Reimbursement",
      formId: 267,
    })
    .then((workflow) => {
      if (workflow && workflow.length > 0) {
        workflowList.value = workflow;
      }
      workflowListLoading.value = false;
    })
    .catch(() => {
      workflowListLoading.value = false;
      workflowList.value = [];
    });
};

// Methods
let form = ref("");
const validateForm = async () => {
  let { valid } = await form.value.validate();
  if (valid) {
    let variables = {};
    if (props.formId === 381) {
      let benefitAssociation = [];
      Object.keys(benefitsAssociation.value).forEach((key) => {
        if (benefitsAssociation.value[key]) {
          benefitAssociation.push(parseInt(key));
        }
      });
      variables = {
        formId: 381,
        allowanceTypeId: props.selectedItem?.Allowance_Type_Id,
        allowanceName: allowanceName.value,
        isFlexiBenefitPlan: isFBPComponent.value,
        allowanceType: calculationType.value,
        amount: parseFloat(amount.value),
        percentage: parseFloat(percentage.value),
        fbpMaxDeclaration:
          isFBPComponent.value === "Yes"
            ? parseFloat(fbpMaxAmount.value)
            : null,
        asIsPayment: asIsPayment.value,
        taxInclusion: taxInclusion.value,
        restrictEmployeeFbpOverride: restrictEmployeeFbpOverride.value,
        benefitAssociation: benefitAssociation,
        epfContribution: showEPfField.value ? considerForEPF.value : null,
        perquisitesId: parseInt(perks.value),
        period: period.value,
        allowanceTypeStatus: status.value,
        description: description.value,
        allowanceMode: "Non Bonus",
        isClaimFromReimbursement: "No",
        reimbursementType: null,
        workflowId: null,
        formulaBased: props.selectedItem?.Formula_Based || "No",
        isBasicPay: props.selectedItem?.Is_Basic_Pay || "No",
        unclaimedLTA: null,
        ltaStartYear: null,
        endMonth: null,
        numberOfClaims: null,
        allowanceSequence: null,
      };
    } else if (props.formId === 382) {
      let benefitAssociation = [];
      Object.keys(benefitsAssociation.value).forEach((key) => {
        if (benefitsAssociation.value[key]) {
          benefitAssociation.push(parseInt(key));
        }
      });
      variables = {
        formId: 382,
        allowanceTypeId: props.selectedItem?.Allowance_Type_Id,
        allowanceName: allowanceName.value,
        isFlexiBenefitPlan: isFBPComponent.value,
        allowanceType: calculationType.value,
        amount: parseFloat(amount.value),
        percentage: parseFloat(percentage.value),
        fbpMaxDeclaration:
          isFBPComponent.value === "Yes"
            ? parseFloat(fbpMaxAmount.value)
            : null,
        asIsPayment: asIsPayment.value,
        taxInclusion: "Non Taxable",
        restrictEmployeeFbpOverride: restrictEmployeeFbpOverride.value,
        benefitAssociation: benefitAssociation,
        epfContribution: null,
        perquisitesId: parseInt(perks.value),
        period: period.value,
        allowanceTypeStatus: status.value,
        description: description.value,
        allowanceMode: "Non Bonus",
        isClaimFromReimbursement: "Yes",
        reimbursementType: reimbursementType.value,
        workflowId: workflow.value,
      };
    } else if (props.formId === 383) {
      variables = {
        formId: 383,
        allowanceTypeId: props.selectedItem?.Allowance_Type_Id,
        allowanceName: allowanceName.value,
        isFlexiBenefitPlan: "No",
        allowanceType: calculationType.value,
        amount: parseFloat(amount.value),
        percentage: parseFloat(percentage.value),
        fbpMaxDeclaration: null,
        asIsPayment: asIsPayment.value,
        taxInclusion: taxInclusion.value,
        restrictEmployeeFbpOverride: "No",
        benefitAssociation: null,
        epfContribution: null,
        perquisitesId: parseInt(perks.value),
        period: period.value,
        allowanceTypeStatus: status.value,
        description: description.value,
        allowanceMode: "Bonus",
        isClaimFromReimbursement: "No",
        reimbursementType: null,
        workflowId: null,
      };
    }
    saveAllowance(variables);
  }
};

import { ADD_UPDATE_ALLOWANCE_TYPE } from "@/graphql/settings/salaryComponents.js";
const saveAllowance = (variables) => {
  isLoading.value = true;
  instance.proxy.$apollo
    .mutate({
      mutation: ADD_UPDATE_ALLOWANCE_TYPE,
      variables: variables,
      client: "apolloClientF",
      fetchPolicy: "no-cache",
    })
    .then(() => {
      isLoading.value = false;
      let formName = "";
      if (props.formId === 381) {
        formName = "Earning";
      } else if (props.formId === 382) {
        formName = "Reimbursement";
      } else if (props.formId === 383) {
        formName = "Bonus";
      }
      let snackbarData = {
        isOpen: true,
        message: props.isEdit
          ? `${formName} updated successfully`
          : `${formName} added successfully`,
        type: "success",
      };
      showAlert(snackbarData);
      emit("add-edit-success");
    })
    .catch((error) => {
      isLoading.value = false;
      let formName = "";
      if (props.formId === 381) {
        formName = "Earning";
      } else if (props.formId === 382) {
        formName = "Reimbursement";
      } else if (props.formId === 383) {
        formName = "Bonus";
      }
      let snackbarData = {
        isOpen: true,
        message:
          error?.message ||
          `Something went wrong while ${
            props.isEdit ? "updating" : "adding"
          } the ${formName}. Please try after some time.`,
        type: "warning",
      };
      showAlert(snackbarData);
    });
};
const showAlert = (snackbarData) => {
  store.commit("OPEN_SNACKBAR", snackbarData);
};
</script>
<style scoped>
:deep(.custom-radio-group .v-icon) {
  font-size: 16px !important;
}
</style>
