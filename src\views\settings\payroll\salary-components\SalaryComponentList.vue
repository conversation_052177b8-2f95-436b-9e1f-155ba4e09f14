<template>
  <div>
    <v-data-table
      class="mt-2"
      :headers="tableHeaders"
      :items="props.itemList"
      :search="props.searchValue"
      fixed-header
      :height="
        $store.getters.getTableHeightBasedOnScreenSize(310, props.itemList)
      "
      :items-per-page="50"
      :items-per-page-options="[
        { value: 50, title: '50' },
        { value: 100, title: '100' },
        {
          value: -1,
          title: '$vuetify.dataFooter.itemsPerPageAll',
        },
      ]"
      @update:current-items="emit('on-update-items', $event)"
    >
      <template v-slot:item="{ item }">
        <tr
          @click="onActions(t('common.view'), item)"
          class="data-table-tr bg-white cursor-pointer"
          :class="isMobileView ? ' v-data-table__mobile-table-row' : ''"
          :style="isMobileView ? `max-width: ${windowWidth - 70}px` : ''"
        >
          <td
            :class="isMobileView ? 'd-flex justify-space-between' : ''"
            :style="isMobileView ? `` : 'max-width: 300px;'"
          >
            <div
              v-if="isMobileView"
              class="d-flex align-center text-subtitle-1 text-grey-darken-1 mt-2"
            >
              {{
                props.currentTab === t("settings.earnings")
                  ? t("settings.earningName")
                  : props.currentTab === t("settings.reimbursements")
                  ? t("settings.reimbursementName")
                  : t("settings.bonusName")
              }}
            </div>
            <section
              class="text-primary font-weight-medium d-flex align-center text-truncate"
              :class="isMobileView ? 'justify-end' : ''"
              :style="isMobileView ? 'width: 50%;' : ''"
            >
              <v-tooltip :text="item.Allowance_Name" location="top">
                <template v-slot:activator="{ props }">
                  <span
                    class="text-truncate"
                    v-bind="item.Allowance_Name ? props : ''"
                  >
                    {{ checkNullValue(item.Allowance_Name) }}
                  </span>
                </template>
              </v-tooltip>
              <v-tooltip
                v-if="item.Is_Flexi_Benefit_Plan?.toLowerCase() === 'yes'"
                text="Flexi Benefit Plan"
              >
                <template v-slot:activator="{ props }">
                  <v-btn
                    class="ml-1"
                    size="x-small"
                    color="hover"
                    v-bind="props"
                  >
                    FBP
                  </v-btn>
                </template>
              </v-tooltip>
            </section>
          </td>
          <td :class="isMobileView ? 'd-flex justify-space-between' : ''">
            <div
              v-if="isMobileView"
              class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
            >
              {{ t("settings.calculationType") }}
            </div>
            <section class="d-flex align-center">
              {{ getCalculationTypeColumn(item) }}
            </section>
          </td>
          <td
            v-if="props.currentTab === t('settings.bonus')"
            :class="isMobileView ? 'd-flex justify-space-between' : ''"
          >
            <div
              v-if="isMobileView"
              class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
            >
              {{ t("settings.taxInclusion") }}
            </div>
            <section class="d-flex align-center">
              {{ checkNullValue(item.Tax_Inclusion) }}
            </section>
          </td>
          <td
            v-if="props.currentTab === t('settings.bonus')"
            :class="isMobileView ? 'd-flex justify-space-between' : ''"
          >
            <div
              v-if="isMobileView"
              class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
            >
              {{ t("settings.period") }}
            </div>
            <section class="d-flex align-center">
              {{ checkNullValue(periodList[item.Period]) }}
            </section>
          </td>
          <td
            v-if="props.currentTab === t('settings.reimbursements')"
            :class="isMobileView ? 'd-flex justify-space-between' : ''"
          >
            <div
              v-if="isMobileView"
              class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
            >
              {{ t("settings.reimbursementType") }}
            </div>
            <section class="d-flex align-center">
              {{ checkNullValue(item.Reimbursement_Type) }}
            </section>
          </td>
          <td
            v-if="props.currentTab === t('settings.earnings')"
            :class="isMobileView ? 'd-flex justify-space-between' : ''"
          >
            <div
              v-if="isMobileView"
              class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
            >
              {{ t("settings.considerForProvidentFund") }}
            </div>
            <section class="d-flex align-center">
              {{ eligibleForProvidentFund(item) }}
            </section>
          </td>
          <td
            v-if="props.currentTab === t('settings.earnings')"
            :class="isMobileView ? 'd-flex justify-space-between' : ''"
          >
            <div
              v-if="isMobileView"
              class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
            >
              {{ t("settings.considerForInsurance") }}
            </div>
            <section class="d-flex align-center">
              {{ eligibleForInsurance(item) }}
            </section>
          </td>
          <td :class="isMobileView ? 'd-flex justify-space-between' : ''">
            <div
              v-if="isMobileView"
              class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
            >
              {{ t("settings.status") }}
            </div>
            <section class="d-flex align-center">
              <v-tooltip
                :text="
                  item.Associated_Templates?.length
                    ? 'You cannot change the status of a component that is already associated with a template.'
                    : 'Basic Allowance and Formula Based Allowance cannot be Inactive'
                "
                location="bottom"
              >
                <template v-slot:activator="{ props }">
                  <AppToggleButton
                    v-bind="
                      item.Is_Basic_Pay?.toLowerCase() === 'yes' ||
                      item.Formula_Based?.toLowerCase() === 'yes' ||
                      item.Associated_Templates?.length
                        ? props
                        : ''
                    "
                    :key="`toggle-${item.Allowance_Type_Id}-${toggleKey}`"
                    :button-active-text="t('common.active')"
                    :button-inactive-text="t('common.inactive')"
                    :isDisableToggle="
                      item.Is_Basic_Pay?.toLowerCase() === 'yes' ||
                      item.Formula_Based?.toLowerCase() === 'yes' ||
                      item.Associated_Templates?.length > 0
                    "
                    button-active-color="#7de272"
                    button-inactive-color="red"
                    :id-value="`status-${item.Allowance_Type_Id}`"
                    :current-value="
                      item.AllowanceType_Status === 'Active' ? true : false
                    "
                    @chosen-value="onChangeStatus($event, item)"
                  >
                  </AppToggleButton>
                </template>
              </v-tooltip>
            </section>
          </td>
          <td :class="isMobileView ? 'd-flex justify-space-between' : ''">
            <div
              v-if="isMobileView"
              class="text-subtitle-1 text-grey-darken-1 mt-2 d-flex align-center"
            >
              {{ t("settings.actions") }}
            </div>
            <section class="d-flex align-center justify-end">
              <ActionMenu
                :actions="itemActions(item)"
                :isPresentTooltip="false"
                tooltipMessage="You cannot delete this component as it is linked to one or more Salary Templates."
                :tooltipActionButtons="
                  item.Associated_Templates?.length ? [t('common.delete')] : []
                "
                :disableActionButtons="
                  item.Associated_Templates?.length ? [t('common.delete')] : []
                "
                iconColor="grey"
                @selected-action="onActions($event, item)"
                :access-rights="actionAccess"
              ></ActionMenu>
            </section>
          </td>
        </tr>
      </template>
    </v-data-table>
  </div>
  <SideOverlayViewForm
    :show-form="showViewForm"
    :selected-item="selectedItem"
    :component-width="componentWidth"
    :form-name="viewFormHeader"
    :form-labels="formLabels"
    :allow-edit="true"
    :form-access="props.formAccess"
    @open-edit-form="onActions(t('common.edit'), $event)"
    @close-side-overlay="showViewForm = false"
  />
  <AppWarningModal
    v-if="confirmationModalObj"
    :open-modal="confirmationModalObj"
    confirmation-heading="Are you sure you want to delete the record?"
    icon-name="fas fa-trash-alt"
    @close-warning-modal="(confirmationModalObj = null), (selectedItem = null)"
    @accept-modal="deleteItem(selectedItem)"
  />
  <AppWarningModal
    v-if="statusChangeConfirmation"
    :open-modal="statusChangeConfirmation"
    confirmation-heading="Are you sure you want to change the status?"
    icon-name=""
    @close-warning-modal="onCloseStatusWarningModal"
    @accept-modal="updateStatus(selectedItem)"
  />
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script setup>
import {
  defineAsyncComponent,
  defineProps,
  computed,
  ref,
  getCurrentInstance,
} from "vue";
import { useStore } from "vuex";
import { useI18n } from "vue-i18n";
import { checkNullValue } from "@/helper";
const SideOverlayViewForm = defineAsyncComponent(() =>
  import("@/components/custom-components/SideOverlayViewForm")
);
const ActionMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/ActionMenu.vue")
);

const store = useStore();
const { t } = useI18n();
const instance = getCurrentInstance();

const props = defineProps({
  itemList: {
    type: Array,
    default: () => [],
  },
  currentTab: {
    type: String,
    required: true,
  },
  formAccess: {
    type: [Boolean, Object],
    required: true,
  },
  searchValue: {
    type: String,
    default: "",
  },
  formId: {
    type: Number,
    default: 0,
  },
});

const isMobileView = computed(() => {
  return store.state.isMobileWindowSize;
});
const windowWidth = computed(() => {
  return store.state.windowWidth;
});
const viewFormHeader = computed(() => {
  if (props.currentTab === t("settings.earnings")) {
    return t("settings.viewEarnings");
  } else if (props.currentTab === t("settings.reimbursements")) {
    return t("settings.viewReimbursements");
  } else {
    return t("settings.viewBonus");
  }
});
const componentWidth = computed(() => {
  if (windowWidth.value > 1410) {
    return "30vw";
  } else if (windowWidth.value > 1264 && windowWidth.value < 1410) {
    return "40vw";
  } else if (windowWidth.value < 1264 && windowWidth.value > 810) {
    return "50vw";
  } else {
    return "100vw";
  }
});

const tableHeaders = computed(() => {
  let headers = [
    {
      title:
        props.currentTab === t("settings.earnings")
          ? t("settings.earningName")
          : props.currentTab === t("settings.reimbursements")
          ? t("settings.reimbursementName")
          : t("settings.bonusName"),
      key: "Allowance_Name",
      fixed: true,
    },
    {
      title: t("settings.calculationType"),
      key: "Calculation_Type",
    },
  ];
  if (props.currentTab === t("settings.earnings")) {
    headers.push(
      {
        title: t("settings.considerForProvidentFund"),
        key: "Consider_For_Provident_Fund",
      },
      {
        title: t("settings.considerForInsurance"),
        key: "Consider_For_Insurance",
      }
    );
  }
  if (props.currentTab === t("settings.bonus")) {
    headers.push({
      title: t("settings.taxInclusion"),
      key: "Tax_Inclusion",
    });
  }
  if (props.currentTab === t("settings.bonus")) {
    headers.push({
      title: t("settings.period"),
      key: "Period",
    });
  }
  if (props.currentTab === t("settings.reimbursements")) {
    headers.push({
      title: t("settings.reimbursementType"),
      key: "Reimbursement_Type",
    });
  }
  headers.push(
    {
      title: t("settings.status"),
      key: "AllowanceType_Status",
    },
    {
      title: t("settings.actions"),
      key: "",
      align: "end",
      sortable: false,
      width: "10%",
    }
  );
  return headers;
});
const periodMap = ref({
  Monthly: "month",
  Quarterly: "quarter",
  HalfYearly: "half year",
  Annually: "annum",
});
const getCalculationTypeColumn = (item) => {
  let value = item.Calculation_Type ? item.Calculation_Type + ";" : "";
  if (item.Is_Flexi_Benefit_Plan?.toLowerCase() === "yes") {
    value = item.FBP_Max_Declaration_Amount
      ? item.FBP_Max_Declaration_Amount + " per month"
      : 0;
  } else if (item.Calculation_Type?.toLowerCase() === "amount") {
    if (props.currentTab !== t("settings.bonus")) {
      value = item.Allowance_Amount ? item.Allowance_Amount + " per month" : 0;
    } else
      value = item.Allowance_Amount
        ? item.Allowance_Amount + " per " + periodMap.value[item.Period]
        : 0;
  } else if (item.Calculation_Type?.toLowerCase() === "percentage") {
    if (props.currentTab !== t("settings.bonus")) {
      value = item.Allowance_Percentage
        ? item.Allowance_Percentage +
          (item.Is_Basic_Pay?.toLowerCase() === "yes"
            ? "% of CTC"
            : "% of Basic")
        : 0;
    } else
      value = item.Allowance_Percentage ? item.Allowance_Percentage + "%" : 0;
  }
  return value;
};
const eligibleForProvidentFund = (item) => {
  if (item.Benefits_Association?.includes(52)) {
    if (item.Consider_For_EPF_Contribution?.toLowerCase() === "always") {
      return "Yes" + " (" + item.Consider_For_EPF_Contribution + ")";
    } else {
      return "Yes" + " (" + "if PF < 15000" + ")";
    }
  } else {
    return "No";
  }
};
const eligibleForInsurance = (item) => {
  return item.Benefits_Association?.includes(58) ? "Yes" : "No";
};
const periodList = ref({
  Monthly: "Monthly",
  Quarterly: "Quarterly",
  HalfYearly: "Half Yearly",
  Annually: "Annually",
});
const selectedStatus = ref("");
const statusChangeConfirmation = ref(false);
const toggleKey = ref(0); // Used to force re-render of toggle buttons when needed
const onChangeStatus = (event, item) => {
  selectedItem.value = item;
  selectedStatus.value = event[1] ? "Active" : "Inactive";
  statusChangeConfirmation.value = true;
};

const onCloseStatusWarningModal = () => {
  statusChangeConfirmation.value = false;
  selectedItem.value = null;
  selectedStatus.value = "";
  // Force re-render of toggle buttons to reset their visual state
  toggleKey.value += 1;
};

import { ADD_UPDATE_ALLOWANCE_TYPE } from "@/graphql/settings/salaryComponents.js";
const updateStatus = (item) => {
  isLoading.value = true;
  instance.proxy.$apollo
    .mutate({
      mutation: ADD_UPDATE_ALLOWANCE_TYPE,
      variables: {
        formId: props.formId,
        allowanceTypeId: item.Allowance_Type_Id,
        allowanceName: item.Allowance_Name,
        isFlexiBenefitPlan: item.Is_Flexi_Benefit_Plan,
        allowanceType: item.Calculation_Type,
        amount: item.Allowance_Amount,
        percentage: item.Allowance_Percentage,
        fbpMaxDeclaration: item.FBP_Max_Declaration_Amount,
        asIsPayment: item.Allowance_As_Is_Payment,
        taxInclusion: item.Tax_Inclusion,
        restrictEmployeeFbpOverride: item.Restrict_Employee_FBP_Override,
        benefitAssociation: item.Benefits_Association,
        epfContribution: item.Consider_For_EPF_Contribution,
        perquisitesId: item.Perquisites_Id,
        period: item.Period,
        allowanceTypeStatus: selectedStatus.value,
        description: item.Description,
        allowanceMode: item.Allowance_Mode,
        isClaimFromReimbursement: item.Is_Claim_From_Reimbursement,
        reimbursementType: item.Reimbursement_Type,
        workflowId: item.Workflow_Id,
      },
      client: "apolloClientF",
      fetchPolicy: "no-cache",
    })
    .then(() => {
      statusChangeConfirmation.value = false;
      isLoading.value = false;
      let formName = "";
      if (props.formId === 381) {
        formName = "Earning";
      } else if (props.formId === 382) {
        formName = "Reimbursement";
      } else if (props.formId === 383) {
        formName = "Bonus";
      }
      let snackbarData = {
        isOpen: true,
        message: `${formName} status updated successfully`,
        type: "success",
      };
      emit("on-status-change", {
        id: item.Allowance_Type_Id,
        status: selectedStatus.value,
      });
      store.commit("OPEN_SNACKBAR", snackbarData);
    })
    .catch((err) => {
      isLoading.value = false;
      statusChangeConfirmation.value = false;
      let formName = "";
      if (props.formId === 381) {
        formName = "Earning";
      } else if (props.formId === 382) {
        formName = "Reimbursement";
      } else if (props.formId === 383) {
        formName = "Bonus";
      }
      let errorMessage =
        err?.message ||
        `Something went wrong while updating the ${formName}. Please try after some time.`;
      errorMessage = errorMessage.replace("allowance type", formName);
      let snackbarData = {
        isOpen: true,
        message: errorMessage,
        type: "warning",
      };
      store.commit("OPEN_SNACKBAR", snackbarData);
    });
};

const showViewForm = ref(false);
const selectedItem = ref(null);
const formLabels = computed(() => {
  let labels = [
    {
      label:
        props.currentTab === t("settings.earnings")
          ? t("settings.earningName")
          : props.currentTab === t("settings.reimbursements")
          ? t("settings.reimbursementName")
          : t("settings.bonusName"),
      key: "Allowance_Name",
    },
  ];
  if (props.currentTab !== t("settings.bonus")) {
    labels.push({
      label: t("settings.fbpComponent"),
      key: "Is_Flexi_Benefit_Plan",
    });
  }
  labels.push({
    label: t("settings.calculationType"),
    key: "Calculation_Type",
  });
  if (selectedItem.value?.Calculation_Type?.toLowerCase() === "amount") {
    labels.push({
      label: t("settings.amount"),
      key: "Allowance_Amount",
    });
  } else {
    labels.push({
      label: t("settings.percentage"),
      key: "Allowance_Percentage",
    });
  }
  if (
    props.currentTab !== t("settings.bonus") &&
    selectedItem.value?.Is_Flexi_Benefit_Plan?.toLowerCase() === "yes"
  ) {
    labels.push(
      {
        label: t("settings.maxLimit"),
        key: "FBP_Max_Declaration_Amount",
      },
      {
        label: t("settings.restrictEmployeeFromOverridingFBPAmount"),
        key: "Restrict_Employee_FBP_Override",
      }
    );
  }
  labels.push(
    {
      label: t("settings.asIsPayment"),
      key: "Allowance_As_Is_Payment",
    },
    {
      label: t("settings.period"),
      key: "Period",
    },
    {
      label: t("settings.taxInclusion"),
      key: "Tax_Inclusion",
    }
  );
  if (props.currentTab === t("settings.reimbursements")) {
    labels.push(
      {
        label: t("settings.reimbursementType"),
        key: "Reimbursement_Type",
      },
      {
        label: t("settings.workFlow"),
        key: "Workflow_Name",
      }
    );
  }
  if (props.currentTab !== t("settings.bonus")) {
    labels.push(
      {
        label: t("settings.perks"),
        key: "Perquisites_Name",
      },
      {
        label: t("settings.benefitsAssociation"),
        key: "Benefits_Name",
      }
    );
  }
  if (
    props.currentTab === t("settings.earnings") &&
    selectedItem.value?.Benefits_Association?.includes(52)
  ) {
    labels.push({
      label: t("settings.considerForEPF"),
      key: "Consider_For_EPF_Contribution",
    });
  }
  labels.push(
    {
      label: t("settings.status"),
      key: "AllowanceType_Status",
    },
    {
      label: t("settings.description"),
      key: "Description",
      col: 12,
    }
  );
  return labels;
});
const emit = defineEmits([
  "on-edit-item",
  "on-update-items",
  "on-delete-item",
  "on-status-change",
]);
const itemActions = (item) => {
  let items = [];
  if (
    item.Is_Basic_Pay?.toLowerCase() === "yes" ||
    item.Formula_Based?.toLowerCase() === "yes"
  ) {
    if (props.formAccess.update) {
      items.push(t("common.edit"));
    }
  } else {
    if (props.formAccess.update) {
      items.push(t("common.edit"));
    }
    if (props.formAccess.delete) {
      items.push(t("common.delete"));
    }
  }
  return items;
};
const actionAccess = computed(() => {
  return {
    [t("common.view")]: props.formAccess.view,
    [t("common.edit")]: props.formAccess.update,
    [t("common.delete")]: props.formAccess.delete,
  };
});
const confirmationModalObj = ref(false);
const onActions = (type, item) => {
  if (type === t("common.view")) {
    selectedItem.value = item;
    showViewForm.value = true;
  } else if (type === t("common.edit")) {
    showViewForm.value = false;
    emit("on-edit-item", item);
  } else if (type === t("common.delete")) {
    selectedItem.value = item;
    confirmationModalObj.value = true;
  }
};
import { DELETE_ALLOWANCE_TYPE } from "@/graphql/settings/salaryComponents.js";
const isLoading = ref(false);
const deleteItem = (item) => {
  isLoading.value = true;
  instance.proxy.$apollo
    .mutate({
      mutation: DELETE_ALLOWANCE_TYPE,
      variables: {
        allowanceTypeId: item.Allowance_Type_Id,
        formId: props.formId,
      },
      client: "apolloClientF",
      fetchPolicy: "no-cache",
    })
    .then(() => {
      confirmationModalObj.value = null;
      emit("on-delete-item");
      let formName = "";
      if (props.formId === 381) {
        formName = "Earning";
      } else if (props.formId === 382) {
        formName = "Reimbursement";
      } else if (props.formId === 383) {
        formName = "Bonus";
      }
      let snackbarData = {
        isOpen: true,
        message: `${formName} deleted successfully`,
        type: "success",
      };
      store.commit("OPEN_SNACKBAR", snackbarData);
      isLoading.value = false;
    })
    .catch((err) => {
      isLoading.value = false;
      confirmationModalObj.value = null;
      let formName = "";
      if (props.formId === 381) {
        formName = "Earning";
      } else if (props.formId === 382) {
        formName = "Reimbursement";
      } else if (props.formId === 383) {
        formName = "Bonus";
      }
      let errorMessage =
        err?.message ||
        `Something went wrong while deleting the ${formName}. Please try after some time.`;
      errorMessage = errorMessage.replace("allowance type", formName);
      let snackbarData = {
        isOpen: true,
        message: errorMessage,
        type: "warning",
      };
      store.commit("OPEN_SNACKBAR", snackbarData);
    });
};
</script>
