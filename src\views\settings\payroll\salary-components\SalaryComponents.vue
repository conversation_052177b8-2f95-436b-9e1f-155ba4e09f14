<template>
  <AppTopBarTab
    :center-tab="true"
    :tabs-list="mainTabs"
    :current-tab="currentTabItem"
    @tab-clicked="onTabChange($event)"
  >
    <template #topBarContent>
      <v-row justify="center" v-if="originalList.length > 0">
        <v-col cols="12" md="9" class="d-flex justify-end">
          <EmployeeDefaultFilterMenu
            class="d-flex justify-end"
            :isFilter="true"
            :isDefaultFilter="false"
            @apply-emp-filter="applyFilter()"
            @reset-emp-filter="resetFilter()"
          >
            <template v-slot:new-filter>
              <v-row class="mr-2 mt-2">
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedCalculationType"
                    label="Calculation Type"
                    variant="solo"
                    density="compact"
                    color="primary"
                    multiple
                    closable-chips
                    chips
                    :items="['Amount', 'Percentage']"
                  ></v-select>
                </v-col>
                <v-col
                  v-if="openedSubTab !== t('settings.bonus')"
                  :cols="windowWidth > 600 ? 6 : 12"
                  class="py-2"
                >
                  <v-select
                    v-model="selectedFBPComponent"
                    label="FBP Component"
                    variant="solo"
                    density="compact"
                    color="primary"
                    multiple
                    closable-chips
                    chips
                    :items="['Yes', 'No']"
                  ></v-select>
                </v-col>
                <v-col
                  v-if="openedSubTab !== t('settings.reimbursements')"
                  :cols="windowWidth > 600 ? 6 : 12"
                  class="py-2"
                >
                  <v-select
                    v-model="selectedTaxInclusion"
                    label="Tax Inclusion"
                    variant="solo"
                    density="compact"
                    color="primary"
                    multiple
                    closable-chips
                    chips
                    :items="['Taxable', 'Non Taxable']"
                  ></v-select>
                </v-col>
                <v-col :cols="windowWidth > 600 ? 6 : 12" class="py-2">
                  <v-select
                    v-model="selectedStatus"
                    label="Status"
                    variant="solo"
                    density="compact"
                    color="primary"
                    multiple
                    closable-chips
                    chips
                    :items="['Active', 'Inactive']"
                  ></v-select>
                </v-col>
              </v-row>
            </template>
          </EmployeeDefaultFilterMenu>
        </v-col>
      </v-row>
    </template>
  </AppTopBarTab>
  <ProfileCard v-if="subTabItems.length > 0" class="sub-tabs">
    <FormTab :model-value="openedSubTab">
      <v-tab
        v-for="tab in subTabItems"
        :key="tab"
        :value="tab"
        color="primary"
        @click="onChangeSubTabs(tab)"
      >
        <div
          :class="[
            isActiveSubTab(tab)
              ? 'text-primary font-weight-bold'
              : 'text-grey-darken-2 font-weight-bold',
          ]"
        >
          <div class="d-flex align-center">
            {{ tab }}
          </div>
        </div>
      </v-tab>
    </FormTab>
  </ProfileCard>
  <v-container fluid class="salary-container">
    <v-window
      v-if="subTabItems.length > 0 && formAccess?.view"
      v-model="currentTabItem"
    >
      <v-window-item :value="currentTabItem">
        <div v-if="listLoading" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 3" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <AppFetchErrorScreen
          v-else-if="isErrorInList"
          :content="errorContent"
          icon-name="fas fa-redo-alt"
          image-name="common/human-error-image"
          :button-text="t('common.retry')"
          @button-click="refetchList()"
        >
        </AppFetchErrorScreen>
        <AppFetchErrorScreen
          v-else-if="itemList.length === 0"
          key="no-results-screen"
          :main-title="
            originalList.length === 0 ? '' : t('common.noFilterRecord')
          "
          :isSmallImage="originalList.length === 0"
          :image-name="originalList.length === 0 ? '' : 'common/no-records'"
        >
          <template #contentSlot>
            <div style="max-width: 80%">
              <v-row
                :style="originalList.length === 0 ? 'background: white' : ''"
                class="rounded-lg pa-5 mb-4"
              >
                <v-col v-if="originalList.length === 0" cols="12">
                  <NotesCard
                    v-if="openedSubTab === t('settings.earnings')"
                    notes="The Earnings form is used to define all fixed salary components like Basic, HRA, or Special Allowance."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    v-if="openedSubTab === t('settings.earnings')"
                    notes="Once created, these earnings can be picked while building a salary template, ensuring every employee’s salary structure is standardized and consistent."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    v-if="openedSubTab === t('settings.reimbursements')"
                    notes="The Reimbursement form helps you set up allowances that employees can claim, like travel, fuel, or medical expenses."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    v-if="openedSubTab === t('settings.reimbursements')"
                    notes="These reimbursements, when added to the salary template, ensure employees receive their eligible claims as part of payroll in a structured way."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    v-if="openedSubTab === t('settings.bonus')"
                    notes="The Bonus form allows you to configure different types of variable payouts, such as performance bonus or annual incentives."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                  <NotesCard
                    v-if="openedSubTab === t('settings.bonus')"
                    notes="By linking these bonuses in the salary template, you can manage payouts effectively based on the defined period (monthly, quarterly, yearly, etc.)."
                    backgroundColor="transparent"
                    class="mb-4"
                  ></NotesCard>
                </v-col>
                <v-col
                  cols="12"
                  class="d-flex align-center justify-center mb-4"
                >
                  <v-btn
                    v-if="
                      formAccess?.add &&
                      originalList.length === 0 &&
                      openedSubTab !== t('settings.reimbursements')
                    "
                    color="primary"
                    rounded="lg"
                    prepend-icon="fas fa-plus"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="openAddForm()"
                  >
                    {{ addButtonText }}</v-btn
                  >
                  <v-btn
                    v-if="originalList.length > 0"
                    color="primary"
                    variant="elevated"
                    class="ml-4 mt-1"
                    rounded="lg"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="resetFilter()"
                  >
                    {{ t("common.resetFilterSearch") }}
                  </v-btn>
                  <v-btn
                    v-if="originalList.length === 0"
                    rounded="lg"
                    class="mt-1"
                    color="transparent"
                    variant="flat"
                    :size="isMobileView ? 'small' : 'default'"
                    @click="refetchList()"
                  >
                    <v-icon>fas fa-redo-alt</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </div>
          </template>
        </AppFetchErrorScreen>
        <div v-else class="mt-3">
          <div
            class="d-flex align-center"
            :class="windowWidth < 450 ? 'flex-column' : 'justify-end'"
          >
            <v-btn
              v-if="
                formAccess?.add && openedSubTab !== t('settings.reimbursements')
              "
              color="primary"
              rounded="lg"
              prepend-icon="fas fa-plus"
              :size="isMobileView ? 'small' : 'default'"
              @click="openAddForm()"
            >
              {{ addButtonText }}</v-btn
            >
            <div>
              <v-btn
                rounded="lg"
                class="mt-1"
                color="transparent"
                variant="flat"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchList()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
              <v-menu v-model="openMoreMenu" transition="scale-transition">
                <template v-slot:activator="{ props }">
                  <v-btn variant="plain" v-bind="props">
                    <v-icon v-if="!openMoreMenu">fas fa-ellipsis-v</v-icon>
                    <v-icon v-else>fas fa-caret-up</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="action in [t('common.export')]"
                    :key="action"
                    @click="onActions(action)"
                  >
                    <v-hover>
                      <template v-slot:default="{ isHovering, props }">
                        <v-list-item-title
                          v-bind="props"
                          class="pa-3"
                          :class="{
                            'bg-hover': isHovering,
                          }"
                          >{{ action }}</v-list-item-title
                        >
                      </template>
                    </v-hover>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </div>
          <SalaryComponentList
            :itemList="itemList"
            :currentTab="openedSubTab"
            :formId="formId"
            :searchValue="searchValue"
            :formAccess="formAccess"
            @on-edit-item="onEditItem($event)"
            @on-delete-item="refetchList()"
            @on-update-items="updateCurrentItems($event)"
            @on-status-change="onStatusChange($event)"
          >
          </SalaryComponentList>
        </div>
      </v-window-item>
    </v-window>
    <AppAccessDenied v-else></AppAccessDenied>
  </v-container>
  <AddEditSalaryComponents
    v-if="showAddEditForm"
    :showForm="showAddEditForm"
    :formId="formId"
    :currentTab="openedSubTab"
    :isEdit="isEdit"
    :selectedItem="selectedItem"
    @close-form="closeAllForms()"
    @add-edit-success="refetchList()"
  />
</template>
<script setup>
import {
  ref,
  computed,
  onMounted,
  defineAsyncComponent,
  watch,
  getCurrentInstance,
  nextTick,
} from "vue";
import { useI18n } from "vue-i18n";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import { useExcelExport } from "@/composables/excelExportComposables";
import { useFormatData } from "@/composables/formatingComposables";

const SalaryComponentList = defineAsyncComponent(() =>
  import("./SalaryComponentList.vue")
);
const AddEditSalaryComponents = defineAsyncComponent(() =>
  import("./AddEditSalaryComponents.vue")
);
const EmployeeDefaultFilterMenu = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeeDefaultFilterMenu.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);

const router = useRouter();
const store = useStore();
const { t } = useI18n();
const instance = getCurrentInstance();
const route = useRoute();
const { formatDate } = useFormatData();

onMounted(() => {
  currentTabItem.value =
    "tab-" + mainTabs.value.indexOf(t("settings.salaryComponents"));
  if (router.currentRoute.value.query?.type) {
    openedSubTab.value = router.currentRoute.value.query?.type;
  } else {
    if (getFormAccess(381, "view")) {
      openedSubTab.value = t("settings.earnings");
    } else if (getFormAccess(382, "view")) {
      openedSubTab.value = t("settings.reimbursements");
    } else if (getFormAccess(383, "view")) {
      openedSubTab.value = t("settings.bonus");
    }
  }
  if (getFormAccess(formId.value, "view")) {
    fetchAllowanceList();
  }
});

// Access Management
const accessRights = computed(() => {
  return store.getters.formIdBasedAccessRights;
});
const formId = computed(() => {
  return openedSubTab.value === t("settings.earnings")
    ? 381
    : openedSubTab.value === t("settings.reimbursements")
    ? 382
    : 383;
});
const getFormAccess = (formId, accessType) => {
  let formAccess = accessRights.value(formId);
  if (
    formAccess &&
    formAccess.accessRights &&
    formAccess.accessRights[accessType]
  ) {
    return true;
  } else {
    return false;
  }
};
const formAccess = computed(() => {
  let formAccess = accessRights.value(formId.value);
  if (
    formAccess &&
    formAccess.accessRights &&
    formAccess.accessRights["view"] &&
    formAccess.accessRights["admin"] === "admin"
  ) {
    return formAccess.accessRights;
  } else {
    return false;
  }
});

//Tabs Management
const currentTabItem = ref("");
const mainTabs = computed(() => {
  let tabs = [];
  if (getFormAccess(261, "view")) {
    tabs.push(t("settings.generalTab"));
  }
  tabs.push(t("settings.salaryComponents"));
  if (getFormAccess(206, "view")) {
    tabs.push(t("settings.salaryTemplate"));
  }
  return tabs;
});
const onTabChange = (tab) => {
  if (tab === t("settings.generalTab")) {
    router.push("/settings/payroll/general");
  } else if (tab === t("settings.salaryTemplate")) {
    router.push("/settings/payroll/salary-template");
  }
};

// Sub Tabs Management
const openedSubTab = ref("");
const subTabItems = computed(() => {
  let tabs = [];
  let queryType = router.currentRoute.value.query?.type;
  if (getFormAccess(381, "view") || queryType === t("settings.earnings")) {
    tabs.push(t("settings.earnings"));
  }
  if (
    getFormAccess(382, "view") ||
    queryType === t("settings.reimbursements")
  ) {
    tabs.push(t("settings.reimbursements"));
  }
  if (getFormAccess(383, "view") || queryType === t("settings.bonus")) {
    tabs.push(t("settings.bonus"));
  }
  return tabs;
});
const isActiveSubTab = (val) => {
  return openedSubTab.value === val;
};
const onChangeSubTabs = (val) => {
  openedSubTab.value = val;
  router.replace({ path: route.path, hash: route.hash });
  if (getFormAccess(formId.value, "view")) {
    refetchList();
  }
};

// Search and Filter
const searchValue = computed(() => {
  return store.state.empSearchValue;
});
watch(searchValue, (val) => {
  if (!val) {
    applyFilter();
  }
});
const updateCurrentItems = (items) => {
  if (items.length === 0) {
    itemList.value = [];
  }
};
const onStatusChange = async (data) => {
  originalList.value.forEach((item) => {
    if (item.Allowance_Type_Id === data.id) {
      item.AllowanceType_Status = data.status;
    }
  });
  await nextTick();
  applyFilter();
};
const selectedStatus = ref(["Active"]);
const selectedCalculationType = ref([]);
const selectedFBPComponent = ref([]);
const selectedTaxInclusion = ref([]);
const applyFilter = () => {
  let filteredList = [...originalList.value];
  if (selectedStatus.value.length > 0) {
    filteredList = filteredList.filter((item) => {
      const isIncluded = selectedStatus.value.includes(
        item.AllowanceType_Status
      );
      return isIncluded;
    });
  }
  if (selectedCalculationType.value.length > 0) {
    filteredList = filteredList.filter((item) => {
      return selectedCalculationType.value.includes(item.Calculation_Type);
    });
  }
  if (selectedFBPComponent.value.length > 0) {
    filteredList = filteredList.filter((item) => {
      return selectedFBPComponent.value.includes(item.Is_Flexi_Benefit_Plan);
    });
  }
  if (selectedTaxInclusion.value.length > 0) {
    filteredList = filteredList.filter((item) => {
      return selectedTaxInclusion.value.includes(item.Tax_Inclusion);
    });
  }
  itemList.value = filteredList;
};
const resetFilter = () => {
  store.commit("UPDATE_EMP_SEARCH_VAL", "");
  selectedStatus.value = ["Active"];
  selectedCalculationType.value = [];
  selectedFBPComponent.value = [];
  selectedTaxInclusion.value = [];
  applyFilter();
};

// Grid Filters
const isMobileView = computed(() => {
  return store.state.isMobileWindowSize;
});
const windowWidth = computed(() => {
  return store.state.windowWidth;
});
const openMoreMenu = ref(false);
const addButtonText = computed(() => {
  if (openedSubTab.value === t("settings.earnings")) {
    return t("settings.addEarnings");
  } else if (openedSubTab.value === t("settings.reimbursements")) {
    return t("settings.addReimbursements");
  } else {
    return t("settings.addBonus");
  }
});
const showAddEditForm = ref(false);
const isEdit = ref(false);
const selectedItem = ref(null);
const openAddForm = () => {
  isEdit.value = false;
  selectedItem.value = null;
  showAddEditForm.value = true;
};
const onEditItem = (item) => {
  isEdit.value = true;
  selectedItem.value = item;
  showAddEditForm.value = true;
};
const closeAllForms = () => {
  isEdit.value = false;
  selectedItem.value = null;
  showAddEditForm.value = false;
};
const onActions = (action) => {
  if (action === t("common.export")) {
    exportReportFile();
  }
};
// Export File
const { singleSheetExportToExcel } = useExcelExport();
const exportReportFile = () => {
  let exportHeaders = [
    {
      header:
        openedSubTab.value === t("settings.earnings")
          ? t("settings.earningName")
          : openedSubTab.value === t("settings.reimbursements")
          ? t("settings.reimbursementName")
          : t("settings.bonusName"),
      key: "Allowance_Name",
    },
  ];
  if (openedSubTab.value !== t("settings.bonus")) {
    exportHeaders.push({
      header: t("settings.fbpComponent"),
      key: "Is_Flexi_Benefit_Plan",
    });
  }
  exportHeaders.push(
    {
      header: t("settings.calculationType"),
      key: "Calculation_Type",
    },
    {
      header: t("settings.amount"),
      key: "Allowance_Amount",
    },
    {
      header: t("settings.percentage"),
      key: "Allowance_Percentage",
    }
  );
  if (openedSubTab.value !== t("settings.bonus")) {
    exportHeaders.push({
      header: t("settings.maxLimit"),
      key: "FBP_Max_Declaration_Amount",
    });
  }
  exportHeaders.push({
    header: t("settings.asIsPayment"),
    key: "Allowance_As_Is_Payment",
  });
  if (openedSubTab.value === t("settings.reimbursements")) {
    exportHeaders.push(
      {
        header: t("settings.reimbursementType"),
        key: "Reimbursement_Type",
      },
      {
        header: t("settings.workFlow"),
        key: "Workflow",
      }
    );
  }
  if (openedSubTab.value !== t("settings.bonus")) {
    exportHeaders.push(
      {
        header: t("settings.restrictEmployeeFromOverridingFBPAmount"),
        key: "Restrict_Employee_FBP_Override",
      },
      {
        header: t("settings.taxInclusion"),
        key: "Tax_Inclusion",
      },
      {
        header: t("settings.perks"),
        key: "Perquisites_Name",
      }
    );
  }
  if (openedSubTab.value === t("settings.earnings")) {
    exportHeaders.push({
      header: t("settings.benefitsAssociation"),
      key: "Benefits_Name",
    });
  }
  exportHeaders.push(
    {
      header: t("settings.period"),
      key: "Period",
    },
    {
      header: t("settings.status"),
      key: "AllowanceType_Status",
    },
    {
      header: t("settings.description"),
      key: "Description",
    },
    {
      header: t("settings.addedBy"),
      key: "Added_By_Name",
    },
    {
      header: t("settings.addedOn"),
      key: "Added_On",
    },
    {
      header: t("settings.updatedBy"),
      key: "Updated_By_Name",
    },
    {
      header: t("settings.updatedOn"),
      key: "Updated_On",
    }
  );
  itemList.value.forEach((element) => {
    element.Added_On = formatDate(element.Added_On, true, true);
    element.Updated_On = formatDate(element.Updated_On, true, true);
  });
  singleSheetExportToExcel(
    itemList.value,
    exportHeaders,
    openedSubTab.value,
    openedSubTab.value
  );
};

// List Management
const listLoading = ref(false);
const errorContent = ref("");
const isErrorInList = ref(false);
const originalList = ref([]);
const itemList = ref([]);
const refetchList = () => {
  isErrorInList.value = false;
  errorContent.value = "";
  originalList.value = [];
  itemList.value = [];
  closeAllForms();
  resetFilter();
  fetchAllowanceList();
};
import { RETRIEVE_ALLOWANCE_TYPE_LIST } from "@/graphql/settings/salaryComponents.js";
const fetchAllowanceList = () => {
  listLoading.value = true;
  instance.proxy.$apollo
    .query({
      query: RETRIEVE_ALLOWANCE_TYPE_LIST,
      client: "apolloClientF",
      variables: {
        formId: formId.value,
      },
      fetchPolicy: "no-cache",
    })
    .then(({ data }) => {
      if (data?.listAllowanceType?.allowanceTypes) {
        let salaryList = JSON.parse(data.listAllowanceType.allowanceTypes);
        salaryList.forEach((element) => {
          element.Benefits_Name = element.Benefits_Name
            ? element.Benefits_Name.join(", ")
            : "";
          element.Consider_For_Provident_Fund =
            element.Benefits_Association.includes(52) ? "Yes" : "No";
          element.Consider_For_Insurance =
            element.Benefits_Association.includes(58) ? "Yes" : "No";
        });
        originalList.value = salaryList;
        itemList.value = salaryList;
        applyFilter();
      } else {
        originalList.value = [];
        itemList.value = [];
      }
      listLoading.value = false;
    })
    .catch((err) => {
      listLoading.value = false;
      store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: openedSubTab.value,
          isListError: true,
        })
        .then((errorMessages) => {
          errorContent.value = errorMessages;
          isErrorInList.value = true;
        });
    });
};
</script>
<style scoped>
.salary-container {
  padding: 118px 30px 0px 30px;
}

.sub-tabs {
  position: fixed;
  top: 118px;
  z-index: 100;
}

@media screen and (max-width: 1218px) {
  .sub-tabs {
    position: fixed;
    top: 125px;
    z-index: 100;
  }
}

@media screen and (max-width: 1218px) {
  .salary-container {
    padding: 125px 30px 0px 30px;
  }
}
</style>
