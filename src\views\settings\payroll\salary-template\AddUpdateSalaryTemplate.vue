<template>
  <v-overlay v-model="showOverlay" class="d-flex justify-end" persistent>
    <v-card
      :style="{
        height: windowHeight + 'px',
        width: windowWidth < 1400 ? '100vw' : '80vw',
      }"
    >
      <v-card-title
        :class="
          windowWidth < 770
            ? ' d-flex bg-white justify-end align-center pa-0'
            : 'd-flex justify-space-between align-center bg-primary'
        "
      >
        <div v-if="windowWidth >= 770" class="text-h6">
          {{ isEdit ? "Edit " : "Add " }}Salary Template
        </div>
        <v-card v-else class="d-flex justify-end pa-2 fixed-title">
          <v-btn
            class="mr-2"
            variant="text"
            elevation="4"
            rounded="lg"
            @click="closeForm()"
            >Cancel</v-btn
          >
          <v-btn
            color="primary"
            rounded="lg"
            :disabled="anyValueUpdated || sumOfComponetsMinusCTC < 0"
            @click="validateForm()"
            >Save</v-btn
          >
        </v-card>
        <v-btn
          v-if="windowWidth >= 770"
          icon="fas fa-times"
          variant="text"
          @click="closeForm()"
          color="white"
        ></v-btn>
      </v-card-title>
      <v-card-text class="add-update-content">
        <v-row>
          <v-col :cols="windowWidth > 1110 ? 3 : 12" class="px-0">
            <v-expansion-panels
              v-model="salaryComponentPanels"
              multiple
              variant="accordion"
              flat
            >
              <v-expansion-panel
                v-for="(item, i) in Object.keys(salaryComponents)"
                :key="i"
                elevation="0"
              >
                <v-expansion-panel-title static>
                  {{ item }}
                  <template v-slot:actions="{ expanded }">
                    <v-icon size="15">
                      {{ expanded ? "fas fa-caret-up" : "fas fa-caret-down" }}
                    </v-icon>
                  </template>
                </v-expansion-panel-title>
                <v-expansion-panel-text>
                  <div v-if="salaryComponents[item].length">
                    <div v-if="item.toLowerCase() !== 'retirals'">
                      <div
                        v-for="component of salaryComponents[item]"
                        :key="component.Allowance_Type_Id"
                      >
                        <v-hover v-if="!component.isSelected">
                          <template v-slot:default="{ isHovering, props }">
                            <div
                              class="pa-2 rounded-lg cursor-pointer d-flex justify-space-between align-center"
                              v-bind="props"
                              :class="isHovering ? 'bg-hover text-primary' : ''"
                              @click="selectComponent(item, component)"
                            >
                              <span
                                class="text-truncate"
                                style="max-width: 90%"
                                >{{
                                  checkNullValue(component.Allowance_Name)
                                }}</span
                              >
                              <v-icon v-if="isHovering" size="10"
                                >fas fa-plus</v-icon
                              >
                            </div>
                          </template>
                        </v-hover>
                      </div>
                    </div>
                    <div v-else>
                      <div
                        v-for="(component, j) of salaryComponents[item]"
                        :key="j"
                      >
                        <v-hover v-if="!component.isSelected">
                          <template v-slot:default="{ isHovering, props }">
                            <div
                              class="pa-2 rounded-lg cursor-pointer d-flex justify-space-between align-center"
                              v-bind="props"
                              :class="isHovering ? 'bg-hover text-primary' : ''"
                              @click="selectComponent(item, component)"
                            >
                              {{
                                component.Insurance_Name
                                  ? component.Insurance_Name
                                  : getCustomFormName(component.Form_Id)
                              }}
                              <v-icon v-if="isHovering" size="10"
                                >fas fa-plus</v-icon
                              >
                            </div>
                          </template>
                        </v-hover>
                      </div>
                    </div>
                    <div v-if="allSelected(item)">
                      You have added all the active {{ item }} to the template.
                    </div>
                  </div>
                  <div v-else>
                    There are no active {{ item }} in your organisation. Click
                    <a
                      style="text-decoration: none; color: blue"
                      target="_blank"
                      :href="getRedirectionUrl(item)"
                      >here</a
                    >
                    to add new or activate existing item.
                  </div>
                </v-expansion-panel-text>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-col>
          <v-col :cols="windowWidth > 1110 ? 9 : 12">
            <v-form ref="form">
              <v-row class="mt-3">
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model="salaryTemplateName"
                    variant="solo"
                    density="comfortable"
                    clearable
                    :rules="[
                      required('Template Name', salaryTemplateName),
                      multilingualNameNumericValidation(
                        'Template Name',
                        salaryTemplateName
                      ),
                      maxLengthValidation(
                        'Template Name',
                        salaryTemplateName,
                        30
                      ),
                    ]"
                  >
                    <template v-slot:label>
                      Template Name<span class="text-red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4">
                  <v-textarea
                    v-model="salaryTemplateDescription"
                    variant="solo"
                    density="comfortable"
                    clearable
                    label="Description"
                    rows="1"
                    :rules="[
                      multilingualNameNumericValidation(
                        'Description',
                        salaryTemplateDescription
                      ),
                      maxLengthValidation(
                        'Description',
                        salaryTemplateDescription,
                        500
                      ),
                    ]"
                  ></v-textarea>
                </v-col>
              </v-row>
              <v-row class="mt-3">
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model="annualCTC"
                    variant="solo"
                    density="comfortable"
                    type="number"
                    :rules="[
                      required('Annual CTC', annualCTC),
                      twoDecimalPrecisionValidation(annualCTC),
                    ]"
                    @update:model-value="updateAnnualCTC"
                  >
                    <template v-slot:prepend-inner>
                      <span
                        class="d-flex justify-center align-center bg-grey-lighten-3"
                        style="height: 100%; width: 40px"
                      >
                        {{ payrollCurrency }}
                      </span>
                    </template>
                    <template v-slot:label>
                      Annual CTC
                      <span class="text-red">*</span>
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" md="4">
                  <v-text-field
                    v-model="monthlyCTC"
                    variant="solo"
                    density="comfortable"
                    type="number"
                    hint="This is an auto-calculated value. Update the Annual CTC to change it."
                    persistent-hint
                    class="disabled-element"
                    :readonly="true"
                  >
                    <template v-slot:prepend-inner>
                      <span
                        class="d-flex justify-center align-center bg-grey-lighten-3"
                        style="height: 100%; width: 40px"
                      >
                        {{ payrollCurrency }}
                      </span>
                    </template>
                    <template v-slot:label> Monthly CTC </template>
                  </v-text-field>
                </v-col>
              </v-row>
              <v-card class="pa-2 mt-3">
                <v-row>
                  <v-col cols="4" class="bg-grey-lighten-4"
                    >Salary Components</v-col
                  >
                  <v-col class="bg-grey-lighten-4">Calculation Type</v-col>
                  <v-col class="bg-grey-lighten-4 text-end">
                    <div class="pr-8">Monthly</div>
                  </v-col>
                  <v-col class="bg-grey-lighten-4 text-end">
                    <div class="pr-5">Annually</div>
                  </v-col>
                </v-row>
                <v-row class="mt-2">
                  <v-col class="font-weight-bold text-subtitle-1">
                    Earnings
                  </v-col>
                </v-row>
                <v-row class="d-flex align-center mt-0">
                  <v-col cols="4" class="text-subtitle-2">
                    {{ basicPayArray[0]?.Allowance_Name || "Basic Pay" }}
                  </v-col>
                  <v-col>
                    <v-text-field
                      v-if="basicPayType?.toLowerCase() === 'percentage'"
                      v-model="basicPayPercentage"
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        required('Basic Pay', basicPayPercentage),
                        twoDecimalPrecisionValidation(basicPayPercentage),
                      ]"
                      type="number"
                      @update:model-value="updateBasicPay"
                    >
                      <template v-slot:append-inner>
                        <span
                          class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                          style="width: max-content; height: 100%"
                          >% of CTC</span
                        >
                      </template>
                    </v-text-field>
                    <div v-else>Fixed Amount</div>
                  </v-col>
                  <v-col class="text-subtitle-2 d-flex">
                    <v-text-field
                      v-model="basicPayAmount"
                      :readonly="basicPayType?.toLowerCase() === 'percentage'"
                      class="custom-input-position"
                      variant="solo"
                      density="comfortable"
                      :rules="[
                        required('Basic Pay', basicPayAmount),
                        twoDecimalPrecisionValidation(basicPayAmount),
                      ]"
                      type="number"
                      @update:model-value="updateBasicPay"
                    >
                    </v-text-field>
                  </v-col>
                  <v-col
                    class="text-body-1 text-end d-flex align-center justify-end"
                  >
                    <div class="pr-5">
                      {{
                        basicPayAmount ? (basicPayAmount * 12).toFixed(2) : 0
                      }}
                    </div>
                    <v-icon size="15" style="opacity: 0">fas fa-times</v-icon>
                  </v-col>
                </v-row>
                <div v-if="selectedEarnings.length">
                  <v-row
                    v-for="item of selectedEarnings"
                    :key="item.id"
                    class="d-flex align-center mt-0"
                  >
                    <v-col cols="4" class="text-subtitle-2">
                      {{ checkNullValue(item.Allowance_Name) }}
                    </v-col>
                    <v-col>
                      <v-text-field
                        v-if="
                          item.Allowance_Type?.toLowerCase() === 'percentage'
                        "
                        v-model="item.Percentage"
                        variant="solo"
                        density="comfortable"
                        :rules="[
                          twoDecimalPrecisionValidation(item.Percentage),
                        ]"
                        type="number"
                        @update:model-value="updateBasicPay"
                      >
                        <template v-slot:append-inner>
                          <span
                            class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                            style="width: max-content; height: 100%"
                            >% of Basic</span
                          >
                        </template>
                      </v-text-field>
                      <div v-else>Fixed Amount</div>
                    </v-col>
                    <v-col class="text-subtitle-2 d-flex">
                      <v-text-field
                        v-model="item.Amount"
                        :readonly="
                          item.Allowance_Type?.toLowerCase() === 'percentage'
                        "
                        class="custom-input-position"
                        variant="solo"
                        density="comfortable"
                        :rules="[twoDecimalPrecisionValidation(item.Amount)]"
                        type="number"
                        @update:model-value="updateBasicPay"
                      >
                      </v-text-field>
                    </v-col>
                    <v-col
                      class="text-body-1 text-end d-flex align-center justify-end"
                    >
                      <div class="pr-5">
                        {{ item.Amount ? (item.Amount * 12).toFixed(2) : 0 }}
                      </div>
                      <v-icon
                        size="15"
                        @click="removeComponent('earnings', item)"
                        >fas fa-times</v-icon
                      >
                    </v-col>
                  </v-row>
                </div>
                <v-row class="d-flex align-center mt-0">
                  <v-col cols="4" class="text-subtitle-2">
                    {{
                      fixedAllowanceArray[0]?.Allowance_Name ||
                      "Fixed Allowance"
                    }}
                    <div class="text-caption">
                      Monthly CTC - Sum of all other components
                    </div>
                  </v-col>
                  <v-col>
                    <div>Fixed Amount</div>
                  </v-col>
                  <v-col
                    class="text-body-1"
                    :class="anyValueUpdated ? 'text-start' : 'text-end'"
                  >
                    <div :class="anyValueUpdated ? '' : 'pr-8'">
                      {{
                        anyValueUpdated
                          ? "System Calculated"
                          : fixedAllowanceAmount?.toFixed(2) || 0
                      }}
                    </div>
                  </v-col>
                  <v-col
                    class="text-body-1 text-end d-flex align-center justify-end"
                  >
                    <div :class="anyValueUpdated ? '' : 'pr-5'">
                      {{
                        anyValueUpdated
                          ? "System Calculated"
                          : fixedAllowanceAmount
                          ? (fixedAllowanceAmount * 12).toFixed(2)
                          : 0
                      }}
                    </div>
                    <v-icon size="15" style="opacity: 0">fas fa-times</v-icon>
                  </v-col>
                </v-row>
                <div v-if="selectedReimbursements.length">
                  <v-row class="mt-2">
                    <v-col class="font-weight-bold text-subtitle-1">
                      Reimbursement
                    </v-col>
                  </v-row>
                  <v-row
                    v-for="item of selectedReimbursements"
                    :key="item.id"
                    class="d-flex align-center mt-0"
                  >
                    <v-col cols="4" class="text-subtitle-2">
                      {{ checkNullValue(item.Allowance_Name) }}
                    </v-col>
                    <v-col>
                      <v-text-field
                        v-if="
                          item.Allowance_Type?.toLowerCase() === 'percentage'
                        "
                        v-model="item.Percentage"
                        variant="solo"
                        density="comfortable"
                        :rules="[
                          twoDecimalPrecisionValidation(item.Percentage),
                        ]"
                        type="number"
                        @update:model-value="updateBasicPay"
                      >
                        <template v-slot:append-inner>
                          <span
                            class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                            style="width: max-content; height: 100%"
                            >% of Basic</span
                          >
                        </template>
                      </v-text-field>
                      <div v-else>Fixed Amount</div>
                    </v-col>
                    <v-col class="text-subtitle-2 d-flex">
                      <v-text-field
                        v-model="item.Amount"
                        :readonly="
                          item.Allowance_Type?.toLowerCase() === 'percentage'
                        "
                        class="custom-input-position"
                        variant="solo"
                        density="comfortable"
                        :rules="[twoDecimalPrecisionValidation(item.Amount)]"
                        type="number"
                        @update:model-value="updateBasicPay"
                      >
                      </v-text-field>
                    </v-col>
                    <v-col
                      class="text-body-1 text-end d-flex align-center justify-end"
                    >
                      <div class="pr-5">
                        {{ item.Amount ? (item.Amount * 12).toFixed(2) : 0 }}
                      </div>
                      <v-icon
                        size="15"
                        @click="removeComponent('reimbursements', item)"
                        >fas fa-times</v-icon
                      >
                    </v-col>
                  </v-row>
                </div>
                <div v-if="selectedFBPComponents.length">
                  <v-row class="mt-2">
                    <v-col class="font-weight-bold text-subtitle-1">
                      Flexi Benefit Plan
                    </v-col>
                  </v-row>
                  <v-row
                    v-for="item of selectedFBPComponents"
                    :key="item.id"
                    class="d-flex align-center mt-0"
                  >
                    <v-col cols="4" class="text-subtitle-2">
                      {{ checkNullValue(item.Allowance_Name) }}
                      <div>
                        <span class="text-caption">Max Amount: </span>
                        <span class="text-caption font-weight-medium"
                          >{{ payrollCurrency }}
                          {{ item.FBP_Max_Declaration }}</span
                        >
                        <v-icon
                          size="12"
                          class="ml-2"
                          color="primary"
                          @click="openFBPExpansionPanel(item)"
                          >fas fa-edit</v-icon
                        >
                      </div>
                    </v-col>
                    <v-col>
                      <v-text-field
                        v-if="
                          item.Allowance_Type?.toLowerCase() === 'percentage'
                        "
                        v-model="item.Percentage"
                        variant="solo"
                        density="comfortable"
                        :rules="[
                          twoDecimalPrecisionValidation(item.Percentage),
                        ]"
                        type="number"
                        @update:model-value="updateBasicPay"
                      >
                        <template v-slot:append-inner>
                          <span
                            class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                            style="width: max-content; height: 100%"
                            >% of Basic</span
                          >
                        </template>
                      </v-text-field>
                      <div v-else>
                        <div>Fixed Amount</div>
                        <v-tooltip
                          v-if="
                            item.Restrict_Employee_FBP_Override?.toLowerCase() ===
                            'yes'
                          "
                          text="Employees cannot change the FBP amount. If this FBP is selected, the maximum amount will be considered automatically in the FBP declaration."
                          :open-on-click="true"
                          location="top"
                          max-width="300px"
                        >
                          <template v-slot:activator="{ props }">
                            <div v-bind="props">
                              <v-icon size="10" class="mr-1" color="grey"
                                >fas fa-lock</v-icon
                              >
                              <span
                                class="text-caption text-grey-darken-1 text-decoration-underline"
                                >Restricted Component</span
                              >
                            </div>
                          </template>
                        </v-tooltip>
                      </div>
                    </v-col>
                    <v-col class="text-subtitle-2 d-flex">
                      <v-text-field
                        v-model="item.Amount"
                        :readonly="
                          item.Allowance_Type?.toLowerCase() === 'percentage'
                        "
                        :rules="[
                          minMaxNumberValidation(
                            item.Allowance_Name,
                            item.Amount,
                            0,
                            item.FBP_Max_Declaration
                          ),
                          twoDecimalPrecisionValidation(item.Amount),
                        ]"
                        class="custom-input-position"
                        variant="solo"
                        density="comfortable"
                        type="number"
                        @update:model-value="updateBasicPay"
                      >
                      </v-text-field>
                    </v-col>
                    <v-col
                      class="text-body-1 text-end d-flex align-center justify-end"
                    >
                      <div class="pr-5">
                        {{ item.Amount ? (item.Amount * 12).toFixed(2) : 0 }}
                      </div>
                      <v-icon
                        size="15"
                        @click="removeComponent('fbp components', item)"
                        >fas fa-times</v-icon
                      >
                    </v-col>
                    <v-col v-if="item.fbpPanels?.length" cols="12">
                      <v-expansion-panels v-model="item.fbpPanels" class="mt-3">
                        <v-expansion-panel>
                          <v-expansion-panel-text>
                            <v-btn
                              icon="fas fa-times"
                              variant="text"
                              @click="item.fbpPanels = []"
                              size="x-small"
                              class="position-absolute top-0 right-0"
                              color="grey-lighten-1"
                            ></v-btn>
                            <v-form :ref="`fbp-${item.Allowance_Type_Id}`">
                              <v-row>
                                <v-col cols="12" sm="4">
                                  <v-text-field
                                    v-model="item.fbpMaxAmount"
                                    variant="solo"
                                    density="comfortable"
                                    :rules="[
                                      required(
                                        'Max Declaration Amount',
                                        item.fbpMaxAmount
                                      ),
                                      minMaxNumberValidation(
                                        'Max Declaration Amount',
                                        item.fbpMaxAmount,
                                        0,
                                        99999999
                                      ),
                                      twoDecimalPrecisionValidation(
                                        item.fbpMaxAmount
                                      ),
                                    ]"
                                    type="number"
                                    @update:model-value="updateBasicPay"
                                  >
                                    <template v-slot:label>
                                      Max Declaration Amount
                                      <span class="text-red">*</span>
                                    </template>
                                  </v-text-field>
                                </v-col>
                              </v-row>
                              <div class="d-flex justify-end">
                                <v-btn
                                  class="mt-3"
                                  rounded="lg"
                                  color="primary"
                                  size="small"
                                  variant="elevated"
                                  @click="updateFBP(item)"
                                  >Save</v-btn
                                >
                              </div>
                            </v-form>
                          </v-expansion-panel-text>
                        </v-expansion-panel>
                      </v-expansion-panels>
                    </v-col>
                  </v-row>
                </div>
                <div v-if="selectedBonuses.length">
                  <v-row class="mt-2">
                    <v-col class="font-weight-bold text-subtitle-1">
                      Bonus
                    </v-col>
                  </v-row>
                  <v-row
                    v-for="item of selectedBonuses"
                    :key="item.Allowance_Type_Id"
                    class="d-flex align-center mt-0"
                  >
                    <v-col cols="4" class="text-subtitle-2 d-flex">
                      <span>{{ checkNullValue(item.Allowance_Name) }}</span>
                      <div
                        class="text-caption bg-primary px-1 rounded elevation-2 d-flex align-center justify-center ml-2"
                        style="color: white; width: max-content"
                      >
                        {{ item.Period }}
                      </div>
                    </v-col>
                    <v-col>
                      <v-text-field
                        v-if="
                          item.Allowance_Type?.toLowerCase() === 'percentage'
                        "
                        v-model="item.Percentage"
                        variant="solo"
                        density="comfortable"
                        :rules="[
                          twoDecimalPrecisionValidation(item.Percentage),
                        ]"
                        :hint="
                          item.Amount ? `${item.Amount}(${item.Period})` : ''
                        "
                        :persistent-hint="true"
                        type="number"
                        @update:model-value="updateBasicPay"
                      >
                        <template v-slot:append-inner>
                          <span
                            class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                            style="width: max-content; height: 100%"
                            >%</span
                          >
                        </template>
                      </v-text-field>
                      <v-text-field
                        v-else
                        v-model="item.Amount"
                        variant="solo"
                        density="comfortable"
                        :rules="[twoDecimalPrecisionValidation(item.Amount)]"
                        type="number"
                        @update:model-value="updateBasicPay"
                      >
                        <template v-slot:append-inner>
                          <span
                            class="bg-grey-lighten-3 d-flex justify-center align-center px-3 text-body-2"
                            style="width: max-content; height: 100%"
                            >Amount</span
                          >
                        </template>
                      </v-text-field>
                    </v-col>
                    <v-col
                      class="text-body-1"
                      :class="anyValueUpdated ? 'text-start' : 'text-end'"
                    >
                      <div :class="anyValueUpdated ? '' : 'pr-8'">
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : (item.Amount / periodMap[item.Period][0]).toFixed(
                                2
                              )
                        }}
                      </div>
                    </v-col>
                    <v-col
                      class="text-body-1 text-end d-flex align-center justify-end"
                    >
                      <div :class="anyValueUpdated ? '' : 'pr-5'">
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : (item.Amount * periodMap[item.Period][1]).toFixed(
                                2
                              )
                        }}
                      </div>
                      <v-icon
                        size="15"
                        @click="removeComponent('bonuses', item)"
                        >fas fa-times</v-icon
                      >
                    </v-col>
                  </v-row>
                </div>
                <div v-if="selectedRetirals.length">
                  <v-row class="mt-2">
                    <v-col class="font-weight-bold text-subtitle-1">
                      Retirals
                    </v-col>
                  </v-row>
                  <v-row
                    v-for="item of selectedRetirals"
                    :key="item.id"
                    class="d-flex align-center mt-0"
                  >
                    <v-col cols="4" class="text-subtitle-2 d-flex align-center">
                      <span class="mr-2">{{
                        item.Insurance_Name
                          ? item.Insurance_Name
                          : getCustomFormName(item.Form_Id)
                      }}</span>
                      <v-icon
                        v-if="item.Form_Id === 52"
                        size="12"
                        class="ml-2"
                        color="primary"
                        @click="openExpansionPanel(item)"
                        >fas fa-edit</v-icon
                      >
                      <div
                        v-if="item.Insurance_Type?.toLowerCase() === 'fixed'"
                        class="text-caption bg-primary px-1 rounded elevation-2 d-flex align-center justify-center ml-2"
                        style="color: white; width: max-content"
                      >
                        {{ item.Payment_Frequency }}
                      </div>
                    </v-col>
                    <v-col
                      v-if="item.Insurance_Type?.toLowerCase() === 'fixed'"
                      class="text-body-1"
                    >
                      {{
                        item.Employer_Share_Amount && item.Payment_Frequency
                          ? `${item.Employer_Share_Amount} (${item.Payment_Frequency})`
                          : "Fixed Amount"
                      }}
                    </v-col>
                    <v-col
                      v-else-if="
                        item.Slab_Wise_Insurance?.toLowerCase() === 'yes'
                      "
                      class="text-body-1"
                      >Slab Wise
                    </v-col>
                    <v-col v-else-if="item.Form_Id === 52" class="text-body-1">
                      {{
                        item.PF_Employer_Contribution == "Actual"
                          ? `${item.Employer_Share_Percentage}% of Actual PF Wage`
                          : "Restrict Contribution to 15000 of PF Wage"
                      }}
                    </v-col>
                    <v-col v-else class="text-body-1">
                      {{
                        item.Retiral_Type?.toLowerCase() === "percentage" ||
                        item.Retiral_Type?.toLowerCase() === "variable"
                          ? `${item.Employer_Share_Percentage || 0}%`
                          : "Fixed Amount"
                      }}
                    </v-col>
                    <v-col
                      class="text-body-1"
                      :class="anyValueUpdated ? 'text-start' : 'text-end'"
                    >
                      <div
                        v-if="item.Insurance_Type?.toLowerCase() === 'fixed'"
                        :class="anyValueUpdated ? '' : 'pr-8'"
                      >
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : (
                                item.Employer_Share_Amount /
                                periodMap[item.Payment_Frequency][0]
                              ).toFixed(2) || 0
                        }}
                      </div>
                      <div v-else-if="item.Form_Id === 110">
                        <div :class="anyValueUpdated ? '' : 'pr-8'">
                          {{
                            anyValueUpdated
                              ? "System Calculated"
                              : (item.Employer_Share_Amount / 12).toFixed(2) ||
                                0
                          }}
                        </div>
                      </div>
                      <div v-else :class="anyValueUpdated ? '' : 'pr-8'">
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : item.Employer_Share_Amount?.toFixed(2) || 0
                        }}
                      </div>
                    </v-col>
                    <v-col
                      class="text-body-1 text-end d-flex align-center justify-end"
                    >
                      <div
                        v-if="item.Insurance_Type?.toLowerCase() === 'fixed'"
                      >
                        <div :class="anyValueUpdated ? '' : 'pr-5'">
                          {{
                            anyValueUpdated
                              ? "System Calculated"
                              : item.Employer_Share_Amount &&
                                item.Payment_Frequency
                              ? (
                                  item.Employer_Share_Amount *
                                  periodMap[item.Payment_Frequency][1]
                                ).toFixed(2)
                              : 0
                          }}
                        </div>
                      </div>
                      <div v-else-if="item.Form_Id === 110">
                        <div :class="anyValueUpdated ? '' : 'pr-5'">
                          {{
                            anyValueUpdated
                              ? "System Calculated"
                              : item.Employer_Share_Amount?.toFixed(2) || 0
                          }}
                        </div>
                      </div>
                      <div v-else :class="anyValueUpdated ? '' : 'pr-5'">
                        {{
                          anyValueUpdated
                            ? "System Calculated"
                            : item.Employer_Share_Amount
                            ? (item.Employer_Share_Amount * 12).toFixed(2)
                            : 0
                        }}
                      </div>
                      <v-icon
                        size="15"
                        @click="removeComponent('retirals', item)"
                        >fas fa-times</v-icon
                      >
                    </v-col>
                    <v-col v-if="item.expansionPanel?.length" cols="12">
                      <v-expansion-panels
                        v-model="item.expansionPanel"
                        class="mt-3"
                      >
                        <v-expansion-panel>
                          <v-expansion-panel-text>
                            <v-btn
                              icon="fas fa-times"
                              variant="text"
                              @click="item.expansionPanel = []"
                              size="x-small"
                              class="position-absolute top-0 right-0"
                              color="grey-lighten-1"
                            ></v-btn>
                            <v-form
                              :ref="`retirals-${
                                item.Retirals_Id || item.Form_Id
                              }`"
                            >
                              <v-row>
                                <v-col cols="12" sm="4">
                                  <CustomSelect
                                    :item-selected="item.tempEmployeeType"
                                    :items="[
                                      {
                                        title: `${item.Employee_Share_Percentage}% of Actual PF Wage`,
                                        value: 'actualWage',
                                      },
                                      {
                                        title: `Restrict Contribution to 15000 of PF Wage`,
                                        value: 'restrict',
                                      },
                                    ]"
                                    item-title="title"
                                    item-value="value"
                                    label="Employee Contribution Rate"
                                    variant="solo"
                                    density="comfortable"
                                    :disabled="
                                      item.disableEmployeeContribution
                                        ? true
                                        : false
                                    "
                                    @selected-item="
                                      item.tempEmployeeType = $event
                                    "
                                  ></CustomSelect>
                                </v-col>
                                <v-col cols="12" sm="4">
                                  <CustomSelect
                                    :item-selected="item.tempEmployerType"
                                    :items="[
                                      {
                                        title: `${item.Employer_Share_Percentage}% of Actual PF Wage`,
                                        value: 'actualWage',
                                      },
                                      {
                                        title: `Restrict Contribution to 15000 of PF Wage`,
                                        value: 'restrict',
                                      },
                                    ]"
                                    item-title="title"
                                    item-value="value"
                                    label="Employer Contribution Rate"
                                    variant="solo"
                                    density="comfortable"
                                    @selected-item="
                                      employerContributionChanged(item, $event)
                                    "
                                  ></CustomSelect>
                                </v-col>
                              </v-row>
                              <div class="d-flex justify-end">
                                <v-btn
                                  class="mt-3"
                                  rounded="lg"
                                  color="primary"
                                  size="small"
                                  variant="elevated"
                                  @click="updateRetirals(item)"
                                  >Save</v-btn
                                >
                              </div>
                            </v-form>
                          </v-expansion-panel-text>
                        </v-expansion-panel>
                      </v-expansion-panels>
                    </v-col>
                  </v-row>
                </div>
              </v-card>
              <v-card
                v-if="anyValueUpdated"
                class="pa-2 d-flex align-center mt-3"
              >
                <v-row>
                  <v-col class="d-flex align-center">
                    <v-icon color="blue" class="mr-4"
                      >fas fa-info-circle</v-icon
                    >
                    <div>
                      <div>
                        <span class="text-subtitle-1 font-weight-medium mr-2"
                          >System Calculated Components' Total</span
                        >
                        <span
                          class="text-blue text-subtitle-1 font-weight-medium cursor-pointer"
                          v-if="
                            sumOfComponetsMinusCTC >= 0 && !componentsLoading
                          "
                          @click="getSystemCalculatedComponets()"
                          >(Preview)</span
                        >
                        <span v-else-if="componentsLoading"
                          >Calculating
                          <v-progress-circular
                            size="15"
                            color="primary"
                            indeterminate
                          ></v-progress-circular
                        ></span>
                      </div>
                      <div
                        class="text-caption text-grey-darken-1"
                        v-if="sumOfComponetsMinusCTC < 0"
                      >
                        Amount must be greater than zero. Adjust the CTC or any
                        of the component's amount.
                      </div>
                    </div>
                  </v-col>
                  <v-col
                    cols="3"
                    :class="sumOfComponetsMinusCTC < 0 ? 'text-red' : ''"
                    class="text-end"
                  >
                    {{ sumOfComponetsMinusCTC?.toFixed(2) }}
                  </v-col>
                  <v-col
                    cols="3"
                    :class="sumOfComponetsMinusCTC < 0 ? 'text-red' : ''"
                    class="text-end pr-10"
                  >
                    {{ (sumOfComponetsMinusCTC * 12)?.toFixed(2) }}
                  </v-col>
                </v-row>
              </v-card>
              <v-card class="pa-2 mt-3">
                <v-row>
                  <v-col cols="4" class="bg-grey-lighten-4 text-body-1"
                    >Cost to Company (CTC)</v-col
                  >
                  <v-col class="bg-grey-lighten-4"></v-col>
                  <v-col class="bg-grey-lighten-4 text-body-1 text-end">
                    <div class="pr-8">
                      {{ payrollCurrency }}
                      {{
                        parseFloat(monthlyCTC)
                          ? parseFloat(monthlyCTC)?.toFixed(2)
                          : 0
                      }}
                    </div>
                  </v-col>
                  <v-col class="bg-grey-lighten-4 text-body-1 text-end">
                    <div class="pr-5">
                      {{ payrollCurrency }}
                      {{ parseFloat(annualCTC)?.toFixed(2) || 0 }}
                    </div>
                  </v-col>
                </v-row>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card
        v-if="windowWidth >= 770"
        class="overlay-footer pa-2"
        elevation="16"
      >
        <v-btn
          class="mr-2"
          variant="text"
          color="primary"
          elevation="4"
          rounded="lg"
          @click="closeForm()"
          >Cancel</v-btn
        >
        <v-tooltip
          text="Please click on 'Preview' to get the System Calculated Components' before saving the template."
          location="top"
          max-width="300px"
        >
          <template v-slot:activator="{ props }">
            <div
              v-bind="
                anyValueUpdated || sumOfComponetsMinusCTC < 0 ? props : ''
              "
            >
              <v-btn
                color="primary"
                rounded="lg"
                :disabled="anyValueUpdated || sumOfComponetsMinusCTC < 0"
                @click="validateForm()"
                >Save</v-btn
              >
            </div>
          </template>
        </v-tooltip>
      </v-card>
      <AppLoading v-if="isLoading"></AppLoading>
    </v-card>
  </v-overlay>
  <AppWarningModal
    v-if="openConfirmationModel"
    :open-modal="openConfirmationModel"
    confirmation-heading="Are you sure to exit this form?"
    imgUrl="common/exit_form"
    @close-warning-modal="openConfirmationModel = false"
    @accept-modal="$emit('close-form')"
  />
</template>
<script>
import { defineAsyncComponent } from "vue";
import validationRules from "@/mixins/validationRules";
import { checkNullValue } from "@/helper";
import {
  RETRIEVE_SALARY_COMPONENTS,
  ADD_UPDATE_SALARY_DETAILS,
  RETRIEVE_SYSTEM_CALCULATED_COMPONENTS,
} from "@/graphql/corehr/salaryQueries";
const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
export default {
  name: "AddUpdateSalaryTemplate",
  mixins: [validationRules],
  emits: ["close-form", "add-update-success"],
  components: {
    CustomSelect,
  },
  props: {
    showForm: {
      type: Boolean,
      default: false,
    },
    payrollCurrency: {
      type: String,
      required: true,
    },
    selectedItem: {
      type: Object,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isLoading: false,
      showOverlay: false,
      openConfirmationModel: false,
      salaryTemplateName: "",
      salaryTemplateDescription: "",
      annualCTC: null,
      monthlyCTC: null,
      anyValueUpdated: true,
      sumOfComponetsMinusCTC: 0,
      basicPayType: "Percentage",
      basicPayPercentage: 50,
      basicPayAmount: null,
      fixedAllowanceAmount: 0,
      salaryComponentPanels: [],
      salaryComponents: {
        Earnings: [],
        Reimbursements: [],
        "FBP Components": [],
        Bonuses: [],
        Retirals: [],
      },
      selectedEarnings: [],
      selectedReimbursements: [],
      selectedFBPComponents: [],
      selectedBonuses: [],
      selectedRetirals: [],
      basicPayArray: [],
      fixedAllowanceArray: [],
      componentsLoading: false,
      periodMap: {
        Monthly: [1, 12],
        Quarterly: [3, 4],
        HalfYearly: [6, 2],
        Annually: [12, 1],
      },
      monthlyGrossSalary: 0,
    };
  },
  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
    formsBasedOnFormId() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    getCustomFormName() {
      return (formId) => {
        let form = this.formsBasedOnFormId(formId);
        return form ? form.customFormName : "";
      };
    },
    allSelected() {
      return (title) => {
        let all = this.salaryComponents[title].every((item) => item.isSelected);
        return all;
      };
    },
    providentFundConfiguration() {
      return this.$store.state.orgDetails.providentFundConfiguration;
    },
  },
  watch: {
    showForm(val) {
      this.showOverlay = val;
    },
  },
  mounted() {
    this.showOverlay = this.showForm;
    this.retrieveSalaryComponents();
  },
  methods: {
    checkNullValue,
    closeForm() {
      this.openConfirmationModel = true;
    },
    getRedirectionUrl(item) {
      let url = this.baseUrl;
      if (item?.toLowerCase() === "earnings") {
        url += `v3/settings/payroll/salary-components?type=${this.$t(
          "settings.earnings"
        )}`;
      } else if (item?.toLowerCase() === "reimbursements") {
        url += `v3/settings/payroll/salary-components?type=${this.$t(
          "settings.reimbursements"
        )}`;
      } else if (item?.toLowerCase() === "fbp components") {
        url += `v3/settings/payroll/salary-components?type=${this.$t(
          "settings.earnings"
        )}`;
      } else if (item?.toLowerCase() === "bonuses") {
        url += `v3/settings/payroll/salary-components?type=${this.$t(
          "settings.bonus"
        )}`;
      } else if (item?.toLowerCase() === "retirals") {
        url += "v3/tax-and-statutory-compliance/statutory-components";
      }
      return url;
    },
    retrieveSalaryComponents() {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_SALARY_COMPONENTS,
          client: "apolloClientF",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.listSalaryComponents &&
            response.data.listSalaryComponents.salaryComponents
          ) {
            let components = JSON.parse(
              response.data.listSalaryComponents.salaryComponents
            );
            if (components?.allowances) {
              vm.salaryComponents.Earnings =
                components.allowances.allowanceArray || [];
              vm.salaryComponents.Reimbursements =
                components.allowances.reimbursementArray || [];
              vm.salaryComponents["FBP Components"] =
                components.allowances.flexiBenefitPlanArray || [];
              vm.salaryComponents.Bonuses =
                components.allowances.bonusArray || [];
              vm.basicPayArray = components.allowances.basicPayArray || [];
              vm.basicPayType = vm.basicPayArray[0]?.Allowance_Type;
              if (vm.basicPayType?.toLowerCase() === "percentage") {
                vm.basicPayPercentage = vm.basicPayArray[0]?.Percentage;
              } else {
                vm.basicPayAmount = vm.basicPayArray[0]?.Amount;
              }
              vm.fixedAllowanceArray =
                components.allowances.fixedAllowanceArray || [];
              let retirals = [];
              if (
                components.retirals &&
                Object.keys(components.retirals).length
              ) {
                [
                  "esiDetails",
                  "variableInsurance",
                  "fixedInsurance",
                  "slabWiseInsurance",
                ].forEach((item) => {
                  retirals = [...retirals, ...components.retirals[item]];
                });
                retirals.forEach((item) => {
                  item.Retirals_Id = item.InsuranceType_Id;
                });
                if (components.retirals.gratuityDetails?.length) {
                  let obj = {
                    Form_Id: components.retirals.gratuityDetails[0].Form_Id,
                    Retiral_Type:
                      components.retirals.gratuityDetails[0].Retiral_Type,
                  };
                  retirals.push(obj);
                }
                if (components.retirals.pfDetails?.length) {
                  let obj = {
                    Form_Id: components.retirals.pfDetails[0].Form_Id,
                    Retiral_Type: components.retirals.pfDetails[0].Retiral_Type,
                    Employee_Share_Amount:
                      components.retirals.pfDetails[0].Retiral_Type?.toLowerCase() ===
                      "percentage"
                        ? null
                        : components.retirals.pfDetails[0].Employee_Share,
                    Employer_Share_Amount:
                      components.retirals.pfDetails[0].Retiral_Type?.toLowerCase() ===
                      "percentage"
                        ? null
                        : components.retirals.pfDetails[0].Employer_Share,
                    Employee_Share_Percentage:
                      components.retirals.pfDetails[0].Retiral_Type?.toLowerCase() ===
                      "percentage"
                        ? components.retirals.pfDetails[0].Employee_Share
                        : null,
                    Employer_Share_Percentage:
                      components.retirals.pfDetails[0].Retiral_Type?.toLowerCase() ===
                      "percentage"
                        ? components.retirals.pfDetails[0].Employer_Share
                        : null,
                    PF_Employee_Contribution:
                      components.retirals.pfDetails[0].Employee_Contribution_Rate?.toLowerCase() ===
                      "restricted"
                        ? "Restrict"
                        : "Actual",
                    PF_Employer_Contribution:
                      components.retirals.pfDetails[0].Employer_Contribution_Rate?.toLowerCase() ===
                      "restricted"
                        ? "Restrict"
                        : "Actual",
                  };
                  retirals.push(obj);
                }
                if (components.retirals.npsDetails?.length) {
                  let obj = {
                    Form_Id: components.retirals.npsDetails[0].Form_Id,
                    Retiral_Type:
                      components.retirals.npsDetails[0].Retiral_Type,
                    Employee_Share_Amount:
                      components.retirals.npsDetails[0].Retiral_Type?.toLowerCase() ===
                      "percentage"
                        ? null
                        : components.retirals.npsDetails[0].Employee_Share,
                    Employer_Share_Amount:
                      components.retirals.npsDetails[0].Retiral_Type?.toLowerCase() ===
                      "percentage"
                        ? null
                        : components.retirals.npsDetails[0].Employer_Share,
                    Employee_Share_Percentage:
                      components.retirals.npsDetails[0].Retiral_Type?.toLowerCase() ===
                      "percentage"
                        ? components.retirals.npsDetails[0].Employee_Share
                        : null,
                    Employer_Share_Percentage:
                      components.retirals.npsDetails[0].Retiral_Type?.toLowerCase() ===
                      "percentage"
                        ? components.retirals.npsDetails[0].Employer_Share
                        : null,
                  };
                  retirals.push(obj);
                }
              }
              vm.salaryComponents.Retirals = retirals;
            }

            if (vm.isEdit) {
              vm.retrieveTemplateDetails();
              vm.anyValueUpdated = false;
            }
          }
          if (!vm.isEdit) {
            vm.isLoading = false;
          }
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieve",
            form: "salary template",
            isListError: false,
          });
        });
    },
    async retrieveTemplateDetails() {
      let vm = this;
      vm.isLoading = true;
      await vm.$store
        .dispatch("getSalaryDetails", {
          formId: 206,
          isViewMode: true,
          isDropdown: false,
          templateId: this.selectedItem.Template_Id,
        })
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let templateDetails = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );
            if (templateDetails && templateDetails[0]) {
              let details = templateDetails[0];
              vm.salaryTemplateName = details.Template_Name;
              vm.salaryTemplateDescription = details.Description;
              vm.annualCTC = details.Annual_CTC;
              vm.monthlyCTC = (details.Annual_CTC / 12).toFixed(2);
              if (details.allowances?.basicPayArray?.length) {
                vm.basicPayType =
                  details.allowances.basicPayArray[0].Allowance_Type;
                vm.basicPayPercentage =
                  details.allowances.basicPayArray[0].Percentage;
                vm.basicPayAmount = details.allowances.basicPayArray[0].Amount;
                vm.basicPayArray = details.allowances.basicPayArray;
              }
              if (details.allowances?.fixedAllowanceArray?.length) {
                this.fixedAllowanceAmount =
                  details.allowances.fixedAllowanceArray[0].Amount;
                this.fixedAllowanceArray =
                  details.allowances.fixedAllowanceArray;
              }
              if (details.allowances?.allowanceArray) {
                details.allowances.allowanceArray.map((item) => {
                  this.salaryComponents.Earnings.forEach((e) => {
                    if (item.Allowance_Type_Id == e.Allowance_Type_Id) {
                      e.isSelected = true;
                    }
                  });
                });
                this.selectedEarnings = details.allowances.allowanceArray || [];
              }
              if (details.allowances?.reimbursementArray) {
                details.allowances.reimbursementArray.map((item) => {
                  this.salaryComponents.Reimbursements.forEach((e) => {
                    if (item.Allowance_Type_Id == e.Allowance_Type_Id) {
                      e.isSelected = true;
                    }
                  });
                });
                this.selectedReimbursements =
                  details.allowances.reimbursementArray || [];
              }
              if (details.allowances?.flexiBenefitPlanArray) {
                details.allowances.flexiBenefitPlanArray.map((item) => {
                  this.salaryComponents["FBP Components"].forEach((e) => {
                    if (item.Allowance_Type_Id == e.Allowance_Type_Id) {
                      e.isSelected = true;
                    }
                  });
                });
                this.selectedFBPComponents =
                  details.allowances.flexiBenefitPlanArray || [];
              }
              if (details.allowances?.bonusArray) {
                details.allowances.bonusArray.map((item) => {
                  this.salaryComponents.Bonuses.forEach((e) => {
                    if (item.Allowance_Type_Id == e.Allowance_Type_Id) {
                      e.isSelected = true;
                    }
                  });
                });
                this.selectedBonuses = details.allowances.bonusArray || [];
              }
              if (details.retirals) {
                const keySet = new Set(
                  details.retirals.map((item) =>
                    item.Retirals_Id
                      ? `R|${item.Retirals_Id}`
                      : `F|${item.Form_Id}`
                  )
                );

                this.salaryComponents.Retirals.forEach((e) => {
                  const key = e.Retirals_Id
                    ? `R|${e.Retirals_Id}`
                    : `F|${e.Form_Id}`;
                  if (keySet.has(key)) {
                    e.isSelected = true;
                  }
                });
                details.retirals.forEach((item) => {
                  item.Retiral_Type = item.Retirals_Type;
                });
                this.selectedRetirals = details.retirals || [];
              }
            }
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "salary details",
            isListError: false,
          });
        });
    },
    updateAnnualCTC() {
      this.monthlyCTC = (this.annualCTC / 12).toFixed(2);
      this.updateBasicPay();
    },
    updateBasicPay() {
      if (this.basicPayType?.toLowerCase() === "percentage") {
        const mutiplier = this.basicPayPercentage / 100;
        const basicPay = (this.annualCTC * mutiplier) / 12;
        if (basicPay - parseInt(basicPay) > 0) {
          this.basicPayAmount = basicPay.toFixed(2);
        } else {
          this.basicPayAmount = basicPay;
        }
      }
      this.selectedEarnings.forEach((item) => {
        if (item?.Allowance_Type?.toLowerCase() === "percentage") {
          const mutiplier = item.Percentage / 100;
          const basicPay = this.basicPayAmount * mutiplier;
          if (basicPay - parseInt(basicPay) > 0) {
            item.Amount = basicPay.toFixed(2);
          } else {
            item.Amount = basicPay;
          }
        }
      });
      this.selectedReimbursements.forEach((item) => {
        if (item?.Allowance_Type?.toLowerCase() === "percentage") {
          const mutiplier = item.Percentage / 100;
          const basicPay = this.basicPayAmount * mutiplier;
          if (basicPay - parseInt(basicPay) > 0) {
            item.Amount = basicPay.toFixed(2);
          } else {
            item.Amount = basicPay;
          }
        }
      });
      this.calculateSumOfComponetsMinusCTC();
    },
    calculateSumOfComponetsMinusCTC() {
      this.anyValueUpdated = true;
      let total = 0;
      total += parseInt(this.basicPayAmount);
      this.selectedEarnings.forEach((item) => {
        total += parseInt(item.Amount);
      });
      this.selectedReimbursements.forEach((item) => {
        total += parseInt(item.Amount);
      });
      this.selectedFBPComponents.forEach((item) => {
        total += parseInt(item.Amount);
      });
      this.sumOfComponetsMinusCTC = this.monthlyCTC
        ? this.monthlyCTC - total
        : 0;
    },
    selectComponent(item, salaryComponent) {
      salaryComponent.isSelected = true;
      let component = JSON.parse(JSON.stringify(salaryComponent));
      if (item?.toLowerCase() === "earnings") {
        this.selectedEarnings.push(component);
      } else if (item?.toLowerCase() === "reimbursements") {
        this.selectedReimbursements.push(component);
      } else if (item?.toLowerCase() === "fbp components") {
        this.selectedFBPComponents.push(component);
      } else if (item?.toLowerCase() === "bonuses") {
        this.selectedBonuses.push(component);
      } else if (item?.toLowerCase() === "retirals") {
        this.selectedRetirals.push(component);
      }
      this.updateBasicPay();
    },
    removeComponent(title, component) {
      if (title?.toLowerCase() === "earnings") {
        this.selectedEarnings = this.selectedEarnings.filter(
          (c) => c.Allowance_Type_Id !== component.Allowance_Type_Id
        );
        this.salaryComponents["Earnings"].forEach((item) => {
          if (item.Allowance_Type_Id === component.Allowance_Type_Id) {
            item.isSelected = false;
          }
        });
      } else if (title?.toLowerCase() === "reimbursements") {
        this.selectedReimbursements = this.selectedReimbursements.filter(
          (c) => c.Allowance_Type_Id !== component.Allowance_Type_Id
        );
        this.salaryComponents["Reimbursements"].forEach((item) => {
          if (item.Allowance_Type_Id === component.Allowance_Type_Id) {
            item.isSelected = false;
          }
        });
      } else if (title?.toLowerCase() === "fbp components") {
        this.selectedFBPComponents = this.selectedFBPComponents.filter(
          (c) => c.Allowance_Type_Id !== component.Allowance_Type_Id
        );
        this.salaryComponents["FBP Components"].forEach((item) => {
          if (item.Allowance_Type_Id === component.Allowance_Type_Id) {
            item.isSelected = false;
          }
        });
      } else if (title?.toLowerCase() === "bonuses") {
        this.selectedBonuses = this.selectedBonuses.filter(
          (c) => c.Allowance_Type_Id !== component.Allowance_Type_Id
        );
        this.salaryComponents["Bonuses"].forEach((item) => {
          if (item.Allowance_Type_Id === component.Allowance_Type_Id) {
            item.isSelected = false;
          }
        });
      } else if (title?.toLowerCase() === "retirals") {
        this.selectedRetirals = this.selectedRetirals.filter((c) => {
          const id = c.Retirals_Id || c.Form_Id;
          const componentId = component.Retirals_Id || component.Form_Id;
          return id !== componentId;
        });
        this.salaryComponents.Retirals.forEach((item) => {
          const itemId = item.Retirals_Id || item.Form_Id;
          const componentId = component.Retirals_Id || component.Form_Id;
          if (itemId === componentId) {
            item.isSelected = false;
          }
        });
      }
      this.updateBasicPay();
    },
    getSystemCalculatedComponets() {
      let vm = this;
      vm.componentsLoading = true;
      let salaryDetails = {
        Annual_Ctc: parseInt(vm.annualCTC),
        Basic_Pay: parseInt(vm.basicPayAmount),
        Effective_From: "",
        Effective_To: "",
        ESI_Contribution_End_Date: null,
        Status: "Active",
      };
      let allowanceDetails = [],
        retiralDetails = [];
      allowanceDetails.push({
        Allowance_Type_Id: vm.basicPayArray[0]?.Allowance_Type_Id,
        Allowance_Type: vm.basicPayType,
        Percentage: vm.basicPayPercentage,
        Amount: parseInt(vm.basicPayAmount),
      });
      allowanceDetails.push({
        Allowance_Type_Id: vm.fixedAllowanceArray[0]?.Allowance_Type_Id,
        Allowance_Type: "Fixed",
        Percentage: null,
        Amount: parseInt(vm.fixedAllowanceAmount),
      });
      this.selectedEarnings.forEach((item) => {
        allowanceDetails.push({
          Allowance_Type_Id: item.Allowance_Type_Id,
          Allowance_Type: item.Allowance_Type,
          Percentage: item.Percentage,
          Amount: parseInt(item.Amount),
        });
      });
      this.selectedReimbursements.forEach((item) => {
        allowanceDetails.push({
          Allowance_Type_Id: item.Allowance_Type_Id,
          Allowance_Type: item.Allowance_Type,
          Percentage: item.Percentage,
          Amount: parseInt(item.Amount),
        });
      });
      this.selectedFBPComponents.forEach((item) => {
        allowanceDetails.push({
          Allowance_Type_Id: item.Allowance_Type_Id,
          Allowance_Type: item.Allowance_Type,
          Percentage: item.Percentage,
          Amount: parseInt(item.Amount),
        });
      });
      this.selectedBonuses.forEach((item) => {
        allowanceDetails.push({
          Allowance_Type_Id: item.Allowance_Type_Id,
          Allowance_Type: item.Allowance_Type,
          Percentage: item.Percentage,
          Amount: parseInt(item.Amount),
        });
      });
      this.selectedRetirals.forEach((item) => {
        retiralDetails.push({
          Form_Id: item.Form_Id,
          Retirals_Id: item.Retirals_Id,
          Retirals_Type: item.Retiral_Type,
          Retiral_Wages: item.Retiral_Wages,
          Employee_Share_Percentage: item.Employee_Share_Percentage,
          Employer_Share_Percentage: item.Employer_Share_Percentage,
          Employee_Share_Amount: parseInt(item.Employee_Share_Amount) || null,
          Employer_Share_Amount: parseInt(item.Employer_Share_Amount) || null,
          PF_Employee_Contribution: item.PF_Employee_Contribution,
          PF_Employer_Contribution: item.PF_Employer_Contribution,
          Employee_Statutory_Limit: item.Employee_Statutory_Limit,
          Employer_Statutory_Limit: item.Employer_Statutory_Limit,
          Eligible_for_EPS: item.Eligible_for_EPS,
          Admin_Charge: item.Admin_Charge,
          EDLI_Charge: item.EDLI_Charge,
        });
      });
      vm.$apollo
        .query({
          query: RETRIEVE_SYSTEM_CALCULATED_COMPONENTS,
          client: "apolloClientAT",
          variables: {
            employeeId: 0,
            retiralDetails: JSON.stringify(retiralDetails),
            allowanceDetails: JSON.stringify(allowanceDetails),
            salaryDetails: JSON.stringify(salaryDetails),
            providentFundConfigurationValue: vm.providentFundConfiguration,
          },
          fetchPolicy: "no-cache",
        })
        .then(({ data }) => {
          if (
            data?.calculateSalary?.employeeRetiralDetails &&
            !data.calculateSalary.errorCode
          ) {
            let salaryStructure = JSON.parse(
              data?.calculateSalary?.salaryStructure
            );
            this.fixedAllowanceAmount = salaryStructure.fixedAllowance;
            this.monthlyGrossSalary = salaryStructure.grossSalary;
            let salaryComponents = JSON.parse(
              data?.calculateSalary?.employeeRetiralDetails
            );
            if (salaryComponents?.employeeSalaryRetirals?.length) {
              const matchedKeys = new Set(
                salaryComponents.employeeSalaryRetirals
                  .map((item) => {
                    if (item.Retirals_Id && item.Form_Id) {
                      return `${item.Retirals_Id}|${item.Form_Id}`;
                    } else if (item.Form_Id) {
                      return `${item.Form_Id}`;
                    } else {
                      return "";
                    }
                  })
                  .filter(Boolean)
              );

              // First, mark all non-matching items as not selected in the original array
              if (this.salaryComponents && this.salaryComponents["Retirals"]) {
                this.salaryComponents["Retirals"].forEach((retiralItem) => {
                  const key = retiralItem.Retirals_Id
                    ? `${retiralItem.Retirals_Id}|${retiralItem.Form_Id}`
                    : `${retiralItem.Form_Id}`;
                  if (!matchedKeys.has(key)) {
                    retiralItem.isSelected = false;
                  }
                });
              }

              // Then filter the selectedRetirals as before
              this.selectedRetirals = this.selectedRetirals.filter(
                (retiralItem) => {
                  const key = retiralItem.Retirals_Id
                    ? `${retiralItem.Retirals_Id}|${retiralItem.Form_Id}`
                    : `${retiralItem.Form_Id}`;
                  const match = matchedKeys.has(key);
                  if (match) {
                    const matchedItem =
                      salaryComponents.employeeSalaryRetirals.find((item) => {
                        const bothIdsMatch =
                          item.Retirals_Id &&
                          retiralItem.Retirals_Id &&
                          parseInt(item.Retirals_Id) ===
                            parseInt(retiralItem.Retirals_Id) &&
                          parseInt(item.Form_Id) ===
                            parseInt(retiralItem.Form_Id);

                        const onlyFormIdMatch =
                          !item.Retirals_Id &&
                          !retiralItem.Retirals_Id &&
                          parseInt(item.Form_Id) ===
                            parseInt(retiralItem.Form_Id);

                        return bothIdsMatch || onlyFormIdMatch;
                      });
                    if (matchedItem) {
                      retiralItem.Retiral_Type = matchedItem.Retiral_Type;
                      retiralItem.Employer_Share_Amount =
                        matchedItem.Employer_Share_Amount;
                      retiralItem.Employer_Share_Percentage =
                        matchedItem.Employer_Share_Percentage;
                      retiralItem.Employer_Retiral_Wages =
                        matchedItem.Employer_Retiral_Wages;
                      retiralItem.Employee_Share_Amount =
                        matchedItem.Employee_Share_Amount;
                      retiralItem.Employee_Share_Percentage =
                        matchedItem.Employee_Share_Percentage;
                      retiralItem.Employee_Retiral_Wages =
                        matchedItem.Employee_Retiral_Wages;
                      retiralItem.Admin_Charge = matchedItem.Admin_Charge;
                      retiralItem.EDLI_Charge = matchedItem.EDLI_Charge;
                    }
                  }
                  return match;
                }
              );
            } else {
              // If there are no employeeSalaryRetirals, mark all retirals as not selected
              if (this.salaryComponents && this.salaryComponents["Retirals"]) {
                this.salaryComponents["Retirals"].forEach((retiralItem) => {
                  retiralItem.isSelected = false;
                });
              }
              this.selectedRetirals = [];
            }

            if (salaryComponents?.employeeSalaryBonus?.length) {
              salaryComponents.employeeSalaryBonus.forEach((item) => {
                this.selectedBonuses.forEach((bonusItem) => {
                  if (
                    parseInt(item.Allowance_Type_Id) ===
                    parseInt(bonusItem.Allowance_Type_Id)
                  ) {
                    bonusItem.Amount = item.Amount;
                    bonusItem.Percentage = item.Percentage;
                    bonusItem.AllowanceWages = item.Allowance_Wage;
                  }
                });
              });
            }
            if (salaryComponents?.employeeSalaryAllowance?.length) {
              salaryComponents.employeeSalaryAllowance.forEach((item) => {
                this.selectedEarnings.forEach((ele) => {
                  if (ele.Allowance_Type_Id === item.Allowance_Type_Id) {
                    ele.AllowanceWages = item.Allowance_Wage;
                    ele.Amount = item.Amount || 0;
                  }
                });
                this.selectedReimbursements.forEach((ele) => {
                  if (ele.Allowance_Type_Id === item.Allowance_Type_Id) {
                    ele.AllowanceWages = item.Allowance_Wage;
                    ele.Amount = item.Amount || 0;
                  }
                });
                this.selectedFBPComponents.forEach((ele) => {
                  if (ele.Allowance_Type_Id === item.Allowance_Type_Id) {
                    ele.AllowanceWages = item.Allowance_Wage;
                    ele.Amount = item.Amount || 0;
                  }
                });
              });
            }
            vm.anyValueUpdated = false;
          } else {
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message:
                data?.calculateSalary?.message ||
                "Something went wrong. Please try after some time.",
            };
            vm.showAlert(snackbarData);
          }
          vm.componentsLoading = false;
        })
        .catch((err) => {
          vm.componentsLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "system calculated components",
            isListError: false,
          });
        });
    },
    async validateForm() {
      let { valid } = await this.$refs.form.validate();
      if (valid) {
        this.addUpdateSalaryTemplate();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          const fields = Array.isArray(field) ? field : [field];
          if (fields && fields[0] && fields[0].rules) {
            const allValid = fields[0].rules.every((value) => value === true);
            if (fields[0].rules.length > 0 && !allValid) {
              invalidFields.push(refName);
            }
          }
        });

        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              const fields = Array.isArray(fieldRef) ? fieldRef : [fieldRef];
              if (fields && fields[0] && fields[0].$el) {
                const element = fields[0].$el;
                if (element && element.scrollIntoView) {
                  element.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                } else if (element && element.getBoundingClientRect) {
                  const rect = element.getBoundingClientRect();
                  window.scrollTo({
                    top: window.scrollY + rect.top - 100, // adjust offset if needed
                    behavior: "smooth",
                  });
                }
              }
            }
          });
        }
      }
    },
    addUpdateSalaryTemplate() {
      let vm = this;
      vm.isLoading = true;
      let allowances = [],
        retirals = [];
      let basicPayObj = this.basicPayArray[0];
      let fixedAllowanceObj = this.fixedAllowanceArray[0];
      allowances.push({
        allowanceTypeId: basicPayObj?.Allowance_Type_Id,
        amount: this.basicPayAmount?.toString(),
        percentage: this.basicPayPercentage?.toString(),
        allowanceType: this.basicPayType,
        allowanceWages: this.basicPayAmount?.toString(),
      });
      allowances.push({
        allowanceTypeId: fixedAllowanceObj?.Allowance_Type_Id,
        amount: this.fixedAllowanceAmount?.toString(),
        percentage: null,
        allowanceType: "Fixed",
        allowanceWages: fixedAllowanceObj?.AllowanceWages?.toString(),
      });
      this.selectedEarnings.forEach((item) => {
        allowances.push({
          allowanceTypeId: item.Allowance_Type_Id,
          amount: item.Amount?.toString(),
          percentage: item.Percentage?.toString(),
          allowanceType: item.Allowance_Type,
          allowanceWages: item.AllowanceWages?.toString(),
        });
      });
      this.selectedReimbursements.forEach((item) => {
        allowances.push({
          allowanceTypeId: item.Allowance_Type_Id,
          amount: item.Amount?.toString(),
          percentage: item.Percentage?.toString(),
          allowanceType: item.Allowance_Type,
          allowanceWages: item.AllowanceWages?.toString(),
        });
      });
      this.selectedFBPComponents.forEach((item) => {
        allowances.push({
          allowanceTypeId: item.Allowance_Type_Id,
          amount: item.Amount?.toString(),
          percentage: item.Percentage?.toString(),
          allowanceType: item.Allowance_Type,
          allowanceWages: item.AllowanceWages?.toString(),
          fbpMaxDeclaration: item.FBP_Max_Declaration?.toString(),
        });
      });
      this.selectedBonuses.forEach((item) => {
        allowances.push({
          allowanceTypeId: item.Allowance_Type_Id,
          amount: item.Amount?.toString(),
          percentage: item.Percentage?.toString(),
          allowanceType: item.Allowance_Type,
          allowanceWages: item.AllowanceWages?.toString(),
        });
      });
      this.selectedRetirals.forEach((item) => {
        retirals.push({
          retiralsId: item.Retirals_Id?.toString(),
          formId: item.Form_Id?.toString(),
          retiralsType: item.Retiral_Type,
          employeeSharePercentage: item.Employee_Share_Percentage?.toString(),
          employerSharePercentage: item.Employer_Share_Percentage?.toString(),
          employeeShareAmount: item.Employee_Share_Amount?.toString(),
          employerShareAmount: item.Employer_Share_Amount?.toString(),
          pfEmployeeContribution: item.PF_Employee_Contribution?.toString(),
          pfEmployerContribution: item.PF_Employer_Contribution?.toString(),
          employeeStatutoryLimit: item.Employee_Statutory_Limit?.toString(),
          employerStatutoryLimit: item.Employer_Statutory_Limit?.toString(),
          employeeRetiralWages: item.Employee_Retiral_Wages?.toString(),
          employerRetiralWages: item.Employer_Retiral_Wages?.toString(),
          eligibleForEPS: item.Eligible_for_EPS ? 1 : 0,
          adminCharge: item.Admin_Charge?.toString(),
          edliCharge: item.EDLI_Charge?.toString(),
        });
      });
      vm.$apollo
        .mutate({
          mutation: ADD_UPDATE_SALARY_DETAILS,
          client: "apolloClientF",
          variables: {
            formId: 206,
            accessFormId: 206,
            isEditMode: vm.isEdit,
            id: vm.selectedItem?.Template_Id,
            templateName: vm.salaryTemplateName,
            description: vm.salaryTemplateDescription,
            templateStatus: "Active",
            annualCTC: vm.annualCTC.toString(),
            annualGrossSalary: (vm.monthlyGrossSalary * 12).toString(),
            monthlyGrossSalary: vm.monthlyGrossSalary.toString(),
            allowance: allowances,
            retirals: retirals,
          },
          fetchPolicy: "no-cache",
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            message: vm.isEdit
              ? "Salary template updated successfully"
              : "Salary template added successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("add-update-success");
          vm.isLoading = false;
        })
        .catch((err) => {
          this.$store.dispatch("handleApiErrors", {
            error: err,
            action: vm.isEdit ? "update" : "add",
            form: "salary template",
            isListError: false,
          });
          vm.isLoading = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    openExpansionPanel(item) {
      if (item.PF_Employee_Contribution === "Restrict") {
        item.tempEmployeeType = "restrict";
      } else {
        item.tempEmployeeType = "actualWage";
      }
      if (item.PF_Employer_Contribution === "Restrict") {
        item.tempEmployerType = "restrict";
      } else {
        item.tempEmployerType = "actualWage";
        item.disableEmployeeContribution = true;
      }

      item.expansionPanel = [0];
    },
    openFBPExpansionPanel(item) {
      item.fbpMaxAmount = item.FBP_Max_Declaration;
      item.fbpPanels = [0];
    },
    async updateFBP(item) {
      let { valid } = await this.$refs[
        `fbp-${item.Allowance_Type_Id}`
      ][0].validate();
      if (valid) {
        item.FBP_Max_Declaration = item.fbpMaxAmount;
        item.fbpPanels = [];
      }
    },
    async updateRetirals(item) {
      let { valid } = await this.$refs[
        `retirals-${item.Retirals_Id || item.Form_Id}`
      ][0].validate();
      if (valid) {
        if (item.tempEmployeeType === "actualWage") {
          item.PF_Employee_Contribution = "Actual";
        } else {
          item.PF_Employee_Contribution = "Restrict";
        }
        if (item.tempEmployerType === "actualWage") {
          item.PF_Employer_Contribution = "Actual";
        } else {
          item.PF_Employer_Contribution = "Restrict";
        }
        item.expansionPanel = [];
        this.calculateSumOfComponetsMinusCTC();
      }
    },
    employerContributionChanged(item, event) {
      item.tempEmployerType = event;
      if (item.tempEmployerType === "actualWage") {
        item.tempEmployeeType = "actualWage";
        item.disableEmployeeContribution = true;
      } else {
        item.disableEmployeeContribution = false;
      }
    },
  },
};
</script>
<style scoped>
.add-update-content {
  max-height: calc(100vh - 130px) !important;
  overflow-y: scroll;
}

:deep(.v-field--prepended) {
  padding-inline-start: 0;
}

:deep(.v-field--appended) {
  padding-inline-end: 0;
}

.disabled-element {
  pointer-events: none;
}

.component-remove-btn {
  cursor: pointer;
  font-size: 12px;
}

:deep(.custom-input-position .v-field__input) {
  text-align: end !important;
}

.overlay-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: auto;
  position: fixed;
  bottom: 0;
  width: 100%;
}

.fixed-title {
  width: 100%;
}
</style>
