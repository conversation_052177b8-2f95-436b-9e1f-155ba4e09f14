<template>
  <div v-if="isLoading" class="mt-3">
    <v-skeleton-loader
      ref="skeleton1"
      type="table-heading"
      class="mx-auto"
    ></v-skeleton-loader>
    <div v-for="i in 3" :key="i" class="mt-4">
      <v-skeleton-loader
        ref="skeleton2"
        type="list-item-avatar"
        class="mx-auto"
      ></v-skeleton-loader>
    </div>
  </div>
  <AppAccessDenied v-else-if="isAccessDenied"></AppAccessDenied>
</template>

<script>
export default {
  name: "RecruitmentSetup",

  data() {
    return {
      isLoading: true,
      isAccessDenied: false,
    };
  },

  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },

  mounted() {
    let { isAnyOneFormHaveAccess, formAccess } =
      this.$store.getters.settingsRecruitmentFormAccess;
    if (isAnyOneFormHaveAccess) {
      for (let access in formAccess) {
        if (formAccess[access].havingAccess) {
          this.redirectToRelevantForm(formAccess[access]);
          break;
        }
      }
    } else {
      this.isAccessDenied = true;
      this.isLoading = false;
    }
  },

  methods: {
    redirectToRelevantForm(formData) {
      if (formData.isVue3) {
        this.$router.push("/settings/recruitment/" + formData.url);
      } else {
        window.location.href =
          this.baseUrl + "in/settings/recruitment/" + formData.url;
      }
    },
  },
};
</script>
