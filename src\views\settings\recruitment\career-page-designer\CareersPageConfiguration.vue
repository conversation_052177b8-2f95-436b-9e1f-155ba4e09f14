<template>
  <div>
    <div v-if="mainTabs?.length > 0">
      <AppTopBarTab
        :center-tab="true"
        :tabs-list="mainTabs"
        :current-tab="currentTabItem"
        @tab-clicked="onTabChange($event)"
      />
    </div>
    <v-container fluid class="pt-16 px-6 px-md-12">
      <v-window v-model="currentTabItem" v-if="formAccess">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            />
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              />
            </div>
          </div>

          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="this.$t('common.retry')"
            @button-click="loadConfiguration()"
          />
          <div v-else>
            <v-row>
              <v-col cols="12">
                <v-card class="rounded-lg" elevation="2">
                  <v-card-title class="pa-4 pa-md-6">
                    <v-row align="center" justify="space-between" no-gutters>
                      <v-col cols="12" sm="auto" class="mb-3 mb-sm-0">
                        <div class="d-flex align-center">
                          <v-icon class="mr-2 mr-md-3" color="primary"
                            >fas fa-briefcase</v-icon
                          >
                          <div
                            class="text-h6 text-md-h5 font-weight-bold text-primary"
                          >
                            {{ landedFormName }}
                          </div>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="auto">
                        <div
                          v-if="formAccess.update"
                          class="d-flex align-center justify-end justify-sm-start"
                        >
                          <v-btn
                            variant="outlined"
                            rounded="lg"
                            class="mr-2 mr-md-3"
                            :size="isMobileView ? 'small' : 'default'"
                            @click="resetForm"
                            :disabled="isLoading"
                          >
                            <v-icon class="mr-1" size="small"
                              >fas fa-undo</v-icon
                            >
                            <!-- Reset - i18n: common.reset -->
                            {{ this.$t("common.reset") }}
                          </v-btn>
                          <v-btn
                            v-if="isFormDirty"
                            variant="elevated"
                            color="primary"
                            rounded="lg"
                            :size="isMobileView ? 'small' : 'default'"
                            @click="saveConfiguration"
                            :loading="isLoading"
                          >
                            <!-- Save - i18n: common.save -->
                            {{ this.$t("common.save") }}
                          </v-btn>
                          <v-tooltip
                            v-else
                            :text="this.$t('settings.noChangesToUpdate')"
                            location="top"
                          >
                            <template v-slot:activator="{ props }">
                              <v-btn
                                v-bind="props"
                                color="primary"
                                variant="elevated"
                                rounded="lg"
                                class="cursor-not-allow primary"
                              >
                                <!-- Save - i18n: common.save -->
                                {{ this.$t("common.save") }}
                              </v-btn>
                            </template>
                          </v-tooltip>
                        </div>
                      </v-col>
                    </v-row>
                  </v-card-title>

                  <v-divider></v-divider>

                  <v-card-text class="pa-0">
                    <v-row no-gutters>
                      <!-- Left Panel - Configuration Form -->
                      <v-col cols="12" md="6" lg="5" class="border-e-md">
                        <div
                          class="pa-4 pa-md-6 overflow-y-auto"
                          style="max-height: 100vh"
                        >
                          <v-form ref="careersConfigForm" v-model="isFormValid">
                            <!-- Brand Assets Section -->
                            <BrandAssetsSection
                              v-model:companyLogo="formData.companyLogo"
                              v-model:careerLogoPath="formData.careerLogoPath"
                              v-model:faviconFilename="formData.faviconFilename"
                              v-model:careerBannerImage="
                                formData.careerBannerImage
                              "
                              :is-loading="isLoading"
                              @update-assets="onAssetsUpdate"
                            />

                            <v-divider class="my-6"></v-divider>

                            <!-- Content Customization Section -->
                            <ContentCustomizationSection
                              v-model:bannerHeading="formData.bannerHeading"
                              v-model:bannerText="formData.bannerText"
                              v-model:textHorizontalPosition="
                                formData.textHorizontalPosition
                              "
                              v-model:textVerticalPosition="
                                formData.textVerticalPosition
                              "
                              :is-loading="isLoading"
                              @form-change="onChangeFields"
                            />

                            <v-divider class="my-6"></v-divider>

                            <!-- Typography Controls Section -->
                            <TypographyControlsSection
                              v-model:headlineFontFamily="
                                formData.headlineFontFamily
                              "
                              v-model:headingFontSize="formData.headingFontSize"
                              v-model:subTextFontSize="formData.subTextFontSize"
                              v-model:headlineFontColor="
                                formData.headlineFontColor
                              "
                              :is-loading="isLoading"
                              :google-fonts-list="googleFontsList"
                              :google-fonts-loaded="googleFontsLoaded"
                              @form-change="onChangeFields"
                            />

                            <v-divider class="my-6"></v-divider>

                            <!-- Layout and Styling Section -->
                            <LayoutStylingSection
                              v-model:primaryColor="formData.primaryColor"
                              v-model:secondaryColor="formData.secondaryColor"
                              v-model:hoverColor="formData.hoverColor"
                              v-model:tableHeaderColor="
                                formData.tableHeaderColor
                              "
                              v-model:tableHeaderTextColor="
                                formData.tableHeaderTextColor
                              "
                              v-model:bannerOpacity="formData.bannerOpacity"
                              :is-loading="isLoading"
                              @form-change="onChangeFields"
                            />
                          </v-form>
                        </div>
                      </v-col>

                      <!-- Right Panel - Live Preview -->
                      <v-col cols="12" md="6" lg="7">
                        <div class="pa-4 pa-md-6">
                          <div
                            class="d-flex align-center justify-space-between mb-4"
                          >
                            <div class="text-h6 font-weight-bold text-primary">
                              <!-- Live Preview - i18n: settings.careerPage.livePreview -->
                              {{ this.$t("settings.careerPage.livePreview") }}
                            </div>
                            <v-btn
                              variant="outlined"
                              size="small"
                              color="primary"
                              rounded="lg"
                              @click="openPreviewInNewTab"
                            >
                              <v-icon class="mr-1" size="small"
                                >fas fa-external-link-alt</v-icon
                              >
                              <!-- View Openings - i18n: settings.careerPage.viewOpenings -->
                              {{ this.$t("settings.careerPage.viewOpenings") }}
                            </v-btn>
                          </div>

                          <!-- Preview Container -->
                          <div
                            class="border rounded-lg overflow-hidden bg-grey-lighten-4 d-flex align-center justify-center"
                            style="min-height: 400px"
                          >
                            <CareerPreview
                              :form-data="formData"
                              :preview-dimensions="previewDimensions"
                              :google-fonts-list="googleFontsList"
                            />
                          </div>

                          <!-- Preview Controls -->
                          <div class="d-flex align-center justify-center mt-4">
                            <v-btn-toggle
                              v-model="previewMode"
                              variant="outlined"
                              color="primary"
                              mandatory
                            >
                              <v-btn value="desktop" size="small">
                                <v-icon>fas fa-desktop</v-icon>
                              </v-btn>
                              <v-btn value="tablet" size="small">
                                <v-icon>fas fa-tablet-alt</v-icon>
                              </v-btn>
                              <v-btn value="mobile" size="small">
                                <v-icon>fas fa-mobile-alt</v-icon>
                              </v-btn>
                            </v-btn-toggle>
                          </div>
                        </div>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <AppLoading v-if="isLoading" />
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
import Config from "@/config";
import {
  RETRIEVE_GENERAL_SETTINGS,
  ADD_UPDATE_GENERAL_SETTINGS,
} from "@/graphql/settings/careerConfigurationQueries.js";

// Async components for better performance
const BrandAssetsSection = defineAsyncComponent(() =>
  import("./components/BrandAssetsSection.vue")
);
const ContentCustomizationSection = defineAsyncComponent(() =>
  import("./components/ContentCustomizationSection.vue")
);
const TypographyControlsSection = defineAsyncComponent(() =>
  import("./components/TypographyControlsSection.vue")
);
const LayoutStylingSection = defineAsyncComponent(() =>
  import("./components/LayoutStylingSection.vue")
);
const CareerPreview = defineAsyncComponent(() =>
  import("./components/CareerPreview.vue")
);

export default {
  name: "CareersPageConfiguration",
  components: {
    BrandAssetsSection,
    ContentCustomizationSection,
    TypographyControlsSection,
    LayoutStylingSection,
    CareerPreview,
  },
  data() {
    return {
      currentTabItem: "",
      isErrorInList: false,
      errorContent: "",
      listLoading: false,
      isLoading: false,
      isFormValid: false,
      isFormDirty: false,
      previewMode: "desktop",
      originalFormData: null,
      // Google Fonts data
      googleFontsList: [],
      googleFontsLoaded: false,
      loadedFonts: new Set(), // Track loaded fonts to avoid duplicates
      formData: {
        // API fields - only Career Page Designer relevant fields
        generalSettingId: null,
        pageTitle: null,
        useCompanyLogoAsProductLogo: null,
        companyLogo: null,
        careerLogoPath: null,
        faviconFilename: null,
        primaryColor: "#1C277D",
        secondaryColor: "#F1F5F9",
        hoverColor: "#0F1B5C",
        tableHeaderColor: "#E2E8F0",
        tableHeaderTextColor: "#000000",
        careerBannerImage: null,
        careerHeadlineText: "Career Vacancies",
        careerSubHeadlineText:
          "With our unique culture and opportunities, our organization is a place where you can grow. Wherever you are in your career, we help you to make a difference every day.",
        careerTextHorizontalPosition: "center",
        careerTextVerticalPosition: "middle",
        careerBannerOpacity: 30,
        careerHeadlineFontFamily: '"Roboto", sans-serif',
        careerHeadingFontSize: "48px",
        careerHeadlineFontColor: "#FFFFFF",
        careerSubHeadlineFontFamily: '"Roboto", sans-serif',
        careerSubHeadlineFontSize: "18px",
        careerSubHeadlineFontColor: "#FFFFFF",

        // Legacy fields for backward compatibility with components
        bannerHeading: "Career Vacancies",
        bannerText:
          "With our unique culture and opportunities, our organization is a place where you can grow. Wherever you are in your career, we help you to make a difference every day.",
        textHorizontalPosition: "center",
        textVerticalPosition: "middle",
        headlineFontFamily: '"Roboto", sans-serif',
        headingFontSize: 48,
        subTextFontSize: 18,
        headlineFontColor: "#FFFFFF",
        bannerOpacity: 30,
      },
    };
  },
  computed: {
    previewDimensions() {
      const dimensions = {
        desktop: { width: "100%", height: "600px" },
        tablet: { width: "768px", height: "500px" },
        mobile: { width: "375px", height: "400px" },
      };
      return dimensions[this.previewMode] || dimensions.desktop;
    },
    settingsRecruitmentFormAccess() {
      return this.$store.getters.settingsRecruitmentFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.settingsRecruitmentFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    landedFormName() {
      return this.accessRights("371")?.customFormName || "Career Page Designer";
    },
    isMobileView() {
      return this.$store?.state?.isMobileWindowSize || false;
    },
    formAccess() {
      try {
        const formAccessRights = this.accessRights("371");
        return formAccessRights?.accessRights?.view &&
          formAccessRights?.accessRights?.admin === "admin"
          ? formAccessRights.accessRights
          : false;
      } catch (error) {
        return false;
      }
    },
    accessRights() {
      return this.$store?.getters?.formIdBasedAccessRights || (() => false);
    },
    domainName() {
      return this.$store?.getters?.domain || "";
    },
    orgCode() {
      try {
        const org_code = localStorage.getItem("orgCode");
        return org_code || this.$store?.getters?.orgCode || "";
      } catch {
        return "";
      }
    },
    baseUrl() {
      return this.$store?.getters?.baseUrl || "";
    },
  },
  watch: {
    // Watch legacy fields and sync with API fields
    "formData.bannerHeading"(newVal) {
      this.formData.careerHeadlineText = newVal;
    },
    "formData.bannerText"(newVal) {
      this.formData.careerSubHeadlineText = newVal;
    },
    "formData.textHorizontalPosition"(newVal) {
      this.formData.careerTextHorizontalPosition = newVal;
    },
    "formData.textVerticalPosition"(newVal) {
      this.formData.careerTextVerticalPosition = newVal;
    },
    "formData.headlineFontFamily"(newVal) {
      // newVal is now in CSS format directly from CustomSelect
      this.formData.careerHeadlineFontFamily = newVal;
      this.formData.careerSubHeadlineFontFamily = newVal;

      // Dynamically load the selected Google Font
      if (newVal) {
        this.loadGoogleFont(newVal);
      }
    },
    "formData.headingFontSize"(newVal) {
      this.formData.careerHeadingFontSize = `${newVal}px`;
    },
    "formData.subTextFontSize"(newVal) {
      this.formData.careerSubHeadlineFontSize = `${newVal}px`;
    },
    "formData.headlineFontColor"(newVal) {
      this.formData.careerHeadlineFontColor = newVal;
      this.formData.careerSubHeadlineFontColor = newVal;
    },
    "formData.bannerOpacity"(newVal) {
      this.formData.careerBannerOpacity = newVal;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    this.loadConfiguration();
    this.loadGoogleFonts();
  },
  methods: {
    // Custom function to extract numbers from font size strings (e.g., "48px" -> 48)
    extractNumberFromString(inputString) {
      if (!inputString) return "";

      // Handle string inputs like "48px", "24pt", "16rem", etc.
      const numberMatches = inputString.toString().match(/(\d+(?:\.\d+)?)/);
      if (numberMatches && numberMatches.length > 0) {
        const extractedNumber = parseFloat(numberMatches[0]);
        return Math.round(extractedNumber); // Round to nearest integer for font sizes
      } else {
        return "";
      }
    },
    async loadGoogleFonts() {
      this.googleFontsLoaded = true;

      // Try live Google Fonts API first
      const response = await this.loadGoogleFontsFromAPI();

      if (response && response.items && response.items.length > 0) {
        // Transform API response to match dropdown format with CSS-ready values
        const transformedFonts = response.items.map((font) => ({
          title: `${font.family} (${
            font.category === "sans-serif" ? "Sans-serif" : "Serif"
          })`,
          value: font.family.toLowerCase().replace(/\s+/g, "-"), // Keep for backward compatibility
          cssFormat: `"${font.family}", ${
            font.category === "serif" ? "serif" : "sans-serif"
          }`, // CSS-ready format
          family: font.family,
          category: font.category,
        }));

        // Add GT Walsheim at the top of the list
        const gtWalsheimFont = {
          title: "GT Walsheim (Sans-serif)",
          value: "gt-walsheim",
          cssFormat: '"GT Walsheim", sans-serif',
          family: "GT Walsheim",
          category: "sans-serif",
        };

        this.googleFontsList = [gtWalsheimFont, ...transformedFonts];

        // Load initial font if one is already selected
        if (this.formData.headlineFontFamily) {
          this.loadGoogleFont(this.formData.headlineFontFamily);
        }
        this.googleFontsLoaded = false;
        return;
      }

      // Final fallback to basic system fonts
      this.googleFontsList = this.getFallbackFonts();
      this.googleFontsLoaded = false;
    },

    // Load Google Fonts from live API
    async loadGoogleFontsFromAPI() {
      const API_KEY = Config.googleFontsAPIKey;
      const API_URL = `${Config.googleFontsAPIUrl}?sort=popularity&key=${API_KEY}`;

      try {
        const response = await fetch(API_URL, {
          method: "GET",
          headers: {
            Accept: "application/json",
          },
        });

        if (!response.ok) {
          return null;
        }

        const data = await response.json();

        if (data.error) {
          return null;
        }

        return data;
      } catch {
        return null;
      }
    },

    // Get fallback fonts if API fails
    getFallbackFonts() {
      return [
        {
          title: "GT Walsheim (Sans-serif)",
          value: "gt-walsheim",
          cssFormat: '"GT Walsheim", sans-serif',
          family: "GT Walsheim",
          category: "sans-serif",
        },
        {
          title: "Roboto (Sans-serif)",
          value: "roboto",
          cssFormat: '"Roboto", sans-serif',
          family: "Roboto",
          category: "sans-serif",
        },
        {
          title: "Inter (Sans-serif)",
          value: "inter",
          cssFormat: '"Inter", sans-serif',
          family: "Inter",
          category: "sans-serif",
        },
        {
          title: "Open Sans (Sans-serif)",
          value: "open-sans",
          cssFormat: '"Open Sans", sans-serif',
          family: "Open Sans",
          category: "sans-serif",
        },
        {
          title: "Lato (Sans-serif)",
          value: "lato",
          cssFormat: '"Lato", sans-serif',
          family: "Lato",
          category: "sans-serif",
        },
        {
          title: "Montserrat (Sans-serif)",
          value: "montserrat",
          cssFormat: '"Montserrat", sans-serif',
          family: "Montserrat",
          category: "sans-serif",
        },
        {
          title: "Playfair Display (Serif)",
          value: "playfair-display",
          cssFormat: '"Playfair Display", serif',
          family: "Playfair Display",
          category: "serif",
        },
        {
          title: "Merriweather (Serif)",
          value: "merriweather",
          cssFormat: '"Merriweather", serif',
          family: "Merriweather",
          category: "serif",
        },
        {
          title: "Georgia (Serif)",
          value: "georgia",
          cssFormat: '"Georgia", serif',
          family: "Georgia",
          category: "serif",
        },
      ];
    },

    // Dynamically load Google Font when font family changes
    loadGoogleFont(cssFont) {
      if (!cssFont) return;

      // Extract font name from CSS format like '"Lilita One", sans-serif'
      const fontMatch = cssFont.match(/^"([^"]+)"/);
      if (!fontMatch) return;

      const fontName = fontMatch[1];

      // Check if font is already loaded
      if (this.loadedFonts.has(fontName)) {
        return;
      }

      // Skip system fonts and local fonts that don't need loading
      const systemFonts = [
        "Arial",
        "Helvetica",
        "Times New Roman",
        "Georgia",
        "Verdana",
        "GT Walsheim", // Local commercial font
      ];
      if (systemFonts.includes(fontName)) return;

      // Create and inject Google Fonts CSS link
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = `https://fonts.googleapis.com/css2?family=${fontName.replace(
        /\s+/g,
        "+"
      )}:wght@100;200;300;400;500;600;700;800;900&display=swap`;
      link.setAttribute("data-font-family", fontName);

      // Add to document head
      document.head.appendChild(link);

      // Track that this font has been loaded
      this.loadedFonts.add(fontName);
    },

    loadConfiguration() {
      if (this.listLoading) return;
      this.listLoading = true;
      this.isErrorInList = false;
      this.errorContent = "";
      this.$apollo
        .query({
          query: RETRIEVE_GENERAL_SETTINGS,
          client: "apolloClientI",
          variables: {
            formId: 371, // Career Page Designer form ID
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response?.data?.retrieveGeneralSettings?.generalSettings) {
            const settings =
              response.data.retrieveGeneralSettings.generalSettings;

            if (settings) {
              // Map API response to form data - only Career Page Designer relevant fields
              this.formData = {
                generalSettingId: settings.General_Setting_Id,
                pageTitle: settings.Page_Title,
                useCompanyLogoAsProductLogo:
                  settings.Use_Company_Logo_As_Product_Logo,

                // File upload fields with proper prefill and backward compatibility
                companyLogo: this.normalizeFilename(
                  settings.Company_Logo,
                  "companyLogo"
                ),
                faviconFilename: this.normalizeFilename(
                  settings.Favicon_Filename,
                  "favicon"
                ),
                careerBannerImage: this.normalizeFilename(
                  settings.Career_Banner_Image,
                  "bannerImage"
                ),
                careerLogoPath: this.normalizeFilename(
                  settings.Career_Logo_Path,
                  "careerLogo"
                ),

                // Color scheme fields
                primaryColor: settings.Primary_Color || "#1C277D",
                secondaryColor: settings.Secondary_Color || "#F1F5F9",
                hoverColor: settings.Hover_Color || "#0F1B5C",
                tableHeaderColor: settings.Table_Header_Color || "#E2E8F0",
                tableHeaderTextColor:
                  settings.Table_Header_Text_Color || "#000000",

                // Content customization fields
                careerHeadlineText: settings.Career_Headline_Text,
                careerSubHeadlineText: settings.Career_Sub_Headline_Text,

                // Banner positioning fields
                careerTextHorizontalPosition:
                  settings.Career_Text_Horizontal_Position || "center",
                careerTextVerticalPosition:
                  settings.Career_Text_Vertical_Position || "middle",
                careerBannerOpacity: settings.Career_Banner_Opacity || 30,

                // Typography fields - store CSS-ready format directly
                careerHeadlineFontFamily:
                  settings.Career_Headline_Font_Family ||
                  '"Roboto", sans-serif',
                careerHeadingFontSize:
                  settings.Career_Heading_Font_Size || "48px",
                careerHeadlineFontColor:
                  settings.Career_Headline_Font_Color || "#FFFFFF",
                careerSubHeadlineFontFamily:
                  settings.Career_Sub_Headline_Font_Family ||
                  '"Roboto", sans-serif',
                careerSubHeadlineFontSize:
                  settings.Career_Sub_Headline_Font_Size || "18px",
                careerSubHeadlineFontColor:
                  settings.Career_Sub_Headline_Font_Color || "#FFFFFF",

                // Legacy fields for backward compatibility
                bannerHeading: settings.Career_Headline_Text,
                bannerText: settings.Career_Sub_Headline_Text,
                textHorizontalPosition:
                  settings.Career_Text_Horizontal_Position || "center",
                textVerticalPosition:
                  settings.Career_Text_Vertical_Position || "middle",
                headlineFontFamily:
                  settings.Career_Headline_Font_Family ||
                  '"Roboto", sans-serif',
                headingFontSize: this.extractNumberFromString(
                  settings.Career_Heading_Font_Size || "48px"
                ),
                subTextFontSize: this.extractNumberFromString(
                  settings.Career_Sub_Headline_Font_Size || "18px"
                ),
                headlineFontColor:
                  settings.Career_Headline_Font_Color || "#FFFFFF",
                bannerOpacity: settings.Career_Banner_Opacity || 30,
              };
            } else {
              // No existing settings, use defaults
              this.formData = { ...this.formData };
            }

            this.originalFormData = { ...this.formData };
          }
          this.listLoading = false;
        })
        .catch((error) => {
          this.listLoading = false;
          this.handleListError(error);
        });
    },
    handleListError(error) {
      this.$store
        .dispatch("handleApiErrors", {
          error: error,
          action: this.$t("common.retrieving") || "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        })
        .catch(() => {
          // Fallback error handling
          this.errorContent =
            "Failed to load Career Page Configuration. Please try again.";
          this.isErrorInList = true;
        });
    },

    async saveConfiguration() {
      // Prevent duplicate save operations
      if (this.isLoading) {
        return;
      }
      if (!(await this.validateForm())) {
        return;
      }

      this.isLoading = true;
      try {
        // Upload assets first if needed
        await this.uploadAssets();

        // Sync legacy fields with API fields before saving
        this.syncFormData();
        let bannerImage, companyLogo, carrerLogo;
        if (this.formData.careerBannerImage) {
          const fileExtension = this.formData.careerBannerImage
            ?.split(".")
            .pop();
          bannerImage = `careerBanner.${fileExtension}`;
        } else bannerImage = null;
        if (this.formData.companyLogo) {
          const fileExtension = this.formData.companyLogo?.split(".").pop();
          if (this.formData.companyLogo.includes(this.orgCode))
            companyLogo = this.formData.companyLogo;
          else companyLogo = `company.${fileExtension}`;
        } else {
          companyLogo = null;
        }
        if (this.formData.careerLogoPath) {
          const fileExtension = this.formData.careerLogoPath?.split(".").pop();
          carrerLogo = `careerLogo.${fileExtension}`;
        } else {
          carrerLogo = null;
        }

        const response = await this.$apollo.mutate({
          mutation: ADD_UPDATE_GENERAL_SETTINGS,
          variables: {
            generalSettingId: this.formData.generalSettingId,
            pageTitle: this.formData.pageTitle, // Add missing pageTitle field
            faviconFilename: this.formData.faviconFilename
              ? "favicon.ico"
              : null,
            companyLogo: companyLogo,
            useCompanyLogoAsProductLogo:
              this.formData.useCompanyLogoAsProductLogo,
            careerLogoPath: carrerLogo,
            primaryColor: this.formData.primaryColor,
            secondaryColor: this.formData.secondaryColor,
            hoverColor: this.formData.hoverColor,
            tableHeaderColor: this.formData.tableHeaderColor,
            tableHeaderTextColor: this.formData.tableHeaderTextColor,
            careerBannerImage: bannerImage,
            careerHeadlineText: this.formData.careerHeadlineText,
            careerSubHeadlineText: this.formData.careerSubHeadlineText,
            careerTextHorizontalPosition:
              this.formData.careerTextHorizontalPosition,
            careerTextVerticalPosition:
              this.formData.careerTextVerticalPosition,
            careerBannerOpacity: this.formData.careerBannerOpacity,
            careerHeadlineFontFamily: this.formData.headlineFontFamily,
            careerHeadingFontSize: this.formData.careerHeadingFontSize,
            careerHeadlineFontColor: this.formData.careerHeadlineFontColor,
            careerSubHeadlineFontFamily: this.formData.headlineFontFamily,
            careerSubHeadlineFontSize: this.formData.careerSubHeadlineFontSize,
            careerSubHeadlineFontColor:
              this.formData.careerSubHeadlineFontColor,
            formId: 371, // Career Page Designer form ID
          },
          client: "apolloClientJ",
        });

        if (!response?.data?.addUpdateGeneralSettings?.errorCode) {
          this.isFormDirty = false;

          this.showAlert({
            isOpen: true,
            message: this.$t(
              "settings.careerPage.configurationSavedSuccessfully"
            ),
            type: "success",
          });
          const colors = {
            Primary_Color: this.formData.primaryColor,
            Secondary_Color: this.formData.secondaryColor,
            Hover_Color: this.formData.hoverColor,
            Table_Header_Color: this.formData.tableHeaderColor,
            Table_Header_Text_Color: this.formData.tableHeaderTextColor,
          };
          localStorage.setItem("brand_color", JSON.stringify(colors));
          localStorage.setItem(
            "faviconFilename",
            this.formData.faviconFilename ? "favicon.ico" : ""
          );
          window.location.reload();
        } else {
          this.handleAppUpdateErrors(
            response.data?.addUpdateGeneralSettings?.errorCode || ""
          );
        }
      } catch (error) {
        this.handleAppUpdateErrors(error);
      } finally {
        this.isLoading = false;
      }
    },
    handleAppUpdateErrors(error) {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error,
          action: this.$t("common.updating") || "updating",
          form: this.landedFormName || "career page configuration",
          isListError: false,
        })
        .catch((handleError) => {
          // Fallback error handling
          this.showAlert({
            isOpen: true,
            message:
              handleError || "Failed to save configuration. Please try again.",
            type: "warning",
          });
        });
    },

    syncFormData() {
      // Sync legacy fields with API fields before saving
      this.formData.careerHeadlineText = this.formData.bannerHeading;
      this.formData.careerSubHeadlineText = this.formData.bannerText;
      this.formData.careerTextHorizontalPosition =
        this.formData.textHorizontalPosition;
      this.formData.careerTextVerticalPosition =
        this.formData.textVerticalPosition;
      this.formData.careerHeadlineFontFamily = this.formData.headlineFontFamily;
      this.formData.careerHeadingFontSize = `${this.formData.headingFontSize}px`;
      this.formData.careerSubHeadlineFontSize = `${this.formData.subTextFontSize}px`;
      this.formData.careerHeadlineFontColor = this.formData.headlineFontColor;
      this.formData.careerSubHeadlineFontColor =
        this.formData.headlineFontColor; // Assuming same color
      this.formData.careerBannerOpacity = this.formData.bannerOpacity;
    },

    resetForm() {
      this.isFormDirty = false;

      // Reset to original API values (not hardcoded defaults)
      if (this.originalFormData) {
        this.formData = { ...this.originalFormData };
      } else {
        // Fallback to defaults if no original data exists
        this.formData = {
          // API fields
          generalSettingId: null,
          faviconFilename: null,
          companyLogo: null,
          careerLogoPath: null,
          primaryColor: "#1C277D",
          secondaryColor: "#F1F5F9",
          hoverColor: "#0F1B5C",
          tableHeaderColor: "#E2E8F0",
          tableHeaderTextColor: "#000000",
          careerBannerImage: null,
          careerHeadlineText: "Career Vacancies",
          careerSubHeadlineText: "Career Portals",
          careerTextHorizontalPosition: "center",
          careerTextVerticalPosition: "middle",
          careerBannerOpacity: 30,
          careerHeadlineFontFamily: '"Roboto", sans-serif',
          careerHeadingFontSize: "48px",
          careerHeadlineFontColor: "#FFFFFF",
          careerSubHeadlineFontFamily: '"Roboto", sans-serif',
          careerSubHeadlineFontSize: "18px",
          careerSubHeadlineFontColor: "#FFFFFF",

          // Legacy fields for backward compatibility
          bannerHeading: "",
          bannerText: "",
          textHorizontalPosition: "center",
          textVerticalPosition: "middle",
          headlineFontFamily: '"Roboto", sans-serif',
          headingFontSize: 48,
          subTextFontSize: 18,
          headlineFontColor: "#FFFFFF",
          bannerOpacity: 30,
        };
      }

      // Reset form validation
      if (this.$refs.careersConfigForm) {
        this.$refs.careersConfigForm.resetValidation();
      }
    },

    onAssetsUpdate(assets) {
      this.isFormDirty = true;

      // Handle asset updates from child component
      Object.assign(this.formData, assets);

      // Sync with API fields - use consistent variable names
      if (assets.faviconFilename !== undefined) {
        this.formData.faviconFilename = assets.faviconFilename;
      }
      if (assets.careerBannerImage !== undefined) {
        this.formData.careerBannerImage = assets.careerBannerImage;
      }
      if (assets.companyLogo !== undefined) {
        this.formData.companyLogo = assets.companyLogo;
      }
      if (assets.careerLogoPath !== undefined) {
        this.formData.careerLogoPath = assets.careerLogoPath;
      }
    },

    openPreviewInNewTab() {
      // Open career page in new tab
      const url = "v3/careers";
      window.open(this.baseUrl + url, "_blank");
    },
    async validateForm() {
      // Use v-form ref validation instead of manual validation
      const { valid } = await this.$refs.careersConfigForm.validate();

      if (!valid) {
        return false;
      }

      return true;
    },

    async uploadAssets() {
      try {
        // Upload company logo if it's a file
        if (
          typeof File !== "undefined" &&
          this.formData.companyLogo instanceof File
        ) {
          const fileName = await this.uploadFileToS3(
            this.formData.companyLogo,
            "companyLogo"
          );
          this.formData.companyLogo = fileName;
        }

        // Upload career logo if it's a file
        if (
          typeof File !== "undefined" &&
          this.formData.careerLogoPath instanceof File
        ) {
          const fileName = await this.uploadFileToS3(
            this.formData.careerLogoPath,
            "careerLogo"
          );
          this.formData.careerLogoPath = fileName;
        }

        // Upload favicon if it's a file
        if (
          typeof File !== "undefined" &&
          this.formData.faviconFilename instanceof File
        ) {
          const fileName = await this.uploadFileToS3(
            this.formData.faviconFilename,
            "favicon"
          );
          this.formData.faviconFilename = fileName;
        }

        // Upload banner image if it's a file
        if (
          typeof File !== "undefined" &&
          this.formData.careerBannerImage instanceof File
        ) {
          const fileName = await this.uploadFileToS3(
            this.formData.careerBannerImage,
            "bannerImage"
          );
          this.formData.careerBannerImage = fileName;
        }
      } catch (error) {
        throw new Error(
          this.$t("settings.careerPage.failedToUploadAssets") +
            ": " +
            error.message
        );
      }
    },

    async uploadFileToS3(file, type) {
      if (!file || typeof File === "undefined" || !(file instanceof File)) {
        throw new Error("Invalid file provided for upload");
      }

      if (!this.orgCode || !this.domainName) {
        throw new Error("Organization code or domain name not available");
      }

      let fileUploadUrl;
      let standardizedFileName;

      try {
        // Get file extension with validation
        const fileExtension = file.name?.split(".").pop()?.toLowerCase();
        if (!fileExtension) {
          throw new Error("File extension could not be determined");
        }

        // Generate standardized filename and upload URL
        if (type === "companyLogo") {
          standardizedFileName = `company.${fileExtension}`;
          fileUploadUrl = `hrapp_upload/${this.orgCode}_tmp/logos/${standardizedFileName}`;
        } else if (type === "careerLogo") {
          standardizedFileName = `careerLogo.${fileExtension}`;
          fileUploadUrl = `${this.domainName}/${this.orgCode}/careerLogo/${standardizedFileName}`;
        } else if (type === "favicon") {
          standardizedFileName = "favicon.ico";
          fileUploadUrl = `${this.domainName}/${this.orgCode}/favicon/${standardizedFileName}`;
        } else if (type === "bannerImage") {
          standardizedFileName = `careerBanner.${fileExtension}`;
          fileUploadUrl = `${this.domainName}/${this.orgCode}/banner/${standardizedFileName}`;
        } else {
          throw new Error(`Unsupported file type: ${type}`);
        }

        await this.$store
          .dispatch("s3FileUploadRetrieveAction", {
            fileName: fileUploadUrl,
            action: "upload",
            type: "logo",
            fileContent: file,
          })
          .catch((error) => {
            throw error;
          });
        return standardizedFileName;
      } catch (error) {
        throw new Error(`Failed to upload ${type}: ${error.message}`);
      }
    },

    // Helper method to ensure backward compatibility with existing filenames
    normalizeFilename(filename, type) {
      if (!filename) return null;

      // If it's already a standardized filename, return as is
      if (type === "companyLogo" && filename.startsWith("company.")) {
        return filename;
      }
      if (type === "favicon" && filename === "favicon.ico") {
        return filename;
      }
      if (type === "bannerImage" && filename.startsWith("careerBanner.")) {
        return filename;
      }
      if (type === "careerLogo" && filename.startsWith("careerLogo.")) {
        return filename;
      }

      // For legacy filenames, return as is for backward compatibility
      // The system will gradually migrate to new patterns as files are re-uploaded
      return filename;
    },
    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.settingsRecruitmentFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/recruitment/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/recruitment/" + clickedForm.url;
        }
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    // Form change tracking
    onChangeFields() {
      this.isFormDirty = true;
    },
  },
};
</script>

<style scoped>
/* Border for configuration panel - responsive */
.border-e-md {
  border-right: 1px solid #e0e0e0;
}

@media (max-width: 960px) {
  .border-e-md {
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }
}

/* Ensure proper spacing on mobile */
:deep(.v-card-title) {
  flex-wrap: wrap;
}

:deep(.v-btn-toggle) {
  flex-wrap: wrap;
}

/* Custom scrollbar styling for configuration panel */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
