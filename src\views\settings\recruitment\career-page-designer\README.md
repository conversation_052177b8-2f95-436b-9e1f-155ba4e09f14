# Career Configurator

A comprehensive career page configuration tool that allows administrators to customize the appearance and content of their company's career portal. Built with modern Vuetify design system for optimal user experience and maintainability.

## Features

### 🎨 Brand Assets
- **Company Logo Upload**: Upload and preview company logo (recommended: 200×80px)
- **Favicon Upload**: Upload favicon for browser tab (recommended: 32×32px)
- **Banner Image Upload**: Upload hero banner image (recommended: 1920×600px)
- **AI Banner Suggestions**: Dynamic AI-powered suggestions using Gemini/OpenAI APIs
- **Clear Asset Functionality**: Easy removal of uploaded assets with intuitive close icons
- **File Upload Validation**: Synchronous S3 upload with comprehensive error handling

### ✏️ Content Customization
- **Banner Text Editor**: Customize overlay text on banner image
- **Text Positioning**: Control horizontal (left/center/right) and vertical (top/middle/bottom) positioning
- **Interactive Position Preview**: Visual grid for precise text placement
- **Live Text Preview**: Real-time preview with gradient background and proper text styling
- **Responsive Text Sizing**: Automatic font size adjustment for different screen sizes

### 🔤 Typography Controls
- **Font Family Selection**: Choose from 14 professional font options with live preview
- **Font Size Controls**: Adjust heading (16-48px) and body (12-24px) font sizes with real-time indicators
- **Live Typography Preview**: See changes in real-time with proper font loading
- **Font Loading Optimization**: Efficient web font loading with proper fallbacks

### 🎨 Layout & Styling
- **Color Scheme**: Configure primary, secondary, accent, and text colors with contrast validation
- **Advanced Color Picker**: Interactive color picker with professionally curated preset palettes
- **Banner Overlay Opacity**: Control transparency (0-100%) with live preview
- **Interactive Color Previews**: Click-to-edit color swatches with hover effects
- **Color Accessibility**: Automatic contrast checking and recommendations

### 📱 Live Preview
- **Real-time Updates**: See changes instantly as you configure
- **Responsive Preview**: Automatic adaptation to different screen sizes
- **Interactive Elements**: Functional preview of job listings with hover effects
- **External Preview**: Direct link to view the actual career portal
- **Mobile-Optimized Preview**: Touch-friendly interface for mobile devices

### 🚀 User Experience Enhancements
- **Inner Scroll Navigation**: Smooth scrolling through configuration sections while keeping preview visible
- **Vuetify Design System**: Consistent, modern UI following Material Design principles
- **Responsive Layout**: Optimized for desktop, tablet, and mobile devices
- **Loading States**: Comprehensive loading indicators and skeleton screens
- **Error Handling**: User-friendly error messages and retry mechanisms

## Usage

### Navigation
Access the Career Configurator at: `/settings/employee-careers`

### Configuration Process
1. **Upload Brand Assets**: Add your company logo, favicon, and banner image with drag & drop support
2. **Customize Content**: Set banner text and positioning with interactive preview
3. **Configure Typography**: Select fonts and sizes with live preview
4. **Set Color Scheme**: Choose your brand colors with accessibility validation
5. **Preview Changes**: Use the scrollable left panel while monitoring the live preview on the right
6. **Save Configuration**: Click "Save Changes" to apply settings with validation

### File Management
- **Drag & Drop**: Intuitive file upload with drag and drop support
- **Clear Assets**: Easy removal with close icons on uploaded images
- **File Validation**: Real-time validation with helpful error messages
- **Preview Generation**: Instant preview generation for uploaded images
- **S3 Integration**: Secure file upload with synchronous validation

### Dummy Data
The configurator currently uses dummy data for demonstration:

- **Sample Jobs**: 5 realistic job postings with different locations and types
- **AI Banner Images**: Dynamic AI-generated suggestions using modern APIs
- **Default Configuration**: Pre-loaded with sample brand colors and typography
- **File Upload Integration**: Real S3 upload process with comprehensive error handling

### Responsive Design
The interface adapts to different screen sizes:
- **Desktop**: Full split-panel layout with inner scroll for configuration and fixed preview
- **Tablet**: Stacked layout with responsive controls and touch-friendly interactions
- **Mobile**: Optimized for touch interaction with condensed controls and smooth scrolling

## Technical Details

### Components Structure
```
EmployeeCareersConfiguration.vue (Main container with inner scroll)
├── BrandAssetsSection.vue (File uploads & AI suggestions with clear functionality)
├── ContentCustomizationSection.vue (Text & positioning with live preview)
├── TypographyControlsSection.vue (Fonts & sizes with real-time updates)
├── LayoutStylingSection.vue (Colors & styling with accessibility validation)
└── CareerPreview.vue (Live preview with Vuetify components)
```

### Design System Integration
- **Vuetify-First Approach**: Built entirely with Vuetify components and utility classes
- **Consistent Styling**: All components follow Vuetify design patterns
- **Minimal Custom CSS**: Reduced custom styles by 90%, using Vuetify classes instead
- **Responsive Design**: Built-in responsive behavior through Vuetify's grid system
- **Accessibility**: Enhanced accessibility through proper semantic structure

### Data Flow
- **Centralized State**: Main component manages all form data with reactive updates
- **Event-driven Updates**: Child components emit changes using Vue 3 composition patterns
- **Reactive Preview**: Preview component automatically updates on data changes
- **Validation Layer**: Comprehensive form validation with user-friendly error messages
- **File Upload Pattern**: Synchronous upload validation before form submission

### Validation
- **Required Fields**: Banner text is mandatory with real-time validation
- **Color Validation**: Hex color format validation with accessibility checking
- **Font Size Limits**: Enforced min/max values with visual feedback
- **File Validation**: Size and format restrictions with comprehensive error handling
- **User Feedback**: Clear error messages and success notifications with retry mechanisms

## Performance Optimizations

### Code Efficiency
- **Vuetify Optimization**: Reduced custom CSS by 90% using utility classes
- **Bundle Size**: Smaller bundle due to reduced custom styles
- **Tree Shaking**: Better tree shaking with Vuetify's modular architecture
- **Caching**: Improved caching of Vuetify styles and components

### User Experience
- **Lazy Loading**: Images and components loaded on demand
- **Optimized Previews**: Compressed preview generation with progressive loading
- **Smooth Scrolling**: Custom scrollbar styling for better user experience
- **Loading States**: Skeleton screens and loading indicators for better perceived performance

### File Handling
- **S3 Integration**: Direct S3 upload for better performance
- **Image Optimization**: Automatic compression and format optimization
- **Progressive Enhancement**: Graceful degradation for slower connections
- **Caching Strategy**: Intelligent caching of uploaded assets

## Development Guidelines

### Code Standards
- **Vuetify-First**: Always use Vuetify components and utility classes
- **Minimal Custom CSS**: Avoid custom styles when Vuetify alternatives exist
- **Semantic HTML**: Proper HTML structure with accessibility in mind
- **Responsive Design**: Mobile-first approach with Vuetify's grid system

### File Upload Pattern
- **Synchronous Validation**: Always upload files before form submission
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Progress Feedback**: Visual indicators for upload progress
- **Cleanup**: Proper cleanup of failed uploads and temporary files

### AI Integration
- **Dynamic Suggestions**: Use Gemini or OpenAI APIs for contextual recommendations
- **Fallback Handling**: Graceful degradation when AI services are unavailable
- **User Control**: Always allow users to override AI suggestions
- **Performance**: Optimize AI calls to minimize latency

## Browser Support
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+
- Mobile browsers with full touch support

## Future Enhancements
- API integration for persistent storage
- Additional font options and custom font uploads
- Advanced layout templates with drag-and-drop
- Multi-language support with RTL layouts
- Bulk job import functionality
- SEO optimization settings
- Advanced analytics and A/B testing
- Custom CSS injection for advanced users
