<template>
  <div>
    <div class="d-flex align-center mb-4">
      <v-progress-circular
        model-value="100"
        color="blue"
        :size="22"
        class="mr-2"
      />
      <!-- Brand Assets - i18n: settings.careerPage.brandAssets -->
      <div class="text-h6 font-weight-bold text-primary">
        {{ this.$t("settings.general.brandAssets") }}
      </div>
    </div>

    <!-- Company Logo -->
    <div class="mb-6">
      <v-row align="center">
        <v-col cols="12" sm="4">
          <div class="text-body-2 font-weight-medium">
            <!-- Company Logo - i18n: settings.careerPage.companyLogo -->
            {{ this.$t("settings.general.companyLogo") }}
            <v-tooltip
              :text="this.$t('settings.general.appliesAcrossApplication')"
              location="top"
              max-width="300px"
            >
              <template v-slot:activator="{ props }">
                <v-icon class="ml-1" v-bind="props" size="x-small" color="blue">
                  fas fa-info-circle
                </v-icon>
              </template>
            </v-tooltip>
          </div>
        </v-col>
        <v-col cols="12" sm="8">
          <v-row no-gutters align="center">
            <v-col cols="auto" class="mr-4">
              <v-card
                class="position-relative d-flex align-center justify-center"
                style="width: 80px; height: 40px"
                variant="outlined"
                color="grey-lighten-4"
              >
                <span v-if="companyLogoPreview">
                  <img
                    :src="companyLogoPreview"
                    :alt="this.$t('settings.general.companyLogo')"
                    class="w-100 h-100 rounded"
                    style="object-fit: cover"
                    loading="eager"
                  />
                </span>
                <div
                  v-else
                  class="d-flex flex-column align-center justify-center h-100"
                >
                  <v-icon color="grey-lighten-1" size="24">fas fa-image</v-icon>
                  <span class="text-caption text-grey-lighten-1 mt-1"
                    >200×80</span
                  >
                </div>
              </v-card>
            </v-col>
            <v-col class="d-flex justify-end">
              <v-file-input
                ref="companyLogoFileInput"
                v-model="companyLogoFile"
                prepend-icon=""
                clearable
                append-inner-icon="fas fa-paperclip"
                :label="this.$t('settings.general.companyLogo')"
                max-width="200px"
                accept="image/png,image/jpeg,image/jpg,image"
                variant="solo"
                density="compact"
                :rules="companyLogoValidationRules"
                hide-details="auto"
                @update:model-value="handleFileUpload('companyLogo', $event)"
              >
                <template v-slot:selection="{ fileNames }">
                  <span class="text-truncate">
                    {{
                      fileNames?.length > 1
                        ? this.$t("settings.careerPage.filesSelected", {
                            count: fileNames?.length,
                          })
                        : fileNames?.length === 1
                        ? fileNames[0]
                        : ""
                    }}
                  </span>
                </template>
              </v-file-input>
            </v-col>
          </v-row>
          <div class="text-caption text-grey mt-1">
            <!-- Recommended size hint - i18n: settings.careerPage.companyLogoHint -->
            {{ this.$t("settings.general.companyLogoHint") }}
          </div>
        </v-col>
      </v-row>
    </div>
    <!-- Career Page Logo -->
    <div class="mb-6">
      <v-row align="center">
        <v-col cols="12" sm="4">
          <div class="text-body-2 font-weight-medium">
            <!-- Career Page Logo - i18n: settings.careerPage.careerLogo -->
            {{ this.$t("settings.careerPage.careerLogo") }}
            <v-tooltip
              :text="this.$t('settings.careerPage.careerLogoTooltip')"
              location="top"
              max-width="300px"
            >
              <template v-slot:activator="{ props }">
                <v-icon class="ml-1" v-bind="props" size="x-small" color="blue">
                  fas fa-info-circle
                </v-icon>
              </template>
            </v-tooltip>
          </div>
        </v-col>
        <v-col cols="12" sm="8">
          <v-row no-gutters align="center">
            <v-col cols="auto" class="mr-4">
              <v-card
                class="position-relative d-flex align-center justify-center"
                style="width: 80px; height: 40px"
                variant="outlined"
                color="grey-lighten-4"
              >
                <span v-if="careerLogoPreview">
                  <img
                    :src="careerLogoPreview"
                    :alt="this.$t('settings.careerPage.careerLogo')"
                    class="w-100 h-100 rounded"
                    style="object-fit: cover"
                    loading="eager"
                  />
                </span>
                <div
                  v-else
                  class="d-flex flex-column align-center justify-center h-100"
                >
                  <v-icon color="grey-lighten-1" size="24">fas fa-image</v-icon>
                  <span class="text-caption text-grey-lighten-1 mt-1"
                    >200×80</span
                  >
                </div>
              </v-card>
            </v-col>
            <v-col class="d-flex justify-end">
              <v-file-input
                ref="careerLogoFileInput"
                v-model="careerLogoFile"
                prepend-icon=""
                clearable
                append-inner-icon="fas fa-paperclip"
                :label="this.$t('settings.careerPage.careerLogo')"
                max-width="200px"
                accept="image/png,image/jpeg,image/jpg,image"
                variant="solo"
                density="compact"
                :rules="careerLogoValidationRules"
                hide-details="auto"
                @update:model-value="handleFileUpload('careerLogoPath', $event)"
              >
                <template v-slot:selection="{ fileNames }">
                  <span class="text-truncate">
                    {{
                      fileNames?.length > 1
                        ? this.$t("settings.careerPage.filesSelected", {
                            count: fileNames?.length,
                          })
                        : fileNames?.length === 1
                        ? fileNames[0]
                        : ""
                    }}
                  </span>
                </template>
              </v-file-input>
            </v-col>
          </v-row>
          <div class="text-caption text-grey mt-1">
            <!-- Career logo hint - i18n: settings.careerPage.careerLogoHint -->
            {{ this.$t("settings.careerPage.careerLogoHint") }}
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Favicon -->
    <div class="mb-6">
      <v-row align="center">
        <v-col cols="12" sm="4">
          <div class="text-body-2 font-weight-medium">
            <!-- Favicon - i18n: settings.careerPage.favicon -->
            {{ this.$t("settings.general.favicon") }}
            <v-tooltip
              :text="this.$t('settings.general.appliesAcrossApplication')"
              location="top"
              max-width="300px"
            >
              <template v-slot:activator="{ props }">
                <v-icon class="ml-1" v-bind="props" size="x-small" color="blue">
                  fas fa-info-circle
                </v-icon>
              </template>
            </v-tooltip>
          </div>
        </v-col>
        <v-col cols="12" sm="8">
          <v-row no-gutters align="center">
            <v-col cols="auto" class="mr-4">
              <v-card
                class="position-relative d-flex align-center justify-center"
                style="width: 40px; height: 40px"
                variant="outlined"
                color="grey-lighten-4"
              >
                <span v-if="faviconPreview">
                  <img
                    :src="faviconPreview"
                    :alt="this.$t('settings.general.favicon')"
                    class="w-100 h-100 rounded"
                    style="object-fit: cover"
                    loading="eager"
                  />
                </span>
                <div
                  v-else
                  class="d-flex flex-column align-center justify-center h-100"
                >
                  <v-icon color="grey-lighten-1" size="16">fas fa-image</v-icon>
                  <span class="text-caption text-grey-lighten-1 mt-1"
                    >32×32</span
                  >
                </div>
              </v-card>
            </v-col>
            <v-col class="d-flex justify-end">
              <v-file-input
                ref="faviconFileInput"
                v-model="faviconFile"
                prepend-icon=""
                clearable
                append-inner-icon="fas fa-paperclip"
                :label="this.$t('settings.general.favicon')"
                accept="image/png,image/jpeg,image/jpg,image"
                max-width="200px"
                variant="solo"
                density="compact"
                :rules="faviconValidationRules"
                hide-details="auto"
                @update:model-value="
                  handleFileUpload('faviconFilename', $event)
                "
              >
                <template v-slot:selection="{ fileNames }">
                  <span class="text-truncate">
                    {{
                      fileNames?.length > 1
                        ? this.$t("settings.careerPage.filesSelected", {
                            count: fileNames?.length,
                          })
                        : fileNames?.length === 1
                        ? fileNames[0]
                        : ""
                    }}
                  </span>
                </template>
              </v-file-input>
            </v-col>
          </v-row>
          <div class="text-caption text-grey mt-1">
            <!-- Favicon hint - i18n: settings.careerPage.faviconHint -->
            {{ this.$t("settings.general.faviconHint") }}
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Banner Image -->
    <div class="mb-4">
      <v-row align="center">
        <v-col cols="12" sm="4">
          <!-- Banner Image - i18n: settings.careerPage.bannerImage -->
          <div class="text-body-2 font-weight-medium">
            {{ this.$t("settings.careerPage.bannerImage") }}
          </div>
        </v-col>
        <v-col cols="12" sm="8">
          <v-row no-gutters align="center">
            <v-col cols="auto" class="mr-4">
              <v-card
                class="position-relative d-flex align-center justify-center"
                style="width: 120px; height: 45px"
                variant="outlined"
                color="grey-lighten-4"
              >
                <span v-if="careerBannerImagePreview">
                  <img
                    :src="careerBannerImagePreview"
                    :alt="this.$t('settings.careerPage.bannerImage')"
                    class="w-100 h-100 rounded"
                    style="object-fit: cover"
                    loading="eager"
                  />
                </span>
                <div
                  v-else
                  class="d-flex flex-column align-center justify-center h-100"
                >
                  <v-icon color="grey-lighten-1" size="32">fas fa-image</v-icon>
                  <span class="text-caption text-grey-lighten-1 mt-1"
                    >1920×600</span
                  >
                </div>
              </v-card>
            </v-col>
            <v-col class="d-flex justify-end">
              <v-file-input
                ref="careerBannerImageFileInput"
                v-model="careerBannerImageFile"
                prepend-icon=""
                clearable
                append-inner-icon="fas fa-paperclip"
                :label="this.$t('settings.careerPage.bannerImage')"
                accept="image/png,image/jpeg,image/jpg,image"
                max-width="200px"
                variant="solo"
                density="compact"
                :rules="careerBannerImageValidationRules"
                hide-details="auto"
                @update:model-value="
                  handleFileUpload('careerBannerImage', $event)
                "
              >
                <template v-slot:selection="{ fileNames }">
                  <span class="text-truncate">
                    {{
                      fileNames?.length > 1
                        ? this.$t("settings.careerPage.filesSelected", {
                            count: fileNames?.length,
                          })
                        : fileNames?.length === 1
                        ? fileNames[0]
                        : ""
                    }}
                  </span>
                </template>
              </v-file-input>
            </v-col>
          </v-row>
          <div class="text-caption text-grey mt-1">
            <!-- Banner image hint - i18n: settings.careerPage.bannerImageHint -->
            {{ this.$t("settings.careerPage.bannerImageHint") }}
          </div>
          <!-- For Further Usage
          <v-btn
            variant="text"
            color="primary"
            size="small"
            @click="showAISuggestions = true"
            :disabled="isLoading"
          >
            <v-icon class="mr-1" size="small">fas fa-magic</v-icon>
            {{ this.$t("settings.careerPage.aiBannerSuggestions") }}
          </v-btn> -->
        </v-col>
      </v-row>
    </div>

    <!-- AI-Powered Banner Suggestions Dialog -->
    <v-dialog v-model="showAISuggestions" max-width="600" persistent>
      <v-card class="rounded-lg">
        <v-card-title
          class="d-flex align-center justify-space-between pa-6 pb-4"
        >
          <div class="d-flex align-center">
            <v-icon class="mr-3" color="primary" size="24">fas fa-magic</v-icon>
            <!-- AI-Powered Banner Suggestions - i18n: settings.careerPage.aiPoweredBannerSuggestions -->
            <span class="text-h6 font-weight-bold">{{
              this.$t("settings.careerPage.aiPoweredBannerSuggestions")
            }}</span>
          </div>
          <v-btn
            icon
            variant="text"
            color="primary"
            size="small"
            @click="closeAISuggestions"
          >
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="px-6 pb-6">
          <!-- How it works section -->
          <v-card class="mb-6" variant="outlined" color="grey-lighten-4">
            <v-card-text class="pa-4">
              <div class="d-flex align-start">
                <v-avatar class="mr-3 mt-1" size="24" color="primary">
                  <v-icon color="white" size="14">fas fa-question</v-icon>
                </v-avatar>
                <div>
                  <div
                    class="text-subtitle-2 font-weight-bold mb-2 text-primary"
                  >
                    <!-- How it works - i18n: settings.careerPage.howItWorks -->
                    {{ this.$t("settings.careerPage.howItWorks") }}
                  </div>
                  <div class="text-body-2 text-grey-darken-1">
                    <!-- AI description - i18n: settings.careerPage.aiDescription -->
                    {{ this.$t("settings.careerPage.aiDescription") }}
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>

          <!-- Company Branding -->
          <div class="mb-4">
            <!-- Company Branding - i18n: settings.careerPage.companyBranding -->
            <v-label class="text-subtitle-2 font-weight-bold mb-2 d-block">
              {{ this.$t("settings.careerPage.companyBranding") }}
            </v-label>
            <v-textarea
              v-model="companyBranding"
              :placeholder="
                this.$t('settings.careerPage.companyBrandingPlaceholder')
              "
              variant="outlined"
              rows="4"
              hide-details
              class="mb-1"
            ></v-textarea>
          </div>

          <!-- Industry Trends -->
          <div class="mb-6">
            <!-- Industry Trends - i18n: settings.careerPage.industryTrends -->
            <v-label class="text-subtitle-2 font-weight-bold mb-2 d-block">
              {{ this.$t("settings.careerPage.industryTrends") }}
            </v-label>
            <v-textarea
              v-model="industryTrends"
              :placeholder="
                this.$t('settings.careerPage.industryTrendsPlaceholder')
              "
              variant="outlined"
              rows="3"
              hide-details
            ></v-textarea>
          </div>

          <!-- Get Suggestions Button
          :disabled="!companyBranding.trim()" -->
          <div class="d-flex justify-center">
            <v-btn
              color="primary"
              variant="elevated"
              size="large"
              class="px-8"
              :loading="isGeneratingSuggestions"
              :disabled="true"
              @click="generateAISuggestions"
              rounded="lg"
            >
              <v-icon class="mr-2">fas fa-magic</v-icon>
              <!-- Get Suggestions - i18n: settings.careerPage.getSuggestions -->
              {{ this.$t("settings.careerPage.getSuggestions") }}
            </v-btn>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- AI Suggestions Results Dialog -->
    <v-dialog v-model="showAISuggestionsResults" max-width="900" persistent>
      <v-card class="rounded-lg">
        <v-card-title
          class="d-flex align-center justify-space-between pa-6 pb-4"
        >
          <div class="d-flex align-center">
            <v-icon class="mr-3" color="primary" size="24">fas fa-magic</v-icon>
            <!-- AI-Generated Banner Suggestions - i18n: settings.careerPage.aiGeneratedBannerSuggestions -->
            <span class="text-h6 font-weight-bold">{{
              this.$t("settings.careerPage.aiGeneratedBannerSuggestions")
            }}</span>
          </div>
          <v-btn
            icon
            variant="text"
            color="primary"
            size="small"
            @click="closeAISuggestionsResults"
          >
            <v-icon class="primary">fas fa-times</v-icon>
          </v-btn>
        </v-card-title>

        <v-card-text class="px-6 pb-6">
          <div class="text-body-2 mb-4 text-grey-darken-1">
            <!-- AI suggestions description - i18n: settings.careerPage.aiSuggestionsDescription -->
            {{ this.$t("settings.careerPage.aiSuggestionsDescription") }}
          </div>

          <v-row>
            <v-col
              v-for="(suggestion, index) in aiSuggestions"
              :key="index"
              cols="12"
              sm="6"
              md="4"
            >
              <v-card
                class="cursor-pointer position-relative"
                :class="{ 'border-primary': selectedSuggestion === index }"
                :style="{
                  transition: 'all 0.2s ease',
                  borderWidth: selectedSuggestion === index ? '2px' : '1px',
                  borderStyle: 'solid',
                  borderColor:
                    selectedSuggestion === index
                      ? 'rgb(var(--v-theme-primary))'
                      : 'transparent',
                }"
                @click="selectSuggestion(index)"
                hover
                variant="outlined"
              >
                <v-img :src="suggestion.url" height="120" cover>
                  <div
                    class="position-absolute rounded-circle d-flex align-center justify-center"
                    style="
                      top: 8px;
                      right: 8px;
                      background: rgba(0, 0, 0, 0.5);
                      width: 24px;
                      height: 24px;
                    "
                  >
                    <v-icon
                      v-if="selectedSuggestion === index"
                      color="white"
                      size="16"
                    >
                      fas fa-check-circle
                    </v-icon>
                  </div>
                </v-img>
                <v-card-subtitle class="text-center pa-2">
                  {{ suggestion.title }}
                </v-card-subtitle>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions class="px-6 pb-6">
          <v-btn variant="text" @click="backToForm" class="mr-2">
            <v-icon class="mr-1">fas fa-arrow-left</v-icon>
            <!-- Back to Form - i18n: settings.careerPage.backToForm -->
            {{ this.$t("settings.careerPage.backToForm") }}
          </v-btn>
          <v-spacer></v-spacer>
          <v-btn variant="text" @click="closeAISuggestionsResults">
            <!-- Cancel - i18n: common.cancel -->
            {{ this.$t("common.cancel") }}
          </v-btn>
          <v-btn
            variant="elevated"
            color="primary"
            @click="applyAISuggestion"
            :disabled="selectedSuggestion === null"
            rounded="lg"
          >
            <!-- Apply Selected - i18n: settings.careerPage.applySelected -->
            {{ this.$t("settings.careerPage.applySelected") }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import Config from "@/config";

export default {
  name: "BrandAssetsSection",
  props: {
    companyLogo: {
      type: [String, File],
      default: null,
    },
    careerLogoPath: {
      type: [String, File],
      default: null,
    },
    faviconFilename: {
      type: [String, File],
      default: null,
    },
    careerBannerImage: {
      type: [String, File],
      default: null,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  emits: [
    "update:companyLogo",
    "update:careerLogoPath",
    "update:faviconFilename",
    "update:careerBannerImage",
    "update-assets",
    "form-change",
  ],
  data() {
    return {
      showAISuggestions: false,
      showAISuggestionsResults: false,
      selectedSuggestion: null,
      companyLogoPreview: null,
      careerLogoPreview: null,
      faviconPreview: null,
      careerBannerImagePreview: null,
      companyBranding: "",
      industryTrends: "",
      isGeneratingSuggestions: false,
      aiSuggestions: [],
      // File input models
      companyLogoFile: null,
      careerLogoFile: null,
      faviconFile: null,
      careerBannerImageFile: null,
      // Validation rules
      companyLogoValidationRules: [
        (files) => {
          if (!files || files?.length === 0) return true;
          const file = Array.isArray(files) ? files[0] : files;
          if (typeof File === "undefined" || !(file instanceof File))
            return true;
          const maxSize = 2 * 1024 * 1024; // 2MB
          return (
            file.size <= maxSize ||
            this.$t("settings.careerPage.fileSizeMustBeLessThan2MB")
          );
        },
      ],
      careerLogoValidationRules: [
        (files) => {
          if (!files || files?.length === 0) return true;
          const file = Array.isArray(files) ? files[0] : files;
          if (typeof File === "undefined" || !(file instanceof File))
            return true;
          const maxSize = 2 * 1024 * 1024; // 2MB
          return (
            file.size <= maxSize ||
            this.$t("settings.careerPage.fileSizeMustBeLessThan2MB")
          );
        },
      ],
      faviconValidationRules: [
        (files) => {
          if (!files || files?.length === 0) return true;
          const file = Array.isArray(files) ? files[0] : files;
          if (typeof File === "undefined" || !(file instanceof File))
            return true;
          const maxSize = 1 * 1024 * 1024; // 1MB
          return (
            file.size <= maxSize ||
            this.$t("settings.careerPage.fileSizeMustBeLessThan1MB")
          );
        },
      ],
      careerBannerImageValidationRules: [
        (files) => {
          if (!files || files?.length === 0) return true;
          const file = Array.isArray(files) ? files[0] : files;
          if (typeof File === "undefined" || !(file instanceof File))
            return true;
          const maxSize = 3 * 1024 * 1024; // 3MB
          return (
            file.size <= maxSize ||
            this.$t("settings.careerPage.fileSizeMustBeLessThan3MB")
          );
        },
      ],
    };
  },
  computed: {
    domainName() {
      return this.$store.getters.domain;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    // Computed properties for image URLs using get ImageUrl method
    companyLogoImageSrc() {
      return this.getImageUrl(this.companyLogo, "companyLogo");
    },
    careerLogoImageSrc() {
      return this.getImageUrl(this.careerLogoPath, "careerLogo");
    },
    faviconImageSrc() {
      return this.faviconFilename
        ? this.getImageUrl("favicon.ico", "favicon")
        : null;
    },
    careerBannerImageSrc() {
      return this.getImageUrl(this.careerBannerImage, "bannerImage");
    },
  },
  mounted() {
    // Initialize file previews when component is mounted
    this.initializeFilePreviews();
  },
  watch: {
    companyLogo: {
      handler(newVal) {
        if (typeof newVal === "string" && newVal) {
          // If it's a filename string, use computed property for image URL and prefill file input
          this.companyLogoPreview = this.companyLogoImageSrc;
          // Prefill file input with existing filename
          this.prefillFileInput("companyLogo", newVal);
        } else {
          this.updatePreview("companyLogo", newVal);
        }
      },
      immediate: true,
    },
    careerLogoPath: {
      handler(newVal) {
        if (typeof newVal === "string" && newVal) {
          // If it's a filename string, use computed property for image URL and prefill file input
          this.careerLogoPreview = this.careerLogoImageSrc;
          // Prefill file input with existing filename
          this.prefillFileInput("careerLogoPath", newVal);
        } else {
          this.updatePreview("careerLogoPath", newVal);
        }
      },
      immediate: true,
    },
    faviconFilename: {
      handler(newVal) {
        if (typeof newVal === "string" && newVal) {
          // If it's a filename string, use computed property for image URL and prefill file input
          this.faviconPreview = this.faviconImageSrc;
          // Prefill file input with existing filename
          this.prefillFileInput("faviconFilename", newVal);
        } else {
          this.updatePreview("faviconFilename", newVal);
        }
      },
      immediate: true,
    },
    careerBannerImage: {
      handler(newVal) {
        if (typeof newVal === "string" && newVal) {
          // If it's a filename string, use computed property for image URL and prefill file input
          this.careerBannerImagePreview = this.careerBannerImageSrc;
          // Prefill file input with existing filename
          this.prefillFileInput("careerBannerImage", newVal);
        } else {
          this.updatePreview("careerBannerImage", newVal);
        }
      },
      immediate: true,
    },
  },

  methods: {
    // Method to prefill file input with existing filename
    prefillFileInput(type, filename) {
      if (!filename) return;

      // Create a mock file object to display the filename in v-file-input
      const mockFile = new File([], filename, {
        type: "application/octet-stream",
      });

      if (type === "companyLogo") {
        this.companyLogoFile = [mockFile];
      } else if (type === "careerLogoPath") {
        this.careerLogoFile = [mockFile];
      } else if (type === "faviconFilename") {
        this.faviconFile = [mockFile];
      } else if (type === "careerBannerImage") {
        this.careerBannerImageFile = [mockFile];
      }
    },

    async handleFileUpload(type, files) {
      // Handle file clearing
      if (!files || files?.length === 0) {
        // Delete previously uploaded S3 file if exists
        await this.deleteFromS3IfExists(type);

        // Clear the model and preview
        this.$emit(`update:${type}`, null);
        // Handle preview property names correctly
        if (type === "faviconFilename") {
          this.faviconPreview = null;
        } else if (type === "companyLogo") {
          this.companyLogoPreview = null;
        } else if (type === "careerLogoPath") {
          this.careerLogoPreview = null;
        } else if (type === "careerBannerImage") {
          this.careerBannerImagePreview = null;
        }
        this.$emit("update-assets", { [type]: null });
        this.$emit("form-change");
        return;
      }

      const file = Array.isArray(files) ? files[0] : files;
      if (!file) return;

      try {
        // Validate file size based on type
        const maxSize =
          type === "careerBannerImage"
            ? 3 * 1024 * 1024
            : type === "companyLogo" || type === "careerLogoPath"
            ? 2 * 1024 * 1024
            : 1 * 1024 * 1024; // faviconFilename

        if (file.size > maxSize) {
          const sizeMB = Math.round(maxSize / (1024 * 1024));
          this.showAlert({
            isOpen: true,
            message: this.$t("settings.careerPage.fileSizeMustBeLessThanMB", {
              size: sizeMB,
            }),
            type: "warning",
          });
          return;
        }

        // Just create preview and emit the File object (no S3 upload yet)
        this.createPreview(type, file);
        this.$emit(`update:${type}`, file);
        this.$emit("update-assets", { [type]: file });
        this.$emit("form-change");
      } catch (error) {
        this.showAlert({
          isOpen: true,
          message: this.$t("settings.careerPage.failedToProcessFile", {
            type: type,
          }),
          type: "warning",
        });
      }
    },

    createPreview(type, file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        // Handle preview property names correctly
        if (type === "faviconFilename") {
          this.faviconPreview = e.target.result;
        } else if (type === "companyLogo") {
          this.companyLogoPreview = e.target.result;
        } else if (type === "careerLogoPath") {
          this.careerLogoPreview = e.target.result;
        } else if (type === "careerBannerImage") {
          this.careerBannerImagePreview = e.target.result;
        }
      };
      reader.readAsDataURL(file);
    },

    updatePreview(type, value) {
      if (typeof File !== "undefined" && value instanceof File) {
        this.createPreview(type, value);
      } else {
        // Handle preview property names correctly
        const previewValue = typeof value === "string" && value ? value : null;
        if (type === "faviconFilename") {
          this.faviconPreview = previewValue;
        } else if (type === "companyLogo") {
          this.companyLogoPreview = previewValue;
        } else if (type === "careerLogoPath") {
          this.careerLogoPreview = previewValue;
        } else if (type === "careerBannerImage") {
          this.careerBannerImagePreview = previewValue;
        }
      }
    },

    getImageUrl(filename, imageType) {
      if (!filename) return null;

      // Check if it's already a full URL
      if (filename.startsWith("http://") || filename.startsWith("https://")) {
        return filename;
      }

      if (imageType === "companyLogo") {
        return `${Config.publicImageS3Path}hrapp_upload/${this.orgCode}_tmp/logos/${filename}`;
      } else if (imageType === "careerLogo") {
        return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/careerLogo/${filename}`;
      } else if (imageType === "favicon") {
        return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/favicon/${filename}`;
      } else if (imageType === "bannerImage") {
        return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/banner/${filename}`;
      }

      return null;
    },

    // Method to show alert messages
    showAlert(alertConfig) {
      this.$emit("show-alert", alertConfig);
    },

    // Delete file from S3 if it exists
    async deleteFromS3IfExists(type) {
      if (type === "faviconFilename") return; // Favicon deletion handled differently
      let currentFileName = null;

      // Get current filename from props
      if (type === "companyLogo" && this.companyLogo) {
        currentFileName = this.companyLogo;
      } else if (type === "careerLogoPath" && this.careerLogoPath) {
        currentFileName = this.careerLogoPath;
      } else if (type === "careerBannerImage" && this.careerBannerImage) {
        currentFileName = this.careerBannerImage;
      }

      if (!currentFileName || typeof currentFileName !== "string") {
        return; // No existing file to delete
      }

      let fullFilePath;

      if (type === "companyLogo") {
        fullFilePath = `hrapp_upload/${this.orgCode}_tmp/logos/${currentFileName}`;
      } else if (type === "careerLogoPath") {
        fullFilePath = `${this.domainName}/${this.orgCode}/careerLogo/${currentFileName}`;
      } else if (type === "careerBannerImage") {
        fullFilePath = `${this.domainName}/${this.orgCode}/banner/${currentFileName}`;
      }

      await this.$store.dispatch("s3FileUploadRetrieveAction", {
        fileName: fullFilePath,
        action: "delete",
        type: "logo",
      });
    },

    // Initialize file previews from existing data (now using computed properties)
    initializeFilePreviews() {
      // Initialize company logo preview using computed property
      if (this.companyLogo) {
        this.companyLogoPreview = this.companyLogoImageSrc;
      }

      // Initialize career logo preview using computed property
      if (this.careerLogoPath) {
        this.careerLogoPreview = this.careerLogoImageSrc;
      }

      // Initialize favicon preview using computed property
      if (this.faviconFilename) {
        this.faviconPreview = this.faviconImageSrc;
      }

      // Initialize banner image preview using computed property
      if (this.careerBannerImage) {
        this.careerBannerImagePreview = this.careerBannerImageSrc;
      }
    },

    selectSuggestion(index) {
      this.selectedSuggestion = index;
    },

    closeAISuggestions() {
      this.showAISuggestions = false;
      this.companyBranding = "";
      this.industryTrends = "";
      this.selectedSuggestion = null;
    },

    async generateAISuggestions() {
      if (!this.companyBranding.trim()) return;

      this.isGeneratingSuggestions = true;

      try {
        // TODO: Replace with actual AI API call (Gemini or OpenAI)
        // For now, simulate API call with timeout
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // Mock AI-generated suggestions based on user input
        this.aiSuggestions = this.generateMockSuggestions();

        // Close form dialog and show results
        this.showAISuggestions = false;
        this.showAISuggestionsResults = true;
      } catch (error) {
        this.showAlert({
          isOpen: true,
          message: this.$t("settings.careerPage.failedToGenerateSuggestions"),
          type: "warning",
        });
      } finally {
        this.isGeneratingSuggestions = false;
      }
    },

    generateMockSuggestions() {
      // Generate dynamic suggestions based on user input
      const brandingKeywords = this.companyBranding.toLowerCase();
      const industryKeywords = this.industryTrends.toLowerCase();

      const suggestions = [];

      // Base suggestions that can be customized
      const baseSuggestions = [
        {
          title: "Professional Team",
          url: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=400&h=150&fit=crop",
        },
        {
          title: "Modern Workspace",
          url: "https://images.unsplash.com/photo-1497366216548-37526070297c?w=400&h=150&fit=crop",
        },
        {
          title: "Innovation Hub",
          url: "https://images.unsplash.com/photo-1504384308090-c894fdcc538d?w=400&h=150&fit=crop",
        },
        {
          title: "Growth & Success",
          url: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=150&fit=crop",
        },
      ];

      // Customize suggestions based on user input
      if (
        brandingKeywords.includes("tech") ||
        brandingKeywords.includes("startup")
      ) {
        suggestions.push({
          title: "Tech Innovation",
          url: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=150&fit=crop",
        });
      }

      if (
        brandingKeywords.includes("blue") ||
        brandingKeywords.includes("minimalist")
      ) {
        suggestions.push({
          title: "Clean & Modern",
          url: "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=150&fit=crop",
        });
      }

      // Add suggestions based on industry trends
      if (
        industryKeywords.includes("abstract") ||
        industryKeywords.includes("data")
      ) {
        suggestions.push({
          title: "Data Visualization",
          url: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=150&fit=crop",
        });
      }

      if (
        industryKeywords.includes("diverse") ||
        industryKeywords.includes("team")
      ) {
        suggestions.push({
          title: "Diverse Team",
          url: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=150&fit=crop",
        });
      }

      if (
        industryKeywords.includes("creative") ||
        industryKeywords.includes("design")
      ) {
        suggestions.push({
          title: "Creative Studio",
          url: "https://images.unsplash.com/photo-1542744094-3a31f272c490?w=400&h=150&fit=crop",
        });
      }

      if (
        industryKeywords.includes("finance") ||
        industryKeywords.includes("business")
      ) {
        suggestions.push({
          title: "Business Growth",
          url: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=150&fit=crop",
        });
      }

      return [...baseSuggestions, ...suggestions].slice(0, 6);
    },

    closeAISuggestionsResults() {
      this.showAISuggestionsResults = false;
      this.selectedSuggestion = null;
      this.aiSuggestions = [];
    },

    backToForm() {
      this.showAISuggestionsResults = false;
      this.showAISuggestions = true;
    },

    async applyAISuggestion() {
      if (this.selectedSuggestion === null) return;

      const suggestion = this.aiSuggestions[this.selectedSuggestion];

      try {
        // Convert URL to blob and then to file
        const response = await fetch(suggestion.url);
        const blob = await response.blob();
        const file = new File(
          [blob],
          `banner-${suggestion.title.toLowerCase().replace(/\s+/g, "-")}.jpg`,
          {
            type: "image/jpeg",
          }
        );

        this.$emit("update:careerBannerImage", file);
        this.createPreview("careerBannerImage", file);
        this.$emit("update-assets", { careerBannerImage: file });
        this.$emit("form-change");

        this.showAISuggestionsResults = false;
        this.selectedSuggestion = null;
        this.aiSuggestions = [];
      } catch (error) {
        // Handle error silently or emit error event if needed
      }
    },
  },
};
</script>

<style scoped>
/* Upload preview styles now use Vuetify v-card components */
</style>
