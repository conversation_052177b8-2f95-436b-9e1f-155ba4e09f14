<template>
  <div
    class="career-preview"
    :style="{
      width: previewDimensions.width,
      height: previewDimensions.height,
      maxWidth: '100%',
    }"
  >
    <!-- Header with Logo -->
    <div class="d-flex align-center pa-4" :style="{ minHeight: '60px' }">
      <div class="d-flex align-center">
        <div v-if="displayLogoUrl" class="mr-3">
          <!-- Career/Company Logo - i18n: settings.careerPage.logoAlt -->
          <img
            :src="displayLogoUrl"
            :alt="this.$t('settings.careerPage.logoAlt')"
            class="rounded"
            style="max-height: 40px; max-width: 120px; object-fit: contain"
          />
        </div>
        <div
          v-else
          class="d-flex align-center justify-center rounded border mr-3"
          style="
            width: 120px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
          "
        >
          <!-- Logo Placeholder - i18n: settings.careerPage.logoPlaceholder -->
          <span class="text-caption text-white">{{
            this.$t("settings.careerPage.logoPlaceholder")
          }}</span>
        </div>
      </div>
    </div>

    <!-- Banner Section -->
    <div class="position-relative overflow-hidden" :style="bannerStyles">
      <div
        class="position-absolute w-100 h-100 d-flex pa-5"
        :style="{
          backgroundColor: `rgba(0, 0, 0, ${formData.bannerOpacity / 100})`,
          alignItems: getVerticalAlignment(),
          justifyContent: getHorizontalAlignment(),
        }"
      >
        <div
          class="d-flex flex-column"
          :style="{
            textAlign: formData.textHorizontalPosition,
            maxWidth: '90%',
            wordWrap: 'break-word',
          }"
        >
          <!-- Banner Heading -->
          <div
            class="font-weight-bold mb-2"
            :style="{
              fontFamily: getFontFamily(formData.headlineFontFamily),
              fontSize: `${getResponsiveHeadingFontSize(
                formData.headingFontSize
              )}px`,
              color: formData.headlineFontColor || '#FFFFFF',
              textShadow: '0 2px 4px rgba(0,0,0,0.5)',
              lineHeight: '1.2',
            }"
          >
            {{ formData.bannerHeading }}
          </div>

          <!-- Banner Sub Text -->
          <div
            v-if="formData.bannerText"
            :style="{
              fontFamily: getFontFamily(formData.headlineFontFamily),
              fontSize: `${getResponsiveSubTextFontSize(
                formData.subTextFontSize
              )}px`,
              color: formData.headlineFontColor || '#FFFFFF',
              textShadow: '0 2px 4px rgba(0,0,0,0.5)',
              lineHeight: '1.4',
              opacity: '0.9',
            }"
          >
            {{ formData.bannerText }}
          </div>
        </div>
      </div>
    </div>

    <!-- Job Listings Section -->
    <div
      class="bg-white"
      :style="{ padding: getContentPadding(), minHeight: '200px' }"
    >
      <!-- Open Positions - i18n: settings.careerPage.openPositions -->
      <div
        class="text-body-1 font-weight-bold mb-4 pb-2 text-primary"
        :style="{ fontFamily: getFontFamily(formData.headlineFontFamily) }"
      >
        {{ this.$t("settings.careerPage.openPositions") }}
      </div>

      <!-- Job Cards -->
      <div class="mt-4">
        <v-card
          v-for="job in sampleJobs"
          :key="job.id"
          class="mb-3 pa-4 d-flex align-center elevation-1"
          :style="{ transition: 'all 0.2s ease' }"
          hover
        >
          <div class="flex-grow-1">
            <div
              class="font-weight-medium mb-1"
              :style="{
                fontFamily: getFontFamily(formData.headlineFontFamily),
                fontSize: '16px',
                color: formData.primaryColor,
                lineHeight: '1.3',
              }"
            >
              {{ job.title }}
            </div>
            <div
              class="text-grey-darken-1"
              :style="{
                fontFamily: getFontFamily(formData.headlineFontFamily),
              }"
            >
              {{ job.location }} • {{ job.type }}
            </div>
          </div>
          <v-btn
            class="ml-4"
            :style="{
              backgroundColor: formData.primaryColor,
              color: getContrastColor(formData.primaryColor),
              fontFamily: getFontFamily(formData.headlineFontFamily),
              fontSize: '14px',
              transition: 'all 0.2s ease',
            }"
            size="small"
            variant="elevated"
            rounded="lg"
          >
            <!-- Apply Button - i18n: settings.careerPage.apply -->
            {{ this.$t("settings.careerPage.apply") }}
          </v-btn>
        </v-card>
      </div>
    </div>
  </div>
</template>

<script>
import Config from "@/config";

export default {
  name: "CareerPreview",
  props: {
    formData: {
      type: Object,
      required: true,
    },
    previewDimensions: {
      type: Object,
      default: () => ({ width: "100%", height: "600px" }),
    },
    googleFontsList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // Processed image URLs for display
      processedCompanyLogoUrl: null,
      processedCareerLogoUrl: null,
      processedBannerImageUrl: null,
      loadedFonts: new Set(), // Track loaded fonts to avoid duplicates
    };
  },

  computed: {
    sampleJobs() {
      return [
        {
          id: 1,
          title: "Senior Software Engineer",
          location: "San Francisco, CA",
          type: "Full-time",
          department: "Engineering",
        },
        {
          id: 2,
          title: "Product Manager",
          location: "Remote",
          type: "Full-time",
          department: "Product",
        },
        {
          id: 3,
          title: "UX/UI Designer",
          location: "New York, NY",
          type: "Full-time",
          department: "Design",
        },
        {
          id: 4,
          title: "DevOps Engineer",
          location: "Austin, TX",
          type: "Full-time",
          department: "Engineering",
        },
        {
          id: 5,
          title: "Marketing Specialist",
          location: "Remote",
          type: "Part-time",
          department: "Marketing",
        },
      ];
    },

    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },

    domainName() {
      return this.$store.getters.domain || localStorage.getItem("domainName");
    },

    bannerStyles() {
      const baseStyle = {
        height: "200px",
        backgroundSize: "cover",
        backgroundPosition: "center",
        position: "relative",
      };

      if (this.processedBannerImageUrl) {
        baseStyle["backgroundImage"] = `url(${this.processedBannerImageUrl})`;
      } else {
        baseStyle["background"] =
          "linear-gradient(135deg, #667eea 0%, #764ba2 100%)";
      }

      return baseStyle;
    },

    jobCardStyles() {
      return {
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "12px 16px",
        border: "1px solid #e0e0e0",
        borderRadius: "8px",
        marginBottom: "8px",
        backgroundColor: "white",
        transition: "all 0.2s ease",
      };
    },
    // Display logo with priority logic: Career Logo > Company Logo > Placeholder
    displayLogoUrl() {
      // Priority 1: If careerLogoPath exists and has a value → use career logo
      if (this.processedCareerLogoUrl) {
        return this.processedCareerLogoUrl;
      }
      // Priority 2: If careerLogoPath is empty but companyLogo exists → fallback to company logo
      if (this.processedCompanyLogoUrl) {
        return this.processedCompanyLogoUrl;
      }
      // Priority 3: If both are empty → return null (will show placeholder)
      return null;
    },
  },

  watch: {
    "formData.companyLogo": {
      handler(newVal) {
        this.processCompanyLogo(newVal);
      },
      immediate: true,
    },
    "formData.careerLogoPath": {
      handler(newVal) {
        this.processCareerLogo(newVal);
      },
      immediate: true,
    },
    "formData.careerBannerImage": {
      handler(newVal) {
        this.processBannerImage(newVal);
      },
      immediate: true,
    },
    "formData.headlineFontFamily": {
      handler(newVal) {
        if (newVal) {
          this.loadGoogleFont(newVal);
        }
      },
      immediate: true,
    },
  },

  methods: {
    getImageUrl(file, imageType = "companyLogo") {
      if (typeof File !== "undefined" && file instanceof File) {
        return URL.createObjectURL(file);
      }

      // If it's a filename string, construct direct S3 URL
      if (typeof file === "string" && file) {
        // Check if it's already a full URL
        if (file.startsWith("http://") || file.startsWith("https://")) {
          return file;
        }

        if (imageType === "companyLogo") {
          return `${Config.publicImageS3Path}hrapp_upload/${this.orgCode}_tmp/logos/${file}`;
        } else if (imageType === "careerLogo") {
          return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/careerLogo/${file}`;
        } else if (imageType === "favicon") {
          return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/favicon/${file}`;
        } else if (imageType === "bannerImage") {
          return `${Config.publicImageS3Path}${this.domainName}/${this.orgCode}/banner/${file}`;
        }
      }

      return file || "";
    },

    // Process company logo for display
    processCompanyLogo(logoFile) {
      if (typeof File !== "undefined" && logoFile instanceof File) {
        // For new File objects, create blob URL immediately
        this.processedCompanyLogoUrl = URL.createObjectURL(logoFile);
      } else if (typeof logoFile === "string" && logoFile) {
        // For saved filenames, construct direct S3 URL
        this.processedCompanyLogoUrl = this.getImageUrl(
          logoFile,
          "companyLogo"
        );
      } else {
        this.processedCompanyLogoUrl = null;
      }
    },

    // Process career logo for display
    processCareerLogo(logoFile) {
      if (typeof File !== "undefined" && logoFile instanceof File) {
        // For new File objects, create blob URL immediately
        this.processedCareerLogoUrl = URL.createObjectURL(logoFile);
      } else if (typeof logoFile === "string" && logoFile) {
        // For saved filenames, construct direct S3 URL
        this.processedCareerLogoUrl = this.getImageUrl(logoFile, "careerLogo");
      } else {
        this.processedCareerLogoUrl = null;
      }
    },

    // Process banner image for display
    processBannerImage(bannerFile) {
      if (typeof File !== "undefined" && bannerFile instanceof File) {
        // For new File objects, create blob URL immediately
        this.processedBannerImageUrl = URL.createObjectURL(bannerFile);
      } else if (typeof bannerFile === "string" && bannerFile) {
        // For saved filenames, construct direct S3 URL
        this.processedBannerImageUrl = this.getImageUrl(
          bannerFile,
          "bannerImage"
        );
      } else {
        this.processedBannerImageUrl = null;
      }
    },

    getFontFamily(fontKey) {
      // With CSS-ready format, just return the value directly
      return fontKey || '"Inter", sans-serif';
    },

    // Dynamically load Google Font when font family changes
    loadGoogleFont(cssFont) {
      if (!cssFont) return;

      // Extract font name from CSS format like '"Lilita One", sans-serif'
      const fontMatch = cssFont.match(/^"([^"]+)"/);
      if (!fontMatch) return;

      const fontName = fontMatch[1];

      // Check if font is already loaded
      if (this.loadedFonts.has(fontName)) return;

      // Skip system fonts that don't need loading
      const systemFonts = [
        "Arial",
        "Helvetica",
        "Times New Roman",
        "Georgia",
        "Verdana",
        "Inter",
        "GT Walsheim", // Local commercial font
      ];
      if (systemFonts.includes(fontName)) return;

      // Create and inject Google Fonts CSS link
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = `https://fonts.googleapis.com/css2?family=${fontName.replace(
        /\s+/g,
        "+"
      )}:wght@100;200;300;400;500;600;700;800;900&display=swap`;
      link.setAttribute("data-font-family", fontName);

      // Add to document head
      document.head.appendChild(link);

      // Track that this font has been loaded
      this.loadedFonts.add(fontName);
    },

    getVerticalAlignment() {
      const alignmentMap = {
        top: "flex-start",
        middle: "center",
        bottom: "flex-end",
      };
      return alignmentMap[this.formData.textVerticalPosition] || "center";
    },

    getHorizontalAlignment() {
      const alignmentMap = {
        left: "flex-start",
        center: "center",
        right: "flex-end",
      };
      return alignmentMap[this.formData.textHorizontalPosition] || "center";
    },

    getResponsiveHeadingFontSize(headingFontSize) {
      const width = parseInt(this.previewDimensions.width) || 800;

      // Scale the heading font size based on screen size while preserving the user's choice
      if (width < 400) {
        // Mobile: Scale down to 60% of original size, minimum 20px
        return Math.max(Math.round(headingFontSize * 0.6), 20);
      } else if (width < 768) {
        // Tablet: Scale down to 80% of original size, minimum 24px
        return Math.max(Math.round(headingFontSize * 0.8), 24);
      } else {
        // Desktop: Use full size, maximum 72px for reasonable display
        return Math.min(headingFontSize, 72);
      }
    },

    getResponsiveSubTextFontSize(subTextFontSize) {
      const width = parseInt(this.previewDimensions.width) || 800;

      // Scale the sub text font size based on screen size while preserving the user's choice
      if (width < 400) {
        // Mobile: Scale down to 70% of original size, minimum 12px
        return Math.max(Math.round(subTextFontSize * 0.7), 12);
      } else if (width < 768) {
        // Tablet: Scale down to 85% of original size, minimum 14px
        return Math.max(Math.round(subTextFontSize * 0.85), 14);
      } else {
        // Desktop: Use full size, maximum 32px for reasonable display
        return Math.min(subTextFontSize, 32);
      }
    },

    getContentPadding() {
      const width = parseInt(this.previewDimensions.width) || 800;
      if (width < 400) return "12px";
      if (width < 768) return "16px";
      return "20px";
    },

    getContrastColor(hexColor) {
      if (!hexColor) return "#FFFFFF";

      // Convert hex to RGB
      const r = parseInt(hexColor.slice(1, 3), 16);
      const g = parseInt(hexColor.slice(3, 5), 16);
      const b = parseInt(hexColor.slice(5, 7), 16);

      // Calculate luminance
      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

      return luminance > 0.5 ? "#000000" : "#FFFFFF";
    },
  },
};
</script>

<style scoped>
.career-preview {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .v-card {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 8px;
  }

  .v-btn {
    align-self: flex-end;
  }
}
</style>
