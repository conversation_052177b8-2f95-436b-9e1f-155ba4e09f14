<template>
  <div>
    <div class="d-flex align-center mb-4">
      <v-progress-circular
        model-value="100"
        color="red"
        :size="22"
        class="mr-2"
      />
      <!-- Content Customization - i18n: settings.careerPage.contentCustomization -->
      <div class="text-h6 font-weight-bold text-primary">
        {{ this.$t("settings.careerPage.contentCustomization") }}
      </div>
    </div>

    <!-- Banner Overlay Heading -->
    <div class="mb-6">
      <v-row>
        <v-col cols="12">
          <!-- Banner Overlay Heading - i18n: settings.careerPage.bannerOverlayHeading -->
          <div class="text-body-2 font-weight-medium mb-2">
            {{ this.$t("settings.careerPage.bannerOverlayHeading") }}
          </div>
          <v-text-field
            ref="bannerHeadingField"
            v-model="bannerHeadingModel"
            variant="solo"
            :placeholder="this.$t('settings.careerPage.enterBannerHeading')"
            :disabled="isLoading"
            :rules="[
              maxLengthValidation(
                this.$t('settings.careerPage.bannerHeading'),
                bannerHeadingModel,
                100
              ),
            ]"
            hide-details="auto"
            class="mb-2"
          ></v-text-field>
          <div class="text-caption text-grey">
            <!-- Banner heading description - i18n: settings.careerPage.bannerHeadingDescription -->
            {{ this.$t("settings.careerPage.bannerHeadingDescription") }}
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Banner Overlay Sub Text -->
    <div class="mb-6">
      <v-row>
        <v-col cols="12">
          <!-- Banner Overlay Sub Text - i18n: settings.careerPage.bannerOverlaySubText -->
          <div class="text-body-2 font-weight-medium mb-2">
            {{ this.$t("settings.careerPage.bannerOverlaySubText") }}
          </div>
          <v-textarea
            ref="bannerTextField"
            v-model="bannerTextModel"
            variant="solo"
            :placeholder="this.$t('settings.careerPage.enterBannerSubText')"
            rows="3"
            :disabled="isLoading"
            :rules="[
              maxLengthValidation(
                this.$t('settings.careerPage.bannerSubText'),
                bannerTextModel,
                200
              ),
            ]"
            hide-details="auto"
            class="mb-2"
          ></v-textarea>
          <div class="text-caption text-grey">
            <!-- Banner sub text description - i18n: settings.careerPage.bannerSubTextDescription -->
            {{ this.$t("settings.careerPage.bannerSubTextDescription") }}
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Text Positioning Controls -->
    <div class="mb-4">
      <!-- Text Position - i18n: settings.careerPage.textPosition -->
      <div class="text-body-2 font-weight-medium mb-3">
        {{ this.$t("settings.careerPage.textPosition") }}
      </div>

      <!-- Text Horizontal Position -->
      <div class="mb-4">
        <!-- Horizontal Position - i18n: settings.careerPage.horizontalPosition -->
        <div class="text-body-2 font-weight-medium mb-2">
          {{ this.$t("settings.careerPage.horizontalPosition") }}
        </div>
        <v-radio-group
          ref="textHorizontalPositionRadioGroup"
          v-model="textHorizontalPositionModel"
          inline
          hide-details
          :disabled="isLoading"
        >
          <!-- Left - i18n: settings.careerPage.left -->
          <v-radio
            :label="this.$t('settings.careerPage.left')"
            value="left"
            color="primary"
          ></v-radio>
          <!-- Center - i18n: settings.careerPage.center -->
          <v-radio
            :label="this.$t('settings.careerPage.center')"
            value="center"
            color="primary"
          ></v-radio>
          <!-- Right - i18n: settings.careerPage.right -->
          <v-radio
            :label="this.$t('settings.careerPage.right')"
            value="right"
            color="primary"
          ></v-radio>
        </v-radio-group>
      </div>

      <!-- Text Vertical Position -->
      <div class="mb-4">
        <!-- Vertical Position - i18n: settings.careerPage.verticalPosition -->
        <div class="text-body-2 font-weight-medium mb-2">
          {{ this.$t("settings.careerPage.verticalPosition") }}
        </div>
        <v-radio-group
          ref="textVerticalPositionRadioGroup"
          v-model="textVerticalPositionModel"
          inline
          hide-details
          :disabled="isLoading"
        >
          <!-- Top - i18n: settings.careerPage.top -->
          <v-radio
            :label="this.$t('settings.careerPage.top')"
            value="top"
            color="primary"
          ></v-radio>
          <!-- Middle - i18n: settings.careerPage.middle -->
          <v-radio
            :label="this.$t('settings.careerPage.middle')"
            value="middle"
            color="primary"
          ></v-radio>
          <!-- Bottom - i18n: settings.careerPage.bottom -->
          <v-radio
            :label="this.$t('settings.careerPage.bottom')"
            value="bottom"
            color="primary"
          ></v-radio>
        </v-radio-group>
      </div>
    </div>

    <!-- Preview Text Positioning -->
    <v-card
      variant="outlined"
      class="mb-4 pa-4 bg-grey-lighten-5"
      elevation="0"
    >
      <!-- Position Preview - i18n: settings.careerPage.positionPreview -->
      <div class="text-body-2 font-weight-medium mb-3">
        {{ this.$t("settings.careerPage.positionPreview") }}
      </div>
      <div class="d-flex justify-center">
        <v-card
          variant="outlined"
          class="position-grid-container bg-white"
          style="width: 120px; height: 80px; position: relative"
        >
          <div class="position-grid-overlay">
            <v-btn
              v-for="(position, index) in positionGrid"
              :key="index"
              :variant="
                isPositionActive(position.horizontal, position.vertical)
                  ? 'elevated'
                  : 'text'
              "
              :color="
                isPositionActive(position.horizontal, position.vertical)
                  ? 'primary'
                  : 'grey'
              "
              size="x-small"
              icon
              class="position-btn"
              :class="
                getPositionClasses(position.horizontal, position.vertical)
              "
              @click="setPosition(position.horizontal, position.vertical)"
            >
              <v-icon size="8">fas fa-circle</v-icon>
            </v-btn>
          </div>
        </v-card>
      </div>
    </v-card>
  </div>
</template>

<script>
import validationRules from "@/mixins/validationRules";

export default {
  name: "ContentCustomizationSection",
  mixins: [validationRules],
  props: {
    bannerHeading: {
      type: String,
      default: "Career Vacancies",
    },
    bannerText: {
      type: String,
      default:
        "Chart your course, explore opportunities and set sail for success with us.",
    },
    textHorizontalPosition: {
      type: String,
      default: "center",
      validator: (value) => ["left", "center", "right"].includes(value),
    },
    textVerticalPosition: {
      type: String,
      default: "middle",
      validator: (value) => ["top", "middle", "bottom"].includes(value),
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  emits: [
    "update:bannerHeading",
    "update:bannerText",
    "update:textHorizontalPosition",
    "update:textVerticalPosition",
    "form-change",
  ],
  data() {
    return {};
  },
  computed: {
    bannerHeadingModel: {
      get() {
        return this.bannerHeading;
      },
      set(value) {
        this.$emit("update:bannerHeading", value);
        this.$emit("form-change");
      },
    },
    bannerTextModel: {
      get() {
        return this.bannerText;
      },
      set(value) {
        this.$emit("update:bannerText", value);
        this.$emit("form-change");
      },
    },
    textHorizontalPositionModel: {
      get() {
        return this.textHorizontalPosition;
      },
      set(value) {
        this.$emit("update:textHorizontalPosition", value);
        this.$emit("form-change");
      },
    },
    textVerticalPositionModel: {
      get() {
        return this.textVerticalPosition;
      },
      set(value) {
        this.$emit("update:textVerticalPosition", value);
        this.$emit("form-change");
      },
    },
    positionGrid() {
      const horizontalPositions = ["left", "center", "right"];
      const verticalPositions = ["top", "middle", "bottom"];
      const grid = [];

      verticalPositions.forEach((vertical) => {
        horizontalPositions.forEach((horizontal) => {
          grid.push({ horizontal, vertical });
        });
      });

      return grid;
    },
  },
  methods: {
    isPositionActive(horizontal, vertical) {
      return (
        this.textHorizontalPosition === horizontal &&
        this.textVerticalPosition === vertical
      );
    },

    setPosition(horizontal, vertical) {
      this.textHorizontalPositionModel = horizontal;
      this.textVerticalPositionModel = vertical;
      this.$emit("form-change");
    },

    getPositionClasses(horizontal, vertical) {
      const classes = [];

      // Horizontal positioning
      if (horizontal === "left") classes.push("position-left");
      else if (horizontal === "center") classes.push("position-center-h");
      else if (horizontal === "right") classes.push("position-right");

      // Vertical positioning
      if (vertical === "top") classes.push("position-top");
      else if (vertical === "middle") classes.push("position-middle");
      else if (vertical === "bottom") classes.push("position-bottom");

      return classes.join(" ");
    },
  },
};
</script>

<style scoped>
/* Position Grid Layout using CSS Grid with Vuetify classes */
.position-grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  padding: 8px;
  gap: 4px;
}

.position-btn {
  min-width: unset !important;
  width: 20px !important;
  height: 20px !important;
}

/* Position-specific classes for grid placement */
.position-left.position-top {
  grid-area: 1 / 1;
}
.position-center-h.position-top {
  grid-area: 1 / 2;
}
.position-right.position-top {
  grid-area: 1 / 3;
}
.position-left.position-middle {
  grid-area: 2 / 1;
}
.position-center-h.position-middle {
  grid-area: 2 / 2;
}
.position-right.position-middle {
  grid-area: 2 / 3;
}
.position-left.position-bottom {
  grid-area: 3 / 1;
}
.position-center-h.position-bottom {
  grid-area: 3 / 2;
}
.position-right.position-bottom {
  grid-area: 3 / 3;
}
</style>
