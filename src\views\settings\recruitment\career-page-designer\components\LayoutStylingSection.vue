<template>
  <div>
    <div class="d-flex align-center mb-4">
      <v-progress-circular
        model-value="100"
        color="red"
        :size="22"
        class="mr-2"
      />
      <!-- Layout and Styling - i18n: settings.general.layoutAndStyling -->
      <div class="text-h6 font-weight-bold text-primary">
        {{ this.$t("settings.general.layoutAndStyling") }}
      </div>
      <v-tooltip
        :text="this.$t('settings.general.appliesAcrossApplication')"
        location="top"
      >
        <template v-slot:activator="{ props }">
          <v-icon class="ml-1" v-bind="props" size="small" color="blue">
            fas fa-info-circle
          </v-icon>
        </template>
      </v-tooltip>
    </div>

    <!-- Color Scheme -->
    <div class="mb-6">
      <!-- Color Controls - i18n: settings.general.colorControls -->
      <div class="text-subtitle-1 font-weight-medium mb-3">
        {{ this.$t("settings.general.colorControls") }}
      </div>

      <!-- Primary Color -->
      <div class="mb-4">
        <v-row align="center">
          <v-col cols="12" sm="4">
            <!-- Primary Color - i18n: settings.general.primaryColor -->
            <div class="text-body-2 font-weight-medium">
              {{ this.$t("settings.general.primaryColor") }}
            </div>
          </v-col>
          <v-col cols="12" sm="8">
            <div class="d-flex align-center">
              <div
                class="mr-3 rounded cursor-pointer border"
                :style="{
                  backgroundColor: primaryColorModel,
                  width: '32px',
                  height: '32px',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                }"
                @click="openColorPicker('primary')"
              ></div>
              <v-text-field
                ref="primaryColorField"
                v-model="primaryColorModel"
                variant="solo"
                density="compact"
                :rules="[
                  required(
                    this.$t('settings.general.primaryColor'),
                    primaryColorModel
                  ),
                  isValidHexColor(primaryColorModel),
                ]"
                hide-details="auto"
                :disabled="isLoading"
                style="max-width: 120px"
              ></v-text-field>
            </div>
          </v-col>
        </v-row>
      </div>

      <!-- Secondary Color -->
      <div class="mb-4">
        <v-row align="center">
          <v-col cols="12" sm="4">
            <!-- Secondary Color - i18n: settings.general.secondaryColor -->
            <div class="text-body-2 font-weight-medium">
              {{ this.$t("settings.general.secondaryColor") }}
            </div>
          </v-col>
          <v-col cols="12" sm="8">
            <div class="d-flex align-center">
              <div
                class="mr-3 rounded cursor-pointer border"
                :style="{
                  backgroundColor: secondaryColorModel,
                  width: '32px',
                  height: '32px',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                }"
                @click="openColorPicker('secondary')"
              ></div>
              <v-text-field
                ref="secondaryColorField"
                v-model="secondaryColorModel"
                variant="solo"
                density="compact"
                :rules="[
                  required(
                    this.$t('settings.general.secondaryColor'),
                    secondaryColorModel
                  ),
                  isValidHexColor(secondaryColorModel),
                ]"
                hide-details="auto"
                :disabled="isLoading"
                style="max-width: 120px"
              ></v-text-field>
            </div>
          </v-col>
        </v-row>
      </div>

      <!-- Hover Color -->
      <div class="mb-4">
        <v-row align="center">
          <v-col cols="12" sm="4">
            <!-- Hover Color - i18n: settings.general.hoverColor -->
            <div class="text-body-2 font-weight-medium">
              {{ this.$t("settings.general.hoverColor") }}
            </div>
          </v-col>
          <v-col cols="12" sm="8">
            <div class="d-flex align-center">
              <div
                class="mr-3 rounded cursor-pointer border"
                :style="{
                  backgroundColor: hoverColorModel,
                  width: '32px',
                  height: '32px',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                }"
                @click="openColorPicker('hover')"
              ></div>
              <v-text-field
                ref="hoverColorField"
                v-model="hoverColorModel"
                variant="solo"
                density="compact"
                :rules="[
                  required(
                    this.$t('settings.general.hoverColor'),
                    hoverColorModel
                  ),
                  isValidHexColor(hoverColorModel),
                ]"
                hide-details="auto"
                :disabled="isLoading"
                style="max-width: 120px"
              ></v-text-field>
            </div>
          </v-col>
        </v-row>
      </div>

      <!-- Data Table Header Panel Color -->
      <div class="mb-4">
        <v-row align="center">
          <v-col cols="12" sm="4">
            <!-- Data Table Header Panel - i18n: settings.general.dataTableHeaderPanel -->
            <div class="text-body-2 font-weight-medium">
              {{ this.$t("settings.general.dataTableHeaderPanel") }}
            </div>
          </v-col>
          <v-col cols="12" sm="8">
            <div class="d-flex align-center">
              <div
                class="mr-3 rounded cursor-pointer border"
                :style="{
                  backgroundColor: tableHeaderColorModel,
                  width: '32px',
                  height: '32px',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                }"
                @click="openColorPicker('tableHeader')"
              ></div>
              <v-text-field
                ref="tableHeaderColorField"
                v-model="tableHeaderColorModel"
                variant="solo"
                density="compact"
                :rules="[
                  required(
                    this.$t('settings.general.dataTableHeaderPanel'),
                    tableHeaderColorModel
                  ),
                  isValidHexColor(tableHeaderColorModel),
                ]"
                hide-details="auto"
                :disabled="isLoading"
                style="max-width: 120px"
              ></v-text-field>
            </div>
          </v-col>
        </v-row>
      </div>

      <!-- Data Table Header Text Color -->
      <div class="mb-4">
        <v-row align="center">
          <v-col cols="12" sm="4">
            <!-- Data Table Header Text - i18n: settings.general.dataTableHeaderText -->
            <div class="text-body-2 font-weight-medium">
              {{ this.$t("settings.general.dataTableHeaderText") }}
            </div>
          </v-col>
          <v-col cols="12" sm="8">
            <div class="d-flex align-center">
              <div
                class="mr-3 rounded cursor-pointer border"
                :style="{
                  backgroundColor: tableHeaderTextColorModel,
                  width: '32px',
                  height: '32px',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                }"
                @click="openColorPicker('tableHeaderText')"
              ></div>
              <v-text-field
                ref="tableHeaderTextColorField"
                v-model="tableHeaderTextColorModel"
                variant="solo"
                density="compact"
                :rules="[
                  required(
                    this.$t('settings.general.dataTableHeaderText'),
                    tableHeaderTextColorModel
                  ),
                  isValidHexColor(tableHeaderTextColorModel),
                ]"
                hide-details="auto"
                :disabled="isLoading"
                style="max-width: 120px"
              ></v-text-field>
            </div>
          </v-col>
        </v-row>
      </div>
    </div>

    <!-- Banner Overlay - i18n: settings.careerPage.bannerOverlay -->
    <div class="mb-6">
      <div class="text-h6 mb-4">
        {{ this.$t("settings.careerPage.bannerOverlay") }}
      </div>
      <!-- Opacity Control -->
      <v-row align="center">
        <v-col cols="12" sm="4">
          <!-- Banner Overlay Opacity - i18n: settings.careerPage.bannerOverlayOpacity -->
          <div class="text-body-2 font-weight-medium">
            {{ this.$t("settings.careerPage.bannerOverlayOpacity") }}:
            {{ bannerOpacityModel }}%
          </div>
        </v-col>
        <v-col cols="12" sm="8">
          <v-slider
            ref="bannerOpacitySlider"
            v-model="bannerOpacityModel"
            :min="0"
            :max="100"
            :step="5"
            color="primary"
            track-color="grey-lighten-3"
            thumb-label
            hide-details
            :disabled="isLoading"
          >
            <template v-slot:thumb-label="{ modelValue }">
              {{ modelValue }}%
            </template>
          </v-slider>
          <div class="d-flex justify-space-between text-caption text-grey mt-1">
            <span>0%</span>
            <span>100%</span>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Color Palette Preview - i18n: settings.careerPage.colorPalettePreview -->
    <div class="color-palette-preview">
      <div class="text-body-2 font-weight-medium mb-3">
        {{ this.$t("settings.careerPage.colorPalettePreview") }}
      </div>
      <v-card class="pa-4" variant="outlined" color="grey-lighten-4">
        <v-row>
          <v-col cols="6" sm="2" class="text-center">
            <v-card
              class="mx-auto mb-2"
              :style="{ backgroundColor: primaryColorModel }"
              width="48"
              height="48"
              variant="outlined"
              rounded="lg"
            ></v-card>
            <!-- Primary Buttons - i18n: settings.careerPage.primaryButtons -->
            <div class="text-caption text-grey font-weight-medium">
              {{ this.$t("settings.careerPage.primaryButtons") }}
            </div>
          </v-col>
          <v-col cols="6" sm="2" class="text-center">
            <v-card
              class="mx-auto mb-2"
              :style="{ backgroundColor: secondaryColorModel }"
              width="48"
              height="48"
              variant="outlined"
              rounded="lg"
            ></v-card>
            <!-- Secondary Preview - i18n: settings.careerPage.secondaryPreview -->
            <div class="text-caption text-grey font-weight-medium">
              {{ this.$t("settings.careerPage.secondaryPreview") }}
            </div>
          </v-col>
          <v-col cols="6" sm="2" class="text-center">
            <v-card
              class="mx-auto mb-2"
              :style="{ backgroundColor: hoverColorModel }"
              width="48"
              height="48"
              variant="outlined"
              rounded="lg"
            ></v-card>
            <!-- Hover Highlighting - i18n: settings.careerPage.hoverHighlighting -->
            <div class="text-caption text-grey font-weight-medium">
              {{ this.$t("settings.careerPage.hoverHighlighting") }}
            </div>
          </v-col>
          <v-col cols="6" sm="3" class="text-center">
            <v-card
              class="mx-auto mb-2"
              :style="{ backgroundColor: tableHeaderColorModel }"
              width="48"
              height="48"
              variant="outlined"
              rounded="lg"
            ></v-card>
            <!-- Data Table Header Panel - i18n: settings.careerPage.dataTableHeaderPanel -->
            <div class="text-caption text-grey font-weight-medium">
              {{ this.$t("settings.careerPage.dataTableHeaderPanel") }}
            </div>
          </v-col>
          <v-col cols="6" sm="3" class="text-center">
            <v-card
              class="mx-auto mb-2"
              :style="{ backgroundColor: tableHeaderTextColorModel }"
              width="48"
              height="48"
              variant="outlined"
              rounded="lg"
            ></v-card>
            <!-- Data Table Header Text - i18n: settings.careerPage.dataTableHeaderText -->
            <div class="text-caption text-grey font-weight-medium">
              {{ this.$t("settings.careerPage.dataTableHeaderText") }}
            </div>
          </v-col>
        </v-row>
      </v-card>
    </div>

    <!-- Color Picker Dialog -->
    <v-dialog v-model="showColorPicker" max-width="400">
      <v-card>
        <!-- Choose Color - i18n: settings.general.chooseColor -->
        <v-card-title>
          {{
            this.$t("settings.general.chooseColor", {
              type: currentColorType,
            })
          }}
        </v-card-title>
        <v-card-text>
          <div class="text-center">
            <input
              ref="colorPicker"
              type="color"
              :value="getCurrentColor()"
              @input="updateColor"
              style="
                width: 100px;
                height: 100px;
                border: none;
                border-radius: 8px;
                cursor: pointer;
              "
            />
            <div class="mt-4">
              <!-- Preset Colors - i18n: settings.careerPage.presetColors -->
              <div class="text-body-2 mb-2">
                {{ this.$t("settings.general.presetColors") }}
              </div>
              <div class="d-flex flex-wrap ga-2">
                <v-card
                  v-for="color in presetColors"
                  :key="color"
                  class="rounded cursor-pointer"
                  :style="{ backgroundColor: color }"
                  width="32"
                  height="32"
                  variant="outlined"
                  @click="selectPresetColor(color)"
                ></v-card>
              </div>
            </div>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn variant="text" @click="showColorPicker = false">
            <!-- Cancel - i18n: common.cancel -->
            {{ this.$t("common.cancel") }}
          </v-btn>
          <v-btn variant="elevated" color="primary" @click="applyColor">
            <!-- Apply - i18n: common.apply -->
            {{ this.$t("common.apply") }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { generateRandomColor } from "@/helper";
import validationRules from "@/mixins/validationRules";

export default {
  name: "LayoutStylingSection",
  mixins: [validationRules],
  props: {
    primaryColor: {
      type: String,
      default: "#1C277D",
    },
    secondaryColor: {
      type: String,
      default: "#F1F5F9",
    },
    hoverColor: {
      type: String,
      default: "#0F1B5C",
    },
    tableHeaderColor: {
      type: String,
      default: "#E2E8F0",
    },
    tableHeaderTextColor: {
      type: String,
      default: "#000000",
    },
    bannerOpacity: {
      type: Number,
      default: 30,
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
  },
  emits: [
    "update:primaryColor",
    "update:secondaryColor",
    "update:hoverColor",
    "update:tableHeaderColor",
    "update:tableHeaderTextColor",
    "update:bannerOpacity",
    "form-change",
  ],
  data() {
    return {
      showColorPicker: false,
      currentColorType: "",
      tempColor: "",
      presetColors: [],
    };
  },
  computed: {
    primaryColorModel: {
      get() {
        return this.primaryColor;
      },
      set(value) {
        this.$emit("update:primaryColor", value);
        this.$emit("form-change");
      },
    },
    secondaryColorModel: {
      get() {
        return this.secondaryColor;
      },
      set(value) {
        this.$emit("update:secondaryColor", value);
        this.$emit("form-change");
      },
    },
    hoverColorModel: {
      get() {
        return this.hoverColor;
      },
      set(value) {
        this.$emit("update:hoverColor", value);
        this.$emit("form-change");
      },
    },
    tableHeaderColorModel: {
      get() {
        return this.tableHeaderColor;
      },
      set(value) {
        this.$emit("update:tableHeaderColor", value);
        this.$emit("form-change");
      },
    },
    tableHeaderTextColorModel: {
      get() {
        return this.tableHeaderTextColor;
      },
      set(value) {
        this.$emit("update:tableHeaderTextColor", value);
        this.$emit("form-change");
      },
    },
    bannerOpacityModel: {
      get() {
        return this.bannerOpacity;
      },
      set(value) {
        this.$emit("update:bannerOpacity", value);
        this.$emit("form-change");
      },
    },
  },
  methods: {
    openColorPicker(type) {
      this.currentColorType = type;
      this.tempColor = this.getCurrentColor();
      // Generate 25 fresh random colors each time the dialog opens
      this.presetColors = Array.from({ length: 25 }, () =>
        generateRandomColor()
      );
      this.showColorPicker = true;
    },

    getCurrentColor() {
      const colorMap = {
        primary: this.primaryColorModel,
        secondary: this.secondaryColorModel,
        hover: this.hoverColorModel,
        tableHeader: this.tableHeaderColorModel,
        tableHeaderText: this.tableHeaderTextColorModel,
      };
      return colorMap[this.currentColorType] || "#000000";
    },

    updateColor(event) {
      this.tempColor = event.target.value;
    },

    selectPresetColor(color) {
      this.tempColor = color;
      this.$refs.colorPicker.value = color;
    },

    applyColor() {
      const emitMap = {
        primary: "update:primaryColor",
        secondary: "update:secondaryColor",
        hover: "update:hoverColor",
        tableHeader: "update:tableHeaderColor",
        tableHeaderText: "update:tableHeaderTextColor",
      };

      this.$emit(emitMap[this.currentColorType], this.tempColor);
      this.$emit("form-change");
      this.showColorPicker = false;
    },

    getContrastColor(hexColor) {
      // Convert hex to RGB
      const r = parseInt(hexColor.slice(1, 3), 16);
      const g = parseInt(hexColor.slice(3, 5), 16);
      const b = parseInt(hexColor.slice(5, 7), 16);

      // Calculate luminance
      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

      return luminance > 0.5 ? "#000000" : "#FFFFFF";
    },
  },
};
</script>

<style scoped>
/* Color preview styles now use Vuetify v-card components */

/* Palette styles now use Vuetify v-row/v-col and v-card components */

/* Sample UI styles now use Vuetify components */

/* Preset color styles now use Vuetify classes */

/* Preview card styles now use Vuetify color props */
</style>
