<template>
  <div>
    <div class="d-flex align-center mb-4">
      <v-progress-circular
        model-value="100"
        color="pink"
        :size="22"
        class="mr-2"
      />
      <!-- Typography Controls - i18n: settings.careerPage.typographyControls -->
      <div class="text-h6 font-weight-bold text-primary">
        {{ this.$t("settings.careerPage.typographyControls") }}
      </div>
    </div>

    <!-- Banner Font Family -->
    <div class="mb-6">
      <v-row align="center">
        <v-col cols="12" sm="4">
          <!-- Banner Font Family - i18n: settings.careerPage.bannerFontFamily -->
          <div class="text-body-2 font-weight-medium">
            {{ this.$t("settings.careerPage.bannerFontFamily") }}
          </div>
        </v-col>
        <v-col cols="12" sm="8">
          <CustomSelect
            ref="headlineFontFamilySelect"
            v-model="headlineFontFamilyModel"
            :items="availableFonts"
            :is-loading="googleFontsLoaded"
            :is-required="true"
            :rules="[
              required(
                this.$t('settings.careerPage.bannerFontFamily'),
                headlineFontFamilyModel
              ),
            ]"
            :is-auto-complete="true"
            variant="solo"
            density="compact"
            item-title="title"
            item-value="cssFormat"
            hide-details
            :itemSelected="headlineFontFamilyModel"
            :disabled="isLoading"
          />
        </v-col>
      </v-row>
    </div>

    <!-- Banner Heading Font Size -->
    <div class="mb-6">
      <v-row align="center">
        <v-col cols="12" sm="4">
          <!-- Banner Heading Font Size - i18n: settings.careerPage.bannerHeadingFontSize -->
          <div class="text-body-2 font-weight-medium">
            {{ this.$t("settings.careerPage.bannerHeadingFontSize") }}:
            {{ headingFontSizeModel }}px
          </div>
        </v-col>
        <v-col cols="12" sm="8">
          <v-slider
            ref="headingFontSizeSlider"
            v-model="headingFontSizeModel"
            :min="24"
            :max="72"
            :step="2"
            color="primary"
            track-color="grey-lighten-3"
            thumb-label
            hide-details
            :disabled="isLoading"
          >
            <template v-slot:thumb-label="{ modelValue }">
              {{ modelValue }}px
            </template>
          </v-slider>
          <div class="d-flex justify-space-between text-caption text-grey mt-1">
            <span>24px</span>
            <span>72px</span>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Banner Subtext Font Size -->
    <div class="mb-6">
      <v-row align="center">
        <v-col cols="12" sm="4">
          <!-- Banner Subtext Font Size - i18n: settings.careerPage.bannerSubtextFontSize -->
          <div class="text-body-2 font-weight-medium">
            {{ this.$t("settings.careerPage.bannerSubtextFontSize") }}:
            {{ subTextFontSizeModel }}px
          </div>
        </v-col>
        <v-col cols="12" sm="8">
          <v-slider
            ref="subTextFontSizeSlider"
            v-model="subTextFontSizeModel"
            :min="12"
            :max="32"
            :step="1"
            :disabled="isLoading"
            color="primary"
            track-color="grey-lighten-3"
            thumb-color="primary"
            hide-details
          />
          <div class="d-flex justify-space-between text-caption text-grey mt-1">
            <span>12px</span>
            <span>32px</span>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Banner Font Color -->
    <div class="mb-6">
      <v-row align="center">
        <v-col cols="12" sm="4">
          <!-- Banner Font Color - i18n: settings.careerPage.bannerFontColor -->
          <div class="text-body-2 font-weight-medium">
            {{ this.$t("settings.careerPage.bannerFontColor") }}
          </div>
        </v-col>
        <v-col cols="12" sm="8">
          <div class="d-flex align-center">
            <div
              class="mr-3 rounded cursor-pointer border"
              :style="{
                backgroundColor: headlineFontColorModel,
                width: '32px',
                height: '32px',
                transition: 'all 0.2s ease',
              }"
              @click="openColorPicker"
            ></div>
            <v-text-field
              ref="headlineFontColorField"
              v-model="headlineFontColorModel"
              variant="solo"
              density="compact"
              :rules="[
                required(
                  this.$t('settings.careerPage.bannerFontColor'),
                  headlineFontColorModel
                ),
                isValidHexColor(headlineFontColorModel),
              ]"
              hide-details="auto"
              :disabled="isLoading"
              style="max-width: 120px"
            ></v-text-field>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Color Picker Dialog -->
    <v-dialog v-model="showColorPicker" max-width="400">
      <v-card>
        <!-- Choose Banner Font Color - i18n: settings.careerPage.chooseBannerFontColor -->
        <v-card-title>{{
          this.$t("settings.careerPage.chooseBannerFontColor")
        }}</v-card-title>
        <v-card-text>
          <div class="text-center">
            <input
              ref="colorPicker"
              type="color"
              :value="headlineFontColorModel"
              @input="updateColor"
              style="
                width: 100px;
                height: 100px;
                border: none;
                border-radius: 8px;
                cursor: pointer;
              "
            />
            <div class="mt-4">
              <!-- Preset Colors - i18n: settings.careerPage.presetColors -->
              <div class="text-body-2 mb-2">
                {{ this.$t("settings.general.presetColors") }}
              </div>
              <div class="d-flex flex-wrap ga-2">
                <v-card
                  v-for="color in presetColors"
                  :key="color"
                  class="rounded cursor-pointer"
                  :style="{ backgroundColor: color }"
                  width="32"
                  height="32"
                  variant="outlined"
                  @click="selectPresetColor(color)"
                ></v-card>
              </div>
            </div>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn variant="text" @click="showColorPicker = false">
            <!-- Cancel - i18n: common.cancel -->
            {{ this.$t("common.cancel") }}
          </v-btn>
          <v-btn variant="elevated" color="primary" @click="applyColor">
            <!-- Apply - i18n: common.apply -->
            {{ this.$t("common.apply") }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { generateRandomColor } from "@/helper";
import validationRules from "@/mixins/validationRules";

export default {
  name: "TypographyControlsSection",
  mixins: [validationRules],
  props: {
    headlineFontFamily: {
      type: String,
      default: "Roboto (Sans-serif)",
    },
    headingFontSize: {
      type: Number,
      default: 48,
    },
    subTextFontSize: {
      type: Number,
      default: 18,
    },
    headlineFontColor: {
      type: String,
      default: "#1C277D",
    },
    isLoading: {
      type: Boolean,
      default: false,
    },
    googleFontsList: {
      type: Array,
      default: () => [],
    },
    googleFontsLoaded: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    CustomSelect,
  },
  emits: [
    "update:headlineFontFamily",
    "update:headingFontSize",
    "update:subTextFontSize",
    "update:headlineFontColor",
    "form-change",
  ],
  data() {
    return {
      showColorPicker: false,
      tempColor: "",
      presetColors: [],
    };
  },
  computed: {
    headlineFontFamilyModel: {
      get() {
        return this.headlineFontFamily;
      },
      set(value) {
        this.$emit("update:headlineFontFamily", value);
        this.$emit("form-change");
      },
    },
    headingFontSizeModel: {
      get() {
        return this.headingFontSize;
      },
      set(value) {
        this.$emit("update:headingFontSize", value);
        this.$emit("form-change");
      },
    },
    subTextFontSizeModel: {
      get() {
        return this.subTextFontSize;
      },
      set(value) {
        this.$emit("update:subTextFontSize", value);
        this.$emit("form-change");
      },
    },
    headlineFontColorModel: {
      get() {
        return this.headlineFontColor;
      },
      set(value) {
        this.$emit("update:headlineFontColor", value);
        this.$emit("form-change");
      },
    },
    // Use fonts from parent component props, fallback to basic fonts if not loaded
    availableFonts() {
      if (this.googleFontsList?.length > 0) {
        return this.googleFontsList;
      }
      return this.getFallbackFonts(); // Fallback to basic system fonts
    },
  },

  methods: {
    openColorPicker() {
      this.tempColor = this.headlineFontColorModel;
      // Generate 25 fresh random colors each time the dialog opens
      this.presetColors = Array.from({ length: 25 }, () =>
        generateRandomColor()
      );
      this.showColorPicker = true;
    },

    updateColor(event) {
      this.tempColor = event.target.value;
    },

    selectPresetColor(color) {
      this.tempColor = color;
      this.$refs.colorPicker.value = color;
    },

    applyColor() {
      this.$emit("update:headlineFontColor", this.tempColor);
      this.showColorPicker = false;
    },

    getFontFamily(fontKey) {
      // Find the font in our options
      const font = this.availableFonts?.find((f) => f.value === fontKey);
      if (font) {
        const fallback = font.category === "serif" ? "serif" : "sans-serif";
        return `"${font.family}", ${fallback}`;
      }

      // Fallback mapping for common fonts
      const fontMap = {
        "gt-walsheim": '"GT Walsheim", sans-serif',
        inter: '"Inter", sans-serif',
        "space-grotesk": '"Space Grotesk", sans-serif',
        roboto: '"Roboto", sans-serif',
        "open-sans": '"Open Sans", sans-serif',
        lato: '"Lato", sans-serif',
        montserrat: '"Montserrat", sans-serif',
        poppins: '"Poppins", sans-serif',
        "source-sans-pro": '"Source Sans Pro", sans-serif',
        nunito: '"Nunito", sans-serif',
        raleway: '"Raleway", sans-serif',
        "work-sans": '"Work Sans", sans-serif',
        "pt-sans": '"PT Sans", sans-serif',
        ubuntu: '"Ubuntu", sans-serif',
        "playfair-display": '"Playfair Display", serif',
        merriweather: '"Merriweather", serif',
        lora: '"Lora", serif',
        georgia: '"Georgia", serif',
        "times-new-roman": '"Times New Roman", serif',
      };
      return fontMap[fontKey] || '"Inter", sans-serif';
    },

    // Fallback fonts when Google Fonts are not available
    getFallbackFonts() {
      return [
        {
          title: "GT Walsheim (Sans-serif)",
          value: "gt-walsheim",
          cssFormat: '"GT Walsheim", sans-serif',
          family: "GT Walsheim",
          category: "sans-serif",
        },
        {
          title: "Roboto (Sans-serif)",
          value: "roboto",
          cssFormat: '"Roboto", sans-serif',
          family: "Roboto",
          category: "sans-serif",
        },
        {
          title: "Inter (Sans-serif)",
          value: "inter",
          cssFormat: '"Inter", sans-serif',
          family: "Inter",
          category: "sans-serif",
        },
        {
          title: "Open Sans (Sans-serif)",
          value: "open-sans",
          cssFormat: '"Open Sans", sans-serif',
          family: "Open Sans",
          category: "sans-serif",
        },
        {
          title: "Georgia (Serif)",
          value: "georgia",
          cssFormat: '"Georgia", serif',
          family: "Georgia",
          category: "serif",
        },
        {
          title: "Times New Roman (Serif)",
          value: "times-new-roman",
          cssFormat: '"Times New Roman", serif',
          family: "Times New Roman",
          category: "serif",
        },
      ];
    },
  },
};
</script>

<style scoped>
/* All styling uses Vuetify classes and inline styles */
</style>
