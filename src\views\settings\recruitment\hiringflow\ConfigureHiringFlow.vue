<template>
  <v-overlay
    :model-value="overlay"
    @click:outside="
      {
      }
    "
    persistent
    class="d-flex justify-end"
  >
    <template v-slot:default="{}">
      <v-card
        class="overlay-card"
        :style="{
          height: windowHeight + 'px',
          width: windowWidth <= 770 ? '100vw' : '50vw',
        }"
      >
        <v-card-title
          class="d-flex bg-primary justify-space-between align-center"
        >
          <div class="text-h6 text-medium ps-2">Configure hiring flow</div>
          <v-btn icon class="clsBtn" variant="text" @click="onCloseOverlay()">
            <v-icon>fas fa-times</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text
          class="card mb-3 px-0 overlay-body"
          :style="{ height: `calc(${windowHeight}px - 130px)` }"
        >
          <div
            v-for="flowItem of originalData"
            :key="flowItem"
            class="pt-4 px-3"
          >
            <v-card
              border="opacity-10 thin"
              class="mx-auto pa-3 bg-white"
              rounded="lg"
              variant="text"
            >
              <div class="text-subtitle-1 font-weight-bold py-2">
                {{ flowItem && flowItem.length && flowItem[0]?.Stage }}
              </div>
              <v-divider></v-divider>
              <div
                v-for="statusList in Object.values(flowItem)"
                :key="statusList"
                class="mt-4 d-flex align-center"
              >
                <!-- <v-hover v-slot="{ isHovering, props }"> -->

                <v-card
                  class="px-3 rounded-md bg-grey-lighten-5"
                  style="width: 100%"
                >
                  <div class="d-flex justify-space-between align-center">
                    <div class="d-flex align-center" style="max-width: 78%">
                      <v-icon color="grey-lighten-2" size="20" class="mr-2"
                        >fas fa-grip-vertical</v-icon
                      >
                      <div class="text-body-1 py-4" style="max-width: 90%">
                        {{ statusList.Status }}
                      </div>
                    </div>
                    <v-expand-transition>
                      <div
                        v-if="statusList.Is_Default === 'No'"
                        class="d-flex transition-fast-in-fast-out v-card--reveal"
                      >
                        <div
                          class="elevation-2 pa-2 rounded-pill d-flex mx-3 cursor-pointer"
                          style="width: fit-content"
                          @click="onChangeEdit(statusList)"
                        >
                          <v-icon color="green" size="15" class="">
                            fas fa-edit
                          </v-icon>
                        </div>
                        <div
                          class="elevation-2 pa-2 rounded-pill d-flex cursor-pointer"
                          style="width: fit-content"
                          @click="onChangeDelete(statusList)"
                        >
                          <v-icon color="red" size="15" class="">
                            fas fa-trash-alt
                          </v-icon>
                        </div>
                      </div>
                    </v-expand-transition>
                  </div>
                </v-card>
                <!-- </v-hover> -->
              </div>
              <div
                v-if="
                  flowItem && flowItem.length && flowItem[0]?.Override === 'Yes'
                "
                @click="enableAdd(flowItem)"
                class="text-blue mt-4 cursor-pointer"
                style="text-decoration: underline"
              >
                Add new
              </div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
      <v-dialog
        v-if="openAddEditModal"
        v-model="openAddEditModal"
        width="600"
        persistent
        @click:outside="
          {
          }
        "
      >
        <v-card class="rounded-lg">
          <v-card-title class="d-flex">
            <span class="text-h6 text-primary font-weight-bold py-2"
              >Add status</span
            >
            <v-spacer></v-spacer>
            <div @click="openAddEditModal = false">
              <v-icon color="primary" size="25">fas fa-times</v-icon>
            </div>
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text>
            <v-card
              border="opacity-10 thin"
              class="mx-auto px-3 pt-2 bg-white"
              rounded="lg"
              variant="text"
            >
              <div class="text-subtitle-1 font-weight-bold py-2">
                {{ onSelectedItem.Stage }}
              </div>
              <v-divider></v-divider>
              <v-form ref="statusRefForm">
                <v-text-field
                  class="pt-3"
                  variant="solo"
                  label=""
                  :rules="[
                    required('Status', selectedStatus),
                    minLengthValidation('Status', selectedStatus, 3),
                    maxLengthValidation('Status', selectedStatus, 50),
                    multilingualNameValidation('Status', selectedStatus),
                  ]"
                  min="0"
                  max="50"
                  v-model="selectedStatus"
                >
                  <template v-slot:label>
                    <span>Status</span>
                    <span style="color: red">*</span>
                  </template></v-text-field
                >
              </v-form>
            </v-card>
            <div class="d-flex justify-end pt-3">
              <v-btn
                rounded="lg"
                variant="outlined"
                class="mr-3 primary"
                @click="openAddEditModal = false"
              >
                Cancel
              </v-btn>
              <v-btn
                rounded="lg"
                class="secondary"
                variant="elevated"
                @click="addEditStatus()"
                :disabled="statusLoader"
              >
                {{ editFlag ? "Update" : "Submit" }}
              </v-btn>
            </div></v-card-text
          ></v-card
        >
      </v-dialog>
      <AppWarningModal
        v-if="openConfirmationPopup"
        :open-modal="openConfirmationPopup"
        confirmation-heading="Are you sure to delete the candidate status?"
        icon-name="fas fa-trash-alt"
        :icon-size="isMobileView ? 30 : 100"
        @close-warning-modal="openConfirmationPopup = false"
        @accept-modal="deleteStatus()"
      >
      </AppWarningModal>
      <AppLoading v-if="statusLoader"></AppLoading>
    </template>
  </v-overlay>
</template>
<script>
import {
  ADD_EDIT_STATUS,
  DELETE_STATUS,
} from "@/graphql/settings/Integration/statusHiringFlowQueries";
import validationRules from "@/mixins/validationRules";

export default {
  name: "ConfigureHiringFlow",
  emits: ["onCloseFlow", "refresh-list"],
  props: {
    addHiringFlow: {
      type: Boolean,
      required: true,
    },
    flowList: {
      type: Object,
      required: true,
    },
  },
  watch: {
    addHiringFlow(val) {
      this.overlay = val;
    },
    flowList(val) {
      this.originalData = val;
    },
  },
  mixins: [validationRules],
  data() {
    return {
      openConfirmationPopup: false,
      onSelectedItem: {},
      onSelectedAddItem: {},
      stageLoader: false,
      statusLoader: false,
      openAddEditModal: false,
      editFlag: false,
      selectedStatus: "",
      originalData: {},
      overlay: false,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    windowHeight() {
      return this.$store.state.windowInnerHeight;
    },
  },
  components: {},
  methods: {
    onCloseOverlay() {
      this.overlay = false;
      this.$emit("onCloseFlow");
    },
    onChangeDelete(item) {
      this.onSelectedItem = item;
      this.openConfirmationPopup = true;
    },
    confirmDeleteAction() {
      this.openConfirmationPopup = false;
    },
    onChangeEdit(item) {
      this.editFlag = true;
      this.openAddEditModal = true;
      this.onSelectedItem = item;
      this.selectedStatus = this.onSelectedItem.Status;
    },
    enableAdd(item) {
      this.editFlag = false;
      this.openAddEditModal = true;
      this.onSelectedItem = item[0];
      this.selectedStatus = "";
    },

    async addEditStatus() {
      const { valid } = await this.$refs.statusRefForm.validate();
      if (valid) {
        this.openAddEditModal = false;
        this.statusLoader = true;
        let requestPayload = {};
        if (this.editFlag) {
          requestPayload = {
            statusId: this.onSelectedItem.Id,
            status: this.selectedStatus,
            formId: this.onSelectedItem.Form_Id,
            stageId: this.onSelectedItem.Stage_Id,
            stage: this.onSelectedItem.Stage,
          };
        } else {
          requestPayload = {
            statusId: 0,
            stageId: this.onSelectedItem.Stage_Id,
            status: this.selectedStatus,
            formId: this.onSelectedItem.Form_Id,
            stage: this.onSelectedItem.Stage,
          };
        }
        this.$apollo
          .query({
            query: ADD_EDIT_STATUS,
            client: "apolloClientAM",
            fetchPolicy: "no-cache",
            variables: requestPayload,
          })
          .then((response) => {
            if (
              response &&
              response.data &&
              response.data.addUpdateStatus &&
              response.data.addUpdateStatus.message
            ) {
              this.openAddEditModal = false;
              this.$emit("refresh-list");
              var snackbarData = {
                isOpen: true,
                type: "success",
                message: response.data.addUpdateStatus.message,
              };
              this.showAlert(snackbarData);
            } else {
              this.statusLoader = false;
            }
            this.statusLoader = false;
          })
          .catch((err) => {
            this.statusLoader = false;
            this.handleDeleteError(err);
          });
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    deleteStatus() {
      this.openConfirmationPopup = false;
      this.stageLoader = true;
      this.$apollo
        .query({
          query: DELETE_STATUS,
          client: "apolloClientAM",
          fetchPolicy: "no-cache",
          variables: {
            statusId: this.onSelectedItem.Id,
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.deleteAtsStatus &&
            response.data.deleteAtsStatus.message
          ) {
            this.$emit("refresh-list");
            this.stageLoader = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: response.data.deleteAtsStatus.message,
            };
            this.showAlert(snackbarData);
          } else {
            this.stageLoader = false;
          }
          this.stageLoader = false;
        })
        .catch((err) => {
          this.stageLoader = false;
          this.handleDeleteError(err);
        });
    },

    handleDeleteError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "deleting",
        form: "Hiring Flow",
        isListError: false,
      });
    },
    handleRetrieveHiringFlow(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "Hiring Flow",
        isListError: false,
      });
    },
    handleUpdateHiringFlowDetailsError(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "Hiring Flow",
        isListError: false,
      });
    },
  },
};
</script>
<style scoped>
.overlay-card {
  overflow-y: auto;
}
.overlay-body {
  overflow-y: scroll !important;
}
</style>
