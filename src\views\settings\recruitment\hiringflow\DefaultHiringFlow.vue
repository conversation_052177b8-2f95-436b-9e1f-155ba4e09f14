<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabList"
      :tab-count="tabCountList"
      :current-tab="currentTabItem"
      :center-tab="true"
      :showBottomSheet="false"
      @tab-clicked="onTabChange($event)"
    >
      <template #topBarContent> </template>
    </AppTopBarTab>
    <v-container fluid class="container">
      <v-window v-model="currentTabItem" id="approvals-tab" v-if="isSuperAdmin">
        <div v-if="stageLoader" class="mt-3">
          <v-skeleton-loader
            ref="skeleton1"
            type="table-heading"
            class="mx-auto"
          ></v-skeleton-loader>
          <div v-for="i in 4" :key="i" class="mt-4">
            <v-skeleton-loader
              ref="skeleton2"
              type="list-item-avatar"
              class="mx-auto"
            ></v-skeleton-loader>
          </div>
        </div>
        <div v-else>
          <v-window-item
            :value="currentTabItem"
            v-if="
              flowList &&
              Object.values(flowList) &&
              Object.values(flowList).length
            "
          >
            <v-card class="rounded-lg mt-10 pa-3 mb-3">
              <v-card-title class="">
                <div class="d-flex justify-space-between flex-wrap">
                  <div class="d-flex mb-2">
                    <div class="text-center font-weight-medium text-wrap">
                      Default Hiring Flow
                    </div>
                    <v-badge color="primary" content="Default" inline></v-badge>
                  </div>
                  <div>
                    <v-btn rounded="lg" color="primary" @click="onEnableEdit()">
                      Edit
                    </v-btn>
                  </div>
                </div>
              </v-card-title>
            </v-card>
            <v-stepper alt-labels class="rounded-lg" color="primary">
              <v-stepper-header
                non-linear
                :class="isMobileView ? 'flex-column' : ''"
              >
                <template
                  v-for="(flowItem, index) in flowList"
                  :key="`${index}-step`"
                >
                  <v-stepper-item
                    style="position: relative"
                    value=""
                    :class="isMobileView ? 'mb-2 w-100' : ''"
                  >
                    <template v-slot:title>
                      {{ flowItem[0].Stage }}
                    </template>
                  </v-stepper-item>
                  <v-divider
                    :key="index"
                    v-if="
                      Object.values(flowList).length !== flowItem[0].Order &&
                      !isMobileView
                    "
                  ></v-divider>
                </template>
              </v-stepper-header>
            </v-stepper>
          </v-window-item>
        </div>
      </v-window>
      <AppAccessDenied v-else></AppAccessDenied>
    </v-container>
    <ConfigureHiringFlow
      @onCloseFlow="onCloseFlow()"
      :addHiringFlow="addHiringFlow"
      :flowList="flowList"
      @refresh-list="getStageList()"
    />
  </div>
</template>
<script>
import { GET_STATUS_LIST } from "@/graphql/settings/Integration/statusHiringFlowQueries";
import ConfigureHiringFlow from "./ConfigureHiringFlow.vue";

export default {
  name: "DefaultHiringFlow",
  data() {
    return {
      tempList: [],
      currentTabItem: "",
      addHiringFlow: false,
      flowList: {},
      stageLoader: false,
    };
  },
  components: {
    ConfigureHiringFlow,
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    mainTabList() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.settingsRecruitmentFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("282");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["view"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    settingsRecruitmentFormAccess() {
      return this.$store.getters.settingsRecruitmentFormAccess;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabList.indexOf("Hiring Flow");
    this.getStageList();
  },
  methods: {
    onEnableEdit() {
      this.addHiringFlow = true;
    },
    onCloseFlow() {
      this.addHiringFlow = false;
    },
    getStageList() {
      this.stageLoader = true;
      this.$apollo
        .query({
          query: GET_STATUS_LIST,
          client: "apolloClientAN",
          fetchPolicy: "no-cache",
          variables: {
            formId: 282,
            conditions: [
              {
                key: "Form_Id",
                value: ["16"],
              },
            ],
          },
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getAtsStatusList &&
            response.data.getAtsStatusList.statusList
          ) {
            let tempData = response.data.getAtsStatusList.statusList;
            const groupedByStage = tempData.reduce((acc, item) => {
              if (!acc[item.Stage]) {
                acc[item.Stage] = [];
              }
              acc[item.Stage].push(item);
              return acc;
            }, {});
            const sortedGroupedByStage = Object.fromEntries(
              Object.entries(groupedByStage).sort(
                ([, a], [, b]) => a[0].Order - b[0].Order
              )
            );
            this.flowList = sortedGroupedByStage;
          } else {
            this.flowList = {};
          }
          this.stageLoader = false;
        })
        .catch((err) => {
          this.stageLoader = false;
          this.handleRetrieveHiringFlow(err);
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleRetrieveHiringFlow(err = "") {
      this.$store.dispatch("handleApiErrors", {
        error: err,
        action: "fetching",
        form: "Hiring Flow",
        isListError: false,
      });
    },
    onTabChange(tab) {
      if (tab !== "Hiring Flow") {
        const { formAccess } = this.settingsRecruitmentFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/recruitment/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/recruitment/" + clickedForm.url;
        }
      }
    },
  },
};
</script>
<style scoped>
.container {
  padding: 5em 2em 0em 3em;
}
@media screen and (max-width: 805px) {
  .container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
