<template>
  <div>
    <!-- Top Bar with Tabs -->
    <AppTopBarTab
      :center-tab="true"
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      @tab-clicked="onTabChange($event)"
    />
    <!-- Main Content -->
    <v-container fluid class="recruitment-settings-container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <!-- Loading State -->
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <!-- Error State -->
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="this.$t('common.retry')"
            @button-click="fetchSettings()"
          />

          <!-- Main Content -->
          <div v-else>
            <v-card class="pa-4 rounded-lg overflow-visible">
              <!-- Action Buttons Row -->
              <v-row no-gutters v-if="formAccess?.update">
                <v-col cols="12" class="d-flex justify-end">
                  <!-- Edit Button -->
                  <v-btn
                    v-if="!isEditMode && formAccess?.update"
                    color="primary"
                    variant="elevated"
                    rounded="lg"
                    class="primary mr-2"
                    @click="enableEditMode()"
                  >
                    {{ this.$t("common.edit") }}
                  </v-btn>
                  <!-- Cancel Button -->
                  <v-btn
                    v-if="isEditMode"
                    color="grey"
                    variant="outlined"
                    rounded="lg"
                    class="mr-2"
                    @click="
                      isFormDirty
                        ? (openConfirmationPopup = true)
                        : cancelEdit()
                    "
                  >
                    {{ this.$t("common.cancel") }}
                  </v-btn>
                  <!-- Save Button -->
                  <v-btn
                    v-if="isEditMode && isFormDirty"
                    color="primary"
                    variant="elevated"
                    rounded="lg"
                    class="primary"
                    @click="validateForm()"
                    >{{ this.$t("common.save") }}</v-btn
                  >
                  <!-- Save Button Tooltip -->
                  <v-tooltip
                    v-else-if="isEditMode"
                    :text="this.$t('settings.thereAreNoChangesToBeUpdated')"
                    location="top"
                  >
                    <template v-slot:activator="{ props }">
                      <v-btn
                        v-bind="props"
                        color="primary"
                        variant="elevated"
                        rounded="lg"
                        class="cursor-not-allow primary"
                      >
                        {{ this.$t("common.save") }}
                      </v-btn>
                    </template>
                  </v-tooltip>
                </v-col>
              </v-row>

              <!-- Edit Mode Form -->
              <v-form
                v-if="isEditMode"
                ref="recruitmentSettingsForm"
                @submit.prevent="validateForm"
              >
                <v-row no-gutters class="pa-4">
                  <!-- General Settings Section Header -->
                  <v-col cols="12">
                    <div class="d-flex align-center my-4">
                      <v-progress-circular
                        model-value="100"
                        color="blue"
                        :size="22"
                        class="mr-2"
                      />
                      <div class="text-h6 text-grey-darken-1 font-weight-bold">
                        <!-- General Settings -->
                        {{ this.$t("settings.generalSettings") }}
                      </div>
                    </div>

                    <v-row>
                      <!-- Job Post Visibility -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Job Post Visibility -->
                          {{ this.$t("settings.jobPostVisibility") }}
                          <v-tooltip
                            :text="
                              fieldForce
                                ? this.$t('settings.jobPostVisibilityTooltip')
                                : this.$t(
                                    'settings.jobPostVisibilityTooltipNoFieldForce'
                                  )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <v-btn-toggle
                          ref="coverageField"
                          v-model="formData.coverage"
                          color="primary"
                          mandatory
                          rounded="lg"
                          variant="outlined"
                          @update:model-value="
                            onChangeFields($event, 'coverage')
                          "
                        >
                          <v-btn
                            value="Organization"
                            size="small"
                            class="text-none"
                          >
                            <!-- Organization -->
                            {{ this.$t("settings.organization") }}
                          </v-btn>
                          <v-btn
                            value="Custom Group"
                            size="small"
                            class="text-none"
                          >
                            <!-- Custom Group -->
                            {{ this.$t("settings.customGroup") }}
                          </v-btn>
                        </v-btn-toggle>
                      </v-col>

                      <!-- Centralised Recruitment -->
                      <v-col
                        v-if="fieldForce"
                        cols="12"
                        sm="6"
                        md="4"
                        class="px-md-4 pb-0 my-3"
                      >
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Centralised Recruitment -->
                          {{ this.$t("settings.centralisedRecruitment") }}
                          <v-tooltip
                            :text="
                              this.$t('settings.centralisedRecruitmentTooltip')
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref="centralisedRecruitmentField"
                            v-model="formData.centralised_recruitment"
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields($event, 'centralised_recruitment')
                            "
                          />
                        </div>
                      </v-col>

                      <!-- Allow to modify the interview rounds after job post creation -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Allow to modify the interview rounds after the job post creation -->
                          {{ this.$t("settings.allowModifyInterviewRounds") }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowModifyInterviewRoundsTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref="showJobRoundsTabField"
                            v-model="formData.show_job_rounds_tab"
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields($event, 'show_job_rounds_tab')
                            "
                          />
                        </div>
                      </v-col>
                      <!-- Enable Recently Introduced Features -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Allow to modify the interview rounds after the job post creation -->
                          {{ this.$t("settings.enableNewFeatures") }}
                          <v-tooltip
                            :text="this.$t('settings.enableNewFeaturesTooltip')"
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref=""
                            v-model="formData.Enable_New_Features"
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields($event, 'Enable_New_Features')
                            "
                          />
                        </div>
                      </v-col>

                      <!-- Equal opportunity statement to present in the candidate application form -->
                      <v-col cols="12" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Equal opportunity statement to present in the candidate application form -->
                          {{ this.$t("settings.equalOpportunityStatement") }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.equalOpportunityStatementTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <v-textarea
                          ref="equalOpportunityStmtField"
                          v-model="formData.equal_opportunity_stmt"
                          variant="solo"
                          density="compact"
                          rows="4"
                          counter="1000"
                          :rules="[
                            minLengthValidation(
                              this.$t('settings.equalOpportunityStatement'),
                              formData.equal_opportunity_stmt,
                              10
                            ),
                            maxLengthValidation(
                              this.$t('settings.equalOpportunityStatement'),
                              formData.equal_opportunity_stmt,
                              1000
                            ),
                          ]"
                          @update:model-value="
                            onChangeFields($event, 'equal_opportunity_stmt')
                          "
                        />
                      </v-col>
                    </v-row>
                  </v-col>

                  <!-- Integration Settings Section Header -->
                  <v-col cols="12">
                    <div class="d-flex align-center my-4">
                      <v-progress-circular
                        model-value="100"
                        color="red"
                        :size="22"
                        class="mr-2"
                      />
                      <div class="text-h6 text-grey-darken-1 font-weight-bold">
                        <!-- Integration Settings -->
                        {{ this.$t("settings.integrationSettings") }}
                      </div>
                    </div>
                    <v-row>
                      <!-- Enable MPP Integration -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Enable MPP Integration -->
                          {{ this.$t("settings.enableMppIntegration") }}
                          <v-tooltip
                            :text="
                              this.$t('settings.enableMppIntegrationTooltip')
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref="mppIntegrationField"
                            v-model="formData.mpp_integration"
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields($event, 'mpp_integration')
                            "
                          />
                        </div>
                      </v-col>

                      <!-- Allow editing job titles -->
                      <v-col
                        v-if="formData.mpp_integration?.toLowerCase() === 'yes'"
                        cols="12"
                        sm="6"
                        md="4"
                        class="px-md-4 pb-0 my-3"
                      >
                        <div class="text-body-2 font-weight-medium mb-2">
                          {{ this.$t("settings.allowEditingJobTitles") }}
                          <v-tooltip
                            :text="
                              this.$t('settings.allowEditingJobTitlesTooltip')
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref="allowEditingJobTitlesField"
                            v-model="formData.job_Title_Editable_Access"
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields(
                                $event,
                                'job_Title_Editable_Access'
                              )
                            "
                          />
                        </div>
                      </v-col>

                      <!-- Automatically send the signed document -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Automatically send the signed document -->
                          {{
                            this.$t("settings.automaticallySendSignedDocument")
                          }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.automaticallySendSignedDocumentTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref="autoSendSignedDocumentField"
                            v-model="formData.auto_send_signed_document"
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields(
                                $event,
                                'auto_send_signed_document'
                              )
                            "
                          />
                        </div>
                      </v-col>

                      <!-- Tenant Unique Key -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Tenant Unique Key -->
                          {{ this.$t("settings.tenantUniqueKey") }}
                          <span
                            v-if="entomoIntegrationEnabled"
                            style="color: red"
                            >*</span
                          >
                          <v-tooltip
                            :text="this.$t('settings.tenantUniqueKeyTooltip')"
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <!-- Secure Tenant Key Input -->
                        <v-text-field
                          ref="tenantKeyField"
                          v-model="formData.tenant_unique_key"
                          variant="solo"
                          density="compact"
                          :placeholder="
                            this.$t('settings.enterTenantUniqueKey')
                          "
                          :rules="[
                            entomoIntegrationEnabled
                              ? required(
                                  this.$t('settings.tenantUniqueKey'),
                                  formData.tenant_unique_key
                                )
                              : true,
                            maxLengthValidation(
                              this.$t('settings.tenantUniqueKey'),
                              formData.tenant_unique_key,
                              100
                            ),
                          ]"
                          @focus="handleTenantKeyFocus"
                          @click="handleTenantKeyClick"
                          @update:model-value="
                            onChangeFields($event, 'tenant_unique_key')
                          "
                        />
                      </v-col>
                    </v-row>
                  </v-col>

                  <!-- Candidate Experience Portal Configuration Section Header -->
                  <v-col cols="12">
                    <div class="d-flex align-center my-4">
                      <v-progress-circular
                        model-value="100"
                        color="green"
                        :size="22"
                        class="mr-2"
                      />
                      <div class="text-h6 text-grey-darken-1 font-weight-bold">
                        <!-- Candidate Experience Portal Configuration -->
                        {{
                          this.$t(
                            "settings.candidateExperiencePortalConfiguration"
                          )
                        }}
                      </div>
                    </div>
                    <v-row>
                      <!-- Allow Candidate Portal Access for New Applicant -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Allow Candidate Portal Access for New Applicant -->
                          {{
                            this.$t(
                              "settings.allowCandidatePortalAccessNewApplicant"
                            )
                          }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowCandidatePortalAccessNewApplicantTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref="candidatePortalLoginField"
                            v-model="formData.candidate_portal_login"
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields($event, 'candidate_portal_login')
                            "
                          />
                        </div>
                      </v-col>

                      <!-- Allow Blacklisted Candidate Portal Access -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Allow Blacklisted Candidate Portal Access -->
                          {{
                            this.$t(
                              "settings.allowBlacklistedCandidatePortalAccess"
                            )
                          }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowBlacklistedCandidatePortalAccessTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref="blacklistedCandidatePortalAccessField"
                            v-model="
                              formData.blacklisted_candidate_portal_access
                            "
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields(
                                $event,
                                'blacklisted_candidate_portal_access'
                              )
                            "
                          />
                        </div>
                      </v-col>

                      <!-- Allow Archived Candidate Portal Access -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Allow Archived Candidate Portal Access -->
                          {{
                            this.$t(
                              "settings.allowArchivedCandidatePortalAccess"
                            )
                          }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowArchivedCandidatePortalAccessTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref="archivedCandidatePortalAccessField"
                            v-model="formData.archived_candidate_portal_access"
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields(
                                $event,
                                'archived_candidate_portal_access'
                              )
                            "
                          />
                        </div>
                      </v-col>

                      <!-- OTP Expiry Duration (conditional) -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- OTP Expiry Duration (in minutes) -->
                          {{ this.$t("settings.otpExpiryDuration") }}
                          <span style="color: red">*</span>
                          <v-tooltip
                            :text="this.$t('settings.otpExpiryDurationTooltip')"
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <v-text-field
                          ref="expPortalExpiryTimeField"
                          v-model="formData.exp_portal_expiry_time"
                          type="number"
                          variant="solo"
                          density="compact"
                          :rules="[
                            required(
                              this.$t('settings.otpExpiryDuration'),
                              formData.exp_portal_expiry_time
                            ),
                            formData.exp_portal_expiry_time
                              ? minMaxNumberValidation(
                                  this.$t('settings.otpExpiryDuration'),
                                  formData.exp_portal_expiry_time,
                                  5,
                                  60
                                )
                              : true,
                          ]"
                          @update:model-value="
                            onChangeFields($event, 'exp_portal_expiry_time')
                          "
                        />
                      </v-col>

                      <!-- Candidate Application Form Type -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Candidate Application Form Type -->
                          {{ this.$t("settings.candidateApplicationFormType") }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.candidateApplicationFormTypeTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <v-btn-toggle
                          ref="candidateApplicationFormTypeField"
                          v-model="formData.candidate_application_form_type"
                          color="primary"
                          variant="outlined"
                          mandatory
                          rounded="lg"
                          @update:model-value="
                            onChangeFields(
                              $event,
                              'candidate_application_form_type'
                            )
                          "
                        >
                          <v-btn value="Simple" size="small" class="text-none">
                            Simple
                          </v-btn>
                          <v-btn
                            value="Detailed"
                            size="small"
                            class="text-none"
                          >
                            Detailed
                          </v-btn>
                        </v-btn-toggle>
                      </v-col>

                      <!-- Allow candidate to apply for multiple jobs -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Allow candidate to apply for multiple jobs -->
                          {{ this.$t("settings.allowCandidateMultipleJobs") }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowCandidateMultipleJobsTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center">
                          <v-switch
                            ref="enableMultipleApplicationField"
                            v-model="formData.enable_multiple_application"
                            true-value="Yes"
                            false-value="No"
                            density="compact"
                            color="primary"
                            hide-details
                            @update:model-value="
                              onChangeFields(
                                $event,
                                'enable_multiple_application'
                              )
                            "
                          />
                        </div>
                      </v-col>

                      <!-- Career Portal Filters -->
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-body-2 font-weight-medium mb-2">
                          <!-- Career Portal Filters -->
                          {{ this.$t("settings.careerPortalFilters") }}
                          <v-tooltip
                            :text="
                              this.$t('settings.careerPortalFiltersTooltip')
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <CustomSelect
                          ref="careerPortalFiltersField"
                          v-model="formData.career_portal_filters"
                          :items="careerPortalFilterOptions"
                          :itemSelected="formData.career_portal_filters"
                          :isAutoComplete="true"
                          :select-properties="{
                            clearable: true,
                            closableChips: true,
                            multiple: true,
                            chips: true,
                          }"
                          item-title="text"
                          item-value="value"
                          variant="solo"
                          density="compact"
                          @update:modelValue="
                            onChangeFields($event, 'career_portal_filters')
                          "
                        />
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-form>

              <!-- View Mode -->
              <div v-else>
                <v-row no-gutters class="pa-4">
                  <!-- General Settings Section Header -->
                  <v-col cols="12" class="mb-4 px-2 bg-grey-lighten-5">
                    <div class="d-flex align-center my-4">
                      <v-progress-circular
                        model-value="100"
                        color="blue"
                        :size="22"
                        class="mr-2"
                      />
                      <div class="text-h6 text-grey-darken-1 font-weight-bold">
                        <!-- General Settings -->
                        {{ this.$t("settings.generalSettings") }}
                      </div>
                    </div>

                    <v-row>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Job Post Visibility -->
                          {{ this.$t("settings.jobPostVisibility") }}
                          <v-tooltip
                            :text="
                              fieldForce
                                ? this.$t('settings.jobPostVisibilityTooltip')
                                : this.$t(
                                    'settings.jobPostVisibilityTooltipNoFieldForce'
                                  )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{ checkNullValue(itemData.coverage) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="fieldForce"
                        cols="12"
                        sm="6"
                        md="4"
                        class="px-md-4 pb-0 my-3"
                      >
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Centralised Recruitment -->
                          {{ this.$t("settings.centralisedRecruitment") }}
                          <v-tooltip
                            :text="
                              this.$t('settings.centralisedRecruitmentTooltip')
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(itemData.centralised_recruitment)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Allow to modify the interview rounds after the job post creation -->
                          {{ this.$t("settings.allowModifyInterviewRounds") }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowModifyInterviewRoundsTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{ checkNullValue(itemData.show_job_rounds_tab) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Enable Recently Introduced Features -->
                          {{ this.$t("settings.enableNewFeatures") }}
                          <v-tooltip
                            :text="this.$t('settings.enableNewFeaturesTooltip')"
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{ checkNullValue(itemData.Enable_New_Features) }}
                          </section>
                        </div>
                      </v-col>

                      <v-col cols="12" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Equal opportunity statement to present in the candidate application form -->
                          {{ this.$t("settings.equalOpportunityStatement") }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.equalOpportunityStatementTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(itemData.equal_opportunity_stmt)
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                  <!-- Integration Settings Section Header -->
                  <v-col cols="12" class="mb-4 px-2 bg-grey-lighten-5">
                    <div class="d-flex align-center my-4">
                      <v-progress-circular
                        model-value="100"
                        color="red"
                        :size="22"
                        class="mr-2"
                      />
                      <div class="text-h6 text-grey-darken-1 font-weight-bold">
                        <!-- Integration Settings -->
                        {{ this.$t("settings.integrationSettings") }}
                      </div>
                    </div>
                    <v-row>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Enable MPP Integration -->
                          {{ this.$t("settings.enableMppIntegration") }}
                          <v-tooltip
                            :text="
                              this.$t('settings.enableMppIntegrationTooltip')
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{ checkNullValue(itemData.mpp_integration) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="itemData.mpp_integration?.toLowerCase() === 'yes'"
                        cols="12"
                        sm="6"
                        md="4"
                        class="px-md-4 pb-0 my-3"
                      >
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Allow editing Job Titles -->
                          {{ this.$t("settings.allowEditingJobTitles") }}
                          <v-tooltip
                            :text="
                              this.$t('settings.allowEditingJobTitlesTooltip')
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(itemData.job_Title_Editable_Access)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Automatically send the signed document -->
                          {{
                            this.$t("settings.automaticallySendSignedDocument")
                          }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.automaticallySendSignedDocumentTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(itemData.auto_send_signed_document)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Tenant Unique Key -->
                          {{ this.$t("settings.tenantUniqueKey") }}
                          <v-tooltip
                            :text="this.$t('settings.tenantUniqueKeyTooltip')"
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              itemData.tenant_unique_key &&
                              itemData.tenant_unique_key.trim() !== ""
                                ? "****"
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                  <!-- Candidate Experience Portal Configuration Section Header -->
                  <v-col cols="12" class="mb-4 px-2 bg-grey-lighten-5">
                    <div class="d-flex align-center my-4">
                      <v-progress-circular
                        model-value="100"
                        color="green"
                        :size="22"
                        class="mr-2"
                      />
                      <div class="text-h6 text-grey-darken-1 font-weight-bold">
                        <!-- Candidate Experience Portal Configuration -->
                        {{
                          this.$t(
                            "settings.candidateExperiencePortalConfiguration"
                          )
                        }}
                      </div>
                    </div>
                    <v-row>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Allow Candidate Portal Access for New Applicant -->
                          {{
                            this.$t(
                              "settings.allowCandidatePortalAccessNewApplicant"
                            )
                          }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowCandidatePortalAccessNewApplicantTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(itemData.candidate_portal_login)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Allow Blacklisted Candidate Portal Access -->
                          {{
                            this.$t(
                              "settings.allowBlacklistedCandidatePortalAccess"
                            )
                          }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowBlacklistedCandidatePortalAccessTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                itemData.blacklisted_candidate_portal_access
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Allow Archived Candidate Portal Access -->
                          {{
                            this.$t(
                              "settings.allowArchivedCandidatePortalAccess"
                            )
                          }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowArchivedCandidatePortalAccessTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                itemData.archived_candidate_portal_access
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- OTP Expiry Duration (in minutes) -->
                          {{ this.$t("settings.otpExpiryDuration") }}
                          <v-tooltip
                            :text="this.$t('settings.otpExpiryDurationTooltip')"
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(itemData.exp_portal_expiry_time)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Candidate Application Form Type -->
                          {{ this.$t("settings.candidateApplicationFormType") }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.candidateApplicationFormTypeTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                itemData.candidate_application_form_type
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Allow candidate to apply for multiple jobs -->
                          {{ this.$t("settings.allowCandidateMultipleJobs") }}
                          <v-tooltip
                            :text="
                              this.$t(
                                'settings.allowCandidateMultipleJobsTooltip'
                              )
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                itemData.enable_multiple_application
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" md="4" class="px-md-4 pb-0 my-3">
                        <div class="text-subtitle-1 text-grey-darken-1">
                          <!-- Career Portal Filters -->
                          {{ this.$t("settings.careerPortalFilters") }}
                          <v-tooltip
                            :text="
                              this.$t('settings.careerPortalFiltersTooltip')
                            "
                            location="top"
                            max-width="300px"
                          >
                            <template v-slot:activator="{ props }">
                              <v-icon
                                v-bind="props"
                                size="x-small"
                                class="ml-1"
                                color="blue"
                                >fas fa-info-circle</v-icon
                              >
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="text-subtitle-1 font-weight-regular">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                formatFilters(itemData.career_portal_filters)
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </div>
            </v-card>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
    <AppLoading v-if="isLoading" />
    <AppWarningModal
      v-if="openConfirmationPopup"
      :open-modal="openConfirmationPopup"
      :confirmation-heading="this.$t('settings.areYouSureToExitThisForm')"
      imgUrl="common/exit_form"
      @close-warning-modal="openConfirmationPopup = false"
      @accept-modal="cancelEdit()"
    />
  </div>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import {
  RETRIEVE_RECRUITMENT_SETTINGS,
  ADD_UPDATE_RECRUITMENT_SETTINGS,
} from "@/graphql/settings/careerConfigurationQueries.js";
import { checkNullValue } from "@/helper";
export default {
  name: "RecruitmentSettings",
  mixins: [validationRules],
  components: {
    CustomSelect,
  },
  data() {
    return {
      currentTabItem: "",
      isErrorInList: false,
      errorContent: "",
      listLoading: false,
      isLoading: false,
      isFormDirty: false,
      openConfirmationPopup: false,
      isEditMode: false,
      formData: {},
      itemData: {
        settingId: null,
        coverage: "Organization",
        centralised_recruitment: "No",
        equal_opportunity_stmt: "",
        mpp_integration: "No",
        tenant_unique_key: "",
        auto_send_signed_document: "No",
        job_Title_Editable_Access: "No",
        candidate_portal_login: "No",
        exp_portal_expiry_time: 60,
        candidate_application_form_type: "Detailed",
        enable_multiple_application: "Yes",
        blacklisted_candidate_portal_access: "No",
        archived_candidate_portal_access: "Yes",
        career_portal_filters: [],
        show_job_rounds_tab: "No",
        Enable_New_Features: "No",
      },
      originalData: {},
      isTenantKeyCleared: false, // Track if tenant key has been cleared for editing
    };
  },
  computed: {
    settingsRecruitmentFormAccess() {
      return this.$store.getters.settingsRecruitmentFormAccess;
    },
    mainTabs() {
      const { isAnyOneFormHaveAccess, formAccess } =
        this.settingsRecruitmentFormAccess;
      if (isAnyOneFormHaveAccess) {
        let formAccessArray = [];
        for (let access in formAccess) {
          if (
            formAccess[access].havingAccess ||
            access === this.landedFormName
          ) {
            formAccessArray.push(access);
          }
        }
        return formAccessArray;
      }
      return [];
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    entomoIntegrationEnabled() {
      return this.$store.getters.entomoIntegrationEnabled;
    },
    hasExistingTenantKey() {
      return (
        this.itemData.tenant_unique_key &&
        this.itemData.tenant_unique_key.trim() !== ""
      );
    },

    landedFormName() {
      let recruitmentForm = this.accessRights("372");
      if (
        recruitmentForm?.customFormName &&
        recruitmentForm.customFormName !== ""
      ) {
        return recruitmentForm.customFormName;
      } else return this.$t("common.general");
    },
    isMobileView() {
      return this.$store?.state?.isMobileWindowSize || false;
    },
    formatFilters() {
      return (filters) => {
        if (!filters || !Array.isArray(filters)) return "";
        return filters
          .map((filter) => {
            const option = this.careerPortalFilterOptions.find(
              (opt) => opt.value === filter
            );
            return option ? option.text : filter;
          })
          .join(", ");
      };
    },
    formAccess() {
      try {
        const formAccessRights = this.accessRights("372");
        return formAccessRights?.accessRights?.view &&
          formAccessRights?.accessRights?.admin === "admin"
          ? formAccessRights.accessRights
          : false;
      } catch (error) {
        return false;
      }
    },

    careerPortalFilterOptions() {
      return [
        { text: "Department", value: "department" },
        { text: "Location", value: "location" },
        {
          text: this.labelList["115"]?.Field_Alias || `Service Provider`,
          value: "serviceProvider",
        },
      ];
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    domainName() {
      return this.$store?.getters?.domain || "";
    },
    orgCode() {
      try {
        const org_code = localStorage.getItem("orgCode");
        return org_code || this.$store?.getters?.orgCode || "";
      } catch {
        return "";
      }
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    if (this.formAccess) {
      this.fetchSettings();
    }
  },
  methods: {
    checkNullValue,
    handleTenantKeyFocus() {
      if (this.hasExistingTenantKey && !this.isTenantKeyCleared) {
        this.isTenantKeyCleared = true;
        this.formData.tenant_unique_key = "";
      }
    },
    handleTenantKeyClick() {
      if (this.hasExistingTenantKey && !this.isTenantKeyCleared) {
        this.isTenantKeyCleared = true;
        this.formData.tenant_unique_key = "";
      }
    },
    getActualTenantKeyValue() {
      // If the key hasn't been cleared and we're showing masked value, return original key
      if (
        this.hasExistingTenantKey &&
        !this.isTenantKeyCleared &&
        this.formData.tenant_unique_key === "****"
      ) {
        return this.itemData.tenant_unique_key;
      }
      // Otherwise return the current form value (new key or empty)
      return this.formData.tenant_unique_key;
    },
    fetchSettings() {
      this.listLoading = true;
      this.errorContent = "";
      this.isErrorInList = false;

      this.$apollo
        .query({
          query: RETRIEVE_RECRUITMENT_SETTINGS,
          client: "apolloClientA",
          variables: {
            formId: 372, // Recruitment Settings form ID
          },
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data?.recruitmentSetting?.settingResult?.length) {
            const settings = response.data.recruitmentSetting.settingResult[0];
            this.itemData = {
              settingId: settings.Setting_Id,
              coverage: settings.Coverage || "Organization",
              centralised_recruitment: settings.Centralised_Recruitment || "No",
              equal_opportunity_stmt: settings.Equal_opportunity_stmt || "",
              mpp_integration: settings.MPP_Integration || "No",
              tenant_unique_key: settings.Tenant_Unique_Key || "",
              auto_send_signed_document:
                settings.auto_send_signed_document || "No",
              job_Title_Editable_Access:
                settings.Job_Title_Editable_Access || "No",
              candidate_portal_login:
                settings.Candidate_Portal_Login_Access || "No",
              exp_portal_expiry_time:
                settings.Exp_portal_Expiry_Time_In_Mins || 60,
              candidate_application_form_type:
                settings.Candidate_Application_Form_Type || "Detailed",
              enable_multiple_application:
                settings.Enable_Multiple_Application || "Yes",
              blacklisted_candidate_portal_access:
                settings.Blacklisted_Candidate_Portal_Access || "No",
              archived_candidate_portal_access:
                settings.Archived_Candidate_Portal_Access || "Yes",
              career_portal_filters: this.parseCareerPortalFilters(
                settings.Career_Portal_Filters
              ),
              show_job_rounds_tab: settings.Show_Job_Rounds_Tab || "No",
              Enable_New_Features: settings.Enable_New_Features || "No",
            };
          } else {
            // No existing settings, use defaults
            this.itemData = { ...this.itemData };
          }
          this.originalData = { ...this.itemData };
          this.listLoading = false;
        })
        .catch((error) => {
          this.listLoading = false;
          this.handleListError(error);
        });
    },
    parseCareerPortalFilters(filtersString) {
      if (!filtersString) return [];
      try {
        // Handle potential double-encoding
        let parsedData = filtersString;

        // If it's a string, try to parse it
        if (typeof parsedData === "string") {
          parsedData = JSON.parse(parsedData);
        }

        // If it's still a string after first parse, parse again (double-encoded)
        if (typeof parsedData === "string") {
          parsedData = JSON.parse(parsedData);
        }
        let selectedFilters = [];

        // Check if it's an object now
        if (typeof parsedData === "object" && parsedData !== null) {
          if (parsedData.department) selectedFilters.push("department");
          if (parsedData.location) selectedFilters.push("location");
          if (parsedData.serviceProvider)
            selectedFilters.push("serviceProvider");
        }

        return selectedFilters;
      } catch (error) {
        return [];
      }
    },

    handleListError(error) {
      this.$store
        .dispatch("handleApiErrors", {
          error: error,
          action: this.$t("common.retrieving") || "retrieving",
          form: this.landedFormName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        })
        .catch(() => {
          // Fallback error handling
          this.errorContent = this.$t(
            "settings.failedToLoadRecruitmentSettings"
          );
          this.isErrorInList = true;
        });
    },

    enableEditMode() {
      this.isEditMode = true;
      this.formData = { ...this.itemData };
      this.isTenantKeyCleared = false; // Reset tenant key state

      // Set masked value for tenant key if it exists
      if (this.hasExistingTenantKey) {
        this.formData.tenant_unique_key = "****";
      }
    },

    cancelEdit() {
      this.openConfirmationPopup = false;
      this.isFormDirty = false;
      this.isEditMode = false;
      this.formData = {};
      this.isTenantKeyCleared = false; // Reset tenant key state
    },

    async addUpdateRecruitmentSettings() {
      // Prevent duplicate save operations
      if (this.isLoading) return;

      this.isLoading = true;
      try {
        // Convert career portal filters to JSON string
        const careerPortalFiltersJson = this.convertFiltersToJson(
          this.formData.career_portal_filters
        );

        const response = await this.$apollo.mutate({
          mutation: ADD_UPDATE_RECRUITMENT_SETTINGS,
          variables: {
            coverage: this.formData.coverage,
            centralisedRecruitment: this.formData.centralised_recruitment,
            equalOpportunityStatement: this.formData.equal_opportunity_stmt,
            autoSendSignedDocument: this.formData.auto_send_signed_document,
            jobTitleEditableAccess:
              this.formData.mpp_integration?.toLowerCase() === "yes"
                ? this.formData.job_Title_Editable_Access
                : "No",
            mppIntegration: this.formData.mpp_integration,
            tenantUniqueKey: this.getActualTenantKeyValue(),
            candidatePortalLoginAccess: this.formData.candidate_portal_login,
            portalExpiryTimeInMins: parseInt(
              this.formData.exp_portal_expiry_time
            ),
            candidateApplicationFormType:
              this.formData.candidate_application_form_type,
            enableMultipleApplication:
              this.formData.enable_multiple_application,
            blacklistedCandidatePortalAccess:
              this.formData.blacklisted_candidate_portal_access,
            archivedCandidatePortalAccess:
              this.formData.archived_candidate_portal_access,
            careerPortalFilters: careerPortalFiltersJson,
            showJobRoundsTab: this.formData.show_job_rounds_tab,
            enableNewFeatures: this.formData.Enable_New_Features,
          },
          client: "apolloClientAM",
        });

        if (!response?.data?.addUpdateRecruitmentSetting?.errorCode) {
          this.isFormDirty = false;
          this.isEditMode = false;

          this.showAlert({
            isOpen: true,
            message:
              response?.data?.addUpdateRecruitmentSetting?.message ||
              `${this.landedFormName} updated successfully.`,
            type: "success",
          });
          this.fetchSettings();
        } else {
          this.handleAppUpdateErrors(
            response.data?.addUpdateRecruitmentSetting?.errorCode || ""
          );
        }
      } catch (error) {
        this.handleAppUpdateErrors(error);
      } finally {
        this.isLoading = false;
      }
    },

    convertFiltersToJson(filters) {
      if (!filters || !Array.isArray(filters)) return "{}";

      const filterObj = {
        department: filters.includes("department"),
        location: filters.includes("location"),
        serviceProvider: filters.includes("serviceProvider"),
      };

      return JSON.stringify(filterObj);
    },
    handleAppUpdateErrors(error) {
      this.$store
        .dispatch("handleApiErrors", {
          error,
          action: this.$t("common.updating") || "updating",
          form: this.landedFormName,
          isListError: false,
        })
        .catch((handleError) => {
          // Fallback error handling
          this.showAlert({
            isOpen: true,
            message: handleError || this.$t("settings.failedToSaveSettings"),
            type: "warning",
          });
        });
    },

    async validateForm() {
      // Use v-form ref validation instead of manual validation
      const { valid } = await this.$refs.recruitmentSettingsForm.validate();

      if (valid) {
        this.addUpdateRecruitmentSettings();
      }
    },

    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },

    onTabChange(tab) {
      if (tab !== this.landedFormName) {
        const { formAccess } = this.settingsRecruitmentFormAccess;
        let clickedForm = formAccess[tab];
        if (clickedForm.isVue3) {
          this.$router.push("/settings/recruitment/" + clickedForm.url);
        } else {
          window.location.href =
            this.baseUrl + "in/settings/recruitment/" + clickedForm.url;
        }
      }
    },

    // Form change tracking
    onChangeFields(value, fieldName) {
      this.isFormDirty = true;
      // Additional field-specific logic can be added here if needed
      if (fieldName === "career_portal_filters") {
        // Handle multi-select changes
        this.formData[fieldName] = value || [];
      }
    },
  },
};
</script>

<style scoped>
.recruitment-settings-container {
  padding: 5em 2em 0em 3em;
}
@media screen and (max-width: 805px) {
  .recruitment-settings-container {
    padding: 4em 1em 0em 1em;
  }
}
</style>
