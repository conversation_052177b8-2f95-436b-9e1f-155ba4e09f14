<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>

    <v-container fluid class="form24q-container">
      <v-window
        v-if="
          formAccess?.view &&
          (getFormAccess(149, 'update') ||
            getFormAccess(219, 'update') ||
            getFormAccess(147, 'update') ||
            getFormAccess(22, 'update'))
        "
        v-model="currentTabItem"
      >
        <v-window-item :value="currentTabItem">
          <!-- Loading State -->
          <div v-if="listLoading" class="mt-3">
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>

          <!-- Error State -->
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="t('common.retry')"
            @button-click="refetchList()"
          >
          </AppFetchErrorScreen>

          <AppFetchErrorScreen
            v-else-if="originalList.length === 0"
            key="no-results-screen"
            main-title=""
            :isSmallImage="true"
            image-name=""
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <CustomSelect
                      :item-selected="selectedAssessmentYear"
                      :items="assessmentYearsList"
                      :label="t('taxStatutory.assessmentYear')"
                      variant="solo"
                      density="comfortable"
                      hide-details
                      class="mr-2"
                      style="min-width: 150px"
                      @selected-item="
                        onChangeFilter($event, 'selectedAssessmentYear')
                      "
                    ></CustomSelect>
                    <CustomSelect
                      v-if="
                        fieldForce &&
                        (getFormAccess(147, 'update') ||
                          getFormAccess(22, 'update'))
                      "
                      :item-selected="selectedServiceProvider"
                      :items="serviceProviderList"
                      item-title="Service_Provider_Name"
                      item-value="Service_Provider_Id"
                      :label="
                        labelList[115]?.Field_Alias ||
                        t('common.serviceProvider')
                      "
                      variant="solo"
                      density="comfortable"
                      hide-details
                      style="min-width: 150px"
                      @selected-item="
                        onChangeFilter($event, 'selectedServiceProvider')
                      "
                    ></CustomSelect>
                    <v-btn
                      rounded="lg"
                      class="mt-1"
                      color="transparent"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>

          <!-- Main Content -->
          <div v-else class="my-3">
            <div
              class="d-flex justify-space-between align-center"
              :class="windowWidth < 600 ? 'flex-column' : ''"
            >
              <div
                class="d-flex align-center"
                :class="windowWidth < 400 ? 'flex-column' : ''"
              >
                <CustomSelect
                  :item-selected="selectedAssessmentYear"
                  :items="assessmentYearsList"
                  :label="t('taxStatutory.assessmentYear')"
                  variant="solo"
                  density="comfortable"
                  hide-details
                  class="mr-2"
                  :class="windowWidth < 400 ? 'mb-4' : ''"
                  :style="{
                    minWidth: windowWidth < 400 ? '100%' : '150px',
                    maxWidth: '250px',
                  }"
                  @selected-item="
                    onChangeFilter($event, 'selectedAssessmentYear')
                  "
                ></CustomSelect>
                <CustomSelect
                  v-if="
                    fieldForce &&
                    (getFormAccess(147, 'update') ||
                      getFormAccess(22, 'update'))
                  "
                  :item-selected="selectedServiceProvider"
                  :items="serviceProviderList"
                  item-title="Service_Provider_Name"
                  item-value="Service_Provider_Id"
                  :label="
                    labelList[115]?.Field_Alias || t('common.serviceProvider')
                  "
                  variant="solo"
                  density="comfortable"
                  hide-details
                  :class="windowWidth < 400 ? 'mb-4' : ''"
                  :style="{
                    minWidth: windowWidth < 400 ? '100%' : '150px',
                    maxWidth: '250px',
                  }"
                  @selected-item="
                    onChangeFilter($event, 'selectedServiceProvider')
                  "
                ></CustomSelect>
              </div>
              <div>
                <v-btn
                  rounded="lg"
                  class="mt-1"
                  color="transparent"
                  variant="flat"
                  :size="isMobileView ? 'small' : 'default'"
                  @click="refetchList()"
                >
                  <v-icon>fas fa-redo-alt</v-icon>
                </v-btn>
              </div>
            </div>
            <v-card
              v-for="(item, index) in originalList"
              :key="index"
              rounded="lg"
              class="mt-3 d-flex"
            >
              <div
                :style="{
                  backgroundColor: getRandomColor(),
                  minHeight: '100%',
                  width: '5px',
                }"
              ></div>
              <div class="pa-3" style="width: calc(100% - 5px)">
                <v-row>
                  <v-col cols="12" class="d-flex align-center">
                    <div class="text-h6 font-weight-bold">
                      {{ t("taxStatutory.form24q") }}
                    </div>
                    <div class="ml-4">
                      <v-chip
                        :color="getStatusColor(item.Status)"
                        label
                        size="small"
                        variant="tonal"
                        class="text-white"
                        >{{ item.Status || "Pending" }}</v-chip
                      >
                    </div>
                  </v-col>
                  <v-col cols="12" sm="4" class="">
                    <div class="text-subtitle-1 text-grey-darken-1">
                      {{ t("taxStatutory.dueDate") }}
                    </div>
                    <div class="text-subtitle-1 font-weight-medium">
                      {{ formatDate(item.Due_Date) }}
                    </div>
                  </v-col>
                  <v-col cols="12" sm="4" class="">
                    <div class="text-subtitle-1 text-grey-darken-1">
                      {{ t("taxStatutory.depositPeriod") }}
                    </div>
                    <div class="text-subtitle-1 font-weight-medium">
                      {{ checkNullValue(item.Deposit_Period) }}
                    </div>
                  </v-col>
                  <v-col cols="12" sm="4" class="d-flex align-end">
                    <v-btn
                      v-if="presentGenerateBtn(item)"
                      variant="tonal"
                      color="primary"
                      min-width="125px"
                      @click="onClickDetails(item)"
                      >{{ getActionText(item.Status) }}</v-btn
                    >
                  </v-col>
                </v-row>
                <v-expansion-panels
                  v-if="item.expansionPanel?.length"
                  v-model="item.expansionPanel"
                  class="mt-3"
                >
                  <v-expansion-panel variant="flat">
                    <v-expansion-panel-text>
                      <div
                        class="d-flex justify-space-between"
                        :class="
                          windowWidth < 800 ? 'flex-column' : 'align-center'
                        "
                      >
                        <div class="d-flex align-center">
                          <div style="height: 40px; width: 40px" class="mr-3">
                            <v-card
                              class="d-flex align-center justify-center pa-3"
                              elevation="4"
                              height="100%"
                              width="100%"
                              rounded="circle"
                            >
                              <v-icon color="primary">fas fa-file</v-icon>
                            </v-card>
                          </div>
                          <div>
                            <div>
                              {{ successMessage.beforeText }}
                              <span class="text-green font-weight-medium">{{
                                successMessage.highlightText
                              }}</span>
                              {{ successMessage.afterText }}
                            </div>
                            <div
                              class="text-subtitle-2 text-grey-darken-1 mt-n1"
                            >
                              {{ t("taxStatutory.downloadGerenatedFile") }}
                            </div>
                          </div>
                        </div>
                        <div :class="windowWidth < 800 ? 'mt-3' : ''">
                          <v-btn
                            :size="windowWidth < 800 ? 'small' : 'default'"
                            append-icon="fas fa-download"
                            class="mr-2"
                            @click="downloadFile(item)"
                            >{{ t("common.download") }}</v-btn
                          >
                          <v-btn
                            v-if="formAccess?.delete"
                            variant="tonal"
                            color="red"
                            icon="fas fa-trash-alt"
                            :size="windowWidth < 800 ? 'small' : 'default'"
                            @click="onClickDelete(item)"
                          >
                          </v-btn>
                        </div>
                      </div>
                      <v-divider class="mt-3" thickness="2"></v-divider>
                      <div class="mt-3 text-subtitle-1 font-weight-medium">
                        {{ t("taxStatutory.stepsToFileGeneration") }}
                      </div>
                      <ul
                        class="mt-2 text-subtitle-2 text-grey-darken-1"
                        style="list-style-position: inside"
                      >
                        <li>
                          {{ step1Message.beforeText }}
                          <span
                            class="text-blue cursor-pointer"
                            @click="openLink()"
                            >{{ step1Message.linkText }}</span
                          >{{ step1Message.afterText }}
                        </li>
                        <li>
                          {{ t("taxStatutory.step2") }}
                        </li>
                        <li>
                          {{ t("taxStatutory.step3") }}
                        </li>
                        <li>
                          {{ t("taxStatutory.step4") }}
                        </li>
                        <li>
                          {{ t("taxStatutory.step5") }}
                        </li>
                      </ul>

                      <more-details
                        class="mt-4"
                        :more-details-list="item.moreDetailsList"
                        :open-close-card="item.openMoreDetails"
                        @on-open-close="item.openMoreDetails = $event"
                      ></more-details>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </div>
            </v-card>
          </div>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
  <FilePreviewModal
    v-if="openDocumentModal"
    :fileName="selectedItem?.File_Name"
    :file-name-position="0"
    :folder-name="folderName"
    fileRetrieveType="documents"
    @close-preview-modal="closeDownloadModel()"
  />
  <AppWarningModal
    v-if="deleteModel"
    :open-modal="deleteModel"
    confirmation-heading="Are you sure to delete the selected record?"
    icon-name="far fa-trash-alt"
    icon-Size="75"
    @close-warning-modal="deleteModel = false"
    @accept-modal="deleteForm24Q()"
  />
  <AppLoading v-if="isLoading" />
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
  getCurrentInstance,
  defineAsyncComponent,
} from "vue";
import { useStore } from "vuex";
import moment from "moment";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { convertUTCToLocal, checkNullValue } from "@/helper";

const CustomSelect = defineAsyncComponent(() =>
  import("@/components/custom-components/CustomSelect.vue")
);
const moreDetails = defineAsyncComponent(() =>
  import("@/components/helper-components/MoreDetailsCard.vue")
);
const FilePreviewModal = defineAsyncComponent(() =>
  import("@/components/custom-components/FilePreviewModal.vue")
);

// Composables
const store = useStore();
const instance = getCurrentInstance();
const { t } = useI18n();
const router = useRouter();
const isMobileView = computed(() => {
  return store.state.isMobileWindowSize;
});
const windowWidth = computed(() => {
  return store.state.windowWidth;
});

// Success message with highlighted text
const successMessage = computed(() => {
  const fullMessage = t("taxStatutory.successGeneration");
  const highlightText = t("taxStatutory.successfullyGenerated");

  const parts = fullMessage.split(highlightText);
  return {
    beforeText: parts[0] || "",
    highlightText: highlightText,
    afterText: parts[1] || "",
  };
});

// Step 1 message with highlighted link text
const step1Message = computed(() => {
  const fullMessage = t("taxStatutory.step1");
  const linkText = t("taxStatutory.step1Link");

  const parts = fullMessage.split(linkText);
  return {
    beforeText: parts[0] || "",
    linkText: linkText,
    afterText: parts[1] || "",
  };
});

// Form Access
const accessRights = computed(() => {
  return store.getters.formIdBasedAccessRights;
});
const getFormAccess = (formIds, accessType) => {
  let formAccess = accessRights.value(formIds);
  if (
    formAccess &&
    formAccess.accessRights &&
    formAccess.accessRights[accessType]
  ) {
    return true;
  } else {
    return false;
  }
};
const formAccess = computed(() => {
  let formAccess = accessRights.value(122);
  if (
    formAccess &&
    formAccess.accessRights &&
    formAccess.accessRights["view"]
  ) {
    return formAccess.accessRights;
  } else {
    return false;
  }
});

// Tab management
const baseUrl = computed(() => {
  return store.getters.baseUrl;
});
const mainTabs = computed(() => {
  let tabs = [];
  if (getFormAccess(124, "view")) {
    tabs.push(t("taxStatutory.complianceForms"));
  }
  if (getFormAccess(373, "view")) {
    tabs.push(t("taxStatutory.taxLiabilities"));
  }
  if (getFormAccess(122, "view")) {
    tabs.push(t("taxStatutory.form24q"));
  }
  return tabs;
});
const currentTabItem = ref("");
const onTabChange = (tab) => {
  if (tab === t("taxStatutory.complianceForms")) {
    window.location.href = baseUrl.value + "forms-manager/compliance-forms";
  }
  if (tab === t("taxStatutory.taxLiabilities")) {
    router.push(
      "/tax-and-statutory-compliance/compliance-forms/tax-liabilities"
    );
  }
};

// Grid Filters
const selectedAssessmentYear = ref(null);
const assessmentYearsList = computed(() => {
  let assessmentYear = store.state.orgDetails?.assessmentYear
    ? store.state.orgDetails.assessmentYear.toString()
    : moment().format("YYYY");
  return [assessmentYear];
});
const selectedServiceProvider = ref(null);
const fieldForce = ref(0);
const serviceProviderList = ref([]);
const labelList = computed(() => {
  return store.state.customFormFields;
});
const loginEmpServiceProider = computed(() => {
  return store.state.orgDetails.serviceProviderId;
});
const onChangeFilter = (event, filterName) => {
  if (filterName?.toLowerCase() === "selectedassessmentyear") {
    selectedAssessmentYear.value = event;
  } else if (filterName?.toLowerCase() === "selectedserviceprovider") {
    selectedServiceProvider.value = event;
  }
  refetchList();
};
const getDropDownDetails = () => {
  listLoading.value = true;
  store
    .dispatch("getDefaultDropdownList", { formId: 122 })
    .then((res) => {
      if (
        res.data &&
        res.data.getDropDownBoxDetails &&
        !res.data.getDropDownBoxDetails.errorCode
      ) {
        const { serviceProvider, fieldForce: fieldForceValue } =
          res.data.getDropDownBoxDetails;
        serviceProviderList.value = serviceProvider;
        fieldForce.value = fieldForceValue;
        if (fieldForceValue && !loginEmpServiceProider.value) {
          selectedServiceProvider.value =
            serviceProvider[0]?.Service_Provider_Id || 0;
        }
      }
    })
    .catch(() => {
      serviceProviderList.value = [];
      fieldForce.value = 0;
    })
    .finally(() => {
      listLoading.value = false;
      refetchList();
    });
};

// List management
const listLoading = ref(false);
const isErrorInList = ref(false);
const errorContent = ref("");
const originalList = ref([]);
const getStatusColor = (status) => {
  if (status) {
    if (status.toLowerCase() === "in progress") {
      return "blue";
    } else if (status.toLowerCase() === "failed") {
      return "red";
    } else if (status.toLowerCase() === "completed") {
      return "green";
    } else {
      return "grey";
    }
  } else {
    return "orange";
  }
};
const getActionText = (status) => {
  if (status) {
    if (status.toLowerCase() === "in progress") {
      return t("taxStatutory.inProgress");
    } else if (status.toLowerCase() === "failed") {
      return t("common.retry");
    } else if (status.toLowerCase() === "completed") {
      return t("taxStatutory.viewDetails");
    } else {
      return "";
    }
  } else {
    return t("taxStatutory.generate");
  }
};
import { RETRIEVE_FORM24Q_LIST } from "@/graphql/tax-and-statutory-compliance/form24qQueries.js";

const fetchForm24qList = () => {
  listLoading.value = true;
  isErrorInList.value = false;

  instance.proxy.$apollo
    .query({
      query: RETRIEVE_FORM24Q_LIST,
      client: "apolloClientAI",
      fetchPolicy: "no-cache",
      variables: {
        formId: 122,
        assessmentYear: parseInt(selectedAssessmentYear.value) || null,
        serviceProviderId: parseInt(selectedServiceProvider.value) || null,
      },
    })
    .then((response) => {
      if (
        response.data &&
        response.data.listForm24Q &&
        !response.data.listForm24Q.error
      ) {
        let { form24QList } = response.data.listForm24Q;
        form24QList = form24QList ? JSON.parse(form24QList) : [];
        form24QList.forEach((item) => {
          item.openMoreDetails = false;
          item.expansionPanel = [];
        });
        originalList.value = form24QList || [];
        listLoading.value = false;
      } else {
        handleListError();
      }
    })
    .catch((err) => {
      handleListError(err);
    })
    .finally(() => {
      listLoading.value = false;
    });
};

const handleListError = (err = "") => {
  isErrorInList.value = true;
  store
    .dispatch("handleApiErrors", {
      error: err,
      action: t("common.retrieving"),
      form: t("taxStatutory.form24q"),
      isListError: true,
    })
    .then((errorMessages) => {
      errorContent.value = errorMessages;
    });
};
const refetchList = () => {
  fetchForm24qList();
};
const formatDate = (date) => {
  if (date && moment(date).isValid()) {
    let orgDateFormat = store.state.orgDetails.orgDateFormat;
    return moment(date).format(orgDateFormat);
  }
  return "-";
};

const getRandomColor = () => {
  const letters = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

const onClickDetails = (item) => {
  if (item.Status?.toLowerCase() === "completed") {
    prefillMoreDetails(item);
    item.expansionPanel = item.expansionPanel?.length ? [] : [0];
  } else {
    generateTextFile(item);
  }
};
const prefillMoreDetails = (item) => {
  item.moreDetailsList = [];
  const addedOn = item.Added_On ? convertUTCToLocal(item.Added_On) : "",
    addedByName = item.Added_By,
    updatedByName = item.Updated_By,
    updatedOn = item.Updated_On ? convertUTCToLocal(item.Updated_On) : "";
  if (addedOn && addedByName) {
    item.moreDetailsList.push({
      actionDate: addedOn,
      actionBy: addedByName,
      text: t("common.added"),
    });
  }
  if (updatedByName && updatedOn) {
    item.moreDetailsList.push({
      actionDate: updatedOn,
      actionBy: updatedByName,
      text: t("common.updated"),
    });
  }
};
const openLink = () => {
  window.open("https://tinpan.proteantech.in/", "_blank");
};
const isLoading = ref(false);
const presentGenerateBtn = (item) => {
  if (formAccess.value?.add && item.Status?.toLowerCase() !== "completed") {
    if (item.Status?.toLowerCase() === "in progress") {
      return false;
    }
    return true;
  } else if (item.Status?.toLowerCase() === "completed") {
    return true;
  }
  return false;
};
import { GENERATE_FORM24Q_TEXT_FILE } from "@/graphql/tax-and-statutory-compliance/form24qQueries.js";
const generateTextFile = (item) => {
  isLoading.value = true;
  instance.proxy.$apollo
    .mutate({
      mutation: GENERATE_FORM24Q_TEXT_FILE,
      client: "apolloClientAK",
      variables: {
        assessmentYear: parseInt(item.Assessment_Year),
        quarter: item.Quarter,
        serviceProviderId: parseInt(selectedServiceProvider.value),
        formId: 122,
      },
    })
    .then((response) => {
      if (
        response.data &&
        response.data.triggerForm24Q &&
        !response.data.triggerForm24Q.error
      ) {
        refetchList();
        showAlert({
          isOpen: true,
          message: t("taxStatutory.successGenerationPrompt"),
          type: "info",
        });
      } else {
        handleGenerateError();
      }
      isLoading.value = false;
    })
    .catch((err) => {
      isLoading.value = false;
      handleGenerateError(err);
    });
};
const handleGenerateError = (err = "") => {
  store.dispatch("handleApiErrors", {
    error: err,
    action: t("common.generating"),
    form: t("taxStatutory.form24q"),
    isListError: false,
  });
};
const showAlert = (snackbarData) => {
  store.commit("OPEN_SNACKBAR", snackbarData);
};
const openDocumentModal = ref(false);
const selectedItem = ref(null);
const folderName = ref("");
const downloadFile = (item) => {
  selectedItem.value = item;
  folderName.value = `Form 24Q/${item.Assessment_Year}`;
  openDocumentModal.value = true;
};
const closeDownloadModel = () => {
  openDocumentModal.value = false;
  folderName.value = "";
  selectedItem.value = null;
};
import { DELETE_FORM24Q } from "@/graphql/tax-and-statutory-compliance/form24qQueries.js";
const deleteModel = ref(false);
const onClickDelete = (item) => {
  selectedItem.value = item;
  deleteModel.value = true;
};
const deleteForm24Q = () => {
  isLoading.value = true;
  deleteModel.value = false;
  instance.proxy.$apollo
    .mutate({
      mutation: DELETE_FORM24Q,
      client: "apolloClientAK",
      variables: {
        form24QId: selectedItem.value.FUV_File_Generation_Id,
        formId: 122,
      },
    })
    .then((response) => {
      if (
        response.data &&
        response.data.deleteForm24Q &&
        !response.data.deleteForm24Q.error
      ) {
        refetchList();
        showAlert({
          isOpen: true,
          message: t("taxStatutory.successDeletePrompt"),
          type: "success",
        });
      } else {
        handleDeleteError();
      }
      isLoading.value = false;
    })
    .catch((err) => {
      isLoading.value = false;
      handleDeleteError(err);
    });
};
const handleDeleteError = (err = "") => {
  store.dispatch("handleApiErrors", {
    error: err,
    action: t("common.deleting"),
    form: t("taxStatutory.form24q"),
    isListError: false,
  });
};

// Lifecycle hooks
onMounted(() => {
  currentTabItem.value =
    "tab-" + mainTabs.value.indexOf(t("taxStatutory.form24q"));
  selectedServiceProvider.value = loginEmpServiceProider.value;
  selectedAssessmentYear.value = assessmentYearsList.value[0];
  getDropDownDetails();
});
</script>

<style scoped>
.form24q-container {
  padding: 58px 30px 0px 30px;
}
@media screen and (max-width: 805px) {
  .form24q-container {
    padding: 58px 20px 0px 20px;
  }
}
</style>
