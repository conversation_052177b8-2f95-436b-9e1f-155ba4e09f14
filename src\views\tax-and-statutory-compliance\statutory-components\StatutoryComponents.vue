<template>
  <div>
    <AppAccessDenied v-if="!mainTabs?.isAnyOneFormHaveAccess"></AppAccessDenied>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
export default {
  name: "StatutoryComponents",
  data() {
    return {
      isLoading: false,
    };
  },
  computed: {
    isPfEnabled() {
      return (
        this.getFormAccess("52", "view") ||
        this.getFormAccess("259", "view") ||
        this.getFormAccess("71", "view")
      );
    },
    isNpsEnabled() {
      return (
        this.getFormAccess("126", "view") ||
        this.getFormAccess("260", "view") ||
        this.getFormAccess("127", "view")
      );
    },
    isInsuranceEnabled() {
      return this.getFormAccess("378", "view");
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    mainTabs() {
      return this.$store.getters.statutoryComponentsTabs;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  mounted() {
    if (this.mainTabs?.isAnyOneFormHaveAccess) {
      let { formsWithAccess } = this.mainTabs;
      for (let form of formsWithAccess) {
        if (form.havingAccess) {
          if (form.isPhp) {
            this.isLoading = true;
            window.location.href = this.baseUrl + `${form.url}`;
          } else {
            this.$router.push(
              "/tax-and-statutory-compliance/statutory-components/" + form.url
            );
          }
          break;
        }
      }
    }
  },
  methods: {
    getFormAccess(formId, action) {
      let formAccess = this.accessRights(formId);
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights[action]
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>
