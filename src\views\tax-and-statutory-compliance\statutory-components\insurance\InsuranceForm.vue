<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>

    <ProfileCard v-if="subTabItems.length > 0" class="sub-tabs">
      <FormTab :model-value="openedSubTab">
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          color="primary"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            <div class="d-flex align-center">
              {{ tab.label }}
            </div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard>
    <v-container
      v-if="subTabItems.length > 0"
      fluid
      class="provident-fund-container"
    >
      <v-window v-model="openedSubTab">
        <v-window-item value="insuranceTypes">
          <InsuranceType v-if="openedSubTab == 'insuranceTypes'">
          </InsuranceType>
        </v-window-item>
      </v-window>
    </v-container>
    <AppAccessDenied v-else class="pt-16"></AppAccessDenied>
  </div>
</template>
<script>
import InsuranceType from "./insurance-type/InsuranceType.vue";
export default {
  name: "InsuranceForm",
  components: { InsuranceType },
  data() {
    return {
      currentTabItem: "",
      openedSubTab: "",
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    landedFormName() {
      let form = this.accessRights(58);
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "Insurance";
    },
    mainTabs() {
      let tabs = [];
      let { formsWithAccess } = this.$store.getters.statutoryComponentsTabs;
      for (let tab of formsWithAccess) {
        if (tab.havingAccess || tab.formName === this.landedFormName)
          tabs.push(tab.formName);
      }
      return tabs;
    },
    subTabItems() {
      let subTabItems = [];
      let insuranceForm = this.accessRights(378);
      if (
        insuranceForm &&
        insuranceForm.accessRights &&
        insuranceForm.accessRights["view"]
      ) {
        subTabItems.push({
          label: insuranceForm.customFormName || "Insurance Configuration",
          value: "insuranceTypes",
          disable: false,
        });
      }
      return subTabItems;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    if (this.subTabItems.length > 0) {
      this.openedSubTab = this.subTabItems[0].value;
    }
  },
  unmounted() {
    this.isLoading = false;
  },
  methods: {
    onTabChange(tab) {
      this.currentTabItem = "tab-" + this.mainTabs.indexOf(tab);
      if (tab !== this.landedFormName) {
        const { formsWithAccess } = this.$store.getters.statutoryComponentsTabs;
        let clickedForm = formsWithAccess.find((form) => form.formName === tab);
        if (clickedForm.isPhp) {
          this.isLoading = true;
          window.location.href = this.baseUrl + `${clickedForm.url}`;
          setTimeout(() => {
            this.currentTabItem =
              "tab-" + this.mainTabs.indexOf(this.landedFormName);
          }, 1000);
        } else {
          this.$router.push(
            "/tax-and-statutory-compliance/statutory-components/" +
              clickedForm.url
          );
        }
      }
    },
    onChangeSubTabs(val) {
      this.openedSubTab = val;
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
  },
};
</script>
<style scoped>
.provident-fund-container {
  padding: 58px 0px 0px 0px;
}
.sub-tabs {
  position: sticky;
  top: 118px;
  z-index: 100;
}
@media screen and (max-width: 1218px) {
  .sub-tabs {
    position: sticky;
    top: 125px;
    z-index: 100;
  }
}
</style>
