<template>
  <div>
    <div v-if="itemList.length > 0">
      <v-row>
        <v-col :cols="12" class="mb-12">
          <v-data-table
            :headers="tableHeaders"
            :items="itemList"
            fixed-header
            :items-per-page="itemList.length"
            :items-per-page-options="[
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
            ]"
            :height="
              $store.getters.getTableHeightBasedOnScreenSize(200, itemList)
            "
            style="box-shadow: none !important"
            class="elevation-1"
          >
            <template v-slot:item="{ item }">
              <tr
                class="data-table-tr bg-white"
                :class="
                  isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''
                "
              >
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Range From
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(item.Range_From) }}
                    </span>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Range To
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(item.Range_To) }}
                    </span>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Capped Value
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(item.Capped_Value) }}
                    </span>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Employee Share
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(item.Employee_Share) }}
                    </span>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Employer Share
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(item.Employer_Share) }}
                    </span>
                  </section>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </div>
  </div>
</template>
<script>
import { checkNullValue } from "@/helper.js";
export default {
  name: "ListInsuranceTypeSlab",
  props: {
    slabList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      itemList: [],
    };
  },
  mounted() {
    if (this.slabList.length) {
      this.itemList = this.slabList;
    }
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      let headers = [];
      headers.push(
        { title: "Range From", key: "Range_From" },
        { title: "Range To", key: "Range_To" },
        { title: "Capped Value", key: "Capped_Value" },
        { title: "Employee Share", key: "Employee_Share" },
        { title: "Employer Share", key: "Employer_Share" }
      );
      return headers;
    },
  },
  methods: {
    checkNullValue,
  },
};
</script>
