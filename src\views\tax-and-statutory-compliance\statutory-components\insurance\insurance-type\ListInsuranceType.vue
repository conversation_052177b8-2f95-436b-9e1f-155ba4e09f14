<template>
  <div>
    <div v-if="itemList.length > 0">
      <div
        class="d-flex align-center my-3"
        :class="isMobileView ? ' justify-center' : 'mt-n5 justify-end'"
      >
        <v-btn
          prepend-icon="fas fa-plus"
          color="primary"
          class="rounded-lg"
          @click="$emit('open-add-form')"
          :size="isMobileView ? 'small' : 'default'"
          v-if="formAccess.add"
        >
          <template v-slot:prepend>
            <v-icon color="white"></v-icon>
          </template>
          Add
        </v-btn>
        <v-btn
          variant="text"
          class="ml-2 mt-1"
          :size="isMobileView ? 'small' : 'default'"
          @click="$emit('refetch-data')"
        >
          <v-icon>fas fa-redo-alt</v-icon>
        </v-btn>
      </div>
      <v-row>
        <v-col :cols="12" class="mb-12">
          <v-data-table
            :headers="tableHeaders"
            :items="itemList"
            fixed-header
            :items-per-page="50"
            :items-per-page-options="[
              { value: 50, title: '50' },
              { value: 100, title: '100' },
              { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
            ]"
            :height="
              $store.getters.getTableHeightBasedOnScreenSize(290, itemList)
            "
            style="box-shadow: none !important"
            class="elevation-1"
          >
            <template v-slot:item="{ item }">
              <tr
                class="data-table-tr bg-white cursor-pointer"
                :class="
                  isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''
                "
                @click="$emit('open-view-form', item)"
              >
                <td
                  v-if="getFieldAlias[63].Field_Visiblity == 'Yes'"
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    {{ getFieldAlias[63].Field_Alias }}
                  </div>
                  <section>
                    <v-tooltip
                      :text="item.Insurance_Name"
                      location="bottom"
                      max-width="400"
                    >
                      <template v-slot:activator="{ props }">
                        <div
                          class="text-subtitle-1 font-weight-regular text-primary text-truncate"
                          :style="
                            !isMobileView
                              ? 'max-width: 300px; '
                              : 'max-width: 200px; '
                          "
                          v-bind="item.Insurance_Name.length > 40 ? props : ''"
                        >
                          {{ checkNullValue(item.Insurance_Name) }}
                        </div>
                      </template>
                    </v-tooltip>
                  </section>
                </td>
                <td
                  v-if="getFieldAlias[64]?.Field_Visiblity == 'Yes'"
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    {{ getFieldAlias[64].Field_Alias }}
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(item.Slab_Wise_Insurance) }}
                    </span>
                  </section>
                </td>
                <td
                  v-if="getFieldAlias[65]?.Field_Visiblity == 'Yes'"
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    {{ getFieldAlias[65].Field_Alias }}
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(item.Insurance_Type) }}
                    </span>
                  </section>
                </td>
                <td
                  v-if="getFieldAlias[74]?.Field_Visiblity == 'Yes'"
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    {{ getFieldAlias[74].Field_Alias }}
                  </div>
                  <section>
                    <span class="text-subtitle-1 font-weight-regular">
                      {{ checkNullValue(item.Payment_Frequency) }}
                    </span>
                  </section>
                </td>

                <td
                  v-if="getFieldAlias[77]?.Field_Visiblity == 'Yes'"
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    {{ getFieldAlias[77].Field_Alias }}
                  </div>
                  <section>
                    <span :class="getStatusColor(item.InsuranceType_Status)">
                      {{ checkNullValue(item.InsuranceType_Status) }}
                    </span>
                  </section>
                </td>
                <td
                  :class="
                    isMobileView
                      ? 'd-flex justify-space-between align-center'
                      : 'pa-2 pl-5'
                  "
                >
                  <div
                    v-if="isMobileView"
                    class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                  >
                    Actions
                  </div>
                  <section class="d-flex justify-end">
                    <ActionMenu
                      v-if="formAccess.delete"
                      :actions="['Delete']"
                      iconColor="grey"
                      @selected-action="deleteInsuranceType(item)"
                      :access-rights="formAccess"
                    ></ActionMenu>
                    <div v-else>-</div>
                  </section>
                </td>
              </tr>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </div>
    <AppFetchErrorScreen
      v-else
      key="no-results-screen"
      :main-title="mainTitle"
      image-name="common/no-records"
    >
      <template #contentSlot>
        <div style="max-width: 80%">
          <v-row class="rounded-lg pa-5 mb-4">
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                color="secondary"
                variant="elevated"
                class="ml-4 mt-1"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="resetSearch()"
              >
                Reset Search
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { checkNullValue } from "@/helper.js";
import { DELETE_INSURANCE_TYPE } from "@/graphql/tax-and-statutory-compliance/insuranceType.js";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
export default {
  name: "ListInsuranceType",
  components: {
    ActionMenu,
  },
  emits: ["open-view-form", "open-add-form", "refetch-data"],
  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    getFieldAlias: {
      type: [Object, Array],
      default: () => {
        return {};
      },
    },
    formAccess: {
      type: Object,
      required: true,
    },
    accessFormName: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      itemList: [],
      isLoading: false,
    };
  },
  mounted() {
    if (this.items.length) {
      this.itemList = this.items;
    }
  },
  watch: {
    items(val) {
      this.itemList = val;
      this.onApplySearch();
    },
    searchValue() {
      this.onApplySearch();
    },
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    searchValue() {
      return this.$store.state.empSearchValue;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      let headers = [];
      if (this.getFieldAlias[63]?.Field_Visiblity == "Yes") {
        headers.push({
          title: this.getFieldAlias[63].Field_Alias,
          key: "Insurance_Name",
        });
      }
      if (this.getFieldAlias[64]?.Field_Visiblity == "Yes") {
        headers.push({
          title: this.getFieldAlias[64].Field_Alias,
          key: "Slab_Wise_Insurance",
        });
      }
      if (this.getFieldAlias[65]?.Field_Visiblity == "Yes") {
        headers.push({
          title: this.getFieldAlias[65].Field_Alias,
          key: "Insurance_Type",
        });
      }
      if (this.getFieldAlias[74]?.Field_Visiblity == "Yes") {
        headers.push({
          title: this.getFieldAlias[74].Field_Alias,
          key: "Payment_Frequency",
        });
      }
      if (this.getFieldAlias[77]?.Field_Visiblity == "Yes") {
        headers.push({
          title: this.getFieldAlias[77].Field_Alias,
          key: "InsuranceType_Status",
        });
      }
      headers.push({
        title: "Actions",
        key: "",
        align: "end",
        sortable: false,
      });
      return headers;
    },
    mainTitle() {
      return `There are no ${this.accessFormName.toLowerCase()} matched for the selected searches.`;
    },
  },
  methods: {
    checkNullValue,
    resetSearch() {
      this.itemList = this.items;
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },
    getStatusColor(status) {
      if (status === "Active") {
        return "text-green";
      } else {
        return "text-red";
      }
    },
    onApplySearch() {
      let val = this.searchValue;
      if (!val) {
        this.itemList = this.items;
      } else {
        let searchValue = val.toString().toLowerCase();
        let searchItems = this.items;
        searchItems = searchItems.filter((item) => {
          return Object.values(item).some(
            (v) => v && v.toString().toLowerCase().includes(searchValue)
          );
        });
        this.itemList = searchItems;
      }
    },
    deleteInsuranceType(item) {
      let vm = this;
      vm.isLoading = true;
      vm.$apollo
        .mutate({
          mutation: DELETE_INSURANCE_TYPE,
          variables: {
            insuranceTypeId: item.InsuranceType_Id,
          },
          client: "apolloClientAK",
        })
        .then(() => {
          let snackbarData = {
            isOpen: true,
            message: "Insurance Type deleted successfully",
            type: "success",
          };
          vm.showAlert(snackbarData);
          vm.$emit("refetch-data");
          vm.isLoading = false;
        })
        .catch((err) => {
          this.$store.dispatch("handleApiErrors", {
            error: err,
            action: "delete",
            form: "insurance type",
            isListError: false,
          });
          vm.isLoading = false;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
