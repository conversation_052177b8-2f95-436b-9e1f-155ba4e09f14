<template>
  <div v-if="isMounted">
    <section class="mb-16">
      <div>
        <v-card
          class="rounded-lg"
          :class="isMobileView ? '' : 'pa-4'"
          elevation="5"
        >
          <v-card-text>
            <v-form ref="InsuranceType">
              <v-row
                class="rounded-lg card-height bg-grey-lighten-5"
                :class="isMobileView ? '' : 'pa-4'"
              >
                <v-col cols="12" class="d-flex justify-space-between mb-4">
                  <div class="d-flex align-center">
                    <v-progress-circular
                      model-value="100"
                      color="primary"
                      :size="22"
                      class="mr-1"
                    ></v-progress-circular>
                    <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                      accessFormName
                    }}</span>
                  </div>
                  <v-avatar
                    v-if="formAccess.update"
                    @click="$emit('open-edit')"
                    :size="30"
                    color="primary"
                    class="cursor-pointer"
                    :class="isMobileView ? 'ml-auto mt-2' : ''"
                  >
                    <v-icon color="white" :size="12">fas fa-pencil-alt</v-icon>
                  </v-avatar>
                </v-col>
                <v-col
                  v-if="getFieldAlias[63]?.Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[63].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Insurance_Name) }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias[64]?.Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[64].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Slab_Wise_Insurance) }}
                  </p>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[65]?.Field_Visiblity == 'Yes' &&
                    editFormData.Slab_Wise_Insurance == 'No'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[65].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Insurance_Type) }}
                  </p>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[66]?.Field_Visiblity == 'Yes' &&
                    editFormData.Slab_Wise_Insurance == 'No' &&
                    editFormData.Insurance_Type == 'Variable'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[66].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Employer_Share_Percentage) }}
                  </p>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[67]?.Field_Visiblity == 'Yes' &&
                    editFormData.Slab_Wise_Insurance == 'No' &&
                    editFormData.Insurance_Type == 'Variable'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[67].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Employee_Share_Percentage) }}
                  </p>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[68]?.Field_Visiblity == 'Yes' &&
                    editFormData.Slab_Wise_Insurance == 'No' &&
                    editFormData.Insurance_Type == 'Fixed'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[68].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Employer_Share_Amount) }}
                  </p>
                </v-col>

                <v-col
                  v-if="
                    getFieldAlias[69]?.Field_Visiblity == 'Yes' &&
                    editFormData.Slab_Wise_Insurance == 'No' &&
                    editFormData.Insurance_Type == 'Fixed'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[69].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Employee_Share_Amount) }}
                  </p>
                </v-col>

                <v-col
                  v-if="editFormData.Insurance_Type == 'Fixed'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Pro Rate Fixed Insurance
                    <v-tooltip
                      text="When Pro-Rate Insurance is enabled, the insurance amount is adjusted based on the number of unpaid leave days taken by the employee in the month."
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          class="ml-1"
                          size="x-small"
                          color="blue"
                          v-bind="props"
                        >
                          fas fa-info-circle
                        </v-icon>
                      </template>
                    </v-tooltip>
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Pro_Rate_Fixed_Insurance) }}
                  </p>
                </v-col>

                <v-col
                  v-if="
                    editFormData.Insurance_Type == 'Variable' ||
                    editFormData.Slab_Wise_Insurance == 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Wage Inclusion
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Wage_Inclusion) }}
                  </p>
                </v-col>
                <v-col
                  v-if="getFieldAlias[74]?.Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[74].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Payment_Frequency) }}
                  </p>
                </v-col>

                <!-- <v-col
                  v-if="getFieldAlias[70]?.Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[70].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Auto_Declaration) }}
                  </p>
                </v-col> -->

                <!-- <v-col
                  v-if="
                    getFieldAlias[71]?.Field_Visiblity == 'Yes' &&
                    editFormData.Auto_Declaration == 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[71].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        editFormData.Auto_Declaration_Applicable_For
                      )
                    }}
                  </p>
                </v-col> -->
                <!-- <v-col
                  v-if="
                    getFieldAlias[72]?.Field_Visiblity == 'Yes' &&
                    editFormData.Auto_Declaration == 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[72].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        editFormData.Section_Investment_Category_Name
                      )
                    }}
                  </p>
                </v-col> -->
                <v-col
                  v-if="
                    getFieldAlias[73]?.Field_Visiblity == 'Yes' &&
                    editFormData.Slab_Wise_Insurance == 'No'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[73].Field_Alias }}
                    <v-tooltip
                      text="When the Override Contribution at Employee Level option is enabled, you can manually edit the insurance amount for each individual employee while configuring or updating their salary."
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          class="ml-1"
                          size="x-small"
                          color="blue"
                          v-bind="props"
                        >
                          fas fa-info-circle
                        </v-icon>
                      </template>
                    </v-tooltip>
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        editFormData.Override_Insurance_Contribution_At_Employee_Level
                      )
                    }}
                  </p>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias[75]?.Field_Visiblity == 'Yes' &&
                    editFormData.Slab_Wise_Insurance == 'No'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[75].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Employee_State_Insurance) }}
                  </p>
                </v-col>
                <v-col cols="12" lg="6" md="6">
                  <p class="text-subtitle-1 text-grey-darken-1">
                    Include in arrears calculation
                    <v-tooltip
                      text="When Include in Arrears Calculation is enabled, the insurance amount  will be included while calculating arrears."
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          class="ml-1"
                          size="x-small"
                          color="blue"
                          v-bind="props"
                        >
                          fas fa-info-circle
                        </v-icon>
                      </template>
                    </v-tooltip>
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        editFormData.Include_In_Arrears_Calculation
                      )
                    }}
                  </p>
                </v-col>
                <v-col cols="12" lg="6" md="6">
                  <p class="text-subtitle-1 text-grey-darken-1">Round Off</p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Round_Off_Insurance) }}
                  </p>
                </v-col>
                <v-col
                  v-if="getFieldAlias[77]?.Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[77].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.InsuranceType_Status) }}
                  </p>
                </v-col>
                <v-col
                  v-if="getFieldAlias[76]?.Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias[76].Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Description) }}
                  </p>
                </v-col>
                <v-col
                  v-if="
                    slabList.length > 0 &&
                    editFormData.Slab_Wise_Insurance == 'Yes'
                  "
                  cols="12"
                >
                  <ListInsuranceSlabs :slabList="slabList"></ListInsuranceSlabs>
                </v-col>
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  ></MoreDetails>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </div>
    </section>
  </div>

  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import ListInsuranceSlabs from "./ListInsuranceSlabs.vue";
import { convertUTCToLocal, checkNullValue } from "@/helper.js";
export default {
  name: "ViewInsuranceType",
  props: {
    accessFormName: {
      type: String,
      required: true,
    },
    getFieldAlias: {
      type: [Object, Array],
      default: () => {
        return {};
      },
    },
    selectedItem: {
      type: Object,
      required: true,
    },
    slabList: {
      type: Array,
      required: true,
      default: () => [],
    },
    formAccess: {
      type: Object,
      required: true,
    },
  },
  components: {
    MoreDetails,
    ListInsuranceSlabs,
  },
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      isLoading: false,
      isMounted: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },
  watch: {
    selectedItem: {
      immediate: true,
      handler(newData) {
        this.editFormData = Object.assign({}, newData);
        this.prefillMoreDetails();
      },
    },
  },
  mounted() {
    this.prefillMoreDetails();
    this.isMounted = true;
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const updatedByName = this.editFormData.Updated_By_Name,
        updatedOn = this.convertUTCToLocal(this.editFormData.Updated_On),
        addedByName = this.selectedItem.Added_By_Name,
        addedOn = this.convertUTCToLocal(this.selectedItem.Added_On);

      this.moreDetailsList.push({
        actionDate: addedOn,
        actionBy: addedByName,
        text: "Added",
      });

      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>
