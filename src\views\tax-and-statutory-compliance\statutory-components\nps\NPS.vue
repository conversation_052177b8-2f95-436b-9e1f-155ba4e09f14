<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="false"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>
    <ProfileCard v-if="subTabItems.length > 0" class="sub-tabs">
      <FormTab :model-value="openedSubTab">
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          color="primary"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            <div class="d-flex align-center">
              {{ tab.label }}
            </div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard>
    <v-container v-if="subTabItems.length > 0" fluid class="nps-container">
      <v-window v-model="openedSubTab">
        <v-window-item value="nps">
          <NPSMainForm v-if="openedSubTab == 'nps'" class="ma-5"> </NPSMainForm>
        </v-window-item>
        <v-window-item value="npsRules">
          <NPSRules
            v-if="openedSubTab == 'npsRules'"
            class="ma-5"
            :baseUrl="baseUrl"
          >
          </NPSRules>
        </v-window-item>
      </v-window>
    </v-container>
    <AppAccessDenied v-else class="pt-16"></AppAccessDenied>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import NPSMainForm from "./Nps configuration/NpsConfigurationMain.vue";
import NPSRules from "./nps-rules/NpsMainForm.vue";
export default {
  name: "NPS",
  components: { NPSMainForm, NPSRules },
  data() {
    return {
      currentTabItem: "",
      openedSubTab: "",
      isLoading: false,
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    landedFormName() {
      let form = this.accessRights(126);
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "NPS";
    },
    mainTabs() {
      let tabs = [];
      let { formsWithAccess } = this.$store.getters.statutoryComponentsTabs;
      for (let tab of formsWithAccess) {
        if (tab.havingAccess || tab.formName === this.landedFormName)
          tabs.push(tab.formName);
      }
      return tabs;
    },
    getFormAccess() {
      return this.$store.getters.getFormAccess;
    },
    subTabItems() {
      let subTabItems = [];
      let npsForm = this.accessRights(126);
      let npsPaymentTracker = this.accessRights(127);
      let npsRules = this.accessRights(260);
      if (npsForm && npsForm.accessRights && npsForm.accessRights["view"]) {
        subTabItems.push({
          label: npsForm.customFormName || "NPS",
          value: "nps",
          disable: false,
        });
      }
      if (
        npsPaymentTracker &&
        npsPaymentTracker.accessRights &&
        npsPaymentTracker.accessRights["view"]
      ) {
        subTabItems.push({
          label: npsPaymentTracker.customFormName || "NPS Payment Tracker",
          value: "npsPaymentTracker",
          disable: false,
        });
      }
      if (npsRules && npsRules.accessRights && npsRules.accessRights["view"]) {
        subTabItems.push({
          label: npsRules.customFormName || "NPS Rules",
          value: "npsRules",
          disable: false,
        });
      }
      return subTabItems;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    let url_string = window.location.href;
    let url = new URL(url_string);
    let tab = url.searchParams.get("tab");
    if (this.subTabItems.length > 0) {
      if (tab) {
        this.openedSubTab = tab;
      } else {
        this.openedSubTab = this.subTabItems[0].value;
        if (this.subTabItems[0].value === "npsPaymentTracker") {
          this.isLoading = true;
          window.location.href = this.baseUrl + "payroll/etf";
        }
      }
    }
  },
  unmounted() {
    this.isLoading = false;
  },
  methods: {
    onTabChange(tab) {
      this.currentTabItem = "tab-" + this.mainTabs.indexOf(tab);
      if (tab !== this.landedFormName) {
        const { formsWithAccess } = this.$store.getters.statutoryComponentsTabs;
        let clickedForm = formsWithAccess.find((form) => form.formName === tab);
        if (clickedForm.isPhp) {
          this.isLoading = true;
          window.location.href = this.baseUrl + `${clickedForm.url}`;
          setTimeout(() => {
            this.currentTabItem =
              "tab-" + this.mainTabs.indexOf(this.landedFormName);
          }, 1000);
        } else {
          this.$router.push(
            "/tax-and-statutory-compliance/statutory-components/" +
              clickedForm.url
          );
        }
      }
    },
    onChangeSubTabs(val) {
      if (val === "npsPaymentTracker") {
        this.isLoading = true;
        window.location.href = this.baseUrl + "payroll/etf";
      }
      this.openedSubTab = val;
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
  },
};
</script>
<style scoped>
.nps-container {
  padding: 58px 0px 0px 0px;
}
.sub-tabs {
  position: sticky;
  top: 118px;
  z-index: 100;
}
@media screen and (max-width: 1218px) {
  .sub-tabs {
    position: sticky;
    top: 125px;
    z-index: 100;
  }
}
</style>
