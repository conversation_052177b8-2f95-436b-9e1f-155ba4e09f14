<template>
  <div v-if="isMounted">
    <section>
      <div>
        <v-card
          class="py-9 rounded-lg"
          :class="isMobileView ? '' : 'px-5'"
          elevation="5"
        >
          <v-card-text>
            <v-form ref="npsFund">
              <v-row class="d-flex justify-space-between mb-4">
                <div class="d-flex align-center">
                  <v-progress-circular
                    model-value="100"
                    color="primary"
                    :size="22"
                    class="mr-1"
                  ></v-progress-circular>
                  <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                    accessFormName
                  }}</span>
                </div>
                <div
                  class="d-flex align-center pa-1"
                  :class="isMobileView ? 'ml-auto' : ''"
                >
                  <v-btn
                    rounded="lg"
                    variant="outlined"
                    color="primary"
                    class="mr-2"
                    @click="closeEditForm()"
                  >
                    Cancel
                  </v-btn>
                  <div class="mt-2 mr-1">
                    <v-btn
                      v-if="isFormDirty"
                      rounded="lg"
                      color="primary"
                      class="mb-2"
                      @click="validateLNpsConfigurationForm()"
                      >Save</v-btn
                    >
                    <v-tooltip v-else location="bottom">
                      <template v-slot:activator="{ props }">
                        <v-btn
                          v-bind="props"
                          rounded="lg"
                          color="grey-lighten-3"
                          class="cursor-not-allow mb-2"
                          variant="flat"
                          >Save</v-btn
                        >
                      </template>
                      <div>There are no changes to be updated</div>
                    </v-tooltip>
                  </div>
                </div>
              </v-row>
              <v-row>
                <v-col
                  v-if="getFieldAlias[50].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employeeShare"
                    type="number"
                    :min="0"
                    :max="100"
                    variant="solo"
                    :rules="[
                      getFieldAlias[50].Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias[50].Field_Alias}`,
                            employeeShare
                          )
                        : true,

                      employeeShare
                        ? validateWithRulesAndReturnMessages(
                            employeeShare,
                            'employeeShare',
                            `${getFieldAlias[50].Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="validateInput('employeeShare')"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias[50].Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias[50].Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>
                <v-col
                  v-if="getFieldAlias[51].Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="employerShare"
                    type="number"
                    :min="0"
                    :max="100"
                    variant="solo"
                    :rules="[
                      getFieldAlias[51].Mandatory_Field == 'Yes'
                        ? numericRequiredValidation(
                            `${getFieldAlias[51].Field_Alias}`,
                            employerShare
                          )
                        : true,

                      employerShare
                        ? validateWithRulesAndReturnMessages(
                            employerShare,
                            'employerShare',
                            `${getFieldAlias[51].Field_Alias}`,
                            true
                          )
                        : true,
                    ]"
                    @update:model-value="validateInput('employerShare')"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias[51].Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias[51].Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>
                <v-col cols="12" lg="6" md="6">
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">
                      Include in arrears calculation
                    </span>
                    <v-switch
                      color="primary"
                      v-model="includeInArrearsCalculation"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </div>
      <AppWarningModal
        v-if="openConfirmationPopup"
        :open-modal="openConfirmationPopup"
        confirmation-heading="Are you sure to exit this form?"
        imgUrl="common/exit_form"
        @close-warning-modal="abortClose()"
        @accept-modal="acceptClose()"
      >
      </AppWarningModal>
    </section>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules";
// Queries
import { UPDATE_NPS_CONFIGURATION_RULES } from "@/graphql/tax-and-statutory-compliance/npsRules";

export default {
  name: "EditNpsFund",
  mixins: [validationRules],
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    accessFormName: {
      type: String,
      required: true,
    },
    getFieldAlias: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      isLoading: false,
      isMounted: false,
      isFormDirty: false,
      openConfirmationPopup: false,
      restrictPFWageAmount: null,
      npsDeductionPercentage: null,
      employerShare: null,
      employeeShare: null,
      showValidationAlert: false,
      validationMessages: [],
      npsConfigurationId: 0,
      isListLoading: false,
      includeInArrearsCalculation: "No",
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  mounted() {
    const {
      Employee_Share,
      Employer_Share,
      Nps_Configuration_Id,
      Include_In_Arrears_Calculation,
    } = this.editFormData;
    this.employeeShare =
      this.getFieldAlias[50].Field_Visiblity == "Yes" && Employee_Share
        ? parseFloat(Employee_Share)
        : null;
    this.employerShare =
      this.getFieldAlias[51].Field_Visiblity == "Yes" && Employer_Share
        ? parseFloat(Employer_Share)
        : null;
    this.npsConfigurationId = Nps_Configuration_Id ? Nps_Configuration_Id : 1;
    this.includeInArrearsCalculation = Include_In_Arrears_Calculation
      ? Include_In_Arrears_Calculation
      : "No";
    this.isMounted = true;
  },
  methods: {
    validateInput(fieldName) {
      // Get the value of the specified field
      let value = this[fieldName];

      // Check if the input value is negative
      if (value !== null && value < 0) {
        // Reset the input value to null
        this[fieldName] = null;
      }
      this.isFormDirty = true;
    },
    onChangeIsFormDirty(val, field) {
      if (field == "Employer_Share") {
        this.employeeShare = val;
      }
      this.isFormDirty = true;
    },
    async validateLNpsConfigurationForm() {
      const { valid } = await this.$refs.npsFund.validate();
      if (valid) {
        this.updateNpsConfiguration();
      }
    },
    updateNpsConfiguration() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_NPS_CONFIGURATION_RULES,
            variables: {
              npsConfigurationId: vm.npsConfigurationId
                ? vm.npsConfigurationId
                : 1,
              employeeShare: vm.employeeShare
                ? parseFloat(vm.employeeShare)
                : null,
              employerShare: vm.employerShare
                ? parseFloat(vm.employerShare)
                : null,
              includeInArrearsCalculation: vm.includeInArrearsCalculation
                ? vm.includeInArrearsCalculation
                : null,
            },
            client: "apolloClientAK",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: `${this.accessFormName} updated successfully.`,
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((error) => {
            vm.handleUpdateError(error);
          });
      } catch {
        vm.handleUpdateError((err = ""));
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: this.accessFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    closeEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
      this.isFormDirty = false;
    },
  },
};
</script>
