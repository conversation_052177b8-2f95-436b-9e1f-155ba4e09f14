<template>
  <div>
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      :button-text="showRetryBtn ? 'Retry' : ''"
      @button-click="refetchData()"
    >
    </AppFetchErrorScreen>
    <div v-else>
      <v-card
        class="py-9 rounded-lg fill-height"
        :class="isMobileView ? '' : 'px-5'"
        elevation="5"
      >
        <v-card-text>
          <v-row>
            <v-col v-if="!isEdit && !listLoading" cols="12">
              <ViewNpsConfiguration
                :editFormData="npsConfigurationData"
                @open-edit="openEditForm()"
                :accessFormName="accessFormName"
                :getFieldAlias="labelList"
                :formAccess="formAccess"
              ></ViewNpsConfiguration>
            </v-col>
            <v-col v-if="isEdit && !listLoading" cols="12">
              <EditNpsConfiguration
                :editFormData="npsConfigurationData"
                @refetch-data="refetchData()"
                @close-form="closeEditForm()"
                :accessFormName="accessFormName"
                :getFieldAlias="labelList"
              >
              </EditNpsConfiguration>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </div>
  </div>
</template>
<script>
// components
import ViewNpsConfiguration from "./ViewNpsConfiguration";
import EditNpsConfiguration from "./EditNpsConfiguration.vue";
// Queries
import { RETRIEVE_NPS_CONFIGURATION_DETAILS } from "@/graphql/tax-and-statutory-compliance/npsRules";
export default {
  name: "NPSMainForm",
  components: {
    ViewNpsConfiguration,
    EditNpsConfiguration,
  },
  data() {
    return {
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      npsConfigurationData: {},
      isEdit: false,
      slabList: [],
    };
  },
  computed: {
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formAccess() {
      let npsConfigAccess = this.accessRights("126");
      if (
        npsConfigAccess &&
        npsConfigAccess.accessRights &&
        npsConfigAccess.accessRights["view"]
      ) {
        return npsConfigAccess.accessRights;
      } else return false;
    },
    accessFormName() {
      let npsConfigAccess = this.accessRights("126");
      if (npsConfigAccess && npsConfigAccess.customFormName) {
        return npsConfigAccess.customFormName;
      } else return "HDMF Configuration";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("NPS Error:", err);
    let msg = `Something went wrong while loading the ${this.accessFormName.toLowerCase()} form. Please try after some time.`;
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  mounted() {
    if (this.formAccess && this.isSuperAdmin) {
      this.fetchNpsConfigurationDetails();
    }
  },
  methods: {
    openEditForm() {
      this.isEdit = true;
    },
    closeEditForm() {
      this.isEdit = false;
    },
    refetchData() {
      this.closeEditForm();
      this.fetchNpsConfigurationDetails();
    },
    fetchNpsConfigurationDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_NPS_CONFIGURATION_DETAILS,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listNpsConfigurationDetails.npsConfigurationDetails
          ) {
            let npsConfigurationData = JSON.parse(
              response.data.listNpsConfigurationDetails.npsConfigurationDetails
            );

            vm.npsConfigurationData = npsConfigurationData[0];
            vm.listLoading = false;
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
