<template>
  <div v-if="isMounted">
    <section>
      <div>
        <div
          class="py-9 pa-8 rounded-lg ma-3 card-height bg-grey-lighten-5"
          :class="isMobileView ? '' : 'px-3'"
        >
          <v-form ref="npsFund">
            <v-row class="d-flex justify-space-between mb-4 ma-6">
              <div class="d-flex align-center">
                <v-progress-circular
                  model-value="100"
                  color="primary"
                  :size="22"
                  class="mr-1"
                ></v-progress-circular>
                <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                  accessFormName
                }}</span>
              </div>
              <v-avatar
                v-if="isSuperAdmin && formAccess.update"
                @click="$emit('open-edit')"
                :size="30"
                color="primary"
                class="cursor-pointer"
                :class="isMobileView ? 'ml-auto mt-2' : ''"
              >
                <v-icon color="white" :size="12">fas fa-pencil-alt</v-icon>
              </v-avatar>
            </v-row>
            <v-row class="ma-6">
              <v-col
                cols="12"
                lg="6"
                md="6"
                v-if="getFieldAlias[52].Field_Visiblity == 'Yes'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ getFieldAlias[52].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editFormData.Auto_Declaration) }}
                </p>
              </v-col>
              <v-col
                v-if="
                  checkNullValue(editFormData.Auto_Declaration) == 'Yes' &&
                  getFieldAlias[53].Field_Visiblity == 'Yes'
                "
                cols="12"
                lg="6"
                md="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ getFieldAlias[53].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editFormData.Investment_Category) }}
                </p>
              </v-col>

              <v-col
                v-if="
                  checkNullValue(editFormData.Auto_Declaration) == 'Yes' &&
                  getFieldAlias[55].Field_Visiblity == 'Yes'
                "
                cols="12"
                lg="6"
                md="6"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ getFieldAlias[55].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{
                    checkNullValue(editFormData.Auto_Declaration_Applicable_For)
                  }}
                </p>
              </v-col>

              <v-col
                cols="12"
                lg="6"
                md="6"
                v-if="getFieldAlias[54].Field_Visiblity == 'Yes'"
              >
                <p class="text-subtitle-1 text-grey-darken-1">
                  {{ getFieldAlias[54].Field_Alias }}
                </p>
                <p class="text-subtitle-1 font-weight-regular">
                  {{ checkNullValue(editFormData.NPS_Deduction_Percentage) }}
                </p>
              </v-col>
            </v-row>
            <v-row>
              <v-col v-if="moreDetailsList.length > 0" cols="12">
                <MoreDetails
                  :more-details-list="moreDetailsList"
                  :open-close-card="openMoreDetails"
                  @on-open-close="openMoreDetails = $event"
                ></MoreDetails>
              </v-col>
            </v-row>
          </v-form>
        </div>
      </div>
    </section>
  </div>

  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { convertUTCToLocal, checkNullValue } from "@/helper.js";
import moment from "moment";
export default {
  name: "npsFund",
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    accessFormName: {
      type: String,
      required: true,
    },
    getFieldAlias: {
      type: Array,
      default: () => {
        return [];
      },
    },
    formAccess: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    MoreDetails,
  },
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      isLoading: false,
      isMounted: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formatDate() {
      return (date) => {
        let orgDateFormat =
          this.$store.state.orgDetails.orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : "";
      };
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
  },
  mounted() {
    this.isMounted = true;
    this.prefillMoreDetails();
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const Updated_By = this.editFormData.Updated_By,
        Updated_On = this.formatDate(
          new Date(this.editFormData.Updated_On + ".000Z")
        );
      if (Updated_By && Updated_On) {
        this.moreDetailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By,
          text: "Updated",
        });
      }
    },
  },
};
</script>
