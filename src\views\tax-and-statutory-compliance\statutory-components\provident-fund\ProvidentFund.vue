<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>

    <ProfileCard v-if="subTabItems.length > 0" class="sub-tabs">
      <FormTab :model-value="openedSubTab">
        <v-tab
          v-for="tab in subTabItems"
          :key="tab.value"
          :value="tab.value"
          :disabled="tab.disable"
          color="primary"
          @click="onChangeSubTabs(tab.value)"
        >
          <div
            :class="[
              isActiveSubTab(tab.value)
                ? 'text-primary font-weight-bold'
                : 'text-grey-darken-2 font-weight-bold',
            ]"
          >
            <div class="d-flex align-center">
              {{ tab.label }}
            </div>
          </div>
        </v-tab>
      </FormTab>
    </ProfileCard>
    <v-container
      v-if="subTabItems.length > 0"
      fluid
      class="provident-fund-container"
    >
      <v-window v-model="openedSubTab">
        <v-window-item value="providentFund">
          <ProvidentFundMainForm
            v-if="openedSubTab == 'providentFund'"
            class="ma-5"
          >
          </ProvidentFundMainForm>
        </v-window-item>
        <v-window-item value="providentFundRules">
          <ProvidentFundRules
            v-if="openedSubTab == 'providentFundRules'"
            class="ma-5"
          >
          </ProvidentFundRules>
        </v-window-item>
      </v-window>
    </v-container>
    <AppAccessDenied v-else class="pt-16"></AppAccessDenied>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import ProvidentFundMainForm from "./provident-fund-configuration/PFConfigurationMainForm.vue";
import ProvidentFundRules from "./provident-fund-rules/ProvidentFundMainForm.vue";
export default {
  name: "ProvidentFund",
  components: { ProvidentFundMainForm, ProvidentFundRules },
  data() {
    return {
      currentTabItem: "",
      openedSubTab: "",
      isLoading: false,
    };
  },
  computed: {
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    landedFormName() {
      let form = this.accessRights(52);
      if (form && form.customFormName) {
        return form.customFormName;
      } else return "Provident Fund";
    },
    mainTabs() {
      let tabs = [];
      let { formsWithAccess } = this.$store.getters.statutoryComponentsTabs;
      for (let tab of formsWithAccess) {
        if (tab.havingAccess || tab.formName === this.landedFormName)
          tabs.push(tab.formName);
      }
      return tabs;
    },
    subTabItems() {
      let subTabItems = [];
      let pfForm = this.accessRights(52);
      let pfPaymentTracker = this.accessRights(71);
      let pfRules = this.accessRights(259);
      if (pfForm && pfForm.accessRights && pfForm.accessRights["view"]) {
        subTabItems.push({
          label: pfForm.customFormName || "Provident Fund",
          value: "providentFund",
          disable: false,
        });
      }
      if (
        pfPaymentTracker &&
        pfPaymentTracker.accessRights &&
        pfPaymentTracker.accessRights["view"]
      ) {
        subTabItems.push({
          label: pfPaymentTracker.customFormName || "PF Payment Tracker",
          value: "pfPaymentTracker",
          disable: false,
        });
      }
      if (pfRules && pfRules.accessRights && pfRules.accessRights["view"]) {
        subTabItems.push({
          label: pfRules.customFormName || "Provident Fund Rules",
          value: "providentFundRules",
          disable: false,
        });
      }
      return subTabItems;
    },
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
  },
  mounted() {
    this.currentTabItem = "tab-" + this.mainTabs.indexOf(this.landedFormName);
    let url_string = window.location.href;
    let url = new URL(url_string);
    let tab = url.searchParams.get("tab");
    if (this.subTabItems.length > 0) {
      if (tab) {
        this.openedSubTab = tab;
      } else {
        this.openedSubTab = this.subTabItems[0].value;
        if (this.subTabItems[0].value === "pfPaymentTracker") {
          this.isLoading = true;
          window.location.href = this.baseUrl + "payroll/provident-fund";
        }
      }
    }
  },
  unmounted() {
    this.isLoading = false;
  },
  methods: {
    onTabChange(tab) {
      this.currentTabItem = "tab-" + this.mainTabs.indexOf(tab);
      if (tab !== this.landedFormName) {
        const { formsWithAccess } = this.$store.getters.statutoryComponentsTabs;
        let clickedForm = formsWithAccess.find((form) => form.formName === tab);
        if (clickedForm.isPhp) {
          this.isLoading = true;
          window.location.href = this.baseUrl + `${clickedForm.url}`;
          setTimeout(() => {
            this.currentTabItem =
              "tab-" + this.mainTabs.indexOf(this.landedFormName);
          }, 1000);
        } else {
          this.$router.push(
            "/tax-and-statutory-compliance/statutory-components/" +
              clickedForm.url
          );
        }
      }
    },
    onChangeSubTabs(val) {
      if (val === "pfPaymentTracker") {
        this.isLoading = true;
        window.location.href = this.baseUrl + "payroll/provident-fund";
      }
      this.openedSubTab = val;
    },
    isActiveSubTab(val) {
      return this.openedSubTab === val;
    },
  },
};
</script>
<style scoped>
.provident-fund-container {
  padding: 58px 0px 0px 0px;
}
.sub-tabs {
  position: sticky;
  top: 118px;
  z-index: 100;
}
@media screen and (max-width: 1218px) {
  .sub-tabs {
    position: sticky;
    top: 125px;
    z-index: 100;
  }
}
</style>
