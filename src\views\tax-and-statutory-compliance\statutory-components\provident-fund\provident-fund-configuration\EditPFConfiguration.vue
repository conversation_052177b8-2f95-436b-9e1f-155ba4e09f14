<template>
  <div v-if="isMounted">
    <section :class="isMobileView ? 'mt-8' : 'mt-4'">
      <div>
        <v-card
          class="py-9 rounded-lg"
          :class="isMobileView ? '' : 'px-5'"
          elevation="5"
        >
          <v-card-text>
            <v-form ref="providentFund">
              <v-row class="d-flex justify-space-between mb-4">
                <div class="d-flex align-center">
                  <v-progress-circular
                    model-value="100"
                    color="primary"
                    :size="22"
                    class="mr-1"
                  ></v-progress-circular>
                  <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                    accessFormName
                  }}</span>
                </div>
                <div
                  class="d-flex align-center pa-1"
                  :class="isMobileView ? 'ml-auto' : ''"
                >
                  <v-btn
                    rounded="lg"
                    variant="outlined"
                    color="primary"
                    class="mr-2"
                    @click="closeEditForm()"
                  >
                    Cancel
                  </v-btn>
                  <div class="mt-2 mr-1">
                    <v-btn
                      v-if="isFormDirty"
                      rounded="lg"
                      color="primary"
                      class="mb-2"
                      @click="validateLProvidentFundForm()"
                      >Save</v-btn
                    >
                    <v-tooltip v-else location="bottom">
                      <template v-slot:activator="{ props }">
                        <v-btn
                          v-bind="props"
                          rounded="lg"
                          color="grey-lighten-3"
                          class="cursor-not-allow mb-2"
                          variant="flat"
                          >Save</v-btn
                        >
                      </template>
                      <div>There are no changes to be updated</div>
                    </v-tooltip>
                  </div>
                </div>
              </v-row>
              <v-row>
                <v-col
                  v-if="getFieldAlias(37).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <v-text-field
                    v-model="epfNumber"
                    type="text"
                    variant="solo"
                    :rules="[
                      getFieldAlias(37).Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias(37).Field_Alias}`,
                            epfNumber
                          )
                        : true,
                      epfNumber
                        ? validateWithRulesAndReturnMessages(
                            epfNumber,
                            'EPF_Number',
                            `${getFieldAlias(37).Field_Alias}`
                          )
                        : true,
                    ]"
                    @update:model-value="isFormDirty = true"
                    style="
                      max-width: 300px !important;
                      min-width: 300px !important;
                    "
                  >
                    <template v-slot:label>
                      <span>{{ getFieldAlias(37).Field_Alias }}</span>
                      <span
                        v-if="getFieldAlias(37).Mandatory_Field == 'Yes'"
                        class="ml-1"
                        style="color: red"
                        >*</span
                      >
                    </template>
                  </v-text-field>
                </v-col>

                <v-col
                  v-if="getFieldAlias(38).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                  class="d-flex"
                >
                  <CustomSelect
                    :items="employeeContributionRateList"
                    :itemSelected="employeeContributionRate"
                    :label="getFieldAlias(38).Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias(38).Mandatory_Field == 'Yes' ? true : false
                    "
                    item-title="itemName"
                    item-value="itemValue"
                    :disabled="employerContributionRate == 'Actual'"
                    @selected-item="
                      onChangeIsFormDirty($event, 'employeeContributionRate')
                    "
                    :rules="[
                      getFieldAlias(38).Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias(38).Field_Alias}`,
                            employeeContributionRate
                          )
                        : true,
                    ]"
                    style="max-width: 300px"
                  ></CustomSelect>
                </v-col>

                <v-col
                  v-if="getFieldAlias(39).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <CustomSelect
                    :items="employerContributionRateList"
                    :itemSelected="employerContributionRate"
                    :label="getFieldAlias(39).Field_Alias"
                    :is-auto-complete="true"
                    :isRequired="
                      getFieldAlias(39).Mandatory_Field == 'Yes' ? true : false
                    "
                    item-title="itemName"
                    item-value="itemValue"
                    @selected-item="
                      onChangeIsFormDirty($event, 'employerContributionRate')
                    "
                    :rules="[
                      getFieldAlias(39).Mandatory_Field == 'Yes'
                        ? required(
                            `${getFieldAlias(39).Field_Alias}`,
                            employerContributionRate
                          )
                        : true,
                    ]"
                    style="max-width: 300px"
                  ></CustomSelect>
                </v-col>

                <v-col
                  v-if="getFieldAlias(40).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">{{
                      getFieldAlias(40).Field_Alias
                    }}</span>
                    <v-switch
                      color="primary"
                      v-model="
                        pfCalculatedAsPercentageOfBasicBeyondStatutoryLimit
                      "
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>

                <v-col
                  v-if="getFieldAlias(41).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">{{
                      getFieldAlias(41).Field_Alias
                    }}</span>
                    <v-switch
                      color="primary"
                      v-model="employerContributionPartOfCTC"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col
                  v-if="getFieldAlias(42).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">{{
                      getFieldAlias(42).Field_Alias
                    }}</span>
                    <v-switch
                      color="primary"
                      v-model="adminChargePartOfCTC"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>

                <v-col
                  v-if="getFieldAlias(43).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">{{
                      getFieldAlias(43).Field_Alias
                    }}</span>
                    <v-switch
                      color="primary"
                      v-model="edliChargePartOfCTC"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>

                <v-col
                  v-if="getFieldAlias(44).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">
                      {{ getFieldAlias(44).Field_Alias }}
                      <v-tooltip
                        text="If enabled, the PF contribution rate can be manually adjusted for each employee during salary configuration or updates."
                        location="top"
                      >
                        <template v-slot:activator="{ props }">
                          <v-icon
                            class="ml-1"
                            size="x-small"
                            color="blue"
                            v-bind="props"
                          >
                            fas fa-info-circle
                          </v-icon>
                        </template>
                      </v-tooltip>
                    </span>
                    <v-switch
                      color="primary"
                      v-model="overridePFContributionRateAtEmployeeLevel"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col
                  v-if="getFieldAlias(45).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">
                      {{ getFieldAlias(45).Field_Alias }}

                      <v-tooltip
                        text="PF contribution will be pro-rated based on the number of days worked by the employee"
                        location="top"
                      >
                        <template v-slot:activator="{ props }">
                          <v-icon
                            class="ml-1"
                            size="x-small"
                            color="blue"
                            v-bind="props"
                          >
                            fas fa-info-circle
                          </v-icon>
                        </template>
                      </v-tooltip>
                    </span>
                    <v-switch
                      color="primary"
                      v-model="proRateRestrictedPFWage"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col
                  v-if="getFieldAlias(46).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">{{
                      getFieldAlias(46).Field_Alias
                    }}</span>
                    <v-switch
                      color="primary"
                      v-model="considerAllSalaryComponentsForLOP"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" lg="6" md="6">
                  <div class="d-flex flex-column">
                    <span class="text-subtitle-1 text-grey-darken-1">
                      Include in arrears calculation
                    </span>
                    <v-switch
                      color="primary"
                      v-model="includeInArrearsCalculation"
                      :true-value="'Yes'"
                      :false-value="'No'"
                      @update:model-value="isFormDirty = true"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </div>
      <AppWarningModal
        v-if="openConfirmationPopup"
        :open-modal="openConfirmationPopup"
        confirmation-heading="Are you sure to exit this form?"
        imgUrl="common/exit_form"
        @close-warning-modal="abortClose()"
        @accept-modal="acceptClose()"
      >
      </AppWarningModal>
    </section>
  </div>
  <AppSnackBar
    v-if="showValidationAlert"
    :show-snack-bar="showValidationAlert"
    snack-bar-type="warning"
    timeOut="-1"
    @close-snack-bar="closeValidationAlert"
  >
    <template #custom-alert>
      <div
        v-for="(validationMsg, index) of validationMessages"
        :key="validationMsg + index"
        class="text-subtitle-1"
      >
        {{ validationMsg }}
      </div>
      <div class="d-flex justify-end">
        <v-btn
          color="primary"
          class="mt-n5"
          variant="text"
          @click="closeValidationAlert()"
        >
          Close
        </v-btn>
      </div>
    </template>
  </AppSnackBar>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import validationRules from "@/mixins/validationRules";
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
// Queries
import { UPDATE_PROVIDENT_FUND_CONFIGURATION } from "@/graphql/tax-and-statutory-compliance/providentFundConfiguration";

export default {
  name: "EditPFConfiguration",
  mixins: [validationRules],
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    accessFormName: {
      type: String,
      required: true,
    },
    labelList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {
    CustomSelect,
  },
  data() {
    return {
      isLoading: false,
      isMounted: false,
      isFormDirty: false,
      openConfirmationPopup: false,
      epfNumber: null,
      employeeContributionRate: null,
      employerContributionRate: null,
      pfCalculatedAsPercentageOfBasicBeyondStatutoryLimit: "No",
      employerContributionPartOfCTC: "No",
      adminChargePartOfCTC: "No",
      edliChargePartOfCTC: "No",
      overridePFContributionRateAtEmployeeLevel: "No",
      proRateRestrictedPFWage: "No",
      considerAllSalaryComponentsForLOP: "No",
      includeInArrearsCalculation: "No",
      showValidationAlert: false,
      validationMessages: [],
      restrictPFWageAmount: null,
      employeeShare: null,
      employerShare: null,
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    employeeContributionRateList() {
      let options = [];
      if (this.Restricted_PF_Wage_Amount !== null) {
        options.push({
          itemName: `Restrict wage contribution to ${this.restrictPFWageAmount}`,
          itemValue: `Restricted`,
        });
      }
      if (this.Employee_Share !== null) {
        options.push({
          itemName: `${this.employeeShare}% of actual wage`,
          itemValue: `Actual`,
        });
      }
      return options;
    },
    employerContributionRateList() {
      let options = [];
      if (this.Restricted_PF_Wage_Amount !== null) {
        options.push({
          itemName: `Restrict wage contribution to ${this.restrictPFWageAmount}`,
          itemValue: `Restricted`,
        });
      }
      if (this.Employer_Share !== null) {
        options.push({
          itemName: `${this.employerShare}% of actual wage`,
          itemValue: `Actual`,
        });
      }
      return options;
    },
  },
  mounted() {
    const {
      EPF_Number,
      Employee_Contribution_Rate,
      Employer_Contribution_Rate,
      PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit,
      Employer_Contribution_Part_Of_CTC,
      Admin_Charge_Part_Of_CTC,
      Edli_Charge_Part_Of_CTC,
      Override_PF_Contribution_Rate_At_Employee_Level,
      Pro_Rate_Restricted_PF_Wage,
      Consider_All_Salary_Components_For_LOP,
      Include_In_Arrears_Calculation,
      Restricted_PF_Wage_Amount,
      Employee_Share,
      Employer_Share,
    } = this.editFormData;
    this.epfNumber =
      this.getFieldAlias(37).Field_Visiblity == "Yes" && EPF_Number
        ? EPF_Number
        : null;
    this.employeeContributionRate =
      this.getFieldAlias(38).Field_Visiblity == "Yes" &&
      Employee_Contribution_Rate
        ? Employee_Contribution_Rate
        : null;
    this.employerContributionRate =
      this.getFieldAlias(39).Field_Visiblity == "Yes" &&
      Employer_Contribution_Rate
        ? Employer_Contribution_Rate
        : null;
    this.employerContributionPartOfCTC =
      this.getFieldAlias(40).Field_Visiblity == "Yes" &&
      Employer_Contribution_Part_Of_CTC
        ? Employer_Contribution_Part_Of_CTC
        : "No";
    this.pfCalculatedAsPercentageOfBasicBeyondStatutoryLimit =
      this.getFieldAlias(41).Field_Visiblity == "Yes" &&
      PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit
        ? PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit
        : "No";

    this.adminChargePartOfCTC =
      this.getFieldAlias(42).Field_Visiblity == "Yes" &&
      Admin_Charge_Part_Of_CTC
        ? Admin_Charge_Part_Of_CTC
        : "No";
    this.edliChargePartOfCTC =
      this.getFieldAlias(43).Field_Visiblity == "Yes" && Edli_Charge_Part_Of_CTC
        ? Edli_Charge_Part_Of_CTC
        : "No";

    this.overridePFContributionRateAtEmployeeLevel =
      this.getFieldAlias(44).Field_Visiblity == "Yes" &&
      Override_PF_Contribution_Rate_At_Employee_Level
        ? Override_PF_Contribution_Rate_At_Employee_Level
        : "No";
    this.proRateRestrictedPFWage =
      this.getFieldAlias(45).Field_Visiblity == "Yes" &&
      Pro_Rate_Restricted_PF_Wage
        ? Pro_Rate_Restricted_PF_Wage
        : "No";
    this.considerAllSalaryComponentsForLOP =
      this.getFieldAlias(46).Field_Visiblity == "Yes" &&
      Consider_All_Salary_Components_For_LOP
        ? Consider_All_Salary_Components_For_LOP
        : "No";
    this.includeInArrearsCalculation = Include_In_Arrears_Calculation
      ? Include_In_Arrears_Calculation
      : "No";
    this.restrictPFWageAmount = Restricted_PF_Wage_Amount
      ? Restricted_PF_Wage_Amount
      : "-";
    this.employeeShare = Employee_Share ? Employee_Share : "-";
    this.employerShare = Employer_Share ? Employer_Share : "-";
    this.isMounted = true;
  },
  watch: {
    employerContributionRate(newValue) {
      if (newValue == "Actual") {
        this.employeeContributionRate = newValue;
      }
    },
  },
  methods: {
    getFieldAlias(fieldId) {
      return this.labelList[fieldId];
    },
    onChangeIsFormDirty(val, field) {
      if (field == "employerContributionRate") {
        this.employerContributionRate = val;
      } else if (field == "employeeContributionRate") {
        this.employeeContributionRate = val;
      }
      this.isFormDirty = true;
    },
    async validateLProvidentFundForm() {
      const { valid } = await this.$refs.providentFund.validate();
      if (valid) {
        this.updateProvidentFund();
      }
    },
    updateProvidentFund() {
      let vm = this;
      vm.isLoading = true;
      try {
        vm.$apollo
          .mutate({
            mutation: UPDATE_PROVIDENT_FUND_CONFIGURATION,
            variables: {
              EPF_Number: vm.epfNumber ? vm.epfNumber : null,
              Employee_Contribution_Rate: vm.employeeContributionRate
                ? vm.employeeContributionRate
                : null,
              Employer_Contribution_Rate: vm.employerContributionRate
                ? vm.employerContributionRate
                : null,
              Employer_Contribution_Part_Of_CTC:
                vm.employerContributionPartOfCTC
                  ? vm.employerContributionPartOfCTC
                  : null,
              PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit:
                vm.pfCalculatedAsPercentageOfBasicBeyondStatutoryLimit
                  ? vm.pfCalculatedAsPercentageOfBasicBeyondStatutoryLimit
                  : null,

              Admin_Charge_Part_Of_CTC: vm.adminChargePartOfCTC
                ? vm.adminChargePartOfCTC
                : null,
              Edli_Charge_Part_Of_CTC: vm.edliChargePartOfCTC
                ? vm.edliChargePartOfCTC
                : null,
              Override_PF_Contribution_Rate_At_Employee_Level:
                vm.overridePFContributionRateAtEmployeeLevel
                  ? vm.overridePFContributionRateAtEmployeeLevel
                  : null,
              Pro_Rate_Restricted_PF_Wage: vm.proRateRestrictedPFWage
                ? vm.proRateRestrictedPFWage
                : null,
              Consider_All_Salary_Components_For_LOP:
                vm.considerAllSalaryComponentsForLOP
                  ? vm.considerAllSalaryComponentsForLOP
                  : null,
              includeInArrearsCalculation: vm.includeInArrearsCalculation
                ? vm.includeInArrearsCalculation
                : null,
            },
            client: "apolloClientAK",
          })
          .then(() => {
            vm.isLoading = false;
            var snackbarData = {
              isOpen: true,
              type: "success",
              message: `${this.accessFormName} updated successfully.`,
            };
            vm.showAlert(snackbarData);
            vm.$emit("refetch-data");
          })
          .catch((error) => {
            vm.handleUpdateError(error);
          });
      } catch {
        vm.handleUpdateError((err = ""));
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    handleUpdateError(err = "") {
      this.isLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "updating",
          form: this.accessFormName,
          isListError: false,
        })
        .then((validationErrors) => {
          let validationMessages = [];
          for (var eCode in validationErrors) {
            validationMessages.push(validationErrors[eCode]);
          }
          this.validationMessages = validationMessages;
          this.showValidationAlert = true;
        });
    },
    closeValidationAlert() {
      this.showValidationAlert = false;
      this.validationMessages = [];
    },
    closeEditForm() {
      if (this.isFormDirty) {
        this.openConfirmationPopup = true;
      } else this.acceptClose();
    },
    abortClose() {
      this.openConfirmationPopup = false;
    },
    acceptClose() {
      this.openConfirmationPopup = false;
      this.$emit("close-form");
      this.isFormDirty = false;
    },
  },
};
</script>
