<template>
  <div v-if="isMounted">
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      :button-text="showRetryBtn ? 'Retry' : ''"
      @button-click="refetchData()"
    >
    </AppFetchErrorScreen>
    <div v-else>
      <v-row>
        <v-col v-if="!isEdit && !listLoading" cols="12">
          <ViewPFConfiguration
            :editFormData="providentFundData"
            @open-edit="openEditForm()"
            :accessFormName="accessFormName"
            :labelList="labelList"
            :formAccess="formAccess"
          ></ViewPFConfiguration>
        </v-col>
        <v-col v-if="isEdit && !listLoading" cols="12">
          <EditPFConfiguration
            :editFormData="providentFundData"
            @refetch-data="refetchData()"
            @close-form="closeEditForm()"
            :accessFormName="accessFormName"
            :labelList="labelList"
          >
          </EditPFConfiguration>
        </v-col>
      </v-row>
    </div>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const EditPFConfiguration = defineAsyncComponent(() =>
  import("./EditPFConfiguration.vue")
);
// components
import ViewPFConfiguration from "./ViewPFConfiguration.vue";

// Queries
import { RETRIEVE_PROVIDENT_FUND_CONFIGURATION } from "@/graphql/tax-and-statutory-compliance/providentFundConfiguration";

export default {
  name: "PFConfigurationMainForm",
  components: {
    ViewPFConfiguration,
    EditPFConfiguration,
  },
  data() {
    return {
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      providentFundData: {},
      isEdit: false,
      isMounted: false,
    };
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formAccess() {
      let pfAccess = this.accessRights("52");
      if (pfAccess && pfAccess.accessRights && pfAccess.accessRights["view"]) {
        return pfAccess.accessRights;
      } else return false;
    },
    accessFormName() {
      let pfAccess = this.accessRights("52");
      if (pfAccess && pfAccess.customFormName) {
        return pfAccess.customFormName;
      } else return "Provident Fund Configuration";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("PF Error:", err);
    let msg = `Something went wrong while loading the ${this.accessFormName.toLowerCase()} form. Please try after some time.`;
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },
  mounted() {
    if (this.formAccess && this.isSuperAdmin) {
      this.fetchProvidentFundDetails();
    }

    this.isMounted = true;
  },
  methods: {
    openEditForm() {
      this.isEdit = true;
    },
    closeEditForm() {
      this.isEdit = false;
    },
    refetchData() {
      this.closeEditForm();
      this.fetchProvidentFundDetails();
    },
    fetchProvidentFundDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_PROVIDENT_FUND_CONFIGURATION,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveProvidentFundConfiguration
          ) {
            let providentFundData = JSON.parse(
              response.data.retrieveProvidentFundConfiguration.pfConfigData
            );
            vm.providentFundData = providentFundData[0];
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
          vm.listLoading = false;
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
