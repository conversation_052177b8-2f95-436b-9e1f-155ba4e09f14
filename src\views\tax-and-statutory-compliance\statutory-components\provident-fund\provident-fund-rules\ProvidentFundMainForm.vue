<template>
  <div v-if="isMounted">
    <div v-if="listLoading || slabWiseLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      :button-text="showRetryBtn ? 'Retry' : ''"
      @button-click="refetchData()"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="noFieldVisibility && !listLoading"
      key="no-results-screen"
    >
      <template #contentSlot>
        <div style="max-width: 80%" class="mx-auto">
          <v-row
            v-if="!isLoading"
            style="background: white"
            class="rounded-lg pa-5 mb-4"
            :class="isMobileView ? 'mt-n16' : ''"
          >
            <v-col cols="12">
              <NotesCard
                notes="This feature is currently unavailable for your organization."
                backgroundColor="transparent"
                class="mb-2"
              ></NotesCard>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <v-row>
        <v-col v-if="!isEdit && !listLoading" cols="12">
          <ViewProvidentFund
            :editFormData="providentFundData"
            @open-edit="openEditForm()"
            :accessFormName="accessFormName"
            :labelList="labelList"
            :formAccess="formAccess"
          ></ViewProvidentFund>
        </v-col>
        <v-col v-if="isEdit && !listLoading" cols="12">
          <EditProvidentFund
            :editFormData="providentFundData"
            @refetch-data="refetchData()"
            @close-form="closeEditForm()"
            :accessFormName="accessFormName"
            :labelList="labelList"
          >
          </EditProvidentFund>
        </v-col>
        <v-col v-if="slabList.length > 0 && !listLoading" cols="12">
          <ProvidentFundSlabList :slabList="slabList"></ProvidentFundSlabList>
        </v-col>
      </v-row>
    </div>
  </div>
</template>
<script>
import { defineAsyncComponent } from "vue";
const EditProvidentFund = defineAsyncComponent(() =>
  import("./EditProvidentFund.vue")
);
// components
import ViewProvidentFund from "./ViewProvidentFund.vue";
import ProvidentFundSlabList from "./ProvidentFundSlabList.vue";

// Queries
import {
  RETRIEVE_PROVIDENT_FUND_RULES,
  RETRIEVE_PROVIDENT_FUND_RULES_SLAB_DATA,
} from "@/graphql/tax-and-statutory-compliance/providentFundRules";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
export default {
  name: "ProvidentFundMainForm",
  components: {
    ViewProvidentFund,
    ProvidentFundSlabList,
    EditProvidentFund,
    NotesCard,
  },
  data() {
    return {
      isLoading: false,
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      providentFundData: {},
      isEdit: false,
      slabList: [],
      isSlabWise: "No",
      slabWiseLoading: false,
      isMounted: false,
    };
  },
  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    formAccess() {
      let pfAccess = this.accessRights("259");
      if (pfAccess && pfAccess.accessRights && pfAccess.accessRights["view"]) {
        return pfAccess.accessRights;
      } else return false;
    },
    accessFormName() {
      let pfAccess = this.accessRights("259");
      if (pfAccess && pfAccess.customFormName) {
        return pfAccess.customFormName;
      } else return "Provident Fund Rules";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    noFieldVisibility() {
      let visibility = false;
      for (let field in this.labelList) {
        if (this.labelList[field].Field_Visiblity === "Yes") {
          visibility = true;
          break;
        }
      }
      return !visibility;
    },
  },
  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("PF Error:", err);
    let msg = `Something went wrong while loading the ${this.accessFormName.toLowerCase()} form. Please try after some time.`;
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },
  mounted() {
    if (this.formAccess && this.isSuperAdmin) {
      this.fetchProvidentFundDetails();
      this.getSlabWisePf();
    }
    this.isMounted = true;
  },
  methods: {
    openEditForm() {
      this.isEdit = true;
    },
    closeEditForm() {
      this.isEdit = false;
    },
    refetchData() {
      this.closeEditForm();
      this.fetchProvidentFundDetails();
    },
    async getSlabWisePf() {
      let vm = this;
      vm.slabWiseLoading = true;
      await this.$store
        .dispatch("retrievePayrollGeneralSettings")
        .then((response) => {
          if (response) {
            vm.isSlabWise = response[0].Slab_Wise_PF;
            if (vm.isSlabWise == "Yes") {
              vm.fetchProvidentFundSlabList();
            }
          }
          vm.slabWiseLoading = false;
        })
        .catch(() => {
          vm.slabWiseLoading = false;
        });
    },
    fetchProvidentFundDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_PROVIDENT_FUND_RULES,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.retrieveProvidentFundRules) {
            let providentFundData = JSON.parse(
              response.data.retrieveProvidentFundRules.response
            );
            vm.providentFundData = providentFundData[0];
            vm.listLoading = false;
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    fetchProvidentFundSlabList() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_PROVIDENT_FUND_RULES_SLAB_DATA,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveProvidentFundRulesSlabWiseData
          ) {
            let slabList =
              response.data.retrieveProvidentFundRulesSlabWiseData.slabWiseData;
            if (slabList && slabList.length) {
              vm.slabList = JSON.parse(slabList);
            }

            vm.listLoading = false;
          } else {
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
