<template>
  <div v-if="isMounted">
    <section :class="isMobileView ? 'mt-8' : 'mt-4'">
      <div>
        <v-card
          class="py-9 rounded-lg"
          :class="isMobileView ? '' : 'px-5'"
          elevation="5"
        >
          <v-card-text>
            <v-form ref="providentFund">
              <v-row class="d-flex justify-space-between mb-4">
                <div class="d-flex align-center">
                  <v-progress-circular
                    model-value="100"
                    color="primary"
                    :size="22"
                    class="mr-1"
                  ></v-progress-circular>
                  <span class="text-h6 text-grey-darken-1 font-weight-bold">{{
                    accessFormName
                  }}</span>
                </div>
                <v-avatar
                  v-if="isSuperAdmin && formAccess.update"
                  @click="$emit('open-edit')"
                  :size="30"
                  color="primary"
                  class="cursor-pointer"
                  :class="isMobileView ? 'ml-auto mt-2' : ''"
                >
                  <v-icon color="white" :size="12">fas fa-pencil-alt</v-icon>
                </v-avatar>
              </v-row>
              <v-row>
                <v-col
                  v-if="getFieldAlias(28).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(28).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Restricted_PF_Wage_Amount) }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(29).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(29).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ editFormData.Employee_Share }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(30).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(30).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ editFormData.Employer_Share }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(31).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(31).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ editFormData.Employees_Pension_Scheme }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(32).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(32).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ editFormData.EPF_Account_Share }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(33).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(33).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ editFormData.Admin_Charge }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(34).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(34).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Admin_Charge_Max_Amount) }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(35).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(35).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ editFormData.EDLI_Charge }}
                  </p>
                </v-col>

                <v-col
                  v-if="getFieldAlias(36).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(36).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.EDLI_Charge_Max_Amount) }}
                  </p>
                </v-col>
                <v-col
                  v-if="getFieldAlias(47).Field_Visiblity == 'Yes'"
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(47).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Auto_Declaration) }}
                  </p>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias(48).Field_Visiblity == 'Yes' &&
                    editFormData.Auto_Declaration == 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(48).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{ checkNullValue(editFormData.Investment_Category) }}
                  </p>
                </v-col>
                <v-col
                  v-if="
                    getFieldAlias(49).Field_Visiblity == 'Yes' &&
                    editFormData.Auto_Declaration == 'Yes'
                  "
                  cols="12"
                  lg="6"
                  md="6"
                >
                  <p class="text-subtitle-1 text-grey-darken-1">
                    {{ getFieldAlias(49).Field_Alias }}
                  </p>
                  <p class="text-subtitle-1 font-weight-regular">
                    {{
                      checkNullValue(
                        editFormData.Auto_Declaration_Applicable_For
                      )
                    }}
                  </p>
                </v-col>
              </v-row>
              <v-row>
                <v-col v-if="moreDetailsList.length > 0" cols="12">
                  <MoreDetails
                    :more-details-list="moreDetailsList"
                    :open-close-card="openMoreDetails"
                    @on-open-close="openMoreDetails = $event"
                  ></MoreDetails>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </div>
    </section>
  </div>

  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";
import { convertUTCToLocal, checkNullValue } from "@/helper.js";
export default {
  name: "ViewProvidentFund",
  props: {
    editFormData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    accessFormName: {
      type: String,
      required: true,
    },
    labelList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    formAccess: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    MoreDetails,
  },
  data() {
    return {
      moreDetailsList: [],
      openMoreDetails: true,
      isLoading: false,
      isMounted: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    isSuperAdmin() {
      let formAccessRights = this.accessRights("147");
      if (
        formAccessRights &&
        formAccessRights.accessRights &&
        formAccessRights.accessRights["optionalChoice"]
      ) {
        return formAccessRights.accessRights;
      } else return false;
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
  },
  watch: {
    editFormData: {
      immediate: true,
      handler() {
        this.prefillMoreDetails();
      },
    },
  },
  mounted() {
    this.prefillMoreDetails();
    this.isMounted = true;
  },
  methods: {
    checkNullValue,
    convertUTCToLocal,
    getFieldAlias(fieldId) {
      return this.labelList[fieldId];
    },
    prefillMoreDetails() {
      this.moreDetailsList = [];
      // to form more details array based on this values
      const updatedByName = this.editFormData.Updated_By,
        updatedOn = this.convertUTCToLocal(this.editFormData.Updated_On);

      if (updatedByName && updatedOn) {
        this.moreDetailsList.push({
          actionDate: updatedOn,
          actionBy: updatedByName,
          text: "Updated",
        });
      }
    },
  },
};
</script>
