<template>
  <div class="ml-5 mr-10">
    <v-menu
      v-model="openFormFilter"
      min-width="150"
      z-index="10000"
      :close-on-content-click="false"
      class="filter-menu-position"
    >
      <template v-slot:activator="{ props }">
        <v-avatar
          class="cursor-pointer"
          size="38"
          color="primary"
          v-bind="props"
        >
          <v-icon size="15" color="white">fas fa-filter</v-icon>
        </v-avatar>
      </template>
      <v-list class="pa-5" min-width="500" max-width="600" max-height="80vh">
        <div class="d-flex justify-end mt-n2 mr-n2">
          <v-icon
            color="secondary"
            style="position: fixed"
            @click="openFormFilter = !openFormFilter"
          >
            fas fa-times</v-icon
          >
        </div>
        <v-row class="mr-2 mt-2">
          <v-col
            class="py-2 mb-6"
            v-if="filterFormId === 34"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedAppliedDateFrom"
              placeholder="Resignation From"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            class="py-2 mb-6"
            v-if="filterFormId === 34"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedExitDateTo"
              placeholder="Resignation To"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            v-if="
              filterFormId !== 15 &&
              filterFormId !== 291 &&
              filterFormId !== 290
            "
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedEmpName"
              color="secondary"
              :items="empNameList"
              label="Employee"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              item-title="employeeName"
              item-value="employeeId"
            >
            </v-autocomplete>
          </v-col>
          <v-col
            v-if="filterFormId == 291 || filterFormId == 290"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedPosition"
              color="secondary"
              :items="positionList"
              label="Position Name"
              clearable
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              item-title="Pos_full_Name"
              item-value="Pos_Code"
            >
            </v-autocomplete>
          </v-col>
          <v-col
            v-if="filterFormId == 291 || filterFormId == 290"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedGroup"
              color="secondary"
              :items="groupList"
              item-title="Pos_full_Name"
              clearable
              item-value="Pos_Code"
              label="Group Code"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            v-if="filterFormId == 291 || filterFormId == 290"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedDivision"
              color="secondary"
              :items="divisionList"
              label="Division Code"
              clearable
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            v-if="filterFormId == 291 || filterFormId == 290"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-text-field
              v-model="selectedNoOfPositions"
              color="secondary"
              label="No. of Positions"
              type="number"
              :min="0"
              density="compact"
              single-line
            >
            </v-text-field>
          </v-col>
          <v-col
            v-if="filterFormId === 267"
            class="py-2 mb-6"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedReimbursementAppliedFrom"
              placeholder="Applied From"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            v-if="filterFormId === 267"
            class="py-2 mb-6"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedReimbursementAppliedTo"
              placeholder="Applied To"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            v-if="filterFormId === 268"
            class="py-2 mb-6"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedWeekendDateFrom"
              placeholder="Weekend Date From"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            v-if="filterFormId === 268"
            class="py-2 mb-6"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedWeekendDateTo"
              placeholder="Weekend Date To"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 31 || filterFormId === 253"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLeaveType"
              color="secondary"
              :items="leaveTypeList"
              label="Leave Type"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              filterFormId === 31 ||
              filterFormId === 244 ||
              filterFormId === 245 ||
              filterFormId === 246 ||
              filterFormId === 352
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <!-- <datepicker
              :format="orgDateFormat"
              v-model="selectedStartDateFrom"
              placeholder="Start Date"
              style="width: 100%"
            ></datepicker
          > -->
            <DatePickerField
              v-model="selectedStartDateFrom"
              label="Start Date"
              variant="filled"
              density="compact"
              :single-line="true"
            ></DatePickerField>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              filterFormId === 31 ||
              filterFormId === 244 ||
              filterFormId === 245 ||
              filterFormId === 246 ||
              filterFormId === 352
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <!-- <datepicker
              :format="orgDateFormat"
              v-model="selectedEndDateTo"
              placeholder="End Date"
              style="width: 100%"
            ></datepicker> -->
            <DatePickerField
              v-model="selectedEndDateTo"
              label="End Date"
              variant="filled"
              density="compact"
              :single-line="true"
            ></DatePickerField>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 334"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <DatePickerField
              v-model="workedDate"
              label="Worked Date"
              variant="filled"
              density="compact"
              :single-line="true"
            ></DatePickerField>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 334"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <DatePickerField
              v-model="compOffDate"
              label="Compensatory Off Date"
              variant="filled"
              density="compact"
              :single-line="true"
            ></DatePickerField>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              filterFormId === 31 ||
              filterFormId === 244 ||
              filterFormId === 253 ||
              filterFormId === 334
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLeaveDuration"
              color="secondary"
              :items="leaveDurationList"
              label="Duration"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              filterFormId === 31 ||
              filterFormId === 244 ||
              filterFormId === 253
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLeavePeriod"
              color="secondary"
              :items="leavePeriodList"
              :label="filterFormId === 31 ? 'Leave Period' : 'Period'"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 31"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedSelfApply"
              color="secondary"
              :items="yesNoList"
              label="Self Apply"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              item-title="text"
              item-value="value"
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 31"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLateAttendance"
              color="secondary"
              :items="yesNoList"
              label="Late Attendance"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
              item-title="text"
              item-value="value"
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 31"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLeaveStatus"
              color="secondary"
              :items="leaveStatusList"
              label="Leave Status"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 334 || filterFormId === 352"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedStatus"
              color="secondary"
              :items="statusList"
              label="Status"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              filterFormId === 244 ||
              filterFormId === 245 ||
              filterFormId === 246
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedPreApprovalStatus"
              color="secondary"
              :items="preApprovalStatusList"
              label="Status"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 253"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLopRecoveryReason"
              color="secondary"
              :items="lopRecoveryReasonList"
              label="Reason"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 253"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLopRecoveryMonth"
              color="secondary"
              :items="recoveryMonthList"
              label="Recovery Month"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>

          <v-col
            class="py-2 mb-6"
            v-if="filterFormId === 15"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedJobPostFrom"
              placeholder="Job Posting From"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            class="py-2 mb-6"
            v-if="filterFormId === 15"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedJobPostTo"
              placeholder="Job Post Ending On"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            class="py-2 mb-6"
            v-if="filterFormId === 15"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedJoiningDateFrom"
              placeholder="Expected Joining Date From"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            class="py-2 mb-6"
            v-if="filterFormId === 15"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <datepicker
              :format="orgDateFormat"
              v-model="selectedJoiningDateTo"
              placeholder="Expected Joining Date To"
              style="width: 100%"
            ></datepicker
          ></v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 15"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedJobPostTitle"
              color="secondary"
              :items="jobPostTitleList"
              label="Job Post Name"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 15"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedPriority"
              color="secondary"
              :items="priorityList"
              label="Priority"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 352"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedRequestFor"
              color="secondary"
              :items="['On Duty', 'Permission']"
              label="Request For"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 352"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLateArrival"
              color="secondary"
              :items="['Yes', 'No']"
              label="Late Arrival"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 352 || filterFormId === 31"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedEarlyCheckout"
              color="secondary"
              :items="['Yes', 'No']"
              label="Early Checkout"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 31"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedAttendanceShortage"
              color="secondary"
              :items="['Yes', 'No']"
              label="Attendance Shortage"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 31"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedAttendanceFinalization"
              color="secondary"
              :items="['Yes', 'No']"
              label="Auto Loss of Pay"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              filterFormId === 31 ||
              filterFormId === 352 ||
              filterFormId === 334
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedEmployeeType"
              color="secondary"
              :items="employeeTypeList"
              label="Employee Type"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              filterFormId === 352 ||
              filterFormId === 31 ||
              filterFormId === 334
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedDesignation"
              color="secondary"
              :items="designationList"
              label="Designation"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              filterFormId === 352 ||
              filterFormId === 31 ||
              filterFormId === 334
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedDepartment"
              color="secondary"
              :items="departmentList"
              label="Department"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              filterFormId === 352 ||
              filterFormId === 31 ||
              filterFormId === 334
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedLocation"
              color="secondary"
              :items="locationList"
              label="Location"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 352 || filterFormId === 31"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedWorkSchedule"
              color="secondary"
              :items="workScheduleList"
              label="Work Schedule"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="filterFormId === 352 || filterFormId === 334"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedBusinessUnit"
              color="secondary"
              :items="businessUnitList"
              label="Business Unit"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="
              (filterFormId === 352 ||
                filterFormId === 31 ||
                filterFormId === 334) &&
              fieldForce &&
              (isAdmin || isServiceProviderAdmin)
            "
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedServiceProvider"
              color="secondary"
              :items="serviceProviderList"
              :label="labelList[115]?.Field_Alias || 'Service Provider'"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            v-if="!isMyApproval && !isGroupApproval"
            class="py-2"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedApprover"
              color="secondary"
              :items="approverList"
              label="Approver Name"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="isApprovalHistory"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedApprovalStatus"
              color="secondary"
              :items="approvalStatusList"
              label="Approval Status"
              multiple
              closable-chips
              chips
              density="compact"
              single-line
            >
            </v-autocomplete>
          </v-col>
          <v-col
            class="py-2"
            v-if="isApprovalHistory"
            :cols="windowWidth > 600 ? 6 : 12"
          >
            <v-autocomplete
              v-model="selectedApprovalBy"
              color="secondary"
              :items="approvalByList"
              label="Listing Criteria"
            >
            </v-autocomplete>
          </v-col>
        </v-row>
        <v-btn
          variant="elevated"
          class="mr-4 secondary"
          rounded="lg"
          @click.stop="fnApplyFilter('manual')"
        >
          <span class="primary">Apply</span>
        </v-btn>
        <v-btn
          class="primary"
          rounded="lg"
          variant="outlined"
          @click="resetFilterValues()"
        >
          <span class="primary">Reset</span>
        </v-btn>
      </v-list>
    </v-menu>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import Datepicker from "vuejs3-datepicker";
import moment from "moment";
import DatePickerField from "@/components/custom-components/DatePickerField.vue";
export default defineComponent({
  name: "FormFilter",

  components: {
    Datepicker,
    DatePickerField,
  },

  props: {
    items: {
      type: Array,
      required: true,
      default: () => [],
    },
    resetFilterCount: {
      type: Number,
      default: 0,
    },
    applyFilterCount: {
      type: Number,
      default: 0,
    },
    filterFormId: {
      type: [String, Number],
      required: false,
    },
    approvalType: {
      type: String,
      default: "",
    },
  },

  data: () => ({
    openFormFilter: false,
    empNameList: [],
    approverList: [],
    selectedEmpName: [],
    selectedApprover: [],
    // leave
    leaveTypeList: [],
    selectedLeaveType: [],
    selectedStartDateFrom: "",
    selectedEndDateTo: "",
    leaveDurationList: ["Full Day", "Half Day"],
    selectedLeaveDuration: [],
    leavePeriodList: ["First Half", "Second Half"],
    selectedLeavePeriod: [],
    selectedPosition: [],
    selectedGroup: [],
    selectedDivision: [],
    selectedNoOfPositions: null,
    divisionList: [],
    yesNoList: [
      {
        value: 1,
        text: "Yes",
      },
      {
        value: 0,
        text: "No",
      },
    ],
    selectedSelfApply: [],
    selectedLateAttendance: [],
    selectedLeaveStatus: [],
    // resignation
    selectedAppliedDateFrom: "",
    selectedExitDateTo: "",
    // reimbursement
    selectedReimbursementAppliedFrom: "",
    selectedReimbursementAppliedTo: "",
    // timesheets
    selectedWeekendDateFrom: "",
    selectedWeekendDateTo: "",
    // job post
    jobPostTitleList: [],
    selectedJobPostTitle: [],
    selectedJobPostFrom: "",
    selectedJobPostTo: "",
    priorityList: ["Low", "Medium", "High"],
    selectedPriority: [],
    selectedJoiningDateFrom: "",
    selectedJoiningDateTo: "",
    // history
    approvalStatusList: ["Approved", "Rejected"],
    selectedApprovalStatus: [],
    approvalByList: ["All", "Actioned By Me", "Assigned For Me"],
    selectedApprovalBy: "Assigned For Me",
    // pre approvals
    selectedPreApprovalStatus: [],
    // lop recovery
    selectedLopRecoveryReason: [],
    lopRecoveryReasonList: [],
    recoveryMonthList: [],
    selectedLopRecoveryMonth: [],
    // short time off
    selectedRequestFor: [],
    selectedLateArrival: [],
    selectedEarlyCheckout: [],
    selectedLocation: [],
    selectedDesignation: [],
    designationList: [],
    locationList: [],
    selectedDepartment: [],
    departmentList: [],
    selectedWorkSchedule: [],
    workScheduleList: [],
    selectedBusinessUnit: [],
    businessUnitList: [],
    selectedAttendanceShortage: [],
    selectedAttendanceFinalization: [],
    selectedEmployeeType: [],
    employeeTypeList: [],
    workedDate: null,
    compOffDate: null,
    statusList: [],
    selectedStatus: [],
    serviceProviderList: [],
    selectedServiceProvider: [],
  }),

  computed: {
    labelList() {
      return this.$store.state.customFormFields;
    },
    fieldForce() {
      return this.$store.state.orgDetails.fieldForce;
    },
    isAdmin() {
      return this.$store.state.isAdmin;
    },
    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    isServiceProviderAdmin() {
      let serviceProviderAdminAccess = this.accessRights(
        "service-provider-admin"
      );
      if (
        serviceProviderAdminAccess &&
        serviceProviderAdminAccess.accessRights &&
        serviceProviderAdminAccess.accessRights.update
      ) {
        return true;
      }
      return false;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    isApprovalHistory() {
      return this.approvalType === "Approval History";
    },
    isMyApproval() {
      return this.approvalType === "My Approvals";
    },
    isGroupApproval() {
      return this.approvalType === "Group Approvals";
    },
    orgDateFormat() {
      let format = this.$store.state.orgDetails.orgDateFormat;
      format = format.replace("YYYY", "yyyy");
      return format.replace("DD", "dd");
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    isNoOnlySelectedInLateAttendance() {
      return (
        this.selectedLateAttendance &&
        this.selectedLateAttendance.length === 1 &&
        this.selectedLateAttendance[0] === 0
      );
    },
    leaveStatusList() {
      if (this.isApprovalHistory) {
        return [
          "Applied",
          "Approved",
          "Rejected",
          "Returned",
          "Cancelled",
          "Cancel Applied",
        ];
      } else {
        return ["Applied", "Returned", "Cancel Applied"];
      }
    },

    preApprovalStatusList() {
      if (this.isApprovalHistory) {
        return [
          "Applied",
          "Approved",
          "Rejected",
          "Returned",
          "Cancel Applied",
        ];
      } else {
        return ["Applied", "Returned", "Cancel Applied"];
      }
    },
    positionList() {
      const positionSet = new Set();
      return (
        this.items
          ?.map((item) => {
            const positionCode = item.instanceData?.positionCode || "";
            const positionTitle = item.instanceData?.positionTitle || "";
            const positionKey = `${positionCode} - ${positionTitle}`;
            if (!positionSet.has(positionKey)) {
              positionSet.add(positionKey);
              return {
                Pos_Code: positionCode,
                Pos_full_Name: positionKey,
              };
            }
          })
          .filter((item) => !!item) || []
      );
    },
    groupList() {
      const groupSet = new Set();
      return (
        this.items
          ?.map((item) => {
            const groupCode = item.instanceData?.groupCode || "";
            const groupName = item.instanceData?.groupName || "";
            const groupKey = `${groupCode} - ${groupName}`;
            if (!groupSet.has(groupKey)) {
              groupSet.add(groupKey);
              return {
                Pos_Code: groupCode,
                Pos_full_Name: groupKey,
              };
            }
          })
          .filter((item) => !!item) || []
      );
    },
  },

  watch: {
    items() {
      this.formFilterData();
    },
    approvalType() {
      this.resetFilterValues();
    },
    resetFilterCount(val) {
      if (val > 0) {
        this.resetFilterValues();
      }
    },
    applyFilterCount(val) {
      if (val > 0) {
        this.fnApplyFilter("auto");
      } else {
        this.resetFilterValues();
      }
    },
    selectedApprovalBy(val) {
      this.$emit("selected-approval-by", val);
    },
  },

  mounted() {
    this.formFilterData();
  },

  methods: {
    // apply filter
    fnApplyFilter(action) {
      this.openFormFilter = false;
      let filteredArray = this.items;
      if (this.selectedStartDateFrom && this.selectedEndDateTo) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            let startKey =
              this.filterFormId === 31 ? "startDate" : "Start_Date";
            let endKey = this.filterFormId === 31 ? "endDate" : "End_Date";
            let leaveStartDate = item.instanceData[startKey]
              ? moment(item.instanceData[startKey])
              : "";
            let leaveEndDate = item.instanceData[endKey]
              ? moment(item.instanceData[endKey])
              : "";
            if (
              leaveStartDate &&
              leaveStartDate.isBetween(
                moment(this.selectedStartDateFrom),
                moment(this.selectedEndDateTo)
              )
            ) {
              return item;
            } else if (
              leaveEndDate &&
              leaveEndDate.isBetween(
                moment(this.selectedStartDateFrom),
                moment(this.selectedEndDateTo)
              )
            ) {
              return item;
            } else if (
              leaveStartDate &&
              leaveEndDate &&
              moment(this.selectedStartDateFrom).isBetween(
                leaveStartDate,
                leaveEndDate
              )
            ) {
              return item;
            } else if (
              leaveStartDate &&
              leaveEndDate &&
              moment(this.selectedEndDateTo).isBetween(
                leaveStartDate,
                leaveEndDate
              )
            ) {
              return item;
            } else if (
              leaveStartDate &&
              leaveStartDate.isSame(
                moment(this.selectedStartDateFrom).format("YYYY-MM-DD")
              )
            ) {
              return item;
            } else if (
              leaveEndDate &&
              leaveEndDate.isSame(
                moment(this.selectedEndDateTo).format("YYYY-MM-DD")
              )
            ) {
              return item;
            }
          }
        });
      } else if (this.selectedStartDateFrom) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            let startKey =
              this.filterFormId === 31 ? "startDate" : "Start_Date";
            return moment(item.instanceData[startKey]).isSame(
              moment(this.selectedStartDateFrom).format("YYYY-MM-DD")
            );
          } else return item;
        });
      } else if (this.selectedEndDateTo) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            let endKey = this.filterFormId === 31 ? "endDate" : "End_Date";
            return moment(item.instanceData[endKey]).isSame(
              moment(this.selectedEndDateTo).format("YYYY-MM-DD")
            );
          } else return item;
        });
      }
      if (this.selectedRequestFor.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedRequestFor.includes(item.requestFor);
        });
      }
      if (this.selectedLateArrival.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLateArrival.includes(
            item.instanceData.Late_Attendance
          );
        });
      }
      if (this.selectedEarlyCheckout.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEarlyCheckout.includes(item.earlyCheckout);
        });
      }
      if (this.selectedLocation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLocation.includes(item.location);
        });
      }
      if (this.selectedDesignation.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDesignation.includes(item.designation);
        });
      }
      if (this.selectedDepartment.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedDepartment.includes(item.department);
        });
      }
      if (this.selectedServiceProvider.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedServiceProvider.includes(item.serviceProvider);
        });
      }
      if (this.selectedAttendanceShortage.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedAttendanceShortage.includes(
            item.attendanceShortage
          );
        });
      }
      if (this.selectedAttendanceFinalization.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedAttendanceFinalization.includes(item.autoLOP);
        });
      }
      if (this.selectedEmployeeType.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmployeeType.includes(item.empType);
        });
      }
      if (this.workedDate) {
        filteredArray = filteredArray.filter((item) => {
          return moment(item.instanceData.Worked_Date).isSame(
            moment(this.workedDate)
          );
        });
      }
      if (this.compOffDate) {
        filteredArray = filteredArray.filter((item) => {
          return moment(item.instanceData.Compensatory_Date).isSame(
            moment(this.compOffDate)
          );
        });
      }
      if (this.selectedStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedStatus.includes(item.approvalStatus);
        });
      }
      if (this.selectedWorkSchedule.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedWorkSchedule.includes(item.workSchedule);
        });
      }
      if (this.selectedBusinessUnit.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedBusinessUnit.includes(item.businessUnit);
        });
      }
      if (this.selectedEmpName.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedEmpName.includes(item.userDefinedEmpId);
        });
      }
      if (this.selectedApprover.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedApprover.includes(item.approver);
        });
      }
      if (this.selectedLeaveType.length > 0) {
        if (this.filterFormId === 253) {
          filteredArray = filteredArray.filter((item) => {
            let leaveNameArray = item.instanceData.Leave_Name
              ? item.instanceData.Leave_Name.split(",")
              : [];
            return this.selectedLeaveType.some((element) =>
              leaveNameArray.includes(element)
            );
          });
        } else {
          filteredArray = filteredArray.filter((item) => {
            return this.selectedLeaveType.includes(item.leaveType);
          });
        }
      }
      if (this.selectedLeaveDuration.length > 0) {
        if (this.filterFormId === 253) {
          filteredArray = filteredArray.filter((item) => {
            let durationArray = item.instanceData.Duration
              ? item.instanceData.Duration.split(",")
              : [];
            return this.selectedLeaveDuration.some((element) =>
              durationArray.includes(element)
            );
          });
        } else {
          filteredArray = filteredArray.filter((item) => {
            return this.selectedLeaveDuration.includes(
              item.moreDetails.duration
            );
          });
        }
      }
      if (this.selectedLeavePeriod.length > 0) {
        if (this.filterFormId === 253) {
          filteredArray = filteredArray.filter((item) => {
            let periodArray = item.instanceData.Period
              ? item.instanceData.Period.split(",")
              : [];
            return this.selectedLeavePeriod.some((element) =>
              periodArray.includes(element)
            );
          });
        } else {
          filteredArray = filteredArray.filter((item) => {
            let periodKey = this.filterFormId === 31 ? "leavePeriod" : "period";
            return this.selectedLeavePeriod.includes(
              item.moreDetails[periodKey]
            );
          });
        }
      }
      if (this.selectedSelfApply.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return this.selectedSelfApply.includes(
              item.instanceData.isSelfApply
            );
          } else return item;
        });
      }
      if (this.selectedLateAttendance.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return this.selectedLateAttendance.includes(
              item.instanceData.lateAttendance
            );
          } else return item;
        });
      }
      if (this.selectedAppliedDateFrom) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.appliedDate).isSameOrAfter(
              this.selectedAppliedDateFrom
            );
          } else return item;
        });
      }
      if (this.selectedExitDateTo) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.exitDate).isSameOrBefore(
              this.selectedExitDateTo
            );
          } else return item;
        });
      }
      if (this.selectedReimbursementAppliedFrom) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.submissionDate).isSameOrAfter(
              this.selectedReimbursementAppliedFrom
            );
          } else return item;
        });
      }
      if (this.selectedReimbursementAppliedTo) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.submissionDate).isSameOrBefore(
              this.selectedReimbursementAppliedTo
            );
          } else return item;
        });
      }
      if (this.selectedWeekendDateFrom) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.Week_Ending_Date).isSameOrAfter(
              this.selectedWeekendDateFrom
            );
          } else return item;
        });
      }
      if (this.selectedWeekendDateTo) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.Week_Ending_Date).isSameOrBefore(
              this.selectedWeekendDateTo
            );
          } else return item;
        });
      }
      if (this.selectedApprovalStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedApprovalStatus.includes(item.taskStatus);
        });
      }
      if (this.selectedLeaveStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLeaveStatus.includes(
            item.instanceData.approvalStatus
          );
        });
      }
      if (this.selectedPreApprovalStatus.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedPreApprovalStatus.includes(
            item.instanceData.Status
          );
        });
      }
      if (this.selectedLopRecoveryReason.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          let reasonArray = item.instanceData.Reason
            ? item.instanceData.Reason.split(",")
            : [];
          return this.selectedLopRecoveryReason.some((element) =>
            reasonArray.includes(element)
          );
        });
      }
      if (this.selectedLopRecoveryMonth.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          return this.selectedLopRecoveryMonth.includes(
            item.lopRecoveryProcessingMonth
          );
        });
      }
      if (this.isApprovalHistory && this.selectedApprovalBy) {
        filteredArray = filteredArray.filter((item) => {
          if (this.selectedApprovalBy === "Actioned By Me") {
            return item.completed_by == this.loginEmployeeId;
          } else if (this.selectedApprovalBy === "Assigned For Me") {
            return item.assignee == this.loginEmployeeId;
          } else {
            return true;
          }
        });
      }
      if (this.selectedJobPostFrom) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.postingDate).isSameOrAfter(
              this.selectedJobPostFrom
            );
          } else return item;
        });
      }
      if (this.selectedJobPostTo) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.closingDate).isSameOrBefore(
              this.selectedJobPostTo
            );
          } else return item;
        });
      }
      if (this.selectedPriority.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return this.selectedPriority.includes(item.instanceData.priority);
          } else return item;
        });
      }
      if (this.selectedJobPostTitle.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return this.selectedJobPostTitle.includes(
              item.instanceData.jobPostName
            );
          } else return item;
        });
      }
      if (this.selectedJoiningDateFrom) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.expectedJoiningDate).isSameOrAfter(
              this.selectedJoiningDateFrom
            );
          } else return item;
        });
      }
      if (this.selectedJoiningDateTo) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return moment(item.instanceData.expectedJoiningDate).isSameOrBefore(
              this.selectedJoiningDateTo
            );
          } else return item;
        });
      }
      if (this.selectedPosition.length > 0) {
        const selectedPos = this.selectedPosition.map((position) => {
          return position.toLowerCase();
        });
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return selectedPos.includes(
              item.instanceData.positionCode?.toLowerCase()
            );
          } else return item;
        });
      }
      if (this.selectedGroup.length > 0) {
        const selectedGroup = this.selectedGroup.map((position) => {
          return position.toLowerCase();
        });
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return selectedGroup.includes(
              item.instanceData.groupCode?.toLowerCase()
            );
          } else return item;
        });
      }
      if (this.selectedDivision.length > 0) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return this.selectedDivision.includes(
              item.instanceData.divisionCode
            );
          } else return item;
        });
      }
      if (this.selectedNoOfPositions) {
        filteredArray = filteredArray.filter((item) => {
          if (item.instanceData) {
            return this.selectedNoOfPositions == item.instanceData.noOfPosition;
          } else return item;
        });
      }
      this.$emit("apply-filter", [
        filteredArray,
        action,
        this.isNoOnlySelectedInLateAttendance,
      ]);
    },

    // reset filter
    resetFilterValues() {
      this.selectedEmpName = [];
      this.selectedRequestFor = [];
      this.selectedLateArrival = [];
      this.selectedEarlyCheckout = [];
      this.selectedAttendanceShortage = [];
      this.selectedAttendanceFinalization = [];
      this.selectedServiceProvider = [];
      this.workedDate = "";
      this.compOffDate = null;
      this.selectedStatus = [];
      this.selectedEmployeeType = [];
      this.selectedLocation = [];
      this.selectedDesignation = [];
      this.selectedDepartment = [];
      this.selectedWorkSchedule = [];
      this.selectedBusinessUnit = [];
      this.selectedApprover = [];
      this.selectedLeaveType = [];
      this.selectedLeaveDuration = [];
      this.selectedSelfApply = [];
      this.selectedLateAttendance = [];
      this.selectedAppliedDateFrom = "";
      this.selectedExitDateTo = "";
      this.selectedReimbursementAppliedFrom = "";
      this.selectedReimbursementAppliedTo = "";
      this.selectedWeekendDateFrom = "";
      this.selectedWeekendDateTo = "";
      this.selectedStartDateFrom = "";
      this.selectedEndDateTo = "";
      this.selectedLeavePeriod = [];
      this.selectedLeaveStatus = [];
      this.selectedApprovalStatus = [];
      this.selectedApprovalBy = "Assigned For Me";
      this.selectedPreApprovalStatus = [];
      this.openFormFilter = false;
      this.selectedJobPostFrom = "";
      this.selectedJobPostTo = "";
      this.selectedPriority = [];
      this.selectedJobPostTitle = [];
      this.selectedJoiningDateFrom = "";
      this.selectedJoiningDateTo = "";
      this.selectedLopRecoveryReason = [];
      this.selectedLopRecoveryMonth = [];
      this.selectedPosition = [];
      this.selectedGroup = [];
      this.selectedDivision = [];
      this.selectedNoOfPositions = null;
      this.$emit("reset-filter");
    },

    formFilterData() {
      this.empNameList = [];
      this.approverList = [];
      this.leaveTypeList = [];
      this.jobPostTitleList = [];
      this.locationList = [];
      this.designationList = [];
      this.departmentList = [];
      this.workScheduleList = [];
      this.divisionList = [];
      this.employeeTypeList = [];
      this.statusList = [];
      this.serviceProviderList = [];
      for (let item of this.items) {
        if (item.location) {
          this.locationList.push(item.location);
        }
        if (item.designation) {
          this.designationList.push(item.designation);
        }
        if (item.department) {
          this.departmentList.push(item.department);
        }
        if (item.workSchedule) {
          this.workScheduleList.push(item.workSchedule);
        }
        if (item.empType) {
          this.employeeTypeList.push(item.empType);
        }
        if (item.businessUnit) {
          this.businessUnitList.push(item.businessUnit);
        }
        if (item.serviceProvider) {
          this.serviceProviderList.push(item.serviceProvider);
        }
        if (item.employeeName && item.userDefinedEmpId)
          this.empNameList.push({
            employeeId: item.userDefinedEmpId,
            employeeName: item.employeeName + " - " + item.userDefinedEmpId,
          });
        if (item.approver) {
          this.approverList.push(item.approver);
        }
        if (this.filterFormId === 31 && item.leaveType) {
          this.leaveTypeList.push(item.leaveType);
        }
        if (
          this.filterFormId === 15 &&
          item.instanceData &&
          item.instanceData.jobPostName
        ) {
          this.jobPostTitleList.push(item.instanceData.jobPostName);
        }
        if (this.filterFormId === 253 && item.instanceData) {
          let reasonArray = item.instanceData.Reason
            ? item.instanceData.Reason.split(",")
            : this.lopRecoveryReasonList;
          let leaveNameArray = item.instanceData.Leave_Name
            ? item.instanceData.Leave_Name.split(",")
            : this.leaveTypeList;
          this.lopRecoveryReasonList =
            this.lopRecoveryReasonList.concat(reasonArray);
          this.leaveTypeList = this.leaveTypeList.concat(leaveNameArray);
          if (item.lopRecoveryProcessingMonth)
            this.recoveryMonthList.push(item.lopRecoveryProcessingMonth);
        }
        if (item.divisionCode) {
          this.divisionList.push(item.divisionCode);
        }
        if (item.approvalStatus) {
          this.statusList.push(item.approvalStatus);
        }
      }
      this.empNameList = this.removeDuplicatesFromArrayOfObject(
        this.empNameList,
        "employeeId"
      );
      this.statusList = this.removeDuplicates(this.statusList);
      this.serviceProviderList = this.removeDuplicates(
        this.serviceProviderList
      );
      this.employeeTypeList = this.removeDuplicates(this.employeeTypeList);
      this.locationList = this.removeDuplicates(this.locationList);
      this.designationList = this.removeDuplicates(this.designationList);
      this.departmentList = this.removeDuplicates(this.departmentList);
      this.workScheduleList = this.removeDuplicates(this.workScheduleList);
      this.businessUnitList = this.removeDuplicates(this.businessUnitList);
      this.divisionList = this.removeDuplicates(this.divisionList);
      this.approverList = this.removeDuplicates(this.approverList);
      this.leaveTypeList = this.removeDuplicates(this.leaveTypeList);
      this.jobPostTitleList = this.removeDuplicates(this.jobPostTitleList);
      this.lopRecoveryReasonList = this.removeDuplicates(
        this.lopRecoveryReasonList
      );
      this.recoveryMonthList = this.removeDuplicates(this.recoveryMonthList);
    },

    removeDuplicates(arr) {
      return arr.filter((item, index) => arr.indexOf(item) === index);
    },

    removeDuplicatesFromArrayOfObject(arr, key) {
      const unique = arr.filter((obj, index) => {
        return index === arr.findIndex((o) => obj[key] === o[key]);
      });
      return unique;
    },
  },
});
</script>
