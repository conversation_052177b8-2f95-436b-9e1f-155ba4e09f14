<template>
  <EditResignation
    v-if="showEditForm"
    :approvalDetails="approvalDetails"
    :resignationAppliedDateMin="resignationAppliedDateMin"
    @close-edit-form="showEditForm = false"
    @edit-update-success="handleResignationEditSuccess()"
  ></EditResignation>
  <div v-else>
    <v-card class="rounded-lg">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center pl-4 py-2">
          <v-avatar class="mr-2" size="40" color="hover" variant="elevated">
            <i class="primary hr-workflow-approval-management text-h6"></i>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 300px'"
          >
            <div class="text-subtitle-1 font-weight-bold">
              {{ approvalDetails.description }}
            </div>
          </div>
        </div>
        <div class="pa-3">
          <v-btn
            v-if="filterFormId === 360"
            color="primary"
            rounded="lg"
            variant="elevated"
            size="small"
            class="mr-1"
            @click="$emit('show-salary-revision-modal', approvalDetails)"
          >
            View
          </v-btn>
          <v-btn
            v-if="showEditButton"
            color="primary"
            rounded="lg"
            variant="outlined"
            size="small"
            class="mr-1"
            @click="openEditForm()"
          >
            Edit
          </v-btn>
          <v-icon color="primary" @click="onCloseViewForm()">
            fas fa-times
          </v-icon>
        </div>
      </div>
      <v-card-text>
        <v-tabs
          v-model="viewFormTab"
          color="primary"
          align-tabs="center"
          class="mt-n4"
          style="box-shadow: 0 4px 6px -6px #222"
        >
          <v-tab :value="1">Details</v-tab>
          <v-tab :value="2">Approval Flow</v-tab>
          <v-tab v-if="filterFormId === 31" :value="3">Leave Balance</v-tab>
        </v-tabs>
        <v-row
          class="px-sm-8 px-md-12 mt-3 mb-6"
          :style="
            'overflow: scroll; height: ' + $store.getters.getTableHeight(350)
          "
        >
          <v-col cols="12">
            <v-card
              class="pa-2 d-flex align-center justify-center"
              elevation="0"
              min-height="250"
              width="100%"
            >
              <v-progress-circular
                v-if="isLoadingCard"
                color="secondary"
                indeterminate
                size="64"
              >
              </v-progress-circular>
              <v-window v-model="viewFormTab" v-else style="width: 100%">
                <v-window-item :value="1">
                  <div v-if="filterFormId === 243 || filterFormId === 18">
                    <v-row>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(approvalDetails.userDefinedEmpId)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(approvalDetails.employeeName) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Designation
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(approvalDetails.designation) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Department
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(approvalDetails.department) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Type
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(approvalDetails.empType) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Work Schedule
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(approvalDetails.workSchedule) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Changes
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ presentEmployeeDetailsTabName(approvalDetails) }}
                            <v-chip
                              color="secondary"
                              class="font-weight-bold"
                              small
                              outlined
                              @click="displayChanges = true"
                            >
                              View
                            </v-chip>
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedOn
                                ? approvalDetails.moreDetails.addedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedOn
                                ? approvalDetails.moreDetails.updatedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedBy
                                ? approvalDetails.moreDetails.updatedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 34">
                    <v-row
                      v-if="
                        resignationDetails &&
                        Object.keys(resignationDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.userDefinedEmpId }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.employeeName }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Applied Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(resignationDetails.appliedDate) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Exit Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(resignationDetails.exitDate) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Reason Type
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              resignationDetails.esicReason
                                ? resignationDetails.esicReason
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Relieving Reason Comment
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              resignationDetails.relievingReasonComment
                                ? resignationDetails.relievingReasonComment
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          resignationDetails.approvalStatus === 'Withdrawn' ||
                          resignationDetails.approvalStatus === 'Canceled'
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{
                            resignationDetails.approvalStatus === "Withdrawn"
                              ? "Withdrawn "
                              : "Cancellation "
                          }}
                          Comment
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              resignationDetails.withdrawnCancellationComment
                                ? resignationDetails.withdrawnCancellationComment
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              resignationDetails.approvalStatus
                                ? resignationDetails.approvalStatus
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(resignationDetails.addedOn, true) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          resignationDetails.updatedOn &&
                          resignationDetails.updatedBy
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(resignationDetails.updatedOn, true) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          resignationDetails.updatedOn &&
                          approvalDetails.moreDetails &&
                          approvalDetails.moreDetails.updatedBy
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.updatedBy }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          isApprovalHistory && resignationDetails.approvalStatus
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ resignationDetails.approvalStatus }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              resignationDetails.approvedOn
                                ? formatDate(
                                    resignationDetails.approvedOn,
                                    true
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 31">
                    <v-row
                      v-if="
                        leaveDetails && Object.keys(leaveDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.userDefinedEmpId }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.employeeName }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Leave Type
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.leaveType
                                ? approvalDetails.leaveType
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Reason
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              leaveDetails.reason ? leaveDetails.reason : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Start Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(leaveDetails.startDate) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          End Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(leaveDetails.endDate) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Duration
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              parseFloat(leaveDetails.duration) === 1
                                ? "Full Day"
                                : parseFloat(leaveDetails.duration) === 0.5
                                ? "Half Day"
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="parseFloat(leaveDetails.duration) === 0.5"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Leave Period
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              leaveDetails.leavePeriod
                                ? leaveDetails.leavePeriod
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Total Days
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              leaveDetails.totalDays
                                ? leaveDetails.totalDays
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Hours
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ leaveDetails.hours ? leaveDetails.hours : "-" }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Reason Type
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              leaveDetails.esicReason
                                ? leaveDetails.esicReason
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Comment
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              leaveDetails.comment ? leaveDetails.comment : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Contact Details
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              leaveDetails.contactNo
                                ? leaveDetails.contactNo
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Alternate Person
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.alternatePerson
                                ? approvalDetails.moreDetails.alternatePerson
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Late Attendance
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              leaveDetails.lateAttendance === 1 ? "Yes" : "No"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          approvalDetails.moreDetails &&
                          approvalDetails.moreDetails.lateAttendanceHours
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Late Arrival Hours
                        </div>
                        <div class="value-text">
                          <section class="text-body-2 text-red">
                            {{
                              approvalDetails.moreDetails.lateAttendanceHours
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              leaveDetails.approvalStatus
                                ? leaveDetails.approvalStatus
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(leaveDetails.addedOn, true) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="leaveDetails.updatedOn && leaveDetails.updatedBy"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(leaveDetails.updatedOn, true) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          leaveDetails.updatedOn &&
                          approvalDetails.moreDetails &&
                          approvalDetails.moreDetails.updatedBy
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.updatedBy }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          isApprovalHistory &&
                          leaveDetails.approvedOn &&
                          leaveDetails.approvalStatus &&
                          leaveDetails.approvalStatus !== 'Applied' &&
                          leaveDetails.approvalStatus !== 'Cancel Applied'
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ leaveDetails.approvalStatus }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              leaveDetails.approvedOn
                                ? formatDate(leaveDetails.approvedOn, true)
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 15">
                    <v-row>
                      <v-col
                        v-if="labelList[115].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[155].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              apiJobPostDetails.Job_Post_Name
                                ? apiJobPostDetails.Job_Post_Name
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[202].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[202].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.Experience_Level)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.Job_Post_Status)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Posting Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(apiJobPostDetails.Posting_Date) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Closing Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(apiJobPostDetails.Closing_Date) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[211].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[211].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              formatDate(
                                apiJobPostDetails.Expected_Joining_Date
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          labelList[115].Field_Visiblity == 'Yes' && fieldForce
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ getCustomFieldName(115, "Service Provider") }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                apiJobPostDetails.Service_Provider_Name
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          labelList[154].Field_Visiblity == 'Yes' && fieldForce
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[154].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                apiJobPostDetails.Organization_Group
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="apiJobPostDetails.CustomGroupName"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Custom Group
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.CustomGroupName)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[254].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[254].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(apiJobPostDetails.Pos_Name) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Division/Department/Section
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.Functional_Area)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[260].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[260].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{ checkNullValue(apiJobPostDetails.Designation) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[261].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[261].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(apiJobPostDetails.Payment_Type) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[257].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[257].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              apiJobPostDetails.JobLocations &&
                              apiJobPostDetails.JobLocations.length &&
                              apiJobPostDetails.JobLocations[0].Location_Name
                                ? apiJobPostDetails.JobLocations[0]
                                    .Location_Name
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[253].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[253].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(apiJobPostDetails.City_Name) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[252].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[252].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(apiJobPostDetails.State_Name) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[203].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[203].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.workPlaceType)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          labelList[258] &&
                          labelList[258].Field_Visiblity === 'Yes'
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[258].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{ checkNullValue(apiJobPostDetails.Job_Type) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          No of Vacancies
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.No_Of_Vacancies)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="apiJobPostDetails.No_Of_Male_Vacancies"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          No of Male Vacancies
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                apiJobPostDetails.No_Of_Male_Vacancies
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="apiJobPostDetails.No_Of_Female_Vacancies"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          No of Female Vacancies
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                apiJobPostDetails.No_Of_Female_Vacancies
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Key Skills
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              checkNullValue(
                                apiJobPostDetails.Skill_Set?.join(", ")
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Experience Range
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              apiJobPostDetails.Min_Work_Experience +
                              " - " +
                              apiJobPostDetails.Max_Work_Experience
                            }}
                            years(s)
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[288].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[288].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              apiJobPostDetails.Qualification &&
                              apiJobPostDetails.Qualification.length &&
                              apiJobPostDetails.Qualification[0].Qualification
                                ? apiJobPostDetails.Qualification[0]
                                    .Qualification
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Rounds
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              apiJobPostDetails.Rounds &&
                              apiJobPostDetails.Rounds.length &&
                              apiJobPostDetails.Rounds[0].Round_Name
                                ? apiJobPostDetails.Rounds[0].Round_Name
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          labelList[327]?.Field_Visiblity?.toLowerCase() ===
                          'yes'
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{
                            labelList[327]?.Field_Alias ||
                            "Required Certification"
                          }}
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              checkNullValue(
                                apiJobPostDetails?.Required_Certification
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[289].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[289].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              apiJobPostDetails.Currency
                                ? apiJobPostDetails.Currency
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Salary Range
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              apiJobPostDetails.Min_Payment_Frequency +
                              " - " +
                              apiJobPostDetails.Max_Payment_Frequency
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Pay Type
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(apiJobPostDetails.Pay_Type) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Cooling Period
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              apiJobPostDetails.Cooling_Period + " months(s)"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Field Work
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              apiJobPostDetails.Travel_Required ? "Yes" : "No"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[278].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[278].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{ checkNullValue(apiJobPostDetails.Client_Name) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[291].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[291].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                apiJobPostDetails.Hiring_Manager_Name
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[256].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[256].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              apiJobPostDetails.ReplacementFor &&
                              apiJobPostDetails.ReplacementFor.length &&
                              apiJobPostDetails.ReplacementFor[0].Employee_Name
                                ? apiJobPostDetails.ReplacementFor[0]
                                    .Employee_Name
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="apiJobPostDetails.Industry_Name"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Industry
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.Industry_Name)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="apiJobPostDetails.Category_Name"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Category
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.Category_Name)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="apiJobPostDetails.Subcategory_Name"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Subcategory
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.Subcategory_Name)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="labelList[259].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[259].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                apiJobPostDetails.Reason_For_Opening
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <!-- <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Job Duration
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              apiJobPostDetails.Job_Duration
                                ? apiJobPostDetails.Job_Duration
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col> -->
                      <!-- <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Expected Work Permits
                        </div>
                        <div class="value-text">
                          <v-skeleton-loader
                            v-if="isFetchingJobPostDetails"
                            type="list-item"
                            width="150"
                            class="ml-n4 mt-n2"
                          ></v-skeleton-loader>
                          <section v-else class="text-body-2">
                            {{
                              apiJobPostDetails.WorkAuthorization &&
                              apiJobPostDetails.WorkAuthorization.length &&
                              apiJobPostDetails.WorkAuthorization[0]
                                .Work_Authorization_Name
                                ? apiJobPostDetails.WorkAuthorization[0]
                                    .Work_Authorization_Name
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Other Work Permits
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              apiJobPostDetails.OtherWorkAuthorization
                                ? apiJobPostDetails.OtherWorkAuthorization
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col> -->
                      <v-col
                        v-if="labelList[228].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[228].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              convertCamelCaseToPascalCase(
                                apiJobPostDetails.Priority
                                  ? apiJobPostDetails.Priority
                                  : "-"
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <!-- <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Agency Involved
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              apiJobPostDetails.Agency_Involved
                                ? apiJobPostDetails.Agency_Involved
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col> -->
                      <v-col
                        v-if="labelList[204].Field_Visiblity == 'Yes'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ labelList[204].Field_Alias }}
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.jobLocationType)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Workflow
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(apiJobPostDetails.Workflow_Name)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                getDateAndTime(apiJobPostDetails.Added_On)
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(apiJobPostDetails.addedByName) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="12" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Job Description
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            <div
                              ref="editorView"
                              class="quill-editorView"
                            ></div>
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div
                    v-if="
                      filterFormId === 244 ||
                      filterFormId === 245 ||
                      filterFormId === 246 ||
                      filterFormId === 301
                    "
                  >
                    <v-row
                      v-if="
                        preApprovalDetails &&
                        Object.keys(preApprovalDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.userDefinedEmpId }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.employeeName }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Reason
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              preApprovalDetails.Reason
                                ? preApprovalDetails.Reason
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Start Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(preApprovalDetails.Start_Date) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          End Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(preApprovalDetails.End_Date) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Duration
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              preApprovalDetails.Duration
                                ? preApprovalDetails.Duration
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="preApprovalDetails.Duration === 'Half Day'"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Leave Period
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              preApprovalDetails.Period
                                ? preApprovalDetails.Period
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Total Days
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              preApprovalDetails.Total_Days
                                ? preApprovalDetails.Total_Days
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              preApprovalDetails.Status
                                ? preApprovalDetails.Status
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(preApprovalDetails.Added_On, true) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          preApprovalDetails.Updated_On &&
                          preApprovalDetails.Updated_By
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              formatDate(preApprovalDetails.Updated_On, true)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          preApprovalDetails.Updated_On &&
                          approvalDetails.moreDetails &&
                          approvalDetails.moreDetails.updatedBy
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.updatedBy }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          isApprovalHistory &&
                          preApprovalDetails.Approved_On &&
                          preApprovalDetails.Status &&
                          preApprovalDetails.Status !== 'Applied' &&
                          preApprovalDetails.Status !== 'Cancel Applied'
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ preApprovalDetails.Status }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              preApprovalDetails.Approved_On
                                ? formatDate(
                                    preApprovalDetails.Approved_On,
                                    true
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 366">
                    <v-row
                      v-if="
                        overTimePreApprovalDetails &&
                        Object.keys(overTimePreApprovalDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.userDefinedEmpId }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.employeeName }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Overtime Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.startDate || "-" }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Overtime Hours
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              fomatOvertimeHours(
                                overTimePreApprovalDetails.Over_Time_Hours
                              ) || "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Reason
                        </div>
                        <div class="value-text">
                          <section>
                            {{ overTimePreApprovalDetails.Reason || "-" }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{ overTimePreApprovalDetails.Status || "-" }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              formatDate(
                                overTimePreApprovalDetails.Added_On,
                                true
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          preApprovalDetails.Updated_On &&
                          preApprovalDetails.Updated_By
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              formatDate(preApprovalDetails.Updated_On, true)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          preApprovalDetails.Updated_On &&
                          approvalDetails.moreDetails &&
                          approvalDetails.moreDetails.updatedBy
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.updatedBy }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 253">
                    <v-row
                      v-if="
                        lopRecoveryDetails &&
                        Object.keys(lopRecoveryDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.userDefinedEmpId }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.employeeName }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          LOP Recovery Processing Month
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.lopRecoveryProcessingMonth }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Deduction Month
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.deductionMonth }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Total Days
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.totalDays }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Total Amount
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.totalAmount }}
                          </section>
                        </div>
                      </v-col>

                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          LOP Days For Recovery
                        </div>
                        <v-menu transition="scale-transition">
                          <template v-slot:activator="{ props }">
                            <v-btn
                              rounded="lg"
                              variant="outlined"
                              size="small"
                              color="primary"
                              v-bind="props"
                              class="mt-2"
                              >view</v-btn
                            >
                          </template>
                          <v-card
                            max-height="400px"
                            min-width="400px"
                            max-width="300px"
                          >
                            <div class="pa-2">
                              <v-row>
                                <v-list>
                                  <v-list-item
                                    v-for="tag in lopDaysForRecovery"
                                    :key="tag"
                                    class="ma-2"
                                    style="pointer-events: none"
                                  >
                                    {{ tag }}
                                  </v-list-item>
                                </v-list>
                              </v-row>
                            </div>
                          </v-card>
                        </v-menu>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Per Day Salary
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.perDaySalary }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Remarks
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.remarks }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{ approvalDetails.moreDetails.status }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.addedOn }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.addedBy }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          approvalDetails.moreDetails.updatedOn &&
                          approvalDetails.moreDetails.updatedBy
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.updatedOn }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          approvalDetails.moreDetails.updatedOn &&
                          approvalDetails.moreDetails.updatedBy
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.updatedBy }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          isApprovalHistory &&
                          lopRecoveryDetails.Approved_On &&
                          approvalDetails.taskStatus !== 'Applied'
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ approvalDetails.taskStatus }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              lopRecoveryDetails.Approved_On
                                ? formatDate(
                                    lopRecoveryDetails.Approved_On,
                                    true
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 267">
                    <v-row
                      v-if="
                        reimbursementDetails &&
                        Object.keys(reimbursementDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.userDefinedEmpId }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.employeeName }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Applied Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              formatDate(reimbursementDetails.submissionDate)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Total Applied Amount
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              reimbursementDetails.totalAppliedAmount
                                ? reimbursementDetails.totalAppliedAmount
                                : 0
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Total Approved Amount
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              reimbursementDetails.totalAmount
                                ? reimbursementDetails.totalAmount
                                : 0
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Invoice Details
                        </div>
                        <div class="value-text">
                          <v-btn
                            rounded="lg"
                            variant="outlined"
                            size="small"
                            color="primary"
                            class="mt-1"
                            @click="
                              $emit('show-invoice-modal', [
                                reimbursementDetails.requestId,
                                approvalDetails.process_instance_id,
                                reimbursementDetails.Approval_Status,
                              ])
                            "
                            >View</v-btn
                          >
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              reimbursementDetails.Approval_Status
                                ? reimbursementDetails.Approval_Status
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="isApprovalHistory"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Remarks
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.remarks }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedOn
                                ? approvalDetails.moreDetails.addedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="isApprovalHistory && approvalDetails.taskStatus"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ approvalDetails.taskStatus }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              reimbursementDetails.Approved_On
                                ? formatDate(
                                    new Date(
                                      reimbursementDetails.Approved_On + ".000Z"
                                    ),
                                    true
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 268">
                    <v-row
                      v-if="
                        timesheetDetails &&
                        Object.keys(timesheetDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.userDefinedEmpId }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.employeeName }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Weekend Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(timesheetDetails.Week_Ending_Date) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Total Hours
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              timesheetDetails.totalEffort
                                ? decimalToHours(timesheetDetails.totalEffort)
                                : "0h"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          More Details
                        </div>
                        <div class="value-text">
                          <v-btn
                            rounded="lg"
                            variant="outlined"
                            size="small"
                            color="primary"
                            class="mt-1"
                            @click="
                              $emit('show-timesheet-modal', [
                                approvalDetails.employeeId,
                                timesheetDetails.Week_Ending_Date,
                              ])
                            "
                            >View</v-btn
                          >
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              timesheetDetails.Approval_Status
                                ? timesheetDetails.Approval_Status
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="isApprovalHistory"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Remarks
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.moreDetails.remarks }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedOn
                                ? approvalDetails.moreDetails.addedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="isApprovalHistory && approvalDetails.taskStatus"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ approvalDetails.taskStatus }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              timesheetDetails.Approved_On
                                ? formatDate(
                                    new Date(
                                      timesheetDetails.Approved_On + ".000Z"
                                    ),
                                    true
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 291 || filterFormId == 290">
                    <v-row
                      v-if="
                        recruitmentRequestDetails &&
                        Object.keys(recruitmentRequestDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Position Code
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                recruitmentRequestDetails.positionCode
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Position Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                recruitmentRequestDetails.positionTitle
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Group Code
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                recruitmentRequestDetails.groupCode
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Division Code
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                recruitmentRequestDetails.divisionCode
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          No. of Position
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                recruitmentRequestDetails.noOfPosition
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              recruitmentRequestDetails.Approval_Status
                                ? recruitmentRequestDetails.Approval_Status
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedOn
                                ? approvalDetails.moreDetails.addedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedBy
                                ? approvalDetails.moreDetails.updatedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedOn
                                ? approvalDetails.moreDetails.updatedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="isApprovalHistory && approvalDetails.taskStatus"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ approvalDetails.taskStatus }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.instanceData.Approved_On
                                ? formatDate(
                                    new Date(
                                      approvalDetails.instanceData.Approved_On +
                                        ".000Z"
                                    ),
                                    true
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 334">
                    <v-row
                      v-if="
                        compOffDetails && Object.keys(compOffDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.userDefinedEmpId }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.employeeName }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Worked Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(compOffDetails.Worked_Date) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Duration
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(compOffDetails.Duration) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Period
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(compOffDetails.Period) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Compensatory Off Date
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              compOffDetails.Compensatory_Date
                                ? formatDate(compOffDetails.Compensatory_Date)
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              compOffDetails.Approval_Status
                                ? compOffDetails.Approval_Status
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedOn
                                ? approvalDetails.moreDetails.addedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedBy
                                ? approvalDetails.moreDetails.updatedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedOn
                                ? approvalDetails.moreDetails.updatedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="isApprovalHistory && approvalDetails.taskStatus"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ approvalDetails.taskStatus }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.instanceData.Approved_On
                                ? formatDate(
                                    new Date(
                                      approvalDetails.instanceData.Approved_On +
                                        ".000Z"
                                    ),
                                    true
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 352">
                    <v-row
                      v-if="
                        shortTimeOffDetails &&
                        Object.keys(shortTimeOffDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.userDefinedEmpId }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ approvalDetails.employeeName }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Request For
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(shortTimeOffDetails.Request_For)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Start Date & Time
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              formatDateTime(
                                shortTimeOffDetails.Start_Date_Time,
                                true
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          End Date & Time
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              formatDateTime(
                                shortTimeOffDetails.End_Date_Time,
                                true
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Total Hours
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              formatTotalHours(shortTimeOffDetails.Total_Hours)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Reason
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(shortTimeOffDetails.Reason) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              checkNullValue(
                                shortTimeOffDetails.Approval_Status
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ formatDate(shortTimeOffDetails.Added_On, true) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="shortTimeOffDetails.Updated_On"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedOn
                                ? approvalDetails.moreDetails.updatedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="shortTimeOffDetails.Updated_On"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedBy
                                ? approvalDetails.moreDetails.updatedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          isApprovalHistory &&
                          shortTimeOffDetails.Approved_On &&
                          shortTimeOffDetails.Approval_Status &&
                          shortTimeOffDetails.Approval_Status !== 'Applied' &&
                          shortTimeOffDetails.Approval_Status !==
                            'Cancel Applied'
                        "
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ shortTimeOffDetails.Approval_Status }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              formatDate(shortTimeOffDetails.Approved_On, true)
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 341">
                    <v-row
                      v-if="
                        travelRequestDetails &&
                        Object.keys(travelRequestDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(approvalDetails.userDefinedEmpId)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(approvalDetails.employeeName) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Travel Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(travelRequestDetails.Trip_Name) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Travel Type
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(travelRequestDetails.Travel_Type)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Destination Country for Visa
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                travelRequestDetails.Destination_Country
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Travel Start Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              travelRequestDetails.Travel_Start_Date
                                ? formatDate(
                                    travelRequestDetails.Travel_Start_Date
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Travel End Date
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              travelRequestDetails.Travel_End_Date
                                ? formatDate(
                                    travelRequestDetails.Travel_End_Date
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Budget Amount
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              travelRequestDetails.Budget_Amount
                                ? travelRequestDetails.Budget_Amount
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Business Purpose
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              travelRequestDetails.Business_Purpose
                                ? travelRequestDetails.Business_Purpose
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Meal Preference
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              travelRequestDetails.Meal_Preference
                                ? travelRequestDetails.Meal_Preference
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Status
                        </div>
                        <div class="value-text">
                          <section>
                            {{
                              travelRequestDetails.Status
                                ? travelRequestDetails.Status
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails?.addedOn
                                ? approvalDetails.moreDetails.addedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.addedBy
                                ? approvalDetails.moreDetails.addedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedBy
                                ? approvalDetails.moreDetails.updatedBy
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.moreDetails &&
                              approvalDetails.moreDetails.updatedOn
                                ? approvalDetails.moreDetails.updatedOn
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col
                        v-if="isApprovalHistory && approvalDetails.taskStatus"
                        cols="12"
                        sm="6"
                        class="px-md-6 pb-0"
                      >
                        <div class="text-subtitle-1 font-weight-bold">
                          {{ approvalDetails.taskStatus }} On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              approvalDetails.instanceData.Approved_On
                                ? formatDate(
                                    new Date(
                                      approvalDetails.instanceData.Approved_On +
                                        ".000Z"
                                    ),
                                    true
                                  )
                                : "-"
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                  <div v-if="filterFormId === 360">
                    <v-row
                      v-if="
                        salaryRevisionDetails &&
                        Object.keys(salaryRevisionDetails).length > 0
                      "
                    >
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Id
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(approvalDetails.userDefinedEmpId)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Employee Name
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{ checkNullValue(approvalDetails.employeeName) }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Effective Month
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                convertToMonth(
                                  salaryRevisionDetails?.salaryEffectiveMonth
                                )
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Payout Month
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                convertToMonth(
                                  salaryRevisionDetails?.payoutMonth
                                )
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Previous CTC
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(salaryRevisionDetails?.previousCtc)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Revised CTC
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(salaryRevisionDetails?.annualCTC)
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                formatDate(salaryRevisionDetails.Added_On, true)
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Added By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                salaryRevisionDetails.Added_By_Name
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated On
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(
                                formatDate(
                                  salaryRevisionDetails.Updated_On,
                                  true
                                )
                              )
                            }}
                          </section>
                        </div>
                      </v-col>
                      <v-col cols="12" sm="6" class="px-md-6 pb-0">
                        <div class="text-subtitle-1 font-weight-bold">
                          Updated By
                        </div>
                        <div class="value-text">
                          <section class="text-body-2">
                            {{
                              checkNullValue(salaryRevisionDetails.Updated_By)
                            }}
                          </section>
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                </v-window-item>
                <v-window-item :value="2">
                  <organization-chart :datasource="orgChartData">
                    <template v-slot="{ nodeData }">
                      <div class="d-flex flex-column my-2">
                        <i :class="nodeData.iconName"></i>
                        <div class="mt-2 text-primary">
                          {{ nodeData.title }}
                        </div>
                        <div class="text-blue d-flex justify-center">
                          {{ nodeData.name }}
                          <v-tooltip
                            v-if="
                              nodeData.name === 'To be decided during runtime'
                            "
                            text="The reporting manager who is associated with the employee in the employee module will be considered"
                            location="top"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div v-bind="props">
                                <v-icon color="blue" class="ml-1"
                                  >fas fa-info-circle</v-icon
                                >
                              </div>
                            </template>
                          </v-tooltip>
                        </div>
                        <div class="d-flex align-center justify-center">
                          <v-tooltip
                            v-if="nodeData.completedOn"
                            :text="nodeData.completedOn"
                            location="top"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div v-bind="props">
                                <v-icon class="ml-2" color="indigo-accent-2"
                                  >fas fa-clock</v-icon
                                >
                              </div>
                            </template>
                          </v-tooltip>
                          <v-tooltip
                            v-if="nodeData.remarks"
                            :text="nodeData.remarks"
                            location="top"
                            max-width="400"
                          >
                            <template v-slot:activator="{ props }">
                              <div v-bind="props">
                                <v-icon class="ml-2" color="amber"
                                  >fas fa-comment-alt</v-icon
                                >
                              </div>
                            </template>
                          </v-tooltip>
                          <div v-if="nodeData.formDetails">
                            <i
                              v-if="nodeData.formDetails.formIdentifier"
                              class="hr-workflow-task-management-form cursor-pointer pl-2"
                              style="color: #ff6666; font-size: 18px"
                              @click="openDynamicForm(nodeData.formDetails)"
                            ></i>
                            <v-tooltip
                              v-else
                              text="Approve the previous pending approval"
                              location="bottom"
                              max-width="400"
                            >
                              <template v-slot:activator="{ props }">
                                <div v-bind="props">
                                  <i
                                    class="hr-workflow-task-management-form cursor-pointer pl-2"
                                    style="color: #4e4e4e; font-size: 18px"
                                  ></i>
                                </div>
                              </template>
                            </v-tooltip>
                          </div>
                        </div>
                      </div>
                    </template>
                  </organization-chart>
                </v-window-item>
                <v-window-item :value="3">
                  <v-row
                    v-if="
                      leaveBalanceDetails &&
                      Object.keys(leaveBalanceDetails).length > 0
                    "
                  >
                    <v-col cols="12" sm="6" class="px-md-6 pb-0">
                      <div class="text-subtitle-1 font-weight-bold d-flex">
                        Leave
                      </div>
                      <div class="value-text">
                        <section class="text-body-2">
                          {{ leaveBalanceDetails.leaveName }}
                        </section>
                      </div>
                    </v-col>
                    <v-col cols="12" sm="6" class="px-md-6 pb-0">
                      <div class="text-subtitle-1 font-weight-bold d-flex">
                        Leave Taken
                        <v-tooltip
                          text="Leave taken during the leave year"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div v-bind="props">
                              <v-icon color="blue" class="ml-1" size="20"
                                >fas fa-info-circle</v-icon
                              >
                            </div>
                          </template>
                        </v-tooltip>
                      </div>
                      <div class="value-text">
                        <section class="text-body-2">
                          {{ leaveBalanceDetails.leavesTaken }}
                        </section>
                      </div>
                    </v-col>
                    <v-col cols="12" sm="6" class="px-md-6 pb-0">
                      <div class="text-subtitle-1 font-weight-bold d-flex">
                        Leave Balance(Based On Leave Period)
                        <v-tooltip
                          text="Current Leave Eligibility - Leave Taken"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div v-bind="props">
                              <v-icon color="blue" class="ml-1" size="20"
                                >fas fa-info-circle</v-icon
                              >
                            </div>
                          </template>
                        </v-tooltip>
                      </div>
                      <div class="value-text">
                        <section class="text-body-2">
                          {{ leaveBalanceDetails.leaveBalanceBasedOnPeriod }}
                        </section>
                      </div>
                    </v-col>
                    <v-col cols="12" sm="6" class="px-md-6 pb-0">
                      <div class="text-subtitle-1 font-weight-bold d-flex">
                        Leave Balance(Per Annum)
                        <v-tooltip
                          text="Total Leave Eligibility - Leave Taken"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div v-bind="props">
                              <v-icon color="blue" class="ml-1" size="20"
                                >fas fa-info-circle</v-icon
                              >
                            </div>
                          </template>
                        </v-tooltip>
                      </div>
                      <div class="value-text">
                        <section class="text-body-2">
                          {{ leaveBalanceDetails.leavesBalancePerAnnum }}
                        </section>
                      </div>
                    </v-col>
                    <v-col cols="12" sm="6" class="px-md-6 pb-0">
                      <div class="text-subtitle-1 font-weight-bold d-flex">
                        Current Leave Eligibility
                        <v-tooltip
                          text="Current leave eligibility based on leave period configuration and carry over balance"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div v-bind="props">
                              <v-icon color="blue" class="ml-1" size="20"
                                >fas fa-info-circle</v-icon
                              >
                            </div>
                          </template>
                        </v-tooltip>
                      </div>
                      <div class="value-text">
                        <section class="text-body-2">
                          {{ leaveBalanceDetails.currentLeaveEligibility }}
                        </section>
                      </div>
                    </v-col>
                    <v-col cols="12" sm="6" class="px-md-6 pb-0 mb-12">
                      <div class="text-subtitle-1 font-weight-bold d-flex">
                        Total Leave Eligibility
                        <v-tooltip
                          text="Total leave eligibility based on leave year and carry over balance"
                          location="bottom"
                          max-width="400"
                        >
                          <template v-slot:activator="{ props }">
                            <div v-bind="props">
                              <v-icon color="blue" class="ml-1" size="20"
                                >fas fa-info-circle</v-icon
                              >
                            </div>
                          </template>
                        </v-tooltip>
                      </div>
                      <div class="value-text">
                        <section class="text-body-2">
                          {{ leaveBalanceDetails.totalEligibleDays }}
                        </section>
                      </div>
                    </v-col>
                  </v-row>
                </v-window-item>
              </v-window>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
  <v-dialog
    v-if="openFormRender"
    v-model="openFormRender"
    persistent
    max-width="800"
  >
    <FormRender
      :form-data="formJsonData"
      :resignationId="resignationDetails.resignationId"
      :taskId="taskId"
      :formResponseId="formResponseId"
      :conversationalId="conversationalId"
      :processInstanceId="processInstanceId"
      :show-approval="false"
      @close-form-render="closeFormRenderForm()"
    ></FormRender>
  </v-dialog>

  <EmployeesListModal
    v-if="showEmployeeList"
    :show-modal="showEmployeeList"
    :employeesList="selectedEmployees"
    :showFilterSearch="true"
    :showFilter="false"
    selectStrategy="single"
    employeeIdKey="employeeId"
    userDefinedEmpIdKey="userDefinedEmpId"
    employeeNameKey="employeeName"
    deptNameKey="departmentName"
    designationKey="designationName"
    :isApplyFilter="true"
    departmentIdKey="departmentId"
    designationIdKey="designationId"
    locationIdKey="locationId"
    empTypeIdKey="empTypeId"
    workScheduleIdKey="workSchedule"
    rolesIdKey="rolesId"
    @close-modal="showEmployeeList = false"
  ></EmployeesListModal>
  <EmployeeApprovalChanges
    v-if="displayChanges"
    v-model="displayChanges"
    :is-approval-history="isApprovalHistory"
    :instance-data="approvalDetails.instanceData"
    @approve-reject="$emit('approve-reject', $event)"
  />
</template>

<script>
import { defineComponent, defineAsyncComponent } from "vue";
import EmployeeApprovalChanges from "../../my-team/team-summary/EmployeeApprovalChanges.vue";
const EmployeesListModal = defineAsyncComponent(() =>
  import("@/components/custom-components/EmployeesListModal")
);
import OrganizationChart from "vue3-organization-chart";
import moment from "moment";
// import DOMPurify from "dompurify";
import { checkNullValue, getCustomFieldName, decimalToHours } from "@/helper";
// queries
import {
  GET_WORK_FLOW_VIEW,
  GET_RESIGNATION_PAYSLIP_DETAILS,
  GET_JOB_POST_DETAILS,
  LIST_LEAVE_HISTORY,
  GET_DYNAMIC_FORM_DETAILS,
} from "@/graphql/workflow/approvalManagementQueries.js";
// components
const EditResignation = defineAsyncComponent(() =>
  import("./EditResignation.vue")
);
import "quill/dist/quill.core.css";
import "quill/dist/quill.bubble.css";
import "quill/dist/quill.snow.css";
import Quill from "quill";
const FormRender = defineAsyncComponent(() => import("./FormRender.vue"));

export default defineComponent({
  name: "ViewApprovals",

  components: {
    OrganizationChart,
    EmployeesListModal,
    EditResignation,
    FormRender,
    EmployeeApprovalChanges,
  },

  emits: [
    "show-invoice-modal",
    "show-timesheet-modal",
    "close-view-form",
    "update-success",
    "close-modal",
    "show-salary-revision-modal",
    "approve-reject",
  ],

  props: {
    approvalDetails: {
      type: Object,
      required: true,
    },
    filterFormId: {
      type: [String, Number],
      required: true,
    },
    isAnyAdmin: {
      type: Boolean,
      default: false,
    },
    approvalType: {
      type: String,
      default: "",
    },
    showTaskLevel: {
      type: Boolean,
      default: false,
    },
  },

  data: () => ({
    displayChanges: false,
    viewFormTab: null,
    orgchartArray: [],
    empGroupData: null,
    keyMap: [],
    startNode: "",
    orgChartData: {},
    isLoading: false,
    isLoadingCard: false,
    isCalled: false,
    showEditButton: false,
    showEditForm: false,
    selectedEmpId: 0,
    // resignation
    resignationDetails: {},
    resignationAppliedDateMin: "",
    // leave
    leaveDetails: {},
    // job post
    jobPostDetails: {},
    apiJobPostDetails: {},
    isFetchingJobPostDetails: false,
    // leave balance
    leaveBalanceDetails: {},
    // pre approval
    preApprovalDetails: {},
    overTimePreApprovalDetails: {},
    // lop recovery
    lopRecoveryDetails: {},
    lopDaysForRecovery: [],
    // reimbursement
    reimbursementDetails: {},
    compOffDetails: {},
    shortTimeOffDetails: {},
    travelRequestDetails: {},
    salaryRevisionDetails: {},
    // timesheets
    timesheetDetails: {},
    recruitmentRequestDetails: {}, //
    // form
    formJsonData: {},
    openFormRender: false,
    formResponseId: null,
    taskId: 0,
    conversationalId: 0,
    processInstanceId: "",
    showEmployeeList: false,
    selectedEmployees: [],
  }),

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
    fieldForce() {
      const { fieldForce } = this.$store.state.orgDetails;
      return fieldForce;
    },
    formatDate() {
      return (date, withTime = false, isEmpty = false) => {
        let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
        if (withTime) orgDateFormat = orgDateFormat + " HH:mm:ss";
        return date ? moment(date).format(orgDateFormat) : isEmpty ? "" : "-";
      };
    },
    formatDateTime() {
      return (date) => {
        if (!date) return "";
        if (moment(date).isValid()) {
          let orgDateFormat =
            this.$store.state.orgDetails.orgDateFormat + " HH:mm";
          return moment(date).format(orgDateFormat);
        }
        return "";
      };
    },
    formatTotalHours() {
      return (totalHours) => {
        if (!totalHours) {
          return "00 Hrs 00 Mins";
        }

        // Parse the total hours value (could be decimal or HH:MM format)
        let hours = 0;
        let minutes = 0;

        // Check if it's in HH:MM format
        if (typeof totalHours === "string" && totalHours.includes(":")) {
          [hours, minutes] = totalHours.split(":").map(Number);
        } else {
          // Assume it's a decimal value
          const totalHoursDecimal = parseFloat(totalHours) || 0;
          hours = Math.floor(totalHoursDecimal);
          minutes = Math.round((totalHoursDecimal - hours) * 60);
        }

        // Format as "XX Hours YY Minutes"
        return `${hours.toString().padStart(2, "0")} Hrs ${minutes
          .toString()
          .padStart(2, "0")} Mins`;
      };
    },
    // login employee id
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    isApprovalHistory() {
      return this.approvalType === "Approval History";
    },

    accessRights() {
      return this.$store.getters.formAccessRights;
    },
    resignationFormAccess() {
      let formAccess = this.accessRights("resignation");
      if (
        formAccess &&
        formAccess.accessRights &&
        formAccess.accessRights.view
      ) {
        return formAccess.accessRights;
      } else return false;
    },
  },

  watch: {
    approvalDetails() {
      this.viewFormTab = 1;
      this.fetchAPIs();
    },
    viewFormTab() {
      this.fetchAPIs();
    },
  },

  mounted() {
    if (this.showTaskLevel) {
      this.viewFormTab = 2;
      this.fetchWorkflowData();
    } else {
      this.viewFormTab = 1;
      this.fetchAPIs();
    }
  },

  methods: {
    checkNullValue,
    decimalToHours,
    getCustomFieldName,
    presentEmployeeDetailsTabName(item) {
      const items = item?.instanceData?.Request_Items;
      let changedData = [];
      if (items) {
        for (let i = 0; i < items.length; i++) {
          if (items[i].Table_Name?.toLowerCase() === "emp_personal_info")
            changedData.push("Personal Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_job")
            changedData.push("Job Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_dependent")
            changedData.push("Dependent Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_passport")
            changedData.push("Passport Details");
          else if (items[i].Table_Name?.toLowerCase() === "contact_details")
            changedData.push("Contact Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_drivinglicense")
            changedData.push("License Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_experience")
            changedData.push("Experience Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_assets")
            changedData.push("Asset Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_skillset")
            changedData.push("Skill Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_education")
            changedData.push("Education Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_certifications")
            changedData.push("Certification Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_training")
            changedData.push("Training Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_awards")
            changedData.push("Award Details");
          else if (items[i].Table_Name?.toLowerCase() === "emp_bankdetails")
            changedData.push("Bank Details");
          else if (
            items[i].Table_Name?.toLowerCase() === "emp_insurancepolicyno"
          )
            changedData.push("Insurance Details");
          else if (
            items[i].Table_Name?.toLowerCase() === "emp_document_category"
          )
            changedData.push("Documents");
          else if (
            items[i].Table_Name?.toLowerCase() ===
            "employee_accreditation_details"
          )
            changedData.push(
              `${this.labelList[229]?.Field_Alias || "Accreditation"}`
            );
          else if (
            items[i].Table_Name?.toLowerCase() === "bond_recovery_details"
          )
            changedData.push("Bond Recovery Details");
          else if (items[i].Table_Name?.toLowerCase() === "overtime_details")
            changedData.push("Overtime Details");
          else if (items[i].Table_Name?.toLowerCase() === "retiral_details")
            changedData.push("Retirals Details");
          else if (items[i].Table_Name?.toLowerCase() === "tax_details")
            changedData.push("Tax Details");
          else if (
            items[i].Table_Name?.toLowerCase() === "emp_air_ticket_policy"
          )
            changedData.push("Air Ticket Policy");
          else changedData.push("-");
        }
      }
      //Remove duplicates
      changedData = [...new Set(changedData)];
      return changedData?.join(", ");
    },
    openCustomGroupEmployees(customGroupId) {
      if (customGroupId) {
        let vm = this;
        vm.isLoadingCard = true;
        vm.selectedEmployees = [];
        vm.$store
          .dispatch("retrieveEmployeesBasedOnCustomGroup", {
            customGroupId: parseInt(customGroupId),
          })
          .then((response) => {
            if (response) {
              const employeeDetails = response;
              for (let i = 0; i < employeeDetails.length; i++) {
                employeeDetails[i].employee_name =
                  employeeDetails[i]["employeeName"];
                employeeDetails[i].designation_name =
                  employeeDetails[i]["designationName"];
                employeeDetails[i].department_name =
                  employeeDetails[i]["departmentName"];
                employeeDetails[i].user_defined_empid =
                  employeeDetails[i]["userDefinedEmpId"];
                delete employeeDetails[i].key1;
              }
              vm.selectedEmployees = employeeDetails;
              vm.showEmployeeList = true;
              vm.isLoadingCard = false;
            }
          })
          .catch(() => {
            vm.isLoadingCard = false;
            vm.selectedEmployees = [];
          });
      } else {
        this.selectedEmployees = [];
      }
    },
    initQuillEditor() {
      this.quill = new Quill(this.$refs.editorView, {
        theme: "snow",
      });
      // Set initial font size
      this.setEditorFontSize("16px");
      this.quill.root.innerHTML = this.apiJobPostDetails?.Job_Description
        ? this.convertEmojiCodepointsToEmojis(
            this.apiJobPostDetails.Job_Description
          )
        : "";
      this.hasContent = !!this.quill.getText().trim();

      // Listen for editor text change events
      this.quill.on("text-change", () => {
        this.hasContent = !!this.quill.getText().trim();
      });
      this.quill.enable(false);
    },
    setEditorFontSize(fontSize) {
      const editorElement = this.$refs.editorView.querySelector(".ql-editor");
      if (editorElement) {
        editorElement.style.fontSize = fontSize;
      }
    },
    getDateAndTime(dateTimeData) {
      // Create a Date object from the date-time string
      const dateTime = new Date(dateTimeData);

      // Extract the date components
      const year = dateTime.getFullYear();
      const month = dateTime.getMonth() + 1; // Month is zero-indexed, so add 1
      const day = dateTime.getDate();

      // Extract the time components
      const hours = dateTime.getUTCHours();
      const minutes = dateTime.getUTCMinutes();
      const seconds = dateTime.getUTCSeconds();

      let dateAndTime =
        day +
        "/" +
        month +
        "/" +
        year +
        " at " +
        hours +
        ":" +
        minutes +
        ":" +
        seconds;
      return dateAndTime;
    },
    fetchAPIs() {
      this.lopDaysForRecovery = [];
      this.lopRecoveryDetails = {};
      this.preApprovalDetails = {};
      this.overTimePreApprovalDetails = {};
      this.leaveDetails = {};
      this.resignationDetails = {};
      this.reimbursementDetails = {};
      this.timesheetDetails = {};
      this.recruitmentRequestDetails = {};
      this.jobPostDetails = {};
      this.apiJobPostDetails = {};
      this.showEditForm = false;
      this.selectedEmpId =
        this.approvalDetails && this.approvalDetails.employeeDetails
          ? this.approvalDetails.employeeDetails.employeeid
          : 0;
      if (this.viewFormTab === 1) {
        if (this.filterFormId === 31) {
          this.leaveDetails = this.approvalDetails.instanceData;
        } else if (this.filterFormId === 34) {
          this.resignationDetails = this.approvalDetails.instanceData;
          if (this.resignationFormAccess && this.resignationFormAccess.update) {
            this.getEmployeeResignationPayslipDetails();
          }
        } else if (this.filterFormId === 15) {
          this.jobPostDetails = this.approvalDetails.instanceData;
          this.getEmployeeJobPostDetails();
        } else if (this.filterFormId === 253) {
          this.lopRecoveryDetails = this.approvalDetails.instanceData;
          if (this.lopRecoveryDetails) {
            const startDateArray = this.lopRecoveryDetails.Lop_Recovery_Date
              ? this.lopRecoveryDetails.Lop_Recovery_Date.split(",")
              : [];
            const reasonArray = this.lopRecoveryDetails.Reason
              ? this.lopRecoveryDetails.Reason.split(",")
              : [];
            const durationArray = this.lopRecoveryDetails.Duration
              ? this.lopRecoveryDetails.Duration.split(",")
              : [];
            const periodArray = this.lopRecoveryDetails.Period
              ? this.lopRecoveryDetails.Period.split(",")
              : [];
            // Create the desired output array
            this.lopDaysForRecovery = startDateArray.map((startDate, index) => {
              const duration = durationArray[index];
              const reason = reasonArray[index];
              const period = periodArray[index];
              let durationLabel = duration;
              if (period) {
                durationLabel = duration + " " + period;
              }
              return `${this.formatDate(
                startDate
              )} - ${reason} (${durationLabel})`;
            });
          }
        } else if (this.filterFormId === 267) {
          this.reimbursementDetails = this.approvalDetails.instanceData;
        } else if (this.filterFormId === 268) {
          this.timesheetDetails = this.approvalDetails.instanceData;
        } else if (this.filterFormId === 291 || this.filterFormId === 290) {
          this.recruitmentRequestDetails = this.approvalDetails.instanceData;
        } else if (this.filterFormId === 334) {
          this.compOffDetails = this.approvalDetails.instanceData;
        } else if (this.filterFormId === 352) {
          this.shortTimeOffDetails = this.approvalDetails.instanceData;
        } else if (this.filterFormId === 341) {
          this.travelRequestDetails = this.approvalDetails.instanceData;
        } else if (this.filterFormId === 360) {
          this.salaryRevisionDetails = this.approvalDetails.instanceData;
        } else if (this.filterFormId === 366) {
          this.overTimePreApprovalDetails = this.approvalDetails.instanceData;
        } else {
          this.preApprovalDetails = this.approvalDetails.instanceData;
        }
      } else if (this.viewFormTab === 2) {
        this.fetchWorkflowData();
      } else if (this.viewFormTab === 3) {
        this.leaveDetails = this.approvalDetails.instanceData;
        this.getLeaveBalanceDetails();
      }
    },

    onCloseViewForm() {
      this.$emit("close-view-form");
    },

    fetchWorkflowData() {
      let vm = this;
      vm.isLoadingCard = true;
      vm.$apollo
        .query({
          query: GET_WORK_FLOW_VIEW,
          variables: {
            taskId: vm.approvalDetails.task_id,
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          var resultData = JSON.parse(response.data.getWorkFlowView.details);
          this.orgchartArray = [];
          this.keyMap = [];
          this.orgChartData = {};
          this.isCalled = false;
          this.empGroupData = resultData.empGroupData;
          this.prepareChartJson(resultData.nodeDetails);
        })
        .catch(() => {
          this.isLoadingCard = false;
        });
    },

    prepareChartJson(workflowData) {
      this.startNode = workflowData["startNode"];
      this.prepareNode(workflowData.nodes[this.startNode], workflowData.nodes);
    },

    prepareNode(node, nodes) {
      for (let i = 0; i < node["next"].length; i++) {
        const currentNode = nodes[node["next"][i]];
        let userDetails = currentNode.data.completed_by
          ? currentNode.data.completed_by
          : currentNode.data.assignee;
        let values = this.empGroupData;
        if (!this.keyMap.includes(node["next"][i])) {
          this.keyMap.push(node["next"][i]);
          let parentNode = "";
          for (let objKey in nodes) {
            if (nodes[objKey].next.includes(node["next"][i])) {
              parentNode = nodes[objKey].nodeNo
                ? nodes[objKey].nodeNo
                : this.startNode;
              break;
            }
          }
          if (currentNode.type == "userTask") {
            if (currentNode.data.modalTaskData) {
              // Handle node with modalTaskData
              const modalData = currentNode.data.modalTaskData;
              let name = "";
              let values = this.empGroupData;

              // Determine name based on approver type
              if (
                modalData.typeOfApprove === "user" &&
                modalData.approveByUser
              ) {
                name =
                  values[`user${modalData.approveByUser.id}`]?.["Name"] ||
                  "Pending User";
              } else if (
                modalData.typeOfApprove === "group" &&
                modalData.approveByGroup
              ) {
                name = modalData.approveByGroup?.["text"] || "Pending Group";
              }

              this.orgchartArray.push({
                id: node["next"][i],
                customGroupId: modalData.approveByGroup
                  ? modalData.approveByGroup.id
                  : "",
                name: name,
                title: modalData.title || "Pending Approval",
                Parent: parentNode,
                iconName:
                  "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                remarks: currentNode.data.remarks || "",
                formDetails:
                  this.filterFormId === 34 &&
                  modalData.formId &&
                  modalData.formId.id !== "0" &&
                  parseInt(modalData.formId.id) > 0
                    ? {
                        formIdentifier: currentNode.data.form_identifier,
                        taskId: currentNode.data.task_id,
                        processInstanceId: currentNode.data.process_instance_id,
                      }
                    : "",
              });
            } else {
              //approved status
              if (userDetails && currentNode.data.status_id === "1002") {
                this.orgchartArray.push({
                  id: node["next"][i],
                  name: values[`user${userDetails}`]["Name"],
                  title: currentNode.data.description,
                  Parent: parentNode,
                  iconName:
                    "text-h3 hr-workflow-task-management-approvedtask text-green",
                  remarks: currentNode.data.remarks,
                  completedOn: currentNode?.data?.completed_date
                    ? this.formatDate(
                        new Date(currentNode.data.completed_date + "00Z"),
                        true,
                        true
                      )
                    : null,
                  formDetails:
                    this.filterFormId === 34 &&
                    currentNode.data.form_identifier &&
                    parseInt(currentNode.data.form_identifier) > 0
                      ? {
                          formIdentifier: currentNode.data.form_identifier,
                          taskId: currentNode.data.task_id,
                          processInstanceId:
                            currentNode.data.process_instance_id,
                        }
                      : "",
                });
              } else if (currentNode.data.status_id === "1007") {
                //rejected status
                this.orgchartArray.push({
                  id: node["next"][i],
                  name: values[`user${userDetails}`]["Name"],
                  title: currentNode.data.description,
                  Parent: parentNode,
                  iconName:
                    "text-h3 hr-workflow-task-management-reject text-red",
                  remarks: currentNode.data.remarks,
                  completedOn: currentNode?.data?.completed_date
                    ? this.formatDate(
                        new Date(currentNode.data.completed_date + "00Z"),
                        true,
                        true
                      )
                    : null,
                  formDetails:
                    this.filterFormId === 34 &&
                    currentNode.data.form_identifier &&
                    parseInt(currentNode.data.form_identifier) > 0
                      ? {
                          formIdentifier: currentNode.data.form_identifier,
                          taskId: currentNode.data.task_id,
                          processInstanceId:
                            currentNode.data.process_instance_id,
                        }
                      : "",
                });
              } else if (
                currentNode.data.status_id === "1001" ||
                currentNode.data.status_id === "1006"
              ) {
                // user task
                if (userDetails) {
                  if (
                    parseInt(this.loginEmployeeId) === parseInt(userDetails) ||
                    this.isAnyAdmin
                  ) {
                    this.orgchartArray.push({
                      id: node["next"][i],
                      name: values[`user${userDetails}`]["Name"],
                      title: currentNode.data.description,
                      Parent: parentNode,
                      iconName:
                        "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                      remarks: currentNode.data.remarks,
                      formDetails:
                        this.filterFormId === 34 &&
                        currentNode.data.form_identifier &&
                        parseInt(currentNode.data.form_identifier) > 0
                          ? {
                              formIdentifier: currentNode.data.form_identifier,
                              taskId: currentNode.data.task_id,
                              processInstanceId:
                                currentNode.data.process_instance_id,
                            }
                          : "",
                    });
                  } else {
                    this.orgchartArray.push({
                      id: node["next"][i],
                      name: values[`user${userDetails}`]["Name"],
                      title: currentNode.data.description,
                      Parent: parentNode,
                      iconName:
                        "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                      remarks: currentNode.data.remarks,
                    });
                  }
                }
                // group task
                else {
                  this.orgchartArray.push({
                    id: node["next"][i],
                    name: values[`group${currentNode.data.custom_group_id}`][
                      "Name"
                    ],
                    title: currentNode.data.description,
                    Parent: parentNode,
                    iconName:
                      "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                    remarks: currentNode.data.remarks,
                  });
                }
              } else {
                let empData = "";
                // check the type of approverUser
                if (currentNode.data.modalTaskData.typeOfApprove === "user") {
                  // check if the approverData is available
                  if (currentNode.data.modalTaskData.approveByUser) {
                    empData =
                      values[
                        `${currentNode.data.modalTaskData.typeOfApprove}${currentNode.data.modalTaskData.approveByUser.id}`
                      ];
                  } else {
                    empData = {
                      Name: "To be decided during runtime",
                    };
                  }
                } else {
                  empData =
                    values[
                      `${currentNode.data.modalTaskData.typeOfApprove}${currentNode.data.modalTaskData.approveByGroup.id}`
                    ];
                }
                this.orgchartArray.push({
                  id: node["next"][i],
                  name: empData["Name"],
                  title: currentNode.data.modalTaskData.title,
                  Parent: parentNode,
                  iconName:
                    "text-h3 hr-workflow-task-management-waitingforapprovaltask text-amber",
                  remarks: currentNode.data.remarks,
                  formDetails:
                    this.filterFormId === 34 &&
                    currentNode.data.modalTaskData.formId.id !== "0" &&
                    currentNode.data.modalTaskData.formId.id != undefined &&
                    currentNode.data.modalTaskData.formId.id > 0
                      ? {
                          formIdentifier: currentNode.data.form_identifier,
                          taskId: currentNode.data.task_id,
                          processInstanceId:
                            currentNode.data.process_instance_id,
                        }
                      : "",
                });
              }
            }
          } else {
            this.orgchartArray.push({
              id: node["next"][i],
              name: "",
              title: "",
              Parent: parentNode,
              iconName: "text-h3 hr-workflow-approval-management text-primary",
              remarks: "",
            });
          }
          this.prepareNode(nodes[node["next"][i]], nodes);
        }
      }
      let nodeLength = Object.keys(nodes).length;
      nodeLength = nodeLength - 1;
      if (nodeLength === this.orgchartArray.length && !this.isCalled) {
        this.isCalled = true;
        let formedOrgChartData = this.formOrgChartData(this.orgchartArray);
        this.orgChartData =
          formedOrgChartData && formedOrgChartData.length > 0
            ? formedOrgChartData[0].children &&
              formedOrgChartData[0].children.length > 0
              ? formedOrgChartData[0].children[0]
              : []
            : [];
        this.isLoadingCard = false;
      }
    },
    fomatOvertimeHours(totalHours) {
      if (!totalHours) {
        return "";
      }
      const hours = Math.floor(totalHours);
      const minutes = Math.round((totalHours - hours) * 60);
      return minutes ? `${hours} Hrs ${minutes} Mins` : `${hours} Hrs`;
    },

    formOrgChartData(items) {
      var tree = [],
        mappedArr = {};
      // Build a hash table and map items to objects
      items.forEach(function (item) {
        var id = item.id;
        // in case of duplicates
        if (!mappedArr.hasOwnProperty(id)) {
          mappedArr[id] = item; // the extracted id as key, and the item as value
          mappedArr[id].children = []; // under each item, add a key "children" with an empty array as value
        }
      });

      // If root-level nodes are not included in hash table, include them
      items.forEach(function (item) {
        var parentId = item.Parent;
        let nodeId =
          parentId === "taWorkStart" ? "taWorkStart" : "node_" + parentId;
        let nodeObj = mappedArr[nodeId];

        if (!mappedArr.hasOwnProperty(parentId)) {
          // make up an item for root-level node
          let newItem = {
            id: parentId,
            name: nodeObj && nodeObj.name ? nodeObj.name : "",
            title: nodeObj && nodeObj.title ? nodeObj.title : "",
            iconName: nodeObj && nodeObj.iconName ? nodeObj.iconName : "",
            // Parent: "",
            Parent: nodeObj && nodeObj.Parent ? nodeObj.Parent : "",
            children: [],
            remarks: nodeObj && nodeObj.remarks ? nodeObj.remarks : "",
            completedOn:
              nodeObj && nodeObj.completedOn ? nodeObj.completedOn : "",
            formDetails:
              nodeObj && nodeObj.formDetails ? nodeObj.formDetails : "",
          };
          mappedArr[parentId] = newItem; // the parent id as key, and made-up an item as value
          if (newItem.Parent) {
            delete mappedArr["node_" + parentId];
          }
        }
      });

      // Loop over hash table
      for (var id in mappedArr) {
        if (mappedArr.hasOwnProperty(id)) {
          let mappedElem = mappedArr[id];

          // If the element is not at the root level, add it to its parent array of children. Note this will continue till we have only root level elements left
          if (mappedElem.Parent) {
            var parentId = mappedElem.Parent;
            mappedArr[parentId].children.push(mappedElem);
          }

          // If the element is at the root level, directly push to the tree
          else {
            tree.push(mappedElem);
          }
        }
      }

      return tree;
    },

    closeFormRenderForm() {
      this.openFormRender = false;
    },

    openDynamicForm(formDetails) {
      let vm = this;
      const { formIdentifier, taskId, processInstanceId } = formDetails;
      vm.taskId = taskId;
      vm.processInstanceId = processInstanceId;
      vm.isLoading = true;
      vm.$apollo
        .query({
          query: GET_DYNAMIC_FORM_DETAILS,
          variables: {
            envelope: {
              orgCode: vm.orgCode,
              loggedInUserId: vm.loginEmployeeId,
            },
            dynamicFormId: parseInt(formIdentifier),
            workflowTaskId: taskId,
          },
          client: "apolloClientZ",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getWorkflowTaskDynamicFormDetails &&
            response.data.getWorkflowTaskDynamicFormDetails.result
          ) {
            const { dynamicFormTemplates, dynamicFormResponse } =
              response.data.getWorkflowTaskDynamicFormDetails.result;
            let formJson = "";
            if (dynamicFormResponse) {
              // response of the form -- empty if it is not submitted before, or the previous response is returned
              formJson = dynamicFormResponse.formResponse;
            } else {
              // form template for new data
              formJson = dynamicFormTemplates.template;
            }
            vm.formJsonData = JSON.parse(formJson);
            vm.conversationalId = dynamicFormTemplates.conversational;
            vm.formResponseId = dynamicFormResponse
              ? parseInt(dynamicFormResponse.formResponseId)
              : null;
            vm.openFormRender = true;
            vm.isLoading = false;
          } else {
            vm.handleFormRetrieveError();
          }
        })
        .catch(() => {
          vm.handleFormRetrieveError();
        });
    },
    handleFormRetrieveError() {
      this.isLoading = false;
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message:
          "Something went wrong while retrieving the form details. Please try after some time.",
      };
      this.showAlert(snackbarData);
    },

    getEmployeeResignationPayslipDetails() {
      let vm = this;
      vm.isFetchingResignationPayslipDetails = true;
      vm.showEditButton = false;
      vm.$apollo
        .query({
          query: GET_RESIGNATION_PAYSLIP_DETAILS,
          variables: {
            employeeId: vm.selectedEmpId
              ? vm.selectedEmpId
              : vm.loginEmployeeId,
          },
          client: "apolloClientL",
        })
        .then((response) => {
          if (
            response &&
            response.data &&
            response.data.getResignationPayslipDetails &&
            response.data.getResignationPayslipDetails.payslipDetails
          ) {
            const { payslipGenerated, resignationDateOverride, salaryDate } =
              response.data.getResignationPayslipDetails.payslipDetails;
            let salaryDateFormatted = moment(salaryDate);
            let resignationStatus = vm.resignationDetails.approvalStatus;
            if (payslipGenerated && resignationDateOverride) {
              vm.resignationAppliedDateMin = moment(salaryDate).subtract(
                "1",
                "days"
              );
            } else {
              vm.resignationAppliedDateMin = moment(salaryDate);
            }
            vm.resignationAppliedDateMin = new Date(
              vm.resignationAppliedDateMin
            );
            // exit/resignation date should be greater than the salary date, to allow edit resignation after approval
            let diffSalDateExitDate = moment(
              vm.resignationDetails.exitDate
            ).isBefore(salaryDateFormatted);
            if (
              vm.isAnyAdmin &&
              ((resignationStatus === "Approved" && diffSalDateExitDate) ||
                resignationStatus === "Applied" ||
                resignationStatus === "Incomplete")
            ) {
              vm.showEditButton = true;
            }
          } else {
            vm.isFetchingResignationPayslipDetails = false;
          }
        })
        .catch(() => {
          vm.isFetchingResignationPayslipDetails = false;
        });
    },

    handleResignationEditSuccess() {
      this.showEditForm = false;
      this.$emit("update-success");
    },

    openEditForm() {
      this.showEditForm = true;
    },

    getEmployeeJobPostDetails() {
      let vm = this;
      vm.isFetchingJobPostDetails = true;
      vm.$apollo
        .query({
          query: GET_JOB_POST_DETAILS,
          variables: {
            jobPostId: vm.approvalDetails.instanceData.jobPostId,
            employeeId: vm.loginEmployeeId,
            action: "view",
          },
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.retrieveJobPost &&
            response.data.retrieveJobPost.jobPostData
          ) {
            vm.apiJobPostDetails = response.data.retrieveJobPost.jobPostData;
            this.initQuillEditor();
          }
          vm.isFetchingJobPostDetails = false;
        })
        .catch(() => {
          vm.isFetchingJobPostDetails = false;
        });
    },
    convertCamelCaseToPascalCase(text) {
      const result = text?.replace(/([A-Z])/g, " $1");
      const finalResult = result?.charAt(0).toUpperCase() + result?.slice(1);
      return finalResult;
    },
    getLeaveBalanceDetails() {
      let vm = this;
      vm.isLoadingCard = true;
      vm.$apollo
        .query({
          query: LIST_LEAVE_HISTORY,
          variables: {
            employeeId: vm.selectedEmpId,
            source: "leave",
          },
          client: "apolloClientC",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.empLeaveHistory &&
            response.data.empLeaveHistory.leaveHistory
          ) {
            let leaveTypeList = JSON.parse(
              response.data.empLeaveHistory.leaveHistory
            );
            leaveTypeList = leaveTypeList.filter(
              (el) => el.LeaveType_Id === vm.leaveDetails.leaveTypeId
            );
            leaveTypeList = leaveTypeList.map((item) => {
              item.coDays = item.coDays ? item.coDays : 0;
              item.eligibleDays = item.eligibleDays ? item.eligibleDays : 0;
              item.currentYearEligibleDays = item.currentYearEligibleDays
                ? item.currentYearEligibleDays
                : 0;
              item.leavesTaken = item.leavesTaken ? item.leavesTaken : 0;
              item.currentLeaveEligibility =
                parseFloat(item.eligibleDays) + parseFloat(item.coDays);
              item.totalEligibleDays =
                parseFloat(item.currentYearEligibleDays) +
                parseFloat(item.coDays);
              item.leaveBalanceBasedOnPeriod =
                parseFloat(item.currentLeaveEligibility) -
                parseFloat(item.leavesTaken);
              item.leavesBalancePerAnnum =
                parseFloat(item.totalEligibleDays) -
                parseFloat(item.leavesTaken);
              return item;
            });
            vm.leaveBalanceDetails =
              leaveTypeList.length > 0 ? leaveTypeList[0] : {};
          }
          vm.isLoadingCard = false;
        })
        .catch(() => {
          vm.isLoadingCard = false;
        });
    },
    convertEmojiCodepointsToEmojis(text) {
      return text.replace(/\[EMOJI:([0-9a-f-]+)\]/gi, (match, codePoints) => {
        // Split by dash if there are multiple code points
        const codePointArray = codePoints.split("-");

        // Convert each hex code point back to a character and join them
        const emoji = codePointArray
          .map((hex) => String.fromCodePoint(parseInt(hex, 16)))
          .join("");

        return emoji;
      });
    },
    convertToMonth(value) {
      if (!value) return "-";
      return moment(value, "M,YYYY").isValid()
        ? moment(value, "M,YYYY").format("MMM YYYY")
        : null;
    },
    // show success/error message in snackbar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
});
</script>

<style scoped>
@import "../../../assets/css/orgchart.css";
.quill-editorView {
  height: auto;
}
::v-deep .ql-toolbar.ql-snow {
  display: none !important;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
  border: none;
}
::v-deep .ql-editor {
  padding: 0px;
}
</style>
