<template>
  <v-container class="py-2 px-1 relative" fluid>
    <perfect-scrollbar>
      <v-sheet>
        <div class="close_modal" @click="$emit('onClose')">
          <v-icon color="primary" size="20" class="ma-4"> fas fa-times </v-icon>
        </div>
      </v-sheet>
      <v-row no-gutters style="height: calc(100vh - 180px); overflow: scroll">
        <v-col cols="4" class="border-e-sm">
          <v-sheet class="ma-1 px-2 py-1 d-flex items-center align-center">
            <i class="fas fa-tasks"></i>
            <p class="font-weight-bold px-2">Task</p>
          </v-sheet>
          <v-divider></v-divider>
          <div style="max-height: calc(100vh - 220px); overflow-y: scroll">
            <v-form ref="taskForm">
              <v-sheet class="ma-1 px-3 pt-2 d-flex align-center">
                <v-text-field
                  v-model="taskTitle"
                  placeholder=""
                  ref="taskTitle"
                  class="custom-label px-1"
                  label=""
                  :rules="[required('Task title', taskTitle)]"
                  variant="solo"
                  density="compact"
                  maxlength="100"
                  style="min-width: 100%"
                >
                  <template v-slot:label>
                    Task title
                    <span style="color: red">*</span>
                  </template></v-text-field
                >
              </v-sheet>
              <v-divider></v-divider>
              <v-sheet class="mx-1 px-2 pt-1 d-flex align-center">
                <p class="px-2 custom-label">Type of approve</p>
                <v-radio-group inline class="d-flex" v-model="taskApproveType">
                  <v-radio
                    label=""
                    value="user"
                    color="primary"
                    density="compact"
                  >
                    <template v-slot:label>
                      <p class="custom-label pl-1 pr-2">User</p>
                    </template>
                  </v-radio>
                  <v-radio
                    label=""
                    value="group"
                    color="primary"
                    density="compact"
                  >
                    <template v-slot:label>
                      <p class="custom-label pl-1 pr-2">Group</p>
                    </template>
                  </v-radio>
                </v-radio-group>
              </v-sheet>
              <v-sheet class="pl-1 pr-3">
                <v-row no-gutters>
                  <v-radio-group
                    inline
                    :disabled="taskApproveType !== 'user'"
                    v-model="taskUserApproveUser"
                  >
                    <v-col cols="12" class="py-0 pl-2 d-flex">
                      <v-radio
                        label=""
                        value="selectedUser"
                        cols="12"
                        color="primary"
                      >
                        <template v-slot:label>
                          <p class="custom-label pl-1 pr-2">
                            Who can approve?(User)
                          </p>
                        </template>
                      </v-radio>
                    </v-col>
                    <v-card
                      variant="flat"
                      :disabled="
                        taskUserApproveUser !== `selectedUser` ||
                        taskApproveType !== 'user'
                      "
                      class="mx-1 px-2 pt-0 d-flex w-100"
                    >
                      <v-col cols="12" sm="6" md="12" class="pb-0">
                        <CustomSelect
                          :items="userApproveList"
                          :itemSelected="selectedUserApprove"
                          itemValue="UserId"
                          itemTitle="userInfo"
                          :isLoading="userApproveLoader"
                          :isAutoComplete="true"
                          :isRequired="
                            taskUserApproveUser === `selectedUser` &&
                            taskApproveType === 'user'
                              ? true
                              : false
                          "
                          label="User"
                          ref="user"
                          v-model="selectedUserApprove"
                          :rules="[
                            taskUserApproveUser === `selectedUser` &&
                            taskApproveType === 'user'
                              ? required('User', selectedUserApprove)
                              : true,
                          ]"
                          @selected-item="(e) => {}"
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                    </v-card>
                    <v-col cols="12" class="py-0 pl-2 d-flex pt-0">
                      <v-radio label="" value="initiator" color="primary">
                        <template v-slot:label>
                          <p class="custom-label pl-1 pr-2">Initiator</p>
                        </template>
                      </v-radio>
                    </v-col>
                    <v-col cols="12" class="py-0 pl-2 d-flex">
                      <v-radio label="" value="api" color="primary">
                        <template v-slot:label>
                          <p class="custom-label pl-1 pr-2">
                            Decider at runtime by calling an API
                          </p>
                        </template>
                      </v-radio>
                    </v-col>
                    <v-card
                      variant="flat"
                      :disabled="
                        taskUserApproveUser !== `api` ||
                        taskApproveType !== 'user'
                      "
                      class="mx-1 px-2 d-flex w-100"
                    >
                      <v-col cols="12" sm="6" md="12" class="pb-0">
                        <CustomSelect
                          :itemSelected="taskRuntimeUserAPI"
                          v-model="taskRuntimeUserAPI"
                          :items="getApproveList"
                          item-title="name"
                          label="API"
                          item-value="endpoint_key"
                          :isLoading="getApproveLoader"
                          :isAutoComplete="true"
                          @selected-item="(e) => {}"
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                    </v-card>
                  </v-radio-group>
                </v-row>
                <v-divider></v-divider>
                <v-row class="pt-3">
                  <v-radio-group
                    inline
                    :disabled="taskApproveType === 'user'"
                    v-model="taskUserApproveGroup"
                  >
                    <v-col cols="12" class="py-0 pl-5 d-flex">
                      <v-radio label="" value="selectedGroup" color="primary">
                        <template v-slot:label>
                          <p class="custom-label pl-1 pr-2">
                            Who can approve?(Group)
                          </p>
                        </template>
                      </v-radio>
                      <v-btn
                        class="mt-1 ml-2"
                        color="primary"
                        variant="text"
                        size="small"
                        @click="openAddGroup()"
                      >
                        <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                        Add Group
                      </v-btn>
                    </v-col>
                    <v-card
                      variant="flat"
                      :disabled="
                        taskApproveType === 'user' ||
                        taskUserApproveGroup !== `selectedGroup`
                      "
                      class="mx-1 px-2 d-flex w-100"
                    >
                      <v-col
                        cols="12"
                        sm="6"
                        md="12"
                        class="pb-0 d-flex align-center"
                      >
                        <CustomSelect
                          :itemSelected="selectedGroupApprove"
                          v-model="selectedGroupApprove"
                          :items="groupApproveList"
                          item-title="Group_Name"
                          ref="groupRef"
                          item-value="Group_Id"
                          label="Group"
                          :isLoading="groupApproveLoader"
                          :isAutoComplete="true"
                          @selected-item="(e) => {}"
                          :isRequired="
                            taskUserApproveGroup === `selectedGroup` &&
                            taskApproveType === 'group'
                              ? true
                              : false
                          "
                          :rules="[
                            taskUserApproveGroup === `selectedGroup` &&
                            taskApproveType === 'group'
                              ? required('Group', selectedGroupApprove)
                              : true,
                          ]"
                          variant="solo"
                        >
                        </CustomSelect>
                        <v-icon
                          class="ml-2"
                          @click="fetchGetGroup()"
                          size="17"
                          color="grey"
                          >fas fa-redo-alt</v-icon
                        >
                      </v-col>
                    </v-card>
                    <v-col cols="12" class="py-0 pl-5 d-flex">
                      <v-radio label="" value="api" color="primary">
                        <template v-slot:label>
                          <p class="custom-label pl-1 pr-2">
                            Decider at runtime by calling an API
                          </p>
                        </template>
                      </v-radio>
                    </v-col>
                    <v-card
                      variant="flat"
                      class="mx-1 px-2 d-flex w-100"
                      :disabled="
                        taskApproveType === 'user' ||
                        taskUserApproveGroup !== `api`
                      "
                    >
                      <v-col cols="12" sm="6" md="12" class="pb-0 px-6">
                        <CustomSelect
                          :itemSelected="taskRuntimeGroupAPI"
                          v-model="taskRuntimeGroupAPI"
                          :items="getApproveList"
                          item-title="name"
                          label="API"
                          item-value="endpoint_key"
                          :isLoading="getApproveLoader"
                          :isAutoComplete="true"
                          @selected-item="(e) => {}"
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                    </v-card>
                  </v-radio-group>
                </v-row>
              </v-sheet>
            </v-form>
            <v-divider></v-divider>
            <v-col cols="12" sm="6" md="12" class="pb-0 d-flex align-center">
              <CustomSelect
                label="Form"
                :itemSelected="selectedFormData"
                v-model="selectedFormData"
                :items="formGetList"
                item-title="templateName"
                item-value="templateId"
                :isLoading="formGetLoader"
                :isAutoComplete="true"
                @selected-item="(e) => {}"
                variant="solo"
              >
              </CustomSelect>
              <v-icon
                class="ml-2"
                @click="fetchGetForm()"
                size="17"
                color="grey"
                >fas fa-redo-alt</v-icon
              >
            </v-col>
            <v-btn
              class="mt-1 ml-2"
              color="primary"
              variant="text"
              size="small"
              @click="openAddForm()"
            >
              <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
              Add Form
            </v-btn>
          </div>
        </v-col>

        <!-- here starting the deadline section -->
        <v-col cols="4" class="border-e-sm">
          <v-sheet class="ma-1 px-2 py-1 d-flex items-center align-center">
            <i class="fas fa-wave-square"></i>
            <p class="font-weight-bold px-2">Deadline</p>
          </v-sheet>
          <v-divider></v-divider>
          <div
            class="pa-1 pb-3"
            style="max-height: calc(100vh - 220px); overflow-y: scroll"
          >
            <v-sheet class="px-2">
              <v-sheet class="d-flex items-center align-center">
                <p class="custom-label">
                  Do you want to define deadline for this task?
                </p>
                <v-switch
                  density="compact"
                  :true-value="true"
                  :false-value="false"
                  class="check text-pink ml-4"
                  v-model="deadLine1.checked"
                  direction="horizontal"
                  value="emailNotification"
                  hide-details
                  color="primary"
                ></v-switch>
              </v-sheet>
              <v-card
                color="grey-lighten-4"
                :class="deadLine1.checked ? 'card-blue-background' : ''"
                :disabled="!deadLine1.checked"
              >
                <v-sheet>
                  <p class="custom-label px-2 py-2 font-weight-bold">
                    Deadline 1
                  </p>
                  <v-sheet class="pl-3 pr-2 d-flex align-center">
                    <!-- <p class="custom-label "></p> -->
                    <v-text-field
                      class="custom-label"
                      label="Deadline (in hours)"
                      v-model="deadLine1.hours"
                      variant="solo"
                      density="compact"
                      maxlength="40"
                    ></v-text-field>
                  </v-sheet>
                  <v-sheet class="pl-3 d-flex align-center">
                    <p class="custom-label">
                      What should happen if the deadline is reached?
                    </p>
                  </v-sheet>
                  <v-sheet>
                    <v-row no-gutters>
                      <v-radio-group inline v-model="deadLine1.deadLineRadio">
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio
                            label=""
                            value="autoApprove"
                            cols="12"
                            color="primary"
                          >
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">Auto approve</p>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio
                            label="Auto Reject"
                            value="autoReject"
                            color="primary"
                          >
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">Auto reject</p>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio
                            label=""
                            value="usersManager"
                            color="primary"
                          >
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">
                                Assign to initiator's manager
                              </p>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio label="" value="deadlineApi" color="primary">
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">Call an API</p>
                            </template>
                          </v-radio>
                        </v-col>
                      </v-radio-group>
                    </v-row>
                  </v-sheet>
                  <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                    <CustomSelect
                      :itemSelected="deadLine1.apiData"
                      v-model="deadLine1.apiData"
                      :items="getApproveList"
                      label="API"
                      item-title="name"
                      item-value="endpoint_key"
                      :disabled="deadLine1.deadLineRadio !== 'deadlineApi'"
                      :isLoading="getApproveLoader"
                      :isAutoComplete="true"
                      @selected-item="(e) => {}"
                      variant="solo"
                    >
                    </CustomSelect>
                  </v-col>
                </v-sheet>
              </v-card>
            </v-sheet>

            <!-- here ending deadline 1  -->

            <v-divider class="mt-3"></v-divider>
            <v-sheet class="px-2">
              <v-sheet class="d-flex items-center align-center">
                <p class="custom-label">
                  Do you want to define another action if deadline 1 exceeds?
                </p>
                <v-switch
                  density="compact"
                  :true-value="true"
                  :false-value="false"
                  v-model="deadLine2.checked"
                  class="check text-pink ml-4"
                  direction="horizontal"
                  value="emailNotification"
                  hide-details
                  color="primary"
                ></v-switch>
              </v-sheet>
              <v-card
                variant="elevated"
                color="grey-lighten-4"
                :class="deadLine2.checked ? 'card-blue-background' : ''"
                :disabled="!deadLine2.checked"
              >
                <v-sheet>
                  <p class="custom-label px-2 py-2 font-weight-bold">
                    Deadline 2
                  </p>
                  <v-sheet class="pl-3 pr-2 d-flex align-center">
                    <v-text-field
                      v-model="deadLine2.hours"
                      class="custom-label px-1"
                      label="Deadline (in hours)"
                      maxlength="40"
                      variant="solo"
                      density="compact"
                    ></v-text-field>
                  </v-sheet>
                  <v-sheet class="pl-3 d-flex align-center">
                    <p class="custom-label">
                      What should happen if the deadline is reached?
                    </p>
                  </v-sheet>
                  <v-sheet>
                    <v-row no-gutters>
                      <v-radio-group inline v-model="deadLine2.deadLineRadio">
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio
                            label=""
                            value="autoApprove"
                            cols="12"
                            color="primary"
                          >
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">Auto approve</p>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio label="" value="autoReject" color="primary">
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">Auto reject</p>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio
                            label=""
                            value="usersManager"
                            color="primary"
                          >
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">
                                Assign to user's manager
                              </p>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio label="" value="deadlineApi" color="primary">
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">Call an API</p>
                            </template>
                          </v-radio>
                        </v-col>
                      </v-radio-group>
                    </v-row>
                  </v-sheet>
                  <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                    <CustomSelect
                      :itemSelected="deadLine2.apiData"
                      v-model="deadLine2.apiData"
                      :items="getApproveList"
                      item-title="name"
                      label="API"
                      item-value="endpoint_key"
                      :isLoading="getApproveLoader"
                      :disabled="deadLine2.deadLineRadio !== 'deadlineApi'"
                      :isAutoComplete="true"
                      @selected-item="(e) => {}"
                      variant="solo"
                    >
                    </CustomSelect>
                  </v-col>
                </v-sheet>
              </v-card>
            </v-sheet>
            <v-divider class="mt-3"></v-divider>
            <!-- here ending deadline 2  -->
            <v-sheet class="px-2">
              <v-sheet class="d-flex items-center align-center">
                <p class="custom-label">
                  Do you want to define another action if deadline 2 exceeds?
                </p>
                <v-switch
                  label=""
                  :true-value="true"
                  :false-value="false"
                  density="compact"
                  class="check text-pink ml-4"
                  v-model="deadLine3.checked"
                  value="emailNotification"
                  hide-details
                  color="primary"
                ></v-switch>
              </v-sheet>
              <v-card
                variant="elevated"
                color="grey-lighten-4"
                :class="deadLine3.checked ? 'card-blue-background' : ''"
                :disabled="!deadLine3.checked"
              >
                <v-sheet>
                  <p class="custom-label px-2 py-2 font-weight-bold">
                    Deadline 3
                  </p>
                  <v-sheet class="pl-3 pr-2 d-flex align-center">
                    <v-text-field
                      class="custom-label px-1"
                      v-model="deadLine3.hours"
                      variant="solo"
                      density="compact"
                      label="Deadline (in hours)"
                      maxlength="40"
                    ></v-text-field>
                  </v-sheet>
                  <v-sheet class="pl-3 d-flex align-center">
                    <p class="custom-label">
                      What should happen if the deadline is reached?
                    </p>
                  </v-sheet>
                  <v-sheet>
                    <v-row no-gutters>
                      <v-radio-group inline v-model="deadLine3.deadLineRadio">
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio
                            label=""
                            value="autoApprove"
                            cols="12"
                            color="primary"
                          >
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">Auto approve</p>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio label="" value="autoReject" color="primary">
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">Auto reject</p>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio
                            label=""
                            value="usersManager"
                            color="primary"
                          >
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">
                                Assign to user's manager
                              </p>
                            </template>
                          </v-radio>
                        </v-col>
                        <v-col cols="12" class="py-0 pl-3 d-flex">
                          <v-radio label="" value="deadlineApi" color="primary">
                            <template v-slot:label>
                              <p class="custom-label pl-1 pr-2">Call an API</p>
                            </template>
                          </v-radio>
                        </v-col>
                      </v-radio-group>
                    </v-row>
                  </v-sheet>
                  <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                    <CustomSelect
                      :itemSelected="deadLine3.apiData"
                      v-model="deadLine3.apiData"
                      :items="getApproveList"
                      label="API"
                      item-title="name"
                      item-value="endpoint_key"
                      :disabled="deadLine3.deadLineRadio !== 'deadlineApi'"
                      :isLoading="getApproveLoader"
                      :isAutoComplete="true"
                      @selected-item="(e) => {}"
                      variant="solo"
                    >
                    </CustomSelect>
                  </v-col>
                </v-sheet>
              </v-card>
            </v-sheet>
          </div>
        </v-col>

        <!-- here starting the notification section -->
        <v-col cols="4">
          <v-sheet class="ma-1 px-2 py-1 d-flex items-center align-center">
            <i class="far fa-bell"></i>
            <p class="font-weight-bold px-2">Notification</p>
          </v-sheet>
          <v-divider></v-divider>
          <div
            class="pa-1"
            style="max-height: calc(100vh - 220px); overflow-y: scroll"
          >
            <v-form ref="notificationForm">
              <v-sheet class="ma-1 pa-2">
                <p class="custom-label">Send notification</p>

                <v-sheet class="d-flex items-center align-center">
                  <p class="custom-label pl-1">When task is assigned</p>
                  <v-switch
                    label=""
                    density="compact"
                    v-model="taskAssigned.checked"
                    :true-value="true"
                    :false-value="false"
                    class="check text-pink ml-4"
                    direction="horizontal"
                    value="assignedValue"
                    hide-details
                    color="primary"
                  ></v-switch>
                </v-sheet>
                <v-card
                  class="px-2"
                  variant="flat"
                  :disabled="!taskAssigned.checked"
                >
                  <v-sheet class="d-flex align-center items-center">
                    <p class="custom-label">To assigned user</p>
                    <v-switch
                      :true-value="true"
                      :false-value="false"
                      label=""
                      density="compact"
                      v-model="taskAssigned.user.checked"
                      class="check text-pink ml-4"
                      direction="horizontal"
                      value="assignedUser"
                      hide-details
                      color="primary"
                    ></v-switch>
                  </v-sheet>
                  <v-card
                    variant="flat"
                    class="px-1"
                    :disabled="!taskAssigned.user.checked"
                  >
                    <v-sheet class="d-flex items-center align-center">
                      <p class="custom-label">
                        Do you want to send an E-mail notification?
                      </p>
                      <v-switch
                        label=""
                        :true-value="true"
                        :false-value="false"
                        density="compact"
                        v-model="taskAssigned.user.emailNotification"
                        class="check text-pink ml-4 mr-3"
                        direction="horizontal"
                        value="emailNotification"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                      <CustomSelect
                        :disabled="!taskAssigned.user.emailNotification"
                        label="Template"
                        :itemSelected="taskAssigned.user.emailTemplate"
                        v-model="taskAssigned.user.emailTemplate"
                        :items="emailTemplateList"
                        item-title="TemplateName"
                        item-value="Template_Id"
                        :isLoading="emailTemplateGetLoader"
                        :isAutoComplete="true"
                        @selected-item="
                          (e) => {
                            templateChange('taskAssigned', 'user', 'email', e);
                          }
                        "
                        variant="solo"
                      >
                      </CustomSelect>
                    </v-col>

                    <v-sheet class="d-flex align-center">
                      <p class="custom-label">
                        Do you want to send an SMS notification?
                      </p>
                      <v-tooltip
                        location="bottom"
                        text="Additional subscription required"
                      >
                        <template v-slot:activator="{ props }">
                          <v-icon
                            color="blue"
                            v-bind="props"
                            class="ml-1"
                            size="20"
                            >fas fa-info-circle</v-icon
                          >
                        </template>
                      </v-tooltip>
                      <v-switch
                        density="compact"
                        :true-value="true"
                        :false-value="false"
                        v-model="taskAssigned.user.smsNotification"
                        class="check text-pink ml-4"
                        direction="horizontal"
                        value="smsNotification"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                      <CustomSelect
                        :disabled="!taskAssigned.user.smsNotification"
                        label="Template"
                        :itemSelected="taskAssigned.user.smsTemplate"
                        v-model="taskAssigned.user.smsTemplate"
                        :items="smsTemplateList"
                        item-title="TemplateName"
                        item-value="Template_Id"
                        :isLoading="smsTemplateGetLoader"
                        :isAutoComplete="true"
                        @selected-item="
                          (e) => {
                            templateChange('taskAssigned', 'user', 'sms', e);
                          }
                        "
                        variant="solo"
                      >
                      </CustomSelect>
                    </v-col>
                  </v-card>
                </v-card>

                <v-sheet class="px-2">
                  <v-card
                    variant="flat"
                    :disabled="!taskAssigned.checked"
                    class="d-flex items-center align-center"
                  >
                    <p class="custom-label">To other user</p>
                    <v-switch
                      density="compact"
                      :true-value="true"
                      :false-value="false"
                      v-model="taskAssigned.others.checked"
                      class="check text-pink ml-4 mr-2"
                      direction="horizontal"
                      value="assignedOtherUser"
                      hide-details
                      color="primary"
                    ></v-switch>
                  </v-card>
                  <v-card
                    class="px-2"
                    variant="flat"
                    :disabled="!taskAssigned.others.checked"
                  >
                    <v-sheet class="d-flex items-center align-center">
                      <p class="custom-label">
                        Do you want to send an E-mail notification?
                      </p>
                      <v-switch
                        density="compact"
                        :true-value="true"
                        :false-value="false"
                        v-model="taskAssigned.others.emailNotification"
                        class="check text-pink ml-4"
                        direction="horizontal"
                        value="emailNotification"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                      <CustomSelect
                        :disabled="!taskAssigned.others.emailNotification"
                        label="Template"
                        ref="othersEmailTemplate"
                        :itemSelected="taskAssigned.others.emailTemplate"
                        v-model="taskAssigned.others.emailTemplate"
                        :items="emailTemplateList"
                        item-title="TemplateName"
                        item-value="Template_Id"
                        :isRequired="taskAssigned.others.emailNotification"
                        :rules="[
                          taskAssigned.others.emailNotification
                            ? required(
                                'Template',
                                taskAssigned.others.emailTemplate
                              )
                            : true,
                        ]"
                        :isLoading="emailTemplateGetLoader"
                        :isAutoComplete="true"
                        @selected-item="
                          (e) => {
                            templateChange(
                              'taskAssigned',
                              'others',
                              'email',
                              e
                            );
                          }
                        "
                        variant="solo"
                      >
                      </CustomSelect>
                    </v-col>
                    <v-text-field
                      class="custom-label"
                      label=""
                      ref="othersSendTo"
                      variant="solo"
                      density="compact"
                      :isRequired="taskAssigned.others.emailNotification"
                      :rules="
                        taskAssigned.others.emailNotification
                          ? [
                              required(
                                'Send to',
                                taskAssigned.others.emailSendTo
                              ),
                              emailValidation(
                                'Email Address',
                                taskAssigned.others.emailSendTo
                              ),
                            ]
                          : []
                      "
                      v-model="taskAssigned.others.emailSendTo"
                      :disabled="!taskAssigned.others.emailNotification"
                      ><template v-slot:label>
                        Send to
                        <span
                          v-if="taskAssigned.others.emailNotification"
                          style="color: red"
                          >*</span
                        >
                      </template></v-text-field
                    >

                    <v-sheet class="font-weight-regular d-flex">
                      <p class="custom-label">
                        Do you want to send an SMS notification?
                      </p>
                      <v-tooltip
                        location="bottom"
                        text="Additional subscription required"
                      >
                        <template v-slot:activator="{ props }">
                          <v-icon
                            color="blue"
                            v-bind="props"
                            class="ml-1"
                            size="20"
                            >fas fa-info-circle</v-icon
                          >
                        </template>
                      </v-tooltip>
                      <v-switch
                        density="compact"
                        :true-value="true"
                        :false-value="false"
                        v-model="taskAssigned.others.smsNotification"
                        class="check text-pink ml-4"
                        direction="horizontal"
                        value="smsNotification"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                      <CustomSelect
                        :disabled="!taskAssigned.others.smsNotification"
                        label="Template"
                        :itemSelected="taskAssigned.others.smsTemplate"
                        v-model="taskAssigned.others.smsTemplate"
                        :items="smsTemplateList"
                        item-title="TemplateName"
                        item-value="Template_Id"
                        :isLoading="smsTemplateGetLoader"
                        :isAutoComplete="true"
                        @selected-item="
                          (e) => {
                            templateChange('taskAssigned', 'others', 'sms', e);
                          }
                        "
                        variant="solo"
                      >
                      </CustomSelect>
                    </v-col>
                    <v-text-field
                      label="Send to"
                      variant="solo"
                      density="compact"
                      v-model="taskAssigned.others.smsSendTo"
                      :disabled="!taskAssigned.others.smsNotification"
                    ></v-text-field>
                  </v-card>
                </v-sheet>
                <v-divider></v-divider>
              </v-sheet>
              <!-- here ending the when assigned task -->

              <v-sheet>
                <v-sheet class="px-2">
                  <v-sheet class="d-flex items-center align-center">
                    <p class="custom-label">When task is completed</p>
                    <v-switch
                      density="compact"
                      :true-value="true"
                      :false-value="false"
                      class="check text-pink ml-4"
                      direction="horizontal"
                      v-model="taskCompleted.checked"
                      value="assignedValue"
                      hide-details
                      color="primary"
                    ></v-switch>
                  </v-sheet>

                  <v-card
                    variant="flat"
                    class="px-2"
                    :disabled="!taskCompleted.checked"
                  >
                    <v-sheet class="d-flex align-center items-center">
                      <p class="custom-label">To assigned user</p>
                      <v-switch
                        density="compact"
                        :true-value="true"
                        :false-value="false"
                        class="check text-pink ml-4"
                        direction="horizontal"
                        v-model="taskCompleted.user.checked"
                        value="assignedUser"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-card
                      variant="flat"
                      :disabled="!taskCompleted.user.checked"
                      class="px-2"
                    >
                      <v-sheet class="d-flex items-center align-center">
                        <p class="custom-label">
                          Do you want to send an E-mail notification?
                        </p>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskCompleted.user.emailNotification"
                          value="emailNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskCompleted.user.emailNotification"
                          label="Template"
                          :itemSelected="taskCompleted.user.emailTemplate"
                          v-model="taskCompleted.user.emailTemplate"
                          :items="emailTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="emailTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskCompleted',
                                'user',
                                'email',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>

                      <v-sheet class="d-flex align-center">
                        <p class="custom-label">
                          Do you want to send an SMS notification?
                        </p>
                        <v-tooltip
                          location="bottom"
                          text="Additional subscription required"
                        >
                          <template v-slot:activator="{ props }">
                            <v-icon
                              color="blue"
                              v-bind="props"
                              class="ml-1"
                              size="20"
                              >fas fa-info-circle</v-icon
                            >
                          </template>
                        </v-tooltip>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskCompleted.user.smsNotification"
                          value="smsNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskCompleted.user.smsNotification"
                          label="Template"
                          :itemSelected="taskCompleted.user.smsTemplate"
                          v-model="taskCompleted.user.smsTemplate"
                          :items="smsTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="smsTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange('taskCompleted', 'user', 'sms', e);
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                    </v-card>
                  </v-card>
                  <v-card
                    variant="flat"
                    class="px-2"
                    :disabled="!taskCompleted.checked"
                  >
                    <v-sheet class="d-flex items-center align-center">
                      <p class="custom-label">To other user</p>
                      <v-switch
                        density="compact"
                        :true-value="true"
                        :false-value="false"
                        class="check text-pink ml-4"
                        direction="horizontal"
                        v-model="taskCompleted.others.checked"
                        value="assignedOtherUser"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-card
                      variant="flat"
                      :disabled="!taskCompleted.others.checked"
                      class="px-2"
                    >
                      <v-sheet class="d-flex items-center align-center">
                        <p class="custom-label">
                          Do you want to send an E-mail notification?
                        </p>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskCompleted.others.emailNotification"
                          value="emailNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskCompleted.others.emailNotification"
                          label="Template"
                          ref="taskCompletedEmailTemplate"
                          :itemSelected="taskCompleted.others.emailTemplate"
                          v-model="taskCompleted.others.emailTemplate"
                          :items="emailTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isRequired="taskCompleted.others.emailNotification"
                          :rules="[
                            taskCompleted.others.emailNotification
                              ? required(
                                  'Template',
                                  taskCompleted.others.emailTemplate
                                )
                              : true,
                          ]"
                          :isLoading="emailTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskCompleted',
                                'others',
                                'email',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                      <v-text-field
                        class="custom-label"
                        label=""
                        variant="solo"
                        ref="taskCompletedSendTo"
                        :disabled="!taskCompleted.others.emailNotification"
                        v-model="taskCompleted.others.emailSendTo"
                        :isRequired="taskCompleted.others.emailNotification"
                        :rules="
                          taskCompleted.others.emailNotification
                            ? [
                                required(
                                  'Send to',
                                  taskCompleted.others.emailSendTo
                                ),
                                emailValidation(
                                  'Email Address',
                                  taskCompleted.others.emailSendTo
                                ),
                              ]
                            : []
                        "
                        maxlength="100"
                        density="compact"
                      >
                        <template v-slot:label>
                          Send to
                          <span
                            v-if="taskCompleted.others.emailNotification"
                            style="color: red"
                            >*</span
                          >
                        </template></v-text-field
                      >

                      <v-sheet class="font-weight-regular d-flex">
                        <p class="custom-label">
                          Do you want to send an SMS notification?
                        </p>
                        <v-tooltip
                          location="bottom"
                          text="Additional subscription required"
                        >
                          <template v-slot:activator="{ props }">
                            <v-icon
                              color="blue"
                              v-bind="props"
                              class="ml-1"
                              size="20"
                              >fas fa-info-circle</v-icon
                            >
                          </template>
                        </v-tooltip>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskCompleted.others.smsNotification"
                          value="smsNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskCompleted.others.smsNotification"
                          label="Template"
                          :itemSelected="taskCompleted.others.smsTemplate"
                          v-model="taskCompleted.others.smsTemplate"
                          :items="smsTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="smsTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskCompleted',
                                'others',
                                'sms',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                      <v-text-field
                        label="Send to"
                        variant="solo"
                        density="compact"
                        v-model="taskCompleted.others.smsSendTo"
                        :disabled="!taskCompleted.others.smsNotification"
                        maxlength="100"
                      ></v-text-field>
                    </v-card>
                  </v-card>
                  <v-divider></v-divider>
                </v-sheet>
              </v-sheet>
              <!-- here ending the when completed task -->

              <v-sheet>
                <v-sheet class="px-2">
                  <v-sheet class="d-flex items-center align-center">
                    <p class="custom-label">When Deadline before exceeded</p>
                    <v-switch
                      :true-value="true"
                      :false-value="false"
                      density="compact"
                      class="check text-pink ml-4"
                      direction="horizontal"
                      v-model="taskBeforeExceed.checked"
                      value="assignedValue"
                      hide-details
                      color="primary"
                    ></v-switch>
                  </v-sheet>
                  <v-text-field
                    label="Time Before exceeded"
                    variant="solo"
                    density="compact"
                    v-model="taskBeforeExceed.beforeExceedHours"
                    :disabled="!taskBeforeExceed.checked"
                    maxlength="100"
                  ></v-text-field>
                  <v-card
                    variant="flat"
                    class="px-2"
                    :disabled="!taskBeforeExceed.checked"
                  >
                    <p class="custom-label">Time before exceeded</p>
                    <v-sheet class="d-flex align-center items-center">
                      <p class="custom-label">To assigned user</p>
                      <v-switch
                        density="compact"
                        :true-value="true"
                        :false-value="false"
                        class="check text-pink ml-4"
                        direction="horizontal"
                        v-model="taskBeforeExceed.user.checked"
                        value="assignedUser"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-card
                      variant="flat"
                      :disabled="!taskBeforeExceed.user.checked"
                      class="px-2"
                    >
                      <v-sheet class="d-flex items-center align-center">
                        <p class="custom-label">
                          Do you want to send an E-mail notification?
                        </p>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskBeforeExceed.user.emailNotification"
                          value="emailNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskBeforeExceed.user.emailNotification"
                          label="Template"
                          :itemSelected="taskBeforeExceed.user.emailTemplate"
                          v-model="taskBeforeExceed.user.emailTemplate"
                          :items="emailTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="emailTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskBeforeExceed',
                                'user',
                                'email',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>

                      <v-sheet class="d-flex align-center">
                        <p class="custom-label">
                          Do you want to send an SMS notification?
                        </p>
                        <v-tooltip
                          location="bottom"
                          text="Additional subscription required"
                        >
                          <template v-slot:activator="{ props }">
                            <v-icon
                              color="blue"
                              v-bind="props"
                              class="ml-1"
                              size="20"
                              >fas fa-info-circle</v-icon
                            >
                          </template>
                        </v-tooltip>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskBeforeExceed.user.smsNotification"
                          value="smsNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskBeforeExceed.user.smsNotification"
                          label="Template"
                          :itemSelected="taskBeforeExceed.user.smsTemplate"
                          v-model="taskBeforeExceed.user.smsTemplate"
                          :items="smsTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="smsTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskBeforeExceed',
                                'user',
                                'sms',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                    </v-card>
                  </v-card>
                  <v-card
                    variant="flat"
                    :disabled="!taskBeforeExceed.checked"
                    class="px-2"
                  >
                    <v-sheet class="d-flex items-center align-center">
                      <p class="custom-label">To other user</p>
                      <v-switch
                        density="compact"
                        :true-value="true"
                        :false-value="false"
                        class="check text-pink ml-4"
                        direction="horizontal"
                        v-model="taskBeforeExceed.others.checked"
                        value="assignedOtherUser"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-card
                      variant="flat"
                      :disabled="!taskBeforeExceed.others.checked"
                      class="px-2"
                    >
                      <v-sheet class="d-flex items-center align-center">
                        <p class="custom-label">
                          Do you want to send an E-mail notification?
                        </p>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskBeforeExceed.others.emailNotification"
                          value="emailNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskBeforeExceed.others.emailNotification"
                          label="Template"
                          :itemSelected="taskBeforeExceed.others.emailTemplate"
                          v-model="taskBeforeExceed.others.emailTemplate"
                          :items="emailTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          ref="taskBeforeExceedEmailTemplate"
                          :isLoading="emailTemplateGetLoader"
                          :isAutoComplete="true"
                          :isRequired="
                            taskBeforeExceed.others.emailNotification
                          "
                          :rules="[
                            taskBeforeExceed.others.emailNotification
                              ? required(
                                  'Template',
                                  taskBeforeExceed.others.emailTemplate
                                )
                              : true,
                          ]"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskBeforeExceed',
                                'others',
                                'email',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                      <v-text-field
                        class="custom-label"
                        label=""
                        variant="solo"
                        ref="taskBeforeExceedEmailSendTo"
                        :rules="
                          taskBeforeExceed.others.emailNotification
                            ? [
                                required(
                                  'Send to',
                                  taskBeforeExceed.others.emailSendTo
                                ),
                                emailValidation(
                                  'Email Address',
                                  taskBeforeExceed.others.emailSendTo
                                ),
                              ]
                            : []
                        "
                        :isRequired="taskBeforeExceed.others.emailNotification"
                        v-model="taskBeforeExceed.others.emailSendTo"
                        :disabled="!taskBeforeExceed.others.emailNotification"
                        density="compact"
                      >
                        <template v-slot:label>
                          Send to
                          <span
                            v-if="taskBeforeExceed.others.emailNotification"
                            style="color: red"
                            >*</span
                          >
                        </template></v-text-field
                      >

                      <v-sheet class="font-weight-regular d-flex">
                        <p class="custom-label">
                          Do you want to send an SMS notification?
                        </p>
                        <v-tooltip
                          location="bottom"
                          text="Additional subscription required"
                        >
                          <template v-slot:activator="{ props }">
                            <v-icon
                              color="blue"
                              v-bind="props"
                              class="ml-1"
                              size="20"
                              >fas fa-info-circle</v-icon
                            >
                          </template>
                        </v-tooltip>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskBeforeExceed.others.smsNotification"
                          value="smsNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskBeforeExceed.others.smsNotification"
                          label="Template"
                          :itemSelected="taskBeforeExceed.others.smsTemplate"
                          v-model="taskBeforeExceed.others.smsTemplate"
                          :items="smsTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="smsTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskBeforeExceed',
                                'others',
                                'sms',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                      <v-text-field
                        label="Send to"
                        variant="solo"
                        v-model="taskBeforeExceed.others.smsSendTo"
                        density="compact"
                        :disabled="!taskBeforeExceed.others.smsNotification"
                      ></v-text-field>
                    </v-card>
                  </v-card>
                  <v-divider></v-divider>
                </v-sheet>
              </v-sheet>
              <!-- here ending the before deadline task -->

              <v-sheet>
                <v-sheet class="px-2">
                  <v-sheet class="d-flex items-center align-center">
                    <p class="custom-label">When Deadline exceeded</p>
                    <v-switch
                      density="compact"
                      :true-value="true"
                      :false-value="false"
                      class="check text-pink ml-4"
                      direction="horizontal"
                      v-model="taskDeadLineExceed.checked"
                      value="assignedValue"
                      hide-details
                      color="primary"
                    ></v-switch>
                  </v-sheet>
                  <v-card
                    variant="flat"
                    class="px-2"
                    :disabled="!taskDeadLineExceed.checked"
                  >
                    <v-sheet class="d-flex align-center items-center">
                      <p class="custom-label">To assigned user</p>
                      <v-switch
                        density="compact"
                        :true-value="true"
                        :false-value="false"
                        class="check text-pink ml-4"
                        direction="horizontal"
                        v-model="taskDeadLineExceed.user.checked"
                        value="assignedUser"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-card
                      variant="flat"
                      :disabled="!taskDeadLineExceed.user.checked"
                      class="px-2"
                    >
                      <v-sheet class="d-flex items-center align-center">
                        <p class="custom-label">
                          Do you want to send an E-mail notification?
                        </p>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskDeadLineExceed.user.emailNotification"
                          value="emailNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskDeadLineExceed.user.emailNotification"
                          label="Template"
                          :itemSelected="taskDeadLineExceed.user.emailTemplate"
                          v-model="taskDeadLineExceed.user.emailTemplate"
                          :items="emailTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="emailTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskDeadLineExceed',
                                'user',
                                'email',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>

                      <v-sheet class="d-flex align-center">
                        <p class="custom-label">
                          Do you want to send an SMS notification?
                        </p>
                        <v-tooltip
                          location="bottom"
                          text="Additional subscription required"
                        >
                          <template v-slot:activator="{ props }">
                            <v-icon
                              color="blue"
                              v-bind="props"
                              class="ml-1"
                              size="20"
                              >fas fa-info-circle</v-icon
                            >
                          </template>
                        </v-tooltip>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskDeadLineExceed.user.smsNotification"
                          value="smsNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskDeadLineExceed.user.smsNotification"
                          label="Template"
                          :itemSelected="taskDeadLineExceed.user.smsTemplate"
                          v-model="taskDeadLineExceed.user.smsTemplate"
                          :items="smsTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="smsTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskDeadLineExceed',
                                'user',
                                'sms',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                    </v-card>
                  </v-card>
                  <v-card
                    variant="flat"
                    :disabled="!taskDeadLineExceed.checked"
                    class="px-2"
                  >
                    <v-sheet class="d-flex items-center align-center">
                      <p class="custom-label">To other user</p>
                      <v-switch
                        density="compact"
                        :true-value="true"
                        :false-value="false"
                        class="check text-pink ml-4"
                        direction="horizontal"
                        v-model="taskDeadLineExceed.others.checked"
                        value="assignedOtherUser"
                        hide-details
                        color="primary"
                      ></v-switch>
                    </v-sheet>
                    <v-card
                      variant="flat"
                      :disabled="!taskDeadLineExceed.others.checked"
                      class="px-2"
                    >
                      <v-sheet class="d-flex items-center align-center">
                        <p class="custom-label">
                          Do you want to send an E-mail notification?
                        </p>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskDeadLineExceed.others.emailNotification"
                          value="emailNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="
                            !taskDeadLineExceed.others.emailNotification
                          "
                          label="Template"
                          :itemSelected="
                            taskDeadLineExceed.others.emailTemplate
                          "
                          v-model="taskDeadLineExceed.others.emailTemplate"
                          :items="emailTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="emailTemplateGetLoader"
                          :isAutoComplete="true"
                          ref="taskDeadLineExceedEmailTemplate"
                          :isRequired="
                            taskDeadLineExceed.others.emailNotification
                          "
                          :rules="[
                            taskDeadLineExceed.others.emailNotification
                              ? required(
                                  'Template',
                                  taskDeadLineExceed.others.emailTemplate
                                )
                              : true,
                          ]"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskDeadLineExceed',
                                'others',
                                'email',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                      <v-text-field
                        class="custom-label"
                        label=""
                        variant="solo"
                        ref="taskDeadLineExceedEmailSendTo"
                        v-model="taskDeadLineExceed.others.emailSendTo"
                        :disabled="!taskDeadLineExceed.others.emailNotification"
                        :rules="
                          taskDeadLineExceed.others.emailNotification
                            ? [
                                required(
                                  'Send to',
                                  taskDeadLineExceed.others.emailSendTo
                                ),
                                emailValidation(
                                  'Email Address',
                                  taskDeadLineExceed.others.emailSendTo
                                ),
                              ]
                            : []
                        "
                        :isRequired="
                          taskDeadLineExceed.others.emailNotification
                        "
                        density="compact"
                      >
                        <template v-slot:label>
                          Send to
                          <span
                            v-if="taskDeadLineExceed.others.emailNotification"
                            style="color: red"
                            >*</span
                          >
                        </template></v-text-field
                      >

                      <v-sheet class="font-weight-regular d-flex">
                        <p class="custom-label">
                          Do you want to send an SMS notification?
                        </p>
                        <v-tooltip
                          location="bottom"
                          text="Additional subscription required"
                        >
                          <template v-slot:activator="{ props }">
                            <v-icon
                              color="blue"
                              v-bind="props"
                              class="ml-1"
                              size="20"
                              >fas fa-info-circle</v-icon
                            >
                          </template>
                        </v-tooltip>
                        <v-switch
                          density="compact"
                          :true-value="true"
                          :false-value="false"
                          class="check text-pink ml-4"
                          direction="horizontal"
                          v-model="taskDeadLineExceed.others.smsNotification"
                          value="smsNotification"
                          hide-details
                          color="primary"
                        ></v-switch>
                      </v-sheet>
                      <v-col cols="12" sm="6" md="12" class="pb-0 mb-2">
                        <CustomSelect
                          :disabled="!taskDeadLineExceed.others.smsNotification"
                          label="Template"
                          :itemSelected="taskDeadLineExceed.others.smsTemplate"
                          v-model="taskDeadLineExceed.others.smsTemplate"
                          :items="smsTemplateList"
                          item-title="TemplateName"
                          item-value="Template_Id"
                          :isLoading="smsTemplateGetLoader"
                          :isAutoComplete="true"
                          @selected-item="
                            (e) => {
                              templateChange(
                                'taskDeadLineExceed',
                                'others',
                                'sms',
                                e
                              );
                            }
                          "
                          variant="solo"
                        >
                        </CustomSelect>
                      </v-col>
                      <v-text-field
                        label="Send to"
                        variant="solo"
                        density="compact"
                        v-model="taskDeadLineExceed.others.smsSendTo"
                        :disabled="!taskDeadLineExceed.others.smsNotification"
                      ></v-text-field>
                    </v-card>
                  </v-card>
                </v-sheet>
              </v-sheet>
            </v-form>
          </div>
        </v-col>
      </v-row>
      <v-divider></v-divider>
      <v-row no-gutters>
        <v-col cols="12" class="d-flex justify-end pt-2 pb-1 pr-5">
          <v-btn
            class="text-capitalize"
            color="primary"
            elevation="1"
            rounded="md"
            variant="outlined"
            @click="$emit('onClose')"
            >Cancel</v-btn
          >
          <v-btn
            class="text-capitalize"
            color="primary"
            elevation="1"
            rounded="md"
            variant="elevated"
            @click="onValidateData"
            >Save</v-btn
          >
        </v-col>
      </v-row>
    </perfect-scrollbar>
  </v-container>
</template>
<script>
import CustomSelect from "@/components/custom-components/CustomSelect.vue";
import { GET_USER_LIST } from "@/graphql/settings/irukka-integration/jobPostFormQueries";
import {
  GET_GROUP_APPROVE,
  GET_FORM_BUILDER,
  GET_EMAIL_NOTIFICATION_TEMPLATE,
} from "@/graphql/commonQueries";
import Config from "@/config.js";
import validationRules from "@/mixins/validationRules";
import axios from "axios";

export default {
  name: "WorkflowForm",
  emits: ["onClose", "onChangeTitle", "onSubmit"],
  mixins: [validationRules],
  props: {
    data: {
      type: Object,
      required: true,
    },
    workflowModule: {
      type: String,
      required: true,
    },
    selectedFormId: {
      type: String,
      required: true,
    },
  },
  data() {
    let modalNotification = this.data?.formData?.modalNotificationData;
    let modalDeadLine = this.data?.formData?.modalDeadlineData;
    let modalTask = this.data?.formData?.modalTaskData;
    return {
      formLoading: false,
      taskTitle: modalTask?.title ? modalTask.title : "",
      taskApproveType: modalTask?.typeOfApprove
        ? modalTask?.typeOfApprove
        : "user",
      taskRuntimeUserAPI:
        modalTask?.approveApi?.id && modalTask?.typeOfApprove === "user"
          ? parseInt(modalTask?.approveApi?.id)
          : "",
      taskUserApproveUser: modalTask?.userOption
        ? modalTask?.userOption
        : "selectedUser",
      taskUserApproveGroup: modalTask?.groupOption
        ? modalTask?.groupOption
        : "selectedGroup",
      taskRuntimeGroupAPI:
        modalTask?.approveApi?.id && modalTask?.typeOfApprove === "group"
          ? modalTask?.approveApi?.id
          : "",
      // autoReject
      deadLine1: {
        checked: modalDeadLine?.deadline === 1 ? true : false,
        hours: modalDeadLine?.deadLine1,
        deadLineRadio: modalDeadLine?.deadlineAction1
          ? modalDeadLine?.deadlineAction1
          : "",
        apiData: modalDeadLine?.deadlineApi1?.id
          ? modalDeadLine?.deadlineApi1?.id
          : "",
        deadlineApi1: {
          id: modalDeadLine?.deadlineApi1?.id
            ? modalDeadLine?.deadlineApi1?.id
            : "",
          text: modalDeadLine?.deadlineApi1?.text
            ? modalDeadLine?.deadlineApi1?.text
            : "",
        },
      },
      //autoReject
      deadLine2: {
        checked: modalDeadLine?.deadlinef1 === 1 ? true : false,
        hours: modalDeadLine?.deadLine2,
        deadLineRadio: modalDeadLine?.deadlineAction2
          ? modalDeadLine?.deadlineAction2
          : "",
        apiData: modalDeadLine?.deadlineApi2?.id
          ? modalDeadLine?.deadlineApi2?.id
          : "",
        deadlineApi2: {
          id: modalDeadLine?.deadlineApi2?.id
            ? modalDeadLine?.deadlineApi2?.id
            : "",
          text: modalDeadLine?.deadlineApi2?.text
            ? modalDeadLine?.deadlineApi2?.text
            : "",
        },
      },
      //autoReject
      deadLine3: {
        checked: modalDeadLine?.deadlinef2 === 1 ? true : false,
        hours: modalDeadLine?.deadLine3,
        deadLineRadio: modalDeadLine?.deadlineAction3
          ? modalDeadLine?.deadlineAction3
          : "",
        apiData: modalDeadLine?.deadlineApi3?.id
          ? modalDeadLine?.deadlineApi3?.id
          : "",
        deadlineApi3: {
          id: modalDeadLine?.deadlineApi3?.id
            ? modalDeadLine?.deadlineApi3?.id
            : "",
          text: modalDeadLine?.deadlineApi3?.text
            ? modalDeadLine?.deadlineApi3?.text
            : "",
        },
      },
      taskAssigned: {
        checked: modalNotification?.taskAssigned === 1 ? true : false,
        user: {
          checked: modalNotification?.assignedUserTa === 1 ? true : false,
          emailNotification:
            modalNotification?.emailNotificationFlagTaAu === 1 ? true : false,
          emailTemplate: modalNotification?.emailTemplateTaAu?.id,
          emailTemplateObj: {
            id: modalNotification?.emailTemplateTaAu?.id,
            text: modalNotification?.emailTemplateTaAu?.text,
          },
          smsTemplateObj: {
            id: modalNotification?.smsTemplateTaAu?.id,
            text: modalNotification?.smsTemplateTaAu?.text,
          },
          smsNotification:
            modalNotification?.smsNotificationFlagTaAu === 1 ? true : false,
          smsTemplate: modalNotification?.smsTemplateTaAu?.id,
          // smsSendTo: modalNotification?.smsSendToTcAu,
        },
        others: {
          checked: modalNotification?.otherUserTa === 1 ? true : false,
          emailNotification:
            modalNotification?.emailNotificationFlagTaOu === 1 ? true : false,
          emailTemplate: modalNotification?.emailTemplateTaOu?.id,
          emailSendTo: modalNotification?.emailSendToTaOu,
          emailTemplateObj: {
            id: modalNotification?.emailTemplateTaOu?.id,
            text: modalNotification?.emailTemplateTaOu?.text,
          },
          smsTemplateObj: {
            id: modalNotification?.smsTemplateTaOu?.id,
            text: modalNotification?.smsTemplateTaOu?.text,
          },
          smsNotification:
            modalNotification?.smsNotificationFlagTaOu === 1 ? true : false,
          smsTemplate: modalNotification?.smsTemplateTaOu?.id,
          smsSendTo: modalNotification?.smsSendToTaOu,
        },
      },
      taskCompleted: {
        checked: modalNotification?.taskCompleted === 1 ? true : false,
        user: {
          checked: modalNotification?.assignedUserTc === 1 ? true : false,
          emailNotification:
            modalNotification?.emailNotificationFlagTcAu === 1 ? true : false,
          emailTemplate: modalNotification?.emailTemplateTcAu?.id,
          emailTemplateObj: {
            id: modalNotification?.emailTemplateTcAu?.id,
            text: modalNotification?.emailTemplateTcAu?.text,
          },
          smsTemplateObj: {
            id: modalNotification?.smsTemplateTcAu?.id,
            text: modalNotification?.smsTemplateTcAu?.text,
          },
          smsNotification:
            modalNotification?.smsNotificationFlagTcAu === 1 ? true : false,
          smsTemplate: modalNotification?.smsTemplateTcAu?.text,
        },
        others: {
          checked: modalNotification?.otherUserTc === 1 ? true : false,
          emailNotification:
            modalNotification?.emailNotificationFlagTcAu === 1 ? true : false,
          emailTemplate: modalNotification?.emailTemplateTcAu?.id,
          emailSendTo: modalNotification?.emailSendToTcAu,
          emailTemplateObj: {
            id: modalNotification?.emailTemplateTcAu?.id,
            text: modalNotification?.emailTemplateTcAu?.text,
          },
          smsTemplateObj: {
            id: modalNotification?.smsTemplateTcAu?.id,
            text: modalNotification?.smsTemplateTcAu?.text,
          },
          smsNotification:
            modalNotification?.smsNotificationFlagTcAu === 1 ? true : false,
          smsTemplate: modalNotification?.smsTemplateTcAu?.id,
          smsSendTo: modalNotification?.smsSendToTcAu,
        },
      },
      taskBeforeExceed: {
        checked: modalNotification?.notificationFlagDb === 1 ? true : false,
        beforeExceedHours: modalNotification?.timeBeforeexceededDb,
        user: {
          checked: modalNotification?.assignedUserDb === 1 ? true : false,
          emailNotification:
            modalNotification?.emailNotificationFlagDbAu === 1 ? true : false,
          emailTemplate: modalNotification?.emailTemplateDbAu?.id,
          emailTemplateObj: {
            id: modalNotification?.emailTemplateDbAu?.id,
            text: modalNotification?.emailTemplateDbAu?.text,
          },
          smsTemplateObj: {
            id: modalNotification?.smsTemplateDbAu?.id,
            text: modalNotification?.smsTemplateDbAu?.text,
          },
          smsNotification:
            modalNotification?.smsNotificationFlagDbAu === 1 ? true : false,
          smsTemplate: modalNotification?.smsTemplateDbAu?.id,
        },
        others: {
          checked: modalNotification?.otherUserDb === 1 ? true : false,
          emailNotification:
            modalNotification?.emailNotificationFlagDbOu === 1 ? true : false,
          emailTemplate: modalNotification?.emailTemplateDbOu?.id,
          emailSendTo: modalNotification?.emailSendToDbOu,
          emailTemplateObj: {
            id: modalNotification?.emailTemplateDbOu?.id,
            text: modalNotification?.emailTemplateDbOu?.text,
          },
          smsTemplateObj: {
            id: modalNotification?.smsTemplateDbOu?.id,
            text: modalNotification?.smsTemplateDbOu?.text,
          },
          smsNotification:
            modalNotification?.smsNotificationFlagDbOu === 1 ? true : false,
          smsTemplate: modalNotification?.smsTemplateDbOu?.id,
          smsSendTo: modalNotification?.smsSendToDbOu,
        },
      },
      taskDeadLineExceed: {
        checked: modalNotification?.NotificationFlagDa === 1 ? true : false,
        user: {
          checked: modalNotification?.assignedUserDa === 1 ? true : false,
          emailNotification:
            modalNotification?.emailNotificationFlagDaAu === 1 ? true : false,
          emailTemplate: modalNotification?.emailTemplateDaAu?.id,
          emailTemplateObj: {
            id: modalNotification?.emailTemplateDaAu?.id,
            text: modalNotification?.emailTemplateDaAu?.text,
          },
          smsTemplateObj: {
            id: modalNotification?.smsTemplateDaAu?.id,
            text: modalNotification?.smsTemplateDaAu?.text,
          },
          smsNotification:
            modalNotification?.smsNotificationFlagDaAu === 1 ? true : false,
          smsTemplate: modalNotification?.smsTemplateDaAu?.id,
        },
        others: {
          checked: modalNotification?.otherUserDa === 1 ? true : false,
          emailNotification:
            modalNotification?.emailNotificationFlagDaOu === 1 ? true : false,
          emailTemplate: modalNotification?.emailTemplateDaOu?.id,
          emailSendTo: modalNotification?.emailSendToDaOu,
          emailTemplateObj: {
            id: modalNotification?.emailTemplateDaOu?.id,
            text: modalNotification?.emailTemplateDaOu?.text,
          },
          smsTemplateObj: {
            id: modalNotification?.smsTemplateDaOu?.id,
            text: modalNotification?.smsTemplateDaOu?.text,
          },
          smsNotification:
            modalNotification?.smsNotificationFlagDaOu === 1 ? true : false,
          smsTemplate: modalNotification?.smsTemplateDaOu?.id,
          smsSendTo: modalNotification?.smsSendToDaOu,
        },
      },
      userApproveLoader: false,
      selectedUserApprove: modalTask?.approveByUser?.id
        ? parseInt(modalTask?.approveByUser?.id)
        : "",
      userApproveList: [],
      groupApproveLoader: false,
      selectedGroupApprove: modalTask?.approveByGroup?.id
        ? parseInt(modalTask?.approveByGroup?.id)
        : "",
      groupApproveList: [],
      formGetLoader: false,
      formGetList: [],
      selectedFormData: modalTask?.formId?.id
        ? parseInt(modalTask?.formId?.id)
        : 0,
      emailTemplateGetLoader: false,
      emailTemplateList: [],
      selectedEmailTemplate: "",
      smsTemplateGetLoader: false,
      smsTemplateList: [],
      selectedSmsTemplate: "",
      getApproveLoader: false,
      selectedGetApprove: "",
      getApproveList: [],
    };
  },
  methods: {
    openAddForm() {
      window.open(this.baseUrl + "v3/workflow/dynamic-form-builder", "_blank");
    },
    openAddGroup() {
      window.open(this.baseUrl + "in/core-hr/custom-employee-groups", "_blank");
    },
    async fetchGetUsers() {
      let vm = this;
      const modalTask = vm.data?.formData?.modalTaskData;
      const selectedValue = modalTask?.approveByUser?.id
        ? parseInt(modalTask?.approveByUser?.id)
        : "";
      this.userApproveLoader = true;
      await vm.$apollo
        .query({
          query: GET_USER_LIST,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.getUsers) {
            this.userApproveList = response.data.getUsers.users.map((item) => ({
              ...item,
              userInfo: item.UserName + " - " + item.userDefinedEmployeeId,
            }));
            this.selectedUserApprove = modalTask?.approveByUser?.id
              ? parseInt(modalTask?.approveByUser?.id)
              : "";
            this.userApproveLoader = false;
          } else {
            this.userApproveList = [];
            this.selectedUserApprove = selectedValue;
          }
          this.userApproveLoader = false;
        })
        .catch(() => {
          this.userApproveList = [];
          this.selectedUserApprove = selectedValue;
          this.userApproveLoader = false;
        });
    },
    async fetchGetGroup() {
      let gm = this;
      this.groupApproveList = [];
      this.groupApproveLoader = true;
      const modalTask = gm.data?.formData?.modalTaskData;
      const selectedValue = modalTask?.approveByGroup?.id
        ? parseInt(modalTask?.approveByGroup?.id)
        : "";
      await gm.$apollo
        .query({
          variables: {
            formId: this.selectedFormId,
          },
          query: GET_GROUP_APPROVE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getCustomGroups &&
            response.data.getCustomGroups.groupData
          ) {
            this.groupApproveList = response.data.getCustomGroups.groupData;
            // this.selectedGroupApprove =
            //   response.data.getCustomGroups.groupData[0].Group_Id;
            this.groupApproveLoader = false;
          } else {
            this.groupApproveList = [];
            this.selectedGroupApprove = selectedValue;
          }
          this.groupApproveLoader = false;
        })
        .catch(() => {
          this.groupApproveList = [];
          this.selectedGroupApprove = selectedValue;
          this.groupApproveLoader = false;
        });
    },
    async fetchGetForm() {
      let lm = this;
      this.formGetList = [];
      this.formGetLoader = true;
      await lm.$apollo
        .query({
          query: GET_FORM_BUILDER,
          client: "apolloClientAL",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getAllMinimalDynamicFormTemplate &&
            response.data.getAllMinimalDynamicFormTemplate.result
          ) {
            this.formGetList =
              response.data.getAllMinimalDynamicFormTemplate.result;
            this.formGetLoader = false;
          } else {
            this.formGetList = [];
          }
          this.formGetLoader = false;
        })
        .catch(() => {
          this.formGetList = [];
          this.formGetLoader = false;
        });
    },
    async fetchGetEmailNotification() {
      let lm = this;
      this.emailTemplateGetLoader = true;
      await lm.$apollo
        .query({
          variables: { type: "Email", formIds: [this.selectedFormId] },
          query: GET_EMAIL_NOTIFICATION_TEMPLATE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getNotificationTemplates &&
            response.data.getNotificationTemplates.notificationTemplates
          ) {
            let emailTemplates = [];
            response.data.getNotificationTemplates.notificationTemplates.forEach(
              (item) => {
                if (
                  item.TemplateName.toLowerCase().includes(
                    this.workflowModule.toLowerCase()
                  )
                ) {
                  emailTemplates.push(item);
                }
              }
            );
            if (emailTemplates.length !== 0) {
              this.emailTemplateList = emailTemplates;
            } else {
              this.emailTemplateList =
                response.data.getNotificationTemplates.notificationTemplates;
            }
            this.emailTemplateGetLoader = false;
          } else {
            this.emailTemplateList = [];
          }
          this.emailTemplateGetLoader = false;
        })
        .catch(() => {
          this.emailTemplateList = [];
          this.emailTemplateGetLoader = false;
        });
    },
    async fetchGetSMSNotification() {
      let lm = this;
      this.smsTemplateGetLoader = true;
      await lm.$apollo
        .query({
          variables: { type: "SMS", formIds: [this.selectedFormId] },
          query: GET_EMAIL_NOTIFICATION_TEMPLATE,
          client: "apolloClientA",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.getNotificationTemplates &&
            response.data.getNotificationTemplates.notificationTemplates
          ) {
            // this.smsTemplateList =
            //   response.data.getNotificationTemplates.notificationTemplates;
            let smsTemplates = [];
            response.data.getNotificationTemplates.notificationTemplates.forEach(
              (item) => {
                if (
                  item.TemplateName.toLowerCase().includes(
                    this.workflowModule.toLowerCase()
                  )
                ) {
                  smsTemplates.push(item);
                }
              }
            );
            if (smsTemplates.length !== 0) {
              this.smsTemplateList = smsTemplates;
            } else {
              this.smsTemplateList =
                response.data.getNotificationTemplates.notificationTemplates;
            }
            this.smsTemplateGetLoader = false;
          } else {
            this.smsTemplateList = [];
          }
          this.smsTemplateGetLoader = false;
        })
        .catch(() => {
          this.smsTemplateList = [];
          this.smsTemplateGetLoader = false;
        });
    },
    async fetchGetAssigned() {
      let vm = this;
      let modalTask = vm.data?.formData?.modalTaskData;
      let modalDeadLine = vm.data?.formData?.modalDeadlineData;
      this.getApproveLoader = true; // Assuming you have a file input element with id "fileInput
      const payload = {
        query_key: "query.api.endpoint",
        filter: [
          {
            key: "Visible",
            value: "Yes",
            op: "=",
          },
        ],
        sort: "created_date",
        order: "desc",
      };
      await axios
        .post(Config.workflowUrl + "/master/query", JSON.stringify(payload), {
          headers: {
            org_code: vm.orgCode,
            employee_id: vm.loginEmployeeId,
            Db_prefix: vm.domainName,
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            Authorization: window.$cookies.get("accessToken")
              ? window.$cookies.get("accessToken")
              : "",
          },
        })
        .then((response) => {
          if (response && response.data && response.data.results) {
            const responseData = response.data.results[0].endpoint_key;
            this.taskRuntimeUserAPI =
              modalTask?.approveApi?.id && modalTask?.typeOfApprove === "user"
                ? modalTask?.approveApi?.id
                : responseData;
            this.taskRuntimeGroupAPI =
              modalTask?.approveApi?.id && modalTask?.typeOfApprove === "group"
                ? modalTask?.approveApi?.id
                : responseData;
            this.deadLine1.apiData = modalDeadLine?.deadlineApi1?.id
              ? modalDeadLine?.deadlineApi1?.id
              : responseData;
            this.deadLine2.apiData = modalDeadLine?.deadlineApi2?.id
              ? modalDeadLine?.deadlineApi2?.id
              : responseData;
            this.deadLine3.apiData = modalDeadLine?.deadlineApi3?.id
              ? modalDeadLine?.deadlineApi3?.id
              : responseData;
            this.getApproveList = response.data.results;
            this.getApproveLoader = false;
          } else {
            this.getApproveList = [];
            this.getApproveLoader = false;
            this.setEmptyStateAPI();
          }
        })
        .catch(() => {
          this.getApproveList = [];
          this.getApproveLoader = false;
          this.setEmptyState();
        });
    },
    setEmptyStateAPI() {
      const modalTask = this.data?.formData?.modalTaskData;
      const modalDeadLine = this.data?.formData?.modalDeadlineData;
      this.taskRuntimeUserAPI =
        modalTask?.approveApi?.id && modalTask?.typeOfApprove === "user"
          ? modalTask?.approveApi?.id
          : "";
      this.taskRuntimeGroupAPI =
        modalTask?.approveApi?.id && modalTask?.typeOfApprove === "group"
          ? modalTask?.approveApi?.id
          : "";
      this.deadLine1.apiData = modalDeadLine?.deadlineApi1?.id
        ? modalDeadLine?.deadlineApi1?.id
        : "";
      this.deadLine2.apiData = modalDeadLine?.deadlineApi2?.id
        ? modalDeadLine?.deadlineApi2?.id
        : "";
      this.deadLine3.apiData = modalDeadLine?.deadlineApi3?.id
        ? modalDeadLine?.deadlineApi3?.id
        : "";
    },
    templateChange(notification, approveType, notificationType, templateId) {
      const emailTemplateData = this.emailTemplateList.find(
        (list) => list?.Template_Id === templateId
      );
      const smsTemplateData = this.smsTemplateList.find(
        (list) => list?.Template_Id === templateId
      );
      if (notification === "taskAssigned") {
        if (notificationType === "email") {
          this.taskAssigned[approveType]["emailTemplateObj"] = {
            id: emailTemplateData.Template_Id,
            text: emailTemplateData.TemplateName,
          };
        } else {
          this.taskAssigned[approveType]["smsTemplateObj"] = {
            id: smsTemplateData.Template_Id,
            text: smsTemplateData.TemplateName,
          };
        }
      } else if (notification === "taskCompleted") {
        if (notificationType === "email") {
          this.taskCompleted[approveType]["emailTemplateObj"] = {
            id: emailTemplateData.Template_Id,
            text: emailTemplateData.TemplateName,
          };
        } else {
          this.taskCompleted[approveType]["smsTemplateObj"] = {
            id: smsTemplateData.Template_Id,
            text: smsTemplateData.TemplateName,
          };
        }
      } else if (notification === "taskBeforeExceed") {
        if (notificationType === "email") {
          this.taskBeforeExceed[approveType]["emailTemplateObj"] = {
            id: emailTemplateData.Template_Id,
            text: emailTemplateData.TemplateName,
          };
        } else {
          this.taskBeforeExceed[approveType]["smsTemplateObj"] = {
            id: smsTemplateData.Template_Id,
            text: smsTemplateData.TemplateName,
          };
        }
      } else if (notification === "taskDeadLineExceed") {
        if (notificationType === "email") {
          this.taskDeadLineExceed[approveType]["emailTemplateObj"] = {
            id: emailTemplateData.Template_Id,
            text: emailTemplateData.TemplateName,
          };
        } else {
          this.taskDeadLineExceed[approveType]["smsTemplateObj"] = {
            id: smsTemplateData.Template_Id,
            text: smsTemplateData.TemplateName,
          };
        }
      }
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    async onValidateData() {
      const { valid } = await this.$refs.taskForm.validate();
      const notificationValidation =
        await this.$refs.notificationForm.validate();
      if (valid && notificationValidation.valid) {
        this.onSubmitFormData();
      } else {
        const invalidFields = [];
        Object.keys(this.$refs).forEach((refName) => {
          const field = this.$refs[refName];
          if (field && field.rules) {
            let allTrue = field.rules.every((value) => value === true);
            if (field.rules.length > 0 && !allTrue) {
              invalidFields.push(refName);
            }
          }
        });
        // Log or handle the invalid fields
        if (invalidFields.length > 0) {
          const firstErrorField = invalidFields[0];
          this.$nextTick(() => {
            const fieldRef = this.$refs[firstErrorField];
            if (fieldRef) {
              let selectFields = [
                "taskTitle",
                "othersEmailTemplate",
                "othersSendTo",
                "taskCompletedEmailTemplate",
                "taskCompletedSendTo",
                "taskBeforeExceedEmailTemplate",
                "taskBeforeExceedEmailSendTo",
                "taskDeadLineExceedEmailTemplate",
                "taskDeadLineExceedEmailSendTo",
              ];
              if (selectFields.includes(firstErrorField)) {
                fieldRef.onFocusCustomSelect
                  ? fieldRef.onFocusCustomSelect()
                  : fieldRef.focus();
              } else {
                // except for select
                fieldRef.focus();
              }
              if (fieldRef.$el) {
                const rect = fieldRef.$el.getBoundingClientRect();
                window.scrollTo({
                  top: (window.scrollY + rect.top) * 0.4, // Adjust as needed
                  behavior: "smooth",
                });
              }
            }
          });
        }
      }
    },
    onSubmitFormData() {
      let payloadData = {};
      let resultForm = this.formGetList.find(
        (list) => list?.templateId === this.selectedFormData
      );
      payloadData = {
        modalTaskData: {
          title: this.taskTitle,
          typeOfApprove: this.taskApproveType,
          formId: {
            id: resultForm?.templateId.toString(),
            text: resultForm?.templateName,
          },
        },
        modalDeadlineData: {
          deadline: this.deadLine1.checked ? 1 : 0,
        },
        modalNotificationData: {
          taskAssigned: this.taskAssigned.checked ? 1 : 0,
          assignedUserTa: this.taskAssigned.user.checked ? 1 : 0,
          emailNotificationFlagTaAu: this.taskAssigned.user.emailNotification
            ? 1
            : 0,
          emailTemplateTaAu: {
            id: this.taskAssigned.user.emailTemplateObj.id,
            text: this.taskAssigned.user.emailTemplateObj.text,
          },
          smsNotificationFlagTaAu: this.taskAssigned.user.smsNotification
            ? 1
            : 0,
          smsTemplateTaAu: {
            id: this.taskAssigned.user.smsTemplateObj.id,
            text: this.taskAssigned.user.smsTemplateObj.text,
          },
          otherUserTa: this.taskAssigned.others.checked ? 1 : 0,
          emailNotificationFlagTaOu: this.taskAssigned.others.emailNotification
            ? 1
            : 0,
          emailTemplateTaOu: {
            id: this.taskAssigned.others.emailTemplateObj.id,
            text: this.taskAssigned.others.emailTemplateObj.text,
          },
          emailSendToTaOu: this.taskAssigned.others.emailSendTo,
          smsNotificationFlagTaOu: this.taskAssigned.others.smsNotification
            ? 1
            : 0,
          smsTemplateTaOu: {
            id: this.taskAssigned.others.smsTemplateObj.id,
            text: this.taskAssigned.others.smsTemplateObj.text,
          },
          smsSendToTaOu: this.taskAssigned.others.smsSendTo,
          taskCompleted: this.taskCompleted.checked ? 1 : 0,
          assignedUserTc: this.taskCompleted.user.checked ? 1 : 0,
          emailNotificationFlagTcAu: this.taskCompleted.user.emailNotification
            ? 1
            : 0,
          emailNotificationFlagTcOu: this.taskCompleted.others.emailNotification
            ? 1
            : 0,
          emailTemplateTcAu: {
            id: this.taskCompleted.user.emailTemplateObj.id,
            text: this.taskCompleted.user.emailTemplateObj.text,
          },
          emailTemplateTcOu: {
            id: this.taskCompleted.others.emailTemplateObj.id,
            text: this.taskCompleted.others.emailTemplateObj.text,
          },
          smsNotificationFlagTcAu: this.taskCompleted.user.smsNotification
            ? 1
            : 0,
          smsTemplateTcAu: {
            id: this.taskCompleted.user.smsTemplateObj.id,
            text: this.taskCompleted.user.smsTemplateObj.text,
          },
          otherUserTc: this.taskCompleted.others.checked ? 1 : 0,
          emailSendToTcAu: this.taskCompleted.others.emailSendTo,
          emailSendToTcOu: this.taskCompleted.others.emailSendTo,
          smsSendToTcAu: this.taskCompleted.others.smsSendTo,
          notificationFlagDb: this.taskBeforeExceed.checked ? 1 : 0,
          timeBeforeexceededDb: this.taskBeforeExceed.beforeExceedHours,
          assignedUserDb: this.taskBeforeExceed.user.checked ? 1 : 0,
          emailNotificationFlagDbAu: this.taskBeforeExceed.user
            .emailNotification
            ? 1
            : 0,
          emailTemplateDbAu: {
            id: this.taskBeforeExceed.user.emailTemplateObj.id,
            text: this.taskBeforeExceed.user.emailTemplateObj.text,
          },
          smsNotificationFlagDbAu: this.taskBeforeExceed.user.smsNotification
            ? 1
            : 0,
          smsTemplateDbAu: {
            id: this.taskBeforeExceed.user.smsTemplateObj.id,
            text: this.taskBeforeExceed.user.smsTemplateObj.text,
          },
          otherUserDb: this.taskBeforeExceed.others.checked ? 1 : 0,
          emailNotificationFlagDbOu: this.taskBeforeExceed.others
            .emailNotification
            ? 1
            : 0,
          emailTemplateDbOu: {
            id: this.taskBeforeExceed.others.emailTemplateObj.id,
            text: this.taskBeforeExceed.others.emailTemplateObj.text,
          },
          emailSendToDbOu: this.taskBeforeExceed.others.emailSendTo,
          smsNotificationFlagDbOu: this.taskBeforeExceed.others.smsNotification
            ? 1
            : 0,
          smsTemplateDbOu: {
            id: this.taskBeforeExceed.others.smsTemplateObj.id,
            text: this.taskBeforeExceed.others.smsTemplateObj.text,
          },
          smsSendToDbOu: this.taskBeforeExceed.others.smsSendTo,
          NotificationFlagDa: this.taskDeadLineExceed.checked ? 1 : 0,
          assignedUserDa: this.taskDeadLineExceed.checked ? 1 : 0,
          emailNotificationFlagDaAu: this.taskDeadLineExceed.user
            .emailNotification
            ? 1
            : 0,
          emailTemplateDaAu: {
            id: this.taskDeadLineExceed.user.emailTemplateObj.id,
            text: this.taskDeadLineExceed.user.emailTemplateObj.text,
          },
          smsNotificationFlagDaAu: this.taskDeadLineExceed.user.smsNotification
            ? 1
            : 0,
          smsTemplateDaAu: {
            id: this.taskDeadLineExceed.user.smsTemplateObj.id,
            text: this.taskDeadLineExceed.user.smsTemplateObj.text,
          },
          otherUserDa: this.taskDeadLineExceed.others.checked ? 1 : 0,
          emailNotificationFlagDaOu: this.taskDeadLineExceed.user
            .emailNotification
            ? 1
            : 0,
          emailTemplateDaOu: {
            id: this.taskDeadLineExceed.others.emailTemplateObj.id,
            text: this.taskDeadLineExceed.others.emailTemplateObj.text,
          },
          emailSendToDaOu: this.taskDeadLineExceed.others.emailSendTo,
          smsNotificationFlagDaOu: this.taskDeadLineExceed.others
            .smsNotification
            ? 1
            : 0,
          smsTemplateDaOu: {
            id: this.taskDeadLineExceed.others.smsTemplateObj.id,
            text: this.taskDeadLineExceed.others.smsTemplateObj.text,
          },
          smsSendToDaOu: this.taskDeadLineExceed.others.smsSendTo,
        },
      };
      if (this.deadLine1.checked) {
        payloadData["modalDeadlineData"]["deadLine1"] =
          this.deadLine1.hours.toString();
        payloadData["modalDeadlineData"]["deadlineAction1"] =
          this.deadLine1.deadLineRadio;
      }
      if (this.deadLine2.checked) {
        payloadData["modalDeadlineData"]["deadlinef1"] = this.deadLine2.checked
          ? 1
          : 0;
        payloadData["modalDeadlineData"]["deadLine2"] =
          this.deadLine2.hours.toString();
        payloadData["modalDeadlineData"]["deadlineAction2"] =
          this.deadLine2.deadLineRadio;
      }
      if (this.deadLine3.checked) {
        payloadData["modalDeadlineData"]["deadlinef2"] = this.deadLine3.checked
          ? 1
          : 0;
        payloadData["modalDeadlineData"]["deadLine3"] =
          this.deadLine3.hours.toString();
        payloadData["modalDeadlineData"]["deadlineAction3"] =
          this.deadLine3.deadLineRadio;
      }
      if (this.deadLine1.deadLineRadio === "deadlineApi") {
        let resultCallAPI = this.getApproveList.find(
          (list) => list.endpoint_key === this.deadLine1.apiData
        );
        payloadData["modalDeadlineData"]["deadlineApi1"] = {
          id: resultCallAPI?.endpoint_key,
          text: resultCallAPI?.name,
        };
      }
      if (this.deadLine2.deadLineRadio === "deadlineApi") {
        let resultCallAPI = this.getApproveList.find(
          (list) => list.endpoint_key === this.deadLine2.apiData
        );
        payloadData["modalDeadlineData"]["deadlineApi2"] = {
          id: resultCallAPI?.endpoint_key,
          text: resultCallAPI?.name,
        };
      }
      if (this.deadLine3.deadLineRadio === "deadlineApi") {
        let resultCallAPI = this.getApproveList.find(
          (list) => list.endpoint_key === this.deadLine3.apiData
        );
        payloadData["modalDeadlineData"]["deadlineApi3"] = {
          id: resultCallAPI?.endpoint_key,
          text: resultCallAPI?.name,
        };
      }
      if (this.taskApproveType === "user") {
        payloadData["modalTaskData"]["userOption"] = this.taskUserApproveUser;
      } else if (this.taskApproveType === "group") {
        payloadData["modalTaskData"]["groupOption"] = this.taskUserApproveGroup;
      }
      if (
        this.taskApproveType === "user" &&
        this.taskUserApproveUser === "api"
      ) {
        let resultUserData = this.getApproveList.find(
          (list) => list.endpoint_key === this.taskRuntimeUserAPI
        );
        payloadData["modalTaskData"]["approveApi"] = {
          id: resultUserData?.endpoint_key.toString(),
          text: resultUserData?.name,
        };
      } else if (
        this.taskApproveType === "group" &&
        this.taskUserApproveGroup === "api"
      ) {
        let resultGroupData = this.getApproveList.find(
          (list) => list.endpoint_key === this.taskRuntimeGroupAPI
        );
        payloadData["modalTaskData"]["approveApi"] = {
          id: resultGroupData?.endpoint_key.toString(),
          text: resultGroupData?.name,
        };
      }
      if (
        this.taskUserApproveUser === "selectedUser" &&
        this.taskApproveType === "user"
      ) {
        let resultUserData = this.userApproveList.find(
          (list) => list.UserId === this.selectedUserApprove
        );
        payloadData["modalTaskData"]["approveByUser"] = {
          id: resultUserData?.UserId.toString(),
          text: resultUserData?.UserName,
        };
      } else if (
        this.taskUserApproveGroup === "selectedGroup" &&
        this.taskApproveType === "group"
      ) {
        let resultGroupData = this.groupApproveList.find(
          (list) => list.Group_Id === this.selectedGroupApprove
        );
        payloadData["modalTaskData"]["approveByGroup"] = {
          id: resultGroupData?.Group_Id.toString(),
          text: resultGroupData?.Group_Name,
        };
      }
      this.$emit("onSubmit", payloadData);
    },
  },
  watch: {
    taskTitle: {
      immediate: true,
      handler(val) {
        this.$emit("onChangeTitle", val);
      },
    },
    taskApproveType(val) {
      if (val === "group") {
        // this.selectedGroupApprove = this.groupApproveList[0]?.Group_Id;
        this.selectedUserApprove = null;
      } else if (val === "user") {
        this.selectedGroupApprove = null;
        // this.selectedUserApprove = this.userApproveList[0]?.UserId;
      }
    },
    "taskAssigned.others.checked"(val) {
      if (!val) {
        this.taskAssigned.others.emailNotification = false;
      }
    },
    "taskCompleted.others.checked"(val) {
      if (!val) {
        this.taskCompleted.others.emailNotification = false;
      }
    },
    "taskBeforeExceed.others.checked"(val) {
      if (!val) {
        this.taskBeforeExceed.others.emailNotification = false;
      }
    },
    "taskDeadLineExceed.others.checked"(val) {
      if (!val) {
        this.taskDeadLineExceed.others.emailNotification = false;
      }
    },
  },
  mounted() {
    this.fetchGetUsers();
    this.fetchGetGroup();
    this.fetchGetForm();
    this.fetchGetAssigned();
    this.fetchGetEmailNotification();
    this.fetchGetSMSNotification();
  },
  components: {
    CustomSelect,
  },
  computed: {
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    orgCode() {
      let org_code = localStorage.getItem("orgCode");
      return org_code ? org_code : this.$store.getters.orgCode;
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
};
</script>

<style>
.check.v-input--horizontal {
  grid-template-areas: none !important;
  /* height: 24px; */
}

.v-input__details,
.v-messages {
  min-height: 6px !important;
}
.close_modal {
  position: absolute;
  right: 0px;
  top: -10px;
  cursor: pointer;
  /* height: 25px;
  width: 25px;
  background: red; */
  display: flex;
  justify-content: center;
  align-items: center;
  /* border-radius: 5px; */
}
</style>
