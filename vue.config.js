const { defineConfig } = require("@vue/cli-service");
const env = process.env.NODE_ENV;
module.exports = defineConfig({
  transpileDependencies: true,
  publicPath:
    env === "production" || env === "development" ? "/v3" : "/hrapponline/v3",
  pluginOptions: {
    vuetify: {
      // https://github.com/vuetifyjs/vuetify-loader/tree/next/packages/vuetify-loader
    },
  },
  //Enable to supress the overlay errors
  devServer: {
    client: {
      overlay: false,
    },
  },
});
